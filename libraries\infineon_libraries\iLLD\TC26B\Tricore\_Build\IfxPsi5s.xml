<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxPsi5s" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Port/Std/IfxPort.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxPort_cfg.c</iLLD:file>
  <iLLD:file class="mchal">Psi5s/Psi5s/IfxPsi5s_Psi5s.c</iLLD:file>
  <iLLD:file class="mchal">Psi5s/Std/IfxPsi5s.c</iLLD:file>
</iLLD:filelist>
