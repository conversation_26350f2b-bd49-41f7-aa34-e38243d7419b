/**
 * \file IfxCcu6_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Ccu6_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Ccu6
 * 
 */
#ifndef IFXCCU6_BF_H
#define IFXCCU6_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Ccu6_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN0 */
#define IFX_CCU6_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN0 */
#define IFX_CCU6_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN0 */
#define IFX_CCU6_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN10 */
#define IFX_CCU6_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN10 */
#define IFX_CCU6_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN10 */
#define IFX_CCU6_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN11 */
#define IFX_CCU6_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN11 */
#define IFX_CCU6_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN11 */
#define IFX_CCU6_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN12 */
#define IFX_CCU6_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN12 */
#define IFX_CCU6_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN12 */
#define IFX_CCU6_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN13 */
#define IFX_CCU6_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN13 */
#define IFX_CCU6_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN13 */
#define IFX_CCU6_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN14 */
#define IFX_CCU6_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN14 */
#define IFX_CCU6_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN14 */
#define IFX_CCU6_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN15 */
#define IFX_CCU6_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN15 */
#define IFX_CCU6_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN15 */
#define IFX_CCU6_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN16 */
#define IFX_CCU6_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN16 */
#define IFX_CCU6_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN16 */
#define IFX_CCU6_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN17 */
#define IFX_CCU6_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN17 */
#define IFX_CCU6_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN17 */
#define IFX_CCU6_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN18 */
#define IFX_CCU6_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN18 */
#define IFX_CCU6_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN18 */
#define IFX_CCU6_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN19 */
#define IFX_CCU6_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN19 */
#define IFX_CCU6_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN19 */
#define IFX_CCU6_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN1 */
#define IFX_CCU6_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN1 */
#define IFX_CCU6_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN1 */
#define IFX_CCU6_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN20 */
#define IFX_CCU6_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN20 */
#define IFX_CCU6_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN20 */
#define IFX_CCU6_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN21 */
#define IFX_CCU6_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN21 */
#define IFX_CCU6_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN21 */
#define IFX_CCU6_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN22 */
#define IFX_CCU6_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN22 */
#define IFX_CCU6_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN22 */
#define IFX_CCU6_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN23 */
#define IFX_CCU6_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN23 */
#define IFX_CCU6_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN23 */
#define IFX_CCU6_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN24 */
#define IFX_CCU6_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN24 */
#define IFX_CCU6_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN24 */
#define IFX_CCU6_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN25 */
#define IFX_CCU6_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN25 */
#define IFX_CCU6_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN25 */
#define IFX_CCU6_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN26 */
#define IFX_CCU6_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN26 */
#define IFX_CCU6_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN26 */
#define IFX_CCU6_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN27 */
#define IFX_CCU6_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN27 */
#define IFX_CCU6_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN27 */
#define IFX_CCU6_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN28 */
#define IFX_CCU6_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN28 */
#define IFX_CCU6_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN28 */
#define IFX_CCU6_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN29 */
#define IFX_CCU6_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN29 */
#define IFX_CCU6_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN29 */
#define IFX_CCU6_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN2 */
#define IFX_CCU6_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN2 */
#define IFX_CCU6_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN2 */
#define IFX_CCU6_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN30 */
#define IFX_CCU6_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN30 */
#define IFX_CCU6_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN30 */
#define IFX_CCU6_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN31 */
#define IFX_CCU6_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN31 */
#define IFX_CCU6_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN31 */
#define IFX_CCU6_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN3 */
#define IFX_CCU6_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN3 */
#define IFX_CCU6_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN3 */
#define IFX_CCU6_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN4 */
#define IFX_CCU6_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN4 */
#define IFX_CCU6_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN4 */
#define IFX_CCU6_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN5 */
#define IFX_CCU6_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN5 */
#define IFX_CCU6_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN5 */
#define IFX_CCU6_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN6 */
#define IFX_CCU6_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN6 */
#define IFX_CCU6_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN6 */
#define IFX_CCU6_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN7 */
#define IFX_CCU6_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN7 */
#define IFX_CCU6_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN7 */
#define IFX_CCU6_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN8 */
#define IFX_CCU6_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN8 */
#define IFX_CCU6_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN8 */
#define IFX_CCU6_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_CCU6_ACCEN0_Bits.EN9 */
#define IFX_CCU6_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ACCEN0_Bits.EN9 */
#define IFX_CCU6_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ACCEN0_Bits.EN9 */
#define IFX_CCU6_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_CCU6_CC60R_Bits.CCV */
#define IFX_CCU6_CC60R_CCV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC60R_Bits.CCV */
#define IFX_CCU6_CC60R_CCV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC60R_Bits.CCV */
#define IFX_CCU6_CC60R_CCV_OFF (0u)

/** \brief  Length for Ifx_CCU6_CC60SR_Bits.CCS */
#define IFX_CCU6_CC60SR_CCS_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC60SR_Bits.CCS */
#define IFX_CCU6_CC60SR_CCS_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC60SR_Bits.CCS */
#define IFX_CCU6_CC60SR_CCS_OFF (0u)

/** \brief  Length for Ifx_CCU6_CC61R_Bits.CCV */
#define IFX_CCU6_CC61R_CCV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC61R_Bits.CCV */
#define IFX_CCU6_CC61R_CCV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC61R_Bits.CCV */
#define IFX_CCU6_CC61R_CCV_OFF (0u)

/** \brief  Length for Ifx_CCU6_CC61SR_Bits.CCS */
#define IFX_CCU6_CC61SR_CCS_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC61SR_Bits.CCS */
#define IFX_CCU6_CC61SR_CCS_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC61SR_Bits.CCS */
#define IFX_CCU6_CC61SR_CCS_OFF (0u)

/** \brief  Length for Ifx_CCU6_CC62R_Bits.CCV */
#define IFX_CCU6_CC62R_CCV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC62R_Bits.CCV */
#define IFX_CCU6_CC62R_CCV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC62R_Bits.CCV */
#define IFX_CCU6_CC62R_CCV_OFF (0u)

/** \brief  Length for Ifx_CCU6_CC62SR_Bits.CCS */
#define IFX_CCU6_CC62SR_CCS_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC62SR_Bits.CCS */
#define IFX_CCU6_CC62SR_CCS_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC62SR_Bits.CCS */
#define IFX_CCU6_CC62SR_CCS_OFF (0u)

/** \brief  Length for Ifx_CCU6_CC63R_Bits.CCV */
#define IFX_CCU6_CC63R_CCV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC63R_Bits.CCV */
#define IFX_CCU6_CC63R_CCV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC63R_Bits.CCV */
#define IFX_CCU6_CC63R_CCV_OFF (0u)

/** \brief  Length for Ifx_CCU6_CC63SR_Bits.CCS */
#define IFX_CCU6_CC63SR_CCS_LEN (16u)

/** \brief  Mask for Ifx_CCU6_CC63SR_Bits.CCS */
#define IFX_CCU6_CC63SR_CCS_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_CC63SR_Bits.CCS */
#define IFX_CCU6_CC63SR_CCS_OFF (0u)

/** \brief  Length for Ifx_CCU6_CLC_Bits.DISR */
#define IFX_CCU6_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CLC_Bits.DISR */
#define IFX_CCU6_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CLC_Bits.DISR */
#define IFX_CCU6_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_CCU6_CLC_Bits.DISS */
#define IFX_CCU6_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CLC_Bits.DISS */
#define IFX_CCU6_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CLC_Bits.DISS */
#define IFX_CCU6_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_CCU6_CLC_Bits.EDIS */
#define IFX_CCU6_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CLC_Bits.EDIS */
#define IFX_CCU6_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CLC_Bits.EDIS */
#define IFX_CCU6_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC60R */
#define IFX_CCU6_CMPMODIF_MCC60R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC60R */
#define IFX_CCU6_CMPMODIF_MCC60R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC60R */
#define IFX_CCU6_CMPMODIF_MCC60R_OFF (8u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC60S */
#define IFX_CCU6_CMPMODIF_MCC60S_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC60S */
#define IFX_CCU6_CMPMODIF_MCC60S_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC60S */
#define IFX_CCU6_CMPMODIF_MCC60S_OFF (0u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC61R */
#define IFX_CCU6_CMPMODIF_MCC61R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC61R */
#define IFX_CCU6_CMPMODIF_MCC61R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC61R */
#define IFX_CCU6_CMPMODIF_MCC61R_OFF (9u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC61S */
#define IFX_CCU6_CMPMODIF_MCC61S_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC61S */
#define IFX_CCU6_CMPMODIF_MCC61S_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC61S */
#define IFX_CCU6_CMPMODIF_MCC61S_OFF (1u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC62R */
#define IFX_CCU6_CMPMODIF_MCC62R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC62R */
#define IFX_CCU6_CMPMODIF_MCC62R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC62R */
#define IFX_CCU6_CMPMODIF_MCC62R_OFF (10u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC62S */
#define IFX_CCU6_CMPMODIF_MCC62S_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC62S */
#define IFX_CCU6_CMPMODIF_MCC62S_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC62S */
#define IFX_CCU6_CMPMODIF_MCC62S_OFF (2u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC63R */
#define IFX_CCU6_CMPMODIF_MCC63R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC63R */
#define IFX_CCU6_CMPMODIF_MCC63R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC63R */
#define IFX_CCU6_CMPMODIF_MCC63R_OFF (14u)

/** \brief  Length for Ifx_CCU6_CMPMODIF_Bits.MCC63S */
#define IFX_CCU6_CMPMODIF_MCC63S_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPMODIF_Bits.MCC63S */
#define IFX_CCU6_CMPMODIF_MCC63S_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPMODIF_Bits.MCC63S */
#define IFX_CCU6_CMPMODIF_MCC63S_OFF (6u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CC60PS */
#define IFX_CCU6_CMPSTAT_CC60PS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CC60PS */
#define IFX_CCU6_CMPSTAT_CC60PS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CC60PS */
#define IFX_CCU6_CMPSTAT_CC60PS_OFF (8u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CC60ST */
#define IFX_CCU6_CMPSTAT_CC60ST_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CC60ST */
#define IFX_CCU6_CMPSTAT_CC60ST_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CC60ST */
#define IFX_CCU6_CMPSTAT_CC60ST_OFF (0u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CC61PS */
#define IFX_CCU6_CMPSTAT_CC61PS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CC61PS */
#define IFX_CCU6_CMPSTAT_CC61PS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CC61PS */
#define IFX_CCU6_CMPSTAT_CC61PS_OFF (10u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CC61ST */
#define IFX_CCU6_CMPSTAT_CC61ST_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CC61ST */
#define IFX_CCU6_CMPSTAT_CC61ST_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CC61ST */
#define IFX_CCU6_CMPSTAT_CC61ST_OFF (1u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CC62PS */
#define IFX_CCU6_CMPSTAT_CC62PS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CC62PS */
#define IFX_CCU6_CMPSTAT_CC62PS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CC62PS */
#define IFX_CCU6_CMPSTAT_CC62PS_OFF (12u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CC62ST */
#define IFX_CCU6_CMPSTAT_CC62ST_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CC62ST */
#define IFX_CCU6_CMPSTAT_CC62ST_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CC62ST */
#define IFX_CCU6_CMPSTAT_CC62ST_OFF (2u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CC63ST */
#define IFX_CCU6_CMPSTAT_CC63ST_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CC63ST */
#define IFX_CCU6_CMPSTAT_CC63ST_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CC63ST */
#define IFX_CCU6_CMPSTAT_CC63ST_OFF (6u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CCPOS60 */
#define IFX_CCU6_CMPSTAT_CCPOS60_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CCPOS60 */
#define IFX_CCU6_CMPSTAT_CCPOS60_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CCPOS60 */
#define IFX_CCU6_CMPSTAT_CCPOS60_OFF (3u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CCPOS61 */
#define IFX_CCU6_CMPSTAT_CCPOS61_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CCPOS61 */
#define IFX_CCU6_CMPSTAT_CCPOS61_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CCPOS61 */
#define IFX_CCU6_CMPSTAT_CCPOS61_OFF (4u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.CCPOS62 */
#define IFX_CCU6_CMPSTAT_CCPOS62_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.CCPOS62 */
#define IFX_CCU6_CMPSTAT_CCPOS62_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.CCPOS62 */
#define IFX_CCU6_CMPSTAT_CCPOS62_OFF (5u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.COUT60PS */
#define IFX_CCU6_CMPSTAT_COUT60PS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.COUT60PS */
#define IFX_CCU6_CMPSTAT_COUT60PS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.COUT60PS */
#define IFX_CCU6_CMPSTAT_COUT60PS_OFF (9u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.COUT61PS */
#define IFX_CCU6_CMPSTAT_COUT61PS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.COUT61PS */
#define IFX_CCU6_CMPSTAT_COUT61PS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.COUT61PS */
#define IFX_CCU6_CMPSTAT_COUT61PS_OFF (11u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.COUT62PS */
#define IFX_CCU6_CMPSTAT_COUT62PS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.COUT62PS */
#define IFX_CCU6_CMPSTAT_COUT62PS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.COUT62PS */
#define IFX_CCU6_CMPSTAT_COUT62PS_OFF (13u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.COUT63PS */
#define IFX_CCU6_CMPSTAT_COUT63PS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.COUT63PS */
#define IFX_CCU6_CMPSTAT_COUT63PS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.COUT63PS */
#define IFX_CCU6_CMPSTAT_COUT63PS_OFF (14u)

/** \brief  Length for Ifx_CCU6_CMPSTAT_Bits.T13IM */
#define IFX_CCU6_CMPSTAT_T13IM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_CMPSTAT_Bits.T13IM */
#define IFX_CCU6_CMPSTAT_T13IM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_CMPSTAT_Bits.T13IM */
#define IFX_CCU6_CMPSTAT_T13IM_OFF (15u)

/** \brief  Length for Ifx_CCU6_ID_Bits.MODNUMBER */
#define IFX_CCU6_ID_MODNUMBER_LEN (8u)

/** \brief  Mask for Ifx_CCU6_ID_Bits.MODNUMBER */
#define IFX_CCU6_ID_MODNUMBER_MSK (0xffu)

/** \brief  Offset for Ifx_CCU6_ID_Bits.MODNUMBER */
#define IFX_CCU6_ID_MODNUMBER_OFF (8u)

/** \brief  Length for Ifx_CCU6_ID_Bits.MODREV */
#define IFX_CCU6_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_CCU6_ID_Bits.MODREV */
#define IFX_CCU6_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_CCU6_ID_Bits.MODREV */
#define IFX_CCU6_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENCC60F */
#define IFX_CCU6_IEN_ENCC60F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENCC60F */
#define IFX_CCU6_IEN_ENCC60F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENCC60F */
#define IFX_CCU6_IEN_ENCC60F_OFF (1u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENCC60R */
#define IFX_CCU6_IEN_ENCC60R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENCC60R */
#define IFX_CCU6_IEN_ENCC60R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENCC60R */
#define IFX_CCU6_IEN_ENCC60R_OFF (0u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENCC61F */
#define IFX_CCU6_IEN_ENCC61F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENCC61F */
#define IFX_CCU6_IEN_ENCC61F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENCC61F */
#define IFX_CCU6_IEN_ENCC61F_OFF (3u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENCC61R */
#define IFX_CCU6_IEN_ENCC61R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENCC61R */
#define IFX_CCU6_IEN_ENCC61R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENCC61R */
#define IFX_CCU6_IEN_ENCC61R_OFF (2u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENCC62F */
#define IFX_CCU6_IEN_ENCC62F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENCC62F */
#define IFX_CCU6_IEN_ENCC62F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENCC62F */
#define IFX_CCU6_IEN_ENCC62F_OFF (5u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENCC62R */
#define IFX_CCU6_IEN_ENCC62R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENCC62R */
#define IFX_CCU6_IEN_ENCC62R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENCC62R */
#define IFX_CCU6_IEN_ENCC62R_OFF (4u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENCHE */
#define IFX_CCU6_IEN_ENCHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENCHE */
#define IFX_CCU6_IEN_ENCHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENCHE */
#define IFX_CCU6_IEN_ENCHE_OFF (12u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENIDLE */
#define IFX_CCU6_IEN_ENIDLE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENIDLE */
#define IFX_CCU6_IEN_ENIDLE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENIDLE */
#define IFX_CCU6_IEN_ENIDLE_OFF (14u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENSTR */
#define IFX_CCU6_IEN_ENSTR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENSTR */
#define IFX_CCU6_IEN_ENSTR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENSTR */
#define IFX_CCU6_IEN_ENSTR_OFF (15u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENT12OM */
#define IFX_CCU6_IEN_ENT12OM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENT12OM */
#define IFX_CCU6_IEN_ENT12OM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENT12OM */
#define IFX_CCU6_IEN_ENT12OM_OFF (6u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENT12PM */
#define IFX_CCU6_IEN_ENT12PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENT12PM */
#define IFX_CCU6_IEN_ENT12PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENT12PM */
#define IFX_CCU6_IEN_ENT12PM_OFF (7u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENT13CM */
#define IFX_CCU6_IEN_ENT13CM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENT13CM */
#define IFX_CCU6_IEN_ENT13CM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENT13CM */
#define IFX_CCU6_IEN_ENT13CM_OFF (8u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENT13PM */
#define IFX_CCU6_IEN_ENT13PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENT13PM */
#define IFX_CCU6_IEN_ENT13PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENT13PM */
#define IFX_CCU6_IEN_ENT13PM_OFF (9u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENTRPF */
#define IFX_CCU6_IEN_ENTRPF_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENTRPF */
#define IFX_CCU6_IEN_ENTRPF_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENTRPF */
#define IFX_CCU6_IEN_ENTRPF_OFF (10u)

/** \brief  Length for Ifx_CCU6_IEN_Bits.ENWHE */
#define IFX_CCU6_IEN_ENWHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IEN_Bits.ENWHE */
#define IFX_CCU6_IEN_ENWHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IEN_Bits.ENWHE */
#define IFX_CCU6_IEN_ENWHE_OFF (13u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.CC60INI */
#define IFX_CCU6_IMON_CC60INI_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.CC60INI */
#define IFX_CCU6_IMON_CC60INI_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.CC60INI */
#define IFX_CCU6_IMON_CC60INI_OFF (4u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.CC61INI */
#define IFX_CCU6_IMON_CC61INI_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.CC61INI */
#define IFX_CCU6_IMON_CC61INI_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.CC61INI */
#define IFX_CCU6_IMON_CC61INI_OFF (5u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.CC62INI */
#define IFX_CCU6_IMON_CC62INI_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.CC62INI */
#define IFX_CCU6_IMON_CC62INI_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.CC62INI */
#define IFX_CCU6_IMON_CC62INI_OFF (6u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.CCPOS0I */
#define IFX_CCU6_IMON_CCPOS0I_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.CCPOS0I */
#define IFX_CCU6_IMON_CCPOS0I_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.CCPOS0I */
#define IFX_CCU6_IMON_CCPOS0I_OFF (1u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.CCPOS1I */
#define IFX_CCU6_IMON_CCPOS1I_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.CCPOS1I */
#define IFX_CCU6_IMON_CCPOS1I_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.CCPOS1I */
#define IFX_CCU6_IMON_CCPOS1I_OFF (2u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.CCPOS2I */
#define IFX_CCU6_IMON_CCPOS2I_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.CCPOS2I */
#define IFX_CCU6_IMON_CCPOS2I_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.CCPOS2I */
#define IFX_CCU6_IMON_CCPOS2I_OFF (3u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.CTRAPI */
#define IFX_CCU6_IMON_CTRAPI_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.CTRAPI */
#define IFX_CCU6_IMON_CTRAPI_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.CTRAPI */
#define IFX_CCU6_IMON_CTRAPI_OFF (7u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.LBE */
#define IFX_CCU6_IMON_LBE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.LBE */
#define IFX_CCU6_IMON_LBE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.LBE */
#define IFX_CCU6_IMON_LBE_OFF (0u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.T12HRI */
#define IFX_CCU6_IMON_T12HRI_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.T12HRI */
#define IFX_CCU6_IMON_T12HRI_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.T12HRI */
#define IFX_CCU6_IMON_T12HRI_OFF (8u)

/** \brief  Length for Ifx_CCU6_IMON_Bits.T13HRI */
#define IFX_CCU6_IMON_T13HRI_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IMON_Bits.T13HRI */
#define IFX_CCU6_IMON_T13HRI_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IMON_Bits.T13HRI */
#define IFX_CCU6_IMON_T13HRI_OFF (9u)

/** \brief  Length for Ifx_CCU6_INP_Bits.INPCC60 */
#define IFX_CCU6_INP_INPCC60_LEN (2u)

/** \brief  Mask for Ifx_CCU6_INP_Bits.INPCC60 */
#define IFX_CCU6_INP_INPCC60_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_INP_Bits.INPCC60 */
#define IFX_CCU6_INP_INPCC60_OFF (0u)

/** \brief  Length for Ifx_CCU6_INP_Bits.INPCC61 */
#define IFX_CCU6_INP_INPCC61_LEN (2u)

/** \brief  Mask for Ifx_CCU6_INP_Bits.INPCC61 */
#define IFX_CCU6_INP_INPCC61_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_INP_Bits.INPCC61 */
#define IFX_CCU6_INP_INPCC61_OFF (2u)

/** \brief  Length for Ifx_CCU6_INP_Bits.INPCC62 */
#define IFX_CCU6_INP_INPCC62_LEN (2u)

/** \brief  Mask for Ifx_CCU6_INP_Bits.INPCC62 */
#define IFX_CCU6_INP_INPCC62_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_INP_Bits.INPCC62 */
#define IFX_CCU6_INP_INPCC62_OFF (4u)

/** \brief  Length for Ifx_CCU6_INP_Bits.INPCHE */
#define IFX_CCU6_INP_INPCHE_LEN (2u)

/** \brief  Mask for Ifx_CCU6_INP_Bits.INPCHE */
#define IFX_CCU6_INP_INPCHE_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_INP_Bits.INPCHE */
#define IFX_CCU6_INP_INPCHE_OFF (6u)

/** \brief  Length for Ifx_CCU6_INP_Bits.INPERR */
#define IFX_CCU6_INP_INPERR_LEN (2u)

/** \brief  Mask for Ifx_CCU6_INP_Bits.INPERR */
#define IFX_CCU6_INP_INPERR_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_INP_Bits.INPERR */
#define IFX_CCU6_INP_INPERR_OFF (8u)

/** \brief  Length for Ifx_CCU6_INP_Bits.INPT12 */
#define IFX_CCU6_INP_INPT12_LEN (2u)

/** \brief  Mask for Ifx_CCU6_INP_Bits.INPT12 */
#define IFX_CCU6_INP_INPT12_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_INP_Bits.INPT12 */
#define IFX_CCU6_INP_INPT12_OFF (10u)

/** \brief  Length for Ifx_CCU6_INP_Bits.INPT13 */
#define IFX_CCU6_INP_INPT13_LEN (2u)

/** \brief  Mask for Ifx_CCU6_INP_Bits.INPT13 */
#define IFX_CCU6_INP_INPT13_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_INP_Bits.INPT13 */
#define IFX_CCU6_INP_INPT13_OFF (12u)

/** \brief  Length for Ifx_CCU6_IS_Bits.CHE */
#define IFX_CCU6_IS_CHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.CHE */
#define IFX_CCU6_IS_CHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.CHE */
#define IFX_CCU6_IS_CHE_OFF (12u)

/** \brief  Length for Ifx_CCU6_IS_Bits.ICC60F */
#define IFX_CCU6_IS_ICC60F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.ICC60F */
#define IFX_CCU6_IS_ICC60F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.ICC60F */
#define IFX_CCU6_IS_ICC60F_OFF (1u)

/** \brief  Length for Ifx_CCU6_IS_Bits.ICC60R */
#define IFX_CCU6_IS_ICC60R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.ICC60R */
#define IFX_CCU6_IS_ICC60R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.ICC60R */
#define IFX_CCU6_IS_ICC60R_OFF (0u)

/** \brief  Length for Ifx_CCU6_IS_Bits.ICC61F */
#define IFX_CCU6_IS_ICC61F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.ICC61F */
#define IFX_CCU6_IS_ICC61F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.ICC61F */
#define IFX_CCU6_IS_ICC61F_OFF (3u)

/** \brief  Length for Ifx_CCU6_IS_Bits.ICC61R */
#define IFX_CCU6_IS_ICC61R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.ICC61R */
#define IFX_CCU6_IS_ICC61R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.ICC61R */
#define IFX_CCU6_IS_ICC61R_OFF (2u)

/** \brief  Length for Ifx_CCU6_IS_Bits.ICC62F */
#define IFX_CCU6_IS_ICC62F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.ICC62F */
#define IFX_CCU6_IS_ICC62F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.ICC62F */
#define IFX_CCU6_IS_ICC62F_OFF (5u)

/** \brief  Length for Ifx_CCU6_IS_Bits.ICC62R */
#define IFX_CCU6_IS_ICC62R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.ICC62R */
#define IFX_CCU6_IS_ICC62R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.ICC62R */
#define IFX_CCU6_IS_ICC62R_OFF (4u)

/** \brief  Length for Ifx_CCU6_IS_Bits.IDLE */
#define IFX_CCU6_IS_IDLE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.IDLE */
#define IFX_CCU6_IS_IDLE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.IDLE */
#define IFX_CCU6_IS_IDLE_OFF (14u)

/** \brief  Length for Ifx_CCU6_IS_Bits.STR */
#define IFX_CCU6_IS_STR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.STR */
#define IFX_CCU6_IS_STR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.STR */
#define IFX_CCU6_IS_STR_OFF (15u)

/** \brief  Length for Ifx_CCU6_IS_Bits.T12OM */
#define IFX_CCU6_IS_T12OM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.T12OM */
#define IFX_CCU6_IS_T12OM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.T12OM */
#define IFX_CCU6_IS_T12OM_OFF (6u)

/** \brief  Length for Ifx_CCU6_IS_Bits.T12PM */
#define IFX_CCU6_IS_T12PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.T12PM */
#define IFX_CCU6_IS_T12PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.T12PM */
#define IFX_CCU6_IS_T12PM_OFF (7u)

/** \brief  Length for Ifx_CCU6_IS_Bits.T13CM */
#define IFX_CCU6_IS_T13CM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.T13CM */
#define IFX_CCU6_IS_T13CM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.T13CM */
#define IFX_CCU6_IS_T13CM_OFF (8u)

/** \brief  Length for Ifx_CCU6_IS_Bits.T13PM */
#define IFX_CCU6_IS_T13PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.T13PM */
#define IFX_CCU6_IS_T13PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.T13PM */
#define IFX_CCU6_IS_T13PM_OFF (9u)

/** \brief  Length for Ifx_CCU6_IS_Bits.TRPF */
#define IFX_CCU6_IS_TRPF_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.TRPF */
#define IFX_CCU6_IS_TRPF_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.TRPF */
#define IFX_CCU6_IS_TRPF_OFF (10u)

/** \brief  Length for Ifx_CCU6_IS_Bits.TRPS */
#define IFX_CCU6_IS_TRPS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.TRPS */
#define IFX_CCU6_IS_TRPS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.TRPS */
#define IFX_CCU6_IS_TRPS_OFF (11u)

/** \brief  Length for Ifx_CCU6_IS_Bits.WHE */
#define IFX_CCU6_IS_WHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_IS_Bits.WHE */
#define IFX_CCU6_IS_WHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_IS_Bits.WHE */
#define IFX_CCU6_IS_WHE_OFF (13u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RCC60F */
#define IFX_CCU6_ISR_RCC60F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RCC60F */
#define IFX_CCU6_ISR_RCC60F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RCC60F */
#define IFX_CCU6_ISR_RCC60F_OFF (1u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RCC60R */
#define IFX_CCU6_ISR_RCC60R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RCC60R */
#define IFX_CCU6_ISR_RCC60R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RCC60R */
#define IFX_CCU6_ISR_RCC60R_OFF (0u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RCC61F */
#define IFX_CCU6_ISR_RCC61F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RCC61F */
#define IFX_CCU6_ISR_RCC61F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RCC61F */
#define IFX_CCU6_ISR_RCC61F_OFF (3u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RCC61R */
#define IFX_CCU6_ISR_RCC61R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RCC61R */
#define IFX_CCU6_ISR_RCC61R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RCC61R */
#define IFX_CCU6_ISR_RCC61R_OFF (2u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RCC62F */
#define IFX_CCU6_ISR_RCC62F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RCC62F */
#define IFX_CCU6_ISR_RCC62F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RCC62F */
#define IFX_CCU6_ISR_RCC62F_OFF (5u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RCC62R */
#define IFX_CCU6_ISR_RCC62R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RCC62R */
#define IFX_CCU6_ISR_RCC62R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RCC62R */
#define IFX_CCU6_ISR_RCC62R_OFF (4u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RCHE */
#define IFX_CCU6_ISR_RCHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RCHE */
#define IFX_CCU6_ISR_RCHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RCHE */
#define IFX_CCU6_ISR_RCHE_OFF (12u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RIDLE */
#define IFX_CCU6_ISR_RIDLE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RIDLE */
#define IFX_CCU6_ISR_RIDLE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RIDLE */
#define IFX_CCU6_ISR_RIDLE_OFF (14u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RSTR */
#define IFX_CCU6_ISR_RSTR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RSTR */
#define IFX_CCU6_ISR_RSTR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RSTR */
#define IFX_CCU6_ISR_RSTR_OFF (15u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RT12OM */
#define IFX_CCU6_ISR_RT12OM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RT12OM */
#define IFX_CCU6_ISR_RT12OM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RT12OM */
#define IFX_CCU6_ISR_RT12OM_OFF (6u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RT12PM */
#define IFX_CCU6_ISR_RT12PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RT12PM */
#define IFX_CCU6_ISR_RT12PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RT12PM */
#define IFX_CCU6_ISR_RT12PM_OFF (7u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RT13CM */
#define IFX_CCU6_ISR_RT13CM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RT13CM */
#define IFX_CCU6_ISR_RT13CM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RT13CM */
#define IFX_CCU6_ISR_RT13CM_OFF (8u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RT13PM */
#define IFX_CCU6_ISR_RT13PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RT13PM */
#define IFX_CCU6_ISR_RT13PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RT13PM */
#define IFX_CCU6_ISR_RT13PM_OFF (9u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RTRPF */
#define IFX_CCU6_ISR_RTRPF_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RTRPF */
#define IFX_CCU6_ISR_RTRPF_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RTRPF */
#define IFX_CCU6_ISR_RTRPF_OFF (10u)

/** \brief  Length for Ifx_CCU6_ISR_Bits.RWHE */
#define IFX_CCU6_ISR_RWHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISR_Bits.RWHE */
#define IFX_CCU6_ISR_RWHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISR_Bits.RWHE */
#define IFX_CCU6_ISR_RWHE_OFF (13u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SCC60F */
#define IFX_CCU6_ISS_SCC60F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SCC60F */
#define IFX_CCU6_ISS_SCC60F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SCC60F */
#define IFX_CCU6_ISS_SCC60F_OFF (1u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SCC60R */
#define IFX_CCU6_ISS_SCC60R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SCC60R */
#define IFX_CCU6_ISS_SCC60R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SCC60R */
#define IFX_CCU6_ISS_SCC60R_OFF (0u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SCC61F */
#define IFX_CCU6_ISS_SCC61F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SCC61F */
#define IFX_CCU6_ISS_SCC61F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SCC61F */
#define IFX_CCU6_ISS_SCC61F_OFF (3u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SCC61R */
#define IFX_CCU6_ISS_SCC61R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SCC61R */
#define IFX_CCU6_ISS_SCC61R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SCC61R */
#define IFX_CCU6_ISS_SCC61R_OFF (2u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SCC62F */
#define IFX_CCU6_ISS_SCC62F_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SCC62F */
#define IFX_CCU6_ISS_SCC62F_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SCC62F */
#define IFX_CCU6_ISS_SCC62F_OFF (5u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SCC62R */
#define IFX_CCU6_ISS_SCC62R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SCC62R */
#define IFX_CCU6_ISS_SCC62R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SCC62R */
#define IFX_CCU6_ISS_SCC62R_OFF (4u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SCHE */
#define IFX_CCU6_ISS_SCHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SCHE */
#define IFX_CCU6_ISS_SCHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SCHE */
#define IFX_CCU6_ISS_SCHE_OFF (12u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SIDLE */
#define IFX_CCU6_ISS_SIDLE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SIDLE */
#define IFX_CCU6_ISS_SIDLE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SIDLE */
#define IFX_CCU6_ISS_SIDLE_OFF (14u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SSTR */
#define IFX_CCU6_ISS_SSTR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SSTR */
#define IFX_CCU6_ISS_SSTR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SSTR */
#define IFX_CCU6_ISS_SSTR_OFF (15u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.ST12OM */
#define IFX_CCU6_ISS_ST12OM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.ST12OM */
#define IFX_CCU6_ISS_ST12OM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.ST12OM */
#define IFX_CCU6_ISS_ST12OM_OFF (6u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.ST12PM */
#define IFX_CCU6_ISS_ST12PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.ST12PM */
#define IFX_CCU6_ISS_ST12PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.ST12PM */
#define IFX_CCU6_ISS_ST12PM_OFF (7u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.ST13CM */
#define IFX_CCU6_ISS_ST13CM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.ST13CM */
#define IFX_CCU6_ISS_ST13CM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.ST13CM */
#define IFX_CCU6_ISS_ST13CM_OFF (8u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.ST13PM */
#define IFX_CCU6_ISS_ST13PM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.ST13PM */
#define IFX_CCU6_ISS_ST13PM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.ST13PM */
#define IFX_CCU6_ISS_ST13PM_OFF (9u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.STRPF */
#define IFX_CCU6_ISS_STRPF_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.STRPF */
#define IFX_CCU6_ISS_STRPF_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.STRPF */
#define IFX_CCU6_ISS_STRPF_OFF (10u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SWHC */
#define IFX_CCU6_ISS_SWHC_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SWHC */
#define IFX_CCU6_ISS_SWHC_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SWHC */
#define IFX_CCU6_ISS_SWHC_OFF (11u)

/** \brief  Length for Ifx_CCU6_ISS_Bits.SWHE */
#define IFX_CCU6_ISS_SWHE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_ISS_Bits.SWHE */
#define IFX_CCU6_ISS_SWHE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_ISS_Bits.SWHE */
#define IFX_CCU6_ISS_SWHE_OFF (13u)

/** \brief  Length for Ifx_CCU6_KRST0_Bits.RST */
#define IFX_CCU6_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KRST0_Bits.RST */
#define IFX_CCU6_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KRST0_Bits.RST */
#define IFX_CCU6_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_CCU6_KRST0_Bits.RSTSTAT */
#define IFX_CCU6_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KRST0_Bits.RSTSTAT */
#define IFX_CCU6_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KRST0_Bits.RSTSTAT */
#define IFX_CCU6_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_CCU6_KRST1_Bits.RST */
#define IFX_CCU6_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KRST1_Bits.RST */
#define IFX_CCU6_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KRST1_Bits.RST */
#define IFX_CCU6_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_CCU6_KRSTCLR_Bits.CLR */
#define IFX_CCU6_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KRSTCLR_Bits.CLR */
#define IFX_CCU6_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KRSTCLR_Bits.CLR */
#define IFX_CCU6_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_CCU6_KSCSR_Bits.SB0 */
#define IFX_CCU6_KSCSR_SB0_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KSCSR_Bits.SB0 */
#define IFX_CCU6_KSCSR_SB0_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KSCSR_Bits.SB0 */
#define IFX_CCU6_KSCSR_SB0_OFF (0u)

/** \brief  Length for Ifx_CCU6_KSCSR_Bits.SB1 */
#define IFX_CCU6_KSCSR_SB1_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KSCSR_Bits.SB1 */
#define IFX_CCU6_KSCSR_SB1_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KSCSR_Bits.SB1 */
#define IFX_CCU6_KSCSR_SB1_OFF (1u)

/** \brief  Length for Ifx_CCU6_KSCSR_Bits.SB2 */
#define IFX_CCU6_KSCSR_SB2_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KSCSR_Bits.SB2 */
#define IFX_CCU6_KSCSR_SB2_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KSCSR_Bits.SB2 */
#define IFX_CCU6_KSCSR_SB2_OFF (2u)

/** \brief  Length for Ifx_CCU6_KSCSR_Bits.SB3 */
#define IFX_CCU6_KSCSR_SB3_LEN (1u)

/** \brief  Mask for Ifx_CCU6_KSCSR_Bits.SB3 */
#define IFX_CCU6_KSCSR_SB3_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_KSCSR_Bits.SB3 */
#define IFX_CCU6_KSCSR_SB3_OFF (3u)

/** \brief  Length for Ifx_CCU6_LI_Bits.CC60INEN */
#define IFX_CCU6_LI_CC60INEN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.CC60INEN */
#define IFX_CCU6_LI_CC60INEN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.CC60INEN */
#define IFX_CCU6_LI_CC60INEN_OFF (4u)

/** \brief  Length for Ifx_CCU6_LI_Bits.CC61INEN */
#define IFX_CCU6_LI_CC61INEN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.CC61INEN */
#define IFX_CCU6_LI_CC61INEN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.CC61INEN */
#define IFX_CCU6_LI_CC61INEN_OFF (5u)

/** \brief  Length for Ifx_CCU6_LI_Bits.CC62INEN */
#define IFX_CCU6_LI_CC62INEN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.CC62INEN */
#define IFX_CCU6_LI_CC62INEN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.CC62INEN */
#define IFX_CCU6_LI_CC62INEN_OFF (6u)

/** \brief  Length for Ifx_CCU6_LI_Bits.CCPOS0EN */
#define IFX_CCU6_LI_CCPOS0EN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.CCPOS0EN */
#define IFX_CCU6_LI_CCPOS0EN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.CCPOS0EN */
#define IFX_CCU6_LI_CCPOS0EN_OFF (1u)

/** \brief  Length for Ifx_CCU6_LI_Bits.CCPOS1EN */
#define IFX_CCU6_LI_CCPOS1EN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.CCPOS1EN */
#define IFX_CCU6_LI_CCPOS1EN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.CCPOS1EN */
#define IFX_CCU6_LI_CCPOS1EN_OFF (2u)

/** \brief  Length for Ifx_CCU6_LI_Bits.CCPOS2EN */
#define IFX_CCU6_LI_CCPOS2EN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.CCPOS2EN */
#define IFX_CCU6_LI_CCPOS2EN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.CCPOS2EN */
#define IFX_CCU6_LI_CCPOS2EN_OFF (3u)

/** \brief  Length for Ifx_CCU6_LI_Bits.CTRAPEN */
#define IFX_CCU6_LI_CTRAPEN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.CTRAPEN */
#define IFX_CCU6_LI_CTRAPEN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.CTRAPEN */
#define IFX_CCU6_LI_CTRAPEN_OFF (7u)

/** \brief  Length for Ifx_CCU6_LI_Bits.INPLBE */
#define IFX_CCU6_LI_INPLBE_LEN (2u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.INPLBE */
#define IFX_CCU6_LI_INPLBE_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.INPLBE */
#define IFX_CCU6_LI_INPLBE_OFF (14u)

/** \brief  Length for Ifx_CCU6_LI_Bits.LBEEN */
#define IFX_CCU6_LI_LBEEN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.LBEEN */
#define IFX_CCU6_LI_LBEEN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.LBEEN */
#define IFX_CCU6_LI_LBEEN_OFF (13u)

/** \brief  Length for Ifx_CCU6_LI_Bits.T12HREN */
#define IFX_CCU6_LI_T12HREN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.T12HREN */
#define IFX_CCU6_LI_T12HREN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.T12HREN */
#define IFX_CCU6_LI_T12HREN_OFF (8u)

/** \brief  Length for Ifx_CCU6_LI_Bits.T13HREN */
#define IFX_CCU6_LI_T13HREN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_LI_Bits.T13HREN */
#define IFX_CCU6_LI_T13HREN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_LI_Bits.T13HREN */
#define IFX_CCU6_LI_T13HREN_OFF (9u)

/** \brief  Length for Ifx_CCU6_MCFG_Bits.MCM */
#define IFX_CCU6_MCFG_MCM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCFG_Bits.MCM */
#define IFX_CCU6_MCFG_MCM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCFG_Bits.MCM */
#define IFX_CCU6_MCFG_MCM_OFF (2u)

/** \brief  Length for Ifx_CCU6_MCFG_Bits.T12 */
#define IFX_CCU6_MCFG_T12_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCFG_Bits.T12 */
#define IFX_CCU6_MCFG_T12_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCFG_Bits.T12 */
#define IFX_CCU6_MCFG_T12_OFF (0u)

/** \brief  Length for Ifx_CCU6_MCFG_Bits.T13 */
#define IFX_CCU6_MCFG_T13_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCFG_Bits.T13 */
#define IFX_CCU6_MCFG_T13_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCFG_Bits.T13 */
#define IFX_CCU6_MCFG_T13_OFF (1u)

/** \brief  Length for Ifx_CCU6_MCMCTR_Bits.STE12D */
#define IFX_CCU6_MCMCTR_STE12D_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCMCTR_Bits.STE12D */
#define IFX_CCU6_MCMCTR_STE12D_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCMCTR_Bits.STE12D */
#define IFX_CCU6_MCMCTR_STE12D_OFF (9u)

/** \brief  Length for Ifx_CCU6_MCMCTR_Bits.STE12U */
#define IFX_CCU6_MCMCTR_STE12U_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCMCTR_Bits.STE12U */
#define IFX_CCU6_MCMCTR_STE12U_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCMCTR_Bits.STE12U */
#define IFX_CCU6_MCMCTR_STE12U_OFF (8u)

/** \brief  Length for Ifx_CCU6_MCMCTR_Bits.STE13U */
#define IFX_CCU6_MCMCTR_STE13U_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCMCTR_Bits.STE13U */
#define IFX_CCU6_MCMCTR_STE13U_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCMCTR_Bits.STE13U */
#define IFX_CCU6_MCMCTR_STE13U_OFF (10u)

/** \brief  Length for Ifx_CCU6_MCMCTR_Bits.SWSEL */
#define IFX_CCU6_MCMCTR_SWSEL_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MCMCTR_Bits.SWSEL */
#define IFX_CCU6_MCMCTR_SWSEL_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MCMCTR_Bits.SWSEL */
#define IFX_CCU6_MCMCTR_SWSEL_OFF (0u)

/** \brief  Length for Ifx_CCU6_MCMCTR_Bits.SWSYN */
#define IFX_CCU6_MCMCTR_SWSYN_LEN (2u)

/** \brief  Mask for Ifx_CCU6_MCMCTR_Bits.SWSYN */
#define IFX_CCU6_MCMCTR_SWSYN_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_MCMCTR_Bits.SWSYN */
#define IFX_CCU6_MCMCTR_SWSYN_OFF (4u)

/** \brief  Length for Ifx_CCU6_MCMOUT_Bits.CURH */
#define IFX_CCU6_MCMOUT_CURH_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MCMOUT_Bits.CURH */
#define IFX_CCU6_MCMOUT_CURH_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MCMOUT_Bits.CURH */
#define IFX_CCU6_MCMOUT_CURH_OFF (11u)

/** \brief  Length for Ifx_CCU6_MCMOUT_Bits.EXPH */
#define IFX_CCU6_MCMOUT_EXPH_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MCMOUT_Bits.EXPH */
#define IFX_CCU6_MCMOUT_EXPH_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MCMOUT_Bits.EXPH */
#define IFX_CCU6_MCMOUT_EXPH_OFF (8u)

/** \brief  Length for Ifx_CCU6_MCMOUT_Bits.MCMP */
#define IFX_CCU6_MCMOUT_MCMP_LEN (6u)

/** \brief  Mask for Ifx_CCU6_MCMOUT_Bits.MCMP */
#define IFX_CCU6_MCMOUT_MCMP_MSK (0x3fu)

/** \brief  Offset for Ifx_CCU6_MCMOUT_Bits.MCMP */
#define IFX_CCU6_MCMOUT_MCMP_OFF (0u)

/** \brief  Length for Ifx_CCU6_MCMOUT_Bits.R */
#define IFX_CCU6_MCMOUT_R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCMOUT_Bits.R */
#define IFX_CCU6_MCMOUT_R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCMOUT_Bits.R */
#define IFX_CCU6_MCMOUT_R_OFF (6u)

/** \brief  Length for Ifx_CCU6_MCMOUTS_Bits.CURHS */
#define IFX_CCU6_MCMOUTS_CURHS_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MCMOUTS_Bits.CURHS */
#define IFX_CCU6_MCMOUTS_CURHS_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MCMOUTS_Bits.CURHS */
#define IFX_CCU6_MCMOUTS_CURHS_OFF (11u)

/** \brief  Length for Ifx_CCU6_MCMOUTS_Bits.EXPHS */
#define IFX_CCU6_MCMOUTS_EXPHS_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MCMOUTS_Bits.EXPHS */
#define IFX_CCU6_MCMOUTS_EXPHS_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MCMOUTS_Bits.EXPHS */
#define IFX_CCU6_MCMOUTS_EXPHS_OFF (8u)

/** \brief  Length for Ifx_CCU6_MCMOUTS_Bits.MCMPS */
#define IFX_CCU6_MCMOUTS_MCMPS_LEN (6u)

/** \brief  Mask for Ifx_CCU6_MCMOUTS_Bits.MCMPS */
#define IFX_CCU6_MCMOUTS_MCMPS_MSK (0x3fu)

/** \brief  Offset for Ifx_CCU6_MCMOUTS_Bits.MCMPS */
#define IFX_CCU6_MCMOUTS_MCMPS_OFF (0u)

/** \brief  Length for Ifx_CCU6_MCMOUTS_Bits.STRHP */
#define IFX_CCU6_MCMOUTS_STRHP_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCMOUTS_Bits.STRHP */
#define IFX_CCU6_MCMOUTS_STRHP_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCMOUTS_Bits.STRHP */
#define IFX_CCU6_MCMOUTS_STRHP_OFF (15u)

/** \brief  Length for Ifx_CCU6_MCMOUTS_Bits.STRMCM */
#define IFX_CCU6_MCMOUTS_STRMCM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MCMOUTS_Bits.STRMCM */
#define IFX_CCU6_MCMOUTS_STRMCM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MCMOUTS_Bits.STRMCM */
#define IFX_CCU6_MCMOUTS_STRMCM_OFF (7u)

/** \brief  Length for Ifx_CCU6_MODCTR_Bits.ECT13O */
#define IFX_CCU6_MODCTR_ECT13O_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MODCTR_Bits.ECT13O */
#define IFX_CCU6_MODCTR_ECT13O_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MODCTR_Bits.ECT13O */
#define IFX_CCU6_MODCTR_ECT13O_OFF (15u)

/** \brief  Length for Ifx_CCU6_MODCTR_Bits.MCMEN */
#define IFX_CCU6_MODCTR_MCMEN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_MODCTR_Bits.MCMEN */
#define IFX_CCU6_MODCTR_MCMEN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_MODCTR_Bits.MCMEN */
#define IFX_CCU6_MODCTR_MCMEN_OFF (7u)

/** \brief  Length for Ifx_CCU6_MODCTR_Bits.T12MODEN */
#define IFX_CCU6_MODCTR_T12MODEN_LEN (6u)

/** \brief  Mask for Ifx_CCU6_MODCTR_Bits.T12MODEN */
#define IFX_CCU6_MODCTR_T12MODEN_MSK (0x3fu)

/** \brief  Offset for Ifx_CCU6_MODCTR_Bits.T12MODEN */
#define IFX_CCU6_MODCTR_T12MODEN_OFF (0u)

/** \brief  Length for Ifx_CCU6_MODCTR_Bits.T13MODEN */
#define IFX_CCU6_MODCTR_T13MODEN_LEN (6u)

/** \brief  Mask for Ifx_CCU6_MODCTR_Bits.T13MODEN */
#define IFX_CCU6_MODCTR_T13MODEN_MSK (0x3fu)

/** \brief  Offset for Ifx_CCU6_MODCTR_Bits.T13MODEN */
#define IFX_CCU6_MODCTR_T13MODEN_OFF (8u)

/** \brief  Length for Ifx_CCU6_MOSEL_Bits.TRIG0SEL */
#define IFX_CCU6_MOSEL_TRIG0SEL_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MOSEL_Bits.TRIG0SEL */
#define IFX_CCU6_MOSEL_TRIG0SEL_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MOSEL_Bits.TRIG0SEL */
#define IFX_CCU6_MOSEL_TRIG0SEL_OFF (0u)

/** \brief  Length for Ifx_CCU6_MOSEL_Bits.TRIG1SEL */
#define IFX_CCU6_MOSEL_TRIG1SEL_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MOSEL_Bits.TRIG1SEL */
#define IFX_CCU6_MOSEL_TRIG1SEL_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MOSEL_Bits.TRIG1SEL */
#define IFX_CCU6_MOSEL_TRIG1SEL_OFF (3u)

/** \brief  Length for Ifx_CCU6_MOSEL_Bits.TRIG2SEL */
#define IFX_CCU6_MOSEL_TRIG2SEL_LEN (3u)

/** \brief  Mask for Ifx_CCU6_MOSEL_Bits.TRIG2SEL */
#define IFX_CCU6_MOSEL_TRIG2SEL_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_MOSEL_Bits.TRIG2SEL */
#define IFX_CCU6_MOSEL_TRIG2SEL_OFF (6u)

/** \brief  Length for Ifx_CCU6_OCS_Bits.SUS */
#define IFX_CCU6_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_CCU6_OCS_Bits.SUS */
#define IFX_CCU6_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_CCU6_OCS_Bits.SUS */
#define IFX_CCU6_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_CCU6_OCS_Bits.SUS_P */
#define IFX_CCU6_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_CCU6_OCS_Bits.SUS_P */
#define IFX_CCU6_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_OCS_Bits.SUS_P */
#define IFX_CCU6_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_CCU6_OCS_Bits.SUSSTA */
#define IFX_CCU6_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_CCU6_OCS_Bits.SUSSTA */
#define IFX_CCU6_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_OCS_Bits.SUSSTA */
#define IFX_CCU6_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_CCU6_OCS_Bits.TG_P */
#define IFX_CCU6_OCS_TG_P_LEN (1u)

/** \brief  Mask for Ifx_CCU6_OCS_Bits.TG_P */
#define IFX_CCU6_OCS_TG_P_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_OCS_Bits.TG_P */
#define IFX_CCU6_OCS_TG_P_OFF (3u)

/** \brief  Length for Ifx_CCU6_OCS_Bits.TGB */
#define IFX_CCU6_OCS_TGB_LEN (1u)

/** \brief  Mask for Ifx_CCU6_OCS_Bits.TGB */
#define IFX_CCU6_OCS_TGB_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_OCS_Bits.TGB */
#define IFX_CCU6_OCS_TGB_OFF (2u)

/** \brief  Length for Ifx_CCU6_OCS_Bits.TGS */
#define IFX_CCU6_OCS_TGS_LEN (2u)

/** \brief  Mask for Ifx_CCU6_OCS_Bits.TGS */
#define IFX_CCU6_OCS_TGS_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_OCS_Bits.TGS */
#define IFX_CCU6_OCS_TGS_OFF (0u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.ISCC60 */
#define IFX_CCU6_PISEL0_ISCC60_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.ISCC60 */
#define IFX_CCU6_PISEL0_ISCC60_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.ISCC60 */
#define IFX_CCU6_PISEL0_ISCC60_OFF (0u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.ISCC61 */
#define IFX_CCU6_PISEL0_ISCC61_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.ISCC61 */
#define IFX_CCU6_PISEL0_ISCC61_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.ISCC61 */
#define IFX_CCU6_PISEL0_ISCC61_OFF (2u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.ISCC62 */
#define IFX_CCU6_PISEL0_ISCC62_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.ISCC62 */
#define IFX_CCU6_PISEL0_ISCC62_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.ISCC62 */
#define IFX_CCU6_PISEL0_ISCC62_OFF (4u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.ISPOS0 */
#define IFX_CCU6_PISEL0_ISPOS0_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.ISPOS0 */
#define IFX_CCU6_PISEL0_ISPOS0_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.ISPOS0 */
#define IFX_CCU6_PISEL0_ISPOS0_OFF (8u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.ISPOS1 */
#define IFX_CCU6_PISEL0_ISPOS1_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.ISPOS1 */
#define IFX_CCU6_PISEL0_ISPOS1_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.ISPOS1 */
#define IFX_CCU6_PISEL0_ISPOS1_OFF (10u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.ISPOS2 */
#define IFX_CCU6_PISEL0_ISPOS2_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.ISPOS2 */
#define IFX_CCU6_PISEL0_ISPOS2_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.ISPOS2 */
#define IFX_CCU6_PISEL0_ISPOS2_OFF (12u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.IST12HR */
#define IFX_CCU6_PISEL0_IST12HR_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.IST12HR */
#define IFX_CCU6_PISEL0_IST12HR_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.IST12HR */
#define IFX_CCU6_PISEL0_IST12HR_OFF (14u)

/** \brief  Length for Ifx_CCU6_PISEL0_Bits.ISTRP */
#define IFX_CCU6_PISEL0_ISTRP_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL0_Bits.ISTRP */
#define IFX_CCU6_PISEL0_ISTRP_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL0_Bits.ISTRP */
#define IFX_CCU6_PISEL0_ISTRP_OFF (6u)

/** \brief  Length for Ifx_CCU6_PISEL2_Bits.ISCNT12 */
#define IFX_CCU6_PISEL2_ISCNT12_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL2_Bits.ISCNT12 */
#define IFX_CCU6_PISEL2_ISCNT12_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL2_Bits.ISCNT12 */
#define IFX_CCU6_PISEL2_ISCNT12_OFF (2u)

/** \brief  Length for Ifx_CCU6_PISEL2_Bits.ISCNT13 */
#define IFX_CCU6_PISEL2_ISCNT13_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL2_Bits.ISCNT13 */
#define IFX_CCU6_PISEL2_ISCNT13_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL2_Bits.ISCNT13 */
#define IFX_CCU6_PISEL2_ISCNT13_OFF (4u)

/** \brief  Length for Ifx_CCU6_PISEL2_Bits.IST13HR */
#define IFX_CCU6_PISEL2_IST13HR_LEN (2u)

/** \brief  Mask for Ifx_CCU6_PISEL2_Bits.IST13HR */
#define IFX_CCU6_PISEL2_IST13HR_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_PISEL2_Bits.IST13HR */
#define IFX_CCU6_PISEL2_IST13HR_OFF (0u)

/** \brief  Length for Ifx_CCU6_PISEL2_Bits.T12EXT */
#define IFX_CCU6_PISEL2_T12EXT_LEN (1u)

/** \brief  Mask for Ifx_CCU6_PISEL2_Bits.T12EXT */
#define IFX_CCU6_PISEL2_T12EXT_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_PISEL2_Bits.T12EXT */
#define IFX_CCU6_PISEL2_T12EXT_OFF (6u)

/** \brief  Length for Ifx_CCU6_PISEL2_Bits.T13EXT */
#define IFX_CCU6_PISEL2_T13EXT_LEN (1u)

/** \brief  Mask for Ifx_CCU6_PISEL2_Bits.T13EXT */
#define IFX_CCU6_PISEL2_T13EXT_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_PISEL2_Bits.T13EXT */
#define IFX_CCU6_PISEL2_T13EXT_OFF (7u)

/** \brief  Length for Ifx_CCU6_PSLR_Bits.PSL63 */
#define IFX_CCU6_PSLR_PSL63_LEN (1u)

/** \brief  Mask for Ifx_CCU6_PSLR_Bits.PSL63 */
#define IFX_CCU6_PSLR_PSL63_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_PSLR_Bits.PSL63 */
#define IFX_CCU6_PSLR_PSL63_OFF (7u)

/** \brief  Length for Ifx_CCU6_PSLR_Bits.PSL */
#define IFX_CCU6_PSLR_PSL_LEN (6u)

/** \brief  Mask for Ifx_CCU6_PSLR_Bits.PSL */
#define IFX_CCU6_PSLR_PSL_MSK (0x3fu)

/** \brief  Offset for Ifx_CCU6_PSLR_Bits.PSL */
#define IFX_CCU6_PSLR_PSL_OFF (0u)

/** \brief  Length for Ifx_CCU6_T12_Bits.T12CV */
#define IFX_CCU6_T12_T12CV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_T12_Bits.T12CV */
#define IFX_CCU6_T12_T12CV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_T12_Bits.T12CV */
#define IFX_CCU6_T12_T12CV_OFF (0u)

/** \brief  Length for Ifx_CCU6_T12DTC_Bits.DTE0 */
#define IFX_CCU6_T12DTC_DTE0_LEN (1u)

/** \brief  Mask for Ifx_CCU6_T12DTC_Bits.DTE0 */
#define IFX_CCU6_T12DTC_DTE0_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_T12DTC_Bits.DTE0 */
#define IFX_CCU6_T12DTC_DTE0_OFF (8u)

/** \brief  Length for Ifx_CCU6_T12DTC_Bits.DTE1 */
#define IFX_CCU6_T12DTC_DTE1_LEN (1u)

/** \brief  Mask for Ifx_CCU6_T12DTC_Bits.DTE1 */
#define IFX_CCU6_T12DTC_DTE1_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_T12DTC_Bits.DTE1 */
#define IFX_CCU6_T12DTC_DTE1_OFF (9u)

/** \brief  Length for Ifx_CCU6_T12DTC_Bits.DTE2 */
#define IFX_CCU6_T12DTC_DTE2_LEN (1u)

/** \brief  Mask for Ifx_CCU6_T12DTC_Bits.DTE2 */
#define IFX_CCU6_T12DTC_DTE2_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_T12DTC_Bits.DTE2 */
#define IFX_CCU6_T12DTC_DTE2_OFF (10u)

/** \brief  Length for Ifx_CCU6_T12DTC_Bits.DTM */
#define IFX_CCU6_T12DTC_DTM_LEN (8u)

/** \brief  Mask for Ifx_CCU6_T12DTC_Bits.DTM */
#define IFX_CCU6_T12DTC_DTM_MSK (0xffu)

/** \brief  Offset for Ifx_CCU6_T12DTC_Bits.DTM */
#define IFX_CCU6_T12DTC_DTM_OFF (0u)

/** \brief  Length for Ifx_CCU6_T12DTC_Bits.DTR0 */
#define IFX_CCU6_T12DTC_DTR0_LEN (1u)

/** \brief  Mask for Ifx_CCU6_T12DTC_Bits.DTR0 */
#define IFX_CCU6_T12DTC_DTR0_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_T12DTC_Bits.DTR0 */
#define IFX_CCU6_T12DTC_DTR0_OFF (12u)

/** \brief  Length for Ifx_CCU6_T12DTC_Bits.DTR1 */
#define IFX_CCU6_T12DTC_DTR1_LEN (1u)

/** \brief  Mask for Ifx_CCU6_T12DTC_Bits.DTR1 */
#define IFX_CCU6_T12DTC_DTR1_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_T12DTC_Bits.DTR1 */
#define IFX_CCU6_T12DTC_DTR1_OFF (13u)

/** \brief  Length for Ifx_CCU6_T12DTC_Bits.DTR2 */
#define IFX_CCU6_T12DTC_DTR2_LEN (1u)

/** \brief  Mask for Ifx_CCU6_T12DTC_Bits.DTR2 */
#define IFX_CCU6_T12DTC_DTR2_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_T12DTC_Bits.DTR2 */
#define IFX_CCU6_T12DTC_DTR2_OFF (14u)

/** \brief  Length for Ifx_CCU6_T12MSEL_Bits.DBYP */
#define IFX_CCU6_T12MSEL_DBYP_LEN (1u)

/** \brief  Mask for Ifx_CCU6_T12MSEL_Bits.DBYP */
#define IFX_CCU6_T12MSEL_DBYP_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_T12MSEL_Bits.DBYP */
#define IFX_CCU6_T12MSEL_DBYP_OFF (15u)

/** \brief  Length for Ifx_CCU6_T12MSEL_Bits.HSYNC */
#define IFX_CCU6_T12MSEL_HSYNC_LEN (3u)

/** \brief  Mask for Ifx_CCU6_T12MSEL_Bits.HSYNC */
#define IFX_CCU6_T12MSEL_HSYNC_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_T12MSEL_Bits.HSYNC */
#define IFX_CCU6_T12MSEL_HSYNC_OFF (12u)

/** \brief  Length for Ifx_CCU6_T12MSEL_Bits.MSEL60 */
#define IFX_CCU6_T12MSEL_MSEL60_LEN (4u)

/** \brief  Mask for Ifx_CCU6_T12MSEL_Bits.MSEL60 */
#define IFX_CCU6_T12MSEL_MSEL60_MSK (0xfu)

/** \brief  Offset for Ifx_CCU6_T12MSEL_Bits.MSEL60 */
#define IFX_CCU6_T12MSEL_MSEL60_OFF (0u)

/** \brief  Length for Ifx_CCU6_T12MSEL_Bits.MSEL61 */
#define IFX_CCU6_T12MSEL_MSEL61_LEN (4u)

/** \brief  Mask for Ifx_CCU6_T12MSEL_Bits.MSEL61 */
#define IFX_CCU6_T12MSEL_MSEL61_MSK (0xfu)

/** \brief  Offset for Ifx_CCU6_T12MSEL_Bits.MSEL61 */
#define IFX_CCU6_T12MSEL_MSEL61_OFF (4u)

/** \brief  Length for Ifx_CCU6_T12MSEL_Bits.MSEL62 */
#define IFX_CCU6_T12MSEL_MSEL62_LEN (4u)

/** \brief  Mask for Ifx_CCU6_T12MSEL_Bits.MSEL62 */
#define IFX_CCU6_T12MSEL_MSEL62_MSK (0xfu)

/** \brief  Offset for Ifx_CCU6_T12MSEL_Bits.MSEL62 */
#define IFX_CCU6_T12MSEL_MSEL62_OFF (8u)

/** \brief  Length for Ifx_CCU6_T12PR_Bits.T12PV */
#define IFX_CCU6_T12PR_T12PV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_T12PR_Bits.T12PV */
#define IFX_CCU6_T12PR_T12PV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_T12PR_Bits.T12PV */
#define IFX_CCU6_T12PR_T12PV_OFF (0u)

/** \brief  Length for Ifx_CCU6_T13_Bits.T13CV */
#define IFX_CCU6_T13_T13CV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_T13_Bits.T13CV */
#define IFX_CCU6_T13_T13CV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_T13_Bits.T13CV */
#define IFX_CCU6_T13_T13CV_OFF (0u)

/** \brief  Length for Ifx_CCU6_T13PR_Bits.T13PV */
#define IFX_CCU6_T13PR_T13PV_LEN (16u)

/** \brief  Mask for Ifx_CCU6_T13PR_Bits.T13PV */
#define IFX_CCU6_T13PR_T13PV_MSK (0xffffu)

/** \brief  Offset for Ifx_CCU6_T13PR_Bits.T13PV */
#define IFX_CCU6_T13PR_T13PV_OFF (0u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.CDIR */
#define IFX_CCU6_TCTR0_CDIR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.CDIR */
#define IFX_CCU6_TCTR0_CDIR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.CDIR */
#define IFX_CCU6_TCTR0_CDIR_OFF (6u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.CTM */
#define IFX_CCU6_TCTR0_CTM_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.CTM */
#define IFX_CCU6_TCTR0_CTM_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.CTM */
#define IFX_CCU6_TCTR0_CTM_OFF (7u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.STE12 */
#define IFX_CCU6_TCTR0_STE12_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.STE12 */
#define IFX_CCU6_TCTR0_STE12_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.STE12 */
#define IFX_CCU6_TCTR0_STE12_OFF (5u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.STE13 */
#define IFX_CCU6_TCTR0_STE13_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.STE13 */
#define IFX_CCU6_TCTR0_STE13_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.STE13 */
#define IFX_CCU6_TCTR0_STE13_OFF (13u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.T12CLK */
#define IFX_CCU6_TCTR0_T12CLK_LEN (3u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.T12CLK */
#define IFX_CCU6_TCTR0_T12CLK_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.T12CLK */
#define IFX_CCU6_TCTR0_T12CLK_OFF (0u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.T12PRE */
#define IFX_CCU6_TCTR0_T12PRE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.T12PRE */
#define IFX_CCU6_TCTR0_T12PRE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.T12PRE */
#define IFX_CCU6_TCTR0_T12PRE_OFF (3u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.T12R */
#define IFX_CCU6_TCTR0_T12R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.T12R */
#define IFX_CCU6_TCTR0_T12R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.T12R */
#define IFX_CCU6_TCTR0_T12R_OFF (4u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.T13CLK */
#define IFX_CCU6_TCTR0_T13CLK_LEN (3u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.T13CLK */
#define IFX_CCU6_TCTR0_T13CLK_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.T13CLK */
#define IFX_CCU6_TCTR0_T13CLK_OFF (8u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.T13PRE */
#define IFX_CCU6_TCTR0_T13PRE_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.T13PRE */
#define IFX_CCU6_TCTR0_T13PRE_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.T13PRE */
#define IFX_CCU6_TCTR0_T13PRE_OFF (11u)

/** \brief  Length for Ifx_CCU6_TCTR0_Bits.T13R */
#define IFX_CCU6_TCTR0_T13R_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR0_Bits.T13R */
#define IFX_CCU6_TCTR0_T13R_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR0_Bits.T13R */
#define IFX_CCU6_TCTR0_T13R_OFF (12u)

/** \brief  Length for Ifx_CCU6_TCTR2_Bits.T12RSEL */
#define IFX_CCU6_TCTR2_T12RSEL_LEN (2u)

/** \brief  Mask for Ifx_CCU6_TCTR2_Bits.T12RSEL */
#define IFX_CCU6_TCTR2_T12RSEL_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_TCTR2_Bits.T12RSEL */
#define IFX_CCU6_TCTR2_T12RSEL_OFF (8u)

/** \brief  Length for Ifx_CCU6_TCTR2_Bits.T12SSC */
#define IFX_CCU6_TCTR2_T12SSC_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR2_Bits.T12SSC */
#define IFX_CCU6_TCTR2_T12SSC_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR2_Bits.T12SSC */
#define IFX_CCU6_TCTR2_T12SSC_OFF (0u)

/** \brief  Length for Ifx_CCU6_TCTR2_Bits.T13RSEL */
#define IFX_CCU6_TCTR2_T13RSEL_LEN (2u)

/** \brief  Mask for Ifx_CCU6_TCTR2_Bits.T13RSEL */
#define IFX_CCU6_TCTR2_T13RSEL_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_TCTR2_Bits.T13RSEL */
#define IFX_CCU6_TCTR2_T13RSEL_OFF (10u)

/** \brief  Length for Ifx_CCU6_TCTR2_Bits.T13SSC */
#define IFX_CCU6_TCTR2_T13SSC_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR2_Bits.T13SSC */
#define IFX_CCU6_TCTR2_T13SSC_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR2_Bits.T13SSC */
#define IFX_CCU6_TCTR2_T13SSC_OFF (1u)

/** \brief  Length for Ifx_CCU6_TCTR2_Bits.T13TEC */
#define IFX_CCU6_TCTR2_T13TEC_LEN (3u)

/** \brief  Mask for Ifx_CCU6_TCTR2_Bits.T13TEC */
#define IFX_CCU6_TCTR2_T13TEC_MSK (0x7u)

/** \brief  Offset for Ifx_CCU6_TCTR2_Bits.T13TEC */
#define IFX_CCU6_TCTR2_T13TEC_OFF (2u)

/** \brief  Length for Ifx_CCU6_TCTR2_Bits.T13TED */
#define IFX_CCU6_TCTR2_T13TED_LEN (2u)

/** \brief  Mask for Ifx_CCU6_TCTR2_Bits.T13TED */
#define IFX_CCU6_TCTR2_T13TED_MSK (0x3u)

/** \brief  Offset for Ifx_CCU6_TCTR2_Bits.T13TED */
#define IFX_CCU6_TCTR2_T13TED_OFF (5u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.DTRES */
#define IFX_CCU6_TCTR4_DTRES_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.DTRES */
#define IFX_CCU6_TCTR4_DTRES_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.DTRES */
#define IFX_CCU6_TCTR4_DTRES_OFF (3u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T12CNT */
#define IFX_CCU6_TCTR4_T12CNT_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T12CNT */
#define IFX_CCU6_TCTR4_T12CNT_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T12CNT */
#define IFX_CCU6_TCTR4_T12CNT_OFF (5u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T12RES */
#define IFX_CCU6_TCTR4_T12RES_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T12RES */
#define IFX_CCU6_TCTR4_T12RES_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T12RES */
#define IFX_CCU6_TCTR4_T12RES_OFF (2u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T12RR */
#define IFX_CCU6_TCTR4_T12RR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T12RR */
#define IFX_CCU6_TCTR4_T12RR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T12RR */
#define IFX_CCU6_TCTR4_T12RR_OFF (0u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T12RS */
#define IFX_CCU6_TCTR4_T12RS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T12RS */
#define IFX_CCU6_TCTR4_T12RS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T12RS */
#define IFX_CCU6_TCTR4_T12RS_OFF (1u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T12STD */
#define IFX_CCU6_TCTR4_T12STD_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T12STD */
#define IFX_CCU6_TCTR4_T12STD_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T12STD */
#define IFX_CCU6_TCTR4_T12STD_OFF (7u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T12STR */
#define IFX_CCU6_TCTR4_T12STR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T12STR */
#define IFX_CCU6_TCTR4_T12STR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T12STR */
#define IFX_CCU6_TCTR4_T12STR_OFF (6u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T13CNT */
#define IFX_CCU6_TCTR4_T13CNT_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T13CNT */
#define IFX_CCU6_TCTR4_T13CNT_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T13CNT */
#define IFX_CCU6_TCTR4_T13CNT_OFF (13u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T13RES */
#define IFX_CCU6_TCTR4_T13RES_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T13RES */
#define IFX_CCU6_TCTR4_T13RES_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T13RES */
#define IFX_CCU6_TCTR4_T13RES_OFF (10u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T13RR */
#define IFX_CCU6_TCTR4_T13RR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T13RR */
#define IFX_CCU6_TCTR4_T13RR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T13RR */
#define IFX_CCU6_TCTR4_T13RR_OFF (8u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T13RS */
#define IFX_CCU6_TCTR4_T13RS_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T13RS */
#define IFX_CCU6_TCTR4_T13RS_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T13RS */
#define IFX_CCU6_TCTR4_T13RS_OFF (9u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T13STD */
#define IFX_CCU6_TCTR4_T13STD_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T13STD */
#define IFX_CCU6_TCTR4_T13STD_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T13STD */
#define IFX_CCU6_TCTR4_T13STD_OFF (15u)

/** \brief  Length for Ifx_CCU6_TCTR4_Bits.T13STR */
#define IFX_CCU6_TCTR4_T13STR_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TCTR4_Bits.T13STR */
#define IFX_CCU6_TCTR4_T13STR_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TCTR4_Bits.T13STR */
#define IFX_CCU6_TCTR4_T13STR_OFF (14u)

/** \brief  Length for Ifx_CCU6_TRPCTR_Bits.TRPEN13 */
#define IFX_CCU6_TRPCTR_TRPEN13_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TRPCTR_Bits.TRPEN13 */
#define IFX_CCU6_TRPCTR_TRPEN13_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TRPCTR_Bits.TRPEN13 */
#define IFX_CCU6_TRPCTR_TRPEN13_OFF (14u)

/** \brief  Length for Ifx_CCU6_TRPCTR_Bits.TRPEN */
#define IFX_CCU6_TRPCTR_TRPEN_LEN (6u)

/** \brief  Mask for Ifx_CCU6_TRPCTR_Bits.TRPEN */
#define IFX_CCU6_TRPCTR_TRPEN_MSK (0x3fu)

/** \brief  Offset for Ifx_CCU6_TRPCTR_Bits.TRPEN */
#define IFX_CCU6_TRPCTR_TRPEN_OFF (8u)

/** \brief  Length for Ifx_CCU6_TRPCTR_Bits.TRPM0 */
#define IFX_CCU6_TRPCTR_TRPM0_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TRPCTR_Bits.TRPM0 */
#define IFX_CCU6_TRPCTR_TRPM0_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TRPCTR_Bits.TRPM0 */
#define IFX_CCU6_TRPCTR_TRPM0_OFF (0u)

/** \brief  Length for Ifx_CCU6_TRPCTR_Bits.TRPM1 */
#define IFX_CCU6_TRPCTR_TRPM1_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TRPCTR_Bits.TRPM1 */
#define IFX_CCU6_TRPCTR_TRPM1_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TRPCTR_Bits.TRPM1 */
#define IFX_CCU6_TRPCTR_TRPM1_OFF (1u)

/** \brief  Length for Ifx_CCU6_TRPCTR_Bits.TRPM2 */
#define IFX_CCU6_TRPCTR_TRPM2_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TRPCTR_Bits.TRPM2 */
#define IFX_CCU6_TRPCTR_TRPM2_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TRPCTR_Bits.TRPM2 */
#define IFX_CCU6_TRPCTR_TRPM2_OFF (2u)

/** \brief  Length for Ifx_CCU6_TRPCTR_Bits.TRPPEN */
#define IFX_CCU6_TRPCTR_TRPPEN_LEN (1u)

/** \brief  Mask for Ifx_CCU6_TRPCTR_Bits.TRPPEN */
#define IFX_CCU6_TRPCTR_TRPPEN_MSK (0x1u)

/** \brief  Offset for Ifx_CCU6_TRPCTR_Bits.TRPPEN */
#define IFX_CCU6_TRPCTR_TRPPEN_OFF (15u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXCCU6_BF_H */
