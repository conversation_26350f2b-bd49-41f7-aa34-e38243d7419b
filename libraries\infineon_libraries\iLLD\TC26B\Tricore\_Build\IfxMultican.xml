<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxMultican" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Port/Std/IfxPort.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxPort_cfg.c</iLLD:file>
  <iLLD:file class="mchal">Multican/Std/IfxMultican.c</iLLD:file>
  <iLLD:file class="mchal">Multican/Can/IfxMultican_Can.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxMultican_cfg.c</iLLD:file>
  <iLLD:file class="mchal">_PinMap/IfxMultican_PinMap.c</iLLD:file>
</iLLD:filelist>
