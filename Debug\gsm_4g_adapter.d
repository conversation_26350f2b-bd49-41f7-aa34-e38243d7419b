gsm_4g_adapter.o :	../code/user1/gsm_4g_adapter.c
../code/user1/gsm_4g_adapter.c :
gsm_4g_adapter.o :	..\code\user1\gsm_4g_adapter.h
..\code\user1\gsm_4g_adapter.h :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\ifxAsclin_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\ifxAsclin_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\Ifx_TypesReg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\Ifx_TypesReg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_Intrinsics.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_Intrinsics.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Platform_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Platform_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_TypesTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_TypesTasking.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_IntrinsicsTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_IntrinsicsTasking.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCpu_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCpu_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCpu_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxSrc_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxStm_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxScu_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxScu_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_bf.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_bf.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.asm.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.asm.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxPort_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxPort_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxPort_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Src\Std\IfxSrc.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Src\Std\IfxSrc.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxSrc_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxSrc_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Stm\Std\IfxStm.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Stm\Std\IfxStm.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxStm_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxStm_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Timer\IfxCcu6_Timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Timer\IfxCcu6_Timer.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Ccu6\Std\IfxCcu6.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Ccu6\Std\IfxCcu6.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxCcu6_bf.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\_Utilities\Ifx_Assert.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxCcu6_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxCcu6_PinMap.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxCcu6_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\If\Ccu6If\Timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\If\Ccu6If\Timer.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxScu_PinMap.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h" :
gsm_4g_adapter.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_clock.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_clock.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_debug.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_debug.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_interrupt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_interrupt.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_fifo.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_fifo.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_font.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_font.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_function.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_function.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\user\isr_config.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\user\isr_config.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_adc.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_adc.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_delay.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_delay.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_dma.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_dma.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Std\IfxDma.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Std\IfxDma.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxDma_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxDma_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_bf.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxDma_regdef.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuEru.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_encoder.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_encoder.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_exti.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_flash.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_flash.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Impl\ifxFlash_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Impl\ifxFlash_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_gpio.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_gpio.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Port\\Std\IFXPORT.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Port\\Std\IFXPORT.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pit.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pit.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pwm.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_pwm.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_iic.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_iic.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_spi.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_spi.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_spi.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_soft_spi.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Asc\ifxAsclin_Asc.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Asclin\\Asc\ifxAsclin_Asc.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Asclin\Std\IfxAsclin.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Asclin\Std\IfxAsclin.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxAsclin_reg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuCcu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Scu\Std\IfxScuWdt.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxAsclin_PinMap.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_PinMap\IfxAsclin_PinMap.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxAsclin_cfg.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Port\Std\IfxPort.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Lib\DataHandling\Ifx_Fifo.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Lib\DataHandling\Ifx_Fifo.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\SysSe\Bsp\Bsp.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf_DPipe.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf_DPipe.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Service\\CpuGeneric\StdIf\IfxStdIf.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_absolute_encoder.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_absolute_encoder.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ble6a20.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ble6a20.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_bluetooth_ch9141.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_bluetooth_ch9141.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_gnss.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_gnss.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_camera.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_camera.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_type.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_type.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1a.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1a.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1b.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_dl1b.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_icm20602.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_icm20602.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu660ra.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu660ra.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu963ra.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_imu963ra.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips114.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips114.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips200.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ips200.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_key.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_key.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mpu6050.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mpu6050.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mt9v03x.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_mt9v03x.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_oled.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_oled.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ov7725.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_ov7725.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_scc8660.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_scc8660.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tft180.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tft180.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tsl1401.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_tsl1401.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_uart_receiver.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_uart_receiver.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_virtual_oscilloscope.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_virtual_oscilloscope.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_uart.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_spi.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wifi_spi.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wireless_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_device\zf_device_wireless_uart.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant_interface.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_components\seekfree_assistant_interface.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\camera.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\camera.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Device.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Device.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_headfile.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\encoder.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\encoder.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Flash.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\Flash.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\gps.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\gps.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\imu.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\imu.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\PID.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\PID.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\quaternion.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\quaternion.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\remote.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\remote.h" :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\user_display_gps.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\code\user_display_gps.h" :
gsm_4g_adapter.o :	..\code\user1\u1_config.h
..\code\user1\u1_config.h :
gsm_4g_adapter.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_uart.h" :
