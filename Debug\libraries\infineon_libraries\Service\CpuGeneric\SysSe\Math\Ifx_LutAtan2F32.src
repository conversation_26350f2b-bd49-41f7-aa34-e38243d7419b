	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc16848a --dep-file=Ifx_LutAtan2F32.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c'

	
$TC16X
	
	.sdecl	'.text.Ifx_LutAtan2F32.Ifx_LutAtan2F32_init',code,cluster('Ifx_LutAtan2F32_init')
	.sect	'.text.Ifx_LutAtan2F32.Ifx_LutAtan2F32_init'
	.align	2
	
	.global	Ifx_LutAtan2F32_init
; Function Ifx_LutAtan2F32_init
.L57:
Ifx_LutAtan2F32_init:	.type	func
	ret
.L81:
	
__Ifx_LutAtan2F32_init_function_end:
	.size	Ifx_LutAtan2F32_init,__Ifx_LutAtan2F32_init_function_end-Ifx_LutAtan2F32_init
.L70:
	; End of function
	
	.sdecl	'.text.Ifx_LutAtan2F32.Ifx_LutAtan2F32_fxpAngle',code,cluster('Ifx_LutAtan2F32_fxpAngle')
	.sect	'.text.Ifx_LutAtan2F32.Ifx_LutAtan2F32_fxpAngle'
	.align	2
	
	.global	Ifx_LutAtan2F32_fxpAngle
; Function Ifx_LutAtan2F32_fxpAngle
.L59:
Ifx_LutAtan2F32_fxpAngle:	.type	func
	mov	d15,#0
.L173:
	cmp.f	d15,d5,d15
	extr.u	d15,d15,#0,#1
.L174:
	jeq	d15,#0,.L2
.L175:
	mov	d15,#0
.L176:
	cmp.f	d15,d4,d15
	extr.u	d15,d15,#0,#1
.L177:
	jeq	d15,#0,.L3
.L178:
	insn.t	d15,d5:31,d5:31
.L179:
	insn.t	d0,d4:31,d4:31
.L180:
	cmp.f	d15,d15,d0
	extr.u	d15,d15,#0,#1
.L181:
	jeq	d15,#0,.L4
.L182:
	insn.t	d15,d5:31,d5:31
.L183:
	insn.t	d0,d4:31,d4:31
.L184:
	div.f	d15,d15,d0
.L91:
	movh	d0,#17536
.L185:
	mul.f	d15,d15,d0
.L186:
	ftouz	d8,d15
.L129:
	utof	d0,d8
.L187:
	sub.f	d4,d15,d0
.L127:
	call	__f_ftod
.L128:
	mov	e4,d3,d2
.L188:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L189:
	call	__d_fgt
.L190:
	jeq	d2,#0,.L5
.L191:
	add	d8,#1
.L5:
	mul	d15,d8,#4
.L192:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L193:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L194:
	j	.L6
.L6:
	addi	d15,d15,#-2048
.L130:
	j	.L7
.L4:
	insn.t	d15,d4:31,d4:31
.L195:
	insn.t	d0,d5:31,d5:31
.L196:
	div.f	d15,d15,d0
.L97:
	movh	d0,#17536
.L197:
	mul.f	d15,d15,d0
.L198:
	ftouz	d8,d15
.L133:
	utof	d0,d8
.L199:
	sub.f	d4,d15,d0
.L131:
	call	__f_ftod
.L132:
	mov	e4,d3,d2
.L200:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L201:
	call	__d_fgt
.L202:
	jeq	d2,#0,.L8
.L203:
	add	d8,#1
.L8:
	mul	d15,d8,#4
.L204:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L205:
	addsc.a	a15,a15,d15,#0
	ld.w	d0,[a15]
.L206:
	j	.L9
.L9:
	mov	d15,#-1024
.L134:
	sub	d15,d0
.L7:
	j	.L10
.L3:
	insn.t	d15,d5:31,d5:31
.L207:
	cmp.f	d15,d15,d4
	extr.u	d15,d15,#0,#1
.L208:
	jeq	d15,#0,.L11
.L209:
	insn.t	d15,d5:31,d5:31
.L210:
	div.f	d15,d15,d4
.L99:
	movh	d0,#17536
.L211:
	mul.f	d15,d15,d0
.L212:
	ftouz	d8,d15
.L137:
	utof	d0,d8
.L213:
	sub.f	d4,d15,d0
.L135:
	call	__f_ftod
.L136:
	mov	e4,d3,d2
.L214:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L215:
	call	__d_fgt
.L216:
	jeq	d2,#0,.L12
.L217:
	add	d8,#1
.L12:
	mul	d15,d8,#4
.L218:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L219:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L138:
	j	.L13
.L13:
	rsub	d15,#0
.L220:
	j	.L14
.L11:
	insn.t	d15,d5:31,d5:31
.L221:
	div.f	d15,d4,d15
.L101:
	movh	d0,#17536
.L222:
	mul.f	d15,d15,d0
.L223:
	ftouz	d8,d15
.L141:
	utof	d0,d8
.L224:
	sub.f	d4,d15,d0
.L139:
	call	__f_ftod
.L140:
	mov	e4,d3,d2
.L225:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L226:
	call	__d_fgt
.L227:
	jeq	d2,#0,.L15
.L228:
	add	d8,#1
.L15:
	mul	d15,d8,#4
.L229:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L230:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L231:
	j	.L16
.L16:
	addi	d15,d15,#-1024
.L14:
.L10:
	j	.L17
.L2:
	mov	d15,#0
.L232:
	cmp.f	d15,d4,d15
	extr.u	d15,d15,#0,#1
.L233:
	jeq	d15,#0,.L18
.L234:
	insn.t	d15,d4:31,d4:31
.L235:
	cmp.f	d15,d5,d15
	extr.u	d15,d15,#0,#1
.L236:
	jeq	d15,#0,.L19
.L237:
	insn.t	d15,d4:31,d4:31
.L238:
	div.f	d15,d5,d15
.L103:
	movh	d0,#17536
.L239:
	mul.f	d15,d15,d0
.L240:
	ftouz	d8,d15
.L144:
	utof	d0,d8
.L241:
	sub.f	d4,d15,d0
.L142:
	call	__f_ftod
.L143:
	mov	e4,d3,d2
.L242:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L243:
	call	__d_fgt
.L244:
	jeq	d2,#0,.L20
.L245:
	add	d8,#1
.L20:
	mul	d15,d8,#4
.L246:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L247:
	addsc.a	a15,a15,d15,#0
	ld.w	d0,[a15]
.L248:
	j	.L21
.L21:
	mov	d15,#2048
.L145:
	sub	d15,d0
.L249:
	j	.L22
.L19:
	insn.t	d15,d4:31,d4:31
.L250:
	div.f	d15,d15,d5
.L105:
	movh	d0,#17536
.L251:
	mul.f	d15,d15,d0
.L252:
	ftouz	d8,d15
.L148:
	utof	d0,d8
.L253:
	sub.f	d4,d15,d0
.L146:
	call	__f_ftod
.L147:
	mov	e4,d3,d2
.L254:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L255:
	call	__d_fgt
.L256:
	jeq	d2,#0,.L23
.L257:
	add	d8,#1
.L23:
	mul	d15,d8,#4
.L258:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L259:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L260:
	j	.L24
.L24:
	addi	d15,d15,#1024
.L22:
	j	.L25
.L18:
	cmp.f	d15,d5,d4
	extr.u	d15,d15,#0,#1
.L261:
	jeq	d15,#0,.L26
.L262:
	div.f	d15,d5,d4
.L107:
	movh	d0,#17536
.L263:
	mul.f	d15,d15,d0
.L264:
	ftouz	d8,d15
.L151:
	utof	d0,d8
.L265:
	sub.f	d4,d15,d0
.L149:
	call	__f_ftod
.L150:
	mov	e4,d3,d2
.L266:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L267:
	call	__d_fgt
.L268:
	jeq	d2,#0,.L27
.L269:
	add	d8,#1
.L27:
	mul	d15,d8,#4
.L270:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L271:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L152:
	j	.L28
.L28:
	j	.L29
.L26:
	div.f	d15,d4,d5
.L109:
	movh	d0,#17536
.L272:
	mul.f	d15,d15,d0
.L273:
	ftouz	d8,d15
.L155:
	utof	d0,d8
.L274:
	sub.f	d4,d15,d0
.L153:
	call	__f_ftod
.L154:
	mov	e4,d3,d2
.L275:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L276:
	call	__d_fgt
.L277:
	jeq	d2,#0,.L30
.L278:
	add	d8,#1
.L30:
	mul	d15,d8,#4
.L279:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_FxpAngle_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_FxpAngle_table)
.L280:
	addsc.a	a15,a15,d15,#0
	ld.w	d0,[a15]
.L281:
	j	.L31
.L31:
	mov	d15,#1024
.L156:
	sub	d15,d0
.L29:
.L25:
.L17:
	insert	d2,d15,#0,#12,#20
.L282:
	j	.L32
.L32:
	ret
.L83:
	
__Ifx_LutAtan2F32_fxpAngle_function_end:
	.size	Ifx_LutAtan2F32_fxpAngle,__Ifx_LutAtan2F32_fxpAngle_function_end-Ifx_LutAtan2F32_fxpAngle
.L75:
	; End of function
	
	.sdecl	'.text.Ifx_LutAtan2F32.Ifx_LutAtan2F32_float32',code,cluster('Ifx_LutAtan2F32_float32')
	.sect	'.text.Ifx_LutAtan2F32.Ifx_LutAtan2F32_float32'
	.align	2
	
	.global	Ifx_LutAtan2F32_float32
; Function Ifx_LutAtan2F32_float32
.L61:
Ifx_LutAtan2F32_float32:	.type	func
	mov	d15,#0
.L287:
	cmp.f	d15,d4,d15
	extr.u	d15,d15,#0,#1
.L288:
	jeq	d15,#0,.L33
.L289:
	mov	d15,#0
.L290:
	cmp.f	d15,d5,d15
	extr.u	d15,d15,#0,#1
.L291:
	jeq	d15,#0,.L34
.L292:
	insn.t	d15,d4:31,d4:31
.L293:
	insn.t	d0,d5:31,d5:31
.L294:
	cmp.f	d15,d15,d0
	extr.u	d15,d15,#0,#1
.L295:
	jeq	d15,#0,.L35
.L296:
	insn.t	d15,d4:31,d4:31
.L297:
	insn.t	d0,d5:31,d5:31
.L298:
	div.f	d15,d15,d0
.L116:
	movh	d0,#17536
.L299:
	mul.f	d15,d15,d0
.L300:
	ftoiz	d15,d15
.L301:
	mul	d15,d15,#4
.L302:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L303:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L304:
	j	.L36
.L36:
	mov	d0,#4059
	addih	d0,d0,#49225
.L305:
	add.f	d2,d15,d0
.L157:
	j	.L37
.L35:
	insn.t	d15,d5:31,d5:31
.L306:
	insn.t	d0,d4:31,d4:31
.L307:
	div.f	d15,d15,d0
.L120:
	movh	d0,#17536
.L308:
	mul.f	d15,d15,d0
.L309:
	ftoiz	d15,d15
.L310:
	mul	d15,d15,#4
.L311:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L312:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L313:
	j	.L38
.L38:
	mov	d0,#4059
	addih	d0,d0,#49097
.L314:
	sub.f	d2,d0,d15
.L37:
	j	.L39
.L34:
	insn.t	d15,d4:31,d4:31
.L315:
	cmp.f	d15,d15,d5
	extr.u	d15,d15,#0,#1
.L316:
	jeq	d15,#0,.L40
.L317:
	insn.t	d15,d4:31,d4:31
.L318:
	div.f	d15,d15,d5
.L121:
	movh	d0,#17536
.L319:
	mul.f	d15,d15,d0
.L320:
	ftoiz	d15,d15
.L321:
	mul	d15,d15,#4
.L322:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L323:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L324:
	j	.L41
.L41:
	insn.t	d2,d15:31,d15:31
.L158:
	j	.L42
.L40:
	insn.t	d15,d4:31,d4:31
.L325:
	div.f	d15,d5,d15
.L122:
	movh	d0,#17536
.L326:
	mul.f	d15,d15,d0
.L327:
	ftoiz	d15,d15
.L328:
	mul	d15,d15,#4
.L329:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L330:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L331:
	j	.L43
.L43:
	mov	d0,#4059
	addih	d0,d0,#49097
.L332:
	add.f	d2,d15,d0
.L42:
.L39:
	j	.L44
.L33:
	mov	d15,#0
.L333:
	cmp.f	d15,d5,d15
	extr.u	d15,d15,#0,#1
.L334:
	jeq	d15,#0,.L45
.L335:
	insn.t	d15,d5:31,d5:31
.L336:
	cmp.f	d15,d4,d15
	extr.u	d15,d15,#0,#1
.L337:
	jeq	d15,#0,.L46
.L338:
	insn.t	d15,d5:31,d5:31
.L339:
	div.f	d15,d4,d15
.L123:
	movh	d0,#17536
.L340:
	mul.f	d15,d15,d0
.L341:
	ftoiz	d15,d15
.L342:
	mul	d15,d15,#4
.L343:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L344:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L345:
	j	.L47
.L47:
	mov	d0,#4059
	addih	d0,d0,#16457
.L346:
	sub.f	d2,d0,d15
.L159:
	j	.L48
.L46:
	insn.t	d15,d5:31,d5:31
.L347:
	div.f	d15,d15,d4
.L124:
	movh	d0,#17536
.L348:
	mul.f	d15,d15,d0
.L349:
	ftoiz	d15,d15
.L350:
	mul	d15,d15,#4
.L351:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L352:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L353:
	j	.L49
.L49:
	mov	d0,#4059
	addih	d0,d0,#16329
.L354:
	add.f	d2,d15,d0
.L48:
	j	.L50
.L45:
	cmp.f	d15,d4,d5
	extr.u	d15,d15,#0,#1
.L355:
	jeq	d15,#0,.L51
.L356:
	div.f	d15,d4,d5
.L125:
	movh	d0,#17536
.L357:
	mul.f	d15,d15,d0
.L358:
	ftoiz	d15,d15
.L359:
	mul	d15,d15,#4
.L360:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L361:
	addsc.a	a15,a15,d15,#0
	ld.w	d2,[a15]
.L160:
	j	.L52
.L52:
	j	.L53
.L51:
	div.f	d15,d5,d4
.L126:
	movh	d0,#17536
.L362:
	mul.f	d15,d15,d0
.L363:
	ftoiz	d15,d15
.L364:
	mul	d15,d15,#4
.L365:
	movh.a	a15,#@his(Ifx_g_LutAtan2F32_table)
	lea	a15,[a15]@los(Ifx_g_LutAtan2F32_table)
.L366:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L367:
	j	.L54
.L54:
	mov	d0,#4059
	addih	d0,d0,#16329
.L368:
	sub.f	d2,d0,d15
.L53:
.L50:
.L44:
	j	.L55
.L55:
	ret
.L111:
	
__Ifx_LutAtan2F32_float32_function_end:
	.size	Ifx_LutAtan2F32_float32,__Ifx_LutAtan2F32_float32_function_end-Ifx_LutAtan2F32_float32
.L80:
	; End of function
	
	.calls	'Ifx_LutAtan2F32_fxpAngle','__f_ftod'
	.calls	'Ifx_LutAtan2F32_fxpAngle','__d_fgt'
	.calls	'Ifx_LutAtan2F32_init','',0
	.calls	'Ifx_LutAtan2F32_fxpAngle','',0
	.extern	Ifx_g_LutAtan2F32_FxpAngle_table
	.extern	Ifx_g_LutAtan2F32_table
	.extern	__f_ftod
	.extern	__d_fgt
	.calls	'Ifx_LutAtan2F32_float32','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L63:
	.word	1159
	.half	3
	.word	.L64
	.byte	4
.L62:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L65
	.byte	2,1,1,3
	.word	242
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	245
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L84:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	290
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	302
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	382
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	356
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	388
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	388
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	356
	.byte	6,0
.L82:
	.byte	7
	.byte	'long int',0,4,5
.L90:
	.byte	8
	.byte	'Ifx_LutAtan2F32_fxpAnglePrivate',0,3,3,66,29
	.word	474
	.byte	1,1
.L92:
	.byte	5
	.byte	'valf',0,3,66,69
	.word	302
.L94:
	.byte	6,0
.L115:
	.byte	8
	.byte	'Ifx_LutAtan2F32_float32Private',0,3,3,81,20
	.word	302
	.byte	1,1
.L117:
	.byte	5
	.byte	'val',0,3,81,59
	.word	302
.L119:
	.byte	6,0,10
	.word	250
	.byte	11
	.word	276
	.byte	6,0,10
	.word	311
	.byte	11
	.word	343
	.byte	6,0,10
	.word	393
	.byte	11
	.word	412
	.byte	6,0,10
	.word	428
	.byte	11
	.word	443
	.byte	11
	.word	457
	.byte	6,0
.L95:
	.byte	7
	.byte	'unsigned long int',0,4,7,10
	.word	486
	.byte	11
	.word	529
	.byte	6,0,10
	.word	544
	.byte	11
	.word	586
	.byte	6,0,7
	.byte	'short int',0,2,5,12
	.byte	'__wchar_t',0,3,1,1
	.word	698
	.byte	7
	.byte	'unsigned int',0,4,7,12
	.byte	'__size_t',0,3,1,1
	.word	729
	.byte	7
	.byte	'int',0,4,5,12
	.byte	'__ptrdiff_t',0,3,1,1
	.word	762
	.byte	13,1,3
	.word	789
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	791
	.byte	7
	.byte	'unsigned char',0,1,8,12
	.byte	'uint8',0,4,105,29
	.word	814
	.byte	7
	.byte	'unsigned short int',0,2,7,12
	.byte	'uint16',0,4,109,29
	.word	845
	.byte	12
	.byte	'uint32',0,4,113,29
	.word	653
	.byte	12
	.byte	'uint64',0,4,118,29
	.word	356
	.byte	12
	.byte	'sint16',0,4,126,29
	.word	698
	.byte	12
	.byte	'sint32',0,4,131,1,29
	.word	474
	.byte	7
	.byte	'long long int',0,8,5,12
	.byte	'sint64',0,4,138,1,29
	.word	943
	.byte	12
	.byte	'float32',0,4,167,1,29
	.word	302
	.byte	12
	.byte	'pvoid',0,5,57,28
	.word	388
	.byte	12
	.byte	'Ifx_TickTime',0,5,79,28
	.word	943
	.byte	12
	.byte	'Ifx_Lut_FxpAngle',0,6,84,16
	.word	474
	.byte	14,132,32
	.word	474
	.byte	15,128,8,0,16
	.word	1053
	.byte	17
	.byte	'Ifx_g_LutAtan2F32_FxpAngle_table',0,7,60,43
	.word	1064
	.byte	1,1,14,132,32
	.word	302
	.byte	15,128,8,0,16
	.word	1112
	.byte	17
	.byte	'Ifx_g_LutAtan2F32_table',0,7,61,43
	.word	1123
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L64:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,46,1,49,19,0,0,11,5
	.byte	0,49,19,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47
	.byte	15,0,0,16,38,0,73,19,0,0,17,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L65:
	.word	.L162-.L161
.L161:
	.half	3
	.word	.L164-.L163
.L163:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Lut.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_LutAtan2F32.h',0,0,0,0,0
.L164:
.L162:
	.sdecl	'.debug_info',debug,cluster('Ifx_LutAtan2F32_init')
	.sect	'.debug_info'
.L66:
	.word	298
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L69,.L68
	.byte	2
	.word	.L62
	.byte	3
	.byte	'Ifx_LutAtan2F32_init',0,1,49,6,1,1,1
	.word	.L57,.L81,.L56
	.byte	4
	.word	.L57,.L81
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_LutAtan2F32_init')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_LutAtan2F32_init')
	.sect	'.debug_line'
.L68:
	.word	.L166-.L165
.L165:
	.half	3
	.word	.L168-.L167
.L167:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0,0,0,0,0
.L168:
	.byte	5,1,7,0,5,2
	.word	.L57
	.byte	3,62,1,7,9
	.half	.L70-.L57
	.byte	0,1,1
.L166:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_LutAtan2F32_init')
	.sect	'.debug_ranges'
.L69:
	.word	-1,.L57,0,.L70-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_LutAtan2F32_fxpAngle')
	.sect	'.debug_info'
.L71:
	.word	815
	.half	3
	.word	.L72
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L74,.L73
	.byte	2
	.word	.L62
	.byte	3
	.byte	'Ifx_LutAtan2F32_fxpAngle',0,1,87,18
	.word	.L82
	.byte	1,1,1
	.word	.L59,.L83,.L58
	.byte	4
	.byte	'x',0,1,87,51
	.word	.L84,.L85
	.byte	4
	.byte	'y',0,1,87,62
	.word	.L84,.L86
	.byte	5
	.word	.L59,.L83
	.byte	6
	.byte	'angle',0,1,89,22
	.word	.L82,.L87
	.byte	6
	.byte	'fx',0,1,90,22
	.word	.L84,.L88
	.byte	6
	.byte	'fy',0,1,91,22
	.word	.L84,.L89
	.byte	7
	.word	.L90,.L91,.L6
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L91,.L6
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L96
	.byte	0,0,7
	.word	.L90,.L97,.L9
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L97,.L9
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L98
	.byte	0,0,7
	.word	.L90,.L99,.L13
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L99,.L13
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L100
	.byte	0,0,7
	.word	.L90,.L101,.L16
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L101,.L16
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L102
	.byte	0,0,7
	.word	.L90,.L103,.L21
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L103,.L21
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L104
	.byte	0,0,7
	.word	.L90,.L105,.L24
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L105,.L24
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L106
	.byte	0,0,7
	.word	.L90,.L107,.L28
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L107,.L28
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L108
	.byte	0,0,7
	.word	.L90,.L109,.L31
	.byte	8
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L109,.L31
	.byte	6
	.byte	'vali',0,1,68,12
	.word	.L95,.L110
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_LutAtan2F32_fxpAngle')
	.sect	'.debug_abbrev'
.L72:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_LutAtan2F32_fxpAngle')
	.sect	'.debug_line'
.L73:
	.word	.L170-.L169
.L169:
	.half	3
	.word	.L172-.L171
.L171:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0,0,0,0,0
.L172:
	.byte	5,13,7,0,5,2
	.word	.L59
	.byte	3,220,0,1,5,11,9
	.half	.L173-.L59
	.byte	1,5,5,9
	.half	.L174-.L173
	.byte	1,5,17,7,9
	.half	.L175-.L174
	.byte	3,2,1,5,15,9
	.half	.L176-.L175
	.byte	1,5,9,9
	.half	.L177-.L176
	.byte	1,5,17,7,9
	.half	.L178-.L177
	.byte	3,2,1,5,22,9
	.half	.L179-.L178
	.byte	1,5,20,9
	.half	.L180-.L179
	.byte	1,5,13,9
	.half	.L181-.L180
	.byte	1,5,77,7,9
	.half	.L182-.L181
	.byte	3,2,1,5,83,9
	.half	.L183-.L182
	.byte	1,5,81,9
	.half	.L184-.L183
	.byte	1,5,12,9
	.half	.L91-.L184
	.byte	3,98,1,5,33,9
	.half	.L185-.L91
	.byte	1,5,12,9
	.half	.L186-.L185
	.byte	3,1,1,5,17,9
	.half	.L129-.L186
	.byte	3,2,1,5,15,9
	.half	.L187-.L129
	.byte	1,5,34,9
	.half	.L188-.L187
	.byte	1,5,32,9
	.half	.L189-.L188
	.byte	1,5,5,9
	.half	.L190-.L189
	.byte	1,5,13,7,9
	.half	.L191-.L190
	.byte	3,2,1,5,44,9
	.half	.L5-.L191
	.byte	3,3,1,5,12,9
	.half	.L192-.L5
	.byte	1,5,44,9
	.half	.L193-.L192
	.byte	1,5,5,9
	.half	.L194-.L193
	.byte	1,5,76,9
	.half	.L6-.L194
	.byte	3,22,1,5,87,9
	.half	.L130-.L6
	.byte	1,5,83,9
	.half	.L4-.L130
	.byte	3,4,1,5,89,9
	.half	.L195-.L4
	.byte	1,5,87,9
	.half	.L196-.L195
	.byte	1,5,12,9
	.half	.L97-.L196
	.byte	3,94,1,5,33,9
	.half	.L197-.L97
	.byte	1,5,12,9
	.half	.L198-.L197
	.byte	3,1,1,5,17,9
	.half	.L133-.L198
	.byte	3,2,1,5,15,9
	.half	.L199-.L133
	.byte	1,5,34,9
	.half	.L200-.L199
	.byte	1,5,32,9
	.half	.L201-.L200
	.byte	1,5,5,9
	.half	.L202-.L201
	.byte	1,5,13,7,9
	.half	.L203-.L202
	.byte	3,2,1,5,44,9
	.half	.L8-.L203
	.byte	3,3,1,5,12,9
	.half	.L204-.L8
	.byte	1,5,44,9
	.half	.L205-.L204
	.byte	1,5,5,9
	.half	.L206-.L205
	.byte	1,5,25,9
	.half	.L9-.L206
	.byte	3,26,1,5,49,9
	.half	.L134-.L9
	.byte	1,5,13,9
	.half	.L7-.L134
	.byte	3,126,1,5,17,9
	.half	.L3-.L7
	.byte	3,7,1,5,20,9
	.half	.L207-.L3
	.byte	1,5,13,9
	.half	.L208-.L207
	.byte	1,5,58,7,9
	.half	.L209-.L208
	.byte	3,2,1,5,62,9
	.half	.L210-.L209
	.byte	1,5,12,9
	.half	.L99-.L210
	.byte	3,87,1,5,33,9
	.half	.L211-.L99
	.byte	1,5,12,9
	.half	.L212-.L211
	.byte	3,1,1,5,17,9
	.half	.L137-.L212
	.byte	3,2,1,5,15,9
	.half	.L213-.L137
	.byte	1,5,34,9
	.half	.L214-.L213
	.byte	1,5,32,9
	.half	.L215-.L214
	.byte	1,5,5,9
	.half	.L216-.L215
	.byte	1,5,13,7,9
	.half	.L217-.L216
	.byte	3,2,1,5,44,9
	.half	.L12-.L217
	.byte	3,3,1,5,12,9
	.half	.L218-.L12
	.byte	1,5,44,9
	.half	.L219-.L218
	.byte	1,5,5,9
	.half	.L138-.L219
	.byte	1,5,25,9
	.half	.L13-.L138
	.byte	3,33,1,5,67,9
	.half	.L220-.L13
	.byte	1,5,88,9
	.half	.L11-.L220
	.byte	3,4,1,5,86,9
	.half	.L221-.L11
	.byte	1,5,12,9
	.half	.L101-.L221
	.byte	3,83,1,5,33,9
	.half	.L222-.L101
	.byte	1,5,12,9
	.half	.L223-.L222
	.byte	3,1,1,5,17,9
	.half	.L141-.L223
	.byte	3,2,1,5,15,9
	.half	.L224-.L141
	.byte	1,5,34,9
	.half	.L225-.L224
	.byte	1,5,32,9
	.half	.L226-.L225
	.byte	1,5,5,9
	.half	.L227-.L226
	.byte	1,5,13,7,9
	.half	.L228-.L227
	.byte	3,2,1,5,44,9
	.half	.L15-.L228
	.byte	3,3,1,5,12,9
	.half	.L229-.L15
	.byte	1,5,44,9
	.half	.L230-.L229
	.byte	1,5,5,9
	.half	.L231-.L230
	.byte	1,5,82,9
	.half	.L16-.L231
	.byte	3,37,1,5,9,9
	.half	.L10-.L16
	.byte	3,120,1,5,17,9
	.half	.L2-.L10
	.byte	3,14,1,5,15,9
	.half	.L232-.L2
	.byte	1,5,9,9
	.half	.L233-.L232
	.byte	1,5,21,7,9
	.half	.L234-.L233
	.byte	3,2,1,5,19,9
	.half	.L235-.L234
	.byte	1,5,13,9
	.half	.L236-.L235
	.byte	1,5,81,7,9
	.half	.L237-.L236
	.byte	3,2,1,5,79,9
	.half	.L238-.L237
	.byte	1,5,12,9
	.half	.L103-.L238
	.byte	3,73,1,5,33,9
	.half	.L239-.L103
	.byte	1,5,12,9
	.half	.L240-.L239
	.byte	3,1,1,5,17,9
	.half	.L144-.L240
	.byte	3,2,1,5,15,9
	.half	.L241-.L144
	.byte	1,5,34,9
	.half	.L242-.L241
	.byte	1,5,32,9
	.half	.L243-.L242
	.byte	1,5,5,9
	.half	.L244-.L243
	.byte	1,5,13,7,9
	.half	.L245-.L244
	.byte	3,2,1,5,44,9
	.half	.L20-.L245
	.byte	3,3,1,5,12,9
	.half	.L246-.L20
	.byte	1,5,44,9
	.half	.L247-.L246
	.byte	1,5,5,9
	.half	.L248-.L247
	.byte	1,5,25,9
	.half	.L21-.L248
	.byte	3,47,1,5,42,9
	.half	.L145-.L21
	.byte	1,5,85,9
	.half	.L249-.L145
	.byte	1,5,82,9
	.half	.L19-.L249
	.byte	3,4,1,5,86,9
	.half	.L250-.L19
	.byte	1,5,12,9
	.half	.L105-.L250
	.byte	3,69,1,5,33,9
	.half	.L251-.L105
	.byte	1,5,12,9
	.half	.L252-.L251
	.byte	3,1,1,5,17,9
	.half	.L148-.L252
	.byte	3,2,1,5,15,9
	.half	.L253-.L148
	.byte	1,5,34,9
	.half	.L254-.L253
	.byte	1,5,32,9
	.half	.L255-.L254
	.byte	1,5,5,9
	.half	.L256-.L255
	.byte	1,5,13,7,9
	.half	.L257-.L256
	.byte	3,2,1,5,44,9
	.half	.L23-.L257
	.byte	3,3,1,5,12,9
	.half	.L258-.L23
	.byte	1,5,44,9
	.half	.L259-.L258
	.byte	1,5,5,9
	.half	.L260-.L259
	.byte	1,5,81,9
	.half	.L24-.L260
	.byte	3,51,1,5,13,9
	.half	.L22-.L24
	.byte	3,126,1,5,19,9
	.half	.L18-.L22
	.byte	3,7,1,5,13,9
	.half	.L261-.L18
	.byte	1,5,60,7,9
	.half	.L262-.L261
	.byte	3,2,1,5,12,9
	.half	.L107-.L262
	.byte	3,190,127,1,5,33,9
	.half	.L263-.L107
	.byte	1,5,12,9
	.half	.L264-.L263
	.byte	3,1,1,5,17,9
	.half	.L151-.L264
	.byte	3,2,1,5,15,9
	.half	.L265-.L151
	.byte	1,5,34,9
	.half	.L266-.L265
	.byte	1,5,32,9
	.half	.L267-.L266
	.byte	1,5,5,9
	.half	.L268-.L267
	.byte	1,5,13,7,9
	.half	.L269-.L268
	.byte	3,2,1,5,44,9
	.half	.L27-.L269
	.byte	3,3,1,5,12,9
	.half	.L270-.L27
	.byte	1,5,44,9
	.half	.L271-.L270
	.byte	1,5,5,9
	.half	.L152-.L271
	.byte	1,5,65,9
	.half	.L28-.L152
	.byte	3,58,1,5,85,9
	.half	.L26-.L28
	.byte	3,4,1,5,12,9
	.half	.L109-.L26
	.byte	3,186,127,1,5,33,9
	.half	.L272-.L109
	.byte	1,5,12,9
	.half	.L273-.L272
	.byte	3,1,1,5,17,9
	.half	.L155-.L273
	.byte	3,2,1,5,15,9
	.half	.L274-.L155
	.byte	1,5,34,9
	.half	.L275-.L274
	.byte	1,5,32,9
	.half	.L276-.L275
	.byte	1,5,5,9
	.half	.L277-.L276
	.byte	1,5,13,7,9
	.half	.L278-.L277
	.byte	3,2,1,5,44,9
	.half	.L30-.L278
	.byte	3,3,1,5,12,9
	.half	.L279-.L30
	.byte	1,5,44,9
	.half	.L280-.L279
	.byte	1,5,5,9
	.half	.L281-.L280
	.byte	1,5,43,9
	.half	.L31-.L281
	.byte	3,62,1,5,48,9
	.half	.L156-.L31
	.byte	1,5,18,9
	.half	.L17-.L156
	.byte	3,5,1,5,5,9
	.half	.L282-.L17
	.byte	1,5,1,9
	.half	.L32-.L282
	.byte	3,1,1,7,9
	.half	.L75-.L32
	.byte	0,1,1
.L170:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_LutAtan2F32_fxpAngle')
	.sect	'.debug_ranges'
.L74:
	.word	-1,.L59,0,.L75-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_LutAtan2F32_float32')
	.sect	'.debug_info'
.L76:
	.word	644
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L79,.L78
	.byte	2
	.word	.L62
	.byte	3
	.byte	'Ifx_LutAtan2F32_float32',0,1,148,1,9
	.word	.L84
	.byte	1,1,1
	.word	.L61,.L111,.L60
	.byte	4
	.byte	'y',0,1,148,1,41
	.word	.L84,.L112
	.byte	4
	.byte	'x',0,1,148,1,52
	.word	.L84,.L113
	.byte	5
	.word	.L61,.L111
	.byte	6
	.byte	'angle',0,1,150,1,13
	.word	.L84,.L114
	.byte	7
	.word	.L115,.L116,.L36
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L116,.L36
	.byte	0,7
	.word	.L115,.L120,.L38
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L120,.L38
	.byte	0,7
	.word	.L115,.L121,.L41
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L121,.L41
	.byte	0,7
	.word	.L115,.L122,.L43
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L122,.L43
	.byte	0,7
	.word	.L115,.L123,.L47
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L123,.L47
	.byte	0,7
	.word	.L115,.L124,.L49
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L124,.L49
	.byte	0,7
	.word	.L115,.L125,.L52
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L125,.L52
	.byte	0,7
	.word	.L115,.L126,.L54
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L126,.L54
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_LutAtan2F32_float32')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_LutAtan2F32_float32')
	.sect	'.debug_line'
.L78:
	.word	.L284-.L283
.L283:
	.half	3
	.word	.L286-.L285
.L285:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32.c',0,0,0,0,0
.L286:
	.byte	5,13,7,0,5,2
	.word	.L61
	.byte	3,151,1,1,5,11,9
	.half	.L287-.L61
	.byte	1,5,5,9
	.half	.L288-.L287
	.byte	1,5,17,7,9
	.half	.L289-.L288
	.byte	3,2,1,5,15,9
	.half	.L290-.L289
	.byte	1,5,9,9
	.half	.L291-.L290
	.byte	1,5,17,7,9
	.half	.L292-.L291
	.byte	3,2,1,5,22,9
	.half	.L293-.L292
	.byte	1,5,20,9
	.half	.L294-.L293
	.byte	1,5,13,9
	.half	.L295-.L294
	.byte	1,5,66,7,9
	.half	.L296-.L295
	.byte	3,2,1,5,71,9
	.half	.L297-.L296
	.byte	1,5,69,9
	.half	.L298-.L297
	.byte	1,5,48,9
	.half	.L116-.L298
	.byte	3,181,127,1,5,46,9
	.half	.L299-.L116
	.byte	1,5,36,9
	.half	.L300-.L299
	.byte	1,5,35,9
	.half	.L301-.L300
	.byte	1,5,12,9
	.half	.L302-.L301
	.byte	1,5,35,9
	.half	.L303-.L302
	.byte	1,5,5,9
	.half	.L304-.L303
	.byte	1,5,25,9
	.half	.L36-.L304
	.byte	3,203,0,1,5,65,9
	.half	.L305-.L36
	.byte	1,5,74,9
	.half	.L157-.L305
	.byte	1,5,72,9
	.half	.L35-.L157
	.byte	3,4,1,5,77,9
	.half	.L306-.L35
	.byte	1,5,75,9
	.half	.L307-.L306
	.byte	1,5,48,9
	.half	.L120-.L307
	.byte	3,177,127,1,5,46,9
	.half	.L308-.L120
	.byte	1,5,36,9
	.half	.L309-.L308
	.byte	1,5,35,9
	.half	.L310-.L309
	.byte	1,5,12,9
	.half	.L311-.L310
	.byte	1,5,35,9
	.half	.L312-.L311
	.byte	1,5,5,9
	.half	.L313-.L312
	.byte	1,5,25,9
	.half	.L38-.L313
	.byte	3,207,0,1,5,39,9
	.half	.L314-.L38
	.byte	1,5,13,9
	.half	.L37-.L314
	.byte	3,126,1,5,17,9
	.half	.L34-.L37
	.byte	3,7,1,5,20,9
	.half	.L315-.L34
	.byte	1,5,13,9
	.half	.L316-.L315
	.byte	1,5,57,7,9
	.half	.L317-.L316
	.byte	3,2,1,5,60,9
	.half	.L318-.L317
	.byte	1,5,48,9
	.half	.L121-.L318
	.byte	3,170,127,1,5,46,9
	.half	.L319-.L121
	.byte	1,5,36,9
	.half	.L320-.L319
	.byte	1,5,35,9
	.half	.L321-.L320
	.byte	1,5,12,9
	.half	.L322-.L321
	.byte	1,5,35,9
	.half	.L323-.L322
	.byte	1,5,5,9
	.half	.L324-.L323
	.byte	1,5,25,9
	.half	.L41-.L324
	.byte	3,214,0,1,5,64,9
	.half	.L158-.L41
	.byte	1,5,76,9
	.half	.L40-.L158
	.byte	3,4,1,5,74,9
	.half	.L325-.L40
	.byte	1,5,48,9
	.half	.L122-.L325
	.byte	3,166,127,1,5,46,9
	.half	.L326-.L122
	.byte	1,5,36,9
	.half	.L327-.L326
	.byte	1,5,35,9
	.half	.L328-.L327
	.byte	1,5,12,9
	.half	.L329-.L328
	.byte	1,5,35,9
	.half	.L330-.L329
	.byte	1,5,5,9
	.half	.L331-.L330
	.byte	1,5,25,9
	.half	.L43-.L331
	.byte	3,218,0,1,5,71,9
	.half	.L332-.L43
	.byte	1,5,9,9
	.half	.L39-.L332
	.byte	3,120,1,5,17,9
	.half	.L33-.L39
	.byte	3,14,1,5,15,9
	.half	.L333-.L33
	.byte	1,5,9,9
	.half	.L334-.L333
	.byte	1,5,21,7,9
	.half	.L335-.L334
	.byte	3,2,1,5,19,9
	.half	.L336-.L335
	.byte	1,5,13,9
	.half	.L337-.L336
	.byte	1,5,69,7,9
	.half	.L338-.L337
	.byte	3,2,1,5,67,9
	.half	.L339-.L338
	.byte	1,5,48,9
	.half	.L123-.L339
	.byte	3,156,127,1,5,46,9
	.half	.L340-.L123
	.byte	1,5,36,9
	.half	.L341-.L340
	.byte	1,5,35,9
	.half	.L342-.L341
	.byte	1,5,12,9
	.half	.L343-.L342
	.byte	1,5,35,9
	.half	.L344-.L343
	.byte	1,5,5,9
	.half	.L345-.L344
	.byte	1,5,25,9
	.half	.L47-.L345
	.byte	3,228,0,1,5,32,9
	.half	.L346-.L47
	.byte	1,5,72,9
	.half	.L159-.L346
	.byte	1,5,71,9
	.half	.L46-.L159
	.byte	3,4,1,5,74,9
	.half	.L347-.L46
	.byte	1,5,48,9
	.half	.L124-.L347
	.byte	3,152,127,1,5,46,9
	.half	.L348-.L124
	.byte	1,5,36,9
	.half	.L349-.L348
	.byte	1,5,35,9
	.half	.L350-.L349
	.byte	1,5,12,9
	.half	.L351-.L350
	.byte	1,5,35,9
	.half	.L352-.L351
	.byte	1,5,5,9
	.half	.L353-.L352
	.byte	1,5,33,9
	.half	.L49-.L353
	.byte	3,232,0,1,5,70,9
	.half	.L354-.L49
	.byte	1,5,13,9
	.half	.L48-.L354
	.byte	3,126,1,5,19,9
	.half	.L45-.L48
	.byte	3,7,1,5,13,9
	.half	.L355-.L45
	.byte	1,5,58,7,9
	.half	.L356-.L355
	.byte	3,2,1,5,48,9
	.half	.L125-.L356
	.byte	3,145,127,1,5,46,9
	.half	.L357-.L125
	.byte	1,5,36,9
	.half	.L358-.L357
	.byte	1,5,35,9
	.half	.L359-.L358
	.byte	1,5,12,9
	.half	.L360-.L359
	.byte	1,5,35,9
	.half	.L361-.L360
	.byte	1,5,5,9
	.half	.L160-.L361
	.byte	1,5,62,9
	.half	.L52-.L160
	.byte	3,239,0,1,5,73,9
	.half	.L51-.L52
	.byte	3,4,1,5,48,9
	.half	.L126-.L51
	.byte	3,141,127,1,5,46,9
	.half	.L362-.L126
	.byte	1,5,36,9
	.half	.L363-.L362
	.byte	1,5,35,9
	.half	.L364-.L363
	.byte	1,5,12,9
	.half	.L365-.L364
	.byte	1,5,35,9
	.half	.L366-.L365
	.byte	1,5,5,9
	.half	.L367-.L366
	.byte	1,5,33,9
	.half	.L54-.L367
	.byte	3,243,0,1,5,38,9
	.half	.L368-.L54
	.byte	1,5,5,9
	.half	.L44-.L368
	.byte	3,5,1,5,1,9
	.half	.L55-.L44
	.byte	3,1,1,7,9
	.half	.L80-.L55
	.byte	0,1,1
.L284:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_LutAtan2F32_float32')
	.sect	'.debug_ranges'
.L79:
	.word	-1,.L61,0,.L80-.L61,0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_LutAtan2F32_float32')
	.sect	'.debug_loc'
.L60:
	.word	-1,.L61,0,.L111-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L114:
	.word	-1,.L61,.L157-.L61,.L35-.L61
	.half	1
	.byte	82
	.word	.L37-.L61,.L34-.L61
	.half	1
	.byte	82
	.word	.L158-.L61,.L40-.L61
	.half	1
	.byte	82
	.word	.L39-.L61,.L33-.L61
	.half	1
	.byte	82
	.word	.L159-.L61,.L46-.L61
	.half	1
	.byte	82
	.word	.L48-.L61,.L45-.L61
	.half	1
	.byte	82
	.word	.L160-.L61,.L51-.L61
	.half	1
	.byte	82
	.word	.L44-.L61,.L111-.L61
	.half	1
	.byte	82
	.word	0,0
.L118:
	.word	0,0
.L113:
	.word	-1,.L61,0,.L111-.L61
	.half	1
	.byte	85
	.word	0,0
.L112:
	.word	-1,.L61,0,.L111-.L61
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_LutAtan2F32_fxpAngle')
	.sect	'.debug_loc'
.L58:
	.word	-1,.L59,0,.L83-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L87:
	.word	-1,.L59,.L130-.L59,.L4-.L59
	.half	1
	.byte	95
	.word	.L134-.L59,.L3-.L59
	.half	1
	.byte	95
	.word	.L138-.L59,.L11-.L59
	.half	1
	.byte	95
	.word	.L10-.L59,.L2-.L59
	.half	1
	.byte	95
	.word	.L145-.L59,.L19-.L59
	.half	1
	.byte	95
	.word	.L22-.L59,.L18-.L59
	.half	1
	.byte	95
	.word	.L152-.L59,.L26-.L59
	.half	1
	.byte	95
	.word	.L156-.L59,.L83-.L59
	.half	1
	.byte	95
	.word	0,0
.L88:
	.word	0,0
.L89:
	.word	0,0
.L93:
	.word	0,0
.L96:
	.word	-1,.L59,.L129-.L59,.L4-.L59
	.half	1
	.byte	88
	.word	0,0
.L110:
	.word	-1,.L59,.L155-.L59,.L17-.L59
	.half	1
	.byte	88
	.word	0,0
.L102:
	.word	-1,.L59,.L141-.L59,.L10-.L59
	.half	1
	.byte	88
	.word	0,0
.L108:
	.word	-1,.L59,.L151-.L59,.L26-.L59
	.half	1
	.byte	88
	.word	0,0
.L98:
	.word	-1,.L59,.L133-.L59,.L7-.L59
	.half	1
	.byte	88
	.word	0,0
.L104:
	.word	-1,.L59,.L144-.L59,.L19-.L59
	.half	1
	.byte	88
	.word	0,0
.L106:
	.word	-1,.L59,.L148-.L59,.L22-.L59
	.half	1
	.byte	88
	.word	0,0
.L100:
	.word	-1,.L59,.L137-.L59,.L11-.L59
	.half	1
	.byte	88
	.word	0,0
.L85:
	.word	-1,.L59,0,.L127-.L59
	.half	1
	.byte	84
	.word	.L4-.L59,.L131-.L59
	.half	1
	.byte	84
	.word	.L3-.L59,.L135-.L59
	.half	1
	.byte	84
	.word	.L11-.L59,.L139-.L59
	.half	1
	.byte	84
	.word	.L2-.L59,.L142-.L59
	.half	1
	.byte	84
	.word	.L19-.L59,.L146-.L59
	.half	1
	.byte	84
	.word	.L18-.L59,.L149-.L59
	.half	1
	.byte	84
	.word	.L26-.L59,.L153-.L59
	.half	1
	.byte	84
	.word	0,0
.L86:
	.word	-1,.L59,0,.L128-.L59
	.half	1
	.byte	85
	.word	.L4-.L59,.L132-.L59
	.half	1
	.byte	85
	.word	.L3-.L59,.L136-.L59
	.half	1
	.byte	85
	.word	.L11-.L59,.L140-.L59
	.half	1
	.byte	85
	.word	.L2-.L59,.L143-.L59
	.half	1
	.byte	85
	.word	.L19-.L59,.L147-.L59
	.half	1
	.byte	85
	.word	.L18-.L59,.L150-.L59
	.half	1
	.byte	85
	.word	.L26-.L59,.L154-.L59
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_LutAtan2F32_init')
	.sect	'.debug_loc'
.L56:
	.word	-1,.L57,0,.L81-.L57
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L369:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Ifx_LutAtan2F32_init')
	.sect	'.debug_frame'
	.word	24
	.word	.L369,.L57,.L81-.L57
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Ifx_LutAtan2F32_fxpAngle')
	.sect	'.debug_frame'
	.word	12
	.word	.L369,.L59,.L83-.L59
	.sdecl	'.debug_frame',debug,cluster('Ifx_LutAtan2F32_float32')
	.sect	'.debug_frame'
	.word	24
	.word	.L369,.L61,.L111-.L61
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	; Module end
