	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc13336a --dep-file=IfxVadc_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX00_P02_6_OUT',data,rom,cluster('IfxVadc_EMUX00_P02_6_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX00_P02_6_OUT'
	.global	IfxVadc_EMUX00_P02_6_OUT
	.align	4
IfxVadc_EMUX00_P02_6_OUT:	.type	object
	.size	IfxVadc_EMUX00_P02_6_OUT,16
	.word	-268304384,-268197376
	.byte	6
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX00_P33_3_OUT',data,rom,cluster('IfxVadc_EMUX00_P33_3_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX00_P33_3_OUT'
	.global	IfxVadc_EMUX00_P33_3_OUT
	.align	4
IfxVadc_EMUX00_P33_3_OUT:	.type	object
	.size	IfxVadc_EMUX00_P33_3_OUT,16
	.word	-268304384,-268184832
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX01_P02_7_OUT',data,rom,cluster('IfxVadc_EMUX01_P02_7_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX01_P02_7_OUT'
	.global	IfxVadc_EMUX01_P02_7_OUT
	.align	4
IfxVadc_EMUX01_P02_7_OUT:	.type	object
	.size	IfxVadc_EMUX01_P02_7_OUT,16
	.word	-268304384,-268197376
	.byte	7
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX01_P33_2_OUT',data,rom,cluster('IfxVadc_EMUX01_P33_2_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX01_P33_2_OUT'
	.global	IfxVadc_EMUX01_P33_2_OUT
	.align	4
IfxVadc_EMUX01_P33_2_OUT:	.type	object
	.size	IfxVadc_EMUX01_P33_2_OUT,16
	.word	-268304384,-268184832
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX02_P02_8_OUT',data,rom,cluster('IfxVadc_EMUX02_P02_8_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX02_P02_8_OUT'
	.global	IfxVadc_EMUX02_P02_8_OUT
	.align	4
IfxVadc_EMUX02_P02_8_OUT:	.type	object
	.size	IfxVadc_EMUX02_P02_8_OUT,16
	.word	-268304384,-268197376
	.byte	8
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX02_P33_1_OUT',data,rom,cluster('IfxVadc_EMUX02_P33_1_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX02_P33_1_OUT'
	.global	IfxVadc_EMUX02_P33_1_OUT
	.align	4
IfxVadc_EMUX02_P33_1_OUT:	.type	object
	.size	IfxVadc_EMUX02_P33_1_OUT,16
	.word	-268304384,-268184832
	.byte	1
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX10_P00_6_OUT',data,rom,cluster('IfxVadc_EMUX10_P00_6_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX10_P00_6_OUT'
	.global	IfxVadc_EMUX10_P00_6_OUT
	.align	4
IfxVadc_EMUX10_P00_6_OUT:	.type	object
	.size	IfxVadc_EMUX10_P00_6_OUT,16
	.word	-268304384,-268197888
	.byte	6
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX10_P33_6_OUT',data,rom,cluster('IfxVadc_EMUX10_P33_6_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX10_P33_6_OUT'
	.global	IfxVadc_EMUX10_P33_6_OUT
	.align	4
IfxVadc_EMUX10_P33_6_OUT:	.type	object
	.size	IfxVadc_EMUX10_P33_6_OUT,16
	.word	-268304384,-268184832
	.byte	6
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX11_P00_7_OUT',data,rom,cluster('IfxVadc_EMUX11_P00_7_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX11_P00_7_OUT'
	.global	IfxVadc_EMUX11_P00_7_OUT
	.align	4
IfxVadc_EMUX11_P00_7_OUT:	.type	object
	.size	IfxVadc_EMUX11_P00_7_OUT,16
	.word	-268304384,-268197888
	.byte	7
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX11_P33_5_OUT',data,rom,cluster('IfxVadc_EMUX11_P33_5_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX11_P33_5_OUT'
	.global	IfxVadc_EMUX11_P33_5_OUT
	.align	4
IfxVadc_EMUX11_P33_5_OUT:	.type	object
	.size	IfxVadc_EMUX11_P33_5_OUT,16
	.word	-268304384,-268184832
	.byte	5
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX12_P00_8_OUT',data,rom,cluster('IfxVadc_EMUX12_P00_8_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX12_P00_8_OUT'
	.global	IfxVadc_EMUX12_P00_8_OUT
	.align	4
IfxVadc_EMUX12_P00_8_OUT:	.type	object
	.size	IfxVadc_EMUX12_P00_8_OUT,16
	.word	-268304384,-268197888
	.byte	8
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX12_P33_4_OUT',data,rom,cluster('IfxVadc_EMUX12_P33_4_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_EMUX12_P33_4_OUT'
	.global	IfxVadc_EMUX12_P33_4_OUT
	.align	4
IfxVadc_EMUX12_P33_4_OUT:	.type	object
	.size	IfxVadc_EMUX12_P33_4_OUT,16
	.word	-268304384,-268184832
	.byte	4
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL0_P33_4_OUT',data,rom,cluster('IfxVadc_G0BFL0_P33_4_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL0_P33_4_OUT'
	.global	IfxVadc_G0BFL0_P33_4_OUT
	.align	4
IfxVadc_G0BFL0_P33_4_OUT:	.type	object
	.size	IfxVadc_G0BFL0_P33_4_OUT,20
	.word	-268304384
	.space	4
	.word	-268184832
	.byte	4
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL1_P33_5_OUT',data,rom,cluster('IfxVadc_G0BFL1_P33_5_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL1_P33_5_OUT'
	.global	IfxVadc_G0BFL1_P33_5_OUT
	.align	4
IfxVadc_G0BFL1_P33_5_OUT:	.type	object
	.size	IfxVadc_G0BFL1_P33_5_OUT,20
	.word	-268304384
	.space	4
	.word	-268184832
	.byte	5
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL2_P33_6_OUT',data,rom,cluster('IfxVadc_G0BFL2_P33_6_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL2_P33_6_OUT'
	.global	IfxVadc_G0BFL2_P33_6_OUT
	.align	4
IfxVadc_G0BFL2_P33_6_OUT:	.type	object
	.size	IfxVadc_G0BFL2_P33_6_OUT,20
	.word	-268304384
	.space	4
	.word	-268184832
	.byte	6
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL3_P33_7_OUT',data,rom,cluster('IfxVadc_G0BFL3_P33_7_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0BFL3_P33_7_OUT'
	.global	IfxVadc_G0BFL3_P33_7_OUT
	.align	4
IfxVadc_G0BFL3_P33_7_OUT:	.type	object
	.size	IfxVadc_G0BFL3_P33_7_OUT,20
	.word	-268304384
	.space	4
	.word	-268184832
	.byte	7
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL0_P33_0_OUT',data,rom,cluster('IfxVadc_G1BFL0_P33_0_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL0_P33_0_OUT'
	.global	IfxVadc_G1BFL0_P33_0_OUT
	.align	4
IfxVadc_G1BFL0_P33_0_OUT:	.type	object
	.size	IfxVadc_G1BFL0_P33_0_OUT,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268184832
	.space	4
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL1_P33_1_OUT',data,rom,cluster('IfxVadc_G1BFL1_P33_1_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL1_P33_1_OUT'
	.global	IfxVadc_G1BFL1_P33_1_OUT
	.align	4
IfxVadc_G1BFL1_P33_1_OUT:	.type	object
	.size	IfxVadc_G1BFL1_P33_1_OUT,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268184832
	.byte	1
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL2_P33_2_OUT',data,rom,cluster('IfxVadc_G1BFL2_P33_2_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL2_P33_2_OUT'
	.global	IfxVadc_G1BFL2_P33_2_OUT
	.align	4
IfxVadc_G1BFL2_P33_2_OUT:	.type	object
	.size	IfxVadc_G1BFL2_P33_2_OUT,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268184832
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL3_P33_3_OUT',data,rom,cluster('IfxVadc_G1BFL3_P33_3_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1BFL3_P33_3_OUT'
	.global	IfxVadc_G1BFL3_P33_3_OUT
	.align	4
IfxVadc_G1BFL3_P33_3_OUT:	.type	object
	.size	IfxVadc_G1BFL3_P33_3_OUT,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268184832
	.byte	3
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL0_P00_4_OUT',data,rom,cluster('IfxVadc_G2BFL0_P00_4_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL0_P00_4_OUT'
	.global	IfxVadc_G2BFL0_P00_4_OUT
	.align	4
IfxVadc_G2BFL0_P00_4_OUT:	.type	object
	.size	IfxVadc_G2BFL0_P00_4_OUT,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268197888
	.byte	4
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL1_P00_5_OUT',data,rom,cluster('IfxVadc_G2BFL1_P00_5_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL1_P00_5_OUT'
	.global	IfxVadc_G2BFL1_P00_5_OUT
	.align	4
IfxVadc_G2BFL1_P00_5_OUT:	.type	object
	.size	IfxVadc_G2BFL1_P00_5_OUT,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268197888
	.byte	5
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL2_P00_6_OUT',data,rom,cluster('IfxVadc_G2BFL2_P00_6_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL2_P00_6_OUT'
	.global	IfxVadc_G2BFL2_P00_6_OUT
	.align	4
IfxVadc_G2BFL2_P00_6_OUT:	.type	object
	.size	IfxVadc_G2BFL2_P00_6_OUT,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268197888
	.byte	6
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL3_P00_7_OUT',data,rom,cluster('IfxVadc_G2BFL3_P00_7_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2BFL3_P00_7_OUT'
	.global	IfxVadc_G2BFL3_P00_7_OUT
	.align	4
IfxVadc_G2BFL3_P00_7_OUT:	.type	object
	.size	IfxVadc_G2BFL3_P00_7_OUT,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268197888
	.byte	7
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL0_P10_0_OUT',data,rom,cluster('IfxVadc_G3BFL0_P10_0_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL0_P10_0_OUT'
	.global	IfxVadc_G3BFL0_P10_0_OUT
	.align	4
IfxVadc_G3BFL0_P10_0_OUT:	.type	object
	.size	IfxVadc_G3BFL0_P10_0_OUT,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268193792
	.space	4
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL0_P10_6_OUT',data,rom,cluster('IfxVadc_G3BFL0_P10_6_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL0_P10_6_OUT'
	.global	IfxVadc_G3BFL0_P10_6_OUT
	.align	4
IfxVadc_G3BFL0_P10_6_OUT:	.type	object
	.size	IfxVadc_G3BFL0_P10_6_OUT,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268193792
	.byte	6
	.space	3
	.byte	184
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL1_P10_1_OUT',data,rom,cluster('IfxVadc_G3BFL1_P10_1_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL1_P10_1_OUT'
	.global	IfxVadc_G3BFL1_P10_1_OUT
	.align	4
IfxVadc_G3BFL1_P10_1_OUT:	.type	object
	.size	IfxVadc_G3BFL1_P10_1_OUT,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268193792
	.byte	1
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL1_P10_7_OUT',data,rom,cluster('IfxVadc_G3BFL1_P10_7_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL1_P10_7_OUT'
	.global	IfxVadc_G3BFL1_P10_7_OUT
	.align	4
IfxVadc_G3BFL1_P10_7_OUT:	.type	object
	.size	IfxVadc_G3BFL1_P10_7_OUT,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268193792
	.byte	7
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL2_P10_2_OUT',data,rom,cluster('IfxVadc_G3BFL2_P10_2_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL2_P10_2_OUT'
	.global	IfxVadc_G3BFL2_P10_2_OUT
	.align	4
IfxVadc_G3BFL2_P10_2_OUT:	.type	object
	.size	IfxVadc_G3BFL2_P10_2_OUT,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268193792
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL3_P10_3_OUT',data,rom,cluster('IfxVadc_G3BFL3_P10_3_OUT')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3BFL3_P10_3_OUT'
	.global	IfxVadc_G3BFL3_P10_3_OUT
	.align	4
IfxVadc_G3BFL3_P10_3_OUT:	.type	object
	.size	IfxVadc_G3BFL3_P10_3_OUT,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268193792
	.byte	3
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_0_AN0_IN',data,rom,cluster('IfxVadc_G0_0_AN0_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_0_AN0_IN'
	.global	IfxVadc_G0_0_AN0_IN
	.align	4
IfxVadc_G0_0_AN0_IN:	.type	object
	.size	IfxVadc_G0_0_AN0_IN,20
	.word	-268304384
	.space	16
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_10_AN10_IN',data,rom,cluster('IfxVadc_G0_10_AN10_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_10_AN10_IN'
	.global	IfxVadc_G0_10_AN10_IN
	.align	4
IfxVadc_G0_10_AN10_IN:	.type	object
	.size	IfxVadc_G0_10_AN10_IN,20
	.word	-268304384
	.space	8
	.byte	10
	.space	3
	.byte	10
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_11_AN11_IN',data,rom,cluster('IfxVadc_G0_11_AN11_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_11_AN11_IN'
	.global	IfxVadc_G0_11_AN11_IN
	.align	4
IfxVadc_G0_11_AN11_IN:	.type	object
	.size	IfxVadc_G0_11_AN11_IN,20
	.word	-268304384
	.space	8
	.byte	11
	.space	3
	.byte	11
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_12_AN12_IN',data,rom,cluster('IfxVadc_G0_12_AN12_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_12_AN12_IN'
	.global	IfxVadc_G0_12_AN12_IN
	.align	4
IfxVadc_G0_12_AN12_IN:	.type	object
	.size	IfxVadc_G0_12_AN12_IN,20
	.word	-268304384
	.space	8
	.byte	12
	.space	3
	.byte	12
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_13_AN13_IN',data,rom,cluster('IfxVadc_G0_13_AN13_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_13_AN13_IN'
	.global	IfxVadc_G0_13_AN13_IN
	.align	4
IfxVadc_G0_13_AN13_IN:	.type	object
	.size	IfxVadc_G0_13_AN13_IN,20
	.word	-268304384
	.space	8
	.byte	13
	.space	3
	.byte	13
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_1_AN1_IN',data,rom,cluster('IfxVadc_G0_1_AN1_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_1_AN1_IN'
	.global	IfxVadc_G0_1_AN1_IN
	.align	4
IfxVadc_G0_1_AN1_IN:	.type	object
	.size	IfxVadc_G0_1_AN1_IN,20
	.word	-268304384
	.space	8
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_2_AN2_IN',data,rom,cluster('IfxVadc_G0_2_AN2_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_2_AN2_IN'
	.global	IfxVadc_G0_2_AN2_IN
	.align	4
IfxVadc_G0_2_AN2_IN:	.type	object
	.size	IfxVadc_G0_2_AN2_IN,20
	.word	-268304384
	.space	8
	.byte	2
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_3_AN3_IN',data,rom,cluster('IfxVadc_G0_3_AN3_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_3_AN3_IN'
	.global	IfxVadc_G0_3_AN3_IN
	.align	4
IfxVadc_G0_3_AN3_IN:	.type	object
	.size	IfxVadc_G0_3_AN3_IN,20
	.word	-268304384
	.space	8
	.byte	3
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_4_AN4_IN',data,rom,cluster('IfxVadc_G0_4_AN4_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_4_AN4_IN'
	.global	IfxVadc_G0_4_AN4_IN
	.align	4
IfxVadc_G0_4_AN4_IN:	.type	object
	.size	IfxVadc_G0_4_AN4_IN,20
	.word	-268304384
	.space	8
	.byte	4
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_5_AN5_IN',data,rom,cluster('IfxVadc_G0_5_AN5_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_5_AN5_IN'
	.global	IfxVadc_G0_5_AN5_IN
	.align	4
IfxVadc_G0_5_AN5_IN:	.type	object
	.size	IfxVadc_G0_5_AN5_IN,20
	.word	-268304384
	.space	8
	.byte	5
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_6_AN6_IN',data,rom,cluster('IfxVadc_G0_6_AN6_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_6_AN6_IN'
	.global	IfxVadc_G0_6_AN6_IN
	.align	4
IfxVadc_G0_6_AN6_IN:	.type	object
	.size	IfxVadc_G0_6_AN6_IN,20
	.word	-268304384
	.space	8
	.byte	6
	.space	3
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_7_AN7_IN',data,rom,cluster('IfxVadc_G0_7_AN7_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_7_AN7_IN'
	.global	IfxVadc_G0_7_AN7_IN
	.align	4
IfxVadc_G0_7_AN7_IN:	.type	object
	.size	IfxVadc_G0_7_AN7_IN,20
	.word	-268304384
	.space	8
	.byte	7
	.space	3
	.byte	7
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G0_8_AN8_IN',data,rom,cluster('IfxVadc_G0_8_AN8_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G0_8_AN8_IN'
	.global	IfxVadc_G0_8_AN8_IN
	.align	4
IfxVadc_G0_8_AN8_IN:	.type	object
	.size	IfxVadc_G0_8_AN8_IN,20
	.word	-268304384
	.space	8
	.byte	8
	.space	3
	.byte	8
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_0_AN16_IN',data,rom,cluster('IfxVadc_G1_0_AN16_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_0_AN16_IN'
	.global	IfxVadc_G1_0_AN16_IN
	.align	4
IfxVadc_G1_0_AN16_IN:	.type	object
	.size	IfxVadc_G1_0_AN16_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	16
	.space	7
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_10_AN26_IN',data,rom,cluster('IfxVadc_G1_10_AN26_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_10_AN26_IN'
	.global	IfxVadc_G1_10_AN26_IN
	.align	4
IfxVadc_G1_10_AN26_IN:	.type	object
	.size	IfxVadc_G1_10_AN26_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	26
	.space	3
	.byte	10
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_10_P40_2_IN',data,rom,cluster('IfxVadc_G1_10_P40_2_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_10_P40_2_IN'
	.global	IfxVadc_G1_10_P40_2_IN
	.align	4
IfxVadc_G1_10_P40_2_IN:	.type	object
	.size	IfxVadc_G1_10_P40_2_IN,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268181504
	.byte	2
	.space	3
	.byte	10
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_11_AN27_IN',data,rom,cluster('IfxVadc_G1_11_AN27_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_11_AN27_IN'
	.global	IfxVadc_G1_11_AN27_IN
	.align	4
IfxVadc_G1_11_AN27_IN:	.type	object
	.size	IfxVadc_G1_11_AN27_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	27
	.space	3
	.byte	11
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_11_P40_3_IN',data,rom,cluster('IfxVadc_G1_11_P40_3_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_11_P40_3_IN'
	.global	IfxVadc_G1_11_P40_3_IN
	.align	4
IfxVadc_G1_11_P40_3_IN:	.type	object
	.size	IfxVadc_G1_11_P40_3_IN,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268181504
	.byte	3
	.space	3
	.byte	11
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_12_AN28_IN',data,rom,cluster('IfxVadc_G1_12_AN28_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_12_AN28_IN'
	.global	IfxVadc_G1_12_AN28_IN
	.align	4
IfxVadc_G1_12_AN28_IN:	.type	object
	.size	IfxVadc_G1_12_AN28_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	28
	.space	3
	.byte	12
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_13_AN29_IN',data,rom,cluster('IfxVadc_G1_13_AN29_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_13_AN29_IN'
	.global	IfxVadc_G1_13_AN29_IN
	.align	4
IfxVadc_G1_13_AN29_IN:	.type	object
	.size	IfxVadc_G1_13_AN29_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	29
	.space	3
	.byte	13
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_1_AN17_IN',data,rom,cluster('IfxVadc_G1_1_AN17_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_1_AN17_IN'
	.global	IfxVadc_G1_1_AN17_IN
	.align	4
IfxVadc_G1_1_AN17_IN:	.type	object
	.size	IfxVadc_G1_1_AN17_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	17
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_2_AN18_IN',data,rom,cluster('IfxVadc_G1_2_AN18_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_2_AN18_IN'
	.global	IfxVadc_G1_2_AN18_IN
	.align	4
IfxVadc_G1_2_AN18_IN:	.type	object
	.size	IfxVadc_G1_2_AN18_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	18
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_3_AN19_IN',data,rom,cluster('IfxVadc_G1_3_AN19_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_3_AN19_IN'
	.global	IfxVadc_G1_3_AN19_IN
	.align	4
IfxVadc_G1_3_AN19_IN:	.type	object
	.size	IfxVadc_G1_3_AN19_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	19
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_4_AN20_IN',data,rom,cluster('IfxVadc_G1_4_AN20_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_4_AN20_IN'
	.global	IfxVadc_G1_4_AN20_IN
	.align	4
IfxVadc_G1_4_AN20_IN:	.type	object
	.size	IfxVadc_G1_4_AN20_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	20
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_5_AN21_IN',data,rom,cluster('IfxVadc_G1_5_AN21_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_5_AN21_IN'
	.global	IfxVadc_G1_5_AN21_IN
	.align	4
IfxVadc_G1_5_AN21_IN:	.type	object
	.size	IfxVadc_G1_5_AN21_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	21
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_8_AN24_IN',data,rom,cluster('IfxVadc_G1_8_AN24_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_8_AN24_IN'
	.global	IfxVadc_G1_8_AN24_IN
	.align	4
IfxVadc_G1_8_AN24_IN:	.type	object
	.size	IfxVadc_G1_8_AN24_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	24
	.space	3
	.byte	8
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_8_P40_0_IN',data,rom,cluster('IfxVadc_G1_8_P40_0_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_8_P40_0_IN'
	.global	IfxVadc_G1_8_P40_0_IN
	.align	4
IfxVadc_G1_8_P40_0_IN:	.type	object
	.size	IfxVadc_G1_8_P40_0_IN,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268181504
	.space	4
	.byte	8
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_9_AN25_IN',data,rom,cluster('IfxVadc_G1_9_AN25_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_9_AN25_IN'
	.global	IfxVadc_G1_9_AN25_IN
	.align	4
IfxVadc_G1_9_AN25_IN:	.type	object
	.size	IfxVadc_G1_9_AN25_IN,20
	.word	-268304384
	.byte	1
	.space	7
	.byte	25
	.space	3
	.byte	9
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G1_9_P40_1_IN',data,rom,cluster('IfxVadc_G1_9_P40_1_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G1_9_P40_1_IN'
	.global	IfxVadc_G1_9_P40_1_IN
	.align	4
IfxVadc_G1_9_P40_1_IN:	.type	object
	.size	IfxVadc_G1_9_P40_1_IN,20
	.word	-268304384
	.byte	1
	.space	3
	.word	-268181504
	.byte	1
	.space	3
	.byte	9
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_0_AN32_IN',data,rom,cluster('IfxVadc_G2_0_AN32_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_0_AN32_IN'
	.global	IfxVadc_G2_0_AN32_IN
	.align	4
IfxVadc_G2_0_AN32_IN:	.type	object
	.size	IfxVadc_G2_0_AN32_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	32
	.space	7
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_10_AN44_IN',data,rom,cluster('IfxVadc_G2_10_AN44_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_10_AN44_IN'
	.global	IfxVadc_G2_10_AN44_IN
	.align	4
IfxVadc_G2_10_AN44_IN:	.type	object
	.size	IfxVadc_G2_10_AN44_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	44
	.space	3
	.byte	10
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_11_AN45_IN',data,rom,cluster('IfxVadc_G2_11_AN45_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_11_AN45_IN'
	.global	IfxVadc_G2_11_AN45_IN
	.align	4
IfxVadc_G2_11_AN45_IN:	.type	object
	.size	IfxVadc_G2_11_AN45_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	45
	.space	3
	.byte	11
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_12_AN46_IN',data,rom,cluster('IfxVadc_G2_12_AN46_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_12_AN46_IN'
	.global	IfxVadc_G2_12_AN46_IN
	.align	4
IfxVadc_G2_12_AN46_IN:	.type	object
	.size	IfxVadc_G2_12_AN46_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	46
	.space	3
	.byte	12
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_13_AN47_IN',data,rom,cluster('IfxVadc_G2_13_AN47_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_13_AN47_IN'
	.global	IfxVadc_G2_13_AN47_IN
	.align	4
IfxVadc_G2_13_AN47_IN:	.type	object
	.size	IfxVadc_G2_13_AN47_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	47
	.space	3
	.byte	13
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_14_AN48_IN',data,rom,cluster('IfxVadc_G2_14_AN48_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_14_AN48_IN'
	.global	IfxVadc_G2_14_AN48_IN
	.align	4
IfxVadc_G2_14_AN48_IN:	.type	object
	.size	IfxVadc_G2_14_AN48_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	48
	.space	3
	.byte	14
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_15_AN49_IN',data,rom,cluster('IfxVadc_G2_15_AN49_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_15_AN49_IN'
	.global	IfxVadc_G2_15_AN49_IN
	.align	4
IfxVadc_G2_15_AN49_IN:	.type	object
	.size	IfxVadc_G2_15_AN49_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	49
	.space	3
	.byte	15
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_1_AN33_IN',data,rom,cluster('IfxVadc_G2_1_AN33_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_1_AN33_IN'
	.global	IfxVadc_G2_1_AN33_IN
	.align	4
IfxVadc_G2_1_AN33_IN:	.type	object
	.size	IfxVadc_G2_1_AN33_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	33
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_3_AN35_IN',data,rom,cluster('IfxVadc_G2_3_AN35_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_3_AN35_IN'
	.global	IfxVadc_G2_3_AN35_IN
	.align	4
IfxVadc_G2_3_AN35_IN:	.type	object
	.size	IfxVadc_G2_3_AN35_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	35
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_4_AN36_IN',data,rom,cluster('IfxVadc_G2_4_AN36_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_4_AN36_IN'
	.global	IfxVadc_G2_4_AN36_IN
	.align	4
IfxVadc_G2_4_AN36_IN:	.type	object
	.size	IfxVadc_G2_4_AN36_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	36
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_4_P40_6_IN',data,rom,cluster('IfxVadc_G2_4_P40_6_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_4_P40_6_IN'
	.global	IfxVadc_G2_4_P40_6_IN
	.align	4
IfxVadc_G2_4_P40_6_IN:	.type	object
	.size	IfxVadc_G2_4_P40_6_IN,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268181504
	.byte	6
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_5_AN37_IN',data,rom,cluster('IfxVadc_G2_5_AN37_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_5_AN37_IN'
	.global	IfxVadc_G2_5_AN37_IN
	.align	4
IfxVadc_G2_5_AN37_IN:	.type	object
	.size	IfxVadc_G2_5_AN37_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	37
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_5_P40_7_IN',data,rom,cluster('IfxVadc_G2_5_P40_7_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_5_P40_7_IN'
	.global	IfxVadc_G2_5_P40_7_IN
	.align	4
IfxVadc_G2_5_P40_7_IN:	.type	object
	.size	IfxVadc_G2_5_P40_7_IN,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268181504
	.byte	7
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_6_AN38_IN',data,rom,cluster('IfxVadc_G2_6_AN38_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_6_AN38_IN'
	.global	IfxVadc_G2_6_AN38_IN
	.align	4
IfxVadc_G2_6_AN38_IN:	.type	object
	.size	IfxVadc_G2_6_AN38_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	38
	.space	3
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_6_P40_8_IN',data,rom,cluster('IfxVadc_G2_6_P40_8_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_6_P40_8_IN'
	.global	IfxVadc_G2_6_P40_8_IN
	.align	4
IfxVadc_G2_6_P40_8_IN:	.type	object
	.size	IfxVadc_G2_6_P40_8_IN,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268181504
	.byte	8
	.space	3
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_7_AN39_IN',data,rom,cluster('IfxVadc_G2_7_AN39_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_7_AN39_IN'
	.global	IfxVadc_G2_7_AN39_IN
	.align	4
IfxVadc_G2_7_AN39_IN:	.type	object
	.size	IfxVadc_G2_7_AN39_IN,20
	.word	-268304384
	.byte	2
	.space	7
	.byte	39
	.space	3
	.byte	7
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G2_7_P40_9_IN',data,rom,cluster('IfxVadc_G2_7_P40_9_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G2_7_P40_9_IN'
	.global	IfxVadc_G2_7_P40_9_IN
	.align	4
IfxVadc_G2_7_P40_9_IN:	.type	object
	.size	IfxVadc_G2_7_P40_9_IN,20
	.word	-268304384
	.byte	2
	.space	3
	.word	-268181504
	.byte	9
	.space	3
	.byte	7
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_0_P00_12_IN',data,rom,cluster('IfxVadc_G3_0_P00_12_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_0_P00_12_IN'
	.global	IfxVadc_G3_0_P00_12_IN
	.align	4
IfxVadc_G3_0_P00_12_IN:	.type	object
	.size	IfxVadc_G3_0_P00_12_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	12
	.space	7
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_10_P00_2_IN',data,rom,cluster('IfxVadc_G3_10_P00_2_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_10_P00_2_IN'
	.global	IfxVadc_G3_10_P00_2_IN
	.align	4
IfxVadc_G3_10_P00_2_IN:	.type	object
	.size	IfxVadc_G3_10_P00_2_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	2
	.space	3
	.byte	10
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_11_P00_1_IN',data,rom,cluster('IfxVadc_G3_11_P00_1_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_11_P00_1_IN'
	.global	IfxVadc_G3_11_P00_1_IN
	.align	4
IfxVadc_G3_11_P00_1_IN:	.type	object
	.size	IfxVadc_G3_11_P00_1_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	1
	.space	3
	.byte	11
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_1_P00_11_IN',data,rom,cluster('IfxVadc_G3_1_P00_11_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_1_P00_11_IN'
	.global	IfxVadc_G3_1_P00_11_IN
	.align	4
IfxVadc_G3_1_P00_11_IN:	.type	object
	.size	IfxVadc_G3_1_P00_11_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	11
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_2_P00_10_IN',data,rom,cluster('IfxVadc_G3_2_P00_10_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_2_P00_10_IN'
	.global	IfxVadc_G3_2_P00_10_IN
	.align	4
IfxVadc_G3_2_P00_10_IN:	.type	object
	.size	IfxVadc_G3_2_P00_10_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	10
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_3_P00_9_IN',data,rom,cluster('IfxVadc_G3_3_P00_9_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_3_P00_9_IN'
	.global	IfxVadc_G3_3_P00_9_IN
	.align	4
IfxVadc_G3_3_P00_9_IN:	.type	object
	.size	IfxVadc_G3_3_P00_9_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	9
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_4_P00_8_IN',data,rom,cluster('IfxVadc_G3_4_P00_8_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_4_P00_8_IN'
	.global	IfxVadc_G3_4_P00_8_IN
	.align	4
IfxVadc_G3_4_P00_8_IN:	.type	object
	.size	IfxVadc_G3_4_P00_8_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	8
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_5_P00_7_IN',data,rom,cluster('IfxVadc_G3_5_P00_7_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_5_P00_7_IN'
	.global	IfxVadc_G3_5_P00_7_IN
	.align	4
IfxVadc_G3_5_P00_7_IN:	.type	object
	.size	IfxVadc_G3_5_P00_7_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	7
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_6_P00_6_IN',data,rom,cluster('IfxVadc_G3_6_P00_6_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_6_P00_6_IN'
	.global	IfxVadc_G3_6_P00_6_IN
	.align	4
IfxVadc_G3_6_P00_6_IN:	.type	object
	.size	IfxVadc_G3_6_P00_6_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	6
	.space	3
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_7_P00_5_IN',data,rom,cluster('IfxVadc_G3_7_P00_5_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_7_P00_5_IN'
	.global	IfxVadc_G3_7_P00_5_IN
	.align	4
IfxVadc_G3_7_P00_5_IN:	.type	object
	.size	IfxVadc_G3_7_P00_5_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	5
	.space	3
	.byte	7
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_8_P00_4_IN',data,rom,cluster('IfxVadc_G3_8_P00_4_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_8_P00_4_IN'
	.global	IfxVadc_G3_8_P00_4_IN
	.align	4
IfxVadc_G3_8_P00_4_IN:	.type	object
	.size	IfxVadc_G3_8_P00_4_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	4
	.space	3
	.byte	8
	.space	3
	.sdecl	'.rodata.IfxVadc_PinMap.IfxVadc_G3_9_P00_3_IN',data,rom,cluster('IfxVadc_G3_9_P00_3_IN')
	.sect	'.rodata.IfxVadc_PinMap.IfxVadc_G3_9_P00_3_IN'
	.global	IfxVadc_G3_9_P00_3_IN
	.align	4
IfxVadc_G3_9_P00_3_IN:	.type	object
	.size	IfxVadc_G3_9_P00_3_IN,20
	.word	-268304384
	.byte	3
	.space	3
	.word	-268197888
	.byte	3
	.space	3
	.byte	9
	.space	3
	.sdecl	'.data.IfxVadc_PinMap.IfxVadc_Emux_Out_pinTable',data,cluster('IfxVadc_Emux_Out_pinTable')
	.sect	'.data.IfxVadc_PinMap.IfxVadc_Emux_Out_pinTable'
	.global	IfxVadc_Emux_Out_pinTable
	.align	4
IfxVadc_Emux_Out_pinTable:	.type	object
	.size	IfxVadc_Emux_Out_pinTable,48
	.word	IfxVadc_EMUX10_P00_6_OUT,IfxVadc_EMUX11_P00_7_OUT,IfxVadc_EMUX12_P00_8_OUT,IfxVadc_EMUX00_P02_6_OUT,IfxVadc_EMUX01_P02_7_OUT,IfxVadc_EMUX02_P02_8_OUT,IfxVadc_EMUX02_P33_1_OUT,IfxVadc_EMUX01_P33_2_OUT
	.word	IfxVadc_EMUX00_P33_3_OUT,IfxVadc_EMUX12_P33_4_OUT,IfxVadc_EMUX11_P33_5_OUT,IfxVadc_EMUX10_P33_6_OUT
	.sdecl	'.data.IfxVadc_PinMap.IfxVadc_GxBfl_Out_pinTable',data,cluster('IfxVadc_GxBfl_Out_pinTable')
	.sect	'.data.IfxVadc_PinMap.IfxVadc_GxBfl_Out_pinTable'
	.global	IfxVadc_GxBfl_Out_pinTable
	.align	4
IfxVadc_GxBfl_Out_pinTable:	.type	object
	.size	IfxVadc_GxBfl_Out_pinTable,96
	.word	IfxVadc_G0BFL0_P33_4_OUT,IfxVadc_G0BFL1_P33_5_OUT,IfxVadc_G0BFL2_P33_6_OUT,IfxVadc_G0BFL3_P33_7_OUT
	.space	8
	.word	IfxVadc_G1BFL0_P33_0_OUT,IfxVadc_G1BFL1_P33_1_OUT,IfxVadc_G1BFL2_P33_2_OUT,IfxVadc_G1BFL3_P33_3_OUT
	.space	8
	.word	IfxVadc_G2BFL0_P00_4_OUT,IfxVadc_G2BFL1_P00_5_OUT,IfxVadc_G2BFL2_P00_6_OUT,IfxVadc_G2BFL3_P00_7_OUT
	.space	8
	.word	IfxVadc_G3BFL0_P10_0_OUT,IfxVadc_G3BFL1_P10_1_OUT,IfxVadc_G3BFL2_P10_2_OUT,IfxVadc_G3BFL3_P10_3_OUT
	.word	IfxVadc_G3BFL0_P10_6_OUT,IfxVadc_G3BFL1_P10_7_OUT
	.sdecl	'.data.IfxVadc_PinMap.IfxVadc_Vadcg_In_pinTable',data,cluster('IfxVadc_Vadcg_In_pinTable')
	.sect	'.data.IfxVadc_PinMap.IfxVadc_Vadcg_In_pinTable'
	.global	IfxVadc_Vadcg_In_pinTable
	.align	4
IfxVadc_Vadcg_In_pinTable:	.type	object
	.size	IfxVadc_Vadcg_In_pinTable,256
	.word	IfxVadc_G0_0_AN0_IN,IfxVadc_G0_1_AN1_IN,IfxVadc_G0_2_AN2_IN,IfxVadc_G0_3_AN3_IN,IfxVadc_G0_4_AN4_IN,IfxVadc_G0_5_AN5_IN,IfxVadc_G0_6_AN6_IN,IfxVadc_G0_7_AN7_IN
	.word	IfxVadc_G0_8_AN8_IN
	.space	4
	.word	IfxVadc_G0_10_AN10_IN,IfxVadc_G0_11_AN11_IN,IfxVadc_G0_12_AN12_IN,IfxVadc_G0_13_AN13_IN
	.space	8
	.word	IfxVadc_G1_0_AN16_IN,IfxVadc_G1_1_AN17_IN,IfxVadc_G1_2_AN18_IN,IfxVadc_G1_3_AN19_IN
	.word	IfxVadc_G1_4_AN20_IN,IfxVadc_G1_5_AN21_IN
	.space	8
	.word	IfxVadc_G1_8_P40_0_IN,IfxVadc_G1_9_P40_1_IN,IfxVadc_G1_10_P40_2_IN,IfxVadc_G1_11_P40_3_IN
	.word	IfxVadc_G1_12_AN28_IN,IfxVadc_G1_13_AN29_IN
	.space	8
	.word	IfxVadc_G2_0_AN32_IN,IfxVadc_G2_1_AN33_IN
	.space	4
	.word	IfxVadc_G2_3_AN35_IN,IfxVadc_G2_4_P40_6_IN,IfxVadc_G2_5_P40_7_IN,IfxVadc_G2_6_P40_8_IN
	.word	IfxVadc_G2_7_P40_9_IN
	.space	8
	.word	IfxVadc_G2_10_AN44_IN,IfxVadc_G2_11_AN45_IN,IfxVadc_G2_12_AN46_IN,IfxVadc_G2_13_AN47_IN,IfxVadc_G2_14_AN48_IN,IfxVadc_G2_15_AN49_IN,IfxVadc_G3_0_P00_12_IN,IfxVadc_G3_1_P00_11_IN
	.word	IfxVadc_G3_2_P00_10_IN,IfxVadc_G3_3_P00_9_IN,IfxVadc_G3_4_P00_8_IN,IfxVadc_G3_5_P00_7_IN,IfxVadc_G3_6_P00_6_IN,IfxVadc_G3_7_P00_5_IN,IfxVadc_G3_8_P00_4_IN,IfxVadc_G3_9_P00_3_IN
	.word	IfxVadc_G3_10_P00_2_IN,IfxVadc_G3_11_P00_1_IN
	.space	16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	64062
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	238
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	241
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	286
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	298
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	352
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	384
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	384
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	352
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	510
	.byte	4,2,35,0,0,14
	.word	800
	.byte	3
	.word	839
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	844
	.byte	6,0,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	914
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1230
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1801
	.byte	4,2,35,0,0,15,4
	.word	493
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1929
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2144
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2359
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2576
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2796
	.byte	4,2,35,0,0,15,24
	.word	493
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3119
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3423
	.byte	4,2,35,0,0,15,8
	.word	493
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3748
	.byte	4,2,35,0,0,15,12
	.word	493
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4088
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4454
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4740
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4887
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5056
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	892
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5228
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	892
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5403
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5577
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5751
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6083
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6416
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6764
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6888
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6972
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7152
	.byte	4,2,35,0,0,15,76
	.word	493
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7405
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7492
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1190
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1761
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1880
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1920
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2104
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2319
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2536
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2756
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1920
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3070
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3110
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3383
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3699
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3739
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4039
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4079
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4414
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4700
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3739
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4847
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5016
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5188
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5363
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5537
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5711
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5887
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6043
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6376
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6724
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3739
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6848
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7097
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7356
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7396
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7452
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8019
	.byte	4,3,35,252,1,0,14
	.word	8059
	.byte	3
	.word	8662
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	8667
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	493
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	8672
	.byte	6,0,19
	.word	246
	.byte	20
	.word	272
	.byte	6,0,19
	.word	307
	.byte	20
	.word	339
	.byte	6,0,19
	.word	389
	.byte	20
	.word	408
	.byte	6,0,19
	.word	424
	.byte	20
	.word	439
	.byte	20
	.word	453
	.byte	6,0,19
	.word	849
	.byte	20
	.word	877
	.byte	6,0,19
	.word	8775
	.byte	20
	.word	8803
	.byte	20
	.word	8817
	.byte	20
	.word	8835
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,7,1,1
	.word	8940
	.byte	21
	.byte	'__size_t',0,7,1,1
	.word	470
	.byte	21
	.byte	'__ptrdiff_t',0,7,1,1
	.word	486
	.byte	22,1,3
	.word	9008
	.byte	21
	.byte	'__codeptr',0,7,1,1
	.word	9010
	.byte	10
	.byte	'_Ifx_VADC_ACCEN0_Bits',0,8,49,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_ACCEN0_Bits',0,8,83,3
	.word	9033
	.byte	10
	.byte	'_Ifx_VADC_ACCPROT0_Bits',0,8,86,16,4,11
	.byte	'APC0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'APC1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'APC2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'APC3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	892
	.byte	11,1,2,35,0,11
	.byte	'APEM',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'API0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'API1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'API2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'API3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	892
	.byte	11,1,2,35,2,11
	.byte	'APGC',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_ACCPROT0_Bits',0,8,100,3
	.word	9592
	.byte	10
	.byte	'_Ifx_VADC_ACCPROT1_Bits',0,8,103,16,4,11
	.byte	'APS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'APS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'APS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'APS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	892
	.byte	11,1,2,35,0,11
	.byte	'APTF',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'APR0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'APR1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'APR2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'APR3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	892
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_VADC_ACCPROT1_Bits',0,8,116,3
	.word	9858
	.byte	10
	.byte	'_Ifx_VADC_BRSCTRL_Bits',0,8,119,16,4,11
	.byte	'SRCRESREG',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'XTSEL',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'XTLVL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'XTMODE',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'XTWC',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'GTSEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'GTLVL',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'GTWC',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_VADC_BRSCTRL_Bits',0,8,132,1,3
	.word	10108
	.byte	10
	.byte	'_Ifx_VADC_BRSMR_Bits',0,8,135,1,16,4,11
	.byte	'ENGT',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ENTR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ENSI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SCAN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'LDM',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'REQGT',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CLRPND',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LDEV',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'RPTDIS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	892
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_VADC_BRSMR_Bits',0,8,149,1,3
	.word	10375
	.byte	10
	.byte	'_Ifx_VADC_BRSPND_Bits',0,8,152,1,16,4,11
	.byte	'CHPNDGy',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_VADC_BRSPND_Bits',0,8,155,1,3
	.word	10648
	.byte	10
	.byte	'_Ifx_VADC_BRSSEL_Bits',0,8,158,1,16,4,11
	.byte	'CHSELGy',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_VADC_BRSSEL_Bits',0,8,161,1,3
	.word	10726
	.byte	10
	.byte	'_Ifx_VADC_CLC_Bits',0,8,164,1,16,4,11
	.byte	'DISR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_VADC_CLC_Bits',0,8,171,1,3
	.word	10804
	.byte	10
	.byte	'_Ifx_VADC_EMUXSEL_Bits',0,8,174,1,16,4,11
	.byte	'EMUXGRP0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EMUXGRP1',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_VADC_EMUXSEL_Bits',0,8,179,1,3
	.word	10949
	.byte	10
	.byte	'_Ifx_VADC_G_ALIAS_Bits',0,8,182,1,16,4,11
	.byte	'ALIAS0',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'ALIAS1',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	470
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_ALIAS_Bits',0,8,188,1,3
	.word	11072
	.byte	10
	.byte	'_Ifx_VADC_G_ARBCFG_Bits',0,8,191,1,16,4,11
	.byte	'ANONC',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'ARBRND',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ARBM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ANONS',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'CSRC',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	892
	.byte	5,7,2,35,2,11
	.byte	'SYNRUN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CAL',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'CALS',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SAMPLE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_ARBCFG_Bits',0,8,208,1,3
	.word	11214
	.byte	10
	.byte	'_Ifx_VADC_G_ARBPR_Bits',0,8,211,1,16,4,11
	.byte	'PRIO0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CSM0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PRIO1',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CSM1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PRIO2',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CSM2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	12,8,2,35,0,11
	.byte	'ASEN0',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'ASEN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'ASEN2',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_ARBPR_Bits',0,8,227,1,3
	.word	11549
	.byte	10
	.byte	'_Ifx_VADC_G_ASCTRL_Bits',0,8,230,1,16,4,11
	.byte	'SRCRESREG',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'XTSEL',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'XTLVL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'XTMODE',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'XTWC',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'GTSEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'GTLVL',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'GTWC',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'TMEN',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'TMWC',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_ASCTRL_Bits',0,8,246,1,3
	.word	11873
	.byte	10
	.byte	'_Ifx_VADC_G_ASMR_Bits',0,8,249,1,16,4,11
	.byte	'ENGT',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ENTR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ENSI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SCAN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'LDM',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'REQGT',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CLRPND',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LDEV',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'RPTDIS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	892
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_ASMR_Bits',0,8,135,2,3
	.word	12198
	.byte	10
	.byte	'_Ifx_VADC_G_ASPND_Bits',0,8,138,2,16,4,11
	.byte	'CHPND',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_ASPND_Bits',0,8,141,2,3
	.word	12473
	.byte	10
	.byte	'_Ifx_VADC_G_ASSEL_Bits',0,8,144,2,16,4,11
	.byte	'CHSEL',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_ASSEL_Bits',0,8,147,2,3
	.word	12551
	.byte	10
	.byte	'_Ifx_VADC_G_BFL_Bits',0,8,150,2,16,4,11
	.byte	'BFL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'BFL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'BFL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'BFL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'BFA0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'BFA1',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BFA2',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'BFA3',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'BFI0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'BFI1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'BFI2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'BFI3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	892
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_BFL_Bits',0,8,167,2,3
	.word	12629
	.byte	10
	.byte	'_Ifx_VADC_G_BFLC_Bits',0,8,170,2,16,4,11
	.byte	'BFM0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BFM1',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'BFM2',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'BFM3',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_BFLC_Bits',0,8,177,2,3
	.word	12946
	.byte	10
	.byte	'_Ifx_VADC_G_BFLNP_Bits',0,8,180,2,16,4,11
	.byte	'BFL0NP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BFL1NP',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'BFL2NP',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'BFL3NP',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_BFLNP_Bits',0,8,187,2,3
	.word	13092
	.byte	10
	.byte	'_Ifx_VADC_G_BFLS_Bits',0,8,190,2,16,4,11
	.byte	'BFC0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'BFC1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'BFC2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'BFC3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	892
	.byte	12,0,2,35,0,11
	.byte	'BFS0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'BFS1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'BFS2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'BFS3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	892
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_BFLS_Bits',0,8,202,2,3
	.word	13248
	.byte	10
	.byte	'_Ifx_VADC_G_BOUND_Bits',0,8,205,2,16,4,11
	.byte	'BOUNDARY0',0,2
	.word	892
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'BOUNDARY1',0,2
	.word	892
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_BOUND_Bits',0,8,211,2,3
	.word	13480
	.byte	10
	.byte	'_Ifx_VADC_G_CEFCLR_Bits',0,8,214,2,16,4,11
	.byte	'CEV0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CEV1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CEV2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CEV3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CEV4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CEV5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CEV6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CEV7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CEV8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CEV9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CEV10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CEV11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'CEV12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CEV13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CEV14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'CEV15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_CEFCLR_Bits',0,8,233,2,3
	.word	13629
	.byte	10
	.byte	'_Ifx_VADC_G_CEFLAG_Bits',0,8,236,2,16,4,11
	.byte	'CEV0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CEV1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CEV2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CEV3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CEV4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CEV5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CEV6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CEV7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'CEV8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CEV9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CEV10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CEV11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'CEV12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CEV13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CEV14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'CEV15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_CEFLAG_Bits',0,8,255,2,3
	.word	13977
	.byte	10
	.byte	'_Ifx_VADC_G_CEVNP0_Bits',0,8,130,3,16,4,11
	.byte	'CEV0NP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'CEV1NP',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'CEV2NP',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'CEV3NP',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'CEV4NP',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'CEV5NP',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'CEV6NP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'CEV7NP',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_CEVNP0_Bits',0,8,140,3,3
	.word	14325
	.byte	10
	.byte	'_Ifx_VADC_G_CEVNP1_Bits',0,8,143,3,16,4,11
	.byte	'CEV8NP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'CEV9NP',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'CEV10NP',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'CEV11NP',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'CEV12NP',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'CEV13NP',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'CEV14NP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'CEV15NP',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_CEVNP1_Bits',0,8,153,3,3
	.word	14532
	.byte	10
	.byte	'_Ifx_VADC_G_CHASS_Bits',0,8,156,3,16,4,11
	.byte	'ASSCH0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ASSCH1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ASSCH2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ASSCH3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ASSCH4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ASSCH5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ASSCH6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ASSCH7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ASSCH8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ASSCH9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ASSCH10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ASSCH11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ASSCH12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'ASSCH13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'ASSCH14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ASSCH15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'ASSCH16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'ASSCH17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'ASSCH18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'ASSCH19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ASSCH20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'ASSCH21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'ASSCH22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'ASSCH23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'ASSCH24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'ASSCH25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'ASSCH26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'ASSCH27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'ASSCH28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'ASSCH29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'ASSCH30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'ASSCH31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_CHASS_Bits',0,8,190,3,3
	.word	14745
	.byte	10
	.byte	'_Ifx_VADC_G_CHCTR_Bits',0,8,193,3,16,4,11
	.byte	'ICLSEL',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'BNDSELL',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'BNDSELU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHEVMODE',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SYNC',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'REFSEL',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'BNDSELX',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'RESREG',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'RESTBS',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'RESPOS',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	892
	.byte	6,4,2,35,2,11
	.byte	'BWDCH',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'BWDEN',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_CHCTR_Bits',0,8,210,3,3
	.word	15404
	.byte	10
	.byte	'_Ifx_VADC_G_EMUXCTR_Bits',0,8,213,3,16,4,11
	.byte	'EMUXSET',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'EMUXACT',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'EMUXCH',0,2
	.word	892
	.byte	10,6,2,35,2,11
	.byte	'EMUXMODE',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'EMXCOD',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EMXST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EMXCSS',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EMXWC',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_EMUXCTR_Bits',0,8,225,3,3
	.word	15750
	.byte	10
	.byte	'_Ifx_VADC_G_Q0R0_Bits',0,8,228,3,16,4,11
	.byte	'REQCHNR',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'RF',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ENSI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EXTR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'V',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_Q0R0_Bits',0,8,236,3,3
	.word	16006
	.byte	10
	.byte	'_Ifx_VADC_G_QBUR0_Bits',0,8,239,3,16,4,11
	.byte	'REQCHNR',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'RF',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ENSI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EXTR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'V',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_QBUR0_Bits',0,8,247,3,3
	.word	16165
	.byte	10
	.byte	'_Ifx_VADC_G_QCTRL0_Bits',0,8,250,3,16,4,11
	.byte	'SRCRESREG',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'XTSEL',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'XTLVL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'XTMODE',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'XTWC',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'GTSEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'GTLVL',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'GTWC',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'TMEN',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'TMWC',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_QCTRL0_Bits',0,8,138,4,3
	.word	16326
	.byte	10
	.byte	'_Ifx_VADC_G_QINR0_Bits',0,8,141,4,16,4,11
	.byte	'REQCHNR',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'RF',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ENSI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EXTR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_QINR0_Bits',0,8,148,4,3
	.word	16651
	.byte	10
	.byte	'_Ifx_VADC_G_QMR0_Bits',0,8,151,4,16,4,11
	.byte	'ENGT',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ENTR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'CLRV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TREV',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'FLUSH',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CEV',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'RPTDIS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	892
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_QMR0_Bits',0,8,163,4,3
	.word	16799
	.byte	10
	.byte	'_Ifx_VADC_G_QSR0_Bits',0,8,166,4,16,4,11
	.byte	'FILL',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EMPTY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'REQGT',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_QSR0_Bits',0,8,175,4,3
	.word	17040
	.byte	10
	.byte	'_Ifx_VADC_G_RCR_Bits',0,8,178,4,16,4,11
	.byte	'reserved_0',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'DRCTR',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'DMM',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'WFR',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'FEN',0,1
	.word	493
	.byte	2,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'SRGEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_RCR_Bits',0,8,188,4,3
	.word	17229
	.byte	10
	.byte	'_Ifx_VADC_G_REFCLR_Bits',0,8,191,4,16,4,11
	.byte	'REV0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'REV1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'REV2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'REV3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'REV4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'REV5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'REV6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'REV7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'REV8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REV9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'REV10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'REV11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'REV12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'REV13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'REV14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'REV15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_REFCLR_Bits',0,8,210,4,3
	.word	17433
	.byte	10
	.byte	'_Ifx_VADC_G_REFLAG_Bits',0,8,213,4,16,4,11
	.byte	'REV0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'REV1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'REV2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'REV3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'REV4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'REV5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'REV6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'REV7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'REV8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REV9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'REV10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'REV11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'REV12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'REV13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'REV14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'REV15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_REFLAG_Bits',0,8,232,4,3
	.word	17781
	.byte	10
	.byte	'_Ifx_VADC_G_RES_Bits',0,8,235,4,16,4,11
	.byte	'RESULT',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'DRC',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	892
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	493
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_RES_Bits',0,8,244,4,3
	.word	18129
	.byte	10
	.byte	'_Ifx_VADC_G_RESD_Bits',0,8,247,4,16,4,11
	.byte	'RESULT',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'DRC',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	892
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	493
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_RESD_Bits',0,8,128,5,3
	.word	18295
	.byte	10
	.byte	'_Ifx_VADC_G_REVNP0_Bits',0,8,131,5,16,4,11
	.byte	'REV0NP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'REV1NP',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'REV2NP',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'REV3NP',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'REV4NP',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'REV5NP',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'REV6NP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'REV7NP',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_REVNP0_Bits',0,8,141,5,3
	.word	18463
	.byte	10
	.byte	'_Ifx_VADC_G_REVNP1_Bits',0,8,144,5,16,4,11
	.byte	'REV8NP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'REV9NP',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'REV10NP',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'REV11NP',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'REV12NP',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'REV13NP',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'REV14NP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'REV15NP',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_VADC_G_REVNP1_Bits',0,8,154,5,3
	.word	18670
	.byte	10
	.byte	'_Ifx_VADC_G_RRASS_Bits',0,8,157,5,16,4,11
	.byte	'ASSRR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ASSRR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ASSRR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ASSRR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ASSRR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ASSRR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ASSRR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ASSRR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ASSRR8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ASSRR9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ASSRR10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ASSRR11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ASSRR12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'ASSRR13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'ASSRR14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ASSRR15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_RRASS_Bits',0,8,176,5,3
	.word	18883
	.byte	10
	.byte	'_Ifx_VADC_G_SEFCLR_Bits',0,8,179,5,16,4,11
	.byte	'SEV0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEV1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_SEFCLR_Bits',0,8,184,5,3
	.word	19261
	.byte	10
	.byte	'_Ifx_VADC_G_SEFLAG_Bits',0,8,187,5,16,4,11
	.byte	'SEV0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEV1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_SEFLAG_Bits',0,8,192,5,3
	.word	19378
	.byte	10
	.byte	'_Ifx_VADC_G_SEVNP_Bits',0,8,195,5,16,4,11
	.byte	'SEV0NP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'SEV1NP',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_SEVNP_Bits',0,8,200,5,3
	.word	19495
	.byte	10
	.byte	'_Ifx_VADC_G_SRACT_Bits',0,8,203,5,16,4,11
	.byte	'AGSR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'AGSR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'AGSR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'AGSR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'ASSR0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ASSR1',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ASSR2',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ASSR3',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_SRACT_Bits',0,8,215,5,3
	.word	19614
	.byte	10
	.byte	'_Ifx_VADC_G_SYNCTR_Bits',0,8,218,5,16,4,11
	.byte	'STSEL',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVALR1',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EVALR2',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EVALR3',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	470
	.byte	25,0,2,35,0,0,21
	.byte	'Ifx_VADC_G_SYNCTR_Bits',0,8,226,5,3
	.word	19856
	.byte	10
	.byte	'_Ifx_VADC_G_VFR_Bits',0,8,229,5,16,4,11
	.byte	'VF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'VF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'VF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'VF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'VF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'VF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'VF8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'VF9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'VF10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'VF11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'VF12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'VF13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'VF14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'VF15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_G_VFR_Bits',0,8,248,5,3
	.word	20034
	.byte	10
	.byte	'_Ifx_VADC_GLOBBOUND_Bits',0,8,251,5,16,4,11
	.byte	'BOUNDARY0',0,2
	.word	892
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'BOUNDARY1',0,2
	.word	892
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_VADC_GLOBBOUND_Bits',0,8,129,6,3
	.word	20360
	.byte	10
	.byte	'_Ifx_VADC_GLOBCFG_Bits',0,8,132,6,16,4,11
	.byte	'DIVA',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'DCMSB',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'DIVD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'REFPC',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'LOSUP',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'DIVWC',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'DPCAL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'DPCAL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DPCAL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'DPCAL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	892
	.byte	11,1,2,35,2,11
	.byte	'SUCAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_GLOBCFG_Bits',0,8,149,6,3
	.word	20513
	.byte	10
	.byte	'_Ifx_VADC_GLOBEFLAG_Bits',0,8,152,6,16,4,11
	.byte	'SEVGLB',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	7,0,2,35,0,11
	.byte	'REVGLB',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'SEVGLBCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'REVGLBCLR',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_VADC_GLOBEFLAG_Bits',0,8,162,6,3
	.word	20854
	.byte	10
	.byte	'_Ifx_VADC_GLOBEVNP_Bits',0,8,165,6,16,4,11
	.byte	'SEV0NP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	892
	.byte	12,0,2,35,0,11
	.byte	'REV0NP',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	892
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_VADC_GLOBEVNP_Bits',0,8,171,6,3
	.word	21087
	.byte	10
	.byte	'_Ifx_VADC_GLOBRCR_Bits',0,8,174,6,16,4,11
	.byte	'reserved_0',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'DRCTR',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'WFR',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	6,1,2,35,3,11
	.byte	'SRGEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_GLOBRCR_Bits',0,8,182,6,3
	.word	21231
	.byte	10
	.byte	'_Ifx_VADC_GLOBRES_Bits',0,8,185,6,16,4,11
	.byte	'RESULT',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'GNR',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	892
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	493
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_GLOBRES_Bits',0,8,194,6,3
	.word	21409
	.byte	10
	.byte	'_Ifx_VADC_GLOBRESD_Bits',0,8,197,6,16,4,11
	.byte	'RESULT',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'GNR',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	892
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	493
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_VADC_GLOBRESD_Bits',0,8,206,6,3
	.word	21579
	.byte	10
	.byte	'_Ifx_VADC_GLOBTF_Bits',0,8,209,6,16,4,11
	.byte	'CDCH',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'CDGR',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'CDEN',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CDSEL',0,1
	.word	493
	.byte	2,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	4,1,2,35,1,11
	.byte	'CDWC',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PDD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MDPD',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MDPU',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	4,1,2,35,2,11
	.byte	'MDWC',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_VADC_GLOBTF_Bits',0,8,223,6,3
	.word	21751
	.byte	10
	.byte	'_Ifx_VADC_ICLASS_Bits',0,8,226,6,16,4,11
	.byte	'STCS',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'CMS',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'STCE',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'CME',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_VADC_ICLASS_Bits',0,8,236,6,3
	.word	22023
	.byte	10
	.byte	'_Ifx_VADC_ID_Bits',0,8,239,6,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_VADC_ID_Bits',0,8,244,6,3
	.word	22235
	.byte	10
	.byte	'_Ifx_VADC_KRST0_Bits',0,8,247,6,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_VADC_KRST0_Bits',0,8,252,6,3
	.word	22344
	.byte	10
	.byte	'_Ifx_VADC_KRST1_Bits',0,8,255,6,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_VADC_KRST1_Bits',0,8,131,7,3
	.word	22457
	.byte	10
	.byte	'_Ifx_VADC_KRSTCLR_Bits',0,8,134,7,16,4,11
	.byte	'CLR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_VADC_KRSTCLR_Bits',0,8,138,7,3
	.word	22551
	.byte	10
	.byte	'_Ifx_VADC_OCS_Bits',0,8,141,7,16,4,11
	.byte	'TGS',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_VADC_OCS_Bits',0,8,151,7,3
	.word	22649
	.byte	12,8,159,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9033
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_ACCEN0',0,8,164,7,3
	.word	22843
	.byte	12,8,167,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9592
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_ACCPROT0',0,8,172,7,3
	.word	22908
	.byte	12,8,175,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9858
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_ACCPROT1',0,8,180,7,3
	.word	22975
	.byte	12,8,183,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10108
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_BRSCTRL',0,8,188,7,3
	.word	23042
	.byte	12,8,191,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10375
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_BRSMR',0,8,196,7,3
	.word	23108
	.byte	12,8,199,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10648
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_BRSPND',0,8,204,7,3
	.word	23172
	.byte	12,8,207,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10726
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_BRSSEL',0,8,212,7,3
	.word	23237
	.byte	12,8,215,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10804
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_CLC',0,8,220,7,3
	.word	23302
	.byte	12,8,223,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10949
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_EMUXSEL',0,8,228,7,3
	.word	23364
	.byte	12,8,231,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11072
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_ALIAS',0,8,236,7,3
	.word	23430
	.byte	12,8,239,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11214
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_ARBCFG',0,8,244,7,3
	.word	23496
	.byte	12,8,247,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11549
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_ARBPR',0,8,252,7,3
	.word	23563
	.byte	12,8,255,7,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11873
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_ASCTRL',0,8,132,8,3
	.word	23629
	.byte	12,8,135,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_ASMR',0,8,140,8,3
	.word	23696
	.byte	12,8,143,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12473
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_ASPND',0,8,148,8,3
	.word	23761
	.byte	12,8,151,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12551
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_ASSEL',0,8,156,8,3
	.word	23827
	.byte	12,8,159,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12629
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_BFL',0,8,164,8,3
	.word	23893
	.byte	12,8,167,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12946
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_BFLC',0,8,172,8,3
	.word	23957
	.byte	12,8,175,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13092
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_BFLNP',0,8,180,8,3
	.word	24022
	.byte	12,8,183,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13248
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_BFLS',0,8,188,8,3
	.word	24088
	.byte	12,8,191,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13480
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_BOUND',0,8,196,8,3
	.word	24153
	.byte	12,8,199,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13629
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_CEFCLR',0,8,204,8,3
	.word	24219
	.byte	12,8,207,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13977
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_CEFLAG',0,8,212,8,3
	.word	24286
	.byte	12,8,215,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14325
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_CEVNP0',0,8,220,8,3
	.word	24353
	.byte	12,8,223,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14532
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_CEVNP1',0,8,228,8,3
	.word	24420
	.byte	12,8,231,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14745
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_CHASS',0,8,236,8,3
	.word	24487
	.byte	12,8,239,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15404
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_CHCTR',0,8,244,8,3
	.word	24553
	.byte	12,8,247,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15750
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_EMUXCTR',0,8,252,8,3
	.word	24619
	.byte	12,8,255,8,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16006
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_Q0R0',0,8,132,9,3
	.word	24687
	.byte	12,8,135,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16165
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_QBUR0',0,8,140,9,3
	.word	24752
	.byte	12,8,143,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16326
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_QCTRL0',0,8,148,9,3
	.word	24818
	.byte	12,8,151,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16651
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_QINR0',0,8,156,9,3
	.word	24885
	.byte	12,8,159,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16799
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_QMR0',0,8,164,9,3
	.word	24951
	.byte	12,8,167,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17040
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_QSR0',0,8,172,9,3
	.word	25016
	.byte	12,8,175,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17229
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_RCR',0,8,180,9,3
	.word	25081
	.byte	12,8,183,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_REFCLR',0,8,188,9,3
	.word	25145
	.byte	12,8,191,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17781
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_REFLAG',0,8,196,9,3
	.word	25212
	.byte	12,8,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18129
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_RES',0,8,204,9,3
	.word	25279
	.byte	12,8,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18295
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_RESD',0,8,212,9,3
	.word	25343
	.byte	12,8,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18463
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_REVNP0',0,8,220,9,3
	.word	25408
	.byte	12,8,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18670
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_REVNP1',0,8,228,9,3
	.word	25475
	.byte	12,8,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18883
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_RRASS',0,8,236,9,3
	.word	25542
	.byte	12,8,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19261
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_SEFCLR',0,8,244,9,3
	.word	25608
	.byte	12,8,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19378
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_SEFLAG',0,8,252,9,3
	.word	25675
	.byte	12,8,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19495
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_SEVNP',0,8,132,10,3
	.word	25742
	.byte	12,8,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19614
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_SRACT',0,8,140,10,3
	.word	25808
	.byte	12,8,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19856
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_SYNCTR',0,8,148,10,3
	.word	25874
	.byte	12,8,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20034
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_G_VFR',0,8,156,10,3
	.word	25941
	.byte	12,8,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20360
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBBOUND',0,8,164,10,3
	.word	26005
	.byte	12,8,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20513
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBCFG',0,8,172,10,3
	.word	26073
	.byte	12,8,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20854
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBEFLAG',0,8,180,10,3
	.word	26139
	.byte	12,8,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21087
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBEVNP',0,8,188,10,3
	.word	26207
	.byte	12,8,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21231
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBRCR',0,8,196,10,3
	.word	26274
	.byte	12,8,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21409
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBRES',0,8,204,10,3
	.word	26340
	.byte	12,8,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21579
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBRESD',0,8,212,10,3
	.word	26406
	.byte	12,8,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21751
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_GLOBTF',0,8,220,10,3
	.word	26473
	.byte	12,8,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22023
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_ICLASS',0,8,228,10,3
	.word	26538
	.byte	12,8,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22235
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_ID',0,8,236,10,3
	.word	26603
	.byte	12,8,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22344
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_KRST0',0,8,244,10,3
	.word	26664
	.byte	12,8,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22457
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_KRST1',0,8,252,10,3
	.word	26728
	.byte	12,8,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22551
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_KRSTCLR',0,8,132,11,3
	.word	26792
	.byte	12,8,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22649
	.byte	4,2,35,0,0,21
	.byte	'Ifx_VADC_OCS',0,8,140,11,3
	.word	26858
	.byte	15,16
	.word	493
	.byte	16,15,0,15,8
	.word	26538
	.byte	16,1,0,15,40
	.word	493
	.byte	16,39,0,12,8,175,11,5,4,13
	.byte	'QBUR0',0
	.word	24752
	.byte	4,2,35,0,13
	.byte	'QINR0',0
	.word	24885
	.byte	4,2,35,0,0,15,80
	.word	493
	.byte	16,79,0,15,36
	.word	493
	.byte	16,35,0,15,64
	.word	24553
	.byte	16,15,0,15,64
	.word	493
	.byte	16,63,0,15,64
	.word	25081
	.byte	16,15,0,15,64
	.word	25279
	.byte	16,15,0,15,64
	.word	25343
	.byte	16,15,0,15,192,1
	.word	493
	.byte	16,191,1,0,10
	.byte	'_Ifx_VADC_G',0,8,151,11,25,128,8,13
	.byte	'ARBCFG',0
	.word	23496
	.byte	4,2,35,0,13
	.byte	'ARBPR',0
	.word	23563
	.byte	4,2,35,4,13
	.byte	'CHASS',0
	.word	24487
	.byte	4,2,35,8,13
	.byte	'RRASS',0
	.word	25542
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	26920
	.byte	16,2,35,16,13
	.byte	'ICLASS',0
	.word	26929
	.byte	8,2,35,32,13
	.byte	'reserved_28',0
	.word	3739
	.byte	8,2,35,40,13
	.byte	'ALIAS',0
	.word	23430
	.byte	4,2,35,48,13
	.byte	'reserved_34',0
	.word	1920
	.byte	4,2,35,52,13
	.byte	'BOUND',0
	.word	24153
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	1920
	.byte	4,2,35,60,13
	.byte	'SYNCTR',0
	.word	25874
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	1920
	.byte	4,2,35,68,13
	.byte	'BFL',0
	.word	23893
	.byte	4,2,35,72,13
	.byte	'BFLS',0
	.word	24088
	.byte	4,2,35,76,13
	.byte	'BFLC',0
	.word	23957
	.byte	4,2,35,80,13
	.byte	'BFLNP',0
	.word	24022
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	26938
	.byte	40,2,35,88,13
	.byte	'QCTRL0',0
	.word	24818
	.byte	4,3,35,128,1,13
	.byte	'QMR0',0
	.word	24951
	.byte	4,3,35,132,1,13
	.byte	'QSR0',0
	.word	25016
	.byte	4,3,35,136,1,13
	.byte	'Q0R0',0
	.word	24687
	.byte	4,3,35,140,1,23
	.word	26947
	.byte	4,3,35,144,1,13
	.byte	'reserved_94',0
	.word	4079
	.byte	12,3,35,148,1,13
	.byte	'ASCTRL',0
	.word	23629
	.byte	4,3,35,160,1,13
	.byte	'ASMR',0
	.word	23696
	.byte	4,3,35,164,1,13
	.byte	'ASSEL',0
	.word	23827
	.byte	4,3,35,168,1,13
	.byte	'ASPND',0
	.word	23761
	.byte	4,3,35,172,1,13
	.byte	'reserved_B0',0
	.word	26984
	.byte	80,3,35,176,1,13
	.byte	'CEFLAG',0
	.word	24286
	.byte	4,3,35,128,2,13
	.byte	'REFLAG',0
	.word	25212
	.byte	4,3,35,132,2,13
	.byte	'SEFLAG',0
	.word	25675
	.byte	4,3,35,136,2,13
	.byte	'reserved_10C',0
	.word	1920
	.byte	4,3,35,140,2,13
	.byte	'CEFCLR',0
	.word	24219
	.byte	4,3,35,144,2,13
	.byte	'REFCLR',0
	.word	25145
	.byte	4,3,35,148,2,13
	.byte	'SEFCLR',0
	.word	25608
	.byte	4,3,35,152,2,13
	.byte	'reserved_11C',0
	.word	1920
	.byte	4,3,35,156,2,13
	.byte	'CEVNP0',0
	.word	24353
	.byte	4,3,35,160,2,13
	.byte	'CEVNP1',0
	.word	24420
	.byte	4,3,35,164,2,13
	.byte	'reserved_128',0
	.word	3739
	.byte	8,3,35,168,2,13
	.byte	'REVNP0',0
	.word	25408
	.byte	4,3,35,176,2,13
	.byte	'REVNP1',0
	.word	25475
	.byte	4,3,35,180,2,13
	.byte	'reserved_138',0
	.word	3739
	.byte	8,3,35,184,2,13
	.byte	'SEVNP',0
	.word	25742
	.byte	4,3,35,192,2,13
	.byte	'reserved_144',0
	.word	1920
	.byte	4,3,35,196,2,13
	.byte	'SRACT',0
	.word	25808
	.byte	4,3,35,200,2,13
	.byte	'reserved_14C',0
	.word	26993
	.byte	36,3,35,204,2,13
	.byte	'EMUXCTR',0
	.word	24619
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	1920
	.byte	4,3,35,244,2,13
	.byte	'VFR',0
	.word	25941
	.byte	4,3,35,248,2,13
	.byte	'reserved_17C',0
	.word	1920
	.byte	4,3,35,252,2,13
	.byte	'CHCTR',0
	.word	27002
	.byte	64,3,35,128,3,13
	.byte	'reserved_1C0',0
	.word	27011
	.byte	64,3,35,192,3,13
	.byte	'RCR',0
	.word	27020
	.byte	64,3,35,128,4,13
	.byte	'reserved_240',0
	.word	27011
	.byte	64,3,35,192,4,13
	.byte	'RES',0
	.word	27029
	.byte	64,3,35,128,5,13
	.byte	'reserved_2C0',0
	.word	27011
	.byte	64,3,35,192,5,13
	.byte	'RESD',0
	.word	27038
	.byte	64,3,35,128,6,13
	.byte	'reserved_340',0
	.word	27047
	.byte	192,1,3,35,192,6,0,14
	.word	27058
	.byte	21
	.byte	'Ifx_VADC_G',0,8,217,11,3
	.word	28133
	.byte	15,28
	.word	493
	.byte	16,27,0,15,92
	.word	493
	.byte	16,91,0,15,16
	.word	23237
	.byte	16,3,0,15,48
	.word	493
	.byte	16,47,0,15,16
	.word	23172
	.byte	16,3,0,15,120
	.word	493
	.byte	16,119,0,15,124
	.word	493
	.byte	16,123,0,15,108
	.word	493
	.byte	16,107,0,15,140,1
	.word	493
	.byte	16,139,1,0,15,128,32
	.word	27058
	.byte	16,3,0,14
	.word	28241
	.byte	15,128,87
	.word	493
	.byte	16,255,86,0,10
	.byte	'_Ifx_VADC',0,8,230,11,25,128,128,1,13
	.byte	'CLC',0
	.word	23302
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1920
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	26603
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	28158
	.byte	28,2,35,12,13
	.byte	'OCS',0
	.word	26858
	.byte	4,2,35,40,13
	.byte	'KRSTCLR',0
	.word	26792
	.byte	4,2,35,44,13
	.byte	'KRST1',0
	.word	26728
	.byte	4,2,35,48,13
	.byte	'KRST0',0
	.word	26664
	.byte	4,2,35,52,13
	.byte	'reserved_38',0
	.word	1920
	.byte	4,2,35,56,13
	.byte	'ACCEN0',0
	.word	22843
	.byte	4,2,35,60,13
	.byte	'reserved_40',0
	.word	27011
	.byte	64,2,35,64,13
	.byte	'GLOBCFG',0
	.word	26073
	.byte	4,3,35,128,1,13
	.byte	'reserved_84',0
	.word	1920
	.byte	4,3,35,132,1,13
	.byte	'ACCPROT0',0
	.word	22908
	.byte	4,3,35,136,1,13
	.byte	'ACCPROT1',0
	.word	22975
	.byte	4,3,35,140,1,13
	.byte	'reserved_90',0
	.word	26920
	.byte	16,3,35,144,1,13
	.byte	'GLOBICLASS',0
	.word	26929
	.byte	8,3,35,160,1,13
	.byte	'reserved_A8',0
	.word	26920
	.byte	16,3,35,168,1,13
	.byte	'GLOBBOUND',0
	.word	26005
	.byte	4,3,35,184,1,13
	.byte	'reserved_BC',0
	.word	26993
	.byte	36,3,35,188,1,13
	.byte	'GLOBEFLAG',0
	.word	26139
	.byte	4,3,35,224,1,13
	.byte	'reserved_E4',0
	.word	28167
	.byte	92,3,35,228,1,13
	.byte	'GLOBEVNP',0
	.word	26207
	.byte	4,3,35,192,2,13
	.byte	'reserved_144',0
	.word	28158
	.byte	28,3,35,196,2,13
	.byte	'GLOBTF',0
	.word	26473
	.byte	4,3,35,224,2,13
	.byte	'reserved_164',0
	.word	28158
	.byte	28,3,35,228,2,13
	.byte	'BRSSEL',0
	.word	28176
	.byte	16,3,35,128,3,13
	.byte	'reserved_190',0
	.word	28185
	.byte	48,3,35,144,3,13
	.byte	'BRSPND',0
	.word	28194
	.byte	16,3,35,192,3,13
	.byte	'reserved_1D0',0
	.word	28185
	.byte	48,3,35,208,3,13
	.byte	'BRSCTRL',0
	.word	23042
	.byte	4,3,35,128,4,13
	.byte	'BRSMR',0
	.word	23108
	.byte	4,3,35,132,4,13
	.byte	'reserved_208',0
	.word	28203
	.byte	120,3,35,136,4,13
	.byte	'GLOBRCR',0
	.word	26274
	.byte	4,3,35,128,5,13
	.byte	'reserved_284',0
	.word	28212
	.byte	124,3,35,132,5,13
	.byte	'GLOBRES',0
	.word	26340
	.byte	4,3,35,128,6,13
	.byte	'reserved_304',0
	.word	28212
	.byte	124,3,35,132,6,13
	.byte	'GLOBRESD',0
	.word	26406
	.byte	4,3,35,128,7,13
	.byte	'reserved_384',0
	.word	28221
	.byte	108,3,35,132,7,13
	.byte	'EMUXSEL',0
	.word	23364
	.byte	4,3,35,240,7,13
	.byte	'reserved_3F4',0
	.word	28230
	.byte	140,1,3,35,244,7,13
	.byte	'G',0
	.word	28251
	.byte	128,32,3,35,128,9,13
	.byte	'reserved_1480',0
	.word	28256
	.byte	128,87,3,35,128,41,0,14
	.word	28267
	.byte	21
	.byte	'Ifx_VADC',0,8,147,12,3
	.word	29119
	.byte	17,9,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,21
	.byte	'IfxSrc_Tos',0,9,74,3
	.word	29142
	.byte	21
	.byte	'boolean',0,10,101,29
	.word	493
	.byte	21
	.byte	'uint8',0,10,105,29
	.word	493
	.byte	21
	.byte	'uint16',0,10,109,29
	.word	892
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,10,113,29
	.word	29265
	.byte	21
	.byte	'uint64',0,10,118,29
	.word	352
	.byte	21
	.byte	'sint16',0,10,126,29
	.word	8940
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,10,131,1,29
	.word	29331
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,10,138,1,29
	.word	29359
	.byte	21
	.byte	'float32',0,10,167,1,29
	.word	298
	.byte	21
	.byte	'pvoid',0,11,57,28
	.word	384
	.byte	21
	.byte	'Ifx_TickTime',0,11,79,28
	.word	29359
	.byte	21
	.byte	'Ifx_Priority',0,11,103,16
	.word	892
	.byte	21
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	510
	.byte	21
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	800
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	29512
	.byte	21
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	29544
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	800
	.byte	4,2,35,8,0,14
	.word	29570
	.byte	21
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	29629
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	29657
	.byte	21
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	29694
	.byte	15,64
	.word	800
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	29722
	.byte	64,2,35,0,0,14
	.word	29731
	.byte	21
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	29763
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	800
	.byte	4,2,35,12,0,14
	.word	29788
	.byte	21
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	29860
	.byte	15,8
	.word	800
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	29886
	.byte	8,2,35,0,0,14
	.word	29895
	.byte	21
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	29931
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	800
	.byte	4,2,35,12,0,14
	.word	29961
	.byte	21
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	30034
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	30060
	.byte	21
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	30095
	.byte	15,192,1
	.word	800
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4079
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	30121
	.byte	192,1,2,35,16,0,14
	.word	30131
	.byte	21
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	30198
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	800
	.byte	4,2,35,4,0,14
	.word	30224
	.byte	21
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	30272
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	30300
	.byte	21
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	30333
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	29886
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	29886
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	29886
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	29886
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	800
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	800
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	26938
	.byte	40,2,35,40,0,14
	.word	30360
	.byte	21
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	30487
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	30514
	.byte	21
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	30546
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	30572
	.byte	21
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	30604
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	800
	.byte	4,2,35,8,0,14
	.word	30630
	.byte	21
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	30690
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	800
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	26920
	.byte	16,2,35,16,0,14
	.word	30716
	.byte	21
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	30810
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	800
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	800
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	800
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3110
	.byte	24,2,35,24,0,14
	.word	30837
	.byte	21
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	30954
	.byte	15,12
	.word	800
	.byte	16,2,0,15,32
	.word	800
	.byte	16,7,0,15,32
	.word	30991
	.byte	16,0,0,15,88
	.word	493
	.byte	16,87,0,15,108
	.word	800
	.byte	16,26,0,15,96
	.word	493
	.byte	16,95,0,15,96
	.word	30991
	.byte	16,2,0,15,160,3
	.word	493
	.byte	16,159,3,0,15,64
	.word	30991
	.byte	16,1,0,15,192,3
	.word	493
	.byte	16,191,3,0,15,16
	.word	800
	.byte	16,3,0,15,64
	.word	31076
	.byte	16,3,0,15,192,2
	.word	493
	.byte	16,191,2,0,15,52
	.word	493
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	30982
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	1920
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	800
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	800
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	29886
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	3739
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	31000
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	31009
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	31018
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	31027
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	800
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4079
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	31036
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	31045
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	31036
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	31045
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	31056
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	31065
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	31085
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	31094
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	30982
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	31105
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	30982
	.byte	12,3,35,192,18,0,14
	.word	31114
	.byte	21
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	31574
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	31600
	.byte	21
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	31633
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	800
	.byte	4,2,35,12,0,14
	.word	31660
	.byte	21
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	31733
	.byte	15,56
	.word	493
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	800
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	800
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	800
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	31760
	.byte	56,2,35,24,0,14
	.word	31769
	.byte	21
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	31892
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	31918
	.byte	21
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	31950
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	800
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	800
	.byte	4,2,35,16,0,14
	.word	31976
	.byte	21
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	32061
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	32087
	.byte	21
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	32119
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	30991
	.byte	32,2,35,0,0,14
	.word	32145
	.byte	21
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	32178
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	30991
	.byte	32,2,35,0,0,14
	.word	32205
	.byte	21
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	32239
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	800
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	800
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	800
	.byte	4,2,35,20,0,14
	.word	32267
	.byte	21
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	32360
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	32387
	.byte	21
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	32419
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	31076
	.byte	16,2,35,4,0,14
	.word	32445
	.byte	21
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	32491
	.byte	15,24
	.word	800
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	32517
	.byte	24,2,35,0,0,14
	.word	32526
	.byte	21
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	32559
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	30982
	.byte	12,2,35,0,0,14
	.word	32586
	.byte	21
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	32618
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	800
	.byte	4,2,35,4,0,14
	.word	32644
	.byte	21
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	32690
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	800
	.byte	4,2,35,12,0,14
	.word	32716
	.byte	21
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	32791
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	800
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	800
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	800
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	800
	.byte	4,2,35,12,0,14
	.word	32820
	.byte	21
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	32894
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	800
	.byte	4,2,35,0,0,14
	.word	32922
	.byte	21
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	32956
	.byte	15,4
	.word	29512
	.byte	16,0,0,14
	.word	32983
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	32992
	.byte	4,2,35,0,0,14
	.word	32997
	.byte	21
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	33033
	.byte	15,48
	.word	29570
	.byte	16,3,0,14
	.word	33061
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	33070
	.byte	48,2,35,0,0,14
	.word	33075
	.byte	21
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	33115
	.byte	14
	.word	29657
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	33145
	.byte	4,2,35,0,0,14
	.word	33150
	.byte	21
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	33184
	.byte	15,64
	.word	29731
	.byte	16,0,0,14
	.word	33211
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	33220
	.byte	64,2,35,0,0,14
	.word	33225
	.byte	21
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	33259
	.byte	15,32
	.word	29788
	.byte	16,1,0,14
	.word	33286
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	33295
	.byte	32,2,35,0,0,14
	.word	33300
	.byte	21
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	33336
	.byte	14
	.word	29895
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	33364
	.byte	8,2,35,0,0,14
	.word	33369
	.byte	21
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	33413
	.byte	15,16
	.word	29961
	.byte	16,0,0,14
	.word	33445
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	33454
	.byte	16,2,35,0,0,14
	.word	33459
	.byte	21
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	33493
	.byte	15,8
	.word	30060
	.byte	16,1,0,14
	.word	33520
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	33529
	.byte	8,2,35,0,0,14
	.word	33534
	.byte	21
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	33568
	.byte	15,208,1
	.word	30131
	.byte	16,0,0,14
	.word	33595
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	33605
	.byte	208,1,2,35,0,0,14
	.word	33610
	.byte	21
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	33646
	.byte	14
	.word	30224
	.byte	14
	.word	30224
	.byte	14
	.word	30224
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	33673
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	3739
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	33678
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	33683
	.byte	8,2,35,24,0,14
	.word	33688
	.byte	21
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	33779
	.byte	15,4
	.word	30300
	.byte	16,0,0,14
	.word	33808
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	33817
	.byte	4,2,35,0,0,14
	.word	33822
	.byte	21
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	33858
	.byte	15,80
	.word	30360
	.byte	16,0,0,14
	.word	33886
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	33895
	.byte	80,2,35,0,0,14
	.word	33900
	.byte	21
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	33936
	.byte	15,4
	.word	30514
	.byte	16,0,0,14
	.word	33964
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	33973
	.byte	4,2,35,0,0,14
	.word	33978
	.byte	21
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	34012
	.byte	15,4
	.word	30572
	.byte	16,0,0,14
	.word	34039
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	34048
	.byte	4,2,35,0,0,14
	.word	34053
	.byte	21
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	34087
	.byte	15,12
	.word	30630
	.byte	16,0,0,14
	.word	34114
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	34123
	.byte	12,2,35,0,0,14
	.word	34128
	.byte	21
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	34162
	.byte	15,64
	.word	30716
	.byte	16,1,0,14
	.word	34189
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	34198
	.byte	64,2,35,0,0,14
	.word	34203
	.byte	21
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	34239
	.byte	15,48
	.word	30837
	.byte	16,0,0,14
	.word	34267
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	34276
	.byte	48,2,35,0,0,14
	.word	34281
	.byte	21
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	34319
	.byte	15,204,18
	.word	31114
	.byte	16,0,0,14
	.word	34348
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	34358
	.byte	204,18,2,35,0,0,14
	.word	34363
	.byte	21
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	34399
	.byte	15,4
	.word	31600
	.byte	16,0,0,14
	.word	34426
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	34435
	.byte	4,2,35,0,0,14
	.word	34440
	.byte	21
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	34476
	.byte	15,64
	.word	31660
	.byte	16,3,0,14
	.word	34504
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	34513
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	800
	.byte	4,2,35,64,0,14
	.word	34518
	.byte	21
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	34567
	.byte	15,80
	.word	31769
	.byte	16,0,0,14
	.word	34595
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	34604
	.byte	80,2,35,0,0,14
	.word	34609
	.byte	21
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	34643
	.byte	15,4
	.word	31918
	.byte	16,0,0,14
	.word	34670
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	34679
	.byte	4,2,35,0,0,14
	.word	34684
	.byte	21
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	34718
	.byte	15,40
	.word	31976
	.byte	16,1,0,14
	.word	34745
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	34754
	.byte	40,2,35,0,0,14
	.word	34759
	.byte	21
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	34793
	.byte	15,8
	.word	32087
	.byte	16,1,0,14
	.word	34820
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	34829
	.byte	8,2,35,0,0,14
	.word	34834
	.byte	21
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	34868
	.byte	15,32
	.word	32145
	.byte	16,0,0,14
	.word	34895
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	34904
	.byte	32,2,35,0,0,14
	.word	34909
	.byte	21
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	34945
	.byte	15,32
	.word	32205
	.byte	16,0,0,14
	.word	34973
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	34982
	.byte	32,2,35,0,0,14
	.word	34987
	.byte	21
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	35025
	.byte	15,96
	.word	32267
	.byte	16,3,0,14
	.word	35054
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	35063
	.byte	96,2,35,0,0,14
	.word	35068
	.byte	21
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	35104
	.byte	15,4
	.word	32387
	.byte	16,0,0,14
	.word	35132
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	35141
	.byte	4,2,35,0,0,14
	.word	35146
	.byte	21
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	35180
	.byte	14
	.word	32445
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	35207
	.byte	20,2,35,0,0,14
	.word	35212
	.byte	21
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	35246
	.byte	15,24
	.word	32526
	.byte	16,0,0,14
	.word	35273
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	35282
	.byte	24,2,35,0,0,14
	.word	35287
	.byte	21
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	35323
	.byte	15,12
	.word	32586
	.byte	16,0,0,14
	.word	35351
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	35360
	.byte	12,2,35,0,0,14
	.word	35365
	.byte	21
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	35399
	.byte	15,16
	.word	32644
	.byte	16,1,0,14
	.word	35426
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	35435
	.byte	16,2,35,0,0,14
	.word	35440
	.byte	21
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	35474
	.byte	15,64
	.word	32820
	.byte	16,3,0,14
	.word	35501
	.byte	15,224,1
	.word	493
	.byte	16,223,1,0,15,32
	.word	32716
	.byte	16,1,0,14
	.word	35526
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	35510
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	35515
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	35535
	.byte	32,3,35,160,2,0,14
	.word	35540
	.byte	21
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	35609
	.byte	14
	.word	32922
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	35637
	.byte	4,2,35,0,0,14
	.word	35642
	.byte	21
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	35678
	.byte	17,12,119,9,1,18
	.byte	'IfxVadc_GroupId_0',0,0,18
	.byte	'IfxVadc_GroupId_1',0,1,18
	.byte	'IfxVadc_GroupId_2',0,2,18
	.byte	'IfxVadc_GroupId_3',0,3,18
	.byte	'IfxVadc_GroupId_global0',0,4,18
	.byte	'IfxVadc_GroupId_global1',0,5,0,21
	.byte	'IfxVadc_GroupId',0,12,127,3
	.word	35706
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	7492
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7405
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	3748
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	1801
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	2796
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	1929
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	2576
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2144
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2359
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	6764
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	6888
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	6972
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7152
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5403
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	5927
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	5577
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	5751
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6416
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1230
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	4740
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5228
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	4887
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5056
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6083
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	914
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4454
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4088
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3119
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3423
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8019
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7452
	.byte	21
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4039
	.byte	21
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	1880
	.byte	21
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3070
	.byte	21
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2104
	.byte	21
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	2756
	.byte	21
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2319
	.byte	21
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	2536
	.byte	21
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	6848
	.byte	21
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7097
	.byte	21
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7356
	.byte	21
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	6724
	.byte	21
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	5537
	.byte	21
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6043
	.byte	21
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	5711
	.byte	21
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	5887
	.byte	21
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	1761
	.byte	21
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6376
	.byte	21
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	4847
	.byte	21
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5363
	.byte	21
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5016
	.byte	21
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5188
	.byte	21
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1190
	.byte	21
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	4700
	.byte	21
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4414
	.byte	21
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3383
	.byte	21
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	3699
	.byte	14
	.word	8059
	.byte	21
	.byte	'Ifx_P',0,6,139,6,3
	.word	37186
	.byte	17,13,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,13,255,10,3
	.word	37206
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,14,79,3
	.word	37328
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,14,85,3
	.word	37885
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,14,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,14,94,3
	.word	37962
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,14,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,14,111,3
	.word	38098
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,14,114,16,4,11
	.byte	'CANDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,14,126,3
	.word	38378
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,14,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,14,135,1,3
	.word	38616
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,14,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,14,150,1,3
	.word	38744
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,14,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,14,165,1,3
	.word	38987
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,14,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,14,174,1,3
	.word	39222
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,14,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,14,181,1,3
	.word	39350
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,14,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,14,188,1,3
	.word	39450
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,14,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,14,202,1,3
	.word	39550
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,14,205,1,16,4,11
	.byte	'PWD',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,14,213,1,3
	.word	39758
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,14,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	892
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	892
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,14,225,1,3
	.word	39923
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,14,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	892
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,14,235,1,3
	.word	40106
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,14,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,14,129,2,3
	.word	40260
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,14,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,14,143,2,3
	.word	40624
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,14,146,2,16,4,11
	.byte	'POL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	892
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,14,159,2,3
	.word	40835
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,14,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	892
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,14,167,2,3
	.word	41087
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,14,170,2,16,4,11
	.byte	'ARI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,14,175,2,3
	.word	41205
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,14,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,14,185,2,3
	.word	41316
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,14,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,14,195,2,3
	.word	41479
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,14,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,14,205,2,3
	.word	41642
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,14,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,14,215,2,3
	.word	41800
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,14,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	892
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,14,232,2,3
	.word	41965
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,14,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	892
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	892
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,14,245,2,3
	.word	42294
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,14,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,14,255,2,3
	.word	42515
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,14,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,14,142,3,3
	.word	42678
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,14,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,14,152,3,3
	.word	42950
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,14,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,14,162,3,3
	.word	43103
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,14,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,14,172,3,3
	.word	43259
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,14,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,14,181,3,3
	.word	43421
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,14,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,14,191,3,3
	.word	43564
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,14,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,14,200,3,3
	.word	43729
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,14,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,14,211,3,3
	.word	43874
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,14,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,14,222,3,3
	.word	44055
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,14,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,14,232,3,3
	.word	44229
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,14,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,14,241,3,3
	.word	44389
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,14,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,14,130,4,3
	.word	44533
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,14,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,14,139,4,3
	.word	44807
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,14,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,14,149,4,3
	.word	44946
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,14,152,4,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	493
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	892
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	493
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,14,163,4,3
	.word	45109
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,14,166,4,16,4,11
	.byte	'STEP',0,2
	.word	892
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	892
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,14,174,4,3
	.word	45327
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,14,177,4,16,4,11
	.byte	'FS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,14,197,4,3
	.word	45490
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,14,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,14,205,4,3
	.word	45826
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,14,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,14,232,4,3
	.word	45933
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,14,235,4,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,14,240,4,3
	.word	46385
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,14,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,14,250,4,3
	.word	46484
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,14,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	892
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,14,131,5,3
	.word	46634
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,14,134,5,16,4,11
	.byte	'SEED',0,4
	.word	470
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,14,141,5,3
	.word	46783
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,14,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,14,149,5,3
	.word	46944
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,14,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	892
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,14,158,5,3
	.word	47074
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,14,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,14,166,5,3
	.word	47206
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,14,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	892
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,14,174,5,3
	.word	47321
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,14,177,5,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	892
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	892
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,14,185,5,3
	.word	47432
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,14,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,14,209,5,3
	.word	47590
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,14,212,5,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,14,217,5,3
	.word	48002
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,14,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	892
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,14,233,5,3
	.word	48103
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,14,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,14,242,5,3
	.word	48370
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,14,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,14,250,5,3
	.word	48506
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,14,253,5,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,14,132,6,3
	.word	48617
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,14,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,14,146,6,3
	.word	48750
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,14,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	892
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,14,166,6,3
	.word	48953
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,14,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	892
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,14,177,6,3
	.word	49309
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,14,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	892
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,14,184,6,3
	.word	49487
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,14,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	892
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,14,204,6,3
	.word	49587
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,14,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	892
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,14,215,6,3
	.word	49957
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,14,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,14,227,6,3
	.word	50143
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,14,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,14,241,6,3
	.word	50341
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,14,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,14,251,6,3
	.word	50574
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,14,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	493
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,14,153,7,3
	.word	50726
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,14,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,14,170,7,3
	.word	51293
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,14,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,14,187,7,3
	.word	51587
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,14,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	892
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,14,214,7,3
	.word	51865
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,14,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	892
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,14,230,7,3
	.word	52361
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,14,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	892
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,14,243,7,3
	.word	52674
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,14,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,14,129,8,3
	.word	52883
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,14,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,14,155,8,3
	.word	53094
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,14,158,8,16,4,11
	.byte	'HBT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,14,162,8,3
	.word	53526
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,14,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,14,178,8,3
	.word	53622
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,14,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,14,186,8,3
	.word	53882
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,14,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,14,198,8,3
	.word	54007
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,14,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,14,208,8,3
	.word	54204
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,14,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,14,218,8,3
	.word	54357
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,14,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,14,228,8,3
	.word	54510
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,14,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,14,238,8,3
	.word	54663
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,14,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	54818
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	54818
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	54818
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	54818
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,14,247,8,3
	.word	54834
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,14,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,14,134,9,3
	.word	54964
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,14,137,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,14,150,9,3
	.word	55202
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,14,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	54818
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	54818
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	54818
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	54818
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,14,159,9,3
	.word	55425
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,14,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,14,175,9,3
	.word	55551
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,14,178,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	892
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,14,191,9,3
	.word	55803
	.byte	12,14,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37328
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,14,204,9,3
	.word	56022
	.byte	12,14,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37885
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,14,212,9,3
	.word	56086
	.byte	12,14,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37962
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,14,220,9,3
	.word	56150
	.byte	12,14,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38098
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,14,228,9,3
	.word	56215
	.byte	12,14,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38378
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,14,236,9,3
	.word	56280
	.byte	12,14,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38616
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,14,244,9,3
	.word	56345
	.byte	12,14,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38744
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,14,252,9,3
	.word	56410
	.byte	12,14,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38987
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,14,132,10,3
	.word	56475
	.byte	12,14,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39222
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,14,140,10,3
	.word	56540
	.byte	12,14,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39350
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,14,148,10,3
	.word	56605
	.byte	12,14,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39450
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,14,156,10,3
	.word	56670
	.byte	12,14,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39550
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,14,164,10,3
	.word	56735
	.byte	12,14,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39758
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,14,172,10,3
	.word	56799
	.byte	12,14,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,14,180,10,3
	.word	56863
	.byte	12,14,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40106
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,14,188,10,3
	.word	56927
	.byte	12,14,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40260
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,14,196,10,3
	.word	56992
	.byte	12,14,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40624
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,14,204,10,3
	.word	57054
	.byte	12,14,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40835
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,14,212,10,3
	.word	57116
	.byte	12,14,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41087
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,14,220,10,3
	.word	57178
	.byte	12,14,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41205
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,14,228,10,3
	.word	57242
	.byte	12,14,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41316
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,14,236,10,3
	.word	57307
	.byte	12,14,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41479
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,14,244,10,3
	.word	57373
	.byte	12,14,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41642
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,14,252,10,3
	.word	57439
	.byte	12,14,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41800
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,14,132,11,3
	.word	57507
	.byte	12,14,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41965
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,14,140,11,3
	.word	57574
	.byte	12,14,143,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42294
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,14,148,11,3
	.word	57642
	.byte	12,14,151,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42515
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,14,156,11,3
	.word	57710
	.byte	12,14,159,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42678
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,14,164,11,3
	.word	57776
	.byte	12,14,167,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42950
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,14,172,11,3
	.word	57843
	.byte	12,14,175,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43103
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,14,180,11,3
	.word	57912
	.byte	12,14,183,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43259
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,14,188,11,3
	.word	57981
	.byte	12,14,191,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43421
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,14,196,11,3
	.word	58050
	.byte	12,14,199,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43564
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,14,204,11,3
	.word	58119
	.byte	12,14,207,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43729
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,14,212,11,3
	.word	58188
	.byte	12,14,215,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43874
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,14,220,11,3
	.word	58257
	.byte	12,14,223,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44055
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,14,228,11,3
	.word	58325
	.byte	12,14,231,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44229
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,14,236,11,3
	.word	58393
	.byte	12,14,239,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44389
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,14,244,11,3
	.word	58461
	.byte	12,14,247,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44533
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,14,252,11,3
	.word	58529
	.byte	12,14,255,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44807
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,14,132,12,3
	.word	58594
	.byte	12,14,135,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44946
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,14,140,12,3
	.word	58659
	.byte	12,14,143,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45109
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,14,148,12,3
	.word	58725
	.byte	12,14,151,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45327
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,14,156,12,3
	.word	58789
	.byte	12,14,159,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45490
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,14,164,12,3
	.word	58850
	.byte	12,14,167,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45826
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,14,172,12,3
	.word	58911
	.byte	12,14,175,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45933
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,14,180,12,3
	.word	58971
	.byte	12,14,183,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46385
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,14,188,12,3
	.word	59033
	.byte	12,14,191,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46484
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,14,196,12,3
	.word	59093
	.byte	12,14,199,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46634
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,14,204,12,3
	.word	59155
	.byte	12,14,207,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46783
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,14,212,12,3
	.word	59223
	.byte	12,14,215,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46944
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,14,220,12,3
	.word	59291
	.byte	12,14,223,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47074
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,14,228,12,3
	.word	59359
	.byte	12,14,231,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47206
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,14,236,12,3
	.word	59423
	.byte	12,14,239,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47321
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,14,244,12,3
	.word	59488
	.byte	12,14,247,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47432
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,14,252,12,3
	.word	59551
	.byte	12,14,255,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47590
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,14,132,13,3
	.word	59612
	.byte	12,14,135,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48002
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,14,140,13,3
	.word	59676
	.byte	12,14,143,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48103
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,14,148,13,3
	.word	59737
	.byte	12,14,151,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48370
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,14,156,13,3
	.word	59801
	.byte	12,14,159,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48506
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,14,164,13,3
	.word	59868
	.byte	12,14,167,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48617
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,14,172,13,3
	.word	59931
	.byte	12,14,175,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48750
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,14,180,13,3
	.word	59992
	.byte	12,14,183,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48953
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,14,188,13,3
	.word	60054
	.byte	12,14,191,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49309
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,14,196,13,3
	.word	60119
	.byte	12,14,199,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49487
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,14,204,13,3
	.word	60184
	.byte	12,14,207,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49587
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,14,212,13,3
	.word	60249
	.byte	12,14,215,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49957
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,14,220,13,3
	.word	60318
	.byte	12,14,223,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50143
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,14,228,13,3
	.word	60387
	.byte	12,14,231,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50341
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,14,236,13,3
	.word	60456
	.byte	12,14,239,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50574
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,14,244,13,3
	.word	60521
	.byte	12,14,247,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50726
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,14,252,13,3
	.word	60584
	.byte	12,14,255,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51293
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,14,132,14,3
	.word	60649
	.byte	12,14,135,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51587
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,14,140,14,3
	.word	60714
	.byte	12,14,143,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51865
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,14,148,14,3
	.word	60779
	.byte	12,14,151,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52361
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,14,156,14,3
	.word	60845
	.byte	12,14,159,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52883
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,14,164,14,3
	.word	60914
	.byte	12,14,167,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52674
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,14,172,14,3
	.word	60978
	.byte	12,14,175,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53094
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,14,180,14,3
	.word	61043
	.byte	12,14,183,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53526
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,14,188,14,3
	.word	61108
	.byte	12,14,191,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53622
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,14,196,14,3
	.word	61173
	.byte	12,14,199,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53882
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,14,204,14,3
	.word	61237
	.byte	12,14,207,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54007
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,14,212,14,3
	.word	61303
	.byte	12,14,215,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54204
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,14,220,14,3
	.word	61367
	.byte	12,14,223,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54357
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,14,228,14,3
	.word	61432
	.byte	12,14,231,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54510
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,14,236,14,3
	.word	61497
	.byte	12,14,239,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54663
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,14,244,14,3
	.word	61562
	.byte	12,14,247,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54834
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,14,252,14,3
	.word	61628
	.byte	12,14,255,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54964
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,14,132,15,3
	.word	61697
	.byte	12,14,135,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55202
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,14,140,15,3
	.word	61766
	.byte	12,14,143,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55425
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,14,148,15,3
	.word	61833
	.byte	12,14,151,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55551
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,14,156,15,3
	.word	61900
	.byte	12,14,159,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55803
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,14,164,15,3
	.word	61967
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,14,175,15,25,12,13
	.byte	'CON0',0
	.word	61628
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	61697
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	61766
	.byte	4,2,35,8,0,14
	.word	62032
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,14,180,15,3
	.word	62095
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,14,183,15,25,12,13
	.byte	'CON0',0
	.word	61833
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	61900
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	61967
	.byte	4,2,35,8,0,14
	.word	62124
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,14,188,15,3
	.word	62185
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	62212
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	62363
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	62607
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	62705
	.byte	21
	.byte	'IfxPort_State',0,5,178,1,3
	.word	8672
	.byte	24,5,190,1,9,8,13
	.byte	'port',0
	.word	8667
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	493
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	63170
	.byte	14
	.word	28267
	.byte	3
	.word	63230
	.byte	24,15,59,15,20,13
	.byte	'module',0
	.word	63235
	.byte	4,2,35,0,13
	.byte	'groupId',0
	.word	35706
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	63170
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	62363
	.byte	1,2,35,16,0,25
	.word	63240
	.byte	21
	.byte	'IfxVadc_GxBfl_Out',0,15,65,3
	.word	63308
	.byte	24,15,68,15,16,13
	.byte	'module',0
	.word	63235
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	63170
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	62363
	.byte	1,2,35,12,0,25
	.word	63339
	.byte	21
	.byte	'IfxVadc_Emux_Out',0,15,73,3
	.word	63390
	.byte	24,15,76,15,20,13
	.byte	'module',0
	.word	63235
	.byte	4,2,35,0,13
	.byte	'groupId',0
	.word	35706
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	63170
	.byte	8,2,35,8,13
	.byte	'channelId',0
	.word	493
	.byte	1,2,35,16,0,25
	.word	63420
	.byte	21
	.byte	'IfxVadc_Vadcg_In',0,15,82,3
	.word	63491
.L188:
	.byte	25
	.word	63339
.L189:
	.byte	25
	.word	63339
.L190:
	.byte	25
	.word	63339
.L191:
	.byte	25
	.word	63339
.L192:
	.byte	25
	.word	63339
.L193:
	.byte	25
	.word	63339
.L194:
	.byte	25
	.word	63339
.L195:
	.byte	25
	.word	63339
.L196:
	.byte	25
	.word	63339
.L197:
	.byte	25
	.word	63339
.L198:
	.byte	25
	.word	63339
.L199:
	.byte	25
	.word	63339
.L200:
	.byte	25
	.word	63240
.L201:
	.byte	25
	.word	63240
.L202:
	.byte	25
	.word	63240
.L203:
	.byte	25
	.word	63240
.L204:
	.byte	25
	.word	63240
.L205:
	.byte	25
	.word	63240
.L206:
	.byte	25
	.word	63240
.L207:
	.byte	25
	.word	63240
.L208:
	.byte	25
	.word	63240
.L209:
	.byte	25
	.word	63240
.L210:
	.byte	25
	.word	63240
.L211:
	.byte	25
	.word	63240
.L212:
	.byte	25
	.word	63240
.L213:
	.byte	25
	.word	63240
.L214:
	.byte	25
	.word	63240
.L215:
	.byte	25
	.word	63240
.L216:
	.byte	25
	.word	63240
.L217:
	.byte	25
	.word	63240
.L218:
	.byte	25
	.word	63420
.L219:
	.byte	25
	.word	63420
.L220:
	.byte	25
	.word	63420
.L221:
	.byte	25
	.word	63420
.L222:
	.byte	25
	.word	63420
.L223:
	.byte	25
	.word	63420
.L224:
	.byte	25
	.word	63420
.L225:
	.byte	25
	.word	63420
.L226:
	.byte	25
	.word	63420
.L227:
	.byte	25
	.word	63420
.L228:
	.byte	25
	.word	63420
.L229:
	.byte	25
	.word	63420
.L230:
	.byte	25
	.word	63420
.L231:
	.byte	25
	.word	63420
.L232:
	.byte	25
	.word	63420
.L233:
	.byte	25
	.word	63420
.L234:
	.byte	25
	.word	63420
.L235:
	.byte	25
	.word	63420
.L236:
	.byte	25
	.word	63420
.L237:
	.byte	25
	.word	63420
.L238:
	.byte	25
	.word	63420
.L239:
	.byte	25
	.word	63420
.L240:
	.byte	25
	.word	63420
.L241:
	.byte	25
	.word	63420
.L242:
	.byte	25
	.word	63420
.L243:
	.byte	25
	.word	63420
.L244:
	.byte	25
	.word	63420
.L245:
	.byte	25
	.word	63420
.L246:
	.byte	25
	.word	63420
.L247:
	.byte	25
	.word	63420
.L248:
	.byte	25
	.word	63420
.L249:
	.byte	25
	.word	63420
.L250:
	.byte	25
	.word	63420
.L251:
	.byte	25
	.word	63420
.L252:
	.byte	25
	.word	63420
.L253:
	.byte	25
	.word	63420
.L254:
	.byte	25
	.word	63420
.L255:
	.byte	25
	.word	63420
.L256:
	.byte	25
	.word	63420
.L257:
	.byte	25
	.word	63420
.L258:
	.byte	25
	.word	63420
.L259:
	.byte	25
	.word	63420
.L260:
	.byte	25
	.word	63420
.L261:
	.byte	25
	.word	63420
.L262:
	.byte	25
	.word	63420
.L263:
	.byte	25
	.word	63420
.L264:
	.byte	25
	.word	63420
.L265:
	.byte	25
	.word	63420
.L266:
	.byte	25
	.word	63420
.L267:
	.byte	25
	.word	63420
.L268:
	.byte	25
	.word	63420
.L269:
	.byte	25
	.word	63420
.L270:
	.byte	25
	.word	63420
.L271:
	.byte	25
	.word	63420
.L272:
	.byte	25
	.word	63420
.L273:
	.byte	25
	.word	63420
.L274:
	.byte	25
	.word	63420
.L275:
	.byte	25
	.word	63420
	.byte	25
	.word	63339
	.byte	3
	.word	63961
	.byte	15,48
	.word	63966
	.byte	16,11,0
.L276:
	.byte	15,48
	.word	63971
	.byte	16,0,0,25
	.word	63240
	.byte	3
	.word	63989
	.byte	15,24
	.word	63994
	.byte	16,5,0,15,96
	.word	63999
	.byte	16,3,0
.L277:
	.byte	15,96
	.word	64008
	.byte	16,0,0,25
	.word	63420
	.byte	3
	.word	64026
	.byte	15,64
	.word	64031
	.byte	16,15,0,15,128,2
	.word	64036
	.byte	16,3,0
.L278:
	.byte	15,128,2
	.word	64045
	.byte	16,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,13,0,73,19,11,15,56,9,0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,25,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L280-.L279
.L279:
	.half	3
	.word	.L282-.L281
.L281:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0,0,0,0
	.byte	'IfxVadc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxVadc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxVadc_PinMap.h',0,0,0,0,0
.L282:
.L280:
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX00_P02_6_OUT')
	.sect	'.debug_info'
.L6:
	.word	276
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX00_P02_6_OUT',0,7,48,18
	.word	.L188
	.byte	1,5,3
	.word	IfxVadc_EMUX00_P02_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX00_P02_6_OUT')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX00_P33_3_OUT')
	.sect	'.debug_info'
.L8:
	.word	276
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX00_P33_3_OUT',0,7,49,18
	.word	.L189
	.byte	1,5,3
	.word	IfxVadc_EMUX00_P33_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX00_P33_3_OUT')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX01_P02_7_OUT')
	.sect	'.debug_info'
.L10:
	.word	276
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX01_P02_7_OUT',0,7,50,18
	.word	.L190
	.byte	1,5,3
	.word	IfxVadc_EMUX01_P02_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX01_P02_7_OUT')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX01_P33_2_OUT')
	.sect	'.debug_info'
.L12:
	.word	276
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX01_P33_2_OUT',0,7,51,18
	.word	.L191
	.byte	1,5,3
	.word	IfxVadc_EMUX01_P33_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX01_P33_2_OUT')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX02_P02_8_OUT')
	.sect	'.debug_info'
.L14:
	.word	276
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX02_P02_8_OUT',0,7,52,18
	.word	.L192
	.byte	1,5,3
	.word	IfxVadc_EMUX02_P02_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX02_P02_8_OUT')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX02_P33_1_OUT')
	.sect	'.debug_info'
.L16:
	.word	276
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX02_P33_1_OUT',0,7,53,18
	.word	.L193
	.byte	1,5,3
	.word	IfxVadc_EMUX02_P33_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX02_P33_1_OUT')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX10_P00_6_OUT')
	.sect	'.debug_info'
.L18:
	.word	276
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX10_P00_6_OUT',0,7,54,18
	.word	.L194
	.byte	1,5,3
	.word	IfxVadc_EMUX10_P00_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX10_P00_6_OUT')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX10_P33_6_OUT')
	.sect	'.debug_info'
.L20:
	.word	276
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX10_P33_6_OUT',0,7,55,18
	.word	.L195
	.byte	1,5,3
	.word	IfxVadc_EMUX10_P33_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX10_P33_6_OUT')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX11_P00_7_OUT')
	.sect	'.debug_info'
.L22:
	.word	276
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX11_P00_7_OUT',0,7,56,18
	.word	.L196
	.byte	1,5,3
	.word	IfxVadc_EMUX11_P00_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX11_P00_7_OUT')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX11_P33_5_OUT')
	.sect	'.debug_info'
.L24:
	.word	276
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX11_P33_5_OUT',0,7,57,18
	.word	.L197
	.byte	1,5,3
	.word	IfxVadc_EMUX11_P33_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX11_P33_5_OUT')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX12_P00_8_OUT')
	.sect	'.debug_info'
.L26:
	.word	276
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX12_P00_8_OUT',0,7,58,18
	.word	.L198
	.byte	1,5,3
	.word	IfxVadc_EMUX12_P00_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX12_P00_8_OUT')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_EMUX12_P33_4_OUT')
	.sect	'.debug_info'
.L28:
	.word	276
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_EMUX12_P33_4_OUT',0,7,59,18
	.word	.L199
	.byte	1,5,3
	.word	IfxVadc_EMUX12_P33_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_EMUX12_P33_4_OUT')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0BFL0_P33_4_OUT')
	.sect	'.debug_info'
.L30:
	.word	276
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0BFL0_P33_4_OUT',0,7,60,19
	.word	.L200
	.byte	1,5,3
	.word	IfxVadc_G0BFL0_P33_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0BFL0_P33_4_OUT')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0BFL1_P33_5_OUT')
	.sect	'.debug_info'
.L32:
	.word	276
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0BFL1_P33_5_OUT',0,7,61,19
	.word	.L201
	.byte	1,5,3
	.word	IfxVadc_G0BFL1_P33_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0BFL1_P33_5_OUT')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0BFL2_P33_6_OUT')
	.sect	'.debug_info'
.L34:
	.word	276
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0BFL2_P33_6_OUT',0,7,62,19
	.word	.L202
	.byte	1,5,3
	.word	IfxVadc_G0BFL2_P33_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0BFL2_P33_6_OUT')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0BFL3_P33_7_OUT')
	.sect	'.debug_info'
.L36:
	.word	276
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0BFL3_P33_7_OUT',0,7,63,19
	.word	.L203
	.byte	1,5,3
	.word	IfxVadc_G0BFL3_P33_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0BFL3_P33_7_OUT')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1BFL0_P33_0_OUT')
	.sect	'.debug_info'
.L38:
	.word	276
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1BFL0_P33_0_OUT',0,7,64,19
	.word	.L204
	.byte	1,5,3
	.word	IfxVadc_G1BFL0_P33_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1BFL0_P33_0_OUT')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1BFL1_P33_1_OUT')
	.sect	'.debug_info'
.L40:
	.word	276
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1BFL1_P33_1_OUT',0,7,65,19
	.word	.L205
	.byte	1,5,3
	.word	IfxVadc_G1BFL1_P33_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1BFL1_P33_1_OUT')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1BFL2_P33_2_OUT')
	.sect	'.debug_info'
.L42:
	.word	276
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1BFL2_P33_2_OUT',0,7,66,19
	.word	.L206
	.byte	1,5,3
	.word	IfxVadc_G1BFL2_P33_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1BFL2_P33_2_OUT')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1BFL3_P33_3_OUT')
	.sect	'.debug_info'
.L44:
	.word	276
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1BFL3_P33_3_OUT',0,7,67,19
	.word	.L207
	.byte	1,5,3
	.word	IfxVadc_G1BFL3_P33_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1BFL3_P33_3_OUT')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2BFL0_P00_4_OUT')
	.sect	'.debug_info'
.L46:
	.word	276
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2BFL0_P00_4_OUT',0,7,68,19
	.word	.L208
	.byte	1,5,3
	.word	IfxVadc_G2BFL0_P00_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2BFL0_P00_4_OUT')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2BFL1_P00_5_OUT')
	.sect	'.debug_info'
.L48:
	.word	276
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2BFL1_P00_5_OUT',0,7,69,19
	.word	.L209
	.byte	1,5,3
	.word	IfxVadc_G2BFL1_P00_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2BFL1_P00_5_OUT')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2BFL2_P00_6_OUT')
	.sect	'.debug_info'
.L50:
	.word	276
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2BFL2_P00_6_OUT',0,7,70,19
	.word	.L210
	.byte	1,5,3
	.word	IfxVadc_G2BFL2_P00_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2BFL2_P00_6_OUT')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2BFL3_P00_7_OUT')
	.sect	'.debug_info'
.L52:
	.word	276
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2BFL3_P00_7_OUT',0,7,71,19
	.word	.L211
	.byte	1,5,3
	.word	IfxVadc_G2BFL3_P00_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2BFL3_P00_7_OUT')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3BFL0_P10_0_OUT')
	.sect	'.debug_info'
.L54:
	.word	276
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3BFL0_P10_0_OUT',0,7,72,19
	.word	.L212
	.byte	1,5,3
	.word	IfxVadc_G3BFL0_P10_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3BFL0_P10_0_OUT')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3BFL0_P10_6_OUT')
	.sect	'.debug_info'
.L56:
	.word	276
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3BFL0_P10_6_OUT',0,7,73,19
	.word	.L213
	.byte	1,5,3
	.word	IfxVadc_G3BFL0_P10_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3BFL0_P10_6_OUT')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3BFL1_P10_1_OUT')
	.sect	'.debug_info'
.L58:
	.word	276
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3BFL1_P10_1_OUT',0,7,74,19
	.word	.L214
	.byte	1,5,3
	.word	IfxVadc_G3BFL1_P10_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3BFL1_P10_1_OUT')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3BFL1_P10_7_OUT')
	.sect	'.debug_info'
.L60:
	.word	276
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3BFL1_P10_7_OUT',0,7,75,19
	.word	.L215
	.byte	1,5,3
	.word	IfxVadc_G3BFL1_P10_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3BFL1_P10_7_OUT')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3BFL2_P10_2_OUT')
	.sect	'.debug_info'
.L62:
	.word	276
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3BFL2_P10_2_OUT',0,7,76,19
	.word	.L216
	.byte	1,5,3
	.word	IfxVadc_G3BFL2_P10_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3BFL2_P10_2_OUT')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3BFL3_P10_3_OUT')
	.sect	'.debug_info'
.L64:
	.word	276
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3BFL3_P10_3_OUT',0,7,77,19
	.word	.L217
	.byte	1,5,3
	.word	IfxVadc_G3BFL3_P10_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3BFL3_P10_3_OUT')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_0_AN0_IN')
	.sect	'.debug_info'
.L66:
	.word	271
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_0_AN0_IN',0,7,78,18
	.word	.L218
	.byte	1,5,3
	.word	IfxVadc_G0_0_AN0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_0_AN0_IN')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_10_AN10_IN')
	.sect	'.debug_info'
.L68:
	.word	273
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_10_AN10_IN',0,7,79,18
	.word	.L219
	.byte	1,5,3
	.word	IfxVadc_G0_10_AN10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_10_AN10_IN')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_11_AN11_IN')
	.sect	'.debug_info'
.L70:
	.word	273
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_11_AN11_IN',0,7,80,18
	.word	.L220
	.byte	1,5,3
	.word	IfxVadc_G0_11_AN11_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_11_AN11_IN')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_12_AN12_IN')
	.sect	'.debug_info'
.L72:
	.word	273
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_12_AN12_IN',0,7,81,18
	.word	.L221
	.byte	1,5,3
	.word	IfxVadc_G0_12_AN12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_12_AN12_IN')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_13_AN13_IN')
	.sect	'.debug_info'
.L74:
	.word	273
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_13_AN13_IN',0,7,82,18
	.word	.L222
	.byte	1,5,3
	.word	IfxVadc_G0_13_AN13_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_13_AN13_IN')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_1_AN1_IN')
	.sect	'.debug_info'
.L76:
	.word	271
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_1_AN1_IN',0,7,83,18
	.word	.L223
	.byte	1,5,3
	.word	IfxVadc_G0_1_AN1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_1_AN1_IN')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_2_AN2_IN')
	.sect	'.debug_info'
.L78:
	.word	271
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_2_AN2_IN',0,7,84,18
	.word	.L224
	.byte	1,5,3
	.word	IfxVadc_G0_2_AN2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_2_AN2_IN')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_3_AN3_IN')
	.sect	'.debug_info'
.L80:
	.word	271
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_3_AN3_IN',0,7,85,18
	.word	.L225
	.byte	1,5,3
	.word	IfxVadc_G0_3_AN3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_3_AN3_IN')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_4_AN4_IN')
	.sect	'.debug_info'
.L82:
	.word	271
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_4_AN4_IN',0,7,86,18
	.word	.L226
	.byte	1,5,3
	.word	IfxVadc_G0_4_AN4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_4_AN4_IN')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_5_AN5_IN')
	.sect	'.debug_info'
.L84:
	.word	271
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_5_AN5_IN',0,7,87,18
	.word	.L227
	.byte	1,5,3
	.word	IfxVadc_G0_5_AN5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_5_AN5_IN')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_6_AN6_IN')
	.sect	'.debug_info'
.L86:
	.word	271
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_6_AN6_IN',0,7,88,18
	.word	.L228
	.byte	1,5,3
	.word	IfxVadc_G0_6_AN6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_6_AN6_IN')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_7_AN7_IN')
	.sect	'.debug_info'
.L88:
	.word	271
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_7_AN7_IN',0,7,89,18
	.word	.L229
	.byte	1,5,3
	.word	IfxVadc_G0_7_AN7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_7_AN7_IN')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G0_8_AN8_IN')
	.sect	'.debug_info'
.L90:
	.word	271
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G0_8_AN8_IN',0,7,90,18
	.word	.L230
	.byte	1,5,3
	.word	IfxVadc_G0_8_AN8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G0_8_AN8_IN')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_0_AN16_IN')
	.sect	'.debug_info'
.L92:
	.word	272
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_0_AN16_IN',0,7,91,18
	.word	.L231
	.byte	1,5,3
	.word	IfxVadc_G1_0_AN16_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_0_AN16_IN')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_10_AN26_IN')
	.sect	'.debug_info'
.L94:
	.word	273
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_10_AN26_IN',0,7,92,18
	.word	.L232
	.byte	1,5,3
	.word	IfxVadc_G1_10_AN26_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_10_AN26_IN')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_10_P40_2_IN')
	.sect	'.debug_info'
.L96:
	.word	274
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_10_P40_2_IN',0,7,93,18
	.word	.L233
	.byte	1,5,3
	.word	IfxVadc_G1_10_P40_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_10_P40_2_IN')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_11_AN27_IN')
	.sect	'.debug_info'
.L98:
	.word	273
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_11_AN27_IN',0,7,94,18
	.word	.L234
	.byte	1,5,3
	.word	IfxVadc_G1_11_AN27_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_11_AN27_IN')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_11_P40_3_IN')
	.sect	'.debug_info'
.L100:
	.word	274
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_11_P40_3_IN',0,7,95,18
	.word	.L235
	.byte	1,5,3
	.word	IfxVadc_G1_11_P40_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_11_P40_3_IN')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_12_AN28_IN')
	.sect	'.debug_info'
.L102:
	.word	273
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_12_AN28_IN',0,7,96,18
	.word	.L236
	.byte	1,5,3
	.word	IfxVadc_G1_12_AN28_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_12_AN28_IN')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_13_AN29_IN')
	.sect	'.debug_info'
.L104:
	.word	273
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_13_AN29_IN',0,7,97,18
	.word	.L237
	.byte	1,5,3
	.word	IfxVadc_G1_13_AN29_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_13_AN29_IN')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_1_AN17_IN')
	.sect	'.debug_info'
.L106:
	.word	272
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_1_AN17_IN',0,7,98,18
	.word	.L238
	.byte	1,5,3
	.word	IfxVadc_G1_1_AN17_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_1_AN17_IN')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_2_AN18_IN')
	.sect	'.debug_info'
.L108:
	.word	272
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_2_AN18_IN',0,7,99,18
	.word	.L239
	.byte	1,5,3
	.word	IfxVadc_G1_2_AN18_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_2_AN18_IN')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_3_AN19_IN')
	.sect	'.debug_info'
.L110:
	.word	272
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_3_AN19_IN',0,7,100,18
	.word	.L240
	.byte	1,5,3
	.word	IfxVadc_G1_3_AN19_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_3_AN19_IN')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_4_AN20_IN')
	.sect	'.debug_info'
.L112:
	.word	272
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_4_AN20_IN',0,7,101,18
	.word	.L241
	.byte	1,5,3
	.word	IfxVadc_G1_4_AN20_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_4_AN20_IN')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_5_AN21_IN')
	.sect	'.debug_info'
.L114:
	.word	272
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_5_AN21_IN',0,7,102,18
	.word	.L242
	.byte	1,5,3
	.word	IfxVadc_G1_5_AN21_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_5_AN21_IN')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_8_AN24_IN')
	.sect	'.debug_info'
.L116:
	.word	272
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_8_AN24_IN',0,7,103,18
	.word	.L243
	.byte	1,5,3
	.word	IfxVadc_G1_8_AN24_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_8_AN24_IN')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_8_P40_0_IN')
	.sect	'.debug_info'
.L118:
	.word	273
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_8_P40_0_IN',0,7,104,18
	.word	.L244
	.byte	1,5,3
	.word	IfxVadc_G1_8_P40_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_8_P40_0_IN')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_9_AN25_IN')
	.sect	'.debug_info'
.L120:
	.word	272
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_9_AN25_IN',0,7,105,18
	.word	.L245
	.byte	1,5,3
	.word	IfxVadc_G1_9_AN25_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_9_AN25_IN')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G1_9_P40_1_IN')
	.sect	'.debug_info'
.L122:
	.word	273
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G1_9_P40_1_IN',0,7,106,18
	.word	.L246
	.byte	1,5,3
	.word	IfxVadc_G1_9_P40_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G1_9_P40_1_IN')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_0_AN32_IN')
	.sect	'.debug_info'
.L124:
	.word	272
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_0_AN32_IN',0,7,107,18
	.word	.L247
	.byte	1,5,3
	.word	IfxVadc_G2_0_AN32_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_0_AN32_IN')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_10_AN44_IN')
	.sect	'.debug_info'
.L126:
	.word	273
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_10_AN44_IN',0,7,108,18
	.word	.L248
	.byte	1,5,3
	.word	IfxVadc_G2_10_AN44_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_10_AN44_IN')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_11_AN45_IN')
	.sect	'.debug_info'
.L128:
	.word	273
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_11_AN45_IN',0,7,109,18
	.word	.L249
	.byte	1,5,3
	.word	IfxVadc_G2_11_AN45_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_11_AN45_IN')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_12_AN46_IN')
	.sect	'.debug_info'
.L130:
	.word	273
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_12_AN46_IN',0,7,110,18
	.word	.L250
	.byte	1,5,3
	.word	IfxVadc_G2_12_AN46_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_12_AN46_IN')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_13_AN47_IN')
	.sect	'.debug_info'
.L132:
	.word	273
	.half	3
	.word	.L133
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_13_AN47_IN',0,7,111,18
	.word	.L251
	.byte	1,5,3
	.word	IfxVadc_G2_13_AN47_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_13_AN47_IN')
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_14_AN48_IN')
	.sect	'.debug_info'
.L134:
	.word	273
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_14_AN48_IN',0,7,112,18
	.word	.L252
	.byte	1,5,3
	.word	IfxVadc_G2_14_AN48_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_14_AN48_IN')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_15_AN49_IN')
	.sect	'.debug_info'
.L136:
	.word	273
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_15_AN49_IN',0,7,113,18
	.word	.L253
	.byte	1,5,3
	.word	IfxVadc_G2_15_AN49_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_15_AN49_IN')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_1_AN33_IN')
	.sect	'.debug_info'
.L138:
	.word	272
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_1_AN33_IN',0,7,114,18
	.word	.L254
	.byte	1,5,3
	.word	IfxVadc_G2_1_AN33_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_1_AN33_IN')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_3_AN35_IN')
	.sect	'.debug_info'
.L140:
	.word	272
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_3_AN35_IN',0,7,115,18
	.word	.L255
	.byte	1,5,3
	.word	IfxVadc_G2_3_AN35_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_3_AN35_IN')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_4_AN36_IN')
	.sect	'.debug_info'
.L142:
	.word	272
	.half	3
	.word	.L143
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_4_AN36_IN',0,7,116,18
	.word	.L256
	.byte	1,5,3
	.word	IfxVadc_G2_4_AN36_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_4_AN36_IN')
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_4_P40_6_IN')
	.sect	'.debug_info'
.L144:
	.word	273
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_4_P40_6_IN',0,7,117,18
	.word	.L257
	.byte	1,5,3
	.word	IfxVadc_G2_4_P40_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_4_P40_6_IN')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_5_AN37_IN')
	.sect	'.debug_info'
.L146:
	.word	272
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_5_AN37_IN',0,7,118,18
	.word	.L258
	.byte	1,5,3
	.word	IfxVadc_G2_5_AN37_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_5_AN37_IN')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_5_P40_7_IN')
	.sect	'.debug_info'
.L148:
	.word	273
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_5_P40_7_IN',0,7,119,18
	.word	.L259
	.byte	1,5,3
	.word	IfxVadc_G2_5_P40_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_5_P40_7_IN')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_6_AN38_IN')
	.sect	'.debug_info'
.L150:
	.word	272
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_6_AN38_IN',0,7,120,18
	.word	.L260
	.byte	1,5,3
	.word	IfxVadc_G2_6_AN38_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_6_AN38_IN')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_6_P40_8_IN')
	.sect	'.debug_info'
.L152:
	.word	273
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_6_P40_8_IN',0,7,121,18
	.word	.L261
	.byte	1,5,3
	.word	IfxVadc_G2_6_P40_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_6_P40_8_IN')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_7_AN39_IN')
	.sect	'.debug_info'
.L154:
	.word	272
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_7_AN39_IN',0,7,122,18
	.word	.L262
	.byte	1,5,3
	.word	IfxVadc_G2_7_AN39_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_7_AN39_IN')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G2_7_P40_9_IN')
	.sect	'.debug_info'
.L156:
	.word	273
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G2_7_P40_9_IN',0,7,123,18
	.word	.L263
	.byte	1,5,3
	.word	IfxVadc_G2_7_P40_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G2_7_P40_9_IN')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_0_P00_12_IN')
	.sect	'.debug_info'
.L158:
	.word	274
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_0_P00_12_IN',0,7,124,18
	.word	.L264
	.byte	1,5,3
	.word	IfxVadc_G3_0_P00_12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_0_P00_12_IN')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_10_P00_2_IN')
	.sect	'.debug_info'
.L160:
	.word	274
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_10_P00_2_IN',0,7,125,18
	.word	.L265
	.byte	1,5,3
	.word	IfxVadc_G3_10_P00_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_10_P00_2_IN')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_11_P00_1_IN')
	.sect	'.debug_info'
.L162:
	.word	274
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_11_P00_1_IN',0,7,126,18
	.word	.L266
	.byte	1,5,3
	.word	IfxVadc_G3_11_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_11_P00_1_IN')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_1_P00_11_IN')
	.sect	'.debug_info'
.L164:
	.word	274
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_1_P00_11_IN',0,7,127,18
	.word	.L267
	.byte	1,5,3
	.word	IfxVadc_G3_1_P00_11_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_1_P00_11_IN')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_2_P00_10_IN')
	.sect	'.debug_info'
.L166:
	.word	275
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_2_P00_10_IN',0,7,128,1,18
	.word	.L268
	.byte	1,5,3
	.word	IfxVadc_G3_2_P00_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_2_P00_10_IN')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_3_P00_9_IN')
	.sect	'.debug_info'
.L168:
	.word	274
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_3_P00_9_IN',0,7,129,1,18
	.word	.L269
	.byte	1,5,3
	.word	IfxVadc_G3_3_P00_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_3_P00_9_IN')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_4_P00_8_IN')
	.sect	'.debug_info'
.L170:
	.word	274
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_4_P00_8_IN',0,7,130,1,18
	.word	.L270
	.byte	1,5,3
	.word	IfxVadc_G3_4_P00_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_4_P00_8_IN')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_5_P00_7_IN')
	.sect	'.debug_info'
.L172:
	.word	274
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_5_P00_7_IN',0,7,131,1,18
	.word	.L271
	.byte	1,5,3
	.word	IfxVadc_G3_5_P00_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_5_P00_7_IN')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_6_P00_6_IN')
	.sect	'.debug_info'
.L174:
	.word	274
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_6_P00_6_IN',0,7,132,1,18
	.word	.L272
	.byte	1,5,3
	.word	IfxVadc_G3_6_P00_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_6_P00_6_IN')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_7_P00_5_IN')
	.sect	'.debug_info'
.L176:
	.word	274
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_7_P00_5_IN',0,7,133,1,18
	.word	.L273
	.byte	1,5,3
	.word	IfxVadc_G3_7_P00_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_7_P00_5_IN')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_8_P00_4_IN')
	.sect	'.debug_info'
.L178:
	.word	274
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_8_P00_4_IN',0,7,134,1,18
	.word	.L274
	.byte	1,5,3
	.word	IfxVadc_G3_8_P00_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_8_P00_4_IN')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_G3_9_P00_3_IN')
	.sect	'.debug_info'
.L180:
	.word	274
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_G3_9_P00_3_IN',0,7,135,1,18
	.word	.L275
	.byte	1,5,3
	.word	IfxVadc_G3_9_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_G3_9_P00_3_IN')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_Emux_Out_pinTable')
	.sect	'.debug_info'
.L182:
	.word	278
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_Emux_Out_pinTable',0,7,138,1,25
	.word	.L276
	.byte	1,5,3
	.word	IfxVadc_Emux_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_Emux_Out_pinTable')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_GxBfl_Out_pinTable')
	.sect	'.debug_info'
.L184:
	.word	279
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_GxBfl_Out_pinTable',0,7,155,1,26
	.word	.L277
	.byte	1,5,3
	.word	IfxVadc_GxBfl_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_GxBfl_Out_pinTable')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxVadc_Vadcg_In_pinTable')
	.sect	'.debug_info'
.L186:
	.word	278
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxVadc_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxVadc_Vadcg_In_pinTable',0,7,192,1,25
	.word	.L278
	.byte	1,5,3
	.word	IfxVadc_Vadcg_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxVadc_Vadcg_In_pinTable')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
