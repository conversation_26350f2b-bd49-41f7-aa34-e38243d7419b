/**
 * \file IfxStdIf_Pos.c
 * \brief Standard interface: Position interface
 * \ingroup IfxStdIf
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */

#include "IfxStdIf_Pos.h"

void IfxStdIf_Pos_initConfig (IfxStdIf_Pos_Config * config)
{
    config->offset = 0;
    config->reversed = FALSE;
    config->resolution = 0;
    config->periodPerRotation = 1;
    config->resolutionFactor = IfxStdIf_Pos_ResolutionFactor_oneFold;
    config->updatePeriod = 0.001;
    config->speedModeThreshold = 0;
    config->minSpeed = 0;       // 0 rpm
    config->maxSpeed = 20000.0 / 60.0 * (2 * IFX_PI);   // 20000 rpm
    config->speedFilterEnabled = FALSE;
    config->speedFilerCutOffFrequency = 0;

}

void IfxStdIf_Pos_printStatus(IfxStdIf_Pos *driver, IfxStdIf_DPipe *io)
{
    IfxStdIf_Pos_Status status;
    status = IfxStdIf_Pos_getFault(driver);

    IfxStdIf_DPipe_print(io, "DSADC RDC status:"ENDL);
    if (status.status != 0)
    {
        if (status.B.commError)
        {
        	IfxStdIf_DPipe_print(io, "- Communication error"ENDL);
        }
        if (status.B.notSynchronised)
        {
        	IfxStdIf_DPipe_print(io, "- Synchronization error"ENDL);
        }
        if (status.B.signalDegradation)
        {
        	IfxStdIf_DPipe_print(io, "- Signal degradation error"ENDL);
        }
        if (status.B.signalLoss)
        {
        	IfxStdIf_DPipe_print(io, "- Signal loss error"ENDL);
        }
        if (status.B.trackingLoss)
        {
        	IfxStdIf_DPipe_print(io, "- Tracking error"ENDL);
        }
    }
    else
    {
    	IfxStdIf_DPipe_print(io, "- Ready"ENDL);
    }
}
