	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18420a --dep-file=zf_driver_timer.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_timer.src ../libraries/zf_driver/zf_driver_timer.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_timer.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_timer.system_start',code,cluster('system_start')
	.sect	'.text.zf_driver_timer.system_start'
	.align	2
	
	.global	system_start
; Function system_start
.L14:
system_start:	.type	func
	mfcr	d15,#65052
.L66:
	and	d15,#7
.L67:
	j	.L2
.L2:
	extr	d4,d15,#0,#8
	call	IfxStm_getAddress
.L38:
	mfcr	d15,#65052
.L68:
	and	d15,#7
.L69:
	j	.L3
.L3:
	mul	d15,d15,#4
.L88:
	movh.a	a15,#@his(systick_count)
	lea	a15,[a15]@los(systick_count)
.L89:
	addsc.a	a15,a15,d15,#0
.L41:
	ld.w	d15,[a2]16
.L90:
	j	.L4
.L4:
	st.w	[a15],d15
.L91:
	ret
.L33:
	
__system_start_function_end:
	.size	system_start,__system_start_function_end-system_start
.L25:
	; End of function
	
	.sdecl	'.text.zf_driver_timer.system_getval',code,cluster('system_getval')
	.sect	'.text.zf_driver_timer.system_getval'
	.align	2
	
	.global	system_getval
; Function system_getval
.L16:
system_getval:	.type	func
	mfcr	d15,#65052
.L70:
	and	d15,#7
.L71:
	j	.L5
.L5:
	extr	d4,d15,#0,#8
	call	IfxStm_getAddress
.L51:
	jz.a	a2,.L6
.L6:
	call	IfxScuCcu_getSourceFrequency
.L96:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
	and	d15,#15
	itof	d15,d15
.L97:
	div.f	d15,d2,d15
.L98:
	j	.L7
.L7:
	j	.L8
.L8:
	ftouz	d8,d15
.L52:
	mfcr	d15,#65052
.L72:
	and	d15,#7
.L73:
	j	.L9
.L9:
	extr	d4,d15,#0,#8
	call	IfxStm_getAddress
.L62:
	ld.w	d4,[a2]16
.L99:
	j	.L10
.L10:
	mfcr	d15,#65052
.L74:
	and	d15,#7
.L75:
	j	.L11
.L11:
	mul	d15,d15,#4
.L100:
	movh.a	a15,#@his(systick_count)
	lea	a15,[a15]@los(systick_count)
.L101:
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]
.L76:
	sub	d4,d15
.L102:
	mov	d5,#0
.L103:
	mov.u	d6,#57600
	addih	d6,d6,#1525
	mov	d7,#0
.L104:
	call	__ll_mul64
.L77:
	mov	e4,d3,d2
.L105:
	mov	d7,#0
	mov	d6,d8
.L78:
	call	__ll_udiv64
.L79:
	j	.L12
.L12:
	ret
.L46:
	
__system_getval_function_end:
	.size	system_getval,__system_getval_function_end-system_getval
.L30:
	; End of function
	
	.sdecl	'.bss.zf_driver_timer.systick_count',data,cluster('systick_count')
	.sect	'.bss.zf_driver_timer.systick_count'
	.align	2
systick_count:	.type	object
	.size	systick_count,8
	.space	8
	.calls	'system_getval','__ll_mul64'
	.calls	'system_getval','__ll_udiv64'
	.calls	'system_start','IfxStm_getAddress'
	.calls	'system_getval','IfxStm_getAddress'
	.calls	'system_getval','IfxScuCcu_getSourceFrequency'
	.calls	'system_start','',0
	.extern	IfxScuCcu_getSourceFrequency
	.extern	IfxStm_getAddress
	.extern	__ll_mul64
	.extern	__ll_udiv64
	.calls	'system_getval','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L18:
	.word	80918
	.half	3
	.word	.L19
	.byte	4
.L17:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L20
	.byte	2,1,1,3
	.word	203
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	206
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L57:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	251
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	263
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	375
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	349
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	381
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	381
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	349
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	506
	.byte	4,2,35,0,0,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	681
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	602
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	885
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1116
	.byte	4,2,35,8,0,14
	.word	1156
	.byte	3
	.word	1219
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1224
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	659
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1224
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	659
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	659
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1224
	.byte	6,0,15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0
.L34:
	.byte	8
	.byte	'IfxCpu_getCoreId',0,3,5,133,6,22
	.word	1454
	.byte	1,1
.L35:
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1536
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	642
	.byte	1,1,6,0
.L45:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1691
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	659
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	642
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	659
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1691
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1691
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1922
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2238
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2809
	.byte	4,2,35,0,0,18,4
	.word	642
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2937
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3152
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3367
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3584
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3804
	.byte	4,2,35,0,0,18,24
	.word	642
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4127
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4431
	.byte	4,2,35,0,0,18,8
	.word	642
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4756
	.byte	4,2,35,0,0,18,12
	.word	642
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5096
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5748
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5895
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6064
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6236
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	659
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6411
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6585
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6759
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6935
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7091
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7424
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7772
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7896
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7980
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8160
	.byte	4,2,35,0,0,18,76
	.word	642
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8413
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2198
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2769
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2888
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2928
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3112
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3327
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3544
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3764
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2928
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4078
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4118
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4391
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4707
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4747
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5047
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5087
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5422
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5708
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4747
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5855
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6024
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6196
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6371
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6545
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6719
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6895
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7051
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7384
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7732
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4747
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7856
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8105
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8364
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8404
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8460
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9027
	.byte	4,3,35,252,1,0,14
	.word	9067
	.byte	3
	.word	9670
	.byte	15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9675
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	642
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	9680
	.byte	6,0
.L59:
	.byte	8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	263
	.byte	1,1
.L60:
	.byte	6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9901
	.byte	4,2,35,0,0,14
	.word	10191
	.byte	3
	.word	10230
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10235
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,13,100,16,4,11
	.byte	'DISR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,13,149,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10283
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,13,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,13,181,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10439
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,13,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,229,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10561
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,13,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,245,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10646
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,13,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,253,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10731
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,13,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,133,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10816
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,13,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,141,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10902
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,13,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,149,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10988
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,13,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,157,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11074
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,13,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,133,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11160
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,13,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,165,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11247
	.byte	4,2,35,0,0,18,8
	.word	11289
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,13,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,12,13,157,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11338
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,13,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	467
	.byte	25,0,2,35,0,0,12,13,173,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11569
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,13,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,13,189,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11786
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,13,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,237,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11950
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,13,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,141,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12037
	.byte	4,2,35,0,0,18,144,1
	.word	642
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,13,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	2,0,2,35,3,0,12,13,221,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12137
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,13,175,1,16,4,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,213,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12297
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,13,168,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,205,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12403
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,13,160,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,13,197,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12507
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,253,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12630
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,245,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12719
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,13,173,3,25,128,2,13
	.byte	'CLC',0
	.word	10399
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2928
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	10521
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2928
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	10606
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	10691
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	10776
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	10862
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	10948
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11034
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11120
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	11207
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	11329
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	11529
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	11746
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	11910
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5087
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	11997
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12086
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12126
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	12257
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	12363
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	12467
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	12590
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	12679
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13248
	.byte	4,3,35,252,1,0,14
	.word	13288
	.byte	3
	.word	13708
	.byte	8
	.byte	'IfxStm_get',0,3,12,162,4,19
	.word	349
	.byte	1,1,5
	.byte	'stm',0,12,162,4,39
	.word	13713
	.byte	6,0
.L50:
	.byte	8
	.byte	'IfxStm_getFrequency',0,3,12,179,4,20
	.word	263
	.byte	1,1
.L53:
	.byte	5
	.byte	'stm',0,12,179,4,49
	.word	13713
.L55:
	.byte	17
.L56:
	.byte	6,6,0,0
.L40:
	.byte	8
	.byte	'IfxStm_getLower',0,3,12,190,4,19
	.word	1691
	.byte	1,1
.L42:
	.byte	5
	.byte	'stm',0,12,190,4,44
	.word	13713
.L44:
	.byte	6,0,14
	.word	483
	.byte	20
	.byte	'__mfcr',0
	.word	13849
	.byte	1,1,1,1,21
	.word	483
	.byte	0,22
	.word	211
	.byte	23
	.word	237
	.byte	6,0,22
	.word	272
	.byte	23
	.word	304
	.byte	6,0,22
	.word	317
	.byte	6,0,22
	.word	386
	.byte	23
	.word	405
	.byte	6,0,22
	.word	421
	.byte	23
	.word	436
	.byte	23
	.word	450
	.byte	6,0,22
	.word	1229
	.byte	23
	.word	1269
	.byte	23
	.word	1287
	.byte	6,0,22
	.word	1307
	.byte	23
	.word	1345
	.byte	23
	.word	1363
	.byte	6,0,22
	.word	1383
	.byte	23
	.word	1434
	.byte	6,0,22
	.word	1505
	.byte	6,0,22
	.word	1615
	.byte	6,0,22
	.word	1649
	.byte	6,0,22
	.word	1712
	.byte	23
	.word	1753
	.byte	6,0,22
	.word	1772
	.byte	23
	.word	1827
	.byte	6,0,22
	.word	1846
	.byte	23
	.word	1886
	.byte	23
	.word	1903
	.byte	17,6,0,0,22
	.word	9783
	.byte	23
	.word	9811
	.byte	23
	.word	9825
	.byte	23
	.word	9843
	.byte	6,0,22
	.word	9861
	.byte	6,0,24
	.byte	'IfxScuCcu_getSourceFrequency',0,9,173,7,20
	.word	263
	.byte	1,1,1,1,22
	.word	10240
	.byte	23
	.word	10268
	.byte	6,0,22
	.word	13718
	.byte	23
	.word	13741
	.byte	6,0,22
	.word	13756
	.byte	23
	.word	13788
	.byte	17,17,25
	.word	9861
	.byte	26
	.word	9899
	.byte	0,0,6,0,0,15,14,87,9,1,16
	.byte	'IfxStm_Index_none',0,127,16
	.byte	'IfxStm_Index_0',0,0,16
	.byte	'IfxStm_Index_1',0,1,0,27
	.byte	'IfxStm_getAddress',0,12,209,2,21
	.word	13713
	.byte	1,1,1,1,5
	.byte	'stm',0,12,209,2,52
	.word	14168
	.byte	0,22
	.word	13806
	.byte	23
	.word	13834
	.byte	6,0,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,15,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0
.L36:
	.byte	12,15,223,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14285
	.byte	4,2,35,0,0,7
	.byte	'short int',0,2,5,28
	.byte	'__wchar_t',0,16,1,1
	.word	14394
	.byte	28
	.byte	'__size_t',0,16,1,1
	.word	467
	.byte	28
	.byte	'__ptrdiff_t',0,16,1,1
	.word	483
	.byte	29,1,3
	.word	14462
	.byte	28
	.byte	'__codeptr',0,16,1,1
	.word	14464
	.byte	28
	.byte	'__intptr_t',0,16,1,1
	.word	483
	.byte	28
	.byte	'__uintptr_t',0,16,1,1
	.word	467
	.byte	28
	.byte	'boolean',0,17,101,29
	.word	642
	.byte	28
	.byte	'uint8',0,17,105,29
	.word	642
	.byte	28
	.byte	'uint16',0,17,109,29
	.word	659
	.byte	28
	.byte	'uint32',0,17,113,29
	.word	1691
	.byte	28
	.byte	'uint64',0,17,118,29
	.word	349
	.byte	28
	.byte	'sint16',0,17,126,29
	.word	14394
	.byte	7
	.byte	'long int',0,4,5,28
	.byte	'sint32',0,17,131,1,29
	.word	14616
	.byte	7
	.byte	'long long int',0,8,5,28
	.byte	'sint64',0,17,138,1,29
	.word	14644
	.byte	28
	.byte	'float32',0,17,167,1,29
	.word	263
	.byte	28
	.byte	'pvoid',0,18,57,28
	.word	381
	.byte	28
	.byte	'Ifx_TickTime',0,18,79,28
	.word	14644
	.byte	28
	.byte	'Ifx_Priority',0,18,103,16
	.word	659
	.byte	15,18,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,28
	.byte	'Ifx_RxSel',0,18,140,1,3
	.word	14750
	.byte	28
	.byte	'Ifx_STM_ACCEN0_Bits',0,13,79,3
	.word	12719
	.byte	28
	.byte	'Ifx_STM_ACCEN1_Bits',0,13,85,3
	.word	12630
	.byte	28
	.byte	'Ifx_STM_CAP_Bits',0,13,91,3
	.word	11160
	.byte	28
	.byte	'Ifx_STM_CAPSV_Bits',0,13,97,3
	.word	12037
	.byte	28
	.byte	'Ifx_STM_CLC_Bits',0,13,107,3
	.word	10283
	.byte	28
	.byte	'Ifx_STM_CMCON_Bits',0,13,120,3
	.word	11338
	.byte	28
	.byte	'Ifx_STM_CMP_Bits',0,13,126,3
	.word	11247
	.byte	28
	.byte	'Ifx_STM_ICR_Bits',0,13,139,1,3
	.word	11569
	.byte	28
	.byte	'Ifx_STM_ID_Bits',0,13,147,1,3
	.word	10439
	.byte	28
	.byte	'Ifx_STM_ISCR_Bits',0,13,157,1,3
	.word	11786
	.byte	28
	.byte	'Ifx_STM_KRST0_Bits',0,13,165,1,3
	.word	12507
	.byte	28
	.byte	'Ifx_STM_KRST1_Bits',0,13,172,1,3
	.word	12403
	.byte	28
	.byte	'Ifx_STM_KRSTCLR_Bits',0,13,179,1,3
	.word	12297
	.byte	28
	.byte	'Ifx_STM_OCS_Bits',0,13,189,1,3
	.word	12137
	.byte	28
	.byte	'Ifx_STM_TIM0_Bits',0,13,195,1,3
	.word	10561
	.byte	28
	.byte	'Ifx_STM_TIM0SV_Bits',0,13,201,1,3
	.word	11950
	.byte	28
	.byte	'Ifx_STM_TIM1_Bits',0,13,207,1,3
	.word	10646
	.byte	28
	.byte	'Ifx_STM_TIM2_Bits',0,13,213,1,3
	.word	10731
	.byte	28
	.byte	'Ifx_STM_TIM3_Bits',0,13,219,1,3
	.word	10816
	.byte	28
	.byte	'Ifx_STM_TIM4_Bits',0,13,225,1,3
	.word	10902
	.byte	28
	.byte	'Ifx_STM_TIM5_Bits',0,13,231,1,3
	.word	10988
	.byte	28
	.byte	'Ifx_STM_TIM6_Bits',0,13,237,1,3
	.word	11074
	.byte	28
	.byte	'Ifx_STM_ACCEN0',0,13,250,1,3
	.word	13248
	.byte	28
	.byte	'Ifx_STM_ACCEN1',0,13,130,2,3
	.word	12679
	.byte	28
	.byte	'Ifx_STM_CAP',0,13,138,2,3
	.word	11207
	.byte	28
	.byte	'Ifx_STM_CAPSV',0,13,146,2,3
	.word	12086
	.byte	28
	.byte	'Ifx_STM_CLC',0,13,154,2,3
	.word	10399
	.byte	28
	.byte	'Ifx_STM_CMCON',0,13,162,2,3
	.word	11529
	.byte	28
	.byte	'Ifx_STM_CMP',0,13,170,2,3
	.word	11289
	.byte	28
	.byte	'Ifx_STM_ICR',0,13,178,2,3
	.word	11746
	.byte	28
	.byte	'Ifx_STM_ID',0,13,186,2,3
	.word	10521
	.byte	28
	.byte	'Ifx_STM_ISCR',0,13,194,2,3
	.word	11910
	.byte	28
	.byte	'Ifx_STM_KRST0',0,13,202,2,3
	.word	12590
	.byte	28
	.byte	'Ifx_STM_KRST1',0,13,210,2,3
	.word	12467
	.byte	28
	.byte	'Ifx_STM_KRSTCLR',0,13,218,2,3
	.word	12363
	.byte	28
	.byte	'Ifx_STM_OCS',0,13,226,2,3
	.word	12257
	.byte	28
	.byte	'Ifx_STM_TIM0',0,13,234,2,3
	.word	10606
	.byte	28
	.byte	'Ifx_STM_TIM0SV',0,13,242,2,3
	.word	11997
	.byte	28
	.byte	'Ifx_STM_TIM1',0,13,250,2,3
	.word	10691
	.byte	28
	.byte	'Ifx_STM_TIM2',0,13,130,3,3
	.word	10776
	.byte	28
	.byte	'Ifx_STM_TIM3',0,13,138,3,3
	.word	10862
	.byte	28
	.byte	'Ifx_STM_TIM4',0,13,146,3,3
	.word	10948
	.byte	28
	.byte	'Ifx_STM_TIM5',0,13,154,3,3
	.word	11034
	.byte	28
	.byte	'Ifx_STM_TIM6',0,13,162,3,3
	.word	11120
	.byte	14
	.word	13288
	.byte	28
	.byte	'Ifx_STM',0,13,201,3,3
	.word	15971
	.byte	28
	.byte	'IfxStm_Index',0,14,92,3
	.word	14168
	.byte	15,19,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,28
	.byte	'IfxScu_CCUCON0_CLKSEL',0,19,240,10,3
	.word	16014
	.byte	15,19,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,28
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	16111
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	16233
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	16790
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	16867
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	17003
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	642
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	17283
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	17521
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	17649
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	17892
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	18127
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	18255
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	18355
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	642
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	18455
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	467
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	18663
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	18828
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	19011
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	467
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	19165
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	19529
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	19740
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	19992
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	20110
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	20221
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	20384
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	20547
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	20705
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	642
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	10,0,2,35,2,0,28
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	20870
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	642
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	659
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	21199
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	21420
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	21583
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	21855
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	22008
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	22164
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	22326
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	22469
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	22634
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	22779
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	22960
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	23134
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	23294
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	23438
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	23712
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	23851
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	642
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	659
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	642
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	642
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	24014
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	24232
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	24395
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	24731
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	642
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	24838
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	25290
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	25389
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	25539
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	467
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	642
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	25688
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	25849
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	659
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	25979
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	26111
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	659
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	26226
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	26337
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	642
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	26495
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	26907
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	659
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	6,0,2,35,3,0,28
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	27008
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	27275
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	27411
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	27522
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	27655
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	27858
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	28214
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	28392
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	28492
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	28862
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	29048
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	29246
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	29479
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	642
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	29631
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	30198
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	30492
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	642
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	642
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	30770
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	31266
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	31579
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	31788
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	31999
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	32431
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	642
	.byte	7,0,2,35,3,0,28
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	32527
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	32787
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	32912
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	33109
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	33262
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	33415
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	33568
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	506
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	681
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	925
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	33823
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	33949
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	34201
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16233
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	34420
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16790
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	34484
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16867
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	34548
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17003
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	34613
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17283
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	34678
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17521
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	34743
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17649
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	34808
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17892
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	34873
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18127
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	34938
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18255
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	35003
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18355
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	35068
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18455
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	35133
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18663
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	35197
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18828
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	35261
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19011
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	35325
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19165
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	35390
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19529
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	35452
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19740
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	35514
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19992
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	35576
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20110
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	35640
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20221
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	35705
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20384
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	35771
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20547
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	35837
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20705
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	35905
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20870
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	35972
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21199
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	36040
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21420
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	36108
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21583
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	36174
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21855
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	36241
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22008
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	36310
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22164
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	36379
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22326
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	36448
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22469
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	36517
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22634
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	36586
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22779
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	36655
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22960
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	36723
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23134
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	36791
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23294
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	36859
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23438
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	36927
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23712
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	36992
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23851
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	37057
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24014
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	37123
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24232
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	37187
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24395
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	37248
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24731
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	37309
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24838
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	37369
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25290
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	37431
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25389
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	37491
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25539
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	37553
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25688
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	37621
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25849
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	37689
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25979
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	37757
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26111
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	37821
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26226
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	37886
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26337
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	37949
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26495
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	38010
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26907
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	38074
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27008
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	38135
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27275
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	38199
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27411
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	38266
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27522
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	38329
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27655
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	38390
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27858
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	38452
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28214
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	38517
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28392
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	38582
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28492
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	38647
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28862
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	38716
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29048
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	38785
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29246
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	38854
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29479
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	38919
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29631
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	38982
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30198
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	39047
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30492
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	39112
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30770
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	39177
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31266
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	39243
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31788
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	39312
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31579
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	39376
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31999
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	39441
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32431
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	39506
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32527
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	39571
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32787
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	39635
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32912
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	39701
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33109
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	39765
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33262
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	39830
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33415
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	39895
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33568
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	39960
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	602
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	885
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1116
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33823
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	40111
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33949
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	40178
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34201
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	40245
	.byte	14
	.word	1156
	.byte	28
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	40310
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	40111
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40178
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40245
	.byte	4,2,35,8,0,14
	.word	40339
	.byte	28
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	40400
	.byte	18,8
	.word	35576
	.byte	19,1,0,18,20
	.word	642
	.byte	19,19,0,18,8
	.word	38919
	.byte	19,1,0,14
	.word	40339
	.byte	18,24
	.word	1156
	.byte	19,1,0,14
	.word	40459
	.byte	18,16
	.word	642
	.byte	19,15,0,18,28
	.word	642
	.byte	19,27,0,18,40
	.word	642
	.byte	19,39,0,18,16
	.word	35390
	.byte	19,3,0,18,16
	.word	37369
	.byte	19,3,0,18,180,3
	.word	642
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4747
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	37309
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2928
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	38010
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	38854
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	38452
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	38517
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	38582
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	38785
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	38647
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	38716
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	34613
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	34678
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	37187
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	37123
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	34743
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	34808
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	34873
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	34938
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	39441
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2928
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	39312
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	34548
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	39635
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	39376
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2928
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	36174
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	40427
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	35640
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	39701
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	35003
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	35068
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	40436
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	38329
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	37491
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	38074
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	37949
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	37431
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	36927
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	35905
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	35705
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	35771
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	39571
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2928
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	38982
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	39177
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	39243
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	40445
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2928
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	35325
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	35197
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	39047
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	39112
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	40454
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	35514
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	40468
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5087
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	39960
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	39895
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	39765
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	39830
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2928
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	37757
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	37821
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	35133
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	37886
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4747
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	39506
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	40473
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	37553
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	37621
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	37689
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	40482
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	38266
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4747
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	36992
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	35837
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	37057
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	36108
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	35972
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2928
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	36655
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	36723
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	36791
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	36859
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	36241
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	36310
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	36379
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	36448
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	36517
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	36586
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	36040
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2928
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	38199
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	38135
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	40491
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	40500
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	35452
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	37248
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	38390
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	40509
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2928
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	35261
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	40518
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	34484
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	34420
	.byte	4,3,35,252,7,0,14
	.word	40529
	.byte	28
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	42519
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,15,45,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_A_Bits',0,15,48,3
	.word	42541
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,15,51,16,4,11
	.byte	'VSS',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	490
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BIV_Bits',0,15,55,3
	.word	42602
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,15,58,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	490
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BTV_Bits',0,15,62,3
	.word	42681
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,15,65,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT_Bits',0,15,69,3
	.word	42767
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,15,72,16,4,11
	.byte	'CM',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	490
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	490
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	490
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL_Bits',0,15,80,3
	.word	42856
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,15,83,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT_Bits',0,15,89,3
	.word	43002
	.byte	28
	.byte	'Ifx_CPU_CORE_ID_Bits',0,15,96,3
	.word	14285
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,15,99,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L_Bits',0,15,103,3
	.word	43158
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,15,106,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U_Bits',0,15,110,3
	.word	43251
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,15,113,16,4,11
	.byte	'MODREV',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	490
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID_Bits',0,15,118,3
	.word	43344
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,15,121,16,4,11
	.byte	'XE',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE_Bits',0,15,125,3
	.word	43451
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,15,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT_Bits',0,15,136,1,3
	.word	43538
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,15,139,1,16,4,11
	.byte	'CID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID_Bits',0,15,143,1,3
	.word	43692
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,15,146,1,16,4,11
	.byte	'DATA',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_D_Bits',0,15,149,1,3
	.word	43786
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,15,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	490
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DATR_Bits',0,15,163,1,3
	.word	43849
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,15,166,1,16,4,11
	.byte	'DE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	490
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	490
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	19,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR_Bits',0,15,177,1,3
	.word	44067
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,15,180,1,16,4,11
	.byte	'DTA',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR_Bits',0,15,184,1,3
	.word	44282
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,15,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0_Bits',0,15,192,1,3
	.word	44376
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,15,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2_Bits',0,15,199,1,3
	.word	44492
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,15,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	490
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCX_Bits',0,15,206,1,3
	.word	44593
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,15,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD_Bits',0,15,212,1,3
	.word	44686
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,15,215,1,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR_Bits',0,15,218,1,3
	.word	44766
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,15,221,1,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR_Bits',0,15,233,1,3
	.word	44835
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,15,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	490
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DMS_Bits',0,15,240,1,3
	.word	45064
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,15,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L_Bits',0,15,247,1,3
	.word	45157
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,15,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U_Bits',0,15,254,1,3
	.word	45252
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,15,129,2,16,4,11
	.byte	'RE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE_Bits',0,15,133,2,3
	.word	45347
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,15,136,2,16,4,11
	.byte	'WE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE_Bits',0,15,140,2,3
	.word	45437
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,15,143,2,16,4,11
	.byte	'SRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	490
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	490
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR_Bits',0,15,161,2,3
	.word	45527
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,15,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT_Bits',0,15,172,2,3
	.word	45851
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,15,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FCX_Bits',0,15,180,2,3
	.word	46005
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,15,183,2,16,4,11
	.byte	'TST',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	490
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	490
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,15,202,2,3
	.word	46111
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,15,205,2,16,4,11
	.byte	'OPC',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,15,212,2,3
	.word	46460
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,15,215,2,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,15,218,2,3
	.word	46620
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,15,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,15,224,2,3
	.word	46701
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,15,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,15,230,2,3
	.word	46788
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,15,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,15,236,2,3
	.word	46875
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,15,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT_Bits',0,15,243,2,3
	.word	46962
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,15,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	490
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	490
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	490
	.byte	6,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICR_Bits',0,15,253,2,3
	.word	47053
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,15,128,3,16,4,11
	.byte	'ISP',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_ISP_Bits',0,15,131,3,3
	.word	47196
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,15,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_LCX_Bits',0,15,139,3,3
	.word	47262
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,15,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT_Bits',0,15,146,3,3
	.word	47368
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,15,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT_Bits',0,15,153,3,3
	.word	47461
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,15,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT_Bits',0,15,160,3,3
	.word	47554
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,15,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	490
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_PC_Bits',0,15,167,3,3
	.word	47647
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,15,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0_Bits',0,15,175,3,3
	.word	47732
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,15,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1_Bits',0,15,183,3,3
	.word	47848
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,15,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2_Bits',0,15,190,3,3
	.word	47959
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,15,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	490
	.byte	10,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI_Bits',0,15,200,3,3
	.word	48060
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,15,203,3,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR_Bits',0,15,206,3,3
	.word	48190
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,15,209,3,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR_Bits',0,15,221,3,3
	.word	48259
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,15,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	490
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0_Bits',0,15,229,3,3
	.word	48488
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,15,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1_Bits',0,15,237,3,3
	.word	48601
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,15,240,3,16,4,11
	.byte	'PSI',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2_Bits',0,15,244,3,3
	.word	48714
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,15,247,3,16,4,11
	.byte	'FRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	17,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR_Bits',0,15,129,4,3
	.word	48805
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,15,132,4,16,4,11
	.byte	'CDC',0,4
	.word	490
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	490
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	490
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSW_Bits',0,15,147,4,3
	.word	49008
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,15,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	490
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN_Bits',0,15,156,4,3
	.word	49251
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,15,159,4,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON_Bits',0,15,171,4,3
	.word	49379
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,15,174,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,15,177,4,3
	.word	49620
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,15,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,15,183,4,3
	.word	49703
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,15,186,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,15,189,4,3
	.word	49794
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,15,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,15,195,4,3
	.word	49885
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,15,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,15,202,4,3
	.word	49984
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,15,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,15,209,4,3
	.word	50091
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,15,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT_Bits',0,15,220,4,3
	.word	50198
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,15,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON_Bits',0,15,231,4,3
	.word	50352
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,15,234,4,16,4,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,15,238,4,3
	.word	50513
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,15,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	490
	.byte	15,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON_Bits',0,15,249,4,3
	.word	50611
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,15,252,4,16,4,11
	.byte	'Timer',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,15,255,4,3
	.word	50783
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,15,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR_Bits',0,15,133,5,3
	.word	50863
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,15,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	490
	.byte	3,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT_Bits',0,15,153,5,3
	.word	50936
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,15,156,5,16,4,11
	.byte	'T0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,15,167,5,3
	.word	51254
	.byte	12,15,175,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42541
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_A',0,15,180,5,3
	.word	51449
	.byte	12,15,183,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42602
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BIV',0,15,188,5,3
	.word	51508
	.byte	12,15,191,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42681
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BTV',0,15,196,5,3
	.word	51569
	.byte	12,15,199,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42767
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT',0,15,204,5,3
	.word	51630
	.byte	12,15,207,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42856
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL',0,15,212,5,3
	.word	51692
	.byte	12,15,215,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43002
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT',0,15,220,5,3
	.word	51755
	.byte	28
	.byte	'Ifx_CPU_CORE_ID',0,15,228,5,3
	.word	14354
	.byte	12,15,231,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43158
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L',0,15,236,5,3
	.word	51844
	.byte	12,15,239,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43251
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U',0,15,244,5,3
	.word	51907
	.byte	12,15,247,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43344
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID',0,15,252,5,3
	.word	51970
	.byte	12,15,255,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43451
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE',0,15,132,6,3
	.word	52034
	.byte	12,15,135,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43538
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT',0,15,140,6,3
	.word	52096
	.byte	12,15,143,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43692
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID',0,15,148,6,3
	.word	52159
	.byte	12,15,151,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43786
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_D',0,15,156,6,3
	.word	52223
	.byte	12,15,159,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43849
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DATR',0,15,164,6,3
	.word	52282
	.byte	12,15,167,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44067
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR',0,15,172,6,3
	.word	52344
	.byte	12,15,175,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44282
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR',0,15,180,6,3
	.word	52407
	.byte	12,15,183,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44376
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0',0,15,188,6,3
	.word	52471
	.byte	12,15,191,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44492
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2',0,15,196,6,3
	.word	52534
	.byte	12,15,199,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44593
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCX',0,15,204,6,3
	.word	52597
	.byte	12,15,207,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44686
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD',0,15,212,6,3
	.word	52658
	.byte	12,15,215,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44766
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR',0,15,220,6,3
	.word	52721
	.byte	12,15,223,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44835
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR',0,15,228,6,3
	.word	52784
	.byte	12,15,231,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45064
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DMS',0,15,236,6,3
	.word	52847
	.byte	12,15,239,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45157
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L',0,15,244,6,3
	.word	52908
	.byte	12,15,247,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45252
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U',0,15,252,6,3
	.word	52971
	.byte	12,15,255,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45347
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE',0,15,132,7,3
	.word	53034
	.byte	12,15,135,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45437
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE',0,15,140,7,3
	.word	53096
	.byte	12,15,143,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45527
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR',0,15,148,7,3
	.word	53158
	.byte	12,15,151,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45851
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT',0,15,156,7,3
	.word	53220
	.byte	12,15,159,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46005
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FCX',0,15,164,7,3
	.word	53283
	.byte	12,15,167,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46111
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,15,172,7,3
	.word	53344
	.byte	12,15,175,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46460
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,15,180,7,3
	.word	53414
	.byte	12,15,183,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46620
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,15,188,7,3
	.word	53484
	.byte	12,15,191,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46701
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,15,196,7,3
	.word	53553
	.byte	12,15,199,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46788
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,15,204,7,3
	.word	53624
	.byte	12,15,207,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46875
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,15,212,7,3
	.word	53695
	.byte	12,15,215,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46962
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT',0,15,220,7,3
	.word	53766
	.byte	12,15,223,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47053
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICR',0,15,228,7,3
	.word	53828
	.byte	12,15,231,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47196
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ISP',0,15,236,7,3
	.word	53889
	.byte	12,15,239,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47262
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_LCX',0,15,244,7,3
	.word	53950
	.byte	12,15,247,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47368
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT',0,15,252,7,3
	.word	54011
	.byte	12,15,255,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47461
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT',0,15,132,8,3
	.word	54074
	.byte	12,15,135,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47554
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT',0,15,140,8,3
	.word	54137
	.byte	12,15,143,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47647
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PC',0,15,148,8,3
	.word	54200
	.byte	12,15,151,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47732
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0',0,15,156,8,3
	.word	54260
	.byte	12,15,159,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47848
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1',0,15,164,8,3
	.word	54323
	.byte	12,15,167,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47959
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2',0,15,172,8,3
	.word	54386
	.byte	12,15,175,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48060
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI',0,15,180,8,3
	.word	54449
	.byte	12,15,183,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48190
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR',0,15,188,8,3
	.word	54511
	.byte	12,15,191,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48259
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR',0,15,196,8,3
	.word	54574
	.byte	12,15,199,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48488
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0',0,15,204,8,3
	.word	54637
	.byte	12,15,207,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48601
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1',0,15,212,8,3
	.word	54699
	.byte	12,15,215,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48714
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2',0,15,220,8,3
	.word	54761
	.byte	12,15,223,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48805
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR',0,15,228,8,3
	.word	54823
	.byte	12,15,231,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49008
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSW',0,15,236,8,3
	.word	54885
	.byte	12,15,239,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49251
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN',0,15,244,8,3
	.word	54946
	.byte	12,15,247,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49379
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON',0,15,252,8,3
	.word	55009
	.byte	12,15,255,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49620
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA',0,15,132,9,3
	.word	55073
	.byte	12,15,135,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49703
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB',0,15,140,9,3
	.word	55143
	.byte	12,15,143,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49794
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,15,148,9,3
	.word	55213
	.byte	12,15,151,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49885
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,15,156,9,3
	.word	55287
	.byte	12,15,159,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49984
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,15,164,9,3
	.word	55361
	.byte	12,15,167,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50091
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,15,172,9,3
	.word	55431
	.byte	12,15,175,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50198
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT',0,15,180,9,3
	.word	55501
	.byte	12,15,183,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50352
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON',0,15,188,9,3
	.word	55564
	.byte	12,15,191,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50513
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI',0,15,196,9,3
	.word	55628
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50611
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON',0,15,204,9,3
	.word	55694
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50783
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER',0,15,212,9,3
	.word	55759
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50863
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR',0,15,220,9,3
	.word	55826
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50936
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT',0,15,228,9,3
	.word	55890
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51254
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC',0,15,236,9,3
	.word	55954
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,15,247,9,25,8,13
	.byte	'L',0
	.word	51844
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	51907
	.byte	4,2,35,4,0,14
	.word	56020
	.byte	28
	.byte	'Ifx_CPU_CPR',0,15,251,9,3
	.word	56062
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,15,254,9,25,8,13
	.byte	'L',0
	.word	52908
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	52971
	.byte	4,2,35,4,0,14
	.word	56088
	.byte	28
	.byte	'Ifx_CPU_DPR',0,15,130,10,3
	.word	56130
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,15,133,10,25,16,13
	.byte	'LA',0
	.word	55361
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	55431
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	55213
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	55287
	.byte	4,2,35,12,0,14
	.word	56156
	.byte	28
	.byte	'Ifx_CPU_SPROT_RGN',0,15,139,10,3
	.word	56238
	.byte	18,12
	.word	55759
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,15,142,10,25,16,13
	.byte	'CON',0
	.word	55694
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	56270
	.byte	12,2,35,4,0,14
	.word	56279
	.byte	28
	.byte	'Ifx_CPU_TPS',0,15,146,10,3
	.word	56327
	.byte	10
	.byte	'_Ifx_CPU_TR',0,15,149,10,25,8,13
	.byte	'EVT',0
	.word	55890
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	55826
	.byte	4,2,35,4,0,14
	.word	56353
	.byte	28
	.byte	'Ifx_CPU_TR',0,15,153,10,3
	.word	56398
	.byte	18,176,32
	.word	642
	.byte	19,175,32,0,18,208,223,1
	.word	642
	.byte	19,207,223,1,0,18,248,1
	.word	642
	.byte	19,247,1,0,18,244,29
	.word	642
	.byte	19,243,29,0,18,188,3
	.word	642
	.byte	19,187,3,0,18,232,3
	.word	642
	.byte	19,231,3,0,18,252,23
	.word	642
	.byte	19,251,23,0,18,228,63
	.word	642
	.byte	19,227,63,0,18,128,1
	.word	56088
	.byte	19,15,0,14
	.word	56513
	.byte	18,128,31
	.word	642
	.byte	19,255,30,0,18,64
	.word	56020
	.byte	19,7,0,14
	.word	56539
	.byte	18,192,31
	.word	642
	.byte	19,191,31,0,18,16
	.word	52034
	.byte	19,3,0,18,16
	.word	53034
	.byte	19,3,0,18,16
	.word	53096
	.byte	19,3,0,18,208,7
	.word	642
	.byte	19,207,7,0,14
	.word	56279
	.byte	18,240,23
	.word	642
	.byte	19,239,23,0,18,64
	.word	56353
	.byte	19,7,0,14
	.word	56618
	.byte	18,192,23
	.word	642
	.byte	19,191,23,0,18,232,1
	.word	642
	.byte	19,231,1,0,18,180,1
	.word	642
	.byte	19,179,1,0,18,172,1
	.word	642
	.byte	19,171,1,0,18,64
	.word	52223
	.byte	19,15,0,18,64
	.word	642
	.byte	19,63,0,18,64
	.word	51449
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,15,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	56423
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	54946
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	56434
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	55628
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	56447
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	54637
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	54699
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	54761
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	56458
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	52534
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4747
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	55009
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	53158
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2928
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	52282
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	52658
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	52721
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	52784
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4118
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	52471
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	56469
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	54823
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	54323
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	54386
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	54260
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	54511
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	54574
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	56480
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	51755
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	56491
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	53344
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	53484
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	53414
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2928
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	53553
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	53624
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	53695
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	56502
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	56523
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	56528
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	56548
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	56553
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	56564
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	56573
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	56582
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	56591
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	56602
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	56607
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	56627
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	56632
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	51692
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	51630
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	53766
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	54011
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	54074
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	54137
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	56643
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	52344
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2928
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	53220
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	52096
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	55501
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	40482
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	55954
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5087
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	52847
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	52597
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	52407
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	56654
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	54449
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	54885
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	54200
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4747
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	55564
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	51970
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	14354
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	51508
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	51569
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	53889
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	53828
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4747
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	53283
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	53950
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	40473
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	52159
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	56665
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	56676
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	56685
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	56694
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	56685
	.byte	64,4,35,192,255,3,0,14
	.word	56703
	.byte	28
	.byte	'Ifx_CPU',0,15,130,11,3
	.word	58494
	.byte	28
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	1454
	.byte	28
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1536
	.byte	28
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	9901
	.byte	28
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10191
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	58610
	.byte	28
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	58642
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10191
	.byte	4,2,35,8,0,14
	.word	58668
	.byte	28
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	58727
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	58755
	.byte	28
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	58792
	.byte	18,64
	.word	10191
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	58820
	.byte	64,2,35,0,0,14
	.word	58829
	.byte	28
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	58861
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10191
	.byte	4,2,35,12,0,14
	.word	58886
	.byte	28
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	58958
	.byte	18,8
	.word	10191
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	58984
	.byte	8,2,35,0,0,14
	.word	58993
	.byte	28
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	59029
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10191
	.byte	4,2,35,12,0,14
	.word	59059
	.byte	28
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	59132
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	59158
	.byte	28
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	59193
	.byte	18,192,1
	.word	10191
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5087
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	59219
	.byte	192,1,2,35,16,0,14
	.word	59229
	.byte	28
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	59296
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10191
	.byte	4,2,35,4,0,14
	.word	59322
	.byte	28
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	59370
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	59398
	.byte	28
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	59431
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	58984
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	58984
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	58984
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	58984
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10191
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10191
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	40491
	.byte	40,2,35,40,0,14
	.word	59458
	.byte	28
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	59585
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	59612
	.byte	28
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	59644
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	59670
	.byte	28
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	59702
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10191
	.byte	4,2,35,8,0,14
	.word	59728
	.byte	28
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	59788
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10191
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	40473
	.byte	16,2,35,16,0,14
	.word	59814
	.byte	28
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	59908
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10191
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10191
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10191
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4118
	.byte	24,2,35,24,0,14
	.word	59935
	.byte	28
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	60052
	.byte	18,12
	.word	10191
	.byte	19,2,0,18,32
	.word	10191
	.byte	19,7,0,18,32
	.word	60089
	.byte	19,0,0,18,88
	.word	642
	.byte	19,87,0,18,108
	.word	10191
	.byte	19,26,0,18,96
	.word	642
	.byte	19,95,0,18,96
	.word	60089
	.byte	19,2,0,18,160,3
	.word	642
	.byte	19,159,3,0,18,64
	.word	60089
	.byte	19,1,0,18,192,3
	.word	642
	.byte	19,191,3,0,18,16
	.word	10191
	.byte	19,3,0,18,64
	.word	60174
	.byte	19,3,0,18,192,2
	.word	642
	.byte	19,191,2,0,18,52
	.word	642
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	60080
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2928
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10191
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10191
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	58984
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4747
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	60098
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	60107
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	60116
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	60125
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10191
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5087
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	60134
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	60143
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	60134
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	60143
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	60154
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	60163
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	60183
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	60192
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	60080
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	60203
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	60080
	.byte	12,3,35,192,18,0,14
	.word	60212
	.byte	28
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	60672
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	60698
	.byte	28
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	60731
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10191
	.byte	4,2,35,12,0,14
	.word	60758
	.byte	28
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	60831
	.byte	18,56
	.word	642
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10191
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10191
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10191
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	60858
	.byte	56,2,35,24,0,14
	.word	60867
	.byte	28
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	60990
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	61016
	.byte	28
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	61048
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10191
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10191
	.byte	4,2,35,16,0,14
	.word	61074
	.byte	28
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	61159
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	61185
	.byte	28
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	61217
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	60089
	.byte	32,2,35,0,0,14
	.word	61243
	.byte	28
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	61276
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	60089
	.byte	32,2,35,0,0,14
	.word	61303
	.byte	28
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	61337
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10191
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10191
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10191
	.byte	4,2,35,20,0,14
	.word	61365
	.byte	28
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	61458
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	61485
	.byte	28
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	61517
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	60174
	.byte	16,2,35,4,0,14
	.word	61543
	.byte	28
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	61589
	.byte	18,24
	.word	10191
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	61615
	.byte	24,2,35,0,0,14
	.word	61624
	.byte	28
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	61657
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	60080
	.byte	12,2,35,0,0,14
	.word	61684
	.byte	28
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	61716
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10191
	.byte	4,2,35,4,0,14
	.word	61742
	.byte	28
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	61788
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10191
	.byte	4,2,35,12,0,14
	.word	61814
	.byte	28
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	61889
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10191
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10191
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10191
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10191
	.byte	4,2,35,12,0,14
	.word	61918
	.byte	28
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	61992
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10191
	.byte	4,2,35,0,0,14
	.word	62020
	.byte	28
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	62054
	.byte	18,4
	.word	58610
	.byte	19,0,0,14
	.word	62081
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	62090
	.byte	4,2,35,0,0,14
	.word	62095
	.byte	28
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	62131
	.byte	18,48
	.word	58668
	.byte	19,3,0,14
	.word	62159
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	62168
	.byte	48,2,35,0,0,14
	.word	62173
	.byte	28
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	62213
	.byte	14
	.word	58755
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	62243
	.byte	4,2,35,0,0,14
	.word	62248
	.byte	28
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	62282
	.byte	18,64
	.word	58829
	.byte	19,0,0,14
	.word	62309
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	62318
	.byte	64,2,35,0,0,14
	.word	62323
	.byte	28
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	62357
	.byte	18,32
	.word	58886
	.byte	19,1,0,14
	.word	62384
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	62393
	.byte	32,2,35,0,0,14
	.word	62398
	.byte	28
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	62434
	.byte	14
	.word	58993
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	62462
	.byte	8,2,35,0,0,14
	.word	62467
	.byte	28
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	62511
	.byte	18,16
	.word	59059
	.byte	19,0,0,14
	.word	62543
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	62552
	.byte	16,2,35,0,0,14
	.word	62557
	.byte	28
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	62591
	.byte	18,8
	.word	59158
	.byte	19,1,0,14
	.word	62618
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	62627
	.byte	8,2,35,0,0,14
	.word	62632
	.byte	28
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	62666
	.byte	18,208,1
	.word	59229
	.byte	19,0,0,14
	.word	62693
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	62703
	.byte	208,1,2,35,0,0,14
	.word	62708
	.byte	28
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	62744
	.byte	14
	.word	59322
	.byte	14
	.word	59322
	.byte	14
	.word	59322
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	62771
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4747
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	62776
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	62781
	.byte	8,2,35,24,0,14
	.word	62786
	.byte	28
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	62877
	.byte	18,4
	.word	59398
	.byte	19,0,0,14
	.word	62906
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	62915
	.byte	4,2,35,0,0,14
	.word	62920
	.byte	28
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	62956
	.byte	18,80
	.word	59458
	.byte	19,0,0,14
	.word	62984
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	62993
	.byte	80,2,35,0,0,14
	.word	62998
	.byte	28
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	63034
	.byte	18,4
	.word	59612
	.byte	19,0,0,14
	.word	63062
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	63071
	.byte	4,2,35,0,0,14
	.word	63076
	.byte	28
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	63110
	.byte	18,4
	.word	59670
	.byte	19,0,0,14
	.word	63137
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	63146
	.byte	4,2,35,0,0,14
	.word	63151
	.byte	28
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	63185
	.byte	18,12
	.word	59728
	.byte	19,0,0,14
	.word	63212
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	63221
	.byte	12,2,35,0,0,14
	.word	63226
	.byte	28
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	63260
	.byte	18,64
	.word	59814
	.byte	19,1,0,14
	.word	63287
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	63296
	.byte	64,2,35,0,0,14
	.word	63301
	.byte	28
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	63337
	.byte	18,48
	.word	59935
	.byte	19,0,0,14
	.word	63365
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	63374
	.byte	48,2,35,0,0,14
	.word	63379
	.byte	28
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	63417
	.byte	18,204,18
	.word	60212
	.byte	19,0,0,14
	.word	63446
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	63456
	.byte	204,18,2,35,0,0,14
	.word	63461
	.byte	28
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	63497
	.byte	18,4
	.word	60698
	.byte	19,0,0,14
	.word	63524
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	63533
	.byte	4,2,35,0,0,14
	.word	63538
	.byte	28
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	63574
	.byte	18,64
	.word	60758
	.byte	19,3,0,14
	.word	63602
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	63611
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10191
	.byte	4,2,35,64,0,14
	.word	63616
	.byte	28
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	63665
	.byte	18,80
	.word	60867
	.byte	19,0,0,14
	.word	63693
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	63702
	.byte	80,2,35,0,0,14
	.word	63707
	.byte	28
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	63741
	.byte	18,4
	.word	61016
	.byte	19,0,0,14
	.word	63768
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	63777
	.byte	4,2,35,0,0,14
	.word	63782
	.byte	28
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	63816
	.byte	18,40
	.word	61074
	.byte	19,1,0,14
	.word	63843
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	63852
	.byte	40,2,35,0,0,14
	.word	63857
	.byte	28
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	63891
	.byte	18,8
	.word	61185
	.byte	19,1,0,14
	.word	63918
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	63927
	.byte	8,2,35,0,0,14
	.word	63932
	.byte	28
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	63966
	.byte	18,32
	.word	61243
	.byte	19,0,0,14
	.word	63993
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	64002
	.byte	32,2,35,0,0,14
	.word	64007
	.byte	28
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	64043
	.byte	18,32
	.word	61303
	.byte	19,0,0,14
	.word	64071
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	64080
	.byte	32,2,35,0,0,14
	.word	64085
	.byte	28
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	64123
	.byte	18,96
	.word	61365
	.byte	19,3,0,14
	.word	64152
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	64161
	.byte	96,2,35,0,0,14
	.word	64166
	.byte	28
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	64202
	.byte	18,4
	.word	61485
	.byte	19,0,0,14
	.word	64230
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	64239
	.byte	4,2,35,0,0,14
	.word	64244
	.byte	28
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	64278
	.byte	14
	.word	61543
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	64305
	.byte	20,2,35,0,0,14
	.word	64310
	.byte	28
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	64344
	.byte	18,24
	.word	61624
	.byte	19,0,0,14
	.word	64371
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	64380
	.byte	24,2,35,0,0,14
	.word	64385
	.byte	28
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	64421
	.byte	18,12
	.word	61684
	.byte	19,0,0,14
	.word	64449
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	64458
	.byte	12,2,35,0,0,14
	.word	64463
	.byte	28
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	64497
	.byte	18,16
	.word	61742
	.byte	19,1,0,14
	.word	64524
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	64533
	.byte	16,2,35,0,0,14
	.word	64538
	.byte	28
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	64572
	.byte	18,64
	.word	61918
	.byte	19,3,0,14
	.word	64599
	.byte	18,224,1
	.word	642
	.byte	19,223,1,0,18,32
	.word	61814
	.byte	19,1,0,14
	.word	64624
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	64608
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	64613
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	64633
	.byte	32,3,35,160,2,0,14
	.word	64638
	.byte	28
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	64707
	.byte	14
	.word	62020
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	64735
	.byte	4,2,35,0,0,14
	.word	64740
	.byte	28
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	64776
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,28
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	64804
	.byte	30,5,160,1,9,6,13
	.byte	'counter',0
	.word	1691
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	642
	.byte	1,2,35,4,0,28
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	64893
	.byte	30,5,172,1,9,32,13
	.byte	'instruction',0
	.word	64893
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	64893
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	64893
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	64893
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	64893
	.byte	6,2,35,24,0,28
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	64959
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,20,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,20,79,3
	.word	65077
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,20,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,20,85,3
	.word	65638
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,20,88,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,20,95,3
	.word	65719
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,20,98,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,20,111,3
	.word	65872
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,20,114,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,20,121,3
	.word	66120
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,20,124,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0_Bits',0,20,128,1,3
	.word	66266
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,20,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM1_Bits',0,20,136,1,3
	.word	66364
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,20,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM2_Bits',0,20,144,1,3
	.word	66480
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,20,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRD_Bits',0,20,153,1,3
	.word	66596
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,20,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRP_Bits',0,20,162,1,3
	.word	66736
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,20,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCW_Bits',0,20,171,1,3
	.word	66876
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,20,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	659
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FCON_Bits',0,20,193,1,3
	.word	67015
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,20,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FPRO_Bits',0,20,218,1,3
	.word	67377
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,20,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FSR_Bits',0,20,254,1,3
	.word	67818
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,20,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_ID_Bits',0,20,134,2,3
	.word	68424
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,20,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	659
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARD_Bits',0,20,147,2,3
	.word	68535
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,20,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARP_Bits',0,20,159,2,3
	.word	68749
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,20,162,2,16,4,11
	.byte	'L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	659
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCOND_Bits',0,20,179,2,3
	.word	68936
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,20,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,20,188,2,3
	.word	69260
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,20,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,20,199,2,3
	.word	69403
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,219,2,3
	.word	69592
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,20,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,20,254,2,3
	.word	69955
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,20,129,3,16,4,11
	.byte	'S0L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONP_Bits',0,20,160,3,3
	.word	70550
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,20,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,20,194,3,3
	.word	71074
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,20,197,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,20,201,3,3
	.word	71656
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,20,204,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,20,208,3,3
	.word	71758
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,20,211,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,20,215,3,3
	.word	71860
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,20,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	467
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD_Bits',0,20,222,3,3
	.word	71962
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,20,225,3,16,4,11
	.byte	'STRT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	659
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_RRCT_Bits',0,20,236,3,3
	.word	72056
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,20,239,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0_Bits',0,20,242,3,3
	.word	72266
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,20,245,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1_Bits',0,20,248,3,3
	.word	72339
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,20,251,3,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,20,130,4,3
	.word	72412
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,20,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,20,137,4,3
	.word	72567
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,20,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,20,147,4,3
	.word	72672
	.byte	12,20,155,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65077
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN0',0,20,160,4,3
	.word	72820
	.byte	12,20,163,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65638
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1',0,20,168,4,3
	.word	72886
	.byte	12,20,171,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65719
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG',0,20,176,4,3
	.word	72952
	.byte	12,20,179,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65872
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT',0,20,184,4,3
	.word	73020
	.byte	12,20,187,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66120
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_TOP',0,20,192,4,3
	.word	73089
	.byte	12,20,195,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66266
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0',0,20,200,4,3
	.word	73157
	.byte	12,20,203,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66364
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM1',0,20,208,4,3
	.word	73222
	.byte	12,20,211,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66480
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM2',0,20,216,4,3
	.word	73287
	.byte	12,20,219,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66596
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRD',0,20,224,4,3
	.word	73352
	.byte	12,20,227,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66736
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRP',0,20,232,4,3
	.word	73417
	.byte	12,20,235,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66876
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCW',0,20,240,4,3
	.word	73482
	.byte	12,20,243,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67015
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FCON',0,20,248,4,3
	.word	73546
	.byte	12,20,251,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67377
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FPRO',0,20,128,5,3
	.word	73610
	.byte	12,20,131,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67818
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FSR',0,20,136,5,3
	.word	73674
	.byte	12,20,139,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68424
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ID',0,20,144,5,3
	.word	73737
	.byte	12,20,147,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68535
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARD',0,20,152,5,3
	.word	73799
	.byte	12,20,155,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68749
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARP',0,20,160,5,3
	.word	73863
	.byte	12,20,163,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68936
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCOND',0,20,168,5,3
	.word	73927
	.byte	12,20,171,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69260
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG',0,20,176,5,3
	.word	73994
	.byte	12,20,179,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69403
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSM',0,20,184,5,3
	.word	74063
	.byte	12,20,187,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69592
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,20,192,5,3
	.word	74132
	.byte	12,20,195,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69955
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONOTP',0,20,200,5,3
	.word	74205
	.byte	12,20,203,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70550
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONP',0,20,208,5,3
	.word	74274
	.byte	12,20,211,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71074
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONWOP',0,20,216,5,3
	.word	74341
	.byte	12,20,219,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71656
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0',0,20,224,5,3
	.word	74410
	.byte	12,20,227,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71758
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1',0,20,232,5,3
	.word	74478
	.byte	12,20,235,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71860
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2',0,20,240,5,3
	.word	74546
	.byte	12,20,243,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71962
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD',0,20,248,5,3
	.word	74614
	.byte	12,20,251,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72056
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRCT',0,20,128,6,3
	.word	74678
	.byte	12,20,131,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72266
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0',0,20,136,6,3
	.word	74742
	.byte	12,20,139,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72339
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1',0,20,144,6,3
	.word	74806
	.byte	12,20,147,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72412
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG',0,20,152,6,3
	.word	74870
	.byte	12,20,155,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72567
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT',0,20,160,6,3
	.word	74938
	.byte	12,20,163,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72672
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_TOP',0,20,168,6,3
	.word	75007
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,20,179,6,25,12,13
	.byte	'CFG',0
	.word	72952
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	73020
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73089
	.byte	4,2,35,8,0,14
	.word	75075
	.byte	28
	.byte	'Ifx_FLASH_CBAB',0,20,184,6,3
	.word	75138
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,20,187,6,25,12,13
	.byte	'CFG0',0
	.word	74410
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	74478
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	74546
	.byte	4,2,35,8,0,14
	.word	75167
	.byte	28
	.byte	'Ifx_FLASH_RDB',0,20,192,6,3
	.word	75231
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,20,195,6,25,12,13
	.byte	'CFG',0
	.word	74870
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	74938
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	75007
	.byte	4,2,35,8,0,14
	.word	75259
	.byte	28
	.byte	'Ifx_FLASH_UBAB',0,20,200,6,3
	.word	75322
	.byte	28
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8500
	.byte	28
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8413
	.byte	28
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4756
	.byte	28
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2809
	.byte	28
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3804
	.byte	28
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	2937
	.byte	28
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3584
	.byte	28
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3152
	.byte	28
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3367
	.byte	28
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7772
	.byte	28
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7896
	.byte	28
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	7980
	.byte	28
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8160
	.byte	28
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6411
	.byte	28
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	6935
	.byte	28
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6585
	.byte	28
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6759
	.byte	28
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7424
	.byte	28
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2238
	.byte	28
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5748
	.byte	28
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6236
	.byte	28
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5895
	.byte	28
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6064
	.byte	28
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7091
	.byte	28
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	1922
	.byte	28
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5462
	.byte	28
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5096
	.byte	28
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4127
	.byte	28
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4431
	.byte	28
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9027
	.byte	28
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8460
	.byte	28
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5047
	.byte	28
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2888
	.byte	28
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4078
	.byte	28
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3112
	.byte	28
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3764
	.byte	28
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3327
	.byte	28
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3544
	.byte	28
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7856
	.byte	28
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8105
	.byte	28
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8364
	.byte	28
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7732
	.byte	28
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6545
	.byte	28
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7051
	.byte	28
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6719
	.byte	28
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6895
	.byte	28
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2769
	.byte	28
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7384
	.byte	28
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5855
	.byte	28
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6371
	.byte	28
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6024
	.byte	28
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6196
	.byte	28
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2198
	.byte	28
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5708
	.byte	28
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5422
	.byte	28
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4391
	.byte	28
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4707
	.byte	14
	.word	9067
	.byte	28
	.byte	'Ifx_P',0,8,139,6,3
	.word	76669
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,28
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	76689
	.byte	15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,28
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	76840
	.byte	15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,28
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	77084
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,28
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	77182
	.byte	28
	.byte	'IfxPort_State',0,7,178,1,3
	.word	9680
	.byte	30,7,190,1,9,8,13
	.byte	'port',0
	.word	9675
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	642
	.byte	1,2,35,4,0,28
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	77647
	.byte	28
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	206
	.byte	30,9,212,5,9,8,13
	.byte	'value',0
	.word	1691
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1691
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	77747
	.byte	30,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	642
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	642
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	77818
	.byte	30,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	77707
	.byte	4,2,35,8,0,28
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	77935
	.byte	3
	.word	203
	.byte	30,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	77747
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	77747
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	77747
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	77747
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	77747
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	77747
	.byte	8,2,35,40,0,28
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	78037
	.byte	30,9,128,6,9,8,13
	.byte	'value',0
	.word	1691
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1691
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	78189
	.byte	3
	.word	77935
	.byte	30,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	78265
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	77818
	.byte	8,2,35,8,0,28
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	78270
	.byte	15,21,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,28
	.byte	'IfxSrc_Tos',0,21,74,3
	.word	78387
	.byte	15,12,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,28
	.byte	'IfxStm_Comparator',0,12,155,1,3
	.word	78465
	.byte	15,12,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,28
	.byte	'IfxStm_ComparatorInterrupt',0,12,163,1,3
	.word	78543
	.byte	15,12,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,28
	.byte	'IfxStm_ComparatorOffset',0,12,201,1,3
	.word	78652
	.byte	15,12,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,28
	.byte	'IfxStm_ComparatorSize',0,12,239,1,3
	.word	79610
	.byte	15,12,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,28
	.byte	'IfxStm_SleepMode',0,12,248,1,3
	.word	80630
	.byte	15,12,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,28
	.byte	'IfxStm_SuspendMode',0,12,129,2,3
	.word	80716
	.byte	28
	.byte	'_iob_flag_t',0,22,82,25
	.word	659
	.byte	7
	.byte	'char',0,1,6,28
	.byte	'int8',0,23,54,29
	.word	80849
	.byte	28
	.byte	'int16',0,23,55,29
	.word	14394
	.byte	28
	.byte	'int32',0,23,56,29
	.word	483
	.byte	28
	.byte	'int64',0,23,57,29
	.word	14644
.L65:
	.byte	18,8
	.word	1691
	.byte	19,1,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12
	.byte	60,12,0,0,25,29,1,49,19,0,0,26,11,0,49,19,0,0,27,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60
	.byte	12,0,0,28,22,0,3,8,58,15,59,15,57,15,73,19,0,0,29,21,0,54,15,0,0,30,19,1,58,15,59,15,57,15,11,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L20:
	.word	.L81-.L80
.L80:
	.half	3
	.word	.L83-.L82
.L82:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm.h',0,2,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxStm_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_driver/zf_driver_timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'zf_common_typedef.h',0,4,0,0,0
.L83:
.L81:
	.sdecl	'.debug_info',debug,cluster('system_start')
	.sect	'.debug_info'
.L21:
	.word	378
	.half	3
	.word	.L22
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L24,.L23
	.byte	2
	.word	.L17
	.byte	3
	.byte	'system_start',0,1,47,6,1,1,1
	.word	.L14,.L33,.L13
	.byte	4
	.word	.L14,.L33
	.byte	5
	.word	.L34,.L14,.L2
	.byte	6
	.word	.L35,.L14,.L2
	.byte	7
	.byte	'reg',0,2,135,6,21
	.word	.L36,.L37
	.byte	0,0,5
	.word	.L34,.L38,.L3
	.byte	6
	.word	.L35,.L38,.L3
	.byte	7
	.byte	'reg',0,2,135,6,21
	.word	.L36,.L39
	.byte	0,0,5
	.word	.L40,.L41,.L4
	.byte	8
	.word	.L42,.L43
	.byte	9
	.word	.L44,.L41,.L4
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('system_start')
	.sect	'.debug_abbrev'
.L22:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,29,1,49,16,17,1,18,1,0,0,6,11,1,49,16,17,1,18,1
	.byte	0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('system_start')
	.sect	'.debug_line'
.L23:
	.word	.L85-.L84
.L84:
	.half	3
	.word	.L87-.L86
.L86:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'IfxStm.h',0,1,0,0,0
.L87:
	.byte	4,2,5,19,7,0,5,2
	.word	.L14
	.byte	3,135,6,1,5,28,9
	.half	.L66-.L14
	.byte	3,1,1,5,5,9
	.half	.L67-.L66
	.byte	1,4,1,5,77,9
	.half	.L2-.L67
	.byte	3,168,122,1,4,2,5,19,9
	.half	.L38-.L2
	.byte	3,215,5,1,5,28,9
	.half	.L68-.L38
	.byte	3,1,1,5,5,9
	.half	.L69-.L68
	.byte	1,4,1,5,18,9
	.half	.L3-.L69
	.byte	3,168,122,1,5,5,9
	.half	.L88-.L3
	.byte	1,5,18,9
	.half	.L89-.L88
	.byte	1,4,3,5,21,9
	.half	.L41-.L89
	.byte	3,143,4,1,5,5,9
	.half	.L90-.L41
	.byte	1,4,1,5,41,9
	.half	.L4-.L90
	.byte	3,241,123,1,5,1,9
	.half	.L91-.L4
	.byte	3,1,1,7,9
	.half	.L25-.L91
	.byte	0,1,1
.L85:
	.sdecl	'.debug_ranges',debug,cluster('system_start')
	.sect	'.debug_ranges'
.L24:
	.word	-1,.L14,0,.L25-.L14,0,0
	.sdecl	'.debug_info',debug,cluster('system_getval')
	.sect	'.debug_info'
.L26:
	.word	563
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L29,.L28
	.byte	2
	.word	.L17
	.byte	3
	.byte	'system_getval',0,1,58,8
	.word	.L45
	.byte	1,1,1
	.word	.L16,.L46,.L15
	.byte	4
	.word	.L16,.L46
	.byte	5
	.byte	'time',0,1,60,12
	.word	.L45,.L47
	.byte	5
	.byte	'stm_clk',0,1,61,12
	.word	.L45,.L48
	.byte	6
	.word	.L34,.L16,.L5
	.byte	7
	.word	.L35,.L16,.L5
	.byte	5
	.byte	'reg',0,2,135,6,21
	.word	.L36,.L49
	.byte	0,0,6
	.word	.L50,.L51,.L52
	.byte	8
	.word	.L53,.L54
	.byte	7
	.word	.L55,.L51,.L52
	.byte	7
	.word	.L56,.L6,.L8
	.byte	5
	.byte	'result',0,3,182,4,13
	.word	.L57,.L58
	.byte	6
	.word	.L59,.L6,.L7
	.byte	9
	.word	.L60,.L6,.L7
	.byte	0,0,0,0,6
	.word	.L34,.L52,.L9
	.byte	7
	.word	.L35,.L52,.L9
	.byte	5
	.byte	'reg',0,2,135,6,21
	.word	.L36,.L61
	.byte	0,0,6
	.word	.L40,.L62,.L10
	.byte	8
	.word	.L42,.L63
	.byte	9
	.word	.L44,.L62,.L10
	.byte	0,6
	.word	.L34,.L10,.L11
	.byte	7
	.word	.L35,.L10,.L11
	.byte	5
	.byte	'reg',0,2,135,6,21
	.word	.L36,.L64
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('system_getval')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6
	.byte	29,1,49,16,17,1,18,1,0,0,7,11,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('system_getval')
	.sect	'.debug_line'
.L28:
	.word	.L93-.L92
.L92:
	.half	3
	.word	.L95-.L94
.L94:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'IfxStm.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L95:
	.byte	4,2,5,19,7,0,5,2
	.word	.L16
	.byte	3,135,6,1,5,28,9
	.half	.L70-.L16
	.byte	3,1,1,5,5,9
	.half	.L71-.L70
	.byte	1,4,1,5,53,9
	.half	.L5-.L71
	.byte	3,182,122,1,4,3,5,5,9
	.half	.L51-.L5
	.byte	3,246,3,1,4,4,5,40,7,9
	.half	.L6-.L51
	.byte	3,175,4,1,5,58,9
	.half	.L96-.L6
	.byte	1,5,43,9
	.half	.L97-.L96
	.byte	1,5,5,9
	.half	.L98-.L97
	.byte	1,4,3,9
	.half	.L7-.L98
	.byte	3,214,123,1,4,1,5,34,9
	.half	.L8-.L7
	.byte	3,133,124,1,4,2,5,19,9
	.half	.L52-.L8
	.byte	3,201,5,1,5,28,9
	.half	.L72-.L52
	.byte	3,1,1,5,5,9
	.half	.L73-.L72
	.byte	1,4,1,5,46,9
	.half	.L9-.L73
	.byte	3,184,122,1,4,3,5,21,9
	.half	.L62-.L9
	.byte	3,255,3,1,5,5,9
	.half	.L99-.L62
	.byte	1,4,2,5,19,9
	.half	.L10-.L99
	.byte	3,200,1,1,5,28,9
	.half	.L74-.L10
	.byte	3,1,1,5,5,9
	.half	.L75-.L74
	.byte	1,4,1,5,32,9
	.half	.L11-.L75
	.byte	3,185,122,1,5,19,9
	.half	.L100-.L11
	.byte	1,5,32,9
	.half	.L101-.L100
	.byte	1,5,17,9
	.half	.L76-.L101
	.byte	1,5,21,9
	.half	.L102-.L76
	.byte	3,1,1,5,36,9
	.half	.L103-.L102
	.byte	1,5,34,9
	.half	.L104-.L103
	.byte	1,5,48,9
	.half	.L105-.L104
	.byte	1,5,46,9
	.half	.L78-.L105
	.byte	1,5,5,9
	.half	.L79-.L78
	.byte	3,2,1,5,1,9
	.half	.L12-.L79
	.byte	3,1,1,7,9
	.half	.L30-.L12
	.byte	0,1,1
.L93:
	.sdecl	'.debug_ranges',debug,cluster('system_getval')
	.sect	'.debug_ranges'
.L29:
	.word	-1,.L16,0,.L30-.L16,0,0
	.sdecl	'.debug_info',debug,cluster('systick_count')
	.sect	'.debug_info'
.L31:
	.word	229
	.half	3
	.word	.L32
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L17
	.byte	3
	.byte	'systick_count',0,16,40,15
	.word	.L65
	.byte	5,3
	.word	systick_count
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('systick_count')
	.sect	'.debug_abbrev'
.L32:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('system_getval')
	.sect	'.debug_loc'
.L64:
	.word	-1,.L16,.L74-.L16,.L75-.L16
	.half	1
	.byte	95
	.word	0,0
.L49:
	.word	-1,.L16,.L70-.L16,.L71-.L16
	.half	1
	.byte	95
	.word	0,0
.L61:
	.word	-1,.L16,.L72-.L16,.L73-.L16
	.half	1
	.byte	95
	.word	0,0
.L58:
	.word	0,0
.L54:
	.word	0,0
.L63:
	.word	0,0
.L48:
	.word	-1,.L16,.L52-.L16,.L46-.L16
	.half	1
	.byte	88
	.word	.L78-.L16,.L79-.L16
	.half	1
	.byte	86
	.word	0,0
.L15:
	.word	-1,.L16,0,.L46-.L16
	.half	2
	.byte	138,0
	.word	0,0
.L47:
	.word	-1,.L16,.L76-.L16,.L77-.L16
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('system_start')
	.sect	'.debug_loc'
.L39:
	.word	-1,.L14,.L68-.L14,.L69-.L14
	.half	1
	.byte	95
	.word	0,0
.L37:
	.word	-1,.L14,.L66-.L14,.L67-.L14
	.half	1
	.byte	95
	.word	0,0
.L43:
	.word	0,0
.L13:
	.word	-1,.L14,0,.L33-.L14
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L106:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('system_start')
	.sect	'.debug_frame'
	.word	12
	.word	.L106,.L14,.L33-.L14
	.sdecl	'.debug_frame',debug,cluster('system_getval')
	.sect	'.debug_frame'
	.word	12
	.word	.L106,.L16,.L46-.L16
	; Module end
