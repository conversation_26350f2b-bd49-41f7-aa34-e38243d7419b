	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc20488a --dep-file=zf_device_type.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_type.src ../libraries/zf_device/zf_device_type.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_type.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_type.type_default_callback',code,cluster('type_default_callback')
	.sect	'.text.zf_device_type.type_default_callback'
	.align	2
	
; Function type_default_callback
.L13:
type_default_callback:	.type	func
	ret
.L75:
	
__type_default_callback_function_end:
	.size	type_default_callback,__type_default_callback_function_end-type_default_callback
.L43:
	; End of function
	
	.sdecl	'.text.zf_device_type.set_camera_type',code,cluster('set_camera_type')
	.sect	'.text.zf_device_type.set_camera_type'
	.align	2
	
	.global	set_camera_type
; Function set_camera_type
.L15:
set_camera_type:	.type	func
	movh.a	a15,#@his(camera_type)
	lea	a15,[a15]@los(camera_type)
.L84:
	st.b	[a15],d4
.L85:
	jnz.a	a6,.L2
.L86:
	movh.a	a6,#@his(type_default_callback)
	lea	a6,[a6]@los(type_default_callback)
.L87:
	j	.L3
.L2:
.L3:
	movh.a	a15,#@his(camera_uart_handler)
	lea	a15,[a15]@los(camera_uart_handler)
.L88:
	st.a	[a15],a6
.L89:
	jnz.a	a4,.L4
.L90:
	movh.a	a4,#@his(type_default_callback)
	lea	a4,[a4]@los(type_default_callback)
.L91:
	j	.L5
.L4:
.L5:
	movh.a	a15,#@his(camera_vsync_handler)
	lea	a15,[a15]@los(camera_vsync_handler)
.L92:
	st.a	[a15],a4
.L93:
	jnz.a	a5,.L6
.L94:
	movh.a	a5,#@his(type_default_callback)
	lea	a5,[a5]@los(type_default_callback)
.L95:
	j	.L7
.L6:
.L7:
	movh.a	a15,#@his(camera_dma_handler)
	lea	a15,[a15]@los(camera_dma_handler)
.L96:
	st.a	[a15],a5
.L97:
	ret
.L60:
	
__set_camera_type_function_end:
	.size	set_camera_type,__set_camera_type_function_end-set_camera_type
.L28:
	; End of function
	
	.sdecl	'.text.zf_device_type.set_wireless_type',code,cluster('set_wireless_type')
	.sect	'.text.zf_device_type.set_wireless_type'
	.align	2
	
	.global	set_wireless_type
; Function set_wireless_type
.L17:
set_wireless_type:	.type	func
	movh.a	a15,#@his(wireless_type)
	lea	a15,[a15]@los(wireless_type)
.L102:
	st.b	[a15],d4
.L103:
	jnz.a	a4,.L8
.L104:
	movh.a	a4,#@his(type_default_callback)
	lea	a4,[a4]@los(type_default_callback)
.L105:
	j	.L9
.L8:
.L9:
	movh.a	a15,#@his(wireless_module_uart_handler)
	lea	a15,[a15]@los(wireless_module_uart_handler)
.L106:
	st.a	[a15],a4
.L107:
	ret
.L67:
	
__set_wireless_type_function_end:
	.size	set_wireless_type,__set_wireless_type_function_end-set_wireless_type
.L33:
	; End of function
	
	.sdecl	'.text.zf_device_type.set_tof_type',code,cluster('set_tof_type')
	.sect	'.text.zf_device_type.set_tof_type'
	.align	2
	
	.global	set_tof_type
; Function set_tof_type
.L19:
set_tof_type:	.type	func
	movh.a	a15,#@his(tof_type)
	lea	a15,[a15]@los(tof_type)
.L112:
	st.b	[a15],d4
.L113:
	jnz.a	a4,.L10
.L114:
	movh.a	a4,#@his(type_default_callback)
	lea	a4,[a4]@los(type_default_callback)
.L115:
	j	.L11
.L10:
.L11:
	movh.a	a15,#@his(tof_module_exti_handler)
	lea	a15,[a15]@los(tof_module_exti_handler)
.L116:
	st.a	[a15],a4
.L117:
	ret
.L71:
	
__set_tof_type_function_end:
	.size	set_tof_type,__set_tof_type_function_end-set_tof_type
.L38:
	; End of function
	
	.sdecl	'.data.zf_device_type.camera_type',data,cluster('camera_type')
	.sect	'.data.zf_device_type.camera_type'
	.global	camera_type
camera_type:	.type	object
	.size	camera_type,1
	.space	1
	.sdecl	'.data.zf_device_type.camera_uart_handler',data,cluster('camera_uart_handler')
	.sect	'.data.zf_device_type.camera_uart_handler'
	.global	camera_uart_handler
	.align	4
camera_uart_handler:	.type	object
	.size	camera_uart_handler,4
	.word	type_default_callback
	.sdecl	'.data.zf_device_type.camera_vsync_handler',data,cluster('camera_vsync_handler')
	.sect	'.data.zf_device_type.camera_vsync_handler'
	.global	camera_vsync_handler
	.align	4
camera_vsync_handler:	.type	object
	.size	camera_vsync_handler,4
	.word	type_default_callback
	.sdecl	'.data.zf_device_type.camera_dma_handler',data,cluster('camera_dma_handler')
	.sect	'.data.zf_device_type.camera_dma_handler'
	.global	camera_dma_handler
	.align	4
camera_dma_handler:	.type	object
	.size	camera_dma_handler,4
	.word	type_default_callback
	.sdecl	'.data.zf_device_type.wireless_type',data,cluster('wireless_type')
	.sect	'.data.zf_device_type.wireless_type'
	.global	wireless_type
wireless_type:	.type	object
	.size	wireless_type,1
	.space	1
	.sdecl	'.data.zf_device_type.wireless_module_uart_handler',data,cluster('wireless_module_uart_handler')
	.sect	'.data.zf_device_type.wireless_module_uart_handler'
	.global	wireless_module_uart_handler
	.align	4
wireless_module_uart_handler:	.type	object
	.size	wireless_module_uart_handler,4
	.word	type_default_callback
	.sdecl	'.data.zf_device_type.tof_type',data,cluster('tof_type')
	.sect	'.data.zf_device_type.tof_type'
	.global	tof_type
tof_type:	.type	object
	.size	tof_type,1
	.space	1
	.sdecl	'.data.zf_device_type.tof_module_exti_handler',data,cluster('tof_module_exti_handler')
	.sect	'.data.zf_device_type.tof_module_exti_handler'
	.global	tof_module_exti_handler
	.align	4
tof_module_exti_handler:	.type	object
	.size	tof_module_exti_handler,4
	.word	type_default_callback
	.calls	'__INDIRECT__','type_default_callback'
	.calls	'type_default_callback','',0
	.calls	'set_camera_type','',0
	.calls	'set_wireless_type','',0
	.extern	__INDIRECT__
	.calls	'set_tof_type','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L21:
	.word	1080
	.half	3
	.word	.L22
	.byte	4
.L20:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L23
.L61:
	.byte	2,1,44,9,1,3
	.byte	'NO_CAMERE',0,0,3
	.byte	'CAMERA_BIN_IIC',0,1,3
	.byte	'CAMERA_BIN_UART',0,2,3
	.byte	'CAMERA_GRAYSCALE',0,3,3
	.byte	'CAMERA_COLOR',0,4,0,4,1,1,5
	.word	289
	.byte	5
	.word	289
.L63:
	.byte	6
	.byte	'callback_function',0,1,73,16
	.word	297
.L68:
	.byte	2,1,53,9,1,3
	.byte	'NO_WIRELESS',0,0,3
	.byte	'WIRELESS_UART',0,1,3
	.byte	'BLE6A20',0,2,3
	.byte	'BLUETOOTH_CH9141',0,3,3
	.byte	'WIFI_UART',0,4,3
	.byte	'RECEIVER_UART',0,5,0
.L72:
	.byte	2,1,63,9,1,3
	.byte	'NO_TOF',0,0,3
	.byte	'TOF_DL1A',0,1,3
	.byte	'TOF_DL1B',0,2,0,7
	.byte	'__INDIRECT__',0,2,1,1,1,1,1,8
	.byte	'short int',0,2,5,6
	.byte	'__wchar_t',0,2,1,1
	.word	478
	.byte	8
	.byte	'unsigned int',0,4,7,6
	.byte	'__size_t',0,2,1,1
	.word	509
	.byte	8
	.byte	'int',0,4,5,6
	.byte	'__ptrdiff_t',0,2,1,1
	.word	542
	.byte	9,1,5
	.word	569
	.byte	6
	.byte	'__codeptr',0,2,1,1
	.word	571
	.byte	6
	.byte	'__intptr_t',0,2,1,1
	.word	542
	.byte	6
	.byte	'__uintptr_t',0,2,1,1
	.word	509
	.byte	8
	.byte	'unsigned short int',0,2,7,6
	.byte	'_iob_flag_t',0,3,82,25
	.word	633
	.byte	8
	.byte	'unsigned char',0,1,8,6
	.byte	'uint8',0,4,105,29
	.word	675
	.byte	6
	.byte	'uint16',0,4,109,29
	.word	633
	.byte	8
	.byte	'unsigned long int',0,4,7,6
	.byte	'uint32',0,4,113,29
	.word	721
	.byte	8
	.byte	'unsigned long long int',0,8,7,6
	.byte	'uint64',0,4,118,29
	.word	757
	.byte	6
	.byte	'sint16',0,4,126,29
	.word	478
	.byte	8
	.byte	'long int',0,4,5,6
	.byte	'sint32',0,4,131,1,29
	.word	813
	.byte	8
	.byte	'long long int',0,8,5,6
	.byte	'sint64',0,4,138,1,29
	.word	841
	.byte	8
	.byte	'float',0,4,4,6
	.byte	'float32',0,4,167,1,29
	.word	874
	.byte	10
	.byte	'void',0,5
	.word	900
	.byte	6
	.byte	'pvoid',0,5,57,28
	.word	906
	.byte	6
	.byte	'Ifx_TickTime',0,5,79,28
	.word	841
	.byte	8
	.byte	'char',0,1,6,6
	.byte	'int8',0,6,54,29
	.word	946
	.byte	6
	.byte	'int16',0,6,55,29
	.word	478
	.byte	6
	.byte	'int32',0,6,56,29
	.word	542
	.byte	6
	.byte	'int64',0,6,57,29
	.word	841
	.byte	6
	.byte	'camera_type_enum',0,1,51,2
	.word	202
	.byte	6
	.byte	'wireless_type_enum',0,1,61,2
	.word	328
	.byte	6
	.byte	'tof_type_enum',0,1,68,2
	.word	421
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L22:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,4,1,58,15,59,15,57,15,11,15,0,0,3,40,0,3,8,28,13,0,0,4
	.byte	21,0,54,15,39,12,0,0,5,15,0,73,19,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19,0,0,7,46,0,3,8,58,15,59,15,57
	.byte	15,54,15,63,12,60,12,0,0,8,36,0,3,8,11,15,62,15,0,0,9,21,0,54,15,0,0,10,59,0,3,8,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L23:
	.word	.L77-.L76
.L76:
	.half	3
	.word	.L79-.L78
.L78:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	0
	.byte	'..\\libraries\\zf_device\\zf_device_type.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_type.c',0,0,0,0
	.byte	'stdio.h',0,1,0,0
	.byte	'Platform_Types.h',0,2,0,0
	.byte	'ifx_types.h',0,2,0,0
	.byte	'zf_common_typedef.h',0,3,0,0,0
.L79:
.L77:
	.sdecl	'.debug_info',debug,cluster('set_camera_type')
	.sect	'.debug_info'
.L24:
	.word	353
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L27,.L26
	.byte	2
	.word	.L20
	.byte	3
	.byte	'set_camera_type',0,1,74,7,1,1,1
	.word	.L15,.L60,.L14
	.byte	4
	.byte	'type_set',0,1,74,41
	.word	.L61,.L62
	.byte	4
	.byte	'vsync_callback',0,1,74,69
	.word	.L63,.L64
	.byte	4
	.byte	'dma_callback',0,1,74,103
	.word	.L63,.L65
	.byte	4
	.byte	'uart_callback',0,1,74,135,1
	.word	.L63,.L66
	.byte	5
	.word	.L15,.L60
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('set_camera_type')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('set_camera_type')
	.sect	'.debug_line'
.L26:
	.word	.L81-.L80
.L80:
	.half	3
	.word	.L83-.L82
.L82:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_type.c',0,0,0,0,0
.L83:
	.byte	5,5,7,0,5,2
	.word	.L15
	.byte	3,203,0,1,5,17,9
	.half	.L84-.L15
	.byte	1,5,28,9
	.half	.L85-.L84
	.byte	3,1,1,5,55,7,9
	.half	.L86-.L85
	.byte	1,5,78,9
	.half	.L87-.L86
	.byte	1,5,5,9
	.half	.L3-.L87
	.byte	1,5,25,9
	.half	.L88-.L3
	.byte	1,5,29,9
	.half	.L89-.L88
	.byte	3,1,1,5,57,7,9
	.half	.L90-.L89
	.byte	1,5,80,9
	.half	.L91-.L90
	.byte	1,5,5,9
	.half	.L5-.L91
	.byte	1,5,26,9
	.half	.L92-.L5
	.byte	1,5,27,9
	.half	.L93-.L92
	.byte	3,1,1,5,53,7,9
	.half	.L94-.L93
	.byte	1,5,76,9
	.half	.L95-.L94
	.byte	1,5,5,9
	.half	.L7-.L95
	.byte	1,5,24,9
	.half	.L96-.L7
	.byte	1,5,1,9
	.half	.L97-.L96
	.byte	3,1,1,7,9
	.half	.L28-.L97
	.byte	0,1,1
.L81:
	.sdecl	'.debug_ranges',debug,cluster('set_camera_type')
	.sect	'.debug_ranges'
.L27:
	.word	-1,.L15,0,.L28-.L15,0,0
	.sdecl	'.debug_info',debug,cluster('set_wireless_type')
	.sect	'.debug_info'
.L29:
	.word	306
	.half	3
	.word	.L30
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L32,.L31
	.byte	2
	.word	.L20
	.byte	3
	.byte	'set_wireless_type',0,1,90,6,1,1,1
	.word	.L17,.L67,.L16
	.byte	4
	.byte	'type_set',0,1,90,44
	.word	.L68,.L69
	.byte	4
	.byte	'wireless_callback',0,1,90,72
	.word	.L63,.L70
	.byte	5
	.word	.L17,.L67
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('set_wireless_type')
	.sect	'.debug_abbrev'
.L30:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('set_wireless_type')
	.sect	'.debug_line'
.L31:
	.word	.L99-.L98
.L98:
	.half	3
	.word	.L101-.L100
.L100:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_type.c',0,0,0,0,0
.L101:
	.byte	5,5,7,0,5,2
	.word	.L17
	.byte	3,219,0,1,5,19,9
	.half	.L102-.L17
	.byte	1,5,37,9
	.half	.L103-.L102
	.byte	3,1,1,5,68,7,9
	.half	.L104-.L103
	.byte	1,5,91,9
	.half	.L105-.L104
	.byte	1,5,5,9
	.half	.L9-.L105
	.byte	1,5,34,9
	.half	.L106-.L9
	.byte	1,5,1,9
	.half	.L107-.L106
	.byte	3,1,1,7,9
	.half	.L33-.L107
	.byte	0,1,1
.L99:
	.sdecl	'.debug_ranges',debug,cluster('set_wireless_type')
	.sect	'.debug_ranges'
.L32:
	.word	-1,.L17,0,.L33-.L17,0,0
	.sdecl	'.debug_info',debug,cluster('set_tof_type')
	.sect	'.debug_info'
.L34:
	.word	297
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L37,.L36
	.byte	2
	.word	.L20
	.byte	3
	.byte	'set_tof_type',0,1,104,6,1,1,1
	.word	.L19,.L71,.L18
	.byte	4
	.byte	'type_set',0,1,104,34
	.word	.L72,.L73
	.byte	4
	.byte	'exti_callback',0,1,104,62
	.word	.L63,.L74
	.byte	5
	.word	.L19,.L71
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('set_tof_type')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('set_tof_type')
	.sect	'.debug_line'
.L36:
	.word	.L109-.L108
.L108:
	.half	3
	.word	.L111-.L110
.L110:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_type.c',0,0,0,0,0
.L111:
	.byte	5,5,7,0,5,2
	.word	.L19
	.byte	3,233,0,1,5,14,9
	.half	.L112-.L19
	.byte	1,5,32,9
	.half	.L113-.L112
	.byte	3,1,1,5,59,7,9
	.half	.L114-.L113
	.byte	1,5,82,9
	.half	.L115-.L114
	.byte	1,5,5,9
	.half	.L11-.L115
	.byte	1,5,29,9
	.half	.L116-.L11
	.byte	1,5,1,9
	.half	.L117-.L116
	.byte	3,1,1,7,9
	.half	.L38-.L117
	.byte	0,1,1
.L109:
	.sdecl	'.debug_ranges',debug,cluster('set_tof_type')
	.sect	'.debug_ranges'
.L37:
	.word	-1,.L19,0,.L38-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('type_default_callback')
	.sect	'.debug_info'
.L39:
	.word	258
	.half	3
	.word	.L40
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L42,.L41
	.byte	2
	.word	.L20
	.byte	3
	.byte	'type_default_callback',0,1,60,13,1,1
	.word	.L13,.L75,.L12
	.byte	4
	.word	.L13,.L75
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('type_default_callback')
	.sect	'.debug_abbrev'
.L40:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('type_default_callback')
	.sect	'.debug_line'
.L41:
	.word	.L119-.L118
.L118:
	.half	3
	.word	.L121-.L120
.L120:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_type.c',0,0,0,0,0
.L121:
	.byte	5,1,7,0,5,2
	.word	.L13
	.byte	3,62,1,7,9
	.half	.L43-.L13
	.byte	0,1,1
.L119:
	.sdecl	'.debug_ranges',debug,cluster('type_default_callback')
	.sect	'.debug_ranges'
.L42:
	.word	-1,.L13,0,.L43-.L13,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_type')
	.sect	'.debug_info'
.L44:
	.word	229
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'wireless_type',0,2,47,21
	.word	.L68
	.byte	1,5,3
	.word	wireless_type
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_type')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('wireless_module_uart_handler')
	.sect	'.debug_info'
.L46:
	.word	244
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'wireless_module_uart_handler',0,2,48,21
	.word	.L63
	.byte	1,5,3
	.word	wireless_module_uart_handler
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wireless_module_uart_handler')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('camera_type')
	.sect	'.debug_info'
.L48:
	.word	227
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'camera_type',0,2,42,21
	.word	.L61
	.byte	1,5,3
	.word	camera_type
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('camera_type')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('camera_dma_handler')
	.sect	'.debug_info'
.L50:
	.word	234
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'camera_dma_handler',0,2,45,21
	.word	.L63
	.byte	1,5,3
	.word	camera_dma_handler
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('camera_dma_handler')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('camera_vsync_handler')
	.sect	'.debug_info'
.L52:
	.word	236
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'camera_vsync_handler',0,2,44,21
	.word	.L63
	.byte	1,5,3
	.word	camera_vsync_handler
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('camera_vsync_handler')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('camera_uart_handler')
	.sect	'.debug_info'
.L54:
	.word	235
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'camera_uart_handler',0,2,43,21
	.word	.L63
	.byte	1,5,3
	.word	camera_uart_handler
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('camera_uart_handler')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('tof_type')
	.sect	'.debug_info'
.L56:
	.word	224
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'tof_type',0,2,50,21
	.word	.L72
	.byte	1,5,3
	.word	tof_type
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tof_type')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('tof_module_exti_handler')
	.sect	'.debug_info'
.L58:
	.word	239
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_type.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L20
	.byte	3
	.byte	'tof_module_exti_handler',0,2,51,21
	.word	.L63
	.byte	1,5,3
	.word	tof_module_exti_handler
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tof_module_exti_handler')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('set_camera_type')
	.sect	'.debug_loc'
.L65:
	.word	-1,.L15,0,.L60-.L15
	.half	1
	.byte	101
	.word	0,0
.L14:
	.word	-1,.L15,0,.L60-.L15
	.half	2
	.byte	138,0
	.word	0,0
.L62:
	.word	-1,.L15,0,.L60-.L15
	.half	1
	.byte	84
	.word	0,0
.L66:
	.word	-1,.L15,0,.L60-.L15
	.half	1
	.byte	102
	.word	0,0
.L64:
	.word	-1,.L15,0,.L60-.L15
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('set_tof_type')
	.sect	'.debug_loc'
.L74:
	.word	-1,.L19,0,.L71-.L19
	.half	1
	.byte	100
	.word	0,0
.L18:
	.word	-1,.L19,0,.L71-.L19
	.half	2
	.byte	138,0
	.word	0,0
.L73:
	.word	-1,.L19,0,.L71-.L19
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('set_wireless_type')
	.sect	'.debug_loc'
.L16:
	.word	-1,.L17,0,.L67-.L17
	.half	2
	.byte	138,0
	.word	0,0
.L69:
	.word	-1,.L17,0,.L67-.L17
	.half	1
	.byte	84
	.word	0,0
.L70:
	.word	-1,.L17,0,.L67-.L17
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('type_default_callback')
	.sect	'.debug_loc'
.L12:
	.word	-1,.L13,0,.L75-.L13
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L122:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('type_default_callback')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L13,.L75-.L13
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('set_camera_type')
	.sect	'.debug_frame'
	.word	20
	.word	.L122,.L15,.L60-.L15
	.byte	8,18,8,19,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('set_wireless_type')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L17,.L67-.L17
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('set_tof_type')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L19,.L71-.L19
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
