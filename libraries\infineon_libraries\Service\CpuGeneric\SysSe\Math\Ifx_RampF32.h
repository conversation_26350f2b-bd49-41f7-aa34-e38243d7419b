/**
 * \file Ifx_RampF32.h
 * \brief Ramp function
 *
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup library_srvsw_sysse_math_f32_ramp Ramp
 * \ingroup library_srvsw_sysse_math_f32
 *
 */

#ifndef IFX_RAMPF32_H
#define IFX_RAMPF32_H

#include "Cpu/Std/IfxCpu_Intrinsics.h"

/**
 * \brief Ifx_RampF32 object definition
 */
typedef struct
{
    float32 uk;
    float32 ik;
    float32 delta;
} Ifx_RampF32;

//________________________________________________________________________________________
// FUNCTION PROTOTYPES

/** \addtogroup library_srvsw_sysse_math_f32_ramp
 * \{ */
IFX_INLINE void    Ifx_RampF32_init(Ifx_RampF32 *ramp, float32 slewRate, float32 period);
IFX_INLINE void    Ifx_RampF32_reset(Ifx_RampF32 *ramp);
IFX_INLINE void    Ifx_RampF32_setSlewRate(Ifx_RampF32 *ramp, float32 slewRate, float32 period);
IFX_INLINE void    Ifx_RampF32_setRef(Ifx_RampF32 *ramp, float32 ref);
IFX_INLINE float32 Ifx_RampF32_getValue(Ifx_RampF32 *ramp);
IFX_EXTERN float32 Ifx_RampF32_step(Ifx_RampF32 *ramp);
/** \} */

//________________________________________________________________________________________
// INLINE FUNCTION IMPLEMENTATION

/**
 * \brief Reset internal values
 * \param ramp Pointer to the Ifx_RampF32 object
 */
IFX_INLINE void Ifx_RampF32_reset(Ifx_RampF32 *ramp)
{
    ramp->ik = 0;
    ramp->uk = 0;
}


/**
 * \brief Get the reference value
 * \param ramp Pointer to the Ifx_RampF32 object
 * \return Returns the ref value
 */
IFX_INLINE float32 Ifx_RampF32_getRef(Ifx_RampF32 *ramp)
{
    return ramp->ik;
}


/**
 * \brief Set the maximum slew rate
 * \param ramp Pointer to the Ifx_RampF32 object
 * \param slewRate Maximum slew rate, value per second
 * \param period Sampling period of the Ifx_RampF32_step() function
 */
IFX_INLINE void Ifx_RampF32_setSlewRate(Ifx_RampF32 *ramp, float32 slewRate, float32 period)
{
    ramp->delta = slewRate * period;
}


/**
 * \brief Initialize the Ifx_RampF32 object.
 * \param ramp Pointer to the Ifx_RampF32 object
 * \param slewRate Maximum slew rate, value per second
 * \param period Sampling period of the Ifx_RampF32_step() function
 */
IFX_INLINE void Ifx_RampF32_init(Ifx_RampF32 *ramp, float32 slewRate, float32 period)
{
    Ifx_RampF32_setSlewRate(ramp, slewRate, period);
    Ifx_RampF32_reset(ramp);
}


/**
 * \brief Set the reference value
 * \param ramp Pointer to the Ifx_RampF32 object
 * \param ref Reference value
 */
IFX_INLINE void Ifx_RampF32_setRef(Ifx_RampF32 *ramp, float32 ref)
{
    ramp->ik = ref;
}


/**
 * \brief Get the actual output value
 * \param ramp Pointer to the Ifx_RampF32 object
 * \return Actual value
 */
IFX_INLINE float32 Ifx_RampF32_getValue(Ifx_RampF32 *ramp)
{
    return ramp->uk;
}


#endif /* IFX_RAMPF32_H */
