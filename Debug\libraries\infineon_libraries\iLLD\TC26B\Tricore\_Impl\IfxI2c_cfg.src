	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18552a --dep-file=IfxI2c_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxI2c_cfg.IfxI2c_cfg_indexMap',data,rom,cluster('IfxI2c_cfg_indexMap')
	.sect	'.rodata.IfxI2c_cfg.IfxI2c_cfg_indexMap'
	.global	IfxI2c_cfg_indexMap
	.align	4
IfxI2c_cfg_indexMap:	.type	object
	.size	IfxI2c_cfg_indexMap,8
	.word	-267649024
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	8338
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	232
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	263
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	296
	.byte	4,1,5
	.word	323
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	325
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	348
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	379
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	416
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	232
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	467
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	495
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	528
	.byte	6
	.byte	'void',0,5
	.word	554
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	560
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	495
	.byte	7
	.word	554
	.byte	5
	.word	600
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	605
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	467
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	610
	.byte	10
	.byte	'_Ifx_I2C_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	348
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	348
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	348
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	348
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	348
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	348
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	348
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	348
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	348
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	348
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	348
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	348
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	348
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	348
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	348
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	348
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	348
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	348
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	348
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	348
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	348
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_I2C_ACCEN0_Bits',0,4,79,3
	.word	676
	.byte	10
	.byte	'_Ifx_I2C_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	263
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_I2C_ACCEN1_Bits',0,4,85,3
	.word	1233
	.byte	10
	.byte	'_Ifx_I2C_ADDRCFG_Bits',0,4,88,16,4,11
	.byte	'ADR',0,2
	.word	379
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	348
	.byte	6,0,2,35,1,11
	.byte	'TBAM',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'GCE',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'MCE',0,1
	.word	348
	.byte	1,5,2,35,2,11
	.byte	'MnS',0,1
	.word	348
	.byte	1,4,2,35,2,11
	.byte	'SONA',0,1
	.word	348
	.byte	1,3,2,35,2,11
	.byte	'SOPE',0,1
	.word	348
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	379
	.byte	10,0,2,35,2,0,3
	.byte	'Ifx_I2C_ADDRCFG_Bits',0,4,99,3
	.word	1310
	.byte	10
	.byte	'_Ifx_I2C_BUSSTAT_Bits',0,4,102,16,4,11
	.byte	'BS',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'RnW',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	263
	.byte	29,0,2,35,0,0,3
	.byte	'Ifx_I2C_BUSSTAT_Bits',0,4,107,3
	.word	1521
	.byte	10
	.byte	'_Ifx_I2C_CLC1_Bits',0,4,110,16,4,11
	.byte	'DISR',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'SPEN',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'SBWE',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'FSOE',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	348
	.byte	2,0,2,35,0,11
	.byte	'RMC',0,1
	.word	348
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_I2C_CLC1_Bits',0,4,121,3
	.word	1629
	.byte	10
	.byte	'_Ifx_I2C_CLC_Bits',0,4,124,16,4,11
	.byte	'DISR',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	263
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_I2C_CLC_Bits',0,4,129,1,3
	.word	1836
	.byte	10
	.byte	'_Ifx_I2C_ENDDCTRL_Bits',0,4,132,1,16,4,11
	.byte	'SETRSC',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'SETEND',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	263
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_I2C_ENDDCTRL_Bits',0,4,137,1,3
	.word	1940
	.byte	10
	.byte	'_Ifx_I2C_ERRIRQSC_Bits',0,4,140,1,16,4,11
	.byte	'RXF_UFL',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'RXF_OFL',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'TXF_UFL',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'TXF_OFL',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	263
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_I2C_ERRIRQSC_Bits',0,4,147,1,3
	.word	2059
	.byte	10
	.byte	'_Ifx_I2C_ERRIRQSM_Bits',0,4,150,1,16,4,11
	.byte	'RXF_UFL',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'RXF_OFL',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'TXF_UFL',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'TXF_OFL',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	263
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_I2C_ERRIRQSM_Bits',0,4,157,1,3
	.word	2218
	.byte	10
	.byte	'_Ifx_I2C_ERRIRQSS_Bits',0,4,160,1,16,4,11
	.byte	'RXF_UFL',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'RXF_OFL',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'TXF_UFL',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'TXF_OFL',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	263
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_I2C_ERRIRQSS_Bits',0,4,167,1,3
	.word	2377
	.byte	10
	.byte	'_Ifx_I2C_FDIVCFG_Bits',0,4,170,1,16,4,11
	.byte	'DEC',0,2
	.word	379
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	348
	.byte	5,0,2,35,1,11
	.byte	'INC',0,1
	.word	348
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	348
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_I2C_FDIVCFG_Bits',0,4,176,1,3
	.word	2536
	.byte	10
	.byte	'_Ifx_I2C_FDIVHIGHCFG_Bits',0,4,179,1,16,4,11
	.byte	'DEC',0,2
	.word	379
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	348
	.byte	5,0,2,35,1,11
	.byte	'INC',0,1
	.word	348
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	348
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_I2C_FDIVHIGHCFG_Bits',0,4,185,1,3
	.word	2671
	.byte	10
	.byte	'_Ifx_I2C_FFSSTAT_Bits',0,4,188,1,16,4,11
	.byte	'FFS',0,1
	.word	348
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	263
	.byte	26,0,2,35,0,0,3
	.byte	'Ifx_I2C_FFSSTAT_Bits',0,4,192,1,3
	.word	2814
	.byte	10
	.byte	'_Ifx_I2C_FIFOCFG_Bits',0,4,195,1,16,4,11
	.byte	'RXBS',0,1
	.word	348
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	348
	.byte	2,4,2,35,0,11
	.byte	'TXBS',0,1
	.word	348
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	348
	.byte	2,0,2,35,0,11
	.byte	'RXFA',0,1
	.word	348
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	348
	.byte	2,4,2,35,1,11
	.byte	'TXFA',0,1
	.word	348
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	348
	.byte	2,0,2,35,1,11
	.byte	'RXFC',0,1
	.word	348
	.byte	1,7,2,35,2,11
	.byte	'TXFC',0,1
	.word	348
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	379
	.byte	14,0,2,35,2,0,3
	.byte	'Ifx_I2C_FIFOCFG_Bits',0,4,208,1,3
	.word	2910
	.byte	10
	.byte	'_Ifx_I2C_GPCTL_Bits',0,4,211,1,16,4,11
	.byte	'PISEL',0,1
	.word	348
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	263
	.byte	29,0,2,35,0,0,3
	.byte	'Ifx_I2C_GPCTL_Bits',0,4,215,1,3
	.word	3178
	.byte	10
	.byte	'_Ifx_I2C_ICR_Bits',0,4,218,1,16,4,11
	.byte	'LSREQ_INT',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'SREQ_INT',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'LBREQ_INT',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'BREQ_INT',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	263
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_I2C_ICR_Bits',0,4,225,1,3
	.word	3272
	.byte	10
	.byte	'_Ifx_I2C_ID_Bits',0,4,228,1,16,4,11
	.byte	'MODREV',0,1
	.word	348
	.byte	8,0,2,35,0,11
	.byte	'MODNUMBER',0,1
	.word	348
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_I2C_ID_Bits',0,4,233,1,3
	.word	3427
	.byte	10
	.byte	'_Ifx_I2C_IMSC_Bits',0,4,236,1,16,4,11
	.byte	'LSREQ_INT',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'SREQ_INT',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'LBREQ_INT',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'BREQ_INT',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'I2C_ERR_INT',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'I2C_P_INT',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	263
	.byte	26,0,2,35,0,0,3
	.byte	'Ifx_I2C_IMSC_Bits',0,4,245,1,3
	.word	3538
	.byte	10
	.byte	'_Ifx_I2C_ISR_Bits',0,4,248,1,16,4,11
	.byte	'LSREQ_INT',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'SREQ_INT',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'LBREQ_INT',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'BREQ_INT',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'I2C_ERR_INT',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'I2C_P_INT',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	263
	.byte	26,0,2,35,0,0,3
	.byte	'Ifx_I2C_ISR_Bits',0,4,129,2,3
	.word	3739
	.byte	10
	.byte	'_Ifx_I2C_KRST0_Bits',0,4,132,2,16,4,11
	.byte	'RST',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	263
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_I2C_KRST0_Bits',0,4,137,2,3
	.word	3938
	.byte	10
	.byte	'_Ifx_I2C_KRST1_Bits',0,4,140,2,16,4,11
	.byte	'RST',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	263
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_I2C_KRST1_Bits',0,4,144,2,3
	.word	4049
	.byte	10
	.byte	'_Ifx_I2C_KRSTCLR_Bits',0,4,147,2,16,4,11
	.byte	'CLR',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	263
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_I2C_KRSTCLR_Bits',0,4,151,2,3
	.word	4141
	.byte	10
	.byte	'_Ifx_I2C_MIS_Bits',0,4,154,2,16,4,11
	.byte	'LSREQ_INT',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'SREQ_INT',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'LBREQ_INT',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'BREQ_INT',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'I2C_ERR_INT',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'I2C_P_INT',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	263
	.byte	26,0,2,35,0,0,3
	.byte	'Ifx_I2C_MIS_Bits',0,4,163,2,3
	.word	4237
	.byte	10
	.byte	'_Ifx_I2C_MODID_Bits',0,4,166,2,16,4,11
	.byte	'MODREV',0,1
	.word	348
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	348
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	379
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_I2C_MODID_Bits',0,4,171,2,3
	.word	4436
	.byte	10
	.byte	'_Ifx_I2C_MRPSCTRL_Bits',0,4,174,2,16,4,11
	.byte	'MRPS',0,2
	.word	379
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,4
	.word	263
	.byte	18,0,2,35,0,0,3
	.byte	'Ifx_I2C_MRPSCTRL_Bits',0,4,178,2,3
	.word	4549
	.byte	10
	.byte	'_Ifx_I2C_PIRQSC_Bits',0,4,181,2,16,4,11
	.byte	'AM',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'GC',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'MC',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'AL',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'NACK',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'TX_END',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'RX',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	263
	.byte	25,0,2,35,0,0,3
	.byte	'Ifx_I2C_PIRQSC_Bits',0,4,191,2,3
	.word	4649
	.byte	10
	.byte	'_Ifx_I2C_PIRQSM_Bits',0,4,194,2,16,4,11
	.byte	'AM',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'GC',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'MC',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'AL',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'NACK',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'TX_END',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'RX',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	263
	.byte	25,0,2,35,0,0,3
	.byte	'Ifx_I2C_PIRQSM_Bits',0,4,204,2,3
	.word	4832
	.byte	10
	.byte	'_Ifx_I2C_PIRQSS_Bits',0,4,207,2,16,4,11
	.byte	'AM',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'GC',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'MC',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'AL',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'NACK',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'TX_END',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'RX',0,1
	.word	348
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	263
	.byte	25,0,2,35,0,0,3
	.byte	'Ifx_I2C_PIRQSS_Bits',0,4,217,2,3
	.word	5015
	.byte	10
	.byte	'_Ifx_I2C_RIS_Bits',0,4,220,2,16,4,11
	.byte	'LSREQ_INT',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'SREQ_INT',0,1
	.word	348
	.byte	1,6,2,35,0,11
	.byte	'LBREQ_INT',0,1
	.word	348
	.byte	1,5,2,35,0,11
	.byte	'BREQ_INT',0,1
	.word	348
	.byte	1,4,2,35,0,11
	.byte	'I2C_ERR_INT',0,1
	.word	348
	.byte	1,3,2,35,0,11
	.byte	'I2C_P_INT',0,1
	.word	348
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	263
	.byte	26,0,2,35,0,0,3
	.byte	'Ifx_I2C_RIS_Bits',0,4,229,2,3
	.word	5198
	.byte	10
	.byte	'_Ifx_I2C_RPSSTAT_Bits',0,4,232,2,16,4,11
	.byte	'RPS',0,2
	.word	379
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,4
	.word	263
	.byte	18,0,2,35,0,0,3
	.byte	'Ifx_I2C_RPSSTAT_Bits',0,4,236,2,3
	.word	5397
	.byte	10
	.byte	'_Ifx_I2C_RUNCTRL_Bits',0,4,239,2,16,4,11
	.byte	'RUN',0,1
	.word	348
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	263
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_I2C_RUNCTRL_Bits',0,4,243,2,3
	.word	5494
	.byte	10
	.byte	'_Ifx_I2C_RXD_Bits',0,4,246,2,16,4,11
	.byte	'RXD',0,4
	.word	263
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_I2C_RXD_Bits',0,4,249,2,3
	.word	5590
	.byte	10
	.byte	'_Ifx_I2C_TIMCFG_Bits',0,4,252,2,16,4,11
	.byte	'SDA_DEL_HD_DAT',0,1
	.word	348
	.byte	6,2,2,35,0,11
	.byte	'HS_SDA_DEL_HD_DAT',0,2
	.word	379
	.byte	3,7,2,35,0,11
	.byte	'SCL_DEL_HD_STA',0,1
	.word	348
	.byte	3,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	348
	.byte	2,2,2,35,1,11
	.byte	'EN_SCL_LOW_LEN',0,1
	.word	348
	.byte	1,1,2,35,1,11
	.byte	'FS_SCL_LOW',0,1
	.word	348
	.byte	1,0,2,35,1,11
	.byte	'HS_SDA_DEL',0,1
	.word	348
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	348
	.byte	5,0,2,35,2,11
	.byte	'SCL_LOW_LEN',0,1
	.word	348
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_I2C_TIMCFG_Bits',0,4,135,3,3
	.word	5656
	.byte	10
	.byte	'_Ifx_I2C_TPSCTRL_Bits',0,4,138,3,16,4,11
	.byte	'TPS',0,2
	.word	379
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,4
	.word	263
	.byte	18,0,2,35,0,0,3
	.byte	'Ifx_I2C_TPSCTRL_Bits',0,4,142,3,3
	.word	5933
	.byte	10
	.byte	'_Ifx_I2C_TXD_Bits',0,4,145,3,16,4,11
	.byte	'TXD',0,4
	.word	263
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_I2C_TXD_Bits',0,4,148,3,3
	.word	6030
	.byte	12,4,156,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	676
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ACCEN0',0,4,161,3,3
	.word	6096
	.byte	12,4,164,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1233
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ACCEN1',0,4,169,3,3
	.word	6160
	.byte	12,4,172,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1310
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ADDRCFG',0,4,177,3,3
	.word	6224
	.byte	12,4,180,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1521
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_BUSSTAT',0,4,185,3,3
	.word	6289
	.byte	12,4,188,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1836
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_CLC',0,4,193,3,3
	.word	6354
	.byte	12,4,196,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1629
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_CLC1',0,4,201,3,3
	.word	6415
	.byte	12,4,204,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1940
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ENDDCTRL',0,4,209,3,3
	.word	6477
	.byte	12,4,212,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2059
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ERRIRQSC',0,4,217,3,3
	.word	6543
	.byte	12,4,220,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2218
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ERRIRQSM',0,4,225,3,3
	.word	6609
	.byte	12,4,228,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2377
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ERRIRQSS',0,4,233,3,3
	.word	6675
	.byte	12,4,236,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2536
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_FDIVCFG',0,4,241,3,3
	.word	6741
	.byte	12,4,244,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2671
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_FDIVHIGHCFG',0,4,249,3,3
	.word	6806
	.byte	12,4,252,3,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2814
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_FFSSTAT',0,4,129,4,3
	.word	6875
	.byte	12,4,132,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2910
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_FIFOCFG',0,4,137,4,3
	.word	6940
	.byte	12,4,140,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3178
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_GPCTL',0,4,145,4,3
	.word	7005
	.byte	12,4,148,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3272
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ICR',0,4,153,4,3
	.word	7068
	.byte	12,4,156,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3427
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ID',0,4,161,4,3
	.word	7129
	.byte	12,4,164,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3538
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_IMSC',0,4,169,4,3
	.word	7189
	.byte	12,4,172,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3739
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_ISR',0,4,177,4,3
	.word	7251
	.byte	12,4,180,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3938
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_KRST0',0,4,185,4,3
	.word	7312
	.byte	12,4,188,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4049
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_KRST1',0,4,193,4,3
	.word	7375
	.byte	12,4,196,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4141
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_KRSTCLR',0,4,201,4,3
	.word	7438
	.byte	12,4,204,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4237
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_MIS',0,4,209,4,3
	.word	7503
	.byte	12,4,212,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4436
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_MODID',0,4,217,4,3
	.word	7564
	.byte	12,4,220,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4549
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_MRPSCTRL',0,4,225,4,3
	.word	7627
	.byte	12,4,228,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4649
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_PIRQSC',0,4,233,4,3
	.word	7693
	.byte	12,4,236,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4832
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_PIRQSM',0,4,241,4,3
	.word	7757
	.byte	12,4,244,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5015
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_PIRQSS',0,4,249,4,3
	.word	7821
	.byte	12,4,252,4,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5198
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_RIS',0,4,129,5,3
	.word	7885
	.byte	12,4,132,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5397
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_RPSSTAT',0,4,137,5,3
	.word	7946
	.byte	12,4,140,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5494
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_RUNCTRL',0,4,145,5,3
	.word	8011
	.byte	12,4,148,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5590
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_RXD',0,4,153,5,3
	.word	8076
	.byte	12,4,156,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5656
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_TIMCFG',0,4,161,5,3
	.word	8137
	.byte	12,4,164,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5933
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_TPSCTRL',0,4,169,5,3
	.word	8201
	.byte	12,4,172,5,9,4,9
	.byte	'U',0
	.word	263
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	296
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6030
	.byte	4,2,35,0,0,3
	.byte	'Ifx_I2C_TXD',0,4,177,5,3
	.word	8266
	.byte	13,8
	.word	610
	.byte	14,0,0
.L8:
	.byte	15
	.word	8327
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxI2c_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxI2c_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	265
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxI2c_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxI2c_cfg_indexMap',0,1,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxI2c_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxI2c_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
