/*
 * user_display_gps.c
 *
 *  Created on: 2025��3��30��
 *      Author: lenovo
 */

#include "zf_common_headfile.h"

static int16 display_start_x     = 0;
static int16 display_start_y     = 0;
static int16 display_width       =0;
static int16 display_high =0;
static int16 transition_point_num =0;

static plane_point plane_point_data[DISPLAY_POINT_MAX];
static screen_point screen_point_data[DISPLAY_POINT_MAX];

static screen_type_enum display_screen_type = SCREEN_IPS114;
gps_point gps_point_test[10];

//  �������  ��γ��  ��������ϵ  ת��Ϊ ƽ������ϵ  ��λΪ��
//  ����˵��  gps_point_input ��γ������
static void spherical_to_plane(gps_point *gps_point_input)
{
    float meters_per_degree_lat = 0;
    float meters_per_degree_lon = 0;
    if(transition_point_num >1)
    {
        meters_per_degree_lat = (USER_PI/180.0)*EARTH_RADIUS1;   //����γ��ÿ������
        meters_per_degree_lon = meters_per_degree_lat * cosf(gps_point_input[0].lat * USER_PI / 180.0); //���㾭��ÿ������

        for(int i= 1;i<transition_point_num;i++)
        {
            plane_point_data[i].x = (gps_point_input[i].lon - gps_point_input[0].lon) * meters_per_degree_lon;   //����ƽ�����꣬������λ��

            plane_point_data[i].y = (gps_point_input[i].lat - gps_point_input[0].lat) * meters_per_degree_lat;   //����ƽ�����꣬������λ��
        }

    }

}
//�������  ƽ������ϵ  ת��Ϊ  ��Ļ����ϵ
static void plane_to_screen(void)
{
    double min_x = plane_point_data[0].x;
    double max_x = plane_point_data[0].x;
    double min_y = plane_point_data[0].y;
    double max_y = plane_point_data[0].y;

    double width   =  0.0f;
    double height  =  0.0f;
    double scale_x =  0.0f;
    double scale_y =  0.0f;
    double scale   =  0.0f;

    double x_offset = 0.0;
    double y_offset = 0.0;

    if(transition_point_num >1)
    {
        for(int i=1;i<transition_point_num;i++)   //�ҳ����귶Χ
        {
            min_x = fminf(min_x,plane_point_data[i].x);
            max_x = fmaxf(max_x,plane_point_data[i].x);
            min_y = fminf(min_y,plane_point_data[i].y);
            max_y = fmaxf(max_y,plane_point_data[i].y);
        }

        if(max_x - min_x >EPSILON && max_y - min_y >EPSILON)  //���е�λû���غ� �������һ������
        {
            //�������ű���
            width = max_x-min_x;  //���귶Χ  ����
            height = max_y - min_y;  //���귶Χ  �߶�
            scale_x = (float)display_width / width; //����������ű���
            scale_y =  (float)display_high / height; //����߶����ű���
            scale = fminf(scale_x,scale_y);    //�Ը�С�����ű���Ϊ�������ű�
            if(scale == scale_x)   //�����������߶Ⱦ���
            {
                y_offset = ((float)display_high - height * scale) / 2.0;
            }
            else
            {
                x_offset = ((float)display_width - width *scale) / 2.0;
            }

            for (int i= 0;i<transition_point_num;i++)
            {
                screen_point_data[i].x = (plane_point_data[i].x - min_x) * scale + x_offset;
                // �������Ӧ��Ļ���Ϸ�
                // ƽ������ϵ��y Խ���ʾԽ��
                // ��Ļ����ϵ��y Խ���ʾԽ��
                screen_point_data[i].y = (float)display_high - ((plane_point_data[i].y-min_y) * scale + y_offset);
            }
        }
    }

}
//�������  GPS����ϵ  ת��Ϊ  ��Ļ��ʾλ��
//����˵��  gps_point_input  ��γ������
//����˵��  point_num        ���ݳ���
void user_gps_transition(gps_point *gps_point_input,int16 point_num)
{
    zf_assert(point_num < DISPLAY_POINT_MAX);   //��Ҫ��ʾ�ĵ�����������������ʾ����  ��Ҫ�����޸�  DISPLAY_POINT_MAX��ֵ

    transition_point_num  = point_num;

    memset(plane_point_data,0,sizeof(plane_point_data));  //���֮ǰ��ƽ������ϵ����
    //memset ��һ���ڴ�ռ�����Ϊָ����ֵ
    memset(screen_point_data , 0,sizeof(screen_point_data));  //���֮ǰ����Ļ��������

    spherical_to_plane(gps_point_input);    //��γ����������ϵ  ת��Ϊ  ƽ������ϵ

    plane_to_screen();                       //ƽ������ϵ  ת��Ϊ  ��Ļ��ʾ��λ

}

//�������  ��Ļ��ʾ�������
//����˵��  display_color ���Ƶı�����ɫ����ͨ������ɫ����Ϊ�����ķ�ʽʵ��  ������
void user_gps_display(uint16 display_color)
{
    for(int i =0 ;i<(transition_point_num - 1);i++)
    {
        switch (display_screen_type)
        {
            case SCREEN_IPS200_SPI:
        ips200_draw_line((uint16)screen_point_data[i].x  + display_start_x,
                                                 (uint16)screen_point_data[i].y  + display_start_y,
                                                 (uint16)screen_point_data[i+1].x + display_start_x,
                                                 (uint16)screen_point_data[i+1].y + display_start_y,
                                                 display_color);
                break;
            case SCREEN_TFT180:
                tft180_draw_line((uint16)screen_point_data[i].x  + display_start_x,
                                                 (uint16)screen_point_data[i].y  + display_start_y,
                                                 (uint16)screen_point_data[i+1].x + display_start_x,
                                                 (uint16)screen_point_data[i+1].y + display_start_y,
                                                 display_color);
                break;
            default:
                break;
        }
    }
}
//   GPSʾ  ʼ
// ˵  screen_type  Ļ  ο screen_type_enum ö
// ˵  start_x  ʾΧx
// ˵  start_y  ʾΧy
// ˵  width    ʾ
// ˵  high     ʾ߶
void user_gps_display_init(screen_type_enum screen_type,int16 start_x,int16 start_y,int16 width,int16 high)
{
    display_start_x   = start_x;
    display_start_y   = start_y;
    display_width     = width - 1;
    display_high      = high  - 1;
    display_screen_type= screen_type;
}
void Function(void)
{
    user_gps_display_init(SCREEN_IPS200_SPI,10,10,220,300);
    user_gps_transition(gps_point_test,4);
    user_gps_display(RGB565_RED);
}

