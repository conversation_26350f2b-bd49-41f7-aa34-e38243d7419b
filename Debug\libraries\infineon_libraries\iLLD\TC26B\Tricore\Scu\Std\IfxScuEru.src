	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc44888a --dep-file=IfxScuEru.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c'

	
$TC16X
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_clearAllEventFlags',code,cluster('IfxScuEru_clearAllEventFlags')
	.sect	'.text.IfxScuEru.IfxScuEru_clearAllEventFlags'
	.align	2
	
	.global	IfxScuEru_clearAllEventFlags
; Function IfxScuEru_clearAllEventFlags
.L49:
IfxScuEru_clearAllEventFlags:	.type	func
	movh	d15,#255
.L321:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036224),d15
.L380:
	ret
.L234:
	
__IfxScuEru_clearAllEventFlags_function_end:
	.size	IfxScuEru_clearAllEventFlags,__IfxScuEru_clearAllEventFlags_function_end-IfxScuEru_clearAllEventFlags
.L111:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_clearEventFlag',code,cluster('IfxScuEru_clearEventFlag')
	.sect	'.text.IfxScuEru.IfxScuEru_clearEventFlag'
	.align	2
	
	.global	IfxScuEru_clearEventFlag
; Function IfxScuEru_clearEventFlag
.L51:
IfxScuEru_clearEventFlag:	.type	func
	mov	d15,#1
.L385:
	add	d0,d4,#16
.L386:
	sha	d15,d15,d0
.L322:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036224),d15
.L387:
	ret
.L236:
	
__IfxScuEru_clearEventFlag_function_end:
	.size	IfxScuEru_clearEventFlag,__IfxScuEru_clearEventFlag_function_end-IfxScuEru_clearEventFlag
.L116:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_clearInputChannelConfiguration',code,cluster('IfxScuEru_clearInputChannelConfiguration')
	.sect	'.text.IfxScuEru.IfxScuEru_clearInputChannelConfiguration'
	.align	2
	
	.global	IfxScuEru_clearInputChannelConfiguration
; Function IfxScuEru_clearInputChannelConfiguration
.L53:
IfxScuEru_clearInputChannelConfiguration:	.type	func
	sha	d0,d4,#-1
.L323:
	jz.t	d4:0,.L2
.L392:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L393:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L394:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036210)
.L395:
	mul	d15,d0,#4
	addsc.a	a2,a2,d15,#0
.L396:
	ld.w	d15,[a2]
.L397:
	insert	d15,d15,#0,#16,#16
.L398:
	st.w	[a15],d15
.L399:
	j	.L3
.L2:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L400:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L401:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036210)
.L402:
	mul	d15,d0,#4
	addsc.a	a2,a2,d15,#0
.L403:
	ld.w	d15,[a2]
.L404:
	insert	d15,d15,#0,#0,#16
.L405:
	st.w	[a15],d15
.L3:
	ret
.L239:
	
__IfxScuEru_clearInputChannelConfiguration_function_end:
	.size	IfxScuEru_clearInputChannelConfiguration,__IfxScuEru_clearInputChannelConfiguration_function_end-IfxScuEru_clearInputChannelConfiguration
.L121:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_clearOutputChannelConfiguration',code,cluster('IfxScuEru_clearOutputChannelConfiguration')
	.sect	'.text.IfxScuEru.IfxScuEru_clearOutputChannelConfiguration'
	.align	2
	
	.global	IfxScuEru_clearOutputChannelConfiguration
; Function IfxScuEru_clearOutputChannelConfiguration
.L55:
IfxScuEru_clearOutputChannelConfiguration:	.type	func
	sha	d0,d4,#-1
.L324:
	jz.t	d4:0,.L4
.L606:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L607:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L608:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf003622c)
.L609:
	mul	d15,d0,#4
	addsc.a	a2,a2,d15,#0
.L610:
	ld.w	d15,[a2]
.L611:
	insert	d15,d15,#0,#16,#16
.L612:
	st.w	[a15],d15
.L613:
	j	.L5
.L4:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L614:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L615:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf003622c)
.L616:
	mul	d15,d0,#4
	addsc.a	a2,a2,d15,#0
.L617:
	ld.w	d15,[a2]
.L618:
	insert	d15,d15,#0,#0,#16
.L619:
	st.w	[a15],d15
.L5:
	ret
.L297:
	
__IfxScuEru_clearOutputChannelConfiguration_function_end:
	.size	IfxScuEru_clearOutputChannelConfiguration,__IfxScuEru_clearOutputChannelConfiguration_function_end-IfxScuEru_clearOutputChannelConfiguration
.L196:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_connectTrigger',code,cluster('IfxScuEru_connectTrigger')
	.sect	'.text.IfxScuEru.IfxScuEru_connectTrigger'
	.align	2
	
	.global	IfxScuEru_connectTrigger
; Function IfxScuEru_connectTrigger
.L57:
IfxScuEru_connectTrigger:	.type	func
	sha	d0,d4,#-1
.L325:
	jz.t	d4:0,.L6
.L537:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L538:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L539:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L540:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L541:
	ld.bu	d15,[a15]3
.L542:
	insert	d15,d15,d5,#4,#3
	st.b	[a2]3,d15
.L543:
	j	.L7
.L6:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L544:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L545:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L546:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L547:
	ld.bu	d15,[a15]1
.L548:
	insert	d15,d15,d5,#4,#3
	st.b	[a2]1,d15
.L7:
	ret
.L274:
	
__IfxScuEru_connectTrigger_function_end:
	.size	IfxScuEru_connectTrigger,__IfxScuEru_connectTrigger_function_end-IfxScuEru_connectTrigger
.L176:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_disableAutoClear',code,cluster('IfxScuEru_disableAutoClear')
	.sect	'.text.IfxScuEru.IfxScuEru_disableAutoClear'
	.align	2
	
	.global	IfxScuEru_disableAutoClear
; Function IfxScuEru_disableAutoClear
.L59:
IfxScuEru_disableAutoClear:	.type	func
	sha	d0,d4,#-1
.L326:
	jz.t	d4:0,.L8
.L410:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L411:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L412:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L413:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L414:
	ld.bu	d15,[a15]3
.L415:
	insert	d15,d15,#0,#2,#1
	st.b	[a2]3,d15
.L416:
	j	.L9
.L8:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L417:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L418:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L419:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L420:
	ld.bu	d15,[a15]1
.L421:
	insert	d15,d15,#0,#2,#1
	st.b	[a2]1,d15
.L9:
	ret
.L243:
	
__IfxScuEru_disableAutoClear_function_end:
	.size	IfxScuEru_disableAutoClear,__IfxScuEru_disableAutoClear_function_end-IfxScuEru_disableAutoClear
.L126:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_disableFallingEdgeDetection',code,cluster('IfxScuEru_disableFallingEdgeDetection')
	.sect	'.text.IfxScuEru.IfxScuEru_disableFallingEdgeDetection'
	.align	2
	
	.global	IfxScuEru_disableFallingEdgeDetection
; Function IfxScuEru_disableFallingEdgeDetection
.L61:
IfxScuEru_disableFallingEdgeDetection:	.type	func
	sha	d0,d4,#-1
.L327:
	jz.t	d4:0,.L10
.L426:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L427:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L428:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L429:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L430:
	ld.bu	d15,[a15]3
.L431:
	insert	d15,d15,#0,#0,#1
	st.b	[a2]3,d15
.L432:
	j	.L11
.L10:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L433:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L434:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L435:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L436:
	ld.bu	d15,[a15]1
.L437:
	insert	d15,d15,#0,#0,#1
	st.b	[a2]1,d15
.L11:
	ret
.L246:
	
__IfxScuEru_disableFallingEdgeDetection_function_end:
	.size	IfxScuEru_disableFallingEdgeDetection,__IfxScuEru_disableFallingEdgeDetection_function_end-IfxScuEru_disableFallingEdgeDetection
.L131:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_disablePatternDetectionTrigger',code,cluster('IfxScuEru_disablePatternDetectionTrigger')
	.sect	'.text.IfxScuEru.IfxScuEru_disablePatternDetectionTrigger'
	.align	2
	
	.global	IfxScuEru_disablePatternDetectionTrigger
; Function IfxScuEru_disablePatternDetectionTrigger
.L63:
IfxScuEru_disablePatternDetectionTrigger:	.type	func
	sha	d0,d4,#-1
.L328:
	jz.t	d4:0,.L12
.L624:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L625:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L626:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L627:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L628:
	ld.bu	d15,[a15]3
.L629:
	insert	d15,d15,#0,#5,#1
	st.b	[a2]3,d15
.L630:
	j	.L13
.L12:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L631:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L632:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L633:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L634:
	ld.bu	d15,[a15]1
.L635:
	insert	d15,d15,#0,#5,#1
	st.b	[a2]1,d15
.L13:
	ret
.L301:
	
__IfxScuEru_disablePatternDetectionTrigger_function_end:
	.size	IfxScuEru_disablePatternDetectionTrigger,__IfxScuEru_disablePatternDetectionTrigger_function_end-IfxScuEru_disablePatternDetectionTrigger
.L201:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_disableRisingEdgeDetection',code,cluster('IfxScuEru_disableRisingEdgeDetection')
	.sect	'.text.IfxScuEru.IfxScuEru_disableRisingEdgeDetection'
	.align	2
	
	.global	IfxScuEru_disableRisingEdgeDetection
; Function IfxScuEru_disableRisingEdgeDetection
.L65:
IfxScuEru_disableRisingEdgeDetection:	.type	func
	sha	d0,d4,#-1
.L329:
	jz.t	d4:0,.L14
.L442:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L443:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L444:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L445:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L446:
	ld.bu	d15,[a15]3
.L447:
	insert	d15,d15,#0,#1,#1
	st.b	[a2]3,d15
.L448:
	j	.L15
.L14:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L449:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L450:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L451:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L452:
	ld.bu	d15,[a15]1
.L453:
	insert	d15,d15,#0,#1,#1
	st.b	[a2]1,d15
.L15:
	ret
.L249:
	
__IfxScuEru_disableRisingEdgeDetection_function_end:
	.size	IfxScuEru_disableRisingEdgeDetection,__IfxScuEru_disableRisingEdgeDetection_function_end-IfxScuEru_disableRisingEdgeDetection
.L136:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_disableTriggerPulse',code,cluster('IfxScuEru_disableTriggerPulse')
	.sect	'.text.IfxScuEru.IfxScuEru_disableTriggerPulse'
	.align	2
	
	.global	IfxScuEru_disableTriggerPulse
; Function IfxScuEru_disableTriggerPulse
.L67:
IfxScuEru_disableTriggerPulse:	.type	func
	sha	d0,d4,#-1
.L330:
	jz.t	d4:0,.L16
.L553:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L554:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L555:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L556:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L557:
	ld.bu	d15,[a15]3
.L558:
	insert	d15,d15,#0,#3,#1
	st.b	[a2]3,d15
.L559:
	j	.L17
.L16:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L560:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L561:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L562:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L563:
	ld.bu	d15,[a15]1
.L564:
	insert	d15,d15,#0,#3,#1
	st.b	[a2]1,d15
.L17:
	ret
.L279:
	
__IfxScuEru_disableTriggerPulse_function_end:
	.size	IfxScuEru_disableTriggerPulse,__IfxScuEru_disableTriggerPulse_function_end-IfxScuEru_disableTriggerPulse
.L181:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_enableAutoClear',code,cluster('IfxScuEru_enableAutoClear')
	.sect	'.text.IfxScuEru.IfxScuEru_enableAutoClear'
	.align	2
	
	.global	IfxScuEru_enableAutoClear
; Function IfxScuEru_enableAutoClear
.L69:
IfxScuEru_enableAutoClear:	.type	func
	sha	d0,d4,#-1
.L331:
	jz.t	d4:0,.L18
.L458:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L459:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L460:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L461:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L462:
	ld.bu	d15,[a15]3
.L463:
	or	d15,#4
	st.b	[a2]3,d15
.L464:
	j	.L19
.L18:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L465:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L466:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L467:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L468:
	ld.bu	d15,[a15]1
.L469:
	or	d15,#4
	st.b	[a2]1,d15
.L19:
	ret
.L252:
	
__IfxScuEru_enableAutoClear_function_end:
	.size	IfxScuEru_enableAutoClear,__IfxScuEru_enableAutoClear_function_end-IfxScuEru_enableAutoClear
.L141:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_enableFallingEdgeDetection',code,cluster('IfxScuEru_enableFallingEdgeDetection')
	.sect	'.text.IfxScuEru.IfxScuEru_enableFallingEdgeDetection'
	.align	2
	
	.global	IfxScuEru_enableFallingEdgeDetection
; Function IfxScuEru_enableFallingEdgeDetection
.L71:
IfxScuEru_enableFallingEdgeDetection:	.type	func
	sha	d0,d4,#-1
.L332:
	jz.t	d4:0,.L20
.L474:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L475:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L476:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L477:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L478:
	ld.bu	d15,[a15]3
.L479:
	or	d15,#1
	st.b	[a2]3,d15
.L480:
	j	.L21
.L20:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L481:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L482:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L483:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L484:
	ld.bu	d15,[a15]1
.L485:
	or	d15,#1
	st.b	[a2]1,d15
.L21:
	ret
.L255:
	
__IfxScuEru_enableFallingEdgeDetection_function_end:
	.size	IfxScuEru_enableFallingEdgeDetection,__IfxScuEru_enableFallingEdgeDetection_function_end-IfxScuEru_enableFallingEdgeDetection
.L146:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_enablePatternDetectionTrigger',code,cluster('IfxScuEru_enablePatternDetectionTrigger')
	.sect	'.text.IfxScuEru.IfxScuEru_enablePatternDetectionTrigger'
	.align	2
	
	.global	IfxScuEru_enablePatternDetectionTrigger
; Function IfxScuEru_enablePatternDetectionTrigger
.L73:
IfxScuEru_enablePatternDetectionTrigger:	.type	func
	sha	d0,d4,#-1
.L333:
	jz.t	d4:0,.L22
.L640:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L641:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L642:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L643:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L644:
	ld.bu	d15,[a15]3
.L645:
	or	d15,#32
	st.b	[a2]3,d15
.L646:
	j	.L23
.L22:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L647:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L648:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L649:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L650:
	ld.bu	d15,[a15]1
.L651:
	or	d15,#32
	st.b	[a2]1,d15
.L23:
	ret
.L304:
	
__IfxScuEru_enablePatternDetectionTrigger_function_end:
	.size	IfxScuEru_enablePatternDetectionTrigger,__IfxScuEru_enablePatternDetectionTrigger_function_end-IfxScuEru_enablePatternDetectionTrigger
.L206:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_enableRisingEdgeDetection',code,cluster('IfxScuEru_enableRisingEdgeDetection')
	.sect	'.text.IfxScuEru.IfxScuEru_enableRisingEdgeDetection'
	.align	2
	
	.global	IfxScuEru_enableRisingEdgeDetection
; Function IfxScuEru_enableRisingEdgeDetection
.L75:
IfxScuEru_enableRisingEdgeDetection:	.type	func
	sha	d0,d4,#-1
.L334:
	jz.t	d4:0,.L24
.L490:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L491:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L492:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L493:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L494:
	ld.bu	d15,[a15]3
.L495:
	or	d15,#2
	st.b	[a2]3,d15
.L496:
	j	.L25
.L24:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L497:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L498:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L499:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L500:
	ld.bu	d15,[a15]1
.L501:
	or	d15,#2
	st.b	[a2]1,d15
.L25:
	ret
.L258:
	
__IfxScuEru_enableRisingEdgeDetection_function_end:
	.size	IfxScuEru_enableRisingEdgeDetection,__IfxScuEru_enableRisingEdgeDetection_function_end-IfxScuEru_enableRisingEdgeDetection
.L151:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_enableTriggerPulse',code,cluster('IfxScuEru_enableTriggerPulse')
	.sect	'.text.IfxScuEru.IfxScuEru_enableTriggerPulse'
	.align	2
	
	.global	IfxScuEru_enableTriggerPulse
; Function IfxScuEru_enableTriggerPulse
.L77:
IfxScuEru_enableTriggerPulse:	.type	func
	sha	d0,d4,#-1
.L335:
	jz.t	d4:0,.L26
.L569:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L570:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L571:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L572:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L573:
	ld.bu	d15,[a15]3
.L574:
	or	d15,#8
	st.b	[a2]3,d15
.L575:
	j	.L27
.L26:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L576:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L577:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L578:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L579:
	ld.bu	d15,[a15]1
.L580:
	or	d15,#8
	st.b	[a2]1,d15
.L27:
	ret
.L282:
	
__IfxScuEru_enableTriggerPulse_function_end:
	.size	IfxScuEru_enableTriggerPulse,__IfxScuEru_enableTriggerPulse_function_end-IfxScuEru_enableTriggerPulse
.L186:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_getAllEventFlagsStatus',code,cluster('IfxScuEru_getAllEventFlagsStatus')
	.sect	'.text.IfxScuEru.IfxScuEru_getAllEventFlagsStatus'
	.align	2
	
	.global	IfxScuEru_getAllEventFlagsStatus
; Function IfxScuEru_getAllEventFlagsStatus
.L79:
IfxScuEru_getAllEventFlagsStatus:	.type	func
	movh.a	a15,#61443
	ld.w	d2,[a15]@los(0xf0036220)
.L506:
	j	.L28
.L28:
	ret
.L261:
	
__IfxScuEru_getAllEventFlagsStatus_function_end:
	.size	IfxScuEru_getAllEventFlagsStatus,__IfxScuEru_getAllEventFlagsStatus_function_end-IfxScuEru_getAllEventFlagsStatus
.L156:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_getEventFlagStatus',code,cluster('IfxScuEru_getEventFlagStatus')
	.sect	'.text.IfxScuEru.IfxScuEru_getEventFlagStatus'
	.align	2
	
	.global	IfxScuEru_getEventFlagStatus
; Function IfxScuEru_getEventFlagStatus
.L81:
IfxScuEru_getEventFlagStatus:	.type	func
	mov	d15,#1
.L511:
	sh	d15,d15,d4
.L336:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf0036220)
.L512:
	and	d0,d15
.L513:
	jeq	d0,#0,.L29
.L514:
	mov	d2,#1
.L515:
	j	.L30
.L29:
	mov	d2,#0
.L30:
	j	.L31
.L31:
	ret
.L263:
	
__IfxScuEru_getEventFlagStatus_function_end:
	.size	IfxScuEru_getEventFlagStatus,__IfxScuEru_getEventFlagStatus_function_end-IfxScuEru_getEventFlagStatus
.L161:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_getInputChannelConfiguration',code,cluster('IfxScuEru_getInputChannelConfiguration')
	.sect	'.text.IfxScuEru.IfxScuEru_getInputChannelConfiguration'
	.align	2
	
	.global	IfxScuEru_getInputChannelConfiguration
; Function IfxScuEru_getInputChannelConfiguration
.L83:
IfxScuEru_getInputChannelConfiguration:	.type	func
	sha	d15,d4,#-1
.L337:
	jz.t	d4:0,.L32
.L520:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L521:
	mul	d15,d15,#4
.L338:
	addsc.a	a15,a15,d15,#0
.L522:
	ld.w	d15,[a15]
.L523:
	insert	d2,d15,#0,#0,#16
.L339:
	j	.L33
.L32:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L524:
	mul	d15,d15,#4
.L340:
	addsc.a	a15,a15,d15,#0
.L525:
	ld.w	d15,[a15]
.L526:
	insert	d2,d15,#0,#16,#16
.L33:
	j	.L34
.L34:
	ret
.L266:
	
__IfxScuEru_getInputChannelConfiguration_function_end:
	.size	IfxScuEru_getInputChannelConfiguration,__IfxScuEru_getInputChannelConfiguration_function_end-IfxScuEru_getInputChannelConfiguration
.L166:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_getOutputChannelConfiguration',code,cluster('IfxScuEru_getOutputChannelConfiguration')
	.sect	'.text.IfxScuEru.IfxScuEru_getOutputChannelConfiguration'
	.align	2
	
	.global	IfxScuEru_getOutputChannelConfiguration
; Function IfxScuEru_getOutputChannelConfiguration
.L85:
IfxScuEru_getOutputChannelConfiguration:	.type	func
	sha	d15,d4,#-1
.L341:
	jz.t	d4:0,.L35
.L656:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L657:
	mul	d15,d15,#4
.L342:
	addsc.a	a15,a15,d15,#0
.L658:
	ld.w	d15,[a15]
.L659:
	insert	d2,d15,#0,#0,#16
.L343:
	j	.L36
.L35:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L660:
	mul	d15,d15,#4
.L344:
	addsc.a	a15,a15,d15,#0
.L661:
	ld.w	d15,[a15]
.L662:
	insert	d2,d15,#0,#16,#16
.L36:
	j	.L37
.L37:
	ret
.L307:
	
__IfxScuEru_getOutputChannelConfiguration_function_end:
	.size	IfxScuEru_getOutputChannelConfiguration,__IfxScuEru_getOutputChannelConfiguration_function_end-IfxScuEru_getOutputChannelConfiguration
.L211:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_getPatternDetectionResult',code,cluster('IfxScuEru_getPatternDetectionResult')
	.sect	'.text.IfxScuEru.IfxScuEru_getPatternDetectionResult'
	.align	2
	
	.global	IfxScuEru_getPatternDetectionResult
; Function IfxScuEru_getPatternDetectionResult
.L87:
IfxScuEru_getPatternDetectionResult:	.type	func
	mov	d15,#1
.L667:
	sh	d15,d15,d4
.L345:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf0036228)
.L668:
	and	d0,d15
.L669:
	jeq	d0,#0,.L38
.L670:
	mov	d2,#1
.L671:
	j	.L39
.L38:
	mov	d2,#0
.L39:
	j	.L40
.L40:
	ret
.L312:
	
__IfxScuEru_getPatternDetectionResult_function_end:
	.size	IfxScuEru_getPatternDetectionResult,__IfxScuEru_getPatternDetectionResult_function_end-IfxScuEru_getPatternDetectionResult
.L216:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_getWholePatternDetectionResult',code,cluster('IfxScuEru_getWholePatternDetectionResult')
	.sect	'.text.IfxScuEru.IfxScuEru_getWholePatternDetectionResult'
	.align	2
	
	.global	IfxScuEru_getWholePatternDetectionResult
; Function IfxScuEru_getWholePatternDetectionResult
.L89:
IfxScuEru_getWholePatternDetectionResult:	.type	func
	movh.a	a15,#61443
	ld.w	d2,[a15]@los(0xf0036228)
.L676:
	j	.L41
.L41:
	ret
.L315:
	
__IfxScuEru_getWholePatternDetectionResult_function_end:
	.size	IfxScuEru_getWholePatternDetectionResult,__IfxScuEru_getWholePatternDetectionResult_function_end-IfxScuEru_getWholePatternDetectionResult
.L221:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_selectExternalInput',code,cluster('IfxScuEru_selectExternalInput')
	.sect	'.text.IfxScuEru.IfxScuEru_selectExternalInput'
	.align	2
	
	.global	IfxScuEru_selectExternalInput
; Function IfxScuEru_selectExternalInput
.L91:
IfxScuEru_selectExternalInput:	.type	func
	sha	d0,d4,#-1
.L346:
	jz.t	d4:0,.L42
.L364:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L365:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L366:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L367:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L368:
	ld.bu	d15,[a15]2
.L369:
	insert	d15,d15,d5,#4,#3
	st.b	[a2]2,d15
.L370:
	j	.L43
.L42:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036210)
.L371:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L372:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf0036210)
.L373:
	mul	d15,d0,#4
	addsc.a	a2,a2,d15,#0
.L374:
	ld.bu	d15,[a2]
.L375:
	insert	d15,d15,d5,#4,#3
	st.b	[a15],d15
.L43:
	ret
.L227:
	
__IfxScuEru_selectExternalInput_function_end:
	.size	IfxScuEru_selectExternalInput,__IfxScuEru_selectExternalInput_function_end-IfxScuEru_selectExternalInput
.L106:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_setEventFlag',code,cluster('IfxScuEru_setEventFlag')
	.sect	'.text.IfxScuEru.IfxScuEru_setEventFlag'
	.align	2
	
	.global	IfxScuEru_setEventFlag
; Function IfxScuEru_setEventFlag
.L93:
IfxScuEru_setEventFlag:	.type	func
	mov	d15,#1
.L531:
	sha	d15,d15,d4
.L347:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036224),d15
.L532:
	ret
.L271:
	
__IfxScuEru_setEventFlag_function_end:
	.size	IfxScuEru_setEventFlag,__IfxScuEru_setEventFlag_function_end-IfxScuEru_setEventFlag
.L171:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_setFlagPatternDetection',code,cluster('IfxScuEru_setFlagPatternDetection')
	.sect	'.text.IfxScuEru.IfxScuEru_setFlagPatternDetection'
	.align	2
	
	.global	IfxScuEru_setFlagPatternDetection
; Function IfxScuEru_setFlagPatternDetection
.L95:
IfxScuEru_setFlagPatternDetection:	.type	func
	jz.t	d4:0,.L44
.L585:
	add	d1,d5,#16
.L350:
	mov	d0,#1
.L586:
	sha	d0,d0,d1
.L292:
	sha	d4,#-1
.L348:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L587:
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L588:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf003622c)
.L589:
	mul	d15,d4,#4
	addsc.a	a2,a2,d15,#0
.L590:
	ld.w	d15,[a2]
.L591:
	mov	d2,#-1
	xor	d0,d2
.L351:
	and	d15,d0
.L592:
	sh	d6,d6,d1
.L349:
	or	d15,d6
.L593:
	st.w	[a15],d15
.L293:
	j	.L45
.L44:
	mov	d0,#1
.L594:
	sha	d0,d0,d5
.L295:
	sha	d4,#-1
.L352:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L595:
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L596:
	movh.a	a2,#61443
	lea	a2,[a2]@los(0xf003622c)
.L597:
	mul	d15,d4,#4
	addsc.a	a2,a2,d15,#0
.L598:
	ld.w	d15,[a2]
.L599:
	mov	d1,#-1
	xor	d0,d1
.L354:
	and	d15,d0
.L600:
	sh	d6,d6,d5
.L353:
	or	d15,d6
.L601:
	st.w	[a15],d15
.L45:
	ret
.L285:
	
__IfxScuEru_setFlagPatternDetection_function_end:
	.size	IfxScuEru_setFlagPatternDetection,__IfxScuEru_setFlagPatternDetection_function_end-IfxScuEru_setFlagPatternDetection
.L191:
	; End of function
	
	.sdecl	'.text.IfxScuEru.IfxScuEru_setInterruptGatingPattern',code,cluster('IfxScuEru_setInterruptGatingPattern')
	.sect	'.text.IfxScuEru.IfxScuEru_setInterruptGatingPattern'
	.align	2
	
	.global	IfxScuEru_setInterruptGatingPattern
; Function IfxScuEru_setInterruptGatingPattern
.L97:
IfxScuEru_setInterruptGatingPattern:	.type	func
	sha	d0,d4,#-1
.L355:
	jz.t	d4:0,.L46
.L681:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L682:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L683:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L684:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L685:
	ld.bu	d15,[a15]3
.L686:
	insert	d15,d15,d5,#6,#2
	st.b	[a2]3,d15
.L687:
	j	.L47
.L46:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L688:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
.L689:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003622c)
.L690:
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L691:
	ld.bu	d15,[a15]1
.L692:
	insert	d15,d15,d5,#6,#2
	st.b	[a2]1,d15
.L47:
	ret
.L316:
	
__IfxScuEru_setInterruptGatingPattern_function_end:
	.size	IfxScuEru_setInterruptGatingPattern,__IfxScuEru_setInterruptGatingPattern_function_end-IfxScuEru_setInterruptGatingPattern
.L226:
	; End of function
	
	.calls	'IfxScuEru_clearAllEventFlags','',0
	.calls	'IfxScuEru_clearEventFlag','',0
	.calls	'IfxScuEru_clearInputChannelConfiguration','',0
	.calls	'IfxScuEru_clearOutputChannelConfiguration','',0
	.calls	'IfxScuEru_connectTrigger','',0
	.calls	'IfxScuEru_disableAutoClear','',0
	.calls	'IfxScuEru_disableFallingEdgeDetection','',0
	.calls	'IfxScuEru_disablePatternDetectionTrigger','',0
	.calls	'IfxScuEru_disableRisingEdgeDetection','',0
	.calls	'IfxScuEru_disableTriggerPulse','',0
	.calls	'IfxScuEru_enableAutoClear','',0
	.calls	'IfxScuEru_enableFallingEdgeDetection','',0
	.calls	'IfxScuEru_enablePatternDetectionTrigger','',0
	.calls	'IfxScuEru_enableRisingEdgeDetection','',0
	.calls	'IfxScuEru_enableTriggerPulse','',0
	.calls	'IfxScuEru_getAllEventFlagsStatus','',0
	.calls	'IfxScuEru_getEventFlagStatus','',0
	.calls	'IfxScuEru_getInputChannelConfiguration','',0
	.calls	'IfxScuEru_getOutputChannelConfiguration','',0
	.calls	'IfxScuEru_getPatternDetectionResult','',0
	.calls	'IfxScuEru_getWholePatternDetectionResult','',0
	.calls	'IfxScuEru_selectExternalInput','',0
	.calls	'IfxScuEru_setEventFlag','',0
	.calls	'IfxScuEru_setFlagPatternDetection','',0
	.calls	'IfxScuEru_setInterruptGatingPattern','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L99:
	.word	78796
	.half	3
	.word	.L100
	.byte	4
.L98:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L101
	.byte	2,1,1,3
	.word	233
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	236
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	281
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	293
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	405
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	379
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	411
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	411
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	379
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	520
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	520
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	536
	.byte	4,2,35,0,0
.L262:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	711
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	955
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	632
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	915
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1146
	.byte	4,2,35,8,0,14
	.word	1186
	.byte	3
	.word	1249
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1254
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	689
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1254
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	689
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	689
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1254
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1484
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1800
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2371
	.byte	4,2,35,0,0,15,4
	.word	672
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2499
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2714
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2929
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3146
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3366
	.byte	4,2,35,0,0,15,24
	.word	672
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3689
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3993
	.byte	4,2,35,0,0,15,8
	.word	672
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4318
	.byte	4,2,35,0,0,15,12
	.word	672
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4658
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	497
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5024
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5310
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5457
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	497
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5626
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5798
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	689
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5973
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6147
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6321
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6497
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6653
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6986
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7334
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7458
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7542
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7722
	.byte	4,2,35,0,0,15,76
	.word	672
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7975
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8062
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1760
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2331
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2450
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2490
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2674
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2889
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3106
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3326
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2490
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3640
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3680
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3953
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4269
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4309
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4609
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4649
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4984
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5270
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4309
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5417
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5586
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5758
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5933
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6107
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6281
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6457
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6613
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6946
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7294
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4309
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7418
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7667
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7926
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7966
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8022
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8589
	.byte	4,3,35,252,1,0,14
	.word	8629
	.byte	3
	.word	9232
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9237
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	672
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9242
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9237
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	672
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9447
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9628
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	672
	.byte	1,1,6,0
.L232:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	9783
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	689
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	672
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	689
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	9783
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	9783
	.byte	19,6,0,0,20
	.word	241
	.byte	21
	.word	267
	.byte	6,0,20
	.word	302
	.byte	21
	.word	334
	.byte	6,0,20
	.word	347
	.byte	6,0,20
	.word	416
	.byte	21
	.word	435
	.byte	6,0,20
	.word	451
	.byte	21
	.word	466
	.byte	21
	.word	480
	.byte	6,0,20
	.word	1259
	.byte	21
	.word	1299
	.byte	21
	.word	1317
	.byte	6,0,20
	.word	1337
	.byte	21
	.word	1375
	.byte	21
	.word	1393
	.byte	6,0,20
	.word	1413
	.byte	21
	.word	1464
	.byte	6,0,20
	.word	9367
	.byte	21
	.word	9399
	.byte	21
	.word	9413
	.byte	21
	.word	9431
	.byte	6,0,20
	.word	9550
	.byte	21
	.word	9578
	.byte	21
	.word	9592
	.byte	21
	.word	9610
	.byte	6,0,20
	.word	9707
	.byte	6,0,20
	.word	9741
	.byte	6,0,20
	.word	9804
	.byte	21
	.word	9845
	.byte	6,0,20
	.word	9864
	.byte	21
	.word	9919
	.byte	6,0,20
	.word	9938
	.byte	21
	.word	9978
	.byte	21
	.word	9995
	.byte	19,6,0,0
.L228:
	.byte	17,9,92,9,1,18
	.byte	'IfxScuEru_InputChannel_0',0,0,18
	.byte	'IfxScuEru_InputChannel_1',0,1,18
	.byte	'IfxScuEru_InputChannel_2',0,2,18
	.byte	'IfxScuEru_InputChannel_3',0,3,18
	.byte	'IfxScuEru_InputChannel_4',0,4,18
	.byte	'IfxScuEru_InputChannel_5',0,5,18
	.byte	'IfxScuEru_InputChannel_6',0,6,18
	.byte	'IfxScuEru_InputChannel_7',0,7,0
.L230:
	.byte	17,9,82,9,1,18
	.byte	'IfxScuEru_ExternalInputSelection_0',0,0,18
	.byte	'IfxScuEru_ExternalInputSelection_1',0,1,18
	.byte	'IfxScuEru_ExternalInputSelection_2',0,2,18
	.byte	'IfxScuEru_ExternalInputSelection_3',0,3,0
.L276:
	.byte	17,9,107,9,1,18
	.byte	'IfxScuEru_InputNodePointer_0',0,0,18
	.byte	'IfxScuEru_InputNodePointer_1',0,1,18
	.byte	'IfxScuEru_InputNodePointer_2',0,2,18
	.byte	'IfxScuEru_InputNodePointer_3',0,3,18
	.byte	'IfxScuEru_InputNodePointer_4',0,4,18
	.byte	'IfxScuEru_InputNodePointer_5',0,5,18
	.byte	'IfxScuEru_InputNodePointer_6',0,6,18
	.byte	'IfxScuEru_InputNodePointer_7',0,7,0
.L286:
	.byte	17,9,132,1,9,1,18
	.byte	'IfxScuEru_OutputChannel_0',0,0,18
	.byte	'IfxScuEru_OutputChannel_1',0,1,18
	.byte	'IfxScuEru_OutputChannel_2',0,2,18
	.byte	'IfxScuEru_OutputChannel_3',0,3,18
	.byte	'IfxScuEru_OutputChannel_4',0,4,18
	.byte	'IfxScuEru_OutputChannel_5',0,5,18
	.byte	'IfxScuEru_OutputChannel_6',0,6,18
	.byte	'IfxScuEru_OutputChannel_7',0,7,0
.L318:
	.byte	17,9,122,9,1,18
	.byte	'IfxScuEru_InterruptGatingPattern_none',0,0,18
	.byte	'IfxScuEru_InterruptGatingPattern_alwaysActive',0,1,18
	.byte	'IfxScuEru_InterruptGatingPattern_patternMatch',0,2,18
	.byte	'IfxScuEru_InterruptGatingPattern_patternMiss',0,3,0,7
	.byte	'short int',0,2,5,22
	.byte	'__wchar_t',0,10,1,1
	.word	11271
	.byte	22
	.byte	'__size_t',0,10,1,1
	.word	497
	.byte	22
	.byte	'__ptrdiff_t',0,10,1,1
	.word	513
	.byte	23,1,3
	.word	11339
	.byte	22
	.byte	'__codeptr',0,10,1,1
	.word	11341
	.byte	17,11,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,22
	.byte	'IfxScu_CCUCON0_CLKSEL',0,11,240,10,3
	.word	11364
	.byte	17,11,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,22
	.byte	'IfxScu_WDTCON1_IR',0,11,255,10,3
	.word	11461
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	11583
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	12140
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	12217
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	672
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	12353
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	672
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	12633
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	12871
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	672
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	12999
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	672
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	13242
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	13477
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	13605
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	13705
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	672
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	13805
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	497
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	14013
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	689
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	14178
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	14361
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	497
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	14515
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	14879
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	689
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	672
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	15090
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	497
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	15342
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	15460
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	15571
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	15734
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	15897
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	16055
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	672
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	672
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	10,0,2,35,2,0,22
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	16220
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	672
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	689
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	16549
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	16770
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	16933
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	17205
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	17358
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	17514
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	17676
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	17819
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	17984
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	18129
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	18310
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	18484
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	18644
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	18788
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	19062
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	19201
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	672
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	689
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	672
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	672
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	19364
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	689
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	19582
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	19745
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	20081
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	672
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	20188
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	20640
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	20739
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	689
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	20889
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	497
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	672
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	21038
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	21199
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	689
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	21329
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	21461
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	689
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	21576
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	689
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	21687
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	672
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	672
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	672
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	21845
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	22257
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	689
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	6,0,2,35,3,0,22
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	22358
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	22625
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	22761
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	22872
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	23005
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	23208
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	672
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	672
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	23564
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	23742
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	672
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	23842
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	672
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	24212
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	24398
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	24596
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	24829
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	672
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	672
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	24981
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	25548
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	25842
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	672
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	672
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	26120
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	26616
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	689
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	26929
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	672
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	27138
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	27349
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	27781
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	672
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	672
	.byte	7,0,2,35,3,0,22
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	27877
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	28137
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	497
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	28262
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	28459
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	28612
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	28765
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	28918
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	536
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	711
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	955
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	520
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	29173
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	29299
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	29551
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11583
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	29770
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12140
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	29834
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12217
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	29898
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12353
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	29963
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12633
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	30028
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12871
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	30093
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12999
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	30158
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13242
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	30223
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13477
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	30288
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13605
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	30353
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13705
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	30418
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13805
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	30483
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14013
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	30547
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14178
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	30611
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14361
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	30675
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14515
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	30740
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14879
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	30802
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15090
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	30864
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15342
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	30926
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15460
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	30990
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15571
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	31055
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15734
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	31121
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15897
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	31187
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16055
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	31255
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16220
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	31322
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16549
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	31390
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16770
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	31458
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16933
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	31524
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17205
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	31591
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17358
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	31660
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17514
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	31729
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17676
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	31798
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17819
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	31867
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17984
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	31936
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18129
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	32005
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18310
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	32073
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18484
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	32141
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18644
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	32209
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18788
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	32277
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19062
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	32342
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19201
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	32407
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19364
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	32473
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19582
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	32537
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19745
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	32598
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20081
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	32659
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20188
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	32719
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20640
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	32781
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20739
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	32841
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20889
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	32903
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21038
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	32971
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21199
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	33039
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21329
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	33107
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21461
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	33171
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21576
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	33236
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21687
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	33299
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21845
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	33360
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22257
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	33424
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22358
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	33485
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22625
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	33549
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22761
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	33616
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22872
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	33679
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23005
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	33740
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23208
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	33802
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23564
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	33867
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23742
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	33932
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23842
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	33997
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24212
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	34066
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24398
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	34135
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24596
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	34204
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24829
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	34269
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24981
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	34332
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25548
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	34397
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25842
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	34462
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26120
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	34527
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26616
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	34593
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27138
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	34662
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26929
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	34726
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27349
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	34791
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27781
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	34856
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27877
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	34921
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28137
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	34985
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28262
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	35051
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28459
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	35115
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28612
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	35180
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28765
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	35245
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28918
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	35310
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	632
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	915
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1146
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29173
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	35461
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29299
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	35528
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29551
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	35595
	.byte	14
	.word	1186
	.byte	22
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	35660
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	35461
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	35528
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	35595
	.byte	4,2,35,8,0,14
	.word	35689
	.byte	22
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	35750
	.byte	15,8
	.word	30926
	.byte	16,1,0,15,20
	.word	672
	.byte	16,19,0,15,8
	.word	34269
	.byte	16,1,0,14
	.word	35689
	.byte	15,24
	.word	1186
	.byte	16,1,0,14
	.word	35809
	.byte	15,16
	.word	672
	.byte	16,15,0,15,28
	.word	672
	.byte	16,27,0,15,40
	.word	672
	.byte	16,39,0,15,16
	.word	30740
	.byte	16,3,0,15,16
	.word	32719
	.byte	16,3,0,15,180,3
	.word	672
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4309
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	32659
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2490
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	33360
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	34204
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	33802
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	33867
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	33932
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	34135
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	33997
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	34066
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	29963
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	30028
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	32537
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	32473
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	30093
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	30158
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	30223
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	30288
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	34791
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2490
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	34662
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	29898
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	34985
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	34726
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2490
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	31524
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	35777
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	30990
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	35051
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	30353
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	30418
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	35786
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	33679
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	32841
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	33424
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	33299
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	32781
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	32277
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	31255
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	31055
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	31121
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	34921
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2490
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	34332
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	34527
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	34593
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	35795
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2490
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	30675
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	30547
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	34397
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	34462
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	35804
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	30864
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	35818
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4649
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	35310
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	35245
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	35115
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	35180
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2490
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	33107
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	33171
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	30483
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	33236
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4309
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	34856
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	35823
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	32903
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	32971
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	33039
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	35832
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	33616
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4309
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	32342
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	31187
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	32407
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	31458
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	31322
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2490
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	32005
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	32073
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	32141
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	32209
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	31591
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	31660
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	31729
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	31798
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	31867
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	31936
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	31390
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2490
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	33549
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	33485
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	35841
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	35850
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	30802
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	32598
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	33740
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	35859
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2490
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	30611
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	35868
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	29834
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	29770
	.byte	4,3,35,252,7,0,14
	.word	35879
	.byte	22
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	37869
	.byte	22
	.byte	'boolean',0,12,101,29
	.word	672
	.byte	22
	.byte	'uint8',0,12,105,29
	.word	672
	.byte	22
	.byte	'uint16',0,12,109,29
	.word	689
	.byte	22
	.byte	'uint32',0,12,113,29
	.word	9783
	.byte	22
	.byte	'uint64',0,12,118,29
	.word	379
	.byte	22
	.byte	'sint16',0,12,126,29
	.word	11271
	.byte	7
	.byte	'long int',0,4,5,22
	.byte	'sint32',0,12,131,1,29
	.word	37981
	.byte	7
	.byte	'long long int',0,8,5,22
	.byte	'sint64',0,12,138,1,29
	.word	38009
	.byte	22
	.byte	'float32',0,12,167,1,29
	.word	293
	.byte	22
	.byte	'pvoid',0,13,57,28
	.word	411
	.byte	22
	.byte	'Ifx_TickTime',0,13,79,28
	.word	38009
	.byte	17,13,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,22
	.byte	'Ifx_RxSel',0,13,140,1,3
	.word	38094
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,14,45,16,4,11
	.byte	'ADDR',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_A_Bits',0,14,48,3
	.word	38232
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,14,51,16,4,11
	.byte	'VSS',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	520
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BIV_Bits',0,14,55,3
	.word	38293
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,14,58,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	520
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BTV_Bits',0,14,62,3
	.word	38372
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,14,65,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT_Bits',0,14,69,3
	.word	38458
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,14,72,16,4,11
	.byte	'CM',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	520
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	520
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	520
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL_Bits',0,14,80,3
	.word	38547
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,14,83,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT_Bits',0,14,89,3
	.word	38693
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,14,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID_Bits',0,14,96,3
	.word	38820
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,14,99,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L_Bits',0,14,103,3
	.word	38918
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,14,106,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U_Bits',0,14,110,3
	.word	39011
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,14,113,16,4,11
	.byte	'MODREV',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	520
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID_Bits',0,14,118,3
	.word	39104
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,14,121,16,4,11
	.byte	'XE',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE_Bits',0,14,125,3
	.word	39211
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,14,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT_Bits',0,14,136,1,3
	.word	39298
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,14,139,1,16,4,11
	.byte	'CID',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID_Bits',0,14,143,1,3
	.word	39452
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,14,146,1,16,4,11
	.byte	'DATA',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_D_Bits',0,14,149,1,3
	.word	39546
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,14,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	520
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	520
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DATR_Bits',0,14,163,1,3
	.word	39609
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,14,166,1,16,4,11
	.byte	'DE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	520
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	520
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	520
	.byte	19,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR_Bits',0,14,177,1,3
	.word	39827
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,14,180,1,16,4,11
	.byte	'DTA',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR_Bits',0,14,184,1,3
	.word	40042
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,14,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0_Bits',0,14,192,1,3
	.word	40136
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,14,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2_Bits',0,14,199,1,3
	.word	40252
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,14,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	520
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCX_Bits',0,14,206,1,3
	.word	40353
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,14,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD_Bits',0,14,212,1,3
	.word	40446
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,14,215,1,16,4,11
	.byte	'TA',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR_Bits',0,14,218,1,3
	.word	40526
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,14,221,1,16,4,11
	.byte	'IED',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	520
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	520
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR_Bits',0,14,233,1,3
	.word	40595
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,14,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	520
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DMS_Bits',0,14,240,1,3
	.word	40824
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,14,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L_Bits',0,14,247,1,3
	.word	40917
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,14,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U_Bits',0,14,254,1,3
	.word	41012
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,14,129,2,16,4,11
	.byte	'RE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE_Bits',0,14,133,2,3
	.word	41107
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,14,136,2,16,4,11
	.byte	'WE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE_Bits',0,14,140,2,3
	.word	41197
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,14,143,2,16,4,11
	.byte	'SRE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	520
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	520
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	520
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	520
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	520
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	520
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	520
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	520
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR_Bits',0,14,161,2,3
	.word	41287
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,14,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT_Bits',0,14,172,2,3
	.word	41611
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,14,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FCX_Bits',0,14,180,2,3
	.word	41765
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,14,183,2,16,4,11
	.byte	'TST',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	520
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	520
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	520
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	520
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	520
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	520
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	520
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	520
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	520
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,14,202,2,3
	.word	41871
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,14,205,2,16,4,11
	.byte	'OPC',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	520
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,14,212,2,3
	.word	42220
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,14,215,2,16,4,11
	.byte	'PC',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,14,218,2,3
	.word	42380
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,14,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,14,224,2,3
	.word	42461
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,14,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,14,230,2,3
	.word	42548
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,14,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,14,236,2,3
	.word	42635
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,14,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT_Bits',0,14,243,2,3
	.word	42722
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,14,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	520
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	520
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	520
	.byte	6,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICR_Bits',0,14,253,2,3
	.word	42813
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,14,128,3,16,4,11
	.byte	'ISP',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_ISP_Bits',0,14,131,3,3
	.word	42956
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,14,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_LCX_Bits',0,14,139,3,3
	.word	43022
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,14,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT_Bits',0,14,146,3,3
	.word	43128
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,14,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT_Bits',0,14,153,3,3
	.word	43221
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,14,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT_Bits',0,14,160,3,3
	.word	43314
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,14,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	520
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_PC_Bits',0,14,167,3,3
	.word	43407
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,14,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0_Bits',0,14,175,3,3
	.word	43492
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,14,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1_Bits',0,14,183,3,3
	.word	43608
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,14,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2_Bits',0,14,190,3,3
	.word	43719
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,14,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	520
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	520
	.byte	10,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI_Bits',0,14,200,3,3
	.word	43820
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,14,203,3,16,4,11
	.byte	'TA',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR_Bits',0,14,206,3,3
	.word	43950
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,14,209,3,16,4,11
	.byte	'IED',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	520
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	520
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR_Bits',0,14,221,3,3
	.word	44019
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,14,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	520
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0_Bits',0,14,229,3,3
	.word	44248
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,14,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	520
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1_Bits',0,14,237,3,3
	.word	44361
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,14,240,3,16,4,11
	.byte	'PSI',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2_Bits',0,14,244,3,3
	.word	44474
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,14,247,3,16,4,11
	.byte	'FRE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	520
	.byte	17,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR_Bits',0,14,129,4,3
	.word	44565
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,14,132,4,16,4,11
	.byte	'CDC',0,4
	.word	520
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	520
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	520
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	520
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	520
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	520
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSW_Bits',0,14,147,4,3
	.word	44768
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,14,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	520
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	520
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN_Bits',0,14,156,4,3
	.word	45011
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,14,159,4,16,4,11
	.byte	'PC',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	520
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	520
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	520
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON_Bits',0,14,171,4,3
	.word	45139
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,14,174,4,16,4,11
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,14,177,4,3
	.word	45380
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,14,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,14,183,4,3
	.word	45463
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,14,186,4,16,4,11
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,14,189,4,3
	.word	45554
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,14,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,14,195,4,3
	.word	45645
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,14,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,14,202,4,3
	.word	45744
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,14,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,14,209,4,3
	.word	45851
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,14,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT_Bits',0,14,220,4,3
	.word	45958
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,14,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON_Bits',0,14,231,4,3
	.word	46112
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,14,234,4,16,4,11
	.byte	'ASI',0,4
	.word	520
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,14,238,4,3
	.word	46273
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,14,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	520
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	520
	.byte	15,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON_Bits',0,14,249,4,3
	.word	46371
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,14,252,4,16,4,11
	.byte	'Timer',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,14,255,4,3
	.word	46543
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,14,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	520
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR_Bits',0,14,133,5,3
	.word	46623
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,14,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	520
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	520
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	520
	.byte	3,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT_Bits',0,14,153,5,3
	.word	46696
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,14,156,5,16,4,11
	.byte	'T0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,14,167,5,3
	.word	47014
	.byte	12,14,175,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38232
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_A',0,14,180,5,3
	.word	47209
	.byte	12,14,183,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38293
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BIV',0,14,188,5,3
	.word	47268
	.byte	12,14,191,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38372
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BTV',0,14,196,5,3
	.word	47329
	.byte	12,14,199,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38458
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT',0,14,204,5,3
	.word	47390
	.byte	12,14,207,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38547
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL',0,14,212,5,3
	.word	47452
	.byte	12,14,215,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38693
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT',0,14,220,5,3
	.word	47515
	.byte	12,14,223,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38820
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID',0,14,228,5,3
	.word	47579
	.byte	12,14,231,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38918
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L',0,14,236,5,3
	.word	47644
	.byte	12,14,239,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39011
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U',0,14,244,5,3
	.word	47707
	.byte	12,14,247,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39104
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID',0,14,252,5,3
	.word	47770
	.byte	12,14,255,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39211
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE',0,14,132,6,3
	.word	47834
	.byte	12,14,135,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39298
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT',0,14,140,6,3
	.word	47896
	.byte	12,14,143,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39452
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID',0,14,148,6,3
	.word	47959
	.byte	12,14,151,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39546
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_D',0,14,156,6,3
	.word	48023
	.byte	12,14,159,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39609
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DATR',0,14,164,6,3
	.word	48082
	.byte	12,14,167,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39827
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR',0,14,172,6,3
	.word	48144
	.byte	12,14,175,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40042
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR',0,14,180,6,3
	.word	48207
	.byte	12,14,183,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40136
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0',0,14,188,6,3
	.word	48271
	.byte	12,14,191,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40252
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2',0,14,196,6,3
	.word	48334
	.byte	12,14,199,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40353
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCX',0,14,204,6,3
	.word	48397
	.byte	12,14,207,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40446
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD',0,14,212,6,3
	.word	48458
	.byte	12,14,215,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40526
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR',0,14,220,6,3
	.word	48521
	.byte	12,14,223,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40595
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR',0,14,228,6,3
	.word	48584
	.byte	12,14,231,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40824
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DMS',0,14,236,6,3
	.word	48647
	.byte	12,14,239,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40917
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L',0,14,244,6,3
	.word	48708
	.byte	12,14,247,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41012
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U',0,14,252,6,3
	.word	48771
	.byte	12,14,255,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41107
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE',0,14,132,7,3
	.word	48834
	.byte	12,14,135,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41197
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE',0,14,140,7,3
	.word	48896
	.byte	12,14,143,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41287
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR',0,14,148,7,3
	.word	48958
	.byte	12,14,151,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41611
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT',0,14,156,7,3
	.word	49020
	.byte	12,14,159,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41765
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FCX',0,14,164,7,3
	.word	49083
	.byte	12,14,167,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41871
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,14,172,7,3
	.word	49144
	.byte	12,14,175,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42220
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,14,180,7,3
	.word	49214
	.byte	12,14,183,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42380
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,14,188,7,3
	.word	49284
	.byte	12,14,191,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42461
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,14,196,7,3
	.word	49353
	.byte	12,14,199,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42548
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,14,204,7,3
	.word	49424
	.byte	12,14,207,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42635
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,14,212,7,3
	.word	49495
	.byte	12,14,215,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42722
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT',0,14,220,7,3
	.word	49566
	.byte	12,14,223,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42813
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICR',0,14,228,7,3
	.word	49628
	.byte	12,14,231,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42956
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ISP',0,14,236,7,3
	.word	49689
	.byte	12,14,239,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43022
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_LCX',0,14,244,7,3
	.word	49750
	.byte	12,14,247,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43128
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT',0,14,252,7,3
	.word	49811
	.byte	12,14,255,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43221
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT',0,14,132,8,3
	.word	49874
	.byte	12,14,135,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43314
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT',0,14,140,8,3
	.word	49937
	.byte	12,14,143,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43407
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PC',0,14,148,8,3
	.word	50000
	.byte	12,14,151,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43492
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0',0,14,156,8,3
	.word	50060
	.byte	12,14,159,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43608
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1',0,14,164,8,3
	.word	50123
	.byte	12,14,167,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43719
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2',0,14,172,8,3
	.word	50186
	.byte	12,14,175,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43820
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI',0,14,180,8,3
	.word	50249
	.byte	12,14,183,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43950
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR',0,14,188,8,3
	.word	50311
	.byte	12,14,191,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44019
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR',0,14,196,8,3
	.word	50374
	.byte	12,14,199,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44248
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0',0,14,204,8,3
	.word	50437
	.byte	12,14,207,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44361
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1',0,14,212,8,3
	.word	50499
	.byte	12,14,215,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44474
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2',0,14,220,8,3
	.word	50561
	.byte	12,14,223,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44565
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR',0,14,228,8,3
	.word	50623
	.byte	12,14,231,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44768
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSW',0,14,236,8,3
	.word	50685
	.byte	12,14,239,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45011
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN',0,14,244,8,3
	.word	50746
	.byte	12,14,247,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45139
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON',0,14,252,8,3
	.word	50809
	.byte	12,14,255,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45380
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA',0,14,132,9,3
	.word	50873
	.byte	12,14,135,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45463
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB',0,14,140,9,3
	.word	50943
	.byte	12,14,143,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45554
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,14,148,9,3
	.word	51013
	.byte	12,14,151,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45645
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,14,156,9,3
	.word	51087
	.byte	12,14,159,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45744
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,14,164,9,3
	.word	51161
	.byte	12,14,167,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45851
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,14,172,9,3
	.word	51231
	.byte	12,14,175,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45958
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT',0,14,180,9,3
	.word	51301
	.byte	12,14,183,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46112
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON',0,14,188,9,3
	.word	51364
	.byte	12,14,191,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46273
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI',0,14,196,9,3
	.word	51428
	.byte	12,14,199,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46371
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON',0,14,204,9,3
	.word	51494
	.byte	12,14,207,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46543
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER',0,14,212,9,3
	.word	51559
	.byte	12,14,215,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46623
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR',0,14,220,9,3
	.word	51626
	.byte	12,14,223,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46696
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT',0,14,228,9,3
	.word	51690
	.byte	12,14,231,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47014
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC',0,14,236,9,3
	.word	51754
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,14,247,9,25,8,13
	.byte	'L',0
	.word	47644
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	47707
	.byte	4,2,35,4,0,14
	.word	51820
	.byte	22
	.byte	'Ifx_CPU_CPR',0,14,251,9,3
	.word	51862
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,14,254,9,25,8,13
	.byte	'L',0
	.word	48708
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	48771
	.byte	4,2,35,4,0,14
	.word	51888
	.byte	22
	.byte	'Ifx_CPU_DPR',0,14,130,10,3
	.word	51930
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,14,133,10,25,16,13
	.byte	'LA',0
	.word	51161
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	51231
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	51013
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	51087
	.byte	4,2,35,12,0,14
	.word	51956
	.byte	22
	.byte	'Ifx_CPU_SPROT_RGN',0,14,139,10,3
	.word	52038
	.byte	15,12
	.word	51559
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,14,142,10,25,16,13
	.byte	'CON',0
	.word	51494
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	52070
	.byte	12,2,35,4,0,14
	.word	52079
	.byte	22
	.byte	'Ifx_CPU_TPS',0,14,146,10,3
	.word	52127
	.byte	10
	.byte	'_Ifx_CPU_TR',0,14,149,10,25,8,13
	.byte	'EVT',0
	.word	51690
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	51626
	.byte	4,2,35,4,0,14
	.word	52153
	.byte	22
	.byte	'Ifx_CPU_TR',0,14,153,10,3
	.word	52198
	.byte	15,176,32
	.word	672
	.byte	16,175,32,0,15,208,223,1
	.word	672
	.byte	16,207,223,1,0,15,248,1
	.word	672
	.byte	16,247,1,0,15,244,29
	.word	672
	.byte	16,243,29,0,15,188,3
	.word	672
	.byte	16,187,3,0,15,232,3
	.word	672
	.byte	16,231,3,0,15,252,23
	.word	672
	.byte	16,251,23,0,15,228,63
	.word	672
	.byte	16,227,63,0,15,128,1
	.word	51888
	.byte	16,15,0,14
	.word	52313
	.byte	15,128,31
	.word	672
	.byte	16,255,30,0,15,64
	.word	51820
	.byte	16,7,0,14
	.word	52339
	.byte	15,192,31
	.word	672
	.byte	16,191,31,0,15,16
	.word	47834
	.byte	16,3,0,15,16
	.word	48834
	.byte	16,3,0,15,16
	.word	48896
	.byte	16,3,0,15,208,7
	.word	672
	.byte	16,207,7,0,14
	.word	52079
	.byte	15,240,23
	.word	672
	.byte	16,239,23,0,15,64
	.word	52153
	.byte	16,7,0,14
	.word	52418
	.byte	15,192,23
	.word	672
	.byte	16,191,23,0,15,232,1
	.word	672
	.byte	16,231,1,0,15,180,1
	.word	672
	.byte	16,179,1,0,15,172,1
	.word	672
	.byte	16,171,1,0,15,64
	.word	48023
	.byte	16,15,0,15,64
	.word	672
	.byte	16,63,0,15,64
	.word	47209
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,14,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	52223
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	50746
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	52234
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	51428
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	52247
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	50437
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	50499
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	50561
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	52258
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	48334
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4309
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	50809
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	48958
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2490
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	48082
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	48458
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	48521
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	48584
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3680
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	48271
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	52269
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	50623
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	50123
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	50186
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	50060
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	50311
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	50374
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	52280
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	47515
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	52291
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	49144
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	49284
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	49214
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2490
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	49353
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	49424
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	49495
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	52302
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	52323
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	52328
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	52348
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	52353
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	52364
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	52373
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	52382
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	52391
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	52402
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	52407
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	52427
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	52432
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	47452
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	47390
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	49566
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	49811
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	49874
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	49937
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	52443
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	48144
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2490
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	49020
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	47896
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	51301
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	35832
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	51754
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4649
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	48647
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	48397
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	48207
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	52454
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	50249
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	50685
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	50000
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4309
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	51364
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	47770
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	47579
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	47268
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	47329
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	49689
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	49628
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4309
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	49083
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	49750
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	35823
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	47959
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	52465
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	52476
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	52485
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	52494
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	52485
	.byte	64,4,35,192,255,3,0,14
	.word	52503
	.byte	22
	.byte	'Ifx_CPU',0,14,130,11,3
	.word	54294
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,22
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	54316
	.byte	22
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9628
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,15,45,16,4,11
	.byte	'SRPN',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SRC_SRCR_Bits',0,15,62,3
	.word	54414
	.byte	12,15,70,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54414
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SRC_SRCR',0,15,75,3
	.word	54730
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,15,86,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	54790
	.byte	22
	.byte	'Ifx_SRC_AGBT',0,15,89,3
	.word	54822
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,15,92,25,12,13
	.byte	'TX',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54730
	.byte	4,2,35,8,0,14
	.word	54848
	.byte	22
	.byte	'Ifx_SRC_ASCLIN',0,15,97,3
	.word	54907
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,15,100,25,4,13
	.byte	'SBSRC',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	54935
	.byte	22
	.byte	'Ifx_SRC_BCUSPB',0,15,103,3
	.word	54972
	.byte	15,64
	.word	54730
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,15,106,25,64,13
	.byte	'INT',0
	.word	55000
	.byte	64,2,35,0,0,14
	.word	55009
	.byte	22
	.byte	'Ifx_SRC_CAN',0,15,109,3
	.word	55041
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,15,112,25,16,13
	.byte	'SR0',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54730
	.byte	4,2,35,12,0,14
	.word	55066
	.byte	22
	.byte	'Ifx_SRC_CCU6',0,15,118,3
	.word	55138
	.byte	15,8
	.word	54730
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,15,121,25,8,13
	.byte	'SR',0
	.word	55164
	.byte	8,2,35,0,0,14
	.word	55173
	.byte	22
	.byte	'Ifx_SRC_CERBERUS',0,15,124,3
	.word	55209
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,15,127,25,16,13
	.byte	'MI',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	54730
	.byte	4,2,35,12,0,14
	.word	55239
	.byte	22
	.byte	'Ifx_SRC_CIF',0,15,133,1,3
	.word	55312
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,15,136,1,25,4,13
	.byte	'SBSRC',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	55338
	.byte	22
	.byte	'Ifx_SRC_CPU',0,15,139,1,3
	.word	55373
	.byte	15,192,1
	.word	54730
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,15,142,1,25,208,1,13
	.byte	'ERR',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4649
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	55399
	.byte	192,1,2,35,16,0,14
	.word	55409
	.byte	22
	.byte	'Ifx_SRC_DMA',0,15,147,1,3
	.word	55476
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,15,150,1,25,8,13
	.byte	'SRM',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	54730
	.byte	4,2,35,4,0,14
	.word	55502
	.byte	22
	.byte	'Ifx_SRC_DSADC',0,15,154,1,3
	.word	55550
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,15,157,1,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	55578
	.byte	22
	.byte	'Ifx_SRC_EMEM',0,15,160,1,3
	.word	55611
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,15,163,1,25,80,13
	.byte	'INT',0
	.word	55164
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	55164
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	55164
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	55164
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	54730
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	54730
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	35841
	.byte	40,2,35,40,0,14
	.word	55638
	.byte	22
	.byte	'Ifx_SRC_ERAY',0,15,172,1,3
	.word	55765
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,15,175,1,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	55792
	.byte	22
	.byte	'Ifx_SRC_ETH',0,15,178,1,3
	.word	55824
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,15,181,1,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	55850
	.byte	22
	.byte	'Ifx_SRC_FCE',0,15,184,1,3
	.word	55882
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,15,187,1,25,12,13
	.byte	'DONE',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	54730
	.byte	4,2,35,8,0,14
	.word	55908
	.byte	22
	.byte	'Ifx_SRC_FFT',0,15,192,1,3
	.word	55968
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,15,195,1,25,32,13
	.byte	'SR0',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54730
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	35823
	.byte	16,2,35,16,0,14
	.word	55994
	.byte	22
	.byte	'Ifx_SRC_GPSR',0,15,202,1,3
	.word	56088
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,15,205,1,25,48,13
	.byte	'CIRQ',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	54730
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	54730
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	54730
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3680
	.byte	24,2,35,24,0,14
	.word	56115
	.byte	22
	.byte	'Ifx_SRC_GPT12',0,15,214,1,3
	.word	56232
	.byte	15,12
	.word	54730
	.byte	16,2,0,15,32
	.word	54730
	.byte	16,7,0,15,32
	.word	56269
	.byte	16,0,0,15,88
	.word	672
	.byte	16,87,0,15,108
	.word	54730
	.byte	16,26,0,15,96
	.word	672
	.byte	16,95,0,15,96
	.word	56269
	.byte	16,2,0,15,160,3
	.word	672
	.byte	16,159,3,0,15,64
	.word	56269
	.byte	16,1,0,15,192,3
	.word	672
	.byte	16,191,3,0,15,16
	.word	54730
	.byte	16,3,0,15,64
	.word	56354
	.byte	16,3,0,15,192,2
	.word	672
	.byte	16,191,2,0,15,52
	.word	672
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,15,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	56260
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2490
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	54730
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	54730
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	55164
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4309
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	56278
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	56287
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	56296
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	56305
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	54730
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4649
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	56314
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	56323
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	56314
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	56323
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	56334
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	56343
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	56363
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	56372
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	56260
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	56383
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	56260
	.byte	12,3,35,192,18,0,14
	.word	56392
	.byte	22
	.byte	'Ifx_SRC_GTM',0,15,243,1,3
	.word	56852
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,15,246,1,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	56878
	.byte	22
	.byte	'Ifx_SRC_HSCT',0,15,249,1,3
	.word	56911
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,15,252,1,25,16,13
	.byte	'COK',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	54730
	.byte	4,2,35,12,0,14
	.word	56938
	.byte	22
	.byte	'Ifx_SRC_HSSL',0,15,130,2,3
	.word	57011
	.byte	15,56
	.word	672
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,15,133,2,25,80,13
	.byte	'BREQ',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	54730
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	54730
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	54730
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	57038
	.byte	56,2,35,24,0,14
	.word	57047
	.byte	22
	.byte	'Ifx_SRC_I2C',0,15,142,2,3
	.word	57170
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,15,145,2,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	57196
	.byte	22
	.byte	'Ifx_SRC_LMU',0,15,148,2,3
	.word	57228
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,15,151,2,25,20,13
	.byte	'SR0',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54730
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	54730
	.byte	4,2,35,16,0,14
	.word	57254
	.byte	22
	.byte	'Ifx_SRC_MSC',0,15,158,2,3
	.word	57339
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,15,161,2,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	57365
	.byte	22
	.byte	'Ifx_SRC_PMU',0,15,164,2,3
	.word	57397
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,15,167,2,25,32,13
	.byte	'SR',0
	.word	56269
	.byte	32,2,35,0,0,14
	.word	57423
	.byte	22
	.byte	'Ifx_SRC_PSI5',0,15,170,2,3
	.word	57456
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,15,173,2,25,32,13
	.byte	'SR',0
	.word	56269
	.byte	32,2,35,0,0,14
	.word	57483
	.byte	22
	.byte	'Ifx_SRC_PSI5S',0,15,176,2,3
	.word	57517
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,15,179,2,25,24,13
	.byte	'TX',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	54730
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	54730
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	54730
	.byte	4,2,35,20,0,14
	.word	57545
	.byte	22
	.byte	'Ifx_SRC_QSPI',0,15,187,2,3
	.word	57638
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,15,190,2,25,4,13
	.byte	'SR',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	57665
	.byte	22
	.byte	'Ifx_SRC_SCR',0,15,193,2,3
	.word	57697
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,15,196,2,25,20,13
	.byte	'DTS',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	56354
	.byte	16,2,35,4,0,14
	.word	57723
	.byte	22
	.byte	'Ifx_SRC_SCU',0,15,200,2,3
	.word	57769
	.byte	15,24
	.word	54730
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,15,203,2,25,24,13
	.byte	'SR',0
	.word	57795
	.byte	24,2,35,0,0,14
	.word	57804
	.byte	22
	.byte	'Ifx_SRC_SENT',0,15,206,2,3
	.word	57837
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,15,209,2,25,12,13
	.byte	'SR',0
	.word	56260
	.byte	12,2,35,0,0,14
	.word	57864
	.byte	22
	.byte	'Ifx_SRC_SMU',0,15,212,2,3
	.word	57896
	.byte	10
	.byte	'_Ifx_SRC_STM',0,15,215,2,25,8,13
	.byte	'SR0',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54730
	.byte	4,2,35,4,0,14
	.word	57922
	.byte	22
	.byte	'Ifx_SRC_STM',0,15,219,2,3
	.word	57968
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,15,222,2,25,16,13
	.byte	'SR0',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54730
	.byte	4,2,35,12,0,14
	.word	57994
	.byte	22
	.byte	'Ifx_SRC_VADCCG',0,15,228,2,3
	.word	58069
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,15,231,2,25,16,13
	.byte	'SR0',0
	.word	54730
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54730
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54730
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54730
	.byte	4,2,35,12,0,14
	.word	58098
	.byte	22
	.byte	'Ifx_SRC_VADCG',0,15,237,2,3
	.word	58172
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,15,240,2,25,4,13
	.byte	'SRC',0
	.word	54730
	.byte	4,2,35,0,0,14
	.word	58200
	.byte	22
	.byte	'Ifx_SRC_XBAR',0,15,243,2,3
	.word	58234
	.byte	15,4
	.word	54790
	.byte	16,0,0,14
	.word	58261
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,15,128,3,25,4,13
	.byte	'AGBT',0
	.word	58270
	.byte	4,2,35,0,0,14
	.word	58275
	.byte	22
	.byte	'Ifx_SRC_GAGBT',0,15,131,3,3
	.word	58311
	.byte	15,48
	.word	54848
	.byte	16,3,0,14
	.word	58339
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,15,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	58348
	.byte	48,2,35,0,0,14
	.word	58353
	.byte	22
	.byte	'Ifx_SRC_GASCLIN',0,15,137,3,3
	.word	58393
	.byte	14
	.word	54935
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,15,140,3,25,4,13
	.byte	'SPB',0
	.word	58423
	.byte	4,2,35,0,0,14
	.word	58428
	.byte	22
	.byte	'Ifx_SRC_GBCU',0,15,143,3,3
	.word	58462
	.byte	15,64
	.word	55009
	.byte	16,0,0,14
	.word	58489
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,15,146,3,25,64,13
	.byte	'CAN',0
	.word	58498
	.byte	64,2,35,0,0,14
	.word	58503
	.byte	22
	.byte	'Ifx_SRC_GCAN',0,15,149,3,3
	.word	58537
	.byte	15,32
	.word	55066
	.byte	16,1,0,14
	.word	58564
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,15,152,3,25,32,13
	.byte	'CCU6',0
	.word	58573
	.byte	32,2,35,0,0,14
	.word	58578
	.byte	22
	.byte	'Ifx_SRC_GCCU6',0,15,155,3,3
	.word	58614
	.byte	14
	.word	55173
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,15,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	58642
	.byte	8,2,35,0,0,14
	.word	58647
	.byte	22
	.byte	'Ifx_SRC_GCERBERUS',0,15,161,3,3
	.word	58691
	.byte	15,16
	.word	55239
	.byte	16,0,0,14
	.word	58723
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,15,164,3,25,16,13
	.byte	'CIF',0
	.word	58732
	.byte	16,2,35,0,0,14
	.word	58737
	.byte	22
	.byte	'Ifx_SRC_GCIF',0,15,167,3,3
	.word	58771
	.byte	15,8
	.word	55338
	.byte	16,1,0,14
	.word	58798
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,15,170,3,25,8,13
	.byte	'CPU',0
	.word	58807
	.byte	8,2,35,0,0,14
	.word	58812
	.byte	22
	.byte	'Ifx_SRC_GCPU',0,15,173,3,3
	.word	58846
	.byte	15,208,1
	.word	55409
	.byte	16,0,0,14
	.word	58873
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,15,176,3,25,208,1,13
	.byte	'DMA',0
	.word	58883
	.byte	208,1,2,35,0,0,14
	.word	58888
	.byte	22
	.byte	'Ifx_SRC_GDMA',0,15,179,3,3
	.word	58924
	.byte	14
	.word	55502
	.byte	14
	.word	55502
	.byte	14
	.word	55502
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,15,182,3,25,32,13
	.byte	'DSADC0',0
	.word	58951
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4309
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	58956
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	58961
	.byte	8,2,35,24,0,14
	.word	58966
	.byte	22
	.byte	'Ifx_SRC_GDSADC',0,15,188,3,3
	.word	59057
	.byte	15,4
	.word	55578
	.byte	16,0,0,14
	.word	59086
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,15,191,3,25,4,13
	.byte	'EMEM',0
	.word	59095
	.byte	4,2,35,0,0,14
	.word	59100
	.byte	22
	.byte	'Ifx_SRC_GEMEM',0,15,194,3,3
	.word	59136
	.byte	15,80
	.word	55638
	.byte	16,0,0,14
	.word	59164
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,15,197,3,25,80,13
	.byte	'ERAY',0
	.word	59173
	.byte	80,2,35,0,0,14
	.word	59178
	.byte	22
	.byte	'Ifx_SRC_GERAY',0,15,200,3,3
	.word	59214
	.byte	15,4
	.word	55792
	.byte	16,0,0,14
	.word	59242
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,15,203,3,25,4,13
	.byte	'ETH',0
	.word	59251
	.byte	4,2,35,0,0,14
	.word	59256
	.byte	22
	.byte	'Ifx_SRC_GETH',0,15,206,3,3
	.word	59290
	.byte	15,4
	.word	55850
	.byte	16,0,0,14
	.word	59317
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,15,209,3,25,4,13
	.byte	'FCE',0
	.word	59326
	.byte	4,2,35,0,0,14
	.word	59331
	.byte	22
	.byte	'Ifx_SRC_GFCE',0,15,212,3,3
	.word	59365
	.byte	15,12
	.word	55908
	.byte	16,0,0,14
	.word	59392
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,15,215,3,25,12,13
	.byte	'FFT',0
	.word	59401
	.byte	12,2,35,0,0,14
	.word	59406
	.byte	22
	.byte	'Ifx_SRC_GFFT',0,15,218,3,3
	.word	59440
	.byte	15,64
	.word	55994
	.byte	16,1,0,14
	.word	59467
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,15,221,3,25,64,13
	.byte	'GPSR',0
	.word	59476
	.byte	64,2,35,0,0,14
	.word	59481
	.byte	22
	.byte	'Ifx_SRC_GGPSR',0,15,224,3,3
	.word	59517
	.byte	15,48
	.word	56115
	.byte	16,0,0,14
	.word	59545
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,15,227,3,25,48,13
	.byte	'GPT12',0
	.word	59554
	.byte	48,2,35,0,0,14
	.word	59559
	.byte	22
	.byte	'Ifx_SRC_GGPT12',0,15,230,3,3
	.word	59597
	.byte	15,204,18
	.word	56392
	.byte	16,0,0,14
	.word	59626
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,15,233,3,25,204,18,13
	.byte	'GTM',0
	.word	59636
	.byte	204,18,2,35,0,0,14
	.word	59641
	.byte	22
	.byte	'Ifx_SRC_GGTM',0,15,236,3,3
	.word	59677
	.byte	15,4
	.word	56878
	.byte	16,0,0,14
	.word	59704
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,15,239,3,25,4,13
	.byte	'HSCT',0
	.word	59713
	.byte	4,2,35,0,0,14
	.word	59718
	.byte	22
	.byte	'Ifx_SRC_GHSCT',0,15,242,3,3
	.word	59754
	.byte	15,64
	.word	56938
	.byte	16,3,0,14
	.word	59782
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,15,245,3,25,68,13
	.byte	'HSSL',0
	.word	59791
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	54730
	.byte	4,2,35,64,0,14
	.word	59796
	.byte	22
	.byte	'Ifx_SRC_GHSSL',0,15,249,3,3
	.word	59845
	.byte	15,80
	.word	57047
	.byte	16,0,0,14
	.word	59873
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,15,252,3,25,80,13
	.byte	'I2C',0
	.word	59882
	.byte	80,2,35,0,0,14
	.word	59887
	.byte	22
	.byte	'Ifx_SRC_GI2C',0,15,255,3,3
	.word	59921
	.byte	15,4
	.word	57196
	.byte	16,0,0,14
	.word	59948
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,15,130,4,25,4,13
	.byte	'LMU',0
	.word	59957
	.byte	4,2,35,0,0,14
	.word	59962
	.byte	22
	.byte	'Ifx_SRC_GLMU',0,15,133,4,3
	.word	59996
	.byte	15,40
	.word	57254
	.byte	16,1,0,14
	.word	60023
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,15,136,4,25,40,13
	.byte	'MSC',0
	.word	60032
	.byte	40,2,35,0,0,14
	.word	60037
	.byte	22
	.byte	'Ifx_SRC_GMSC',0,15,139,4,3
	.word	60071
	.byte	15,8
	.word	57365
	.byte	16,1,0,14
	.word	60098
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,15,142,4,25,8,13
	.byte	'PMU',0
	.word	60107
	.byte	8,2,35,0,0,14
	.word	60112
	.byte	22
	.byte	'Ifx_SRC_GPMU',0,15,145,4,3
	.word	60146
	.byte	15,32
	.word	57423
	.byte	16,0,0,14
	.word	60173
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,15,148,4,25,32,13
	.byte	'PSI5',0
	.word	60182
	.byte	32,2,35,0,0,14
	.word	60187
	.byte	22
	.byte	'Ifx_SRC_GPSI5',0,15,151,4,3
	.word	60223
	.byte	15,32
	.word	57483
	.byte	16,0,0,14
	.word	60251
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,15,154,4,25,32,13
	.byte	'PSI5S',0
	.word	60260
	.byte	32,2,35,0,0,14
	.word	60265
	.byte	22
	.byte	'Ifx_SRC_GPSI5S',0,15,157,4,3
	.word	60303
	.byte	15,96
	.word	57545
	.byte	16,3,0,14
	.word	60332
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,15,160,4,25,96,13
	.byte	'QSPI',0
	.word	60341
	.byte	96,2,35,0,0,14
	.word	60346
	.byte	22
	.byte	'Ifx_SRC_GQSPI',0,15,163,4,3
	.word	60382
	.byte	15,4
	.word	57665
	.byte	16,0,0,14
	.word	60410
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,15,166,4,25,4,13
	.byte	'SCR',0
	.word	60419
	.byte	4,2,35,0,0,14
	.word	60424
	.byte	22
	.byte	'Ifx_SRC_GSCR',0,15,169,4,3
	.word	60458
	.byte	14
	.word	57723
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,15,172,4,25,20,13
	.byte	'SCU',0
	.word	60485
	.byte	20,2,35,0,0,14
	.word	60490
	.byte	22
	.byte	'Ifx_SRC_GSCU',0,15,175,4,3
	.word	60524
	.byte	15,24
	.word	57804
	.byte	16,0,0,14
	.word	60551
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,15,178,4,25,24,13
	.byte	'SENT',0
	.word	60560
	.byte	24,2,35,0,0,14
	.word	60565
	.byte	22
	.byte	'Ifx_SRC_GSENT',0,15,181,4,3
	.word	60601
	.byte	15,12
	.word	57864
	.byte	16,0,0,14
	.word	60629
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,15,184,4,25,12,13
	.byte	'SMU',0
	.word	60638
	.byte	12,2,35,0,0,14
	.word	60643
	.byte	22
	.byte	'Ifx_SRC_GSMU',0,15,187,4,3
	.word	60677
	.byte	15,16
	.word	57922
	.byte	16,1,0,14
	.word	60704
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,15,190,4,25,16,13
	.byte	'STM',0
	.word	60713
	.byte	16,2,35,0,0,14
	.word	60718
	.byte	22
	.byte	'Ifx_SRC_GSTM',0,15,193,4,3
	.word	60752
	.byte	15,64
	.word	58098
	.byte	16,3,0,14
	.word	60779
	.byte	15,224,1
	.word	672
	.byte	16,223,1,0,15,32
	.word	57994
	.byte	16,1,0,14
	.word	60804
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,15,196,4,25,192,2,13
	.byte	'G',0
	.word	60788
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	60793
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	60813
	.byte	32,3,35,160,2,0,14
	.word	60818
	.byte	22
	.byte	'Ifx_SRC_GVADC',0,15,201,4,3
	.word	60887
	.byte	14
	.word	58200
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,15,204,4,25,4,13
	.byte	'XBAR',0
	.word	60915
	.byte	4,2,35,0,0,14
	.word	60920
	.byte	22
	.byte	'Ifx_SRC_GXBAR',0,15,207,4,3
	.word	60956
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	60984
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	61541
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	61618
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	61690
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	61766
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	672
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	672
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	61907
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	62125
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	497
	.byte	25,0,2,35,0,0,22
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	62192
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	62395
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	62502
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	62653
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	62764
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	62856
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	62952
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	63098
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	63170
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	63246
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	63318
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	63390
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	63463
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	63536
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	63609
	.byte	12,16,245,1,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60984
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	63682
	.byte	12,16,253,1,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61541
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	63746
	.byte	12,16,133,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61618
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	63810
	.byte	12,16,141,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61690
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	63871
	.byte	12,16,149,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61766
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	63934
	.byte	12,16,157,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61907
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	63995
	.byte	12,16,165,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62125
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	64058
	.byte	12,16,173,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62192
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	64119
	.byte	12,16,181,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62395
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	64180
	.byte	12,16,189,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62502
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	64240
	.byte	12,16,197,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62653
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	64302
	.byte	12,16,205,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62764
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	64365
	.byte	12,16,213,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62856
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	64428
	.byte	12,16,221,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62952
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	64493
	.byte	12,16,229,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63098
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	64554
	.byte	12,16,237,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63170
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	64616
	.byte	12,16,245,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63246
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	64680
	.byte	12,16,253,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63318
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	64742
	.byte	12,16,133,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63390
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	64804
	.byte	12,16,141,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63463
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	64866
	.byte	12,16,149,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63536
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	64928
	.byte	12,16,157,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63609
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	64990
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,17,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,17,79,3
	.word	65052
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,17,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,17,85,3
	.word	65613
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,17,88,16,4,11
	.byte	'SEL',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,17,95,3
	.word	65694
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,17,98,16,4,11
	.byte	'VLD0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,17,111,3
	.word	65847
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,17,114,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,17,121,3
	.word	66095
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,17,124,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0_Bits',0,17,128,1,3
	.word	66241
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,17,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM1_Bits',0,17,136,1,3
	.word	66339
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,17,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM2_Bits',0,17,144,1,3
	.word	66455
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,17,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRD_Bits',0,17,153,1,3
	.word	66571
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,17,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRP_Bits',0,17,162,1,3
	.word	66711
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,17,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCW_Bits',0,17,171,1,3
	.word	66851
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,17,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	689
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FCON_Bits',0,17,193,1,3
	.word	66990
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,17,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FPRO_Bits',0,17,218,1,3
	.word	67352
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,17,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FSR_Bits',0,17,254,1,3
	.word	67793
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,17,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_ID_Bits',0,17,134,2,3
	.word	68399
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,17,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	689
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARD_Bits',0,17,147,2,3
	.word	68510
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,17,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	689
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARP_Bits',0,17,159,2,3
	.word	68724
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,17,162,2,16,4,11
	.byte	'L',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	672
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	689
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCOND_Bits',0,17,179,2,3
	.word	68911
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,17,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,17,188,2,3
	.word	69235
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,17,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	689
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,17,199,2,3
	.word	69378
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,17,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	672
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,17,219,2,3
	.word	69567
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,17,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	672
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,17,254,2,3
	.word	69930
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,17,129,3,16,4,11
	.byte	'S0L',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONP_Bits',0,17,160,3,3
	.word	70525
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,17,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,17,194,3,3
	.word	71049
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,17,197,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,17,201,3,3
	.word	71631
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,17,204,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,17,208,3,3
	.word	71733
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,17,211,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,17,215,3,3
	.word	71835
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,17,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	497
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD_Bits',0,17,222,3,3
	.word	71937
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,17,225,3,16,4,11
	.byte	'STRT',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	689
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_RRCT_Bits',0,17,236,3,3
	.word	72031
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,17,239,3,16,4,11
	.byte	'DATA',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0_Bits',0,17,242,3,3
	.word	72241
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,17,245,3,16,4,11
	.byte	'DATA',0,4
	.word	497
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1_Bits',0,17,248,3,3
	.word	72314
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,17,251,3,16,4,11
	.byte	'SEL',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,17,130,4,3
	.word	72387
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,17,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,17,137,4,3
	.word	72542
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,17,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,17,147,4,3
	.word	72647
	.byte	12,17,155,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65052
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN0',0,17,160,4,3
	.word	72795
	.byte	12,17,163,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65613
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1',0,17,168,4,3
	.word	72861
	.byte	12,17,171,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65694
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG',0,17,176,4,3
	.word	72927
	.byte	12,17,179,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65847
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT',0,17,184,4,3
	.word	72995
	.byte	12,17,187,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66095
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_TOP',0,17,192,4,3
	.word	73064
	.byte	12,17,195,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66241
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0',0,17,200,4,3
	.word	73132
	.byte	12,17,203,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66339
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM1',0,17,208,4,3
	.word	73197
	.byte	12,17,211,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66455
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM2',0,17,216,4,3
	.word	73262
	.byte	12,17,219,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66571
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRD',0,17,224,4,3
	.word	73327
	.byte	12,17,227,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66711
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRP',0,17,232,4,3
	.word	73392
	.byte	12,17,235,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66851
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCW',0,17,240,4,3
	.word	73457
	.byte	12,17,243,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66990
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FCON',0,17,248,4,3
	.word	73521
	.byte	12,17,251,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67352
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FPRO',0,17,128,5,3
	.word	73585
	.byte	12,17,131,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67793
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FSR',0,17,136,5,3
	.word	73649
	.byte	12,17,139,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68399
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ID',0,17,144,5,3
	.word	73712
	.byte	12,17,147,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68510
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARD',0,17,152,5,3
	.word	73774
	.byte	12,17,155,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68724
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARP',0,17,160,5,3
	.word	73838
	.byte	12,17,163,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68911
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCOND',0,17,168,5,3
	.word	73902
	.byte	12,17,171,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69235
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG',0,17,176,5,3
	.word	73969
	.byte	12,17,179,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69378
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSM',0,17,184,5,3
	.word	74038
	.byte	12,17,187,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69567
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,17,192,5,3
	.word	74107
	.byte	12,17,195,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69930
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONOTP',0,17,200,5,3
	.word	74180
	.byte	12,17,203,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70525
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONP',0,17,208,5,3
	.word	74249
	.byte	12,17,211,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71049
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONWOP',0,17,216,5,3
	.word	74316
	.byte	12,17,219,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71631
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0',0,17,224,5,3
	.word	74385
	.byte	12,17,227,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71733
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1',0,17,232,5,3
	.word	74453
	.byte	12,17,235,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71835
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2',0,17,240,5,3
	.word	74521
	.byte	12,17,243,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71937
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD',0,17,248,5,3
	.word	74589
	.byte	12,17,251,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72031
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRCT',0,17,128,6,3
	.word	74653
	.byte	12,17,131,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72241
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0',0,17,136,6,3
	.word	74717
	.byte	12,17,139,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72314
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1',0,17,144,6,3
	.word	74781
	.byte	12,17,147,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72387
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG',0,17,152,6,3
	.word	74845
	.byte	12,17,155,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72542
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT',0,17,160,6,3
	.word	74913
	.byte	12,17,163,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72647
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_TOP',0,17,168,6,3
	.word	74982
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,17,179,6,25,12,13
	.byte	'CFG',0
	.word	72927
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	72995
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73064
	.byte	4,2,35,8,0,14
	.word	75050
	.byte	22
	.byte	'Ifx_FLASH_CBAB',0,17,184,6,3
	.word	75113
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,17,187,6,25,12,13
	.byte	'CFG0',0
	.word	74385
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	74453
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	74521
	.byte	4,2,35,8,0,14
	.word	75142
	.byte	22
	.byte	'Ifx_FLASH_RDB',0,17,192,6,3
	.word	75206
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,17,195,6,25,12,13
	.byte	'CFG',0
	.word	74845
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	74913
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	74982
	.byte	4,2,35,8,0,14
	.word	75234
	.byte	22
	.byte	'Ifx_FLASH_UBAB',0,17,200,6,3
	.word	75297
	.byte	22
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8062
	.byte	22
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7975
	.byte	22
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4318
	.byte	22
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2371
	.byte	22
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3366
	.byte	22
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2499
	.byte	22
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3146
	.byte	22
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2714
	.byte	22
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2929
	.byte	22
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7334
	.byte	22
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7458
	.byte	22
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7542
	.byte	22
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7722
	.byte	22
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5973
	.byte	22
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6497
	.byte	22
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6147
	.byte	22
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6321
	.byte	22
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6986
	.byte	22
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1800
	.byte	22
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5310
	.byte	22
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5798
	.byte	22
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5457
	.byte	22
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5626
	.byte	22
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6653
	.byte	22
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1484
	.byte	22
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5024
	.byte	22
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4658
	.byte	22
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3689
	.byte	22
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3993
	.byte	22
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8589
	.byte	22
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8022
	.byte	22
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4609
	.byte	22
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2450
	.byte	22
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3640
	.byte	22
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2674
	.byte	22
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3326
	.byte	22
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2889
	.byte	22
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3106
	.byte	22
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7418
	.byte	22
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7667
	.byte	22
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7926
	.byte	22
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7294
	.byte	22
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6107
	.byte	22
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6613
	.byte	22
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6281
	.byte	22
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6457
	.byte	22
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2331
	.byte	22
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6946
	.byte	22
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5417
	.byte	22
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5933
	.byte	22
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5586
	.byte	22
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5758
	.byte	22
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1760
	.byte	22
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5270
	.byte	22
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4984
	.byte	22
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3953
	.byte	22
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4269
	.byte	14
	.word	8629
	.byte	22
	.byte	'Ifx_P',0,6,139,6,3
	.word	76644
	.byte	22
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9242
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,22
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	76690
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,22
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	76934
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,22
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	77032
	.byte	22
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9447
	.byte	24,5,190,1,9,8,13
	.byte	'port',0
	.word	9237
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	672
	.byte	1,2,35,4,0,22
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	77497
	.byte	14
	.word	35879
	.byte	3
	.word	77557
	.byte	24,18,74,15,20,13
	.byte	'module',0
	.word	77562
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	672
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	77497
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	38094
	.byte	1,2,35,16,0,25
	.word	77567
	.byte	22
	.byte	'IfxScu_Req_In',0,18,80,3
	.word	77637
	.byte	22
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,19,148,1,16
	.word	236
	.byte	24,19,212,5,9,8,13
	.byte	'value',0
	.word	9783
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9783
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_CcuconRegConfig',0,19,216,5,3
	.word	77704
	.byte	24,19,221,5,9,8,13
	.byte	'pDivider',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	672
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	672
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	293
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_InitialStepConfig',0,19,227,5,3
	.word	77775
	.byte	24,19,231,5,9,12,13
	.byte	'k2Step',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	293
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	77664
	.byte	4,2,35,8,0,22
	.byte	'IfxScuCcu_PllStepsConfig',0,19,236,5,3
	.word	77892
	.byte	3
	.word	233
	.byte	24,19,244,5,9,48,13
	.byte	'ccucon0',0
	.word	77704
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	77704
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	77704
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	77704
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	77704
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	77704
	.byte	8,2,35,40,0,22
	.byte	'IfxScuCcu_ClockDistributionConfig',0,19,252,5,3
	.word	77994
	.byte	24,19,128,6,9,8,13
	.byte	'value',0
	.word	9783
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9783
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,19,132,6,3
	.word	78146
	.byte	3
	.word	77892
	.byte	24,19,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	78222
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	77775
	.byte	8,2,35,8,0,22
	.byte	'IfxScuCcu_SysPllConfig',0,19,142,6,3
	.word	78227
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,22
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	78344
	.byte	24,7,160,1,9,6,13
	.byte	'counter',0
	.word	9783
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	672
	.byte	1,2,35,4,0,22
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	78433
	.byte	24,7,172,1,9,32,13
	.byte	'instruction',0
	.word	78433
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	78433
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	78433
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	78433
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	78433
	.byte	6,2,35,24,0,22
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	78499
	.byte	22
	.byte	'IfxScuEru_ExternalInputSelection',0,9,88,3
	.word	10443
	.byte	22
	.byte	'IfxScuEru_InputChannel',0,9,102,3
	.word	10221
	.byte	22
	.byte	'IfxScuEru_InputNodePointer',0,9,117,3
	.word	10597
	.byte	22
	.byte	'IfxScuEru_InterruptGatingPattern',0,9,128,1,3
	.word	11082
	.byte	22
	.byte	'IfxScuEru_OutputChannel',0,9,142,1,3
	.word	10851
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L100:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,49,19,0,0,21,5,0,49,19,0,0,22,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,23,21,0,54,15,0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,25,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L101:
	.word	.L357-.L356
.L356:
	.half	3
	.word	.L359-.L358
.L358:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuEru.h',0,0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L359:
.L357:
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_selectExternalInput')
	.sect	'.debug_info'
.L102:
	.word	370
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L105,.L104
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_selectExternalInput',0,1,228,2,6,1,1,1
	.word	.L91,.L227,.L90
	.byte	4
	.byte	'inputChannel',0,1,228,2,59
	.word	.L228,.L229
	.byte	4
	.byte	'inputSignal',0,1,228,2,106
	.word	.L230,.L231
	.byte	5
	.word	.L91,.L227
	.byte	6
	.byte	'index',0,1,231,2,12
	.word	.L232,.L233
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_selectExternalInput')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_selectExternalInput')
	.sect	'.debug_line'
.L104:
	.word	.L361-.L360
.L360:
	.half	3
	.word	.L363-.L362
.L362:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L363:
	.byte	5,34,7,0,5,2
	.word	.L91
	.byte	3,230,2,1,5,5,9
	.half	.L346-.L91
	.byte	3,2,1,5,19,7,9
	.half	.L364-.L346
	.byte	3,2,1,5,24,9
	.half	.L365-.L364
	.byte	1,5,19,9
	.half	.L366-.L365
	.byte	1,5,24,9
	.half	.L367-.L366
	.byte	1,5,33,9
	.half	.L368-.L367
	.byte	1,5,40,9
	.half	.L369-.L368
	.byte	1,5,53,9
	.half	.L370-.L369
	.byte	1,5,19,9
	.half	.L42-.L370
	.byte	3,4,1,5,24,9
	.half	.L371-.L42
	.byte	1,5,19,9
	.half	.L372-.L371
	.byte	1,5,24,9
	.half	.L373-.L372
	.byte	1,5,33,9
	.half	.L374-.L373
	.byte	1,5,40,9
	.half	.L375-.L374
	.byte	1,5,1,9
	.half	.L43-.L375
	.byte	3,2,1,7,9
	.half	.L106-.L43
	.byte	0,1,1
.L361:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_selectExternalInput')
	.sect	'.debug_ranges'
.L105:
	.word	-1,.L91,0,.L106-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_clearAllEventFlags')
	.sect	'.debug_info'
.L107:
	.word	315
	.half	3
	.word	.L108
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L110,.L109
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_clearAllEventFlags',0,1,64,6,1,1,1
	.word	.L49,.L234,.L48
	.byte	4
	.word	.L49,.L234
	.byte	5
	.byte	'mask',0,1,66,12
	.word	.L232,.L235
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_clearAllEventFlags')
	.sect	'.debug_abbrev'
.L108:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_clearAllEventFlags')
	.sect	'.debug_line'
.L109:
	.word	.L377-.L376
.L376:
	.half	3
	.word	.L379-.L378
.L378:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L379:
	.byte	5,25,7,0,5,2
	.word	.L49
	.byte	3,193,0,1,5,22,9
	.half	.L321-.L49
	.byte	3,1,1,5,1,9
	.half	.L380-.L321
	.byte	3,1,1,7,9
	.half	.L111-.L380
	.byte	0,1,1
.L377:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_clearAllEventFlags')
	.sect	'.debug_ranges'
.L110:
	.word	-1,.L49,0,.L111-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_clearEventFlag')
	.sect	'.debug_info'
.L112:
	.word	336
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L115,.L114
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_clearEventFlag',0,1,71,6,1,1,1
	.word	.L51,.L236,.L50
	.byte	4
	.byte	'inputChannel',0,1,71,54
	.word	.L228,.L237
	.byte	5
	.word	.L51,.L236
	.byte	6
	.byte	'mask',0,1,73,12
	.word	.L232,.L238
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_clearEventFlag')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_clearEventFlag')
	.sect	'.debug_line'
.L114:
	.word	.L382-.L381
.L381:
	.half	3
	.word	.L384-.L383
.L383:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L384:
	.byte	5,19,7,0,5,2
	.word	.L51
	.byte	3,200,0,1,5,38,9
	.half	.L385-.L51
	.byte	1,5,21,9
	.half	.L386-.L385
	.byte	1,5,15,9
	.half	.L322-.L386
	.byte	3,1,1,5,1,9
	.half	.L387-.L322
	.byte	3,1,1,7,9
	.half	.L116-.L387
	.byte	0,1,1
.L382:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_clearEventFlag')
	.sect	'.debug_ranges'
.L115:
	.word	-1,.L51,0,.L116-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_clearInputChannelConfiguration')
	.sect	'.debug_info'
.L117:
	.word	370
	.half	3
	.word	.L118
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L120,.L119
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_clearInputChannelConfiguration',0,1,78,6,1,1,1
	.word	.L53,.L239,.L52
	.byte	4
	.byte	'inputChannel',0,1,78,70
	.word	.L228,.L240
	.byte	5
	.word	.L53,.L239
	.byte	6
	.byte	'index',0,1,81,12
	.word	.L232,.L241
	.byte	6
	.byte	'mask',0,1,82,12
	.word	.L232,.L242
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_clearInputChannelConfiguration')
	.sect	'.debug_abbrev'
.L118:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_clearInputChannelConfiguration')
	.sect	'.debug_line'
.L119:
	.word	.L389-.L388
.L388:
	.half	3
	.word	.L391-.L390
.L390:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L391:
	.byte	5,34,7,0,5,2
	.word	.L53
	.byte	3,208,0,1,5,5,9
	.half	.L323-.L53
	.byte	3,3,1,5,19,7,9
	.half	.L392-.L323
	.byte	3,2,1,5,24,9
	.half	.L393-.L392
	.byte	1,5,47,9
	.half	.L394-.L393
	.byte	1,5,52,9
	.half	.L395-.L394
	.byte	1,5,59,9
	.half	.L396-.L395
	.byte	1,5,62,9
	.half	.L397-.L396
	.byte	1,5,34,9
	.half	.L398-.L397
	.byte	1,5,69,9
	.half	.L399-.L398
	.byte	1,5,19,9
	.half	.L2-.L399
	.byte	3,5,1,5,24,9
	.half	.L400-.L2
	.byte	1,5,47,9
	.half	.L401-.L400
	.byte	1,5,52,9
	.half	.L402-.L401
	.byte	1,5,59,9
	.half	.L403-.L402
	.byte	1,5,62,9
	.half	.L404-.L403
	.byte	1,5,34,9
	.half	.L405-.L404
	.byte	1,5,1,9
	.half	.L3-.L405
	.byte	3,2,1,7,9
	.half	.L121-.L3
	.byte	0,1,1
.L389:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_clearInputChannelConfiguration')
	.sect	'.debug_ranges'
.L120:
	.word	-1,.L53,0,.L121-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_disableAutoClear')
	.sect	'.debug_info'
.L122:
	.word	342
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L125,.L124
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_disableAutoClear',0,1,130,1,6,1,1,1
	.word	.L59,.L243,.L58
	.byte	4
	.byte	'inputChannel',0,1,130,1,56
	.word	.L228,.L244
	.byte	5
	.word	.L59,.L243
	.byte	6
	.byte	'index',0,1,133,1,12
	.word	.L232,.L245
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_disableAutoClear')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_disableAutoClear')
	.sect	'.debug_line'
.L124:
	.word	.L407-.L406
.L406:
	.half	3
	.word	.L409-.L408
.L408:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L409:
	.byte	5,34,7,0,5,2
	.word	.L59
	.byte	3,132,1,1,5,5,9
	.half	.L326-.L59
	.byte	3,2,1,5,19,7,9
	.half	.L410-.L326
	.byte	3,2,1,5,24,9
	.half	.L411-.L410
	.byte	1,5,19,9
	.half	.L412-.L411
	.byte	1,5,24,9
	.half	.L413-.L412
	.byte	1,5,33,9
	.half	.L414-.L413
	.byte	1,5,40,9
	.half	.L415-.L414
	.byte	1,5,47,9
	.half	.L416-.L415
	.byte	1,5,19,9
	.half	.L8-.L416
	.byte	3,4,1,5,24,9
	.half	.L417-.L8
	.byte	1,5,19,9
	.half	.L418-.L417
	.byte	1,5,24,9
	.half	.L419-.L418
	.byte	1,5,33,9
	.half	.L420-.L419
	.byte	1,5,40,9
	.half	.L421-.L420
	.byte	1,5,1,9
	.half	.L9-.L421
	.byte	3,2,1,7,9
	.half	.L126-.L9
	.byte	0,1,1
.L407:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_disableAutoClear')
	.sect	'.debug_ranges'
.L125:
	.word	-1,.L59,0,.L126-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_disableFallingEdgeDetection')
	.sect	'.debug_info'
.L127:
	.word	353
	.half	3
	.word	.L128
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L130,.L129
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_disableFallingEdgeDetection',0,1,146,1,6,1,1,1
	.word	.L61,.L246,.L60
	.byte	4
	.byte	'inputChannel',0,1,146,1,67
	.word	.L228,.L247
	.byte	5
	.word	.L61,.L246
	.byte	6
	.byte	'index',0,1,149,1,12
	.word	.L232,.L248
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_disableFallingEdgeDetection')
	.sect	'.debug_abbrev'
.L128:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_disableFallingEdgeDetection')
	.sect	'.debug_line'
.L129:
	.word	.L423-.L422
.L422:
	.half	3
	.word	.L425-.L424
.L424:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L425:
	.byte	5,34,7,0,5,2
	.word	.L61
	.byte	3,148,1,1,5,5,9
	.half	.L327-.L61
	.byte	3,2,1,5,19,7,9
	.half	.L426-.L327
	.byte	3,2,1,5,24,9
	.half	.L427-.L426
	.byte	1,5,19,9
	.half	.L428-.L427
	.byte	1,5,24,9
	.half	.L429-.L428
	.byte	1,5,33,9
	.half	.L430-.L429
	.byte	1,5,39,9
	.half	.L431-.L430
	.byte	1,5,46,9
	.half	.L432-.L431
	.byte	1,5,19,9
	.half	.L10-.L432
	.byte	3,4,1,5,24,9
	.half	.L433-.L10
	.byte	1,5,19,9
	.half	.L434-.L433
	.byte	1,5,24,9
	.half	.L435-.L434
	.byte	1,5,33,9
	.half	.L436-.L435
	.byte	1,5,39,9
	.half	.L437-.L436
	.byte	1,5,1,9
	.half	.L11-.L437
	.byte	3,2,1,7,9
	.half	.L131-.L11
	.byte	0,1,1
.L423:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_disableFallingEdgeDetection')
	.sect	'.debug_ranges'
.L130:
	.word	-1,.L61,0,.L131-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_disableRisingEdgeDetection')
	.sect	'.debug_info'
.L132:
	.word	352
	.half	3
	.word	.L133
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L135,.L134
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_disableRisingEdgeDetection',0,1,178,1,6,1,1,1
	.word	.L65,.L249,.L64
	.byte	4
	.byte	'inputChannel',0,1,178,1,66
	.word	.L228,.L250
	.byte	5
	.word	.L65,.L249
	.byte	6
	.byte	'index',0,1,181,1,12
	.word	.L232,.L251
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_disableRisingEdgeDetection')
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_disableRisingEdgeDetection')
	.sect	'.debug_line'
.L134:
	.word	.L439-.L438
.L438:
	.half	3
	.word	.L441-.L440
.L440:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L441:
	.byte	5,34,7,0,5,2
	.word	.L65
	.byte	3,180,1,1,5,5,9
	.half	.L329-.L65
	.byte	3,2,1,5,19,7,9
	.half	.L442-.L329
	.byte	3,2,1,5,24,9
	.half	.L443-.L442
	.byte	1,5,19,9
	.half	.L444-.L443
	.byte	1,5,24,9
	.half	.L445-.L444
	.byte	1,5,33,9
	.half	.L446-.L445
	.byte	1,5,39,9
	.half	.L447-.L446
	.byte	1,5,46,9
	.half	.L448-.L447
	.byte	1,5,19,9
	.half	.L14-.L448
	.byte	3,4,1,5,24,9
	.half	.L449-.L14
	.byte	1,5,19,9
	.half	.L450-.L449
	.byte	1,5,24,9
	.half	.L451-.L450
	.byte	1,5,33,9
	.half	.L452-.L451
	.byte	1,5,39,9
	.half	.L453-.L452
	.byte	1,5,1,9
	.half	.L15-.L453
	.byte	3,2,1,7,9
	.half	.L136-.L15
	.byte	0,1,1
.L439:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_disableRisingEdgeDetection')
	.sect	'.debug_ranges'
.L135:
	.word	-1,.L65,0,.L136-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_enableAutoClear')
	.sect	'.debug_info'
.L137:
	.word	341
	.half	3
	.word	.L138
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L140,.L139
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_enableAutoClear',0,1,210,1,6,1,1,1
	.word	.L69,.L252,.L68
	.byte	4
	.byte	'inputChannel',0,1,210,1,55
	.word	.L228,.L253
	.byte	5
	.word	.L69,.L252
	.byte	6
	.byte	'index',0,1,213,1,12
	.word	.L232,.L254
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_enableAutoClear')
	.sect	'.debug_abbrev'
.L138:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_enableAutoClear')
	.sect	'.debug_line'
.L139:
	.word	.L455-.L454
.L454:
	.half	3
	.word	.L457-.L456
.L456:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L457:
	.byte	5,34,7,0,5,2
	.word	.L69
	.byte	3,212,1,1,5,5,9
	.half	.L331-.L69
	.byte	3,2,1,5,19,7,9
	.half	.L458-.L331
	.byte	3,2,1,5,24,9
	.half	.L459-.L458
	.byte	1,5,19,9
	.half	.L460-.L459
	.byte	1,5,24,9
	.half	.L461-.L460
	.byte	1,5,33,9
	.half	.L462-.L461
	.byte	1,5,40,9
	.half	.L463-.L462
	.byte	1,5,46,9
	.half	.L464-.L463
	.byte	1,5,19,9
	.half	.L18-.L464
	.byte	3,4,1,5,24,9
	.half	.L465-.L18
	.byte	1,5,19,9
	.half	.L466-.L465
	.byte	1,5,24,9
	.half	.L467-.L466
	.byte	1,5,33,9
	.half	.L468-.L467
	.byte	1,5,40,9
	.half	.L469-.L468
	.byte	1,5,1,9
	.half	.L19-.L469
	.byte	3,2,1,7,9
	.half	.L141-.L19
	.byte	0,1,1
.L455:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_enableAutoClear')
	.sect	'.debug_ranges'
.L140:
	.word	-1,.L69,0,.L141-.L69,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_enableFallingEdgeDetection')
	.sect	'.debug_info'
.L142:
	.word	352
	.half	3
	.word	.L143
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L145,.L144
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_enableFallingEdgeDetection',0,1,226,1,6,1,1,1
	.word	.L71,.L255,.L70
	.byte	4
	.byte	'inputChannel',0,1,226,1,66
	.word	.L228,.L256
	.byte	5
	.word	.L71,.L255
	.byte	6
	.byte	'index',0,1,229,1,12
	.word	.L232,.L257
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_enableFallingEdgeDetection')
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_enableFallingEdgeDetection')
	.sect	'.debug_line'
.L144:
	.word	.L471-.L470
.L470:
	.half	3
	.word	.L473-.L472
.L472:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L473:
	.byte	5,34,7,0,5,2
	.word	.L71
	.byte	3,228,1,1,5,5,9
	.half	.L332-.L71
	.byte	3,2,1,5,19,7,9
	.half	.L474-.L332
	.byte	3,2,1,5,24,9
	.half	.L475-.L474
	.byte	1,5,19,9
	.half	.L476-.L475
	.byte	1,5,24,9
	.half	.L477-.L476
	.byte	1,5,33,9
	.half	.L478-.L477
	.byte	1,5,39,9
	.half	.L479-.L478
	.byte	1,5,45,9
	.half	.L480-.L479
	.byte	1,5,19,9
	.half	.L20-.L480
	.byte	3,4,1,5,24,9
	.half	.L481-.L20
	.byte	1,5,19,9
	.half	.L482-.L481
	.byte	1,5,24,9
	.half	.L483-.L482
	.byte	1,5,33,9
	.half	.L484-.L483
	.byte	1,5,39,9
	.half	.L485-.L484
	.byte	1,5,1,9
	.half	.L21-.L485
	.byte	3,2,1,7,9
	.half	.L146-.L21
	.byte	0,1,1
.L471:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_enableFallingEdgeDetection')
	.sect	'.debug_ranges'
.L145:
	.word	-1,.L71,0,.L146-.L71,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_enableRisingEdgeDetection')
	.sect	'.debug_info'
.L147:
	.word	351
	.half	3
	.word	.L148
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L150,.L149
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_enableRisingEdgeDetection',0,1,130,2,6,1,1,1
	.word	.L75,.L258,.L74
	.byte	4
	.byte	'inputChannel',0,1,130,2,65
	.word	.L228,.L259
	.byte	5
	.word	.L75,.L258
	.byte	6
	.byte	'index',0,1,133,2,12
	.word	.L232,.L260
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_enableRisingEdgeDetection')
	.sect	'.debug_abbrev'
.L148:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_enableRisingEdgeDetection')
	.sect	'.debug_line'
.L149:
	.word	.L487-.L486
.L486:
	.half	3
	.word	.L489-.L488
.L488:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L489:
	.byte	5,34,7,0,5,2
	.word	.L75
	.byte	3,132,2,1,5,5,9
	.half	.L334-.L75
	.byte	3,2,1,5,19,7,9
	.half	.L490-.L334
	.byte	3,2,1,5,24,9
	.half	.L491-.L490
	.byte	1,5,19,9
	.half	.L492-.L491
	.byte	1,5,24,9
	.half	.L493-.L492
	.byte	1,5,33,9
	.half	.L494-.L493
	.byte	1,5,39,9
	.half	.L495-.L494
	.byte	1,5,45,9
	.half	.L496-.L495
	.byte	1,5,19,9
	.half	.L24-.L496
	.byte	3,4,1,5,24,9
	.half	.L497-.L24
	.byte	1,5,19,9
	.half	.L498-.L497
	.byte	1,5,24,9
	.half	.L499-.L498
	.byte	1,5,33,9
	.half	.L500-.L499
	.byte	1,5,39,9
	.half	.L501-.L500
	.byte	1,5,1,9
	.half	.L25-.L501
	.byte	3,2,1,7,9
	.half	.L151-.L25
	.byte	0,1,1
.L487:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_enableRisingEdgeDetection')
	.sect	'.debug_ranges'
.L150:
	.word	-1,.L75,0,.L151-.L75,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_getAllEventFlagsStatus')
	.sect	'.debug_info'
.L152:
	.word	306
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L155,.L154
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_getAllEventFlagsStatus',0,1,162,2,8
	.word	.L232
	.byte	1,1,1
	.word	.L79,.L261,.L78
	.byte	4
	.word	.L79,.L261
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_getAllEventFlagsStatus')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_getAllEventFlagsStatus')
	.sect	'.debug_line'
.L154:
	.word	.L503-.L502
.L502:
	.half	3
	.word	.L505-.L504
.L504:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L505:
	.byte	5,27,7,0,5,2
	.word	.L79
	.byte	3,163,2,1,5,5,9
	.half	.L506-.L79
	.byte	1,5,1,9
	.half	.L28-.L506
	.byte	3,1,1,7,9
	.half	.L156-.L28
	.byte	0,1,1
.L503:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_getAllEventFlagsStatus')
	.sect	'.debug_ranges'
.L155:
	.word	-1,.L79,0,.L156-.L79,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_getEventFlagStatus')
	.sect	'.debug_info'
.L157:
	.word	347
	.half	3
	.word	.L158
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L160,.L159
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_getEventFlagStatus',0,1,168,2,9
	.word	.L262
	.byte	1,1,1
	.word	.L81,.L263,.L80
	.byte	4
	.byte	'inputChannel',0,1,168,2,61
	.word	.L228,.L264
	.byte	5
	.word	.L81,.L263
	.byte	6
	.byte	'mask',0,1,170,2,12
	.word	.L232,.L265
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_getEventFlagStatus')
	.sect	'.debug_abbrev'
.L158:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_getEventFlagStatus')
	.sect	'.debug_line'
.L159:
	.word	.L508-.L507
.L507:
	.half	3
	.word	.L510-.L509
.L509:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L510:
	.byte	5,20,7,0,5,2
	.word	.L81
	.byte	3,169,2,1,5,23,9
	.half	.L511-.L81
	.byte	1,5,28,9
	.half	.L336-.L511
	.byte	3,1,1,5,31,9
	.half	.L512-.L336
	.byte	1,5,12,9
	.half	.L513-.L512
	.byte	1,5,39,7,9
	.half	.L514-.L513
	.byte	1,5,46,9
	.half	.L515-.L514
	.byte	1,5,39,9
	.half	.L29-.L515
	.byte	1,5,5,9
	.half	.L30-.L29
	.byte	1,5,1,9
	.half	.L31-.L30
	.byte	3,1,1,7,9
	.half	.L161-.L31
	.byte	0,1,1
.L508:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_getEventFlagStatus')
	.sect	'.debug_ranges'
.L160:
	.word	-1,.L81,0,.L161-.L81,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_getInputChannelConfiguration')
	.sect	'.debug_info'
.L162:
	.word	396
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L165,.L164
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_getInputChannelConfiguration',0,1,175,2,8
	.word	.L232
	.byte	1,1,1
	.word	.L83,.L266,.L82
	.byte	4
	.byte	'inputChannel',0,1,175,2,70
	.word	.L228,.L267
	.byte	5
	.word	.L83,.L266
	.byte	6
	.byte	'index',0,1,178,2,12
	.word	.L232,.L268
	.byte	6
	.byte	'status',0,1,179,2,12
	.word	.L232,.L269
	.byte	6
	.byte	'mask',0,1,179,2,20
	.word	.L232,.L270
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_getInputChannelConfiguration')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_getInputChannelConfiguration')
	.sect	'.debug_line'
.L164:
	.word	.L517-.L516
.L516:
	.half	3
	.word	.L519-.L518
.L518:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L519:
	.byte	5,34,7,0,5,2
	.word	.L83
	.byte	3,177,2,1,5,5,9
	.half	.L337-.L83
	.byte	3,3,1,5,29,7,9
	.half	.L520-.L337
	.byte	3,3,1,5,34,9
	.half	.L521-.L520
	.byte	1,5,41,9
	.half	.L522-.L521
	.byte	1,5,44,9
	.half	.L523-.L522
	.byte	1,5,30,9
	.half	.L339-.L523
	.byte	3,127,1,5,29,9
	.half	.L32-.L339
	.byte	3,5,1,5,34,9
	.half	.L524-.L32
	.byte	1,5,41,9
	.half	.L525-.L524
	.byte	1,5,44,9
	.half	.L526-.L525
	.byte	1,5,5,9
	.half	.L33-.L526
	.byte	3,3,1,5,1,9
	.half	.L34-.L33
	.byte	3,1,1,7,9
	.half	.L166-.L34
	.byte	0,1,1
.L517:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_getInputChannelConfiguration')
	.sect	'.debug_ranges'
.L165:
	.word	-1,.L83,0,.L166-.L83,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_setEventFlag')
	.sect	'.debug_info'
.L167:
	.word	337
	.half	3
	.word	.L168
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L170,.L169
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_setEventFlag',0,1,244,2,6,1,1,1
	.word	.L93,.L271,.L92
	.byte	4
	.byte	'inputChannel',0,1,244,2,52
	.word	.L228,.L272
	.byte	5
	.word	.L93,.L271
	.byte	6
	.byte	'mask',0,1,246,2,12
	.word	.L232,.L273
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_setEventFlag')
	.sect	'.debug_abbrev'
.L168:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_setEventFlag')
	.sect	'.debug_line'
.L169:
	.word	.L528-.L527
.L527:
	.half	3
	.word	.L530-.L529
.L529:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L530:
	.byte	5,19,7,0,5,2
	.word	.L93
	.byte	3,245,2,1,5,21,9
	.half	.L531-.L93
	.byte	1,5,15,9
	.half	.L347-.L531
	.byte	3,1,1,5,1,9
	.half	.L532-.L347
	.byte	3,1,1,7,9
	.half	.L171-.L532
	.byte	0,1,1
.L528:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_setEventFlag')
	.sect	'.debug_ranges'
.L170:
	.word	-1,.L93,0,.L171-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_connectTrigger')
	.sect	'.debug_info'
.L172:
	.word	363
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L175,.L174
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_connectTrigger',0,1,114,6,1,1,1
	.word	.L57,.L274,.L56
	.byte	4
	.byte	'inputChannel',0,1,114,54
	.word	.L228,.L275
	.byte	4
	.byte	'triggerSelect',0,1,114,95
	.word	.L276,.L277
	.byte	5
	.word	.L57,.L274
	.byte	6
	.byte	'index',0,1,117,12
	.word	.L232,.L278
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_connectTrigger')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_connectTrigger')
	.sect	'.debug_line'
.L174:
	.word	.L534-.L533
.L533:
	.half	3
	.word	.L536-.L535
.L535:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L536:
	.byte	5,34,7,0,5,2
	.word	.L57
	.byte	3,244,0,1,5,5,9
	.half	.L325-.L57
	.byte	3,2,1,5,19,7,9
	.half	.L537-.L325
	.byte	3,2,1,5,24,9
	.half	.L538-.L537
	.byte	1,5,19,9
	.half	.L539-.L538
	.byte	1,5,24,9
	.half	.L540-.L539
	.byte	1,5,33,9
	.half	.L541-.L540
	.byte	1,5,39,9
	.half	.L542-.L541
	.byte	1,5,54,9
	.half	.L543-.L542
	.byte	1,5,19,9
	.half	.L6-.L543
	.byte	3,4,1,5,24,9
	.half	.L544-.L6
	.byte	1,5,19,9
	.half	.L545-.L544
	.byte	1,5,24,9
	.half	.L546-.L545
	.byte	1,5,33,9
	.half	.L547-.L546
	.byte	1,5,39,9
	.half	.L548-.L547
	.byte	1,5,1,9
	.half	.L7-.L548
	.byte	3,2,1,7,9
	.half	.L176-.L7
	.byte	0,1,1
.L534:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_connectTrigger')
	.sect	'.debug_ranges'
.L175:
	.word	-1,.L57,0,.L176-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_disableTriggerPulse')
	.sect	'.debug_info'
.L177:
	.word	345
	.half	3
	.word	.L178
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L180,.L179
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_disableTriggerPulse',0,1,194,1,6,1,1,1
	.word	.L67,.L279,.L66
	.byte	4
	.byte	'inputChannel',0,1,194,1,59
	.word	.L228,.L280
	.byte	5
	.word	.L67,.L279
	.byte	6
	.byte	'index',0,1,197,1,12
	.word	.L232,.L281
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_disableTriggerPulse')
	.sect	'.debug_abbrev'
.L178:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_disableTriggerPulse')
	.sect	'.debug_line'
.L179:
	.word	.L550-.L549
.L549:
	.half	3
	.word	.L552-.L551
.L551:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L552:
	.byte	5,34,7,0,5,2
	.word	.L67
	.byte	3,196,1,1,5,5,9
	.half	.L330-.L67
	.byte	3,2,1,5,19,7,9
	.half	.L553-.L330
	.byte	3,2,1,5,24,9
	.half	.L554-.L553
	.byte	1,5,19,9
	.half	.L555-.L554
	.byte	1,5,24,9
	.half	.L556-.L555
	.byte	1,5,33,9
	.half	.L557-.L556
	.byte	1,5,40,9
	.half	.L558-.L557
	.byte	1,5,47,9
	.half	.L559-.L558
	.byte	1,5,19,9
	.half	.L16-.L559
	.byte	3,4,1,5,24,9
	.half	.L560-.L16
	.byte	1,5,19,9
	.half	.L561-.L560
	.byte	1,5,24,9
	.half	.L562-.L561
	.byte	1,5,33,9
	.half	.L563-.L562
	.byte	1,5,40,9
	.half	.L564-.L563
	.byte	1,5,1,9
	.half	.L17-.L564
	.byte	3,2,1,7,9
	.half	.L181-.L17
	.byte	0,1,1
.L550:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_disableTriggerPulse')
	.sect	'.debug_ranges'
.L180:
	.word	-1,.L67,0,.L181-.L67,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_enableTriggerPulse')
	.sect	'.debug_info'
.L182:
	.word	344
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L185,.L184
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_enableTriggerPulse',0,1,146,2,6,1,1,1
	.word	.L77,.L282,.L76
	.byte	4
	.byte	'inputChannel',0,1,146,2,58
	.word	.L228,.L283
	.byte	5
	.word	.L77,.L282
	.byte	6
	.byte	'index',0,1,149,2,12
	.word	.L232,.L284
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_enableTriggerPulse')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_enableTriggerPulse')
	.sect	'.debug_line'
.L184:
	.word	.L566-.L565
.L565:
	.half	3
	.word	.L568-.L567
.L567:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L568:
	.byte	5,34,7,0,5,2
	.word	.L77
	.byte	3,148,2,1,5,5,9
	.half	.L335-.L77
	.byte	3,2,1,5,19,7,9
	.half	.L569-.L335
	.byte	3,2,1,5,24,9
	.half	.L570-.L569
	.byte	1,5,19,9
	.half	.L571-.L570
	.byte	1,5,24,9
	.half	.L572-.L571
	.byte	1,5,33,9
	.half	.L573-.L572
	.byte	1,5,40,9
	.half	.L574-.L573
	.byte	1,5,46,9
	.half	.L575-.L574
	.byte	1,5,19,9
	.half	.L26-.L575
	.byte	3,4,1,5,24,9
	.half	.L576-.L26
	.byte	1,5,19,9
	.half	.L577-.L576
	.byte	1,5,24,9
	.half	.L578-.L577
	.byte	1,5,33,9
	.half	.L579-.L578
	.byte	1,5,40,9
	.half	.L580-.L579
	.byte	1,5,1,9
	.half	.L27-.L580
	.byte	3,2,1,7,9
	.half	.L186-.L27
	.byte	0,1,1
.L566:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_enableTriggerPulse')
	.sect	'.debug_ranges'
.L185:
	.word	-1,.L77,0,.L186-.L77,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_setFlagPatternDetection')
	.sect	'.debug_info'
.L187:
	.word	471
	.half	3
	.word	.L188
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L190,.L189
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_setFlagPatternDetection',0,1,251,2,6,1,1,1
	.word	.L95,.L285,.L94
	.byte	4
	.byte	'outputChannel',0,1,251,2,64
	.word	.L286,.L287
	.byte	4
	.byte	'inputChannel',0,1,251,2,102
	.word	.L228,.L288
	.byte	4
	.byte	'state',0,1,251,2,124
	.word	.L262,.L289
	.byte	5
	.word	.L95,.L285
	.byte	6
	.byte	'shift',0,1,253,2,12
	.word	.L232,.L290
	.byte	6
	.byte	'mask',0,1,253,2,19
	.word	.L232,.L291
	.byte	5
	.word	.L292,.L293
	.byte	6
	.byte	'index',0,1,132,3,16
	.word	.L232,.L294
	.byte	0,5
	.word	.L295,.L45
	.byte	6
	.byte	'index',0,1,140,3,16
	.word	.L232,.L296
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_setFlagPatternDetection')
	.sect	'.debug_abbrev'
.L188:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_setFlagPatternDetection')
	.sect	'.debug_line'
.L189:
	.word	.L582-.L581
.L581:
	.half	3
	.word	.L584-.L583
.L583:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L584:
	.byte	5,5,7,0,5,2
	.word	.L95
	.byte	3,254,2,1,5,31,7,9
	.half	.L585-.L95
	.byte	3,2,1,5,18,9
	.half	.L350-.L585
	.byte	3,1,1,5,20,9
	.half	.L586-.L350
	.byte	1,5,39,9
	.half	.L292-.L586
	.byte	3,2,1,5,19,9
	.half	.L348-.L292
	.byte	3,1,1,5,24,9
	.half	.L587-.L348
	.byte	1,5,47,9
	.half	.L588-.L587
	.byte	1,5,52,9
	.half	.L589-.L588
	.byte	1,5,59,9
	.half	.L590-.L589
	.byte	1,5,64,9
	.half	.L591-.L590
	.byte	1,5,62,9
	.half	.L351-.L591
	.byte	1,5,88,9
	.half	.L592-.L351
	.byte	1,5,71,9
	.half	.L349-.L592
	.byte	1,5,34,9
	.half	.L593-.L349
	.byte	1,5,36,9
	.half	.L293-.L593
	.byte	3,124,1,5,18,9
	.half	.L44-.L293
	.byte	3,9,1,5,20,9
	.half	.L594-.L44
	.byte	1,5,39,9
	.half	.L295-.L594
	.byte	3,2,1,5,19,9
	.half	.L352-.L295
	.byte	3,1,1,5,24,9
	.half	.L595-.L352
	.byte	1,5,47,9
	.half	.L596-.L595
	.byte	1,5,52,9
	.half	.L597-.L596
	.byte	1,5,59,9
	.half	.L598-.L597
	.byte	1,5,64,9
	.half	.L599-.L598
	.byte	1,5,62,9
	.half	.L354-.L599
	.byte	1,5,88,9
	.half	.L600-.L354
	.byte	1,5,71,9
	.half	.L353-.L600
	.byte	1,5,34,9
	.half	.L601-.L353
	.byte	1,5,1,9
	.half	.L45-.L601
	.byte	3,2,1,7,9
	.half	.L191-.L45
	.byte	0,1,1
.L582:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_setFlagPatternDetection')
	.sect	'.debug_ranges'
.L190:
	.word	-1,.L95,0,.L191-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_clearOutputChannelConfiguration')
	.sect	'.debug_info'
.L192:
	.word	372
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L195,.L194
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_clearOutputChannelConfiguration',0,1,96,6,1,1,1
	.word	.L55,.L297,.L54
	.byte	4
	.byte	'outputChannel',0,1,96,72
	.word	.L286,.L298
	.byte	5
	.word	.L55,.L297
	.byte	6
	.byte	'index',0,1,99,12
	.word	.L232,.L299
	.byte	6
	.byte	'mask',0,1,100,12
	.word	.L232,.L300
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_clearOutputChannelConfiguration')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_clearOutputChannelConfiguration')
	.sect	'.debug_line'
.L194:
	.word	.L603-.L602
.L602:
	.half	3
	.word	.L605-.L604
.L604:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L605:
	.byte	5,35,7,0,5,2
	.word	.L55
	.byte	3,226,0,1,5,5,9
	.half	.L324-.L55
	.byte	3,3,1,5,19,7,9
	.half	.L606-.L324
	.byte	3,2,1,5,24,9
	.half	.L607-.L606
	.byte	1,5,47,9
	.half	.L608-.L607
	.byte	1,5,52,9
	.half	.L609-.L608
	.byte	1,5,59,9
	.half	.L610-.L609
	.byte	1,5,62,9
	.half	.L611-.L610
	.byte	1,5,34,9
	.half	.L612-.L611
	.byte	1,5,69,9
	.half	.L613-.L612
	.byte	1,5,19,9
	.half	.L4-.L613
	.byte	3,5,1,5,24,9
	.half	.L614-.L4
	.byte	1,5,47,9
	.half	.L615-.L614
	.byte	1,5,52,9
	.half	.L616-.L615
	.byte	1,5,59,9
	.half	.L617-.L616
	.byte	1,5,62,9
	.half	.L618-.L617
	.byte	1,5,34,9
	.half	.L619-.L618
	.byte	1,5,1,9
	.half	.L5-.L619
	.byte	3,2,1,7,9
	.half	.L196-.L5
	.byte	0,1,1
.L603:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_clearOutputChannelConfiguration')
	.sect	'.debug_ranges'
.L195:
	.word	-1,.L55,0,.L196-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_disablePatternDetectionTrigger')
	.sect	'.debug_info'
.L197:
	.word	357
	.half	3
	.word	.L198
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L200,.L199
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_disablePatternDetectionTrigger',0,1,162,1,6,1,1,1
	.word	.L63,.L301,.L62
	.byte	4
	.byte	'outputChannel',0,1,162,1,71
	.word	.L286,.L302
	.byte	5
	.word	.L63,.L301
	.byte	6
	.byte	'index',0,1,165,1,12
	.word	.L232,.L303
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_disablePatternDetectionTrigger')
	.sect	'.debug_abbrev'
.L198:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_disablePatternDetectionTrigger')
	.sect	'.debug_line'
.L199:
	.word	.L621-.L620
.L620:
	.half	3
	.word	.L623-.L622
.L622:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L623:
	.byte	5,35,7,0,5,2
	.word	.L63
	.byte	3,164,1,1,5,5,9
	.half	.L328-.L63
	.byte	3,2,1,5,19,7,9
	.half	.L624-.L328
	.byte	3,2,1,5,24,9
	.half	.L625-.L624
	.byte	1,5,19,9
	.half	.L626-.L625
	.byte	1,5,24,9
	.half	.L627-.L626
	.byte	1,5,33,9
	.half	.L628-.L627
	.byte	1,5,40,9
	.half	.L629-.L628
	.byte	1,5,47,9
	.half	.L630-.L629
	.byte	1,5,19,9
	.half	.L12-.L630
	.byte	3,4,1,5,24,9
	.half	.L631-.L12
	.byte	1,5,19,9
	.half	.L632-.L631
	.byte	1,5,24,9
	.half	.L633-.L632
	.byte	1,5,33,9
	.half	.L634-.L633
	.byte	1,5,40,9
	.half	.L635-.L634
	.byte	1,5,1,9
	.half	.L13-.L635
	.byte	3,2,1,7,9
	.half	.L201-.L13
	.byte	0,1,1
.L621:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_disablePatternDetectionTrigger')
	.sect	'.debug_ranges'
.L200:
	.word	-1,.L63,0,.L201-.L63,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_enablePatternDetectionTrigger')
	.sect	'.debug_info'
.L202:
	.word	356
	.half	3
	.word	.L203
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L205,.L204
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_enablePatternDetectionTrigger',0,1,242,1,6,1,1,1
	.word	.L73,.L304,.L72
	.byte	4
	.byte	'outputChannel',0,1,242,1,70
	.word	.L286,.L305
	.byte	5
	.word	.L73,.L304
	.byte	6
	.byte	'index',0,1,245,1,12
	.word	.L232,.L306
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_enablePatternDetectionTrigger')
	.sect	'.debug_abbrev'
.L203:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_enablePatternDetectionTrigger')
	.sect	'.debug_line'
.L204:
	.word	.L637-.L636
.L636:
	.half	3
	.word	.L639-.L638
.L638:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L639:
	.byte	5,35,7,0,5,2
	.word	.L73
	.byte	3,244,1,1,5,5,9
	.half	.L333-.L73
	.byte	3,2,1,5,19,7,9
	.half	.L640-.L333
	.byte	3,2,1,5,24,9
	.half	.L641-.L640
	.byte	1,5,19,9
	.half	.L642-.L641
	.byte	1,5,24,9
	.half	.L643-.L642
	.byte	1,5,33,9
	.half	.L644-.L643
	.byte	1,5,40,9
	.half	.L645-.L644
	.byte	1,5,46,9
	.half	.L646-.L645
	.byte	1,5,19,9
	.half	.L22-.L646
	.byte	3,4,1,5,24,9
	.half	.L647-.L22
	.byte	1,5,19,9
	.half	.L648-.L647
	.byte	1,5,24,9
	.half	.L649-.L648
	.byte	1,5,33,9
	.half	.L650-.L649
	.byte	1,5,40,9
	.half	.L651-.L650
	.byte	1,5,1,9
	.half	.L23-.L651
	.byte	3,2,1,7,9
	.half	.L206-.L23
	.byte	0,1,1
.L637:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_enablePatternDetectionTrigger')
	.sect	'.debug_ranges'
.L205:
	.word	-1,.L73,0,.L206-.L73,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_getOutputChannelConfiguration')
	.sect	'.debug_info'
.L207:
	.word	398
	.half	3
	.word	.L208
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L210,.L209
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_getOutputChannelConfiguration',0,1,195,2,8
	.word	.L232
	.byte	1,1,1
	.word	.L85,.L307,.L84
	.byte	4
	.byte	'outputChannel',0,1,195,2,72
	.word	.L286,.L308
	.byte	5
	.word	.L85,.L307
	.byte	6
	.byte	'index',0,1,198,2,12
	.word	.L232,.L309
	.byte	6
	.byte	'status',0,1,199,2,12
	.word	.L232,.L310
	.byte	6
	.byte	'mask',0,1,199,2,20
	.word	.L232,.L311
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_getOutputChannelConfiguration')
	.sect	'.debug_abbrev'
.L208:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_getOutputChannelConfiguration')
	.sect	'.debug_line'
.L209:
	.word	.L653-.L652
.L652:
	.half	3
	.word	.L655-.L654
.L654:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L655:
	.byte	5,35,7,0,5,2
	.word	.L85
	.byte	3,197,2,1,5,5,9
	.half	.L341-.L85
	.byte	3,3,1,5,29,7,9
	.half	.L656-.L341
	.byte	3,3,1,5,34,9
	.half	.L657-.L656
	.byte	1,5,41,9
	.half	.L658-.L657
	.byte	1,5,44,9
	.half	.L659-.L658
	.byte	1,5,30,9
	.half	.L343-.L659
	.byte	3,127,1,5,29,9
	.half	.L35-.L343
	.byte	3,5,1,5,34,9
	.half	.L660-.L35
	.byte	1,5,41,9
	.half	.L661-.L660
	.byte	1,5,44,9
	.half	.L662-.L661
	.byte	1,5,5,9
	.half	.L36-.L662
	.byte	3,3,1,5,1,9
	.half	.L37-.L36
	.byte	3,1,1,7,9
	.half	.L211-.L37
	.byte	0,1,1
.L653:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_getOutputChannelConfiguration')
	.sect	'.debug_ranges'
.L210:
	.word	-1,.L85,0,.L211-.L85,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_getPatternDetectionResult')
	.sect	'.debug_info'
.L212:
	.word	355
	.half	3
	.word	.L213
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L215,.L214
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_getPatternDetectionResult',0,1,215,2,9
	.word	.L262
	.byte	1,1,1
	.word	.L87,.L312,.L86
	.byte	4
	.byte	'outputChannel',0,1,215,2,69
	.word	.L286,.L313
	.byte	5
	.word	.L87,.L312
	.byte	6
	.byte	'mask',0,1,217,2,12
	.word	.L232,.L314
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_getPatternDetectionResult')
	.sect	'.debug_abbrev'
.L213:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_getPatternDetectionResult')
	.sect	'.debug_line'
.L214:
	.word	.L664-.L663
.L663:
	.half	3
	.word	.L666-.L665
.L665:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L666:
	.byte	5,20,7,0,5,2
	.word	.L87
	.byte	3,216,2,1,5,23,9
	.half	.L667-.L87
	.byte	1,5,28,9
	.half	.L345-.L667
	.byte	3,1,1,5,31,9
	.half	.L668-.L345
	.byte	1,5,12,9
	.half	.L669-.L668
	.byte	1,5,39,7,9
	.half	.L670-.L669
	.byte	1,5,46,9
	.half	.L671-.L670
	.byte	1,5,39,9
	.half	.L38-.L671
	.byte	1,5,5,9
	.half	.L39-.L38
	.byte	1,5,1,9
	.half	.L40-.L39
	.byte	3,1,1,7,9
	.half	.L216-.L40
	.byte	0,1,1
.L664:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_getPatternDetectionResult')
	.sect	'.debug_ranges'
.L215:
	.word	-1,.L87,0,.L216-.L87,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_getWholePatternDetectionResult')
	.sect	'.debug_info'
.L217:
	.word	314
	.half	3
	.word	.L218
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L220,.L219
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_getWholePatternDetectionResult',0,1,222,2,8
	.word	.L232
	.byte	1,1,1
	.word	.L89,.L315,.L88
	.byte	4
	.word	.L89,.L315
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_getWholePatternDetectionResult')
	.sect	'.debug_abbrev'
.L218:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_getWholePatternDetectionResult')
	.sect	'.debug_line'
.L219:
	.word	.L673-.L672
.L672:
	.half	3
	.word	.L675-.L674
.L674:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L675:
	.byte	5,27,7,0,5,2
	.word	.L89
	.byte	3,223,2,1,5,5,9
	.half	.L676-.L89
	.byte	1,5,1,9
	.half	.L41-.L676
	.byte	3,1,1,7,9
	.half	.L221-.L41
	.byte	0,1,1
.L673:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_getWholePatternDetectionResult')
	.sect	'.debug_ranges'
.L220:
	.word	-1,.L89,0,.L221-.L89,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuEru_setInterruptGatingPattern')
	.sect	'.debug_info'
.L222:
	.word	379
	.half	3
	.word	.L223
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L225,.L224
	.byte	2
	.word	.L98
	.byte	3
	.byte	'IfxScuEru_setInterruptGatingPattern',0,1,146,3,6,1,1,1
	.word	.L97,.L316,.L96
	.byte	4
	.byte	'outputChannel',0,1,146,3,66
	.word	.L286,.L317
	.byte	4
	.byte	'gatingPattern',0,1,146,3,114
	.word	.L318,.L319
	.byte	5
	.word	.L97,.L316
	.byte	6
	.byte	'index',0,1,149,3,12
	.word	.L232,.L320
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuEru_setInterruptGatingPattern')
	.sect	'.debug_abbrev'
.L223:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuEru_setInterruptGatingPattern')
	.sect	'.debug_line'
.L224:
	.word	.L678-.L677
.L677:
	.half	3
	.word	.L680-.L679
.L679:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuEru.c',0,0,0,0,0
.L680:
	.byte	5,35,7,0,5,2
	.word	.L97
	.byte	3,148,3,1,5,5,9
	.half	.L355-.L97
	.byte	3,2,1,5,19,7,9
	.half	.L681-.L355
	.byte	3,2,1,5,24,9
	.half	.L682-.L681
	.byte	1,5,19,9
	.half	.L683-.L682
	.byte	1,5,24,9
	.half	.L684-.L683
	.byte	1,5,33,9
	.half	.L685-.L684
	.byte	1,5,39,9
	.half	.L686-.L685
	.byte	1,5,54,9
	.half	.L687-.L686
	.byte	1,5,19,9
	.half	.L46-.L687
	.byte	3,4,1,5,24,9
	.half	.L688-.L46
	.byte	1,5,19,9
	.half	.L689-.L688
	.byte	1,5,24,9
	.half	.L690-.L689
	.byte	1,5,33,9
	.half	.L691-.L690
	.byte	1,5,39,9
	.half	.L692-.L691
	.byte	1,5,1,9
	.half	.L47-.L692
	.byte	3,2,1,7,9
	.half	.L226-.L47
	.byte	0,1,1
.L678:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuEru_setInterruptGatingPattern')
	.sect	'.debug_ranges'
.L225:
	.word	-1,.L97,0,.L226-.L97,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_clearAllEventFlags')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L234-.L49
	.half	2
	.byte	138,0
	.word	0,0
.L235:
	.word	-1,.L49,.L321-.L49,.L234-.L49
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_clearEventFlag')
	.sect	'.debug_loc'
.L50:
	.word	-1,.L51,0,.L236-.L51
	.half	2
	.byte	138,0
	.word	0,0
.L237:
	.word	-1,.L51,0,.L236-.L51
	.half	1
	.byte	84
	.word	0,0
.L238:
	.word	-1,.L51,.L322-.L51,.L236-.L51
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_clearInputChannelConfiguration')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L239-.L53
	.half	2
	.byte	138,0
	.word	0,0
.L241:
	.word	-1,.L53,.L323-.L53,.L239-.L53
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L240:
	.word	-1,.L53,0,.L239-.L53
	.half	1
	.byte	84
	.word	0,0
.L242:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_clearOutputChannelConfiguration')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L297-.L55
	.half	2
	.byte	138,0
	.word	0,0
.L299:
	.word	-1,.L55,.L324-.L55,.L297-.L55
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L300:
	.word	0,0
.L298:
	.word	-1,.L55,0,.L297-.L55
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_connectTrigger')
	.sect	'.debug_loc'
.L56:
	.word	-1,.L57,0,.L274-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L278:
	.word	-1,.L57,.L325-.L57,.L274-.L57
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L275:
	.word	-1,.L57,0,.L274-.L57
	.half	1
	.byte	84
	.word	0,0
.L277:
	.word	-1,.L57,0,.L274-.L57
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_disableAutoClear')
	.sect	'.debug_loc'
.L58:
	.word	-1,.L59,0,.L243-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L245:
	.word	-1,.L59,.L326-.L59,.L243-.L59
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L244:
	.word	-1,.L59,0,.L243-.L59
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_disableFallingEdgeDetection')
	.sect	'.debug_loc'
.L60:
	.word	-1,.L61,0,.L246-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L248:
	.word	-1,.L61,.L327-.L61,.L246-.L61
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L247:
	.word	-1,.L61,0,.L246-.L61
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_disablePatternDetectionTrigger')
	.sect	'.debug_loc'
.L62:
	.word	-1,.L63,0,.L301-.L63
	.half	2
	.byte	138,0
	.word	0,0
.L303:
	.word	-1,.L63,.L328-.L63,.L301-.L63
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L302:
	.word	-1,.L63,0,.L301-.L63
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_disableRisingEdgeDetection')
	.sect	'.debug_loc'
.L64:
	.word	-1,.L65,0,.L249-.L65
	.half	2
	.byte	138,0
	.word	0,0
.L251:
	.word	-1,.L65,.L329-.L65,.L249-.L65
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L250:
	.word	-1,.L65,0,.L249-.L65
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_disableTriggerPulse')
	.sect	'.debug_loc'
.L66:
	.word	-1,.L67,0,.L279-.L67
	.half	2
	.byte	138,0
	.word	0,0
.L281:
	.word	-1,.L67,.L330-.L67,.L279-.L67
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L280:
	.word	-1,.L67,0,.L279-.L67
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_enableAutoClear')
	.sect	'.debug_loc'
.L68:
	.word	-1,.L69,0,.L252-.L69
	.half	2
	.byte	138,0
	.word	0,0
.L254:
	.word	-1,.L69,.L331-.L69,.L252-.L69
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L253:
	.word	-1,.L69,0,.L252-.L69
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_enableFallingEdgeDetection')
	.sect	'.debug_loc'
.L70:
	.word	-1,.L71,0,.L255-.L71
	.half	2
	.byte	138,0
	.word	0,0
.L257:
	.word	-1,.L71,.L332-.L71,.L255-.L71
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L256:
	.word	-1,.L71,0,.L255-.L71
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_enablePatternDetectionTrigger')
	.sect	'.debug_loc'
.L72:
	.word	-1,.L73,0,.L304-.L73
	.half	2
	.byte	138,0
	.word	0,0
.L306:
	.word	-1,.L73,.L333-.L73,.L304-.L73
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L305:
	.word	-1,.L73,0,.L304-.L73
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_enableRisingEdgeDetection')
	.sect	'.debug_loc'
.L74:
	.word	-1,.L75,0,.L258-.L75
	.half	2
	.byte	138,0
	.word	0,0
.L260:
	.word	-1,.L75,.L334-.L75,.L258-.L75
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L259:
	.word	-1,.L75,0,.L258-.L75
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_enableTriggerPulse')
	.sect	'.debug_loc'
.L76:
	.word	-1,.L77,0,.L282-.L77
	.half	2
	.byte	138,0
	.word	0,0
.L284:
	.word	-1,.L77,.L335-.L77,.L282-.L77
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L283:
	.word	-1,.L77,0,.L282-.L77
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_getAllEventFlagsStatus')
	.sect	'.debug_loc'
.L78:
	.word	-1,.L79,0,.L261-.L79
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_getEventFlagStatus')
	.sect	'.debug_loc'
.L80:
	.word	-1,.L81,0,.L263-.L81
	.half	2
	.byte	138,0
	.word	0,0
.L264:
	.word	-1,.L81,0,.L263-.L81
	.half	1
	.byte	84
	.word	0,0
.L265:
	.word	-1,.L81,.L336-.L81,.L263-.L81
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_getInputChannelConfiguration')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L83,0,.L266-.L83
	.half	2
	.byte	138,0
	.word	0,0
.L268:
	.word	-1,.L83,.L337-.L83,.L338-.L83
	.half	1
	.byte	95
	.word	.L32-.L83,.L340-.L83
	.half	1
	.byte	95
	.word	0,0
.L267:
	.word	-1,.L83,0,.L266-.L83
	.half	1
	.byte	84
	.word	0,0
.L270:
	.word	0,0
.L269:
	.word	-1,.L83,.L339-.L83,.L32-.L83
	.half	1
	.byte	82
	.word	.L33-.L83,.L266-.L83
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_getOutputChannelConfiguration')
	.sect	'.debug_loc'
.L84:
	.word	-1,.L85,0,.L307-.L85
	.half	2
	.byte	138,0
	.word	0,0
.L309:
	.word	-1,.L85,.L341-.L85,.L342-.L85
	.half	1
	.byte	95
	.word	.L35-.L85,.L344-.L85
	.half	1
	.byte	95
	.word	0,0
.L311:
	.word	0,0
.L308:
	.word	-1,.L85,0,.L307-.L85
	.half	1
	.byte	84
	.word	0,0
.L310:
	.word	-1,.L85,.L343-.L85,.L35-.L85
	.half	1
	.byte	82
	.word	.L36-.L85,.L307-.L85
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_getPatternDetectionResult')
	.sect	'.debug_loc'
.L86:
	.word	-1,.L87,0,.L312-.L87
	.half	2
	.byte	138,0
	.word	0,0
.L314:
	.word	-1,.L87,.L345-.L87,.L312-.L87
	.half	1
	.byte	95
	.word	0,0
.L313:
	.word	-1,.L87,0,.L312-.L87
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_getWholePatternDetectionResult')
	.sect	'.debug_loc'
.L88:
	.word	-1,.L89,0,.L315-.L89
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_selectExternalInput')
	.sect	'.debug_loc'
.L90:
	.word	-1,.L91,0,.L227-.L91
	.half	2
	.byte	138,0
	.word	0,0
.L233:
	.word	-1,.L91,.L346-.L91,.L227-.L91
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L229:
	.word	-1,.L91,0,.L227-.L91
	.half	1
	.byte	84
	.word	0,0
.L231:
	.word	-1,.L91,0,.L227-.L91
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_setEventFlag')
	.sect	'.debug_loc'
.L92:
	.word	-1,.L93,0,.L271-.L93
	.half	2
	.byte	138,0
	.word	0,0
.L272:
	.word	-1,.L93,0,.L271-.L93
	.half	1
	.byte	84
	.word	0,0
.L273:
	.word	-1,.L93,.L347-.L93,.L271-.L93
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_setFlagPatternDetection')
	.sect	'.debug_loc'
.L94:
	.word	-1,.L95,0,.L285-.L95
	.half	2
	.byte	138,0
	.word	0,0
.L294:
	.word	-1,.L95,.L292-.L95,.L44-.L95
	.half	1
	.byte	84
	.word	0,0
.L296:
	.word	-1,.L95,.L295-.L95,.L45-.L95
	.half	1
	.byte	84
	.word	0,0
.L288:
	.word	-1,.L95,0,.L285-.L95
	.half	1
	.byte	85
	.word	0,0
.L291:
	.word	-1,.L95,.L292-.L95,.L351-.L95
	.half	5
	.byte	144,32,157,32,0
	.word	.L295-.L95,.L354-.L95
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L287:
	.word	-1,.L95,0,.L348-.L95
	.half	1
	.byte	84
	.word	.L44-.L95,.L352-.L95
	.half	1
	.byte	84
	.word	0,0
.L290:
	.word	-1,.L95,.L350-.L95,.L44-.L95
	.half	1
	.byte	81
	.word	0,0
.L289:
	.word	-1,.L95,0,.L349-.L95
	.half	1
	.byte	86
	.word	.L44-.L95,.L353-.L95
	.half	1
	.byte	86
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuEru_setInterruptGatingPattern')
	.sect	'.debug_loc'
.L96:
	.word	-1,.L97,0,.L316-.L97
	.half	2
	.byte	138,0
	.word	0,0
.L319:
	.word	-1,.L97,0,.L316-.L97
	.half	1
	.byte	85
	.word	0,0
.L320:
	.word	-1,.L97,.L355-.L97,.L316-.L97
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L317:
	.word	-1,.L97,0,.L316-.L97
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L693:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_clearAllEventFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L49,.L234-.L49
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_clearEventFlag')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L51,.L236-.L51
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_clearInputChannelConfiguration')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L53,.L239-.L53
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_clearOutputChannelConfiguration')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L55,.L297-.L55
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_connectTrigger')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L57,.L274-.L57
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_disableAutoClear')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L59,.L243-.L59
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_disableFallingEdgeDetection')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L61,.L246-.L61
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_disablePatternDetectionTrigger')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L63,.L301-.L63
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_disableRisingEdgeDetection')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L65,.L249-.L65
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_disableTriggerPulse')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L67,.L279-.L67
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_enableAutoClear')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L69,.L252-.L69
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_enableFallingEdgeDetection')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L71,.L255-.L71
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_enablePatternDetectionTrigger')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L73,.L304-.L73
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_enableRisingEdgeDetection')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L75,.L258-.L75
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_enableTriggerPulse')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L77,.L282-.L77
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_getAllEventFlagsStatus')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L79,.L261-.L79
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_getEventFlagStatus')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L81,.L263-.L81
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_getInputChannelConfiguration')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L83,.L266-.L83
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_getOutputChannelConfiguration')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L85,.L307-.L85
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_getPatternDetectionResult')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L87,.L312-.L87
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_getWholePatternDetectionResult')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L89,.L315-.L89
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_selectExternalInput')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L91,.L227-.L91
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_setEventFlag')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L93,.L271-.L93
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_setFlagPatternDetection')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L95,.L285-.L95
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuEru_setInterruptGatingPattern')
	.sect	'.debug_frame'
	.word	24
	.word	.L693,.L97,.L316-.L97
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	; Module end
