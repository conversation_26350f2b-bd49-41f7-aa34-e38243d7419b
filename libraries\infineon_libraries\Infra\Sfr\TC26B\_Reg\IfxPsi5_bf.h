/**
 * \file IfxPsi5_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Psi5_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Psi5
 * 
 */
#ifndef IFXPSI5_BF_H
#define IFXPSI5_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Psi5_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN0 */
#define IFX_PSI5_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN0 */
#define IFX_PSI5_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN0 */
#define IFX_PSI5_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN10 */
#define IFX_PSI5_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN10 */
#define IFX_PSI5_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN10 */
#define IFX_PSI5_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN11 */
#define IFX_PSI5_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN11 */
#define IFX_PSI5_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN11 */
#define IFX_PSI5_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN12 */
#define IFX_PSI5_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN12 */
#define IFX_PSI5_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN12 */
#define IFX_PSI5_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN13 */
#define IFX_PSI5_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN13 */
#define IFX_PSI5_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN13 */
#define IFX_PSI5_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN14 */
#define IFX_PSI5_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN14 */
#define IFX_PSI5_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN14 */
#define IFX_PSI5_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN15 */
#define IFX_PSI5_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN15 */
#define IFX_PSI5_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN15 */
#define IFX_PSI5_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN16 */
#define IFX_PSI5_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN16 */
#define IFX_PSI5_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN16 */
#define IFX_PSI5_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN17 */
#define IFX_PSI5_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN17 */
#define IFX_PSI5_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN17 */
#define IFX_PSI5_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN18 */
#define IFX_PSI5_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN18 */
#define IFX_PSI5_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN18 */
#define IFX_PSI5_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN19 */
#define IFX_PSI5_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN19 */
#define IFX_PSI5_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN19 */
#define IFX_PSI5_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN1 */
#define IFX_PSI5_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN1 */
#define IFX_PSI5_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN1 */
#define IFX_PSI5_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN20 */
#define IFX_PSI5_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN20 */
#define IFX_PSI5_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN20 */
#define IFX_PSI5_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN21 */
#define IFX_PSI5_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN21 */
#define IFX_PSI5_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN21 */
#define IFX_PSI5_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN22 */
#define IFX_PSI5_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN22 */
#define IFX_PSI5_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN22 */
#define IFX_PSI5_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN23 */
#define IFX_PSI5_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN23 */
#define IFX_PSI5_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN23 */
#define IFX_PSI5_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN24 */
#define IFX_PSI5_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN24 */
#define IFX_PSI5_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN24 */
#define IFX_PSI5_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN25 */
#define IFX_PSI5_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN25 */
#define IFX_PSI5_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN25 */
#define IFX_PSI5_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN26 */
#define IFX_PSI5_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN26 */
#define IFX_PSI5_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN26 */
#define IFX_PSI5_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN27 */
#define IFX_PSI5_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN27 */
#define IFX_PSI5_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN27 */
#define IFX_PSI5_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN28 */
#define IFX_PSI5_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN28 */
#define IFX_PSI5_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN28 */
#define IFX_PSI5_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN29 */
#define IFX_PSI5_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN29 */
#define IFX_PSI5_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN29 */
#define IFX_PSI5_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN2 */
#define IFX_PSI5_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN2 */
#define IFX_PSI5_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN2 */
#define IFX_PSI5_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN30 */
#define IFX_PSI5_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN30 */
#define IFX_PSI5_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN30 */
#define IFX_PSI5_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN31 */
#define IFX_PSI5_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN31 */
#define IFX_PSI5_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN31 */
#define IFX_PSI5_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN3 */
#define IFX_PSI5_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN3 */
#define IFX_PSI5_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN3 */
#define IFX_PSI5_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN4 */
#define IFX_PSI5_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN4 */
#define IFX_PSI5_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN4 */
#define IFX_PSI5_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN5 */
#define IFX_PSI5_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN5 */
#define IFX_PSI5_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN5 */
#define IFX_PSI5_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN6 */
#define IFX_PSI5_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN6 */
#define IFX_PSI5_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN6 */
#define IFX_PSI5_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN7 */
#define IFX_PSI5_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN7 */
#define IFX_PSI5_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN7 */
#define IFX_PSI5_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN8 */
#define IFX_PSI5_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN8 */
#define IFX_PSI5_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN8 */
#define IFX_PSI5_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_PSI5_ACCEN0_Bits.EN9 */
#define IFX_PSI5_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_ACCEN0_Bits.EN9 */
#define IFX_PSI5_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_ACCEN0_Bits.EN9 */
#define IFX_PSI5_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_CTV_Bits.CTC */
#define IFX_PSI5_CH_CTV_CTC_LEN (16u)

/** \brief  Mask for Ifx_PSI5_CH_CTV_Bits.CTC */
#define IFX_PSI5_CH_CTV_CTC_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5_CH_CTV_Bits.CTC */
#define IFX_PSI5_CH_CTV_CTC_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_CTV_Bits.CTV */
#define IFX_PSI5_CH_CTV_CTV_LEN (16u)

/** \brief  Mask for Ifx_PSI5_CH_CTV_Bits.CTV */
#define IFX_PSI5_CH_CTV_CTV_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5_CH_CTV_Bits.CTV */
#define IFX_PSI5_CH_CTV_CTV_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.ALTI */
#define IFX_PSI5_CH_IOCR_ALTI_LEN (2u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.ALTI */
#define IFX_PSI5_CH_IOCR_ALTI_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.ALTI */
#define IFX_PSI5_CH_IOCR_ALTI_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.CFEG */
#define IFX_PSI5_CH_IOCR_CFEG_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.CFEG */
#define IFX_PSI5_CH_IOCR_CFEG_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.CFEG */
#define IFX_PSI5_CH_IOCR_CFEG_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.CREG */
#define IFX_PSI5_CH_IOCR_CREG_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.CREG */
#define IFX_PSI5_CH_IOCR_CREG_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.CREG */
#define IFX_PSI5_CH_IOCR_CREG_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.DEPTH */
#define IFX_PSI5_CH_IOCR_DEPTH_LEN (4u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.DEPTH */
#define IFX_PSI5_CH_IOCR_DEPTH_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.DEPTH */
#define IFX_PSI5_CH_IOCR_DEPTH_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.FEG */
#define IFX_PSI5_CH_IOCR_FEG_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.FEG */
#define IFX_PSI5_CH_IOCR_FEG_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.FEG */
#define IFX_PSI5_CH_IOCR_FEG_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.IIE */
#define IFX_PSI5_CH_IOCR_IIE_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.IIE */
#define IFX_PSI5_CH_IOCR_IIE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.IIE */
#define IFX_PSI5_CH_IOCR_IIE_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.OIE */
#define IFX_PSI5_CH_IOCR_OIE_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.OIE */
#define IFX_PSI5_CH_IOCR_OIE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.OIE */
#define IFX_PSI5_CH_IOCR_OIE_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.REG */
#define IFX_PSI5_CH_IOCR_REG_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.REG */
#define IFX_PSI5_CH_IOCR_REG_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.REG */
#define IFX_PSI5_CH_IOCR_REG_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.RXM */
#define IFX_PSI5_CH_IOCR_RXM_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.RXM */
#define IFX_PSI5_CH_IOCR_RXM_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.RXM */
#define IFX_PSI5_CH_IOCR_RXM_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_IOCR_Bits.TXM */
#define IFX_PSI5_CH_IOCR_TXM_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_IOCR_Bits.TXM */
#define IFX_PSI5_CH_IOCR_TXM_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_IOCR_Bits.TXM */
#define IFX_PSI5_CH_IOCR_TXM_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.BOT */
#define IFX_PSI5_CH_PGC_BOT_LEN (7u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.BOT */
#define IFX_PSI5_CH_PGC_BOT_MSK (0x7fu)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.BOT */
#define IFX_PSI5_CH_PGC_BOT_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.BYP */
#define IFX_PSI5_CH_PGC_BYP_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.BYP */
#define IFX_PSI5_CH_PGC_BYP_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.BYP */
#define IFX_PSI5_CH_PGC_BYP_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.DEL */
#define IFX_PSI5_CH_PGC_DEL_LEN (6u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.DEL */
#define IFX_PSI5_CH_PGC_DEL_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.DEL */
#define IFX_PSI5_CH_PGC_DEL_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.ETB */
#define IFX_PSI5_CH_PGC_ETB_LEN (3u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.ETB */
#define IFX_PSI5_CH_PGC_ETB_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.ETB */
#define IFX_PSI5_CH_PGC_ETB_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.ETE */
#define IFX_PSI5_CH_PGC_ETE_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.ETE */
#define IFX_PSI5_CH_PGC_ETE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.ETE */
#define IFX_PSI5_CH_PGC_ETE_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.ETS */
#define IFX_PSI5_CH_PGC_ETS_LEN (3u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.ETS */
#define IFX_PSI5_CH_PGC_ETS_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.ETS */
#define IFX_PSI5_CH_PGC_ETS_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.PLEN */
#define IFX_PSI5_CH_PGC_PLEN_LEN (6u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.PLEN */
#define IFX_PSI5_CH_PGC_PLEN_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.PLEN */
#define IFX_PSI5_CH_PGC_PLEN_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.PTE */
#define IFX_PSI5_CH_PGC_PTE_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.PTE */
#define IFX_PSI5_CH_PGC_PTE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.PTE */
#define IFX_PSI5_CH_PGC_PTE_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_PGC_Bits.TBS */
#define IFX_PSI5_CH_PGC_TBS_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_PGC_Bits.TBS */
#define IFX_PSI5_CH_PGC_TBS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_PGC_Bits.TBS */
#define IFX_PSI5_CH_PGC_TBS_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.ASYN */
#define IFX_PSI5_CH_RCRA_ASYN_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.ASYN */
#define IFX_PSI5_CH_RCRA_ASYN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.ASYN */
#define IFX_PSI5_CH_RCRA_ASYN_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.AVBS */
#define IFX_PSI5_CH_RCRA_AVBS_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.AVBS */
#define IFX_PSI5_CH_RCRA_AVBS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.AVBS */
#define IFX_PSI5_CH_RCRA_AVBS_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.PDL0 */
#define IFX_PSI5_CH_RCRA_PDL0_LEN (5u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.PDL0 */
#define IFX_PSI5_CH_RCRA_PDL0_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.PDL0 */
#define IFX_PSI5_CH_RCRA_PDL0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.PDL1 */
#define IFX_PSI5_CH_RCRA_PDL1_LEN (5u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.PDL1 */
#define IFX_PSI5_CH_RCRA_PDL1_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.PDL1 */
#define IFX_PSI5_CH_RCRA_PDL1_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.PDL2 */
#define IFX_PSI5_CH_RCRA_PDL2_LEN (5u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.PDL2 */
#define IFX_PSI5_CH_RCRA_PDL2_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.PDL2 */
#define IFX_PSI5_CH_RCRA_PDL2_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.PDL3 */
#define IFX_PSI5_CH_RCRA_PDL3_LEN (5u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.PDL3 */
#define IFX_PSI5_CH_RCRA_PDL3_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.PDL3 */
#define IFX_PSI5_CH_RCRA_PDL3_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.PDL4 */
#define IFX_PSI5_CH_RCRA_PDL4_LEN (5u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.PDL4 */
#define IFX_PSI5_CH_RCRA_PDL4_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.PDL4 */
#define IFX_PSI5_CH_RCRA_PDL4_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_RCRA_Bits.PDL5 */
#define IFX_PSI5_CH_RCRA_PDL5_LEN (5u)

/** \brief  Mask for Ifx_PSI5_CH_RCRA_Bits.PDL5 */
#define IFX_PSI5_CH_RCRA_PDL5_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5_CH_RCRA_Bits.PDL5 */
#define IFX_PSI5_CH_RCRA_PDL5_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.CRC0 */
#define IFX_PSI5_CH_RCRB_CRC0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.CRC0 */
#define IFX_PSI5_CH_RCRB_CRC0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.CRC0 */
#define IFX_PSI5_CH_RCRB_CRC0_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.CRC1 */
#define IFX_PSI5_CH_RCRB_CRC1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.CRC1 */
#define IFX_PSI5_CH_RCRB_CRC1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.CRC1 */
#define IFX_PSI5_CH_RCRB_CRC1_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.CRC2 */
#define IFX_PSI5_CH_RCRB_CRC2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.CRC2 */
#define IFX_PSI5_CH_RCRB_CRC2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.CRC2 */
#define IFX_PSI5_CH_RCRB_CRC2_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.CRC3 */
#define IFX_PSI5_CH_RCRB_CRC3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.CRC3 */
#define IFX_PSI5_CH_RCRB_CRC3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.CRC3 */
#define IFX_PSI5_CH_RCRB_CRC3_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.CRC4 */
#define IFX_PSI5_CH_RCRB_CRC4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.CRC4 */
#define IFX_PSI5_CH_RCRB_CRC4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.CRC4 */
#define IFX_PSI5_CH_RCRB_CRC4_OFF (17u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.CRC5 */
#define IFX_PSI5_CH_RCRB_CRC5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.CRC5 */
#define IFX_PSI5_CH_RCRB_CRC5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.CRC5 */
#define IFX_PSI5_CH_RCRB_CRC5_OFF (21u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.FEC0 */
#define IFX_PSI5_CH_RCRB_FEC0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.FEC0 */
#define IFX_PSI5_CH_RCRB_FEC0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.FEC0 */
#define IFX_PSI5_CH_RCRB_FEC0_OFF (2u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.FEC1 */
#define IFX_PSI5_CH_RCRB_FEC1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.FEC1 */
#define IFX_PSI5_CH_RCRB_FEC1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.FEC1 */
#define IFX_PSI5_CH_RCRB_FEC1_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.FEC2 */
#define IFX_PSI5_CH_RCRB_FEC2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.FEC2 */
#define IFX_PSI5_CH_RCRB_FEC2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.FEC2 */
#define IFX_PSI5_CH_RCRB_FEC2_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.FEC3 */
#define IFX_PSI5_CH_RCRB_FEC3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.FEC3 */
#define IFX_PSI5_CH_RCRB_FEC3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.FEC3 */
#define IFX_PSI5_CH_RCRB_FEC3_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.FEC4 */
#define IFX_PSI5_CH_RCRB_FEC4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.FEC4 */
#define IFX_PSI5_CH_RCRB_FEC4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.FEC4 */
#define IFX_PSI5_CH_RCRB_FEC4_OFF (18u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.FEC5 */
#define IFX_PSI5_CH_RCRB_FEC5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.FEC5 */
#define IFX_PSI5_CH_RCRB_FEC5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.FEC5 */
#define IFX_PSI5_CH_RCRB_FEC5_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.MSG0 */
#define IFX_PSI5_CH_RCRB_MSG0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.MSG0 */
#define IFX_PSI5_CH_RCRB_MSG0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.MSG0 */
#define IFX_PSI5_CH_RCRB_MSG0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.MSG1 */
#define IFX_PSI5_CH_RCRB_MSG1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.MSG1 */
#define IFX_PSI5_CH_RCRB_MSG1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.MSG1 */
#define IFX_PSI5_CH_RCRB_MSG1_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.MSG2 */
#define IFX_PSI5_CH_RCRB_MSG2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.MSG2 */
#define IFX_PSI5_CH_RCRB_MSG2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.MSG2 */
#define IFX_PSI5_CH_RCRB_MSG2_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.MSG3 */
#define IFX_PSI5_CH_RCRB_MSG3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.MSG3 */
#define IFX_PSI5_CH_RCRB_MSG3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.MSG3 */
#define IFX_PSI5_CH_RCRB_MSG3_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.MSG4 */
#define IFX_PSI5_CH_RCRB_MSG4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.MSG4 */
#define IFX_PSI5_CH_RCRB_MSG4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.MSG4 */
#define IFX_PSI5_CH_RCRB_MSG4_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.MSG5 */
#define IFX_PSI5_CH_RCRB_MSG5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.MSG5 */
#define IFX_PSI5_CH_RCRB_MSG5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.MSG5 */
#define IFX_PSI5_CH_RCRB_MSG5_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.VBS0 */
#define IFX_PSI5_CH_RCRB_VBS0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.VBS0 */
#define IFX_PSI5_CH_RCRB_VBS0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.VBS0 */
#define IFX_PSI5_CH_RCRB_VBS0_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.VBS1 */
#define IFX_PSI5_CH_RCRB_VBS1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.VBS1 */
#define IFX_PSI5_CH_RCRB_VBS1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.VBS1 */
#define IFX_PSI5_CH_RCRB_VBS1_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.VBS2 */
#define IFX_PSI5_CH_RCRB_VBS2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.VBS2 */
#define IFX_PSI5_CH_RCRB_VBS2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.VBS2 */
#define IFX_PSI5_CH_RCRB_VBS2_OFF (11u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.VBS3 */
#define IFX_PSI5_CH_RCRB_VBS3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.VBS3 */
#define IFX_PSI5_CH_RCRB_VBS3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.VBS3 */
#define IFX_PSI5_CH_RCRB_VBS3_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.VBS4 */
#define IFX_PSI5_CH_RCRB_VBS4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.VBS4 */
#define IFX_PSI5_CH_RCRB_VBS4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.VBS4 */
#define IFX_PSI5_CH_RCRB_VBS4_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_RCRB_Bits.VBS5 */
#define IFX_PSI5_CH_RCRB_VBS5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRB_Bits.VBS5 */
#define IFX_PSI5_CH_RCRB_VBS5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRB_Bits.VBS5 */
#define IFX_PSI5_CH_RCRB_VBS5_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_RCRC_Bits.BRS */
#define IFX_PSI5_CH_RCRC_BRS_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRC_Bits.BRS */
#define IFX_PSI5_CH_RCRC_BRS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRC_Bits.BRS */
#define IFX_PSI5_CH_RCRC_BRS_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_RCRC_Bits.TSF */
#define IFX_PSI5_CH_RCRC_TSF_LEN (2u)

/** \brief  Mask for Ifx_PSI5_CH_RCRC_Bits.TSF */
#define IFX_PSI5_CH_RCRC_TSF_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_CH_RCRC_Bits.TSF */
#define IFX_PSI5_CH_RCRC_TSF_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_RCRC_Bits.TSP */
#define IFX_PSI5_CH_RCRC_TSP_LEN (2u)

/** \brief  Mask for Ifx_PSI5_CH_RCRC_Bits.TSP */
#define IFX_PSI5_CH_RCRC_TSP_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_CH_RCRC_Bits.TSP */
#define IFX_PSI5_CH_RCRC_TSP_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_RCRC_Bits.TSR */
#define IFX_PSI5_CH_RCRC_TSR_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RCRC_Bits.TSR */
#define IFX_PSI5_CH_RCRC_TSR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RCRC_Bits.TSR */
#define IFX_PSI5_CH_RCRC_TSR_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_RDRH_Bits.MEI */
#define IFX_PSI5_CH_RDRH_MEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RDRH_Bits.MEI */
#define IFX_PSI5_CH_RDRH_MEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RDRH_Bits.MEI */
#define IFX_PSI5_CH_RDRH_MEI_OFF (29u)

/** \brief  Length for Ifx_PSI5_CH_RDRH_Bits.NBI */
#define IFX_PSI5_CH_RDRH_NBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RDRH_Bits.NBI */
#define IFX_PSI5_CH_RDRH_NBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RDRH_Bits.NBI */
#define IFX_PSI5_CH_RDRH_NBI_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_RDRH_Bits.NFI */
#define IFX_PSI5_CH_RDRH_NFI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RDRH_Bits.NFI */
#define IFX_PSI5_CH_RDRH_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RDRH_Bits.NFI */
#define IFX_PSI5_CH_RDRH_NFI_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_RDRH_Bits.RBI */
#define IFX_PSI5_CH_RDRH_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RDRH_Bits.RBI */
#define IFX_PSI5_CH_RDRH_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RDRH_Bits.RBI */
#define IFX_PSI5_CH_RDRH_RBI_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_RDRH_Bits.SC */
#define IFX_PSI5_CH_RDRH_SC_LEN (3u)

/** \brief  Mask for Ifx_PSI5_CH_RDRH_Bits.SC */
#define IFX_PSI5_CH_RDRH_SC_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_CH_RDRH_Bits.SC */
#define IFX_PSI5_CH_RDRH_SC_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_RDRH_Bits.TEI */
#define IFX_PSI5_CH_RDRH_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RDRH_Bits.TEI */
#define IFX_PSI5_CH_RDRH_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RDRH_Bits.TEI */
#define IFX_PSI5_CH_RDRH_TEI_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_RDRH_Bits.TS */
#define IFX_PSI5_CH_RDRH_TS_LEN (24u)

/** \brief  Mask for Ifx_PSI5_CH_RDRH_Bits.TS */
#define IFX_PSI5_CH_RDRH_TS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5_CH_RDRH_Bits.TS */
#define IFX_PSI5_CH_RDRH_TS_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_RDRL_Bits.CRC */
#define IFX_PSI5_CH_RDRL_CRC_LEN (3u)

/** \brief  Mask for Ifx_PSI5_CH_RDRL_Bits.CRC */
#define IFX_PSI5_CH_RDRL_CRC_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_CH_RDRL_Bits.CRC */
#define IFX_PSI5_CH_RDRL_CRC_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_RDRL_Bits.CRCI */
#define IFX_PSI5_CH_RDRL_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_RDRL_Bits.CRCI */
#define IFX_PSI5_CH_RDRL_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_RDRL_Bits.CRCI */
#define IFX_PSI5_CH_RDRL_CRCI_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_RDRL_Bits.RD */
#define IFX_PSI5_CH_RDRL_RD_LEN (28u)

/** \brief  Mask for Ifx_PSI5_CH_RDRL_Bits.RD */
#define IFX_PSI5_CH_RDRL_RD_MSK (0xfffffffu)

/** \brief  Offset for Ifx_PSI5_CH_RDRL_Bits.RD */
#define IFX_PSI5_CH_RDRL_RD_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_RSR_Bits.CRC */
#define IFX_PSI5_CH_RSR_CRC_LEN (3u)

/** \brief  Mask for Ifx_PSI5_CH_RSR_Bits.CRC */
#define IFX_PSI5_CH_RSR_CRC_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_CH_RSR_Bits.CRC */
#define IFX_PSI5_CH_RSR_CRC_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_RSR_Bits.MSG */
#define IFX_PSI5_CH_RSR_MSG_LEN (2u)

/** \brief  Mask for Ifx_PSI5_CH_RSR_Bits.MSG */
#define IFX_PSI5_CH_RSR_MSG_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_CH_RSR_Bits.MSG */
#define IFX_PSI5_CH_RSR_MSG_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.BSC */
#define IFX_PSI5_CH_SCR_BSC_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.BSC */
#define IFX_PSI5_CH_SCR_BSC_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.BSC */
#define IFX_PSI5_CH_SCR_BSC_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.CRC */
#define IFX_PSI5_CH_SCR_CRC_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.CRC */
#define IFX_PSI5_CH_SCR_CRC_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.CRC */
#define IFX_PSI5_CH_SCR_CRC_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.EPS */
#define IFX_PSI5_CH_SCR_EPS_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.EPS */
#define IFX_PSI5_CH_SCR_EPS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.EPS */
#define IFX_PSI5_CH_SCR_EPS_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.FLUO */
#define IFX_PSI5_CH_SCR_FLUO_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.FLUO */
#define IFX_PSI5_CH_SCR_FLUO_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.FLUO */
#define IFX_PSI5_CH_SCR_FLUO_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.FLUS */
#define IFX_PSI5_CH_SCR_FLUS_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.FLUS */
#define IFX_PSI5_CH_SCR_FLUS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.FLUS */
#define IFX_PSI5_CH_SCR_FLUS_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.GO */
#define IFX_PSI5_CH_SCR_GO_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.GO */
#define IFX_PSI5_CH_SCR_GO_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.GO */
#define IFX_PSI5_CH_SCR_GO_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.INH */
#define IFX_PSI5_CH_SCR_INH_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.INH */
#define IFX_PSI5_CH_SCR_INH_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.INH */
#define IFX_PSI5_CH_SCR_INH_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.PLL */
#define IFX_PSI5_CH_SCR_PLL_LEN (6u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.PLL */
#define IFX_PSI5_CH_SCR_PLL_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.PLL */
#define IFX_PSI5_CH_SCR_PLL_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.SOL */
#define IFX_PSI5_CH_SCR_SOL_LEN (6u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.SOL */
#define IFX_PSI5_CH_SCR_SOL_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.SOL */
#define IFX_PSI5_CH_SCR_SOL_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.SSL */
#define IFX_PSI5_CH_SCR_SSL_LEN (6u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.SSL */
#define IFX_PSI5_CH_SCR_SSL_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.SSL */
#define IFX_PSI5_CH_SCR_SSL_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.STA */
#define IFX_PSI5_CH_SCR_STA_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.STA */
#define IFX_PSI5_CH_SCR_STA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.STA */
#define IFX_PSI5_CH_SCR_STA_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.TOF */
#define IFX_PSI5_CH_SCR_TOF_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.TOF */
#define IFX_PSI5_CH_SCR_TOF_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.TOF */
#define IFX_PSI5_CH_SCR_TOF_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.TPF */
#define IFX_PSI5_CH_SCR_TPF_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.TPF */
#define IFX_PSI5_CH_SCR_TPF_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.TPF */
#define IFX_PSI5_CH_SCR_TPF_OFF (26u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.TRQ */
#define IFX_PSI5_CH_SCR_TRQ_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.TRQ */
#define IFX_PSI5_CH_SCR_TRQ_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.TRQ */
#define IFX_PSI5_CH_SCR_TRQ_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SCR_Bits.TSF */
#define IFX_PSI5_CH_SCR_TSF_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SCR_Bits.TSF */
#define IFX_PSI5_CH_SCR_TSF_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SCR_Bits.TSF */
#define IFX_PSI5_CH_SCR_TSF_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD32 */
#define IFX_PSI5_CH_SDRH_SD32_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD32 */
#define IFX_PSI5_CH_SDRH_SD32_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD32 */
#define IFX_PSI5_CH_SDRH_SD32_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD33 */
#define IFX_PSI5_CH_SDRH_SD33_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD33 */
#define IFX_PSI5_CH_SDRH_SD33_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD33 */
#define IFX_PSI5_CH_SDRH_SD33_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD34 */
#define IFX_PSI5_CH_SDRH_SD34_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD34 */
#define IFX_PSI5_CH_SDRH_SD34_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD34 */
#define IFX_PSI5_CH_SDRH_SD34_OFF (2u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD35 */
#define IFX_PSI5_CH_SDRH_SD35_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD35 */
#define IFX_PSI5_CH_SDRH_SD35_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD35 */
#define IFX_PSI5_CH_SDRH_SD35_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD36 */
#define IFX_PSI5_CH_SDRH_SD36_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD36 */
#define IFX_PSI5_CH_SDRH_SD36_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD36 */
#define IFX_PSI5_CH_SDRH_SD36_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD37 */
#define IFX_PSI5_CH_SDRH_SD37_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD37 */
#define IFX_PSI5_CH_SDRH_SD37_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD37 */
#define IFX_PSI5_CH_SDRH_SD37_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD38 */
#define IFX_PSI5_CH_SDRH_SD38_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD38 */
#define IFX_PSI5_CH_SDRH_SD38_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD38 */
#define IFX_PSI5_CH_SDRH_SD38_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD39 */
#define IFX_PSI5_CH_SDRH_SD39_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD39 */
#define IFX_PSI5_CH_SDRH_SD39_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD39 */
#define IFX_PSI5_CH_SDRH_SD39_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD40 */
#define IFX_PSI5_CH_SDRH_SD40_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD40 */
#define IFX_PSI5_CH_SDRH_SD40_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD40 */
#define IFX_PSI5_CH_SDRH_SD40_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD41 */
#define IFX_PSI5_CH_SDRH_SD41_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD41 */
#define IFX_PSI5_CH_SDRH_SD41_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD41 */
#define IFX_PSI5_CH_SDRH_SD41_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD42 */
#define IFX_PSI5_CH_SDRH_SD42_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD42 */
#define IFX_PSI5_CH_SDRH_SD42_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD42 */
#define IFX_PSI5_CH_SDRH_SD42_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD43 */
#define IFX_PSI5_CH_SDRH_SD43_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD43 */
#define IFX_PSI5_CH_SDRH_SD43_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD43 */
#define IFX_PSI5_CH_SDRH_SD43_OFF (11u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD44 */
#define IFX_PSI5_CH_SDRH_SD44_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD44 */
#define IFX_PSI5_CH_SDRH_SD44_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD44 */
#define IFX_PSI5_CH_SDRH_SD44_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD45 */
#define IFX_PSI5_CH_SDRH_SD45_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD45 */
#define IFX_PSI5_CH_SDRH_SD45_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD45 */
#define IFX_PSI5_CH_SDRH_SD45_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD46 */
#define IFX_PSI5_CH_SDRH_SD46_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD46 */
#define IFX_PSI5_CH_SDRH_SD46_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD46 */
#define IFX_PSI5_CH_SDRH_SD46_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD47 */
#define IFX_PSI5_CH_SDRH_SD47_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD47 */
#define IFX_PSI5_CH_SDRH_SD47_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD47 */
#define IFX_PSI5_CH_SDRH_SD47_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD48 */
#define IFX_PSI5_CH_SDRH_SD48_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD48 */
#define IFX_PSI5_CH_SDRH_SD48_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD48 */
#define IFX_PSI5_CH_SDRH_SD48_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD49 */
#define IFX_PSI5_CH_SDRH_SD49_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD49 */
#define IFX_PSI5_CH_SDRH_SD49_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD49 */
#define IFX_PSI5_CH_SDRH_SD49_OFF (17u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD50 */
#define IFX_PSI5_CH_SDRH_SD50_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD50 */
#define IFX_PSI5_CH_SDRH_SD50_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD50 */
#define IFX_PSI5_CH_SDRH_SD50_OFF (18u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD51 */
#define IFX_PSI5_CH_SDRH_SD51_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD51 */
#define IFX_PSI5_CH_SDRH_SD51_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD51 */
#define IFX_PSI5_CH_SDRH_SD51_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD52 */
#define IFX_PSI5_CH_SDRH_SD52_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD52 */
#define IFX_PSI5_CH_SDRH_SD52_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD52 */
#define IFX_PSI5_CH_SDRH_SD52_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD53 */
#define IFX_PSI5_CH_SDRH_SD53_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD53 */
#define IFX_PSI5_CH_SDRH_SD53_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD53 */
#define IFX_PSI5_CH_SDRH_SD53_OFF (21u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD54 */
#define IFX_PSI5_CH_SDRH_SD54_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD54 */
#define IFX_PSI5_CH_SDRH_SD54_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD54 */
#define IFX_PSI5_CH_SDRH_SD54_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD55 */
#define IFX_PSI5_CH_SDRH_SD55_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD55 */
#define IFX_PSI5_CH_SDRH_SD55_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD55 */
#define IFX_PSI5_CH_SDRH_SD55_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD56 */
#define IFX_PSI5_CH_SDRH_SD56_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD56 */
#define IFX_PSI5_CH_SDRH_SD56_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD56 */
#define IFX_PSI5_CH_SDRH_SD56_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD57 */
#define IFX_PSI5_CH_SDRH_SD57_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD57 */
#define IFX_PSI5_CH_SDRH_SD57_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD57 */
#define IFX_PSI5_CH_SDRH_SD57_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD58 */
#define IFX_PSI5_CH_SDRH_SD58_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD58 */
#define IFX_PSI5_CH_SDRH_SD58_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD58 */
#define IFX_PSI5_CH_SDRH_SD58_OFF (26u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD59 */
#define IFX_PSI5_CH_SDRH_SD59_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD59 */
#define IFX_PSI5_CH_SDRH_SD59_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD59 */
#define IFX_PSI5_CH_SDRH_SD59_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD60 */
#define IFX_PSI5_CH_SDRH_SD60_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD60 */
#define IFX_PSI5_CH_SDRH_SD60_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD60 */
#define IFX_PSI5_CH_SDRH_SD60_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD61 */
#define IFX_PSI5_CH_SDRH_SD61_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD61 */
#define IFX_PSI5_CH_SDRH_SD61_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD61 */
#define IFX_PSI5_CH_SDRH_SD61_OFF (29u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD62 */
#define IFX_PSI5_CH_SDRH_SD62_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD62 */
#define IFX_PSI5_CH_SDRH_SD62_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD62 */
#define IFX_PSI5_CH_SDRH_SD62_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_SDRH_Bits.SD63 */
#define IFX_PSI5_CH_SDRH_SD63_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRH_Bits.SD63 */
#define IFX_PSI5_CH_SDRH_SD63_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRH_Bits.SD63 */
#define IFX_PSI5_CH_SDRH_SD63_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD0 */
#define IFX_PSI5_CH_SDRL_SD0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD0 */
#define IFX_PSI5_CH_SDRL_SD0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD0 */
#define IFX_PSI5_CH_SDRL_SD0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD10 */
#define IFX_PSI5_CH_SDRL_SD10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD10 */
#define IFX_PSI5_CH_SDRL_SD10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD10 */
#define IFX_PSI5_CH_SDRL_SD10_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD11 */
#define IFX_PSI5_CH_SDRL_SD11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD11 */
#define IFX_PSI5_CH_SDRL_SD11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD11 */
#define IFX_PSI5_CH_SDRL_SD11_OFF (11u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD12 */
#define IFX_PSI5_CH_SDRL_SD12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD12 */
#define IFX_PSI5_CH_SDRL_SD12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD12 */
#define IFX_PSI5_CH_SDRL_SD12_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD13 */
#define IFX_PSI5_CH_SDRL_SD13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD13 */
#define IFX_PSI5_CH_SDRL_SD13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD13 */
#define IFX_PSI5_CH_SDRL_SD13_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD14 */
#define IFX_PSI5_CH_SDRL_SD14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD14 */
#define IFX_PSI5_CH_SDRL_SD14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD14 */
#define IFX_PSI5_CH_SDRL_SD14_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD15 */
#define IFX_PSI5_CH_SDRL_SD15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD15 */
#define IFX_PSI5_CH_SDRL_SD15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD15 */
#define IFX_PSI5_CH_SDRL_SD15_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD16 */
#define IFX_PSI5_CH_SDRL_SD16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD16 */
#define IFX_PSI5_CH_SDRL_SD16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD16 */
#define IFX_PSI5_CH_SDRL_SD16_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD17 */
#define IFX_PSI5_CH_SDRL_SD17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD17 */
#define IFX_PSI5_CH_SDRL_SD17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD17 */
#define IFX_PSI5_CH_SDRL_SD17_OFF (17u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD18 */
#define IFX_PSI5_CH_SDRL_SD18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD18 */
#define IFX_PSI5_CH_SDRL_SD18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD18 */
#define IFX_PSI5_CH_SDRL_SD18_OFF (18u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD19 */
#define IFX_PSI5_CH_SDRL_SD19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD19 */
#define IFX_PSI5_CH_SDRL_SD19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD19 */
#define IFX_PSI5_CH_SDRL_SD19_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD1 */
#define IFX_PSI5_CH_SDRL_SD1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD1 */
#define IFX_PSI5_CH_SDRL_SD1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD1 */
#define IFX_PSI5_CH_SDRL_SD1_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD20 */
#define IFX_PSI5_CH_SDRL_SD20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD20 */
#define IFX_PSI5_CH_SDRL_SD20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD20 */
#define IFX_PSI5_CH_SDRL_SD20_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD21 */
#define IFX_PSI5_CH_SDRL_SD21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD21 */
#define IFX_PSI5_CH_SDRL_SD21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD21 */
#define IFX_PSI5_CH_SDRL_SD21_OFF (21u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD22 */
#define IFX_PSI5_CH_SDRL_SD22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD22 */
#define IFX_PSI5_CH_SDRL_SD22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD22 */
#define IFX_PSI5_CH_SDRL_SD22_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD23 */
#define IFX_PSI5_CH_SDRL_SD23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD23 */
#define IFX_PSI5_CH_SDRL_SD23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD23 */
#define IFX_PSI5_CH_SDRL_SD23_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD24 */
#define IFX_PSI5_CH_SDRL_SD24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD24 */
#define IFX_PSI5_CH_SDRL_SD24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD24 */
#define IFX_PSI5_CH_SDRL_SD24_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD25 */
#define IFX_PSI5_CH_SDRL_SD25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD25 */
#define IFX_PSI5_CH_SDRL_SD25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD25 */
#define IFX_PSI5_CH_SDRL_SD25_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD26 */
#define IFX_PSI5_CH_SDRL_SD26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD26 */
#define IFX_PSI5_CH_SDRL_SD26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD26 */
#define IFX_PSI5_CH_SDRL_SD26_OFF (26u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD27 */
#define IFX_PSI5_CH_SDRL_SD27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD27 */
#define IFX_PSI5_CH_SDRL_SD27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD27 */
#define IFX_PSI5_CH_SDRL_SD27_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD28 */
#define IFX_PSI5_CH_SDRL_SD28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD28 */
#define IFX_PSI5_CH_SDRL_SD28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD28 */
#define IFX_PSI5_CH_SDRL_SD28_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD29 */
#define IFX_PSI5_CH_SDRL_SD29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD29 */
#define IFX_PSI5_CH_SDRL_SD29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD29 */
#define IFX_PSI5_CH_SDRL_SD29_OFF (29u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD2 */
#define IFX_PSI5_CH_SDRL_SD2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD2 */
#define IFX_PSI5_CH_SDRL_SD2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD2 */
#define IFX_PSI5_CH_SDRL_SD2_OFF (2u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD30 */
#define IFX_PSI5_CH_SDRL_SD30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD30 */
#define IFX_PSI5_CH_SDRL_SD30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD30 */
#define IFX_PSI5_CH_SDRL_SD30_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD31 */
#define IFX_PSI5_CH_SDRL_SD31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD31 */
#define IFX_PSI5_CH_SDRL_SD31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD31 */
#define IFX_PSI5_CH_SDRL_SD31_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD3 */
#define IFX_PSI5_CH_SDRL_SD3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD3 */
#define IFX_PSI5_CH_SDRL_SD3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD3 */
#define IFX_PSI5_CH_SDRL_SD3_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD4 */
#define IFX_PSI5_CH_SDRL_SD4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD4 */
#define IFX_PSI5_CH_SDRL_SD4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD4 */
#define IFX_PSI5_CH_SDRL_SD4_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD5 */
#define IFX_PSI5_CH_SDRL_SD5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD5 */
#define IFX_PSI5_CH_SDRL_SD5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD5 */
#define IFX_PSI5_CH_SDRL_SD5_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD6 */
#define IFX_PSI5_CH_SDRL_SD6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD6 */
#define IFX_PSI5_CH_SDRL_SD6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD6 */
#define IFX_PSI5_CH_SDRL_SD6_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD7 */
#define IFX_PSI5_CH_SDRL_SD7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD7 */
#define IFX_PSI5_CH_SDRL_SD7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD7 */
#define IFX_PSI5_CH_SDRL_SD7_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD8 */
#define IFX_PSI5_CH_SDRL_SD8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD8 */
#define IFX_PSI5_CH_SDRL_SD8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD8 */
#define IFX_PSI5_CH_SDRL_SD8_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SDRL_Bits.SD9 */
#define IFX_PSI5_CH_SDRL_SD9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDRL_Bits.SD9 */
#define IFX_PSI5_CH_SDRL_SD9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDRL_Bits.SD9 */
#define IFX_PSI5_CH_SDRL_SD9_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_SDS_Bits.CON */
#define IFX_PSI5_CH_SDS_CON_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDS_Bits.CON */
#define IFX_PSI5_CH_SDS_CON_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDS_Bits.CON */
#define IFX_PSI5_CH_SDS_CON_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SDS_Bits.MID */
#define IFX_PSI5_CH_SDS_MID_LEN (8u)

/** \brief  Mask for Ifx_PSI5_CH_SDS_Bits.MID */
#define IFX_PSI5_CH_SDS_MID_MSK (0xffu)

/** \brief  Offset for Ifx_PSI5_CH_SDS_Bits.MID */
#define IFX_PSI5_CH_SDS_MID_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SDS_Bits.SCRC */
#define IFX_PSI5_CH_SDS_SCRC_LEN (6u)

/** \brief  Mask for Ifx_PSI5_CH_SDS_Bits.SCRC */
#define IFX_PSI5_CH_SDS_SCRC_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_CH_SDS_Bits.SCRC */
#define IFX_PSI5_CH_SDS_SCRC_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SDS_Bits.SCRI */
#define IFX_PSI5_CH_SDS_SCRI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SDS_Bits.SCRI */
#define IFX_PSI5_CH_SDS_SCRI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SDS_Bits.SCRI */
#define IFX_PSI5_CH_SDS_SCRI_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_SDS_Bits.SD */
#define IFX_PSI5_CH_SDS_SD_LEN (16u)

/** \brief  Mask for Ifx_PSI5_CH_SDS_Bits.SD */
#define IFX_PSI5_CH_SDS_SD_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5_CH_SDS_Bits.SD */
#define IFX_PSI5_CH_SDS_SD_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SFTSC_Bits.TS */
#define IFX_PSI5_CH_SFTSC_TS_LEN (24u)

/** \brief  Mask for Ifx_PSI5_CH_SFTSC_Bits.TS */
#define IFX_PSI5_CH_SFTSC_TS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5_CH_SFTSC_Bits.TS */
#define IFX_PSI5_CH_SFTSC_TS_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD32 */
#define IFX_PSI5_CH_SORH_SD32_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD32 */
#define IFX_PSI5_CH_SORH_SD32_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD32 */
#define IFX_PSI5_CH_SORH_SD32_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD33 */
#define IFX_PSI5_CH_SORH_SD33_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD33 */
#define IFX_PSI5_CH_SORH_SD33_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD33 */
#define IFX_PSI5_CH_SORH_SD33_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD34 */
#define IFX_PSI5_CH_SORH_SD34_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD34 */
#define IFX_PSI5_CH_SORH_SD34_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD34 */
#define IFX_PSI5_CH_SORH_SD34_OFF (2u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD35 */
#define IFX_PSI5_CH_SORH_SD35_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD35 */
#define IFX_PSI5_CH_SORH_SD35_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD35 */
#define IFX_PSI5_CH_SORH_SD35_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD36 */
#define IFX_PSI5_CH_SORH_SD36_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD36 */
#define IFX_PSI5_CH_SORH_SD36_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD36 */
#define IFX_PSI5_CH_SORH_SD36_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD37 */
#define IFX_PSI5_CH_SORH_SD37_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD37 */
#define IFX_PSI5_CH_SORH_SD37_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD37 */
#define IFX_PSI5_CH_SORH_SD37_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD38 */
#define IFX_PSI5_CH_SORH_SD38_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD38 */
#define IFX_PSI5_CH_SORH_SD38_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD38 */
#define IFX_PSI5_CH_SORH_SD38_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD39 */
#define IFX_PSI5_CH_SORH_SD39_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD39 */
#define IFX_PSI5_CH_SORH_SD39_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD39 */
#define IFX_PSI5_CH_SORH_SD39_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD40 */
#define IFX_PSI5_CH_SORH_SD40_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD40 */
#define IFX_PSI5_CH_SORH_SD40_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD40 */
#define IFX_PSI5_CH_SORH_SD40_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD41 */
#define IFX_PSI5_CH_SORH_SD41_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD41 */
#define IFX_PSI5_CH_SORH_SD41_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD41 */
#define IFX_PSI5_CH_SORH_SD41_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD42 */
#define IFX_PSI5_CH_SORH_SD42_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD42 */
#define IFX_PSI5_CH_SORH_SD42_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD42 */
#define IFX_PSI5_CH_SORH_SD42_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD43 */
#define IFX_PSI5_CH_SORH_SD43_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD43 */
#define IFX_PSI5_CH_SORH_SD43_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD43 */
#define IFX_PSI5_CH_SORH_SD43_OFF (11u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD44 */
#define IFX_PSI5_CH_SORH_SD44_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD44 */
#define IFX_PSI5_CH_SORH_SD44_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD44 */
#define IFX_PSI5_CH_SORH_SD44_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD45 */
#define IFX_PSI5_CH_SORH_SD45_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD45 */
#define IFX_PSI5_CH_SORH_SD45_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD45 */
#define IFX_PSI5_CH_SORH_SD45_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD46 */
#define IFX_PSI5_CH_SORH_SD46_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD46 */
#define IFX_PSI5_CH_SORH_SD46_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD46 */
#define IFX_PSI5_CH_SORH_SD46_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD47 */
#define IFX_PSI5_CH_SORH_SD47_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD47 */
#define IFX_PSI5_CH_SORH_SD47_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD47 */
#define IFX_PSI5_CH_SORH_SD47_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD48 */
#define IFX_PSI5_CH_SORH_SD48_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD48 */
#define IFX_PSI5_CH_SORH_SD48_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD48 */
#define IFX_PSI5_CH_SORH_SD48_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD49 */
#define IFX_PSI5_CH_SORH_SD49_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD49 */
#define IFX_PSI5_CH_SORH_SD49_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD49 */
#define IFX_PSI5_CH_SORH_SD49_OFF (17u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD50 */
#define IFX_PSI5_CH_SORH_SD50_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD50 */
#define IFX_PSI5_CH_SORH_SD50_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD50 */
#define IFX_PSI5_CH_SORH_SD50_OFF (18u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD51 */
#define IFX_PSI5_CH_SORH_SD51_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD51 */
#define IFX_PSI5_CH_SORH_SD51_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD51 */
#define IFX_PSI5_CH_SORH_SD51_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD52 */
#define IFX_PSI5_CH_SORH_SD52_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD52 */
#define IFX_PSI5_CH_SORH_SD52_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD52 */
#define IFX_PSI5_CH_SORH_SD52_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD53 */
#define IFX_PSI5_CH_SORH_SD53_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD53 */
#define IFX_PSI5_CH_SORH_SD53_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD53 */
#define IFX_PSI5_CH_SORH_SD53_OFF (21u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD54 */
#define IFX_PSI5_CH_SORH_SD54_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD54 */
#define IFX_PSI5_CH_SORH_SD54_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD54 */
#define IFX_PSI5_CH_SORH_SD54_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD55 */
#define IFX_PSI5_CH_SORH_SD55_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD55 */
#define IFX_PSI5_CH_SORH_SD55_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD55 */
#define IFX_PSI5_CH_SORH_SD55_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD56 */
#define IFX_PSI5_CH_SORH_SD56_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD56 */
#define IFX_PSI5_CH_SORH_SD56_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD56 */
#define IFX_PSI5_CH_SORH_SD56_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD57 */
#define IFX_PSI5_CH_SORH_SD57_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD57 */
#define IFX_PSI5_CH_SORH_SD57_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD57 */
#define IFX_PSI5_CH_SORH_SD57_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD58 */
#define IFX_PSI5_CH_SORH_SD58_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD58 */
#define IFX_PSI5_CH_SORH_SD58_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD58 */
#define IFX_PSI5_CH_SORH_SD58_OFF (26u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD59 */
#define IFX_PSI5_CH_SORH_SD59_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD59 */
#define IFX_PSI5_CH_SORH_SD59_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD59 */
#define IFX_PSI5_CH_SORH_SD59_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD60 */
#define IFX_PSI5_CH_SORH_SD60_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD60 */
#define IFX_PSI5_CH_SORH_SD60_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD60 */
#define IFX_PSI5_CH_SORH_SD60_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD61 */
#define IFX_PSI5_CH_SORH_SD61_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD61 */
#define IFX_PSI5_CH_SORH_SD61_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD61 */
#define IFX_PSI5_CH_SORH_SD61_OFF (29u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD62 */
#define IFX_PSI5_CH_SORH_SD62_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD62 */
#define IFX_PSI5_CH_SORH_SD62_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD62 */
#define IFX_PSI5_CH_SORH_SD62_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_SORH_Bits.SD63 */
#define IFX_PSI5_CH_SORH_SD63_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORH_Bits.SD63 */
#define IFX_PSI5_CH_SORH_SD63_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORH_Bits.SD63 */
#define IFX_PSI5_CH_SORH_SD63_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD0 */
#define IFX_PSI5_CH_SORL_SD0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD0 */
#define IFX_PSI5_CH_SORL_SD0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD0 */
#define IFX_PSI5_CH_SORL_SD0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD10 */
#define IFX_PSI5_CH_SORL_SD10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD10 */
#define IFX_PSI5_CH_SORL_SD10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD10 */
#define IFX_PSI5_CH_SORL_SD10_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD11 */
#define IFX_PSI5_CH_SORL_SD11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD11 */
#define IFX_PSI5_CH_SORL_SD11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD11 */
#define IFX_PSI5_CH_SORL_SD11_OFF (11u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD12 */
#define IFX_PSI5_CH_SORL_SD12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD12 */
#define IFX_PSI5_CH_SORL_SD12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD12 */
#define IFX_PSI5_CH_SORL_SD12_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD13 */
#define IFX_PSI5_CH_SORL_SD13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD13 */
#define IFX_PSI5_CH_SORL_SD13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD13 */
#define IFX_PSI5_CH_SORL_SD13_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD14 */
#define IFX_PSI5_CH_SORL_SD14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD14 */
#define IFX_PSI5_CH_SORL_SD14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD14 */
#define IFX_PSI5_CH_SORL_SD14_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD15 */
#define IFX_PSI5_CH_SORL_SD15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD15 */
#define IFX_PSI5_CH_SORL_SD15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD15 */
#define IFX_PSI5_CH_SORL_SD15_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD16 */
#define IFX_PSI5_CH_SORL_SD16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD16 */
#define IFX_PSI5_CH_SORL_SD16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD16 */
#define IFX_PSI5_CH_SORL_SD16_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD17 */
#define IFX_PSI5_CH_SORL_SD17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD17 */
#define IFX_PSI5_CH_SORL_SD17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD17 */
#define IFX_PSI5_CH_SORL_SD17_OFF (17u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD18 */
#define IFX_PSI5_CH_SORL_SD18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD18 */
#define IFX_PSI5_CH_SORL_SD18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD18 */
#define IFX_PSI5_CH_SORL_SD18_OFF (18u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD19 */
#define IFX_PSI5_CH_SORL_SD19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD19 */
#define IFX_PSI5_CH_SORL_SD19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD19 */
#define IFX_PSI5_CH_SORL_SD19_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD1 */
#define IFX_PSI5_CH_SORL_SD1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD1 */
#define IFX_PSI5_CH_SORL_SD1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD1 */
#define IFX_PSI5_CH_SORL_SD1_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD20 */
#define IFX_PSI5_CH_SORL_SD20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD20 */
#define IFX_PSI5_CH_SORL_SD20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD20 */
#define IFX_PSI5_CH_SORL_SD20_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD21 */
#define IFX_PSI5_CH_SORL_SD21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD21 */
#define IFX_PSI5_CH_SORL_SD21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD21 */
#define IFX_PSI5_CH_SORL_SD21_OFF (21u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD22 */
#define IFX_PSI5_CH_SORL_SD22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD22 */
#define IFX_PSI5_CH_SORL_SD22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD22 */
#define IFX_PSI5_CH_SORL_SD22_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD23 */
#define IFX_PSI5_CH_SORL_SD23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD23 */
#define IFX_PSI5_CH_SORL_SD23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD23 */
#define IFX_PSI5_CH_SORL_SD23_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD24 */
#define IFX_PSI5_CH_SORL_SD24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD24 */
#define IFX_PSI5_CH_SORL_SD24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD24 */
#define IFX_PSI5_CH_SORL_SD24_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD25 */
#define IFX_PSI5_CH_SORL_SD25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD25 */
#define IFX_PSI5_CH_SORL_SD25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD25 */
#define IFX_PSI5_CH_SORL_SD25_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD26 */
#define IFX_PSI5_CH_SORL_SD26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD26 */
#define IFX_PSI5_CH_SORL_SD26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD26 */
#define IFX_PSI5_CH_SORL_SD26_OFF (26u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD27 */
#define IFX_PSI5_CH_SORL_SD27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD27 */
#define IFX_PSI5_CH_SORL_SD27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD27 */
#define IFX_PSI5_CH_SORL_SD27_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD28 */
#define IFX_PSI5_CH_SORL_SD28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD28 */
#define IFX_PSI5_CH_SORL_SD28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD28 */
#define IFX_PSI5_CH_SORL_SD28_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD29 */
#define IFX_PSI5_CH_SORL_SD29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD29 */
#define IFX_PSI5_CH_SORL_SD29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD29 */
#define IFX_PSI5_CH_SORL_SD29_OFF (29u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD2 */
#define IFX_PSI5_CH_SORL_SD2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD2 */
#define IFX_PSI5_CH_SORL_SD2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD2 */
#define IFX_PSI5_CH_SORL_SD2_OFF (2u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD30 */
#define IFX_PSI5_CH_SORL_SD30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD30 */
#define IFX_PSI5_CH_SORL_SD30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD30 */
#define IFX_PSI5_CH_SORL_SD30_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD31 */
#define IFX_PSI5_CH_SORL_SD31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD31 */
#define IFX_PSI5_CH_SORL_SD31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD31 */
#define IFX_PSI5_CH_SORL_SD31_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD3 */
#define IFX_PSI5_CH_SORL_SD3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD3 */
#define IFX_PSI5_CH_SORL_SD3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD3 */
#define IFX_PSI5_CH_SORL_SD3_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD4 */
#define IFX_PSI5_CH_SORL_SD4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD4 */
#define IFX_PSI5_CH_SORL_SD4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD4 */
#define IFX_PSI5_CH_SORL_SD4_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD5 */
#define IFX_PSI5_CH_SORL_SD5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD5 */
#define IFX_PSI5_CH_SORL_SD5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD5 */
#define IFX_PSI5_CH_SORL_SD5_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD6 */
#define IFX_PSI5_CH_SORL_SD6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD6 */
#define IFX_PSI5_CH_SORL_SD6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD6 */
#define IFX_PSI5_CH_SORL_SD6_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD7 */
#define IFX_PSI5_CH_SORL_SD7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD7 */
#define IFX_PSI5_CH_SORL_SD7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD7 */
#define IFX_PSI5_CH_SORL_SD7_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD8 */
#define IFX_PSI5_CH_SORL_SD8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD8 */
#define IFX_PSI5_CH_SORL_SD8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD8 */
#define IFX_PSI5_CH_SORL_SD8_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SORL_Bits.SD9 */
#define IFX_PSI5_CH_SORL_SD9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SORL_Bits.SD9 */
#define IFX_PSI5_CH_SORL_SD9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SORL_Bits.SD9 */
#define IFX_PSI5_CH_SORL_SD9_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_SPTSC_Bits.TS */
#define IFX_PSI5_CH_SPTSC_TS_LEN (24u)

/** \brief  Mask for Ifx_PSI5_CH_SPTSC_Bits.TS */
#define IFX_PSI5_CH_SPTSC_TS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5_CH_SPTSC_Bits.TS */
#define IFX_PSI5_CH_SPTSC_TS_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD32 */
#define IFX_PSI5_CH_SSRH_SD32_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD32 */
#define IFX_PSI5_CH_SSRH_SD32_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD32 */
#define IFX_PSI5_CH_SSRH_SD32_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD33 */
#define IFX_PSI5_CH_SSRH_SD33_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD33 */
#define IFX_PSI5_CH_SSRH_SD33_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD33 */
#define IFX_PSI5_CH_SSRH_SD33_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD34 */
#define IFX_PSI5_CH_SSRH_SD34_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD34 */
#define IFX_PSI5_CH_SSRH_SD34_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD34 */
#define IFX_PSI5_CH_SSRH_SD34_OFF (2u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD35 */
#define IFX_PSI5_CH_SSRH_SD35_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD35 */
#define IFX_PSI5_CH_SSRH_SD35_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD35 */
#define IFX_PSI5_CH_SSRH_SD35_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD36 */
#define IFX_PSI5_CH_SSRH_SD36_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD36 */
#define IFX_PSI5_CH_SSRH_SD36_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD36 */
#define IFX_PSI5_CH_SSRH_SD36_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD37 */
#define IFX_PSI5_CH_SSRH_SD37_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD37 */
#define IFX_PSI5_CH_SSRH_SD37_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD37 */
#define IFX_PSI5_CH_SSRH_SD37_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD38 */
#define IFX_PSI5_CH_SSRH_SD38_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD38 */
#define IFX_PSI5_CH_SSRH_SD38_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD38 */
#define IFX_PSI5_CH_SSRH_SD38_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD39 */
#define IFX_PSI5_CH_SSRH_SD39_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD39 */
#define IFX_PSI5_CH_SSRH_SD39_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD39 */
#define IFX_PSI5_CH_SSRH_SD39_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD40 */
#define IFX_PSI5_CH_SSRH_SD40_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD40 */
#define IFX_PSI5_CH_SSRH_SD40_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD40 */
#define IFX_PSI5_CH_SSRH_SD40_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD41 */
#define IFX_PSI5_CH_SSRH_SD41_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD41 */
#define IFX_PSI5_CH_SSRH_SD41_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD41 */
#define IFX_PSI5_CH_SSRH_SD41_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD42 */
#define IFX_PSI5_CH_SSRH_SD42_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD42 */
#define IFX_PSI5_CH_SSRH_SD42_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD42 */
#define IFX_PSI5_CH_SSRH_SD42_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD43 */
#define IFX_PSI5_CH_SSRH_SD43_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD43 */
#define IFX_PSI5_CH_SSRH_SD43_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD43 */
#define IFX_PSI5_CH_SSRH_SD43_OFF (11u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD44 */
#define IFX_PSI5_CH_SSRH_SD44_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD44 */
#define IFX_PSI5_CH_SSRH_SD44_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD44 */
#define IFX_PSI5_CH_SSRH_SD44_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD45 */
#define IFX_PSI5_CH_SSRH_SD45_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD45 */
#define IFX_PSI5_CH_SSRH_SD45_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD45 */
#define IFX_PSI5_CH_SSRH_SD45_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD46 */
#define IFX_PSI5_CH_SSRH_SD46_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD46 */
#define IFX_PSI5_CH_SSRH_SD46_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD46 */
#define IFX_PSI5_CH_SSRH_SD46_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD47 */
#define IFX_PSI5_CH_SSRH_SD47_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD47 */
#define IFX_PSI5_CH_SSRH_SD47_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD47 */
#define IFX_PSI5_CH_SSRH_SD47_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD48 */
#define IFX_PSI5_CH_SSRH_SD48_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD48 */
#define IFX_PSI5_CH_SSRH_SD48_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD48 */
#define IFX_PSI5_CH_SSRH_SD48_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD49 */
#define IFX_PSI5_CH_SSRH_SD49_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD49 */
#define IFX_PSI5_CH_SSRH_SD49_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD49 */
#define IFX_PSI5_CH_SSRH_SD49_OFF (17u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD50 */
#define IFX_PSI5_CH_SSRH_SD50_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD50 */
#define IFX_PSI5_CH_SSRH_SD50_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD50 */
#define IFX_PSI5_CH_SSRH_SD50_OFF (18u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD51 */
#define IFX_PSI5_CH_SSRH_SD51_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD51 */
#define IFX_PSI5_CH_SSRH_SD51_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD51 */
#define IFX_PSI5_CH_SSRH_SD51_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD52 */
#define IFX_PSI5_CH_SSRH_SD52_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD52 */
#define IFX_PSI5_CH_SSRH_SD52_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD52 */
#define IFX_PSI5_CH_SSRH_SD52_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD53 */
#define IFX_PSI5_CH_SSRH_SD53_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD53 */
#define IFX_PSI5_CH_SSRH_SD53_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD53 */
#define IFX_PSI5_CH_SSRH_SD53_OFF (21u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD54 */
#define IFX_PSI5_CH_SSRH_SD54_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD54 */
#define IFX_PSI5_CH_SSRH_SD54_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD54 */
#define IFX_PSI5_CH_SSRH_SD54_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD55 */
#define IFX_PSI5_CH_SSRH_SD55_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD55 */
#define IFX_PSI5_CH_SSRH_SD55_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD55 */
#define IFX_PSI5_CH_SSRH_SD55_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD56 */
#define IFX_PSI5_CH_SSRH_SD56_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD56 */
#define IFX_PSI5_CH_SSRH_SD56_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD56 */
#define IFX_PSI5_CH_SSRH_SD56_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD57 */
#define IFX_PSI5_CH_SSRH_SD57_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD57 */
#define IFX_PSI5_CH_SSRH_SD57_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD57 */
#define IFX_PSI5_CH_SSRH_SD57_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD58 */
#define IFX_PSI5_CH_SSRH_SD58_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD58 */
#define IFX_PSI5_CH_SSRH_SD58_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD58 */
#define IFX_PSI5_CH_SSRH_SD58_OFF (26u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD59 */
#define IFX_PSI5_CH_SSRH_SD59_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD59 */
#define IFX_PSI5_CH_SSRH_SD59_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD59 */
#define IFX_PSI5_CH_SSRH_SD59_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD60 */
#define IFX_PSI5_CH_SSRH_SD60_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD60 */
#define IFX_PSI5_CH_SSRH_SD60_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD60 */
#define IFX_PSI5_CH_SSRH_SD60_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD61 */
#define IFX_PSI5_CH_SSRH_SD61_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD61 */
#define IFX_PSI5_CH_SSRH_SD61_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD61 */
#define IFX_PSI5_CH_SSRH_SD61_OFF (29u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD62 */
#define IFX_PSI5_CH_SSRH_SD62_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD62 */
#define IFX_PSI5_CH_SSRH_SD62_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD62 */
#define IFX_PSI5_CH_SSRH_SD62_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_SSRH_Bits.SD63 */
#define IFX_PSI5_CH_SSRH_SD63_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRH_Bits.SD63 */
#define IFX_PSI5_CH_SSRH_SD63_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRH_Bits.SD63 */
#define IFX_PSI5_CH_SSRH_SD63_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD0 */
#define IFX_PSI5_CH_SSRL_SD0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD0 */
#define IFX_PSI5_CH_SSRL_SD0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD0 */
#define IFX_PSI5_CH_SSRL_SD0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD10 */
#define IFX_PSI5_CH_SSRL_SD10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD10 */
#define IFX_PSI5_CH_SSRL_SD10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD10 */
#define IFX_PSI5_CH_SSRL_SD10_OFF (10u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD11 */
#define IFX_PSI5_CH_SSRL_SD11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD11 */
#define IFX_PSI5_CH_SSRL_SD11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD11 */
#define IFX_PSI5_CH_SSRL_SD11_OFF (11u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD12 */
#define IFX_PSI5_CH_SSRL_SD12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD12 */
#define IFX_PSI5_CH_SSRL_SD12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD12 */
#define IFX_PSI5_CH_SSRL_SD12_OFF (12u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD13 */
#define IFX_PSI5_CH_SSRL_SD13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD13 */
#define IFX_PSI5_CH_SSRL_SD13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD13 */
#define IFX_PSI5_CH_SSRL_SD13_OFF (13u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD14 */
#define IFX_PSI5_CH_SSRL_SD14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD14 */
#define IFX_PSI5_CH_SSRL_SD14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD14 */
#define IFX_PSI5_CH_SSRL_SD14_OFF (14u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD15 */
#define IFX_PSI5_CH_SSRL_SD15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD15 */
#define IFX_PSI5_CH_SSRL_SD15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD15 */
#define IFX_PSI5_CH_SSRL_SD15_OFF (15u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD16 */
#define IFX_PSI5_CH_SSRL_SD16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD16 */
#define IFX_PSI5_CH_SSRL_SD16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD16 */
#define IFX_PSI5_CH_SSRL_SD16_OFF (16u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD17 */
#define IFX_PSI5_CH_SSRL_SD17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD17 */
#define IFX_PSI5_CH_SSRL_SD17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD17 */
#define IFX_PSI5_CH_SSRL_SD17_OFF (17u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD18 */
#define IFX_PSI5_CH_SSRL_SD18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD18 */
#define IFX_PSI5_CH_SSRL_SD18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD18 */
#define IFX_PSI5_CH_SSRL_SD18_OFF (18u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD19 */
#define IFX_PSI5_CH_SSRL_SD19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD19 */
#define IFX_PSI5_CH_SSRL_SD19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD19 */
#define IFX_PSI5_CH_SSRL_SD19_OFF (19u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD1 */
#define IFX_PSI5_CH_SSRL_SD1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD1 */
#define IFX_PSI5_CH_SSRL_SD1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD1 */
#define IFX_PSI5_CH_SSRL_SD1_OFF (1u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD20 */
#define IFX_PSI5_CH_SSRL_SD20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD20 */
#define IFX_PSI5_CH_SSRL_SD20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD20 */
#define IFX_PSI5_CH_SSRL_SD20_OFF (20u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD21 */
#define IFX_PSI5_CH_SSRL_SD21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD21 */
#define IFX_PSI5_CH_SSRL_SD21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD21 */
#define IFX_PSI5_CH_SSRL_SD21_OFF (21u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD22 */
#define IFX_PSI5_CH_SSRL_SD22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD22 */
#define IFX_PSI5_CH_SSRL_SD22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD22 */
#define IFX_PSI5_CH_SSRL_SD22_OFF (22u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD23 */
#define IFX_PSI5_CH_SSRL_SD23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD23 */
#define IFX_PSI5_CH_SSRL_SD23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD23 */
#define IFX_PSI5_CH_SSRL_SD23_OFF (23u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD24 */
#define IFX_PSI5_CH_SSRL_SD24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD24 */
#define IFX_PSI5_CH_SSRL_SD24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD24 */
#define IFX_PSI5_CH_SSRL_SD24_OFF (24u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD25 */
#define IFX_PSI5_CH_SSRL_SD25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD25 */
#define IFX_PSI5_CH_SSRL_SD25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD25 */
#define IFX_PSI5_CH_SSRL_SD25_OFF (25u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD26 */
#define IFX_PSI5_CH_SSRL_SD26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD26 */
#define IFX_PSI5_CH_SSRL_SD26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD26 */
#define IFX_PSI5_CH_SSRL_SD26_OFF (26u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD27 */
#define IFX_PSI5_CH_SSRL_SD27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD27 */
#define IFX_PSI5_CH_SSRL_SD27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD27 */
#define IFX_PSI5_CH_SSRL_SD27_OFF (27u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD28 */
#define IFX_PSI5_CH_SSRL_SD28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD28 */
#define IFX_PSI5_CH_SSRL_SD28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD28 */
#define IFX_PSI5_CH_SSRL_SD28_OFF (28u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD29 */
#define IFX_PSI5_CH_SSRL_SD29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD29 */
#define IFX_PSI5_CH_SSRL_SD29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD29 */
#define IFX_PSI5_CH_SSRL_SD29_OFF (29u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD2 */
#define IFX_PSI5_CH_SSRL_SD2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD2 */
#define IFX_PSI5_CH_SSRL_SD2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD2 */
#define IFX_PSI5_CH_SSRL_SD2_OFF (2u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD30 */
#define IFX_PSI5_CH_SSRL_SD30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD30 */
#define IFX_PSI5_CH_SSRL_SD30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD30 */
#define IFX_PSI5_CH_SSRL_SD30_OFF (30u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD31 */
#define IFX_PSI5_CH_SSRL_SD31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD31 */
#define IFX_PSI5_CH_SSRL_SD31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD31 */
#define IFX_PSI5_CH_SSRL_SD31_OFF (31u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD3 */
#define IFX_PSI5_CH_SSRL_SD3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD3 */
#define IFX_PSI5_CH_SSRL_SD3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD3 */
#define IFX_PSI5_CH_SSRL_SD3_OFF (3u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD4 */
#define IFX_PSI5_CH_SSRL_SD4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD4 */
#define IFX_PSI5_CH_SSRL_SD4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD4 */
#define IFX_PSI5_CH_SSRL_SD4_OFF (4u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD5 */
#define IFX_PSI5_CH_SSRL_SD5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD5 */
#define IFX_PSI5_CH_SSRL_SD5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD5 */
#define IFX_PSI5_CH_SSRL_SD5_OFF (5u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD6 */
#define IFX_PSI5_CH_SSRL_SD6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD6 */
#define IFX_PSI5_CH_SSRL_SD6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD6 */
#define IFX_PSI5_CH_SSRL_SD6_OFF (6u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD7 */
#define IFX_PSI5_CH_SSRL_SD7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD7 */
#define IFX_PSI5_CH_SSRL_SD7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD7 */
#define IFX_PSI5_CH_SSRL_SD7_OFF (7u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD8 */
#define IFX_PSI5_CH_SSRL_SD8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD8 */
#define IFX_PSI5_CH_SSRL_SD8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD8 */
#define IFX_PSI5_CH_SSRL_SD8_OFF (8u)

/** \brief  Length for Ifx_PSI5_CH_SSRL_Bits.SD9 */
#define IFX_PSI5_CH_SSRL_SD9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CH_SSRL_Bits.SD9 */
#define IFX_PSI5_CH_SSRL_SD9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CH_SSRL_Bits.SD9 */
#define IFX_PSI5_CH_SSRL_SD9_OFF (9u)

/** \brief  Length for Ifx_PSI5_CH_WDT_Bits.WDLxw */
#define IFX_PSI5_CH_WDT_WDLXW_LEN (16u)

/** \brief  Mask for Ifx_PSI5_CH_WDT_Bits.WDLxw */
#define IFX_PSI5_CH_WDT_WDLXW_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5_CH_WDT_Bits.WDLxw */
#define IFX_PSI5_CH_WDT_WDLXW_OFF (0u)

/** \brief  Length for Ifx_PSI5_CLC_Bits.DISR */
#define IFX_PSI5_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CLC_Bits.DISR */
#define IFX_PSI5_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CLC_Bits.DISR */
#define IFX_PSI5_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_PSI5_CLC_Bits.DISS */
#define IFX_PSI5_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CLC_Bits.DISS */
#define IFX_PSI5_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CLC_Bits.DISS */
#define IFX_PSI5_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI0 */
#define IFX_PSI5_CRCICLR_CRCI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI0 */
#define IFX_PSI5_CRCICLR_CRCI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI0 */
#define IFX_PSI5_CRCICLR_CRCI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI10 */
#define IFX_PSI5_CRCICLR_CRCI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI10 */
#define IFX_PSI5_CRCICLR_CRCI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI10 */
#define IFX_PSI5_CRCICLR_CRCI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI11 */
#define IFX_PSI5_CRCICLR_CRCI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI11 */
#define IFX_PSI5_CRCICLR_CRCI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI11 */
#define IFX_PSI5_CRCICLR_CRCI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI12 */
#define IFX_PSI5_CRCICLR_CRCI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI12 */
#define IFX_PSI5_CRCICLR_CRCI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI12 */
#define IFX_PSI5_CRCICLR_CRCI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI13 */
#define IFX_PSI5_CRCICLR_CRCI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI13 */
#define IFX_PSI5_CRCICLR_CRCI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI13 */
#define IFX_PSI5_CRCICLR_CRCI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI14 */
#define IFX_PSI5_CRCICLR_CRCI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI14 */
#define IFX_PSI5_CRCICLR_CRCI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI14 */
#define IFX_PSI5_CRCICLR_CRCI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI15 */
#define IFX_PSI5_CRCICLR_CRCI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI15 */
#define IFX_PSI5_CRCICLR_CRCI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI15 */
#define IFX_PSI5_CRCICLR_CRCI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI16 */
#define IFX_PSI5_CRCICLR_CRCI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI16 */
#define IFX_PSI5_CRCICLR_CRCI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI16 */
#define IFX_PSI5_CRCICLR_CRCI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI17 */
#define IFX_PSI5_CRCICLR_CRCI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI17 */
#define IFX_PSI5_CRCICLR_CRCI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI17 */
#define IFX_PSI5_CRCICLR_CRCI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI18 */
#define IFX_PSI5_CRCICLR_CRCI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI18 */
#define IFX_PSI5_CRCICLR_CRCI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI18 */
#define IFX_PSI5_CRCICLR_CRCI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI19 */
#define IFX_PSI5_CRCICLR_CRCI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI19 */
#define IFX_PSI5_CRCICLR_CRCI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI19 */
#define IFX_PSI5_CRCICLR_CRCI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI1 */
#define IFX_PSI5_CRCICLR_CRCI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI1 */
#define IFX_PSI5_CRCICLR_CRCI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI1 */
#define IFX_PSI5_CRCICLR_CRCI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI20 */
#define IFX_PSI5_CRCICLR_CRCI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI20 */
#define IFX_PSI5_CRCICLR_CRCI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI20 */
#define IFX_PSI5_CRCICLR_CRCI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI21 */
#define IFX_PSI5_CRCICLR_CRCI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI21 */
#define IFX_PSI5_CRCICLR_CRCI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI21 */
#define IFX_PSI5_CRCICLR_CRCI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI22 */
#define IFX_PSI5_CRCICLR_CRCI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI22 */
#define IFX_PSI5_CRCICLR_CRCI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI22 */
#define IFX_PSI5_CRCICLR_CRCI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI23 */
#define IFX_PSI5_CRCICLR_CRCI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI23 */
#define IFX_PSI5_CRCICLR_CRCI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI23 */
#define IFX_PSI5_CRCICLR_CRCI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI24 */
#define IFX_PSI5_CRCICLR_CRCI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI24 */
#define IFX_PSI5_CRCICLR_CRCI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI24 */
#define IFX_PSI5_CRCICLR_CRCI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI25 */
#define IFX_PSI5_CRCICLR_CRCI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI25 */
#define IFX_PSI5_CRCICLR_CRCI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI25 */
#define IFX_PSI5_CRCICLR_CRCI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI26 */
#define IFX_PSI5_CRCICLR_CRCI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI26 */
#define IFX_PSI5_CRCICLR_CRCI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI26 */
#define IFX_PSI5_CRCICLR_CRCI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI27 */
#define IFX_PSI5_CRCICLR_CRCI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI27 */
#define IFX_PSI5_CRCICLR_CRCI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI27 */
#define IFX_PSI5_CRCICLR_CRCI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI28 */
#define IFX_PSI5_CRCICLR_CRCI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI28 */
#define IFX_PSI5_CRCICLR_CRCI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI28 */
#define IFX_PSI5_CRCICLR_CRCI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI29 */
#define IFX_PSI5_CRCICLR_CRCI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI29 */
#define IFX_PSI5_CRCICLR_CRCI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI29 */
#define IFX_PSI5_CRCICLR_CRCI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI2 */
#define IFX_PSI5_CRCICLR_CRCI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI2 */
#define IFX_PSI5_CRCICLR_CRCI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI2 */
#define IFX_PSI5_CRCICLR_CRCI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI30 */
#define IFX_PSI5_CRCICLR_CRCI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI30 */
#define IFX_PSI5_CRCICLR_CRCI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI30 */
#define IFX_PSI5_CRCICLR_CRCI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI31 */
#define IFX_PSI5_CRCICLR_CRCI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI31 */
#define IFX_PSI5_CRCICLR_CRCI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI31 */
#define IFX_PSI5_CRCICLR_CRCI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI3 */
#define IFX_PSI5_CRCICLR_CRCI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI3 */
#define IFX_PSI5_CRCICLR_CRCI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI3 */
#define IFX_PSI5_CRCICLR_CRCI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI4 */
#define IFX_PSI5_CRCICLR_CRCI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI4 */
#define IFX_PSI5_CRCICLR_CRCI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI4 */
#define IFX_PSI5_CRCICLR_CRCI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI5 */
#define IFX_PSI5_CRCICLR_CRCI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI5 */
#define IFX_PSI5_CRCICLR_CRCI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI5 */
#define IFX_PSI5_CRCICLR_CRCI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI6 */
#define IFX_PSI5_CRCICLR_CRCI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI6 */
#define IFX_PSI5_CRCICLR_CRCI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI6 */
#define IFX_PSI5_CRCICLR_CRCI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI7 */
#define IFX_PSI5_CRCICLR_CRCI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI7 */
#define IFX_PSI5_CRCICLR_CRCI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI7 */
#define IFX_PSI5_CRCICLR_CRCI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI8 */
#define IFX_PSI5_CRCICLR_CRCI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI8 */
#define IFX_PSI5_CRCICLR_CRCI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI8 */
#define IFX_PSI5_CRCICLR_CRCI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_CRCICLR_Bits.CRCI9 */
#define IFX_PSI5_CRCICLR_CRCI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCICLR_Bits.CRCI9 */
#define IFX_PSI5_CRCICLR_CRCI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCICLR_Bits.CRCI9 */
#define IFX_PSI5_CRCICLR_CRCI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI0 */
#define IFX_PSI5_CRCIOV_CRCI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI0 */
#define IFX_PSI5_CRCIOV_CRCI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI0 */
#define IFX_PSI5_CRCIOV_CRCI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI10 */
#define IFX_PSI5_CRCIOV_CRCI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI10 */
#define IFX_PSI5_CRCIOV_CRCI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI10 */
#define IFX_PSI5_CRCIOV_CRCI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI11 */
#define IFX_PSI5_CRCIOV_CRCI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI11 */
#define IFX_PSI5_CRCIOV_CRCI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI11 */
#define IFX_PSI5_CRCIOV_CRCI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI12 */
#define IFX_PSI5_CRCIOV_CRCI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI12 */
#define IFX_PSI5_CRCIOV_CRCI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI12 */
#define IFX_PSI5_CRCIOV_CRCI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI13 */
#define IFX_PSI5_CRCIOV_CRCI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI13 */
#define IFX_PSI5_CRCIOV_CRCI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI13 */
#define IFX_PSI5_CRCIOV_CRCI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI14 */
#define IFX_PSI5_CRCIOV_CRCI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI14 */
#define IFX_PSI5_CRCIOV_CRCI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI14 */
#define IFX_PSI5_CRCIOV_CRCI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI15 */
#define IFX_PSI5_CRCIOV_CRCI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI15 */
#define IFX_PSI5_CRCIOV_CRCI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI15 */
#define IFX_PSI5_CRCIOV_CRCI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI16 */
#define IFX_PSI5_CRCIOV_CRCI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI16 */
#define IFX_PSI5_CRCIOV_CRCI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI16 */
#define IFX_PSI5_CRCIOV_CRCI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI17 */
#define IFX_PSI5_CRCIOV_CRCI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI17 */
#define IFX_PSI5_CRCIOV_CRCI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI17 */
#define IFX_PSI5_CRCIOV_CRCI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI18 */
#define IFX_PSI5_CRCIOV_CRCI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI18 */
#define IFX_PSI5_CRCIOV_CRCI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI18 */
#define IFX_PSI5_CRCIOV_CRCI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI19 */
#define IFX_PSI5_CRCIOV_CRCI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI19 */
#define IFX_PSI5_CRCIOV_CRCI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI19 */
#define IFX_PSI5_CRCIOV_CRCI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI1 */
#define IFX_PSI5_CRCIOV_CRCI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI1 */
#define IFX_PSI5_CRCIOV_CRCI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI1 */
#define IFX_PSI5_CRCIOV_CRCI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI20 */
#define IFX_PSI5_CRCIOV_CRCI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI20 */
#define IFX_PSI5_CRCIOV_CRCI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI20 */
#define IFX_PSI5_CRCIOV_CRCI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI21 */
#define IFX_PSI5_CRCIOV_CRCI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI21 */
#define IFX_PSI5_CRCIOV_CRCI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI21 */
#define IFX_PSI5_CRCIOV_CRCI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI22 */
#define IFX_PSI5_CRCIOV_CRCI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI22 */
#define IFX_PSI5_CRCIOV_CRCI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI22 */
#define IFX_PSI5_CRCIOV_CRCI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI23 */
#define IFX_PSI5_CRCIOV_CRCI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI23 */
#define IFX_PSI5_CRCIOV_CRCI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI23 */
#define IFX_PSI5_CRCIOV_CRCI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI24 */
#define IFX_PSI5_CRCIOV_CRCI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI24 */
#define IFX_PSI5_CRCIOV_CRCI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI24 */
#define IFX_PSI5_CRCIOV_CRCI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI25 */
#define IFX_PSI5_CRCIOV_CRCI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI25 */
#define IFX_PSI5_CRCIOV_CRCI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI25 */
#define IFX_PSI5_CRCIOV_CRCI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI26 */
#define IFX_PSI5_CRCIOV_CRCI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI26 */
#define IFX_PSI5_CRCIOV_CRCI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI26 */
#define IFX_PSI5_CRCIOV_CRCI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI27 */
#define IFX_PSI5_CRCIOV_CRCI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI27 */
#define IFX_PSI5_CRCIOV_CRCI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI27 */
#define IFX_PSI5_CRCIOV_CRCI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI28 */
#define IFX_PSI5_CRCIOV_CRCI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI28 */
#define IFX_PSI5_CRCIOV_CRCI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI28 */
#define IFX_PSI5_CRCIOV_CRCI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI29 */
#define IFX_PSI5_CRCIOV_CRCI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI29 */
#define IFX_PSI5_CRCIOV_CRCI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI29 */
#define IFX_PSI5_CRCIOV_CRCI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI2 */
#define IFX_PSI5_CRCIOV_CRCI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI2 */
#define IFX_PSI5_CRCIOV_CRCI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI2 */
#define IFX_PSI5_CRCIOV_CRCI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI30 */
#define IFX_PSI5_CRCIOV_CRCI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI30 */
#define IFX_PSI5_CRCIOV_CRCI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI30 */
#define IFX_PSI5_CRCIOV_CRCI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI31 */
#define IFX_PSI5_CRCIOV_CRCI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI31 */
#define IFX_PSI5_CRCIOV_CRCI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI31 */
#define IFX_PSI5_CRCIOV_CRCI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI3 */
#define IFX_PSI5_CRCIOV_CRCI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI3 */
#define IFX_PSI5_CRCIOV_CRCI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI3 */
#define IFX_PSI5_CRCIOV_CRCI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI4 */
#define IFX_PSI5_CRCIOV_CRCI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI4 */
#define IFX_PSI5_CRCIOV_CRCI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI4 */
#define IFX_PSI5_CRCIOV_CRCI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI5 */
#define IFX_PSI5_CRCIOV_CRCI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI5 */
#define IFX_PSI5_CRCIOV_CRCI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI5 */
#define IFX_PSI5_CRCIOV_CRCI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI6 */
#define IFX_PSI5_CRCIOV_CRCI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI6 */
#define IFX_PSI5_CRCIOV_CRCI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI6 */
#define IFX_PSI5_CRCIOV_CRCI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI7 */
#define IFX_PSI5_CRCIOV_CRCI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI7 */
#define IFX_PSI5_CRCIOV_CRCI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI7 */
#define IFX_PSI5_CRCIOV_CRCI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI8 */
#define IFX_PSI5_CRCIOV_CRCI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI8 */
#define IFX_PSI5_CRCIOV_CRCI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI8 */
#define IFX_PSI5_CRCIOV_CRCI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_CRCIOV_Bits.CRCI9 */
#define IFX_PSI5_CRCIOV_CRCI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCIOV_Bits.CRCI9 */
#define IFX_PSI5_CRCIOV_CRCI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCIOV_Bits.CRCI9 */
#define IFX_PSI5_CRCIOV_CRCI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI0 */
#define IFX_PSI5_CRCISET_CRCI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI0 */
#define IFX_PSI5_CRCISET_CRCI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI0 */
#define IFX_PSI5_CRCISET_CRCI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI10 */
#define IFX_PSI5_CRCISET_CRCI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI10 */
#define IFX_PSI5_CRCISET_CRCI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI10 */
#define IFX_PSI5_CRCISET_CRCI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI11 */
#define IFX_PSI5_CRCISET_CRCI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI11 */
#define IFX_PSI5_CRCISET_CRCI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI11 */
#define IFX_PSI5_CRCISET_CRCI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI12 */
#define IFX_PSI5_CRCISET_CRCI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI12 */
#define IFX_PSI5_CRCISET_CRCI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI12 */
#define IFX_PSI5_CRCISET_CRCI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI13 */
#define IFX_PSI5_CRCISET_CRCI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI13 */
#define IFX_PSI5_CRCISET_CRCI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI13 */
#define IFX_PSI5_CRCISET_CRCI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI14 */
#define IFX_PSI5_CRCISET_CRCI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI14 */
#define IFX_PSI5_CRCISET_CRCI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI14 */
#define IFX_PSI5_CRCISET_CRCI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI15 */
#define IFX_PSI5_CRCISET_CRCI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI15 */
#define IFX_PSI5_CRCISET_CRCI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI15 */
#define IFX_PSI5_CRCISET_CRCI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI16 */
#define IFX_PSI5_CRCISET_CRCI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI16 */
#define IFX_PSI5_CRCISET_CRCI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI16 */
#define IFX_PSI5_CRCISET_CRCI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI17 */
#define IFX_PSI5_CRCISET_CRCI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI17 */
#define IFX_PSI5_CRCISET_CRCI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI17 */
#define IFX_PSI5_CRCISET_CRCI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI18 */
#define IFX_PSI5_CRCISET_CRCI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI18 */
#define IFX_PSI5_CRCISET_CRCI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI18 */
#define IFX_PSI5_CRCISET_CRCI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI19 */
#define IFX_PSI5_CRCISET_CRCI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI19 */
#define IFX_PSI5_CRCISET_CRCI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI19 */
#define IFX_PSI5_CRCISET_CRCI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI1 */
#define IFX_PSI5_CRCISET_CRCI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI1 */
#define IFX_PSI5_CRCISET_CRCI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI1 */
#define IFX_PSI5_CRCISET_CRCI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI20 */
#define IFX_PSI5_CRCISET_CRCI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI20 */
#define IFX_PSI5_CRCISET_CRCI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI20 */
#define IFX_PSI5_CRCISET_CRCI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI21 */
#define IFX_PSI5_CRCISET_CRCI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI21 */
#define IFX_PSI5_CRCISET_CRCI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI21 */
#define IFX_PSI5_CRCISET_CRCI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI22 */
#define IFX_PSI5_CRCISET_CRCI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI22 */
#define IFX_PSI5_CRCISET_CRCI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI22 */
#define IFX_PSI5_CRCISET_CRCI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI23 */
#define IFX_PSI5_CRCISET_CRCI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI23 */
#define IFX_PSI5_CRCISET_CRCI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI23 */
#define IFX_PSI5_CRCISET_CRCI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI24 */
#define IFX_PSI5_CRCISET_CRCI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI24 */
#define IFX_PSI5_CRCISET_CRCI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI24 */
#define IFX_PSI5_CRCISET_CRCI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI25 */
#define IFX_PSI5_CRCISET_CRCI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI25 */
#define IFX_PSI5_CRCISET_CRCI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI25 */
#define IFX_PSI5_CRCISET_CRCI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI26 */
#define IFX_PSI5_CRCISET_CRCI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI26 */
#define IFX_PSI5_CRCISET_CRCI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI26 */
#define IFX_PSI5_CRCISET_CRCI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI27 */
#define IFX_PSI5_CRCISET_CRCI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI27 */
#define IFX_PSI5_CRCISET_CRCI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI27 */
#define IFX_PSI5_CRCISET_CRCI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI28 */
#define IFX_PSI5_CRCISET_CRCI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI28 */
#define IFX_PSI5_CRCISET_CRCI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI28 */
#define IFX_PSI5_CRCISET_CRCI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI29 */
#define IFX_PSI5_CRCISET_CRCI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI29 */
#define IFX_PSI5_CRCISET_CRCI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI29 */
#define IFX_PSI5_CRCISET_CRCI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI2 */
#define IFX_PSI5_CRCISET_CRCI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI2 */
#define IFX_PSI5_CRCISET_CRCI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI2 */
#define IFX_PSI5_CRCISET_CRCI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI30 */
#define IFX_PSI5_CRCISET_CRCI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI30 */
#define IFX_PSI5_CRCISET_CRCI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI30 */
#define IFX_PSI5_CRCISET_CRCI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI31 */
#define IFX_PSI5_CRCISET_CRCI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI31 */
#define IFX_PSI5_CRCISET_CRCI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI31 */
#define IFX_PSI5_CRCISET_CRCI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI3 */
#define IFX_PSI5_CRCISET_CRCI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI3 */
#define IFX_PSI5_CRCISET_CRCI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI3 */
#define IFX_PSI5_CRCISET_CRCI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI4 */
#define IFX_PSI5_CRCISET_CRCI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI4 */
#define IFX_PSI5_CRCISET_CRCI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI4 */
#define IFX_PSI5_CRCISET_CRCI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI5 */
#define IFX_PSI5_CRCISET_CRCI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI5 */
#define IFX_PSI5_CRCISET_CRCI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI5 */
#define IFX_PSI5_CRCISET_CRCI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI6 */
#define IFX_PSI5_CRCISET_CRCI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI6 */
#define IFX_PSI5_CRCISET_CRCI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI6 */
#define IFX_PSI5_CRCISET_CRCI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI7 */
#define IFX_PSI5_CRCISET_CRCI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI7 */
#define IFX_PSI5_CRCISET_CRCI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI7 */
#define IFX_PSI5_CRCISET_CRCI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI8 */
#define IFX_PSI5_CRCISET_CRCI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI8 */
#define IFX_PSI5_CRCISET_CRCI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI8 */
#define IFX_PSI5_CRCISET_CRCI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_CRCISET_Bits.CRCI9 */
#define IFX_PSI5_CRCISET_CRCI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_CRCISET_Bits.CRCI9 */
#define IFX_PSI5_CRCISET_CRCI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_CRCISET_Bits.CRCI9 */
#define IFX_PSI5_CRCISET_CRCI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_FDR_Bits.DM */
#define IFX_PSI5_FDR_DM_LEN (2u)

/** \brief  Mask for Ifx_PSI5_FDR_Bits.DM */
#define IFX_PSI5_FDR_DM_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_FDR_Bits.DM */
#define IFX_PSI5_FDR_DM_OFF (14u)

/** \brief  Length for Ifx_PSI5_FDR_Bits.RESULT */
#define IFX_PSI5_FDR_RESULT_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDR_Bits.RESULT */
#define IFX_PSI5_FDR_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDR_Bits.RESULT */
#define IFX_PSI5_FDR_RESULT_OFF (16u)

/** \brief  Length for Ifx_PSI5_FDR_Bits.STEP */
#define IFX_PSI5_FDR_STEP_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDR_Bits.STEP */
#define IFX_PSI5_FDR_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDR_Bits.STEP */
#define IFX_PSI5_FDR_STEP_OFF (0u)

/** \brief  Length for Ifx_PSI5_FDRH_Bits.DM */
#define IFX_PSI5_FDRH_DM_LEN (2u)

/** \brief  Mask for Ifx_PSI5_FDRH_Bits.DM */
#define IFX_PSI5_FDRH_DM_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_FDRH_Bits.DM */
#define IFX_PSI5_FDRH_DM_OFF (14u)

/** \brief  Length for Ifx_PSI5_FDRH_Bits.RESULT */
#define IFX_PSI5_FDRH_RESULT_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDRH_Bits.RESULT */
#define IFX_PSI5_FDRH_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDRH_Bits.RESULT */
#define IFX_PSI5_FDRH_RESULT_OFF (16u)

/** \brief  Length for Ifx_PSI5_FDRH_Bits.STEP */
#define IFX_PSI5_FDRH_STEP_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDRH_Bits.STEP */
#define IFX_PSI5_FDRH_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDRH_Bits.STEP */
#define IFX_PSI5_FDRH_STEP_OFF (0u)

/** \brief  Length for Ifx_PSI5_FDRL_Bits.DM */
#define IFX_PSI5_FDRL_DM_LEN (2u)

/** \brief  Mask for Ifx_PSI5_FDRL_Bits.DM */
#define IFX_PSI5_FDRL_DM_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_FDRL_Bits.DM */
#define IFX_PSI5_FDRL_DM_OFF (14u)

/** \brief  Length for Ifx_PSI5_FDRL_Bits.RESULT */
#define IFX_PSI5_FDRL_RESULT_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDRL_Bits.RESULT */
#define IFX_PSI5_FDRL_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDRL_Bits.RESULT */
#define IFX_PSI5_FDRL_RESULT_OFF (16u)

/** \brief  Length for Ifx_PSI5_FDRL_Bits.STEP */
#define IFX_PSI5_FDRL_STEP_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDRL_Bits.STEP */
#define IFX_PSI5_FDRL_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDRL_Bits.STEP */
#define IFX_PSI5_FDRL_STEP_OFF (0u)

/** \brief  Length for Ifx_PSI5_FDRT_Bits.DM */
#define IFX_PSI5_FDRT_DM_LEN (2u)

/** \brief  Mask for Ifx_PSI5_FDRT_Bits.DM */
#define IFX_PSI5_FDRT_DM_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5_FDRT_Bits.DM */
#define IFX_PSI5_FDRT_DM_OFF (14u)

/** \brief  Length for Ifx_PSI5_FDRT_Bits.ECEA */
#define IFX_PSI5_FDRT_ECEA_LEN (1u)

/** \brief  Mask for Ifx_PSI5_FDRT_Bits.ECEA */
#define IFX_PSI5_FDRT_ECEA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_FDRT_Bits.ECEA */
#define IFX_PSI5_FDRT_ECEA_OFF (29u)

/** \brief  Length for Ifx_PSI5_FDRT_Bits.ECEB */
#define IFX_PSI5_FDRT_ECEB_LEN (1u)

/** \brief  Mask for Ifx_PSI5_FDRT_Bits.ECEB */
#define IFX_PSI5_FDRT_ECEB_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_FDRT_Bits.ECEB */
#define IFX_PSI5_FDRT_ECEB_OFF (30u)

/** \brief  Length for Ifx_PSI5_FDRT_Bits.ECEC */
#define IFX_PSI5_FDRT_ECEC_LEN (1u)

/** \brief  Mask for Ifx_PSI5_FDRT_Bits.ECEC */
#define IFX_PSI5_FDRT_ECEC_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_FDRT_Bits.ECEC */
#define IFX_PSI5_FDRT_ECEC_OFF (31u)

/** \brief  Length for Ifx_PSI5_FDRT_Bits.ECS */
#define IFX_PSI5_FDRT_ECS_LEN (3u)

/** \brief  Mask for Ifx_PSI5_FDRT_Bits.ECS */
#define IFX_PSI5_FDRT_ECS_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_FDRT_Bits.ECS */
#define IFX_PSI5_FDRT_ECS_OFF (26u)

/** \brief  Length for Ifx_PSI5_FDRT_Bits.RESULT */
#define IFX_PSI5_FDRT_RESULT_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDRT_Bits.RESULT */
#define IFX_PSI5_FDRT_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDRT_Bits.RESULT */
#define IFX_PSI5_FDRT_RESULT_OFF (16u)

/** \brief  Length for Ifx_PSI5_FDRT_Bits.STEP */
#define IFX_PSI5_FDRT_STEP_LEN (10u)

/** \brief  Mask for Ifx_PSI5_FDRT_Bits.STEP */
#define IFX_PSI5_FDRT_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5_FDRT_Bits.STEP */
#define IFX_PSI5_FDRT_STEP_OFF (0u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.CEN0 */
#define IFX_PSI5_GCR_CEN0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.CEN0 */
#define IFX_PSI5_GCR_CEN0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.CEN0 */
#define IFX_PSI5_GCR_CEN0_OFF (16u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.CEN1 */
#define IFX_PSI5_GCR_CEN1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.CEN1 */
#define IFX_PSI5_GCR_CEN1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.CEN1 */
#define IFX_PSI5_GCR_CEN1_OFF (17u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.CEN2 */
#define IFX_PSI5_GCR_CEN2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.CEN2 */
#define IFX_PSI5_GCR_CEN2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.CEN2 */
#define IFX_PSI5_GCR_CEN2_OFF (18u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.CEN3 */
#define IFX_PSI5_GCR_CEN3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.CEN3 */
#define IFX_PSI5_GCR_CEN3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.CEN3 */
#define IFX_PSI5_GCR_CEN3_OFF (19u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.CEN4 */
#define IFX_PSI5_GCR_CEN4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.CEN4 */
#define IFX_PSI5_GCR_CEN4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.CEN4 */
#define IFX_PSI5_GCR_CEN4_OFF (20u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.CRCI */
#define IFX_PSI5_GCR_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.CRCI */
#define IFX_PSI5_GCR_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.CRCI */
#define IFX_PSI5_GCR_CRCI_OFF (0u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.ETC0 */
#define IFX_PSI5_GCR_ETC0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.ETC0 */
#define IFX_PSI5_GCR_ETC0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.ETC0 */
#define IFX_PSI5_GCR_ETC0_OFF (8u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.ETC1 */
#define IFX_PSI5_GCR_ETC1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.ETC1 */
#define IFX_PSI5_GCR_ETC1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.ETC1 */
#define IFX_PSI5_GCR_ETC1_OFF (9u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.ETC2 */
#define IFX_PSI5_GCR_ETC2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.ETC2 */
#define IFX_PSI5_GCR_ETC2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.ETC2 */
#define IFX_PSI5_GCR_ETC2_OFF (10u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.ETC3 */
#define IFX_PSI5_GCR_ETC3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.ETC3 */
#define IFX_PSI5_GCR_ETC3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.ETC3 */
#define IFX_PSI5_GCR_ETC3_OFF (11u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.ETC4 */
#define IFX_PSI5_GCR_ETC4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.ETC4 */
#define IFX_PSI5_GCR_ETC4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.ETC4 */
#define IFX_PSI5_GCR_ETC4_OFF (12u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.MEI */
#define IFX_PSI5_GCR_MEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.MEI */
#define IFX_PSI5_GCR_MEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.MEI */
#define IFX_PSI5_GCR_MEI_OFF (2u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.NBI */
#define IFX_PSI5_GCR_NBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.NBI */
#define IFX_PSI5_GCR_NBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.NBI */
#define IFX_PSI5_GCR_NBI_OFF (1u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.NFI */
#define IFX_PSI5_GCR_NFI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.NFI */
#define IFX_PSI5_GCR_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.NFI */
#define IFX_PSI5_GCR_NFI_OFF (3u)

/** \brief  Length for Ifx_PSI5_GCR_Bits.TEI */
#define IFX_PSI5_GCR_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_GCR_Bits.TEI */
#define IFX_PSI5_GCR_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_GCR_Bits.TEI */
#define IFX_PSI5_GCR_TEI_OFF (4u)

/** \brief  Length for Ifx_PSI5_ID_Bits.MODNUMBER */
#define IFX_PSI5_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_PSI5_ID_Bits.MODNUMBER */
#define IFX_PSI5_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5_ID_Bits.MODNUMBER */
#define IFX_PSI5_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_PSI5_ID_Bits.MODREV */
#define IFX_PSI5_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_PSI5_ID_Bits.MODREV */
#define IFX_PSI5_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_PSI5_ID_Bits.MODREV */
#define IFX_PSI5_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_PSI5_ID_Bits.MODTYPE */
#define IFX_PSI5_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_PSI5_ID_Bits.MODTYPE */
#define IFX_PSI5_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_PSI5_ID_Bits.MODTYPE */
#define IFX_PSI5_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_PSI5_INP_Bits.ERRI */
#define IFX_PSI5_INP_ERRI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.ERRI */
#define IFX_PSI5_INP_ERRI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.ERRI */
#define IFX_PSI5_INP_ERRI_OFF (20u)

/** \brief  Length for Ifx_PSI5_INP_Bits.FWI */
#define IFX_PSI5_INP_FWI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.FWI */
#define IFX_PSI5_INP_FWI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.FWI */
#define IFX_PSI5_INP_FWI_OFF (28u)

/** \brief  Length for Ifx_PSI5_INP_Bits.RBI */
#define IFX_PSI5_INP_RBI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.RBI */
#define IFX_PSI5_INP_RBI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.RBI */
#define IFX_PSI5_INP_RBI_OFF (8u)

/** \brief  Length for Ifx_PSI5_INP_Bits.RDI */
#define IFX_PSI5_INP_RDI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.RDI */
#define IFX_PSI5_INP_RDI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.RDI */
#define IFX_PSI5_INP_RDI_OFF (4u)

/** \brief  Length for Ifx_PSI5_INP_Bits.RSI */
#define IFX_PSI5_INP_RSI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.RSI */
#define IFX_PSI5_INP_RSI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.RSI */
#define IFX_PSI5_INP_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5_INP_Bits.SDI */
#define IFX_PSI5_INP_SDI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.SDI */
#define IFX_PSI5_INP_SDI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.SDI */
#define IFX_PSI5_INP_SDI_OFF (24u)

/** \brief  Length for Ifx_PSI5_INP_Bits.TBI */
#define IFX_PSI5_INP_TBI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.TBI */
#define IFX_PSI5_INP_TBI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.TBI */
#define IFX_PSI5_INP_TBI_OFF (16u)

/** \brief  Length for Ifx_PSI5_INP_Bits.TDI */
#define IFX_PSI5_INP_TDI_LEN (4u)

/** \brief  Mask for Ifx_PSI5_INP_Bits.TDI */
#define IFX_PSI5_INP_TDI_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_INP_Bits.TDI */
#define IFX_PSI5_INP_TDI_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.CRCI */
#define IFX_PSI5_INTCLRA_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.CRCI */
#define IFX_PSI5_INTCLRA_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.CRCI */
#define IFX_PSI5_INTCLRA_CRCI_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.FWI */
#define IFX_PSI5_INTCLRA_FWI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.FWI */
#define IFX_PSI5_INTCLRA_FWI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.FWI */
#define IFX_PSI5_INTCLRA_FWI_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.MEI */
#define IFX_PSI5_INTCLRA_MEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.MEI */
#define IFX_PSI5_INTCLRA_MEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.MEI */
#define IFX_PSI5_INTCLRA_MEI_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.NBI */
#define IFX_PSI5_INTCLRA_NBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.NBI */
#define IFX_PSI5_INTCLRA_NBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.NBI */
#define IFX_PSI5_INTCLRA_NBI_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.NFI */
#define IFX_PSI5_INTCLRA_NFI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.NFI */
#define IFX_PSI5_INTCLRA_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.NFI */
#define IFX_PSI5_INTCLRA_NFI_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.RBI */
#define IFX_PSI5_INTCLRA_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.RBI */
#define IFX_PSI5_INTCLRA_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.RBI */
#define IFX_PSI5_INTCLRA_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.RDI */
#define IFX_PSI5_INTCLRA_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.RDI */
#define IFX_PSI5_INTCLRA_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.RDI */
#define IFX_PSI5_INTCLRA_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.RMI */
#define IFX_PSI5_INTCLRA_RMI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.RMI */
#define IFX_PSI5_INTCLRA_RMI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.RMI */
#define IFX_PSI5_INTCLRA_RMI_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.RSI */
#define IFX_PSI5_INTCLRA_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.RSI */
#define IFX_PSI5_INTCLRA_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.RSI */
#define IFX_PSI5_INTCLRA_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.RUI */
#define IFX_PSI5_INTCLRA_RUI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.RUI */
#define IFX_PSI5_INTCLRA_RUI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.RUI */
#define IFX_PSI5_INTCLRA_RUI_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.TEI */
#define IFX_PSI5_INTCLRA_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.TEI */
#define IFX_PSI5_INTCLRA_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.TEI */
#define IFX_PSI5_INTCLRA_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.TOI */
#define IFX_PSI5_INTCLRA_TOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.TOI */
#define IFX_PSI5_INTCLRA_TOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.TOI */
#define IFX_PSI5_INTCLRA_TOI_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.TOOI */
#define IFX_PSI5_INTCLRA_TOOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.TOOI */
#define IFX_PSI5_INTCLRA_TOOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.TOOI */
#define IFX_PSI5_INTCLRA_TOOI_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.TPI */
#define IFX_PSI5_INTCLRA_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.TPI */
#define IFX_PSI5_INTCLRA_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.TPI */
#define IFX_PSI5_INTCLRA_TPI_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.TPOI */
#define IFX_PSI5_INTCLRA_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.TPOI */
#define IFX_PSI5_INTCLRA_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.TPOI */
#define IFX_PSI5_INTCLRA_TPOI_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.TSI */
#define IFX_PSI5_INTCLRA_TSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.TSI */
#define IFX_PSI5_INTCLRA_TSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.TSI */
#define IFX_PSI5_INTCLRA_TSI_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTCLRA_Bits.TSOI */
#define IFX_PSI5_INTCLRA_TSOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRA_Bits.TSOI */
#define IFX_PSI5_INTCLRA_TSOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRA_Bits.TSOI */
#define IFX_PSI5_INTCLRA_TSOI_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SCRI0 */
#define IFX_PSI5_INTCLRB_SCRI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SCRI0 */
#define IFX_PSI5_INTCLRB_SCRI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SCRI0 */
#define IFX_PSI5_INTCLRB_SCRI0_OFF (18u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SCRI1 */
#define IFX_PSI5_INTCLRB_SCRI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SCRI1 */
#define IFX_PSI5_INTCLRB_SCRI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SCRI1 */
#define IFX_PSI5_INTCLRB_SCRI1_OFF (19u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SCRI2 */
#define IFX_PSI5_INTCLRB_SCRI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SCRI2 */
#define IFX_PSI5_INTCLRB_SCRI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SCRI2 */
#define IFX_PSI5_INTCLRB_SCRI2_OFF (20u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SCRI3 */
#define IFX_PSI5_INTCLRB_SCRI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SCRI3 */
#define IFX_PSI5_INTCLRB_SCRI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SCRI3 */
#define IFX_PSI5_INTCLRB_SCRI3_OFF (21u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SCRI4 */
#define IFX_PSI5_INTCLRB_SCRI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SCRI4 */
#define IFX_PSI5_INTCLRB_SCRI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SCRI4 */
#define IFX_PSI5_INTCLRB_SCRI4_OFF (22u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SCRI5 */
#define IFX_PSI5_INTCLRB_SCRI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SCRI5 */
#define IFX_PSI5_INTCLRB_SCRI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SCRI5 */
#define IFX_PSI5_INTCLRB_SCRI5_OFF (23u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SDI0 */
#define IFX_PSI5_INTCLRB_SDI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SDI0 */
#define IFX_PSI5_INTCLRB_SDI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SDI0 */
#define IFX_PSI5_INTCLRB_SDI0_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SDI1 */
#define IFX_PSI5_INTCLRB_SDI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SDI1 */
#define IFX_PSI5_INTCLRB_SDI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SDI1 */
#define IFX_PSI5_INTCLRB_SDI1_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SDI2 */
#define IFX_PSI5_INTCLRB_SDI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SDI2 */
#define IFX_PSI5_INTCLRB_SDI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SDI2 */
#define IFX_PSI5_INTCLRB_SDI2_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SDI3 */
#define IFX_PSI5_INTCLRB_SDI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SDI3 */
#define IFX_PSI5_INTCLRB_SDI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SDI3 */
#define IFX_PSI5_INTCLRB_SDI3_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SDI4 */
#define IFX_PSI5_INTCLRB_SDI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SDI4 */
#define IFX_PSI5_INTCLRB_SDI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SDI4 */
#define IFX_PSI5_INTCLRB_SDI4_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SDI5 */
#define IFX_PSI5_INTCLRB_SDI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SDI5 */
#define IFX_PSI5_INTCLRB_SDI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SDI5 */
#define IFX_PSI5_INTCLRB_SDI5_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SOI0 */
#define IFX_PSI5_INTCLRB_SOI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SOI0 */
#define IFX_PSI5_INTCLRB_SOI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SOI0 */
#define IFX_PSI5_INTCLRB_SOI0_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SOI1 */
#define IFX_PSI5_INTCLRB_SOI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SOI1 */
#define IFX_PSI5_INTCLRB_SOI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SOI1 */
#define IFX_PSI5_INTCLRB_SOI1_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SOI2 */
#define IFX_PSI5_INTCLRB_SOI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SOI2 */
#define IFX_PSI5_INTCLRB_SOI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SOI2 */
#define IFX_PSI5_INTCLRB_SOI2_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SOI3 */
#define IFX_PSI5_INTCLRB_SOI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SOI3 */
#define IFX_PSI5_INTCLRB_SOI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SOI3 */
#define IFX_PSI5_INTCLRB_SOI3_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SOI4 */
#define IFX_PSI5_INTCLRB_SOI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SOI4 */
#define IFX_PSI5_INTCLRB_SOI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SOI4 */
#define IFX_PSI5_INTCLRB_SOI4_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.SOI5 */
#define IFX_PSI5_INTCLRB_SOI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.SOI5 */
#define IFX_PSI5_INTCLRB_SOI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.SOI5 */
#define IFX_PSI5_INTCLRB_SOI5_OFF (17u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.WSI0 */
#define IFX_PSI5_INTCLRB_WSI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.WSI0 */
#define IFX_PSI5_INTCLRB_WSI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.WSI0 */
#define IFX_PSI5_INTCLRB_WSI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.WSI1 */
#define IFX_PSI5_INTCLRB_WSI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.WSI1 */
#define IFX_PSI5_INTCLRB_WSI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.WSI1 */
#define IFX_PSI5_INTCLRB_WSI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.WSI2 */
#define IFX_PSI5_INTCLRB_WSI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.WSI2 */
#define IFX_PSI5_INTCLRB_WSI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.WSI2 */
#define IFX_PSI5_INTCLRB_WSI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.WSI3 */
#define IFX_PSI5_INTCLRB_WSI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.WSI3 */
#define IFX_PSI5_INTCLRB_WSI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.WSI3 */
#define IFX_PSI5_INTCLRB_WSI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.WSI4 */
#define IFX_PSI5_INTCLRB_WSI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.WSI4 */
#define IFX_PSI5_INTCLRB_WSI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.WSI4 */
#define IFX_PSI5_INTCLRB_WSI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTCLRB_Bits.WSI5 */
#define IFX_PSI5_INTCLRB_WSI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTCLRB_Bits.WSI5 */
#define IFX_PSI5_INTCLRB_WSI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTCLRB_Bits.WSI5 */
#define IFX_PSI5_INTCLRB_WSI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.CRCI */
#define IFX_PSI5_INTENA_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.CRCI */
#define IFX_PSI5_INTENA_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.CRCI */
#define IFX_PSI5_INTENA_CRCI_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.FWI */
#define IFX_PSI5_INTENA_FWI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.FWI */
#define IFX_PSI5_INTENA_FWI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.FWI */
#define IFX_PSI5_INTENA_FWI_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.MEI */
#define IFX_PSI5_INTENA_MEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.MEI */
#define IFX_PSI5_INTENA_MEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.MEI */
#define IFX_PSI5_INTENA_MEI_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.NBI */
#define IFX_PSI5_INTENA_NBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.NBI */
#define IFX_PSI5_INTENA_NBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.NBI */
#define IFX_PSI5_INTENA_NBI_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.NFI */
#define IFX_PSI5_INTENA_NFI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.NFI */
#define IFX_PSI5_INTENA_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.NFI */
#define IFX_PSI5_INTENA_NFI_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.RBI */
#define IFX_PSI5_INTENA_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.RBI */
#define IFX_PSI5_INTENA_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.RBI */
#define IFX_PSI5_INTENA_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.RDI */
#define IFX_PSI5_INTENA_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.RDI */
#define IFX_PSI5_INTENA_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.RDI */
#define IFX_PSI5_INTENA_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.RMI */
#define IFX_PSI5_INTENA_RMI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.RMI */
#define IFX_PSI5_INTENA_RMI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.RMI */
#define IFX_PSI5_INTENA_RMI_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.RSI */
#define IFX_PSI5_INTENA_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.RSI */
#define IFX_PSI5_INTENA_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.RSI */
#define IFX_PSI5_INTENA_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.RUI */
#define IFX_PSI5_INTENA_RUI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.RUI */
#define IFX_PSI5_INTENA_RUI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.RUI */
#define IFX_PSI5_INTENA_RUI_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.TEI */
#define IFX_PSI5_INTENA_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.TEI */
#define IFX_PSI5_INTENA_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.TEI */
#define IFX_PSI5_INTENA_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.TOI */
#define IFX_PSI5_INTENA_TOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.TOI */
#define IFX_PSI5_INTENA_TOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.TOI */
#define IFX_PSI5_INTENA_TOI_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.TOOI */
#define IFX_PSI5_INTENA_TOOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.TOOI */
#define IFX_PSI5_INTENA_TOOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.TOOI */
#define IFX_PSI5_INTENA_TOOI_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.TPI */
#define IFX_PSI5_INTENA_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.TPI */
#define IFX_PSI5_INTENA_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.TPI */
#define IFX_PSI5_INTENA_TPI_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.TPOI */
#define IFX_PSI5_INTENA_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.TPOI */
#define IFX_PSI5_INTENA_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.TPOI */
#define IFX_PSI5_INTENA_TPOI_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.TSI */
#define IFX_PSI5_INTENA_TSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.TSI */
#define IFX_PSI5_INTENA_TSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.TSI */
#define IFX_PSI5_INTENA_TSI_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTENA_Bits.TSOI */
#define IFX_PSI5_INTENA_TSOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENA_Bits.TSOI */
#define IFX_PSI5_INTENA_TSOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENA_Bits.TSOI */
#define IFX_PSI5_INTENA_TSOI_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SCRI0 */
#define IFX_PSI5_INTENB_SCRI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SCRI0 */
#define IFX_PSI5_INTENB_SCRI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SCRI0 */
#define IFX_PSI5_INTENB_SCRI0_OFF (18u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SCRI1 */
#define IFX_PSI5_INTENB_SCRI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SCRI1 */
#define IFX_PSI5_INTENB_SCRI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SCRI1 */
#define IFX_PSI5_INTENB_SCRI1_OFF (19u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SCRI2 */
#define IFX_PSI5_INTENB_SCRI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SCRI2 */
#define IFX_PSI5_INTENB_SCRI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SCRI2 */
#define IFX_PSI5_INTENB_SCRI2_OFF (20u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SCRI3 */
#define IFX_PSI5_INTENB_SCRI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SCRI3 */
#define IFX_PSI5_INTENB_SCRI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SCRI3 */
#define IFX_PSI5_INTENB_SCRI3_OFF (21u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SCRI4 */
#define IFX_PSI5_INTENB_SCRI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SCRI4 */
#define IFX_PSI5_INTENB_SCRI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SCRI4 */
#define IFX_PSI5_INTENB_SCRI4_OFF (22u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SCRI5 */
#define IFX_PSI5_INTENB_SCRI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SCRI5 */
#define IFX_PSI5_INTENB_SCRI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SCRI5 */
#define IFX_PSI5_INTENB_SCRI5_OFF (23u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SDI0 */
#define IFX_PSI5_INTENB_SDI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SDI0 */
#define IFX_PSI5_INTENB_SDI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SDI0 */
#define IFX_PSI5_INTENB_SDI0_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SDI1 */
#define IFX_PSI5_INTENB_SDI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SDI1 */
#define IFX_PSI5_INTENB_SDI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SDI1 */
#define IFX_PSI5_INTENB_SDI1_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SDI2 */
#define IFX_PSI5_INTENB_SDI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SDI2 */
#define IFX_PSI5_INTENB_SDI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SDI2 */
#define IFX_PSI5_INTENB_SDI2_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SDI3 */
#define IFX_PSI5_INTENB_SDI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SDI3 */
#define IFX_PSI5_INTENB_SDI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SDI3 */
#define IFX_PSI5_INTENB_SDI3_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SDI4 */
#define IFX_PSI5_INTENB_SDI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SDI4 */
#define IFX_PSI5_INTENB_SDI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SDI4 */
#define IFX_PSI5_INTENB_SDI4_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SDI5 */
#define IFX_PSI5_INTENB_SDI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SDI5 */
#define IFX_PSI5_INTENB_SDI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SDI5 */
#define IFX_PSI5_INTENB_SDI5_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SOI0 */
#define IFX_PSI5_INTENB_SOI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SOI0 */
#define IFX_PSI5_INTENB_SOI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SOI0 */
#define IFX_PSI5_INTENB_SOI0_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SOI1 */
#define IFX_PSI5_INTENB_SOI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SOI1 */
#define IFX_PSI5_INTENB_SOI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SOI1 */
#define IFX_PSI5_INTENB_SOI1_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SOI2 */
#define IFX_PSI5_INTENB_SOI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SOI2 */
#define IFX_PSI5_INTENB_SOI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SOI2 */
#define IFX_PSI5_INTENB_SOI2_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SOI3 */
#define IFX_PSI5_INTENB_SOI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SOI3 */
#define IFX_PSI5_INTENB_SOI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SOI3 */
#define IFX_PSI5_INTENB_SOI3_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SOI4 */
#define IFX_PSI5_INTENB_SOI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SOI4 */
#define IFX_PSI5_INTENB_SOI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SOI4 */
#define IFX_PSI5_INTENB_SOI4_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.SOI5 */
#define IFX_PSI5_INTENB_SOI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.SOI5 */
#define IFX_PSI5_INTENB_SOI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.SOI5 */
#define IFX_PSI5_INTENB_SOI5_OFF (17u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.WSI0 */
#define IFX_PSI5_INTENB_WSI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.WSI0 */
#define IFX_PSI5_INTENB_WSI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.WSI0 */
#define IFX_PSI5_INTENB_WSI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.WSI1 */
#define IFX_PSI5_INTENB_WSI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.WSI1 */
#define IFX_PSI5_INTENB_WSI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.WSI1 */
#define IFX_PSI5_INTENB_WSI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.WSI2 */
#define IFX_PSI5_INTENB_WSI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.WSI2 */
#define IFX_PSI5_INTENB_WSI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.WSI2 */
#define IFX_PSI5_INTENB_WSI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.WSI3 */
#define IFX_PSI5_INTENB_WSI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.WSI3 */
#define IFX_PSI5_INTENB_WSI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.WSI3 */
#define IFX_PSI5_INTENB_WSI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.WSI4 */
#define IFX_PSI5_INTENB_WSI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.WSI4 */
#define IFX_PSI5_INTENB_WSI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.WSI4 */
#define IFX_PSI5_INTENB_WSI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTENB_Bits.WSI5 */
#define IFX_PSI5_INTENB_WSI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTENB_Bits.WSI5 */
#define IFX_PSI5_INTENB_WSI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTENB_Bits.WSI5 */
#define IFX_PSI5_INTENB_WSI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.ERRI */
#define IFX_PSI5_INTOV_ERRI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.ERRI */
#define IFX_PSI5_INTOV_ERRI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.ERRI */
#define IFX_PSI5_INTOV_ERRI_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.FWI */
#define IFX_PSI5_INTOV_FWI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.FWI */
#define IFX_PSI5_INTOV_FWI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.FWI */
#define IFX_PSI5_INTOV_FWI_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.RBI */
#define IFX_PSI5_INTOV_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.RBI */
#define IFX_PSI5_INTOV_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.RBI */
#define IFX_PSI5_INTOV_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.RDI */
#define IFX_PSI5_INTOV_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.RDI */
#define IFX_PSI5_INTOV_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.RDI */
#define IFX_PSI5_INTOV_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.RSI */
#define IFX_PSI5_INTOV_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.RSI */
#define IFX_PSI5_INTOV_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.RSI */
#define IFX_PSI5_INTOV_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.SDI */
#define IFX_PSI5_INTOV_SDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.SDI */
#define IFX_PSI5_INTOV_SDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.SDI */
#define IFX_PSI5_INTOV_SDI_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.TBI */
#define IFX_PSI5_INTOV_TBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.TBI */
#define IFX_PSI5_INTOV_TBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.TBI */
#define IFX_PSI5_INTOV_TBI_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTOV_Bits.TDI */
#define IFX_PSI5_INTOV_TDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTOV_Bits.TDI */
#define IFX_PSI5_INTOV_TDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTOV_Bits.TDI */
#define IFX_PSI5_INTOV_TDI_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.CRCI */
#define IFX_PSI5_INTSETA_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.CRCI */
#define IFX_PSI5_INTSETA_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.CRCI */
#define IFX_PSI5_INTSETA_CRCI_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.FWI */
#define IFX_PSI5_INTSETA_FWI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.FWI */
#define IFX_PSI5_INTSETA_FWI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.FWI */
#define IFX_PSI5_INTSETA_FWI_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.MEI */
#define IFX_PSI5_INTSETA_MEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.MEI */
#define IFX_PSI5_INTSETA_MEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.MEI */
#define IFX_PSI5_INTSETA_MEI_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.NBI */
#define IFX_PSI5_INTSETA_NBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.NBI */
#define IFX_PSI5_INTSETA_NBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.NBI */
#define IFX_PSI5_INTSETA_NBI_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.NFI */
#define IFX_PSI5_INTSETA_NFI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.NFI */
#define IFX_PSI5_INTSETA_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.NFI */
#define IFX_PSI5_INTSETA_NFI_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.RBI */
#define IFX_PSI5_INTSETA_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.RBI */
#define IFX_PSI5_INTSETA_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.RBI */
#define IFX_PSI5_INTSETA_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.RDI */
#define IFX_PSI5_INTSETA_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.RDI */
#define IFX_PSI5_INTSETA_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.RDI */
#define IFX_PSI5_INTSETA_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.RMI */
#define IFX_PSI5_INTSETA_RMI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.RMI */
#define IFX_PSI5_INTSETA_RMI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.RMI */
#define IFX_PSI5_INTSETA_RMI_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.RSI */
#define IFX_PSI5_INTSETA_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.RSI */
#define IFX_PSI5_INTSETA_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.RSI */
#define IFX_PSI5_INTSETA_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.RUI */
#define IFX_PSI5_INTSETA_RUI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.RUI */
#define IFX_PSI5_INTSETA_RUI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.RUI */
#define IFX_PSI5_INTSETA_RUI_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.TEI */
#define IFX_PSI5_INTSETA_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.TEI */
#define IFX_PSI5_INTSETA_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.TEI */
#define IFX_PSI5_INTSETA_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.TOI */
#define IFX_PSI5_INTSETA_TOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.TOI */
#define IFX_PSI5_INTSETA_TOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.TOI */
#define IFX_PSI5_INTSETA_TOI_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.TOOI */
#define IFX_PSI5_INTSETA_TOOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.TOOI */
#define IFX_PSI5_INTSETA_TOOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.TOOI */
#define IFX_PSI5_INTSETA_TOOI_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.TPI */
#define IFX_PSI5_INTSETA_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.TPI */
#define IFX_PSI5_INTSETA_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.TPI */
#define IFX_PSI5_INTSETA_TPI_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.TPOI */
#define IFX_PSI5_INTSETA_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.TPOI */
#define IFX_PSI5_INTSETA_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.TPOI */
#define IFX_PSI5_INTSETA_TPOI_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.TSI */
#define IFX_PSI5_INTSETA_TSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.TSI */
#define IFX_PSI5_INTSETA_TSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.TSI */
#define IFX_PSI5_INTSETA_TSI_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTSETA_Bits.TSOI */
#define IFX_PSI5_INTSETA_TSOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETA_Bits.TSOI */
#define IFX_PSI5_INTSETA_TSOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETA_Bits.TSOI */
#define IFX_PSI5_INTSETA_TSOI_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SCRI0 */
#define IFX_PSI5_INTSETB_SCRI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SCRI0 */
#define IFX_PSI5_INTSETB_SCRI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SCRI0 */
#define IFX_PSI5_INTSETB_SCRI0_OFF (18u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SCRI1 */
#define IFX_PSI5_INTSETB_SCRI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SCRI1 */
#define IFX_PSI5_INTSETB_SCRI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SCRI1 */
#define IFX_PSI5_INTSETB_SCRI1_OFF (19u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SCRI2 */
#define IFX_PSI5_INTSETB_SCRI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SCRI2 */
#define IFX_PSI5_INTSETB_SCRI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SCRI2 */
#define IFX_PSI5_INTSETB_SCRI2_OFF (20u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SCRI3 */
#define IFX_PSI5_INTSETB_SCRI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SCRI3 */
#define IFX_PSI5_INTSETB_SCRI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SCRI3 */
#define IFX_PSI5_INTSETB_SCRI3_OFF (21u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SCRI4 */
#define IFX_PSI5_INTSETB_SCRI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SCRI4 */
#define IFX_PSI5_INTSETB_SCRI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SCRI4 */
#define IFX_PSI5_INTSETB_SCRI4_OFF (22u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SCRI5 */
#define IFX_PSI5_INTSETB_SCRI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SCRI5 */
#define IFX_PSI5_INTSETB_SCRI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SCRI5 */
#define IFX_PSI5_INTSETB_SCRI5_OFF (23u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SDI0 */
#define IFX_PSI5_INTSETB_SDI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SDI0 */
#define IFX_PSI5_INTSETB_SDI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SDI0 */
#define IFX_PSI5_INTSETB_SDI0_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SDI1 */
#define IFX_PSI5_INTSETB_SDI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SDI1 */
#define IFX_PSI5_INTSETB_SDI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SDI1 */
#define IFX_PSI5_INTSETB_SDI1_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SDI2 */
#define IFX_PSI5_INTSETB_SDI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SDI2 */
#define IFX_PSI5_INTSETB_SDI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SDI2 */
#define IFX_PSI5_INTSETB_SDI2_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SDI3 */
#define IFX_PSI5_INTSETB_SDI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SDI3 */
#define IFX_PSI5_INTSETB_SDI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SDI3 */
#define IFX_PSI5_INTSETB_SDI3_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SDI4 */
#define IFX_PSI5_INTSETB_SDI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SDI4 */
#define IFX_PSI5_INTSETB_SDI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SDI4 */
#define IFX_PSI5_INTSETB_SDI4_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SDI5 */
#define IFX_PSI5_INTSETB_SDI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SDI5 */
#define IFX_PSI5_INTSETB_SDI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SDI5 */
#define IFX_PSI5_INTSETB_SDI5_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SOI0 */
#define IFX_PSI5_INTSETB_SOI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SOI0 */
#define IFX_PSI5_INTSETB_SOI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SOI0 */
#define IFX_PSI5_INTSETB_SOI0_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SOI1 */
#define IFX_PSI5_INTSETB_SOI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SOI1 */
#define IFX_PSI5_INTSETB_SOI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SOI1 */
#define IFX_PSI5_INTSETB_SOI1_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SOI2 */
#define IFX_PSI5_INTSETB_SOI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SOI2 */
#define IFX_PSI5_INTSETB_SOI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SOI2 */
#define IFX_PSI5_INTSETB_SOI2_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SOI3 */
#define IFX_PSI5_INTSETB_SOI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SOI3 */
#define IFX_PSI5_INTSETB_SOI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SOI3 */
#define IFX_PSI5_INTSETB_SOI3_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SOI4 */
#define IFX_PSI5_INTSETB_SOI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SOI4 */
#define IFX_PSI5_INTSETB_SOI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SOI4 */
#define IFX_PSI5_INTSETB_SOI4_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.SOI5 */
#define IFX_PSI5_INTSETB_SOI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.SOI5 */
#define IFX_PSI5_INTSETB_SOI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.SOI5 */
#define IFX_PSI5_INTSETB_SOI5_OFF (17u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.WSI0 */
#define IFX_PSI5_INTSETB_WSI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.WSI0 */
#define IFX_PSI5_INTSETB_WSI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.WSI0 */
#define IFX_PSI5_INTSETB_WSI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.WSI1 */
#define IFX_PSI5_INTSETB_WSI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.WSI1 */
#define IFX_PSI5_INTSETB_WSI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.WSI1 */
#define IFX_PSI5_INTSETB_WSI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.WSI2 */
#define IFX_PSI5_INTSETB_WSI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.WSI2 */
#define IFX_PSI5_INTSETB_WSI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.WSI2 */
#define IFX_PSI5_INTSETB_WSI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.WSI3 */
#define IFX_PSI5_INTSETB_WSI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.WSI3 */
#define IFX_PSI5_INTSETB_WSI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.WSI3 */
#define IFX_PSI5_INTSETB_WSI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.WSI4 */
#define IFX_PSI5_INTSETB_WSI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.WSI4 */
#define IFX_PSI5_INTSETB_WSI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.WSI4 */
#define IFX_PSI5_INTSETB_WSI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTSETB_Bits.WSI5 */
#define IFX_PSI5_INTSETB_WSI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSETB_Bits.WSI5 */
#define IFX_PSI5_INTSETB_WSI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSETB_Bits.WSI5 */
#define IFX_PSI5_INTSETB_WSI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.CRCI */
#define IFX_PSI5_INTSTATA_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.CRCI */
#define IFX_PSI5_INTSTATA_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.CRCI */
#define IFX_PSI5_INTSTATA_CRCI_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.FWI */
#define IFX_PSI5_INTSTATA_FWI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.FWI */
#define IFX_PSI5_INTSTATA_FWI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.FWI */
#define IFX_PSI5_INTSTATA_FWI_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.MEI */
#define IFX_PSI5_INTSTATA_MEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.MEI */
#define IFX_PSI5_INTSTATA_MEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.MEI */
#define IFX_PSI5_INTSTATA_MEI_OFF (5u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.NBI */
#define IFX_PSI5_INTSTATA_NBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.NBI */
#define IFX_PSI5_INTSTATA_NBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.NBI */
#define IFX_PSI5_INTSTATA_NBI_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.NFI */
#define IFX_PSI5_INTSTATA_NFI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.NFI */
#define IFX_PSI5_INTSTATA_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.NFI */
#define IFX_PSI5_INTSTATA_NFI_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.RBI */
#define IFX_PSI5_INTSTATA_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.RBI */
#define IFX_PSI5_INTSTATA_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.RBI */
#define IFX_PSI5_INTSTATA_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.RDI */
#define IFX_PSI5_INTSTATA_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.RDI */
#define IFX_PSI5_INTSTATA_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.RDI */
#define IFX_PSI5_INTSTATA_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.RMI */
#define IFX_PSI5_INTSTATA_RMI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.RMI */
#define IFX_PSI5_INTSTATA_RMI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.RMI */
#define IFX_PSI5_INTSTATA_RMI_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.RSI */
#define IFX_PSI5_INTSTATA_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.RSI */
#define IFX_PSI5_INTSTATA_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.RSI */
#define IFX_PSI5_INTSTATA_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.RUI */
#define IFX_PSI5_INTSTATA_RUI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.RUI */
#define IFX_PSI5_INTSTATA_RUI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.RUI */
#define IFX_PSI5_INTSTATA_RUI_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.TEI */
#define IFX_PSI5_INTSTATA_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.TEI */
#define IFX_PSI5_INTSTATA_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.TEI */
#define IFX_PSI5_INTSTATA_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.TOI */
#define IFX_PSI5_INTSTATA_TOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.TOI */
#define IFX_PSI5_INTSTATA_TOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.TOI */
#define IFX_PSI5_INTSTATA_TOI_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.TOOI */
#define IFX_PSI5_INTSTATA_TOOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.TOOI */
#define IFX_PSI5_INTSTATA_TOOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.TOOI */
#define IFX_PSI5_INTSTATA_TOOI_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.TPI */
#define IFX_PSI5_INTSTATA_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.TPI */
#define IFX_PSI5_INTSTATA_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.TPI */
#define IFX_PSI5_INTSTATA_TPI_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.TPOI */
#define IFX_PSI5_INTSTATA_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.TPOI */
#define IFX_PSI5_INTSTATA_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.TPOI */
#define IFX_PSI5_INTSTATA_TPOI_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.TSI */
#define IFX_PSI5_INTSTATA_TSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.TSI */
#define IFX_PSI5_INTSTATA_TSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.TSI */
#define IFX_PSI5_INTSTATA_TSI_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTSTATA_Bits.TSOI */
#define IFX_PSI5_INTSTATA_TSOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATA_Bits.TSOI */
#define IFX_PSI5_INTSTATA_TSOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATA_Bits.TSOI */
#define IFX_PSI5_INTSTATA_TSOI_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SCRI0 */
#define IFX_PSI5_INTSTATB_SCRI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SCRI0 */
#define IFX_PSI5_INTSTATB_SCRI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SCRI0 */
#define IFX_PSI5_INTSTATB_SCRI0_OFF (18u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SCRI1 */
#define IFX_PSI5_INTSTATB_SCRI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SCRI1 */
#define IFX_PSI5_INTSTATB_SCRI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SCRI1 */
#define IFX_PSI5_INTSTATB_SCRI1_OFF (19u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SCRI2 */
#define IFX_PSI5_INTSTATB_SCRI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SCRI2 */
#define IFX_PSI5_INTSTATB_SCRI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SCRI2 */
#define IFX_PSI5_INTSTATB_SCRI2_OFF (20u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SCRI3 */
#define IFX_PSI5_INTSTATB_SCRI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SCRI3 */
#define IFX_PSI5_INTSTATB_SCRI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SCRI3 */
#define IFX_PSI5_INTSTATB_SCRI3_OFF (21u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SCRI4 */
#define IFX_PSI5_INTSTATB_SCRI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SCRI4 */
#define IFX_PSI5_INTSTATB_SCRI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SCRI4 */
#define IFX_PSI5_INTSTATB_SCRI4_OFF (22u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SCRI5 */
#define IFX_PSI5_INTSTATB_SCRI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SCRI5 */
#define IFX_PSI5_INTSTATB_SCRI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SCRI5 */
#define IFX_PSI5_INTSTATB_SCRI5_OFF (23u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SDI0 */
#define IFX_PSI5_INTSTATB_SDI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SDI0 */
#define IFX_PSI5_INTSTATB_SDI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SDI0 */
#define IFX_PSI5_INTSTATB_SDI0_OFF (6u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SDI1 */
#define IFX_PSI5_INTSTATB_SDI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SDI1 */
#define IFX_PSI5_INTSTATB_SDI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SDI1 */
#define IFX_PSI5_INTSTATB_SDI1_OFF (7u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SDI2 */
#define IFX_PSI5_INTSTATB_SDI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SDI2 */
#define IFX_PSI5_INTSTATB_SDI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SDI2 */
#define IFX_PSI5_INTSTATB_SDI2_OFF (8u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SDI3 */
#define IFX_PSI5_INTSTATB_SDI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SDI3 */
#define IFX_PSI5_INTSTATB_SDI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SDI3 */
#define IFX_PSI5_INTSTATB_SDI3_OFF (9u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SDI4 */
#define IFX_PSI5_INTSTATB_SDI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SDI4 */
#define IFX_PSI5_INTSTATB_SDI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SDI4 */
#define IFX_PSI5_INTSTATB_SDI4_OFF (10u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SDI5 */
#define IFX_PSI5_INTSTATB_SDI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SDI5 */
#define IFX_PSI5_INTSTATB_SDI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SDI5 */
#define IFX_PSI5_INTSTATB_SDI5_OFF (11u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SOI0 */
#define IFX_PSI5_INTSTATB_SOI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SOI0 */
#define IFX_PSI5_INTSTATB_SOI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SOI0 */
#define IFX_PSI5_INTSTATB_SOI0_OFF (12u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SOI1 */
#define IFX_PSI5_INTSTATB_SOI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SOI1 */
#define IFX_PSI5_INTSTATB_SOI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SOI1 */
#define IFX_PSI5_INTSTATB_SOI1_OFF (13u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SOI2 */
#define IFX_PSI5_INTSTATB_SOI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SOI2 */
#define IFX_PSI5_INTSTATB_SOI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SOI2 */
#define IFX_PSI5_INTSTATB_SOI2_OFF (14u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SOI3 */
#define IFX_PSI5_INTSTATB_SOI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SOI3 */
#define IFX_PSI5_INTSTATB_SOI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SOI3 */
#define IFX_PSI5_INTSTATB_SOI3_OFF (15u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SOI4 */
#define IFX_PSI5_INTSTATB_SOI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SOI4 */
#define IFX_PSI5_INTSTATB_SOI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SOI4 */
#define IFX_PSI5_INTSTATB_SOI4_OFF (16u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.SOI5 */
#define IFX_PSI5_INTSTATB_SOI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.SOI5 */
#define IFX_PSI5_INTSTATB_SOI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.SOI5 */
#define IFX_PSI5_INTSTATB_SOI5_OFF (17u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.WSI0 */
#define IFX_PSI5_INTSTATB_WSI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.WSI0 */
#define IFX_PSI5_INTSTATB_WSI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.WSI0 */
#define IFX_PSI5_INTSTATB_WSI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.WSI1 */
#define IFX_PSI5_INTSTATB_WSI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.WSI1 */
#define IFX_PSI5_INTSTATB_WSI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.WSI1 */
#define IFX_PSI5_INTSTATB_WSI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.WSI2 */
#define IFX_PSI5_INTSTATB_WSI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.WSI2 */
#define IFX_PSI5_INTSTATB_WSI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.WSI2 */
#define IFX_PSI5_INTSTATB_WSI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.WSI3 */
#define IFX_PSI5_INTSTATB_WSI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.WSI3 */
#define IFX_PSI5_INTSTATB_WSI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.WSI3 */
#define IFX_PSI5_INTSTATB_WSI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.WSI4 */
#define IFX_PSI5_INTSTATB_WSI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.WSI4 */
#define IFX_PSI5_INTSTATB_WSI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.WSI4 */
#define IFX_PSI5_INTSTATB_WSI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_INTSTATB_Bits.WSI5 */
#define IFX_PSI5_INTSTATB_WSI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_INTSTATB_Bits.WSI5 */
#define IFX_PSI5_INTSTATB_WSI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_INTSTATB_Bits.WSI5 */
#define IFX_PSI5_INTSTATB_WSI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_KRST0_Bits.RST */
#define IFX_PSI5_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_PSI5_KRST0_Bits.RST */
#define IFX_PSI5_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_KRST0_Bits.RST */
#define IFX_PSI5_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_PSI5_KRST0_Bits.RSTSTAT */
#define IFX_PSI5_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_PSI5_KRST0_Bits.RSTSTAT */
#define IFX_PSI5_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_KRST0_Bits.RSTSTAT */
#define IFX_PSI5_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_PSI5_KRST1_Bits.RST */
#define IFX_PSI5_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_PSI5_KRST1_Bits.RST */
#define IFX_PSI5_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_KRST1_Bits.RST */
#define IFX_PSI5_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_PSI5_KRSTCLR_Bits.CLR */
#define IFX_PSI5_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_PSI5_KRSTCLR_Bits.CLR */
#define IFX_PSI5_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_KRSTCLR_Bits.CLR */
#define IFX_PSI5_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI0 */
#define IFX_PSI5_MEICLR_MEI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI0 */
#define IFX_PSI5_MEICLR_MEI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI0 */
#define IFX_PSI5_MEICLR_MEI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI10 */
#define IFX_PSI5_MEICLR_MEI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI10 */
#define IFX_PSI5_MEICLR_MEI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI10 */
#define IFX_PSI5_MEICLR_MEI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI11 */
#define IFX_PSI5_MEICLR_MEI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI11 */
#define IFX_PSI5_MEICLR_MEI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI11 */
#define IFX_PSI5_MEICLR_MEI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI12 */
#define IFX_PSI5_MEICLR_MEI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI12 */
#define IFX_PSI5_MEICLR_MEI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI12 */
#define IFX_PSI5_MEICLR_MEI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI13 */
#define IFX_PSI5_MEICLR_MEI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI13 */
#define IFX_PSI5_MEICLR_MEI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI13 */
#define IFX_PSI5_MEICLR_MEI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI14 */
#define IFX_PSI5_MEICLR_MEI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI14 */
#define IFX_PSI5_MEICLR_MEI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI14 */
#define IFX_PSI5_MEICLR_MEI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI15 */
#define IFX_PSI5_MEICLR_MEI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI15 */
#define IFX_PSI5_MEICLR_MEI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI15 */
#define IFX_PSI5_MEICLR_MEI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI16 */
#define IFX_PSI5_MEICLR_MEI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI16 */
#define IFX_PSI5_MEICLR_MEI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI16 */
#define IFX_PSI5_MEICLR_MEI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI17 */
#define IFX_PSI5_MEICLR_MEI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI17 */
#define IFX_PSI5_MEICLR_MEI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI17 */
#define IFX_PSI5_MEICLR_MEI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI18 */
#define IFX_PSI5_MEICLR_MEI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI18 */
#define IFX_PSI5_MEICLR_MEI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI18 */
#define IFX_PSI5_MEICLR_MEI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI19 */
#define IFX_PSI5_MEICLR_MEI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI19 */
#define IFX_PSI5_MEICLR_MEI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI19 */
#define IFX_PSI5_MEICLR_MEI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI1 */
#define IFX_PSI5_MEICLR_MEI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI1 */
#define IFX_PSI5_MEICLR_MEI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI1 */
#define IFX_PSI5_MEICLR_MEI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI20 */
#define IFX_PSI5_MEICLR_MEI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI20 */
#define IFX_PSI5_MEICLR_MEI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI20 */
#define IFX_PSI5_MEICLR_MEI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI21 */
#define IFX_PSI5_MEICLR_MEI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI21 */
#define IFX_PSI5_MEICLR_MEI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI21 */
#define IFX_PSI5_MEICLR_MEI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI22 */
#define IFX_PSI5_MEICLR_MEI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI22 */
#define IFX_PSI5_MEICLR_MEI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI22 */
#define IFX_PSI5_MEICLR_MEI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI23 */
#define IFX_PSI5_MEICLR_MEI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI23 */
#define IFX_PSI5_MEICLR_MEI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI23 */
#define IFX_PSI5_MEICLR_MEI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI24 */
#define IFX_PSI5_MEICLR_MEI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI24 */
#define IFX_PSI5_MEICLR_MEI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI24 */
#define IFX_PSI5_MEICLR_MEI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI25 */
#define IFX_PSI5_MEICLR_MEI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI25 */
#define IFX_PSI5_MEICLR_MEI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI25 */
#define IFX_PSI5_MEICLR_MEI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI26 */
#define IFX_PSI5_MEICLR_MEI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI26 */
#define IFX_PSI5_MEICLR_MEI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI26 */
#define IFX_PSI5_MEICLR_MEI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI27 */
#define IFX_PSI5_MEICLR_MEI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI27 */
#define IFX_PSI5_MEICLR_MEI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI27 */
#define IFX_PSI5_MEICLR_MEI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI28 */
#define IFX_PSI5_MEICLR_MEI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI28 */
#define IFX_PSI5_MEICLR_MEI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI28 */
#define IFX_PSI5_MEICLR_MEI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI29 */
#define IFX_PSI5_MEICLR_MEI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI29 */
#define IFX_PSI5_MEICLR_MEI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI29 */
#define IFX_PSI5_MEICLR_MEI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI2 */
#define IFX_PSI5_MEICLR_MEI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI2 */
#define IFX_PSI5_MEICLR_MEI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI2 */
#define IFX_PSI5_MEICLR_MEI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI30 */
#define IFX_PSI5_MEICLR_MEI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI30 */
#define IFX_PSI5_MEICLR_MEI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI30 */
#define IFX_PSI5_MEICLR_MEI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI31 */
#define IFX_PSI5_MEICLR_MEI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI31 */
#define IFX_PSI5_MEICLR_MEI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI31 */
#define IFX_PSI5_MEICLR_MEI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI3 */
#define IFX_PSI5_MEICLR_MEI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI3 */
#define IFX_PSI5_MEICLR_MEI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI3 */
#define IFX_PSI5_MEICLR_MEI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI4 */
#define IFX_PSI5_MEICLR_MEI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI4 */
#define IFX_PSI5_MEICLR_MEI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI4 */
#define IFX_PSI5_MEICLR_MEI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI5 */
#define IFX_PSI5_MEICLR_MEI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI5 */
#define IFX_PSI5_MEICLR_MEI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI5 */
#define IFX_PSI5_MEICLR_MEI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI6 */
#define IFX_PSI5_MEICLR_MEI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI6 */
#define IFX_PSI5_MEICLR_MEI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI6 */
#define IFX_PSI5_MEICLR_MEI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI7 */
#define IFX_PSI5_MEICLR_MEI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI7 */
#define IFX_PSI5_MEICLR_MEI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI7 */
#define IFX_PSI5_MEICLR_MEI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI8 */
#define IFX_PSI5_MEICLR_MEI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI8 */
#define IFX_PSI5_MEICLR_MEI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI8 */
#define IFX_PSI5_MEICLR_MEI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_MEICLR_Bits.MEI9 */
#define IFX_PSI5_MEICLR_MEI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEICLR_Bits.MEI9 */
#define IFX_PSI5_MEICLR_MEI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEICLR_Bits.MEI9 */
#define IFX_PSI5_MEICLR_MEI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI0 */
#define IFX_PSI5_MEIOV_MEI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI0 */
#define IFX_PSI5_MEIOV_MEI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI0 */
#define IFX_PSI5_MEIOV_MEI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI10 */
#define IFX_PSI5_MEIOV_MEI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI10 */
#define IFX_PSI5_MEIOV_MEI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI10 */
#define IFX_PSI5_MEIOV_MEI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI11 */
#define IFX_PSI5_MEIOV_MEI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI11 */
#define IFX_PSI5_MEIOV_MEI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI11 */
#define IFX_PSI5_MEIOV_MEI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI12 */
#define IFX_PSI5_MEIOV_MEI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI12 */
#define IFX_PSI5_MEIOV_MEI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI12 */
#define IFX_PSI5_MEIOV_MEI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI13 */
#define IFX_PSI5_MEIOV_MEI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI13 */
#define IFX_PSI5_MEIOV_MEI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI13 */
#define IFX_PSI5_MEIOV_MEI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI14 */
#define IFX_PSI5_MEIOV_MEI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI14 */
#define IFX_PSI5_MEIOV_MEI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI14 */
#define IFX_PSI5_MEIOV_MEI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI15 */
#define IFX_PSI5_MEIOV_MEI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI15 */
#define IFX_PSI5_MEIOV_MEI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI15 */
#define IFX_PSI5_MEIOV_MEI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI16 */
#define IFX_PSI5_MEIOV_MEI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI16 */
#define IFX_PSI5_MEIOV_MEI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI16 */
#define IFX_PSI5_MEIOV_MEI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI17 */
#define IFX_PSI5_MEIOV_MEI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI17 */
#define IFX_PSI5_MEIOV_MEI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI17 */
#define IFX_PSI5_MEIOV_MEI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI18 */
#define IFX_PSI5_MEIOV_MEI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI18 */
#define IFX_PSI5_MEIOV_MEI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI18 */
#define IFX_PSI5_MEIOV_MEI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI19 */
#define IFX_PSI5_MEIOV_MEI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI19 */
#define IFX_PSI5_MEIOV_MEI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI19 */
#define IFX_PSI5_MEIOV_MEI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI1 */
#define IFX_PSI5_MEIOV_MEI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI1 */
#define IFX_PSI5_MEIOV_MEI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI1 */
#define IFX_PSI5_MEIOV_MEI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI20 */
#define IFX_PSI5_MEIOV_MEI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI20 */
#define IFX_PSI5_MEIOV_MEI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI20 */
#define IFX_PSI5_MEIOV_MEI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI21 */
#define IFX_PSI5_MEIOV_MEI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI21 */
#define IFX_PSI5_MEIOV_MEI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI21 */
#define IFX_PSI5_MEIOV_MEI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI22 */
#define IFX_PSI5_MEIOV_MEI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI22 */
#define IFX_PSI5_MEIOV_MEI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI22 */
#define IFX_PSI5_MEIOV_MEI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI23 */
#define IFX_PSI5_MEIOV_MEI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI23 */
#define IFX_PSI5_MEIOV_MEI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI23 */
#define IFX_PSI5_MEIOV_MEI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI24 */
#define IFX_PSI5_MEIOV_MEI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI24 */
#define IFX_PSI5_MEIOV_MEI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI24 */
#define IFX_PSI5_MEIOV_MEI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI25 */
#define IFX_PSI5_MEIOV_MEI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI25 */
#define IFX_PSI5_MEIOV_MEI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI25 */
#define IFX_PSI5_MEIOV_MEI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI26 */
#define IFX_PSI5_MEIOV_MEI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI26 */
#define IFX_PSI5_MEIOV_MEI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI26 */
#define IFX_PSI5_MEIOV_MEI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI27 */
#define IFX_PSI5_MEIOV_MEI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI27 */
#define IFX_PSI5_MEIOV_MEI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI27 */
#define IFX_PSI5_MEIOV_MEI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI28 */
#define IFX_PSI5_MEIOV_MEI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI28 */
#define IFX_PSI5_MEIOV_MEI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI28 */
#define IFX_PSI5_MEIOV_MEI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI29 */
#define IFX_PSI5_MEIOV_MEI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI29 */
#define IFX_PSI5_MEIOV_MEI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI29 */
#define IFX_PSI5_MEIOV_MEI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI2 */
#define IFX_PSI5_MEIOV_MEI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI2 */
#define IFX_PSI5_MEIOV_MEI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI2 */
#define IFX_PSI5_MEIOV_MEI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI30 */
#define IFX_PSI5_MEIOV_MEI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI30 */
#define IFX_PSI5_MEIOV_MEI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI30 */
#define IFX_PSI5_MEIOV_MEI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI31 */
#define IFX_PSI5_MEIOV_MEI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI31 */
#define IFX_PSI5_MEIOV_MEI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI31 */
#define IFX_PSI5_MEIOV_MEI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI3 */
#define IFX_PSI5_MEIOV_MEI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI3 */
#define IFX_PSI5_MEIOV_MEI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI3 */
#define IFX_PSI5_MEIOV_MEI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI4 */
#define IFX_PSI5_MEIOV_MEI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI4 */
#define IFX_PSI5_MEIOV_MEI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI4 */
#define IFX_PSI5_MEIOV_MEI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI5 */
#define IFX_PSI5_MEIOV_MEI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI5 */
#define IFX_PSI5_MEIOV_MEI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI5 */
#define IFX_PSI5_MEIOV_MEI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI6 */
#define IFX_PSI5_MEIOV_MEI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI6 */
#define IFX_PSI5_MEIOV_MEI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI6 */
#define IFX_PSI5_MEIOV_MEI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI7 */
#define IFX_PSI5_MEIOV_MEI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI7 */
#define IFX_PSI5_MEIOV_MEI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI7 */
#define IFX_PSI5_MEIOV_MEI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI8 */
#define IFX_PSI5_MEIOV_MEI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI8 */
#define IFX_PSI5_MEIOV_MEI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI8 */
#define IFX_PSI5_MEIOV_MEI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_MEIOV_Bits.MEI9 */
#define IFX_PSI5_MEIOV_MEI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEIOV_Bits.MEI9 */
#define IFX_PSI5_MEIOV_MEI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEIOV_Bits.MEI9 */
#define IFX_PSI5_MEIOV_MEI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI0 */
#define IFX_PSI5_MEISET_MEI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI0 */
#define IFX_PSI5_MEISET_MEI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI0 */
#define IFX_PSI5_MEISET_MEI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI10 */
#define IFX_PSI5_MEISET_MEI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI10 */
#define IFX_PSI5_MEISET_MEI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI10 */
#define IFX_PSI5_MEISET_MEI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI11 */
#define IFX_PSI5_MEISET_MEI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI11 */
#define IFX_PSI5_MEISET_MEI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI11 */
#define IFX_PSI5_MEISET_MEI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI12 */
#define IFX_PSI5_MEISET_MEI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI12 */
#define IFX_PSI5_MEISET_MEI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI12 */
#define IFX_PSI5_MEISET_MEI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI13 */
#define IFX_PSI5_MEISET_MEI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI13 */
#define IFX_PSI5_MEISET_MEI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI13 */
#define IFX_PSI5_MEISET_MEI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI14 */
#define IFX_PSI5_MEISET_MEI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI14 */
#define IFX_PSI5_MEISET_MEI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI14 */
#define IFX_PSI5_MEISET_MEI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI15 */
#define IFX_PSI5_MEISET_MEI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI15 */
#define IFX_PSI5_MEISET_MEI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI15 */
#define IFX_PSI5_MEISET_MEI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI16 */
#define IFX_PSI5_MEISET_MEI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI16 */
#define IFX_PSI5_MEISET_MEI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI16 */
#define IFX_PSI5_MEISET_MEI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI17 */
#define IFX_PSI5_MEISET_MEI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI17 */
#define IFX_PSI5_MEISET_MEI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI17 */
#define IFX_PSI5_MEISET_MEI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI18 */
#define IFX_PSI5_MEISET_MEI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI18 */
#define IFX_PSI5_MEISET_MEI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI18 */
#define IFX_PSI5_MEISET_MEI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI19 */
#define IFX_PSI5_MEISET_MEI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI19 */
#define IFX_PSI5_MEISET_MEI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI19 */
#define IFX_PSI5_MEISET_MEI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI1 */
#define IFX_PSI5_MEISET_MEI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI1 */
#define IFX_PSI5_MEISET_MEI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI1 */
#define IFX_PSI5_MEISET_MEI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI20 */
#define IFX_PSI5_MEISET_MEI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI20 */
#define IFX_PSI5_MEISET_MEI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI20 */
#define IFX_PSI5_MEISET_MEI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI21 */
#define IFX_PSI5_MEISET_MEI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI21 */
#define IFX_PSI5_MEISET_MEI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI21 */
#define IFX_PSI5_MEISET_MEI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI22 */
#define IFX_PSI5_MEISET_MEI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI22 */
#define IFX_PSI5_MEISET_MEI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI22 */
#define IFX_PSI5_MEISET_MEI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI23 */
#define IFX_PSI5_MEISET_MEI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI23 */
#define IFX_PSI5_MEISET_MEI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI23 */
#define IFX_PSI5_MEISET_MEI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI24 */
#define IFX_PSI5_MEISET_MEI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI24 */
#define IFX_PSI5_MEISET_MEI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI24 */
#define IFX_PSI5_MEISET_MEI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI25 */
#define IFX_PSI5_MEISET_MEI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI25 */
#define IFX_PSI5_MEISET_MEI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI25 */
#define IFX_PSI5_MEISET_MEI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI26 */
#define IFX_PSI5_MEISET_MEI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI26 */
#define IFX_PSI5_MEISET_MEI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI26 */
#define IFX_PSI5_MEISET_MEI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI27 */
#define IFX_PSI5_MEISET_MEI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI27 */
#define IFX_PSI5_MEISET_MEI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI27 */
#define IFX_PSI5_MEISET_MEI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI28 */
#define IFX_PSI5_MEISET_MEI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI28 */
#define IFX_PSI5_MEISET_MEI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI28 */
#define IFX_PSI5_MEISET_MEI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI29 */
#define IFX_PSI5_MEISET_MEI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI29 */
#define IFX_PSI5_MEISET_MEI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI29 */
#define IFX_PSI5_MEISET_MEI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI2 */
#define IFX_PSI5_MEISET_MEI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI2 */
#define IFX_PSI5_MEISET_MEI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI2 */
#define IFX_PSI5_MEISET_MEI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI30 */
#define IFX_PSI5_MEISET_MEI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI30 */
#define IFX_PSI5_MEISET_MEI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI30 */
#define IFX_PSI5_MEISET_MEI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI31 */
#define IFX_PSI5_MEISET_MEI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI31 */
#define IFX_PSI5_MEISET_MEI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI31 */
#define IFX_PSI5_MEISET_MEI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI3 */
#define IFX_PSI5_MEISET_MEI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI3 */
#define IFX_PSI5_MEISET_MEI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI3 */
#define IFX_PSI5_MEISET_MEI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI4 */
#define IFX_PSI5_MEISET_MEI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI4 */
#define IFX_PSI5_MEISET_MEI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI4 */
#define IFX_PSI5_MEISET_MEI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI5 */
#define IFX_PSI5_MEISET_MEI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI5 */
#define IFX_PSI5_MEISET_MEI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI5 */
#define IFX_PSI5_MEISET_MEI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI6 */
#define IFX_PSI5_MEISET_MEI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI6 */
#define IFX_PSI5_MEISET_MEI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI6 */
#define IFX_PSI5_MEISET_MEI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI7 */
#define IFX_PSI5_MEISET_MEI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI7 */
#define IFX_PSI5_MEISET_MEI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI7 */
#define IFX_PSI5_MEISET_MEI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI8 */
#define IFX_PSI5_MEISET_MEI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI8 */
#define IFX_PSI5_MEISET_MEI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI8 */
#define IFX_PSI5_MEISET_MEI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_MEISET_Bits.MEI9 */
#define IFX_PSI5_MEISET_MEI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_MEISET_Bits.MEI9 */
#define IFX_PSI5_MEISET_MEI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_MEISET_Bits.MEI9 */
#define IFX_PSI5_MEISET_MEI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI0 */
#define IFX_PSI5_NBICLR_NBI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI0 */
#define IFX_PSI5_NBICLR_NBI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI0 */
#define IFX_PSI5_NBICLR_NBI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI10 */
#define IFX_PSI5_NBICLR_NBI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI10 */
#define IFX_PSI5_NBICLR_NBI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI10 */
#define IFX_PSI5_NBICLR_NBI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI11 */
#define IFX_PSI5_NBICLR_NBI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI11 */
#define IFX_PSI5_NBICLR_NBI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI11 */
#define IFX_PSI5_NBICLR_NBI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI12 */
#define IFX_PSI5_NBICLR_NBI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI12 */
#define IFX_PSI5_NBICLR_NBI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI12 */
#define IFX_PSI5_NBICLR_NBI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI13 */
#define IFX_PSI5_NBICLR_NBI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI13 */
#define IFX_PSI5_NBICLR_NBI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI13 */
#define IFX_PSI5_NBICLR_NBI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI14 */
#define IFX_PSI5_NBICLR_NBI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI14 */
#define IFX_PSI5_NBICLR_NBI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI14 */
#define IFX_PSI5_NBICLR_NBI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI15 */
#define IFX_PSI5_NBICLR_NBI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI15 */
#define IFX_PSI5_NBICLR_NBI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI15 */
#define IFX_PSI5_NBICLR_NBI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI16 */
#define IFX_PSI5_NBICLR_NBI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI16 */
#define IFX_PSI5_NBICLR_NBI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI16 */
#define IFX_PSI5_NBICLR_NBI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI17 */
#define IFX_PSI5_NBICLR_NBI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI17 */
#define IFX_PSI5_NBICLR_NBI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI17 */
#define IFX_PSI5_NBICLR_NBI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI18 */
#define IFX_PSI5_NBICLR_NBI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI18 */
#define IFX_PSI5_NBICLR_NBI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI18 */
#define IFX_PSI5_NBICLR_NBI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI19 */
#define IFX_PSI5_NBICLR_NBI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI19 */
#define IFX_PSI5_NBICLR_NBI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI19 */
#define IFX_PSI5_NBICLR_NBI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI1 */
#define IFX_PSI5_NBICLR_NBI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI1 */
#define IFX_PSI5_NBICLR_NBI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI1 */
#define IFX_PSI5_NBICLR_NBI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI20 */
#define IFX_PSI5_NBICLR_NBI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI20 */
#define IFX_PSI5_NBICLR_NBI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI20 */
#define IFX_PSI5_NBICLR_NBI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI21 */
#define IFX_PSI5_NBICLR_NBI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI21 */
#define IFX_PSI5_NBICLR_NBI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI21 */
#define IFX_PSI5_NBICLR_NBI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI22 */
#define IFX_PSI5_NBICLR_NBI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI22 */
#define IFX_PSI5_NBICLR_NBI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI22 */
#define IFX_PSI5_NBICLR_NBI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI23 */
#define IFX_PSI5_NBICLR_NBI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI23 */
#define IFX_PSI5_NBICLR_NBI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI23 */
#define IFX_PSI5_NBICLR_NBI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI24 */
#define IFX_PSI5_NBICLR_NBI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI24 */
#define IFX_PSI5_NBICLR_NBI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI24 */
#define IFX_PSI5_NBICLR_NBI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI25 */
#define IFX_PSI5_NBICLR_NBI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI25 */
#define IFX_PSI5_NBICLR_NBI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI25 */
#define IFX_PSI5_NBICLR_NBI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI26 */
#define IFX_PSI5_NBICLR_NBI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI26 */
#define IFX_PSI5_NBICLR_NBI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI26 */
#define IFX_PSI5_NBICLR_NBI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI27 */
#define IFX_PSI5_NBICLR_NBI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI27 */
#define IFX_PSI5_NBICLR_NBI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI27 */
#define IFX_PSI5_NBICLR_NBI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI28 */
#define IFX_PSI5_NBICLR_NBI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI28 */
#define IFX_PSI5_NBICLR_NBI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI28 */
#define IFX_PSI5_NBICLR_NBI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI29 */
#define IFX_PSI5_NBICLR_NBI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI29 */
#define IFX_PSI5_NBICLR_NBI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI29 */
#define IFX_PSI5_NBICLR_NBI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI2 */
#define IFX_PSI5_NBICLR_NBI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI2 */
#define IFX_PSI5_NBICLR_NBI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI2 */
#define IFX_PSI5_NBICLR_NBI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI30 */
#define IFX_PSI5_NBICLR_NBI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI30 */
#define IFX_PSI5_NBICLR_NBI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI30 */
#define IFX_PSI5_NBICLR_NBI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI31 */
#define IFX_PSI5_NBICLR_NBI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI31 */
#define IFX_PSI5_NBICLR_NBI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI31 */
#define IFX_PSI5_NBICLR_NBI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI3 */
#define IFX_PSI5_NBICLR_NBI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI3 */
#define IFX_PSI5_NBICLR_NBI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI3 */
#define IFX_PSI5_NBICLR_NBI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI4 */
#define IFX_PSI5_NBICLR_NBI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI4 */
#define IFX_PSI5_NBICLR_NBI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI4 */
#define IFX_PSI5_NBICLR_NBI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI5 */
#define IFX_PSI5_NBICLR_NBI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI5 */
#define IFX_PSI5_NBICLR_NBI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI5 */
#define IFX_PSI5_NBICLR_NBI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI6 */
#define IFX_PSI5_NBICLR_NBI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI6 */
#define IFX_PSI5_NBICLR_NBI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI6 */
#define IFX_PSI5_NBICLR_NBI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI7 */
#define IFX_PSI5_NBICLR_NBI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI7 */
#define IFX_PSI5_NBICLR_NBI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI7 */
#define IFX_PSI5_NBICLR_NBI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI8 */
#define IFX_PSI5_NBICLR_NBI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI8 */
#define IFX_PSI5_NBICLR_NBI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI8 */
#define IFX_PSI5_NBICLR_NBI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_NBICLR_Bits.NBI9 */
#define IFX_PSI5_NBICLR_NBI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBICLR_Bits.NBI9 */
#define IFX_PSI5_NBICLR_NBI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBICLR_Bits.NBI9 */
#define IFX_PSI5_NBICLR_NBI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI0 */
#define IFX_PSI5_NBIOV_NBI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI0 */
#define IFX_PSI5_NBIOV_NBI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI0 */
#define IFX_PSI5_NBIOV_NBI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI10 */
#define IFX_PSI5_NBIOV_NBI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI10 */
#define IFX_PSI5_NBIOV_NBI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI10 */
#define IFX_PSI5_NBIOV_NBI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI11 */
#define IFX_PSI5_NBIOV_NBI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI11 */
#define IFX_PSI5_NBIOV_NBI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI11 */
#define IFX_PSI5_NBIOV_NBI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI12 */
#define IFX_PSI5_NBIOV_NBI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI12 */
#define IFX_PSI5_NBIOV_NBI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI12 */
#define IFX_PSI5_NBIOV_NBI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI13 */
#define IFX_PSI5_NBIOV_NBI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI13 */
#define IFX_PSI5_NBIOV_NBI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI13 */
#define IFX_PSI5_NBIOV_NBI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI14 */
#define IFX_PSI5_NBIOV_NBI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI14 */
#define IFX_PSI5_NBIOV_NBI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI14 */
#define IFX_PSI5_NBIOV_NBI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI15 */
#define IFX_PSI5_NBIOV_NBI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI15 */
#define IFX_PSI5_NBIOV_NBI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI15 */
#define IFX_PSI5_NBIOV_NBI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI16 */
#define IFX_PSI5_NBIOV_NBI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI16 */
#define IFX_PSI5_NBIOV_NBI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI16 */
#define IFX_PSI5_NBIOV_NBI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI17 */
#define IFX_PSI5_NBIOV_NBI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI17 */
#define IFX_PSI5_NBIOV_NBI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI17 */
#define IFX_PSI5_NBIOV_NBI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI18 */
#define IFX_PSI5_NBIOV_NBI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI18 */
#define IFX_PSI5_NBIOV_NBI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI18 */
#define IFX_PSI5_NBIOV_NBI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI19 */
#define IFX_PSI5_NBIOV_NBI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI19 */
#define IFX_PSI5_NBIOV_NBI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI19 */
#define IFX_PSI5_NBIOV_NBI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI1 */
#define IFX_PSI5_NBIOV_NBI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI1 */
#define IFX_PSI5_NBIOV_NBI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI1 */
#define IFX_PSI5_NBIOV_NBI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI20 */
#define IFX_PSI5_NBIOV_NBI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI20 */
#define IFX_PSI5_NBIOV_NBI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI20 */
#define IFX_PSI5_NBIOV_NBI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI21 */
#define IFX_PSI5_NBIOV_NBI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI21 */
#define IFX_PSI5_NBIOV_NBI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI21 */
#define IFX_PSI5_NBIOV_NBI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI22 */
#define IFX_PSI5_NBIOV_NBI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI22 */
#define IFX_PSI5_NBIOV_NBI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI22 */
#define IFX_PSI5_NBIOV_NBI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI23 */
#define IFX_PSI5_NBIOV_NBI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI23 */
#define IFX_PSI5_NBIOV_NBI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI23 */
#define IFX_PSI5_NBIOV_NBI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI24 */
#define IFX_PSI5_NBIOV_NBI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI24 */
#define IFX_PSI5_NBIOV_NBI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI24 */
#define IFX_PSI5_NBIOV_NBI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI25 */
#define IFX_PSI5_NBIOV_NBI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI25 */
#define IFX_PSI5_NBIOV_NBI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI25 */
#define IFX_PSI5_NBIOV_NBI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI26 */
#define IFX_PSI5_NBIOV_NBI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI26 */
#define IFX_PSI5_NBIOV_NBI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI26 */
#define IFX_PSI5_NBIOV_NBI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI27 */
#define IFX_PSI5_NBIOV_NBI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI27 */
#define IFX_PSI5_NBIOV_NBI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI27 */
#define IFX_PSI5_NBIOV_NBI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI28 */
#define IFX_PSI5_NBIOV_NBI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI28 */
#define IFX_PSI5_NBIOV_NBI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI28 */
#define IFX_PSI5_NBIOV_NBI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI29 */
#define IFX_PSI5_NBIOV_NBI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI29 */
#define IFX_PSI5_NBIOV_NBI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI29 */
#define IFX_PSI5_NBIOV_NBI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI2 */
#define IFX_PSI5_NBIOV_NBI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI2 */
#define IFX_PSI5_NBIOV_NBI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI2 */
#define IFX_PSI5_NBIOV_NBI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI30 */
#define IFX_PSI5_NBIOV_NBI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI30 */
#define IFX_PSI5_NBIOV_NBI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI30 */
#define IFX_PSI5_NBIOV_NBI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI31 */
#define IFX_PSI5_NBIOV_NBI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI31 */
#define IFX_PSI5_NBIOV_NBI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI31 */
#define IFX_PSI5_NBIOV_NBI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI3 */
#define IFX_PSI5_NBIOV_NBI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI3 */
#define IFX_PSI5_NBIOV_NBI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI3 */
#define IFX_PSI5_NBIOV_NBI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI4 */
#define IFX_PSI5_NBIOV_NBI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI4 */
#define IFX_PSI5_NBIOV_NBI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI4 */
#define IFX_PSI5_NBIOV_NBI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI5 */
#define IFX_PSI5_NBIOV_NBI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI5 */
#define IFX_PSI5_NBIOV_NBI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI5 */
#define IFX_PSI5_NBIOV_NBI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI6 */
#define IFX_PSI5_NBIOV_NBI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI6 */
#define IFX_PSI5_NBIOV_NBI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI6 */
#define IFX_PSI5_NBIOV_NBI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI7 */
#define IFX_PSI5_NBIOV_NBI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI7 */
#define IFX_PSI5_NBIOV_NBI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI7 */
#define IFX_PSI5_NBIOV_NBI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI8 */
#define IFX_PSI5_NBIOV_NBI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI8 */
#define IFX_PSI5_NBIOV_NBI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI8 */
#define IFX_PSI5_NBIOV_NBI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_NBIOV_Bits.NBI9 */
#define IFX_PSI5_NBIOV_NBI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBIOV_Bits.NBI9 */
#define IFX_PSI5_NBIOV_NBI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBIOV_Bits.NBI9 */
#define IFX_PSI5_NBIOV_NBI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI0 */
#define IFX_PSI5_NBISET_NBI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI0 */
#define IFX_PSI5_NBISET_NBI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI0 */
#define IFX_PSI5_NBISET_NBI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI10 */
#define IFX_PSI5_NBISET_NBI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI10 */
#define IFX_PSI5_NBISET_NBI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI10 */
#define IFX_PSI5_NBISET_NBI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI11 */
#define IFX_PSI5_NBISET_NBI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI11 */
#define IFX_PSI5_NBISET_NBI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI11 */
#define IFX_PSI5_NBISET_NBI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI12 */
#define IFX_PSI5_NBISET_NBI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI12 */
#define IFX_PSI5_NBISET_NBI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI12 */
#define IFX_PSI5_NBISET_NBI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI13 */
#define IFX_PSI5_NBISET_NBI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI13 */
#define IFX_PSI5_NBISET_NBI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI13 */
#define IFX_PSI5_NBISET_NBI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI14 */
#define IFX_PSI5_NBISET_NBI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI14 */
#define IFX_PSI5_NBISET_NBI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI14 */
#define IFX_PSI5_NBISET_NBI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI15 */
#define IFX_PSI5_NBISET_NBI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI15 */
#define IFX_PSI5_NBISET_NBI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI15 */
#define IFX_PSI5_NBISET_NBI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI16 */
#define IFX_PSI5_NBISET_NBI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI16 */
#define IFX_PSI5_NBISET_NBI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI16 */
#define IFX_PSI5_NBISET_NBI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI17 */
#define IFX_PSI5_NBISET_NBI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI17 */
#define IFX_PSI5_NBISET_NBI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI17 */
#define IFX_PSI5_NBISET_NBI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI18 */
#define IFX_PSI5_NBISET_NBI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI18 */
#define IFX_PSI5_NBISET_NBI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI18 */
#define IFX_PSI5_NBISET_NBI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI19 */
#define IFX_PSI5_NBISET_NBI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI19 */
#define IFX_PSI5_NBISET_NBI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI19 */
#define IFX_PSI5_NBISET_NBI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI1 */
#define IFX_PSI5_NBISET_NBI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI1 */
#define IFX_PSI5_NBISET_NBI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI1 */
#define IFX_PSI5_NBISET_NBI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI20 */
#define IFX_PSI5_NBISET_NBI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI20 */
#define IFX_PSI5_NBISET_NBI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI20 */
#define IFX_PSI5_NBISET_NBI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI21 */
#define IFX_PSI5_NBISET_NBI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI21 */
#define IFX_PSI5_NBISET_NBI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI21 */
#define IFX_PSI5_NBISET_NBI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI22 */
#define IFX_PSI5_NBISET_NBI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI22 */
#define IFX_PSI5_NBISET_NBI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI22 */
#define IFX_PSI5_NBISET_NBI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI23 */
#define IFX_PSI5_NBISET_NBI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI23 */
#define IFX_PSI5_NBISET_NBI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI23 */
#define IFX_PSI5_NBISET_NBI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI24 */
#define IFX_PSI5_NBISET_NBI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI24 */
#define IFX_PSI5_NBISET_NBI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI24 */
#define IFX_PSI5_NBISET_NBI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI25 */
#define IFX_PSI5_NBISET_NBI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI25 */
#define IFX_PSI5_NBISET_NBI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI25 */
#define IFX_PSI5_NBISET_NBI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI26 */
#define IFX_PSI5_NBISET_NBI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI26 */
#define IFX_PSI5_NBISET_NBI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI26 */
#define IFX_PSI5_NBISET_NBI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI27 */
#define IFX_PSI5_NBISET_NBI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI27 */
#define IFX_PSI5_NBISET_NBI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI27 */
#define IFX_PSI5_NBISET_NBI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI28 */
#define IFX_PSI5_NBISET_NBI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI28 */
#define IFX_PSI5_NBISET_NBI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI28 */
#define IFX_PSI5_NBISET_NBI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI29 */
#define IFX_PSI5_NBISET_NBI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI29 */
#define IFX_PSI5_NBISET_NBI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI29 */
#define IFX_PSI5_NBISET_NBI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI2 */
#define IFX_PSI5_NBISET_NBI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI2 */
#define IFX_PSI5_NBISET_NBI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI2 */
#define IFX_PSI5_NBISET_NBI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI30 */
#define IFX_PSI5_NBISET_NBI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI30 */
#define IFX_PSI5_NBISET_NBI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI30 */
#define IFX_PSI5_NBISET_NBI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI31 */
#define IFX_PSI5_NBISET_NBI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI31 */
#define IFX_PSI5_NBISET_NBI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI31 */
#define IFX_PSI5_NBISET_NBI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI3 */
#define IFX_PSI5_NBISET_NBI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI3 */
#define IFX_PSI5_NBISET_NBI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI3 */
#define IFX_PSI5_NBISET_NBI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI4 */
#define IFX_PSI5_NBISET_NBI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI4 */
#define IFX_PSI5_NBISET_NBI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI4 */
#define IFX_PSI5_NBISET_NBI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI5 */
#define IFX_PSI5_NBISET_NBI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI5 */
#define IFX_PSI5_NBISET_NBI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI5 */
#define IFX_PSI5_NBISET_NBI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI6 */
#define IFX_PSI5_NBISET_NBI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI6 */
#define IFX_PSI5_NBISET_NBI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI6 */
#define IFX_PSI5_NBISET_NBI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI7 */
#define IFX_PSI5_NBISET_NBI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI7 */
#define IFX_PSI5_NBISET_NBI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI7 */
#define IFX_PSI5_NBISET_NBI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI8 */
#define IFX_PSI5_NBISET_NBI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI8 */
#define IFX_PSI5_NBISET_NBI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI8 */
#define IFX_PSI5_NBISET_NBI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_NBISET_Bits.NBI9 */
#define IFX_PSI5_NBISET_NBI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NBISET_Bits.NBI9 */
#define IFX_PSI5_NBISET_NBI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NBISET_Bits.NBI9 */
#define IFX_PSI5_NBISET_NBI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI0 */
#define IFX_PSI5_NFICLR_NFI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI0 */
#define IFX_PSI5_NFICLR_NFI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI0 */
#define IFX_PSI5_NFICLR_NFI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI10 */
#define IFX_PSI5_NFICLR_NFI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI10 */
#define IFX_PSI5_NFICLR_NFI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI10 */
#define IFX_PSI5_NFICLR_NFI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI11 */
#define IFX_PSI5_NFICLR_NFI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI11 */
#define IFX_PSI5_NFICLR_NFI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI11 */
#define IFX_PSI5_NFICLR_NFI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI12 */
#define IFX_PSI5_NFICLR_NFI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI12 */
#define IFX_PSI5_NFICLR_NFI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI12 */
#define IFX_PSI5_NFICLR_NFI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI13 */
#define IFX_PSI5_NFICLR_NFI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI13 */
#define IFX_PSI5_NFICLR_NFI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI13 */
#define IFX_PSI5_NFICLR_NFI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI14 */
#define IFX_PSI5_NFICLR_NFI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI14 */
#define IFX_PSI5_NFICLR_NFI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI14 */
#define IFX_PSI5_NFICLR_NFI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI15 */
#define IFX_PSI5_NFICLR_NFI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI15 */
#define IFX_PSI5_NFICLR_NFI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI15 */
#define IFX_PSI5_NFICLR_NFI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI16 */
#define IFX_PSI5_NFICLR_NFI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI16 */
#define IFX_PSI5_NFICLR_NFI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI16 */
#define IFX_PSI5_NFICLR_NFI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI17 */
#define IFX_PSI5_NFICLR_NFI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI17 */
#define IFX_PSI5_NFICLR_NFI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI17 */
#define IFX_PSI5_NFICLR_NFI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI18 */
#define IFX_PSI5_NFICLR_NFI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI18 */
#define IFX_PSI5_NFICLR_NFI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI18 */
#define IFX_PSI5_NFICLR_NFI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI19 */
#define IFX_PSI5_NFICLR_NFI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI19 */
#define IFX_PSI5_NFICLR_NFI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI19 */
#define IFX_PSI5_NFICLR_NFI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI1 */
#define IFX_PSI5_NFICLR_NFI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI1 */
#define IFX_PSI5_NFICLR_NFI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI1 */
#define IFX_PSI5_NFICLR_NFI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI20 */
#define IFX_PSI5_NFICLR_NFI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI20 */
#define IFX_PSI5_NFICLR_NFI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI20 */
#define IFX_PSI5_NFICLR_NFI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI21 */
#define IFX_PSI5_NFICLR_NFI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI21 */
#define IFX_PSI5_NFICLR_NFI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI21 */
#define IFX_PSI5_NFICLR_NFI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI22 */
#define IFX_PSI5_NFICLR_NFI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI22 */
#define IFX_PSI5_NFICLR_NFI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI22 */
#define IFX_PSI5_NFICLR_NFI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI23 */
#define IFX_PSI5_NFICLR_NFI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI23 */
#define IFX_PSI5_NFICLR_NFI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI23 */
#define IFX_PSI5_NFICLR_NFI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI24 */
#define IFX_PSI5_NFICLR_NFI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI24 */
#define IFX_PSI5_NFICLR_NFI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI24 */
#define IFX_PSI5_NFICLR_NFI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI25 */
#define IFX_PSI5_NFICLR_NFI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI25 */
#define IFX_PSI5_NFICLR_NFI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI25 */
#define IFX_PSI5_NFICLR_NFI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI26 */
#define IFX_PSI5_NFICLR_NFI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI26 */
#define IFX_PSI5_NFICLR_NFI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI26 */
#define IFX_PSI5_NFICLR_NFI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI27 */
#define IFX_PSI5_NFICLR_NFI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI27 */
#define IFX_PSI5_NFICLR_NFI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI27 */
#define IFX_PSI5_NFICLR_NFI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI28 */
#define IFX_PSI5_NFICLR_NFI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI28 */
#define IFX_PSI5_NFICLR_NFI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI28 */
#define IFX_PSI5_NFICLR_NFI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI29 */
#define IFX_PSI5_NFICLR_NFI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI29 */
#define IFX_PSI5_NFICLR_NFI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI29 */
#define IFX_PSI5_NFICLR_NFI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI2 */
#define IFX_PSI5_NFICLR_NFI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI2 */
#define IFX_PSI5_NFICLR_NFI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI2 */
#define IFX_PSI5_NFICLR_NFI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI30 */
#define IFX_PSI5_NFICLR_NFI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI30 */
#define IFX_PSI5_NFICLR_NFI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI30 */
#define IFX_PSI5_NFICLR_NFI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI31 */
#define IFX_PSI5_NFICLR_NFI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI31 */
#define IFX_PSI5_NFICLR_NFI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI31 */
#define IFX_PSI5_NFICLR_NFI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI3 */
#define IFX_PSI5_NFICLR_NFI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI3 */
#define IFX_PSI5_NFICLR_NFI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI3 */
#define IFX_PSI5_NFICLR_NFI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI4 */
#define IFX_PSI5_NFICLR_NFI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI4 */
#define IFX_PSI5_NFICLR_NFI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI4 */
#define IFX_PSI5_NFICLR_NFI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI5 */
#define IFX_PSI5_NFICLR_NFI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI5 */
#define IFX_PSI5_NFICLR_NFI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI5 */
#define IFX_PSI5_NFICLR_NFI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI6 */
#define IFX_PSI5_NFICLR_NFI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI6 */
#define IFX_PSI5_NFICLR_NFI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI6 */
#define IFX_PSI5_NFICLR_NFI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI7 */
#define IFX_PSI5_NFICLR_NFI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI7 */
#define IFX_PSI5_NFICLR_NFI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI7 */
#define IFX_PSI5_NFICLR_NFI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI8 */
#define IFX_PSI5_NFICLR_NFI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI8 */
#define IFX_PSI5_NFICLR_NFI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI8 */
#define IFX_PSI5_NFICLR_NFI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_NFICLR_Bits.NFI9 */
#define IFX_PSI5_NFICLR_NFI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFICLR_Bits.NFI9 */
#define IFX_PSI5_NFICLR_NFI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFICLR_Bits.NFI9 */
#define IFX_PSI5_NFICLR_NFI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI0 */
#define IFX_PSI5_NFIOV_NFI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI0 */
#define IFX_PSI5_NFIOV_NFI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI0 */
#define IFX_PSI5_NFIOV_NFI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI10 */
#define IFX_PSI5_NFIOV_NFI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI10 */
#define IFX_PSI5_NFIOV_NFI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI10 */
#define IFX_PSI5_NFIOV_NFI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI11 */
#define IFX_PSI5_NFIOV_NFI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI11 */
#define IFX_PSI5_NFIOV_NFI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI11 */
#define IFX_PSI5_NFIOV_NFI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI12 */
#define IFX_PSI5_NFIOV_NFI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI12 */
#define IFX_PSI5_NFIOV_NFI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI12 */
#define IFX_PSI5_NFIOV_NFI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI13 */
#define IFX_PSI5_NFIOV_NFI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI13 */
#define IFX_PSI5_NFIOV_NFI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI13 */
#define IFX_PSI5_NFIOV_NFI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI14 */
#define IFX_PSI5_NFIOV_NFI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI14 */
#define IFX_PSI5_NFIOV_NFI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI14 */
#define IFX_PSI5_NFIOV_NFI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI15 */
#define IFX_PSI5_NFIOV_NFI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI15 */
#define IFX_PSI5_NFIOV_NFI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI15 */
#define IFX_PSI5_NFIOV_NFI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI16 */
#define IFX_PSI5_NFIOV_NFI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI16 */
#define IFX_PSI5_NFIOV_NFI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI16 */
#define IFX_PSI5_NFIOV_NFI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI17 */
#define IFX_PSI5_NFIOV_NFI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI17 */
#define IFX_PSI5_NFIOV_NFI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI17 */
#define IFX_PSI5_NFIOV_NFI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI18 */
#define IFX_PSI5_NFIOV_NFI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI18 */
#define IFX_PSI5_NFIOV_NFI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI18 */
#define IFX_PSI5_NFIOV_NFI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI19 */
#define IFX_PSI5_NFIOV_NFI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI19 */
#define IFX_PSI5_NFIOV_NFI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI19 */
#define IFX_PSI5_NFIOV_NFI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI1 */
#define IFX_PSI5_NFIOV_NFI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI1 */
#define IFX_PSI5_NFIOV_NFI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI1 */
#define IFX_PSI5_NFIOV_NFI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI20 */
#define IFX_PSI5_NFIOV_NFI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI20 */
#define IFX_PSI5_NFIOV_NFI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI20 */
#define IFX_PSI5_NFIOV_NFI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI21 */
#define IFX_PSI5_NFIOV_NFI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI21 */
#define IFX_PSI5_NFIOV_NFI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI21 */
#define IFX_PSI5_NFIOV_NFI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI22 */
#define IFX_PSI5_NFIOV_NFI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI22 */
#define IFX_PSI5_NFIOV_NFI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI22 */
#define IFX_PSI5_NFIOV_NFI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI23 */
#define IFX_PSI5_NFIOV_NFI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI23 */
#define IFX_PSI5_NFIOV_NFI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI23 */
#define IFX_PSI5_NFIOV_NFI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI24 */
#define IFX_PSI5_NFIOV_NFI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI24 */
#define IFX_PSI5_NFIOV_NFI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI24 */
#define IFX_PSI5_NFIOV_NFI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI25 */
#define IFX_PSI5_NFIOV_NFI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI25 */
#define IFX_PSI5_NFIOV_NFI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI25 */
#define IFX_PSI5_NFIOV_NFI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI26 */
#define IFX_PSI5_NFIOV_NFI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI26 */
#define IFX_PSI5_NFIOV_NFI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI26 */
#define IFX_PSI5_NFIOV_NFI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI27 */
#define IFX_PSI5_NFIOV_NFI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI27 */
#define IFX_PSI5_NFIOV_NFI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI27 */
#define IFX_PSI5_NFIOV_NFI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI28 */
#define IFX_PSI5_NFIOV_NFI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI28 */
#define IFX_PSI5_NFIOV_NFI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI28 */
#define IFX_PSI5_NFIOV_NFI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI29 */
#define IFX_PSI5_NFIOV_NFI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI29 */
#define IFX_PSI5_NFIOV_NFI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI29 */
#define IFX_PSI5_NFIOV_NFI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI2 */
#define IFX_PSI5_NFIOV_NFI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI2 */
#define IFX_PSI5_NFIOV_NFI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI2 */
#define IFX_PSI5_NFIOV_NFI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI30 */
#define IFX_PSI5_NFIOV_NFI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI30 */
#define IFX_PSI5_NFIOV_NFI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI30 */
#define IFX_PSI5_NFIOV_NFI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI31 */
#define IFX_PSI5_NFIOV_NFI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI31 */
#define IFX_PSI5_NFIOV_NFI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI31 */
#define IFX_PSI5_NFIOV_NFI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI3 */
#define IFX_PSI5_NFIOV_NFI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI3 */
#define IFX_PSI5_NFIOV_NFI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI3 */
#define IFX_PSI5_NFIOV_NFI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI4 */
#define IFX_PSI5_NFIOV_NFI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI4 */
#define IFX_PSI5_NFIOV_NFI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI4 */
#define IFX_PSI5_NFIOV_NFI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI5 */
#define IFX_PSI5_NFIOV_NFI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI5 */
#define IFX_PSI5_NFIOV_NFI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI5 */
#define IFX_PSI5_NFIOV_NFI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI6 */
#define IFX_PSI5_NFIOV_NFI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI6 */
#define IFX_PSI5_NFIOV_NFI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI6 */
#define IFX_PSI5_NFIOV_NFI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI7 */
#define IFX_PSI5_NFIOV_NFI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI7 */
#define IFX_PSI5_NFIOV_NFI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI7 */
#define IFX_PSI5_NFIOV_NFI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI8 */
#define IFX_PSI5_NFIOV_NFI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI8 */
#define IFX_PSI5_NFIOV_NFI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI8 */
#define IFX_PSI5_NFIOV_NFI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_NFIOV_Bits.NFI9 */
#define IFX_PSI5_NFIOV_NFI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFIOV_Bits.NFI9 */
#define IFX_PSI5_NFIOV_NFI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFIOV_Bits.NFI9 */
#define IFX_PSI5_NFIOV_NFI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI0 */
#define IFX_PSI5_NFISET_NFI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI0 */
#define IFX_PSI5_NFISET_NFI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI0 */
#define IFX_PSI5_NFISET_NFI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI10 */
#define IFX_PSI5_NFISET_NFI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI10 */
#define IFX_PSI5_NFISET_NFI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI10 */
#define IFX_PSI5_NFISET_NFI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI11 */
#define IFX_PSI5_NFISET_NFI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI11 */
#define IFX_PSI5_NFISET_NFI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI11 */
#define IFX_PSI5_NFISET_NFI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI12 */
#define IFX_PSI5_NFISET_NFI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI12 */
#define IFX_PSI5_NFISET_NFI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI12 */
#define IFX_PSI5_NFISET_NFI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI13 */
#define IFX_PSI5_NFISET_NFI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI13 */
#define IFX_PSI5_NFISET_NFI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI13 */
#define IFX_PSI5_NFISET_NFI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI14 */
#define IFX_PSI5_NFISET_NFI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI14 */
#define IFX_PSI5_NFISET_NFI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI14 */
#define IFX_PSI5_NFISET_NFI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI15 */
#define IFX_PSI5_NFISET_NFI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI15 */
#define IFX_PSI5_NFISET_NFI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI15 */
#define IFX_PSI5_NFISET_NFI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI16 */
#define IFX_PSI5_NFISET_NFI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI16 */
#define IFX_PSI5_NFISET_NFI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI16 */
#define IFX_PSI5_NFISET_NFI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI17 */
#define IFX_PSI5_NFISET_NFI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI17 */
#define IFX_PSI5_NFISET_NFI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI17 */
#define IFX_PSI5_NFISET_NFI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI18 */
#define IFX_PSI5_NFISET_NFI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI18 */
#define IFX_PSI5_NFISET_NFI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI18 */
#define IFX_PSI5_NFISET_NFI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI19 */
#define IFX_PSI5_NFISET_NFI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI19 */
#define IFX_PSI5_NFISET_NFI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI19 */
#define IFX_PSI5_NFISET_NFI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI1 */
#define IFX_PSI5_NFISET_NFI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI1 */
#define IFX_PSI5_NFISET_NFI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI1 */
#define IFX_PSI5_NFISET_NFI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI20 */
#define IFX_PSI5_NFISET_NFI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI20 */
#define IFX_PSI5_NFISET_NFI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI20 */
#define IFX_PSI5_NFISET_NFI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI21 */
#define IFX_PSI5_NFISET_NFI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI21 */
#define IFX_PSI5_NFISET_NFI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI21 */
#define IFX_PSI5_NFISET_NFI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI22 */
#define IFX_PSI5_NFISET_NFI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI22 */
#define IFX_PSI5_NFISET_NFI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI22 */
#define IFX_PSI5_NFISET_NFI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI23 */
#define IFX_PSI5_NFISET_NFI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI23 */
#define IFX_PSI5_NFISET_NFI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI23 */
#define IFX_PSI5_NFISET_NFI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI24 */
#define IFX_PSI5_NFISET_NFI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI24 */
#define IFX_PSI5_NFISET_NFI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI24 */
#define IFX_PSI5_NFISET_NFI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI25 */
#define IFX_PSI5_NFISET_NFI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI25 */
#define IFX_PSI5_NFISET_NFI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI25 */
#define IFX_PSI5_NFISET_NFI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI26 */
#define IFX_PSI5_NFISET_NFI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI26 */
#define IFX_PSI5_NFISET_NFI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI26 */
#define IFX_PSI5_NFISET_NFI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI27 */
#define IFX_PSI5_NFISET_NFI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI27 */
#define IFX_PSI5_NFISET_NFI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI27 */
#define IFX_PSI5_NFISET_NFI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI28 */
#define IFX_PSI5_NFISET_NFI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI28 */
#define IFX_PSI5_NFISET_NFI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI28 */
#define IFX_PSI5_NFISET_NFI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI29 */
#define IFX_PSI5_NFISET_NFI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI29 */
#define IFX_PSI5_NFISET_NFI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI29 */
#define IFX_PSI5_NFISET_NFI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI2 */
#define IFX_PSI5_NFISET_NFI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI2 */
#define IFX_PSI5_NFISET_NFI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI2 */
#define IFX_PSI5_NFISET_NFI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI30 */
#define IFX_PSI5_NFISET_NFI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI30 */
#define IFX_PSI5_NFISET_NFI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI30 */
#define IFX_PSI5_NFISET_NFI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI31 */
#define IFX_PSI5_NFISET_NFI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI31 */
#define IFX_PSI5_NFISET_NFI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI31 */
#define IFX_PSI5_NFISET_NFI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI3 */
#define IFX_PSI5_NFISET_NFI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI3 */
#define IFX_PSI5_NFISET_NFI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI3 */
#define IFX_PSI5_NFISET_NFI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI4 */
#define IFX_PSI5_NFISET_NFI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI4 */
#define IFX_PSI5_NFISET_NFI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI4 */
#define IFX_PSI5_NFISET_NFI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI5 */
#define IFX_PSI5_NFISET_NFI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI5 */
#define IFX_PSI5_NFISET_NFI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI5 */
#define IFX_PSI5_NFISET_NFI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI6 */
#define IFX_PSI5_NFISET_NFI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI6 */
#define IFX_PSI5_NFISET_NFI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI6 */
#define IFX_PSI5_NFISET_NFI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI7 */
#define IFX_PSI5_NFISET_NFI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI7 */
#define IFX_PSI5_NFISET_NFI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI7 */
#define IFX_PSI5_NFISET_NFI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI8 */
#define IFX_PSI5_NFISET_NFI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI8 */
#define IFX_PSI5_NFISET_NFI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI8 */
#define IFX_PSI5_NFISET_NFI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_NFISET_Bits.NFI9 */
#define IFX_PSI5_NFISET_NFI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_NFISET_Bits.NFI9 */
#define IFX_PSI5_NFISET_NFI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_NFISET_Bits.NFI9 */
#define IFX_PSI5_NFISET_NFI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_OCS_Bits.SUS */
#define IFX_PSI5_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_PSI5_OCS_Bits.SUS */
#define IFX_PSI5_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5_OCS_Bits.SUS */
#define IFX_PSI5_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_PSI5_OCS_Bits.SUS_P */
#define IFX_PSI5_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_PSI5_OCS_Bits.SUS_P */
#define IFX_PSI5_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_OCS_Bits.SUS_P */
#define IFX_PSI5_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_PSI5_OCS_Bits.SUSSTA */
#define IFX_PSI5_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_PSI5_OCS_Bits.SUSSTA */
#define IFX_PSI5_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_OCS_Bits.SUSSTA */
#define IFX_PSI5_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_PSI5_RDF_Bits.RD */
#define IFX_PSI5_RDF_RD_LEN (32u)

/** \brief  Mask for Ifx_PSI5_RDF_Bits.RD */
#define IFX_PSI5_RDF_RD_MSK (0xffffffffu)

/** \brief  Offset for Ifx_PSI5_RDF_Bits.RD */
#define IFX_PSI5_RDF_RD_OFF (0u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI0 */
#define IFX_PSI5_RDICLR_RDI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI0 */
#define IFX_PSI5_RDICLR_RDI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI0 */
#define IFX_PSI5_RDICLR_RDI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI10 */
#define IFX_PSI5_RDICLR_RDI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI10 */
#define IFX_PSI5_RDICLR_RDI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI10 */
#define IFX_PSI5_RDICLR_RDI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI11 */
#define IFX_PSI5_RDICLR_RDI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI11 */
#define IFX_PSI5_RDICLR_RDI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI11 */
#define IFX_PSI5_RDICLR_RDI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI12 */
#define IFX_PSI5_RDICLR_RDI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI12 */
#define IFX_PSI5_RDICLR_RDI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI12 */
#define IFX_PSI5_RDICLR_RDI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI13 */
#define IFX_PSI5_RDICLR_RDI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI13 */
#define IFX_PSI5_RDICLR_RDI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI13 */
#define IFX_PSI5_RDICLR_RDI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI14 */
#define IFX_PSI5_RDICLR_RDI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI14 */
#define IFX_PSI5_RDICLR_RDI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI14 */
#define IFX_PSI5_RDICLR_RDI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI15 */
#define IFX_PSI5_RDICLR_RDI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI15 */
#define IFX_PSI5_RDICLR_RDI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI15 */
#define IFX_PSI5_RDICLR_RDI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI16 */
#define IFX_PSI5_RDICLR_RDI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI16 */
#define IFX_PSI5_RDICLR_RDI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI16 */
#define IFX_PSI5_RDICLR_RDI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI17 */
#define IFX_PSI5_RDICLR_RDI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI17 */
#define IFX_PSI5_RDICLR_RDI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI17 */
#define IFX_PSI5_RDICLR_RDI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI18 */
#define IFX_PSI5_RDICLR_RDI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI18 */
#define IFX_PSI5_RDICLR_RDI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI18 */
#define IFX_PSI5_RDICLR_RDI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI19 */
#define IFX_PSI5_RDICLR_RDI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI19 */
#define IFX_PSI5_RDICLR_RDI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI19 */
#define IFX_PSI5_RDICLR_RDI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI1 */
#define IFX_PSI5_RDICLR_RDI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI1 */
#define IFX_PSI5_RDICLR_RDI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI1 */
#define IFX_PSI5_RDICLR_RDI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI20 */
#define IFX_PSI5_RDICLR_RDI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI20 */
#define IFX_PSI5_RDICLR_RDI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI20 */
#define IFX_PSI5_RDICLR_RDI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI21 */
#define IFX_PSI5_RDICLR_RDI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI21 */
#define IFX_PSI5_RDICLR_RDI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI21 */
#define IFX_PSI5_RDICLR_RDI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI22 */
#define IFX_PSI5_RDICLR_RDI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI22 */
#define IFX_PSI5_RDICLR_RDI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI22 */
#define IFX_PSI5_RDICLR_RDI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI23 */
#define IFX_PSI5_RDICLR_RDI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI23 */
#define IFX_PSI5_RDICLR_RDI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI23 */
#define IFX_PSI5_RDICLR_RDI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI24 */
#define IFX_PSI5_RDICLR_RDI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI24 */
#define IFX_PSI5_RDICLR_RDI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI24 */
#define IFX_PSI5_RDICLR_RDI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI25 */
#define IFX_PSI5_RDICLR_RDI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI25 */
#define IFX_PSI5_RDICLR_RDI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI25 */
#define IFX_PSI5_RDICLR_RDI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI26 */
#define IFX_PSI5_RDICLR_RDI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI26 */
#define IFX_PSI5_RDICLR_RDI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI26 */
#define IFX_PSI5_RDICLR_RDI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI27 */
#define IFX_PSI5_RDICLR_RDI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI27 */
#define IFX_PSI5_RDICLR_RDI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI27 */
#define IFX_PSI5_RDICLR_RDI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI28 */
#define IFX_PSI5_RDICLR_RDI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI28 */
#define IFX_PSI5_RDICLR_RDI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI28 */
#define IFX_PSI5_RDICLR_RDI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI29 */
#define IFX_PSI5_RDICLR_RDI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI29 */
#define IFX_PSI5_RDICLR_RDI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI29 */
#define IFX_PSI5_RDICLR_RDI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI2 */
#define IFX_PSI5_RDICLR_RDI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI2 */
#define IFX_PSI5_RDICLR_RDI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI2 */
#define IFX_PSI5_RDICLR_RDI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI30 */
#define IFX_PSI5_RDICLR_RDI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI30 */
#define IFX_PSI5_RDICLR_RDI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI30 */
#define IFX_PSI5_RDICLR_RDI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI31 */
#define IFX_PSI5_RDICLR_RDI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI31 */
#define IFX_PSI5_RDICLR_RDI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI31 */
#define IFX_PSI5_RDICLR_RDI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI3 */
#define IFX_PSI5_RDICLR_RDI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI3 */
#define IFX_PSI5_RDICLR_RDI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI3 */
#define IFX_PSI5_RDICLR_RDI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI4 */
#define IFX_PSI5_RDICLR_RDI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI4 */
#define IFX_PSI5_RDICLR_RDI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI4 */
#define IFX_PSI5_RDICLR_RDI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI5 */
#define IFX_PSI5_RDICLR_RDI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI5 */
#define IFX_PSI5_RDICLR_RDI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI5 */
#define IFX_PSI5_RDICLR_RDI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI6 */
#define IFX_PSI5_RDICLR_RDI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI6 */
#define IFX_PSI5_RDICLR_RDI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI6 */
#define IFX_PSI5_RDICLR_RDI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI7 */
#define IFX_PSI5_RDICLR_RDI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI7 */
#define IFX_PSI5_RDICLR_RDI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI7 */
#define IFX_PSI5_RDICLR_RDI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI8 */
#define IFX_PSI5_RDICLR_RDI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI8 */
#define IFX_PSI5_RDICLR_RDI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI8 */
#define IFX_PSI5_RDICLR_RDI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RDICLR_Bits.RDI9 */
#define IFX_PSI5_RDICLR_RDI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDICLR_Bits.RDI9 */
#define IFX_PSI5_RDICLR_RDI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDICLR_Bits.RDI9 */
#define IFX_PSI5_RDICLR_RDI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI0 */
#define IFX_PSI5_RDIOV_RDI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI0 */
#define IFX_PSI5_RDIOV_RDI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI0 */
#define IFX_PSI5_RDIOV_RDI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI10 */
#define IFX_PSI5_RDIOV_RDI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI10 */
#define IFX_PSI5_RDIOV_RDI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI10 */
#define IFX_PSI5_RDIOV_RDI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI11 */
#define IFX_PSI5_RDIOV_RDI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI11 */
#define IFX_PSI5_RDIOV_RDI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI11 */
#define IFX_PSI5_RDIOV_RDI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI12 */
#define IFX_PSI5_RDIOV_RDI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI12 */
#define IFX_PSI5_RDIOV_RDI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI12 */
#define IFX_PSI5_RDIOV_RDI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI13 */
#define IFX_PSI5_RDIOV_RDI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI13 */
#define IFX_PSI5_RDIOV_RDI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI13 */
#define IFX_PSI5_RDIOV_RDI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI14 */
#define IFX_PSI5_RDIOV_RDI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI14 */
#define IFX_PSI5_RDIOV_RDI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI14 */
#define IFX_PSI5_RDIOV_RDI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI15 */
#define IFX_PSI5_RDIOV_RDI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI15 */
#define IFX_PSI5_RDIOV_RDI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI15 */
#define IFX_PSI5_RDIOV_RDI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI16 */
#define IFX_PSI5_RDIOV_RDI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI16 */
#define IFX_PSI5_RDIOV_RDI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI16 */
#define IFX_PSI5_RDIOV_RDI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI17 */
#define IFX_PSI5_RDIOV_RDI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI17 */
#define IFX_PSI5_RDIOV_RDI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI17 */
#define IFX_PSI5_RDIOV_RDI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI18 */
#define IFX_PSI5_RDIOV_RDI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI18 */
#define IFX_PSI5_RDIOV_RDI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI18 */
#define IFX_PSI5_RDIOV_RDI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI19 */
#define IFX_PSI5_RDIOV_RDI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI19 */
#define IFX_PSI5_RDIOV_RDI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI19 */
#define IFX_PSI5_RDIOV_RDI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI1 */
#define IFX_PSI5_RDIOV_RDI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI1 */
#define IFX_PSI5_RDIOV_RDI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI1 */
#define IFX_PSI5_RDIOV_RDI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI20 */
#define IFX_PSI5_RDIOV_RDI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI20 */
#define IFX_PSI5_RDIOV_RDI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI20 */
#define IFX_PSI5_RDIOV_RDI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI21 */
#define IFX_PSI5_RDIOV_RDI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI21 */
#define IFX_PSI5_RDIOV_RDI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI21 */
#define IFX_PSI5_RDIOV_RDI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI22 */
#define IFX_PSI5_RDIOV_RDI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI22 */
#define IFX_PSI5_RDIOV_RDI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI22 */
#define IFX_PSI5_RDIOV_RDI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI23 */
#define IFX_PSI5_RDIOV_RDI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI23 */
#define IFX_PSI5_RDIOV_RDI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI23 */
#define IFX_PSI5_RDIOV_RDI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI24 */
#define IFX_PSI5_RDIOV_RDI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI24 */
#define IFX_PSI5_RDIOV_RDI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI24 */
#define IFX_PSI5_RDIOV_RDI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI25 */
#define IFX_PSI5_RDIOV_RDI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI25 */
#define IFX_PSI5_RDIOV_RDI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI25 */
#define IFX_PSI5_RDIOV_RDI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI26 */
#define IFX_PSI5_RDIOV_RDI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI26 */
#define IFX_PSI5_RDIOV_RDI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI26 */
#define IFX_PSI5_RDIOV_RDI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI27 */
#define IFX_PSI5_RDIOV_RDI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI27 */
#define IFX_PSI5_RDIOV_RDI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI27 */
#define IFX_PSI5_RDIOV_RDI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI28 */
#define IFX_PSI5_RDIOV_RDI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI28 */
#define IFX_PSI5_RDIOV_RDI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI28 */
#define IFX_PSI5_RDIOV_RDI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI29 */
#define IFX_PSI5_RDIOV_RDI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI29 */
#define IFX_PSI5_RDIOV_RDI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI29 */
#define IFX_PSI5_RDIOV_RDI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI2 */
#define IFX_PSI5_RDIOV_RDI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI2 */
#define IFX_PSI5_RDIOV_RDI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI2 */
#define IFX_PSI5_RDIOV_RDI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI30 */
#define IFX_PSI5_RDIOV_RDI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI30 */
#define IFX_PSI5_RDIOV_RDI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI30 */
#define IFX_PSI5_RDIOV_RDI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI31 */
#define IFX_PSI5_RDIOV_RDI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI31 */
#define IFX_PSI5_RDIOV_RDI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI31 */
#define IFX_PSI5_RDIOV_RDI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI3 */
#define IFX_PSI5_RDIOV_RDI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI3 */
#define IFX_PSI5_RDIOV_RDI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI3 */
#define IFX_PSI5_RDIOV_RDI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI4 */
#define IFX_PSI5_RDIOV_RDI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI4 */
#define IFX_PSI5_RDIOV_RDI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI4 */
#define IFX_PSI5_RDIOV_RDI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI5 */
#define IFX_PSI5_RDIOV_RDI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI5 */
#define IFX_PSI5_RDIOV_RDI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI5 */
#define IFX_PSI5_RDIOV_RDI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI6 */
#define IFX_PSI5_RDIOV_RDI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI6 */
#define IFX_PSI5_RDIOV_RDI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI6 */
#define IFX_PSI5_RDIOV_RDI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI7 */
#define IFX_PSI5_RDIOV_RDI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI7 */
#define IFX_PSI5_RDIOV_RDI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI7 */
#define IFX_PSI5_RDIOV_RDI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI8 */
#define IFX_PSI5_RDIOV_RDI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI8 */
#define IFX_PSI5_RDIOV_RDI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI8 */
#define IFX_PSI5_RDIOV_RDI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RDIOV_Bits.RDI9 */
#define IFX_PSI5_RDIOV_RDI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDIOV_Bits.RDI9 */
#define IFX_PSI5_RDIOV_RDI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDIOV_Bits.RDI9 */
#define IFX_PSI5_RDIOV_RDI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI0 */
#define IFX_PSI5_RDISET_RDI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI0 */
#define IFX_PSI5_RDISET_RDI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI0 */
#define IFX_PSI5_RDISET_RDI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI10 */
#define IFX_PSI5_RDISET_RDI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI10 */
#define IFX_PSI5_RDISET_RDI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI10 */
#define IFX_PSI5_RDISET_RDI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI11 */
#define IFX_PSI5_RDISET_RDI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI11 */
#define IFX_PSI5_RDISET_RDI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI11 */
#define IFX_PSI5_RDISET_RDI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI12 */
#define IFX_PSI5_RDISET_RDI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI12 */
#define IFX_PSI5_RDISET_RDI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI12 */
#define IFX_PSI5_RDISET_RDI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI13 */
#define IFX_PSI5_RDISET_RDI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI13 */
#define IFX_PSI5_RDISET_RDI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI13 */
#define IFX_PSI5_RDISET_RDI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI14 */
#define IFX_PSI5_RDISET_RDI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI14 */
#define IFX_PSI5_RDISET_RDI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI14 */
#define IFX_PSI5_RDISET_RDI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI15 */
#define IFX_PSI5_RDISET_RDI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI15 */
#define IFX_PSI5_RDISET_RDI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI15 */
#define IFX_PSI5_RDISET_RDI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI16 */
#define IFX_PSI5_RDISET_RDI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI16 */
#define IFX_PSI5_RDISET_RDI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI16 */
#define IFX_PSI5_RDISET_RDI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI17 */
#define IFX_PSI5_RDISET_RDI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI17 */
#define IFX_PSI5_RDISET_RDI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI17 */
#define IFX_PSI5_RDISET_RDI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI18 */
#define IFX_PSI5_RDISET_RDI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI18 */
#define IFX_PSI5_RDISET_RDI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI18 */
#define IFX_PSI5_RDISET_RDI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI19 */
#define IFX_PSI5_RDISET_RDI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI19 */
#define IFX_PSI5_RDISET_RDI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI19 */
#define IFX_PSI5_RDISET_RDI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI1 */
#define IFX_PSI5_RDISET_RDI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI1 */
#define IFX_PSI5_RDISET_RDI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI1 */
#define IFX_PSI5_RDISET_RDI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI20 */
#define IFX_PSI5_RDISET_RDI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI20 */
#define IFX_PSI5_RDISET_RDI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI20 */
#define IFX_PSI5_RDISET_RDI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI21 */
#define IFX_PSI5_RDISET_RDI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI21 */
#define IFX_PSI5_RDISET_RDI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI21 */
#define IFX_PSI5_RDISET_RDI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI22 */
#define IFX_PSI5_RDISET_RDI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI22 */
#define IFX_PSI5_RDISET_RDI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI22 */
#define IFX_PSI5_RDISET_RDI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI23 */
#define IFX_PSI5_RDISET_RDI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI23 */
#define IFX_PSI5_RDISET_RDI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI23 */
#define IFX_PSI5_RDISET_RDI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI24 */
#define IFX_PSI5_RDISET_RDI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI24 */
#define IFX_PSI5_RDISET_RDI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI24 */
#define IFX_PSI5_RDISET_RDI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI25 */
#define IFX_PSI5_RDISET_RDI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI25 */
#define IFX_PSI5_RDISET_RDI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI25 */
#define IFX_PSI5_RDISET_RDI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI26 */
#define IFX_PSI5_RDISET_RDI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI26 */
#define IFX_PSI5_RDISET_RDI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI26 */
#define IFX_PSI5_RDISET_RDI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI27 */
#define IFX_PSI5_RDISET_RDI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI27 */
#define IFX_PSI5_RDISET_RDI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI27 */
#define IFX_PSI5_RDISET_RDI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI28 */
#define IFX_PSI5_RDISET_RDI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI28 */
#define IFX_PSI5_RDISET_RDI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI28 */
#define IFX_PSI5_RDISET_RDI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI29 */
#define IFX_PSI5_RDISET_RDI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI29 */
#define IFX_PSI5_RDISET_RDI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI29 */
#define IFX_PSI5_RDISET_RDI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI2 */
#define IFX_PSI5_RDISET_RDI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI2 */
#define IFX_PSI5_RDISET_RDI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI2 */
#define IFX_PSI5_RDISET_RDI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI30 */
#define IFX_PSI5_RDISET_RDI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI30 */
#define IFX_PSI5_RDISET_RDI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI30 */
#define IFX_PSI5_RDISET_RDI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI31 */
#define IFX_PSI5_RDISET_RDI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI31 */
#define IFX_PSI5_RDISET_RDI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI31 */
#define IFX_PSI5_RDISET_RDI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI3 */
#define IFX_PSI5_RDISET_RDI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI3 */
#define IFX_PSI5_RDISET_RDI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI3 */
#define IFX_PSI5_RDISET_RDI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI4 */
#define IFX_PSI5_RDISET_RDI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI4 */
#define IFX_PSI5_RDISET_RDI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI4 */
#define IFX_PSI5_RDISET_RDI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI5 */
#define IFX_PSI5_RDISET_RDI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI5 */
#define IFX_PSI5_RDISET_RDI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI5 */
#define IFX_PSI5_RDISET_RDI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI6 */
#define IFX_PSI5_RDISET_RDI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI6 */
#define IFX_PSI5_RDISET_RDI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI6 */
#define IFX_PSI5_RDISET_RDI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI7 */
#define IFX_PSI5_RDISET_RDI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI7 */
#define IFX_PSI5_RDISET_RDI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI7 */
#define IFX_PSI5_RDISET_RDI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI8 */
#define IFX_PSI5_RDISET_RDI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI8 */
#define IFX_PSI5_RDISET_RDI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI8 */
#define IFX_PSI5_RDISET_RDI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RDISET_Bits.RDI9 */
#define IFX_PSI5_RDISET_RDI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDISET_Bits.RDI9 */
#define IFX_PSI5_RDISET_RDI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDISET_Bits.RDI9 */
#define IFX_PSI5_RDISET_RDI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RDM_H_Bits.MEI */
#define IFX_PSI5_RDM_H_MEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDM_H_Bits.MEI */
#define IFX_PSI5_RDM_H_MEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDM_H_Bits.MEI */
#define IFX_PSI5_RDM_H_MEI_OFF (29u)

/** \brief  Length for Ifx_PSI5_RDM_H_Bits.NBI */
#define IFX_PSI5_RDM_H_NBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDM_H_Bits.NBI */
#define IFX_PSI5_RDM_H_NBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDM_H_Bits.NBI */
#define IFX_PSI5_RDM_H_NBI_OFF (28u)

/** \brief  Length for Ifx_PSI5_RDM_H_Bits.NFI */
#define IFX_PSI5_RDM_H_NFI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDM_H_Bits.NFI */
#define IFX_PSI5_RDM_H_NFI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDM_H_Bits.NFI */
#define IFX_PSI5_RDM_H_NFI_OFF (30u)

/** \brief  Length for Ifx_PSI5_RDM_H_Bits.RMI */
#define IFX_PSI5_RDM_H_RMI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDM_H_Bits.RMI */
#define IFX_PSI5_RDM_H_RMI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDM_H_Bits.RMI */
#define IFX_PSI5_RDM_H_RMI_OFF (31u)

/** \brief  Length for Ifx_PSI5_RDM_H_Bits.SC */
#define IFX_PSI5_RDM_H_SC_LEN (3u)

/** \brief  Mask for Ifx_PSI5_RDM_H_Bits.SC */
#define IFX_PSI5_RDM_H_SC_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_RDM_H_Bits.SC */
#define IFX_PSI5_RDM_H_SC_OFF (24u)

/** \brief  Length for Ifx_PSI5_RDM_H_Bits.TEI */
#define IFX_PSI5_RDM_H_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDM_H_Bits.TEI */
#define IFX_PSI5_RDM_H_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDM_H_Bits.TEI */
#define IFX_PSI5_RDM_H_TEI_OFF (27u)

/** \brief  Length for Ifx_PSI5_RDM_H_Bits.TS */
#define IFX_PSI5_RDM_H_TS_LEN (24u)

/** \brief  Mask for Ifx_PSI5_RDM_H_Bits.TS */
#define IFX_PSI5_RDM_H_TS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5_RDM_H_Bits.TS */
#define IFX_PSI5_RDM_H_TS_OFF (0u)

/** \brief  Length for Ifx_PSI5_RDM_L_Bits.CRC */
#define IFX_PSI5_RDM_L_CRC_LEN (3u)

/** \brief  Mask for Ifx_PSI5_RDM_L_Bits.CRC */
#define IFX_PSI5_RDM_L_CRC_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_RDM_L_Bits.CRC */
#define IFX_PSI5_RDM_L_CRC_OFF (1u)

/** \brief  Length for Ifx_PSI5_RDM_L_Bits.CRCI */
#define IFX_PSI5_RDM_L_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RDM_L_Bits.CRCI */
#define IFX_PSI5_RDM_L_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RDM_L_Bits.CRCI */
#define IFX_PSI5_RDM_L_CRCI_OFF (0u)

/** \brief  Length for Ifx_PSI5_RDM_L_Bits.RD */
#define IFX_PSI5_RDM_L_RD_LEN (28u)

/** \brief  Mask for Ifx_PSI5_RDM_L_Bits.RD */
#define IFX_PSI5_RDM_L_RD_MSK (0xfffffffu)

/** \brief  Offset for Ifx_PSI5_RDM_L_Bits.RD */
#define IFX_PSI5_RDM_L_RD_OFF (4u)

/** \brief  Length for Ifx_PSI5_RFC_Bits.FLU */
#define IFX_PSI5_RFC_FLU_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RFC_Bits.FLU */
#define IFX_PSI5_RFC_FLU_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RFC_Bits.FLU */
#define IFX_PSI5_RFC_FLU_OFF (31u)

/** \brief  Length for Ifx_PSI5_RFC_Bits.FRQ */
#define IFX_PSI5_RFC_FRQ_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RFC_Bits.FRQ */
#define IFX_PSI5_RFC_FRQ_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RFC_Bits.FRQ */
#define IFX_PSI5_RFC_FRQ_OFF (30u)

/** \brief  Length for Ifx_PSI5_RFC_Bits.FWL */
#define IFX_PSI5_RFC_FWL_LEN (5u)

/** \brief  Mask for Ifx_PSI5_RFC_Bits.FWL */
#define IFX_PSI5_RFC_FWL_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5_RFC_Bits.FWL */
#define IFX_PSI5_RFC_FWL_OFF (16u)

/** \brief  Length for Ifx_PSI5_RFC_Bits.REP */
#define IFX_PSI5_RFC_REP_LEN (6u)

/** \brief  Mask for Ifx_PSI5_RFC_Bits.REP */
#define IFX_PSI5_RFC_REP_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_RFC_Bits.REP */
#define IFX_PSI5_RFC_REP_OFF (0u)

/** \brief  Length for Ifx_PSI5_RFC_Bits.WRAP */
#define IFX_PSI5_RFC_WRAP_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RFC_Bits.WRAP */
#define IFX_PSI5_RFC_WRAP_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RFC_Bits.WRAP */
#define IFX_PSI5_RFC_WRAP_OFF (29u)

/** \brief  Length for Ifx_PSI5_RFC_Bits.WRP */
#define IFX_PSI5_RFC_WRP_LEN (6u)

/** \brief  Mask for Ifx_PSI5_RFC_Bits.WRP */
#define IFX_PSI5_RFC_WRP_MSK (0x3fu)

/** \brief  Offset for Ifx_PSI5_RFC_Bits.WRP */
#define IFX_PSI5_RFC_WRP_OFF (8u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI0 */
#define IFX_PSI5_RMICLR_RMI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI0 */
#define IFX_PSI5_RMICLR_RMI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI0 */
#define IFX_PSI5_RMICLR_RMI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI10 */
#define IFX_PSI5_RMICLR_RMI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI10 */
#define IFX_PSI5_RMICLR_RMI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI10 */
#define IFX_PSI5_RMICLR_RMI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI11 */
#define IFX_PSI5_RMICLR_RMI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI11 */
#define IFX_PSI5_RMICLR_RMI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI11 */
#define IFX_PSI5_RMICLR_RMI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI12 */
#define IFX_PSI5_RMICLR_RMI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI12 */
#define IFX_PSI5_RMICLR_RMI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI12 */
#define IFX_PSI5_RMICLR_RMI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI13 */
#define IFX_PSI5_RMICLR_RMI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI13 */
#define IFX_PSI5_RMICLR_RMI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI13 */
#define IFX_PSI5_RMICLR_RMI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI14 */
#define IFX_PSI5_RMICLR_RMI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI14 */
#define IFX_PSI5_RMICLR_RMI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI14 */
#define IFX_PSI5_RMICLR_RMI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI15 */
#define IFX_PSI5_RMICLR_RMI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI15 */
#define IFX_PSI5_RMICLR_RMI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI15 */
#define IFX_PSI5_RMICLR_RMI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI16 */
#define IFX_PSI5_RMICLR_RMI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI16 */
#define IFX_PSI5_RMICLR_RMI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI16 */
#define IFX_PSI5_RMICLR_RMI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI17 */
#define IFX_PSI5_RMICLR_RMI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI17 */
#define IFX_PSI5_RMICLR_RMI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI17 */
#define IFX_PSI5_RMICLR_RMI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI18 */
#define IFX_PSI5_RMICLR_RMI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI18 */
#define IFX_PSI5_RMICLR_RMI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI18 */
#define IFX_PSI5_RMICLR_RMI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI19 */
#define IFX_PSI5_RMICLR_RMI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI19 */
#define IFX_PSI5_RMICLR_RMI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI19 */
#define IFX_PSI5_RMICLR_RMI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI1 */
#define IFX_PSI5_RMICLR_RMI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI1 */
#define IFX_PSI5_RMICLR_RMI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI1 */
#define IFX_PSI5_RMICLR_RMI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI20 */
#define IFX_PSI5_RMICLR_RMI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI20 */
#define IFX_PSI5_RMICLR_RMI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI20 */
#define IFX_PSI5_RMICLR_RMI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI21 */
#define IFX_PSI5_RMICLR_RMI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI21 */
#define IFX_PSI5_RMICLR_RMI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI21 */
#define IFX_PSI5_RMICLR_RMI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI22 */
#define IFX_PSI5_RMICLR_RMI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI22 */
#define IFX_PSI5_RMICLR_RMI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI22 */
#define IFX_PSI5_RMICLR_RMI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI23 */
#define IFX_PSI5_RMICLR_RMI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI23 */
#define IFX_PSI5_RMICLR_RMI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI23 */
#define IFX_PSI5_RMICLR_RMI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI24 */
#define IFX_PSI5_RMICLR_RMI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI24 */
#define IFX_PSI5_RMICLR_RMI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI24 */
#define IFX_PSI5_RMICLR_RMI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI25 */
#define IFX_PSI5_RMICLR_RMI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI25 */
#define IFX_PSI5_RMICLR_RMI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI25 */
#define IFX_PSI5_RMICLR_RMI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI26 */
#define IFX_PSI5_RMICLR_RMI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI26 */
#define IFX_PSI5_RMICLR_RMI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI26 */
#define IFX_PSI5_RMICLR_RMI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI27 */
#define IFX_PSI5_RMICLR_RMI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI27 */
#define IFX_PSI5_RMICLR_RMI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI27 */
#define IFX_PSI5_RMICLR_RMI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI28 */
#define IFX_PSI5_RMICLR_RMI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI28 */
#define IFX_PSI5_RMICLR_RMI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI28 */
#define IFX_PSI5_RMICLR_RMI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI29 */
#define IFX_PSI5_RMICLR_RMI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI29 */
#define IFX_PSI5_RMICLR_RMI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI29 */
#define IFX_PSI5_RMICLR_RMI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI2 */
#define IFX_PSI5_RMICLR_RMI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI2 */
#define IFX_PSI5_RMICLR_RMI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI2 */
#define IFX_PSI5_RMICLR_RMI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI30 */
#define IFX_PSI5_RMICLR_RMI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI30 */
#define IFX_PSI5_RMICLR_RMI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI30 */
#define IFX_PSI5_RMICLR_RMI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI31 */
#define IFX_PSI5_RMICLR_RMI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI31 */
#define IFX_PSI5_RMICLR_RMI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI31 */
#define IFX_PSI5_RMICLR_RMI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI3 */
#define IFX_PSI5_RMICLR_RMI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI3 */
#define IFX_PSI5_RMICLR_RMI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI3 */
#define IFX_PSI5_RMICLR_RMI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI4 */
#define IFX_PSI5_RMICLR_RMI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI4 */
#define IFX_PSI5_RMICLR_RMI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI4 */
#define IFX_PSI5_RMICLR_RMI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI5 */
#define IFX_PSI5_RMICLR_RMI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI5 */
#define IFX_PSI5_RMICLR_RMI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI5 */
#define IFX_PSI5_RMICLR_RMI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI6 */
#define IFX_PSI5_RMICLR_RMI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI6 */
#define IFX_PSI5_RMICLR_RMI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI6 */
#define IFX_PSI5_RMICLR_RMI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI7 */
#define IFX_PSI5_RMICLR_RMI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI7 */
#define IFX_PSI5_RMICLR_RMI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI7 */
#define IFX_PSI5_RMICLR_RMI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI8 */
#define IFX_PSI5_RMICLR_RMI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI8 */
#define IFX_PSI5_RMICLR_RMI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI8 */
#define IFX_PSI5_RMICLR_RMI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RMICLR_Bits.RMI9 */
#define IFX_PSI5_RMICLR_RMI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMICLR_Bits.RMI9 */
#define IFX_PSI5_RMICLR_RMI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMICLR_Bits.RMI9 */
#define IFX_PSI5_RMICLR_RMI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI0 */
#define IFX_PSI5_RMIOV_RMI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI0 */
#define IFX_PSI5_RMIOV_RMI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI0 */
#define IFX_PSI5_RMIOV_RMI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI10 */
#define IFX_PSI5_RMIOV_RMI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI10 */
#define IFX_PSI5_RMIOV_RMI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI10 */
#define IFX_PSI5_RMIOV_RMI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI11 */
#define IFX_PSI5_RMIOV_RMI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI11 */
#define IFX_PSI5_RMIOV_RMI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI11 */
#define IFX_PSI5_RMIOV_RMI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI12 */
#define IFX_PSI5_RMIOV_RMI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI12 */
#define IFX_PSI5_RMIOV_RMI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI12 */
#define IFX_PSI5_RMIOV_RMI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI13 */
#define IFX_PSI5_RMIOV_RMI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI13 */
#define IFX_PSI5_RMIOV_RMI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI13 */
#define IFX_PSI5_RMIOV_RMI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI14 */
#define IFX_PSI5_RMIOV_RMI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI14 */
#define IFX_PSI5_RMIOV_RMI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI14 */
#define IFX_PSI5_RMIOV_RMI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI15 */
#define IFX_PSI5_RMIOV_RMI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI15 */
#define IFX_PSI5_RMIOV_RMI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI15 */
#define IFX_PSI5_RMIOV_RMI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI16 */
#define IFX_PSI5_RMIOV_RMI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI16 */
#define IFX_PSI5_RMIOV_RMI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI16 */
#define IFX_PSI5_RMIOV_RMI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI17 */
#define IFX_PSI5_RMIOV_RMI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI17 */
#define IFX_PSI5_RMIOV_RMI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI17 */
#define IFX_PSI5_RMIOV_RMI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI18 */
#define IFX_PSI5_RMIOV_RMI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI18 */
#define IFX_PSI5_RMIOV_RMI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI18 */
#define IFX_PSI5_RMIOV_RMI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI19 */
#define IFX_PSI5_RMIOV_RMI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI19 */
#define IFX_PSI5_RMIOV_RMI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI19 */
#define IFX_PSI5_RMIOV_RMI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI1 */
#define IFX_PSI5_RMIOV_RMI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI1 */
#define IFX_PSI5_RMIOV_RMI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI1 */
#define IFX_PSI5_RMIOV_RMI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI20 */
#define IFX_PSI5_RMIOV_RMI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI20 */
#define IFX_PSI5_RMIOV_RMI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI20 */
#define IFX_PSI5_RMIOV_RMI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI21 */
#define IFX_PSI5_RMIOV_RMI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI21 */
#define IFX_PSI5_RMIOV_RMI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI21 */
#define IFX_PSI5_RMIOV_RMI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI22 */
#define IFX_PSI5_RMIOV_RMI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI22 */
#define IFX_PSI5_RMIOV_RMI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI22 */
#define IFX_PSI5_RMIOV_RMI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI23 */
#define IFX_PSI5_RMIOV_RMI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI23 */
#define IFX_PSI5_RMIOV_RMI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI23 */
#define IFX_PSI5_RMIOV_RMI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI24 */
#define IFX_PSI5_RMIOV_RMI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI24 */
#define IFX_PSI5_RMIOV_RMI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI24 */
#define IFX_PSI5_RMIOV_RMI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI25 */
#define IFX_PSI5_RMIOV_RMI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI25 */
#define IFX_PSI5_RMIOV_RMI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI25 */
#define IFX_PSI5_RMIOV_RMI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI26 */
#define IFX_PSI5_RMIOV_RMI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI26 */
#define IFX_PSI5_RMIOV_RMI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI26 */
#define IFX_PSI5_RMIOV_RMI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI27 */
#define IFX_PSI5_RMIOV_RMI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI27 */
#define IFX_PSI5_RMIOV_RMI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI27 */
#define IFX_PSI5_RMIOV_RMI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI28 */
#define IFX_PSI5_RMIOV_RMI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI28 */
#define IFX_PSI5_RMIOV_RMI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI28 */
#define IFX_PSI5_RMIOV_RMI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI29 */
#define IFX_PSI5_RMIOV_RMI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI29 */
#define IFX_PSI5_RMIOV_RMI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI29 */
#define IFX_PSI5_RMIOV_RMI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI2 */
#define IFX_PSI5_RMIOV_RMI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI2 */
#define IFX_PSI5_RMIOV_RMI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI2 */
#define IFX_PSI5_RMIOV_RMI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI30 */
#define IFX_PSI5_RMIOV_RMI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI30 */
#define IFX_PSI5_RMIOV_RMI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI30 */
#define IFX_PSI5_RMIOV_RMI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI31 */
#define IFX_PSI5_RMIOV_RMI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI31 */
#define IFX_PSI5_RMIOV_RMI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI31 */
#define IFX_PSI5_RMIOV_RMI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI3 */
#define IFX_PSI5_RMIOV_RMI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI3 */
#define IFX_PSI5_RMIOV_RMI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI3 */
#define IFX_PSI5_RMIOV_RMI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI4 */
#define IFX_PSI5_RMIOV_RMI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI4 */
#define IFX_PSI5_RMIOV_RMI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI4 */
#define IFX_PSI5_RMIOV_RMI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI5 */
#define IFX_PSI5_RMIOV_RMI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI5 */
#define IFX_PSI5_RMIOV_RMI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI5 */
#define IFX_PSI5_RMIOV_RMI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI6 */
#define IFX_PSI5_RMIOV_RMI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI6 */
#define IFX_PSI5_RMIOV_RMI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI6 */
#define IFX_PSI5_RMIOV_RMI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI7 */
#define IFX_PSI5_RMIOV_RMI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI7 */
#define IFX_PSI5_RMIOV_RMI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI7 */
#define IFX_PSI5_RMIOV_RMI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI8 */
#define IFX_PSI5_RMIOV_RMI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI8 */
#define IFX_PSI5_RMIOV_RMI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI8 */
#define IFX_PSI5_RMIOV_RMI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RMIOV_Bits.RMI9 */
#define IFX_PSI5_RMIOV_RMI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMIOV_Bits.RMI9 */
#define IFX_PSI5_RMIOV_RMI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMIOV_Bits.RMI9 */
#define IFX_PSI5_RMIOV_RMI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI0 */
#define IFX_PSI5_RMISET_RMI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI0 */
#define IFX_PSI5_RMISET_RMI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI0 */
#define IFX_PSI5_RMISET_RMI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI10 */
#define IFX_PSI5_RMISET_RMI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI10 */
#define IFX_PSI5_RMISET_RMI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI10 */
#define IFX_PSI5_RMISET_RMI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI11 */
#define IFX_PSI5_RMISET_RMI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI11 */
#define IFX_PSI5_RMISET_RMI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI11 */
#define IFX_PSI5_RMISET_RMI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI12 */
#define IFX_PSI5_RMISET_RMI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI12 */
#define IFX_PSI5_RMISET_RMI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI12 */
#define IFX_PSI5_RMISET_RMI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI13 */
#define IFX_PSI5_RMISET_RMI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI13 */
#define IFX_PSI5_RMISET_RMI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI13 */
#define IFX_PSI5_RMISET_RMI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI14 */
#define IFX_PSI5_RMISET_RMI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI14 */
#define IFX_PSI5_RMISET_RMI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI14 */
#define IFX_PSI5_RMISET_RMI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI15 */
#define IFX_PSI5_RMISET_RMI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI15 */
#define IFX_PSI5_RMISET_RMI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI15 */
#define IFX_PSI5_RMISET_RMI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI16 */
#define IFX_PSI5_RMISET_RMI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI16 */
#define IFX_PSI5_RMISET_RMI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI16 */
#define IFX_PSI5_RMISET_RMI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI17 */
#define IFX_PSI5_RMISET_RMI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI17 */
#define IFX_PSI5_RMISET_RMI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI17 */
#define IFX_PSI5_RMISET_RMI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI18 */
#define IFX_PSI5_RMISET_RMI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI18 */
#define IFX_PSI5_RMISET_RMI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI18 */
#define IFX_PSI5_RMISET_RMI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI19 */
#define IFX_PSI5_RMISET_RMI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI19 */
#define IFX_PSI5_RMISET_RMI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI19 */
#define IFX_PSI5_RMISET_RMI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI1 */
#define IFX_PSI5_RMISET_RMI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI1 */
#define IFX_PSI5_RMISET_RMI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI1 */
#define IFX_PSI5_RMISET_RMI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI20 */
#define IFX_PSI5_RMISET_RMI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI20 */
#define IFX_PSI5_RMISET_RMI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI20 */
#define IFX_PSI5_RMISET_RMI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI21 */
#define IFX_PSI5_RMISET_RMI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI21 */
#define IFX_PSI5_RMISET_RMI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI21 */
#define IFX_PSI5_RMISET_RMI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI22 */
#define IFX_PSI5_RMISET_RMI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI22 */
#define IFX_PSI5_RMISET_RMI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI22 */
#define IFX_PSI5_RMISET_RMI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI23 */
#define IFX_PSI5_RMISET_RMI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI23 */
#define IFX_PSI5_RMISET_RMI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI23 */
#define IFX_PSI5_RMISET_RMI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI24 */
#define IFX_PSI5_RMISET_RMI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI24 */
#define IFX_PSI5_RMISET_RMI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI24 */
#define IFX_PSI5_RMISET_RMI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI25 */
#define IFX_PSI5_RMISET_RMI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI25 */
#define IFX_PSI5_RMISET_RMI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI25 */
#define IFX_PSI5_RMISET_RMI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI26 */
#define IFX_PSI5_RMISET_RMI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI26 */
#define IFX_PSI5_RMISET_RMI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI26 */
#define IFX_PSI5_RMISET_RMI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI27 */
#define IFX_PSI5_RMISET_RMI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI27 */
#define IFX_PSI5_RMISET_RMI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI27 */
#define IFX_PSI5_RMISET_RMI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI28 */
#define IFX_PSI5_RMISET_RMI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI28 */
#define IFX_PSI5_RMISET_RMI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI28 */
#define IFX_PSI5_RMISET_RMI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI29 */
#define IFX_PSI5_RMISET_RMI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI29 */
#define IFX_PSI5_RMISET_RMI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI29 */
#define IFX_PSI5_RMISET_RMI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI2 */
#define IFX_PSI5_RMISET_RMI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI2 */
#define IFX_PSI5_RMISET_RMI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI2 */
#define IFX_PSI5_RMISET_RMI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI30 */
#define IFX_PSI5_RMISET_RMI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI30 */
#define IFX_PSI5_RMISET_RMI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI30 */
#define IFX_PSI5_RMISET_RMI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI31 */
#define IFX_PSI5_RMISET_RMI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI31 */
#define IFX_PSI5_RMISET_RMI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI31 */
#define IFX_PSI5_RMISET_RMI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI3 */
#define IFX_PSI5_RMISET_RMI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI3 */
#define IFX_PSI5_RMISET_RMI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI3 */
#define IFX_PSI5_RMISET_RMI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI4 */
#define IFX_PSI5_RMISET_RMI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI4 */
#define IFX_PSI5_RMISET_RMI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI4 */
#define IFX_PSI5_RMISET_RMI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI5 */
#define IFX_PSI5_RMISET_RMI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI5 */
#define IFX_PSI5_RMISET_RMI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI5 */
#define IFX_PSI5_RMISET_RMI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI6 */
#define IFX_PSI5_RMISET_RMI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI6 */
#define IFX_PSI5_RMISET_RMI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI6 */
#define IFX_PSI5_RMISET_RMI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI7 */
#define IFX_PSI5_RMISET_RMI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI7 */
#define IFX_PSI5_RMISET_RMI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI7 */
#define IFX_PSI5_RMISET_RMI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI8 */
#define IFX_PSI5_RMISET_RMI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI8 */
#define IFX_PSI5_RMISET_RMI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI8 */
#define IFX_PSI5_RMISET_RMI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RMISET_Bits.RMI9 */
#define IFX_PSI5_RMISET_RMI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RMISET_Bits.RMI9 */
#define IFX_PSI5_RMISET_RMI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RMISET_Bits.RMI9 */
#define IFX_PSI5_RMISET_RMI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI0 */
#define IFX_PSI5_RSICLR_RSI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI0 */
#define IFX_PSI5_RSICLR_RSI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI0 */
#define IFX_PSI5_RSICLR_RSI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI10 */
#define IFX_PSI5_RSICLR_RSI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI10 */
#define IFX_PSI5_RSICLR_RSI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI10 */
#define IFX_PSI5_RSICLR_RSI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI11 */
#define IFX_PSI5_RSICLR_RSI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI11 */
#define IFX_PSI5_RSICLR_RSI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI11 */
#define IFX_PSI5_RSICLR_RSI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI12 */
#define IFX_PSI5_RSICLR_RSI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI12 */
#define IFX_PSI5_RSICLR_RSI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI12 */
#define IFX_PSI5_RSICLR_RSI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI13 */
#define IFX_PSI5_RSICLR_RSI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI13 */
#define IFX_PSI5_RSICLR_RSI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI13 */
#define IFX_PSI5_RSICLR_RSI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI14 */
#define IFX_PSI5_RSICLR_RSI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI14 */
#define IFX_PSI5_RSICLR_RSI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI14 */
#define IFX_PSI5_RSICLR_RSI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI15 */
#define IFX_PSI5_RSICLR_RSI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI15 */
#define IFX_PSI5_RSICLR_RSI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI15 */
#define IFX_PSI5_RSICLR_RSI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI16 */
#define IFX_PSI5_RSICLR_RSI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI16 */
#define IFX_PSI5_RSICLR_RSI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI16 */
#define IFX_PSI5_RSICLR_RSI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI17 */
#define IFX_PSI5_RSICLR_RSI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI17 */
#define IFX_PSI5_RSICLR_RSI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI17 */
#define IFX_PSI5_RSICLR_RSI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI18 */
#define IFX_PSI5_RSICLR_RSI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI18 */
#define IFX_PSI5_RSICLR_RSI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI18 */
#define IFX_PSI5_RSICLR_RSI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI19 */
#define IFX_PSI5_RSICLR_RSI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI19 */
#define IFX_PSI5_RSICLR_RSI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI19 */
#define IFX_PSI5_RSICLR_RSI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI1 */
#define IFX_PSI5_RSICLR_RSI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI1 */
#define IFX_PSI5_RSICLR_RSI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI1 */
#define IFX_PSI5_RSICLR_RSI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI20 */
#define IFX_PSI5_RSICLR_RSI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI20 */
#define IFX_PSI5_RSICLR_RSI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI20 */
#define IFX_PSI5_RSICLR_RSI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI21 */
#define IFX_PSI5_RSICLR_RSI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI21 */
#define IFX_PSI5_RSICLR_RSI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI21 */
#define IFX_PSI5_RSICLR_RSI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI22 */
#define IFX_PSI5_RSICLR_RSI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI22 */
#define IFX_PSI5_RSICLR_RSI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI22 */
#define IFX_PSI5_RSICLR_RSI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI23 */
#define IFX_PSI5_RSICLR_RSI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI23 */
#define IFX_PSI5_RSICLR_RSI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI23 */
#define IFX_PSI5_RSICLR_RSI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI24 */
#define IFX_PSI5_RSICLR_RSI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI24 */
#define IFX_PSI5_RSICLR_RSI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI24 */
#define IFX_PSI5_RSICLR_RSI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI25 */
#define IFX_PSI5_RSICLR_RSI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI25 */
#define IFX_PSI5_RSICLR_RSI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI25 */
#define IFX_PSI5_RSICLR_RSI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI26 */
#define IFX_PSI5_RSICLR_RSI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI26 */
#define IFX_PSI5_RSICLR_RSI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI26 */
#define IFX_PSI5_RSICLR_RSI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI27 */
#define IFX_PSI5_RSICLR_RSI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI27 */
#define IFX_PSI5_RSICLR_RSI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI27 */
#define IFX_PSI5_RSICLR_RSI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI28 */
#define IFX_PSI5_RSICLR_RSI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI28 */
#define IFX_PSI5_RSICLR_RSI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI28 */
#define IFX_PSI5_RSICLR_RSI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI29 */
#define IFX_PSI5_RSICLR_RSI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI29 */
#define IFX_PSI5_RSICLR_RSI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI29 */
#define IFX_PSI5_RSICLR_RSI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI2 */
#define IFX_PSI5_RSICLR_RSI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI2 */
#define IFX_PSI5_RSICLR_RSI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI2 */
#define IFX_PSI5_RSICLR_RSI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI30 */
#define IFX_PSI5_RSICLR_RSI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI30 */
#define IFX_PSI5_RSICLR_RSI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI30 */
#define IFX_PSI5_RSICLR_RSI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI31 */
#define IFX_PSI5_RSICLR_RSI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI31 */
#define IFX_PSI5_RSICLR_RSI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI31 */
#define IFX_PSI5_RSICLR_RSI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI3 */
#define IFX_PSI5_RSICLR_RSI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI3 */
#define IFX_PSI5_RSICLR_RSI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI3 */
#define IFX_PSI5_RSICLR_RSI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI4 */
#define IFX_PSI5_RSICLR_RSI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI4 */
#define IFX_PSI5_RSICLR_RSI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI4 */
#define IFX_PSI5_RSICLR_RSI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI5 */
#define IFX_PSI5_RSICLR_RSI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI5 */
#define IFX_PSI5_RSICLR_RSI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI5 */
#define IFX_PSI5_RSICLR_RSI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI6 */
#define IFX_PSI5_RSICLR_RSI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI6 */
#define IFX_PSI5_RSICLR_RSI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI6 */
#define IFX_PSI5_RSICLR_RSI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI7 */
#define IFX_PSI5_RSICLR_RSI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI7 */
#define IFX_PSI5_RSICLR_RSI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI7 */
#define IFX_PSI5_RSICLR_RSI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI8 */
#define IFX_PSI5_RSICLR_RSI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI8 */
#define IFX_PSI5_RSICLR_RSI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI8 */
#define IFX_PSI5_RSICLR_RSI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RSICLR_Bits.RSI9 */
#define IFX_PSI5_RSICLR_RSI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSICLR_Bits.RSI9 */
#define IFX_PSI5_RSICLR_RSI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSICLR_Bits.RSI9 */
#define IFX_PSI5_RSICLR_RSI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI0 */
#define IFX_PSI5_RSIOV_RSI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI0 */
#define IFX_PSI5_RSIOV_RSI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI0 */
#define IFX_PSI5_RSIOV_RSI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI10 */
#define IFX_PSI5_RSIOV_RSI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI10 */
#define IFX_PSI5_RSIOV_RSI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI10 */
#define IFX_PSI5_RSIOV_RSI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI11 */
#define IFX_PSI5_RSIOV_RSI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI11 */
#define IFX_PSI5_RSIOV_RSI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI11 */
#define IFX_PSI5_RSIOV_RSI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI12 */
#define IFX_PSI5_RSIOV_RSI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI12 */
#define IFX_PSI5_RSIOV_RSI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI12 */
#define IFX_PSI5_RSIOV_RSI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI13 */
#define IFX_PSI5_RSIOV_RSI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI13 */
#define IFX_PSI5_RSIOV_RSI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI13 */
#define IFX_PSI5_RSIOV_RSI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI14 */
#define IFX_PSI5_RSIOV_RSI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI14 */
#define IFX_PSI5_RSIOV_RSI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI14 */
#define IFX_PSI5_RSIOV_RSI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI15 */
#define IFX_PSI5_RSIOV_RSI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI15 */
#define IFX_PSI5_RSIOV_RSI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI15 */
#define IFX_PSI5_RSIOV_RSI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI16 */
#define IFX_PSI5_RSIOV_RSI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI16 */
#define IFX_PSI5_RSIOV_RSI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI16 */
#define IFX_PSI5_RSIOV_RSI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI17 */
#define IFX_PSI5_RSIOV_RSI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI17 */
#define IFX_PSI5_RSIOV_RSI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI17 */
#define IFX_PSI5_RSIOV_RSI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI18 */
#define IFX_PSI5_RSIOV_RSI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI18 */
#define IFX_PSI5_RSIOV_RSI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI18 */
#define IFX_PSI5_RSIOV_RSI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI19 */
#define IFX_PSI5_RSIOV_RSI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI19 */
#define IFX_PSI5_RSIOV_RSI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI19 */
#define IFX_PSI5_RSIOV_RSI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI1 */
#define IFX_PSI5_RSIOV_RSI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI1 */
#define IFX_PSI5_RSIOV_RSI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI1 */
#define IFX_PSI5_RSIOV_RSI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI20 */
#define IFX_PSI5_RSIOV_RSI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI20 */
#define IFX_PSI5_RSIOV_RSI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI20 */
#define IFX_PSI5_RSIOV_RSI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI21 */
#define IFX_PSI5_RSIOV_RSI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI21 */
#define IFX_PSI5_RSIOV_RSI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI21 */
#define IFX_PSI5_RSIOV_RSI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI22 */
#define IFX_PSI5_RSIOV_RSI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI22 */
#define IFX_PSI5_RSIOV_RSI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI22 */
#define IFX_PSI5_RSIOV_RSI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI23 */
#define IFX_PSI5_RSIOV_RSI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI23 */
#define IFX_PSI5_RSIOV_RSI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI23 */
#define IFX_PSI5_RSIOV_RSI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI24 */
#define IFX_PSI5_RSIOV_RSI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI24 */
#define IFX_PSI5_RSIOV_RSI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI24 */
#define IFX_PSI5_RSIOV_RSI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI25 */
#define IFX_PSI5_RSIOV_RSI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI25 */
#define IFX_PSI5_RSIOV_RSI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI25 */
#define IFX_PSI5_RSIOV_RSI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI26 */
#define IFX_PSI5_RSIOV_RSI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI26 */
#define IFX_PSI5_RSIOV_RSI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI26 */
#define IFX_PSI5_RSIOV_RSI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI27 */
#define IFX_PSI5_RSIOV_RSI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI27 */
#define IFX_PSI5_RSIOV_RSI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI27 */
#define IFX_PSI5_RSIOV_RSI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI28 */
#define IFX_PSI5_RSIOV_RSI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI28 */
#define IFX_PSI5_RSIOV_RSI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI28 */
#define IFX_PSI5_RSIOV_RSI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI29 */
#define IFX_PSI5_RSIOV_RSI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI29 */
#define IFX_PSI5_RSIOV_RSI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI29 */
#define IFX_PSI5_RSIOV_RSI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI2 */
#define IFX_PSI5_RSIOV_RSI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI2 */
#define IFX_PSI5_RSIOV_RSI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI2 */
#define IFX_PSI5_RSIOV_RSI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI30 */
#define IFX_PSI5_RSIOV_RSI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI30 */
#define IFX_PSI5_RSIOV_RSI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI30 */
#define IFX_PSI5_RSIOV_RSI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI31 */
#define IFX_PSI5_RSIOV_RSI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI31 */
#define IFX_PSI5_RSIOV_RSI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI31 */
#define IFX_PSI5_RSIOV_RSI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI3 */
#define IFX_PSI5_RSIOV_RSI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI3 */
#define IFX_PSI5_RSIOV_RSI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI3 */
#define IFX_PSI5_RSIOV_RSI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI4 */
#define IFX_PSI5_RSIOV_RSI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI4 */
#define IFX_PSI5_RSIOV_RSI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI4 */
#define IFX_PSI5_RSIOV_RSI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI5 */
#define IFX_PSI5_RSIOV_RSI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI5 */
#define IFX_PSI5_RSIOV_RSI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI5 */
#define IFX_PSI5_RSIOV_RSI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI6 */
#define IFX_PSI5_RSIOV_RSI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI6 */
#define IFX_PSI5_RSIOV_RSI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI6 */
#define IFX_PSI5_RSIOV_RSI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI7 */
#define IFX_PSI5_RSIOV_RSI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI7 */
#define IFX_PSI5_RSIOV_RSI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI7 */
#define IFX_PSI5_RSIOV_RSI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI8 */
#define IFX_PSI5_RSIOV_RSI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI8 */
#define IFX_PSI5_RSIOV_RSI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI8 */
#define IFX_PSI5_RSIOV_RSI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RSIOV_Bits.RSI9 */
#define IFX_PSI5_RSIOV_RSI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSIOV_Bits.RSI9 */
#define IFX_PSI5_RSIOV_RSI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSIOV_Bits.RSI9 */
#define IFX_PSI5_RSIOV_RSI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI0 */
#define IFX_PSI5_RSISET_RSI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI0 */
#define IFX_PSI5_RSISET_RSI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI0 */
#define IFX_PSI5_RSISET_RSI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI10 */
#define IFX_PSI5_RSISET_RSI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI10 */
#define IFX_PSI5_RSISET_RSI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI10 */
#define IFX_PSI5_RSISET_RSI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI11 */
#define IFX_PSI5_RSISET_RSI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI11 */
#define IFX_PSI5_RSISET_RSI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI11 */
#define IFX_PSI5_RSISET_RSI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI12 */
#define IFX_PSI5_RSISET_RSI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI12 */
#define IFX_PSI5_RSISET_RSI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI12 */
#define IFX_PSI5_RSISET_RSI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI13 */
#define IFX_PSI5_RSISET_RSI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI13 */
#define IFX_PSI5_RSISET_RSI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI13 */
#define IFX_PSI5_RSISET_RSI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI14 */
#define IFX_PSI5_RSISET_RSI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI14 */
#define IFX_PSI5_RSISET_RSI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI14 */
#define IFX_PSI5_RSISET_RSI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI15 */
#define IFX_PSI5_RSISET_RSI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI15 */
#define IFX_PSI5_RSISET_RSI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI15 */
#define IFX_PSI5_RSISET_RSI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI16 */
#define IFX_PSI5_RSISET_RSI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI16 */
#define IFX_PSI5_RSISET_RSI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI16 */
#define IFX_PSI5_RSISET_RSI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI17 */
#define IFX_PSI5_RSISET_RSI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI17 */
#define IFX_PSI5_RSISET_RSI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI17 */
#define IFX_PSI5_RSISET_RSI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI18 */
#define IFX_PSI5_RSISET_RSI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI18 */
#define IFX_PSI5_RSISET_RSI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI18 */
#define IFX_PSI5_RSISET_RSI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI19 */
#define IFX_PSI5_RSISET_RSI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI19 */
#define IFX_PSI5_RSISET_RSI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI19 */
#define IFX_PSI5_RSISET_RSI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI1 */
#define IFX_PSI5_RSISET_RSI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI1 */
#define IFX_PSI5_RSISET_RSI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI1 */
#define IFX_PSI5_RSISET_RSI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI20 */
#define IFX_PSI5_RSISET_RSI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI20 */
#define IFX_PSI5_RSISET_RSI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI20 */
#define IFX_PSI5_RSISET_RSI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI21 */
#define IFX_PSI5_RSISET_RSI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI21 */
#define IFX_PSI5_RSISET_RSI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI21 */
#define IFX_PSI5_RSISET_RSI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI22 */
#define IFX_PSI5_RSISET_RSI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI22 */
#define IFX_PSI5_RSISET_RSI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI22 */
#define IFX_PSI5_RSISET_RSI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI23 */
#define IFX_PSI5_RSISET_RSI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI23 */
#define IFX_PSI5_RSISET_RSI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI23 */
#define IFX_PSI5_RSISET_RSI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI24 */
#define IFX_PSI5_RSISET_RSI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI24 */
#define IFX_PSI5_RSISET_RSI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI24 */
#define IFX_PSI5_RSISET_RSI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI25 */
#define IFX_PSI5_RSISET_RSI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI25 */
#define IFX_PSI5_RSISET_RSI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI25 */
#define IFX_PSI5_RSISET_RSI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI26 */
#define IFX_PSI5_RSISET_RSI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI26 */
#define IFX_PSI5_RSISET_RSI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI26 */
#define IFX_PSI5_RSISET_RSI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI27 */
#define IFX_PSI5_RSISET_RSI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI27 */
#define IFX_PSI5_RSISET_RSI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI27 */
#define IFX_PSI5_RSISET_RSI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI28 */
#define IFX_PSI5_RSISET_RSI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI28 */
#define IFX_PSI5_RSISET_RSI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI28 */
#define IFX_PSI5_RSISET_RSI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI29 */
#define IFX_PSI5_RSISET_RSI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI29 */
#define IFX_PSI5_RSISET_RSI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI29 */
#define IFX_PSI5_RSISET_RSI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI2 */
#define IFX_PSI5_RSISET_RSI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI2 */
#define IFX_PSI5_RSISET_RSI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI2 */
#define IFX_PSI5_RSISET_RSI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI30 */
#define IFX_PSI5_RSISET_RSI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI30 */
#define IFX_PSI5_RSISET_RSI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI30 */
#define IFX_PSI5_RSISET_RSI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI31 */
#define IFX_PSI5_RSISET_RSI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI31 */
#define IFX_PSI5_RSISET_RSI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI31 */
#define IFX_PSI5_RSISET_RSI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI3 */
#define IFX_PSI5_RSISET_RSI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI3 */
#define IFX_PSI5_RSISET_RSI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI3 */
#define IFX_PSI5_RSISET_RSI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI4 */
#define IFX_PSI5_RSISET_RSI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI4 */
#define IFX_PSI5_RSISET_RSI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI4 */
#define IFX_PSI5_RSISET_RSI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI5 */
#define IFX_PSI5_RSISET_RSI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI5 */
#define IFX_PSI5_RSISET_RSI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI5 */
#define IFX_PSI5_RSISET_RSI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI6 */
#define IFX_PSI5_RSISET_RSI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI6 */
#define IFX_PSI5_RSISET_RSI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI6 */
#define IFX_PSI5_RSISET_RSI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI7 */
#define IFX_PSI5_RSISET_RSI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI7 */
#define IFX_PSI5_RSISET_RSI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI7 */
#define IFX_PSI5_RSISET_RSI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI8 */
#define IFX_PSI5_RSISET_RSI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI8 */
#define IFX_PSI5_RSISET_RSI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI8 */
#define IFX_PSI5_RSISET_RSI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_RSISET_Bits.RSI9 */
#define IFX_PSI5_RSISET_RSI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_RSISET_Bits.RSI9 */
#define IFX_PSI5_RSISET_RSI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_RSISET_Bits.RSI9 */
#define IFX_PSI5_RSISET_RSI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI0 */
#define IFX_PSI5_TEICLR_TEI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI0 */
#define IFX_PSI5_TEICLR_TEI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI0 */
#define IFX_PSI5_TEICLR_TEI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI10 */
#define IFX_PSI5_TEICLR_TEI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI10 */
#define IFX_PSI5_TEICLR_TEI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI10 */
#define IFX_PSI5_TEICLR_TEI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI11 */
#define IFX_PSI5_TEICLR_TEI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI11 */
#define IFX_PSI5_TEICLR_TEI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI11 */
#define IFX_PSI5_TEICLR_TEI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI12 */
#define IFX_PSI5_TEICLR_TEI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI12 */
#define IFX_PSI5_TEICLR_TEI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI12 */
#define IFX_PSI5_TEICLR_TEI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI13 */
#define IFX_PSI5_TEICLR_TEI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI13 */
#define IFX_PSI5_TEICLR_TEI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI13 */
#define IFX_PSI5_TEICLR_TEI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI14 */
#define IFX_PSI5_TEICLR_TEI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI14 */
#define IFX_PSI5_TEICLR_TEI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI14 */
#define IFX_PSI5_TEICLR_TEI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI15 */
#define IFX_PSI5_TEICLR_TEI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI15 */
#define IFX_PSI5_TEICLR_TEI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI15 */
#define IFX_PSI5_TEICLR_TEI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI16 */
#define IFX_PSI5_TEICLR_TEI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI16 */
#define IFX_PSI5_TEICLR_TEI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI16 */
#define IFX_PSI5_TEICLR_TEI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI17 */
#define IFX_PSI5_TEICLR_TEI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI17 */
#define IFX_PSI5_TEICLR_TEI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI17 */
#define IFX_PSI5_TEICLR_TEI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI18 */
#define IFX_PSI5_TEICLR_TEI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI18 */
#define IFX_PSI5_TEICLR_TEI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI18 */
#define IFX_PSI5_TEICLR_TEI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI19 */
#define IFX_PSI5_TEICLR_TEI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI19 */
#define IFX_PSI5_TEICLR_TEI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI19 */
#define IFX_PSI5_TEICLR_TEI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI1 */
#define IFX_PSI5_TEICLR_TEI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI1 */
#define IFX_PSI5_TEICLR_TEI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI1 */
#define IFX_PSI5_TEICLR_TEI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI20 */
#define IFX_PSI5_TEICLR_TEI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI20 */
#define IFX_PSI5_TEICLR_TEI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI20 */
#define IFX_PSI5_TEICLR_TEI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI21 */
#define IFX_PSI5_TEICLR_TEI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI21 */
#define IFX_PSI5_TEICLR_TEI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI21 */
#define IFX_PSI5_TEICLR_TEI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI22 */
#define IFX_PSI5_TEICLR_TEI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI22 */
#define IFX_PSI5_TEICLR_TEI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI22 */
#define IFX_PSI5_TEICLR_TEI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI23 */
#define IFX_PSI5_TEICLR_TEI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI23 */
#define IFX_PSI5_TEICLR_TEI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI23 */
#define IFX_PSI5_TEICLR_TEI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI24 */
#define IFX_PSI5_TEICLR_TEI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI24 */
#define IFX_PSI5_TEICLR_TEI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI24 */
#define IFX_PSI5_TEICLR_TEI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI25 */
#define IFX_PSI5_TEICLR_TEI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI25 */
#define IFX_PSI5_TEICLR_TEI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI25 */
#define IFX_PSI5_TEICLR_TEI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI26 */
#define IFX_PSI5_TEICLR_TEI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI26 */
#define IFX_PSI5_TEICLR_TEI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI26 */
#define IFX_PSI5_TEICLR_TEI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI27 */
#define IFX_PSI5_TEICLR_TEI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI27 */
#define IFX_PSI5_TEICLR_TEI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI27 */
#define IFX_PSI5_TEICLR_TEI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI28 */
#define IFX_PSI5_TEICLR_TEI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI28 */
#define IFX_PSI5_TEICLR_TEI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI28 */
#define IFX_PSI5_TEICLR_TEI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI29 */
#define IFX_PSI5_TEICLR_TEI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI29 */
#define IFX_PSI5_TEICLR_TEI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI29 */
#define IFX_PSI5_TEICLR_TEI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI2 */
#define IFX_PSI5_TEICLR_TEI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI2 */
#define IFX_PSI5_TEICLR_TEI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI2 */
#define IFX_PSI5_TEICLR_TEI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI30 */
#define IFX_PSI5_TEICLR_TEI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI30 */
#define IFX_PSI5_TEICLR_TEI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI30 */
#define IFX_PSI5_TEICLR_TEI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI31 */
#define IFX_PSI5_TEICLR_TEI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI31 */
#define IFX_PSI5_TEICLR_TEI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI31 */
#define IFX_PSI5_TEICLR_TEI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI3 */
#define IFX_PSI5_TEICLR_TEI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI3 */
#define IFX_PSI5_TEICLR_TEI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI3 */
#define IFX_PSI5_TEICLR_TEI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI4 */
#define IFX_PSI5_TEICLR_TEI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI4 */
#define IFX_PSI5_TEICLR_TEI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI4 */
#define IFX_PSI5_TEICLR_TEI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI5 */
#define IFX_PSI5_TEICLR_TEI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI5 */
#define IFX_PSI5_TEICLR_TEI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI5 */
#define IFX_PSI5_TEICLR_TEI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI6 */
#define IFX_PSI5_TEICLR_TEI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI6 */
#define IFX_PSI5_TEICLR_TEI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI6 */
#define IFX_PSI5_TEICLR_TEI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI7 */
#define IFX_PSI5_TEICLR_TEI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI7 */
#define IFX_PSI5_TEICLR_TEI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI7 */
#define IFX_PSI5_TEICLR_TEI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI8 */
#define IFX_PSI5_TEICLR_TEI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI8 */
#define IFX_PSI5_TEICLR_TEI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI8 */
#define IFX_PSI5_TEICLR_TEI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_TEICLR_Bits.TEI9 */
#define IFX_PSI5_TEICLR_TEI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEICLR_Bits.TEI9 */
#define IFX_PSI5_TEICLR_TEI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEICLR_Bits.TEI9 */
#define IFX_PSI5_TEICLR_TEI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI0 */
#define IFX_PSI5_TEIOV_TEI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI0 */
#define IFX_PSI5_TEIOV_TEI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI0 */
#define IFX_PSI5_TEIOV_TEI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI10 */
#define IFX_PSI5_TEIOV_TEI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI10 */
#define IFX_PSI5_TEIOV_TEI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI10 */
#define IFX_PSI5_TEIOV_TEI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI11 */
#define IFX_PSI5_TEIOV_TEI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI11 */
#define IFX_PSI5_TEIOV_TEI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI11 */
#define IFX_PSI5_TEIOV_TEI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI12 */
#define IFX_PSI5_TEIOV_TEI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI12 */
#define IFX_PSI5_TEIOV_TEI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI12 */
#define IFX_PSI5_TEIOV_TEI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI13 */
#define IFX_PSI5_TEIOV_TEI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI13 */
#define IFX_PSI5_TEIOV_TEI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI13 */
#define IFX_PSI5_TEIOV_TEI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI14 */
#define IFX_PSI5_TEIOV_TEI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI14 */
#define IFX_PSI5_TEIOV_TEI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI14 */
#define IFX_PSI5_TEIOV_TEI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI15 */
#define IFX_PSI5_TEIOV_TEI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI15 */
#define IFX_PSI5_TEIOV_TEI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI15 */
#define IFX_PSI5_TEIOV_TEI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI16 */
#define IFX_PSI5_TEIOV_TEI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI16 */
#define IFX_PSI5_TEIOV_TEI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI16 */
#define IFX_PSI5_TEIOV_TEI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI17 */
#define IFX_PSI5_TEIOV_TEI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI17 */
#define IFX_PSI5_TEIOV_TEI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI17 */
#define IFX_PSI5_TEIOV_TEI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI18 */
#define IFX_PSI5_TEIOV_TEI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI18 */
#define IFX_PSI5_TEIOV_TEI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI18 */
#define IFX_PSI5_TEIOV_TEI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI19 */
#define IFX_PSI5_TEIOV_TEI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI19 */
#define IFX_PSI5_TEIOV_TEI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI19 */
#define IFX_PSI5_TEIOV_TEI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI1 */
#define IFX_PSI5_TEIOV_TEI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI1 */
#define IFX_PSI5_TEIOV_TEI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI1 */
#define IFX_PSI5_TEIOV_TEI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI20 */
#define IFX_PSI5_TEIOV_TEI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI20 */
#define IFX_PSI5_TEIOV_TEI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI20 */
#define IFX_PSI5_TEIOV_TEI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI21 */
#define IFX_PSI5_TEIOV_TEI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI21 */
#define IFX_PSI5_TEIOV_TEI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI21 */
#define IFX_PSI5_TEIOV_TEI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI22 */
#define IFX_PSI5_TEIOV_TEI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI22 */
#define IFX_PSI5_TEIOV_TEI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI22 */
#define IFX_PSI5_TEIOV_TEI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI23 */
#define IFX_PSI5_TEIOV_TEI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI23 */
#define IFX_PSI5_TEIOV_TEI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI23 */
#define IFX_PSI5_TEIOV_TEI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI24 */
#define IFX_PSI5_TEIOV_TEI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI24 */
#define IFX_PSI5_TEIOV_TEI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI24 */
#define IFX_PSI5_TEIOV_TEI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI25 */
#define IFX_PSI5_TEIOV_TEI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI25 */
#define IFX_PSI5_TEIOV_TEI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI25 */
#define IFX_PSI5_TEIOV_TEI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI26 */
#define IFX_PSI5_TEIOV_TEI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI26 */
#define IFX_PSI5_TEIOV_TEI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI26 */
#define IFX_PSI5_TEIOV_TEI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI27 */
#define IFX_PSI5_TEIOV_TEI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI27 */
#define IFX_PSI5_TEIOV_TEI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI27 */
#define IFX_PSI5_TEIOV_TEI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI28 */
#define IFX_PSI5_TEIOV_TEI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI28 */
#define IFX_PSI5_TEIOV_TEI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI28 */
#define IFX_PSI5_TEIOV_TEI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI29 */
#define IFX_PSI5_TEIOV_TEI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI29 */
#define IFX_PSI5_TEIOV_TEI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI29 */
#define IFX_PSI5_TEIOV_TEI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI2 */
#define IFX_PSI5_TEIOV_TEI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI2 */
#define IFX_PSI5_TEIOV_TEI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI2 */
#define IFX_PSI5_TEIOV_TEI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI30 */
#define IFX_PSI5_TEIOV_TEI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI30 */
#define IFX_PSI5_TEIOV_TEI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI30 */
#define IFX_PSI5_TEIOV_TEI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI31 */
#define IFX_PSI5_TEIOV_TEI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI31 */
#define IFX_PSI5_TEIOV_TEI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI31 */
#define IFX_PSI5_TEIOV_TEI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI3 */
#define IFX_PSI5_TEIOV_TEI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI3 */
#define IFX_PSI5_TEIOV_TEI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI3 */
#define IFX_PSI5_TEIOV_TEI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI4 */
#define IFX_PSI5_TEIOV_TEI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI4 */
#define IFX_PSI5_TEIOV_TEI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI4 */
#define IFX_PSI5_TEIOV_TEI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI5 */
#define IFX_PSI5_TEIOV_TEI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI5 */
#define IFX_PSI5_TEIOV_TEI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI5 */
#define IFX_PSI5_TEIOV_TEI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI6 */
#define IFX_PSI5_TEIOV_TEI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI6 */
#define IFX_PSI5_TEIOV_TEI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI6 */
#define IFX_PSI5_TEIOV_TEI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI7 */
#define IFX_PSI5_TEIOV_TEI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI7 */
#define IFX_PSI5_TEIOV_TEI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI7 */
#define IFX_PSI5_TEIOV_TEI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI8 */
#define IFX_PSI5_TEIOV_TEI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI8 */
#define IFX_PSI5_TEIOV_TEI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI8 */
#define IFX_PSI5_TEIOV_TEI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_TEIOV_Bits.TEI9 */
#define IFX_PSI5_TEIOV_TEI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEIOV_Bits.TEI9 */
#define IFX_PSI5_TEIOV_TEI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEIOV_Bits.TEI9 */
#define IFX_PSI5_TEIOV_TEI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI0 */
#define IFX_PSI5_TEISET_TEI0_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI0 */
#define IFX_PSI5_TEISET_TEI0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI0 */
#define IFX_PSI5_TEISET_TEI0_OFF (0u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI10 */
#define IFX_PSI5_TEISET_TEI10_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI10 */
#define IFX_PSI5_TEISET_TEI10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI10 */
#define IFX_PSI5_TEISET_TEI10_OFF (10u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI11 */
#define IFX_PSI5_TEISET_TEI11_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI11 */
#define IFX_PSI5_TEISET_TEI11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI11 */
#define IFX_PSI5_TEISET_TEI11_OFF (11u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI12 */
#define IFX_PSI5_TEISET_TEI12_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI12 */
#define IFX_PSI5_TEISET_TEI12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI12 */
#define IFX_PSI5_TEISET_TEI12_OFF (12u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI13 */
#define IFX_PSI5_TEISET_TEI13_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI13 */
#define IFX_PSI5_TEISET_TEI13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI13 */
#define IFX_PSI5_TEISET_TEI13_OFF (13u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI14 */
#define IFX_PSI5_TEISET_TEI14_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI14 */
#define IFX_PSI5_TEISET_TEI14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI14 */
#define IFX_PSI5_TEISET_TEI14_OFF (14u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI15 */
#define IFX_PSI5_TEISET_TEI15_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI15 */
#define IFX_PSI5_TEISET_TEI15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI15 */
#define IFX_PSI5_TEISET_TEI15_OFF (15u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI16 */
#define IFX_PSI5_TEISET_TEI16_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI16 */
#define IFX_PSI5_TEISET_TEI16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI16 */
#define IFX_PSI5_TEISET_TEI16_OFF (16u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI17 */
#define IFX_PSI5_TEISET_TEI17_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI17 */
#define IFX_PSI5_TEISET_TEI17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI17 */
#define IFX_PSI5_TEISET_TEI17_OFF (17u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI18 */
#define IFX_PSI5_TEISET_TEI18_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI18 */
#define IFX_PSI5_TEISET_TEI18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI18 */
#define IFX_PSI5_TEISET_TEI18_OFF (18u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI19 */
#define IFX_PSI5_TEISET_TEI19_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI19 */
#define IFX_PSI5_TEISET_TEI19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI19 */
#define IFX_PSI5_TEISET_TEI19_OFF (19u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI1 */
#define IFX_PSI5_TEISET_TEI1_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI1 */
#define IFX_PSI5_TEISET_TEI1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI1 */
#define IFX_PSI5_TEISET_TEI1_OFF (1u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI20 */
#define IFX_PSI5_TEISET_TEI20_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI20 */
#define IFX_PSI5_TEISET_TEI20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI20 */
#define IFX_PSI5_TEISET_TEI20_OFF (20u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI21 */
#define IFX_PSI5_TEISET_TEI21_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI21 */
#define IFX_PSI5_TEISET_TEI21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI21 */
#define IFX_PSI5_TEISET_TEI21_OFF (21u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI22 */
#define IFX_PSI5_TEISET_TEI22_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI22 */
#define IFX_PSI5_TEISET_TEI22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI22 */
#define IFX_PSI5_TEISET_TEI22_OFF (22u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI23 */
#define IFX_PSI5_TEISET_TEI23_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI23 */
#define IFX_PSI5_TEISET_TEI23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI23 */
#define IFX_PSI5_TEISET_TEI23_OFF (23u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI24 */
#define IFX_PSI5_TEISET_TEI24_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI24 */
#define IFX_PSI5_TEISET_TEI24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI24 */
#define IFX_PSI5_TEISET_TEI24_OFF (24u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI25 */
#define IFX_PSI5_TEISET_TEI25_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI25 */
#define IFX_PSI5_TEISET_TEI25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI25 */
#define IFX_PSI5_TEISET_TEI25_OFF (25u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI26 */
#define IFX_PSI5_TEISET_TEI26_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI26 */
#define IFX_PSI5_TEISET_TEI26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI26 */
#define IFX_PSI5_TEISET_TEI26_OFF (26u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI27 */
#define IFX_PSI5_TEISET_TEI27_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI27 */
#define IFX_PSI5_TEISET_TEI27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI27 */
#define IFX_PSI5_TEISET_TEI27_OFF (27u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI28 */
#define IFX_PSI5_TEISET_TEI28_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI28 */
#define IFX_PSI5_TEISET_TEI28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI28 */
#define IFX_PSI5_TEISET_TEI28_OFF (28u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI29 */
#define IFX_PSI5_TEISET_TEI29_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI29 */
#define IFX_PSI5_TEISET_TEI29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI29 */
#define IFX_PSI5_TEISET_TEI29_OFF (29u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI2 */
#define IFX_PSI5_TEISET_TEI2_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI2 */
#define IFX_PSI5_TEISET_TEI2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI2 */
#define IFX_PSI5_TEISET_TEI2_OFF (2u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI30 */
#define IFX_PSI5_TEISET_TEI30_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI30 */
#define IFX_PSI5_TEISET_TEI30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI30 */
#define IFX_PSI5_TEISET_TEI30_OFF (30u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI31 */
#define IFX_PSI5_TEISET_TEI31_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI31 */
#define IFX_PSI5_TEISET_TEI31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI31 */
#define IFX_PSI5_TEISET_TEI31_OFF (31u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI3 */
#define IFX_PSI5_TEISET_TEI3_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI3 */
#define IFX_PSI5_TEISET_TEI3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI3 */
#define IFX_PSI5_TEISET_TEI3_OFF (3u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI4 */
#define IFX_PSI5_TEISET_TEI4_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI4 */
#define IFX_PSI5_TEISET_TEI4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI4 */
#define IFX_PSI5_TEISET_TEI4_OFF (4u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI5 */
#define IFX_PSI5_TEISET_TEI5_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI5 */
#define IFX_PSI5_TEISET_TEI5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI5 */
#define IFX_PSI5_TEISET_TEI5_OFF (5u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI6 */
#define IFX_PSI5_TEISET_TEI6_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI6 */
#define IFX_PSI5_TEISET_TEI6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI6 */
#define IFX_PSI5_TEISET_TEI6_OFF (6u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI7 */
#define IFX_PSI5_TEISET_TEI7_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI7 */
#define IFX_PSI5_TEISET_TEI7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI7 */
#define IFX_PSI5_TEISET_TEI7_OFF (7u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI8 */
#define IFX_PSI5_TEISET_TEI8_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI8 */
#define IFX_PSI5_TEISET_TEI8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI8 */
#define IFX_PSI5_TEISET_TEI8_OFF (8u)

/** \brief  Length for Ifx_PSI5_TEISET_Bits.TEI9 */
#define IFX_PSI5_TEISET_TEI9_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TEISET_Bits.TEI9 */
#define IFX_PSI5_TEISET_TEI9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TEISET_Bits.TEI9 */
#define IFX_PSI5_TEISET_TEI9_OFF (9u)

/** \brief  Length for Ifx_PSI5_TSR_Bits.ACLR */
#define IFX_PSI5_TSR_ACLR_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TSR_Bits.ACLR */
#define IFX_PSI5_TSR_ACLR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TSR_Bits.ACLR */
#define IFX_PSI5_TSR_ACLR_OFF (30u)

/** \brief  Length for Ifx_PSI5_TSR_Bits.CLR */
#define IFX_PSI5_TSR_CLR_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TSR_Bits.CLR */
#define IFX_PSI5_TSR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TSR_Bits.CLR */
#define IFX_PSI5_TSR_CLR_OFF (31u)

/** \brief  Length for Ifx_PSI5_TSR_Bits.CTS */
#define IFX_PSI5_TSR_CTS_LEN (24u)

/** \brief  Mask for Ifx_PSI5_TSR_Bits.CTS */
#define IFX_PSI5_TSR_CTS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5_TSR_Bits.CTS */
#define IFX_PSI5_TSR_CTS_OFF (0u)

/** \brief  Length for Ifx_PSI5_TSR_Bits.ETB */
#define IFX_PSI5_TSR_ETB_LEN (3u)

/** \brief  Mask for Ifx_PSI5_TSR_Bits.ETB */
#define IFX_PSI5_TSR_ETB_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5_TSR_Bits.ETB */
#define IFX_PSI5_TSR_ETB_OFF (24u)

/** \brief  Length for Ifx_PSI5_TSR_Bits.TBS */
#define IFX_PSI5_TSR_TBS_LEN (1u)

/** \brief  Mask for Ifx_PSI5_TSR_Bits.TBS */
#define IFX_PSI5_TSR_TBS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5_TSR_Bits.TBS */
#define IFX_PSI5_TSR_TBS_OFF (27u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXPSI5_BF_H */
