<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxEmem" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Emem/Std/IfxEmem.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxEmem_cfg.c</iLLD:file>
</iLLD:filelist>
