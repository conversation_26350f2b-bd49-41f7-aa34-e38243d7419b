	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc13472a --dep-file=zf_driver_delay.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_delay.src ../libraries/zf_driver/zf_driver_delay.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_delay.c'

	
$TC16X
	
	.sdecl	'.text.inttab0.intvec.0dc',code
	.sect	'.text.inttab0.intvec.0dc'
	movh.a	a14,#@his(stm0_isr)
	lea	a14,[a14]@los(stm0_isr)
	ji	a14
	.sdecl	'.text.zf_driver_delay.stm0_isr',code,cluster('stm0_isr')
	.sect	'.text.zf_driver_delay.stm0_isr'
	.align	2
	
	.global	stm0_isr
; Function stm0_isr
.L40:
stm0_isr:	.type	func
	svlcx
.L276:
	mov	d4,#0
	call	interrupt_global_enable
.L277:
	movh.a	a4,#61440
.L278:
	mov	d4,#0
	call	IfxStm_clearCompareFlag
.L279:
	movh.a	a15,#@his(stm0_isr_flag)
	lea	a15,[a15]@los(stm0_isr_flag)
.L280:
	mov	d15,#0
.L281:
	st.b	[a15],d15
.L282:
	rslcx
	rfe
.L147:
	
__stm0_isr_function_end:
	.size	stm0_isr,__stm0_isr_function_end-stm0_isr
.L81:
	; End of function
	
	.sdecl	'.text.inttab0.intvec.0dd',code
	.sect	'.text.inttab0.intvec.0dd'
	movh.a	a14,#@his(stm1_isr)
	lea	a14,[a14]@los(stm1_isr)
	ji	a14
	.sdecl	'.text.zf_driver_delay.stm1_isr',code,cluster('stm1_isr')
	.sect	'.text.zf_driver_delay.stm1_isr'
	.align	2
	
	.global	stm1_isr
; Function stm1_isr
.L42:
stm1_isr:	.type	func
	svlcx
.L287:
	mov	d4,#0
	call	interrupt_global_enable
.L288:
	lea	a4,0xf0000100
.L289:
	mov	d4,#0
	call	IfxStm_clearCompareFlag
.L290:
	movh.a	a15,#@his(stm1_isr_flag)
	lea	a15,[a15]@los(stm1_isr_flag)
.L291:
	mov	d15,#0
.L292:
	st.b	[a15],d15
.L293:
	rslcx
	rfe
.L148:
	
__stm1_isr_function_end:
	.size	stm1_isr,__stm1_isr_function_end-stm1_isr
.L86:
	; End of function
	
	.sdecl	'.text.zf_driver_delay.system_delay_10ns',code,cluster('system_delay_10ns')
	.sect	'.text.zf_driver_delay.system_delay_10ns'
	.align	2
	
	.global	system_delay_10ns
; Function system_delay_10ns
.L44:
system_delay_10ns:	.type	func
	mov	d8,d4
.L153:
	mfcr	d15,#65052
.L174:
	and	d15,#7
.L175:
	j	.L2
.L2:
	extr	d4,d15,#0,#8
.L156:
	mfcr	d0,#65068
.L176:
	extr.u	d0,d0,#15,#1
.L177:
	ne	d15,d0,#0
.L298:
	j	.L3
.L3:
	j	.L4
.L4:
	jne	d15,#0,.L5
.L157:
	call	IfxStm_getAddress
.L160:
	ld.w	d0,[a2]16
.L299:
	j	.L6
.L6:
	j	.L7
.L8:
.L7:
	ld.w	d15,[a2]16
.L300:
	j	.L9
.L9:
	sub	d15,d0
.L301:
	jlt.u	d15,d8,.L8
.L161:
	j	.L10
.L5:
	mov	d15,#-1
	jeq	d15,d4,.L11
.L302:
	mov	d15,#0
	jeq	d15,d4,.L12
.L303:
	mov	d15,#1
	jeq	d15,d4,.L13
	j	.L14
.L12:
	movh.a	a15,#@his(stm0_isr_flag)
	lea	a15,[a15]@los(stm0_isr_flag)
.L304:
	mov	d15,#1
.L305:
	st.b	[a15],d15
.L306:
	call	interrupt_global_disable
.L178:
	ld.w	d15,0xf0000010
.L307:
	add	d8,d15
.L173:
	st.w	0xf0000030,d8
.L308:
	mov	d4,d2
.L180:
	call	interrupt_global_enable
.L179:
	j	.L15
.L16:
.L15:
	movh.a	a15,#@his(stm0_isr_flag)
	lea	a15,[a15]@los(stm0_isr_flag)
	ld.bu	d15,[a15]
.L309:
	jne	d15,#0,.L16
.L166:
	j	.L17
.L13:
	movh.a	a15,#@his(stm1_isr_flag)
	lea	a15,[a15]@los(stm1_isr_flag)
.L310:
	mov	d15,#1
.L311:
	st.b	[a15],d15
.L312:
	call	interrupt_global_disable
.L181:
	ld.w	d15,0xf0000110
.L313:
	add	d15,d8
.L314:
	st.w	0xf0000130,d15
.L315:
	mov	d4,d2
.L183:
	call	interrupt_global_enable
.L182:
	j	.L18
.L19:
.L18:
	movh.a	a15,#@his(stm1_isr_flag)
	lea	a15,[a15]@los(stm1_isr_flag)
	ld.bu	d15,[a15]
.L316:
	jne	d15,#0,.L19
.L169:
	j	.L20
.L11:
	j	.L21
.L14:
.L21:
.L20:
.L17:
.L10:
	ret
.L149:
	
__system_delay_10ns_function_end:
	.size	system_delay_10ns,__system_delay_10ns_function_end-system_delay_10ns
.L91:
	; End of function
	
	.sdecl	'.text.zf_driver_delay.system_delay_us_register',code,cluster('system_delay_us_register')
	.sect	'.text.zf_driver_delay.system_delay_us_register'
	.align	2
	
	.global	system_delay_us_register
; Function system_delay_us_register
.L46:
system_delay_us_register:	.type	func
	mov	d8,d4
.L102:
	mfcr	d15,#65052
.L185:
	and	d15,#7
.L186:
	j	.L22
.L22:
	extr	d4,d15,#0,#8
.L184:
	call	IfxStm_getAddress
.L187:
	mul	d0,d8,#100
.L107:
	ld.w	d1,[a2]16
.L203:
	j	.L23
.L23:
	j	.L24
.L25:
.L24:
	ld.w	d15,[a2]16
.L204:
	j	.L26
.L26:
	sub	d15,d1
.L205:
	jlt.u	d15,d0,.L25
.L108:
	ret
.L96:
	
__system_delay_us_register_function_end:
	.size	system_delay_us_register,__system_delay_us_register_function_end-system_delay_us_register
.L61:
	; End of function
	
	.sdecl	'.text.zf_driver_delay.system_delay_us',code,cluster('system_delay_us')
	.sect	'.text.zf_driver_delay.system_delay_us'
	.align	2
	
	.global	system_delay_us
; Function system_delay_us
.L48:
system_delay_us:	.type	func
	mov	d15,d4
.L188:
	mov	d0,#23040
	addih	d0,d0,#610
.L210:
	jge.u	d0,d15,.L27
.L211:
	j	.L28
.L29:
	mov	d4,#10240
	addih	d4,d4,#61035
	call	system_delay_10ns
.L212:
	mov	d0,#23040
	addih	d0,d0,#610
.L213:
	sub	d15,d0
.L28:
	mov	d0,#23040
	addih	d0,d0,#610
.L214:
	jlt.u	d0,d15,.L29
.L215:
	mul	d4,d15,#100
	call	system_delay_10ns
.L216:
	j	.L30
.L27:
	mul	d4,d15,#100
.L189:
	call	system_delay_10ns
.L30:
	ret
.L119:
	
__system_delay_us_function_end:
	.size	system_delay_us,__system_delay_us_function_end-system_delay_us
.L66:
	; End of function
	
	.sdecl	'.text.zf_driver_delay.system_delay_ms',code,cluster('system_delay_ms')
	.sect	'.text.zf_driver_delay.system_delay_ms'
	.align	2
	
	.global	system_delay_ms
; Function system_delay_ms
.L50:
system_delay_ms:	.type	func
	mov	d15,d4
.L190:
	mov.u	d0,#40000
.L221:
	jge.u	d0,d15,.L31
.L222:
	j	.L32
.L33:
	mov	d4,#10240
	addih	d4,d4,#61035
	call	system_delay_10ns
.L223:
	mov.u	d0,#40000
.L224:
	sub	d15,d0
.L32:
	mov.u	d0,#40000
.L225:
	jlt.u	d0,d15,.L33
.L226:
	mov	d4,#3125
	sh	d4,#5
.L227:
	mul	d4,d15
	call	system_delay_10ns
.L228:
	j	.L34
.L31:
	mov	d4,#3125
.L191:
	sh	d4,#5
.L229:
	mul	d4,d15
	call	system_delay_10ns
.L34:
	ret
.L121:
	
__system_delay_ms_function_end:
	.size	system_delay_ms,__system_delay_ms_function_end-system_delay_ms
.L71:
	; End of function
	
	.sdecl	'.text.zf_driver_delay.system_delay_init',code,cluster('system_delay_init')
	.sect	'.text.zf_driver_delay.system_delay_init'
	.align	2
	
	.global	system_delay_init
; Function system_delay_init
.L52:
system_delay_init:	.type	func
	sub.a	a10,#16
.L129:
	mfcr	d15,#65068
.L192:
	extr.u	d15,d15,#15,#1
.L193:
	ne	d8,d15,#0
.L194:
	j	.L35
.L35:
	disable
.L234:
	nop
.L235:
	j	.L36
.L36:
	j	.L37
.L37:
	mov	d0,#0
.L236:
	st.b	[a10],d0
.L237:
	mov	d15,#0
.L238:
	st.b	[a10]2,d15
.L239:
	mov	d15,#31
.L240:
	st.b	[a10]3,d15
.L241:
	mov	d15,#0
.L242:
	st.b	[a10]1,d15
.L243:
	mov	d15,#1
.L244:
	st.w	[a10]4,d15
.L245:
	mov	d15,#220
.L246:
	st.h	[a10]8,d15
.L247:
	mov	d15,#0
.L248:
	st.b	[a10]10,d15
.L249:
	movh.a	a4,#61440
.L250:
	lea	a5,[a10]0
	call	IfxStm_initCompare
.L251:
	movh.a	a4,#61440
.L252:
	mov	d4,#0
	call	IfxStm_clearCompareFlag
.L253:
	mov	d15,#0
.L254:
	st.b	[a10],d15
.L255:
	mov	d15,#0
.L256:
	st.b	[a10]2,d15
.L257:
	mov	d15,#31
.L258:
	st.b	[a10]3,d15
.L259:
	mov	d15,#0
.L260:
	st.b	[a10]1,d15
.L261:
	mov	d15,#1
.L262:
	st.w	[a10]4,d15
.L263:
	mov	d15,#221
.L264:
	st.h	[a10]8,d15
.L265:
	mov	d15,#1
.L266:
	st.b	[a10]10,d15
.L267:
	lea	a4,0xf0000100
.L268:
	lea	a5,[a10]0
	call	IfxStm_initCompare
.L269:
	lea	a4,0xf0000100
.L270:
	mov	d4,#0
	call	IfxStm_clearCompareFlag
.L139:
	jeq	d8,#0,.L38
.L271:
	enable
.L38:
	ret
.L123:
	
__system_delay_init_function_end:
	.size	system_delay_init,__system_delay_init_function_end-system_delay_init
.L76:
	; End of function
	
	.sdecl	'.data.zf_driver_delay.stm0_isr_flag',data,cluster('stm0_isr_flag')
	.sect	'.data.zf_driver_delay.stm0_isr_flag'
stm0_isr_flag:	.type	object
	.size	stm0_isr_flag,1
	.byte	1
	.sdecl	'.data.zf_driver_delay.stm1_isr_flag',data,cluster('stm1_isr_flag')
	.sect	'.data.zf_driver_delay.stm1_isr_flag'
stm1_isr_flag:	.type	object
	.size	stm1_isr_flag,1
	.byte	1
	.calls	'stm0_isr','interrupt_global_enable'
	.calls	'stm0_isr','IfxStm_clearCompareFlag'
	.calls	'stm1_isr','interrupt_global_enable'
	.calls	'stm1_isr','IfxStm_clearCompareFlag'
	.calls	'system_delay_10ns','IfxStm_getAddress'
	.calls	'system_delay_10ns','interrupt_global_disable'
	.calls	'system_delay_10ns','interrupt_global_enable'
	.calls	'system_delay_us_register','IfxStm_getAddress'
	.calls	'system_delay_us','system_delay_10ns'
	.calls	'system_delay_ms','system_delay_10ns'
	.calls	'system_delay_init','IfxStm_initCompare'
	.calls	'system_delay_init','IfxStm_clearCompareFlag'
	.calls	'stm0_isr','',0
	.calls	'stm1_isr','',0
	.calls	'system_delay_10ns','',0
	.calls	'system_delay_us_register','',0
	.calls	'system_delay_us','',0
	.calls	'system_delay_ms','',0
	.extern	IfxStm_getAddress
	.extern	IfxStm_clearCompareFlag
	.extern	IfxStm_initCompare
	.extern	interrupt_global_enable
	.extern	interrupt_global_disable
	.calls	'system_delay_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L54:
	.word	82450
	.half	3
	.word	.L55
	.byte	4
.L53:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L56
	.byte	2,1,1,3
	.word	203
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	206
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	251
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	263
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	375
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	349
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	381
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	381
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	349
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	506
	.byte	4,2,35,0,0
.L126:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	681
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	602
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	885
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1116
	.byte	4,2,35,8,0,14
	.word	1156
	.byte	3
	.word	1219
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1224
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	659
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1224
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	659
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	659
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1224
	.byte	6,0,15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0
.L101:
	.byte	8
	.byte	'IfxCpu_getCoreId',0,3,5,133,6,22
	.word	1454
	.byte	1,1
.L103:
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1536
	.byte	1,1,6,0
.L134:
	.byte	8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	642
	.byte	1,1
.L135:
	.byte	6,0
.L131:
	.byte	8
	.byte	'IfxCpu_disableInterrupts',0,3,5,147,5,20
	.word	642
	.byte	1,1
.L132:
	.byte	17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,5,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,5,225,5,17,1,1,6,0
.L143:
	.byte	4
	.byte	'IfxCpu_restoreInterrupts',0,3,5,168,7,17,1,1
.L144:
	.byte	5
	.byte	'enabled',0,5,168,7,50
	.word	642
.L146:
	.byte	6,0
.L97:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1858
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	659
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	642
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	659
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1858
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1858
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2089
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2405
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2976
	.byte	4,2,35,0,0,18,4
	.word	642
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3104
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3319
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3534
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	642
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3751
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3971
	.byte	4,2,35,0,0,18,24
	.word	642
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4294
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	642
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4598
	.byte	4,2,35,0,0,18,8
	.word	642
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4923
	.byte	4,2,35,0,0,18,12
	.word	642
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5263
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5629
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5915
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6062
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6231
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6403
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	659
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6578
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6752
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6926
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7102
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7258
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7591
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7939
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8063
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8147
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8327
	.byte	4,2,35,0,0,18,76
	.word	642
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8580
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2365
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2936
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3055
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3095
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3279
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3494
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3711
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3931
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3095
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4245
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4285
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4558
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4874
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4914
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5214
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5254
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5589
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5875
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4914
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6022
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6191
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6363
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6538
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6712
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6886
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7062
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7218
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7551
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7899
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4914
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8023
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8272
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8531
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8571
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8627
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9194
	.byte	4,3,35,252,1,0,14
	.word	9234
	.byte	3
	.word	9837
	.byte	15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9842
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	642
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	9847
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	263
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10068
	.byte	4,2,35,0,0,14
	.word	10358
	.byte	3
	.word	10397
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10402
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,13,100,16,4,11
	.byte	'DISR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,13,149,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10450
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,13,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,12,13,181,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10606
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,13,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,229,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10728
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,13,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,245,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10813
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,13,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,253,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10898
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,13,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,133,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10983
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,13,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,141,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11069
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,13,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,149,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11155
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,13,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,157,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11241
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,13,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,133,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11327
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,13,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,165,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11414
	.byte	4,2,35,0,0,18,8
	.word	11456
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,13,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,12,13,157,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,13,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	467
	.byte	25,0,2,35,0,0,12,13,173,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11736
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,13,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,13,189,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11953
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,13,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,237,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12117
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,13,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,141,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12204
	.byte	4,2,35,0,0,18,144,1
	.word	642
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,13,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	2,0,2,35,3,0,12,13,221,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12304
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,13,175,1,16,4,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,213,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12464
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,13,168,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,12,13,205,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12570
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,13,160,1,16,4,11
	.byte	'RST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,13,197,2,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,13,253,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12797
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,12,13,245,1,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12886
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,13,173,3,25,128,2,13
	.byte	'CLC',0
	.word	10566
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3095
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	10688
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3095
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	10773
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	10858
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	10943
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	11029
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	11115
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11201
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11287
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	11374
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	11496
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	11696
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	11913
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	12077
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5254
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	12164
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12253
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12293
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	12424
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	12530
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	12634
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	12757
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	12846
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13415
	.byte	4,3,35,252,1,0,14
	.word	13455
.L167:
	.byte	3
	.word	13875
	.byte	8
	.byte	'IfxStm_get',0,3,12,162,4,19
	.word	349
	.byte	1,1,5
	.byte	'stm',0,12,162,4,39
	.word	13880
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,12,179,4,20
	.word	263
	.byte	1,1,5
	.byte	'stm',0,12,179,4,49
	.word	13880
	.byte	17,6,6,0,0
.L115:
	.byte	8
	.byte	'IfxStm_getLower',0,3,12,190,4,19
	.word	1858
	.byte	1,1
.L116:
	.byte	5
	.byte	'stm',0,12,190,4,44
	.word	13880
.L118:
	.byte	6,0
.L106:
	.byte	4
	.byte	'IfxStm_waitTicks',0,3,12,172,5,17,1,1
.L109:
	.byte	5
	.byte	'stm',0,12,172,5,43
	.word	13880
.L111:
	.byte	5
	.byte	'ticks',0,12,172,5,55
	.word	1858
.L113:
	.byte	17,6,6,0,0
.L155:
	.byte	8
	.byte	'areInterruptsEnabled',0,3,14,95,20
	.word	642
	.byte	1,1
.L158:
	.byte	17,6,0,0
.L128:
	.byte	8
	.byte	'disableInterrupts',0,3,14,108,20
	.word	642
	.byte	1,1
.L130:
	.byte	17,6,0,0
.L138:
	.byte	4
	.byte	'restoreInterrupts',0,3,14,142,1,17,1,1
.L140:
	.byte	5
	.byte	'enabled',0,14,142,1,43
	.word	642
.L142:
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,14,164,2,25
	.word	14190
	.byte	1,1,5
	.byte	'timeout',0,14,164,2,50
	.word	14190
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,14,211,2,20
	.word	642
	.byte	1,1,5
	.byte	'deadLine',0,14,211,2,44
	.word	14190
	.byte	17,6,0,0,8
	.byte	'now',0,3,14,221,1,25
	.word	14190
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,14,240,1,25
	.word	14190
	.byte	1,1,17,6,0,0,14
	.word	483
	.byte	20
	.byte	'__mfcr',0
	.word	14361
	.byte	1,1,1,1,21
	.word	483
	.byte	0,22
	.byte	'__nop',0,1,1,1,1,22
	.byte	'__disable',0,1,1,1,1,22
	.byte	'__enable',0,1,1,1,1,23
	.word	211
	.byte	24
	.word	237
	.byte	6,0,23
	.word	272
	.byte	24
	.word	304
	.byte	6,0,23
	.word	317
	.byte	6,0,23
	.word	386
	.byte	24
	.word	405
	.byte	6,0,23
	.word	421
	.byte	24
	.word	436
	.byte	24
	.word	450
	.byte	6,0,23
	.word	1229
	.byte	24
	.word	1269
	.byte	24
	.word	1287
	.byte	6,0,23
	.word	1307
	.byte	24
	.word	1345
	.byte	24
	.word	1363
	.byte	6,0,23
	.word	1383
	.byte	24
	.word	1434
	.byte	6,0,23
	.word	1505
	.byte	6,0,23
	.word	1615
	.byte	6,0,23
	.word	1649
	.byte	6,0,23
	.word	1691
	.byte	17,25
	.word	1649
	.byte	26
	.word	1689
	.byte	0,6,0,0,23
	.word	1732
	.byte	6,0,23
	.word	1766
	.byte	6,0,23
	.word	1806
	.byte	24
	.word	1839
	.byte	6,0,23
	.word	1879
	.byte	24
	.word	1920
	.byte	6,0,23
	.word	1939
	.byte	24
	.word	1994
	.byte	6,0,23
	.word	2013
	.byte	24
	.word	2053
	.byte	24
	.word	2070
	.byte	17,6,0,0,23
	.word	9950
	.byte	24
	.word	9978
	.byte	24
	.word	9992
	.byte	24
	.word	10010
	.byte	6,0,23
	.word	10028
	.byte	6,0,23
	.word	10407
	.byte	24
	.word	10435
	.byte	6,0,23
	.word	13885
	.byte	24
	.word	13908
	.byte	6,0,23
	.word	13923
	.byte	24
	.word	13955
	.byte	17,17,25
	.word	10028
	.byte	26
	.word	10066
	.byte	0,0,6,0,0
.L99:
	.byte	15,15,87,9,1,16
	.byte	'IfxStm_Index_none',0,127,16
	.byte	'IfxStm_Index_0',0,0,16
	.byte	'IfxStm_Index_1',0,1,0,27
	.byte	'IfxStm_getAddress',0,12,209,2,21
	.word	13880
	.byte	1,1,1,1,5
	.byte	'stm',0,12,209,2,52
	.word	14724
	.byte	0,23
	.word	13973
	.byte	24
	.word	14001
	.byte	6,0,23
	.word	14016
	.byte	24
	.word	14041
	.byte	24
	.word	14054
	.byte	17,25
	.word	13973
	.byte	24
	.word	14001
	.byte	26
	.word	14014
	.byte	0,6,25
	.word	13973
	.byte	24
	.word	14001
	.byte	26
	.word	14014
	.byte	0,6,0,0,15,12,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,28
	.byte	'IfxStm_clearCompareFlag',0,12,210,3,17,1,1,1,1,5
	.byte	'stm',0,12,210,3,50
	.word	13880
	.byte	5
	.byte	'comparator',0,12,210,3,73
	.word	14893
	.byte	0,15,12,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,15,12,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,15,12,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,15,16,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0
.L124:
	.byte	29,12,141,2,9,12,13
	.byte	'comparator',0
	.word	14893
	.byte	1,2,35,0,13
	.byte	'comparatorInterrupt',0
	.word	15011
	.byte	1,2,35,1,13
	.byte	'compareOffset',0
	.word	15084
	.byte	1,2,35,2,13
	.byte	'compareSize',0
	.word	16009
	.byte	1,2,35,3,13
	.byte	'ticks',0
	.word	1858
	.byte	4,2,35,4,13
	.byte	'triggerPriority',0
	.word	659
	.byte	2,2,35,8,13
	.byte	'typeOfService',0
	.word	16998
	.byte	1,2,35,10,0,30
	.word	17057
	.byte	3
	.word	17220
	.byte	27
	.byte	'IfxStm_initCompare',0,12,238,3,20
	.word	642
	.byte	1,1,1,1,5
	.byte	'stm',0,12,238,3,48
	.word	13880
	.byte	5
	.byte	'config',0,12,238,3,81
	.word	17225
	.byte	0,23
	.word	14074
	.byte	17,25
	.word	1649
	.byte	26
	.word	1689
	.byte	0,6,0,0,23
	.word	14110
	.byte	17,25
	.word	1691
	.byte	31
	.word	1728
	.byte	25
	.word	1649
	.byte	26
	.word	1689
	.byte	0,26
	.word	1729
	.byte	0,0,6,0,0,23
	.word	14143
	.byte	24
	.word	14169
	.byte	17,25
	.word	1806
	.byte	24
	.word	1839
	.byte	26
	.word	1856
	.byte	0,6,0,0,23
	.word	14207
	.byte	24
	.word	14231
	.byte	17,25
	.word	14297
	.byte	31
	.word	14313
	.byte	25
	.word	14110
	.byte	31
	.word	14139
	.byte	25
	.word	1691
	.byte	31
	.word	1728
	.byte	25
	.word	1649
	.byte	26
	.word	1689
	.byte	0,26
	.word	1729
	.byte	0,0,26
	.word	14140
	.byte	0,0,26
	.word	14314
	.byte	25
	.word	14143
	.byte	24
	.word	14169
	.byte	31
	.word	14186
	.byte	25
	.word	1806
	.byte	24
	.word	1839
	.byte	26
	.word	1856
	.byte	0,26
	.word	14187
	.byte	0,0,26
	.word	14315
	.byte	25
	.word	13885
	.byte	24
	.word	13908
	.byte	26
	.word	13921
	.byte	0,26
	.word	14316
	.byte	0,0,6,0,0,23
	.word	14252
	.byte	24
	.word	14275
	.byte	17,25
	.word	14297
	.byte	31
	.word	14313
	.byte	25
	.word	14110
	.byte	31
	.word	14139
	.byte	25
	.word	1691
	.byte	31
	.word	1728
	.byte	25
	.word	1649
	.byte	26
	.word	1689
	.byte	0,26
	.word	1729
	.byte	0,0,26
	.word	14140
	.byte	0,0,26
	.word	14314
	.byte	25
	.word	14143
	.byte	24
	.word	14169
	.byte	31
	.word	14186
	.byte	25
	.word	1806
	.byte	24
	.word	1839
	.byte	26
	.word	1856
	.byte	0,26
	.word	14187
	.byte	0,0,26
	.word	14315
	.byte	25
	.word	13885
	.byte	24
	.word	13908
	.byte	26
	.word	13921
	.byte	0,26
	.word	14316
	.byte	0,0,6,0,0,23
	.word	14297
	.byte	17,25
	.word	14110
	.byte	31
	.word	14139
	.byte	25
	.word	1691
	.byte	31
	.word	1728
	.byte	25
	.word	1649
	.byte	26
	.word	1689
	.byte	0,26
	.word	1729
	.byte	0,0,26
	.word	14140
	.byte	0,0,6,25
	.word	14143
	.byte	24
	.word	14169
	.byte	31
	.word	14186
	.byte	25
	.word	1806
	.byte	24
	.word	1839
	.byte	26
	.word	1856
	.byte	0,26
	.word	14187
	.byte	0,0,6,25
	.word	13885
	.byte	24
	.word	13908
	.byte	26
	.word	13921
	.byte	0,6,0,0,23
	.word	14319
	.byte	17,25
	.word	13885
	.byte	24
	.word	13908
	.byte	26
	.word	13921
	.byte	0,6,0,0,28
	.byte	'interrupt_global_enable',0,17,41,8,1,1,1,1,5
	.byte	'primask',0,17,41,40
	.word	1858
	.byte	0,32
	.byte	'interrupt_global_disable',0,17,42,8
	.word	1858
	.byte	1,1,1,1,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,18,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0
.L104:
	.byte	12,18,223,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17880
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_ICR_Bits',0,18,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	490
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	490
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	490
	.byte	6,0,2,35,0,0
.L136:
	.byte	12,18,223,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17989
	.byte	4,2,35,0,0,7
	.byte	'short int',0,2,5,33
	.byte	'__wchar_t',0,19,1,1
	.word	18146
	.byte	33
	.byte	'__size_t',0,19,1,1
	.word	467
	.byte	33
	.byte	'__ptrdiff_t',0,19,1,1
	.word	483
	.byte	34,1,3
	.word	18214
	.byte	33
	.byte	'__codeptr',0,19,1,1
	.word	18216
	.byte	33
	.byte	'__intptr_t',0,19,1,1
	.word	483
	.byte	33
	.byte	'__uintptr_t',0,19,1,1
	.word	467
	.byte	33
	.byte	'boolean',0,20,101,29
	.word	642
	.byte	33
	.byte	'uint8',0,20,105,29
	.word	642
	.byte	33
	.byte	'uint16',0,20,109,29
	.word	659
	.byte	33
	.byte	'uint32',0,20,113,29
	.word	1858
	.byte	33
	.byte	'uint64',0,20,118,29
	.word	349
	.byte	33
	.byte	'sint16',0,20,126,29
	.word	18146
	.byte	7
	.byte	'long int',0,4,5,33
	.byte	'sint32',0,20,131,1,29
	.word	18368
	.byte	33
	.byte	'sint64',0,20,138,1,29
	.word	14190
	.byte	33
	.byte	'float32',0,20,167,1,29
	.word	263
	.byte	33
	.byte	'pvoid',0,21,57,28
	.word	381
	.byte	33
	.byte	'Ifx_TickTime',0,21,79,28
	.word	14190
	.byte	33
	.byte	'Ifx_Priority',0,21,103,16
	.word	659
	.byte	15,21,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,33
	.byte	'Ifx_RxSel',0,21,140,1,3
	.word	18485
	.byte	33
	.byte	'Ifx_STM_ACCEN0_Bits',0,13,79,3
	.word	12886
	.byte	33
	.byte	'Ifx_STM_ACCEN1_Bits',0,13,85,3
	.word	12797
	.byte	33
	.byte	'Ifx_STM_CAP_Bits',0,13,91,3
	.word	11327
	.byte	33
	.byte	'Ifx_STM_CAPSV_Bits',0,13,97,3
	.word	12204
	.byte	33
	.byte	'Ifx_STM_CLC_Bits',0,13,107,3
	.word	10450
	.byte	33
	.byte	'Ifx_STM_CMCON_Bits',0,13,120,3
	.word	11505
	.byte	33
	.byte	'Ifx_STM_CMP_Bits',0,13,126,3
	.word	11414
	.byte	33
	.byte	'Ifx_STM_ICR_Bits',0,13,139,1,3
	.word	11736
	.byte	33
	.byte	'Ifx_STM_ID_Bits',0,13,147,1,3
	.word	10606
	.byte	33
	.byte	'Ifx_STM_ISCR_Bits',0,13,157,1,3
	.word	11953
	.byte	33
	.byte	'Ifx_STM_KRST0_Bits',0,13,165,1,3
	.word	12674
	.byte	33
	.byte	'Ifx_STM_KRST1_Bits',0,13,172,1,3
	.word	12570
	.byte	33
	.byte	'Ifx_STM_KRSTCLR_Bits',0,13,179,1,3
	.word	12464
	.byte	33
	.byte	'Ifx_STM_OCS_Bits',0,13,189,1,3
	.word	12304
	.byte	33
	.byte	'Ifx_STM_TIM0_Bits',0,13,195,1,3
	.word	10728
	.byte	33
	.byte	'Ifx_STM_TIM0SV_Bits',0,13,201,1,3
	.word	12117
	.byte	33
	.byte	'Ifx_STM_TIM1_Bits',0,13,207,1,3
	.word	10813
	.byte	33
	.byte	'Ifx_STM_TIM2_Bits',0,13,213,1,3
	.word	10898
	.byte	33
	.byte	'Ifx_STM_TIM3_Bits',0,13,219,1,3
	.word	10983
	.byte	33
	.byte	'Ifx_STM_TIM4_Bits',0,13,225,1,3
	.word	11069
	.byte	33
	.byte	'Ifx_STM_TIM5_Bits',0,13,231,1,3
	.word	11155
	.byte	33
	.byte	'Ifx_STM_TIM6_Bits',0,13,237,1,3
	.word	11241
	.byte	33
	.byte	'Ifx_STM_ACCEN0',0,13,250,1,3
	.word	13415
	.byte	33
	.byte	'Ifx_STM_ACCEN1',0,13,130,2,3
	.word	12846
	.byte	33
	.byte	'Ifx_STM_CAP',0,13,138,2,3
	.word	11374
	.byte	33
	.byte	'Ifx_STM_CAPSV',0,13,146,2,3
	.word	12253
	.byte	33
	.byte	'Ifx_STM_CLC',0,13,154,2,3
	.word	10566
	.byte	33
	.byte	'Ifx_STM_CMCON',0,13,162,2,3
	.word	11696
	.byte	33
	.byte	'Ifx_STM_CMP',0,13,170,2,3
	.word	11456
	.byte	33
	.byte	'Ifx_STM_ICR',0,13,178,2,3
	.word	11913
	.byte	33
	.byte	'Ifx_STM_ID',0,13,186,2,3
	.word	10688
	.byte	33
	.byte	'Ifx_STM_ISCR',0,13,194,2,3
	.word	12077
	.byte	33
	.byte	'Ifx_STM_KRST0',0,13,202,2,3
	.word	12757
	.byte	33
	.byte	'Ifx_STM_KRST1',0,13,210,2,3
	.word	12634
	.byte	33
	.byte	'Ifx_STM_KRSTCLR',0,13,218,2,3
	.word	12530
	.byte	33
	.byte	'Ifx_STM_OCS',0,13,226,2,3
	.word	12424
	.byte	33
	.byte	'Ifx_STM_TIM0',0,13,234,2,3
	.word	10773
	.byte	33
	.byte	'Ifx_STM_TIM0SV',0,13,242,2,3
	.word	12164
	.byte	33
	.byte	'Ifx_STM_TIM1',0,13,250,2,3
	.word	10858
	.byte	33
	.byte	'Ifx_STM_TIM2',0,13,130,3,3
	.word	10943
	.byte	33
	.byte	'Ifx_STM_TIM3',0,13,138,3,3
	.word	11029
	.byte	33
	.byte	'Ifx_STM_TIM4',0,13,146,3,3
	.word	11115
	.byte	33
	.byte	'Ifx_STM_TIM5',0,13,154,3,3
	.word	11201
	.byte	33
	.byte	'Ifx_STM_TIM6',0,13,162,3,3
	.word	11287
	.byte	14
	.word	13455
	.byte	33
	.byte	'Ifx_STM',0,13,201,3,3
	.word	19706
	.byte	33
	.byte	'IfxStm_Index',0,15,92,3
	.word	14724
	.byte	15,22,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,33
	.byte	'IfxScu_CCUCON0_CLKSEL',0,22,240,10,3
	.word	19749
	.byte	15,22,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,33
	.byte	'IfxScu_WDTCON1_IR',0,22,255,10,3
	.word	19846
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	19968
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	20525
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	20602
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	20738
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	642
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	21018
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	21256
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	21384
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	642
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	642
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	21627
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	21862
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	21990
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	22090
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	642
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	22190
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	467
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	22398
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	22563
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	22746
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	467
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	642
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	22900
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	23264
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	642
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	23475
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,33
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	23727
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	23845
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	23956
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	24119
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	24282
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	24440
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	642
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	10,0,2,35,2,0,33
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	24605
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	642
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	642
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	659
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	24934
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	25155
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	25318
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	25590
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	25743
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	25899
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	26061
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	26204
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	26369
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	26514
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	26695
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	26869
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	27029
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	27173
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	27447
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	27586
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	642
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	659
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	642
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	642
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	27749
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	659
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	642
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	659
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	27967
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	28130
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	28466
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	642
	.byte	2,0,2,35,3,0,33
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	28573
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	29025
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	29124
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	29274
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	467
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	642
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	29423
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	29584
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	659
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	29714
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	29846
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	659
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	29961
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	30072
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	642
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	642
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	30230
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	30642
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	659
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	6,0,2,35,3,0,33
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	30743
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	31010
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	31146
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	642
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	31257
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	31390
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	31593
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	31949
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	659
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	32127
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	642
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	642
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	32227
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	642
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	642
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	642
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	9,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	32597
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	32783
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	32981
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	642
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	33214
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	642
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	642
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	33366
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	642
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	642
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	642
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	642
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	33933
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	642
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	642
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	34227
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	642
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	642
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	642
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	34505
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	35001
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	659
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	35314
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	642
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	642
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	35523
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	642
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	3,0,2,35,3,0,33
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	35734
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	36166
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	642
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	642
	.byte	7,0,2,35,3,0,33
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	36262
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	36522
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,33
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	36647
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	36844
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	36997
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	37150
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	37303
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	506
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	681
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	925
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	490
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	37558
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	37684
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	37936
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19968
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	38155
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20525
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	38219
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20602
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	38283
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20738
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	38348
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21018
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	38413
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21256
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	38478
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21384
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	38543
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21627
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	38608
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21862
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	38673
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21990
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	38738
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22090
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	38803
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22190
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	38868
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22398
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	38932
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22563
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	38996
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22746
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	39060
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22900
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	39125
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23264
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	39187
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23475
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	39249
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23727
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	39311
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23845
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	39375
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23956
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	39440
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24119
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	39506
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24282
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	39572
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24440
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	39640
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24605
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	39707
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24934
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	39775
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25155
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	39843
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25318
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	39909
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25590
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	39976
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25743
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	40045
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25899
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	40114
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26061
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	40183
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26204
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	40252
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26369
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	40321
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26514
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	40390
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26695
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	40458
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26869
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	40526
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27029
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	40594
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27173
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	40662
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27447
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	40727
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27586
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	40792
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27749
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	40858
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27967
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	40922
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28130
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	40983
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28466
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	41044
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28573
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	41104
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29025
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	41166
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29124
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	41226
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29274
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	41288
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29423
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	41356
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29584
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	41424
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29714
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	41492
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29846
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	41556
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29961
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	41621
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30072
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	41684
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30230
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	41745
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30642
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	41809
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30743
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	41870
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31010
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	41934
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31146
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	42001
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31257
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	42064
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31390
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	42125
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31593
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	42187
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31949
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	42252
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32127
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	42317
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32227
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	42382
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32597
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	42451
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32783
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	42520
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32981
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	42589
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33214
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	42654
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33366
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	42717
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33933
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	42782
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34227
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	42847
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34505
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	42912
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35001
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	42978
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35523
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	43047
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35314
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	43111
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35734
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	43176
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36166
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	43241
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36262
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	43306
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36522
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	43370
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36647
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	43436
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36844
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	43500
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36997
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	43565
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37150
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	43630
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37303
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	43695
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	602
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	885
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1116
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37558
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	43846
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37684
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	43913
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37936
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	43980
	.byte	14
	.word	1156
	.byte	33
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	44045
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	43846
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43913
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43980
	.byte	4,2,35,8,0,14
	.word	44074
	.byte	33
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	44135
	.byte	18,8
	.word	39311
	.byte	19,1,0,18,20
	.word	642
	.byte	19,19,0,18,8
	.word	42654
	.byte	19,1,0,14
	.word	44074
	.byte	18,24
	.word	1156
	.byte	19,1,0,14
	.word	44194
	.byte	18,16
	.word	642
	.byte	19,15,0,18,28
	.word	642
	.byte	19,27,0,18,40
	.word	642
	.byte	19,39,0,18,16
	.word	39125
	.byte	19,3,0,18,16
	.word	41104
	.byte	19,3,0,18,180,3
	.word	642
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4914
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	41044
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3095
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	41745
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	42589
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	42187
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	42252
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	42317
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	42520
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	42382
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	42451
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	38348
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	38413
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	40922
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	40858
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	38478
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	38543
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	38608
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	38673
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	43176
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3095
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	43047
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	38283
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	43370
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	43111
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3095
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	39909
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	44162
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	39375
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	43436
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	38738
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	38803
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	44171
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	42064
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	41226
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	41809
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	41684
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	41166
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	40662
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	39640
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	39440
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	39506
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	43306
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3095
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	42717
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	42912
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	42978
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	44180
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3095
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	39060
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	38932
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	42782
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	42847
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	44189
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	39249
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	44203
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5254
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	43695
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	43630
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	43500
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	43565
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3095
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	41492
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	41556
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	38868
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	41621
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4914
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	43241
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	44208
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	41288
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	41356
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	41424
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	44217
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	42001
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4914
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	40727
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	39572
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	40792
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	39843
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	39707
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3095
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	40390
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	40458
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	40526
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	40594
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	39976
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	40045
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	40114
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	40183
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	40252
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	40321
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	39775
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3095
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	41934
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	41870
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	44226
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	44235
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	39187
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	40983
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	42125
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	44244
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3095
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	38996
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	44253
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	38219
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	38155
	.byte	4,3,35,252,7,0,14
	.word	44264
	.byte	33
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	46254
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,18,45,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_A_Bits',0,18,48,3
	.word	46276
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,18,51,16,4,11
	.byte	'VSS',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	490
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_BIV_Bits',0,18,55,3
	.word	46337
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,18,58,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	490
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_BTV_Bits',0,18,62,3
	.word	46416
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,18,65,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_CCNT_Bits',0,18,69,3
	.word	46502
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,18,72,16,4,11
	.byte	'CM',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	490
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	490
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	490
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_CPU_CCTRL_Bits',0,18,80,3
	.word	46591
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,18,83,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_COMPAT_Bits',0,18,89,3
	.word	46737
	.byte	33
	.byte	'Ifx_CPU_CORE_ID_Bits',0,18,96,3
	.word	17880
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,18,99,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_L_Bits',0,18,103,3
	.word	46893
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,18,106,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_U_Bits',0,18,110,3
	.word	46986
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,18,113,16,4,11
	.byte	'MODREV',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	490
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPU_ID_Bits',0,18,118,3
	.word	47079
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,18,121,16,4,11
	.byte	'XE',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPXE_Bits',0,18,125,3
	.word	47186
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,18,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_CREVT_Bits',0,18,136,1,3
	.word	47273
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,18,139,1,16,4,11
	.byte	'CID',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CUS_ID_Bits',0,18,143,1,3
	.word	47427
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,18,146,1,16,4,11
	.byte	'DATA',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_D_Bits',0,18,149,1,3
	.word	47521
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,18,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	490
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DATR_Bits',0,18,163,1,3
	.word	47584
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,18,166,1,16,4,11
	.byte	'DE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	490
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	490
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	19,0,2,35,0,0,33
	.byte	'Ifx_CPU_DBGSR_Bits',0,18,177,1,3
	.word	47802
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,18,180,1,16,4,11
	.byte	'DTA',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_DBGTCR_Bits',0,18,184,1,3
	.word	48017
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,18,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCON0_Bits',0,18,192,1,3
	.word	48111
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,18,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCON2_Bits',0,18,199,1,3
	.word	48227
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,18,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	490
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCX_Bits',0,18,206,1,3
	.word	48328
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,18,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_DEADD_Bits',0,18,212,1,3
	.word	48421
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,18,215,1,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_DIEAR_Bits',0,18,218,1,3
	.word	48501
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,18,221,1,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,33
	.byte	'Ifx_CPU_DIETR_Bits',0,18,233,1,3
	.word	48570
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,18,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	490
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_DMS_Bits',0,18,240,1,3
	.word	48799
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,18,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_L_Bits',0,18,247,1,3
	.word	48892
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,18,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	490
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_U_Bits',0,18,254,1,3
	.word	48987
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,18,129,2,16,4,11
	.byte	'RE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPRE_Bits',0,18,133,2,3
	.word	49082
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,18,136,2,16,4,11
	.byte	'WE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPWE_Bits',0,18,140,2,3
	.word	49172
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,18,143,2,16,4,11
	.byte	'SRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	490
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	490
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,33
	.byte	'Ifx_CPU_DSTR_Bits',0,18,161,2,3
	.word	49262
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,18,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_EXEVT_Bits',0,18,172,2,3
	.word	49586
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,18,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_FCX_Bits',0,18,180,2,3
	.word	49740
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,18,183,2,16,4,11
	.byte	'TST',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	490
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	490
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	490
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	490
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,18,202,2,3
	.word	49846
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,205,2,16,4,11
	.byte	'OPC',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,212,2,3
	.word	50195
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,18,215,2,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,18,218,2,3
	.word	50355
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,224,2,3
	.word	50436
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,230,2,3
	.word	50523
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,236,2,3
	.word	50610
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,18,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_ICNT_Bits',0,18,243,2,3
	.word	50697
	.byte	33
	.byte	'Ifx_CPU_ICR_Bits',0,18,253,2,3
	.word	17989
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,18,128,3,16,4,11
	.byte	'ISP',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_ISP_Bits',0,18,131,3,3
	.word	50814
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,18,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	490
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_LCX_Bits',0,18,139,3,3
	.word	50880
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,18,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M1CNT_Bits',0,18,146,3,3
	.word	50986
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,18,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M2CNT_Bits',0,18,153,3,3
	.word	51079
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,18,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	490
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M3CNT_Bits',0,18,160,3,3
	.word	51172
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,18,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	490
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_PC_Bits',0,18,167,3,3
	.word	51265
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,18,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON0_Bits',0,18,175,3,3
	.word	51350
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,18,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	490
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON1_Bits',0,18,183,3,3
	.word	51466
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,18,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON2_Bits',0,18,190,3,3
	.word	51577
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,18,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	490
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	490
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	490
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	490
	.byte	10,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCXI_Bits',0,18,200,3,3
	.word	51678
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,18,203,3,16,4,11
	.byte	'TA',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_PIEAR_Bits',0,18,206,3,3
	.word	51808
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,18,209,3,16,4,11
	.byte	'IED',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	490
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	490
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	18,0,2,35,0,0,33
	.byte	'Ifx_CPU_PIETR_Bits',0,18,221,3,3
	.word	51877
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,18,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	490
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA0_Bits',0,18,229,3,3
	.word	52106
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,18,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	490
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	490
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA1_Bits',0,18,237,3,3
	.word	52219
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,18,240,3,16,4,11
	.byte	'PSI',0,4
	.word	490
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	490
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA2_Bits',0,18,244,3,3
	.word	52332
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,18,247,3,16,4,11
	.byte	'FRE',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	17,0,2,35,0,0,33
	.byte	'Ifx_CPU_PSTR_Bits',0,18,129,4,3
	.word	52423
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,18,132,4,16,4,11
	.byte	'CDC',0,4
	.word	490
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	490
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	490
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	490
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_PSW_Bits',0,18,147,4,3
	.word	52626
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,18,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	490
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	490
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	490
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	490
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_SEGEN_Bits',0,18,156,4,3
	.word	52869
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,18,159,4,16,4,11
	.byte	'PC',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	490
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	490
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	490
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	490
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	490
	.byte	7,0,2,35,0,0,33
	.byte	'Ifx_CPU_SMACON_Bits',0,18,171,4,3
	.word	52997
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,18,174,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,18,177,4,3
	.word	53238
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,18,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,18,183,4,3
	.word	53321
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,186,4,16,4,11
	.byte	'EN',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,189,4,3
	.word	53412
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,195,4,3
	.word	53503
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,18,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,18,202,4,3
	.word	53602
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,18,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,18,209,4,3
	.word	53709
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,18,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_SWEVT_Bits',0,18,220,4,3
	.word	53816
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,18,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SYSCON_Bits',0,18,231,4,3
	.word	53970
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,18,234,4,16,4,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	490
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,18,238,4,3
	.word	54131
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,18,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	490
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	490
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	490
	.byte	15,0,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_CON_Bits',0,18,249,4,3
	.word	54229
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,18,252,4,16,4,11
	.byte	'Timer',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,18,255,4,3
	.word	54401
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,18,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	490
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_TR_ADR_Bits',0,18,133,5,3
	.word	54481
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,18,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	490
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	490
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	490
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	490
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	490
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	490
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	490
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	490
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	490
	.byte	3,0,2,35,0,0,33
	.byte	'Ifx_CPU_TR_EVT_Bits',0,18,153,5,3
	.word	54554
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,18,156,5,16,4,11
	.byte	'T0',0,4
	.word	490
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	490
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	490
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	490
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	490
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	490
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	490
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	490
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	490
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,18,167,5,3
	.word	54872
	.byte	12,18,175,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46276
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_A',0,18,180,5,3
	.word	55067
	.byte	12,18,183,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46337
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_BIV',0,18,188,5,3
	.word	55126
	.byte	12,18,191,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46416
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_BTV',0,18,196,5,3
	.word	55187
	.byte	12,18,199,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46502
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CCNT',0,18,204,5,3
	.word	55248
	.byte	12,18,207,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46591
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CCTRL',0,18,212,5,3
	.word	55310
	.byte	12,18,215,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46737
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_COMPAT',0,18,220,5,3
	.word	55373
	.byte	33
	.byte	'Ifx_CPU_CORE_ID',0,18,228,5,3
	.word	17949
	.byte	12,18,231,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46893
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_L',0,18,236,5,3
	.word	55462
	.byte	12,18,239,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46986
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_U',0,18,244,5,3
	.word	55525
	.byte	12,18,247,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47079
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPU_ID',0,18,252,5,3
	.word	55588
	.byte	12,18,255,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47186
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPXE',0,18,132,6,3
	.word	55652
	.byte	12,18,135,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47273
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CREVT',0,18,140,6,3
	.word	55714
	.byte	12,18,143,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47427
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CUS_ID',0,18,148,6,3
	.word	55777
	.byte	12,18,151,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47521
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_D',0,18,156,6,3
	.word	55841
	.byte	12,18,159,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47584
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DATR',0,18,164,6,3
	.word	55900
	.byte	12,18,167,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47802
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DBGSR',0,18,172,6,3
	.word	55962
	.byte	12,18,175,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48017
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DBGTCR',0,18,180,6,3
	.word	56025
	.byte	12,18,183,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48111
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCON0',0,18,188,6,3
	.word	56089
	.byte	12,18,191,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48227
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCON2',0,18,196,6,3
	.word	56152
	.byte	12,18,199,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48328
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCX',0,18,204,6,3
	.word	56215
	.byte	12,18,207,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48421
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DEADD',0,18,212,6,3
	.word	56276
	.byte	12,18,215,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48501
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DIEAR',0,18,220,6,3
	.word	56339
	.byte	12,18,223,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48570
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DIETR',0,18,228,6,3
	.word	56402
	.byte	12,18,231,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48799
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DMS',0,18,236,6,3
	.word	56465
	.byte	12,18,239,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48892
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_L',0,18,244,6,3
	.word	56526
	.byte	12,18,247,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48987
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_U',0,18,252,6,3
	.word	56589
	.byte	12,18,255,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49082
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPRE',0,18,132,7,3
	.word	56652
	.byte	12,18,135,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49172
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPWE',0,18,140,7,3
	.word	56714
	.byte	12,18,143,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49262
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DSTR',0,18,148,7,3
	.word	56776
	.byte	12,18,151,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49586
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_EXEVT',0,18,156,7,3
	.word	56838
	.byte	12,18,159,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49740
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FCX',0,18,164,7,3
	.word	56901
	.byte	12,18,167,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49846
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,18,172,7,3
	.word	56962
	.byte	12,18,175,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50195
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,18,180,7,3
	.word	57032
	.byte	12,18,183,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50355
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,18,188,7,3
	.word	57102
	.byte	12,18,191,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50436
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,18,196,7,3
	.word	57171
	.byte	12,18,199,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50523
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,18,204,7,3
	.word	57242
	.byte	12,18,207,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50610
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,18,212,7,3
	.word	57313
	.byte	12,18,215,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50697
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_ICNT',0,18,220,7,3
	.word	57384
	.byte	33
	.byte	'Ifx_CPU_ICR',0,18,228,7,3
	.word	18106
	.byte	12,18,231,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50814
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_ISP',0,18,236,7,3
	.word	57467
	.byte	12,18,239,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50880
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_LCX',0,18,244,7,3
	.word	57528
	.byte	12,18,247,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50986
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M1CNT',0,18,252,7,3
	.word	57589
	.byte	12,18,255,7,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51079
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M2CNT',0,18,132,8,3
	.word	57652
	.byte	12,18,135,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51172
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M3CNT',0,18,140,8,3
	.word	57715
	.byte	12,18,143,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51265
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PC',0,18,148,8,3
	.word	57778
	.byte	12,18,151,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51350
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON0',0,18,156,8,3
	.word	57838
	.byte	12,18,159,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51466
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON1',0,18,164,8,3
	.word	57901
	.byte	12,18,167,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51577
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON2',0,18,172,8,3
	.word	57964
	.byte	12,18,175,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51678
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCXI',0,18,180,8,3
	.word	58027
	.byte	12,18,183,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51808
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PIEAR',0,18,188,8,3
	.word	58089
	.byte	12,18,191,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51877
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PIETR',0,18,196,8,3
	.word	58152
	.byte	12,18,199,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52106
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA0',0,18,204,8,3
	.word	58215
	.byte	12,18,207,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52219
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA1',0,18,212,8,3
	.word	58277
	.byte	12,18,215,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52332
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA2',0,18,220,8,3
	.word	58339
	.byte	12,18,223,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52423
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PSTR',0,18,228,8,3
	.word	58401
	.byte	12,18,231,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52626
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PSW',0,18,236,8,3
	.word	58463
	.byte	12,18,239,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52869
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SEGEN',0,18,244,8,3
	.word	58524
	.byte	12,18,247,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52997
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SMACON',0,18,252,8,3
	.word	58587
	.byte	12,18,255,8,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53238
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENA',0,18,132,9,3
	.word	58651
	.byte	12,18,135,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53321
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENB',0,18,140,9,3
	.word	58721
	.byte	12,18,143,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53412
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,18,148,9,3
	.word	58791
	.byte	12,18,151,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53503
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,18,156,9,3
	.word	58865
	.byte	12,18,159,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53602
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,18,164,9,3
	.word	58939
	.byte	12,18,167,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53709
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,18,172,9,3
	.word	59009
	.byte	12,18,175,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53816
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SWEVT',0,18,180,9,3
	.word	59079
	.byte	12,18,183,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53970
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SYSCON',0,18,188,9,3
	.word	59142
	.byte	12,18,191,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54131
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TASK_ASI',0,18,196,9,3
	.word	59206
	.byte	12,18,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54229
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_CON',0,18,204,9,3
	.word	59272
	.byte	12,18,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54401
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_TIMER',0,18,212,9,3
	.word	59337
	.byte	12,18,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54481
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TR_ADR',0,18,220,9,3
	.word	59404
	.byte	12,18,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54554
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TR_EVT',0,18,228,9,3
	.word	59468
	.byte	12,18,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54872
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TRIG_ACC',0,18,236,9,3
	.word	59532
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,18,247,9,25,8,13
	.byte	'L',0
	.word	55462
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	55525
	.byte	4,2,35,4,0,14
	.word	59598
	.byte	33
	.byte	'Ifx_CPU_CPR',0,18,251,9,3
	.word	59640
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,18,254,9,25,8,13
	.byte	'L',0
	.word	56526
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	56589
	.byte	4,2,35,4,0,14
	.word	59666
	.byte	33
	.byte	'Ifx_CPU_DPR',0,18,130,10,3
	.word	59708
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,18,133,10,25,16,13
	.byte	'LA',0
	.word	58939
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	59009
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	58791
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	58865
	.byte	4,2,35,12,0,14
	.word	59734
	.byte	33
	.byte	'Ifx_CPU_SPROT_RGN',0,18,139,10,3
	.word	59816
	.byte	18,12
	.word	59337
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,18,142,10,25,16,13
	.byte	'CON',0
	.word	59272
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	59848
	.byte	12,2,35,4,0,14
	.word	59857
	.byte	33
	.byte	'Ifx_CPU_TPS',0,18,146,10,3
	.word	59905
	.byte	10
	.byte	'_Ifx_CPU_TR',0,18,149,10,25,8,13
	.byte	'EVT',0
	.word	59468
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	59404
	.byte	4,2,35,4,0,14
	.word	59931
	.byte	33
	.byte	'Ifx_CPU_TR',0,18,153,10,3
	.word	59976
	.byte	18,176,32
	.word	642
	.byte	19,175,32,0,18,208,223,1
	.word	642
	.byte	19,207,223,1,0,18,248,1
	.word	642
	.byte	19,247,1,0,18,244,29
	.word	642
	.byte	19,243,29,0,18,188,3
	.word	642
	.byte	19,187,3,0,18,232,3
	.word	642
	.byte	19,231,3,0,18,252,23
	.word	642
	.byte	19,251,23,0,18,228,63
	.word	642
	.byte	19,227,63,0,18,128,1
	.word	59666
	.byte	19,15,0,14
	.word	60091
	.byte	18,128,31
	.word	642
	.byte	19,255,30,0,18,64
	.word	59598
	.byte	19,7,0,14
	.word	60117
	.byte	18,192,31
	.word	642
	.byte	19,191,31,0,18,16
	.word	55652
	.byte	19,3,0,18,16
	.word	56652
	.byte	19,3,0,18,16
	.word	56714
	.byte	19,3,0,18,208,7
	.word	642
	.byte	19,207,7,0,14
	.word	59857
	.byte	18,240,23
	.word	642
	.byte	19,239,23,0,18,64
	.word	59931
	.byte	19,7,0,14
	.word	60196
	.byte	18,192,23
	.word	642
	.byte	19,191,23,0,18,232,1
	.word	642
	.byte	19,231,1,0,18,180,1
	.word	642
	.byte	19,179,1,0,18,172,1
	.word	642
	.byte	19,171,1,0,18,64
	.word	55841
	.byte	19,15,0,18,64
	.word	642
	.byte	19,63,0,18,64
	.word	55067
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,18,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	60001
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	58524
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	60012
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	59206
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	60025
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	58215
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	58277
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	58339
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	60036
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	56152
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4914
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	58587
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	56776
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3095
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	55900
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	56276
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	56339
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	56402
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4285
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	56089
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	60047
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	58401
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	57901
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	57964
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	57838
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	58089
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	58152
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	60058
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	55373
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	60069
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	56962
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	57102
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	57032
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3095
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	57171
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	57242
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	57313
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	60080
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	60101
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	60106
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	60126
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	60131
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	60142
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	60151
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	60160
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	60169
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	60180
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	60185
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	60205
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	60210
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	55310
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	55248
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	57384
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	57589
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	57652
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	57715
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	60221
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	55962
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3095
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	56838
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	55714
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	59079
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	44217
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	59532
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5254
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	56465
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	56215
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	56025
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	60232
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	58027
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	58463
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	57778
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4914
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	59142
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	55588
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	17949
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	55126
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	55187
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	57467
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	18106
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4914
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	56901
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	57528
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	44208
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	55777
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	60243
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	60254
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	60263
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	60272
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	60263
	.byte	64,4,35,192,255,3,0,14
	.word	60281
	.byte	33
	.byte	'Ifx_CPU',0,18,130,11,3
	.word	62072
	.byte	33
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	1454
	.byte	33
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1536
	.byte	33
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10068
	.byte	33
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10358
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	62188
	.byte	33
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	62220
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10358
	.byte	4,2,35,8,0,14
	.word	62246
	.byte	33
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	62305
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	62333
	.byte	33
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	62370
	.byte	18,64
	.word	10358
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	62398
	.byte	64,2,35,0,0,14
	.word	62407
	.byte	33
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	62439
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10358
	.byte	4,2,35,12,0,14
	.word	62464
	.byte	33
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	62536
	.byte	18,8
	.word	10358
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	62562
	.byte	8,2,35,0,0,14
	.word	62571
	.byte	33
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	62607
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10358
	.byte	4,2,35,12,0,14
	.word	62637
	.byte	33
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	62710
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	62736
	.byte	33
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	62771
	.byte	18,192,1
	.word	10358
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5254
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	62797
	.byte	192,1,2,35,16,0,14
	.word	62807
	.byte	33
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	62874
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10358
	.byte	4,2,35,4,0,14
	.word	62900
	.byte	33
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	62948
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	62976
	.byte	33
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	63009
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	62562
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	62562
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	62562
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	62562
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10358
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10358
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	44226
	.byte	40,2,35,40,0,14
	.word	63036
	.byte	33
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	63163
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	63190
	.byte	33
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	63222
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	63248
	.byte	33
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	63280
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10358
	.byte	4,2,35,8,0,14
	.word	63306
	.byte	33
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	63366
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10358
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	44208
	.byte	16,2,35,16,0,14
	.word	63392
	.byte	33
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	63486
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10358
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10358
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10358
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4285
	.byte	24,2,35,24,0,14
	.word	63513
	.byte	33
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	63630
	.byte	18,12
	.word	10358
	.byte	19,2,0,18,32
	.word	10358
	.byte	19,7,0,18,32
	.word	63667
	.byte	19,0,0,18,88
	.word	642
	.byte	19,87,0,18,108
	.word	10358
	.byte	19,26,0,18,96
	.word	642
	.byte	19,95,0,18,96
	.word	63667
	.byte	19,2,0,18,160,3
	.word	642
	.byte	19,159,3,0,18,64
	.word	63667
	.byte	19,1,0,18,192,3
	.word	642
	.byte	19,191,3,0,18,16
	.word	10358
	.byte	19,3,0,18,64
	.word	63752
	.byte	19,3,0,18,192,2
	.word	642
	.byte	19,191,2,0,18,52
	.word	642
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	63658
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3095
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10358
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10358
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	62562
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4914
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	63676
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	63685
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	63694
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	63703
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10358
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5254
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	63712
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	63721
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	63712
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	63721
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	63732
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	63741
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	63761
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	63770
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	63658
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	63781
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	63658
	.byte	12,3,35,192,18,0,14
	.word	63790
	.byte	33
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	64250
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	64276
	.byte	33
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	64309
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10358
	.byte	4,2,35,12,0,14
	.word	64336
	.byte	33
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	64409
	.byte	18,56
	.word	642
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10358
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10358
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10358
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	64436
	.byte	56,2,35,24,0,14
	.word	64445
	.byte	33
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	64568
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	64594
	.byte	33
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	64626
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10358
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10358
	.byte	4,2,35,16,0,14
	.word	64652
	.byte	33
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	64737
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	64763
	.byte	33
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	64795
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	63667
	.byte	32,2,35,0,0,14
	.word	64821
	.byte	33
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	64854
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	63667
	.byte	32,2,35,0,0,14
	.word	64881
	.byte	33
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	64915
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10358
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10358
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10358
	.byte	4,2,35,20,0,14
	.word	64943
	.byte	33
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	65036
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	65063
	.byte	33
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	65095
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	63752
	.byte	16,2,35,4,0,14
	.word	65121
	.byte	33
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	65167
	.byte	18,24
	.word	10358
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	65193
	.byte	24,2,35,0,0,14
	.word	65202
	.byte	33
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	65235
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	63658
	.byte	12,2,35,0,0,14
	.word	65262
	.byte	33
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	65294
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10358
	.byte	4,2,35,4,0,14
	.word	65320
	.byte	33
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	65366
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10358
	.byte	4,2,35,12,0,14
	.word	65392
	.byte	33
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	65467
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10358
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10358
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10358
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10358
	.byte	4,2,35,12,0,14
	.word	65496
	.byte	33
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	65570
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10358
	.byte	4,2,35,0,0,14
	.word	65598
	.byte	33
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	65632
	.byte	18,4
	.word	62188
	.byte	19,0,0,14
	.word	65659
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	65668
	.byte	4,2,35,0,0,14
	.word	65673
	.byte	33
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	65709
	.byte	18,48
	.word	62246
	.byte	19,3,0,14
	.word	65737
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	65746
	.byte	48,2,35,0,0,14
	.word	65751
	.byte	33
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	65791
	.byte	14
	.word	62333
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	65821
	.byte	4,2,35,0,0,14
	.word	65826
	.byte	33
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	65860
	.byte	18,64
	.word	62407
	.byte	19,0,0,14
	.word	65887
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	65896
	.byte	64,2,35,0,0,14
	.word	65901
	.byte	33
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	65935
	.byte	18,32
	.word	62464
	.byte	19,1,0,14
	.word	65962
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	65971
	.byte	32,2,35,0,0,14
	.word	65976
	.byte	33
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	66012
	.byte	14
	.word	62571
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	66040
	.byte	8,2,35,0,0,14
	.word	66045
	.byte	33
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	66089
	.byte	18,16
	.word	62637
	.byte	19,0,0,14
	.word	66121
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	66130
	.byte	16,2,35,0,0,14
	.word	66135
	.byte	33
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	66169
	.byte	18,8
	.word	62736
	.byte	19,1,0,14
	.word	66196
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	66205
	.byte	8,2,35,0,0,14
	.word	66210
	.byte	33
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	66244
	.byte	18,208,1
	.word	62807
	.byte	19,0,0,14
	.word	66271
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	66281
	.byte	208,1,2,35,0,0,14
	.word	66286
	.byte	33
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	66322
	.byte	14
	.word	62900
	.byte	14
	.word	62900
	.byte	14
	.word	62900
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	66349
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4914
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	66354
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	66359
	.byte	8,2,35,24,0,14
	.word	66364
	.byte	33
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	66455
	.byte	18,4
	.word	62976
	.byte	19,0,0,14
	.word	66484
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	66493
	.byte	4,2,35,0,0,14
	.word	66498
	.byte	33
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	66534
	.byte	18,80
	.word	63036
	.byte	19,0,0,14
	.word	66562
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	66571
	.byte	80,2,35,0,0,14
	.word	66576
	.byte	33
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	66612
	.byte	18,4
	.word	63190
	.byte	19,0,0,14
	.word	66640
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	66649
	.byte	4,2,35,0,0,14
	.word	66654
	.byte	33
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	66688
	.byte	18,4
	.word	63248
	.byte	19,0,0,14
	.word	66715
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	66724
	.byte	4,2,35,0,0,14
	.word	66729
	.byte	33
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	66763
	.byte	18,12
	.word	63306
	.byte	19,0,0,14
	.word	66790
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	66799
	.byte	12,2,35,0,0,14
	.word	66804
	.byte	33
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	66838
	.byte	18,64
	.word	63392
	.byte	19,1,0,14
	.word	66865
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	66874
	.byte	64,2,35,0,0,14
	.word	66879
	.byte	33
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	66915
	.byte	18,48
	.word	63513
	.byte	19,0,0,14
	.word	66943
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	66952
	.byte	48,2,35,0,0,14
	.word	66957
	.byte	33
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	66995
	.byte	18,204,18
	.word	63790
	.byte	19,0,0,14
	.word	67024
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	67034
	.byte	204,18,2,35,0,0,14
	.word	67039
	.byte	33
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	67075
	.byte	18,4
	.word	64276
	.byte	19,0,0,14
	.word	67102
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	67111
	.byte	4,2,35,0,0,14
	.word	67116
	.byte	33
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	67152
	.byte	18,64
	.word	64336
	.byte	19,3,0,14
	.word	67180
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	67189
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10358
	.byte	4,2,35,64,0,14
	.word	67194
	.byte	33
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	67243
	.byte	18,80
	.word	64445
	.byte	19,0,0,14
	.word	67271
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	67280
	.byte	80,2,35,0,0,14
	.word	67285
	.byte	33
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	67319
	.byte	18,4
	.word	64594
	.byte	19,0,0,14
	.word	67346
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	67355
	.byte	4,2,35,0,0,14
	.word	67360
	.byte	33
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	67394
	.byte	18,40
	.word	64652
	.byte	19,1,0,14
	.word	67421
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	67430
	.byte	40,2,35,0,0,14
	.word	67435
	.byte	33
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	67469
	.byte	18,8
	.word	64763
	.byte	19,1,0,14
	.word	67496
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	67505
	.byte	8,2,35,0,0,14
	.word	67510
	.byte	33
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	67544
	.byte	18,32
	.word	64821
	.byte	19,0,0,14
	.word	67571
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	67580
	.byte	32,2,35,0,0,14
	.word	67585
	.byte	33
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	67621
	.byte	18,32
	.word	64881
	.byte	19,0,0,14
	.word	67649
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	67658
	.byte	32,2,35,0,0,14
	.word	67663
	.byte	33
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	67701
	.byte	18,96
	.word	64943
	.byte	19,3,0,14
	.word	67730
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	67739
	.byte	96,2,35,0,0,14
	.word	67744
	.byte	33
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	67780
	.byte	18,4
	.word	65063
	.byte	19,0,0,14
	.word	67808
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	67817
	.byte	4,2,35,0,0,14
	.word	67822
	.byte	33
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	67856
	.byte	14
	.word	65121
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	67883
	.byte	20,2,35,0,0,14
	.word	67888
	.byte	33
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	67922
	.byte	18,24
	.word	65202
	.byte	19,0,0,14
	.word	67949
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	67958
	.byte	24,2,35,0,0,14
	.word	67963
	.byte	33
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	67999
	.byte	18,12
	.word	65262
	.byte	19,0,0,14
	.word	68027
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	68036
	.byte	12,2,35,0,0,14
	.word	68041
	.byte	33
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	68075
	.byte	18,16
	.word	65320
	.byte	19,1,0,14
	.word	68102
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	68111
	.byte	16,2,35,0,0,14
	.word	68116
	.byte	33
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	68150
	.byte	18,64
	.word	65496
	.byte	19,3,0,14
	.word	68177
	.byte	18,224,1
	.word	642
	.byte	19,223,1,0,18,32
	.word	65392
	.byte	19,1,0,14
	.word	68202
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	68186
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	68191
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	68211
	.byte	32,3,35,160,2,0,14
	.word	68216
	.byte	33
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	68285
	.byte	14
	.word	65598
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	68313
	.byte	4,2,35,0,0,14
	.word	68318
	.byte	33
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	68354
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,33
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	68382
	.byte	29,5,160,1,9,6,13
	.byte	'counter',0
	.word	1858
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	642
	.byte	1,2,35,4,0,33
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	68471
	.byte	29,5,172,1,9,32,13
	.byte	'instruction',0
	.word	68471
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	68471
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	68471
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	68471
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	68471
	.byte	6,2,35,24,0,33
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	68537
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,23,45,16,4,11
	.byte	'EN0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,23,79,3
	.word	68655
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,23,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,23,85,3
	.word	69216
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,23,88,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,23,95,3
	.word	69297
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,23,98,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,23,111,3
	.word	69450
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,23,114,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,23,121,3
	.word	69698
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,23,124,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM0_Bits',0,23,128,1,3
	.word	69844
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,23,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_COMM1_Bits',0,23,136,1,3
	.word	69942
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,23,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_COMM2_Bits',0,23,144,1,3
	.word	70058
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,23,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCRD_Bits',0,23,153,1,3
	.word	70174
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,23,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCRP_Bits',0,23,162,1,3
	.word	70314
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,23,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	467
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	659
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCW_Bits',0,23,171,1,3
	.word	70454
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,23,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	642
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	642
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	659
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	642
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	642
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FCON_Bits',0,23,193,1,3
	.word	70593
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,23,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	642
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	642
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	642
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FPRO_Bits',0,23,218,1,3
	.word	70955
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,23,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	659
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	642
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	642
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FSR_Bits',0,23,254,1,3
	.word	71396
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,23,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	642
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	642
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_ID_Bits',0,23,134,2,3
	.word	72002
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,23,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	659
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_MARD_Bits',0,23,147,2,3
	.word	72113
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,23,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	659
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_MARP_Bits',0,23,159,2,3
	.word	72327
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,23,162,2,16,4,11
	.byte	'L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	642
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	659
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	642
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCOND_Bits',0,23,179,2,3
	.word	72514
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,23,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	642
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,23,188,2,3
	.word	72838
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,23,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	659
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,23,199,2,3
	.word	72981
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	659
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	642
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	642
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	642
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	659
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,219,2,3
	.word	73170
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,23,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	642
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,23,254,2,3
	.word	73533
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,23,129,3,16,4,11
	.byte	'S0L',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONP_Bits',0,23,160,3,3
	.word	74128
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,23,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	642
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	642
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	642
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	642
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	642
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	642
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	642
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	642
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	642
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	642
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	642
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	642
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	642
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	642
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	642
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	642
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	642
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	642
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	642
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	642
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	642
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,23,194,3,3
	.word	74652
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,23,197,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,23,201,3,3
	.word	75234
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,23,204,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,23,208,3,3
	.word	75336
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,23,211,3,16,4,11
	.byte	'TAG',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,23,215,3,3
	.word	75438
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,23,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	467
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRAD_Bits',0,23,222,3,3
	.word	75540
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,23,225,3,16,4,11
	.byte	'STRT',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	642
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	642
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	642
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	642
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	642
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	659
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_RRCT_Bits',0,23,236,3,3
	.word	75634
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,23,239,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD0_Bits',0,23,242,3,3
	.word	75844
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,23,245,3,16,4,11
	.byte	'DATA',0,4
	.word	467
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD1_Bits',0,23,248,3,3
	.word	75917
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,23,251,3,16,4,11
	.byte	'SEL',0,1
	.word	642
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	642
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	642
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	467
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,23,130,4,3
	.word	75990
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,23,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	642
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,23,137,4,3
	.word	76145
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,23,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	642
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	467
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	642
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	642
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	642
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,23,147,4,3
	.word	76250
	.byte	12,23,155,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68655
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN0',0,23,160,4,3
	.word	76398
	.byte	12,23,163,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69216
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN1',0,23,168,4,3
	.word	76464
	.byte	12,23,171,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69297
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_CFG',0,23,176,4,3
	.word	76530
	.byte	12,23,179,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69450
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_STAT',0,23,184,4,3
	.word	76598
	.byte	12,23,187,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69698
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_TOP',0,23,192,4,3
	.word	76667
	.byte	12,23,195,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69844
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM0',0,23,200,4,3
	.word	76735
	.byte	12,23,203,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69942
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM1',0,23,208,4,3
	.word	76800
	.byte	12,23,211,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70058
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM2',0,23,216,4,3
	.word	76865
	.byte	12,23,219,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70174
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCRD',0,23,224,4,3
	.word	76930
	.byte	12,23,227,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70314
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCRP',0,23,232,4,3
	.word	76995
	.byte	12,23,235,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70454
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCW',0,23,240,4,3
	.word	77060
	.byte	12,23,243,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70593
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FCON',0,23,248,4,3
	.word	77124
	.byte	12,23,251,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70955
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FPRO',0,23,128,5,3
	.word	77188
	.byte	12,23,131,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71396
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FSR',0,23,136,5,3
	.word	77252
	.byte	12,23,139,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72002
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ID',0,23,144,5,3
	.word	77315
	.byte	12,23,147,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72113
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_MARD',0,23,152,5,3
	.word	77377
	.byte	12,23,155,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72327
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_MARP',0,23,160,5,3
	.word	77441
	.byte	12,23,163,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72514
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCOND',0,23,168,5,3
	.word	77505
	.byte	12,23,171,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72838
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONDBG',0,23,176,5,3
	.word	77572
	.byte	12,23,179,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72981
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONHSM',0,23,184,5,3
	.word	77641
	.byte	12,23,187,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73170
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,23,192,5,3
	.word	77710
	.byte	12,23,195,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73533
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONOTP',0,23,200,5,3
	.word	77783
	.byte	12,23,203,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74128
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONP',0,23,208,5,3
	.word	77852
	.byte	12,23,211,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74652
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONWOP',0,23,216,5,3
	.word	77919
	.byte	12,23,219,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75234
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG0',0,23,224,5,3
	.word	77988
	.byte	12,23,227,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75336
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG1',0,23,232,5,3
	.word	78056
	.byte	12,23,235,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75438
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG2',0,23,240,5,3
	.word	78124
	.byte	12,23,243,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75540
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRAD',0,23,248,5,3
	.word	78192
	.byte	12,23,251,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75634
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRCT',0,23,128,6,3
	.word	78256
	.byte	12,23,131,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75844
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD0',0,23,136,6,3
	.word	78320
	.byte	12,23,139,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75917
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD1',0,23,144,6,3
	.word	78384
	.byte	12,23,147,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75990
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_CFG',0,23,152,6,3
	.word	78448
	.byte	12,23,155,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76145
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_STAT',0,23,160,6,3
	.word	78516
	.byte	12,23,163,6,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76250
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_TOP',0,23,168,6,3
	.word	78585
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,23,179,6,25,12,13
	.byte	'CFG',0
	.word	76530
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	76598
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	76667
	.byte	4,2,35,8,0,14
	.word	78653
	.byte	33
	.byte	'Ifx_FLASH_CBAB',0,23,184,6,3
	.word	78716
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,23,187,6,25,12,13
	.byte	'CFG0',0
	.word	77988
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	78056
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	78124
	.byte	4,2,35,8,0,14
	.word	78745
	.byte	33
	.byte	'Ifx_FLASH_RDB',0,23,192,6,3
	.word	78809
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,23,195,6,25,12,13
	.byte	'CFG',0
	.word	78448
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	78516
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	78585
	.byte	4,2,35,8,0,14
	.word	78837
	.byte	33
	.byte	'Ifx_FLASH_UBAB',0,23,200,6,3
	.word	78900
	.byte	33
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8667
	.byte	33
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8580
	.byte	33
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4923
	.byte	33
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2976
	.byte	33
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3971
	.byte	33
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	3104
	.byte	33
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3751
	.byte	33
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3319
	.byte	33
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3534
	.byte	33
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7939
	.byte	33
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	8063
	.byte	33
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	8147
	.byte	33
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8327
	.byte	33
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6578
	.byte	33
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	7102
	.byte	33
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6752
	.byte	33
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6926
	.byte	33
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7591
	.byte	33
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2405
	.byte	33
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5915
	.byte	33
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6403
	.byte	33
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	6062
	.byte	33
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6231
	.byte	33
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7258
	.byte	33
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	2089
	.byte	33
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5629
	.byte	33
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5263
	.byte	33
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4294
	.byte	33
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4598
	.byte	33
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9194
	.byte	33
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8627
	.byte	33
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5214
	.byte	33
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	3055
	.byte	33
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4245
	.byte	33
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3279
	.byte	33
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3931
	.byte	33
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3494
	.byte	33
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3711
	.byte	33
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	8023
	.byte	33
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8272
	.byte	33
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8531
	.byte	33
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7899
	.byte	33
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6712
	.byte	33
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7218
	.byte	33
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6886
	.byte	33
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	7062
	.byte	33
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2936
	.byte	33
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7551
	.byte	33
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	6022
	.byte	33
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6538
	.byte	33
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6191
	.byte	33
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6363
	.byte	33
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2365
	.byte	33
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5875
	.byte	33
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5589
	.byte	33
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4558
	.byte	33
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4874
	.byte	14
	.word	9234
	.byte	33
	.byte	'Ifx_P',0,8,139,6,3
	.word	80247
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,33
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	80267
	.byte	15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,33
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	80418
	.byte	15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,33
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	80662
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,33
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	80760
	.byte	33
	.byte	'IfxPort_State',0,7,178,1,3
	.word	9847
	.byte	29,7,190,1,9,8,13
	.byte	'port',0
	.word	9842
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	642
	.byte	1,2,35,4,0,33
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	81225
	.byte	33
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	206
	.byte	29,9,212,5,9,8,13
	.byte	'value',0
	.word	1858
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1858
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	81325
	.byte	29,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	642
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	642
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	81396
	.byte	29,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	263
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	81285
	.byte	4,2,35,8,0,33
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	81513
	.byte	3
	.word	203
	.byte	29,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	81325
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	81325
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	81325
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	81325
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	81325
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	81325
	.byte	8,2,35,40,0,33
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	81615
	.byte	29,9,128,6,9,8,13
	.byte	'value',0
	.word	1858
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1858
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	81767
	.byte	3
	.word	81513
	.byte	29,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	642
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	81843
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	81396
	.byte	8,2,35,8,0,33
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	81848
	.byte	33
	.byte	'IfxSrc_Tos',0,16,74,3
	.word	16998
	.byte	33
	.byte	'IfxStm_Comparator',0,12,155,1,3
	.word	14893
	.byte	33
	.byte	'IfxStm_ComparatorInterrupt',0,12,163,1,3
	.word	15011
	.byte	33
	.byte	'IfxStm_ComparatorOffset',0,12,201,1,3
	.word	15084
	.byte	33
	.byte	'IfxStm_ComparatorSize',0,12,239,1,3
	.word	16009
	.byte	15,12,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,33
	.byte	'IfxStm_SleepMode',0,12,248,1,3
	.word	82111
	.byte	15,12,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,33
	.byte	'IfxStm_SuspendMode',0,12,129,2,3
	.word	82197
	.byte	33
	.byte	'IfxStm_CompareConfig',0,12,150,2,3
	.word	17057
	.byte	33
	.byte	'_iob_flag_t',0,24,82,25
	.word	659
	.byte	7
	.byte	'char',0,1,6,33
	.byte	'int8',0,25,54,29
	.word	82360
	.byte	33
	.byte	'int16',0,25,55,29
	.word	18146
	.byte	33
	.byte	'int32',0,25,56,29
	.word	483
	.byte	33
	.byte	'int64',0,25,57,29
	.word	14190
	.byte	14
	.word	642
	.byte	33
	.byte	'vuint8',0,25,59,29
	.word	82423
.L171:
	.byte	14
	.word	642
.L172:
	.byte	14
	.word	642
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,0,3,8,54,15,39,12,63,12,60,12,0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,29,1,49,19
	.byte	0,0,26,11,0,49,19,0,0,27,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,28,46,1,3,8,58,15
	.byte	59,15,57,15,54,15,39,12,63,12,60,12,0,0,29,19,1,58,15,59,15,57,15,11,15,0,0,30,38,0,73,19,0,0,31,11,1
	.byte	49,19,0,0,32,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,33,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,34,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L56:
	.word	.L196-.L195
.L195:
	.half	3
	.word	.L198-.L197
.L197:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm.h',0,2,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxStm_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'zf_common_interrupt.h',0,3,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,3,0,0,0
.L198:
.L196:
	.sdecl	'.debug_info',debug,cluster('system_delay_us_register')
	.sect	'.debug_info'
.L57:
	.word	489
	.half	3
	.word	.L58
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L60,.L59
	.byte	2
	.word	.L53
	.byte	3
	.byte	'system_delay_us_register',0,1,116,6,1,1,1
	.word	.L46,.L96,.L45
	.byte	4
	.byte	'time',0,1,116,39
	.word	.L97,.L98
	.byte	5
	.word	.L46,.L96
	.byte	6
	.byte	'stm_index',0,1,118,18
	.word	.L99,.L100
	.byte	7
	.word	.L101,.L102,.L22
	.byte	8
	.word	.L103,.L102,.L22
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L104,.L105
	.byte	0,0,7
	.word	.L106,.L107,.L108
	.byte	9
	.word	.L109,.L110
	.byte	9
	.word	.L111,.L112
	.byte	8
	.word	.L113,.L107,.L108
	.byte	6
	.byte	'beginTime',0,3,174,5,12
	.word	.L97,.L114
	.byte	7
	.word	.L115,.L107,.L23
	.byte	9
	.word	.L116,.L117
	.byte	10
	.word	.L118,.L107,.L23
	.byte	0,7
	.word	.L115,.L24,.L26
	.byte	9
	.word	.L116,.L117
	.byte	10
	.word	.L118,.L24,.L26
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('system_delay_us_register')
	.sect	'.debug_abbrev'
.L58:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('system_delay_us_register')
	.sect	'.debug_line'
.L59:
	.word	.L200-.L199
.L199:
	.half	3
	.word	.L202-.L201
.L201:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'IfxStm.h',0,1,0,0,0
.L202:
	.byte	5,6,7,0,5,2
	.word	.L46
	.byte	3,243,0,1,4,2,5,19,9
	.half	.L102-.L46
	.byte	3,148,5,1,5,28,9
	.half	.L185-.L102
	.byte	3,1,1,5,5,9
	.half	.L186-.L185
	.byte	1,4,1,5,17,9
	.half	.L22-.L186
	.byte	3,239,122,1,5,40,9
	.half	.L184-.L22
	.byte	3,2,1,5,57,9
	.half	.L187-.L184
	.byte	1,4,3,5,21,9
	.half	.L107-.L187
	.byte	3,198,3,1,5,5,9
	.half	.L203-.L107
	.byte	1,5,54,9
	.half	.L23-.L203
	.byte	3,245,0,1,5,21,9
	.half	.L24-.L23
	.byte	3,139,127,1,5,5,9
	.half	.L204-.L24
	.byte	1,5,34,9
	.half	.L26-.L204
	.byte	3,245,0,1,5,54,9
	.half	.L205-.L26
	.byte	1,4,1,5,1,7,9
	.half	.L108-.L205
	.byte	3,198,123,1,7,9
	.half	.L61-.L108
	.byte	0,1,1
.L200:
	.sdecl	'.debug_ranges',debug,cluster('system_delay_us_register')
	.sect	'.debug_ranges'
.L60:
	.word	-1,.L46,0,.L61-.L46,0,0
	.sdecl	'.debug_info',debug,cluster('system_delay_us')
	.sect	'.debug_info'
.L62:
	.word	273
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L65,.L64
	.byte	2
	.word	.L53
	.byte	3
	.byte	'system_delay_us',0,1,131,1,6,1,1,1
	.word	.L48,.L119,.L47
	.byte	4
	.byte	'time',0,1,131,1,30
	.word	.L97,.L120
	.byte	5
	.word	.L48,.L119
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('system_delay_us')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('system_delay_us')
	.sect	'.debug_line'
.L64:
	.word	.L207-.L206
.L206:
	.half	3
	.word	.L209-.L208
.L208:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0,0
.L209:
	.byte	5,6,7,0,5,2
	.word	.L48
	.byte	3,130,1,1,5,15,9
	.half	.L188-.L48
	.byte	3,2,1,5,5,9
	.half	.L210-.L188
	.byte	1,5,30,7,9
	.half	.L211-.L210
	.byte	3,2,1,5,31,9
	.half	.L29-.L211
	.byte	3,2,1,5,27,9
	.half	.L212-.L29
	.byte	3,1,1,5,25,9
	.half	.L213-.L212
	.byte	1,5,22,9
	.half	.L28-.L213
	.byte	3,125,1,5,30,9
	.half	.L214-.L28
	.byte	1,5,32,7,9
	.half	.L215-.L214
	.byte	3,5,1,5,38,9
	.half	.L216-.L215
	.byte	1,5,32,9
	.half	.L27-.L216
	.byte	3,4,1,5,1,9
	.half	.L30-.L27
	.byte	3,2,1,7,9
	.half	.L66-.L30
	.byte	0,1,1
.L207:
	.sdecl	'.debug_ranges',debug,cluster('system_delay_us')
	.sect	'.debug_ranges'
.L65:
	.word	-1,.L48,0,.L66-.L48,0,0
	.sdecl	'.debug_info',debug,cluster('system_delay_ms')
	.sect	'.debug_info'
.L67:
	.word	273
	.half	3
	.word	.L68
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L70,.L69
	.byte	2
	.word	.L53
	.byte	3
	.byte	'system_delay_ms',0,1,154,1,6,1,1,1
	.word	.L50,.L121,.L49
	.byte	4
	.byte	'time',0,1,154,1,30
	.word	.L97,.L122
	.byte	5
	.word	.L50,.L121
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('system_delay_ms')
	.sect	'.debug_abbrev'
.L68:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('system_delay_ms')
	.sect	'.debug_line'
.L69:
	.word	.L218-.L217
.L217:
	.half	3
	.word	.L220-.L219
.L219:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0,0
.L220:
	.byte	5,6,7,0,5,2
	.word	.L50
	.byte	3,153,1,1,5,15,9
	.half	.L190-.L50
	.byte	3,2,1,5,5,9
	.half	.L221-.L190
	.byte	1,5,27,7,9
	.half	.L222-.L221
	.byte	3,2,1,5,31,9
	.half	.L33-.L222
	.byte	3,2,1,5,27,9
	.half	.L223-.L33
	.byte	3,1,1,5,25,9
	.half	.L224-.L223
	.byte	1,5,22,9
	.half	.L32-.L224
	.byte	3,125,1,5,27,9
	.half	.L225-.L32
	.byte	1,5,34,7,9
	.half	.L226-.L225
	.byte	3,5,1,5,32,9
	.half	.L227-.L226
	.byte	1,5,41,9
	.half	.L228-.L227
	.byte	1,5,34,9
	.half	.L31-.L228
	.byte	3,4,1,5,32,9
	.half	.L229-.L31
	.byte	1,5,1,9
	.half	.L34-.L229
	.byte	3,2,1,7,9
	.half	.L71-.L34
	.byte	0,1,1
.L218:
	.sdecl	'.debug_ranges',debug,cluster('system_delay_ms')
	.sect	'.debug_ranges'
.L70:
	.word	-1,.L50,0,.L71-.L50,0,0
	.sdecl	'.debug_info',debug,cluster('system_delay_init')
	.sect	'.debug_info'
.L72:
	.word	505
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L75,.L74
	.byte	2
	.word	.L53
	.byte	3
	.byte	'system_delay_init',0,1,176,1,6,1,1,1
	.word	.L52,.L123,.L51
	.byte	4
	.word	.L52,.L123
	.byte	5
	.byte	'stmConfig',0,1,178,1,26
	.word	.L124,.L125
	.byte	5
	.byte	'interrupt_state',0,1,179,1,13
	.word	.L126,.L127
	.byte	6
	.word	.L128,.L129,.L37
	.byte	7
	.word	.L130,.L129,.L37
	.byte	6
	.word	.L131,.L129,.L36
	.byte	7
	.word	.L132,.L129,.L36
	.byte	5
	.byte	'enabled',0,2,149,5,13
	.word	.L126,.L133
	.byte	6
	.word	.L134,.L129,.L35
	.byte	7
	.word	.L135,.L129,.L35
	.byte	5
	.byte	'reg',0,2,141,5,17
	.word	.L136,.L137
	.byte	0,0,0,0,0,0,6
	.word	.L138,.L139,.L38
	.byte	8
	.word	.L140,.L141
	.byte	7
	.word	.L142,.L139,.L38
	.byte	6
	.word	.L143,.L139,.L38
	.byte	8
	.word	.L144,.L145
	.byte	9
	.word	.L146,.L139,.L38
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('system_delay_init')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,29,1
	.byte	49,16,17,1,18,1,0,0,7,11,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('system_delay_init')
	.sect	'.debug_line'
.L74:
	.word	.L231-.L230
.L230:
	.half	3
	.word	.L233-.L232
.L232:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0,0
.L233:
	.byte	5,6,7,0,5,2
	.word	.L52
	.byte	3,175,1,1,4,2,5,19,9
	.half	.L129-.L52
	.byte	3,222,3,1,5,17,9
	.half	.L192-.L129
	.byte	3,1,1,5,21,9
	.half	.L193-.L192
	.byte	1,5,5,9
	.half	.L194-.L193
	.byte	1,5,14,9
	.half	.L35-.L194
	.byte	3,8,1,5,10,9
	.half	.L234-.L35
	.byte	3,1,1,5,5,9
	.half	.L235-.L234
	.byte	3,1,1,4,3,9
	.half	.L36-.L235
	.byte	3,213,123,1,4,1,5,37,9
	.half	.L37-.L36
	.byte	3,199,0,1,5,35,9
	.half	.L236-.L37
	.byte	1,5,37,9
	.half	.L237-.L236
	.byte	3,1,1,5,35,9
	.half	.L238-.L237
	.byte	1,5,37,9
	.half	.L239-.L238
	.byte	3,1,1,5,35,9
	.half	.L240-.L239
	.byte	1,5,37,9
	.half	.L241-.L240
	.byte	3,1,1,5,35,9
	.half	.L242-.L241
	.byte	1,5,37,9
	.half	.L243-.L242
	.byte	3,1,1,5,35,9
	.half	.L244-.L243
	.byte	1,5,37,9
	.half	.L245-.L244
	.byte	3,1,1,5,35,9
	.half	.L246-.L245
	.byte	1,5,37,9
	.half	.L247-.L246
	.byte	3,1,1,5,35,9
	.half	.L248-.L247
	.byte	1,5,25,9
	.half	.L249-.L248
	.byte	3,2,1,5,39,9
	.half	.L250-.L249
	.byte	1,5,30,9
	.half	.L251-.L250
	.byte	3,1,1,5,43,9
	.half	.L252-.L251
	.byte	1,5,37,9
	.half	.L253-.L252
	.byte	3,2,1,5,35,9
	.half	.L254-.L253
	.byte	1,5,37,9
	.half	.L255-.L254
	.byte	3,1,1,5,35,9
	.half	.L256-.L255
	.byte	1,5,37,9
	.half	.L257-.L256
	.byte	3,1,1,5,35,9
	.half	.L258-.L257
	.byte	1,5,37,9
	.half	.L259-.L258
	.byte	3,1,1,5,35,9
	.half	.L260-.L259
	.byte	1,5,37,9
	.half	.L261-.L260
	.byte	3,1,1,5,35,9
	.half	.L262-.L261
	.byte	1,5,37,9
	.half	.L263-.L262
	.byte	3,1,1,5,35,9
	.half	.L264-.L263
	.byte	1,5,37,9
	.half	.L265-.L264
	.byte	3,1,1,5,35,9
	.half	.L266-.L265
	.byte	1,5,25,9
	.half	.L267-.L266
	.byte	3,2,1,5,39,9
	.half	.L268-.L267
	.byte	1,5,30,9
	.half	.L269-.L268
	.byte	3,1,1,5,43,9
	.half	.L270-.L269
	.byte	1,4,2,5,5,9
	.half	.L139-.L270
	.byte	3,225,5,1,5,17,7,9
	.half	.L271-.L139
	.byte	3,2,1,4,1,5,1,9
	.half	.L38-.L271
	.byte	3,160,122,1,7,9
	.half	.L76-.L38
	.byte	0,1,1
.L231:
	.sdecl	'.debug_ranges',debug,cluster('system_delay_init')
	.sect	'.debug_ranges'
.L75:
	.word	-1,.L52,0,.L76-.L52,0,0
	.sdecl	'.debug_info',debug,cluster('stm0_isr')
	.sect	'.debug_info'
.L77:
	.word	247
	.half	3
	.word	.L78
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L80,.L79
	.byte	2
	.word	.L53
	.byte	3
	.byte	'stm0_isr',0,1,50,1,1,1,1
	.word	.L40,.L147,.L39
	.byte	4
	.word	.L40,.L147
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('stm0_isr')
	.sect	'.debug_abbrev'
.L78:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('stm0_isr')
	.sect	'.debug_line'
.L79:
	.word	.L273-.L272
.L272:
	.half	3
	.word	.L275-.L274
.L274:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0,0
.L275:
	.byte	5,1,7,0,5,2
	.word	.L40
	.byte	3,49,1,5,29,9
	.half	.L276-.L40
	.byte	3,2,1,5,30,9
	.half	.L277-.L276
	.byte	3,1,1,5,43,9
	.half	.L278-.L277
	.byte	1,5,5,9
	.half	.L279-.L278
	.byte	3,1,1,5,21,9
	.half	.L280-.L279
	.byte	1,5,19,9
	.half	.L281-.L280
	.byte	1,5,1,9
	.half	.L282-.L281
	.byte	3,1,1,9
	.half	.L81-.L282
	.byte	0,1,1
.L273:
	.sdecl	'.debug_ranges',debug,cluster('stm0_isr')
	.sect	'.debug_ranges'
.L80:
	.word	-1,.L40,0,.L81-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('stm1_isr')
	.sect	'.debug_info'
.L82:
	.word	247
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L85,.L84
	.byte	2
	.word	.L53
	.byte	3
	.byte	'stm1_isr',0,1,57,1,1,1,1
	.word	.L42,.L148,.L41
	.byte	4
	.word	.L42,.L148
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('stm1_isr')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('stm1_isr')
	.sect	'.debug_line'
.L84:
	.word	.L284-.L283
.L283:
	.half	3
	.word	.L286-.L285
.L285:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0,0
.L286:
	.byte	5,1,7,0,5,2
	.word	.L42
	.byte	3,56,1,5,29,9
	.half	.L287-.L42
	.byte	3,2,1,5,30,9
	.half	.L288-.L287
	.byte	3,1,1,5,43,9
	.half	.L289-.L288
	.byte	1,5,5,9
	.half	.L290-.L289
	.byte	3,1,1,5,21,9
	.half	.L291-.L290
	.byte	1,5,19,9
	.half	.L292-.L291
	.byte	1,5,1,9
	.half	.L293-.L292
	.byte	3,1,1,9
	.half	.L86-.L293
	.byte	0,1,1
.L284:
	.sdecl	'.debug_ranges',debug,cluster('stm1_isr')
	.sect	'.debug_ranges'
.L85:
	.word	-1,.L42,0,.L86-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('system_delay_10ns')
	.sect	'.debug_info'
.L87:
	.word	650
	.half	3
	.word	.L88
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L90,.L89
	.byte	2
	.word	.L53
	.byte	3
	.byte	'system_delay_10ns',0,1,71,6,1,1,1
	.word	.L44,.L149,.L43
	.byte	4
	.byte	'time',0,1,71,32
	.word	.L97,.L150
	.byte	5
	.word	.L44,.L149
	.byte	6
	.byte	'interrupt_global_state',0,1,73,12
	.word	.L97,.L151
	.byte	6
	.byte	'stm_index',0,1,75,18
	.word	.L99,.L152
	.byte	7
	.word	.L101,.L153,.L2
	.byte	8
	.word	.L103,.L153,.L2
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L104,.L154
	.byte	0,0,7
	.word	.L155,.L156,.L157
	.byte	8
	.word	.L158,.L156,.L157
	.byte	7
	.word	.L134,.L156,.L3
	.byte	8
	.word	.L135,.L156,.L3
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L136,.L159
	.byte	0,0,0,0,7
	.word	.L106,.L160,.L161
	.byte	9
	.word	.L109,.L162
	.byte	9
	.word	.L111,.L163
	.byte	8
	.word	.L113,.L160,.L161
	.byte	6
	.byte	'beginTime',0,3,174,5,12
	.word	.L97,.L164
	.byte	7
	.word	.L115,.L160,.L6
	.byte	9
	.word	.L116,.L165
	.byte	10
	.word	.L118,.L160,.L6
	.byte	0,7
	.word	.L115,.L7,.L9
	.byte	9
	.word	.L116,.L165
	.byte	10
	.word	.L118,.L7,.L9
	.byte	0,0,0,5
	.word	.L12,.L166
	.byte	6
	.byte	'stm_sfr',0,1,89,26
	.word	.L167,.L168
	.byte	0,5
	.word	.L13,.L169
	.byte	6
	.byte	'stm_sfr',0,1,98,26
	.word	.L167,.L170
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('system_delay_10ns')
	.sect	'.debug_abbrev'
.L88:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('system_delay_10ns')
	.sect	'.debug_line'
.L89:
	.word	.L295-.L294
.L294:
	.half	3
	.word	.L297-.L296
.L296:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'IfxStm.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0,0
.L297:
	.byte	5,6,7,0,5,2
	.word	.L44
	.byte	3,198,0,1,4,2,5,19,9
	.half	.L153-.L44
	.byte	3,193,5,1,5,28,9
	.half	.L174-.L153
	.byte	3,1,1,5,5,9
	.half	.L175-.L174
	.byte	1,4,1,5,17,9
	.half	.L2-.L175
	.byte	3,196,122,1,4,2,5,19,9
	.half	.L156-.L2
	.byte	3,193,4,1,5,17,9
	.half	.L176-.L156
	.byte	3,1,1,5,21,9
	.half	.L177-.L176
	.byte	1,5,5,9
	.half	.L298-.L177
	.byte	1,4,4,9
	.half	.L3-.L298
	.byte	3,210,123,1,4,1,9
	.half	.L4-.L3
	.byte	3,110,1,5,44,7,9
	.half	.L157-.L4
	.byte	3,2,1,4,3,5,21,9
	.half	.L160-.L157
	.byte	3,239,3,1,5,5,9
	.half	.L299-.L160
	.byte	1,5,54,9
	.half	.L6-.L299
	.byte	3,245,0,1,5,21,9
	.half	.L7-.L6
	.byte	3,139,127,1,5,5,9
	.half	.L300-.L7
	.byte	1,5,34,9
	.half	.L9-.L300
	.byte	3,245,0,1,5,54,9
	.half	.L301-.L9
	.byte	1,4,1,5,43,7,9
	.half	.L161-.L301
	.byte	3,156,123,1,5,18,9
	.half	.L5-.L161
	.byte	3,24,1,9
	.half	.L302-.L5
	.byte	3,110,1,9
	.half	.L303-.L302
	.byte	3,9,1,5,17,9
	.half	.L12-.L303
	.byte	3,122,1,5,33,9
	.half	.L304-.L12
	.byte	1,5,31,9
	.half	.L305-.L304
	.byte	1,5,66,9
	.half	.L306-.L305
	.byte	3,1,1,5,50,9
	.half	.L178-.L306
	.byte	3,1,1,5,53,9
	.half	.L307-.L178
	.byte	1,5,35,9
	.half	.L173-.L307
	.byte	1,5,41,9
	.half	.L308-.L173
	.byte	3,1,1,5,36,9
	.half	.L179-.L308
	.byte	3,1,1,5,23,9
	.half	.L15-.L179
	.byte	1,5,36,9
	.half	.L309-.L15
	.byte	1,5,14,7,9
	.half	.L166-.L309
	.byte	3,1,1,5,17,9
	.half	.L13-.L166
	.byte	3,4,1,5,33,9
	.half	.L310-.L13
	.byte	1,5,31,9
	.half	.L311-.L310
	.byte	1,5,66,9
	.half	.L312-.L311
	.byte	3,1,1,5,50,9
	.half	.L181-.L312
	.byte	3,1,1,5,53,9
	.half	.L313-.L181
	.byte	1,5,35,9
	.half	.L314-.L313
	.byte	1,5,41,9
	.half	.L315-.L314
	.byte	3,1,1,5,36,9
	.half	.L182-.L315
	.byte	3,1,1,5,23,9
	.half	.L18-.L182
	.byte	1,5,36,9
	.half	.L316-.L18
	.byte	1,5,14,7,9
	.half	.L169-.L316
	.byte	3,1,1,5,37,9
	.half	.L11-.L169
	.byte	3,1,1,5,1,9
	.half	.L10-.L11
	.byte	3,3,1,7,9
	.half	.L91-.L10
	.byte	0,1,1
.L295:
	.sdecl	'.debug_ranges',debug,cluster('system_delay_10ns')
	.sect	'.debug_ranges'
.L90:
	.word	-1,.L44,0,.L91-.L44,0,0
	.sdecl	'.debug_info',debug,cluster('stm0_isr_flag')
	.sect	'.debug_info'
.L92:
	.word	229
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L53
	.byte	3
	.byte	'stm0_isr_flag',0,19,47,15
	.word	.L171
	.byte	5,3
	.word	stm0_isr_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('stm0_isr_flag')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('stm1_isr_flag')
	.sect	'.debug_info'
.L94:
	.word	229
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_delay.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L53
	.byte	3
	.byte	'stm1_isr_flag',0,19,48,15
	.word	.L172
	.byte	5,3
	.word	stm1_isr_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('stm1_isr_flag')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('stm0_isr')
	.sect	'.debug_loc'
.L39:
	.word	-1,.L40,0,.L147-.L40
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('stm1_isr')
	.sect	'.debug_loc'
.L41:
	.word	-1,.L42,0,.L148-.L42
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('system_delay_10ns')
	.sect	'.debug_loc'
.L164:
	.word	0,0
.L151:
	.word	-1,.L44,.L178-.L44,.L179-.L44
	.half	1
	.byte	82
	.word	.L180-.L44,.L179-.L44
	.half	1
	.byte	84
	.word	.L181-.L44,.L182-.L44
	.half	1
	.byte	82
	.word	.L183-.L44,.L182-.L44
	.half	1
	.byte	84
	.word	0,0
.L159:
	.word	-1,.L44,.L176-.L44,.L177-.L44
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L154:
	.word	-1,.L44,.L174-.L44,.L175-.L44
	.half	1
	.byte	95
	.word	0,0
.L165:
	.word	0,0
.L162:
	.word	0,0
.L152:
	.word	-1,.L44,.L156-.L44,.L160-.L44
	.half	1
	.byte	84
	.word	.L5-.L44,.L178-.L44
	.half	1
	.byte	84
	.word	.L13-.L44,.L181-.L44
	.half	1
	.byte	84
	.word	.L11-.L44,.L10-.L44
	.half	1
	.byte	84
	.word	0,0
.L168:
	.word	0,0
.L170:
	.word	0,0
.L43:
	.word	-1,.L44,0,.L149-.L44
	.half	2
	.byte	138,0
	.word	0,0
.L163:
	.word	0,0
.L150:
	.word	-1,.L44,0,.L156-.L44
	.half	1
	.byte	84
	.word	.L153-.L44,.L173-.L44
	.half	1
	.byte	88
	.word	.L13-.L44,.L10-.L44
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('system_delay_init')
	.sect	'.debug_loc'
.L141:
	.word	0,0
.L133:
	.word	0,0
.L145:
	.word	0,0
.L127:
	.word	-1,.L52,.L194-.L52,.L123-.L52
	.half	1
	.byte	88
	.word	0,0
.L137:
	.word	-1,.L52,.L192-.L52,.L193-.L52
	.half	1
	.byte	95
	.word	0,0
.L125:
	.word	-1,.L52,0,.L123-.L52
	.half	2
	.byte	145,112
	.word	0,0
.L51:
	.word	-1,.L52,0,.L129-.L52
	.half	2
	.byte	138,0
	.word	.L129-.L52,.L123-.L52
	.half	2
	.byte	138,16
	.word	.L123-.L52,.L123-.L52
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('system_delay_ms')
	.sect	'.debug_loc'
.L49:
	.word	-1,.L50,0,.L121-.L50
	.half	2
	.byte	138,0
	.word	0,0
.L122:
	.word	-1,.L50,0,.L33-.L50
	.half	1
	.byte	84
	.word	.L190-.L50,.L121-.L50
	.half	1
	.byte	95
	.word	.L31-.L50,.L191-.L50
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('system_delay_us')
	.sect	'.debug_loc'
.L47:
	.word	-1,.L48,0,.L119-.L48
	.half	2
	.byte	138,0
	.word	0,0
.L120:
	.word	-1,.L48,0,.L29-.L48
	.half	1
	.byte	84
	.word	.L188-.L48,.L119-.L48
	.half	1
	.byte	95
	.word	.L27-.L48,.L189-.L48
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('system_delay_us_register')
	.sect	'.debug_loc'
.L114:
	.word	0,0
.L105:
	.word	-1,.L46,.L185-.L46,.L186-.L46
	.half	1
	.byte	95
	.word	0,0
.L117:
	.word	0,0
.L110:
	.word	0,0
.L100:
	.word	-1,.L46,.L184-.L46,.L187-.L46
	.half	1
	.byte	84
	.word	0,0
.L45:
	.word	-1,.L46,0,.L96-.L46
	.half	2
	.byte	138,0
	.word	0,0
.L112:
	.word	0,0
.L98:
	.word	-1,.L46,0,.L184-.L46
	.half	1
	.byte	84
	.word	.L102-.L46,.L96-.L46
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L317:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('stm0_isr')
	.sect	'.debug_frame'
	.word	12
	.word	.L317,.L40,.L147-.L40
	.sdecl	'.debug_frame',debug,cluster('stm1_isr')
	.sect	'.debug_frame'
	.word	12
	.word	.L317,.L42,.L148-.L42
	.sdecl	'.debug_frame',debug,cluster('system_delay_10ns')
	.sect	'.debug_frame'
	.word	12
	.word	.L317,.L44,.L149-.L44
	.sdecl	'.debug_frame',debug,cluster('system_delay_us_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L317,.L46,.L96-.L46
	.sdecl	'.debug_frame',debug,cluster('system_delay_us')
	.sect	'.debug_frame'
	.word	12
	.word	.L317,.L48,.L119-.L48
	.sdecl	'.debug_frame',debug,cluster('system_delay_ms')
	.sect	'.debug_frame'
	.word	12
	.word	.L317,.L50,.L121-.L50
	.sdecl	'.debug_frame',debug,cluster('system_delay_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L317,.L52,.L123-.L52
	.byte	4
	.word	(.L129-.L52)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L123-.L129)/2
	.byte	19,0,8,26,0,0
	; Module end
