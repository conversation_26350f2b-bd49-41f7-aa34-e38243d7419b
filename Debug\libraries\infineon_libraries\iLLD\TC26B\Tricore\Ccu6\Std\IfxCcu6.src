	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18232a --dep-file=IfxCcu6.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c'

	
$TC16X
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_connectTrigger',code,cluster('IfxCcu6_connectTrigger')
	.sect	'.text.IfxCcu6.IfxCcu6_connectTrigger'
	.align	2
	
	.global	IfxCcu6_connectTrigger
; Function IfxCcu6_connectTrigger
.L105:
IfxCcu6_connectTrigger:	.type	func
	sub.a	a10,#8
.L476:
	ld.w	d15,[a4]12
.L593:
	st.w	[a10],d15
.L594:
	mul	d0,d5,#2
.L477:
	lea	a15,0xf0002b00
.L595:
	jne.a	a4,a15,.L2
.L596:
	add	d0,#1
.L2:
	ld.w	d15,[a4]12
.L597:
	st.w	[a10],d15
.L598:
	lea	a15,[a10]0
.L599:
	mov	d15,#7
.L600:
	sh	d15,d15,d4
.L601:
	sha	d0,d0,d4
.L268:
	ld.w	d1,[a15]
.L602:
	mov	d2,#-1
	xor	d2,d15
.L603:
	and	d1,d2
.L604:
	and	d15,d0
.L605:
	or	d1,d15
.L606:
	st.w	[a15],d1
.L269:
	ld.w	d15,[a10]
.L607:
	st.w	[a4]12,d15
.L608:
	ret
.L255:
	
__IfxCcu6_connectTrigger_function_end:
	.size	IfxCcu6_connectTrigger,__IfxCcu6_connectTrigger_function_end-IfxCcu6_connectTrigger
.L154:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_disableModulationOutput',code,cluster('IfxCcu6_disableModulationOutput')
	.sect	'.text.IfxCcu6.IfxCcu6_disableModulationOutput'
	.align	2
	
	.global	IfxCcu6_disableModulationOutput
; Function IfxCcu6_disableModulationOutput
.L107:
IfxCcu6_disableModulationOutput:	.type	func
	jeq	d4,#1,.L3
.L825:
	jeq	d5,#6,.L4
.L826:
	mov	d15,#1
.L827:
	sha	d15,d15,d5
.L478:
	ld.w	d0,[a4]128
.L828:
	mov	d1,#-1
	xor	d15,d1
.L479:
	and	d0,d15
.L829:
	st.w	[a4]128,d0
.L830:
	j	.L5
.L4:
.L5:
	j	.L6
.L3:
	jeq	d5,#6,.L7
.L831:
	add	d0,d5,#8
.L480:
	mov	d15,#1
.L832:
	sha	d15,d15,d0
.L482:
	ld.w	d0,[a4]128
.L481:
	mov	d1,#-1
	xor	d15,d1
.L483:
	and	d0,d15
.L833:
	st.w	[a4]128,d0
.L834:
	j	.L8
.L7:
	ld.bu	d15,[a4]129
.L835:
	insert	d15,d15,#0,#7,#1
	st.b	[a4]129,d15
.L8:
.L6:
	ret
.L450:
	
__IfxCcu6_disableModulationOutput_function_end:
	.size	IfxCcu6_disableModulationOutput,__IfxCcu6_disableModulationOutput_function_end-IfxCcu6_disableModulationOutput
.L234:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_disableModule',code,cluster('IfxCcu6_disableModule')
	.sect	'.text.IfxCcu6.IfxCcu6_disableModule'
	.align	2
	
	.global	IfxCcu6_disableModule
; Function IfxCcu6_disableModule
.L109:
IfxCcu6_disableModule:	.type	func
	mov.aa	a15,a4
.L485:
	call	IfxScuWdt_getCpuWatchdogPassword
.L484:
	mov	d15,d2
.L487:
	mov	d4,d15
.L486:
	call	IfxScuWdt_clearCpuEndinit
.L488:
	mov	d0,#1
.L840:
	st.w	[a15],d0
.L841:
	mov	d4,d15
.L489:
	call	IfxScuWdt_setCpuEndinit
.L490:
	j	.L9
.L10:
.L9:
	ld.bu	d15,[a15]
	extr.u	d15,d15,#1,#1
.L842:
	eq	d15,d15,#0
.L843:
	j	.L11
.L11:
	jeq	d15,#1,.L10
.L844:
	ret
.L456:
	
__IfxCcu6_disableModule_function_end:
	.size	IfxCcu6_disableModule,__IfxCcu6_disableModule_function_end-IfxCcu6_disableModule
.L239:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_enableModulationOutput',code,cluster('IfxCcu6_enableModulationOutput')
	.sect	'.text.IfxCcu6.IfxCcu6_enableModulationOutput'
	.align	2
	
	.global	IfxCcu6_enableModulationOutput
; Function IfxCcu6_enableModulationOutput
.L111:
IfxCcu6_enableModulationOutput:	.type	func
	jeq	d4,#1,.L12
.L849:
	jeq	d5,#6,.L13
.L850:
	mov	d15,#1
.L851:
	sha	d15,d15,d5
.L491:
	ld.w	d0,[a4]128
.L852:
	or	d0,d15
.L853:
	st.w	[a4]128,d0
.L854:
	j	.L14
.L13:
.L14:
	j	.L15
.L12:
	jeq	d5,#6,.L16
.L855:
	add	d0,d5,#8
.L492:
	mov	d15,#1
.L856:
	sha	d15,d15,d0
.L494:
	ld.w	d0,[a4]128
.L493:
	or	d0,d15
.L857:
	st.w	[a4]128,d0
.L858:
	j	.L17
.L16:
	ld.bu	d15,[a4]129
.L859:
	or	d15,#128
	st.b	[a4]129,d15
.L17:
.L15:
	ret
.L463:
	
__IfxCcu6_enableModulationOutput_function_end:
	.size	IfxCcu6_enableModulationOutput,__IfxCcu6_enableModulationOutput_function_end-IfxCcu6_enableModulationOutput
.L244:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_enableModule',code,cluster('IfxCcu6_enableModule')
	.sect	'.text.IfxCcu6.IfxCcu6_enableModule'
	.align	2
	
	.global	IfxCcu6_enableModule
; Function IfxCcu6_enableModule
.L113:
IfxCcu6_enableModule:	.type	func
	mov.aa	a15,a4
.L496:
	call	IfxScuWdt_getCpuWatchdogPassword
.L495:
	mov	d15,d2
.L498:
	mov	d4,d15
.L497:
	call	IfxScuWdt_clearCpuEndinit
.L499:
	ld.bu	d0,[a15]
.L864:
	insert	d0,d0,#0,#0,#1
	st.b	[a15],d0
.L865:
	mov	d4,d15
.L500:
	call	IfxScuWdt_setCpuEndinit
.L501:
	j	.L18
.L19:
.L18:
	ld.bu	d15,[a15]
	extr.u	d15,d15,#1,#1
.L866:
	eq	d15,d15,#0
.L867:
	j	.L20
.L20:
	jeq	d15,#0,.L19
.L868:
	ret
.L469:
	
__IfxCcu6_enableModule_function_end:
	.size	IfxCcu6_enableModule,__IfxCcu6_enableModule_function_end-IfxCcu6_enableModule
.L249:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_getAddress',code,cluster('IfxCcu6_getAddress')
	.sect	'.text.IfxCcu6.IfxCcu6_getAddress'
	.align	2
	
	.global	IfxCcu6_getAddress
; Function IfxCcu6_getAddress
.L115:
IfxCcu6_getAddress:	.type	func
	jge	d4,#2,.L21
.L774:
	mul	d15,d4,#8
.L775:
	movh.a	a15,#@his(IfxCcu6_cfg_indexMap)
	lea	a15,[a15]@los(IfxCcu6_cfg_indexMap)
.L776:
	addsc.a	a15,a15,d15,#0
.L777:
	ld.a	a2,[a15]
.L502:
	j	.L22
.L21:
	mov.a	a2,#0
.L22:
	j	.L23
.L23:
	ret
.L422:
	
__IfxCcu6_getAddress_function_end:
	.size	IfxCcu6_getAddress,__IfxCcu6_getAddress_function_end-IfxCcu6_getAddress
.L204:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_getCaptureRegisterValue',code,cluster('IfxCcu6_getCaptureRegisterValue')
	.sect	'.text.IfxCcu6.IfxCcu6_getCaptureRegisterValue'
	.align	2
	
	.global	IfxCcu6_getCaptureRegisterValue
; Function IfxCcu6_getCaptureRegisterValue
.L117:
IfxCcu6_getCaptureRegisterValue:	.type	func
	mov	d2,#0
.L503:
	mov	d15,#0
	jeq	d15,d4,.L24
.L782:
	mov	d15,#1
	jeq	d15,d4,.L25
.L783:
	mov	d15,#2
	jeq	d15,d4,.L26
	j	.L27
.L24:
	ld.w	d2,[a4]48
.L784:
	j	.L28
.L25:
	ld.w	d2,[a4]52
.L785:
	j	.L29
.L26:
	ld.w	d2,[a4]56
.L786:
	j	.L30
.L27:
.L30:
.L29:
.L28:
	j	.L31
.L31:
	ret
.L426:
	
__IfxCcu6_getCaptureRegisterValue_function_end:
	.size	IfxCcu6_getCaptureRegisterValue,__IfxCcu6_getCaptureRegisterValue_function_end-IfxCcu6_getCaptureRegisterValue
.L209:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_getCaptureShadowRegisterValue',code,cluster('IfxCcu6_getCaptureShadowRegisterValue')
	.sect	'.text.IfxCcu6.IfxCcu6_getCaptureShadowRegisterValue'
	.align	2
	
	.global	IfxCcu6_getCaptureShadowRegisterValue
; Function IfxCcu6_getCaptureShadowRegisterValue
.L119:
IfxCcu6_getCaptureShadowRegisterValue:	.type	func
	mov	d2,#0
.L504:
	mov	d15,#0
	jeq	d15,d4,.L32
.L791:
	mov	d15,#1
	jeq	d15,d4,.L33
.L792:
	mov	d15,#2
	jeq	d15,d4,.L34
	j	.L35
.L32:
	ld.w	d2,[a4]64
.L793:
	j	.L36
.L33:
	ld.w	d2,[a4]68
.L794:
	j	.L37
.L34:
	ld.w	d2,[a4]72
.L795:
	j	.L38
.L35:
.L38:
.L37:
.L36:
	j	.L39
.L39:
	ret
.L430:
	
__IfxCcu6_getCaptureShadowRegisterValue_function_end:
	.size	IfxCcu6_getCaptureShadowRegisterValue,__IfxCcu6_getCaptureShadowRegisterValue_function_end-IfxCcu6_getCaptureShadowRegisterValue
.L214:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_getIndex',code,cluster('IfxCcu6_getIndex')
	.sect	'.text.IfxCcu6.IfxCcu6_getIndex'
	.align	2
	
	.global	IfxCcu6_getIndex
; Function IfxCcu6_getIndex
.L121:
IfxCcu6_getIndex:	.type	func
	mov	d2,#-1
.L505:
	mov	d0,#0
.L506:
	j	.L40
.L41:
	mul	d15,d0,#8
.L800:
	movh.a	a15,#@his(IfxCcu6_cfg_indexMap)
	lea	a15,[a15]@los(IfxCcu6_cfg_indexMap)
.L801:
	addsc.a	a15,a15,d15,#0
.L802:
	ld.a	a15,[a15]
.L803:
	jne.a	a15,a4,.L42
.L804:
	mul	d15,d0,#8
.L805:
	movh.a	a15,#@his(IfxCcu6_cfg_indexMap)
	lea	a15,[a15]@los(IfxCcu6_cfg_indexMap)
.L806:
	addsc.a	a15,a15,d15,#0
.L807:
	ld.w	d15,[a15]4
.L808:
	extr	d2,d15,#0,#8
.L809:
	j	.L43
.L42:
	add	d0,#1
.L40:
	jlt.u	d0,#2,.L41
.L43:
	j	.L44
.L44:
	ret
.L434:
	
__IfxCcu6_getIndex_function_end:
	.size	IfxCcu6_getIndex,__IfxCcu6_getIndex_function_end-IfxCcu6_getIndex
.L219:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_getSrcAddress',code,cluster('IfxCcu6_getSrcAddress')
	.sect	'.text.IfxCcu6.IfxCcu6_getSrcAddress'
	.align	2
	
	.global	IfxCcu6_getSrcAddress
; Function IfxCcu6_getSrcAddress
.L123:
IfxCcu6_getSrcAddress:	.type	func
	mov	d8,d4
.L508:
	call	IfxCcu6_getIndex
.L507:
	movh.a	a15,#61444
	lea	a15,[a15]@los(0xf0038420)
.L814:
	mul	d15,d2,#16
	addsc.a	a15,a15,d15,#0
.L509:
	mul	d15,d8,#4
	addsc.a	a2,a15,d15,#0
.L815:
	j	.L45
.L45:
	ret
.L439:
	
__IfxCcu6_getSrcAddress_function_end:
	.size	IfxCcu6_getSrcAddress,__IfxCcu6_getSrcAddress_function_end-IfxCcu6_getSrcAddress
.L224:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_readTimer',code,cluster('IfxCcu6_readTimer')
	.sect	'.text.IfxCcu6.IfxCcu6_readTimer'
	.align	2
	
	.global	IfxCcu6_readTimer
; Function IfxCcu6_readTimer
.L125:
IfxCcu6_readTimer:	.type	func
	jeq	d4,#1,.L46
.L820:
	ld.w	d2,[a4]32
.L510:
	j	.L47
.L46:
	ld.w	d2,[a4]80
.L47:
	j	.L48
.L48:
	ret
.L445:
	
__IfxCcu6_readTimer_function_end:
	.size	IfxCcu6_readTimer,__IfxCcu6_readTimer_function_end-IfxCcu6_readTimer
.L229:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_resetModule',code,cluster('IfxCcu6_resetModule')
	.sect	'.text.IfxCcu6.IfxCcu6_resetModule'
	.align	2
	
	.global	IfxCcu6_resetModule
; Function IfxCcu6_resetModule
.L127:
IfxCcu6_resetModule:	.type	func
	mov.aa	a15,a4
.L512:
	call	IfxScuWdt_getCpuWatchdogPassword
.L511:
	mov	d8,d2
.L514:
	mov	d4,d8
.L513:
	call	IfxScuWdt_clearCpuEndinit
.L515:
	ld.bu	d15,[a15]244
.L873:
	or	d15,#1
	st.b	[a15]244,d15
.L874:
	ld.bu	d15,[a15]240
.L875:
	or	d15,#1
	st.b	[a15]240,d15
.L876:
	mov	d4,d8
.L516:
	call	IfxScuWdt_setCpuEndinit
.L517:
	j	.L49
.L50:
.L49:
	ld.bu	d15,[a15]244
.L877:
	jz.t	d15:1,.L50
.L878:
	mov	d4,d8
.L518:
	call	IfxScuWdt_clearCpuEndinit
.L519:
	ld.bu	d15,[a15]236
.L879:
	or	d15,#1
	st.b	[a15]236,d15
.L880:
	mov	d4,d8
.L520:
	call	IfxScuWdt_setCpuEndinit
.L521:
	ret
.L473:
	
__IfxCcu6_resetModule_function_end:
	.size	IfxCcu6_resetModule,__IfxCcu6_resetModule_function_end-IfxCcu6_resetModule
.L254:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_routeInterruptNode',code,cluster('IfxCcu6_routeInterruptNode')
	.sect	'.text.IfxCcu6.IfxCcu6_routeInterruptNode'
	.align	2
	
	.global	IfxCcu6_routeInterruptNode
; Function IfxCcu6_routeInterruptNode
.L129:
IfxCcu6_routeInterruptNode:	.type	func
	jeq	d4,#0,.L51
.L613:
	jne	d4,#1,.L52
.L51:
	ld.bu	d15,[a4]172
.L614:
	insert	d15,d15,d5,#0,#2
	st.b	[a4]172,d15
.L615:
	j	.L53
.L52:
	jeq	d4,#2,.L54
.L616:
	jne	d4,#3,.L55
.L54:
	ld.bu	d15,[a4]172
.L617:
	insert	d15,d15,d5,#2,#2
	st.b	[a4]172,d15
.L618:
	j	.L56
.L55:
	jeq	d4,#4,.L57
.L619:
	jne	d4,#5,.L58
.L57:
	ld.bu	d15,[a4]172
.L620:
	insert	d15,d15,d5,#4,#2
	st.b	[a4]172,d15
.L621:
	j	.L59
.L58:
	mov	d15,#12
.L622:
	jne	d15,d4,.L60
.L623:
	ld.bu	d15,[a4]172
.L624:
	insert	d15,d15,d5,#6,#2
	st.b	[a4]172,d15
.L625:
	j	.L61
.L60:
	mov	d15,#10
.L626:
	jeq	d15,d4,.L62
.L627:
	mov	d15,#13
.L628:
	jne	d15,d4,.L63
.L62:
	ld.bu	d15,[a4]173
.L629:
	insert	d15,d15,d5,#0,#2
	st.b	[a4]173,d15
.L630:
	j	.L64
.L63:
	jeq	d4,#6,.L65
.L631:
	jne	d4,#7,.L66
.L65:
	ld.bu	d15,[a4]173
.L632:
	insert	d15,d15,d5,#2,#2
	st.b	[a4]173,d15
.L633:
	j	.L67
.L66:
	mov	d15,#8
.L634:
	jeq	d15,d4,.L68
.L635:
	mov	d15,#9
.L636:
	jne	d15,d4,.L69
.L68:
	ld.bu	d15,[a4]173
.L637:
	insert	d15,d15,d5,#4,#2
	st.b	[a4]173,d15
.L638:
	j	.L70
.L69:
	debug
.L70:
.L67:
.L64:
.L61:
.L59:
.L56:
.L53:
	ret
.L277:
	
__IfxCcu6_routeInterruptNode_function_end:
	.size	IfxCcu6_routeInterruptNode,__IfxCcu6_routeInterruptNode_function_end-IfxCcu6_routeInterruptNode
.L159:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setOutputPassiveLevel',code,cluster('IfxCcu6_setOutputPassiveLevel')
	.sect	'.text.IfxCcu6.IfxCcu6_setOutputPassiveLevel'
	.align	2
	
	.global	IfxCcu6_setOutputPassiveLevel
; Function IfxCcu6_setOutputPassiveLevel
.L131:
IfxCcu6_setOutputPassiveLevel:	.type	func
	jeq	d4,#6,.L71
.L289:
	mov	d15,#1
.L643:
	sha	d15,d15,d4
.L523:
	ld.w	d0,[a4]136
.L644:
	mov	d1,#-1
	xor	d15,d1
.L524:
	and	d0,d15
.L645:
	sh	d5,d5,d4
.L522:
	or	d0,d5
.L646:
	st.w	[a4]136,d0
.L290:
	j	.L72
.L71:
	ld.bu	d15,[a4]136
.L647:
	insert	d15,d15,d5,#7,#1
	st.b	[a4]136,d15
.L72:
	ret
.L283:
	
__IfxCcu6_setOutputPassiveLevel_function_end:
	.size	IfxCcu6_setOutputPassiveLevel,__IfxCcu6_setOutputPassiveLevel_function_end-IfxCcu6_setOutputPassiveLevel
.L164:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setT12CaptureCompareState',code,cluster('IfxCcu6_setT12CaptureCompareState')
	.sect	'.text.IfxCcu6.IfxCcu6_setT12CaptureCompareState'
	.align	2
	
	.global	IfxCcu6_setT12CaptureCompareState
; Function IfxCcu6_setT12CaptureCompareState
.L133:
IfxCcu6_setT12CaptureCompareState:	.type	func
	mov	d15,d5
.L525:
	mov	d0,#257
.L652:
	sh	d0,d0,d4
.L527:
	jne	d15,#0,.L73
.L653:
	mov	d1,#1
.L529:
	ld.w	d2,[a4]100
.L654:
	mov	d3,#-1
	xor	d3,d0
.L655:
	and	d2,d3
.L656:
	sh	d1,d1,d4
.L530:
	or	d2,d1
.L657:
	st.w	[a4]100,d2
.L73:
	jne	d15,#1,.L74
.L658:
	mov	d1,#256
.L531:
	ld.w	d2,[a4]100
.L659:
	mov	d3,#-1
	xor	d3,d0
.L660:
	and	d2,d3
.L661:
	sh	d1,d1,d4
.L532:
	or	d2,d1
.L662:
	st.w	[a4]100,d2
.L74:
	jne	d15,#2,.L75
.L663:
	mov	d15,#257
.L526:
	ld.w	d1,[a4]100
.L664:
	mov	d2,#-1
	xor	d0,d2
.L528:
	and	d1,d0
.L665:
	sh	d15,d15,d4
.L533:
	or	d1,d15
.L666:
	st.w	[a4]100,d1
.L75:
	ret
.L294:
	
__IfxCcu6_setT12CaptureCompareState_function_end:
	.size	IfxCcu6_setT12CaptureCompareState,__IfxCcu6_setT12CaptureCompareState_function_end-IfxCcu6_setT12CaptureCompareState
.L169:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setT12CompareValue',code,cluster('IfxCcu6_setT12CompareValue')
	.sect	'.text.IfxCcu6.IfxCcu6_setT12CompareValue'
	.align	2
	
	.global	IfxCcu6_setT12CompareValue
; Function IfxCcu6_setT12CompareValue
.L135:
IfxCcu6_setT12CompareValue:	.type	func
	mov	d15,#0
	jeq	d15,d4,.L76
.L671:
	mov	d15,#1
	jeq	d15,d4,.L77
.L672:
	mov	d15,#2
	jeq	d15,d4,.L78
	j	.L79
.L76:
	ld.hu	d15,[a4]64
.L673:
	insert	d15,d15,d5,#0,#16
	st.h	[a4]64,d15
.L674:
	j	.L80
.L77:
	ld.hu	d15,[a4]68
.L675:
	insert	d15,d15,d5,#0,#16
	st.h	[a4]68,d15
.L676:
	j	.L81
.L78:
	ld.hu	d15,[a4]72
.L677:
	insert	d15,d15,d5,#0,#16
	st.h	[a4]72,d15
.L678:
	j	.L82
.L79:
.L82:
.L81:
.L80:
	ret
.L302:
	
__IfxCcu6_setT12CompareValue_function_end:
	.size	IfxCcu6_setT12CompareValue,__IfxCcu6_setT12CompareValue_function_end-IfxCcu6_setT12CompareValue
.L174:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setT12Frequency',code,cluster('IfxCcu6_setT12Frequency')
	.sect	'.text.IfxCcu6.IfxCcu6_setT12Frequency'
	.align	2
	
	.global	IfxCcu6_setT12Frequency
; Function IfxCcu6_setT12Frequency
.L137:
IfxCcu6_setT12Frequency:	.type	func
	mov.aa	a15,a4
.L535:
	mov	e8,d5,d4
	mov	d10,d6
.L536:
	call	IfxScuCcu_getSpbFrequency
.L534:
	mov	d11,d2
.L538:
	div.f	d4,d11,d8
.L539:
	call	__f_ftous
.L537:
	mov	d0,d2
.L540:
	mov	d3,#0
.L542:
	j	.L83
.L84:
	mov	d2,#2
.L683:
	div	e0,d0,d2
.L684:
	jne	d0,#0,.L85
.L685:
	j	.L86
.L85:
	add	d3,#1
.L83:
	mov	d15,#16
.L686:
	jlt.u	d3,d15,.L84
.L86:
	mov	d15,#16
.L687:
	jge.u	d3,d15,.L87
.L316:
	jeq	d10,#0,.L88
.L688:
	mov	d0,#2
.L541:
	div.u	e0,d9,d0
.L543:
	add	d15,d0,#-1
.L689:
	j	.L89
.L88:
	add	d15,d9,#-1
.L89:
	extr.u	d0,d15,#0,#16
.L323:
	ld.hu	d1,[a15]36
.L690:
	insert	d0,d1,d0,#0,#16
.L544:
	st.h	[a15]36,d0
.L324:
	mov	d15,#1
.L691:
	mov	d0,#0
.L331:
	mov	d1,#0
.L545:
	insert	d1,d1,d15,#6,#1
.L692:
	insert	d1,d1,d0,#14,#1
.L693:
	st.w	[a15]120,d1
.L332:
	and	d15,d3,#7
.L547:
	mov	d0,#0
.L343:
	mul	d1,d0,#8
.L546:
	mov	d0,#7
.L694:
	sh	d0,d0,d1
.L549:
	ld.w	d2,[a15]112
.L695:
	mov	d4,#-1
	xor	d0,d4
.L550:
	and	d2,d0
.L696:
	sh	d15,d15,d1
.L548:
	or	d2,d15
.L697:
	st.w	[a15]112,d2
.L344:
	and	d15,d3,#8
.L698:
	ne	d15,d15,#0
.L551:
	jeq	d15,#0,.L90
.L699:
	mov	d15,#0
.L355:
	mul	d15,d15,#8
.L552:
	add	d15,#3
.L700:
	mov	d0,#1
.L701:
	sh	d0,d0,d15
.L554:
	ld.w	d15,[a15]112
.L553:
	or	d15,d0
.L702:
	st.w	[a15]112,d15
.L90:
	ld.bu	d15,[a15]112
.L703:
	insert	d15,d15,d10,#7,#1
	st.b	[a15]112,d15
.L364:
	mov	d15,#1
.L704:
	sh	d15,d15,d3
	utof	d15,d15
.L705:
	div.f	d2,d11,d15
.L317:
	j	.L91
.L87:
	mov	d2,#0
.L91:
	j	.L92
.L92:
	ret
.L307:
	
__IfxCcu6_setT12Frequency_function_end:
	.size	IfxCcu6_setT12Frequency,__IfxCcu6_setT12Frequency_function_end-IfxCcu6_setT12Frequency
.L179:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setT12InputSignal',code,cluster('IfxCcu6_setT12InputSignal')
	.sect	'.text.IfxCcu6.IfxCcu6_setT12InputSignal'
	.align	2
	
	.global	IfxCcu6_setT12InputSignal
; Function IfxCcu6_setT12InputSignal
.L139:
IfxCcu6_setT12InputSignal:	.type	func
	ld.bu	d0,[a4]20
.L710:
	ld.bu	d15,[a5]12
.L711:
	ge.u	d15,d15,#4
.L712:
	insert	d15,d0,d15,#6,#1
	st.b	[a4]20,d15
.L713:
	ld.bu	d0,[a4]17
.L714:
	ld.bu	d15,[a5]12
.L715:
	insert	d15,d0,d15,#6,#2
	st.b	[a4]17,d15
.L716:
	ret
.L370:
	
__IfxCcu6_setT12InputSignal_function_end:
	.size	IfxCcu6_setT12InputSignal,__IfxCcu6_setT12InputSignal_function_end-IfxCcu6_setT12InputSignal
.L184:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setT13CaptureCompareState',code,cluster('IfxCcu6_setT13CaptureCompareState')
	.sect	'.text.IfxCcu6.IfxCcu6_setT13CaptureCompareState'
	.align	2
	
	.global	IfxCcu6_setT13CaptureCompareState
; Function IfxCcu6_setT13CaptureCompareState
.L141:
IfxCcu6_setT13CaptureCompareState:	.type	func
	mov	d15,d4
.L555:
	mov	d0,#257
.L557:
	sh	d0,#6
.L721:
	jne	d15,#0,.L93
.L722:
	mov	d1,#1
.L559:
	ld.w	d2,[a4]100
.L723:
	mov	d3,#-1
	xor	d3,d0
.L724:
	and	d2,d3
.L725:
	sh	d1,#6
.L560:
	or	d2,d1
.L726:
	st.w	[a4]100,d2
.L93:
	jne	d15,#1,.L94
.L727:
	mov	d1,#256
.L561:
	ld.w	d2,[a4]100
.L728:
	mov	d3,#-1
	xor	d3,d0
.L729:
	and	d2,d3
.L730:
	sh	d1,#6
.L562:
	or	d2,d1
.L731:
	st.w	[a4]100,d2
.L94:
	jne	d15,#2,.L95
.L732:
	mov	d15,#257
.L556:
	ld.w	d1,[a4]100
.L733:
	mov	d2,#-1
	xor	d0,d2
.L558:
	and	d1,d0
.L734:
	sh	d15,#6
.L563:
	or	d1,d15
.L735:
	st.w	[a4]100,d1
.L95:
	ret
.L374:
	
__IfxCcu6_setT13CaptureCompareState_function_end:
	.size	IfxCcu6_setT13CaptureCompareState,__IfxCcu6_setT13CaptureCompareState_function_end-IfxCcu6_setT13CaptureCompareState
.L189:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setT13Frequency',code,cluster('IfxCcu6_setT13Frequency')
	.sect	'.text.IfxCcu6.IfxCcu6_setT13Frequency'
	.align	2
	
	.global	IfxCcu6_setT13Frequency
; Function IfxCcu6_setT13Frequency
.L143:
IfxCcu6_setT13Frequency:	.type	func
	mov.aa	a15,a4
.L565:
	mov	d15,d4
.L566:
	mov	d8,d5
.L567:
	call	IfxScuCcu_getSpbFrequency
.L564:
	mov	d9,d2
.L570:
	div.f	d4,d9,d15
	call	__f_ftous
.L569:
	mov	d0,d2
.L571:
	mov	d3,#0
.L572:
	j	.L96
.L97:
	mov	d2,#2
.L740:
	div	e0,d0,d2
.L741:
	jne	d0,#0,.L98
.L742:
	j	.L99
.L98:
	add	d3,#1
.L96:
	mov	d15,#16
.L743:
	jlt.u	d3,d15,.L97
.L99:
	mov	d15,#16
.L744:
	jge.u	d3,d15,.L100
.L387:
	add	d8,#-1
.L568:
	extr.u	d0,d8,#0,#16
.L393:
	ld.hu	d1,[a15]84
.L745:
	insert	d0,d1,d0,#0,#16
.L573:
	st.h	[a15]84,d0
.L394:
	mov	d0,#0
.L746:
	mov	d1,#1
.L400:
	mov	d2,#0
.L574:
	insert	d2,d2,d0,#6,#1
.L747:
	insert	d2,d2,d1,#14,#1
.L748:
	st.w	[a15]120,d2
.L401:
	and	d15,d3,#7
.L576:
	mov	d0,#1
.L406:
	mul	d1,d0,#8
.L578:
	mov	d0,#7
.L749:
	sh	d0,d0,d1
.L579:
	ld.w	d2,[a15]112
.L575:
	mov	d4,#-1
	xor	d0,d4
.L580:
	and	d2,d0
.L750:
	sh	d15,d15,d1
.L577:
	or	d2,d15
.L751:
	st.w	[a15]112,d2
.L407:
	and	d15,d3,#8
.L752:
	ne	d15,d15,#0
.L581:
	jeq	d15,#0,.L101
.L753:
	mov	d15,#1
.L413:
	mul	d15,d15,#8
.L582:
	add	d15,#3
.L754:
	mov	d0,#1
.L755:
	sh	d0,d0,d15
.L584:
	ld.w	d15,[a15]112
.L583:
	or	d15,d0
.L756:
	st.w	[a15]112,d15
.L101:
	mov	d15,#1
.L757:
	sh	d15,d15,d3
	utof	d15,d15
.L758:
	div.f	d2,d9,d15
.L388:
	j	.L102
.L100:
	mov	d2,#0
.L102:
	j	.L103
.L103:
	ret
.L380:
	
__IfxCcu6_setT13Frequency_function_end:
	.size	IfxCcu6_setT13Frequency,__IfxCcu6_setT13Frequency_function_end-IfxCcu6_setT13Frequency
.L194:
	; End of function
	
	.sdecl	'.text.IfxCcu6.IfxCcu6_setT13InputSignal',code,cluster('IfxCcu6_setT13InputSignal')
	.sect	'.text.IfxCcu6.IfxCcu6_setT13InputSignal'
	.align	2
	
	.global	IfxCcu6_setT13InputSignal
; Function IfxCcu6_setT13InputSignal
.L145:
IfxCcu6_setT13InputSignal:	.type	func
	ld.bu	d0,[a4]20
.L763:
	ld.bu	d15,[a5]12
.L764:
	ge.u	d15,d15,#4
.L765:
	insert	d15,d0,d15,#7,#1
	st.b	[a4]20,d15
.L766:
	ld.bu	d0,[a4]20
.L767:
	ld.bu	d15,[a5]12
.L768:
	insert	d15,d0,d15,#0,#2
	st.b	[a4]20,d15
.L769:
	ret
.L418:
	
__IfxCcu6_setT13InputSignal_function_end:
	.size	IfxCcu6_setT13InputSignal,__IfxCcu6_setT13InputSignal_function_end-IfxCcu6_setT13InputSignal
.L199:
	; End of function
	
	.calls	'IfxCcu6_setT12Frequency','__f_ftous'
	.calls	'IfxCcu6_setT13Frequency','__f_ftous'
	.calls	'IfxCcu6_disableModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxCcu6_disableModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxCcu6_disableModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxCcu6_enableModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxCcu6_enableModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxCcu6_enableModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxCcu6_getSrcAddress','IfxCcu6_getIndex'
	.calls	'IfxCcu6_resetModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxCcu6_resetModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxCcu6_resetModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxCcu6_setT12Frequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxCcu6_setT13Frequency','IfxScuCcu_getSpbFrequency'
	.calls	'IfxCcu6_connectTrigger','',8
	.calls	'IfxCcu6_disableModulationOutput','',0
	.calls	'IfxCcu6_disableModule','',0
	.calls	'IfxCcu6_enableModulationOutput','',0
	.calls	'IfxCcu6_enableModule','',0
	.calls	'IfxCcu6_getAddress','',0
	.calls	'IfxCcu6_getCaptureRegisterValue','',0
	.calls	'IfxCcu6_getCaptureShadowRegisterValue','',0
	.calls	'IfxCcu6_getIndex','',0
	.calls	'IfxCcu6_getSrcAddress','',0
	.calls	'IfxCcu6_readTimer','',0
	.calls	'IfxCcu6_resetModule','',0
	.calls	'IfxCcu6_routeInterruptNode','',0
	.calls	'IfxCcu6_setOutputPassiveLevel','',0
	.calls	'IfxCcu6_setT12CaptureCompareState','',0
	.calls	'IfxCcu6_setT12CompareValue','',0
	.calls	'IfxCcu6_setT12Frequency','',0
	.calls	'IfxCcu6_setT12InputSignal','',0
	.calls	'IfxCcu6_setT13CaptureCompareState','',0
	.calls	'IfxCcu6_setT13Frequency','',0
	.extern	IfxCcu6_cfg_indexMap
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	IfxScuCcu_getSpbFrequency
	.extern	__f_ftous
	.calls	'IfxCcu6_setT13InputSignal','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L147:
	.word	98344
	.half	3
	.word	.L148
	.byte	4
.L146:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L149
	.byte	2,1,1,3
	.word	232
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	235
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L306:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	280
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	292
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,9
	.byte	'void',0,10
	.word	378
	.byte	3
	.word	384
.L262:
	.byte	7
	.byte	'unsigned int',0,4,7
.L267:
	.byte	4
	.byte	'__ldmst_c',0,3,2,111,17,1,1
.L270:
	.byte	5
	.byte	'address',0,2,111,42
	.word	389
.L272:
	.byte	5
	.byte	'mask',0,2,111,60
	.word	394
.L274:
	.byte	5
	.byte	'value',0,2,111,75
	.word	394
.L276:
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	472
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	498
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	498
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	472
	.byte	6,0,7
	.byte	'int',0,4,5
.L287:
	.byte	7
	.byte	'unsigned char',0,1,8,11
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,12
	.byte	'SRPN',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'SRE',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'TOS',0,1
	.word	591
	.byte	2,3,2,35,1,12
	.byte	'reserved_13',0,1
	.word	591
	.byte	3,0,2,35,1,12
	.byte	'ECC',0,1
	.word	591
	.byte	6,2,2,35,2,12
	.byte	'reserved_22',0,1
	.word	591
	.byte	2,0,2,35,2,12
	.byte	'SRR',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'CLRR',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'SETR',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'IOV',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'IOVCLR',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'SWS',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'SWSCLR',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'reserved_31',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,4,70,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	608
	.byte	4,2,35,0,0,10
	.word	898
.L438:
	.byte	3
	.word	937
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	942
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,11
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,12
	.byte	'ENDINIT',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'LCK',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'PW',0,4
	.word	990
	.byte	14,16,2,35,0,12
	.byte	'REL',0,4
	.word	990
	.byte	16,0,2,35,0,0,13,6,247,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	1006
	.byte	4,2,35,0,0
.L264:
	.byte	7
	.byte	'unsigned short int',0,2,7,11
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'IR0',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'DR',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'IR1',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'UR',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PAR',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'TCR',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'TCTR',0,1
	.word	591
	.byte	7,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,6,255,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	1164
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,12
	.byte	'AE',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'OE',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'IS0',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'DS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'TO',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'IS1',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'US',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PAS',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'TCS',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'TCT',0,1
	.word	591
	.byte	7,0,2,35,1,12
	.byte	'TIM',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,6,135,15,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	1408
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,14
	.byte	'CON0',0
	.word	1102
	.byte	4,2,35,0,14
	.byte	'CON1',0
	.word	1368
	.byte	4,2,35,4,14
	.byte	'SR',0
	.word	1599
	.byte	4,2,35,8,0,10
	.word	1639
	.byte	3
	.word	1702
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,5,181,3,17,1,1,5
	.byte	'watchdog',0,5,181,3,65
	.word	1707
	.byte	5
	.byte	'password',0,5,181,3,82
	.word	1142
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,5,140,4,17,1,1,5
	.byte	'watchdog',0,5,140,4,63
	.word	1707
	.byte	5
	.byte	'password',0,5,140,4,80
	.word	1142
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,5,227,3,19
	.word	1142
	.byte	1,1,5
	.byte	'watchdog',0,5,227,3,74
	.word	1707
	.byte	6,0,15,8,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	1937
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	591
	.byte	1,1,6,0
.L291:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	2092
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	1142
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	591
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	1142
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	2092
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	2092
	.byte	17,6,0,0,11
	.byte	'_Ifx_P_OUT_Bits',0,10,143,3,16,4,12
	.byte	'P0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'P2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'P3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'P4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'P5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'P6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'P7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'P8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'P9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'P10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'P11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'P12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'P13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'P14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'P15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,181,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	2323
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMR_Bits',0,10,169,2,16,4,12
	.byte	'PS0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PS4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'PS8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'PS12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'PCL0',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'PCL4',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'PCL8',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'PCL12',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,10,133,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	2639
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_ID_Bits',0,10,110,16,4,12
	.byte	'MODREV',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,148,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3210
	.byte	4,2,35,0,0,18,4
	.word	591
	.byte	19,3,0,11
	.byte	'_Ifx_P_IOCR0_Bits',0,10,140,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'PC0',0,1
	.word	591
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'PC1',0,1
	.word	591
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	591
	.byte	3,5,2,35,2,12
	.byte	'PC2',0,1
	.word	591
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'PC3',0,1
	.word	591
	.byte	5,0,2,35,3,0,13,10,164,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3338
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IOCR4_Bits',0,10,166,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'PC4',0,1
	.word	591
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'PC5',0,1
	.word	591
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	591
	.byte	3,5,2,35,2,12
	.byte	'PC6',0,1
	.word	591
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'PC7',0,1
	.word	591
	.byte	5,0,2,35,3,0,13,10,180,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3553
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IOCR8_Bits',0,10,179,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'PC8',0,1
	.word	591
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'PC9',0,1
	.word	591
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	591
	.byte	3,5,2,35,2,12
	.byte	'PC10',0,1
	.word	591
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'PC11',0,1
	.word	591
	.byte	5,0,2,35,3,0,13,10,188,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3768
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IOCR12_Bits',0,10,153,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'PC12',0,1
	.word	591
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'PC13',0,1
	.word	591
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	591
	.byte	3,5,2,35,2,12
	.byte	'PC14',0,1
	.word	591
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'PC15',0,1
	.word	591
	.byte	5,0,2,35,3,0,13,10,172,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	3985
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_IN_Bits',0,10,118,16,4,12
	.byte	'P0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'P2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'P3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'P4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'P5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'P6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'P7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'P8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'P9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'P10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'P11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'P12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'P13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'P14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'P15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,156,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4205
	.byte	4,2,35,0,0,18,24
	.word	591
	.byte	19,23,0,11
	.byte	'_Ifx_P_PDR0_Bits',0,10,205,3,16,4,12
	.byte	'PD0',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'PL0',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PD1',0,1
	.word	591
	.byte	3,1,2,35,0,12
	.byte	'PL1',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'PD2',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'PL2',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'PD3',0,1
	.word	591
	.byte	3,1,2,35,1,12
	.byte	'PL3',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'PD4',0,1
	.word	591
	.byte	3,5,2,35,2,12
	.byte	'PL4',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'PD5',0,1
	.word	591
	.byte	3,1,2,35,2,12
	.byte	'PL5',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'PD6',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'PL6',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'PD7',0,1
	.word	591
	.byte	3,1,2,35,3,12
	.byte	'PL7',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,10,205,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4528
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_PDR1_Bits',0,10,226,3,16,4,12
	.byte	'PD8',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'PL8',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PD9',0,1
	.word	591
	.byte	3,1,2,35,0,12
	.byte	'PL9',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'PD10',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'PL10',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'PD11',0,1
	.word	591
	.byte	3,1,2,35,1,12
	.byte	'PL11',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'PD12',0,1
	.word	591
	.byte	3,5,2,35,2,12
	.byte	'PL12',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'PD13',0,1
	.word	591
	.byte	3,1,2,35,2,12
	.byte	'PL13',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'PD14',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'PL14',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'PD15',0,1
	.word	591
	.byte	3,1,2,35,3,12
	.byte	'PL15',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,10,213,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	4832
	.byte	4,2,35,0,0,18,8
	.word	591
	.byte	19,7,0,11
	.byte	'_Ifx_P_ESR_Bits',0,10,88,16,4,12
	.byte	'EN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,140,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5157
	.byte	4,2,35,0,0,18,12
	.word	591
	.byte	19,11,0,11
	.byte	'_Ifx_P_PDISC_Bits',0,10,183,3,16,4,12
	.byte	'PDIS0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PDIS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PDIS2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PDIS3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PDIS4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'PDIS5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PDIS6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PDIS7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'PDIS8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'PDIS9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'PDIS10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'PDIS11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'PDIS12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'PDIS13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'PDIS14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'PDIS15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,197,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5497
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_PCSR_Bits',0,10,165,3,16,4,12
	.byte	'SEL0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SEL1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'SEL2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SEL3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'SEL4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'SEL5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'SEL6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'SEL7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'SEL10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'SEL11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,4
	.word	394
	.byte	19,1,2,35,0,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,10,189,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	5863
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR0_Bits',0,10,206,2,16,4,12
	.byte	'PS0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,13,10,149,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6149
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR4_Bits',0,10,227,2,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'PS4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,13,10,165,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6296
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR8_Bits',0,10,238,2,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'PS8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,4
	.word	394
	.byte	20,0,2,35,0,0,13,10,173,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6465
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR12_Bits',0,10,216,2,16,4,12
	.byte	'reserved_0',0,2
	.word	1142
	.byte	12,4,2,35,0,12
	.byte	'PS12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,157,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6637
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR0_Bits',0,10,232,1,16,4,12
	.byte	'reserved_0',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'reserved_20',0,2
	.word	1142
	.byte	12,0,2,35,2,0,13,10,229,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6812
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR4_Bits',0,10,253,1,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	20,12,2,35,0,12
	.byte	'PCL4',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	8,0,2,35,3,0,13,10,245,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	6986
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR8_Bits',0,10,136,2,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	24,8,2,35,0,12
	.byte	'PCL8',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	591
	.byte	4,0,2,35,3,0,13,10,253,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	7160
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR12_Bits',0,10,243,1,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	28,4,2,35,0,12
	.byte	'PCL12',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,10,237,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	7336
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMSR_Bits',0,10,249,2,16,4,12
	.byte	'PS0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PS4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'PS8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'PS12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,141,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	7492
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_OMCR_Bits',0,10,147,2,16,4,12
	.byte	'reserved_0',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'PCL4',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'PCL8',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'PCL12',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,10,221,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	7825
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_LPCR0_Bits',0,10,192,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,13,10,196,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	8173
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_LPCR1_Bits',0,10,200,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,11
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,10,208,1,16,4,12
	.byte	'RDIS_CTRL',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'RX_DIS',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'TERM',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'LRXTERM',0,1
	.word	591
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,13,10,204,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	8297
	.byte	4,2,35,0,14
	.byte	'B_P21',0
	.word	8381
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_LPCR2_Bits',0,10,218,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'LVDSR',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'LVDSRL',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'reserved_10',0,1
	.word	591
	.byte	2,4,2,35,1,12
	.byte	'TDIS_CTRL',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'TX_DIS',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'TX_PD',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'TX_PWDPD',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,10,213,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	8561
	.byte	4,2,35,0,0,18,76
	.word	591
	.byte	19,75,0,11
	.byte	'_Ifx_P_ACCEN1_Bits',0,10,82,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,13,10,132,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	8814
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P_ACCEN0_Bits',0,10,45,16,4,12
	.byte	'EN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,10,252,3,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	8901
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_P',0,10,229,5,25,128,2,14
	.byte	'OUT',0
	.word	2599
	.byte	4,2,35,0,14
	.byte	'OMR',0
	.word	3170
	.byte	4,2,35,4,14
	.byte	'ID',0
	.word	3289
	.byte	4,2,35,8,14
	.byte	'reserved_C',0
	.word	3329
	.byte	4,2,35,12,14
	.byte	'IOCR0',0
	.word	3513
	.byte	4,2,35,16,14
	.byte	'IOCR4',0
	.word	3728
	.byte	4,2,35,20,14
	.byte	'IOCR8',0
	.word	3945
	.byte	4,2,35,24,14
	.byte	'IOCR12',0
	.word	4165
	.byte	4,2,35,28,14
	.byte	'reserved_20',0
	.word	3329
	.byte	4,2,35,32,14
	.byte	'IN',0
	.word	4479
	.byte	4,2,35,36,14
	.byte	'reserved_28',0
	.word	4519
	.byte	24,2,35,40,14
	.byte	'PDR0',0
	.word	4792
	.byte	4,2,35,64,14
	.byte	'PDR1',0
	.word	5108
	.byte	4,2,35,68,14
	.byte	'reserved_48',0
	.word	5148
	.byte	8,2,35,72,14
	.byte	'ESR',0
	.word	5448
	.byte	4,2,35,80,14
	.byte	'reserved_54',0
	.word	5488
	.byte	12,2,35,84,14
	.byte	'PDISC',0
	.word	5823
	.byte	4,2,35,96,14
	.byte	'PCSR',0
	.word	6109
	.byte	4,2,35,100,14
	.byte	'reserved_68',0
	.word	5148
	.byte	8,2,35,104,14
	.byte	'OMSR0',0
	.word	6256
	.byte	4,2,35,112,14
	.byte	'OMSR4',0
	.word	6425
	.byte	4,2,35,116,14
	.byte	'OMSR8',0
	.word	6597
	.byte	4,2,35,120,14
	.byte	'OMSR12',0
	.word	6772
	.byte	4,2,35,124,14
	.byte	'OMCR0',0
	.word	6946
	.byte	4,3,35,128,1,14
	.byte	'OMCR4',0
	.word	7120
	.byte	4,3,35,132,1,14
	.byte	'OMCR8',0
	.word	7296
	.byte	4,3,35,136,1,14
	.byte	'OMCR12',0
	.word	7452
	.byte	4,3,35,140,1,14
	.byte	'OMSR',0
	.word	7785
	.byte	4,3,35,144,1,14
	.byte	'OMCR',0
	.word	8133
	.byte	4,3,35,148,1,14
	.byte	'reserved_98',0
	.word	5148
	.byte	8,3,35,152,1,14
	.byte	'LPCR0',0
	.word	8257
	.byte	4,3,35,160,1,14
	.byte	'LPCR1',0
	.word	8506
	.byte	4,3,35,164,1,14
	.byte	'LPCR2',0
	.word	8765
	.byte	4,3,35,168,1,14
	.byte	'reserved_A4',0
	.word	8805
	.byte	76,3,35,172,1,14
	.byte	'ACCEN1',0
	.word	8861
	.byte	4,3,35,248,1,14
	.byte	'ACCEN0',0
	.word	9428
	.byte	4,3,35,252,1,0,10
	.word	9468
	.byte	3
	.word	10071
	.byte	15,9,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,9,196,4,17,1,1,5
	.byte	'port',0,9,196,4,48
	.word	10076
	.byte	5
	.byte	'pinIndex',0,9,196,4,60
	.word	591
	.byte	5
	.byte	'mode',0,9,196,4,88
	.word	10081
	.byte	6,0,15,9,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,9,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,9,202,4,17,1,1,5
	.byte	'port',0,9,202,4,49
	.word	10076
	.byte	5
	.byte	'pinIndex',0,9,202,4,61
	.word	591
	.byte	5
	.byte	'mode',0,9,202,4,90
	.word	10286
	.byte	5
	.byte	'index',0,9,202,4,114
	.word	10356
	.byte	6,0,15,9,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,9,208,4,17,1,1,5
	.byte	'port',0,9,208,4,44
	.word	10076
	.byte	5
	.byte	'pinIndex',0,9,208,4,56
	.word	591
	.byte	5
	.byte	'action',0,9,208,4,80
	.word	10669
	.byte	6,0,11
	.byte	'_Ifx_CCU6_CLC_Bits',0,12,144,1,16,4,12
	.byte	'DISR',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'DISS',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EDIS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,13,12,172,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	10850
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_MCFG_Bits',0,12,241,2,16,4,12
	.byte	'T12',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'T13',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'MCM',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,4
	.word	394
	.byte	29,0,2,35,0,0,13,12,164,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	11008
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_ID_Bits',0,12,193,1,16,4,12
	.byte	'MODREV',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'MODNUMBER',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,196,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	11142
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_MOSEL_Bits',0,12,171,3,16,4,12
	.byte	'TRIG0SEL',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'TRIG1SEL',0,1
	.word	591
	.byte	3,2,2,35,0,12
	.byte	'TRIG2SEL',0,2
	.word	1142
	.byte	3,7,2,35,0,12
	.byte	'reserved_9',0,4
	.word	394
	.byte	23,0,2,35,0,0,13,12,204,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	11269
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_PISEL0_Bits',0,12,193,3,16,4,12
	.byte	'ISCC60',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'ISCC61',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'ISCC62',0,1
	.word	591
	.byte	2,2,2,35,0,12
	.byte	'ISTRP',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'ISPOS0',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'ISPOS1',0,1
	.word	591
	.byte	2,4,2,35,1,12
	.byte	'ISPOS2',0,1
	.word	591
	.byte	2,2,2,35,1,12
	.byte	'IST12HR',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,220,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	11419
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_PISEL2_Bits',0,12,207,3,16,4,12
	.byte	'IST13HR',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'ISCNT12',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'ISCNT13',0,1
	.word	591
	.byte	2,2,2,35,0,12
	.byte	'T12EXT',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'T13EXT',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,13,12,228,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	11655
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_KSCSR_Bits',0,12,212,2,16,4,12
	.byte	'SB0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SB1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'SB2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SB3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,13,12,148,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	11839
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_T12_Bits',0,12,227,3,16,4,12
	.byte	'T12CV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,244,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	11989
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_T12PR_Bits',0,12,131,4,16,4,12
	.byte	'T12PV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,140,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12095
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_T12DTC_Bits',0,12,234,3,16,4,12
	.byte	'DTM',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'DTE0',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'DTE1',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'DTE2',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'DTR0',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'DTR1',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'DTR2',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'reserved_15',0,4
	.word	394
	.byte	17,0,2,35,0,0,13,12,252,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12203
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC60R_Bits',0,12,88,16,4,12
	.byte	'CCV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,236,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12429
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC61R_Bits',0,12,102,16,4,12
	.byte	'CCV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,252,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12534
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC62R_Bits',0,12,116,16,4,12
	.byte	'CCV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,140,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12639
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC60SR_Bits',0,12,95,16,4,12
	.byte	'CCS',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,244,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12744
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC61SR_Bits',0,12,109,16,4,12
	.byte	'CCS',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,132,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12850
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC62SR_Bits',0,12,123,16,4,12
	.byte	'CCS',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,148,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	12956
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_T13_Bits',0,12,138,4,16,4,12
	.byte	'T13CV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,148,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13062
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_T13PR_Bits',0,12,145,4,16,4,12
	.byte	'T13PV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,156,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13168
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC63R_Bits',0,12,130,1,16,4,12
	.byte	'CCV',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,156,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13276
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CC63SR_Bits',0,12,137,1,16,4,12
	.byte	'CCS',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,164,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13382
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CMPSTAT_Bits',0,12,171,1,16,4,12
	.byte	'CC60ST',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'CC61ST',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'CC62ST',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'CCPOS60',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'CCPOS61',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'CCPOS62',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'CC63ST',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'CC60PS',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'COUT60PS',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'CC61PS',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'COUT61PS',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'CC62PS',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'COUT62PS',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'COUT63PS',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'T13IM',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,188,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13489
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_CMPMODIF_Bits',0,12,154,1,16,4,12
	.byte	'MCC60S',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'MCC61S',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'MCC62S',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	591
	.byte	3,2,2,35,0,12
	.byte	'MCC63S',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'MCC60R',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'MCC61R',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'MCC62R',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	591
	.byte	3,2,2,35,1,12
	.byte	'MCC63R',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'reserved_15',0,4
	.word	394
	.byte	17,0,2,35,0,0,13,12,180,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	13884
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_T12MSEL_Bits',0,12,248,3,16,4,12
	.byte	'MSEL60',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'MSEL61',0,1
	.word	591
	.byte	4,0,2,35,0,12
	.byte	'MSEL62',0,1
	.word	591
	.byte	4,4,2,35,1,12
	.byte	'HSYNC',0,1
	.word	591
	.byte	3,1,2,35,1,12
	.byte	'DBYP',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,132,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14189
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_TCTR0_Bits',0,12,152,4,16,4,12
	.byte	'T12CLK',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'T12PRE',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'T12R',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'STE12',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'CDIR',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'CTM',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'T13CLK',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'T13PRE',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'T13R',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'STE13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'reserved_14',0,4
	.word	394
	.byte	18,0,2,35,0,0,13,12,164,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14369
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_TCTR2_Bits',0,12,168,4,16,4,12
	.byte	'T12SSC',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'T13SSC',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'T13TEC',0,1
	.word	591
	.byte	3,3,2,35,0,12
	.byte	'T13TED',0,1
	.word	591
	.byte	2,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'T12RSEL',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'T13RSEL',0,1
	.word	591
	.byte	2,4,2,35,1,12
	.byte	'reserved_12',0,4
	.word	394
	.byte	20,0,2,35,0,0,13,12,172,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14629
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_TCTR4_Bits',0,12,181,4,16,4,12
	.byte	'T12RR',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'T12RS',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'T12RES',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'DTRES',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'T12CNT',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'T12STR',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'T12STD',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'T13RR',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'T13RS',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'T13RES',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	591
	.byte	2,3,2,35,1,12
	.byte	'T13CNT',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'T13STR',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'T13STD',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0
.L340:
	.byte	13,12,180,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	14852
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_MODCTR_Bits',0,12,159,3,16,4,12
	.byte	'T12MODEN',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'MCMEN',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'T13MODEN',0,1
	.word	591
	.byte	6,2,2,35,1,12
	.byte	'reserved_14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'ECT13O',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,196,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15217
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_TRPCTR_Bits',0,12,202,4,16,4,12
	.byte	'TRPM0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'TRPM1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'TRPM2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	591
	.byte	5,0,2,35,0,12
	.byte	'TRPEN',0,1
	.word	591
	.byte	6,2,2,35,1,12
	.byte	'TRPEN13',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'TRPPEN',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,188,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15429
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_PSLR_Bits',0,12,218,3,16,4,12
	.byte	'PSL',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PSL63',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,13,12,236,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15648
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_MCMOUTS_Bits',0,12,146,3,16,4,12
	.byte	'MCMPS',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'STRMCM',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EXPHS',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'CURHS',0,1
	.word	591
	.byte	3,2,2,35,1,12
	.byte	'reserved_14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'STRHP',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,188,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	15791
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_MCMOUT_Bits',0,12,135,3,16,4,12
	.byte	'MCMP',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'R',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EXPH',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'CURH',0,1
	.word	591
	.byte	3,2,2,35,1,12
	.byte	'reserved_14',0,4
	.word	394
	.byte	18,0,2,35,0,0,13,12,180,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16015
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_MCMCTR_Bits',0,12,250,2,16,4,12
	.byte	'SWSEL',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'SWSYN',0,1
	.word	591
	.byte	2,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'STE12U',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'STE12D',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'STE13U',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	394
	.byte	21,0,2,35,0,0,13,12,172,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16190
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_IMON_Bits',0,12,223,1,16,4,12
	.byte	'LBE',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'CCPOS0I',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'CCPOS1I',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'CCPOS2I',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'CC60INI',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'CC61INI',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'CC62INI',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'CTRAPI',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'T12HRI',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'T13HRI',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'reserved_10',0,4
	.word	394
	.byte	22,0,2,35,0,0,13,12,212,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16414
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_LI_Bits',0,12,222,2,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'CCPOS0EN',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'CCPOS1EN',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'CCPOS2EN',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'CC60INEN',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'CC61INEN',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'CC62INEN',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'CTRAPEN',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'T12HREN',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'T13HREN',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'reserved_10',0,1
	.word	591
	.byte	3,3,2,35,1,12
	.byte	'LBEEN',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'INPLBE',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,156,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	16687
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_IS_Bits',0,12,252,1,16,4,12
	.byte	'ICC60R',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ICC60F',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'ICC61R',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'ICC61F',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'ICC62R',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'ICC62F',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'T12OM',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'T12PM',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'T13CM',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'T13PM',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'TRPF',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'TRPS',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'CHE',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'WHE',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'IDLE',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'STR',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,228,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17032
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_ISS_Bits',0,12,168,2,16,4,12
	.byte	'SCC60R',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SCC60F',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'SCC61R',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SCC61F',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'SCC62R',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'SCC62F',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'ST12OM',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'ST12PM',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'ST13CM',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'ST13PM',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'STRPF',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'SWHC',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'SCHE',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'SWHE',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'SIDLE',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'SSTR',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,244,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17389
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_ISR_Bits',0,12,146,2,16,4,12
	.byte	'RCC60R',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'RCC60F',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'RCC61R',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'RCC61F',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'RCC62R',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'RCC62F',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'RT12OM',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'RT12PM',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'RT13CM',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'RT13PM',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'RTRPF',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'RCHE',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'RWHE',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'RIDLE',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'RSTR',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,236,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	17756
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_INP_Bits',0,12,239,1,16,4,12
	.byte	'INPCC60',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'INPCC61',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'INPCC62',0,1
	.word	591
	.byte	2,2,2,35,0,12
	.byte	'INPCHE',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'INPERR',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'INPT12',0,1
	.word	591
	.byte	2,4,2,35,1,12
	.byte	'INPT13',0,1
	.word	591
	.byte	2,2,2,35,1,12
	.byte	'reserved_14',0,4
	.word	394
	.byte	18,0,2,35,0,0,13,12,220,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18130
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_IEN_Bits',0,12,201,1,16,4,12
	.byte	'ENCC60R',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ENCC60F',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'ENCC61R',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'ENCC61F',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'ENCC62R',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'ENCC62F',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'ENT12OM',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'ENT12PM',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'ENT13CM',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'ENT13PM',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'ENTRPF',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'ENCHE',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'ENWHE',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'ENIDLE',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'ENSTR',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,13,12,204,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18348
	.byte	4,2,35,0,0,18,52
	.word	591
	.byte	19,51,0,11
	.byte	'_Ifx_CCU6_OCS_Bits',0,12,180,3,16,4,12
	.byte	'TGS',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'TGB',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'TG_P',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	20,8,2,35,0,12
	.byte	'SUS',0,1
	.word	591
	.byte	4,4,2,35,3,12
	.byte	'SUS_P',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'SUSSTA',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	591
	.byte	2,0,2,35,3,0,13,12,212,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18746
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_KRSTCLR_Bits',0,12,205,2,16,4,12
	.byte	'CLR',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	394
	.byte	31,0,2,35,0,0,13,12,140,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	18953
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_KRST1_Bits',0,12,198,2,16,4,12
	.byte	'RST',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	394
	.byte	31,0,2,35,0,0,13,12,132,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19060
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_KRST0_Bits',0,12,190,2,16,4,12
	.byte	'RST',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'RSTSTAT',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,13,12,252,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19165
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_ACCEN1_Bits',0,12,82,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,13,12,228,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19289
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6_ACCEN0_Bits',0,12,45,16,4,12
	.byte	'EN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	591
	.byte	1,0,2,35,3,0,13,12,220,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	19379
	.byte	4,2,35,0,0,11
	.byte	'_Ifx_CCU6',0,12,204,7,25,128,2,14
	.byte	'CLC',0
	.word	10968
	.byte	4,2,35,0,14
	.byte	'MCFG',0
	.word	11102
	.byte	4,2,35,4,14
	.byte	'ID',0
	.word	11229
	.byte	4,2,35,8,14
	.byte	'MOSEL',0
	.word	11379
	.byte	4,2,35,12,14
	.byte	'PISEL0',0
	.word	11615
	.byte	4,2,35,16,14
	.byte	'PISEL2',0
	.word	11799
	.byte	4,2,35,20,14
	.byte	'reserved_18',0
	.word	3329
	.byte	4,2,35,24,14
	.byte	'KSCSR',0
	.word	11949
	.byte	4,2,35,28,14
	.byte	'T12',0
	.word	12055
	.byte	4,2,35,32,14
	.byte	'T12PR',0
	.word	12163
	.byte	4,2,35,36,14
	.byte	'T12DTC',0
	.word	12389
	.byte	4,2,35,40,14
	.byte	'reserved_2C',0
	.word	3329
	.byte	4,2,35,44,14
	.byte	'CC60R',0
	.word	12494
	.byte	4,2,35,48,14
	.byte	'CC61R',0
	.word	12599
	.byte	4,2,35,52,14
	.byte	'CC62R',0
	.word	12704
	.byte	4,2,35,56,14
	.byte	'reserved_3C',0
	.word	3329
	.byte	4,2,35,60,14
	.byte	'CC60SR',0
	.word	12810
	.byte	4,2,35,64,14
	.byte	'CC61SR',0
	.word	12916
	.byte	4,2,35,68,14
	.byte	'CC62SR',0
	.word	13022
	.byte	4,2,35,72,14
	.byte	'reserved_4C',0
	.word	3329
	.byte	4,2,35,76,14
	.byte	'T13',0
	.word	13128
	.byte	4,2,35,80,14
	.byte	'T13PR',0
	.word	13236
	.byte	4,2,35,84,14
	.byte	'CC63R',0
	.word	13342
	.byte	4,2,35,88,14
	.byte	'CC63SR',0
	.word	13449
	.byte	4,2,35,92,14
	.byte	'CMPSTAT',0
	.word	13844
	.byte	4,2,35,96,14
	.byte	'CMPMODIF',0
	.word	14149
	.byte	4,2,35,100,14
	.byte	'T12MSEL',0
	.word	14329
	.byte	4,2,35,104,14
	.byte	'reserved_6C',0
	.word	3329
	.byte	4,2,35,108,14
	.byte	'TCTR0',0
	.word	14589
	.byte	4,2,35,112,14
	.byte	'TCTR2',0
	.word	14812
	.byte	4,2,35,116,14
	.byte	'TCTR4',0
	.word	15177
	.byte	4,2,35,120,14
	.byte	'reserved_7C',0
	.word	3329
	.byte	4,2,35,124,14
	.byte	'MODCTR',0
	.word	15389
	.byte	4,3,35,128,1,14
	.byte	'TRPCTR',0
	.word	15608
	.byte	4,3,35,132,1,14
	.byte	'PSLR',0
	.word	15751
	.byte	4,3,35,136,1,14
	.byte	'MCMOUTS',0
	.word	15975
	.byte	4,3,35,140,1,14
	.byte	'MCMOUT',0
	.word	16150
	.byte	4,3,35,144,1,14
	.byte	'MCMCTR',0
	.word	16374
	.byte	4,3,35,148,1,14
	.byte	'IMON',0
	.word	16647
	.byte	4,3,35,152,1,14
	.byte	'LI',0
	.word	16992
	.byte	4,3,35,156,1,14
	.byte	'IS',0
	.word	17349
	.byte	4,3,35,160,1,14
	.byte	'ISS',0
	.word	17716
	.byte	4,3,35,164,1,14
	.byte	'ISR',0
	.word	18090
	.byte	4,3,35,168,1,14
	.byte	'INP',0
	.word	18308
	.byte	4,3,35,172,1,14
	.byte	'IEN',0
	.word	18697
	.byte	4,3,35,176,1,14
	.byte	'reserved_B4',0
	.word	18737
	.byte	52,3,35,180,1,14
	.byte	'OCS',0
	.word	18913
	.byte	4,3,35,232,1,14
	.byte	'KRSTCLR',0
	.word	19020
	.byte	4,3,35,236,1,14
	.byte	'KRST1',0
	.word	19125
	.byte	4,3,35,240,1,14
	.byte	'KRST0',0
	.word	19249
	.byte	4,3,35,244,1,14
	.byte	'ACCEN1',0
	.word	19339
	.byte	4,3,35,248,1,14
	.byte	'ACCEN0',0
	.word	19909
	.byte	4,3,35,252,1,0,10
	.word	19949
.L256:
	.byte	3
	.word	20805
.L459:
	.byte	8
	.byte	'IfxCcu6_isModuleEnabled',0,3,11,253,14,20
	.word	591
	.byte	1,1
.L460:
	.byte	5
	.byte	'ccu6',0,11,253,14,54
	.word	20810
.L462:
	.byte	6,0
.L447:
	.byte	15,11,209,2,9,1,16
	.byte	'IfxCcu6_TimerId_t12',0,0,16
	.byte	'IfxCcu6_TimerId_t13',0,1,0
.L320:
	.byte	15,11,218,2,9,1,16
	.byte	'IfxCcu6_TimerInputClock_fcc6',0,0,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By2',0,1,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By4',0,2,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By8',0,3,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By16',0,4,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By32',0,5,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By64',0,6,16
	.byte	'IfxCcu6_TimerInputClock_fcc6By128',0,7,0
.L342:
	.byte	4
	.byte	'IfxCcu6_setInputClockFrequency',0,3,11,211,15,17,1,1
.L345:
	.byte	5
	.byte	'ccu6',0,11,211,15,58
	.word	20810
.L347:
	.byte	5
	.byte	'timer',0,11,211,15,80
	.word	20867
.L349:
	.byte	5
	.byte	'frequency',0,11,211,15,111
	.word	20918
.L351:
	.byte	6,0
.L311:
	.byte	15,11,172,2,9,1,16
	.byte	'IfxCcu6_T12CountMode_edgeAligned',0,0,16
	.byte	'IfxCcu6_T12CountMode_centerAligned',0,1,0
.L363:
	.byte	4
	.byte	'IfxCcu6_setT12CountMode',0,3,11,162,16,17,1,1
.L365:
	.byte	5
	.byte	'ccu6',0,11,162,16,51
	.word	20810
.L367:
	.byte	5
	.byte	'mode',0,11,162,16,78
	.word	21288
.L369:
	.byte	6,0
.L322:
	.byte	4
	.byte	'IfxCcu6_setT12PeriodValue',0,3,11,174,16,17,1,1
.L325:
	.byte	5
	.byte	'ccu6',0,11,174,16,53
	.word	20810
.L327:
	.byte	5
	.byte	'value',0,11,174,16,66
	.word	1142
.L329:
	.byte	6,0
.L392:
	.byte	4
	.byte	'IfxCcu6_setT13PeriodValue',0,3,11,192,16,17,1,1
.L395:
	.byte	5
	.byte	'ccu6',0,11,192,16,53
	.word	20810
.L397:
	.byte	5
	.byte	'value',0,11,192,16,66
	.word	1142
.L399:
	.byte	6,0
.L354:
	.byte	4
	.byte	'IfxCcu6_enableAdditionalPrescaler',0,3,11,245,11,17,1,1
.L356:
	.byte	5
	.byte	'ccu6',0,11,245,11,61
	.word	20810
.L358:
	.byte	5
	.byte	'timer',0,11,245,11,83
	.word	20867
.L360:
	.byte	6,0
.L330:
	.byte	4
	.byte	'IfxCcu6_enableShadowTransfer',0,3,11,198,12,17,1,1
.L333:
	.byte	5
	.byte	'ccu6',0,11,198,12,56
	.word	20810
.L335:
	.byte	5
	.byte	't12',0,11,198,12,70
	.word	591
.L337:
	.byte	5
	.byte	't13',0,11,198,12,83
	.word	591
.L339:
	.byte	6,0,15,11,81,9,1,16
	.byte	'IfxCcu6_CaptureCompareInput_cC60',0,0,16
	.byte	'IfxCcu6_CaptureCompareInput_cC61',0,2,16
	.byte	'IfxCcu6_CaptureCompareInput_cC62',0,4,16
	.byte	'IfxCcu6_CaptureCompareInput_cTRAP',0,6,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS0',0,8,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS1',0,10,16
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS2',0,12,0,15,11,94,9,1,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_a',0,0,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_b',0,1,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_c',0,2,16
	.byte	'IfxCcu6_CaptureCompareInputSignal_d',0,3,0,4
	.byte	'IfxCcu6_setCaptureCompareInputSignal',0,3,11,161,15,17,1,1,5
	.byte	'ccu6',0,11,161,15,64
	.word	20810
	.byte	5
	.byte	'input',0,11,161,15,98
	.word	21711
	.byte	5
	.byte	'signal',0,11,161,15,139,1
	.word	21969
	.byte	6,0,20
	.byte	'__debug',0,1,1,1,1,21
	.word	240
	.byte	22
	.word	266
	.byte	6,0,21
	.word	301
	.byte	22
	.word	333
	.byte	6,0,21
	.word	346
	.byte	6,0,21
	.word	410
	.byte	22
	.word	427
	.byte	22
	.word	443
	.byte	22
	.word	456
	.byte	6,0,21
	.word	503
	.byte	22
	.word	522
	.byte	6,0,21
	.word	538
	.byte	22
	.word	553
	.byte	22
	.word	567
	.byte	6,0,21
	.word	947
	.byte	22
	.word	975
	.byte	6,0,21
	.word	1712
	.byte	22
	.word	1752
	.byte	22
	.word	1770
	.byte	6,0,21
	.word	1790
	.byte	22
	.word	1828
	.byte	22
	.word	1846
	.byte	6,0,23
	.byte	'IfxScuWdt_clearCpuEndinit',0,5,217,1,17,1,1,1,1,5
	.byte	'password',0,5,217,1,50
	.word	1142
	.byte	0,23
	.byte	'IfxScuWdt_setCpuEndinit',0,5,239,1,17,1,1,1,1,5
	.byte	'password',0,5,239,1,48
	.word	1142
	.byte	0,21
	.word	1866
	.byte	22
	.word	1917
	.byte	6,0,24
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,5,129,3,19
	.word	1142
	.byte	1,1,1,1,21
	.word	2016
	.byte	6,0,21
	.word	2050
	.byte	6,0,21
	.word	2113
	.byte	22
	.word	2154
	.byte	6,0,21
	.word	2173
	.byte	22
	.word	2228
	.byte	6,0,21
	.word	2247
	.byte	22
	.word	2287
	.byte	22
	.word	2304
	.byte	17,6,0,0,21
	.word	10206
	.byte	22
	.word	10238
	.byte	22
	.word	10252
	.byte	22
	.word	10270
	.byte	6,0,21
	.word	10573
	.byte	22
	.word	10606
	.byte	22
	.word	10620
	.byte	22
	.word	10638
	.byte	22
	.word	10652
	.byte	6,0,21
	.word	10772
	.byte	22
	.word	10800
	.byte	22
	.word	10814
	.byte	22
	.word	10832
	.byte	6,0,24
	.byte	'IfxScuCcu_getSpbFrequency',0,13,179,7,20
	.word	292
	.byte	1,1,1,1,21
	.word	20815
	.byte	22
	.word	20851
	.byte	6,0,21
	.word	21199
	.byte	22
	.word	21238
	.byte	22
	.word	21252
	.byte	22
	.word	21267
	.byte	6,0,21
	.word	21367
	.byte	22
	.word	21399
	.byte	22
	.word	21413
	.byte	6,0,21
	.word	21429
	.byte	22
	.word	21463
	.byte	22
	.word	21477
	.byte	6,0,21
	.word	21494
	.byte	22
	.word	21528
	.byte	22
	.word	21542
	.byte	6,0
.L258:
	.byte	15,14,90,9,1,16
	.byte	'IfxCcu6_TrigOut_0',0,0,16
	.byte	'IfxCcu6_TrigOut_1',0,3,16
	.byte	'IfxCcu6_TrigOut_2',0,6,0
.L260:
	.byte	15,14,97,9,1,16
	.byte	'IfxCcu6_TrigSel_cout63',0,0,16
	.byte	'IfxCcu6_TrigSel_cc60',0,1,16
	.byte	'IfxCcu6_TrigSel_cc61',0,1,16
	.byte	'IfxCcu6_TrigSel_cc62',0,1,16
	.byte	'IfxCcu6_TrigSel_sr1',0,2,16
	.byte	'IfxCcu6_TrigSel_sr3',0,3,0
.L279:
	.byte	15,11,182,1,9,1,16
	.byte	'IfxCcu6_InterruptSource_cc60RisingEdge',0,0,16
	.byte	'IfxCcu6_InterruptSource_cc60FallingEdge',0,1,16
	.byte	'IfxCcu6_InterruptSource_cc61RisingEdge',0,2,16
	.byte	'IfxCcu6_InterruptSource_cc61FallingEdge',0,3,16
	.byte	'IfxCcu6_InterruptSource_cc62RisingEdge',0,4,16
	.byte	'IfxCcu6_InterruptSource_cc62FallingEdge',0,5,16
	.byte	'IfxCcu6_InterruptSource_t12OneMatch',0,6,16
	.byte	'IfxCcu6_InterruptSource_t12PeriodMatch',0,7,16
	.byte	'IfxCcu6_InterruptSource_t13CompareMatch',0,8,16
	.byte	'IfxCcu6_InterruptSource_t13PeriodMatch',0,9,16
	.byte	'IfxCcu6_InterruptSource_trap',0,10,16
	.byte	'IfxCcu6_InterruptSource_correctHallEvent',0,12,16
	.byte	'IfxCcu6_InterruptSource_wrongHallEvent',0,13,0
.L281:
	.byte	15,11,233,1,9,1,16
	.byte	'IfxCcu6_ServiceRequest_0',0,0,16
	.byte	'IfxCcu6_ServiceRequest_1',0,1,16
	.byte	'IfxCcu6_ServiceRequest_2',0,2,16
	.byte	'IfxCcu6_ServiceRequest_3',0,3,0
.L285:
	.byte	15,11,113,9,1,16
	.byte	'IfxCcu6_ChannelOut_cc0',0,0,16
	.byte	'IfxCcu6_ChannelOut_cout0',0,1,16
	.byte	'IfxCcu6_ChannelOut_cc1',0,2,16
	.byte	'IfxCcu6_ChannelOut_cout1',0,3,16
	.byte	'IfxCcu6_ChannelOut_cc2',0,4,16
	.byte	'IfxCcu6_ChannelOut_cout2',0,5,16
	.byte	'IfxCcu6_ChannelOut_cout3',0,6,0
.L296:
	.byte	15,11,133,2,9,1,16
	.byte	'IfxCcu6_T12Channel_0',0,0,16
	.byte	'IfxCcu6_T12Channel_1',0,1,16
	.byte	'IfxCcu6_T12Channel_2',0,2,0
.L298:
	.byte	15,11,104,9,1,16
	.byte	'IfxCcu6_CaptureCompareState_set',0,0,16
	.byte	'IfxCcu6_CaptureCompareState_clear',0,1,16
	.byte	'IfxCcu6_CaptureCompareState_toggle',0,2,0,25,9,190,1,9,8,14
	.byte	'port',0
	.word	10076
	.byte	4,2,35,0,14
	.byte	'pinIndex',0
	.word	591
	.byte	1,2,35,4,0,15,16,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,25,15,115,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	24171
.L372:
	.byte	3
	.word	24222
	.byte	25,15,123,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	24232
.L420:
	.byte	3
	.word	24283
.L423:
	.byte	15,14,83,9,1,16
	.byte	'IfxCcu6_Index_none',0,127,16
	.byte	'IfxCcu6_Index_0',0,0,16
	.byte	'IfxCcu6_Index_1',0,1,0
.L442:
	.byte	7
	.byte	'long int',0,4,5,21
	.word	21559
	.byte	22
	.word	21601
	.byte	22
	.word	21615
	.byte	6,0,21
	.word	21632
	.byte	22
	.word	21669
	.byte	22
	.word	21683
	.byte	22
	.word	21696
	.byte	6,0,21
	.word	22127
	.byte	22
	.word	22172
	.byte	22
	.word	22186
	.byte	22
	.word	22201
	.byte	6,0,7
	.byte	'short int',0,2,5,27
	.byte	'__wchar_t',0,17,1,1
	.word	24429
	.byte	27
	.byte	'__size_t',0,17,1,1
	.word	394
	.byte	27
	.byte	'__ptrdiff_t',0,17,1,1
	.word	584
	.byte	28,1,3
	.word	24497
	.byte	27
	.byte	'__codeptr',0,17,1,1
	.word	24499
	.byte	27
	.byte	'boolean',0,18,101,29
	.word	591
	.byte	27
	.byte	'uint8',0,18,105,29
	.word	591
	.byte	27
	.byte	'uint16',0,18,109,29
	.word	1142
	.byte	27
	.byte	'uint32',0,18,113,29
	.word	2092
	.byte	27
	.byte	'uint64',0,18,118,29
	.word	472
	.byte	27
	.byte	'sint16',0,18,126,29
	.word	24429
	.byte	27
	.byte	'sint32',0,18,131,1,29
	.word	24356
	.byte	7
	.byte	'long long int',0,8,5,27
	.byte	'sint64',0,18,138,1,29
	.word	24628
	.byte	27
	.byte	'float32',0,18,167,1,29
	.word	292
	.byte	27
	.byte	'pvoid',0,16,57,28
	.word	498
	.byte	27
	.byte	'Ifx_TickTime',0,16,79,28
	.word	24628
	.byte	27
	.byte	'Ifx_Priority',0,16,103,16
	.word	1142
	.byte	27
	.byte	'Ifx_TimerValue',0,16,104,16
	.word	2092
	.byte	27
	.byte	'Ifx_RxSel',0,16,140,1,3
	.word	24052
	.byte	25,16,143,1,9,8,14
	.byte	'module',0
	.word	389
	.byte	4,2,35,0,14
	.byte	'index',0
	.word	24356
	.byte	4,2,35,4,0,27
	.byte	'IfxModule_IndexMap',0,16,147,1,3
	.word	24776
	.byte	27
	.byte	'Ifx_CCU6_ACCEN0_Bits',0,12,79,3
	.word	19379
	.byte	27
	.byte	'Ifx_CCU6_ACCEN1_Bits',0,12,85,3
	.word	19289
	.byte	27
	.byte	'Ifx_CCU6_CC60R_Bits',0,12,92,3
	.word	12429
	.byte	27
	.byte	'Ifx_CCU6_CC60SR_Bits',0,12,99,3
	.word	12744
	.byte	27
	.byte	'Ifx_CCU6_CC61R_Bits',0,12,106,3
	.word	12534
	.byte	27
	.byte	'Ifx_CCU6_CC61SR_Bits',0,12,113,3
	.word	12850
	.byte	27
	.byte	'Ifx_CCU6_CC62R_Bits',0,12,120,3
	.word	12639
	.byte	27
	.byte	'Ifx_CCU6_CC62SR_Bits',0,12,127,3
	.word	12956
	.byte	27
	.byte	'Ifx_CCU6_CC63R_Bits',0,12,134,1,3
	.word	13276
	.byte	27
	.byte	'Ifx_CCU6_CC63SR_Bits',0,12,141,1,3
	.word	13382
	.byte	27
	.byte	'Ifx_CCU6_CLC_Bits',0,12,151,1,3
	.word	10850
	.byte	27
	.byte	'Ifx_CCU6_CMPMODIF_Bits',0,12,168,1,3
	.word	13884
	.byte	27
	.byte	'Ifx_CCU6_CMPSTAT_Bits',0,12,190,1,3
	.word	13489
	.byte	27
	.byte	'Ifx_CCU6_ID_Bits',0,12,198,1,3
	.word	11142
	.byte	27
	.byte	'Ifx_CCU6_IEN_Bits',0,12,220,1,3
	.word	18348
	.byte	27
	.byte	'Ifx_CCU6_IMON_Bits',0,12,236,1,3
	.word	16414
	.byte	27
	.byte	'Ifx_CCU6_INP_Bits',0,12,249,1,3
	.word	18130
	.byte	27
	.byte	'Ifx_CCU6_IS_Bits',0,12,143,2,3
	.word	17032
	.byte	27
	.byte	'Ifx_CCU6_ISR_Bits',0,12,165,2,3
	.word	17756
	.byte	27
	.byte	'Ifx_CCU6_ISS_Bits',0,12,187,2,3
	.word	17389
	.byte	27
	.byte	'Ifx_CCU6_KRST0_Bits',0,12,195,2,3
	.word	19165
	.byte	27
	.byte	'Ifx_CCU6_KRST1_Bits',0,12,202,2,3
	.word	19060
	.byte	27
	.byte	'Ifx_CCU6_KRSTCLR_Bits',0,12,209,2,3
	.word	18953
	.byte	27
	.byte	'Ifx_CCU6_KSCSR_Bits',0,12,219,2,3
	.word	11839
	.byte	27
	.byte	'Ifx_CCU6_LI_Bits',0,12,238,2,3
	.word	16687
	.byte	27
	.byte	'Ifx_CCU6_MCFG_Bits',0,12,247,2,3
	.word	11008
	.byte	27
	.byte	'Ifx_CCU6_MCMCTR_Bits',0,12,132,3,3
	.word	16190
	.byte	27
	.byte	'Ifx_CCU6_MCMOUT_Bits',0,12,143,3,3
	.word	16015
	.byte	27
	.byte	'Ifx_CCU6_MCMOUTS_Bits',0,12,156,3,3
	.word	15791
	.byte	27
	.byte	'Ifx_CCU6_MODCTR_Bits',0,12,168,3,3
	.word	15217
	.byte	27
	.byte	'Ifx_CCU6_MOSEL_Bits',0,12,177,3,3
	.word	11269
	.byte	27
	.byte	'Ifx_CCU6_OCS_Bits',0,12,190,3,3
	.word	18746
	.byte	27
	.byte	'Ifx_CCU6_PISEL0_Bits',0,12,204,3,3
	.word	11419
	.byte	27
	.byte	'Ifx_CCU6_PISEL2_Bits',0,12,215,3,3
	.word	11655
	.byte	27
	.byte	'Ifx_CCU6_PSLR_Bits',0,12,224,3,3
	.word	15648
	.byte	27
	.byte	'Ifx_CCU6_T12_Bits',0,12,231,3,3
	.word	11989
	.byte	27
	.byte	'Ifx_CCU6_T12DTC_Bits',0,12,245,3,3
	.word	12203
	.byte	27
	.byte	'Ifx_CCU6_T12MSEL_Bits',0,12,128,4,3
	.word	14189
	.byte	27
	.byte	'Ifx_CCU6_T12PR_Bits',0,12,135,4,3
	.word	12095
	.byte	27
	.byte	'Ifx_CCU6_T13_Bits',0,12,142,4,3
	.word	13062
	.byte	27
	.byte	'Ifx_CCU6_T13PR_Bits',0,12,149,4,3
	.word	13168
	.byte	27
	.byte	'Ifx_CCU6_TCTR0_Bits',0,12,165,4,3
	.word	14369
	.byte	27
	.byte	'Ifx_CCU6_TCTR2_Bits',0,12,178,4,3
	.word	14629
	.byte	27
	.byte	'Ifx_CCU6_TCTR4_Bits',0,12,199,4,3
	.word	14852
	.byte	27
	.byte	'Ifx_CCU6_TRPCTR_Bits',0,12,212,4,3
	.word	15429
	.byte	27
	.byte	'Ifx_CCU6_ACCEN0',0,12,225,4,3
	.word	19909
	.byte	27
	.byte	'Ifx_CCU6_ACCEN1',0,12,233,4,3
	.word	19339
	.byte	27
	.byte	'Ifx_CCU6_CC60R',0,12,241,4,3
	.word	12494
	.byte	27
	.byte	'Ifx_CCU6_CC60SR',0,12,249,4,3
	.word	12810
	.byte	27
	.byte	'Ifx_CCU6_CC61R',0,12,129,5,3
	.word	12599
	.byte	27
	.byte	'Ifx_CCU6_CC61SR',0,12,137,5,3
	.word	12916
	.byte	27
	.byte	'Ifx_CCU6_CC62R',0,12,145,5,3
	.word	12704
	.byte	27
	.byte	'Ifx_CCU6_CC62SR',0,12,153,5,3
	.word	13022
	.byte	27
	.byte	'Ifx_CCU6_CC63R',0,12,161,5,3
	.word	13342
	.byte	27
	.byte	'Ifx_CCU6_CC63SR',0,12,169,5,3
	.word	13449
	.byte	27
	.byte	'Ifx_CCU6_CLC',0,12,177,5,3
	.word	10968
	.byte	27
	.byte	'Ifx_CCU6_CMPMODIF',0,12,185,5,3
	.word	14149
	.byte	27
	.byte	'Ifx_CCU6_CMPSTAT',0,12,193,5,3
	.word	13844
	.byte	27
	.byte	'Ifx_CCU6_ID',0,12,201,5,3
	.word	11229
	.byte	27
	.byte	'Ifx_CCU6_IEN',0,12,209,5,3
	.word	18697
	.byte	27
	.byte	'Ifx_CCU6_IMON',0,12,217,5,3
	.word	16647
	.byte	27
	.byte	'Ifx_CCU6_INP',0,12,225,5,3
	.word	18308
	.byte	27
	.byte	'Ifx_CCU6_IS',0,12,233,5,3
	.word	17349
	.byte	27
	.byte	'Ifx_CCU6_ISR',0,12,241,5,3
	.word	18090
	.byte	27
	.byte	'Ifx_CCU6_ISS',0,12,249,5,3
	.word	17716
	.byte	27
	.byte	'Ifx_CCU6_KRST0',0,12,129,6,3
	.word	19249
	.byte	27
	.byte	'Ifx_CCU6_KRST1',0,12,137,6,3
	.word	19125
	.byte	27
	.byte	'Ifx_CCU6_KRSTCLR',0,12,145,6,3
	.word	19020
	.byte	27
	.byte	'Ifx_CCU6_KSCSR',0,12,153,6,3
	.word	11949
	.byte	27
	.byte	'Ifx_CCU6_LI',0,12,161,6,3
	.word	16992
	.byte	27
	.byte	'Ifx_CCU6_MCFG',0,12,169,6,3
	.word	11102
	.byte	27
	.byte	'Ifx_CCU6_MCMCTR',0,12,177,6,3
	.word	16374
	.byte	27
	.byte	'Ifx_CCU6_MCMOUT',0,12,185,6,3
	.word	16150
	.byte	27
	.byte	'Ifx_CCU6_MCMOUTS',0,12,193,6,3
	.word	15975
	.byte	27
	.byte	'Ifx_CCU6_MODCTR',0,12,201,6,3
	.word	15389
	.byte	27
	.byte	'Ifx_CCU6_MOSEL',0,12,209,6,3
	.word	11379
	.byte	27
	.byte	'Ifx_CCU6_OCS',0,12,217,6,3
	.word	18913
	.byte	27
	.byte	'Ifx_CCU6_PISEL0',0,12,225,6,3
	.word	11615
	.byte	27
	.byte	'Ifx_CCU6_PISEL2',0,12,233,6,3
	.word	11799
	.byte	27
	.byte	'Ifx_CCU6_PSLR',0,12,241,6,3
	.word	15751
	.byte	27
	.byte	'Ifx_CCU6_T12',0,12,249,6,3
	.word	12055
	.byte	27
	.byte	'Ifx_CCU6_T12DTC',0,12,129,7,3
	.word	12389
	.byte	27
	.byte	'Ifx_CCU6_T12MSEL',0,12,137,7,3
	.word	14329
	.byte	27
	.byte	'Ifx_CCU6_T12PR',0,12,145,7,3
	.word	12163
	.byte	27
	.byte	'Ifx_CCU6_T13',0,12,153,7,3
	.word	13128
	.byte	27
	.byte	'Ifx_CCU6_T13PR',0,12,161,7,3
	.word	13236
	.byte	27
	.byte	'Ifx_CCU6_TCTR0',0,12,169,7,3
	.word	14589
	.byte	27
	.byte	'Ifx_CCU6_TCTR2',0,12,177,7,3
	.word	14812
	.byte	27
	.byte	'Ifx_CCU6_TCTR4',0,12,185,7,3
	.word	15177
	.byte	27
	.byte	'Ifx_CCU6_TRPCTR',0,12,193,7,3
	.word	15608
	.byte	10
	.word	19949
	.byte	27
	.byte	'Ifx_CCU6',0,12,130,8,3
	.word	27211
	.byte	27
	.byte	'IfxCcu6_Index',0,14,88,3
	.word	24293
	.byte	27
	.byte	'IfxCcu6_TrigOut',0,14,95,3
	.word	22777
	.byte	27
	.byte	'IfxCcu6_TrigSel',0,14,105,3
	.word	22843
	.byte	18,16
	.word	24776
	.byte	19,1,0,26
	.word	27304
	.byte	29
	.byte	'IfxCcu6_cfg_indexMap',0,14,111,41
	.word	27313
	.byte	1,1,15,19,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,27
	.byte	'IfxSrc_Tos',0,19,74,3
	.word	27349
	.byte	27
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	608
	.byte	27
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	898
	.byte	11
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	27474
	.byte	27
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	27506
	.byte	11
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,14
	.byte	'TX',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'RX',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'ERR',0
	.word	898
	.byte	4,2,35,8,0,10
	.word	27532
	.byte	27
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	27591
	.byte	11
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,14
	.byte	'SBSRC',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	27619
	.byte	27
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	27656
	.byte	18,64
	.word	898
	.byte	19,15,0,11
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,14
	.byte	'INT',0
	.word	27684
	.byte	64,2,35,0,0,10
	.word	27693
	.byte	27
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	27725
	.byte	11
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,14
	.byte	'SR0',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'SR1',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'SR2',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'SR3',0
	.word	898
	.byte	4,2,35,12,0,10
	.word	27750
	.byte	27
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	27822
	.byte	18,8
	.word	898
	.byte	19,1,0,11
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,14
	.byte	'SR',0
	.word	27848
	.byte	8,2,35,0,0,10
	.word	27857
	.byte	27
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	27893
	.byte	11
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,14
	.byte	'MI',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'MIEP',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'ISP',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'MJPEG',0
	.word	898
	.byte	4,2,35,12,0,10
	.word	27923
	.byte	27
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	27996
	.byte	11
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,14
	.byte	'SBSRC',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	28022
	.byte	27
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	28057
	.byte	18,192,1
	.word	898
	.byte	19,47,0,11
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,14
	.byte	'ERR',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'reserved_4',0
	.word	5488
	.byte	12,2,35,4,14
	.byte	'CH',0
	.word	28083
	.byte	192,1,2,35,16,0,10
	.word	28093
	.byte	27
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	28160
	.byte	11
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,14
	.byte	'SRM',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'SRA',0
	.word	898
	.byte	4,2,35,4,0,10
	.word	28186
	.byte	27
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	28234
	.byte	11
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	28262
	.byte	27
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	28295
	.byte	18,40
	.word	591
	.byte	19,39,0,11
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,14
	.byte	'INT',0
	.word	27848
	.byte	8,2,35,0,14
	.byte	'TINT',0
	.word	27848
	.byte	8,2,35,8,14
	.byte	'NDAT',0
	.word	27848
	.byte	8,2,35,16,14
	.byte	'MBSC',0
	.word	27848
	.byte	8,2,35,24,14
	.byte	'OBUSY',0
	.word	898
	.byte	4,2,35,32,14
	.byte	'IBUSY',0
	.word	898
	.byte	4,2,35,36,14
	.byte	'reserved_28',0
	.word	28322
	.byte	40,2,35,40,0,10
	.word	28331
	.byte	27
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	28458
	.byte	11
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	28485
	.byte	27
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	28517
	.byte	11
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	28543
	.byte	27
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	28575
	.byte	11
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,14
	.byte	'DONE',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'ERR',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'RFS',0
	.word	898
	.byte	4,2,35,8,0,10
	.word	28601
	.byte	27
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	28661
	.byte	18,16
	.word	591
	.byte	19,15,0,11
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,14
	.byte	'SR0',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'SR1',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'SR2',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'SR3',0
	.word	898
	.byte	4,2,35,12,14
	.byte	'reserved_10',0
	.word	28687
	.byte	16,2,35,16,0,10
	.word	28696
	.byte	27
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	28790
	.byte	11
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,14
	.byte	'CIRQ',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'T2',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'T3',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'T4',0
	.word	898
	.byte	4,2,35,12,14
	.byte	'T5',0
	.word	898
	.byte	4,2,35,16,14
	.byte	'T6',0
	.word	898
	.byte	4,2,35,20,14
	.byte	'reserved_18',0
	.word	4519
	.byte	24,2,35,24,0,10
	.word	28817
	.byte	27
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	28934
	.byte	18,12
	.word	898
	.byte	19,2,0,18,32
	.word	898
	.byte	19,7,0,18,32
	.word	28971
	.byte	19,0,0,18,88
	.word	591
	.byte	19,87,0,18,108
	.word	898
	.byte	19,26,0,18,96
	.word	591
	.byte	19,95,0,18,96
	.word	28971
	.byte	19,2,0,18,160,3
	.word	591
	.byte	19,159,3,0,18,64
	.word	28971
	.byte	19,1,0,18,192,3
	.word	591
	.byte	19,191,3,0,18,16
	.word	898
	.byte	19,3,0,18,64
	.word	29056
	.byte	19,3,0,18,192,2
	.word	591
	.byte	19,191,2,0,11
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,14
	.byte	'AEIIRQ',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'ARUIRQ',0
	.word	28962
	.byte	12,2,35,4,14
	.byte	'reserved_10',0
	.word	3329
	.byte	4,2,35,16,14
	.byte	'BRCIRQ',0
	.word	898
	.byte	4,2,35,20,14
	.byte	'CMPIRQ',0
	.word	898
	.byte	4,2,35,24,14
	.byte	'SPEIRQ',0
	.word	27848
	.byte	8,2,35,28,14
	.byte	'reserved_24',0
	.word	5148
	.byte	8,2,35,36,14
	.byte	'PSM',0
	.word	28980
	.byte	32,2,35,44,14
	.byte	'reserved_4C',0
	.word	28989
	.byte	88,2,35,76,14
	.byte	'DPLL',0
	.word	28998
	.byte	108,3,35,164,1,14
	.byte	'reserved_110',0
	.word	29007
	.byte	96,3,35,144,2,14
	.byte	'ERR',0
	.word	898
	.byte	4,3,35,240,2,14
	.byte	'reserved_174',0
	.word	5488
	.byte	12,3,35,244,2,14
	.byte	'TIM',0
	.word	29016
	.byte	96,3,35,128,3,14
	.byte	'reserved_1E0',0
	.word	29025
	.byte	160,3,3,35,224,3,14
	.byte	'MCS',0
	.word	29016
	.byte	96,3,35,128,7,14
	.byte	'reserved_3E0',0
	.word	29025
	.byte	160,3,3,35,224,7,14
	.byte	'TOM',0
	.word	29036
	.byte	64,3,35,128,11,14
	.byte	'reserved_5C0',0
	.word	29045
	.byte	192,3,3,35,192,11,14
	.byte	'ATOM',0
	.word	29065
	.byte	64,3,35,128,15,14
	.byte	'reserved_7C0',0
	.word	29074
	.byte	192,2,3,35,192,15,14
	.byte	'MCSW0',0
	.word	28962
	.byte	12,3,35,128,18,14
	.byte	'reserved_90C',0
	.word	18737
	.byte	52,3,35,140,18,14
	.byte	'MCSW1',0
	.word	28962
	.byte	12,3,35,192,18,0,10
	.word	29085
	.byte	27
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	29545
	.byte	11
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	29571
	.byte	27
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	29604
	.byte	11
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,14
	.byte	'COK',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'RDI',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'ERR',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'TRG',0
	.word	898
	.byte	4,2,35,12,0,10
	.word	29631
	.byte	27
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	29704
	.byte	18,56
	.word	591
	.byte	19,55,0,11
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,14
	.byte	'BREQ',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'LBREQ',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'SREQ',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'LSREQ',0
	.word	898
	.byte	4,2,35,12,14
	.byte	'ERR',0
	.word	898
	.byte	4,2,35,16,14
	.byte	'P',0
	.word	898
	.byte	4,2,35,20,14
	.byte	'reserved_18',0
	.word	29731
	.byte	56,2,35,24,0,10
	.word	29740
	.byte	27
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	29863
	.byte	11
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	29889
	.byte	27
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	29921
	.byte	11
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,14
	.byte	'SR0',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'SR1',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'SR2',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'SR3',0
	.word	898
	.byte	4,2,35,12,14
	.byte	'SR4',0
	.word	898
	.byte	4,2,35,16,0,10
	.word	29947
	.byte	27
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	30032
	.byte	11
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	30058
	.byte	27
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	30090
	.byte	11
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,14
	.byte	'SR',0
	.word	28971
	.byte	32,2,35,0,0,10
	.word	30116
	.byte	27
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	30149
	.byte	11
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,14
	.byte	'SR',0
	.word	28971
	.byte	32,2,35,0,0,10
	.word	30176
	.byte	27
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	30210
	.byte	11
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,14
	.byte	'TX',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'RX',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'ERR',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'PT',0
	.word	898
	.byte	4,2,35,12,14
	.byte	'HC',0
	.word	898
	.byte	4,2,35,16,14
	.byte	'U',0
	.word	898
	.byte	4,2,35,20,0,10
	.word	30238
	.byte	27
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	30331
	.byte	11
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,14
	.byte	'SR',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	30358
	.byte	27
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	30390
	.byte	11
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,14
	.byte	'DTS',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'ERU',0
	.word	29056
	.byte	16,2,35,4,0,10
	.word	30416
	.byte	27
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	30462
	.byte	18,24
	.word	898
	.byte	19,5,0,11
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,14
	.byte	'SR',0
	.word	30488
	.byte	24,2,35,0,0,10
	.word	30497
	.byte	27
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	30530
	.byte	11
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,14
	.byte	'SR',0
	.word	28962
	.byte	12,2,35,0,0,10
	.word	30557
	.byte	27
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	30589
	.byte	11
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,14
	.byte	'SR0',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'SR1',0
	.word	898
	.byte	4,2,35,4,0,10
	.word	30615
	.byte	27
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	30661
	.byte	11
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,14
	.byte	'SR0',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'SR1',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'SR2',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'SR3',0
	.word	898
	.byte	4,2,35,12,0,10
	.word	30687
	.byte	27
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	30762
	.byte	11
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,14
	.byte	'SR0',0
	.word	898
	.byte	4,2,35,0,14
	.byte	'SR1',0
	.word	898
	.byte	4,2,35,4,14
	.byte	'SR2',0
	.word	898
	.byte	4,2,35,8,14
	.byte	'SR3',0
	.word	898
	.byte	4,2,35,12,0,10
	.word	30791
	.byte	27
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	30865
	.byte	11
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,14
	.byte	'SRC',0
	.word	898
	.byte	4,2,35,0,0,10
	.word	30893
	.byte	27
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	30927
	.byte	18,4
	.word	27474
	.byte	19,0,0,10
	.word	30954
	.byte	11
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,14
	.byte	'AGBT',0
	.word	30963
	.byte	4,2,35,0,0,10
	.word	30968
	.byte	27
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	31004
	.byte	18,48
	.word	27532
	.byte	19,3,0,10
	.word	31032
	.byte	11
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,14
	.byte	'ASCLIN',0
	.word	31041
	.byte	48,2,35,0,0,10
	.word	31046
	.byte	27
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	31086
	.byte	10
	.word	27619
	.byte	11
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,14
	.byte	'SPB',0
	.word	31116
	.byte	4,2,35,0,0,10
	.word	31121
	.byte	27
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	31155
	.byte	18,64
	.word	27693
	.byte	19,0,0,10
	.word	31182
	.byte	11
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,14
	.byte	'CAN',0
	.word	31191
	.byte	64,2,35,0,0,10
	.word	31196
	.byte	27
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	31230
	.byte	18,32
	.word	27750
	.byte	19,1,0,10
	.word	31257
	.byte	11
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,14
	.byte	'CCU6',0
	.word	31266
	.byte	32,2,35,0,0,10
	.word	31271
	.byte	27
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	31307
	.byte	10
	.word	27857
	.byte	11
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,14
	.byte	'CERBERUS',0
	.word	31335
	.byte	8,2,35,0,0,10
	.word	31340
	.byte	27
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	31384
	.byte	18,16
	.word	27923
	.byte	19,0,0,10
	.word	31416
	.byte	11
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,14
	.byte	'CIF',0
	.word	31425
	.byte	16,2,35,0,0,10
	.word	31430
	.byte	27
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	31464
	.byte	18,8
	.word	28022
	.byte	19,1,0,10
	.word	31491
	.byte	11
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,14
	.byte	'CPU',0
	.word	31500
	.byte	8,2,35,0,0,10
	.word	31505
	.byte	27
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	31539
	.byte	18,208,1
	.word	28093
	.byte	19,0,0,10
	.word	31566
	.byte	11
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,14
	.byte	'DMA',0
	.word	31576
	.byte	208,1,2,35,0,0,10
	.word	31581
	.byte	27
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	31617
	.byte	10
	.word	28186
	.byte	10
	.word	28186
	.byte	10
	.word	28186
	.byte	11
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,14
	.byte	'DSADC0',0
	.word	31644
	.byte	8,2,35,0,14
	.byte	'reserved_8',0
	.word	5148
	.byte	8,2,35,8,14
	.byte	'DSADC2',0
	.word	31649
	.byte	8,2,35,16,14
	.byte	'DSADC3',0
	.word	31654
	.byte	8,2,35,24,0,10
	.word	31659
	.byte	27
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	31750
	.byte	18,4
	.word	28262
	.byte	19,0,0,10
	.word	31779
	.byte	11
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,14
	.byte	'EMEM',0
	.word	31788
	.byte	4,2,35,0,0,10
	.word	31793
	.byte	27
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	31829
	.byte	18,80
	.word	28331
	.byte	19,0,0,10
	.word	31857
	.byte	11
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,14
	.byte	'ERAY',0
	.word	31866
	.byte	80,2,35,0,0,10
	.word	31871
	.byte	27
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	31907
	.byte	18,4
	.word	28485
	.byte	19,0,0,10
	.word	31935
	.byte	11
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,14
	.byte	'ETH',0
	.word	31944
	.byte	4,2,35,0,0,10
	.word	31949
	.byte	27
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	31983
	.byte	18,4
	.word	28543
	.byte	19,0,0,10
	.word	32010
	.byte	11
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,14
	.byte	'FCE',0
	.word	32019
	.byte	4,2,35,0,0,10
	.word	32024
	.byte	27
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	32058
	.byte	18,12
	.word	28601
	.byte	19,0,0,10
	.word	32085
	.byte	11
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,14
	.byte	'FFT',0
	.word	32094
	.byte	12,2,35,0,0,10
	.word	32099
	.byte	27
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	32133
	.byte	18,64
	.word	28696
	.byte	19,1,0,10
	.word	32160
	.byte	11
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,14
	.byte	'GPSR',0
	.word	32169
	.byte	64,2,35,0,0,10
	.word	32174
	.byte	27
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	32210
	.byte	18,48
	.word	28817
	.byte	19,0,0,10
	.word	32238
	.byte	11
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,14
	.byte	'GPT12',0
	.word	32247
	.byte	48,2,35,0,0,10
	.word	32252
	.byte	27
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	32290
	.byte	18,204,18
	.word	29085
	.byte	19,0,0,10
	.word	32319
	.byte	11
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,14
	.byte	'GTM',0
	.word	32329
	.byte	204,18,2,35,0,0,10
	.word	32334
	.byte	27
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	32370
	.byte	18,4
	.word	29571
	.byte	19,0,0,10
	.word	32397
	.byte	11
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,14
	.byte	'HSCT',0
	.word	32406
	.byte	4,2,35,0,0,10
	.word	32411
	.byte	27
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	32447
	.byte	18,64
	.word	29631
	.byte	19,3,0,10
	.word	32475
	.byte	11
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,14
	.byte	'HSSL',0
	.word	32484
	.byte	64,2,35,0,14
	.byte	'EXI',0
	.word	898
	.byte	4,2,35,64,0,10
	.word	32489
	.byte	27
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	32538
	.byte	18,80
	.word	29740
	.byte	19,0,0,10
	.word	32566
	.byte	11
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,14
	.byte	'I2C',0
	.word	32575
	.byte	80,2,35,0,0,10
	.word	32580
	.byte	27
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	32614
	.byte	18,4
	.word	29889
	.byte	19,0,0,10
	.word	32641
	.byte	11
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,14
	.byte	'LMU',0
	.word	32650
	.byte	4,2,35,0,0,10
	.word	32655
	.byte	27
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	32689
	.byte	18,40
	.word	29947
	.byte	19,1,0,10
	.word	32716
	.byte	11
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,14
	.byte	'MSC',0
	.word	32725
	.byte	40,2,35,0,0,10
	.word	32730
	.byte	27
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	32764
	.byte	18,8
	.word	30058
	.byte	19,1,0,10
	.word	32791
	.byte	11
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,14
	.byte	'PMU',0
	.word	32800
	.byte	8,2,35,0,0,10
	.word	32805
	.byte	27
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	32839
	.byte	18,32
	.word	30116
	.byte	19,0,0,10
	.word	32866
	.byte	11
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,14
	.byte	'PSI5',0
	.word	32875
	.byte	32,2,35,0,0,10
	.word	32880
	.byte	27
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	32916
	.byte	18,32
	.word	30176
	.byte	19,0,0,10
	.word	32944
	.byte	11
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,14
	.byte	'PSI5S',0
	.word	32953
	.byte	32,2,35,0,0,10
	.word	32958
	.byte	27
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	32996
	.byte	18,96
	.word	30238
	.byte	19,3,0,10
	.word	33025
	.byte	11
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,14
	.byte	'QSPI',0
	.word	33034
	.byte	96,2,35,0,0,10
	.word	33039
	.byte	27
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	33075
	.byte	18,4
	.word	30358
	.byte	19,0,0,10
	.word	33103
	.byte	11
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,14
	.byte	'SCR',0
	.word	33112
	.byte	4,2,35,0,0,10
	.word	33117
	.byte	27
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	33151
	.byte	10
	.word	30416
	.byte	11
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,14
	.byte	'SCU',0
	.word	33178
	.byte	20,2,35,0,0,10
	.word	33183
	.byte	27
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	33217
	.byte	18,24
	.word	30497
	.byte	19,0,0,10
	.word	33244
	.byte	11
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,14
	.byte	'SENT',0
	.word	33253
	.byte	24,2,35,0,0,10
	.word	33258
	.byte	27
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	33294
	.byte	18,12
	.word	30557
	.byte	19,0,0,10
	.word	33322
	.byte	11
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,14
	.byte	'SMU',0
	.word	33331
	.byte	12,2,35,0,0,10
	.word	33336
	.byte	27
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	33370
	.byte	18,16
	.word	30615
	.byte	19,1,0,10
	.word	33397
	.byte	11
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,14
	.byte	'STM',0
	.word	33406
	.byte	16,2,35,0,0,10
	.word	33411
	.byte	27
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	33445
	.byte	18,64
	.word	30791
	.byte	19,3,0,10
	.word	33472
	.byte	18,224,1
	.word	591
	.byte	19,223,1,0,18,32
	.word	30687
	.byte	19,1,0,10
	.word	33497
	.byte	11
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,14
	.byte	'G',0
	.word	33481
	.byte	64,2,35,0,14
	.byte	'reserved_40',0
	.word	33486
	.byte	224,1,2,35,64,14
	.byte	'CG',0
	.word	33506
	.byte	32,3,35,160,2,0,10
	.word	33511
	.byte	27
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	33580
	.byte	10
	.word	30893
	.byte	11
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,14
	.byte	'XBAR',0
	.word	33608
	.byte	4,2,35,0,0,10
	.word	33613
	.byte	27
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	33649
	.byte	15,20,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,27
	.byte	'IfxScu_CCUCON0_CLKSEL',0,20,240,10,3
	.word	33677
	.byte	15,20,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,27
	.byte	'IfxScu_WDTCON1_IR',0,20,255,10,3
	.word	33774
	.byte	11
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,12
	.byte	'EN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	33896
	.byte	11
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	34453
	.byte	11
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,12
	.byte	'STM0DIS',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'STM1DIS',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'STM2DIS',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,4
	.word	394
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	34530
	.byte	11
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,12
	.byte	'BAUD1DIV',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'BAUD2DIV',0,1
	.word	591
	.byte	4,0,2,35,0,12
	.byte	'SRIDIV',0,1
	.word	591
	.byte	4,4,2,35,1,12
	.byte	'LPDIV',0,1
	.word	591
	.byte	4,0,2,35,1,12
	.byte	'SPBDIV',0,1
	.word	591
	.byte	4,4,2,35,2,12
	.byte	'FSI2DIV',0,1
	.word	591
	.byte	2,2,2,35,2,12
	.byte	'reserved_22',0,1
	.word	591
	.byte	2,0,2,35,2,12
	.byte	'FSIDIV',0,1
	.word	591
	.byte	2,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	591
	.byte	2,4,2,35,3,12
	.byte	'CLKSEL',0,1
	.word	591
	.byte	2,2,2,35,3,12
	.byte	'UP',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	34666
	.byte	11
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,12
	.byte	'CANDIV',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'ERAYDIV',0,1
	.word	591
	.byte	4,0,2,35,0,12
	.byte	'STMDIV',0,1
	.word	591
	.byte	4,4,2,35,1,12
	.byte	'GTMDIV',0,1
	.word	591
	.byte	4,0,2,35,1,12
	.byte	'ETHDIV',0,1
	.word	591
	.byte	4,4,2,35,2,12
	.byte	'ASCLINFDIV',0,1
	.word	591
	.byte	4,0,2,35,2,12
	.byte	'ASCLINSDIV',0,1
	.word	591
	.byte	4,4,2,35,3,12
	.byte	'INSEL',0,1
	.word	591
	.byte	2,2,2,35,3,12
	.byte	'UP',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	34946
	.byte	11
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,12
	.byte	'BBBDIV',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	26,2,2,35,0,12
	.byte	'UP',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	35184
	.byte	11
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,12
	.byte	'PLLDIV',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'PLLSEL',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'PLLERAYDIV',0,1
	.word	591
	.byte	6,2,2,35,1,12
	.byte	'PLLERAYSEL',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'SRIDIV',0,1
	.word	591
	.byte	6,2,2,35,2,12
	.byte	'SRISEL',0,1
	.word	591
	.byte	2,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	5,3,2,35,3,12
	.byte	'SLCK',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'UP',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	35312
	.byte	11
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,12
	.byte	'SPBDIV',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'SPBSEL',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'GTMDIV',0,1
	.word	591
	.byte	6,2,2,35,1,12
	.byte	'GTMSEL',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'STMDIV',0,1
	.word	591
	.byte	6,2,2,35,2,12
	.byte	'STMSEL',0,1
	.word	591
	.byte	2,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	5,3,2,35,3,12
	.byte	'SLCK',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'UP',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	35555
	.byte	11
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,12
	.byte	'MAXDIV',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	26,2,2,35,0,12
	.byte	'UP',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	35790
	.byte	11
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,12
	.byte	'CPU0DIV',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	394
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	35918
	.byte	11
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,12
	.byte	'CPU1DIV',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	394
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	36018
	.byte	11
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,12
	.byte	'CHREV',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'CHTEC',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'CHID',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'EEA',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'UCODE',0,1
	.word	591
	.byte	7,0,2,35,2,12
	.byte	'FSIZE',0,1
	.word	591
	.byte	4,4,2,35,3,12
	.byte	'SP',0,1
	.word	591
	.byte	2,2,2,35,3,12
	.byte	'SEC',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'reserved_31',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	36118
	.byte	11
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,12
	.byte	'PWD',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'START',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'CAL',0,4
	.word	394
	.byte	20,8,2,35,0,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'SLCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	36326
	.byte	11
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,12
	.byte	'LOWER',0,2
	.word	1142
	.byte	10,6,2,35,0,12
	.byte	'reserved_10',0,1
	.word	591
	.byte	5,1,2,35,1,12
	.byte	'LLU',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'UPPER',0,2
	.word	1142
	.byte	10,6,2,35,2,12
	.byte	'reserved_26',0,1
	.word	591
	.byte	4,2,2,35,3,12
	.byte	'SLCK',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'UOF',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	36491
	.byte	11
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,12
	.byte	'RESULT',0,2
	.word	1142
	.byte	10,6,2,35,0,12
	.byte	'reserved_10',0,1
	.word	591
	.byte	4,2,2,35,1,12
	.byte	'RDY',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'BUSY',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	36674
	.byte	11
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'EXIS0',0,1
	.word	591
	.byte	3,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'FEN0',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'REN0',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'LDEN0',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'EIEN0',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'INP0',0,1
	.word	591
	.byte	3,1,2,35,1,12
	.byte	'reserved_15',0,4
	.word	394
	.byte	5,12,2,35,0,12
	.byte	'EXIS1',0,1
	.word	591
	.byte	3,1,2,35,2,12
	.byte	'reserved_23',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'FEN1',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'REN1',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'LDEN1',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'EIEN1',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'INP1',0,1
	.word	591
	.byte	3,1,2,35,3,12
	.byte	'reserved_31',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	36828
	.byte	11
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,12
	.byte	'INTF0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'INTF1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'INTF2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'INTF3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'INTF4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'INTF5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'INTF6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'INTF7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	37192
	.byte	11
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,12
	.byte	'POL',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'MODE',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'ENON',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PSEL',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,2
	.word	1142
	.byte	12,0,2,35,0,12
	.byte	'EMSF',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'SEMSF',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,1
	.word	591
	.byte	6,0,2,35,2,12
	.byte	'EMSFM',0,1
	.word	591
	.byte	2,6,2,35,3,12
	.byte	'SEMSFM',0,1
	.word	591
	.byte	2,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	591
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	37403
	.byte	11
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	7,1,2,35,0,12
	.byte	'EDCON',0,2
	.word	1142
	.byte	2,7,2,35,0,12
	.byte	'reserved_9',0,4
	.word	394
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	37655
	.byte	11
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,12
	.byte	'ARI',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ARC',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	37773
	.byte	11
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	28,4,2,35,0,12
	.byte	'EVR13OFF',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'BPEVR13OFF',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	37884
	.byte	11
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	28,4,2,35,0,12
	.byte	'EVR33OFF',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'BPEVR33OFF',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	38047
	.byte	11
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,12
	.byte	'ADC13V',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'ADC33V',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'ADCSWDV',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'VAL',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	38210
	.byte	11
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,12
	.byte	'DVS13TRIM',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'DVS33TRIM',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'VAL',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	38368
	.byte	11
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,12
	.byte	'EVR13OVMOD',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'EVR13UVMOD',0,1
	.word	591
	.byte	2,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'EVR33OVMOD',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'reserved_10',0,1
	.word	591
	.byte	2,4,2,35,1,12
	.byte	'EVR33UVMOD',0,1
	.word	591
	.byte	2,2,2,35,1,12
	.byte	'reserved_14',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'SWDOVMOD',0,1
	.word	591
	.byte	2,6,2,35,2,12
	.byte	'reserved_18',0,1
	.word	591
	.byte	2,4,2,35,2,12
	.byte	'SWDUVMOD',0,1
	.word	591
	.byte	2,2,2,35,2,12
	.byte	'reserved_22',0,2
	.word	1142
	.byte	10,0,2,35,2,0,27
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	38533
	.byte	11
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,12
	.byte	'OSCTRIM',0,2
	.word	1142
	.byte	10,6,2,35,0,12
	.byte	'OSCPTAT',0,1
	.word	591
	.byte	6,0,2,35,1,12
	.byte	'OSCANASEL',0,1
	.word	591
	.byte	4,4,2,35,2,12
	.byte	'HPBGTRIM',0,2
	.word	1142
	.byte	7,5,2,35,2,12
	.byte	'HPBGCLKEN',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'OSC3V3',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'reserved_29',0,1
	.word	591
	.byte	2,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	38862
	.byte	11
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,12
	.byte	'EVR13OVVAL',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'EVR33OVVAL',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'SWDOVVAL',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	39083
	.byte	11
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,12
	.byte	'RST13TRIM',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	16,8,2,35,0,12
	.byte	'RST13OFF',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'BPRST13OFF',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'RST33OFF',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'BPRST33OFF',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'RSTSWDOFF',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'BPRSTSWDOFF',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	39246
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,12
	.byte	'SD5P',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'SD5I',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'SD5D',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	39518
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,12
	.byte	'SD33P',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'SD33I',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'SD33D',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	39671
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,12
	.byte	'CT5REG0',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'CT5REG1',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'CT5REG2',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	39827
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,12
	.byte	'CT5REG3',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'CT5REG4',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	15,1,2,35,2,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	39989
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,12
	.byte	'CT33REG0',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'CT33REG1',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'CT33REG2',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	40132
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,12
	.byte	'CT33REG3',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'CT33REG4',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	15,1,2,35,2,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	40297
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,12
	.byte	'SDFREQSPRD',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'SDFREQ',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'SDSTEP',0,1
	.word	591
	.byte	4,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	591
	.byte	2,2,2,35,3,12
	.byte	'SDSAMPLE',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	40442
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,12
	.byte	'DRVP',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'SDMINMAXDC',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'DRVN',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'SDLUT',0,1
	.word	591
	.byte	6,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	40623
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,12
	.byte	'SDPWMPRE',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'SDPID',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'SDVOKLVL',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	40797
	.byte	11
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'SYNCDIV',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	394
	.byte	20,1,2,35,0,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	40957
	.byte	11
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,12
	.byte	'EVR13',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'OV13',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EVR33',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'OV33',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'OVSWD',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'UV13',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'UV33',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'UVSWD',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EXTPASS13',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'EXTPASS33',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'BGPROK',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	394
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	41101
	.byte	11
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,12
	.byte	'EVR13TRIM',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'SDVOUTSEL',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	15,1,2,35,2,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	41375
	.byte	11
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,12
	.byte	'EVR13UVVAL',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'EVR33UVVAL',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'SWDUVVAL',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	41514
	.byte	11
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,12
	.byte	'EN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'SEL0',0,1
	.word	591
	.byte	4,2,2,35,0,12
	.byte	'reserved_6',0,2
	.word	1142
	.byte	10,0,2,35,0,12
	.byte	'EN1',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'NSEL',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'SEL1',0,1
	.word	591
	.byte	4,2,2,35,2,12
	.byte	'reserved_22',0,1
	.word	591
	.byte	2,0,2,35,2,12
	.byte	'DIV1',0,1
	.word	591
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	41677
	.byte	11
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,12
	.byte	'STEP',0,2
	.word	1142
	.byte	10,6,2,35,0,12
	.byte	'reserved_10',0,1
	.word	591
	.byte	4,2,2,35,1,12
	.byte	'DM',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'RESULT',0,2
	.word	1142
	.byte	10,6,2,35,2,12
	.byte	'reserved_26',0,1
	.word	591
	.byte	5,1,2,35,3,12
	.byte	'DISCLK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	41895
	.byte	11
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,12
	.byte	'FS0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'FS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'FS2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'FS3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'FS4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'FS5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'FS6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'FS7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'FC0',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'FC1',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'FC2',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'FC3',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'FC4',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'FC5',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'FC6',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'FC7',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	42058
	.byte	11
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,12
	.byte	'MODREV',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	42394
	.byte	11
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,12
	.byte	'IPEN00',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'IPEN01',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'IPEN02',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'IPEN03',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'IPEN04',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'IPEN05',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'IPEN06',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'IPEN07',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	5,3,2,35,1,12
	.byte	'GEEN0',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'IGP0',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'IPEN10',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'IPEN11',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'IPEN12',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'IPEN13',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'IPEN14',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'IPEN15',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'IPEN16',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'IPEN17',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	5,3,2,35,3,12
	.byte	'GEEN1',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'IGP1',0,1
	.word	591
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	42501
	.byte	11
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,12
	.byte	'P0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	42953
	.byte	11
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'PC0',0,1
	.word	591
	.byte	4,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	4,4,2,35,1,12
	.byte	'PC1',0,1
	.word	591
	.byte	4,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	43052
	.byte	11
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,12
	.byte	'LBISTREQ',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'LBISTREQP',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PATTERNS',0,2
	.word	1142
	.byte	14,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	43202
	.byte	11
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,12
	.byte	'SEED',0,4
	.word	394
	.byte	23,9,2,35,0,12
	.byte	'reserved_23',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'SPLITSH',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'BODY',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'LBISTFREQU',0,1
	.word	591
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	43351
	.byte	11
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,12
	.byte	'SIGNATURE',0,4
	.word	394
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	7,1,2,35,3,12
	.byte	'LBISTDONE',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	43512
	.byte	11
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,12
	.byte	'reserved_0',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'LS',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,2
	.word	1142
	.byte	14,1,2,35,2,12
	.byte	'LSEN',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	43642
	.byte	11
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,12
	.byte	'LCLT0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'LCLT1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	43774
	.byte	11
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,12
	.byte	'DEPT',0,1
	.word	591
	.byte	5,3,2,35,0,12
	.byte	'MANUF',0,2
	.word	1142
	.byte	11,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	43889
	.byte	11
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,12
	.byte	'PS0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,2
	.word	1142
	.byte	14,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,2
	.word	1142
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	44000
	.byte	11
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PLLLV',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'OSCRES',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'GAINSEL',0,1
	.word	591
	.byte	2,3,2,35,0,12
	.byte	'MODE',0,1
	.word	591
	.byte	2,1,2,35,0,12
	.byte	'SHBY',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'PLLHV',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'reserved_9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'X1D',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'X1DEN',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,1
	.word	591
	.byte	4,0,2,35,1,12
	.byte	'OSCVAL',0,1
	.word	591
	.byte	5,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	591
	.byte	2,1,2,35,2,12
	.byte	'APREN',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'CAP0EN',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'CAP1EN',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'CAP2EN',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'CAP3EN',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	591
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	44158
	.byte	11
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,12
	.byte	'P0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	44570
	.byte	11
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,12
	.byte	'CSEL0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'CSEL1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'CSEL2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,2
	.word	1142
	.byte	13,0,2,35,0,12
	.byte	'OVSTRT',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'OVSTP',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'DCINVAL',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'reserved_19',0,1
	.word	591
	.byte	5,0,2,35,2,12
	.byte	'OVCONF',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'POVCONF',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	591
	.byte	6,0,2,35,3,0,27
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	44671
	.byte	11
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,12
	.byte	'OVEN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'OVEN1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'OVEN2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,4
	.word	394
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	44938
	.byte	11
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,12
	.byte	'PDIS0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PDIS1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	45074
	.byte	11
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,12
	.byte	'PD0',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'PL0',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PD1',0,1
	.word	591
	.byte	3,1,2,35,0,12
	.byte	'PL1',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	45185
	.byte	11
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,12
	.byte	'PDR0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PDR1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PDR2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PDR3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PDR4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'PDR5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PDR6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PDR7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	45318
	.byte	11
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,12
	.byte	'VCOBYP',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'VCOPWD',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'MODEN',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'SETFINDIS',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'CLRFINDIS',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'OSCDISCDIS',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,2
	.word	1142
	.byte	2,7,2,35,0,12
	.byte	'NDIV',0,1
	.word	591
	.byte	7,0,2,35,1,12
	.byte	'PLLPWD',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'RESLD',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'reserved_19',0,1
	.word	591
	.byte	5,0,2,35,2,12
	.byte	'PDIV',0,1
	.word	591
	.byte	4,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	591
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	45521
	.byte	11
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,12
	.byte	'K2DIV',0,1
	.word	591
	.byte	7,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'K3DIV',0,1
	.word	591
	.byte	7,1,2,35,1,12
	.byte	'reserved_15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'K1DIV',0,1
	.word	591
	.byte	7,1,2,35,2,12
	.byte	'reserved_23',0,2
	.word	1142
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	45877
	.byte	11
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,12
	.byte	'MODCFG',0,2
	.word	1142
	.byte	16,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	46055
	.byte	11
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,12
	.byte	'VCOBYP',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'VCOPWD',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'SETFINDIS',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'CLRFINDIS',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'OSCDISCDIS',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,2
	.word	1142
	.byte	2,7,2,35,0,12
	.byte	'NDIV',0,1
	.word	591
	.byte	5,2,2,35,1,12
	.byte	'reserved_14',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'PLLPWD',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'RESLD',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'reserved_19',0,1
	.word	591
	.byte	5,0,2,35,2,12
	.byte	'PDIV',0,1
	.word	591
	.byte	4,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	591
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	46155
	.byte	11
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,12
	.byte	'K2DIV',0,1
	.word	591
	.byte	7,1,2,35,0,12
	.byte	'reserved_7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'K3DIV',0,1
	.word	591
	.byte	4,4,2,35,1,12
	.byte	'reserved_12',0,1
	.word	591
	.byte	4,0,2,35,1,12
	.byte	'K1DIV',0,1
	.word	591
	.byte	7,1,2,35,2,12
	.byte	'reserved_23',0,2
	.word	1142
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	46525
	.byte	11
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,12
	.byte	'VCOBYST',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PWDSTAT',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'VCOLOCK',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'FINDIS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'K1RDY',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'K2RDY',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	394
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	46711
	.byte	11
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,12
	.byte	'VCOBYST',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'VCOLOCK',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'FINDIS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'K1RDY',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'K2RDY',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'MODRUN',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	46909
	.byte	11
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,12
	.byte	'REQSLP',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'SMUSLP',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	591
	.byte	5,0,2,35,0,12
	.byte	'PMST',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	394
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	47142
	.byte	11
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ESR1WKEN',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PINAWKEN',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PINBWKEN',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'ESR0DFEN',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'ESR0EDCON',0,1
	.word	591
	.byte	2,1,2,35,0,12
	.byte	'ESR1DFEN',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'ESR1EDCON',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'PINADFEN',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'PINAEDCON',0,1
	.word	591
	.byte	2,3,2,35,1,12
	.byte	'PINBDFEN',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'PINBEDCON',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'SCREN',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'STBYRAMSEL',0,1
	.word	591
	.byte	2,5,2,35,2,12
	.byte	'SCRCLKSEL',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'SCRWKEN',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'TRISTEN',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'TRISTREQ',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'PORSTDF',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'DCDCSYNC',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	591
	.byte	3,3,2,35,3,12
	.byte	'ESR0TRIST',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	47294
	.byte	11
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,12
	.byte	'SCRSTEN',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SCRSTREQ',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	6,0,2,35,0,12
	.byte	'CPUIDLSEL',0,1
	.word	591
	.byte	3,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'IRADIS',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'reserved_13',0,1
	.word	591
	.byte	3,0,2,35,1,12
	.byte	'SCRCFG',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'CPUSEL',0,1
	.word	591
	.byte	3,5,2,35,3,12
	.byte	'STBYEVEN',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'STBYEV',0,1
	.word	591
	.byte	3,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	47861
	.byte	11
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,12
	.byte	'SCRINT',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'BUSY',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'SCRECC',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'SCRWDT',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'SCRRST',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,1
	.word	591
	.byte	4,0,2,35,1,12
	.byte	'TCINT',0,1
	.word	591
	.byte	8,0,2,35,2,12
	.byte	'TCINTREQ',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'SMURST',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'RST',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'reserved_27',0,1
	.word	591
	.byte	4,1,2,35,3,12
	.byte	'LCK',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	48155
	.byte	11
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'ESR1WKP',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'ESR1OVRUN',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PINAWKP',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'PINAOVRUN',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PINBWKP',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PINBOVRUN',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'PORSTDF',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'HWCFGEVR',0,1
	.word	591
	.byte	3,3,2,35,1,12
	.byte	'STBYRAM',0,1
	.word	591
	.byte	2,1,2,35,1,12
	.byte	'TRIST',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'SCRST',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'SCRWKP',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'SCR',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'SCRWKEN',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'ESR1WKEN',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'PINAWKEN',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'PINBWKEN',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'reserved_23',0,2
	.word	1142
	.byte	4,5,2,35,2,12
	.byte	'ESR0TRIST',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	591
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	48433
	.byte	11
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'ESR1WKPCLR',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'ESR1OVRUNCLR',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PINAWKPCLR',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'PINAOVRUNCLR',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PINBWKPCLR',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PINBOVRUNCLR',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'SCRSTCLR',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'SCRWKPCLR',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,2
	.word	1142
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	48929
	.byte	11
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'CLRC',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,2
	.word	1142
	.byte	10,4,2,35,0,12
	.byte	'CSS0',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'CSS1',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'CSS2',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'reserved_15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'USRINFO',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	49242
	.byte	11
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,12
	.byte	'ESR0',0,1
	.word	591
	.byte	2,6,2,35,0,12
	.byte	'ESR1',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'reserved_4',0,1
	.word	591
	.byte	2,2,2,35,0,12
	.byte	'SMU',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'SW',0,1
	.word	591
	.byte	2,6,2,35,1,12
	.byte	'STM0',0,1
	.word	591
	.byte	2,4,2,35,1,12
	.byte	'STM1',0,1
	.word	591
	.byte	2,2,2,35,1,12
	.byte	'STM2',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	49451
	.byte	11
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,12
	.byte	'ESR0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ESR1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SMU',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'SW',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'STM0',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'STM1',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'STM2',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'PORST',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'reserved_17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'CB0',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'CB1',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'CB3',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	591
	.byte	2,1,2,35,2,12
	.byte	'EVR13',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'EVR33',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'SWD',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'reserved_26',0,1
	.word	591
	.byte	2,4,2,35,3,12
	.byte	'STBYR',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'reserved_29',0,1
	.word	591
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	49662
	.byte	11
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,12
	.byte	'HBT',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	394
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	50094
	.byte	11
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,12
	.byte	'HWCFG',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'FTM',0,1
	.word	591
	.byte	7,1,2,35,1,12
	.byte	'MODE',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'FCBAE',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'LUDIS',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'TRSTL',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'SPDEN',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	591
	.byte	3,0,2,35,2,12
	.byte	'RAMINT',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'reserved_25',0,1
	.word	591
	.byte	7,0,2,35,3,0,27
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	50190
	.byte	11
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SWRSTREQ',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	50450
	.byte	11
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,12
	.byte	'CCTRIG0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'RAMINTM',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'SETLUDIS',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'reserved_5',0,1
	.word	591
	.byte	3,0,2,35,0,12
	.byte	'DATM',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'reserved_9',0,4
	.word	394
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	50575
	.byte	11
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,12
	.byte	'ESR0T',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	50772
	.byte	11
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,12
	.byte	'ESR0T',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	50925
	.byte	11
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,12
	.byte	'ESR0T',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	51078
	.byte	11
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,12
	.byte	'ESR0T',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'ESR1T',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SMUT',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	51231
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	1006
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	1164
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	1408
	.byte	11
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,12
	.byte	'ENDINIT',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'LCK',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'PW',0,4
	.word	990
	.byte	14,16,2,35,0,12
	.byte	'REL',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	51486
	.byte	11
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,12
	.byte	'CLRIRF',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'IR0',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'DR',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'IR1',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'UR',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PAR',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'TCR',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'TCTR',0,1
	.word	591
	.byte	7,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	51612
	.byte	11
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,12
	.byte	'AE',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'OE',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'IS0',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'DS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'TO',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'IS1',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'US',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PAS',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'TCS',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'TCT',0,1
	.word	591
	.byte	7,0,2,35,1,12
	.byte	'TIM',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	51864
	.byte	13,6,199,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	33896
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	52083
	.byte	13,6,207,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	34453
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	52147
	.byte	13,6,215,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	34530
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	52211
	.byte	13,6,223,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	34666
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	52276
	.byte	13,6,231,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	34946
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	52341
	.byte	13,6,239,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	35184
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	52406
	.byte	13,6,247,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	35312
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	52471
	.byte	13,6,255,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	35555
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	52536
	.byte	13,6,135,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	35790
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	52601
	.byte	13,6,143,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	35918
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	52666
	.byte	13,6,151,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	36018
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	52731
	.byte	13,6,159,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	36118
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	52796
	.byte	13,6,167,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	36326
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	52860
	.byte	13,6,175,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	36491
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	52924
	.byte	13,6,183,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	36674
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	52988
	.byte	13,6,191,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	36828
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	53053
	.byte	13,6,199,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	37192
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	53115
	.byte	13,6,207,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	37403
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	53177
	.byte	13,6,215,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	37655
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	53239
	.byte	13,6,223,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	37773
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	53303
	.byte	13,6,231,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	37884
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	53368
	.byte	13,6,239,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	38047
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	53434
	.byte	13,6,247,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	38210
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	53500
	.byte	13,6,255,10,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	38368
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	53568
	.byte	13,6,135,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	38533
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	53635
	.byte	13,6,143,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	38862
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	53703
	.byte	13,6,151,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	39083
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	53771
	.byte	13,6,159,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	39246
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	53837
	.byte	13,6,167,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	39518
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	53904
	.byte	13,6,175,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	39671
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	53973
	.byte	13,6,183,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	39827
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	54042
	.byte	13,6,191,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	39989
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	54111
	.byte	13,6,199,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	40132
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	54180
	.byte	13,6,207,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	40297
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	54249
	.byte	13,6,215,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	40442
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	54318
	.byte	13,6,223,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	40623
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	54386
	.byte	13,6,231,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	40797
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	54454
	.byte	13,6,239,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	40957
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	54522
	.byte	13,6,247,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	41101
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	54590
	.byte	13,6,255,11,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	41375
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	54655
	.byte	13,6,135,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	41514
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	54720
	.byte	13,6,143,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	41677
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	54786
	.byte	13,6,151,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	41895
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	54850
	.byte	13,6,159,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	42058
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	54911
	.byte	13,6,167,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	42394
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	54972
	.byte	13,6,175,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	42501
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	55032
	.byte	13,6,183,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	42953
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	55094
	.byte	13,6,191,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	43052
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	55154
	.byte	13,6,199,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	43202
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	55216
	.byte	13,6,207,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	43351
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	55284
	.byte	13,6,215,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	43512
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	55352
	.byte	13,6,223,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	43642
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	55420
	.byte	13,6,231,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	43774
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	55484
	.byte	13,6,239,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	43889
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	55549
	.byte	13,6,247,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	44000
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	55612
	.byte	13,6,255,12,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	44158
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	55673
	.byte	13,6,135,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	44570
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	55737
	.byte	13,6,143,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	44671
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	55798
	.byte	13,6,151,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	44938
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	55862
	.byte	13,6,159,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	45074
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	55929
	.byte	13,6,167,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	45185
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	55992
	.byte	13,6,175,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	45318
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	56053
	.byte	13,6,183,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	45521
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	56115
	.byte	13,6,191,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	45877
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	56180
	.byte	13,6,199,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	46055
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	56245
	.byte	13,6,207,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	46155
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	56310
	.byte	13,6,215,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	46525
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	56379
	.byte	13,6,223,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	46711
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	56448
	.byte	13,6,231,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	46909
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	56517
	.byte	13,6,239,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	47142
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	56582
	.byte	13,6,247,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	47294
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	56645
	.byte	13,6,255,13,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	47861
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	56710
	.byte	13,6,135,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	48155
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	56775
	.byte	13,6,143,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	48433
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	56840
	.byte	13,6,151,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	48929
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	56906
	.byte	13,6,159,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	49451
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	56975
	.byte	13,6,167,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	49242
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	57039
	.byte	13,6,175,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	49662
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	57104
	.byte	13,6,183,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	50094
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	57169
	.byte	13,6,191,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	50190
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	57234
	.byte	13,6,199,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	50450
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	57298
	.byte	13,6,207,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	50575
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	57364
	.byte	13,6,215,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	50772
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	57428
	.byte	13,6,223,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	50925
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	57493
	.byte	13,6,231,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	51078
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	57558
	.byte	13,6,239,14,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	51231
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	57623
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	1102
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	1368
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	1599
	.byte	13,6,143,15,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	51486
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	57774
	.byte	13,6,151,15,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	51612
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	57841
	.byte	13,6,159,15,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	51864
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	57908
	.byte	10
	.word	1639
	.byte	27
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	57973
	.byte	11
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,14
	.byte	'CON0',0
	.word	57774
	.byte	4,2,35,0,14
	.byte	'CON1',0
	.word	57841
	.byte	4,2,35,4,14
	.byte	'SR',0
	.word	57908
	.byte	4,2,35,8,0,10
	.word	58002
	.byte	27
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	58063
	.byte	18,8
	.word	53239
	.byte	19,1,0,18,20
	.word	591
	.byte	19,19,0,18,8
	.word	56582
	.byte	19,1,0,10
	.word	58002
	.byte	18,24
	.word	1639
	.byte	19,1,0,10
	.word	58122
	.byte	18,28
	.word	591
	.byte	19,27,0,18,16
	.word	53053
	.byte	19,3,0,18,16
	.word	55032
	.byte	19,3,0,18,180,3
	.word	591
	.byte	19,179,3,0,11
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,14
	.byte	'reserved_0',0
	.word	5148
	.byte	8,2,35,0,14
	.byte	'ID',0
	.word	54972
	.byte	4,2,35,8,14
	.byte	'reserved_C',0
	.word	3329
	.byte	4,2,35,12,14
	.byte	'OSCCON',0
	.word	55673
	.byte	4,2,35,16,14
	.byte	'PLLSTAT',0
	.word	56517
	.byte	4,2,35,20,14
	.byte	'PLLCON0',0
	.word	56115
	.byte	4,2,35,24,14
	.byte	'PLLCON1',0
	.word	56180
	.byte	4,2,35,28,14
	.byte	'PLLCON2',0
	.word	56245
	.byte	4,2,35,32,14
	.byte	'PLLERAYSTAT',0
	.word	56448
	.byte	4,2,35,36,14
	.byte	'PLLERAYCON0',0
	.word	56310
	.byte	4,2,35,40,14
	.byte	'PLLERAYCON1',0
	.word	56379
	.byte	4,2,35,44,14
	.byte	'CCUCON0',0
	.word	52276
	.byte	4,2,35,48,14
	.byte	'CCUCON1',0
	.word	52341
	.byte	4,2,35,52,14
	.byte	'FDR',0
	.word	54850
	.byte	4,2,35,56,14
	.byte	'EXTCON',0
	.word	54786
	.byte	4,2,35,60,14
	.byte	'CCUCON2',0
	.word	52406
	.byte	4,2,35,64,14
	.byte	'CCUCON3',0
	.word	52471
	.byte	4,2,35,68,14
	.byte	'CCUCON4',0
	.word	52536
	.byte	4,2,35,72,14
	.byte	'CCUCON5',0
	.word	52601
	.byte	4,2,35,76,14
	.byte	'RSTSTAT',0
	.word	57104
	.byte	4,2,35,80,14
	.byte	'reserved_54',0
	.word	3329
	.byte	4,2,35,84,14
	.byte	'RSTCON',0
	.word	56975
	.byte	4,2,35,88,14
	.byte	'ARSTDIS',0
	.word	52211
	.byte	4,2,35,92,14
	.byte	'SWRSTCON',0
	.word	57298
	.byte	4,2,35,96,14
	.byte	'RSTCON2',0
	.word	57039
	.byte	4,2,35,100,14
	.byte	'reserved_68',0
	.word	3329
	.byte	4,2,35,104,14
	.byte	'EVRRSTCON',0
	.word	53837
	.byte	4,2,35,108,14
	.byte	'ESRCFG',0
	.word	58090
	.byte	8,2,35,112,14
	.byte	'ESROCFG',0
	.word	53303
	.byte	4,2,35,120,14
	.byte	'SYSCON',0
	.word	57364
	.byte	4,2,35,124,14
	.byte	'CCUCON6',0
	.word	52666
	.byte	4,3,35,128,1,14
	.byte	'CCUCON7',0
	.word	52731
	.byte	4,3,35,132,1,14
	.byte	'reserved_88',0
	.word	58099
	.byte	20,3,35,136,1,14
	.byte	'PDR',0
	.word	55992
	.byte	4,3,35,156,1,14
	.byte	'IOCR',0
	.word	55154
	.byte	4,3,35,160,1,14
	.byte	'OUT',0
	.word	55737
	.byte	4,3,35,164,1,14
	.byte	'OMR',0
	.word	55612
	.byte	4,3,35,168,1,14
	.byte	'IN',0
	.word	55094
	.byte	4,3,35,172,1,14
	.byte	'EVRSTAT',0
	.word	54590
	.byte	4,3,35,176,1,14
	.byte	'EVRDVSTAT',0
	.word	53568
	.byte	4,3,35,180,1,14
	.byte	'EVR13CON',0
	.word	53368
	.byte	4,3,35,184,1,14
	.byte	'EVR33CON',0
	.word	53434
	.byte	4,3,35,188,1,14
	.byte	'STSTAT',0
	.word	57234
	.byte	4,3,35,192,1,14
	.byte	'reserved_C4',0
	.word	3329
	.byte	4,3,35,196,1,14
	.byte	'PMSWCR0',0
	.word	56645
	.byte	4,3,35,200,1,14
	.byte	'PMSWSTAT',0
	.word	56840
	.byte	4,3,35,204,1,14
	.byte	'PMSWSTATCLR',0
	.word	56906
	.byte	4,3,35,208,1,14
	.byte	'PMCSR',0
	.word	58108
	.byte	8,3,35,212,1,14
	.byte	'reserved_DC',0
	.word	3329
	.byte	4,3,35,220,1,14
	.byte	'DTSSTAT',0
	.word	52988
	.byte	4,3,35,224,1,14
	.byte	'DTSCON',0
	.word	52860
	.byte	4,3,35,228,1,14
	.byte	'PMSWCR1',0
	.word	56710
	.byte	4,3,35,232,1,14
	.byte	'PMSWCR2',0
	.word	56775
	.byte	4,3,35,236,1,14
	.byte	'WDTS',0
	.word	58117
	.byte	12,3,35,240,1,14
	.byte	'EMSR',0
	.word	53177
	.byte	4,3,35,252,1,14
	.byte	'WDTCPU',0
	.word	58131
	.byte	24,3,35,128,2,14
	.byte	'reserved_118',0
	.word	5488
	.byte	12,3,35,152,2,14
	.byte	'TRAPSTAT',0
	.word	57623
	.byte	4,3,35,164,2,14
	.byte	'TRAPSET',0
	.word	57558
	.byte	4,3,35,168,2,14
	.byte	'TRAPCLR',0
	.word	57428
	.byte	4,3,35,172,2,14
	.byte	'TRAPDIS',0
	.word	57493
	.byte	4,3,35,176,2,14
	.byte	'reserved_134',0
	.word	3329
	.byte	4,3,35,180,2,14
	.byte	'LCLCON1',0
	.word	55420
	.byte	4,3,35,184,2,14
	.byte	'LCLTEST',0
	.word	55484
	.byte	4,3,35,188,2,14
	.byte	'CHIPID',0
	.word	52796
	.byte	4,3,35,192,2,14
	.byte	'MANID',0
	.word	55549
	.byte	4,3,35,196,2,14
	.byte	'reserved_148',0
	.word	5148
	.byte	8,3,35,200,2,14
	.byte	'SAFECON',0
	.word	57169
	.byte	4,3,35,208,2,14
	.byte	'reserved_154',0
	.word	28687
	.byte	16,3,35,212,2,14
	.byte	'LBISTCTRL0',0
	.word	55216
	.byte	4,3,35,228,2,14
	.byte	'LBISTCTRL1',0
	.word	55284
	.byte	4,3,35,232,2,14
	.byte	'LBISTCTRL2',0
	.word	55352
	.byte	4,3,35,236,2,14
	.byte	'reserved_170',0
	.word	58136
	.byte	28,3,35,240,2,14
	.byte	'PDISC',0
	.word	55929
	.byte	4,3,35,140,3,14
	.byte	'reserved_190',0
	.word	5148
	.byte	8,3,35,144,3,14
	.byte	'EVRTRIM',0
	.word	54655
	.byte	4,3,35,152,3,14
	.byte	'EVRADCSTAT',0
	.word	53500
	.byte	4,3,35,156,3,14
	.byte	'EVRUVMON',0
	.word	54720
	.byte	4,3,35,160,3,14
	.byte	'EVROVMON',0
	.word	53771
	.byte	4,3,35,164,3,14
	.byte	'EVRMONCTRL',0
	.word	53635
	.byte	4,3,35,168,3,14
	.byte	'reserved_1AC',0
	.word	3329
	.byte	4,3,35,172,3,14
	.byte	'EVRSDCTRL1',0
	.word	54318
	.byte	4,3,35,176,3,14
	.byte	'EVRSDCTRL2',0
	.word	54386
	.byte	4,3,35,180,3,14
	.byte	'EVRSDCTRL3',0
	.word	54454
	.byte	4,3,35,184,3,14
	.byte	'EVRSDCTRL4',0
	.word	54522
	.byte	4,3,35,188,3,14
	.byte	'EVRSDCOEFF1',0
	.word	53904
	.byte	4,3,35,192,3,14
	.byte	'EVRSDCOEFF2',0
	.word	53973
	.byte	4,3,35,196,3,14
	.byte	'EVRSDCOEFF3',0
	.word	54042
	.byte	4,3,35,200,3,14
	.byte	'EVRSDCOEFF4',0
	.word	54111
	.byte	4,3,35,204,3,14
	.byte	'EVRSDCOEFF5',0
	.word	54180
	.byte	4,3,35,208,3,14
	.byte	'EVRSDCOEFF6',0
	.word	54249
	.byte	4,3,35,212,3,14
	.byte	'EVROSCCTRL',0
	.word	53703
	.byte	4,3,35,216,3,14
	.byte	'reserved_1DC',0
	.word	3329
	.byte	4,3,35,220,3,14
	.byte	'OVCENABLE',0
	.word	55862
	.byte	4,3,35,224,3,14
	.byte	'OVCCON',0
	.word	55798
	.byte	4,3,35,228,3,14
	.byte	'reserved_1E8',0
	.word	28322
	.byte	40,3,35,232,3,14
	.byte	'EICR',0
	.word	58145
	.byte	16,3,35,144,4,14
	.byte	'EIFR',0
	.word	53115
	.byte	4,3,35,160,4,14
	.byte	'FMR',0
	.word	54911
	.byte	4,3,35,164,4,14
	.byte	'PDRR',0
	.word	56053
	.byte	4,3,35,168,4,14
	.byte	'IGCR',0
	.word	58154
	.byte	16,3,35,172,4,14
	.byte	'reserved_23C',0
	.word	3329
	.byte	4,3,35,188,4,14
	.byte	'DTSLIM',0
	.word	52924
	.byte	4,3,35,192,4,14
	.byte	'reserved_244',0
	.word	58163
	.byte	180,3,3,35,196,4,14
	.byte	'ACCEN1',0
	.word	52147
	.byte	4,3,35,248,7,14
	.byte	'ACCEN0',0
	.word	52083
	.byte	4,3,35,252,7,0,10
	.word	58174
	.byte	27
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	60164
	.byte	11
	.byte	'_Ifx_CPU_A_Bits',0,21,45,16,4,12
	.byte	'ADDR',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_A_Bits',0,21,48,3
	.word	60186
	.byte	11
	.byte	'_Ifx_CPU_BIV_Bits',0,21,51,16,4,12
	.byte	'VSS',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'BIV',0,4
	.word	990
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BIV_Bits',0,21,55,3
	.word	60247
	.byte	11
	.byte	'_Ifx_CPU_BTV_Bits',0,21,58,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'BTV',0,4
	.word	990
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BTV_Bits',0,21,62,3
	.word	60326
	.byte	11
	.byte	'_Ifx_CPU_CCNT_Bits',0,21,65,16,4,12
	.byte	'CountValue',0,4
	.word	990
	.byte	31,1,2,35,0,12
	.byte	'SOvf',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT_Bits',0,21,69,3
	.word	60412
	.byte	11
	.byte	'_Ifx_CPU_CCTRL_Bits',0,21,72,16,4,12
	.byte	'CM',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'CE',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'M1',0,4
	.word	990
	.byte	3,27,2,35,0,12
	.byte	'M2',0,4
	.word	990
	.byte	3,24,2,35,0,12
	.byte	'M3',0,4
	.word	990
	.byte	3,21,2,35,0,12
	.byte	'reserved_11',0,4
	.word	990
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL_Bits',0,21,80,3
	.word	60501
	.byte	11
	.byte	'_Ifx_CPU_COMPAT_Bits',0,21,83,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'RM',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'SP',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	990
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT_Bits',0,21,89,3
	.word	60647
	.byte	11
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,21,92,16,4,12
	.byte	'CORE_ID',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	990
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID_Bits',0,21,96,3
	.word	60774
	.byte	11
	.byte	'_Ifx_CPU_CPR_L_Bits',0,21,99,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'LOWBND',0,4
	.word	990
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L_Bits',0,21,103,3
	.word	60872
	.byte	11
	.byte	'_Ifx_CPU_CPR_U_Bits',0,21,106,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'UPPBND',0,4
	.word	990
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U_Bits',0,21,110,3
	.word	60965
	.byte	11
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,21,113,16,4,12
	.byte	'MODREV',0,4
	.word	990
	.byte	8,24,2,35,0,12
	.byte	'MOD_32B',0,4
	.word	990
	.byte	8,16,2,35,0,12
	.byte	'MOD',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID_Bits',0,21,118,3
	.word	61058
	.byte	11
	.byte	'_Ifx_CPU_CPXE_Bits',0,21,121,16,4,12
	.byte	'XE',0,4
	.word	990
	.byte	8,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	990
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE_Bits',0,21,125,3
	.word	61165
	.byte	11
	.byte	'_Ifx_CPU_CREVT_Bits',0,21,128,1,16,4,12
	.byte	'EVTA',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'BBM',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'BOD',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'SUSP',0,4
	.word	990
	.byte	1,26,2,35,0,12
	.byte	'CNT',0,4
	.word	990
	.byte	2,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	990
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT_Bits',0,21,136,1,3
	.word	61252
	.byte	11
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,21,139,1,16,4,12
	.byte	'CID',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	990
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID_Bits',0,21,143,1,3
	.word	61406
	.byte	11
	.byte	'_Ifx_CPU_D_Bits',0,21,146,1,16,4,12
	.byte	'DATA',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_D_Bits',0,21,149,1,3
	.word	61500
	.byte	11
	.byte	'_Ifx_CPU_DATR_Bits',0,21,152,1,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'SBE',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	990
	.byte	5,23,2,35,0,12
	.byte	'CWE',0,4
	.word	990
	.byte	1,22,2,35,0,12
	.byte	'CFE',0,4
	.word	990
	.byte	1,21,2,35,0,12
	.byte	'reserved_11',0,4
	.word	990
	.byte	3,18,2,35,0,12
	.byte	'SOE',0,4
	.word	990
	.byte	1,17,2,35,0,12
	.byte	'SME',0,4
	.word	990
	.byte	1,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DATR_Bits',0,21,163,1,3
	.word	61563
	.byte	11
	.byte	'_Ifx_CPU_DBGSR_Bits',0,21,166,1,16,4,12
	.byte	'DE',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'HALT',0,4
	.word	990
	.byte	2,29,2,35,0,12
	.byte	'SIH',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'SUSP',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	990
	.byte	1,26,2,35,0,12
	.byte	'PREVSUSP',0,4
	.word	990
	.byte	1,25,2,35,0,12
	.byte	'PEVT',0,4
	.word	990
	.byte	1,24,2,35,0,12
	.byte	'EVTSRC',0,4
	.word	990
	.byte	5,19,2,35,0,12
	.byte	'reserved_13',0,4
	.word	990
	.byte	19,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR_Bits',0,21,177,1,3
	.word	61781
	.byte	11
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,21,180,1,16,4,12
	.byte	'DTA',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	990
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR_Bits',0,21,184,1,3
	.word	61996
	.byte	11
	.byte	'_Ifx_CPU_DCON0_Bits',0,21,187,1,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'DCBYP',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	990
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0_Bits',0,21,192,1,3
	.word	62090
	.byte	11
	.byte	'_Ifx_CPU_DCON2_Bits',0,21,195,1,16,4,12
	.byte	'DCACHE_SZE',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'DSCRATCH_SZE',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2_Bits',0,21,199,1,3
	.word	62206
	.byte	11
	.byte	'_Ifx_CPU_DCX_Bits',0,21,202,1,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	6,26,2,35,0,12
	.byte	'DCXValue',0,4
	.word	990
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCX_Bits',0,21,206,1,3
	.word	62307
	.byte	11
	.byte	'_Ifx_CPU_DEADD_Bits',0,21,209,1,16,4,12
	.byte	'ERROR_ADDRESS',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD_Bits',0,21,212,1,3
	.word	62400
	.byte	11
	.byte	'_Ifx_CPU_DIEAR_Bits',0,21,215,1,16,4,12
	.byte	'TA',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR_Bits',0,21,218,1,3
	.word	62480
	.byte	11
	.byte	'_Ifx_CPU_DIETR_Bits',0,21,221,1,16,4,12
	.byte	'IED',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'IE_T',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'IE_C',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'IE_S',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'IE_BI',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'E_INFO',0,4
	.word	990
	.byte	6,21,2,35,0,12
	.byte	'IE_DUAL',0,4
	.word	990
	.byte	1,20,2,35,0,12
	.byte	'IE_SP',0,4
	.word	990
	.byte	1,19,2,35,0,12
	.byte	'IE_BS',0,4
	.word	990
	.byte	1,18,2,35,0,12
	.byte	'reserved_14',0,4
	.word	990
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR_Bits',0,21,233,1,3
	.word	62549
	.byte	11
	.byte	'_Ifx_CPU_DMS_Bits',0,21,236,1,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'DMSValue',0,4
	.word	990
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DMS_Bits',0,21,240,1,3
	.word	62778
	.byte	11
	.byte	'_Ifx_CPU_DPR_L_Bits',0,21,243,1,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'LOWBND',0,4
	.word	990
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L_Bits',0,21,247,1,3
	.word	62871
	.byte	11
	.byte	'_Ifx_CPU_DPR_U_Bits',0,21,250,1,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'UPPBND',0,4
	.word	990
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U_Bits',0,21,254,1,3
	.word	62966
	.byte	11
	.byte	'_Ifx_CPU_DPRE_Bits',0,21,129,2,16,4,12
	.byte	'RE',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE_Bits',0,21,133,2,3
	.word	63061
	.byte	11
	.byte	'_Ifx_CPU_DPWE_Bits',0,21,136,2,16,4,12
	.byte	'WE',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE_Bits',0,21,140,2,3
	.word	63151
	.byte	11
	.byte	'_Ifx_CPU_DSTR_Bits',0,21,143,2,16,4,12
	.byte	'SRE',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'GAE',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'LBE',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	990
	.byte	3,26,2,35,0,12
	.byte	'CRE',0,4
	.word	990
	.byte	1,25,2,35,0,12
	.byte	'reserved_7',0,4
	.word	990
	.byte	7,18,2,35,0,12
	.byte	'DTME',0,4
	.word	990
	.byte	1,17,2,35,0,12
	.byte	'LOE',0,4
	.word	990
	.byte	1,16,2,35,0,12
	.byte	'SDE',0,4
	.word	990
	.byte	1,15,2,35,0,12
	.byte	'SCE',0,4
	.word	990
	.byte	1,14,2,35,0,12
	.byte	'CAC',0,4
	.word	990
	.byte	1,13,2,35,0,12
	.byte	'MPE',0,4
	.word	990
	.byte	1,12,2,35,0,12
	.byte	'CLE',0,4
	.word	990
	.byte	1,11,2,35,0,12
	.byte	'reserved_21',0,4
	.word	990
	.byte	3,8,2,35,0,12
	.byte	'ALN',0,4
	.word	990
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	990
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR_Bits',0,21,161,2,3
	.word	63241
	.byte	11
	.byte	'_Ifx_CPU_EXEVT_Bits',0,21,164,2,16,4,12
	.byte	'EVTA',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'BBM',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'BOD',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'SUSP',0,4
	.word	990
	.byte	1,26,2,35,0,12
	.byte	'CNT',0,4
	.word	990
	.byte	2,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	990
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT_Bits',0,21,172,2,3
	.word	63565
	.byte	11
	.byte	'_Ifx_CPU_FCX_Bits',0,21,175,2,16,4,12
	.byte	'FCXO',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'FCXS',0,4
	.word	990
	.byte	4,12,2,35,0,12
	.byte	'reserved_20',0,4
	.word	990
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FCX_Bits',0,21,180,2,3
	.word	63719
	.byte	11
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,21,183,2,16,4,12
	.byte	'TST',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'TCL',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	990
	.byte	6,24,2,35,0,12
	.byte	'RM',0,4
	.word	990
	.byte	2,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	990
	.byte	8,14,2,35,0,12
	.byte	'FXE',0,4
	.word	990
	.byte	1,13,2,35,0,12
	.byte	'FUE',0,4
	.word	990
	.byte	1,12,2,35,0,12
	.byte	'FZE',0,4
	.word	990
	.byte	1,11,2,35,0,12
	.byte	'FVE',0,4
	.word	990
	.byte	1,10,2,35,0,12
	.byte	'FIE',0,4
	.word	990
	.byte	1,9,2,35,0,12
	.byte	'reserved_23',0,4
	.word	990
	.byte	3,6,2,35,0,12
	.byte	'FX',0,4
	.word	990
	.byte	1,5,2,35,0,12
	.byte	'FU',0,4
	.word	990
	.byte	1,4,2,35,0,12
	.byte	'FZ',0,4
	.word	990
	.byte	1,3,2,35,0,12
	.byte	'FV',0,4
	.word	990
	.byte	1,2,2,35,0,12
	.byte	'FI',0,4
	.word	990
	.byte	1,1,2,35,0,12
	.byte	'reserved_31',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,21,202,2,3
	.word	63825
	.byte	11
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,205,2,16,4,12
	.byte	'OPC',0,4
	.word	990
	.byte	8,24,2,35,0,12
	.byte	'FMT',0,4
	.word	990
	.byte	1,23,2,35,0,12
	.byte	'reserved_9',0,4
	.word	990
	.byte	7,16,2,35,0,12
	.byte	'DREG',0,4
	.word	990
	.byte	4,12,2,35,0,12
	.byte	'reserved_20',0,4
	.word	990
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,212,2,3
	.word	64174
	.byte	11
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,21,215,2,16,4,12
	.byte	'PC',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,21,218,2,3
	.word	64334
	.byte	11
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,221,2,16,4,12
	.byte	'SRC1',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,224,2,3
	.word	64415
	.byte	11
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,227,2,16,4,12
	.byte	'SRC2',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,230,2,3
	.word	64502
	.byte	11
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,233,2,16,4,12
	.byte	'SRC3',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,236,2,3
	.word	64589
	.byte	11
	.byte	'_Ifx_CPU_ICNT_Bits',0,21,239,2,16,4,12
	.byte	'CountValue',0,4
	.word	990
	.byte	31,1,2,35,0,12
	.byte	'SOvf',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT_Bits',0,21,243,2,3
	.word	64676
	.byte	11
	.byte	'_Ifx_CPU_ICR_Bits',0,21,246,2,16,4,12
	.byte	'CCPN',0,4
	.word	990
	.byte	10,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	990
	.byte	5,17,2,35,0,12
	.byte	'IE',0,4
	.word	990
	.byte	1,16,2,35,0,12
	.byte	'PIPN',0,4
	.word	990
	.byte	10,6,2,35,0,12
	.byte	'reserved_26',0,4
	.word	990
	.byte	6,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICR_Bits',0,21,253,2,3
	.word	64767
	.byte	11
	.byte	'_Ifx_CPU_ISP_Bits',0,21,128,3,16,4,12
	.byte	'ISP',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_ISP_Bits',0,21,131,3,3
	.word	64910
	.byte	11
	.byte	'_Ifx_CPU_LCX_Bits',0,21,134,3,16,4,12
	.byte	'LCXO',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'LCXS',0,4
	.word	990
	.byte	4,12,2,35,0,12
	.byte	'reserved_20',0,4
	.word	990
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_LCX_Bits',0,21,139,3,3
	.word	64976
	.byte	11
	.byte	'_Ifx_CPU_M1CNT_Bits',0,21,142,3,16,4,12
	.byte	'CountValue',0,4
	.word	990
	.byte	31,1,2,35,0,12
	.byte	'SOvf',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT_Bits',0,21,146,3,3
	.word	65082
	.byte	11
	.byte	'_Ifx_CPU_M2CNT_Bits',0,21,149,3,16,4,12
	.byte	'CountValue',0,4
	.word	990
	.byte	31,1,2,35,0,12
	.byte	'SOvf',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT_Bits',0,21,153,3,3
	.word	65175
	.byte	11
	.byte	'_Ifx_CPU_M3CNT_Bits',0,21,156,3,16,4,12
	.byte	'CountValue',0,4
	.word	990
	.byte	31,1,2,35,0,12
	.byte	'SOvf',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT_Bits',0,21,160,3,3
	.word	65268
	.byte	11
	.byte	'_Ifx_CPU_PC_Bits',0,21,163,3,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'PC',0,4
	.word	990
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_PC_Bits',0,21,167,3,3
	.word	65361
	.byte	11
	.byte	'_Ifx_CPU_PCON0_Bits',0,21,170,3,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'PCBYP',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	990
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0_Bits',0,21,175,3,3
	.word	65446
	.byte	11
	.byte	'_Ifx_CPU_PCON1_Bits',0,21,178,3,16,4,12
	.byte	'PCINV',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'PBINV',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	990
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1_Bits',0,21,183,3,3
	.word	65562
	.byte	11
	.byte	'_Ifx_CPU_PCON2_Bits',0,21,186,3,16,4,12
	.byte	'PCACHE_SZE',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'PSCRATCH_SZE',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2_Bits',0,21,190,3,3
	.word	65673
	.byte	11
	.byte	'_Ifx_CPU_PCXI_Bits',0,21,193,3,16,4,12
	.byte	'PCXO',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'PCXS',0,4
	.word	990
	.byte	4,12,2,35,0,12
	.byte	'UL',0,4
	.word	990
	.byte	1,11,2,35,0,12
	.byte	'PIE',0,4
	.word	990
	.byte	1,10,2,35,0,12
	.byte	'PCPN',0,4
	.word	990
	.byte	10,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI_Bits',0,21,200,3,3
	.word	65774
	.byte	11
	.byte	'_Ifx_CPU_PIEAR_Bits',0,21,203,3,16,4,12
	.byte	'TA',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR_Bits',0,21,206,3,3
	.word	65904
	.byte	11
	.byte	'_Ifx_CPU_PIETR_Bits',0,21,209,3,16,4,12
	.byte	'IED',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'IE_T',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'IE_C',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'IE_S',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'IE_BI',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'E_INFO',0,4
	.word	990
	.byte	6,21,2,35,0,12
	.byte	'IE_DUAL',0,4
	.word	990
	.byte	1,20,2,35,0,12
	.byte	'IE_SP',0,4
	.word	990
	.byte	1,19,2,35,0,12
	.byte	'IE_BS',0,4
	.word	990
	.byte	1,18,2,35,0,12
	.byte	'reserved_14',0,4
	.word	990
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR_Bits',0,21,221,3,3
	.word	65973
	.byte	11
	.byte	'_Ifx_CPU_PMA0_Bits',0,21,224,3,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	13,19,2,35,0,12
	.byte	'DAC',0,4
	.word	990
	.byte	3,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0_Bits',0,21,229,3,3
	.word	66202
	.byte	11
	.byte	'_Ifx_CPU_PMA1_Bits',0,21,232,3,16,4,12
	.byte	'reserved_0',0,4
	.word	990
	.byte	14,18,2,35,0,12
	.byte	'CAC',0,4
	.word	990
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1_Bits',0,21,237,3,3
	.word	66315
	.byte	11
	.byte	'_Ifx_CPU_PMA2_Bits',0,21,240,3,16,4,12
	.byte	'PSI',0,4
	.word	990
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	990
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2_Bits',0,21,244,3,3
	.word	66428
	.byte	11
	.byte	'_Ifx_CPU_PSTR_Bits',0,21,247,3,16,4,12
	.byte	'FRE',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'FBE',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	990
	.byte	9,20,2,35,0,12
	.byte	'FPE',0,4
	.word	990
	.byte	1,19,2,35,0,12
	.byte	'reserved_13',0,4
	.word	990
	.byte	1,18,2,35,0,12
	.byte	'FME',0,4
	.word	990
	.byte	1,17,2,35,0,12
	.byte	'reserved_15',0,4
	.word	990
	.byte	17,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR_Bits',0,21,129,4,3
	.word	66519
	.byte	11
	.byte	'_Ifx_CPU_PSW_Bits',0,21,132,4,16,4,12
	.byte	'CDC',0,4
	.word	990
	.byte	7,25,2,35,0,12
	.byte	'CDE',0,4
	.word	990
	.byte	1,24,2,35,0,12
	.byte	'GW',0,4
	.word	990
	.byte	1,23,2,35,0,12
	.byte	'IS',0,4
	.word	990
	.byte	1,22,2,35,0,12
	.byte	'IO',0,4
	.word	990
	.byte	2,20,2,35,0,12
	.byte	'PRS',0,4
	.word	990
	.byte	2,18,2,35,0,12
	.byte	'S',0,4
	.word	990
	.byte	1,17,2,35,0,12
	.byte	'reserved_15',0,4
	.word	990
	.byte	12,5,2,35,0,12
	.byte	'SAV',0,4
	.word	990
	.byte	1,4,2,35,0,12
	.byte	'AV',0,4
	.word	990
	.byte	1,3,2,35,0,12
	.byte	'SV',0,4
	.word	990
	.byte	1,2,2,35,0,12
	.byte	'V',0,4
	.word	990
	.byte	1,1,2,35,0,12
	.byte	'C',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSW_Bits',0,21,147,4,3
	.word	66722
	.byte	11
	.byte	'_Ifx_CPU_SEGEN_Bits',0,21,150,4,16,4,12
	.byte	'ADFLIP',0,4
	.word	990
	.byte	8,24,2,35,0,12
	.byte	'ADTYPE',0,4
	.word	990
	.byte	2,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	990
	.byte	21,1,2,35,0,12
	.byte	'AE',0,4
	.word	990
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN_Bits',0,21,156,4,3
	.word	66965
	.byte	11
	.byte	'_Ifx_CPU_SMACON_Bits',0,21,159,4,16,4,12
	.byte	'PC',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'PT',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	990
	.byte	5,24,2,35,0,12
	.byte	'DC',0,4
	.word	990
	.byte	1,23,2,35,0,12
	.byte	'reserved_9',0,4
	.word	990
	.byte	1,22,2,35,0,12
	.byte	'DT',0,4
	.word	990
	.byte	1,21,2,35,0,12
	.byte	'reserved_11',0,4
	.word	990
	.byte	13,8,2,35,0,12
	.byte	'IODT',0,4
	.word	990
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	990
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON_Bits',0,21,171,4,3
	.word	67093
	.byte	11
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,21,174,4,16,4,12
	.byte	'EN',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,21,177,4,3
	.word	67334
	.byte	11
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,21,180,4,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,21,183,4,3
	.word	67417
	.byte	11
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,186,4,16,4,12
	.byte	'EN',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,189,4,3
	.word	67508
	.byte	11
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,192,4,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,195,4,3
	.word	67599
	.byte	11
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,21,198,4,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	5,3,2,35,0,12
	.byte	'ADDR',0,4
	.word	394
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,21,202,4,3
	.word	67698
	.byte	11
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,21,205,4,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	5,3,2,35,0,12
	.byte	'ADDR',0,4
	.word	394
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,21,209,4,3
	.word	67805
	.byte	11
	.byte	'_Ifx_CPU_SWEVT_Bits',0,21,212,4,16,4,12
	.byte	'EVTA',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'BBM',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'BOD',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'SUSP',0,4
	.word	990
	.byte	1,26,2,35,0,12
	.byte	'CNT',0,4
	.word	990
	.byte	2,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	990
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT_Bits',0,21,220,4,3
	.word	67912
	.byte	11
	.byte	'_Ifx_CPU_SYSCON_Bits',0,21,223,4,16,4,12
	.byte	'FCDSF',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'PROTEN',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'TPROTEN',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'IS',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'IT',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	990
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON_Bits',0,21,231,4,3
	.word	68066
	.byte	11
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,21,234,4,16,4,12
	.byte	'ASI',0,4
	.word	990
	.byte	5,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	990
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,21,238,4,3
	.word	68227
	.byte	11
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,21,241,4,16,4,12
	.byte	'TEXP0',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'TEXP1',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'TEXP2',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	990
	.byte	13,16,2,35,0,12
	.byte	'TTRAP',0,4
	.word	990
	.byte	1,15,2,35,0,12
	.byte	'reserved_17',0,4
	.word	990
	.byte	15,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON_Bits',0,21,249,4,3
	.word	68325
	.byte	11
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,21,252,4,16,4,12
	.byte	'Timer',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,21,255,4,3
	.word	68497
	.byte	11
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,21,130,5,16,4,12
	.byte	'ADDR',0,4
	.word	990
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR_Bits',0,21,133,5,3
	.word	68577
	.byte	11
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,21,136,5,16,4,12
	.byte	'EVTA',0,4
	.word	990
	.byte	3,29,2,35,0,12
	.byte	'BBM',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'BOD',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'SUSP',0,4
	.word	990
	.byte	1,26,2,35,0,12
	.byte	'CNT',0,4
	.word	990
	.byte	2,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	990
	.byte	4,20,2,35,0,12
	.byte	'TYP',0,4
	.word	990
	.byte	1,19,2,35,0,12
	.byte	'RNG',0,4
	.word	990
	.byte	1,18,2,35,0,12
	.byte	'reserved_14',0,4
	.word	990
	.byte	1,17,2,35,0,12
	.byte	'ASI_EN',0,4
	.word	990
	.byte	1,16,2,35,0,12
	.byte	'ASI',0,4
	.word	990
	.byte	5,11,2,35,0,12
	.byte	'reserved_21',0,4
	.word	990
	.byte	6,5,2,35,0,12
	.byte	'AST',0,4
	.word	990
	.byte	1,4,2,35,0,12
	.byte	'ALD',0,4
	.word	990
	.byte	1,3,2,35,0,12
	.byte	'reserved_29',0,4
	.word	990
	.byte	3,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT_Bits',0,21,153,5,3
	.word	68650
	.byte	11
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,21,156,5,16,4,12
	.byte	'T0',0,4
	.word	990
	.byte	1,31,2,35,0,12
	.byte	'T1',0,4
	.word	990
	.byte	1,30,2,35,0,12
	.byte	'T2',0,4
	.word	990
	.byte	1,29,2,35,0,12
	.byte	'T3',0,4
	.word	990
	.byte	1,28,2,35,0,12
	.byte	'T4',0,4
	.word	990
	.byte	1,27,2,35,0,12
	.byte	'T5',0,4
	.word	990
	.byte	1,26,2,35,0,12
	.byte	'T6',0,4
	.word	990
	.byte	1,25,2,35,0,12
	.byte	'T7',0,4
	.word	990
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	990
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,21,167,5,3
	.word	68968
	.byte	13,21,175,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60186
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_A',0,21,180,5,3
	.word	69163
	.byte	13,21,183,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60247
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BIV',0,21,188,5,3
	.word	69222
	.byte	13,21,191,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60326
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BTV',0,21,196,5,3
	.word	69283
	.byte	13,21,199,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60412
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT',0,21,204,5,3
	.word	69344
	.byte	13,21,207,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60501
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL',0,21,212,5,3
	.word	69406
	.byte	13,21,215,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60647
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT',0,21,220,5,3
	.word	69469
	.byte	13,21,223,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60774
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID',0,21,228,5,3
	.word	69533
	.byte	13,21,231,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60872
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L',0,21,236,5,3
	.word	69598
	.byte	13,21,239,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	60965
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U',0,21,244,5,3
	.word	69661
	.byte	13,21,247,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61058
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID',0,21,252,5,3
	.word	69724
	.byte	13,21,255,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61165
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE',0,21,132,6,3
	.word	69788
	.byte	13,21,135,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61252
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT',0,21,140,6,3
	.word	69850
	.byte	13,21,143,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61406
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID',0,21,148,6,3
	.word	69913
	.byte	13,21,151,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61500
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_D',0,21,156,6,3
	.word	69977
	.byte	13,21,159,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61563
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DATR',0,21,164,6,3
	.word	70036
	.byte	13,21,167,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61781
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR',0,21,172,6,3
	.word	70098
	.byte	13,21,175,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	61996
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR',0,21,180,6,3
	.word	70161
	.byte	13,21,183,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62090
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0',0,21,188,6,3
	.word	70225
	.byte	13,21,191,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62206
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2',0,21,196,6,3
	.word	70288
	.byte	13,21,199,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62307
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCX',0,21,204,6,3
	.word	70351
	.byte	13,21,207,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62400
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD',0,21,212,6,3
	.word	70412
	.byte	13,21,215,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62480
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR',0,21,220,6,3
	.word	70475
	.byte	13,21,223,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62549
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR',0,21,228,6,3
	.word	70538
	.byte	13,21,231,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62778
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DMS',0,21,236,6,3
	.word	70601
	.byte	13,21,239,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62871
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L',0,21,244,6,3
	.word	70662
	.byte	13,21,247,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	62966
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U',0,21,252,6,3
	.word	70725
	.byte	13,21,255,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	63061
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE',0,21,132,7,3
	.word	70788
	.byte	13,21,135,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	63151
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE',0,21,140,7,3
	.word	70850
	.byte	13,21,143,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	63241
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR',0,21,148,7,3
	.word	70912
	.byte	13,21,151,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	63565
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT',0,21,156,7,3
	.word	70974
	.byte	13,21,159,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	63719
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FCX',0,21,164,7,3
	.word	71037
	.byte	13,21,167,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	63825
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,21,172,7,3
	.word	71098
	.byte	13,21,175,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64174
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,21,180,7,3
	.word	71168
	.byte	13,21,183,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64334
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,21,188,7,3
	.word	71238
	.byte	13,21,191,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64415
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,21,196,7,3
	.word	71307
	.byte	13,21,199,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64502
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,21,204,7,3
	.word	71378
	.byte	13,21,207,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64589
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,21,212,7,3
	.word	71449
	.byte	13,21,215,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64676
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT',0,21,220,7,3
	.word	71520
	.byte	13,21,223,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64767
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICR',0,21,228,7,3
	.word	71582
	.byte	13,21,231,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64910
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ISP',0,21,236,7,3
	.word	71643
	.byte	13,21,239,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	64976
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_LCX',0,21,244,7,3
	.word	71704
	.byte	13,21,247,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65082
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT',0,21,252,7,3
	.word	71765
	.byte	13,21,255,7,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65175
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT',0,21,132,8,3
	.word	71828
	.byte	13,21,135,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65268
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT',0,21,140,8,3
	.word	71891
	.byte	13,21,143,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65361
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PC',0,21,148,8,3
	.word	71954
	.byte	13,21,151,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65446
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0',0,21,156,8,3
	.word	72014
	.byte	13,21,159,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65562
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1',0,21,164,8,3
	.word	72077
	.byte	13,21,167,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65673
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2',0,21,172,8,3
	.word	72140
	.byte	13,21,175,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65774
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI',0,21,180,8,3
	.word	72203
	.byte	13,21,183,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65904
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR',0,21,188,8,3
	.word	72265
	.byte	13,21,191,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	65973
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR',0,21,196,8,3
	.word	72328
	.byte	13,21,199,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	66202
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0',0,21,204,8,3
	.word	72391
	.byte	13,21,207,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	66315
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1',0,21,212,8,3
	.word	72453
	.byte	13,21,215,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	66428
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2',0,21,220,8,3
	.word	72515
	.byte	13,21,223,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	66519
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR',0,21,228,8,3
	.word	72577
	.byte	13,21,231,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	66722
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSW',0,21,236,8,3
	.word	72639
	.byte	13,21,239,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	66965
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN',0,21,244,8,3
	.word	72700
	.byte	13,21,247,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67093
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON',0,21,252,8,3
	.word	72763
	.byte	13,21,255,8,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67334
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA',0,21,132,9,3
	.word	72827
	.byte	13,21,135,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67417
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB',0,21,140,9,3
	.word	72897
	.byte	13,21,143,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67508
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,21,148,9,3
	.word	72967
	.byte	13,21,151,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67599
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,21,156,9,3
	.word	73041
	.byte	13,21,159,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67698
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,21,164,9,3
	.word	73115
	.byte	13,21,167,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67805
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,21,172,9,3
	.word	73185
	.byte	13,21,175,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	67912
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT',0,21,180,9,3
	.word	73255
	.byte	13,21,183,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	68066
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON',0,21,188,9,3
	.word	73318
	.byte	13,21,191,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	68227
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI',0,21,196,9,3
	.word	73382
	.byte	13,21,199,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	68325
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON',0,21,204,9,3
	.word	73448
	.byte	13,21,207,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	68497
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER',0,21,212,9,3
	.word	73513
	.byte	13,21,215,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	68577
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR',0,21,220,9,3
	.word	73580
	.byte	13,21,223,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	68650
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT',0,21,228,9,3
	.word	73644
	.byte	13,21,231,9,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	68968
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC',0,21,236,9,3
	.word	73708
	.byte	11
	.byte	'_Ifx_CPU_CPR',0,21,247,9,25,8,14
	.byte	'L',0
	.word	69598
	.byte	4,2,35,0,14
	.byte	'U',0
	.word	69661
	.byte	4,2,35,4,0,10
	.word	73774
	.byte	27
	.byte	'Ifx_CPU_CPR',0,21,251,9,3
	.word	73816
	.byte	11
	.byte	'_Ifx_CPU_DPR',0,21,254,9,25,8,14
	.byte	'L',0
	.word	70662
	.byte	4,2,35,0,14
	.byte	'U',0
	.word	70725
	.byte	4,2,35,4,0,10
	.word	73842
	.byte	27
	.byte	'Ifx_CPU_DPR',0,21,130,10,3
	.word	73884
	.byte	11
	.byte	'_Ifx_CPU_SPROT_RGN',0,21,133,10,25,16,14
	.byte	'LA',0
	.word	73115
	.byte	4,2,35,0,14
	.byte	'UA',0
	.word	73185
	.byte	4,2,35,4,14
	.byte	'ACCENA',0
	.word	72967
	.byte	4,2,35,8,14
	.byte	'ACCENB',0
	.word	73041
	.byte	4,2,35,12,0,10
	.word	73910
	.byte	27
	.byte	'Ifx_CPU_SPROT_RGN',0,21,139,10,3
	.word	73992
	.byte	18,12
	.word	73513
	.byte	19,2,0,11
	.byte	'_Ifx_CPU_TPS',0,21,142,10,25,16,14
	.byte	'CON',0
	.word	73448
	.byte	4,2,35,0,14
	.byte	'TIMER',0
	.word	74024
	.byte	12,2,35,4,0,10
	.word	74033
	.byte	27
	.byte	'Ifx_CPU_TPS',0,21,146,10,3
	.word	74081
	.byte	11
	.byte	'_Ifx_CPU_TR',0,21,149,10,25,8,14
	.byte	'EVT',0
	.word	73644
	.byte	4,2,35,0,14
	.byte	'ADR',0
	.word	73580
	.byte	4,2,35,4,0,10
	.word	74107
	.byte	27
	.byte	'Ifx_CPU_TR',0,21,153,10,3
	.word	74152
	.byte	18,176,32
	.word	591
	.byte	19,175,32,0,18,208,223,1
	.word	591
	.byte	19,207,223,1,0,18,248,1
	.word	591
	.byte	19,247,1,0,18,244,29
	.word	591
	.byte	19,243,29,0,18,188,3
	.word	591
	.byte	19,187,3,0,18,232,3
	.word	591
	.byte	19,231,3,0,18,252,23
	.word	591
	.byte	19,251,23,0,18,228,63
	.word	591
	.byte	19,227,63,0,18,128,1
	.word	73842
	.byte	19,15,0,10
	.word	74267
	.byte	18,128,31
	.word	591
	.byte	19,255,30,0,18,64
	.word	73774
	.byte	19,7,0,10
	.word	74293
	.byte	18,192,31
	.word	591
	.byte	19,191,31,0,18,16
	.word	69788
	.byte	19,3,0,18,16
	.word	70788
	.byte	19,3,0,18,16
	.word	70850
	.byte	19,3,0,18,208,7
	.word	591
	.byte	19,207,7,0,10
	.word	74033
	.byte	18,240,23
	.word	591
	.byte	19,239,23,0,18,64
	.word	74107
	.byte	19,7,0,10
	.word	74372
	.byte	18,192,23
	.word	591
	.byte	19,191,23,0,18,232,1
	.word	591
	.byte	19,231,1,0,18,180,1
	.word	591
	.byte	19,179,1,0,18,172,1
	.word	591
	.byte	19,171,1,0,18,64
	.word	69977
	.byte	19,15,0,18,64
	.word	591
	.byte	19,63,0,18,64
	.word	69163
	.byte	19,15,0,11
	.byte	'_Ifx_CPU',0,21,166,10,25,128,128,4,14
	.byte	'reserved_0',0
	.word	74177
	.byte	176,32,2,35,0,14
	.byte	'SEGEN',0
	.word	72700
	.byte	4,3,35,176,32,14
	.byte	'reserved_1034',0
	.word	74188
	.byte	208,223,1,3,35,180,32,14
	.byte	'TASK_ASI',0
	.word	73382
	.byte	4,4,35,132,128,2,14
	.byte	'reserved_8008',0
	.word	74201
	.byte	248,1,4,35,136,128,2,14
	.byte	'PMA0',0
	.word	72391
	.byte	4,4,35,128,130,2,14
	.byte	'PMA1',0
	.word	72453
	.byte	4,4,35,132,130,2,14
	.byte	'PMA2',0
	.word	72515
	.byte	4,4,35,136,130,2,14
	.byte	'reserved_810C',0
	.word	74212
	.byte	244,29,4,35,140,130,2,14
	.byte	'DCON2',0
	.word	70288
	.byte	4,4,35,128,160,2,14
	.byte	'reserved_9004',0
	.word	5148
	.byte	8,4,35,132,160,2,14
	.byte	'SMACON',0
	.word	72763
	.byte	4,4,35,140,160,2,14
	.byte	'DSTR',0
	.word	70912
	.byte	4,4,35,144,160,2,14
	.byte	'reserved_9014',0
	.word	3329
	.byte	4,4,35,148,160,2,14
	.byte	'DATR',0
	.word	70036
	.byte	4,4,35,152,160,2,14
	.byte	'DEADD',0
	.word	70412
	.byte	4,4,35,156,160,2,14
	.byte	'DIEAR',0
	.word	70475
	.byte	4,4,35,160,160,2,14
	.byte	'DIETR',0
	.word	70538
	.byte	4,4,35,164,160,2,14
	.byte	'reserved_9028',0
	.word	4519
	.byte	24,4,35,168,160,2,14
	.byte	'DCON0',0
	.word	70225
	.byte	4,4,35,192,160,2,14
	.byte	'reserved_9044',0
	.word	74223
	.byte	188,3,4,35,196,160,2,14
	.byte	'PSTR',0
	.word	72577
	.byte	4,4,35,128,164,2,14
	.byte	'PCON1',0
	.word	72077
	.byte	4,4,35,132,164,2,14
	.byte	'PCON2',0
	.word	72140
	.byte	4,4,35,136,164,2,14
	.byte	'PCON0',0
	.word	72014
	.byte	4,4,35,140,164,2,14
	.byte	'PIEAR',0
	.word	72265
	.byte	4,4,35,144,164,2,14
	.byte	'PIETR',0
	.word	72328
	.byte	4,4,35,148,164,2,14
	.byte	'reserved_9218',0
	.word	74234
	.byte	232,3,4,35,152,164,2,14
	.byte	'COMPAT',0
	.word	69469
	.byte	4,4,35,128,168,2,14
	.byte	'reserved_9404',0
	.word	74245
	.byte	252,23,4,35,132,168,2,14
	.byte	'FPU_TRAP_CON',0
	.word	71098
	.byte	4,4,35,128,192,2,14
	.byte	'FPU_TRAP_PC',0
	.word	71238
	.byte	4,4,35,132,192,2,14
	.byte	'FPU_TRAP_OPC',0
	.word	71168
	.byte	4,4,35,136,192,2,14
	.byte	'reserved_A00C',0
	.word	3329
	.byte	4,4,35,140,192,2,14
	.byte	'FPU_TRAP_SRC1',0
	.word	71307
	.byte	4,4,35,144,192,2,14
	.byte	'FPU_TRAP_SRC2',0
	.word	71378
	.byte	4,4,35,148,192,2,14
	.byte	'FPU_TRAP_SRC3',0
	.word	71449
	.byte	4,4,35,152,192,2,14
	.byte	'reserved_A01C',0
	.word	74256
	.byte	228,63,4,35,156,192,2,14
	.byte	'DPR',0
	.word	74277
	.byte	128,1,4,35,128,128,3,14
	.byte	'reserved_C080',0
	.word	74282
	.byte	128,31,4,35,128,129,3,14
	.byte	'CPR',0
	.word	74302
	.byte	64,4,35,128,160,3,14
	.byte	'reserved_D040',0
	.word	74307
	.byte	192,31,4,35,192,160,3,14
	.byte	'CPXE',0
	.word	74318
	.byte	16,4,35,128,192,3,14
	.byte	'DPRE',0
	.word	74327
	.byte	16,4,35,144,192,3,14
	.byte	'DPWE',0
	.word	74336
	.byte	16,4,35,160,192,3,14
	.byte	'reserved_E030',0
	.word	74345
	.byte	208,7,4,35,176,192,3,14
	.byte	'TPS',0
	.word	74356
	.byte	16,4,35,128,200,3,14
	.byte	'reserved_E410',0
	.word	74361
	.byte	240,23,4,35,144,200,3,14
	.byte	'TR',0
	.word	74381
	.byte	64,4,35,128,224,3,14
	.byte	'reserved_F040',0
	.word	74386
	.byte	192,23,4,35,192,224,3,14
	.byte	'CCTRL',0
	.word	69406
	.byte	4,4,35,128,248,3,14
	.byte	'CCNT',0
	.word	69344
	.byte	4,4,35,132,248,3,14
	.byte	'ICNT',0
	.word	71520
	.byte	4,4,35,136,248,3,14
	.byte	'M1CNT',0
	.word	71765
	.byte	4,4,35,140,248,3,14
	.byte	'M2CNT',0
	.word	71828
	.byte	4,4,35,144,248,3,14
	.byte	'M3CNT',0
	.word	71891
	.byte	4,4,35,148,248,3,14
	.byte	'reserved_FC18',0
	.word	74397
	.byte	232,1,4,35,152,248,3,14
	.byte	'DBGSR',0
	.word	70098
	.byte	4,4,35,128,250,3,14
	.byte	'reserved_FD04',0
	.word	3329
	.byte	4,4,35,132,250,3,14
	.byte	'EXEVT',0
	.word	70974
	.byte	4,4,35,136,250,3,14
	.byte	'CREVT',0
	.word	69850
	.byte	4,4,35,140,250,3,14
	.byte	'SWEVT',0
	.word	73255
	.byte	4,4,35,144,250,3,14
	.byte	'reserved_FD14',0
	.word	58136
	.byte	28,4,35,148,250,3,14
	.byte	'TRIG_ACC',0
	.word	73708
	.byte	4,4,35,176,250,3,14
	.byte	'reserved_FD34',0
	.word	5488
	.byte	12,4,35,180,250,3,14
	.byte	'DMS',0
	.word	70601
	.byte	4,4,35,192,250,3,14
	.byte	'DCX',0
	.word	70351
	.byte	4,4,35,196,250,3,14
	.byte	'DBGTCR',0
	.word	70161
	.byte	4,4,35,200,250,3,14
	.byte	'reserved_FD4C',0
	.word	74408
	.byte	180,1,4,35,204,250,3,14
	.byte	'PCXI',0
	.word	72203
	.byte	4,4,35,128,252,3,14
	.byte	'PSW',0
	.word	72639
	.byte	4,4,35,132,252,3,14
	.byte	'PC',0
	.word	71954
	.byte	4,4,35,136,252,3,14
	.byte	'reserved_FE0C',0
	.word	5148
	.byte	8,4,35,140,252,3,14
	.byte	'SYSCON',0
	.word	73318
	.byte	4,4,35,148,252,3,14
	.byte	'CPU_ID',0
	.word	69724
	.byte	4,4,35,152,252,3,14
	.byte	'CORE_ID',0
	.word	69533
	.byte	4,4,35,156,252,3,14
	.byte	'BIV',0
	.word	69222
	.byte	4,4,35,160,252,3,14
	.byte	'BTV',0
	.word	69283
	.byte	4,4,35,164,252,3,14
	.byte	'ISP',0
	.word	71643
	.byte	4,4,35,168,252,3,14
	.byte	'ICR',0
	.word	71582
	.byte	4,4,35,172,252,3,14
	.byte	'reserved_FE30',0
	.word	5148
	.byte	8,4,35,176,252,3,14
	.byte	'FCX',0
	.word	71037
	.byte	4,4,35,184,252,3,14
	.byte	'LCX',0
	.word	71704
	.byte	4,4,35,188,252,3,14
	.byte	'reserved_FE40',0
	.word	28687
	.byte	16,4,35,192,252,3,14
	.byte	'CUS_ID',0
	.word	69913
	.byte	4,4,35,208,252,3,14
	.byte	'reserved_FE54',0
	.word	74419
	.byte	172,1,4,35,212,252,3,14
	.byte	'D',0
	.word	74430
	.byte	64,4,35,128,254,3,14
	.byte	'reserved_FF40',0
	.word	74439
	.byte	64,4,35,192,254,3,14
	.byte	'A',0
	.word	74448
	.byte	64,4,35,128,255,3,14
	.byte	'reserved_FFC0',0
	.word	74439
	.byte	64,4,35,192,255,3,0,10
	.word	74457
	.byte	27
	.byte	'Ifx_CPU',0,21,130,11,3
	.word	76248
	.byte	15,8,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,27
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	76270
	.byte	27
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	1937
	.byte	11
	.byte	'_Ifx_STM_ACCEN0_Bits',0,22,45,16,4,12
	.byte	'EN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_STM_ACCEN0_Bits',0,22,79,3
	.word	76368
	.byte	11
	.byte	'_Ifx_STM_ACCEN1_Bits',0,22,82,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1_Bits',0,22,85,3
	.word	76925
	.byte	11
	.byte	'_Ifx_STM_CAP_Bits',0,22,88,16,4,12
	.byte	'STMCAP63_32',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAP_Bits',0,22,91,3
	.word	77002
	.byte	11
	.byte	'_Ifx_STM_CAPSV_Bits',0,22,94,16,4,12
	.byte	'STMCAP63_32',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV_Bits',0,22,97,3
	.word	77074
	.byte	11
	.byte	'_Ifx_STM_CLC_Bits',0,22,100,16,4,12
	.byte	'DISR',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'DISS',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EDIS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_CLC_Bits',0,22,107,3
	.word	77150
	.byte	11
	.byte	'_Ifx_STM_CMCON_Bits',0,22,110,16,4,12
	.byte	'MSIZE0',0,1
	.word	591
	.byte	5,3,2,35,0,12
	.byte	'reserved_5',0,1
	.word	591
	.byte	3,0,2,35,0,12
	.byte	'MSTART0',0,1
	.word	591
	.byte	5,3,2,35,1,12
	.byte	'reserved_13',0,1
	.word	591
	.byte	3,0,2,35,1,12
	.byte	'MSIZE1',0,1
	.word	591
	.byte	5,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	591
	.byte	3,0,2,35,2,12
	.byte	'MSTART1',0,1
	.word	591
	.byte	5,3,2,35,3,12
	.byte	'reserved_29',0,1
	.word	591
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_STM_CMCON_Bits',0,22,120,3
	.word	77291
	.byte	11
	.byte	'_Ifx_STM_CMP_Bits',0,22,123,16,4,12
	.byte	'CMPVAL',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CMP_Bits',0,22,126,3
	.word	77509
	.byte	11
	.byte	'_Ifx_STM_ICR_Bits',0,22,129,1,16,4,12
	.byte	'CMP0EN',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'CMP0IR',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'CMP0OS',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'CMP1EN',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'CMP1IR',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'CMP1OS',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,4
	.word	394
	.byte	25,0,2,35,0,0,27
	.byte	'Ifx_STM_ICR_Bits',0,22,139,1,3
	.word	77576
	.byte	11
	.byte	'_Ifx_STM_ID_Bits',0,22,142,1,16,4,12
	.byte	'MODREV',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_STM_ID_Bits',0,22,147,1,3
	.word	77779
	.byte	11
	.byte	'_Ifx_STM_ISCR_Bits',0,22,150,1,16,4,12
	.byte	'CMP0IRR',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'CMP0IRS',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'CMP1IRR',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'CMP1IRS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_ISCR_Bits',0,22,157,1,3
	.word	77886
	.byte	11
	.byte	'_Ifx_STM_KRST0_Bits',0,22,160,1,16,4,12
	.byte	'RST',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'RSTSTAT',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	394
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST0_Bits',0,22,165,1,3
	.word	78037
	.byte	11
	.byte	'_Ifx_STM_KRST1_Bits',0,22,168,1,16,4,12
	.byte	'RST',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	394
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST1_Bits',0,22,172,1,3
	.word	78148
	.byte	11
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,22,175,1,16,4,12
	.byte	'CLR',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	394
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR_Bits',0,22,179,1,3
	.word	78240
	.byte	11
	.byte	'_Ifx_STM_OCS_Bits',0,22,182,1,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	24,8,2,35,0,12
	.byte	'SUS',0,1
	.word	591
	.byte	4,4,2,35,3,12
	.byte	'SUS_P',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'SUSSTA',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	591
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_STM_OCS_Bits',0,22,189,1,3
	.word	78336
	.byte	11
	.byte	'_Ifx_STM_TIM0_Bits',0,22,192,1,16,4,12
	.byte	'STM31_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0_Bits',0,22,195,1,3
	.word	78482
	.byte	11
	.byte	'_Ifx_STM_TIM0SV_Bits',0,22,198,1,16,4,12
	.byte	'STM31_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV_Bits',0,22,201,1,3
	.word	78554
	.byte	11
	.byte	'_Ifx_STM_TIM1_Bits',0,22,204,1,16,4,12
	.byte	'STM35_4',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM1_Bits',0,22,207,1,3
	.word	78630
	.byte	11
	.byte	'_Ifx_STM_TIM2_Bits',0,22,210,1,16,4,12
	.byte	'STM39_8',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM2_Bits',0,22,213,1,3
	.word	78702
	.byte	11
	.byte	'_Ifx_STM_TIM3_Bits',0,22,216,1,16,4,12
	.byte	'STM43_12',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM3_Bits',0,22,219,1,3
	.word	78774
	.byte	11
	.byte	'_Ifx_STM_TIM4_Bits',0,22,222,1,16,4,12
	.byte	'STM47_16',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM4_Bits',0,22,225,1,3
	.word	78847
	.byte	11
	.byte	'_Ifx_STM_TIM5_Bits',0,22,228,1,16,4,12
	.byte	'STM51_20',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM5_Bits',0,22,231,1,3
	.word	78920
	.byte	11
	.byte	'_Ifx_STM_TIM6_Bits',0,22,234,1,16,4,12
	.byte	'STM63_32',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM6_Bits',0,22,237,1,3
	.word	78993
	.byte	13,22,245,1,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	76368
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN0',0,22,250,1,3
	.word	79066
	.byte	13,22,253,1,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	76925
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1',0,22,130,2,3
	.word	79130
	.byte	13,22,133,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77002
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAP',0,22,138,2,3
	.word	79194
	.byte	13,22,141,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77074
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV',0,22,146,2,3
	.word	79255
	.byte	13,22,149,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77150
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CLC',0,22,154,2,3
	.word	79318
	.byte	13,22,157,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77291
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMCON',0,22,162,2,3
	.word	79379
	.byte	13,22,165,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77509
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMP',0,22,170,2,3
	.word	79442
	.byte	13,22,173,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77576
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ICR',0,22,178,2,3
	.word	79503
	.byte	13,22,181,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77779
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ID',0,22,186,2,3
	.word	79564
	.byte	13,22,189,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	77886
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ISCR',0,22,194,2,3
	.word	79624
	.byte	13,22,197,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78037
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST0',0,22,202,2,3
	.word	79686
	.byte	13,22,205,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78148
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST1',0,22,210,2,3
	.word	79749
	.byte	13,22,213,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78240
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR',0,22,218,2,3
	.word	79812
	.byte	13,22,221,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78336
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_OCS',0,22,226,2,3
	.word	79877
	.byte	13,22,229,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78482
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0',0,22,234,2,3
	.word	79938
	.byte	13,22,237,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78554
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV',0,22,242,2,3
	.word	80000
	.byte	13,22,245,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78630
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM1',0,22,250,2,3
	.word	80064
	.byte	13,22,253,2,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78702
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM2',0,22,130,3,3
	.word	80126
	.byte	13,22,133,3,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78774
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM3',0,22,138,3,3
	.word	80188
	.byte	13,22,141,3,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78847
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM4',0,22,146,3,3
	.word	80250
	.byte	13,22,149,3,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78920
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM5',0,22,154,3,3
	.word	80312
	.byte	13,22,157,3,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	78993
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM6',0,22,162,3,3
	.word	80374
	.byte	15,7,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,27
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	80436
	.byte	25,7,160,1,9,6,14
	.byte	'counter',0
	.word	2092
	.byte	4,2,35,0,14
	.byte	'overlfow',0
	.word	591
	.byte	1,2,35,4,0,27
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	80525
	.byte	25,7,172,1,9,32,14
	.byte	'instruction',0
	.word	80525
	.byte	6,2,35,0,14
	.byte	'clock',0
	.word	80525
	.byte	6,2,35,6,14
	.byte	'counter1',0
	.word	80525
	.byte	6,2,35,12,14
	.byte	'counter2',0
	.word	80525
	.byte	6,2,35,18,14
	.byte	'counter3',0
	.word	80525
	.byte	6,2,35,24,0,27
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	80591
	.byte	11
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,23,45,16,4,12
	.byte	'EN0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,23,79,3
	.word	80709
	.byte	11
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,23,82,16,4,12
	.byte	'reserved_0',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,23,85,3
	.word	81270
	.byte	11
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,23,88,16,4,12
	.byte	'SEL',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'CLR',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'DIS',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'reserved_10',0,4
	.word	394
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,23,95,3
	.word	81351
	.byte	11
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,23,98,16,4,12
	.byte	'VLD0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'VLD1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'VLD2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'VLD3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'VLD4',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'VLD5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'VLD6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'VLD7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'VLD8',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'VLD9',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'reserved_10',0,4
	.word	394
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,23,111,3
	.word	81504
	.byte	11
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,23,114,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	5,3,2,35,0,12
	.byte	'ADDR',0,4
	.word	394
	.byte	19,8,2,35,0,12
	.byte	'ERR',0,1
	.word	591
	.byte	6,2,2,35,3,12
	.byte	'VLD',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'CLR',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,23,121,3
	.word	81752
	.byte	11
	.byte	'_Ifx_FLASH_COMM0_Bits',0,23,124,16,4,12
	.byte	'STATUS',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	394
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0_Bits',0,23,128,1,3
	.word	81898
	.byte	11
	.byte	'_Ifx_FLASH_COMM1_Bits',0,23,131,1,16,4,12
	.byte	'STATUS',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'DATA',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM1_Bits',0,23,136,1,3
	.word	81996
	.byte	11
	.byte	'_Ifx_FLASH_COMM2_Bits',0,23,139,1,16,4,12
	.byte	'STATUS',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'DATA',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM2_Bits',0,23,144,1,3
	.word	82112
	.byte	11
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,23,147,1,16,4,12
	.byte	'RCODE',0,4
	.word	394
	.byte	22,10,2,35,0,12
	.byte	'reserved_22',0,2
	.word	1142
	.byte	8,2,2,35,2,12
	.byte	'EDCERRINJ',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'ECCORDIS',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRD_Bits',0,23,153,1,3
	.word	82228
	.byte	11
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,23,156,1,16,4,12
	.byte	'RCODE',0,4
	.word	394
	.byte	22,10,2,35,0,12
	.byte	'reserved_22',0,2
	.word	1142
	.byte	8,2,2,35,2,12
	.byte	'EDCERRINJ',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'ECCORDIS',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRP_Bits',0,23,162,1,3
	.word	82368
	.byte	11
	.byte	'_Ifx_FLASH_ECCW_Bits',0,23,165,1,16,4,12
	.byte	'WCODE',0,4
	.word	394
	.byte	22,10,2,35,0,12
	.byte	'reserved_22',0,2
	.word	1142
	.byte	8,2,2,35,2,12
	.byte	'DECENCDIS',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'PECENCDIS',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCW_Bits',0,23,171,1,3
	.word	82508
	.byte	11
	.byte	'_Ifx_FLASH_FCON_Bits',0,23,174,1,16,4,12
	.byte	'WSPFLASH',0,1
	.word	591
	.byte	4,4,2,35,0,12
	.byte	'WSECPF',0,1
	.word	591
	.byte	2,2,2,35,0,12
	.byte	'WSDFLASH',0,2
	.word	1142
	.byte	6,4,2,35,0,12
	.byte	'WSECDF',0,1
	.word	591
	.byte	3,1,2,35,1,12
	.byte	'IDLE',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'ESLDIS',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'SLEEP',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'NSAFECC',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'STALL',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'RES21',0,1
	.word	591
	.byte	2,2,2,35,2,12
	.byte	'RES23',0,1
	.word	591
	.byte	2,0,2,35,2,12
	.byte	'VOPERM',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'SQERM',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'PROERM',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'reserved_27',0,1
	.word	591
	.byte	3,2,2,35,3,12
	.byte	'PR5V',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'EOBM',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FCON_Bits',0,23,193,1,3
	.word	82647
	.byte	11
	.byte	'_Ifx_FLASH_FPRO_Bits',0,23,196,1,16,4,12
	.byte	'PROINP',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'PRODISP',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'PROIND',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'PRODISD',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'PROINHSMCOTP',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'RES5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'PROINOTP',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'RES7',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'PROINDBG',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'PRODISDBG',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'PROINHSM',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,1
	.word	591
	.byte	5,0,2,35,1,12
	.byte	'DCFP',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'DDFP',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'DDFPX',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'reserved_19',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'DDFD',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'ENPE',0,1
	.word	591
	.byte	2,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	591
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FPRO_Bits',0,23,218,1,3
	.word	83009
	.byte	11
	.byte	'_Ifx_FLASH_FSR_Bits',0,23,221,1,16,4,12
	.byte	'FABUSY',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'D0BUSY',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'RES1',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'P0BUSY',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'P1BUSY',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'RES5',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'RES6',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'PROG',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'ERASE',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'PFPAGE',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'DFPAGE',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'OPER',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'SQER',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'PROER',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'PFSBER',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'PFDBER',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'PFMBER',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'RES17',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'DFSBER',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'DFDBER',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'DFTBER',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'DFMBER',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'SRIADDERR',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'reserved_23',0,2
	.word	1142
	.byte	2,7,2,35,2,12
	.byte	'PVER',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'EVER',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'SPND',0,1
	.word	591
	.byte	1,4,2,35,3,12
	.byte	'SLM',0,1
	.word	591
	.byte	1,3,2,35,3,12
	.byte	'reserved_29',0,1
	.word	591
	.byte	1,2,2,35,3,12
	.byte	'ORIER',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'reserved_31',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FSR_Bits',0,23,254,1,3
	.word	83450
	.byte	11
	.byte	'_Ifx_FLASH_ID_Bits',0,23,129,2,16,4,12
	.byte	'MODREV',0,1
	.word	591
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	591
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_ID_Bits',0,23,134,2,3
	.word	84056
	.byte	11
	.byte	'_Ifx_FLASH_MARD_Bits',0,23,137,2,16,4,12
	.byte	'HMARGIN',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SELD0',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'SPND',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'SPNDERR',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'reserved_5',0,2
	.word	1142
	.byte	10,1,2,35,0,12
	.byte	'TRAPDIS',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARD_Bits',0,23,147,2,3
	.word	84167
	.byte	11
	.byte	'_Ifx_FLASH_MARP_Bits',0,23,150,2,16,4,12
	.byte	'SELP0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SELP1',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'RES2',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'RES3',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,2
	.word	1142
	.byte	11,1,2,35,0,12
	.byte	'TRAPDIS',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARP_Bits',0,23,159,2,3
	.word	84381
	.byte	11
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,23,162,2,16,4,12
	.byte	'L',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'NSAFECC',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'RAMIN',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'RAMINSEL',0,1
	.word	591
	.byte	4,0,2,35,0,12
	.byte	'OSCCFG',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'MODE',0,1
	.word	591
	.byte	2,5,2,35,1,12
	.byte	'APREN',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'CAP0EN',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'CAP1EN',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'CAP2EN',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'CAP3EN',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'ESR0CNT',0,2
	.word	1142
	.byte	12,4,2,35,2,12
	.byte	'RES29',0,1
	.word	591
	.byte	2,2,2,35,3,12
	.byte	'RES30',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'RPRO',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCOND_Bits',0,23,179,2,3
	.word	84568
	.byte	11
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,23,182,2,16,4,12
	.byte	'OCDSDIS',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'DBGIFLCK',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'EDM',0,1
	.word	591
	.byte	2,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	394
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,23,188,2,3
	.word	84892
	.byte	11
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,23,191,2,16,4,12
	.byte	'HSMDBGDIS',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'DBGIFLCK',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'TSTIFLCK',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'HSMTSTDIS',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'RES15',0,2
	.word	1142
	.byte	12,0,2,35,0,12
	.byte	'reserved_16',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,23,199,2,3
	.word	85035
	.byte	11
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,202,2,16,4,12
	.byte	'HSMBOOTEN',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'SSWWAIT',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'HSMDX',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'HSM6X',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'HSM16X',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'HSM17X',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'S6ROM',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'HSMENPINS',0,2
	.word	1142
	.byte	2,7,2,35,0,12
	.byte	'HSMENRES',0,1
	.word	591
	.byte	2,5,2,35,1,12
	.byte	'DESTDBG',0,1
	.word	591
	.byte	2,3,2,35,1,12
	.byte	'BLKFLAN',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'reserved_14',0,1
	.word	591
	.byte	2,0,2,35,1,12
	.byte	'S16ROM',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'S17ROM',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'reserved_18',0,2
	.word	1142
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,219,2,3
	.word	85224
	.byte	11
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,23,222,2,16,4,12
	.byte	'S0ROM',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'S1ROM',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'S2ROM',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'S3ROM',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'S4ROM',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'S5ROM',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'S6ROM',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'S7ROM',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'S8ROM',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'S9ROM',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'S10ROM',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'S11ROM',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'S12ROM',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'S13ROM',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'S14ROM',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'S15ROM',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'S16ROM',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'S17ROM',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'S18ROM',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'S19ROM',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'S20ROM',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'S21ROM',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'S22ROM',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'S23ROM',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'S24ROM',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'S25ROM',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'S26ROM',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'reserved_27',0,1
	.word	591
	.byte	2,3,2,35,3,12
	.byte	'BML',0,1
	.word	591
	.byte	2,1,2,35,3,12
	.byte	'TP',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,23,254,2,3
	.word	85587
	.byte	11
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,23,129,3,16,4,12
	.byte	'S0L',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'S1L',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'S2L',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'S3L',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'S4L',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'S5L',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'S6L',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'S7L',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'S8L',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'S9L',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'S10L',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'S11L',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'S12L',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'S13L',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'S14L',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'S15L',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'S16L',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'S17L',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'S18L',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'S19L',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'S20L',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'S21L',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'S22L',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'S23L',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'S24L',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'S25L',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'S26L',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'reserved_27',0,1
	.word	591
	.byte	4,1,2,35,3,12
	.byte	'RPRO',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONP_Bits',0,23,160,3,3
	.word	86182
	.byte	11
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,23,163,3,16,4,12
	.byte	'S0WOP',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'S1WOP',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'S2WOP',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'S3WOP',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'S4WOP',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'S5WOP',0,1
	.word	591
	.byte	1,2,2,35,0,12
	.byte	'S6WOP',0,1
	.word	591
	.byte	1,1,2,35,0,12
	.byte	'S7WOP',0,1
	.word	591
	.byte	1,0,2,35,0,12
	.byte	'S8WOP',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'S9WOP',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'S10WOP',0,1
	.word	591
	.byte	1,5,2,35,1,12
	.byte	'S11WOP',0,1
	.word	591
	.byte	1,4,2,35,1,12
	.byte	'S12WOP',0,1
	.word	591
	.byte	1,3,2,35,1,12
	.byte	'S13WOP',0,1
	.word	591
	.byte	1,2,2,35,1,12
	.byte	'S14WOP',0,1
	.word	591
	.byte	1,1,2,35,1,12
	.byte	'S15WOP',0,1
	.word	591
	.byte	1,0,2,35,1,12
	.byte	'S16WOP',0,1
	.word	591
	.byte	1,7,2,35,2,12
	.byte	'S17WOP',0,1
	.word	591
	.byte	1,6,2,35,2,12
	.byte	'S18WOP',0,1
	.word	591
	.byte	1,5,2,35,2,12
	.byte	'S19WOP',0,1
	.word	591
	.byte	1,4,2,35,2,12
	.byte	'S20WOP',0,1
	.word	591
	.byte	1,3,2,35,2,12
	.byte	'S21WOP',0,1
	.word	591
	.byte	1,2,2,35,2,12
	.byte	'S22WOP',0,1
	.word	591
	.byte	1,1,2,35,2,12
	.byte	'S23WOP',0,1
	.word	591
	.byte	1,0,2,35,2,12
	.byte	'S24WOP',0,1
	.word	591
	.byte	1,7,2,35,3,12
	.byte	'S25WOP',0,1
	.word	591
	.byte	1,6,2,35,3,12
	.byte	'S26WOP',0,1
	.word	591
	.byte	1,5,2,35,3,12
	.byte	'reserved_27',0,1
	.word	591
	.byte	4,1,2,35,3,12
	.byte	'DATM',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,23,194,3,3
	.word	86706
	.byte	11
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,23,197,3,16,4,12
	.byte	'TAG',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	394
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,23,201,3,3
	.word	87288
	.byte	11
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,23,204,3,16,4,12
	.byte	'TAG',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	394
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,23,208,3,3
	.word	87390
	.byte	11
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,23,211,3,16,4,12
	.byte	'TAG',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,4
	.word	394
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,23,215,3,3
	.word	87492
	.byte	11
	.byte	'_Ifx_FLASH_RRAD_Bits',0,23,218,3,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	3,5,2,35,0,12
	.byte	'ADD',0,4
	.word	394
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD_Bits',0,23,222,3,3
	.word	87594
	.byte	11
	.byte	'_Ifx_FLASH_RRCT_Bits',0,23,225,3,16,4,12
	.byte	'STRT',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'STP',0,1
	.word	591
	.byte	1,6,2,35,0,12
	.byte	'BUSY',0,1
	.word	591
	.byte	1,5,2,35,0,12
	.byte	'DONE',0,1
	.word	591
	.byte	1,4,2,35,0,12
	.byte	'ERR',0,1
	.word	591
	.byte	1,3,2,35,0,12
	.byte	'reserved_5',0,1
	.word	591
	.byte	3,0,2,35,0,12
	.byte	'EOBM',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'reserved_9',0,1
	.word	591
	.byte	7,0,2,35,1,12
	.byte	'CNT',0,2
	.word	1142
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_RRCT_Bits',0,23,236,3,3
	.word	87688
	.byte	11
	.byte	'_Ifx_FLASH_RRD0_Bits',0,23,239,3,16,4,12
	.byte	'DATA',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0_Bits',0,23,242,3,3
	.word	87898
	.byte	11
	.byte	'_Ifx_FLASH_RRD1_Bits',0,23,245,3,16,4,12
	.byte	'DATA',0,4
	.word	394
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1_Bits',0,23,248,3,3
	.word	87971
	.byte	11
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,23,251,3,16,4,12
	.byte	'SEL',0,1
	.word	591
	.byte	6,2,2,35,0,12
	.byte	'reserved_6',0,1
	.word	591
	.byte	2,0,2,35,0,12
	.byte	'CLR',0,1
	.word	591
	.byte	1,7,2,35,1,12
	.byte	'DIS',0,1
	.word	591
	.byte	1,6,2,35,1,12
	.byte	'reserved_10',0,4
	.word	394
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,23,130,4,3
	.word	88044
	.byte	11
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,23,133,4,16,4,12
	.byte	'VLD0',0,1
	.word	591
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	394
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,23,137,4,3
	.word	88199
	.byte	11
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,23,140,4,16,4,12
	.byte	'reserved_0',0,1
	.word	591
	.byte	5,3,2,35,0,12
	.byte	'ADDR',0,4
	.word	394
	.byte	19,8,2,35,0,12
	.byte	'ERR',0,1
	.word	591
	.byte	6,2,2,35,3,12
	.byte	'VLD',0,1
	.word	591
	.byte	1,1,2,35,3,12
	.byte	'CLR',0,1
	.word	591
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,23,147,4,3
	.word	88304
	.byte	13,23,155,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	80709
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN0',0,23,160,4,3
	.word	88452
	.byte	13,23,163,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	81270
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1',0,23,168,4,3
	.word	88518
	.byte	13,23,171,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	81351
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG',0,23,176,4,3
	.word	88584
	.byte	13,23,179,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	81504
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT',0,23,184,4,3
	.word	88652
	.byte	13,23,187,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	81752
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_TOP',0,23,192,4,3
	.word	88721
	.byte	13,23,195,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	81898
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0',0,23,200,4,3
	.word	88789
	.byte	13,23,203,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	81996
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM1',0,23,208,4,3
	.word	88854
	.byte	13,23,211,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	82112
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM2',0,23,216,4,3
	.word	88919
	.byte	13,23,219,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	82228
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRD',0,23,224,4,3
	.word	88984
	.byte	13,23,227,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	82368
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRP',0,23,232,4,3
	.word	89049
	.byte	13,23,235,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	82508
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCW',0,23,240,4,3
	.word	89114
	.byte	13,23,243,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	82647
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FCON',0,23,248,4,3
	.word	89178
	.byte	13,23,251,4,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	83009
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FPRO',0,23,128,5,3
	.word	89242
	.byte	13,23,131,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	83450
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FSR',0,23,136,5,3
	.word	89306
	.byte	13,23,139,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	84056
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ID',0,23,144,5,3
	.word	89369
	.byte	13,23,147,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	84167
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARD',0,23,152,5,3
	.word	89431
	.byte	13,23,155,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	84381
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARP',0,23,160,5,3
	.word	89495
	.byte	13,23,163,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	84568
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCOND',0,23,168,5,3
	.word	89559
	.byte	13,23,171,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	84892
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG',0,23,176,5,3
	.word	89626
	.byte	13,23,179,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	85035
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSM',0,23,184,5,3
	.word	89695
	.byte	13,23,187,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	85224
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,23,192,5,3
	.word	89764
	.byte	13,23,195,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	85587
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONOTP',0,23,200,5,3
	.word	89837
	.byte	13,23,203,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	86182
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONP',0,23,208,5,3
	.word	89906
	.byte	13,23,211,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	86706
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONWOP',0,23,216,5,3
	.word	89973
	.byte	13,23,219,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	87288
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0',0,23,224,5,3
	.word	90042
	.byte	13,23,227,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	87390
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1',0,23,232,5,3
	.word	90110
	.byte	13,23,235,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	87492
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2',0,23,240,5,3
	.word	90178
	.byte	13,23,243,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	87594
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD',0,23,248,5,3
	.word	90246
	.byte	13,23,251,5,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	87688
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRCT',0,23,128,6,3
	.word	90310
	.byte	13,23,131,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	87898
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0',0,23,136,6,3
	.word	90374
	.byte	13,23,139,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	87971
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1',0,23,144,6,3
	.word	90438
	.byte	13,23,147,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	88044
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG',0,23,152,6,3
	.word	90502
	.byte	13,23,155,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	88199
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT',0,23,160,6,3
	.word	90570
	.byte	13,23,163,6,9,4,14
	.byte	'U',0
	.word	394
	.byte	4,2,35,0,14
	.byte	'I',0
	.word	584
	.byte	4,2,35,0,14
	.byte	'B',0
	.word	88304
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_TOP',0,23,168,6,3
	.word	90639
	.byte	11
	.byte	'_Ifx_FLASH_CBAB',0,23,179,6,25,12,14
	.byte	'CFG',0
	.word	88584
	.byte	4,2,35,0,14
	.byte	'STAT',0
	.word	88652
	.byte	4,2,35,4,14
	.byte	'TOP',0
	.word	88721
	.byte	4,2,35,8,0,10
	.word	90707
	.byte	27
	.byte	'Ifx_FLASH_CBAB',0,23,184,6,3
	.word	90770
	.byte	11
	.byte	'_Ifx_FLASH_RDB',0,23,187,6,25,12,14
	.byte	'CFG0',0
	.word	90042
	.byte	4,2,35,0,14
	.byte	'CFG1',0
	.word	90110
	.byte	4,2,35,4,14
	.byte	'CFG2',0
	.word	90178
	.byte	4,2,35,8,0,10
	.word	90799
	.byte	27
	.byte	'Ifx_FLASH_RDB',0,23,192,6,3
	.word	90863
	.byte	11
	.byte	'_Ifx_FLASH_UBAB',0,23,195,6,25,12,14
	.byte	'CFG',0
	.word	90502
	.byte	4,2,35,0,14
	.byte	'STAT',0
	.word	90570
	.byte	4,2,35,4,14
	.byte	'TOP',0
	.word	90639
	.byte	4,2,35,8,0,10
	.word	90891
	.byte	27
	.byte	'Ifx_FLASH_UBAB',0,23,200,6,3
	.word	90954
	.byte	27
	.byte	'Ifx_P_ACCEN0_Bits',0,10,79,3
	.word	8901
	.byte	27
	.byte	'Ifx_P_ACCEN1_Bits',0,10,85,3
	.word	8814
	.byte	27
	.byte	'Ifx_P_ESR_Bits',0,10,107,3
	.word	5157
	.byte	27
	.byte	'Ifx_P_ID_Bits',0,10,115,3
	.word	3210
	.byte	27
	.byte	'Ifx_P_IN_Bits',0,10,137,1,3
	.word	4205
	.byte	27
	.byte	'Ifx_P_IOCR0_Bits',0,10,150,1,3
	.word	3338
	.byte	27
	.byte	'Ifx_P_IOCR12_Bits',0,10,163,1,3
	.word	3985
	.byte	27
	.byte	'Ifx_P_IOCR4_Bits',0,10,176,1,3
	.word	3553
	.byte	27
	.byte	'Ifx_P_IOCR8_Bits',0,10,189,1,3
	.word	3768
	.byte	27
	.byte	'Ifx_P_LPCR0_Bits',0,10,197,1,3
	.word	8173
	.byte	27
	.byte	'Ifx_P_LPCR1_Bits',0,10,205,1,3
	.word	8297
	.byte	27
	.byte	'Ifx_P_LPCR1_P21_Bits',0,10,215,1,3
	.word	8381
	.byte	27
	.byte	'Ifx_P_LPCR2_Bits',0,10,229,1,3
	.word	8561
	.byte	27
	.byte	'Ifx_P_OMCR0_Bits',0,10,240,1,3
	.word	6812
	.byte	27
	.byte	'Ifx_P_OMCR12_Bits',0,10,250,1,3
	.word	7336
	.byte	27
	.byte	'Ifx_P_OMCR4_Bits',0,10,133,2,3
	.word	6986
	.byte	27
	.byte	'Ifx_P_OMCR8_Bits',0,10,144,2,3
	.word	7160
	.byte	27
	.byte	'Ifx_P_OMCR_Bits',0,10,166,2,3
	.word	7825
	.byte	27
	.byte	'Ifx_P_OMR_Bits',0,10,203,2,3
	.word	2639
	.byte	27
	.byte	'Ifx_P_OMSR0_Bits',0,10,213,2,3
	.word	6149
	.byte	27
	.byte	'Ifx_P_OMSR12_Bits',0,10,224,2,3
	.word	6637
	.byte	27
	.byte	'Ifx_P_OMSR4_Bits',0,10,235,2,3
	.word	6296
	.byte	27
	.byte	'Ifx_P_OMSR8_Bits',0,10,246,2,3
	.word	6465
	.byte	27
	.byte	'Ifx_P_OMSR_Bits',0,10,140,3,3
	.word	7492
	.byte	27
	.byte	'Ifx_P_OUT_Bits',0,10,162,3,3
	.word	2323
	.byte	27
	.byte	'Ifx_P_PCSR_Bits',0,10,180,3,3
	.word	5863
	.byte	27
	.byte	'Ifx_P_PDISC_Bits',0,10,202,3,3
	.word	5497
	.byte	27
	.byte	'Ifx_P_PDR0_Bits',0,10,223,3,3
	.word	4528
	.byte	27
	.byte	'Ifx_P_PDR1_Bits',0,10,244,3,3
	.word	4832
	.byte	27
	.byte	'Ifx_P_ACCEN0',0,10,129,4,3
	.word	9428
	.byte	27
	.byte	'Ifx_P_ACCEN1',0,10,137,4,3
	.word	8861
	.byte	27
	.byte	'Ifx_P_ESR',0,10,145,4,3
	.word	5448
	.byte	27
	.byte	'Ifx_P_ID',0,10,153,4,3
	.word	3289
	.byte	27
	.byte	'Ifx_P_IN',0,10,161,4,3
	.word	4479
	.byte	27
	.byte	'Ifx_P_IOCR0',0,10,169,4,3
	.word	3513
	.byte	27
	.byte	'Ifx_P_IOCR12',0,10,177,4,3
	.word	4165
	.byte	27
	.byte	'Ifx_P_IOCR4',0,10,185,4,3
	.word	3728
	.byte	27
	.byte	'Ifx_P_IOCR8',0,10,193,4,3
	.word	3945
	.byte	27
	.byte	'Ifx_P_LPCR0',0,10,201,4,3
	.word	8257
	.byte	27
	.byte	'Ifx_P_LPCR1',0,10,210,4,3
	.word	8506
	.byte	27
	.byte	'Ifx_P_LPCR2',0,10,218,4,3
	.word	8765
	.byte	27
	.byte	'Ifx_P_OMCR',0,10,226,4,3
	.word	8133
	.byte	27
	.byte	'Ifx_P_OMCR0',0,10,234,4,3
	.word	6946
	.byte	27
	.byte	'Ifx_P_OMCR12',0,10,242,4,3
	.word	7452
	.byte	27
	.byte	'Ifx_P_OMCR4',0,10,250,4,3
	.word	7120
	.byte	27
	.byte	'Ifx_P_OMCR8',0,10,130,5,3
	.word	7296
	.byte	27
	.byte	'Ifx_P_OMR',0,10,138,5,3
	.word	3170
	.byte	27
	.byte	'Ifx_P_OMSR',0,10,146,5,3
	.word	7785
	.byte	27
	.byte	'Ifx_P_OMSR0',0,10,154,5,3
	.word	6256
	.byte	27
	.byte	'Ifx_P_OMSR12',0,10,162,5,3
	.word	6772
	.byte	27
	.byte	'Ifx_P_OMSR4',0,10,170,5,3
	.word	6425
	.byte	27
	.byte	'Ifx_P_OMSR8',0,10,178,5,3
	.word	6597
	.byte	27
	.byte	'Ifx_P_OUT',0,10,186,5,3
	.word	2599
	.byte	27
	.byte	'Ifx_P_PCSR',0,10,194,5,3
	.word	6109
	.byte	27
	.byte	'Ifx_P_PDISC',0,10,202,5,3
	.word	5823
	.byte	27
	.byte	'Ifx_P_PDR0',0,10,210,5,3
	.word	4792
	.byte	27
	.byte	'Ifx_P_PDR1',0,10,218,5,3
	.word	5108
	.byte	10
	.word	9468
	.byte	27
	.byte	'Ifx_P',0,10,139,6,3
	.word	92301
	.byte	27
	.byte	'IfxPort_InputMode',0,9,89,3
	.word	10081
	.byte	27
	.byte	'IfxPort_OutputIdx',0,9,130,1,3
	.word	10356
	.byte	27
	.byte	'IfxPort_OutputMode',0,9,138,1,3
	.word	10286
	.byte	15,9,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,27
	.byte	'IfxPort_PadDriver',0,9,158,1,3
	.word	92402
	.byte	27
	.byte	'IfxPort_State',0,9,178,1,3
	.word	10669
	.byte	27
	.byte	'IfxPort_Pin',0,9,194,1,3
	.word	24013
	.byte	27
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,13,148,1,16
	.word	235
	.byte	25,13,212,5,9,8,14
	.byte	'value',0
	.word	2092
	.byte	4,2,35,0,14
	.byte	'mask',0
	.word	2092
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_CcuconRegConfig',0,13,216,5,3
	.word	92928
	.byte	25,13,221,5,9,8,14
	.byte	'pDivider',0
	.word	591
	.byte	1,2,35,0,14
	.byte	'nDivider',0
	.word	591
	.byte	1,2,35,1,14
	.byte	'k2Initial',0
	.word	591
	.byte	1,2,35,2,14
	.byte	'waitTime',0
	.word	292
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_InitialStepConfig',0,13,227,5,3
	.word	92999
	.byte	25,13,231,5,9,12,14
	.byte	'k2Step',0
	.word	591
	.byte	1,2,35,0,14
	.byte	'waitTime',0
	.word	292
	.byte	4,2,35,2,14
	.byte	'hookFunction',0
	.word	92888
	.byte	4,2,35,8,0,27
	.byte	'IfxScuCcu_PllStepsConfig',0,13,236,5,3
	.word	93116
	.byte	3
	.word	232
	.byte	25,13,244,5,9,48,14
	.byte	'ccucon0',0
	.word	92928
	.byte	8,2,35,0,14
	.byte	'ccucon1',0
	.word	92928
	.byte	8,2,35,8,14
	.byte	'ccucon2',0
	.word	92928
	.byte	8,2,35,16,14
	.byte	'ccucon5',0
	.word	92928
	.byte	8,2,35,24,14
	.byte	'ccucon6',0
	.word	92928
	.byte	8,2,35,32,14
	.byte	'ccucon7',0
	.word	92928
	.byte	8,2,35,40,0,27
	.byte	'IfxScuCcu_ClockDistributionConfig',0,13,252,5,3
	.word	93218
	.byte	25,13,128,6,9,8,14
	.byte	'value',0
	.word	2092
	.byte	4,2,35,0,14
	.byte	'mask',0
	.word	2092
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,13,132,6,3
	.word	93370
	.byte	3
	.word	93116
	.byte	25,13,137,6,9,16,14
	.byte	'numOfPllDividerSteps',0
	.word	591
	.byte	1,2,35,0,14
	.byte	'pllDividerStep',0
	.word	93446
	.byte	4,2,35,4,14
	.byte	'pllInitialStep',0
	.word	92999
	.byte	8,2,35,8,0,27
	.byte	'IfxScuCcu_SysPllConfig',0,13,142,6,3
	.word	93451
	.byte	25,15,59,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	93568
	.byte	27
	.byte	'IfxCcu6_Cc60in_In',0,15,64,3
	.word	93619
	.byte	25,15,67,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	93650
	.byte	27
	.byte	'IfxCcu6_Cc61in_In',0,15,72,3
	.word	93701
	.byte	25,15,75,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	93732
	.byte	27
	.byte	'IfxCcu6_Cc62in_In',0,15,80,3
	.word	93783
	.byte	25,15,83,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	93814
	.byte	27
	.byte	'IfxCcu6_Ccpos0_In',0,15,88,3
	.word	93865
	.byte	25,15,91,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	93896
	.byte	27
	.byte	'IfxCcu6_Ccpos1_In',0,15,96,3
	.word	93947
	.byte	25,15,99,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	93978
	.byte	27
	.byte	'IfxCcu6_Ccpos2_In',0,15,104,3
	.word	94029
	.byte	25,15,107,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	24052
	.byte	1,2,35,12,0,26
	.word	94060
	.byte	27
	.byte	'IfxCcu6_Ctrap_In',0,15,112,3
	.word	94111
	.byte	26
	.word	24171
	.byte	27
	.byte	'IfxCcu6_T12hr_In',0,15,120,3
	.word	94141
	.byte	26
	.word	24232
	.byte	27
	.byte	'IfxCcu6_T13hr_In',0,15,128,1,3
	.word	94171
	.byte	25,15,131,1,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	10356
	.byte	1,2,35,12,0,26
	.word	94202
	.byte	27
	.byte	'IfxCcu6_Cc60_Out',0,15,136,1,3
	.word	94254
	.byte	25,15,139,1,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	10356
	.byte	1,2,35,12,0,26
	.word	94285
	.byte	27
	.byte	'IfxCcu6_Cc61_Out',0,15,144,1,3
	.word	94337
	.byte	25,15,147,1,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	10356
	.byte	1,2,35,12,0,26
	.word	94368
	.byte	27
	.byte	'IfxCcu6_Cc62_Out',0,15,152,1,3
	.word	94420
	.byte	25,15,155,1,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	10356
	.byte	1,2,35,12,0,26
	.word	94451
	.byte	27
	.byte	'IfxCcu6_Cout60_Out',0,15,160,1,3
	.word	94503
	.byte	25,15,163,1,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	10356
	.byte	1,2,35,12,0,26
	.word	94536
	.byte	27
	.byte	'IfxCcu6_Cout61_Out',0,15,168,1,3
	.word	94588
	.byte	25,15,171,1,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	10356
	.byte	1,2,35,12,0,26
	.word	94621
	.byte	27
	.byte	'IfxCcu6_Cout62_Out',0,15,176,1,3
	.word	94673
	.byte	25,15,179,1,15,16,14
	.byte	'module',0
	.word	20810
	.byte	4,2,35,0,14
	.byte	'pin',0
	.word	24013
	.byte	8,2,35,4,14
	.byte	'select',0
	.word	10356
	.byte	1,2,35,12,0,26
	.word	94706
	.byte	27
	.byte	'IfxCcu6_Cout63_Out',0,15,184,1,3
	.word	94758
	.byte	27
	.byte	'IfxCcu6_CaptureCompareInput',0,11,90,3
	.word	21711
	.byte	27
	.byte	'IfxCcu6_CaptureCompareInputSignal',0,11,100,3
	.word	21969
	.byte	27
	.byte	'IfxCcu6_CaptureCompareState',0,11,109,3
	.word	23900
	.byte	27
	.byte	'IfxCcu6_ChannelOut',0,11,122,3
	.word	23635
	.byte	15,11,127,9,1,16
	.byte	'IfxCcu6_CountingInputMode_internal',0,0,16
	.byte	'IfxCcu6_CountingInputMode_manual',0,1,16
	.byte	'IfxCcu6_CountingInputMode_externalRising',0,2,16
	.byte	'IfxCcu6_CountingInputMode_externalFalling',0,3,0,27
	.byte	'IfxCcu6_CountingInputMode',0,11,137,1,3
	.word	94932
	.byte	15,11,152,1,9,1,16
	.byte	'IfxCcu6_ExternalTriggerMode_disable',0,0,16
	.byte	'IfxCcu6_ExternalTriggerMode_risingEdge',0,1,16
	.byte	'IfxCcu6_ExternalTriggerMode_fallingEdge',0,2,16
	.byte	'IfxCcu6_ExternalTriggerMode_anyEdge',0,3,0,27
	.byte	'IfxCcu6_ExternalTriggerMode',0,11,161,1,3
	.word	95132
	.byte	15,11,166,1,9,1,16
	.byte	'IfxCcu6_HallSensorTriggerMode_permanentCheck',0,0,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM63',0,1,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t13PM',0,2,16
	.byte	'IfxCcu6_HallSensorTriggerMode_off',0,3,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t12PMCountingUp',0,4,16
	.byte	'IfxCcu6_HallSensorTriggerMode_t12OMCountingDown',0,5,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingUp',0,6,16
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingDown',0,7,0,27
	.byte	'IfxCcu6_HallSensorTriggerMode',0,11,177,1,3
	.word	95335
	.byte	27
	.byte	'IfxCcu6_InterruptSource',0,11,203,1,3
	.word	22987
	.byte	15,11,208,1,9,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_noEvent',0,0,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_correctHallEvent',0,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t13PeriodMatch',0,2,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12OneMatch',0,3,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12Channel1CompareMatch',0,4,16
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12PeriodMatch',0,5,0,27
	.byte	'IfxCcu6_MultiChannelSwitchingSelect',0,11,217,1,3
	.word	95766
	.byte	15,11,222,1,9,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_direct',0,0,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t13ZeroMatch',0,1,16
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t12ZeroMatch',0,2,0,27
	.byte	'IfxCcu6_MultiChannelSwitchingSync',0,11,229,1,3
	.word	96137
	.byte	27
	.byte	'IfxCcu6_ServiceRequest',0,11,239,1,3
	.word	23520
	.byte	15,11,244,1,9,1,16
	.byte	'IfxCcu6_SleepMode_enable',0,0,16
	.byte	'IfxCcu6_SleepMode_disable',0,1,0,27
	.byte	'IfxCcu6_SleepMode',0,11,248,1,3
	.word	96360
	.byte	15,11,252,1,9,1,16
	.byte	'IfxCcu6_SuspendMode_none',0,0,16
	.byte	'IfxCcu6_SuspendMode_hard',0,1,16
	.byte	'IfxCcu6_SuspendMode_soft',0,2,0,27
	.byte	'IfxCcu6_SuspendMode',0,11,129,2,3
	.word	96449
	.byte	27
	.byte	'IfxCcu6_T12Channel',0,11,138,2,3
	.word	23824
	.byte	15,11,142,2,9,1,16
	.byte	'IfxCcu6_T12ChannelMode_off',0,0,16
	.byte	'IfxCcu6_T12ChannelMode_compareMode',0,1,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRisingAndFalling',0,4,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRising',0,5,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureFalling',0,6,16
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureAny',0,7,16
	.byte	'IfxCcu6_T12ChannelMode_hallSensor',0,8,16
	.byte	'IfxCcu6_T12ChannelMode_hysteresisLikecompare',0,9,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureRisingAndFalling',0,10,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureFallingAndRising',0,11,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothRising',0,12,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothFalling',0,13,16
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureAny',0,14,0,27
	.byte	'IfxCcu6_T12ChannelMode',0,11,158,2,3
	.word	96594
	.byte	15,11,163,2,9,1,16
	.byte	'IfxCcu6_T12CountDirection_up',0,0,16
	.byte	'IfxCcu6_T12CountDirection_down',0,1,0,27
	.byte	'IfxCcu6_T12CountDirection',0,11,167,2,3
	.word	97273
	.byte	27
	.byte	'IfxCcu6_T12CountMode',0,11,178,2,3
	.word	21288
	.byte	15,11,183,2,9,1,16
	.byte	'IfxCcu6_T13TriggerDirection_noAction',0,0,16
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingUp',0,1,16
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingDown',0,2,16
	.byte	'IfxCcu6_T13TriggerDirection_anyT12',0,3,0,27
	.byte	'IfxCcu6_T13TriggerDirection',0,11,189,2,3
	.word	97409
	.byte	15,11,194,2,9,1,16
	.byte	'IfxCcu6_T13TriggerEvent_noAction',0,0,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC60RCompare',0,1,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC61RCompare',0,2,16
	.byte	'IfxCcu6_T13TriggerEvent_onCC62RCompare',0,3,16
	.byte	'IfxCcu6_T13TriggerEvent_onAnyT12Compare',0,4,16
	.byte	'IfxCcu6_T13TriggerEvent_onT12Period',0,5,16
	.byte	'IfxCcu6_T13TriggerEvent_onT12Zero',0,6,16
	.byte	'IfxCcu6_T13TriggerEvent_onCCPOSxEdge',0,7,0,27
	.byte	'IfxCcu6_T13TriggerEvent',0,11,205,2,3
	.word	97623
	.byte	27
	.byte	'IfxCcu6_TimerId',0,11,213,2,3
	.word	20867
	.byte	27
	.byte	'IfxCcu6_TimerInputClock',0,11,228,2,3
	.word	20918
	.byte	15,11,247,2,9,1,16
	.byte	'IfxCcu6_TimerRunStatus_stopped',0,0,16
	.byte	'IfxCcu6_TimerRunStatus_running',0,1,0,27
	.byte	'IfxCcu6_TimerRunStatus',0,11,251,2,3
	.word	98034
	.byte	15,11,128,3,9,1,16
	.byte	'IfxCcu6_TrapMode_automatic',0,0,16
	.byte	'IfxCcu6_TrapMode_manual',0,1,0,27
	.byte	'IfxCcu6_TrapMode',0,11,133,3,3
	.word	98139
	.byte	15,11,138,3,9,1,16
	.byte	'IfxCcu6_TrapState_t12Sync',0,0,16
	.byte	'IfxCcu6_TrapState_t13Sync',0,1,16
	.byte	'IfxCcu6_TrapState_immediate',0,3,0,27
	.byte	'IfxCcu6_TrapState',0,11,145,3,3
	.word	98227
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L148:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,53,0,73,19,0,0,11,19
	.byte	1,3,8,58,15,59,15,57,15,11,15,0,0,12,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,13,23,1,58,15,59,15,57
	.byte	15,11,15,0,0,14,13,0,3,8,73,19,11,15,56,9,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,0,3,8,54,15,39,12,63,12,60,12,0,0,21,46,1,49
	.byte	19,0,0,22,5,0,49,19,0,0,23,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,24,46,0,3,8,58,15,59
	.byte	15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,25,19,1,58,15,59,15,57,15,11,15,0,0,26,38,0,73,19,0,0,27,22
	.byte	0,3,8,58,15,59,15,57,15,73,19,0,0,28,21,0,54,15,0,0,29,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0
	.byte	0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L149:
	.word	.L586-.L585
.L585:
	.half	3
	.word	.L588-.L587
.L587:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0,0,0,0
	.byte	'IfxCcu6_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCcu6_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxCcu6_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0,0
.L588:
.L586:
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_connectTrigger')
	.sect	'.debug_info'
.L150:
	.word	466
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L153,.L152
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_connectTrigger',0,1,56,6,1,1,1
	.word	.L105,.L255,.L104
	.byte	4
	.byte	'ccu6',0,1,56,39
	.word	.L256,.L257
	.byte	4
	.byte	'outputLine',0,1,56,61
	.word	.L258,.L259
	.byte	4
	.byte	'selectedTrigger',0,1,56,89
	.word	.L260,.L261
	.byte	5
	.word	.L105,.L255
	.byte	6
	.byte	'mosel',0,1,58,14
	.word	.L262,.L263
	.byte	6
	.byte	'shift',0,1,59,14
	.word	.L264,.L265
	.byte	6
	.byte	'data',0,1,60,14
	.word	.L264,.L266
	.byte	7
	.word	.L267,.L268,.L269
	.byte	8
	.word	.L270,.L271
	.byte	8
	.word	.L272,.L273
	.byte	8
	.word	.L274,.L275
	.byte	9
	.word	.L276,.L268,.L269
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_connectTrigger')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_connectTrigger')
	.sect	'.debug_line'
.L152:
	.word	.L590-.L589
.L589:
	.half	3
	.word	.L592-.L591
.L591:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0,0
.L592:
	.byte	5,6,7,0,5,2
	.word	.L105
	.byte	3,55,1,5,33,9
	.half	.L476-.L105
	.byte	3,2,1,5,20,9
	.half	.L593-.L476
	.byte	1,5,24,9
	.half	.L594-.L593
	.byte	3,2,1,5,18,9
	.half	.L477-.L594
	.byte	3,2,1,5,5,9
	.half	.L595-.L477
	.byte	1,5,21,7,9
	.half	.L596-.L595
	.byte	3,2,1,5,32,9
	.half	.L2-.L596
	.byte	3,3,1,5,19,9
	.half	.L597-.L2
	.byte	1,5,16,9
	.half	.L598-.L597
	.byte	3,1,1,5,24,9
	.half	.L599-.L598
	.byte	1,5,29,9
	.half	.L600-.L599
	.byte	1,5,46,9
	.half	.L601-.L600
	.byte	1,4,2,5,36,9
	.half	.L268-.L601
	.byte	3,45,1,5,66,9
	.half	.L602-.L268
	.byte	1,5,64,9
	.half	.L603-.L602
	.byte	1,5,83,9
	.half	.L604-.L603
	.byte	1,5,75,9
	.half	.L605-.L604
	.byte	1,5,33,9
	.half	.L606-.L605
	.byte	1,4,1,5,21,9
	.half	.L269-.L606
	.byte	3,84,1,5,19,9
	.half	.L607-.L269
	.byte	1,5,1,9
	.half	.L608-.L607
	.byte	3,1,1,7,9
	.half	.L154-.L608
	.byte	0,1,1
.L590:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_connectTrigger')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L105,0,.L154-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_routeInterruptNode')
	.sect	'.debug_info'
.L155:
	.word	361
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L158,.L157
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_routeInterruptNode',0,1,158,2,6,1,1,1
	.word	.L129,.L277,.L128
	.byte	4
	.byte	'ccu6',0,1,158,2,43
	.word	.L256,.L278
	.byte	4
	.byte	'source',0,1,158,2,73
	.word	.L279,.L280
	.byte	4
	.byte	'serviceRequest',0,1,158,2,104
	.word	.L281,.L282
	.byte	5
	.word	.L129,.L277
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_routeInterruptNode')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_routeInterruptNode')
	.sect	'.debug_line'
.L157:
	.word	.L610-.L609
.L609:
	.half	3
	.word	.L612-.L611
.L611:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L612:
	.byte	5,9,7,0,5,2
	.word	.L129
	.byte	3,159,2,1,5,71,7,9
	.half	.L613-.L129
	.byte	1,5,20,7,9
	.half	.L51-.L613
	.byte	3,2,1,5,29,9
	.half	.L614-.L51
	.byte	1,5,45,9
	.half	.L615-.L614
	.byte	1,5,14,9
	.half	.L52-.L615
	.byte	3,2,1,5,76,7,9
	.half	.L616-.L52
	.byte	1,5,20,7,9
	.half	.L54-.L616
	.byte	3,2,1,5,29,9
	.half	.L617-.L54
	.byte	1,5,45,9
	.half	.L618-.L617
	.byte	1,5,14,9
	.half	.L55-.L618
	.byte	3,2,1,5,76,7,9
	.half	.L619-.L55
	.byte	1,5,20,7,9
	.half	.L57-.L619
	.byte	3,2,1,5,29,9
	.half	.L620-.L57
	.byte	1,5,45,9
	.half	.L621-.L620
	.byte	1,5,25,9
	.half	.L58-.L621
	.byte	3,2,1,5,10,9
	.half	.L622-.L58
	.byte	1,5,20,7,9
	.half	.L623-.L622
	.byte	3,2,1,5,28,9
	.half	.L624-.L623
	.byte	1,5,44,9
	.half	.L625-.L624
	.byte	1,5,25,9
	.half	.L60-.L625
	.byte	3,2,1,5,14,9
	.half	.L626-.L60
	.byte	1,5,69,7,9
	.half	.L627-.L626
	.byte	1,5,66,9
	.half	.L628-.L627
	.byte	1,5,20,7,9
	.half	.L62-.L628
	.byte	3,2,1,5,28,9
	.half	.L629-.L62
	.byte	1,5,44,9
	.half	.L630-.L629
	.byte	1,5,14,9
	.half	.L63-.L630
	.byte	3,2,1,5,73,7,9
	.half	.L631-.L63
	.byte	1,5,20,7,9
	.half	.L65-.L631
	.byte	3,2,1,5,28,9
	.half	.L632-.L65
	.byte	1,5,44,9
	.half	.L633-.L632
	.byte	1,5,25,9
	.half	.L66-.L633
	.byte	3,2,1,5,14,9
	.half	.L634-.L66
	.byte	1,5,80,7,9
	.half	.L635-.L634
	.byte	1,5,77,9
	.half	.L636-.L635
	.byte	1,5,20,7,9
	.half	.L68-.L636
	.byte	3,2,1,5,28,9
	.half	.L637-.L68
	.byte	1,5,44,9
	.half	.L638-.L637
	.byte	1,5,16,9
	.half	.L69-.L638
	.byte	3,4,1,5,1,9
	.half	.L53-.L69
	.byte	3,2,1,7,9
	.half	.L159-.L53
	.byte	0,1,1
.L610:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_routeInterruptNode')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L129,0,.L159-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setOutputPassiveLevel')
	.sect	'.debug_info'
.L160:
	.word	407
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L163,.L162
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setOutputPassiveLevel',0,1,195,2,6,1,1,1
	.word	.L131,.L283,.L130
	.byte	4
	.byte	'ccu6',0,1,195,2,46
	.word	.L256,.L284
	.byte	4
	.byte	'channelOut',0,1,195,2,71
	.word	.L285,.L286
	.byte	4
	.byte	'state',0,1,195,2,91
	.word	.L287,.L288
	.byte	5
	.word	.L131,.L283
	.byte	5
	.word	.L289,.L290
	.byte	6
	.byte	'shift',0,1,199,2,16
	.word	.L291,.L292
	.byte	6
	.byte	'mask',0,1,200,2,16
	.word	.L291,.L293
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setOutputPassiveLevel')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setOutputPassiveLevel')
	.sect	'.debug_line'
.L162:
	.word	.L640-.L639
.L639:
	.half	3
	.word	.L642-.L641
.L641:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L642:
	.byte	5,5,7,0,5,2
	.word	.L131
	.byte	3,196,2,1,5,25,7,9
	.half	.L289-.L131
	.byte	3,3,1,5,27,9
	.half	.L643-.L289
	.byte	1,5,35,9
	.half	.L523-.L643
	.byte	3,1,1,5,40,9
	.half	.L644-.L523
	.byte	1,5,38,9
	.half	.L524-.L644
	.byte	1,5,64,9
	.half	.L645-.L524
	.byte	1,5,47,9
	.half	.L522-.L645
	.byte	1,5,22,9
	.half	.L646-.L522
	.byte	1,9
	.half	.L290-.L646
	.byte	3,126,1,5,21,9
	.half	.L71-.L290
	.byte	3,6,1,5,28,9
	.half	.L647-.L71
	.byte	1,5,1,9
	.half	.L72-.L647
	.byte	3,2,1,7,9
	.half	.L164-.L72
	.byte	0,1,1
.L640:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setOutputPassiveLevel')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L131,0,.L164-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setT12CaptureCompareState')
	.sect	'.debug_info'
.L165:
	.word	397
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L168,.L167
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setT12CaptureCompareState',0,1,210,2,6,1,1,1
	.word	.L133,.L294,.L132
	.byte	4
	.byte	'ccu6',0,1,210,2,50
	.word	.L256,.L295
	.byte	4
	.byte	'channel',0,1,210,2,75
	.word	.L296,.L297
	.byte	4
	.byte	'state',0,1,210,2,112
	.word	.L298,.L299
	.byte	5
	.word	.L133,.L294
	.byte	6
	.byte	'mask',0,1,212,2,12
	.word	.L291,.L300
	.byte	6
	.byte	'mode',0,1,213,2,12
	.word	.L291,.L301
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setT12CaptureCompareState')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setT12CaptureCompareState')
	.sect	'.debug_line'
.L167:
	.word	.L649-.L648
.L648:
	.half	3
	.word	.L651-.L650
.L650:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L651:
	.byte	5,6,7,0,5,2
	.word	.L133
	.byte	3,209,2,1,5,20,9
	.half	.L525-.L133
	.byte	3,2,1,5,28,9
	.half	.L652-.L525
	.byte	1,5,5,9
	.half	.L527-.L652
	.byte	3,3,1,5,26,7,9
	.half	.L653-.L527
	.byte	3,2,1,5,43,9
	.half	.L529-.L653
	.byte	3,1,1,5,48,9
	.half	.L654-.L529
	.byte	1,5,46,9
	.half	.L655-.L654
	.byte	1,5,63,9
	.half	.L656-.L655
	.byte	1,5,55,9
	.half	.L530-.L656
	.byte	1,5,26,9
	.half	.L657-.L530
	.byte	1,5,5,9
	.half	.L73-.L657
	.byte	3,3,1,5,26,7,9
	.half	.L658-.L73
	.byte	3,2,1,5,43,9
	.half	.L531-.L658
	.byte	3,1,1,5,48,9
	.half	.L659-.L531
	.byte	1,5,46,9
	.half	.L660-.L659
	.byte	1,5,63,9
	.half	.L661-.L660
	.byte	1,5,55,9
	.half	.L532-.L661
	.byte	1,5,26,9
	.half	.L662-.L532
	.byte	1,5,5,9
	.half	.L74-.L662
	.byte	3,3,1,5,26,7,9
	.half	.L663-.L74
	.byte	3,2,1,5,43,9
	.half	.L526-.L663
	.byte	3,1,1,5,48,9
	.half	.L664-.L526
	.byte	1,5,46,9
	.half	.L528-.L664
	.byte	1,5,63,9
	.half	.L665-.L528
	.byte	1,5,55,9
	.half	.L533-.L665
	.byte	1,5,26,9
	.half	.L666-.L533
	.byte	1,5,1,9
	.half	.L75-.L666
	.byte	3,2,1,7,9
	.half	.L169-.L75
	.byte	0,1,1
.L649:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setT12CaptureCompareState')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L133,0,.L169-.L133,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setT12CompareValue')
	.sect	'.debug_info'
.L170:
	.word	353
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L173,.L172
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setT12CompareValue',0,1,235,2,6,1,1,1
	.word	.L135,.L302,.L134
	.byte	4
	.byte	'ccu6',0,1,235,2,43
	.word	.L256,.L303
	.byte	4
	.byte	'channel',0,1,235,2,68
	.word	.L296,.L304
	.byte	4
	.byte	'value',0,1,235,2,84
	.word	.L264,.L305
	.byte	5
	.word	.L135,.L302
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setT12CompareValue')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setT12CompareValue')
	.sect	'.debug_line'
.L172:
	.word	.L668-.L667
.L667:
	.half	3
	.word	.L670-.L669
.L669:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L670:
	.byte	5,10,7,0,5,2
	.word	.L135
	.byte	3,238,2,1,9
	.half	.L671-.L135
	.byte	3,3,1,9
	.half	.L672-.L671
	.byte	3,3,1,5,23,9
	.half	.L76-.L672
	.byte	3,123,1,5,28,9
	.half	.L673-.L76
	.byte	1,5,9,9
	.half	.L674-.L673
	.byte	3,1,1,5,23,9
	.half	.L77-.L674
	.byte	3,2,1,5,28,9
	.half	.L675-.L77
	.byte	1,5,9,9
	.half	.L676-.L675
	.byte	3,1,1,5,23,9
	.half	.L78-.L676
	.byte	3,2,1,5,28,9
	.half	.L677-.L78
	.byte	1,5,9,9
	.half	.L678-.L677
	.byte	3,1,1,5,1,9
	.half	.L80-.L678
	.byte	3,2,1,7,9
	.half	.L174-.L80
	.byte	0,1,1
.L668:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setT12CompareValue')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L135,0,.L174-.L135,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setT12Frequency')
	.sect	'.debug_info'
.L175:
	.word	877
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L178,.L177
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setT12Frequency',0,1,252,2,9
	.word	.L306
	.byte	1,1,1
	.word	.L137,.L307,.L136
	.byte	4
	.byte	'ccu6',0,1,252,2,43
	.word	.L256,.L308
	.byte	4
	.byte	'frequency',0,1,252,2,57
	.word	.L306,.L309
	.byte	4
	.byte	'period',0,1,252,2,83
	.word	.L291,.L310
	.byte	4
	.byte	'countMode',0,1,252,2,112
	.word	.L311,.L312
	.byte	5
	.word	.L137,.L307
	.byte	6
	.byte	'prescaler',0,1,254,2,13
	.word	.L264,.L313
	.byte	6
	.byte	'freqCC6',0,1,255,2,13
	.word	.L306,.L314
	.byte	6
	.byte	'divFactor',0,1,128,3,13
	.word	.L264,.L315
	.byte	5
	.word	.L316,.L317
	.byte	6
	.byte	'periodVal',0,1,142,3,33
	.word	.L264,.L318
	.byte	6
	.byte	'additionalPrescaler',0,1,143,3,33
	.word	.L287,.L319
	.byte	6
	.byte	'clockInput',0,1,144,3,33
	.word	.L320,.L321
	.byte	7
	.word	.L322,.L323,.L324
	.byte	8
	.word	.L325,.L326
	.byte	8
	.word	.L327,.L328
	.byte	9
	.word	.L329,.L323,.L324
	.byte	0,7
	.word	.L330,.L331,.L332
	.byte	8
	.word	.L333,.L334
	.byte	8
	.word	.L335,.L336
	.byte	8
	.word	.L337,.L338
	.byte	10
	.word	.L339,.L331,.L332
	.byte	6
	.byte	'tctr4',0,2,200,12,20
	.word	.L340,.L341
	.byte	0,0,7
	.word	.L342,.L343,.L344
	.byte	8
	.word	.L345,.L346
	.byte	8
	.word	.L347,.L348
	.byte	8
	.word	.L349,.L350
	.byte	10
	.word	.L351,.L343,.L344
	.byte	6
	.byte	'shift',0,2,213,15,12
	.word	.L291,.L352
	.byte	6
	.byte	'mask',0,2,214,15,12
	.word	.L291,.L353
	.byte	0,0,7
	.word	.L354,.L355,.L90
	.byte	8
	.word	.L356,.L357
	.byte	8
	.word	.L358,.L359
	.byte	10
	.word	.L360,.L355,.L90
	.byte	6
	.byte	'shift',0,2,247,11,12
	.word	.L291,.L361
	.byte	6
	.byte	'mask',0,2,248,11,12
	.word	.L291,.L362
	.byte	0,0,7
	.word	.L363,.L90,.L364
	.byte	8
	.word	.L365,.L366
	.byte	8
	.word	.L367,.L368
	.byte	9
	.word	.L369,.L90,.L364
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setT12Frequency')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setT12Frequency')
	.sect	'.debug_line'
.L177:
	.word	.L680-.L679
.L679:
	.half	3
	.word	.L682-.L681
.L681:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0,0,0,0,0
.L682:
	.byte	5,9,7,0,5,2
	.word	.L137
	.byte	3,251,2,1,5,50,9
	.half	.L536-.L137
	.byte	3,3,1,5,23,9
	.half	.L534-.L536
	.byte	1,5,33,9
	.half	.L538-.L534
	.byte	3,1,1,5,23,9
	.half	.L537-.L538
	.byte	1,5,20,9
	.half	.L540-.L537
	.byte	3,2,1,5,39,9
	.half	.L542-.L540
	.byte	1,5,33,9
	.half	.L84-.L542
	.byte	3,2,1,5,31,9
	.half	.L683-.L84
	.byte	1,5,9,9
	.half	.L684-.L683
	.byte	3,2,1,5,13,7,9
	.half	.L685-.L684
	.byte	3,2,1,5,50,9
	.half	.L85-.L685
	.byte	3,122,1,5,37,9
	.half	.L83-.L85
	.byte	1,5,39,9
	.half	.L686-.L83
	.byte	1,5,21,7,9
	.half	.L86-.L686
	.byte	3,10,1,5,5,9
	.half	.L687-.L86
	.byte	1,5,30,7,9
	.half	.L316-.L687
	.byte	3,6,1,5,91,7,9
	.half	.L688-.L316
	.byte	1,5,89,9
	.half	.L541-.L688
	.byte	1,5,94,9
	.half	.L543-.L541
	.byte	1,5,99,9
	.half	.L689-.L543
	.byte	1,5,109,9
	.half	.L88-.L689
	.byte	1,5,21,9
	.half	.L89-.L88
	.byte	1,4,2,5,18,9
	.half	.L323-.L89
	.byte	3,158,13,1,5,25,9
	.half	.L690-.L323
	.byte	1,4,1,5,44,9
	.half	.L324-.L690
	.byte	3,228,114,1,5,50,9
	.half	.L691-.L324
	.byte	1,4,2,5,20,9
	.half	.L331-.L691
	.byte	3,181,9,1,9
	.half	.L545-.L331
	.byte	3,1,1,9
	.half	.L692-.L545
	.byte	3,1,1,9
	.half	.L693-.L692
	.byte	3,1,1,4,1,5,58,9
	.half	.L332-.L693
	.byte	3,202,118,1,5,46,9
	.half	.L547-.L332
	.byte	3,1,1,4,2,5,27,9
	.half	.L343-.L547
	.byte	3,190,12,1,5,21,9
	.half	.L546-.L343
	.byte	3,1,1,5,26,9
	.half	.L694-.L546
	.byte	1,5,33,9
	.half	.L549-.L694
	.byte	3,1,1,5,38,9
	.half	.L695-.L549
	.byte	1,5,36,9
	.half	.L550-.L695
	.byte	1,5,66,9
	.half	.L696-.L550
	.byte	1,5,45,9
	.half	.L548-.L696
	.byte	1,5,19,9
	.half	.L697-.L548
	.byte	1,4,1,5,43,9
	.half	.L344-.L697
	.byte	3,194,115,1,5,51,9
	.half	.L698-.L344
	.byte	1,5,9,9
	.half	.L551-.L698
	.byte	3,2,1,5,53,7,9
	.half	.L699-.L551
	.byte	3,2,1,4,2,5,28,9
	.half	.L355-.L699
	.byte	3,218,8,1,5,33,9
	.half	.L552-.L355
	.byte	1,5,21,9
	.half	.L700-.L552
	.byte	3,1,1,5,24,9
	.half	.L701-.L700
	.byte	1,5,32,9
	.half	.L554-.L701
	.byte	3,1,1,5,35,9
	.half	.L553-.L554
	.byte	1,5,19,9
	.half	.L702-.L553
	.byte	1,5,18,9
	.half	.L90-.L702
	.byte	3,171,4,1,5,23,9
	.half	.L703-.L90
	.byte	1,4,1,5,32,9
	.half	.L364-.L703
	.byte	3,254,114,1,5,37,9
	.half	.L704-.L364
	.byte	1,5,29,9
	.half	.L705-.L704
	.byte	1,5,49,9
	.half	.L317-.L705
	.byte	3,126,1,5,19,9
	.half	.L87-.L317
	.byte	3,6,1,5,5,9
	.half	.L91-.L87
	.byte	3,3,1,5,1,9
	.half	.L92-.L91
	.byte	3,1,1,7,9
	.half	.L179-.L92
	.byte	0,1,1
.L680:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setT12Frequency')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L137,0,.L179-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setT12InputSignal')
	.sect	'.debug_info'
.L180:
	.word	334
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L183,.L182
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setT12InputSignal',0,1,173,3,6,1,1,1
	.word	.L139,.L370,.L138
	.byte	4
	.byte	'ccu6',0,1,173,3,42
	.word	.L256,.L371
	.byte	4
	.byte	'extInput',0,1,173,3,66
	.word	.L372,.L373
	.byte	5
	.word	.L139,.L370
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setT12InputSignal')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setT12InputSignal')
	.sect	'.debug_line'
.L182:
	.word	.L707-.L706
.L706:
	.half	3
	.word	.L709-.L708
.L708:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L709:
	.byte	5,19,7,0,5,2
	.word	.L139
	.byte	3,174,3,1,5,38,9
	.half	.L710-.L139
	.byte	1,5,47,9
	.half	.L711-.L710
	.byte	1,5,28,9
	.half	.L712-.L711
	.byte	1,5,19,9
	.half	.L713-.L712
	.byte	3,1,1,5,38,9
	.half	.L714-.L713
	.byte	1,5,28,9
	.half	.L715-.L714
	.byte	1,5,1,9
	.half	.L716-.L715
	.byte	3,3,1,7,9
	.half	.L184-.L716
	.byte	0,1,1
.L707:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setT12InputSignal')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L139,0,.L184-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setT13CaptureCompareState')
	.sect	'.debug_info'
.L185:
	.word	395
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L188,.L187
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setT13CaptureCompareState',0,1,182,3,6,1,1,1
	.word	.L141,.L374,.L140
	.byte	4
	.byte	'ccu6',0,1,182,3,50
	.word	.L256,.L375
	.byte	4
	.byte	'state',0,1,182,3,84
	.word	.L298,.L376
	.byte	5
	.word	.L141,.L374
	.byte	6
	.byte	'shift',0,1,184,3,12
	.word	.L291,.L377
	.byte	6
	.byte	'mask',0,1,185,3,12
	.word	.L291,.L378
	.byte	6
	.byte	'mode',0,1,186,3,12
	.word	.L291,.L379
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setT13CaptureCompareState')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setT13CaptureCompareState')
	.sect	'.debug_line'
.L187:
	.word	.L718-.L717
.L717:
	.half	3
	.word	.L720-.L719
.L719:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L720:
	.byte	5,6,7,0,5,2
	.word	.L141
	.byte	3,181,3,1,5,21,9
	.half	.L555-.L141
	.byte	3,3,1,5,29,9
	.half	.L557-.L555
	.byte	1,5,5,9
	.half	.L721-.L557
	.byte	3,3,1,5,26,7,9
	.half	.L722-.L721
	.byte	3,2,1,5,43,9
	.half	.L559-.L722
	.byte	3,1,1,5,48,9
	.half	.L723-.L559
	.byte	1,5,46,9
	.half	.L724-.L723
	.byte	1,5,63,9
	.half	.L725-.L724
	.byte	1,5,55,9
	.half	.L560-.L725
	.byte	1,5,26,9
	.half	.L726-.L560
	.byte	1,5,5,9
	.half	.L93-.L726
	.byte	3,3,1,5,26,7,9
	.half	.L727-.L93
	.byte	3,2,1,5,43,9
	.half	.L561-.L727
	.byte	3,1,1,5,48,9
	.half	.L728-.L561
	.byte	1,5,46,9
	.half	.L729-.L728
	.byte	1,5,63,9
	.half	.L730-.L729
	.byte	1,5,55,9
	.half	.L562-.L730
	.byte	1,5,26,9
	.half	.L731-.L562
	.byte	1,5,5,9
	.half	.L94-.L731
	.byte	3,3,1,5,26,7,9
	.half	.L732-.L94
	.byte	3,2,1,5,43,9
	.half	.L556-.L732
	.byte	3,1,1,5,48,9
	.half	.L733-.L556
	.byte	1,5,46,9
	.half	.L558-.L733
	.byte	1,5,63,9
	.half	.L734-.L558
	.byte	1,5,55,9
	.half	.L563-.L734
	.byte	1,5,26,9
	.half	.L735-.L563
	.byte	1,5,1,9
	.half	.L95-.L735
	.byte	3,2,1,7,9
	.half	.L189-.L95
	.byte	0,1,1
.L718:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setT13CaptureCompareState')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L141,0,.L189-.L141,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setT13Frequency')
	.sect	'.debug_info'
.L190:
	.word	809
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L193,.L192
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setT13Frequency',0,1,208,3,9
	.word	.L306
	.byte	1,1,1
	.word	.L143,.L380,.L142
	.byte	4
	.byte	'ccu6',0,1,208,3,43
	.word	.L256,.L381
	.byte	4
	.byte	'frequency',0,1,208,3,57
	.word	.L306,.L382
	.byte	4
	.byte	'period',0,1,208,3,83
	.word	.L291,.L383
	.byte	5
	.word	.L143,.L380
	.byte	6
	.byte	'prescaler',0,1,210,3,13
	.word	.L264,.L384
	.byte	6
	.byte	'freqCC6',0,1,211,3,13
	.word	.L306,.L385
	.byte	6
	.byte	'divFactor',0,1,212,3,13
	.word	.L264,.L386
	.byte	5
	.word	.L387,.L388
	.byte	6
	.byte	'periodVal',0,1,226,3,33
	.word	.L264,.L389
	.byte	6
	.byte	'additionalPrescaler',0,1,227,3,33
	.word	.L287,.L390
	.byte	6
	.byte	'clockInput',0,1,228,3,33
	.word	.L320,.L391
	.byte	7
	.word	.L392,.L393,.L394
	.byte	8
	.word	.L395,.L396
	.byte	8
	.word	.L397,.L398
	.byte	9
	.word	.L399,.L393,.L394
	.byte	0,7
	.word	.L330,.L400,.L401
	.byte	8
	.word	.L333,.L402
	.byte	8
	.word	.L335,.L403
	.byte	8
	.word	.L337,.L404
	.byte	10
	.word	.L339,.L400,.L401
	.byte	6
	.byte	'tctr4',0,2,200,12,20
	.word	.L340,.L405
	.byte	0,0,7
	.word	.L342,.L406,.L407
	.byte	8
	.word	.L345,.L408
	.byte	8
	.word	.L347,.L409
	.byte	8
	.word	.L349,.L410
	.byte	10
	.word	.L351,.L406,.L407
	.byte	6
	.byte	'shift',0,2,213,15,12
	.word	.L291,.L411
	.byte	6
	.byte	'mask',0,2,214,15,12
	.word	.L291,.L412
	.byte	0,0,7
	.word	.L354,.L413,.L101
	.byte	8
	.word	.L356,.L414
	.byte	8
	.word	.L358,.L415
	.byte	10
	.word	.L360,.L413,.L101
	.byte	6
	.byte	'shift',0,2,247,11,12
	.word	.L291,.L416
	.byte	6
	.byte	'mask',0,2,248,11,12
	.word	.L291,.L417
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setT13Frequency')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setT13Frequency')
	.sect	'.debug_line'
.L192:
	.word	.L737-.L736
.L736:
	.half	3
	.word	.L739-.L738
.L738:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0,0,0,0,0
.L739:
	.byte	5,9,7,0,5,2
	.word	.L143
	.byte	3,207,3,1,5,50,9
	.half	.L567-.L143
	.byte	3,3,1,5,23,9
	.half	.L564-.L567
	.byte	1,5,33,9
	.half	.L570-.L564
	.byte	3,1,1,5,23,9
	.half	.L569-.L570
	.byte	1,5,20,9
	.half	.L571-.L569
	.byte	3,2,1,5,39,9
	.half	.L572-.L571
	.byte	1,5,33,9
	.half	.L97-.L572
	.byte	3,2,1,5,31,9
	.half	.L740-.L97
	.byte	1,5,9,9
	.half	.L741-.L740
	.byte	3,2,1,5,13,7,9
	.half	.L742-.L741
	.byte	3,2,1,5,50,9
	.half	.L98-.L742
	.byte	3,122,1,5,37,9
	.half	.L96-.L98
	.byte	1,5,39,9
	.half	.L743-.L96
	.byte	1,5,21,7,9
	.half	.L99-.L743
	.byte	3,10,1,5,5,9
	.half	.L744-.L99
	.byte	1,5,37,7,9
	.half	.L387-.L744
	.byte	3,6,1,5,21,9
	.half	.L568-.L387
	.byte	1,4,2,5,18,9
	.half	.L393-.L568
	.byte	3,220,12,1,5,25,9
	.half	.L745-.L393
	.byte	1,4,1,5,44,9
	.half	.L394-.L745
	.byte	3,166,115,1,5,51,9
	.half	.L746-.L394
	.byte	1,4,2,5,20,9
	.half	.L400-.L746
	.byte	3,225,8,1,9
	.half	.L574-.L400
	.byte	3,1,1,9
	.half	.L747-.L574
	.byte	3,1,1,9
	.half	.L748-.L747
	.byte	3,1,1,4,1,5,58,9
	.half	.L401-.L748
	.byte	3,158,119,1,5,46,9
	.half	.L576-.L401
	.byte	3,1,1,4,2,5,27,9
	.half	.L406-.L576
	.byte	3,234,11,1,5,21,9
	.half	.L578-.L406
	.byte	3,1,1,5,26,9
	.half	.L749-.L578
	.byte	1,5,33,9
	.half	.L579-.L749
	.byte	3,1,1,5,38,9
	.half	.L575-.L579
	.byte	1,5,36,9
	.half	.L580-.L575
	.byte	1,5,66,9
	.half	.L750-.L580
	.byte	1,5,45,9
	.half	.L577-.L750
	.byte	1,5,19,9
	.half	.L751-.L577
	.byte	1,4,1,5,43,9
	.half	.L407-.L751
	.byte	3,150,116,1,5,51,9
	.half	.L752-.L407
	.byte	1,5,9,9
	.half	.L581-.L752
	.byte	3,2,1,5,53,7,9
	.half	.L753-.L581
	.byte	3,2,1,4,2,5,28,9
	.half	.L413-.L753
	.byte	3,134,8,1,5,33,9
	.half	.L582-.L413
	.byte	1,5,21,9
	.half	.L754-.L582
	.byte	3,1,1,5,24,9
	.half	.L755-.L754
	.byte	1,5,32,9
	.half	.L584-.L755
	.byte	3,1,1,5,35,9
	.half	.L583-.L584
	.byte	1,5,19,9
	.half	.L756-.L583
	.byte	1,4,1,5,32,9
	.half	.L101-.L756
	.byte	3,251,119,1,5,37,9
	.half	.L757-.L101
	.byte	1,5,29,9
	.half	.L758-.L757
	.byte	1,5,50,9
	.half	.L388-.L758
	.byte	1,5,19,9
	.half	.L100-.L388
	.byte	3,6,1,5,5,9
	.half	.L102-.L100
	.byte	3,3,1,5,1,9
	.half	.L103-.L102
	.byte	3,1,1,7,9
	.half	.L194-.L103
	.byte	0,1,1
.L737:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setT13Frequency')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L143,0,.L194-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_setT13InputSignal')
	.sect	'.debug_info'
.L195:
	.word	334
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L198,.L197
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_setT13InputSignal',0,1,129,4,6,1,1,1
	.word	.L145,.L418,.L144
	.byte	4
	.byte	'ccu6',0,1,129,4,42
	.word	.L256,.L419
	.byte	4
	.byte	'extInput',0,1,129,4,66
	.word	.L420,.L421
	.byte	5
	.word	.L145,.L418
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_setT13InputSignal')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_setT13InputSignal')
	.sect	'.debug_line'
.L197:
	.word	.L760-.L759
.L759:
	.half	3
	.word	.L762-.L761
.L761:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L762:
	.byte	5,19,7,0,5,2
	.word	.L145
	.byte	3,130,4,1,5,38,9
	.half	.L763-.L145
	.byte	1,5,47,9
	.half	.L764-.L763
	.byte	1,5,28,9
	.half	.L765-.L764
	.byte	1,5,19,9
	.half	.L766-.L765
	.byte	3,1,1,5,38,9
	.half	.L767-.L766
	.byte	1,5,28,9
	.half	.L768-.L767
	.byte	1,5,1,9
	.half	.L769-.L768
	.byte	3,3,1,7,9
	.half	.L199-.L769
	.byte	0,1,1
.L760:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_setT13InputSignal')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L145,0,.L199-.L145,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_getAddress')
	.sect	'.debug_info'
.L200:
	.word	330
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L203,.L202
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_getAddress',0,1,161,1,11
	.word	.L256
	.byte	1,1,1
	.word	.L115,.L422,.L114
	.byte	4
	.byte	'ccu6',0,1,161,1,44
	.word	.L423,.L424
	.byte	5
	.word	.L115,.L422
	.byte	6
	.byte	'module',0,1,163,1,15
	.word	.L256,.L425
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_getAddress')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_getAddress')
	.sect	'.debug_line'
.L202:
	.word	.L771-.L770
.L770:
	.half	3
	.word	.L773-.L772
.L772:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L773:
	.byte	5,5,7,0,5,2
	.word	.L115
	.byte	3,164,1,1,5,50,7,9
	.half	.L774-.L115
	.byte	3,2,1,5,30,9
	.half	.L775-.L774
	.byte	1,5,50,9
	.half	.L776-.L775
	.byte	1,5,56,9
	.half	.L777-.L776
	.byte	1,5,63,9
	.half	.L502-.L777
	.byte	1,5,16,9
	.half	.L21-.L502
	.byte	3,4,1,5,5,9
	.half	.L22-.L21
	.byte	3,3,1,5,1,9
	.half	.L23-.L22
	.byte	3,1,1,7,9
	.half	.L204-.L23
	.byte	0,1,1
.L771:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_getAddress')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L115,0,.L204-.L115,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_getCaptureRegisterValue')
	.sect	'.debug_info'
.L205:
	.word	363
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L208,.L207
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_getCaptureRegisterValue',0,1,178,1,8
	.word	.L291
	.byte	1,1,1
	.word	.L117,.L426,.L116
	.byte	4
	.byte	'ccu6',0,1,178,1,50
	.word	.L256,.L427
	.byte	4
	.byte	'channel',0,1,178,1,75
	.word	.L296,.L428
	.byte	5
	.word	.L117,.L426
	.byte	6
	.byte	'value',0,1,180,1,12
	.word	.L291,.L429
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_getCaptureRegisterValue')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_getCaptureRegisterValue')
	.sect	'.debug_line'
.L207:
	.word	.L779-.L778
.L778:
	.half	3
	.word	.L781-.L780
.L780:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L781:
	.byte	5,18,7,0,5,2
	.word	.L117
	.byte	3,179,1,1,5,10,9
	.half	.L503-.L117
	.byte	3,4,1,9
	.half	.L782-.L503
	.byte	3,3,1,9
	.half	.L783-.L782
	.byte	3,3,1,5,28,9
	.half	.L24-.L783
	.byte	3,123,1,5,9,9
	.half	.L784-.L24
	.byte	3,1,1,5,28,9
	.half	.L25-.L784
	.byte	3,2,1,5,9,9
	.half	.L785-.L25
	.byte	3,1,1,5,28,9
	.half	.L26-.L785
	.byte	3,2,1,5,9,9
	.half	.L786-.L26
	.byte	3,1,1,5,5,9
	.half	.L28-.L786
	.byte	3,3,1,5,1,9
	.half	.L31-.L28
	.byte	3,1,1,7,9
	.half	.L209-.L31
	.byte	0,1,1
.L779:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_getCaptureRegisterValue')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L117,0,.L209-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_getCaptureShadowRegisterValue')
	.sect	'.debug_info'
.L210:
	.word	369
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L213,.L212
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_getCaptureShadowRegisterValue',0,1,199,1,8
	.word	.L291
	.byte	1,1,1
	.word	.L119,.L430,.L118
	.byte	4
	.byte	'ccu6',0,1,199,1,56
	.word	.L256,.L431
	.byte	4
	.byte	'channel',0,1,199,1,81
	.word	.L296,.L432
	.byte	5
	.word	.L119,.L430
	.byte	6
	.byte	'value',0,1,201,1,12
	.word	.L291,.L433
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_getCaptureShadowRegisterValue')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_getCaptureShadowRegisterValue')
	.sect	'.debug_line'
.L212:
	.word	.L788-.L787
.L787:
	.half	3
	.word	.L790-.L789
.L789:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L790:
	.byte	5,18,7,0,5,2
	.word	.L119
	.byte	3,200,1,1,5,10,9
	.half	.L504-.L119
	.byte	3,4,1,9
	.half	.L791-.L504
	.byte	3,3,1,9
	.half	.L792-.L791
	.byte	3,3,1,5,29,9
	.half	.L32-.L792
	.byte	3,123,1,5,9,9
	.half	.L793-.L32
	.byte	3,1,1,5,29,9
	.half	.L33-.L793
	.byte	3,2,1,5,9,9
	.half	.L794-.L33
	.byte	3,1,1,5,29,9
	.half	.L34-.L794
	.byte	3,2,1,5,9,9
	.half	.L795-.L34
	.byte	3,1,1,5,5,9
	.half	.L36-.L795
	.byte	3,3,1,5,1,9
	.half	.L39-.L36
	.byte	3,1,1,7,9
	.half	.L214-.L39
	.byte	0,1,1
.L788:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_getCaptureShadowRegisterValue')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L119,0,.L214-.L119,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_getIndex')
	.sect	'.debug_info'
.L215:
	.word	347
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L218,.L217
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_getIndex',0,1,220,1,15
	.word	.L423
	.byte	1,1,1
	.word	.L121,.L434,.L120
	.byte	4
	.byte	'ccu6',0,1,220,1,42
	.word	.L256,.L435
	.byte	5
	.word	.L121,.L434
	.byte	6
	.byte	'index',0,1,222,1,19
	.word	.L291,.L436
	.byte	6
	.byte	'result',0,1,223,1,19
	.word	.L423,.L437
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_getIndex')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_getIndex')
	.sect	'.debug_line'
.L217:
	.word	.L797-.L796
.L796:
	.half	3
	.word	.L799-.L798
.L798:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L799:
	.byte	5,12,7,0,5,2
	.word	.L121
	.byte	3,224,1,1,5,16,9
	.half	.L505-.L121
	.byte	3,2,1,5,48,9
	.half	.L506-.L505
	.byte	1,5,33,9
	.half	.L41-.L506
	.byte	3,2,1,5,13,9
	.half	.L800-.L41
	.byte	1,5,33,9
	.half	.L801-.L800
	.byte	1,5,40,9
	.half	.L802-.L801
	.byte	1,5,9,9
	.half	.L803-.L802
	.byte	1,5,57,7,9
	.half	.L804-.L803
	.byte	3,2,1,5,37,9
	.half	.L805-.L804
	.byte	1,5,57,9
	.half	.L806-.L805
	.byte	1,5,64,9
	.half	.L807-.L806
	.byte	1,5,22,9
	.half	.L808-.L807
	.byte	1,5,13,9
	.half	.L809-.L808
	.byte	3,1,1,5,55,9
	.half	.L42-.L809
	.byte	3,123,1,5,48,9
	.half	.L40-.L42
	.byte	1,5,5,7,9
	.half	.L43-.L40
	.byte	3,9,1,5,1,9
	.half	.L44-.L43
	.byte	3,1,1,7,9
	.half	.L219-.L44
	.byte	0,1,1
.L797:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_getIndex')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L121,0,.L219-.L121,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_getSrcAddress')
	.sect	'.debug_info'
.L220:
	.word	382
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L223,.L222
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_getSrcAddress',0,1,240,1,24
	.word	.L438
	.byte	1,1,1
	.word	.L123,.L439,.L122
	.byte	4
	.byte	'ccu6',0,1,240,1,56
	.word	.L256,.L440
	.byte	4
	.byte	'serviceRequest',0,1,240,1,85
	.word	.L281,.L441
	.byte	5
	.word	.L123,.L439
	.byte	6
	.byte	'moduleIdx',0,1,242,1,28
	.word	.L442,.L443
	.byte	6
	.byte	'srcr',0,1,243,1,28
	.word	.L438,.L444
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_getSrcAddress')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_getSrcAddress')
	.sect	'.debug_line'
.L222:
	.word	.L811-.L810
.L810:
	.half	3
	.word	.L813-.L812
.L812:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L813:
	.byte	5,24,7,0,5,2
	.word	.L123
	.byte	3,239,1,1,5,57,9
	.half	.L508-.L123
	.byte	3,2,1,5,29,9
	.half	.L507-.L508
	.byte	3,3,1,5,34,9
	.half	.L814-.L507
	.byte	1,5,18,9
	.half	.L509-.L814
	.byte	3,1,1,5,5,9
	.half	.L815-.L509
	.byte	1,5,1,9
	.half	.L45-.L815
	.byte	3,1,1,7,9
	.half	.L224-.L45
	.byte	0,1,1
.L811:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_getSrcAddress')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L123,0,.L224-.L123,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_readTimer')
	.sect	'.debug_info'
.L225:
	.word	348
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L228,.L227
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_readTimer',0,1,250,1,8
	.word	.L291
	.byte	1,1,1
	.word	.L125,.L445,.L124
	.byte	4
	.byte	'ccu6',0,1,250,1,36
	.word	.L256,.L446
	.byte	4
	.byte	'timer',0,1,250,1,58
	.word	.L447,.L448
	.byte	5
	.word	.L125,.L445
	.byte	6
	.byte	'result',0,1,252,1,12
	.word	.L291,.L449
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_readTimer')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_readTimer')
	.sect	'.debug_line'
.L227:
	.word	.L817-.L816
.L816:
	.half	3
	.word	.L819-.L818
.L818:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L819:
	.byte	5,5,7,0,5,2
	.word	.L125
	.byte	3,253,1,1,5,27,7,9
	.half	.L820-.L125
	.byte	3,2,1,5,29,9
	.half	.L510-.L820
	.byte	1,5,27,9
	.half	.L46-.L510
	.byte	3,4,1,5,5,9
	.half	.L47-.L46
	.byte	3,3,1,5,1,9
	.half	.L48-.L47
	.byte	3,1,1,7,9
	.half	.L229-.L48
	.byte	0,1,1
.L817:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_readTimer')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L125,0,.L229-.L125,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_disableModulationOutput')
	.sect	'.debug_info'
.L230:
	.word	393
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L233,.L232
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_disableModulationOutput',0,1,73,6,1,1,1
	.word	.L107,.L450,.L106
	.byte	4
	.byte	'ccu6',0,1,73,48
	.word	.L256,.L451
	.byte	4
	.byte	'timer',0,1,73,70
	.word	.L447,.L452
	.byte	4
	.byte	'channelOut',0,1,73,96
	.word	.L285,.L453
	.byte	5
	.word	.L107,.L450
	.byte	6
	.byte	'shift',0,1,75,12
	.word	.L291,.L454
	.byte	6
	.byte	'mask',0,1,75,19
	.word	.L291,.L455
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_disableModulationOutput')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_disableModulationOutput')
	.sect	'.debug_line'
.L232:
	.word	.L822-.L821
.L821:
	.half	3
	.word	.L824-.L823
.L823:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L824:
	.byte	5,5,7,0,5,2
	.word	.L107
	.byte	3,204,0,1,5,9,7,9
	.half	.L825-.L107
	.byte	3,2,1,5,31,7,9
	.half	.L826-.L825
	.byte	3,3,1,5,33,9
	.half	.L827-.L826
	.byte	1,5,42,9
	.half	.L478-.L827
	.byte	3,1,1,5,47,9
	.half	.L828-.L478
	.byte	1,5,45,9
	.half	.L479-.L828
	.byte	1,5,28,9
	.half	.L829-.L479
	.byte	1,5,40,9
	.half	.L830-.L829
	.byte	3,126,1,5,9,9
	.half	.L5-.L830
	.byte	3,4,1,9
	.half	.L3-.L5
	.byte	3,5,1,5,41,7,9
	.half	.L831-.L3
	.byte	3,2,1,5,31,9
	.half	.L480-.L831
	.byte	3,1,1,5,33,9
	.half	.L832-.L480
	.byte	1,5,42,9
	.half	.L482-.L832
	.byte	3,1,1,5,47,9
	.half	.L481-.L482
	.byte	1,5,45,9
	.half	.L483-.L481
	.byte	1,5,28,9
	.half	.L833-.L483
	.byte	1,5,44,9
	.half	.L834-.L833
	.byte	3,126,1,5,27,9
	.half	.L7-.L834
	.byte	3,6,1,5,35,9
	.half	.L835-.L7
	.byte	1,5,1,9
	.half	.L6-.L835
	.byte	3,3,1,7,9
	.half	.L234-.L6
	.byte	0,1,1
.L822:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_disableModulationOutput')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L107,0,.L234-.L107,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_disableModule')
	.sect	'.debug_info'
.L235:
	.word	362
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L238,.L237
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_disableModule',0,1,104,6,1,1,1
	.word	.L109,.L456,.L108
	.byte	4
	.byte	'ccu6',0,1,104,38
	.word	.L256,.L457
	.byte	5
	.word	.L109,.L456
	.byte	6
	.byte	'passwd',0,1,106,12
	.word	.L264,.L458
	.byte	7
	.word	.L459,.L9,.L11
	.byte	8
	.word	.L460,.L461
	.byte	9
	.word	.L462,.L9,.L11
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_disableModule')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_disableModule')
	.sect	'.debug_line'
.L237:
	.word	.L837-.L836
.L836:
	.half	3
	.word	.L839-.L838
.L838:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0,0,0,0,0
.L839:
	.byte	5,6,7,0,5,2
	.word	.L109
	.byte	3,231,0,1,5,53,9
	.half	.L485-.L109
	.byte	3,2,1,5,19,9
	.half	.L484-.L485
	.byte	1,5,31,9
	.half	.L487-.L484
	.byte	3,1,1,5,19,9
	.half	.L488-.L487
	.byte	3,1,1,5,17,9
	.half	.L840-.L488
	.byte	1,5,29,9
	.half	.L841-.L840
	.byte	3,1,1,5,49,9
	.half	.L490-.L841
	.byte	3,3,1,4,2,5,23,9
	.half	.L9-.L490
	.byte	3,143,14,1,5,29,9
	.half	.L842-.L9
	.byte	1,5,5,9
	.half	.L843-.L842
	.byte	1,4,1,5,49,9
	.half	.L11-.L843
	.byte	3,241,113,1,5,1,7,9
	.half	.L844-.L11
	.byte	3,2,1,7,9
	.half	.L239-.L844
	.byte	0,1,1
.L837:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_disableModule')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L109,0,.L239-.L109,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_enableModulationOutput')
	.sect	'.debug_info'
.L240:
	.word	392
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L243,.L242
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_enableModulationOutput',0,1,117,6,1,1,1
	.word	.L111,.L463,.L110
	.byte	4
	.byte	'ccu6',0,1,117,47
	.word	.L256,.L464
	.byte	4
	.byte	'timer',0,1,117,69
	.word	.L447,.L465
	.byte	4
	.byte	'channelOut',0,1,117,95
	.word	.L285,.L466
	.byte	5
	.word	.L111,.L463
	.byte	6
	.byte	'shift',0,1,119,12
	.word	.L291,.L467
	.byte	6
	.byte	'mask',0,1,119,19
	.word	.L291,.L468
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_enableModulationOutput')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_enableModulationOutput')
	.sect	'.debug_line'
.L242:
	.word	.L846-.L845
.L845:
	.half	3
	.word	.L848-.L847
.L847:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L848:
	.byte	5,5,7,0,5,2
	.word	.L111
	.byte	3,248,0,1,5,9,7,9
	.half	.L849-.L111
	.byte	3,2,1,5,31,7,9
	.half	.L850-.L849
	.byte	3,3,1,5,33,9
	.half	.L851-.L850
	.byte	1,5,42,9
	.half	.L491-.L851
	.byte	3,1,1,5,45,9
	.half	.L852-.L491
	.byte	1,5,28,9
	.half	.L853-.L852
	.byte	1,5,40,9
	.half	.L854-.L853
	.byte	3,126,1,5,9,9
	.half	.L14-.L854
	.byte	3,4,1,9
	.half	.L12-.L14
	.byte	3,5,1,5,41,7,9
	.half	.L855-.L12
	.byte	3,2,1,5,31,9
	.half	.L492-.L855
	.byte	3,1,1,5,33,9
	.half	.L856-.L492
	.byte	1,5,42,9
	.half	.L494-.L856
	.byte	3,1,1,5,45,9
	.half	.L493-.L494
	.byte	1,5,28,9
	.half	.L857-.L493
	.byte	1,5,44,9
	.half	.L858-.L857
	.byte	3,126,1,5,27,9
	.half	.L16-.L858
	.byte	3,6,1,5,35,9
	.half	.L859-.L16
	.byte	1,5,1,9
	.half	.L15-.L859
	.byte	3,3,1,7,9
	.half	.L244-.L15
	.byte	0,1,1
.L846:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_enableModulationOutput')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L111,0,.L244-.L111,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_enableModule')
	.sect	'.debug_info'
.L245:
	.word	364
	.half	3
	.word	.L246
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L248,.L247
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_enableModule',0,1,148,1,6,1,1,1
	.word	.L113,.L469,.L112
	.byte	4
	.byte	'ccu6',0,1,148,1,37
	.word	.L256,.L470
	.byte	5
	.word	.L113,.L469
	.byte	6
	.byte	'passwd',0,1,150,1,12
	.word	.L264,.L471
	.byte	7
	.word	.L459,.L18,.L20
	.byte	8
	.word	.L460,.L472
	.byte	9
	.word	.L462,.L18,.L20
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_enableModule')
	.sect	'.debug_abbrev'
.L246:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_enableModule')
	.sect	'.debug_line'
.L247:
	.word	.L861-.L860
.L860:
	.half	3
	.word	.L863-.L862
.L862:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0,0,0,0,0
.L863:
	.byte	5,6,7,0,5,2
	.word	.L113
	.byte	3,147,1,1,5,53,9
	.half	.L496-.L113
	.byte	3,2,1,5,19,9
	.half	.L495-.L496
	.byte	1,5,31,9
	.half	.L498-.L495
	.byte	3,1,1,5,16,9
	.half	.L499-.L498
	.byte	3,1,1,5,22,9
	.half	.L864-.L499
	.byte	1,5,29,9
	.half	.L865-.L864
	.byte	3,1,1,5,50,9
	.half	.L501-.L865
	.byte	3,3,1,4,2,5,23,9
	.half	.L18-.L501
	.byte	3,227,13,1,5,29,9
	.half	.L866-.L18
	.byte	1,5,5,9
	.half	.L867-.L866
	.byte	1,4,1,5,50,9
	.half	.L20-.L867
	.byte	3,157,114,1,5,1,7,9
	.half	.L868-.L20
	.byte	3,2,1,7,9
	.half	.L249-.L868
	.byte	0,1,1
.L861:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_enableModule')
	.sect	'.debug_ranges'
.L248:
	.word	-1,.L113,0,.L249-.L113,0,0
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_resetModule')
	.sect	'.debug_info'
.L250:
	.word	327
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L253,.L252
	.byte	2
	.word	.L146
	.byte	3
	.byte	'IfxCcu6_resetModule',0,1,139,2,6,1,1,1
	.word	.L127,.L473,.L126
	.byte	4
	.byte	'ccu6',0,1,139,2,36
	.word	.L256,.L474
	.byte	5
	.word	.L127,.L473
	.byte	6
	.byte	'passwd',0,1,141,2,12
	.word	.L264,.L475
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_resetModule')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxCcu6_resetModule')
	.sect	'.debug_line'
.L252:
	.word	.L870-.L869
.L869:
	.half	3
	.word	.L872-.L871
.L871:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std/IfxCcu6.c',0,0,0,0,0
.L872:
	.byte	5,6,7,0,5,2
	.word	.L127
	.byte	3,138,2,1,5,53,9
	.half	.L512-.L127
	.byte	3,2,1,5,19,9
	.half	.L511-.L512
	.byte	1,5,31,9
	.half	.L514-.L511
	.byte	3,2,1,5,18,9
	.half	.L515-.L514
	.byte	3,1,1,5,23,9
	.half	.L873-.L515
	.byte	1,5,18,9
	.half	.L874-.L873
	.byte	3,1,1,5,23,9
	.half	.L875-.L874
	.byte	1,5,29,9
	.half	.L876-.L875
	.byte	3,1,1,5,38,9
	.half	.L517-.L876
	.byte	3,2,1,5,30,9
	.half	.L49-.L517
	.byte	1,5,38,9
	.half	.L877-.L49
	.byte	1,5,31,7,9
	.half	.L878-.L877
	.byte	3,4,1,5,20,9
	.half	.L519-.L878
	.byte	3,1,1,5,25,9
	.half	.L879-.L519
	.byte	1,5,29,9
	.half	.L880-.L879
	.byte	3,1,1,5,1,9
	.half	.L521-.L880
	.byte	3,1,1,7,9
	.half	.L254-.L521
	.byte	0,1,1
.L870:
	.sdecl	'.debug_ranges',debug,cluster('IfxCcu6_resetModule')
	.sect	'.debug_ranges'
.L253:
	.word	-1,.L127,0,.L254-.L127,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_connectTrigger')
	.sect	'.debug_loc'
.L104:
	.word	-1,.L105,0,.L476-.L105
	.half	2
	.byte	138,0
	.word	.L476-.L105,.L255-.L105
	.half	2
	.byte	138,8
	.word	.L255-.L105,.L255-.L105
	.half	2
	.byte	138,0
	.word	0,0
.L271:
	.word	0,0
.L257:
	.word	-1,.L105,0,.L255-.L105
	.half	1
	.byte	100
	.word	0,0
.L266:
	.word	-1,.L105,.L477-.L105,.L268-.L105
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L273:
	.word	0,0
.L263:
	.word	-1,.L105,0,.L255-.L105
	.half	2
	.byte	145,120
	.word	0,0
.L259:
	.word	-1,.L105,0,.L255-.L105
	.half	1
	.byte	84
	.word	0,0
.L261:
	.word	-1,.L105,0,.L255-.L105
	.half	1
	.byte	85
	.word	0,0
.L265:
	.word	0,0
.L275:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_disableModulationOutput')
	.sect	'.debug_loc'
.L106:
	.word	-1,.L107,0,.L450-.L107
	.half	2
	.byte	138,0
	.word	0,0
.L451:
	.word	-1,.L107,0,.L450-.L107
	.half	1
	.byte	100
	.word	0,0
.L453:
	.word	-1,.L107,0,.L450-.L107
	.half	1
	.byte	85
	.word	0,0
.L455:
	.word	-1,.L107,.L478-.L107,.L479-.L107
	.half	1
	.byte	95
	.word	.L482-.L107,.L483-.L107
	.half	1
	.byte	95
	.word	0,0
.L454:
	.word	-1,.L107,.L480-.L107,.L481-.L107
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L452:
	.word	-1,.L107,0,.L450-.L107
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_disableModule')
	.sect	'.debug_loc'
.L108:
	.word	-1,.L109,0,.L456-.L109
	.half	2
	.byte	138,0
	.word	0,0
.L457:
	.word	-1,.L109,0,.L484-.L109
	.half	1
	.byte	100
	.word	.L485-.L109,.L456-.L109
	.half	1
	.byte	111
	.word	0,0
.L461:
	.word	0,0
.L458:
	.word	-1,.L109,.L484-.L109,.L486-.L109
	.half	1
	.byte	82
	.word	.L487-.L109,.L9-.L109
	.half	1
	.byte	95
	.word	.L486-.L109,.L488-.L109
	.half	1
	.byte	84
	.word	.L489-.L109,.L490-.L109
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_enableModulationOutput')
	.sect	'.debug_loc'
.L110:
	.word	-1,.L111,0,.L463-.L111
	.half	2
	.byte	138,0
	.word	0,0
.L464:
	.word	-1,.L111,0,.L463-.L111
	.half	1
	.byte	100
	.word	0,0
.L466:
	.word	-1,.L111,0,.L463-.L111
	.half	1
	.byte	85
	.word	0,0
.L468:
	.word	-1,.L111,.L491-.L111,.L14-.L111
	.half	1
	.byte	95
	.word	.L494-.L111,.L16-.L111
	.half	1
	.byte	95
	.word	0,0
.L467:
	.word	-1,.L111,.L492-.L111,.L493-.L111
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L465:
	.word	-1,.L111,0,.L463-.L111
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_enableModule')
	.sect	'.debug_loc'
.L112:
	.word	-1,.L113,0,.L469-.L113
	.half	2
	.byte	138,0
	.word	0,0
.L470:
	.word	-1,.L113,0,.L495-.L113
	.half	1
	.byte	100
	.word	.L496-.L113,.L469-.L113
	.half	1
	.byte	111
	.word	0,0
.L472:
	.word	0,0
.L471:
	.word	-1,.L113,.L495-.L113,.L497-.L113
	.half	1
	.byte	82
	.word	.L498-.L113,.L18-.L113
	.half	1
	.byte	95
	.word	.L497-.L113,.L499-.L113
	.half	1
	.byte	84
	.word	.L500-.L113,.L501-.L113
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_getAddress')
	.sect	'.debug_loc'
.L114:
	.word	-1,.L115,0,.L422-.L115
	.half	2
	.byte	138,0
	.word	0,0
.L424:
	.word	-1,.L115,0,.L422-.L115
	.half	1
	.byte	84
	.word	0,0
.L425:
	.word	-1,.L115,.L502-.L115,.L21-.L115
	.half	1
	.byte	98
	.word	.L22-.L115,.L422-.L115
	.half	1
	.byte	98
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_getCaptureRegisterValue')
	.sect	'.debug_loc'
.L116:
	.word	-1,.L117,0,.L426-.L117
	.half	2
	.byte	138,0
	.word	0,0
.L427:
	.word	-1,.L117,0,.L426-.L117
	.half	1
	.byte	100
	.word	0,0
.L428:
	.word	-1,.L117,0,.L426-.L117
	.half	1
	.byte	84
	.word	0,0
.L429:
	.word	-1,.L117,.L503-.L117,.L426-.L117
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_getCaptureShadowRegisterValue')
	.sect	'.debug_loc'
.L118:
	.word	-1,.L119,0,.L430-.L119
	.half	2
	.byte	138,0
	.word	0,0
.L431:
	.word	-1,.L119,0,.L430-.L119
	.half	1
	.byte	100
	.word	0,0
.L432:
	.word	-1,.L119,0,.L430-.L119
	.half	1
	.byte	84
	.word	0,0
.L433:
	.word	-1,.L119,.L504-.L119,.L430-.L119
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_getIndex')
	.sect	'.debug_loc'
.L120:
	.word	-1,.L121,0,.L434-.L121
	.half	2
	.byte	138,0
	.word	0,0
.L435:
	.word	-1,.L121,0,.L434-.L121
	.half	1
	.byte	100
	.word	0,0
.L436:
	.word	-1,.L121,.L506-.L121,.L434-.L121
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L437:
	.word	-1,.L121,.L505-.L121,.L434-.L121
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_getSrcAddress')
	.sect	'.debug_loc'
.L122:
	.word	-1,.L123,0,.L439-.L123
	.half	2
	.byte	138,0
	.word	0,0
.L440:
	.word	-1,.L123,0,.L507-.L123
	.half	1
	.byte	100
	.word	0,0
.L443:
	.word	-1,.L123,.L507-.L123,.L439-.L123
	.half	1
	.byte	82
	.word	0,0
.L441:
	.word	-1,.L123,0,.L507-.L123
	.half	1
	.byte	84
	.word	.L508-.L123,.L439-.L123
	.half	1
	.byte	88
	.word	0,0
.L444:
	.word	-1,.L123,.L509-.L123,.L439-.L123
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_readTimer')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L125,0,.L445-.L125
	.half	2
	.byte	138,0
	.word	0,0
.L446:
	.word	-1,.L125,0,.L445-.L125
	.half	1
	.byte	100
	.word	0,0
.L449:
	.word	-1,.L125,.L510-.L125,.L46-.L125
	.half	1
	.byte	82
	.word	.L47-.L125,.L445-.L125
	.half	1
	.byte	82
	.word	0,0
.L448:
	.word	-1,.L125,0,.L445-.L125
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_resetModule')
	.sect	'.debug_loc'
.L126:
	.word	-1,.L127,0,.L473-.L127
	.half	2
	.byte	138,0
	.word	0,0
.L474:
	.word	-1,.L127,0,.L511-.L127
	.half	1
	.byte	100
	.word	.L512-.L127,.L473-.L127
	.half	1
	.byte	111
	.word	0,0
.L475:
	.word	-1,.L127,.L511-.L127,.L513-.L127
	.half	1
	.byte	82
	.word	.L514-.L127,.L473-.L127
	.half	1
	.byte	88
	.word	.L513-.L127,.L515-.L127
	.half	1
	.byte	84
	.word	.L516-.L127,.L517-.L127
	.half	1
	.byte	84
	.word	.L518-.L127,.L519-.L127
	.half	1
	.byte	84
	.word	.L520-.L127,.L521-.L127
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_routeInterruptNode')
	.sect	'.debug_loc'
.L128:
	.word	-1,.L129,0,.L277-.L129
	.half	2
	.byte	138,0
	.word	0,0
.L278:
	.word	-1,.L129,0,.L277-.L129
	.half	1
	.byte	100
	.word	0,0
.L282:
	.word	-1,.L129,0,.L277-.L129
	.half	1
	.byte	85
	.word	0,0
.L280:
	.word	-1,.L129,0,.L277-.L129
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setOutputPassiveLevel')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L131,0,.L283-.L131
	.half	2
	.byte	138,0
	.word	0,0
.L284:
	.word	-1,.L131,0,.L283-.L131
	.half	1
	.byte	100
	.word	0,0
.L286:
	.word	-1,.L131,0,.L283-.L131
	.half	1
	.byte	84
	.word	0,0
.L293:
	.word	-1,.L131,.L523-.L131,.L524-.L131
	.half	1
	.byte	95
	.word	0,0
.L292:
	.word	0,0
.L288:
	.word	-1,.L131,0,.L522-.L131
	.half	1
	.byte	85
	.word	.L71-.L131,.L72-.L131
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setT12CaptureCompareState')
	.sect	'.debug_loc'
.L132:
	.word	-1,.L133,0,.L294-.L133
	.half	2
	.byte	138,0
	.word	0,0
.L295:
	.word	-1,.L133,0,.L294-.L133
	.half	1
	.byte	100
	.word	0,0
.L297:
	.word	-1,.L133,0,.L294-.L133
	.half	1
	.byte	84
	.word	0,0
.L300:
	.word	-1,.L133,.L527-.L133,.L528-.L133
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L301:
	.word	-1,.L133,.L529-.L133,.L530-.L133
	.half	1
	.byte	81
	.word	.L531-.L133,.L532-.L133
	.half	1
	.byte	81
	.word	.L526-.L133,.L533-.L133
	.half	1
	.byte	95
	.word	0,0
.L299:
	.word	-1,.L133,0,.L294-.L133
	.half	1
	.byte	85
	.word	.L525-.L133,.L526-.L133
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setT12CompareValue')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L135,0,.L302-.L135
	.half	2
	.byte	138,0
	.word	0,0
.L303:
	.word	-1,.L135,0,.L302-.L135
	.half	1
	.byte	100
	.word	0,0
.L304:
	.word	-1,.L135,0,.L302-.L135
	.half	1
	.byte	84
	.word	0,0
.L305:
	.word	-1,.L135,0,.L302-.L135
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setT12Frequency')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L137,0,.L307-.L137
	.half	2
	.byte	138,0
	.word	0,0
.L319:
	.word	-1,.L137,.L551-.L137,.L355-.L137
	.half	1
	.byte	95
	.word	0,0
.L308:
	.word	-1,.L137,0,.L534-.L137
	.half	1
	.byte	100
	.word	.L535-.L137,.L307-.L137
	.half	1
	.byte	111
	.word	0,0
.L357:
	.word	0,0
.L334:
	.word	0,0
.L346:
	.word	0,0
.L366:
	.word	0,0
.L326:
	.word	0,0
.L321:
	.word	-1,.L137,.L547-.L137,.L548-.L137
	.half	1
	.byte	95
	.word	0,0
.L312:
	.word	-1,.L137,0,.L534-.L137
	.half	1
	.byte	86
	.word	.L536-.L137,.L307-.L137
	.half	1
	.byte	90
	.word	0,0
.L315:
	.word	-1,.L137,.L537-.L137,.L540-.L137
	.half	1
	.byte	82
	.word	.L540-.L137,.L541-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	.L88-.L137,.L89-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	.L87-.L137,.L91-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L314:
	.word	-1,.L137,.L534-.L137,.L537-.L137
	.half	1
	.byte	82
	.word	.L538-.L137,.L307-.L137
	.half	1
	.byte	91
	.word	0,0
.L309:
	.word	-1,.L137,0,.L534-.L137
	.half	1
	.byte	84
	.word	.L538-.L137,.L539-.L137
	.half	1
	.byte	88
	.word	.L317-.L137,.L87-.L137
	.half	1
	.byte	82
	.word	.L91-.L137,.L307-.L137
	.half	1
	.byte	82
	.word	0,0
.L350:
	.word	0,0
.L362:
	.word	-1,.L137,.L554-.L137,.L90-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L353:
	.word	-1,.L137,.L549-.L137,.L550-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L368:
	.word	0,0
.L310:
	.word	-1,.L137,0,.L534-.L137
	.half	1
	.byte	85
	.word	.L541-.L137,.L543-.L137
	.half	1
	.byte	89
	.word	.L88-.L137,.L89-.L137
	.half	1
	.byte	89
	.word	0,0
.L318:
	.word	-1,.L137,.L323-.L137,.L544-.L137
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L313:
	.word	-1,.L137,.L542-.L137,.L307-.L137
	.half	1
	.byte	83
	.word	0,0
.L361:
	.word	-1,.L137,.L552-.L137,.L553-.L137
	.half	1
	.byte	95
	.word	0,0
.L352:
	.word	-1,.L137,.L546-.L137,.L87-.L137
	.half	1
	.byte	81
	.word	0,0
.L336:
	.word	0,0
.L338:
	.word	0,0
.L341:
	.word	-1,.L137,.L545-.L137,.L546-.L137
	.half	1
	.byte	81
	.word	0,0
.L359:
	.word	0,0
.L348:
	.word	0,0
.L328:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setT12InputSignal')
	.sect	'.debug_loc'
.L138:
	.word	-1,.L139,0,.L370-.L139
	.half	2
	.byte	138,0
	.word	0,0
.L371:
	.word	-1,.L139,0,.L370-.L139
	.half	1
	.byte	100
	.word	0,0
.L373:
	.word	-1,.L139,0,.L370-.L139
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setT13CaptureCompareState')
	.sect	'.debug_loc'
.L140:
	.word	-1,.L141,0,.L374-.L141
	.half	2
	.byte	138,0
	.word	0,0
.L375:
	.word	-1,.L141,0,.L374-.L141
	.half	1
	.byte	100
	.word	0,0
.L378:
	.word	-1,.L141,.L557-.L141,.L558-.L141
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L379:
	.word	-1,.L141,.L559-.L141,.L560-.L141
	.half	1
	.byte	81
	.word	.L561-.L141,.L562-.L141
	.half	1
	.byte	81
	.word	.L556-.L141,.L563-.L141
	.half	1
	.byte	95
	.word	0,0
.L377:
	.word	0,0
.L376:
	.word	-1,.L141,0,.L374-.L141
	.half	1
	.byte	84
	.word	.L555-.L141,.L556-.L141
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setT13Frequency')
	.sect	'.debug_loc'
.L142:
	.word	-1,.L143,0,.L380-.L143
	.half	2
	.byte	138,0
	.word	0,0
.L390:
	.word	-1,.L143,.L581-.L143,.L413-.L143
	.half	1
	.byte	95
	.word	0,0
.L381:
	.word	-1,.L143,0,.L564-.L143
	.half	1
	.byte	100
	.word	.L565-.L143,.L380-.L143
	.half	1
	.byte	111
	.word	0,0
.L414:
	.word	0,0
.L402:
	.word	0,0
.L408:
	.word	0,0
.L396:
	.word	0,0
.L391:
	.word	-1,.L143,.L576-.L143,.L577-.L143
	.half	1
	.byte	95
	.word	0,0
.L386:
	.word	-1,.L143,.L569-.L143,.L571-.L143
	.half	1
	.byte	82
	.word	.L571-.L143,.L393-.L143
	.half	5
	.byte	144,32,157,32,0
	.word	.L100-.L143,.L102-.L143
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L385:
	.word	-1,.L143,.L564-.L143,.L569-.L143
	.half	1
	.byte	82
	.word	.L570-.L143,.L380-.L143
	.half	1
	.byte	89
	.word	0,0
.L382:
	.word	-1,.L143,0,.L564-.L143
	.half	1
	.byte	84
	.word	.L566-.L143,.L97-.L143
	.half	1
	.byte	95
	.word	.L388-.L143,.L100-.L143
	.half	1
	.byte	82
	.word	.L102-.L143,.L380-.L143
	.half	1
	.byte	82
	.word	0,0
.L410:
	.word	0,0
.L417:
	.word	-1,.L143,.L584-.L143,.L101-.L143
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L412:
	.word	-1,.L143,.L579-.L143,.L580-.L143
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L383:
	.word	-1,.L143,0,.L564-.L143
	.half	1
	.byte	85
	.word	.L567-.L143,.L568-.L143
	.half	1
	.byte	88
	.word	.L100-.L143,.L102-.L143
	.half	1
	.byte	88
	.word	0,0
.L389:
	.word	-1,.L143,.L393-.L143,.L573-.L143
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L384:
	.word	-1,.L143,.L572-.L143,.L380-.L143
	.half	1
	.byte	83
	.word	0,0
.L416:
	.word	-1,.L143,.L582-.L143,.L583-.L143
	.half	1
	.byte	95
	.word	0,0
.L411:
	.word	-1,.L143,.L578-.L143,.L100-.L143
	.half	1
	.byte	81
	.word	0,0
.L403:
	.word	0,0
.L404:
	.word	0,0
.L405:
	.word	-1,.L143,.L574-.L143,.L575-.L143
	.half	1
	.byte	82
	.word	0,0
.L415:
	.word	0,0
.L409:
	.word	0,0
.L398:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxCcu6_setT13InputSignal')
	.sect	'.debug_loc'
.L144:
	.word	-1,.L145,0,.L418-.L145
	.half	2
	.byte	138,0
	.word	0,0
.L419:
	.word	-1,.L145,0,.L418-.L145
	.half	1
	.byte	100
	.word	0,0
.L421:
	.word	-1,.L145,0,.L418-.L145
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L881:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_connectTrigger')
	.sect	'.debug_frame'
	.word	44
	.word	.L881,.L105,.L255-.L105
	.byte	8,18,8,19,8,21,8,22,8,23,4
	.word	(.L476-.L105)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L255-.L476)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_disableModulationOutput')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L107,.L450-.L107
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_disableModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L881,.L109,.L456-.L109
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_enableModulationOutput')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L111,.L463-.L111
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_enableModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L881,.L113,.L469-.L113
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_getAddress')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L115,.L422-.L115
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_getCaptureRegisterValue')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L117,.L426-.L117
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_getCaptureShadowRegisterValue')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L119,.L430-.L119
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_getIndex')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L121,.L434-.L121
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_getSrcAddress')
	.sect	'.debug_frame'
	.word	12
	.word	.L881,.L123,.L439-.L123
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_readTimer')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L125,.L445-.L125
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_resetModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L881,.L127,.L473-.L127
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_routeInterruptNode')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L129,.L277-.L129
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setOutputPassiveLevel')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L131,.L283-.L131
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setT12CaptureCompareState')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L133,.L294-.L133
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setT12CompareValue')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L135,.L302-.L135
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setT12Frequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L881,.L137,.L307-.L137
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setT12InputSignal')
	.sect	'.debug_frame'
	.word	20
	.word	.L881,.L139,.L370-.L139
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setT13CaptureCompareState')
	.sect	'.debug_frame'
	.word	24
	.word	.L881,.L141,.L374-.L141
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setT13Frequency')
	.sect	'.debug_frame'
	.word	12
	.word	.L881,.L143,.L380-.L143
	.sdecl	'.debug_frame',debug,cluster('IfxCcu6_setT13InputSignal')
	.sect	'.debug_frame'
	.word	20
	.word	.L881,.L145,.L418-.L145
	.byte	8,18,8,19,8,22,8,23
	; Module end
