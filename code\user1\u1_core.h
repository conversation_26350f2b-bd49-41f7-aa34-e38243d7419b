/*
 * u1_core.h - 核心逻辑层接口
 * 作者: BMW_智能车组_Baiyuyu_Xiaohuihui_Wzz
 * 创建时间: 2025年07月06日
 * 描述: 4G模块核心逻辑层，整合状态机管理、AT指令处理、WebSocket通信、讯飞ASR协议
 */

#ifndef U1_CORE_H
#define U1_CORE_H

#include "zf_common_headfile.h" /* 统一包含项目总头文件，确保类型定义一致 */
#include "u1_config.h"
#include "u1_adapter.h"

/* 
 * ========================================================================
 * 状态枚举定义
 * ========================================================================
 */
#define U1_STATE_IDLE           0   /* 空闲状态 */
#define U1_STATE_INIT           1   /* 初始化状态 */
#define U1_STATE_NETWORK_CHECK  2   /* 网络检查状态 */
#define U1_STATE_ASR_CONNECT    3   /* ASR服务连接状态 */
#define U1_STATE_RECOGNIZING    4   /* 正在识别状态 */
#define U1_STATE_RESULT_READY   5   /* 结果就绪状态 */
#define U1_STATE_ERROR          6   /* 错误状态 */
typedef uint8 u1_state_t;

/* 
 * ========================================================================
 * 错误码定义
 * ========================================================================
 */
#define U1_ERROR_NONE               0   /* 无错误 */
#define U1_ERROR_INIT_FAILED        1   /* 初始化失败 */
#define U1_ERROR_NETWORK_FAILED     2   /* 网络连接失败 */
#define U1_ERROR_ASR_CONNECT_FAILED 3   /* ASR服务连接失败 */
#define U1_ERROR_SEND_FAILED        4   /* 数据发送失败 */
#define U1_ERROR_TIMEOUT            5   /* 操作超时 */
#define U1_ERROR_INVALID_PARAM      6   /* 无效参数 */
#define U1_ERROR_BUFFER_FULL        7   /* 缓冲区满 */
#define U1_ERROR_UNKNOWN            8   /* 未知错误 */
typedef uint8 u1_error_t;

/* 
 * ========================================================================
 * 数据结构定义
 * ========================================================================
 */
/* 识别结果结构体 */
typedef struct {
    char text[U1_RESULT_BUFFER_SIZE];   /* 识别文本 */
    uint8 confidence;                   /* 置信度 0-100 */
    boolean is_final;                   /* 是否为最终结果 */
    uint32 timestamp;                   /* 时间戳 */
} u1_result_t;

/* 状态信息结构体 */
typedef struct {
    u1_state_t current_state;           /* 当前状态 */
    u1_error_t last_error;              /* 最后错误码 */
    uint32 retry_count;                 /* 重试次数 */
    uint32 uptime_ms;                   /* 运行时间(ms) */
    boolean network_ready;              /* 网络就绪标志 */
    boolean asr_connected;              /* ASR连接标志 */
} u1_status_t;

/* 
 * ========================================================================
 * 核心接口函数 - 与cpu0_main.c调用完全匹配
 * ========================================================================
 */

/* 初始化4G模块 - 对应cpu0_main.c中的u1_init()调用 */
boolean u1_init(void);

/* 主处理函数 - 对应cpu0_main.c中的u1_process()调用 */
void u1_process(void);

/* 开始语音识别 - 对应cpu0_main.c中的u1_start_recognition()调用 */
boolean u1_start_recognition(void);

/* 获取识别结果 - 对应cpu0_main.c中的u1_get_result()调用 */
boolean u1_get_result(char* buffer, uint32 size);

/* 
 * ========================================================================
 * 扩展接口函数
 * ========================================================================
 */

/* 获取当前状态 */
u1_state_t u1_get_state(void);

/* 获取详细状态信息 */
void u1_get_status(u1_status_t* status);

/* 获取最后错误码 */
u1_error_t u1_get_last_error(void);

/* 停止语音识别 */
void u1_stop_recognition(void);

/* 重置模块 */
boolean u1_reset(void);

/* 发送音频数据（用于外部音频输入） */
boolean u1_send_audio_data(const uint8* audio_data, uint32 len, boolean is_last);

/* 获取结果结构体（包含置信度等详细信息） */
boolean u1_get_result_detail(u1_result_t* result);

/* 设置ASR配置（运行时配置） */
boolean u1_set_asr_config(const char* app_id, const char* api_key, const char* api_secret);

/* 
 * ========================================================================
 * 调试和监控接口
 * ========================================================================
 */

/* 获取错误描述 */
const char* u1_get_error_description(u1_error_t error_code);

/* 获取状态描述 */
const char* u1_get_state_description(u1_state_t state);

/* 执行自检 */
boolean u1_self_test(void);

/* 获取统计信息 */
void u1_get_statistics(uint32* total_recognitions_out, uint32* success_count_out, uint32* error_count_out);

#endif /* U1_CORE_H */ 