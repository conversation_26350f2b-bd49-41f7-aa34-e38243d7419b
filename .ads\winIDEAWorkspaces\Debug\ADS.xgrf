 <Root>
  <SWPlugins>
   <Editor>
    <EditorOptions>
     <REG_HEADER>
      <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
      <VersionHigh>0</VersionHigh>
      <VersionLow>1</VersionLow>
     </REG_HEADER>
     <REG_ROOT>
      <RegVer>1297076244</RegVer>
      <EditorPersist>
       <EditorPersist>
        <__.0>
         <BookmarkList></BookmarkList>
         <CaretPos>1848</CaretPos>
         <Encoding>6</Encoding>
         <FileName>D:\myADS\first\Seekfree_TC264_Opensource_Library\user\cpu0_main.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>10</ScrollPos>
        </__.0>
        <__.10>
         <BookmarkList></BookmarkList>
         <CaretPos>15774</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\iLLD\TC26B\Tricore\Asclin\Asc\IfxAsclin_Asc.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>375</ScrollPos>
        </__.10>
        <__.11>
         <BookmarkList></BookmarkList>
         <CaretPos>14594</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_device\zf_device_tft180.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>246</ScrollPos>
        </__.11>
        <__.12>
         <BookmarkList></BookmarkList>
         <CaretPos>2795</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_driver\zf_driver_gpio.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>43</ScrollPos>
        </__.12>
        <__.13>
         <BookmarkList></BookmarkList>
         <CaretPos>13490</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_driver\zf_driver_spi.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>215</ScrollPos>
        </__.13>
        <__.14>
         <BookmarkList></BookmarkList>
         <CaretPos>9606</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\iLLD\TC26B\Tricore\Qspi\Std\IfxQspi.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>266</ScrollPos>
        </__.14>
        <__.15>
         <BookmarkList></BookmarkList>
         <CaretPos>37729</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\iLLD\TC26B\Tricore\Qspi\Std\IfxQspi.h</FileName>
         <FileType>1</FileType>
         <ScrollPos>903</ScrollPos>
        </__.15>
        <__.16>
         <BookmarkList></BookmarkList>
         <CaretPos>22331</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_driver\zf_driver_uart.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>469</ScrollPos>
        </__.16>
        <__.17>
         <BookmarkList></BookmarkList>
         <CaretPos>13544</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\code\user1\u1_core.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>512</ScrollPos>
        </__.17>
        <__.18>
         <BookmarkList></BookmarkList>
         <CaretPos>1728</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\code\user1\u1_adapter.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>47</ScrollPos>
        </__.18>
        <__.19>
         <BookmarkList></BookmarkList>
         <CaretPos>2935</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_driver\zf_driver_timer.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>43</ScrollPos>
        </__.19>
        <__.1>
         <BookmarkList></BookmarkList>
         <CaretPos>1848</CaretPos>
         <Encoding>0</Encoding>
         <FileName>D:\ADS\open source\TC264\TC264_Library-master\Seekfree_TC264_Opensource_Library\user\cpu0_main.c</FileName>
         <FileType>0</FileType>
         <ScrollPos>12</ScrollPos>
        </__.1>
        <__.20>
         <BookmarkList></BookmarkList>
         <CaretPos>3712</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\iLLD\TC26B\Tricore\Asclin\Std\IfxAsclin.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>69</ScrollPos>
        </__.20>
        <__.21>
         <BookmarkList></BookmarkList>
         <CaretPos>25934</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\iLLD\TC26B\Tricore\Stm\Std\IfxStm.h</FileName>
         <FileType>1</FileType>
         <ScrollPos>552</ScrollPos>
        </__.21>
        <__.22>
         <BookmarkList></BookmarkList>
         <CaretPos>58938</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\iLLD\TC26B\Tricore\Scu\Std\IfxScuCcu.h</FileName>
         <FileType>1</FileType>
         <ScrollPos>1100</ScrollPos>
        </__.22>
        <__.2>
         <BookmarkList></BookmarkList>
         <CaretPos>6623</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\user\cpu0_main.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>128</ScrollPos>
        </__.2>
        <__.3>
         <BookmarkList></BookmarkList>
         <CaretPos>1856</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code\Mark_1\user\cpu0_main.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>20</ScrollPos>
        </__.3>
        <__.4>
         <BookmarkList></BookmarkList>
         <CaretPos>4287</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_common\zf_common_clock.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>77</ScrollPos>
        </__.4>
        <__.5>
         <BookmarkList></BookmarkList>
         <CaretPos>29768</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\iLLD\TC26B\Tricore\Cpu\Std\IfxCpu.h</FileName>
         <FileType>1</FileType>
         <ScrollPos>773</ScrollPos>
        </__.5>
        <__.6>
         <BookmarkList></BookmarkList>
         <CaretPos>4120</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\infineon_libraries\Service\CpuGeneric\SysSe\Bsp\Bsp.h</FileName>
         <FileType>1</FileType>
         <ScrollPos>86</ScrollPos>
        </__.6>
        <__.7>
         <BookmarkList></BookmarkList>
         <CaretPos>2577</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_common\zf_common_interrupt.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>42</ScrollPos>
        </__.7>
        <__.8>
         <BookmarkList></BookmarkList>
         <CaretPos>584</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\code\Device.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>0</ScrollPos>
        </__.8>
        <__.9>
         <BookmarkList></BookmarkList>
         <CaretPos>14238</CaretPos>
         <Encoding>6</Encoding>
         <FileName>C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\libraries\zf_common\zf_common_debug.c</FileName>
         <FileType>1</FileType>
         <ScrollPos>350</ScrollPos>
        </__.9>
       </EditorPersist>
      </EditorPersist>
     </REG_ROOT>
    </EditorOptions>
   </Editor>
   <LA>
    <LAOptions>
     <REG_HEADER>
      <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
      <VersionHigh>0</VersionHigh>
      <VersionLow>1</VersionLow>
     </REG_HEADER>
     <REG_ROOT>
      <RegVer>1297076244</RegVer>
     </REG_ROOT>
    </LAOptions>
   </LA>
  </SWPlugins>
  <winIDEA>
   <REG_HEADER>
    <Title>BF283F24-70B9-4f97-86E6-0AD5BAAE9E01</Title>
    <VersionHigh>0</VersionHigh>
    <VersionLow>1</VersionLow>
   </REG_HEADER>
   <REG_ROOT>
    <RegVer>1297076244</RegVer>
    <DesktopLayout>
     <ToolbarData>
      <Toolbars.CMake>
       <Opened>false</Opened>
      </Toolbars.CMake>
      <Toolbars.Debug>
       <Opened>true</Opened>
      </Toolbars.Debug>
      <Toolbars.FBM>
       <Opened>false</Opened>
      </Toolbars.FBM>
      <Toolbars.File>
       <Opened>true</Opened>
      </Toolbars.File>
      <Toolbars.Project>
       <Opened>true</Opened>
      </Toolbars.Project>
      <Toolbars.View>
       <Opened>true</Opened>
      </Toolbars.View>
     </ToolbarData>
     <ViewData>
      <AllViewBaseData>
       <__.0>
        <ViewId>4</ViewId>
        <ViewType>Disassembly</ViewType>
       </__.0>
       <__.10>
        <ViewId>12</ViewId>
        <ViewType>Document</ViewType>
       </__.10>
       <__.11>
        <ViewId>13</ViewId>
        <ViewType>Document</ViewType>
       </__.11>
       <__.12>
        <ViewId>14</ViewId>
        <ViewType>Document</ViewType>
       </__.12>
       <__.13>
        <ViewId>15</ViewId>
        <ViewType>Document</ViewType>
       </__.13>
       <__.14>
        <ViewId>16</ViewId>
        <ViewType>Document</ViewType>
       </__.14>
       <__.15>
        <ViewId>17</ViewId>
        <ViewType>Document</ViewType>
       </__.15>
       <__.16>
        <ViewId>18</ViewId>
        <ViewType>Document</ViewType>
       </__.16>
       <__.17>
        <ViewId>19</ViewId>
        <ViewType>Document</ViewType>
       </__.17>
       <__.18>
        <ViewId>20</ViewId>
        <ViewType>Document</ViewType>
       </__.18>
       <__.19>
        <ViewId>21</ViewId>
        <ViewType>Document</ViewType>
       </__.19>
       <__.1>
        <ViewId>2</ViewId>
        <ViewType>Locals</ViewType>
       </__.1>
       <__.20>
        <ViewId>22</ViewId>
        <ViewType>Document</ViewType>
       </__.20>
       <__.21>
        <ViewId>23</ViewId>
        <ViewType>Document</ViewType>
       </__.21>
       <__.22>
        <ViewId>24</ViewId>
        <ViewType>Document</ViewType>
       </__.22>
       <__.23>
        <ViewId>1</ViewId>
        <ViewType>Output_Progress</ViewType>
       </__.23>
       <__.24>
        <ViewId>0</ViewId>
        <ViewType>Symbols</ViewType>
       </__.24>
       <__.2>
        <ViewId>3</ViewId>
        <ViewType>CustomWatch</ViewType>
       </__.2>
       <__.3>
        <ViewId>5</ViewId>
        <ViewType>Document</ViewType>
       </__.3>
       <__.4>
        <ViewId>6</ViewId>
        <ViewType>Document</ViewType>
       </__.4>
       <__.5>
        <ViewId>7</ViewId>
        <ViewType>Document</ViewType>
       </__.5>
       <__.6>
        <ViewId>8</ViewId>
        <ViewType>Document</ViewType>
       </__.6>
       <__.7>
        <ViewId>9</ViewId>
        <ViewType>Document</ViewType>
       </__.7>
       <__.8>
        <ViewId>10</ViewId>
        <ViewType>Document</ViewType>
       </__.8>
       <__.9>
        <ViewId>11</ViewId>
        <ViewType>Document</ViewType>
       </__.9>
      </AllViewBaseData>
      <DissasemblyViews>
       <__.0>
        <ViewId>4</ViewId>
        <ViewType>Disassembly</ViewType>
        <ColumnData>
         <__.0>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>100</Width>
         </__.0>
         <__.1>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>80</Width>
         </__.1>
         <__.2>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>400</Width>
         </__.2>
        </ColumnData>
       </__.0>
      </DissasemblyViews>
      <DocumentViews>
       <__.0>
        <RelativePath>..\..\..\user\cpu0_main.c</RelativePath>
        <ViewId>5</ViewId>
        <ViewType>Document</ViewType>
       </__.0>
       <__.10>
        <RelativePath>..\..\..\libraries\infineon_libraries\iLLD\TC26B\Tricore\Qspi\Std\IfxQspi.c</RelativePath>
        <ViewId>15</ViewId>
        <ViewType>Document</ViewType>
       </__.10>
       <__.11>
        <RelativePath>..\..\..\libraries\infineon_libraries\iLLD\TC26B\Tricore\Qspi\Std\IfxQspi.h</RelativePath>
        <ViewId>16</ViewId>
        <ViewType>Document</ViewType>
       </__.11>
       <__.12>
        <RelativePath>..\..\..\code\user1\u1_core.c</RelativePath>
        <ViewId>17</ViewId>
        <ViewType>Document</ViewType>
       </__.12>
       <__.13>
        <RelativePath>..\..\..\code\user1\u1_adapter.c</RelativePath>
        <ViewId>18</ViewId>
        <ViewType>Document</ViewType>
       </__.13>
       <__.14>
        <RelativePath>..\..\..\libraries\zf_driver\zf_driver_uart.c</RelativePath>
        <ViewId>19</ViewId>
        <ViewType>Document</ViewType>
       </__.14>
       <__.15>
        <RelativePath>..\..\..\libraries\infineon_libraries\iLLD\TC26B\Tricore\Asclin\Std\IfxAsclin.c</RelativePath>
        <ViewId>20</ViewId>
        <ViewType>Document</ViewType>
       </__.15>
       <__.16>
        <RelativePath>..\..\..\libraries\infineon_libraries\iLLD\TC26B\Tricore\Asclin\Asc\IfxAsclin_Asc.c</RelativePath>
        <ViewId>21</ViewId>
        <ViewType>Document</ViewType>
       </__.16>
       <__.17>
        <RelativePath>..\..\..\libraries\zf_driver\zf_driver_timer.c</RelativePath>
        <ViewId>22</ViewId>
        <ViewType>Document</ViewType>
       </__.17>
       <__.18>
        <RelativePath>..\..\..\libraries\infineon_libraries\iLLD\TC26B\Tricore\Stm\Std\IfxStm.h</RelativePath>
        <ViewId>23</ViewId>
        <ViewType>Document</ViewType>
       </__.18>
       <__.19>
        <RelativePath>..\..\..\libraries\infineon_libraries\iLLD\TC26B\Tricore\Scu\Std\IfxScuCcu.h</RelativePath>
        <ViewId>24</ViewId>
        <ViewType>Document</ViewType>
       </__.19>
       <__.1>
        <RelativePath>..\..\..\libraries\zf_common\zf_common_clock.c</RelativePath>
        <ViewId>6</ViewId>
        <ViewType>Document</ViewType>
       </__.1>
       <__.2>
        <RelativePath>..\..\..\libraries\infineon_libraries\iLLD\TC26B\Tricore\Cpu\Std\IfxCpu.h</RelativePath>
        <ViewId>7</ViewId>
        <ViewType>Document</ViewType>
       </__.2>
       <__.3>
        <RelativePath>..\..\..\libraries\infineon_libraries\Service\CpuGeneric\SysSe\Bsp\Bsp.h</RelativePath>
        <ViewId>8</ViewId>
        <ViewType>Document</ViewType>
       </__.3>
       <__.4>
        <RelativePath>..\..\..\libraries\zf_common\zf_common_interrupt.c</RelativePath>
        <ViewId>9</ViewId>
        <ViewType>Document</ViewType>
       </__.4>
       <__.5>
        <RelativePath>..\..\..\code\Device.c</RelativePath>
        <ViewId>10</ViewId>
        <ViewType>Document</ViewType>
       </__.5>
       <__.6>
        <RelativePath>..\..\..\libraries\zf_common\zf_common_debug.c</RelativePath>
        <ViewId>11</ViewId>
        <ViewType>Document</ViewType>
       </__.6>
       <__.7>
        <RelativePath>..\..\..\libraries\zf_device\zf_device_tft180.c</RelativePath>
        <ViewId>12</ViewId>
        <ViewType>Document</ViewType>
       </__.7>
       <__.8>
        <RelativePath>..\..\..\libraries\zf_driver\zf_driver_gpio.c</RelativePath>
        <ViewId>13</ViewId>
        <ViewType>Document</ViewType>
       </__.8>
       <__.9>
        <RelativePath>..\..\..\libraries\zf_driver\zf_driver_spi.c</RelativePath>
        <ViewId>14</ViewId>
        <ViewType>Document</ViewType>
       </__.9>
      </DocumentViews>
      <OtherViews>
       <__.0>
        <ViewId>0</ViewId>
        <ViewType>Symbols</ViewType>
       </__.0>
      </OtherViews>
      <OutputViews>
       <__.0>
        <ViewId>1</ViewId>
        <ViewType>Output_Progress</ViewType>
        <FindData>
         <IsInReplaceMode>false</IsInReplaceMode>
         <IsVisible>false</IsVisible>
        </FindData>
       </__.0>
      </OutputViews>
      <VariableViews>
       <__.0>
        <ViewId>2</ViewId>
        <ViewType>Locals</ViewType>
        <ColumnData>
         <__.0>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>180</Width>
         </__.0>
         <__.1>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>80</Width>
         </__.1>
         <__.2>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>80</Width>
         </__.2>
         <__.3>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>140</Width>
         </__.3>
         <__.4>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>400</Width>
         </__.4>
        </ColumnData>
       </__.0>
      </VariableViews>
      <WatchViews>
       <__.0>
        <Name>Watch</Name>
        <ViewId>3</ViewId>
        <ViewType>CustomWatch</ViewType>
        <ColumnData>
         <__.0>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>180</Width>
         </__.0>
         <__.1>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>80</Width>
         </__.1>
         <__.2>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>80</Width>
         </__.2>
         <__.3>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>140</Width>
         </__.3>
         <__.4>
          <Pos>0</Pos>
          <Visible>true</Visible>
          <Width>400</Width>
         </__.4>
        </ColumnData>
        <WatchData>
         <MiscData>
          <InitScript></InitScript>
         </MiscData>
         <PaneMiscData.0>
          <InitScript></InitScript>
         </PaneMiscData.0>
         <PaneMiscData.1>
          <InitScript></InitScript>
         </PaneMiscData.1>
         <PaneMiscData.2>
          <InitScript></InitScript>
         </PaneMiscData.2>
         <PaneMiscData.3>
          <InitScript></InitScript>
         </PaneMiscData.3>
         <VariableData>
          <IsRealTimeEnabled>false</IsRealTimeEnabled>
         </VariableData>
         <WatchPaneData.0>
          <IsRealTimeEnabled>false</IsRealTimeEnabled>
         </WatchPaneData.0>
         <WatchPaneData.1>
          <IsRealTimeEnabled>false</IsRealTimeEnabled>
         </WatchPaneData.1>
         <WatchPaneData.2>
          <IsRealTimeEnabled>false</IsRealTimeEnabled>
         </WatchPaneData.2>
         <WatchPaneData.3>
          <IsRealTimeEnabled>false</IsRealTimeEnabled>
         </WatchPaneData.3>
        </WatchData>
       </__.0>
      </WatchViews>
     </ViewData>
     <ViewLayout>
      <PrimaryDocumentPaneId>5</PrimaryDocumentPaneId>
      <Frames>
       <__.0>
        <FocusedViewId>4</FocusedViewId>
        <FrameState>3</FrameState>
        <IsPrimary>true</IsPrimary>
        <Bounds>
         <Bottom>868</Bottom>
         <Left>100</Left>
         <Right>1124</Right>
         <Top>100</Top>
        </Bounds>
        <Dock>
         <DockType>None</DockType>
         <Extent>1</Extent>
         <IsDocumentPane>false</IsDocumentPane>
         <IsPinned>false</IsPinned>
         <IsTabbed>false</IsTabbed>
         <Orientation>Horizontal</Orientation>
         <TopTabViewId>-1</TopTabViewId>
         <ViewId>-1</ViewId>
         <Children>
          <__.0>
           <DockType>None</DockType>
           <Extent>0.75</Extent>
           <IsDocumentPane>false</IsDocumentPane>
           <IsPinned>false</IsPinned>
           <IsTabbed>false</IsTabbed>
           <Orientation>Vertical</Orientation>
           <TopTabViewId>-1</TopTabViewId>
           <ViewId>-1</ViewId>
           <Children>
            <__.0>
             <DockType>None</DockType>
             <Extent>0.682002</Extent>
             <IsDocumentPane>false</IsDocumentPane>
             <IsPinned>false</IsPinned>
             <IsTabbed>false</IsTabbed>
             <Orientation>Horizontal</Orientation>
             <TopTabViewId>-1</TopTabViewId>
             <ViewId>-1</ViewId>
             <Children>
              <__.0>
               <DockType>View</DockType>
               <Extent>0.25</Extent>
               <IsDocumentPane>false</IsDocumentPane>
               <IsPinned>false</IsPinned>
               <IsTabbed>false</IsTabbed>
               <Orientation>None</Orientation>
               <TopTabViewId>-1</TopTabViewId>
               <ViewId>0</ViewId>
              </__.0>
              <__.1>
               <DockType>None</DockType>
               <Extent>0.75</Extent>
               <IsDocumentPane>true</IsDocumentPane>
               <IsPinned>false</IsPinned>
               <IsTabbed>true</IsTabbed>
               <Orientation>None</Orientation>
               <TopTabViewId>5</TopTabViewId>
               <ViewId>-1</ViewId>
               <Children>
                <__.0>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>5</ViewId>
                </__.0>
                <__.10>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>15</ViewId>
                </__.10>
                <__.11>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>16</ViewId>
                </__.11>
                <__.12>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>17</ViewId>
                </__.12>
                <__.13>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>18</ViewId>
                </__.13>
                <__.14>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>19</ViewId>
                </__.14>
                <__.15>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>20</ViewId>
                </__.15>
                <__.16>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>21</ViewId>
                </__.16>
                <__.17>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>22</ViewId>
                </__.17>
                <__.18>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>23</ViewId>
                </__.18>
                <__.19>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>24</ViewId>
                </__.19>
                <__.1>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>6</ViewId>
                </__.1>
                <__.2>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>7</ViewId>
                </__.2>
                <__.3>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>8</ViewId>
                </__.3>
                <__.4>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>9</ViewId>
                </__.4>
                <__.5>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>10</ViewId>
                </__.5>
                <__.6>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>11</ViewId>
                </__.6>
                <__.7>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>12</ViewId>
                </__.7>
                <__.8>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>13</ViewId>
                </__.8>
                <__.9>
                 <DockType>Document</DockType>
                 <Extent>1</Extent>
                 <IsDocumentPane>false</IsDocumentPane>
                 <IsPinned>false</IsPinned>
                 <IsTabbed>false</IsTabbed>
                 <Orientation>None</Orientation>
                 <TopTabViewId>-1</TopTabViewId>
                 <ViewId>14</ViewId>
                </__.9>
               </Children>
              </__.1>
             </Children>
            </__.0>
            <__.1>
             <DockType>None</DockType>
             <Extent>0.317998</Extent>
             <IsDocumentPane>false</IsDocumentPane>
             <IsPinned>false</IsPinned>
             <IsTabbed>false</IsTabbed>
             <Orientation>Horizontal</Orientation>
             <TopTabViewId>-1</TopTabViewId>
             <ViewId>-1</ViewId>
             <Children>
              <__.0>
               <DockType>View</DockType>
               <Extent>0.4</Extent>
               <IsDocumentPane>false</IsDocumentPane>
               <IsPinned>false</IsPinned>
               <IsTabbed>false</IsTabbed>
               <Orientation>Vertical</Orientation>
               <TopTabViewId>-1</TopTabViewId>
               <ViewId>1</ViewId>
              </__.0>
              <__.1>
               <DockType>View</DockType>
               <Extent>0.3</Extent>
               <IsDocumentPane>false</IsDocumentPane>
               <IsPinned>false</IsPinned>
               <IsTabbed>false</IsTabbed>
               <Orientation>None</Orientation>
               <TopTabViewId>-1</TopTabViewId>
               <ViewId>2</ViewId>
              </__.1>
              <__.2>
               <DockType>View</DockType>
               <Extent>0.3</Extent>
               <IsDocumentPane>false</IsDocumentPane>
               <IsPinned>false</IsPinned>
               <IsTabbed>false</IsTabbed>
               <Orientation>Vertical</Orientation>
               <TopTabViewId>-1</TopTabViewId>
               <ViewId>3</ViewId>
              </__.2>
             </Children>
            </__.1>
           </Children>
          </__.0>
          <__.1>
           <DockType>View</DockType>
           <Extent>0.25</Extent>
           <IsDocumentPane>false</IsDocumentPane>
           <IsPinned>false</IsPinned>
           <IsTabbed>false</IsTabbed>
           <Orientation>Vertical</Orientation>
           <TopTabViewId>-1</TopTabViewId>
           <ViewId>4</ViewId>
          </__.1>
         </Children>
        </Dock>
       </__.0>
      </Frames>
     </ViewLayout>
    </DesktopLayout>
    <Environment>
     <GlobalData>
      <FindInFilesDialogData>
       <CaseSensitive>false</CaseSensitive>
       <IncludeAllFilesInProjectFolders>false</IncludeAllFilesInProjectFolders>
       <IncludeProjectDependencies>true</IncludeProjectDependencies>
       <IncludeProjectFiles>true</IncludeProjectFiles>
       <LastFileType></LastFileType>
       <Subfolders>false</Subfolders>
       <WholeWords>false</WholeWords>
      </FindInFilesDialogData>
     </GlobalData>
     <History>
     </History>
     <Options>
      <CallstackWindow>
       <LinesAfter>2</LinesAfter>
       <LinesBefore>2</LinesBefore>
       <Path>Module</Path>
       <ShowSourceLines>true</ShowSourceLines>
      </CallstackWindow>
      <Environment>
       <CheckProjectUpToDate>false</CheckProjectUpToDate>
       <DocumentTabNumLines>1</DocumentTabNumLines>
      </Environment>
      <MemoryWindow>
       <Default8bitMAUDisplayOptions>0</Default8bitMAUDisplayOptions>
      </MemoryWindow>
      <ProjectWindow>
       <Symbols>
        <ModuleFolders>RelPath</ModuleFolders>
       </Symbols>
      </ProjectWindow>
      <RTOSWindow>
       <PropertyDialogRect>
        <b>0</b>
        <l>0</l>
        <r>0</r>
        <t>0</t>
       </PropertyDialogRect>
      </RTOSWindow>
      <SFRWindow>
       <Display>Descr</Display>
       <FilterString></FilterString>
       <RadixPfx>true</RadixPfx>
       <ShortNames>true</ShortNames>
       <ShowFilter>true</ShowFilter>
       <SubSFRDisplay>NameValue</SubSFRDisplay>
      </SFRWindow>
      <TerminalWindow>
       <CurrentDownloadFile>-1</CurrentDownloadFile>
       <OutputFileName></OutputFileName>
       <SaveToDisk>false</SaveToDisk>
       <SessionOptions>
        <AppendNewlineOnInput>false</AppendNewlineOnInput>
        <AppendNewlineOnOutput>false</AppendNewlineOnOutput>
        <BaudRate>9600</BaudRate>
        <Channel>COM1</Channel>
        <Columns>80</Columns>
        <DataBits>8</DataBits>
        <DTR_DSR>false</DTR_DSR>
        <LocalEcho>false</LocalEcho>
        <Parity>0</Parity>
        <Rows>24</Rows>
        <RTS_CTS>false</RTS_CTS>
        <Screen_0>24</Screen_0>
        <Screen_1>80</Screen_1>
        <StopBits>0</StopBits>
        <TerminalEmulation>VT100</TerminalEmulation>
        <XON_XOFF>false</XON_XOFF>
       </SessionOptions>
      </TerminalWindow>
      <Themes>
       <CurrentScheme>Light Classic</CurrentScheme>
       <CurrentTheme>Light</CurrentTheme>
       <IsCurrentLight>true</IsCurrentLight>
      </Themes>
      <UIPreferences>
       <DialogHexEval>false</DialogHexEval>
      </UIPreferences>
     </Options>
     <WindowsInfo>
      <Memory>
       <FillDisplayUnit>1</FillDisplayUnit>
       <FillValue>0</FillValue>
       <FillValueType>0</FillValueType>
       <FindAddressType>1</FindAddressType>
       <FindValueType>0</FindValueType>
       <Format>0</Format>
       <FSA>0x1</FSA>
       <MAUsPerLine>16</MAUsPerLine>
       <SaveFile></SaveFile>
       <SaveSize>100</SaveSize>
       <SaveSizeType>1</SaveSizeType>
       <ShowLastValidAccess>false</ShowLastValidAccess>
       <SymbolTips>true</SymbolTips>
      </Memory>
      <SFR>
       <InitScript></InitScript>
      </SFR>
      <SFR_Tree>
       <ReorderItems>false</ReorderItems>
       <SelectedItems></SelectedItems>
       <RootItem>
        <Name></Name>
        <Options>0</Options>
       </RootItem>
      </SFR_Tree>
     </WindowsInfo>
    </Environment>
    <PluginData>
     <PluginInstance.0>
      <InstanceData>
       <Data>
        <DiscreteBkg>0x4040</DiscreteBkg>
       </Data>
      </InstanceData>
      <InstanceInfo>
       <Instance></Instance>
       <Type>HILMonitor</Type>
      </InstanceInfo>
     </PluginInstance.0>
    </PluginData>
    <Plugins>
     <_108541C3-E380-41DE-AFA5-3D8517465B5E>
      <Plugin>
       <Data>
        <DiscreteBkg>0x4040</DiscreteBkg>
       </Data>
      </Plugin>
     </_108541C3-E380-41DE-AFA5-3D8517465B5E>
    </Plugins>
   </REG_ROOT>
  </winIDEA>
 </Root>
