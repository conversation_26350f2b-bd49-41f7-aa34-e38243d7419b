/*
 * u1_core.c - 核心逻辑层实现
 * 作者: BMW_智能车组_Baiyuyu_Xiaohuihui_Wzz
 * 创建时间: 2025年07月06日
 * 描述: 4G模块核心逻辑层，整合状态机管理、AT指令处理、WebSocket通信、讯飞ASR协议
 */

#include "u1_core.h"
#include "zf_driver_timer.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* 
 * ========================================================================
 * 私有变量和数据结构
 * ========================================================================
 */

/* 状态机变量 */
static u1_state_t current_state = U1_STATE_IDLE;
static u1_error_t last_error = U1_ERROR_NONE;
static uint32 retry_count = 0;
static uint32 init_time = 0;

/* 网络和连接状态 */
static boolean network_ready = FALSE;
static boolean asr_connected = FALSE;
static boolean is_recognizing = FALSE;

/* 结果缓存 */
static u1_result_t cached_result;
static boolean result_ready = FALSE;

/* 统计信息 */
static uint32 total_recognitions = 0;
static uint32 success_count = 0;
static uint32 error_count = 0;

#if U1_FEATURE_AUDIO
/* 音频处理状态 */
static boolean audio_recording = FALSE;        /* 录音状态 */
static boolean websocket_connected = FALSE;    /* WebSocket连接状态 */
static uint32 audio_sample_count = 0;          /* 音频采样计数 */
static uint32 recording_start_time = 0;        /* 录音开始时间 */
static int16 audio_send_buffer[U1_AUDIO_SEND_SIZE]; /* 音频发送缓冲区 */
#endif

/* 静态缓冲区 */
static char at_buffer[U1_AT_BUFFER_SIZE];
static char response_buffer[U1_RX_BUFFER_SIZE];
static char json_buffer[U1_MAX_FRAME_SIZE];
static char url_buffer[512];
static uint8 frame_buffer[U1_MAX_FRAME_SIZE];
static char base64_buffer[U1_MAX_AUDIO_SIZE * 2];
static char time_now_data[64];                      /* 当前时间戳 */
static char asr_url_buffer[1024];                   /* ASR WebSocket URL */

/*
 * ========================================================================
 * SHA256和HMAC-SHA256相关定义
 * ========================================================================
 */

/* SHA256常量定义 */
#define U1_SHA256_BLOCKLEN  64ul  /* 消息块缓冲区大小 */
#define U1_SHA256_DIGESTLEN 32ul  /* 摘要长度(字节) */
#define U1_SHA256_DIGESTINT 8ul   /* 摘要长度(uint32) */

/* HMAC填充常量 */
#define U1_INNER_PAD   0x36
#define U1_OUTER_PAD   0x5c

/* SHA256上下文结构体 */
typedef struct {
    uint32 len;                         /* 已处理消息长度 */
    uint32 h[8];                        /* 哈希状态 */
    uint8 buf[U1_SHA256_BLOCKLEN];      /* 消息块缓冲区 */
} u1_sha256_ctx_t;

/* HMAC-SHA256上下文结构体 */
typedef struct {
    uint8 buf[U1_SHA256_BLOCKLEN];      /* 密钥块缓冲区 */
    uint32 h_inner[8];                  /* 内部哈希状态 */
    uint32 h_outer[8];                  /* 外部哈希状态 */
    u1_sha256_ctx_t sha;                /* SHA256上下文 */
} u1_hmac_sha256_ctx_t;

/*
 * ========================================================================
 * 内部函数声明
 * ========================================================================
 */

/* 状态机处理函数 */
static void u1_state_idle_process(void);
static void u1_state_init_process(void);
static void u1_state_network_check_process(void);
static void u1_state_asr_connect_process(void);
static void u1_state_recognizing_process(void);
static void u1_state_error_process(void);

/* AT指令处理 */
static boolean u1_send_at_command(const char* cmd, uint32 timeout_ms);
static boolean u1_test_at(void);
static boolean u1_check_network(void);
static boolean u1_connect_tcp(const char* host, const char* port);

/* WebSocket通信 */
static boolean u1_websocket_handshake(const char* host, const char* path);
static boolean u1_websocket_send(const uint8* data, uint32 len);
static int u1_websocket_receive(uint8* buffer, uint32 buffer_size, uint32 timeout_ms);

/* 讯飞ASR协议 */
static boolean u1_generate_asr_url(void);
static boolean u1_create_json_payload(const uint8* audio_data, uint32 audio_len, boolean is_last);
static boolean u1_parse_asr_result(const char* json_data);

/* 加密算法 */
static void u1_base64_encode(const uint8* input, uint32 input_len, char* output);
static void u1_hmac_sha256(const char* key, const char* data, uint8* output);
static void u1_sha256(const uint8* input, uint32 input_len, uint8* output);
static void u1_url_encode(const char* input, char* output);

/* 工具函数 */
static uint32 u1_get_system_time(void);
static boolean u1_get_network_time(void);
static boolean u1_get_http_time(void);
static void u1_format_gmt_time(uint32 timestamp, char* buffer, uint32 buffer_size);
static void u1_set_error(u1_error_t error);
static void u1_clear_error(void);

#if U1_FEATURE_VOICE_COMMANDS
/* 语音命令检测函数 */
static u1_voice_command_t u1_detect_voice_command(const char* text);
static const char* u1_get_command_text(u1_voice_command_t command_id);
#endif

/* 
 * ========================================================================
 * 核心接口函数实现 - 与cpu0_main.c调用完全匹配
 * ========================================================================
 */

/* 初始化4G模块 */
boolean u1_init(void)
{
    /* 初始化适配层 */
    if(!u1_adapter_init())
    {
        u1_set_error(U1_ERROR_INIT_FAILED);
        return FALSE;
    }
    
    /* 初始化状态机 */
    current_state = U1_STATE_INIT;
    last_error = U1_ERROR_NONE;
    retry_count = 0;
    init_time = system_getval_ms();
    
    /* 清空结果缓存 */
    memset(&cached_result, 0, sizeof(cached_result));
    result_ready = FALSE;
    
    /* 重置网络状态 */
    network_ready = FALSE;
    asr_connected = FALSE;
    is_recognizing = FALSE;
    
    /* 重置统计信息 */
    total_recognitions = 0;
    success_count = 0;
    error_count = 0;
    
#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("U1核心层初始化成功");
#endif
    
    return TRUE;
}

/* 主处理函数 */
void u1_process(void)
{
    /* 状态机处理 */
    switch(current_state)
    {
        case U1_STATE_IDLE:
            u1_state_idle_process();
            break;
            
        case U1_STATE_INIT:
            u1_state_init_process();
            break;
            
        case U1_STATE_NETWORK_CHECK:
            u1_state_network_check_process();
            break;
            
        case U1_STATE_ASR_CONNECT:
            u1_state_asr_connect_process();
            break;
            
        case U1_STATE_RECOGNIZING:
            u1_state_recognizing_process();
            break;
            
        case U1_STATE_ERROR:
            u1_state_error_process();
            break;
            
        default:
            current_state = U1_STATE_ERROR;
            u1_set_error(U1_ERROR_UNKNOWN);
            break;
    }
}

/* 开始语音识别 */
boolean u1_start_recognition(void)
{
    /* 检查当前状态 */
    if(current_state != U1_STATE_IDLE)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }
    
    /* 检查网络状态 */
    if(!network_ready)
    {
        u1_set_error(U1_ERROR_NETWORK_FAILED);
        return FALSE;
    }
    
    /* 清空上次结果 */
    result_ready = FALSE;
    memset(&cached_result, 0, sizeof(cached_result));
    
    /* 开始连接ASR服务 */
    current_state = U1_STATE_ASR_CONNECT;
    total_recognitions++;
    
#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("开始语音识别");
#endif
    
    return TRUE;
}

/* 获取识别结果 */
boolean u1_get_result(char* buffer, uint32 size)
{
    /* 参数检查 */
    if(!buffer || size == 0)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }
    
    /* 检查是否有结果 */
    if(!result_ready)
    {
        return FALSE;
    }
    
    /* 复制结果 */
    strncpy(buffer, cached_result.text, size - 1);
    buffer[size - 1] = '\0';
    
    /* 清除结果标志 */
    result_ready = FALSE;
    
    return TRUE;
}

/* 
 * ========================================================================
 * 状态机处理函数实现
 * ========================================================================
 */

static void u1_state_idle_process(void)
{
    /* 空闲状态，等待外部触发 */
    /* 定期检查网络状态 */
    static uint32 last_check_time = 0;
    uint32 current_time = system_getval_ms();
    
    if(current_time - last_check_time > 30000) /* 30秒检查一次 */
    {
        last_check_time = current_time;
        
        /* 检查网络状态 */
        if(!u1_check_network())
        {
            network_ready = FALSE;
            current_state = U1_STATE_NETWORK_CHECK;
        }
    }
}

static void u1_state_init_process(void)
{
    static uint32 step = 0;
    static uint32 last_step_time = 0;
    uint32 current_time = system_getval_ms();
    
    /* 初始化超时检查 */
    if(current_time - init_time > U1_CONN_TIMEOUT_MS)
    {
        u1_set_error(U1_ERROR_TIMEOUT);
        current_state = U1_STATE_ERROR;
        return;
    }
    
    switch(step)
    {
        case 0: /* 等待模块启动 */
            if(current_time - init_time > 3000)
            {
                step = 1;
                last_step_time = current_time;
            }
            break;
            
        case 1: /* 测试AT指令 */
            if(u1_test_at())
            {
                step = 2;
                last_step_time = current_time;
#if U1_DEBUG_ENABLE
                u1_adapter_debug_output("AT测试成功");
#endif
            }
            else if(current_time - last_step_time > 5000)
            {
                /* 重试 */
                last_step_time = current_time;
                retry_count++;
                if(retry_count >= U1_MAX_RETRIES)
                {
                    u1_set_error(U1_ERROR_INIT_FAILED);
                    current_state = U1_STATE_ERROR;
                    return;
                }
            }
            break;
            
        case 2: /* 检查网络 */
            current_state = U1_STATE_NETWORK_CHECK;
            step = 0;
            retry_count = 0;
            break;
    }
}

static void u1_state_network_check_process(void)
{
    static uint32 last_check_time = 0;
    uint32 current_time = system_getval_ms();
    
    /* 每2秒检查一次网络状态 */
    if(current_time - last_check_time > 2000)
    {
        last_check_time = current_time;
        
        if(u1_check_network())
        {
            network_ready = TRUE;
            current_state = U1_STATE_IDLE;
            retry_count = 0;
            
#if U1_DEBUG_ENABLE
            u1_adapter_debug_output("网络连接成功");
#endif
        }
        else
        {
            retry_count++;
            if(retry_count >= U1_MAX_RETRIES * 5) /* 网络检查多重试几次 */
            {
                u1_set_error(U1_ERROR_NETWORK_FAILED);
                current_state = U1_STATE_ERROR;
            }
        }
    }
}

static void u1_state_asr_connect_process(void)
{
    /* 简化的ASR连接处理 */
    current_state = U1_STATE_RECOGNIZING;
    asr_connected = TRUE;
    
#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("ASR连接成功");
#endif
}

static void u1_state_recognizing_process(void)
{
#if U1_FEATURE_AUDIO
    /* 变量声明 */
    uint32 current_time = system_getval_ms();
    uint32 samples_read;
    uint8 receive_buffer[1024];
    int receive_len;

    /* 检查按键状态开始录音 */
    if(!audio_recording && u1_adapter_button_pressed())
    {
        /* 开始录音 */
        if(!u1_adapter_audio_start())
        {
            u1_set_error(U1_ERROR_INIT_FAILED);
            current_state = U1_STATE_ERROR;
            return;
        }

        audio_recording = TRUE;
        websocket_connected = FALSE;
        audio_sample_count = 0;
        recording_start_time = current_time;

#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("开始录音");
#endif
    }

    /* 录音状态处理 */
    if(audio_recording)
    {
        /* 建立WebSocket连接（如果尚未连接） */
        if(!websocket_connected)
        {
            /* 生成ASR URL */
            if(!u1_generate_asr_url())
            {
                u1_set_error(U1_ERROR_ASR_CONNECT_FAILED);
                current_state = U1_STATE_ERROR;
                return;
            }

            /* 建立WebSocket连接 */
            if(!u1_websocket_handshake(U1_WEBSOCKET_HOST, U1_WEBSOCKET_PATH))
            {
                u1_set_error(U1_ERROR_ASR_CONNECT_FAILED);
                current_state = U1_STATE_ERROR;
                return;
            }

            /* 发送开始帧 */
            if(!u1_create_json_payload(NULL, 0, FALSE))
            {
                u1_set_error(U1_ERROR_SEND_FAILED);
                current_state = U1_STATE_ERROR;
                return;
            }

            if(!u1_websocket_send((uint8*)json_buffer, strlen(json_buffer)))
            {
                u1_set_error(U1_ERROR_SEND_FAILED);
                current_state = U1_STATE_ERROR;
                return;
            }

            websocket_connected = TRUE;

#if U1_DEBUG_ENABLE
            u1_adapter_debug_output("WebSocket连接成功，开始语音识别");
#endif
        }

        /* 读取音频数据并发送 */
        if(websocket_connected && u1_adapter_audio_available())
        {
            samples_read = u1_adapter_audio_read(audio_send_buffer, U1_AUDIO_SEND_SIZE);

            if(samples_read > 0)
            {
                audio_sample_count += samples_read;

                /* 每收集到足够的音频数据就发送一次 */
                if(samples_read >= U1_AUDIO_SEND_SIZE ||
                   (current_time - recording_start_time) > (U1_MAX_RECORD_TIME * 1000))
                {
                    /* 创建音频数据包 */
                    if(!u1_create_json_payload((uint8*)audio_send_buffer,
                                               samples_read * sizeof(int16), FALSE))
                    {
                        u1_set_error(U1_ERROR_SEND_FAILED);
                        current_state = U1_STATE_ERROR;
                        return;
                    }

                    /* 发送音频数据 */
                    if(!u1_websocket_send((uint8*)json_buffer, strlen(json_buffer)))
                    {
                        u1_set_error(U1_ERROR_SEND_FAILED);
                        current_state = U1_STATE_ERROR;
                        return;
                    }

                    /* 接收识别结果 */
                    receive_len = u1_websocket_receive(receive_buffer,
                                                       sizeof(receive_buffer) - 1, 300);
                    if(receive_len > 0)
                    {
                        receive_buffer[receive_len] = '\0';
                        u1_parse_asr_result((char*)receive_buffer);
                    }
                }
            }
        }

        /* 检查停止录音条件 */
        if(!u1_adapter_button_pressed() ||
           (current_time - recording_start_time) > (U1_MAX_RECORD_TIME * 1000))
        {
            /* 停止录音 */
            u1_adapter_audio_stop();

            /* 发送结束帧 */
            if(websocket_connected)
            {
                if(u1_create_json_payload(NULL, 0, TRUE))
                {
                    u1_websocket_send((uint8*)json_buffer, strlen(json_buffer));

                    /* 等待最终结果 */
                    system_delay_ms(1000);
                    receive_len = u1_websocket_receive(receive_buffer,
                                                       sizeof(receive_buffer) - 1, 2000);
                    if(receive_len > 0)
                    {
                        receive_buffer[receive_len] = '\0';
                        u1_parse_asr_result((char*)receive_buffer);
                    }
                }
            }

            /* 重置状态 */
            audio_recording = FALSE;
            websocket_connected = FALSE;
            audio_sample_count = 0;
            recording_start_time = 0;

            /* 转换到空闲状态 */
            current_state = U1_STATE_IDLE;
            success_count++;

#if U1_DEBUG_ENABLE
            u1_adapter_debug_output("录音结束，识别完成");
#endif
        }
    }
#else
    /* 如果音频功能未启用，使用简化处理 */
    static uint32 start_time = 0;
    uint32 current_time = system_getval_ms();

    if(start_time == 0)
    {
        start_time = current_time;
    }

    if(current_time - start_time > 3000)
    {
        strcpy(cached_result.text, "测试识别结果");
        cached_result.confidence = 95;
        cached_result.is_final = TRUE;
        cached_result.timestamp = current_time;

        result_ready = TRUE;
        current_state = U1_STATE_IDLE;
        success_count++;
        start_time = 0;

#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("识别完成");
#endif
    }
#endif
}

static void u1_state_error_process(void)
{
    static uint32 error_time = 0;
    uint32 current_time = system_getval_ms();
    
    if(error_time == 0)
    {
        error_time = current_time;
        error_count++;
        
#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("进入错误状态");
#endif
    }
    
    /* 错误恢复超时 */
    if(current_time - error_time > 5000)
    {
        /* 重置到初始状态 */
        current_state = U1_STATE_INIT;
        last_error = U1_ERROR_NONE;
        retry_count = 0;
        error_time = 0;
        
        /* 重置连接状态 */
        network_ready = FALSE;
        asr_connected = FALSE;
        is_recognizing = FALSE;
        
#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("错误恢复");
#endif
    }
}

/* 
 * ========================================================================
 * 内部函数实现（简化版本）
 * ========================================================================
 */

static boolean u1_send_at_command(const char* cmd, uint32 timeout_ms)
{
    /* 变量声明 */
    uint32 recv_len;
    uint32 start_time;

    /* 参数检查 */
    if(!cmd)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }

    /* 清空接收缓冲区 */
    memset(response_buffer, 0, sizeof(response_buffer));

    /* 发送AT指令 */
    if(!u1_adapter_send_data((const uint8*)cmd, strlen(cmd)))
    {
        u1_set_error(U1_ERROR_SEND_FAILED);
        return FALSE;
    }

    /* 等待响应 */
    start_time = system_getval_ms();
    recv_len = u1_adapter_receive_data((uint8*)response_buffer,
                                       sizeof(response_buffer) - 1,
                                       timeout_ms);

    if(recv_len == 0)
    {
        u1_set_error(U1_ERROR_TIMEOUT);
        return FALSE;
    }

    /* 确保字符串结束符 */
    response_buffer[recv_len] = '\0';

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("AT响应接收成功");
#endif

    return TRUE;
}

static boolean u1_test_at(void)
{
    /* 发送AT指令并等待OK响应 */
    if(!u1_send_at_command("AT\r\n", 1000))
    {
        return FALSE;
    }

    /* 检查响应是否包含OK */
    if(strstr(response_buffer, "OK") != NULL)
    {
#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("AT测试成功");
#endif
        return TRUE;
    }
    else
    {
        u1_set_error(U1_ERROR_INIT_FAILED);
        return FALSE;
    }
}

static boolean u1_check_network(void)
{
    /* 变量声明 */
    char* creg_pos;
    char* comma_pos;
    char status_char;

    /* 发送AT+CREG?查询网络注册状态 */
    if(!u1_send_at_command("AT+CREG?\r\n", 2000))
    {
        return FALSE;
    }

    /* 查找+CREG响应 */
    creg_pos = strstr(response_buffer, "+CREG:");
    if(!creg_pos)
    {
        u1_set_error(U1_ERROR_NETWORK_FAILED);
        return FALSE;
    }

    /* 跳过"+CREG: " */
    creg_pos += 7;

    /* 查找第一个逗号（跳过n参数） */
    comma_pos = strchr(creg_pos, ',');
    if(!comma_pos)
    {
        u1_set_error(U1_ERROR_NETWORK_FAILED);
        return FALSE;
    }

    /* 获取状态字符 */
    status_char = *(comma_pos + 1);

    /* 检查网络注册状态 */
    /* 1=已注册本地网络, 5=已注册漫游网络 */
    if(status_char == '1' || status_char == '5')
    {
#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("网络状态检查成功");
#endif
        return TRUE;
    }
    else
    {
        u1_set_error(U1_ERROR_NETWORK_FAILED);
        return FALSE;
    }
}

static boolean u1_connect_tcp(const char* host, const char* port)
{
    /* 变量声明 */
    char at_cmd[256];
    char* connect_pos;

    /* 参数检查 */
    if(!host || !port)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }

    /* 构建AT+CIPSTART指令 */
    memset(at_cmd, 0, sizeof(at_cmd));
    snprintf(at_cmd, sizeof(at_cmd), "AT+CIPSTART=\"TCP\",\"%s\",%s\r\n", host, port);

    /* 发送TCP连接指令 */
    if(!u1_send_at_command(at_cmd, 10000)) /* TCP连接可能需要较长时间 */
    {
        return FALSE;
    }

    /* 检查连接响应 */
    connect_pos = strstr(response_buffer, "CONNECT OK");
    if(!connect_pos)
    {
        /* 也检查是否已经连接 */
        connect_pos = strstr(response_buffer, "ALREADY CONNECT");
        if(!connect_pos)
        {
            u1_set_error(U1_ERROR_ASR_CONNECT_FAILED);
            return FALSE;
        }
    }

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("TCP连接成功");
#endif

    return TRUE;
}

static boolean u1_websocket_handshake(const char* host, const char* path)
{
    /* 变量声明 */
    char sec_websocket_key[32];
    char handshake_request[1024];
    char at_send_cmd[64];
    uint32 request_len;
    uint32 recv_len;
    char* upgrade_pos;

    /* 参数检查 */
    if(!host || !path)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }

    /* 生成WebSocket Key（简化版本） */
    strcpy(sec_websocket_key, "dGhlIHNhbXBsZSBub25jZQ==");

    /* 创建WebSocket握手请求 */
    memset(handshake_request, 0, sizeof(handshake_request));
    snprintf(handshake_request, sizeof(handshake_request),
             "GET %s HTTP/1.1\r\n"
             "Host: %s\r\n"
             "Upgrade: websocket\r\n"
             "Connection: Upgrade\r\n"
             "Sec-WebSocket-Key: %s\r\n"
             "Sec-WebSocket-Version: 13\r\n\r\n",
             path, host, sec_websocket_key);

    request_len = strlen(handshake_request);

    /* 通过4G发送握手请求 */
    snprintf(at_send_cmd, sizeof(at_send_cmd), "AT+CIPSEND=%u\r\n", request_len);
    if(!u1_send_at_command(at_send_cmd, 5000))
    {
        return FALSE;
    }

    /* 检查是否收到">"提示符 */
    if(!strstr(response_buffer, ">"))
    {
        u1_set_error(U1_ERROR_SEND_FAILED);
        return FALSE;
    }

    /* 发送握手数据 */
    if(!u1_adapter_send_data((uint8*)handshake_request, request_len))
    {
        u1_set_error(U1_ERROR_SEND_FAILED);
        return FALSE;
    }

    /* 等待握手响应 */
    system_delay_ms(2000); /* 等待服务器响应 */
    recv_len = u1_adapter_receive_data((uint8*)response_buffer,
                                       sizeof(response_buffer) - 1,
                                       5000);

    if(recv_len == 0)
    {
        u1_set_error(U1_ERROR_TIMEOUT);
        return FALSE;
    }

    response_buffer[recv_len] = '\0';

    /* 验证握手响应 */
    upgrade_pos = strstr(response_buffer, "101 Switching Protocols");
    if(!upgrade_pos)
    {
        upgrade_pos = strstr(response_buffer, "Upgrade: websocket");
        if(!upgrade_pos)
        {
            u1_set_error(U1_ERROR_ASR_CONNECT_FAILED);
            return FALSE;
        }
    }

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("WebSocket握手成功");
#endif

    return TRUE;
}

static boolean u1_websocket_send(const uint8* data, uint32 len)
{
    /* 变量声明 */
    uint8 frame_buffer_local[U1_MAX_FRAME_SIZE];
    uint32 frame_len;
    char at_send_cmd[64];
    uint8* ptr;
    uint32 i;

    /* 参数检查 */
    if(!data || len == 0)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }

    /* 创建WebSocket帧 */
    memset(frame_buffer_local, 0, sizeof(frame_buffer_local));
    ptr = frame_buffer_local;

    /* WebSocket帧头 */
    *ptr++ = 0x81; /* FIN=1, Opcode=1 (文本帧) */

    /* 载荷长度 */
    if(len <= 125)
    {
        *ptr++ = (uint8)(len | 0x80); /* MASK=1 */
    }
    else if(len <= 65535)
    {
        *ptr++ = 126 | 0x80; /* MASK=1 */
        *ptr++ = (uint8)((len >> 8) & 0xFF);
        *ptr++ = (uint8)(len & 0xFF);
    }
    else
    {
        u1_set_error(U1_ERROR_BUFFER_FULL);
        return FALSE;
    }

    /* 掩码键（简化版本） */
    *ptr++ = 0x12;
    *ptr++ = 0x34;
    *ptr++ = 0x56;
    *ptr++ = 0x78;

    /* 掩码载荷 */
    for(i = 0; i < len; i++)
    {
        *ptr++ = data[i] ^ (0x12 + (i % 4) * 0x22);
    }

    frame_len = ptr - frame_buffer_local;

    /* 通过4G发送WebSocket帧 */
    snprintf(at_send_cmd, sizeof(at_send_cmd), "AT+CIPSEND=%u\r\n", frame_len);
    if(!u1_send_at_command(at_send_cmd, 5000))
    {
        return FALSE;
    }

    /* 检查是否收到">"提示符 */
    if(!strstr(response_buffer, ">"))
    {
        u1_set_error(U1_ERROR_SEND_FAILED);
        return FALSE;
    }

    /* 发送帧数据 */
    if(!u1_adapter_send_data(frame_buffer_local, frame_len))
    {
        u1_set_error(U1_ERROR_SEND_FAILED);
        return FALSE;
    }

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("WebSocket数据发送成功");
#endif

    return TRUE;
}

static int u1_websocket_receive(uint8* buffer, uint32 buffer_size, uint32 timeout_ms)
{
    /* 变量声明 */
    uint32 recv_len;
    uint8* frame_start;
    uint8 opcode;
    uint32 payload_len;
    uint8* payload_start;
    uint32 copy_len;

    /* 参数检查 */
    if(!buffer || buffer_size == 0)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return 0;
    }

    /* 从4G接收数据 */
    recv_len = u1_adapter_receive_data((uint8*)response_buffer,
                                       sizeof(response_buffer) - 1,
                                       timeout_ms);

    if(recv_len == 0)
    {
        return 0; /* 超时或无数据 */
    }

    /* 查找WebSocket帧起始位置 */
    frame_start = (uint8*)response_buffer;

    /* 简化的WebSocket帧解析 */
    if(recv_len < 2)
    {
        return 0; /* 数据太少 */
    }

    /* 获取操作码 */
    opcode = frame_start[0] & 0x0F;

    /* 只处理文本帧 */
    if(opcode != 1)
    {
        return 0;
    }

    /* 获取载荷长度 */
    payload_len = frame_start[1] & 0x7F;
    payload_start = frame_start + 2;

    if(payload_len == 126)
    {
        if(recv_len < 4) return 0;
        payload_len = (frame_start[2] << 8) | frame_start[3];
        payload_start = frame_start + 4;
    }
    else if(payload_len == 127)
    {
        /* 不支持64位长度 */
        return 0;
    }

    /* 检查是否有足够的数据 */
    if((payload_start - frame_start) + payload_len > recv_len)
    {
        return 0;
    }

    /* 复制载荷数据到输出缓冲区 */
    copy_len = (payload_len < buffer_size - 1) ? payload_len : (buffer_size - 1);
    memcpy(buffer, payload_start, copy_len);
    buffer[copy_len] = '\0';

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("WebSocket数据接收成功");
#endif

    return copy_len;
}

static boolean u1_generate_asr_url(void)
{
    /* 变量声明 */
    char base_url[256];
    char host_domain[256];
    char path[256];
    char indata[256];
    char authorization_origin[512];
    char encoded_authorization[512];
    char encoded_date[256];
    char encoded_host[256];
    uint8 sha256_out[32];
    char base64_out[512];
    uint32 total_len;

    /* 初始化数组 */
    memset(host_domain, 0, sizeof(host_domain));
    memset(path, 0, sizeof(path));
    memset(indata, 0, sizeof(indata));
    memset(authorization_origin, 0, sizeof(authorization_origin));

    /* 获取网络时间 */
    if(!time_now_data[0])
    {
        /* 尝试通过4G网络获取时间 */
        if(!u1_get_network_time())
        {
            /* 网络时间获取失败，使用系统时间作为备用 */
            u1_format_gmt_time(u1_get_system_time(), time_now_data, sizeof(time_now_data));

#if U1_DEBUG_ENABLE
            u1_adapter_debug_output("网络时间获取失败，使用系统时间");
#endif
        }
    }

    /* 构建基础URL */
    snprintf(base_url, sizeof(base_url), "wss://%s%s", U1_WEBSOCKET_HOST, U1_WEBSOCKET_PATH);

    /* 解析域名和路径 */
    strcpy(host_domain, U1_WEBSOCKET_HOST);
    strcpy(path, U1_WEBSOCKET_PATH);

    /* 构建签名字符串 */
    snprintf(indata, sizeof(indata),
             "host: %s\n"
             "date: %s\n"
             "GET %s HTTP/1.1", host_domain, time_now_data, path);

    /* 计算HMAC-SHA256签名 */
    u1_hmac_sha256(U1_XF_API_SECRET, indata, sha256_out);

    /* Base64编码签名 */
    u1_base64_encode(sha256_out, 32, base64_out);

    /* 构建authorization字符串 */
    snprintf(authorization_origin, sizeof(authorization_origin),
             "api_key=\"%s\", algorithm=\"hmac-sha256\", headers=\"host date request-line\", signature=\"%s\"",
             U1_XF_API_KEY, base64_out);

    /* Base64编码authorization */
    u1_base64_encode((uint8*)authorization_origin, strlen(authorization_origin), base64_out);

    /* URL编码各个参数 */
    u1_url_encode(base64_out, encoded_authorization);
    u1_url_encode(time_now_data, encoded_date);
    u1_url_encode(host_domain, encoded_host);

    /* 拼接最终URL */
    total_len = strlen(base_url) + strlen(encoded_authorization) + strlen(encoded_date) + strlen(encoded_host) + 100;
    if(total_len >= sizeof(asr_url_buffer))
    {
        u1_set_error(U1_ERROR_BUFFER_FULL);
        return FALSE;
    }

    snprintf(asr_url_buffer, sizeof(asr_url_buffer), "%s?authorization=%s&date=%s&host=%s",
             base_url, encoded_authorization, encoded_date, encoded_host);

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("ASR URL生成成功");
#endif

    return TRUE;
}

static boolean u1_create_json_payload(const uint8* audio_data, uint32 audio_len, boolean is_last)
{
    /* 变量声明 */
    uint8 status;
    char temp_data[U1_MAX_AUDIO_SIZE * 2];

    /* 参数检查 */
    if(!audio_data && audio_len > 0)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }

    /* 确定状态值 */
    if(audio_len == 0)
    {
        status = 0;  /* 开始帧 */
    }
    else if(is_last)
    {
        status = 2;  /* 结束帧 */
    }
    else
    {
        status = 1;  /* 中间帧 */
    }

    /* 初始化临时数据缓冲区 */
    memset(temp_data, 0, sizeof(temp_data));

    /* 如果不是开始帧，需要Base64编码音频数据 */
    if(status != 0 && audio_data && audio_len > 0)
    {
        u1_base64_encode(audio_data, audio_len, temp_data);
    }

    /* 构建JSON载荷 */
    memset(json_buffer, 0, sizeof(json_buffer));
    snprintf(json_buffer, sizeof(json_buffer),
             "{"
             "\"common\": {"
                 "\"app_id\": \"%s\""
             "},"
             "\"business\": {"
                 "\"domain\": \"iat\","
                 "\"language\": \"zh_cn\","
                 "\"accent\": \"mandarin\","
                 "\"vinfo\": 1,"
                 "\"vad_eos\": 10000"
             "},"
             "\"data\": {"
                 "\"status\": %d,"
                 "\"format\": \"audio/L16;rate=8000\","
                 "\"audio\": \"%s\","
                 "\"encoding\": \"raw\""
             "}"
             "}",
             U1_XF_APP_ID, status, temp_data);

    /* 检查JSON长度 */
    if(strlen(json_buffer) >= sizeof(json_buffer) - 1)
    {
        u1_set_error(U1_ERROR_BUFFER_FULL);
        return FALSE;
    }

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("JSON载荷创建成功");
#endif

    return TRUE;
}

static boolean u1_parse_asr_result(const char* json_data)
{
    /* 变量声明 */
    const char* ptr;
    const char* w_start;
    const char* w_end;
    const char* is_end_start;
    char w_value[256];
    char temp_text[U1_RESULT_BUFFER_SIZE];
    int length;
    boolean found_text = FALSE;

    /* 参数检查 */
    if(!json_data)
    {
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }

    /* 初始化临时文本缓冲区 */
    memset(temp_text, 0, sizeof(temp_text));
    ptr = json_data;

    /* 解析JSON响应中的ws字段和w字段内容 */
    while(*ptr)
    {
        /* 查找 "w":" 模式 */
        w_start = strstr(ptr, "\"w\":\"");
        if(!w_start)
        {
            break;
        }

        w_start += 5; /* 跳过 "\"w\":\"" */
        w_end = strchr(w_start, '"');
        if(!w_end)
        {
            break;
        }

        /* 提取 w 字段的值 */
        length = w_end - w_start;
        if(length > 0 && length < sizeof(w_value))
        {
            memset(w_value, 0, sizeof(w_value));
            strncpy(w_value, w_start, length);
            w_value[length] = '\0';

            /* 拼接识别文本片段 */
            if(strlen(temp_text) + strlen(w_value) < sizeof(temp_text) - 1)
            {
                strcat(temp_text, w_value);
                found_text = TRUE;
            }
        }

        ptr = w_end + 1;
    }

    /* 检查是否为最终结果 */
    is_end_start = strstr(json_data, "\"is_end\":");
    if(is_end_start)
    {
        is_end_start += 9; /* 跳过 "\"is_end\":" */
        while(*is_end_start == ' ' || *is_end_start == '\t') is_end_start++; /* 跳过空白字符 */
        cached_result.is_final = (*is_end_start == '1' || strncmp(is_end_start, "true", 4) == 0);
    }
    else
    {
        cached_result.is_final = FALSE; /* 默认不是最终结果 */
    }

    /* 更新cached_result结构体 */
    if(found_text)
    {
        strncpy(cached_result.text, temp_text, sizeof(cached_result.text) - 1);
        cached_result.text[sizeof(cached_result.text) - 1] = '\0';
        cached_result.confidence = 85; /* 设置默认置信度 */
        cached_result.timestamp = u1_get_system_time();

#if U1_FEATURE_VOICE_COMMANDS
        /* 检测语音命令 */
        cached_result.command_id = u1_detect_voice_command(temp_text);
        cached_result.is_command = (cached_result.command_id != U1_VOICE_CMD_NONE);

        if(cached_result.is_command)
        {
            /* 提高命令识别的置信度 */
            cached_result.confidence = 95;
#if U1_DEBUG_ENABLE
            u1_adapter_debug_output("检测到语音命令");
#endif
        }
#endif

        result_ready = TRUE;

#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("ASR结果解析成功");
#endif
        return TRUE;
    }
    else
    {
        /* 没有找到有效文本 */
        u1_set_error(U1_ERROR_INVALID_PARAM);
        return FALSE;
    }
}

static void u1_base64_encode(const uint8* input, uint32 input_len, char* output)
{
    /* Base64编码表 */
    static const char base64_table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    uint32 i, j;

    /* 参数检查 */
    if(!input || !output || input_len == 0)
    {
        if(output) output[0] = '\0';
        return;
    }

    /* Base64编码处理 */
    for(i = 0, j = 0; i < input_len; i += 3)
    {
        uint32 octet_a = i < input_len ? input[i] : 0;
        uint32 octet_b = (i + 1) < input_len ? input[i + 1] : 0;
        uint32 octet_c = (i + 2) < input_len ? input[i + 2] : 0;

        uint32 triple = (octet_a << 16) | (octet_b << 8) | octet_c;

        output[j++] = base64_table[(triple >> 18) & 0x3F];
        output[j++] = base64_table[(triple >> 12) & 0x3F];
        output[j++] = (i + 1) < input_len ? base64_table[(triple >> 6) & 0x3F] : '=';
        output[j++] = (i + 2) < input_len ? base64_table[triple & 0x3F] : '=';
    }
    output[j] = '\0'; /* 添加字符串结束符 */
}

/*
 * ========================================================================
 * SHA256算法实现
 * ========================================================================
 */

/* SHA256算法宏定义 */
#define U1_ROR(n,k)    ((n >> k) | (n << (32 - k)))
#define U1_CH(x,y,z)   (z ^ (x & (y ^ z)))
#define U1_MAJ(x,y,z)  ((x & y) | (z & (x | y)))
#define U1_S0(x)       (U1_ROR(x, 2) ^ U1_ROR(x,13) ^ U1_ROR(x,22))
#define U1_S1(x)       (U1_ROR(x, 6) ^ U1_ROR(x,11) ^ U1_ROR(x,25))
#define U1_R0(x)       (U1_ROR(x, 7) ^ U1_ROR(x,18) ^ (x>>3))
#define U1_R1(x)       (U1_ROR(x,17) ^ U1_ROR(x,19) ^ (x>>10))

/* SHA256常量K */
static const uint32 u1_sha256_k[64] = {
    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
};

/* SHA256变换函数 */
static void u1_sha256_transform(u1_sha256_ctx_t* s, const uint8* buf)
{
    uint32 t1, t2, va, vb, vc, vd, ve, vf, vg, vh, m[64];
    uint32 i, j;

    for(i = 0, j = 0; i < 16; i++, j += 4)
    {
        m[i] = (uint32) buf[j] << 24 | (uint32) buf[j + 1] << 16 |
               (uint32) buf[j + 2] << 8 | (uint32) buf[j + 3];
    }
    for(; i < 64; i++)
    {
        m[i] = U1_R1(m[i - 2]) + m[i - 7] + U1_R0(m[i - 15]) + m[i - 16];
    }
    va = s->h[0]; vb = s->h[1]; vc = s->h[2]; vd = s->h[3];
    ve = s->h[4]; vf = s->h[5]; vg = s->h[6]; vh = s->h[7];

    for(i = 0; i < 64; i++)
    {
        t1 = vh + U1_S1(ve) + U1_CH(ve, vf, vg) + u1_sha256_k[i] + m[i];
        t2 = U1_S0(va) + U1_MAJ(va, vb, vc);
        vh = vg; vg = vf; vf = ve; ve = vd + t1;
        vd = vc; vc = vb; vb = va; va = t1 + t2;
    }
    s->h[0] += va; s->h[1] += vb; s->h[2] += vc; s->h[3] += vd;
    s->h[4] += ve; s->h[5] += vf; s->h[6] += vg; s->h[7] += vh;
}

/* SHA256初始化 */
static void u1_sha256_init(u1_sha256_ctx_t* s)
{
    s->len = 0;
    s->h[0] = 0x6a09e667; s->h[1] = 0xbb67ae85; s->h[2] = 0x3c6ef372; s->h[3] = 0xa54ff53a;
    s->h[4] = 0x510e527f; s->h[5] = 0x9b05688c; s->h[6] = 0x1f83d9ab; s->h[7] = 0x5be0cd19;
}

/* SHA256更新 */
static void u1_sha256_update(u1_sha256_ctx_t* s, const uint8* m, uint32 len)
{
    const uint8* p = m;
    uint32 vr = s->len % U1_SHA256_BLOCKLEN;

    s->len += len;
    if(vr)
    {
        if(len + vr < U1_SHA256_BLOCKLEN)
        {
            memcpy(s->buf + vr, p, len);
            return;
        }
        memcpy(s->buf + vr, p, U1_SHA256_BLOCKLEN - vr);
        len -= U1_SHA256_BLOCKLEN - vr;
        p += U1_SHA256_BLOCKLEN - vr;
        u1_sha256_transform(s, s->buf);
    }
    for(; len >= U1_SHA256_BLOCKLEN; len -= U1_SHA256_BLOCKLEN, p += U1_SHA256_BLOCKLEN)
    {
        u1_sha256_transform(s, p);
    }
    memcpy(s->buf, p, len);
}

/* SHA256完成 */
static void u1_sha256_final(u1_sha256_ctx_t* s, uint8* md)
{
    uint32 vr = s->len % U1_SHA256_BLOCKLEN;
    int i;

    s->buf[vr++] = 0x80;
    if(vr > 56)
    {
        memset(s->buf + vr, 0, U1_SHA256_BLOCKLEN - vr);
        vr = 0;
        u1_sha256_transform(s, s->buf);
    }
    memset(s->buf + vr, 0, 56 - vr);
    s->len *= 8;
    s->buf[56] = (uint8)(s->len >> 56); s->buf[57] = (uint8)(s->len >> 48);
    s->buf[58] = (uint8)(s->len >> 40); s->buf[59] = (uint8)(s->len >> 32);
    s->buf[60] = (uint8)(s->len >> 24); s->buf[61] = (uint8)(s->len >> 16);
    s->buf[62] = (uint8)(s->len >> 8);  s->buf[63] = (uint8)(s->len);
    u1_sha256_transform(s, s->buf);

    for(i = 0; i < U1_SHA256_DIGESTINT; i++)
    {
        md[4 * i    ] = (uint8)(s->h[i] >> 24); md[4 * i + 1] = (uint8)(s->h[i] >> 16);
        md[4 * i + 2] = (uint8)(s->h[i] >> 8);  md[4 * i + 3] = (uint8)(s->h[i]);
    }
    u1_sha256_init(s);
}

static void u1_hmac_sha256(const char* key, const char* data, uint8* output)
{
    u1_hmac_sha256_ctx_t hmac;
    const uint8* key_bytes = (const uint8*)key;
    const uint8* data_bytes = (const uint8*)data;
    uint32 key_len = strlen(key);
    uint32 data_len = strlen(data);

    /* 参数检查 */
    if(!key || !data || !output) return;

    /* HMAC-SHA256初始化 */
    u1_sha256_ctx_t* sha = &hmac.sha;
    uint32 i;

    if(key_len <= U1_SHA256_BLOCKLEN)
    {
        memcpy(hmac.buf, key_bytes, key_len);
        memset(hmac.buf + key_len, 0, U1_SHA256_BLOCKLEN - key_len);
    }
    else
    {
        u1_sha256_init(sha);
        u1_sha256_update(sha, key_bytes, key_len);
        u1_sha256_final(sha, hmac.buf);
        memset(hmac.buf + U1_SHA256_DIGESTLEN, 0, U1_SHA256_BLOCKLEN - U1_SHA256_DIGESTLEN);
    }

    for(i = 0; i < U1_SHA256_BLOCKLEN; i++)
    {
        hmac.buf[i] = hmac.buf[i] ^ U1_OUTER_PAD;
    }

    u1_sha256_init(sha);
    u1_sha256_update(sha, hmac.buf, U1_SHA256_BLOCKLEN);
    memcpy(hmac.h_outer, sha->h, U1_SHA256_DIGESTLEN);

    for(i = 0; i < U1_SHA256_BLOCKLEN; i++)
    {
        hmac.buf[i] = (hmac.buf[i] ^ U1_OUTER_PAD) ^ U1_INNER_PAD;
    }

    u1_sha256_init(sha);
    u1_sha256_update(sha, hmac.buf, U1_SHA256_BLOCKLEN);
    memcpy(hmac.h_inner, sha->h, U1_SHA256_DIGESTLEN);

    /* HMAC-SHA256更新和完成 */
    u1_sha256_update(sha, data_bytes, data_len);
    u1_sha256_final(sha, output);

    memcpy(sha->h, hmac.h_outer, U1_SHA256_DIGESTLEN);
    sha->len = U1_SHA256_BLOCKLEN;
    u1_sha256_update(sha, output, U1_SHA256_DIGESTLEN);
    u1_sha256_final(sha, output);

    memcpy(sha->h, hmac.h_inner, U1_SHA256_DIGESTLEN);
    sha->len = U1_SHA256_BLOCKLEN;
}

static void u1_sha256(const uint8* input, uint32 input_len, uint8* output)
{
    u1_sha256_ctx_t ctx;

    /* 参数检查 */
    if(!input || !output || input_len == 0)
    {
        if(output) memset(output, 0, U1_SHA256_DIGESTLEN);
        return;
    }

    /* SHA256计算 */
    u1_sha256_init(&ctx);
    u1_sha256_update(&ctx, input, input_len);
    u1_sha256_final(&ctx, output);
}

static void u1_url_encode(const char* input, char* output)
{
    /* 十六进制字符表 */
    const char* hex_chars = "0123456789ABCDEF";
    uint32 len, i;
    char* ptr;
    unsigned char c;

    /* 参数检查 */
    if(!input || !output)
    {
        if(output) output[0] = '\0';
        return;
    }

    len = strlen(input);
    ptr = output;

    /* URL编码处理 */
    for(i = 0; i < len; i++)
    {
        c = input[i];

        /* 不需要编码的字符：字母、数字、-_.~ */
        if((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z') ||
           (c >= 'a' && c <= 'z') || c == '-' || c == '_' ||
           c == '.' || c == '~')
        {
            *ptr++ = c;
        }
        /* 空格编码为+ */
        else if(c == ' ')
        {
            *ptr++ = '+';
        }
        /* 其他字符编码为%XX格式 */
        else
        {
            *ptr++ = '%';
            *ptr++ = hex_chars[(c >> 4) & 0x0F];
            *ptr++ = hex_chars[c & 0x0F];
        }
    }
    *ptr = '\0'; /* 添加字符串结束符 */
}

static uint32 u1_get_system_time(void)
{
    return system_getval_ms();
}

static boolean u1_get_network_time(void)
{
    /* 变量声明 */
    char* cclk_pos;
    char* quote_start;
    char* quote_end;
    char time_str[32];
    uint32 year, month, day, hour, minute, second;
    int parsed_count;

    /* 发送AT+CCLK?获取网络时间 */
    if(!u1_send_at_command("AT+CCLK?\r\n", 3000))
    {
        return FALSE;
    }

    /* 查找+CCLK响应 */
    cclk_pos = strstr(response_buffer, "+CCLK:");
    if(!cclk_pos)
    {
        /* 如果AT+CCLK不支持，尝试通过HTTP获取时间 */
        return u1_get_http_time();
    }

    /* 查找时间字符串（在双引号之间） */
    quote_start = strchr(cclk_pos, '"');
    if(!quote_start)
    {
        return FALSE;
    }
    quote_start++; /* 跳过第一个引号 */

    quote_end = strchr(quote_start, '"');
    if(!quote_end)
    {
        return FALSE;
    }

    /* 提取时间字符串 */
    memset(time_str, 0, sizeof(time_str));
    strncpy(time_str, quote_start, quote_end - quote_start);

    /* 解析时间格式：yy/MM/dd,hh:mm:ss+zz */
    parsed_count = sscanf(time_str, "%u/%u/%u,%u:%u:%u",
                          &year, &month, &day, &hour, &minute, &second);

    if(parsed_count >= 6)
    {
        /* 转换为GMT格式字符串 */
        /* 假设年份是20xx格式 */
        if(year < 50) year += 2000;
        else if(year < 100) year += 1900;

        /* 格式化为RFC 2822格式：Wed, 21 Oct 2015 07:28:00 GMT */
        snprintf(time_now_data, sizeof(time_now_data),
                 "Wed, %02u Oct %04u %02u:%02u:%02u GMT",
                 day, year, hour, minute, second);

#if U1_DEBUG_ENABLE
        u1_adapter_debug_output("网络时间获取成功");
#endif
        return TRUE;
    }

    return FALSE;
}

static boolean u1_get_http_time(void)
{
    /* 通过HTTP请求获取时间的备用方案 */
    char http_request[128];
    char* time_start;
    uint32 recv_len;

    /* 建立HTTP连接到时间服务器 */
    if(!u1_connect_tcp("worldtimeapi.org", "80"))
    {
        return FALSE;
    }

    /* 构建HTTP请求 */
    snprintf(http_request, sizeof(http_request),
             "GET /api/timezone/Etc/GMT HTTP/1.1\r\n"
             "Host: worldtimeapi.org\r\n"
             "Connection: close\r\n\r\n");

    /* 发送HTTP请求 */
    if(!u1_websocket_send((uint8*)http_request, strlen(http_request)))
    {
        return FALSE;
    }

    /* 接收HTTP响应 */
    system_delay_ms(2000);
    recv_len = u1_adapter_receive_data((uint8*)response_buffer,
                                       sizeof(response_buffer) - 1, 5000);

    if(recv_len == 0)
    {
        return FALSE;
    }

    response_buffer[recv_len] = '\0';

    /* 查找datetime字段 */
    time_start = strstr(response_buffer, "\"datetime\":");
    if(time_start)
    {
        /* 简化处理：使用当前系统时间 */
        u1_format_gmt_time(u1_get_system_time(), time_now_data, sizeof(time_now_data));
        return TRUE;
    }

    return FALSE;
}

static void u1_format_gmt_time(uint32 timestamp, char* buffer, uint32 buffer_size)
{
    /* 变量声明 */
    uint32 seconds, minutes, hours, days;
    uint32 year, month, day;
    const char* weekdays[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
    const char* months[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun",
                           "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

    /* 参数检查 */
    if(!buffer || buffer_size == 0) return;

    /* 简化的时间转换（基于2020年1月1日为起点） */
    seconds = timestamp / 1000; /* 转换为秒 */

    /* 计算天数（简化算法） */
    days = seconds / 86400; /* 86400秒 = 1天 */
    seconds %= 86400;

    /* 计算小时、分钟、秒 */
    hours = seconds / 3600;
    seconds %= 3600;
    minutes = seconds / 60;
    seconds %= 60;

    /* 简化的日期计算（假设从2020年开始） */
    year = 2020 + (days / 365);
    day = (days % 365) % 30 + 1; /* 简化为每月30天 */
    month = ((days % 365) / 30) % 12;

    /* 格式化为RFC 2822格式 */
    snprintf(buffer, buffer_size, "%s, %02u %s %04u %02u:%02u:%02u GMT",
             weekdays[days % 7], day, months[month], year, hours, minutes, seconds);
}

static void u1_set_error(u1_error_t error)
{
    last_error = error;
}

static void u1_clear_error(void)
{
    last_error = U1_ERROR_NONE;
}

/* 
 * ========================================================================
 * 扩展接口函数实现
 * ========================================================================
 */

u1_state_t u1_get_state(void)
{
    return current_state;
}

void u1_get_status(u1_status_t* status)
{
    if(!status) return;
    
    status->current_state = current_state;
    status->last_error = last_error;
    status->retry_count = retry_count;
    status->uptime_ms = system_getval_ms() - init_time;
    status->network_ready = network_ready;
    status->asr_connected = asr_connected;
}

u1_error_t u1_get_last_error(void)
{
    return last_error;
}

void u1_stop_recognition(void)
{
    if(current_state == U1_STATE_RECOGNIZING)
    {
        current_state = U1_STATE_IDLE;
        is_recognizing = FALSE;
    }
}

boolean u1_reset(void)
{
    /* 重置所有状态 */
    current_state = U1_STATE_INIT;
    last_error = U1_ERROR_NONE;
    retry_count = 0;
    network_ready = FALSE;
    asr_connected = FALSE;
    is_recognizing = FALSE;
    result_ready = FALSE;
    
    return TRUE;
}

boolean u1_send_audio_data(const uint8* audio_data, uint32 len, boolean is_last)
{
    /* 简化的音频数据发送 */
    return TRUE;
}

boolean u1_get_result_detail(u1_result_t* result)
{
    if(!result || !result_ready) return FALSE;
    
    *result = cached_result;
    return TRUE;
}

boolean u1_set_asr_config(const char* app_id, const char* api_key, const char* api_secret)
{
    /* 简化的配置设置 */
    return TRUE;
}

const char* u1_get_error_description(u1_error_t error_code)
{
    switch(error_code)
    {
        case U1_ERROR_NONE: return "无错误";
        case U1_ERROR_INIT_FAILED: return "初始化失败";
        case U1_ERROR_NETWORK_FAILED: return "网络连接失败";
        case U1_ERROR_ASR_CONNECT_FAILED: return "ASR连接失败";
        case U1_ERROR_SEND_FAILED: return "发送失败";
        case U1_ERROR_TIMEOUT: return "超时";
        case U1_ERROR_INVALID_PARAM: return "无效参数";
        case U1_ERROR_BUFFER_FULL: return "缓冲区满";
        default: return "未知错误";
    }
}

const char* u1_get_state_description(u1_state_t state)
{
    switch(state)
    {
        case U1_STATE_IDLE: return "空闲状态";
        case U1_STATE_INIT: return "初始化状态";
        case U1_STATE_NETWORK_CHECK: return "网络检查状态";
        case U1_STATE_ASR_CONNECT: return "ASR连接状态";
        case U1_STATE_RECOGNIZING: return "识别状态";
        case U1_STATE_RESULT_READY: return "结果就绪状态";
        case U1_STATE_ERROR: return "错误状态";
        default: return "未知状态";
    }
}

boolean u1_self_test(void)
{
    /* 简化的自检 */
    return u1_adapter_is_ready() && u1_test_at();
}

void u1_get_statistics(uint32* total_recognitions_out, uint32* success_count_out, uint32* error_count_out)
{
    if(total_recognitions_out) *total_recognitions_out = total_recognitions;
    if(success_count_out) *success_count_out = success_count;
    if(error_count_out) *error_count_out = error_count;
}

#if U1_FEATURE_VOICE_COMMANDS
/*
 * ========================================================================
 * 语音命令检测功能实现
 * ========================================================================
 */

/* 语音命令检测函数 */
static u1_voice_command_t u1_detect_voice_command(const char* text)
{
    if(!text) return U1_VOICE_CMD_NONE;

    /* 检测15条语音命令 */
    if(strstr(text, "打开双灯") || strstr(text, "双灯"))
        return U1_VOICE_CMD_LIGHT_ON;

    if(strstr(text, "打开左转灯") || strstr(text, "左转灯"))
        return U1_VOICE_CMD_LEFT_LIGHT_ON;

    if(strstr(text, "打开右转灯") || strstr(text, "右转灯"))
        return U1_VOICE_CMD_RIGHT_LIGHT_ON;

    if(strstr(text, "打开远光灯") || strstr(text, "远光灯"))
        return U1_VOICE_CMD_DISTANCE_LIGHT_ON;

    if(strstr(text, "打开近光灯") || strstr(text, "近光灯"))
        return U1_VOICE_CMD_NEAR_LIGHT_ON;

    if(strstr(text, "打开刹灯") || strstr(text, "刹灯"))
        return U1_VOICE_CMD_BRAKE_LIGHT_ON;

    if(strstr(text, "向前直行10米") || strstr(text, "前进10米"))
        return U1_VOICE_CMD_FORWARD_10M;

    if(strstr(text, "后退直行10米") || strstr(text, "后退10米"))
        return U1_VOICE_CMD_BACKWARD_10M;

    if(strstr(text, "蛇形前进10米") || strstr(text, "蛇形前进"))
        return U1_VOICE_CMD_SERPENTINE_10M;

    if(strstr(text, "蛇形后退10米") || strstr(text, "蛇形后退"))
        return U1_VOICE_CMD_SERPENTINE_BACK_10M;

    if(strstr(text, "逆时针转一圈") || strstr(text, "逆时针"))
        return U1_VOICE_CMD_REVERSE_TURN;

    if(strstr(text, "顺时针转一圈") || strstr(text, "顺时针"))
        return U1_VOICE_CMD_CLOCKWISE_TURN;

    if(strstr(text, "停进停车区1") || strstr(text, "停车区1"))
        return U1_VOICE_CMD_STOP_AREA_1;

    if(strstr(text, "停进停车区2") || strstr(text, "停车区2"))
        return U1_VOICE_CMD_STOP_AREA_2;

    if(strstr(text, "停进停车区3") || strstr(text, "停车区3"))
        return U1_VOICE_CMD_STOP_AREA_3;

    return U1_VOICE_CMD_NONE;
}

/* 获取命令文本描述 */
static const char* u1_get_command_text(u1_voice_command_t command_id)
{
    switch(command_id)
    {
        case U1_VOICE_CMD_LIGHT_ON: return U1_CMD_LIGHT_ON;
        case U1_VOICE_CMD_LEFT_LIGHT_ON: return U1_CMD_LEFT_LIGHT_ON;
        case U1_VOICE_CMD_RIGHT_LIGHT_ON: return U1_CMD_RIGHT_LIGHT_ON;
        case U1_VOICE_CMD_DISTANCE_LIGHT_ON: return U1_CMD_DISTANCE_LIGHT_ON;
        case U1_VOICE_CMD_NEAR_LIGHT_ON: return U1_CMD_NEAR_LIGHT_ON;
        case U1_VOICE_CMD_BRAKE_LIGHT_ON: return U1_CMD_BRAKE_LIGHT_ON;
        case U1_VOICE_CMD_FORWARD_10M: return U1_CMD_FORWARD_10M;
        case U1_VOICE_CMD_BACKWARD_10M: return U1_CMD_BACKWARD_10M;
        case U1_VOICE_CMD_SERPENTINE_10M: return U1_CMD_SERPENTINE_10M;
        case U1_VOICE_CMD_SERPENTINE_BACK_10M: return U1_CMD_SERPENTINE_BACK_10M;
        case U1_VOICE_CMD_REVERSE_TURN: return U1_CMD_REVERSE_TURN;
        case U1_VOICE_CMD_CLOCKWISE_TURN: return U1_CMD_CLOCKWISE_TURN;
        case U1_VOICE_CMD_STOP_AREA_1: return U1_CMD_STOP_AREA_1;
        case U1_VOICE_CMD_STOP_AREA_2: return U1_CMD_STOP_AREA_2;
        case U1_VOICE_CMD_STOP_AREA_3: return U1_CMD_STOP_AREA_3;
        default: return "无命令";
    }
}

/* 获取最后识别的语音命令ID */
u1_voice_command_t u1_get_last_command(void)
{
    if(result_ready)
    {
        return cached_result.command_id;
    }
    return U1_VOICE_CMD_NONE;
}

/* 获取语音命令描述 */
const char* u1_get_command_description(u1_voice_command_t command_id)
{
    return u1_get_command_text(command_id);
}

/* 检查是否识别到有效命令 */
boolean u1_is_valid_command(void)
{
    if(result_ready)
    {
        return cached_result.is_command;
    }
    return FALSE;
}
#endif
 