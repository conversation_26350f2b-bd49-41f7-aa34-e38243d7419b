/**
 * \file Ifx_Crc.h
 * \brief CRC algorithm
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup library_srvsw_sysse_math_crc CRC
 * This module implements CRC algorithm
 * \ingroup library_srvsw_sysse_math
 *
 */

#ifndef IFX_CRC_H
#define IFX_CRC_H        1
//---------------------------------------------------------------------------
#include "Cpu/Std/Ifx_Types.h"

#ifndef CRC_ENABLE_DPIPE
#define CRC_ENABLE_DPIPE 0
#endif

#if CRC_ENABLE_DPIPE
#include "IfxStdIf_DPipe.h"
#endif
//---------------------------------------------------------------------------
typedef struct
{
    sint32 order;
    uint32 polynom;
    sint32 refin;
    uint32 crchighbit;
    uint32 crcmask;
}Ifc_Crc_Table;
typedef struct
{
    Ifc_Crc_Table data;        /**< \brief CRC data, must be 1st member of the struct */
    uint8         crctab[256]; /**< \brief CRC Table, must be 2st member of the struct */
}Ifc_Crc_Table8;

typedef struct
{
    Ifc_Crc_Table data;        /**< \brief CRC data, must be 1st member of the struct */
    uint16        crctab[256]; /**< \brief CRC Table, must be 2st member of the struct */
}Ifc_Crc_Table16;

typedef struct
{
    Ifc_Crc_Table data;        /**< \brief CRC data, must be 1st member of the struct */
    uint32        crctab[256]; /**< \brief CRC Table, must be 2st member of the struct */
}Ifc_Crc_Table32;

typedef struct
{
    uint32               crcxor;
    sint32               refout;

    uint32               crcinit_direct;
    uint32               crcinit_nondirect;
    const Ifc_Crc_Table *table;
}Ifc_Crc;

/** \addtogroup library_srvsw_sysse_math_crc
 * \{ */
/**
 * \param direct [0,1] specifies the kind of algorithm: 1=direct, no augmented zero bits
 * \param driver pointer to the crc driver
 * \param table pointer to crc table
 * \param direct direct
 * \param crcinit is the initial CRC value belonging to that algorithm
 * \param crcxor is the final XOR value
 * \param refout [0,1] specifies if the CRC will be reflected before XOR
 * \param table pointer to the table: Ifc_Crc_Table8, Ifc_Crc_Table16, Ifc_Crc_Table32
 */
boolean Ifx_Crc_init(Ifc_Crc *driver, const Ifc_Crc_Table *table, sint32 direct, sint32 refout, uint32 crcinit, uint32 crcxor);
/**
 * \param table pointer to the crc table
 * \param order [1..32] is the CRC polynom order, counted without the leading '1' bit
 * \param polynom is the CRC polynom without leading '1' bit
 * \param refin [0,1] specifies if a data byte is reflected before processing (UART) or not
 */
boolean Ifx_Crc_createTable(Ifc_Crc_Table *table, sint32 order, uint32 polynom, sint32 refin);

#if CRC_ENABLE_DPIPE
boolean Ifx_Crc_Test(Ifc_Crc *driver, uint8 *string, uint32 length, IfxStdIf_DPipe *io);
void    Ifx_Crc_printTable(Ifc_Crc_Table *table, IfxStdIf_DPipe *io);
#endif
uint32 Ifx_Crc_tableFast(Ifc_Crc *driver, uint8 *p, uint32 len);
uint32 Ifx_Crc_table(Ifc_Crc *driver, uint8 *p, uint32 len);
uint32 Ifx_Crc_bitByBit(Ifc_Crc *driver, uint8 *p, uint32 len);
uint32 Ifx_Crc_bitByBitFast(Ifc_Crc *driver, uint8 *p, uint32 len);
/** \} */

//---------------------------------------------------------------------------
#endif  // IFX_CRC_H
