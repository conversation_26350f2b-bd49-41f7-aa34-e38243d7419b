	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc42512a --dep-file=zf_driver_soft_iic.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_soft_iic.src ../libraries/zf_driver/zf_driver_soft_iic.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_soft_iic.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_start',code,cluster('soft_iic_start')
	.sect	'.text.zf_driver_soft_iic.soft_iic_start'
	.align	2
	
; Function soft_iic_start
.L78:
soft_iic_start:	.type	func
	sub.a	a10,#8
.L466:
	ld.a	a15,[a4]16
	mov	d0,#1
	ld.w	d15,[a4]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L1028:
	ld.a	a15,[a4]20
	mov	d0,#1
	ld.w	d15,[a4]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L385:
	ld.w	d15,[a4]10
	st.w	[a10],d15
	j	.L2
.L3:
.L2:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L3
.L386:
	ld.a	a15,[a4]20
	movh	d0,#1
	ld.w	d15,[a4]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L389:
	ld.w	d15,[a4]10
	st.w	[a10],d15
	j	.L4
.L5:
.L4:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L5
.L390:
	ld.a	a15,[a4]16
	movh	d0,#1
	ld.w	d15,[a4]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L1029:
	ret
.L383:
	
__soft_iic_start_function_end:
	.size	soft_iic_start,__soft_iic_start_function_end-soft_iic_start
.L244:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_stop',code,cluster('soft_iic_stop')
	.sect	'.text.zf_driver_soft_iic.soft_iic_stop'
	.align	2
	
; Function soft_iic_stop
.L80:
soft_iic_stop:	.type	func
	sub.a	a10,#8
.L467:
	ld.a	a15,[a4]20
	movh	d0,#1
	ld.w	d15,[a4]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L1034:
	ld.a	a15,[a4]16
	movh	d0,#1
	ld.w	d15,[a4]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L395:
	ld.w	d15,[a4]10
	st.w	[a10],d15
	j	.L6
.L7:
.L6:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L7
.L396:
	ld.a	a15,[a4]16
	mov	d0,#1
	ld.w	d15,[a4]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L399:
	ld.w	d15,[a4]10
	st.w	[a10],d15
	j	.L8
.L9:
.L8:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L9
.L400:
	ld.a	a15,[a4]20
	mov	d0,#1
	ld.w	d15,[a4]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L403:
	ld.w	d15,[a4]10
	st.w	[a10],d15
	j	.L10
.L11:
.L10:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L11
.L404:
	ret
.L393:
	
__soft_iic_stop_function_end:
	.size	soft_iic_stop,__soft_iic_stop_function_end-soft_iic_stop
.L249:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_send_ack',code,cluster('soft_iic_send_ack')
	.sect	'.text.zf_driver_soft_iic.soft_iic_send_ack'
	.align	2
	
; Function soft_iic_send_ack
.L82:
soft_iic_send_ack:	.type	func
	sub.a	a10,#8
.L468:
	ld.a	a15,[a4]16
	movh	d0,#1
	ld.w	d15,[a4]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L1039:
	jeq	d4,#0,.L12
.L1040:
	ld.a	a15,[a4]20
	mov	d0,#1
	ld.w	d15,[a4]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L1041:
	j	.L13
.L12:
	ld.a	a15,[a4]20
	movh	d0,#1
	ld.w	d15,[a4]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L13:
	ld.w	d15,[a4]10
	st.w	[a10],d15
	j	.L14
.L15:
.L14:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L15
.L410:
	ld.a	a15,[a4]16
	mov	d0,#1
	ld.w	d15,[a4]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L413:
	ld.w	d15,[a4]10
	st.w	[a10],d15
	j	.L16
.L17:
.L16:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L17
.L414:
	ld.a	a15,[a4]16
	movh	d0,#1
	ld.w	d15,[a4]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L1042:
	ld.a	a15,[a4]20
	mov	d0,#1
	ld.w	d15,[a4]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a15]4,d0
.L1043:
	ret
.L407:
	
__soft_iic_send_ack_function_end:
	.size	soft_iic_send_ack,__soft_iic_send_ack_function_end-soft_iic_send_ack
.L254:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_wait_ack',code,cluster('soft_iic_wait_ack')
	.sect	'.text.zf_driver_soft_iic.soft_iic_wait_ack'
	.align	2
	
; Function soft_iic_wait_ack
.L84:
soft_iic_wait_ack:	.type	func
	sub.a	a10,#8
.L469:
	mov.aa	a15,a4
.L471:
	mov	d8,#0
.L472:
	ld.a	a2,[a15]16
	movh	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L1048:
	ld.a	a2,[a15]20
	mov	d0,#1
	ld.w	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L420:
	ld.w	d15,[a15]10
	st.w	[a10],d15
	j	.L18
.L19:
.L18:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L19
.L421:
	ld.a	a2,[a15]16
	mov	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L424:
	ld.w	d15,[a15]10
	st.w	[a10],d15
	j	.L20
.L21:
.L20:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L21
.L425:
	ld.w	d15,[a15]4
	extr	d4,d15,#0,#16
	call	gpio_get_level
.L470:
	jeq	d2,#0,.L22
.L1049:
	mov	d8,#1
.L22:
	ld.a	a2,[a15]16
	movh	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L428:
	ld.w	d15,[a15]10
	st.w	[a10],d15
	j	.L23
.L24:
.L23:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L24
.L429:
	mov	d2,d8
.L473:
	j	.L25
.L25:
	ret
.L417:
	
__soft_iic_wait_ack_function_end:
	.size	soft_iic_wait_ack,__soft_iic_wait_ack_function_end-soft_iic_wait_ack
.L259:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_send_data',code,cluster('soft_iic_send_data')
	.sect	'.text.zf_driver_soft_iic.soft_iic_send_data'
	.align	2
	
; Function soft_iic_send_data
.L86:
soft_iic_send_data:	.type	func
	sub.a	a10,#8
.L474:
	mov.aa	a15,a4
.L475:
	mov	d8,d4
.L476:
	mov	d9,#128
.L477:
	j	.L26
.L27:
	ld.w	d15,[a15]4
	extr	d4,d15,#0,#16
.L1054:
	and	d5,d8,d9
	call	gpio_set_level
.L1055:
	sha	d9,#-1
.L437:
	ld.w	d0,[a15]10
	st.w	[a10],d0
	j	.L28
.L29:
.L28:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L29
.L438:
	ld.a	a2,[a15]16
	mov	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L441:
	ld.w	d15,[a15]10
	st.w	[a10],d15
	j	.L30
.L31:
.L30:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L31
.L442:
	ld.a	a2,[a15]16
	movh	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L26:
	jne	d9,#0,.L27
.L1056:
	mov.aa	a4,a15
.L478:
	call	soft_iic_wait_ack
.L479:
	jne	d2,#1,.L32
.L1057:
	mov	d2,#0
.L1058:
	j	.L33
.L32:
	mov	d2,#1
.L33:
	j	.L34
.L34:
	ret
.L432:
	
__soft_iic_send_data_function_end:
	.size	soft_iic_send_data,__soft_iic_send_data_function_end-soft_iic_send_data
.L264:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_data',code,cluster('soft_iic_read_data')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_data'
	.align	2
	
; Function soft_iic_read_data
.L88:
soft_iic_read_data:	.type	func
	sub.a	a10,#8
.L480:
	mov.aa	a15,a4
.L481:
	mov	d9,d4
.L482:
	mov	d8,#0
.L483:
	mov	d10,#8
.L485:
	ld.a	a2,[a15]16
	movh	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L450:
	ld.w	d0,[a15]10
	st.w	[a10],d0
	j	.L35
.L36:
.L35:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L36
.L451:
	ld.a	a2,[a15]20
	mov	d0,#1
	ld.w	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L1063:
	j	.L37
.L38:
	ld.a	a2,[a15]16
	movh	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L454:
	ld.w	d15,[a15]10
	st.w	[a10],d15
	j	.L39
.L40:
.L39:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L40
.L455:
	ld.a	a2,[a15]16
	mov	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L458:
	ld.w	d15,[a15]10
	st.w	[a10],d15
	j	.L41
.L42:
.L41:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L42
.L459:
	sha	d8,#1
.L484:
	ld.w	d15,[a15]4
	extr	d4,d15,#0,#16
	call	gpio_get_level
.L1064:
	or	d8,d2
	extr.u	d8,d8,#0,#8
.L37:
	mov	d15,d10
	add	d10,#-1
.L486:
	extr.u	d10,d10,#0,#8
.L487:
	jne	d15,#0,.L38
.L1065:
	ld.a	a2,[a15]16
	movh	d0,#1
	ld.w	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2]4,d0
.L462:
	ld.w	d15,[a15]10
	st.w	[a10],d15
	j	.L43
.L44:
.L43:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
	add	d0,#-1
	st.w	[a10],d0
	jne	d15,#0,.L44
.L463:
	mov.aa	a4,a15
.L488:
	mov	d4,d9
.L490:
	call	soft_iic_send_ack
.L489:
	mov	d2,d8
.L491:
	j	.L45
.L45:
	ret
.L445:
	
__soft_iic_read_data_function_end:
	.size	soft_iic_read_data,__soft_iic_read_data_function_end-soft_iic_read_data
.L269:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_8bit',code,cluster('soft_iic_write_8bit')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_8bit'
	.align	2
	
	.global	soft_iic_write_8bit
; Function soft_iic_write_8bit
.L90:
soft_iic_write_8bit:	.type	func
	mov.aa	a15,a4
.L493:
	mov	d8,d4
.L494:
	mov.aa	a4,a15
	call	soft_iic_start
.L492:
	ld.bu	d15,[a15]8
.L811:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L495:
	call	soft_iic_send_data
.L496:
	mov.aa	a4,a15
.L497:
	mov	d4,d8
.L499:
	call	soft_iic_send_data
.L498:
	mov.aa	a4,a15
.L500:
	call	soft_iic_stop
.L501:
	ret
.L270:
	
__soft_iic_write_8bit_function_end:
	.size	soft_iic_write_8bit,__soft_iic_write_8bit_function_end-soft_iic_write_8bit
.L139:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_8bit_array',code,cluster('soft_iic_write_8bit_array')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_8bit_array'
	.align	2
	
	.global	soft_iic_write_8bit_array
; Function soft_iic_write_8bit_array
.L92:
soft_iic_write_8bit_array:	.type	func
	mov.aa	a15,a4
.L503:
	mov.aa	a12,a5
.L504:
	mov	d8,d4
.L505:
	mov.aa	a4,a15
	call	soft_iic_start
.L502:
	ld.bu	d15,[a15]8
.L816:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L506:
	call	soft_iic_send_data
.L507:
	j	.L46
.L47:
	ld.bu	d4,[a12]
.L817:
	add.a	a12,#1
	mov.aa	a4,a15
.L508:
	call	soft_iic_send_data
.L46:
	mov	d15,d8
	add	d8,#-1
.L818:
	jne	d15,#0,.L47
.L819:
	mov.aa	a4,a15
.L509:
	call	soft_iic_stop
.L510:
	ret
.L275:
	
__soft_iic_write_8bit_array_function_end:
	.size	soft_iic_write_8bit_array,__soft_iic_write_8bit_array_function_end-soft_iic_write_8bit_array
.L144:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_16bit',code,cluster('soft_iic_write_16bit')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_16bit'
	.align	2
	
	.global	soft_iic_write_16bit
; Function soft_iic_write_16bit
.L94:
soft_iic_write_16bit:	.type	func
	mov.aa	a15,a4
.L512:
	mov	d8,d4
.L513:
	mov.aa	a4,a15
	call	soft_iic_start
.L511:
	ld.bu	d15,[a15]8
.L824:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L514:
	call	soft_iic_send_data
.L515:
	mov.u	d15,#65280
.L825:
	and	d15,d8
.L826:
	sha	d4,d15,#-8
.L827:
	mov.aa	a4,a15
.L516:
	call	soft_iic_send_data
.L517:
	and	d4,d8,#255
.L828:
	mov.aa	a4,a15
.L518:
	call	soft_iic_send_data
.L519:
	mov.aa	a4,a15
.L520:
	call	soft_iic_stop
.L521:
	ret
.L281:
	
__soft_iic_write_16bit_function_end:
	.size	soft_iic_write_16bit,__soft_iic_write_16bit_function_end-soft_iic_write_16bit
.L149:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_16bit_array',code,cluster('soft_iic_write_16bit_array')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_16bit_array'
	.align	2
	
	.global	soft_iic_write_16bit_array
; Function soft_iic_write_16bit_array
.L96:
soft_iic_write_16bit_array:	.type	func
	mov.aa	a15,a4
.L523:
	mov.aa	a12,a5
.L524:
	mov	d8,d4
.L525:
	mov.aa	a4,a15
	call	soft_iic_start
.L522:
	ld.bu	d15,[a15]8
.L833:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L526:
	call	soft_iic_send_data
.L527:
	j	.L48
.L49:
	ld.hu	d15,[a12]0
.L834:
	mov.u	d0,#65280
.L835:
	and	d15,d0
.L836:
	sha	d4,d15,#-8
.L837:
	mov.aa	a4,a15
.L528:
	call	soft_iic_send_data
.L529:
	ld.hu	d15,[a12]0
.L838:
	and	d4,d15,#255
.L839:
	add.a	a12,#2
	mov.aa	a4,a15
.L530:
	call	soft_iic_send_data
.L48:
	mov	d15,d8
	add	d8,#-1
.L840:
	jne	d15,#0,.L49
.L841:
	mov.aa	a4,a15
.L531:
	call	soft_iic_stop
.L532:
	ret
.L285:
	
__soft_iic_write_16bit_array_function_end:
	.size	soft_iic_write_16bit_array,__soft_iic_write_16bit_array_function_end-soft_iic_write_16bit_array
.L154:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_8bit_register',code,cluster('soft_iic_write_8bit_register')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_8bit_register'
	.align	2
	
	.global	soft_iic_write_8bit_register
; Function soft_iic_write_8bit_register
.L98:
soft_iic_write_8bit_register:	.type	func
	mov.aa	a15,a4
.L534:
	mov	e8,d5,d4
.L846:
	mov.aa	a4,a15
	call	soft_iic_start
.L533:
	ld.bu	d15,[a15]8
.L847:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L535:
	call	soft_iic_send_data
.L536:
	mov.aa	a4,a15
.L537:
	mov	d4,d8
.L538:
	call	soft_iic_send_data
.L539:
	mov.aa	a4,a15
.L540:
	mov	d4,d9
.L541:
	call	soft_iic_send_data
.L542:
	mov.aa	a4,a15
.L543:
	call	soft_iic_stop
.L544:
	ret
.L290:
	
__soft_iic_write_8bit_register_function_end:
	.size	soft_iic_write_8bit_register,__soft_iic_write_8bit_register_function_end-soft_iic_write_8bit_register
.L159:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_8bit_registers',code,cluster('soft_iic_write_8bit_registers')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_8bit_registers'
	.align	2
	
	.global	soft_iic_write_8bit_registers
; Function soft_iic_write_8bit_registers
.L100:
soft_iic_write_8bit_registers:	.type	func
	mov.aa	a15,a4
.L546:
	mov	d8,d4
.L547:
	mov.aa	a12,a5
.L548:
	mov	d9,d5
.L549:
	mov.aa	a4,a15
	call	soft_iic_start
.L545:
	ld.bu	d15,[a15]8
.L852:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L550:
	call	soft_iic_send_data
.L551:
	mov.aa	a4,a15
.L552:
	mov	d4,d8
.L554:
	call	soft_iic_send_data
.L553:
	j	.L50
.L51:
	ld.bu	d4,[a12]
.L853:
	add.a	a12,#1
	mov.aa	a4,a15
.L555:
	call	soft_iic_send_data
.L50:
	mov	d15,d9
	add	d9,#-1
.L854:
	jne	d15,#0,.L51
.L855:
	mov.aa	a4,a15
.L556:
	call	soft_iic_stop
.L557:
	ret
.L296:
	
__soft_iic_write_8bit_registers_function_end:
	.size	soft_iic_write_8bit_registers,__soft_iic_write_8bit_registers_function_end-soft_iic_write_8bit_registers
.L164:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_16bit_register',code,cluster('soft_iic_write_16bit_register')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_16bit_register'
	.align	2
	
	.global	soft_iic_write_16bit_register
; Function soft_iic_write_16bit_register
.L102:
soft_iic_write_16bit_register:	.type	func
	mov.aa	a15,a4
.L559:
	mov	e8,d5,d4
.L860:
	mov.aa	a4,a15
	call	soft_iic_start
.L558:
	ld.bu	d15,[a15]8
.L861:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L560:
	call	soft_iic_send_data
.L561:
	mov.u	d15,#65280
.L562:
	and	d15,d8
.L563:
	sha	d4,d15,#-8
.L862:
	mov.aa	a4,a15
.L564:
	call	soft_iic_send_data
.L565:
	and	d4,d8,#255
.L566:
	mov.aa	a4,a15
.L567:
	call	soft_iic_send_data
.L568:
	mov.u	d15,#65280
.L569:
	and	d15,d9
.L570:
	sha	d4,d15,#-8
.L863:
	mov.aa	a4,a15
.L571:
	call	soft_iic_send_data
.L572:
	and	d4,d9,#255
.L573:
	mov.aa	a4,a15
.L574:
	call	soft_iic_send_data
.L575:
	mov.aa	a4,a15
.L576:
	call	soft_iic_stop
.L577:
	ret
.L302:
	
__soft_iic_write_16bit_register_function_end:
	.size	soft_iic_write_16bit_register,__soft_iic_write_16bit_register_function_end-soft_iic_write_16bit_register
.L169:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_write_16bit_registers',code,cluster('soft_iic_write_16bit_registers')
	.sect	'.text.zf_driver_soft_iic.soft_iic_write_16bit_registers'
	.align	2
	
	.global	soft_iic_write_16bit_registers
; Function soft_iic_write_16bit_registers
.L104:
soft_iic_write_16bit_registers:	.type	func
	mov.aa	a15,a4
.L579:
	mov	d8,d4
.L580:
	mov.aa	a12,a5
.L581:
	mov	d9,d5
.L582:
	mov.aa	a4,a15
	call	soft_iic_start
.L578:
	ld.bu	d15,[a15]8
.L868:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L583:
	call	soft_iic_send_data
.L584:
	mov.u	d15,#65280
.L869:
	and	d15,d8
.L870:
	sha	d4,d15,#-8
.L871:
	mov.aa	a4,a15
.L585:
	call	soft_iic_send_data
.L586:
	and	d4,d8,#255
.L872:
	mov.aa	a4,a15
.L587:
	call	soft_iic_send_data
.L588:
	j	.L52
.L53:
	ld.hu	d15,[a12]0
.L873:
	mov.u	d0,#65280
.L874:
	and	d15,d0
.L875:
	sha	d4,d15,#-8
.L876:
	mov.aa	a4,a15
.L589:
	call	soft_iic_send_data
.L590:
	ld.hu	d15,[a12]0
.L877:
	and	d4,d15,#255
.L878:
	add.a	a12,#2
	mov.aa	a4,a15
.L591:
	call	soft_iic_send_data
.L52:
	mov	d15,d9
	add	d9,#-1
.L879:
	jne	d15,#0,.L53
.L880:
	mov.aa	a4,a15
.L592:
	call	soft_iic_stop
.L593:
	ret
.L308:
	
__soft_iic_write_16bit_registers_function_end:
	.size	soft_iic_write_16bit_registers,__soft_iic_write_16bit_registers_function_end-soft_iic_write_16bit_registers
.L174:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_8bit',code,cluster('soft_iic_read_8bit')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_8bit'
	.align	2
	
	.global	soft_iic_read_8bit
; Function soft_iic_read_8bit
.L106:
soft_iic_read_8bit:	.type	func
	mov.aa	a15,a4
.L595:
	mov.aa	a4,a15
	call	soft_iic_start
.L594:
	ld.bu	d15,[a15]8
.L885:
	sha	d15,#1
.L886:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L596:
	call	soft_iic_send_data
.L597:
	mov	d4,#1
	mov.aa	a4,a15
.L598:
	call	soft_iic_read_data
.L599:
	mov	d15,d2
.L601:
	mov.aa	a4,a15
.L602:
	call	soft_iic_stop
.L600:
	mov	d2,d15
.L603:
	j	.L54
.L54:
	ret
.L315:
	
__soft_iic_read_8bit_function_end:
	.size	soft_iic_read_8bit,__soft_iic_read_8bit_function_end-soft_iic_read_8bit
.L179:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_8bit_array',code,cluster('soft_iic_read_8bit_array')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_8bit_array'
	.align	2
	
	.global	soft_iic_read_8bit_array
; Function soft_iic_read_8bit_array
.L108:
soft_iic_read_8bit_array:	.type	func
	mov.aa	a15,a4
.L605:
	mov.aa	a12,a5
.L606:
	mov	d8,d4
.L607:
	mov.aa	a4,a15
	call	soft_iic_start
.L604:
	ld.bu	d15,[a15]8
.L891:
	sha	d15,#1
.L892:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L608:
	call	soft_iic_send_data
.L609:
	j	.L55
.L56:
	eq	d4,d8,#0
	mov.aa	a4,a15
.L610:
	call	soft_iic_read_data
.L611:
	st.b	[a12],d2
.L893:
	add.a	a12,#1
.L55:
	mov	d15,d8
	add	d8,#-1
.L894:
	jne	d15,#0,.L56
.L895:
	mov.aa	a4,a15
.L612:
	call	soft_iic_stop
.L613:
	ret
.L318:
	
__soft_iic_read_8bit_array_function_end:
	.size	soft_iic_read_8bit_array,__soft_iic_read_8bit_array_function_end-soft_iic_read_8bit_array
.L184:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_16bit',code,cluster('soft_iic_read_16bit')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_16bit'
	.align	2
	
	.global	soft_iic_read_16bit
; Function soft_iic_read_16bit
.L110:
soft_iic_read_16bit:	.type	func
	mov.aa	a15,a4
.L615:
	mov.aa	a4,a15
	call	soft_iic_start
.L614:
	ld.bu	d15,[a15]8
.L900:
	sha	d15,#1
.L901:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L616:
	call	soft_iic_send_data
.L617:
	mov	d4,#0
	mov.aa	a4,a15
.L618:
	call	soft_iic_read_data
.L619:
	sha	d15,d2,#8
.L902:
	mov	d4,#1
	mov.aa	a4,a15
.L621:
	call	soft_iic_read_data
.L620:
	or	d15,d2
.L903:
	mov.aa	a4,a15
.L622:
	call	soft_iic_stop
.L623:
	mov	d2,d15
.L624:
	j	.L57
.L57:
	ret
.L324:
	
__soft_iic_read_16bit_function_end:
	.size	soft_iic_read_16bit,__soft_iic_read_16bit_function_end-soft_iic_read_16bit
.L189:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_16bit_array',code,cluster('soft_iic_read_16bit_array')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_16bit_array'
	.align	2
	
	.global	soft_iic_read_16bit_array
; Function soft_iic_read_16bit_array
.L112:
soft_iic_read_16bit_array:	.type	func
	mov.aa	a15,a4
.L626:
	mov.aa	a12,a5
.L627:
	mov	d8,d4
.L628:
	mov.aa	a4,a15
	call	soft_iic_start
.L625:
	ld.bu	d15,[a15]8
.L908:
	sha	d15,#1
.L909:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L629:
	call	soft_iic_send_data
.L630:
	j	.L58
.L59:
	mov	d4,#0
	mov.aa	a4,a15
.L631:
	call	soft_iic_read_data
.L632:
	st.h	[a12],d2
.L910:
	ld.hu	d15,[a12]0
.L911:
	sha	d15,d15,#8
.L912:
	eq	d4,d8,#0
	mov.aa	a4,a15
.L633:
	call	soft_iic_read_data
.L634:
	or	d15,d2
.L913:
	st.h	[a12],d15
.L914:
	add.a	a12,#2
.L58:
	mov	d15,d8
	add	d8,#-1
.L915:
	jne	d15,#0,.L59
.L916:
	mov.aa	a4,a15
.L635:
	call	soft_iic_stop
.L636:
	ret
.L327:
	
__soft_iic_read_16bit_array_function_end:
	.size	soft_iic_read_16bit_array,__soft_iic_read_16bit_array_function_end-soft_iic_read_16bit_array
.L194:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_8bit_register',code,cluster('soft_iic_read_8bit_register')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_8bit_register'
	.align	2
	
	.global	soft_iic_read_8bit_register
; Function soft_iic_read_8bit_register
.L114:
soft_iic_read_8bit_register:	.type	func
	mov.aa	a15,a4
.L638:
	mov	d8,d4
.L639:
	mov.aa	a4,a15
	call	soft_iic_start
.L637:
	ld.bu	d15,[a15]8
.L921:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L640:
	call	soft_iic_send_data
.L641:
	mov.aa	a4,a15
.L642:
	mov	d4,d8
.L644:
	call	soft_iic_send_data
.L643:
	mov.aa	a4,a15
.L645:
	call	soft_iic_start
.L646:
	ld.bu	d15,[a15]8
.L922:
	sha	d15,#1
.L923:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L647:
	call	soft_iic_send_data
.L648:
	mov	d4,#1
	mov.aa	a4,a15
.L649:
	call	soft_iic_read_data
.L650:
	mov	d15,d2
.L652:
	mov.aa	a4,a15
.L653:
	call	soft_iic_stop
.L651:
	mov	d2,d15
.L654:
	j	.L60
.L60:
	ret
.L332:
	
__soft_iic_read_8bit_register_function_end:
	.size	soft_iic_read_8bit_register,__soft_iic_read_8bit_register_function_end-soft_iic_read_8bit_register
.L199:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_8bit_registers',code,cluster('soft_iic_read_8bit_registers')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_8bit_registers'
	.align	2
	
	.global	soft_iic_read_8bit_registers
; Function soft_iic_read_8bit_registers
.L116:
soft_iic_read_8bit_registers:	.type	func
	mov.aa	a15,a4
.L656:
	mov	d8,d4
.L657:
	mov.aa	a12,a5
.L658:
	mov	d9,d5
.L659:
	mov.aa	a4,a15
	call	soft_iic_start
.L655:
	ld.bu	d15,[a15]8
.L928:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L660:
	call	soft_iic_send_data
.L661:
	mov.aa	a4,a15
.L662:
	mov	d4,d8
.L664:
	call	soft_iic_send_data
.L663:
	mov.aa	a4,a15
.L665:
	call	soft_iic_start
.L666:
	ld.bu	d15,[a15]8
.L929:
	sha	d15,#1
.L930:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L667:
	call	soft_iic_send_data
.L668:
	j	.L61
.L62:
	eq	d4,d9,#0
	mov.aa	a4,a15
.L669:
	call	soft_iic_read_data
.L670:
	st.b	[a12],d2
.L931:
	add.a	a12,#1
.L61:
	mov	d15,d9
	add	d9,#-1
.L932:
	jne	d15,#0,.L62
.L933:
	mov.aa	a4,a15
.L671:
	call	soft_iic_stop
.L672:
	ret
.L337:
	
__soft_iic_read_8bit_registers_function_end:
	.size	soft_iic_read_8bit_registers,__soft_iic_read_8bit_registers_function_end-soft_iic_read_8bit_registers
.L204:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_16bit_register',code,cluster('soft_iic_read_16bit_register')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_16bit_register'
	.align	2
	
	.global	soft_iic_read_16bit_register
; Function soft_iic_read_16bit_register
.L118:
soft_iic_read_16bit_register:	.type	func
	mov.aa	a15,a4
.L674:
	mov	d8,d4
.L675:
	mov.aa	a4,a15
	call	soft_iic_start
.L673:
	ld.bu	d15,[a15]8
.L938:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L676:
	call	soft_iic_send_data
.L677:
	mov.u	d15,#65280
.L939:
	and	d15,d8
.L940:
	sha	d4,d15,#-8
.L941:
	mov.aa	a4,a15
.L678:
	call	soft_iic_send_data
.L679:
	and	d4,d8,#255
.L942:
	mov.aa	a4,a15
.L680:
	call	soft_iic_send_data
.L681:
	mov.aa	a4,a15
.L682:
	call	soft_iic_start
.L683:
	ld.bu	d15,[a15]8
.L943:
	sha	d15,#1
.L944:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L684:
	call	soft_iic_send_data
.L685:
	mov	d4,#0
	mov.aa	a4,a15
.L686:
	call	soft_iic_read_data
.L687:
	sha	d15,d2,#8
.L945:
	mov	d4,#1
	mov.aa	a4,a15
.L689:
	call	soft_iic_read_data
.L688:
	or	d15,d2
.L946:
	mov.aa	a4,a15
.L690:
	call	soft_iic_stop
.L691:
	mov	d2,d15
.L692:
	j	.L63
.L63:
	ret
.L343:
	
__soft_iic_read_16bit_register_function_end:
	.size	soft_iic_read_16bit_register,__soft_iic_read_16bit_register_function_end-soft_iic_read_16bit_register
.L209:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_read_16bit_registers',code,cluster('soft_iic_read_16bit_registers')
	.sect	'.text.zf_driver_soft_iic.soft_iic_read_16bit_registers'
	.align	2
	
	.global	soft_iic_read_16bit_registers
; Function soft_iic_read_16bit_registers
.L120:
soft_iic_read_16bit_registers:	.type	func
	mov.aa	a15,a4
.L694:
	mov	d8,d4
.L695:
	mov.aa	a12,a5
.L696:
	mov	d9,d5
.L697:
	mov.aa	a4,a15
	call	soft_iic_start
.L693:
	ld.bu	d15,[a15]8
.L951:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L698:
	call	soft_iic_send_data
.L699:
	mov.u	d15,#65280
.L952:
	and	d15,d8
.L953:
	sha	d4,d15,#-8
.L954:
	mov.aa	a4,a15
.L700:
	call	soft_iic_send_data
.L701:
	and	d4,d8,#255
.L955:
	mov.aa	a4,a15
.L702:
	call	soft_iic_send_data
.L703:
	mov.aa	a4,a15
.L704:
	call	soft_iic_start
.L705:
	ld.bu	d15,[a15]8
.L956:
	sha	d15,#1
.L957:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L706:
	call	soft_iic_send_data
.L707:
	j	.L64
.L65:
	mov	d4,#0
	mov.aa	a4,a15
.L708:
	call	soft_iic_read_data
.L709:
	st.h	[a12],d2
.L958:
	ld.hu	d15,[a12]0
.L959:
	sha	d15,d15,#8
.L960:
	eq	d4,d9,#0
	mov.aa	a4,a15
.L710:
	call	soft_iic_read_data
.L711:
	or	d15,d2
.L961:
	st.h	[a12],d15
.L962:
	add.a	a12,#2
.L64:
	mov	d15,d9
	add	d9,#-1
.L963:
	jne	d15,#0,.L65
.L964:
	mov.aa	a4,a15
.L712:
	call	soft_iic_stop
.L713:
	ret
.L348:
	
__soft_iic_read_16bit_registers_function_end:
	.size	soft_iic_read_16bit_registers,__soft_iic_read_16bit_registers_function_end-soft_iic_read_16bit_registers
.L214:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_transfer_8bit_array',code,cluster('soft_iic_transfer_8bit_array')
	.sect	'.text.zf_driver_soft_iic.soft_iic_transfer_8bit_array'
	.align	2
	
	.global	soft_iic_transfer_8bit_array
; Function soft_iic_transfer_8bit_array
.L122:
soft_iic_transfer_8bit_array:	.type	func
	mov.aa	a15,a4
.L715:
	mov.aa	a12,a5
.L716:
	mov	d8,d4
.L717:
	mov.aa	a13,a6
.L718:
	mov	d9,d5
.L719:
	mov.aa	a4,a15
	call	soft_iic_start
.L714:
	ld.bu	d15,[a15]8
.L969:
	sha	d0,d15,#1
	extr.u	d4,d0,#0,#8
	mov.aa	a4,a15
.L720:
	call	soft_iic_send_data
.L721:
	j	.L66
.L67:
	ld.bu	d4,[a12]
.L970:
	add.a	a12,#1
	mov.aa	a4,a15
.L722:
	call	soft_iic_send_data
.L66:
	mov	d15,d8
	add	d8,#-1
.L971:
	jne	d15,#0,.L67
.L972:
	jeq	d9,#0,.L68
.L973:
	mov.aa	a4,a15
.L723:
	call	soft_iic_start
.L724:
	ld.bu	d15,[a15]8
.L974:
	sha	d0,d15,#1
.L975:
	or	d15,d0,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L725:
	call	soft_iic_send_data
.L726:
	j	.L69
.L70:
	eq	d4,d9,#0
	mov.aa	a4,a15
.L727:
	call	soft_iic_read_data
.L728:
	st.b	[a13],d2
.L976:
	add.a	a13,#1
.L69:
	mov	d15,d9
	add	d9,#-1
.L977:
	jne	d15,#0,.L70
.L68:
	mov.aa	a4,a15
.L729:
	call	soft_iic_stop
.L730:
	ret
.L354:
	
__soft_iic_transfer_8bit_array_function_end:
	.size	soft_iic_transfer_8bit_array,__soft_iic_transfer_8bit_array_function_end-soft_iic_transfer_8bit_array
.L219:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_transfer_16bit_array',code,cluster('soft_iic_transfer_16bit_array')
	.sect	'.text.zf_driver_soft_iic.soft_iic_transfer_16bit_array'
	.align	2
	
	.global	soft_iic_transfer_16bit_array
; Function soft_iic_transfer_16bit_array
.L124:
soft_iic_transfer_16bit_array:	.type	func
	mov.aa	a15,a4
.L732:
	mov.aa	a12,a5
.L733:
	mov	d8,d4
.L734:
	mov.aa	a13,a6
.L735:
	mov	d9,d5
.L736:
	mov.aa	a4,a15
	call	soft_iic_start
.L731:
	ld.bu	d15,[a15]8
.L982:
	sha	d0,d15,#1
	extr.u	d4,d0,#0,#8
	mov.aa	a4,a15
.L737:
	call	soft_iic_send_data
.L738:
	j	.L71
.L72:
	ld.hu	d0,[a12]0
.L983:
	mov.u	d1,#65280
.L984:
	and	d0,d1
.L985:
	sha	d4,d0,#-8
.L986:
	mov.aa	a4,a15
.L739:
	call	soft_iic_send_data
.L740:
	ld.hu	d15,[a12]0
.L987:
	and	d4,d15,#255
.L988:
	add.a	a12,#2
	mov.aa	a4,a15
.L741:
	call	soft_iic_send_data
.L71:
	mov	d15,d8
	add	d8,#-1
.L989:
	jne	d15,#0,.L72
.L990:
	jeq	d9,#0,.L73
.L991:
	mov.aa	a4,a15
.L742:
	call	soft_iic_start
.L743:
	ld.bu	d15,[a15]8
.L992:
	sha	d15,#1
.L993:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L744:
	call	soft_iic_send_data
.L745:
	j	.L74
.L75:
	mov	d4,#0
	mov.aa	a4,a15
.L746:
	call	soft_iic_read_data
.L747:
	st.h	[a13],d2
.L994:
	ld.hu	d15,[a13]0
.L995:
	sha	d15,d15,#8
.L996:
	eq	d4,d9,#0
	mov.aa	a4,a15
.L748:
	call	soft_iic_read_data
.L749:
	or	d15,d2
.L997:
	st.h	[a13],d15
.L998:
	add.a	a13,#2
.L74:
	mov	d15,d9
	add	d9,#-1
.L999:
	jne	d15,#0,.L75
.L73:
	mov.aa	a4,a15
.L750:
	call	soft_iic_stop
.L751:
	ret
.L360:
	
__soft_iic_transfer_16bit_array_function_end:
	.size	soft_iic_transfer_16bit_array,__soft_iic_transfer_16bit_array_function_end-soft_iic_transfer_16bit_array
.L224:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_sccb_write_register',code,cluster('soft_iic_sccb_write_register')
	.sect	'.text.zf_driver_soft_iic.soft_iic_sccb_write_register'
	.align	2
	
	.global	soft_iic_sccb_write_register
; Function soft_iic_sccb_write_register
.L126:
soft_iic_sccb_write_register:	.type	func
	mov.aa	a15,a4
.L753:
	mov	e8,d5,d4
.L1004:
	mov.aa	a4,a15
	call	soft_iic_start
.L752:
	ld.bu	d15,[a15]8
.L1005:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L754:
	call	soft_iic_send_data
.L755:
	mov.aa	a4,a15
.L756:
	mov	d4,d8
.L757:
	call	soft_iic_send_data
.L758:
	mov.aa	a4,a15
.L759:
	mov	d4,d9
.L760:
	call	soft_iic_send_data
.L761:
	mov.aa	a4,a15
.L762:
	call	soft_iic_stop
.L763:
	ret
.L366:
	
__soft_iic_sccb_write_register_function_end:
	.size	soft_iic_sccb_write_register,__soft_iic_sccb_write_register_function_end-soft_iic_sccb_write_register
.L229:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_sccb_read_register',code,cluster('soft_iic_sccb_read_register')
	.sect	'.text.zf_driver_soft_iic.soft_iic_sccb_read_register'
	.align	2
	
	.global	soft_iic_sccb_read_register
; Function soft_iic_sccb_read_register
.L128:
soft_iic_sccb_read_register:	.type	func
	mov.aa	a15,a4
.L765:
	mov	d8,d4
.L766:
	mov.aa	a4,a15
	call	soft_iic_start
.L764:
	ld.bu	d15,[a15]8
.L1010:
	sha	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L767:
	call	soft_iic_send_data
.L768:
	mov.aa	a4,a15
.L769:
	mov	d4,d8
.L771:
	call	soft_iic_send_data
.L770:
	mov.aa	a4,a15
.L772:
	call	soft_iic_stop
.L773:
	mov.aa	a4,a15
.L774:
	call	soft_iic_start
.L775:
	ld.bu	d15,[a15]8
.L1011:
	sha	d15,#1
.L1012:
	or	d15,#1
	extr.u	d4,d15,#0,#8
	mov.aa	a4,a15
.L776:
	call	soft_iic_send_data
.L777:
	mov	d4,#1
	mov.aa	a4,a15
.L778:
	call	soft_iic_read_data
.L779:
	mov	d15,d2
.L781:
	mov.aa	a4,a15
.L782:
	call	soft_iic_stop
.L780:
	mov	d2,d15
.L783:
	j	.L76
.L76:
	ret
.L371:
	
__soft_iic_sccb_read_register_function_end:
	.size	soft_iic_sccb_read_register,__soft_iic_sccb_read_register_function_end-soft_iic_sccb_read_register
.L234:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_iic.soft_iic_init',code,cluster('soft_iic_init')
	.sect	'.text.zf_driver_soft_iic.soft_iic_init'
	.align	2
	
	.global	soft_iic_init
; Function soft_iic_init
.L130:
soft_iic_init:	.type	func
	mov.aa	a15,a4
.L788:
	mov	d15,d4
.L789:
	mov	e8,d6,d5
	mov	d10,d7
.L790:
	ne	d4,d9,d10
.L784:
	movh.a	a4,#@his(.1.str)
.L787:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#673
.L785:
	call	debug_assert_handler
.L786:
	st.w	[a15],d9
.L791:
	st.w	[a15]4,d10
.L1017:
	st.b	[a15]8,d15
.L792:
	st.w	[a15]10,d8
.L793:
	mov	d4,d9
.L794:
	call	get_port
.L795:
	st.a	[a15]16,a2
.L1018:
	mov	d4,d10
.L796:
	call	get_port
.L797:
	st.a	[a15]20,a2
.L1019:
	mov	d5,#1
.L1020:
	mov	d6,#1
.L1021:
	mov	d7,#3
.L798:
	mov	d4,d9
.L799:
	call	gpio_init
.L800:
	mov	d5,#1
.L1022:
	mov	d6,#1
.L1023:
	mov	d7,#4
	mov	d4,d10
.L801:
	call	gpio_init
.L802:
	ret
.L376:
	
__soft_iic_init_function_end:
	.size	soft_iic_init,__soft_iic_init_function_end-soft_iic_init
.L239:
	; End of function
	
	.sdecl	'.rodata.zf_driver_soft_iic..1.str',data,rom
	.sect	'.rodata.zf_driver_soft_iic..1.str'
.1.str:	.type	object
	.size	.1.str,44
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,115,111,102,116,95,105,105
	.byte	99,46,99
	.space	1
	.calls	'soft_iic_wait_ack','gpio_get_level'
	.calls	'soft_iic_send_data','gpio_set_level'
	.calls	'soft_iic_send_data','soft_iic_wait_ack'
	.calls	'soft_iic_read_data','gpio_get_level'
	.calls	'soft_iic_read_data','soft_iic_send_ack'
	.calls	'soft_iic_write_8bit','soft_iic_start'
	.calls	'soft_iic_write_8bit','soft_iic_send_data'
	.calls	'soft_iic_write_8bit','soft_iic_stop'
	.calls	'soft_iic_write_8bit_array','soft_iic_start'
	.calls	'soft_iic_write_8bit_array','soft_iic_send_data'
	.calls	'soft_iic_write_8bit_array','soft_iic_stop'
	.calls	'soft_iic_write_16bit','soft_iic_start'
	.calls	'soft_iic_write_16bit','soft_iic_send_data'
	.calls	'soft_iic_write_16bit','soft_iic_stop'
	.calls	'soft_iic_write_16bit_array','soft_iic_start'
	.calls	'soft_iic_write_16bit_array','soft_iic_send_data'
	.calls	'soft_iic_write_16bit_array','soft_iic_stop'
	.calls	'soft_iic_write_8bit_register','soft_iic_start'
	.calls	'soft_iic_write_8bit_register','soft_iic_send_data'
	.calls	'soft_iic_write_8bit_register','soft_iic_stop'
	.calls	'soft_iic_write_8bit_registers','soft_iic_start'
	.calls	'soft_iic_write_8bit_registers','soft_iic_send_data'
	.calls	'soft_iic_write_8bit_registers','soft_iic_stop'
	.calls	'soft_iic_write_16bit_register','soft_iic_start'
	.calls	'soft_iic_write_16bit_register','soft_iic_send_data'
	.calls	'soft_iic_write_16bit_register','soft_iic_stop'
	.calls	'soft_iic_write_16bit_registers','soft_iic_start'
	.calls	'soft_iic_write_16bit_registers','soft_iic_send_data'
	.calls	'soft_iic_write_16bit_registers','soft_iic_stop'
	.calls	'soft_iic_read_8bit','soft_iic_start'
	.calls	'soft_iic_read_8bit','soft_iic_send_data'
	.calls	'soft_iic_read_8bit','soft_iic_read_data'
	.calls	'soft_iic_read_8bit','soft_iic_stop'
	.calls	'soft_iic_read_8bit_array','soft_iic_start'
	.calls	'soft_iic_read_8bit_array','soft_iic_send_data'
	.calls	'soft_iic_read_8bit_array','soft_iic_read_data'
	.calls	'soft_iic_read_8bit_array','soft_iic_stop'
	.calls	'soft_iic_read_16bit','soft_iic_start'
	.calls	'soft_iic_read_16bit','soft_iic_send_data'
	.calls	'soft_iic_read_16bit','soft_iic_read_data'
	.calls	'soft_iic_read_16bit','soft_iic_stop'
	.calls	'soft_iic_read_16bit_array','soft_iic_start'
	.calls	'soft_iic_read_16bit_array','soft_iic_send_data'
	.calls	'soft_iic_read_16bit_array','soft_iic_read_data'
	.calls	'soft_iic_read_16bit_array','soft_iic_stop'
	.calls	'soft_iic_read_8bit_register','soft_iic_start'
	.calls	'soft_iic_read_8bit_register','soft_iic_send_data'
	.calls	'soft_iic_read_8bit_register','soft_iic_read_data'
	.calls	'soft_iic_read_8bit_register','soft_iic_stop'
	.calls	'soft_iic_read_8bit_registers','soft_iic_start'
	.calls	'soft_iic_read_8bit_registers','soft_iic_send_data'
	.calls	'soft_iic_read_8bit_registers','soft_iic_read_data'
	.calls	'soft_iic_read_8bit_registers','soft_iic_stop'
	.calls	'soft_iic_read_16bit_register','soft_iic_start'
	.calls	'soft_iic_read_16bit_register','soft_iic_send_data'
	.calls	'soft_iic_read_16bit_register','soft_iic_read_data'
	.calls	'soft_iic_read_16bit_register','soft_iic_stop'
	.calls	'soft_iic_read_16bit_registers','soft_iic_start'
	.calls	'soft_iic_read_16bit_registers','soft_iic_send_data'
	.calls	'soft_iic_read_16bit_registers','soft_iic_read_data'
	.calls	'soft_iic_read_16bit_registers','soft_iic_stop'
	.calls	'soft_iic_transfer_8bit_array','soft_iic_start'
	.calls	'soft_iic_transfer_8bit_array','soft_iic_send_data'
	.calls	'soft_iic_transfer_8bit_array','soft_iic_read_data'
	.calls	'soft_iic_transfer_8bit_array','soft_iic_stop'
	.calls	'soft_iic_transfer_16bit_array','soft_iic_start'
	.calls	'soft_iic_transfer_16bit_array','soft_iic_send_data'
	.calls	'soft_iic_transfer_16bit_array','soft_iic_read_data'
	.calls	'soft_iic_transfer_16bit_array','soft_iic_stop'
	.calls	'soft_iic_sccb_write_register','soft_iic_start'
	.calls	'soft_iic_sccb_write_register','soft_iic_send_data'
	.calls	'soft_iic_sccb_write_register','soft_iic_stop'
	.calls	'soft_iic_sccb_read_register','soft_iic_start'
	.calls	'soft_iic_sccb_read_register','soft_iic_send_data'
	.calls	'soft_iic_sccb_read_register','soft_iic_stop'
	.calls	'soft_iic_sccb_read_register','soft_iic_read_data'
	.calls	'soft_iic_init','debug_assert_handler'
	.calls	'soft_iic_init','get_port'
	.calls	'soft_iic_init','gpio_init'
	.calls	'soft_iic_start','',8
	.calls	'soft_iic_stop','',8
	.calls	'soft_iic_send_ack','',8
	.calls	'soft_iic_wait_ack','',8
	.calls	'soft_iic_send_data','',8
	.calls	'soft_iic_read_data','',8
	.calls	'soft_iic_write_8bit','',0
	.calls	'soft_iic_write_8bit_array','',0
	.calls	'soft_iic_write_16bit','',0
	.calls	'soft_iic_write_16bit_array','',0
	.calls	'soft_iic_write_8bit_register','',0
	.calls	'soft_iic_write_8bit_registers','',0
	.calls	'soft_iic_write_16bit_register','',0
	.calls	'soft_iic_write_16bit_registers','',0
	.calls	'soft_iic_read_8bit','',0
	.calls	'soft_iic_read_8bit_array','',0
	.calls	'soft_iic_read_16bit','',0
	.calls	'soft_iic_read_16bit_array','',0
	.calls	'soft_iic_read_8bit_register','',0
	.calls	'soft_iic_read_8bit_registers','',0
	.calls	'soft_iic_read_16bit_register','',0
	.calls	'soft_iic_read_16bit_registers','',0
	.calls	'soft_iic_transfer_8bit_array','',0
	.calls	'soft_iic_transfer_16bit_array','',0
	.calls	'soft_iic_sccb_write_register','',0
	.calls	'soft_iic_sccb_read_register','',0
	.extern	debug_assert_handler
	.extern	get_port
	.extern	gpio_set_level
	.extern	gpio_get_level
	.extern	gpio_init
	.calls	'soft_iic_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L132:
	.word	38971
	.half	3
	.word	.L133
	.byte	4
.L131:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L134
	.byte	2,1,1,3
	.word	206
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	209
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	254
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	266
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	346
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	320
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	352
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	352
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	320
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L314:
	.byte	7
	.byte	'unsigned char',0,1,8
.L323:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	816
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1387
	.byte	4,2,35,0,0,14,4
	.word	461
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1515
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1730
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2382
	.byte	4,2,35,0,0,14,24
	.word	461
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3009
	.byte	4,2,35,0,0,14,8
	.word	461
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3334
	.byte	4,2,35,0,0,14,12
	.word	461
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4326
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4642
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	478
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5163
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5337
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6002
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6350
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6474
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6738
	.byte	4,2,35,0,0,14,76
	.word	461
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6991
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	776
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1347
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1466
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1506
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1690
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1905
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2122
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2342
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1506
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2656
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2696
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2969
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3285
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3325
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3625
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3665
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4000
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4286
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3325
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4433
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4602
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4774
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4949
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5123
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5297
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5473
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5629
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5962
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6310
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3325
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6434
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6683
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6942
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6982
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7038
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7605
	.byte	4,3,35,252,1,0,16
	.word	7645
	.byte	3
	.word	8248
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8253
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	461
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8258
	.byte	6,0,7
	.byte	'char',0,1,6,3
	.word	8439
	.byte	19
	.byte	'debug_assert_handler',0,5,112,9,1,1,1,1,5
	.byte	'pass',0,5,112,47
	.word	461
	.byte	5
	.byte	'file',0,5,112,59
	.word	8447
	.byte	5
	.byte	'line',0,5,112,69
	.word	454
	.byte	0,20
	.word	214
	.byte	21
	.word	240
	.byte	6,0,20
	.word	275
	.byte	21
	.word	307
	.byte	6,0,20
	.word	357
	.byte	21
	.word	376
	.byte	6,0,20
	.word	392
	.byte	21
	.word	407
	.byte	21
	.word	421
	.byte	6,0,20
	.word	8361
	.byte	21
	.word	8389
	.byte	21
	.word	8403
	.byte	21
	.word	8421
	.byte	6,0
.L380:
	.byte	17,6,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'get_port',0,6,114,13
	.word	8253
	.byte	1,1,1,1,5
	.byte	'pin',0,6,114,56
	.word	8596
	.byte	0,19
	.byte	'gpio_set_level',0,6,139,1,7,1,1,1,1,5
	.byte	'pin',0,6,139,1,40
	.word	8596
	.byte	5
	.byte	'dat',0,6,139,1,51
	.word	461
	.byte	0,22
	.byte	'gpio_get_level',0,6,140,1,7
	.word	461
	.byte	1,1,1,1,5
	.byte	'pin',0,6,140,1,40
	.word	8596
	.byte	0,17,6,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,6,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,19
	.byte	'gpio_init',0,6,143,1,7,1,1,1,1,5
	.byte	'pin',0,6,143,1,40
	.word	8596
	.byte	5
	.byte	'dir',0,6,143,1,59
	.word	10663
	.byte	5
	.byte	'dat',0,6,143,1,70
	.word	461
	.byte	5
	.byte	'pinconf',0,6,143,1,90
	.word	10681
	.byte	0
.L279:
	.byte	7
	.byte	'unsigned long int',0,4,7,23,7,42,9,24,13
	.byte	'scl_pin',0
	.word	10844
	.byte	4,2,35,0,13
	.byte	'sda_pin',0
	.word	10844
	.byte	4,2,35,4,13
	.byte	'addr',0
	.word	461
	.byte	1,2,35,8,13
	.byte	'delay',0
	.word	10844
	.byte	4,2,35,10,13
	.byte	'iic_scl',0
	.word	352
	.byte	4,2,35,16,13
	.byte	'iic_sda',0
	.word	352
	.byte	4,2,35,20,0
.L271:
	.byte	3
	.word	10865
.L273:
	.byte	24
	.word	461
	.byte	24
	.word	461
.L277:
	.byte	3
	.word	10978
.L283:
	.byte	24
	.word	478
	.byte	24
	.word	478
.L287:
	.byte	3
	.word	10993
.L292:
	.byte	24
	.word	461
.L294:
	.byte	24
	.word	461
.L298:
	.byte	24
	.word	461
.L304:
	.byte	24
	.word	478
.L306:
	.byte	24
	.word	478
.L310:
	.byte	24
	.word	478
.L320:
	.byte	3
	.word	461
.L329:
	.byte	3
	.word	478
.L334:
	.byte	24
	.word	461
.L339:
	.byte	24
	.word	461
.L345:
	.byte	24
	.word	478
.L350:
	.byte	24
	.word	478
.L368:
	.byte	24
	.word	461
.L373:
	.byte	24
	.word	461
.L387:
	.byte	16
	.word	10844
.L391:
	.byte	16
	.word	10844
.L397:
	.byte	16
	.word	10844
.L401:
	.byte	16
	.word	10844
.L405:
	.byte	16
	.word	10844
.L411:
	.byte	16
	.word	10844
.L415:
	.byte	16
	.word	10844
.L422:
	.byte	16
	.word	10844
.L426:
	.byte	16
	.word	10844
.L430:
	.byte	16
	.word	10844
.L434:
	.byte	24
	.word	461
.L439:
	.byte	16
	.word	10844
.L443:
	.byte	16
	.word	10844
.L452:
	.byte	16
	.word	10844
.L456:
	.byte	16
	.word	10844
.L460:
	.byte	16
	.word	10844
.L464:
	.byte	16
	.word	10844
	.byte	7
	.byte	'short int',0,2,5,25
	.byte	'__wchar_t',0,8,1,1
	.word	11158
	.byte	25
	.byte	'__size_t',0,8,1,1
	.word	438
	.byte	25
	.byte	'__ptrdiff_t',0,8,1,1
	.word	454
	.byte	26,1,3
	.word	11226
	.byte	25
	.byte	'__codeptr',0,8,1,1
	.word	11228
	.byte	25
	.byte	'__intptr_t',0,8,1,1
	.word	454
	.byte	25
	.byte	'__uintptr_t',0,8,1,1
	.word	438
	.byte	25
	.byte	'_iob_flag_t',0,9,82,25
	.word	478
	.byte	25
	.byte	'boolean',0,10,101,29
	.word	461
	.byte	25
	.byte	'uint8',0,10,105,29
	.word	461
	.byte	25
	.byte	'uint16',0,10,109,29
	.word	478
	.byte	25
	.byte	'uint32',0,10,113,29
	.word	10844
	.byte	25
	.byte	'uint64',0,10,118,29
	.word	320
	.byte	25
	.byte	'sint16',0,10,126,29
	.word	11158
	.byte	7
	.byte	'long int',0,4,5,25
	.byte	'sint32',0,10,131,1,29
	.word	11400
	.byte	7
	.byte	'long long int',0,8,5,25
	.byte	'sint64',0,10,138,1,29
	.word	11428
	.byte	25
	.byte	'float32',0,10,167,1,29
	.word	266
	.byte	25
	.byte	'pvoid',0,11,57,28
	.word	352
	.byte	25
	.byte	'Ifx_TickTime',0,11,79,28
	.word	11428
	.byte	7
	.byte	'char',0,1,6,25
	.byte	'int8',0,12,54,29
	.word	11513
	.byte	25
	.byte	'int16',0,12,55,29
	.word	11158
	.byte	25
	.byte	'int32',0,12,56,29
	.word	454
	.byte	25
	.byte	'int64',0,12,57,29
	.word	11428
	.byte	25
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7078
	.byte	25
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6991
	.byte	25
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3334
	.byte	25
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1387
	.byte	25
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2382
	.byte	25
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1515
	.byte	25
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2162
	.byte	25
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1730
	.byte	25
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1945
	.byte	25
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6350
	.byte	25
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6474
	.byte	25
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6558
	.byte	25
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6738
	.byte	25
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4989
	.byte	25
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5513
	.byte	25
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5163
	.byte	25
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5337
	.byte	25
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6002
	.byte	25
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	816
	.byte	25
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4326
	.byte	25
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4814
	.byte	25
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4473
	.byte	25
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4642
	.byte	25
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5669
	.byte	25
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	500
	.byte	25
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4040
	.byte	25
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3674
	.byte	25
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2705
	.byte	25
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3009
	.byte	25
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7605
	.byte	25
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7038
	.byte	25
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3625
	.byte	25
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1466
	.byte	25
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2656
	.byte	25
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1690
	.byte	25
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2342
	.byte	25
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1905
	.byte	25
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2122
	.byte	25
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6434
	.byte	25
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6683
	.byte	25
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6942
	.byte	25
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6310
	.byte	25
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5123
	.byte	25
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5629
	.byte	25
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5297
	.byte	25
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5473
	.byte	25
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1347
	.byte	25
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5962
	.byte	25
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4433
	.byte	25
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4949
	.byte	25
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4602
	.byte	25
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4774
	.byte	25
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	776
	.byte	25
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4286
	.byte	25
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4000
	.byte	25
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2969
	.byte	25
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3285
	.byte	16
	.word	7645
	.byte	25
	.byte	'Ifx_P',0,4,139,6,3
	.word	12894
	.byte	17,13,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,25
	.byte	'IfxScu_WDTCON1_IR',0,13,255,10,3
	.word	12914
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_ACCEN0_Bits',0,14,79,3
	.word	13036
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1_Bits',0,14,85,3
	.word	13593
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,14,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,14,94,3
	.word	13670
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,14,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON0_Bits',0,14,111,3
	.word	13806
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,14,114,16,4,11
	.byte	'CANDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	461
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON1_Bits',0,14,126,3
	.word	14086
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,14,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON2_Bits',0,14,135,1,3
	.word	14324
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,14,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON3_Bits',0,14,150,1,3
	.word	14452
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,14,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON4_Bits',0,14,165,1,3
	.word	14695
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,14,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON5_Bits',0,14,174,1,3
	.word	14930
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,14,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6_Bits',0,14,181,1,3
	.word	15058
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,14,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7_Bits',0,14,188,1,3
	.word	15158
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,14,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	461
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CHIPID_Bits',0,14,202,1,3
	.word	15258
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,14,205,1,16,4,11
	.byte	'PWD',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	438
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSCON_Bits',0,14,213,1,3
	.word	15466
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,14,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSLIM_Bits',0,14,225,1,3
	.word	15631
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,14,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,14,235,1,3
	.word	15814
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,14,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	438
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EICR_Bits',0,14,129,2,3
	.word	15968
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,14,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR_Bits',0,14,143,2,3
	.word	16332
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,14,146,2,16,4,11
	.byte	'POL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	478
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_EMSR_Bits',0,14,159,2,3
	.word	16543
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,14,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG_Bits',0,14,167,2,3
	.word	16795
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,14,170,2,16,4,11
	.byte	'ARI',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG_Bits',0,14,175,2,3
	.word	16913
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,14,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR13CON_Bits',0,14,185,2,3
	.word	17024
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,14,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR33CON_Bits',0,14,195,2,3
	.word	17187
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,14,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,14,205,2,3
	.word	17350
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,14,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,14,215,2,3
	.word	17508
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,14,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	461
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	478
	.byte	10,0,2,35,2,0,25
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,14,232,2,3
	.word	17673
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,14,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	461
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	478
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,14,245,2,3
	.word	18002
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,14,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROVMON_Bits',0,14,255,2,3
	.word	18223
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,14,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,14,142,3,3
	.word	18386
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,14,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,14,152,3,3
	.word	18658
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,14,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,14,162,3,3
	.word	18811
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,14,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,14,172,3,3
	.word	18967
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,14,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,14,181,3,3
	.word	19129
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,14,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,14,191,3,3
	.word	19272
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,14,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,14,200,3,3
	.word	19437
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,14,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,14,211,3,3
	.word	19582
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,14,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	461
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,14,222,3,3
	.word	19763
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,14,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,14,232,3,3
	.word	19937
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,14,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,14,241,3,3
	.word	20097
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,14,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,14,130,4,3
	.word	20241
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,14,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,14,139,4,3
	.word	20515
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,14,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,14,149,4,3
	.word	20654
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,14,152,4,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	461
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	478
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	461
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	461
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_EXTCON_Bits',0,14,163,4,3
	.word	20817
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,14,166,4,16,4,11
	.byte	'STEP',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_FDR_Bits',0,14,174,4,3
	.word	21035
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,14,177,4,16,4,11
	.byte	'FS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_FMR_Bits',0,14,197,4,3
	.word	21198
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,14,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_ID_Bits',0,14,205,4,3
	.word	21534
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,14,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	461
	.byte	2,0,2,35,3,0,25
	.byte	'Ifx_SCU_IGCR_Bits',0,14,232,4,3
	.word	21641
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,14,235,4,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_IN_Bits',0,14,240,4,3
	.word	22093
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,14,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_IOCR_Bits',0,14,250,4,3
	.word	22192
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,14,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,14,131,5,3
	.word	22342
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,14,134,5,16,4,11
	.byte	'SEED',0,4
	.word	438
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,14,141,5,3
	.word	22491
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,14,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,14,149,5,3
	.word	22652
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,14,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	478
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LCLCON_Bits',0,14,158,5,3
	.word	22782
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,14,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST_Bits',0,14,166,5,3
	.word	22914
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,14,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	461
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	478
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_MANID_Bits',0,14,174,5,3
	.word	23029
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,14,177,5,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_OMR_Bits',0,14,185,5,3
	.word	23140
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,14,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	461
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	461
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_OSCCON_Bits',0,14,209,5,3
	.word	23298
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,14,212,5,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_OUT_Bits',0,14,217,5,3
	.word	23710
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,14,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	478
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	6,0,2,35,3,0,25
	.byte	'Ifx_SCU_OVCCON_Bits',0,14,233,5,3
	.word	23811
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,14,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,14,242,5,3
	.word	24078
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,14,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC_Bits',0,14,250,5,3
	.word	24214
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,14,253,5,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDR_Bits',0,14,132,6,3
	.word	24325
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,14,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR_Bits',0,14,146,6,3
	.word	24458
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,14,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLCON0_Bits',0,14,166,6,3
	.word	24661
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,14,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON1_Bits',0,14,177,6,3
	.word	25017
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,14,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON2_Bits',0,14,184,6,3
	.word	25195
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,14,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,14,204,6,3
	.word	25295
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,14,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,14,215,6,3
	.word	25665
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,14,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,14,227,6,3
	.word	25851
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,14,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,14,241,6,3
	.word	26049
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,14,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR_Bits',0,14,251,6,3
	.word	26282
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,14,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	461
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	461
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,14,153,7,3
	.word	26434
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,14,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	461
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,14,170,7,3
	.word	27001
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,14,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	461
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,14,187,7,3
	.word	27295
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,14,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	461
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	461
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,14,214,7,3
	.word	27573
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,14,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,14,230,7,3
	.word	28069
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,14,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON2_Bits',0,14,243,7,3
	.word	28382
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,14,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON_Bits',0,14,129,8,3
	.word	28591
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,14,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	3,0,2,35,3,0,25
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,14,155,8,3
	.word	28802
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,14,158,8,16,4,11
	.byte	'HBT',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	438
	.byte	31,0,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON_Bits',0,14,162,8,3
	.word	29234
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,14,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	461
	.byte	7,0,2,35,3,0,25
	.byte	'Ifx_SCU_STSTAT_Bits',0,14,178,8,3
	.word	29330
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,14,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,14,186,8,3
	.word	29590
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,14,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	461
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON_Bits',0,14,198,8,3
	.word	29715
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,14,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,14,208,8,3
	.word	29912
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,14,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,14,218,8,3
	.word	30065
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,14,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET_Bits',0,14,228,8,3
	.word	30218
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,14,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,14,238,8,3
	.word	30371
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,14,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	30526
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30526
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30526
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30526
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,14,247,8,3
	.word	30542
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,14,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,14,134,9,3
	.word	30672
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,14,137,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,14,150,9,3
	.word	30910
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,14,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	30526
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30526
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30526
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30526
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,14,159,9,3
	.word	31133
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,14,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,14,175,9,3
	.word	31259
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,14,178,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,14,191,9,3
	.word	31511
	.byte	12,14,199,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13036
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN0',0,14,204,9,3
	.word	31730
	.byte	12,14,207,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13593
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1',0,14,212,9,3
	.word	31794
	.byte	12,14,215,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13670
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS',0,14,220,9,3
	.word	31858
	.byte	12,14,223,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13806
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON0',0,14,228,9,3
	.word	31923
	.byte	12,14,231,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14086
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON1',0,14,236,9,3
	.word	31988
	.byte	12,14,239,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14324
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON2',0,14,244,9,3
	.word	32053
	.byte	12,14,247,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14452
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON3',0,14,252,9,3
	.word	32118
	.byte	12,14,255,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14695
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON4',0,14,132,10,3
	.word	32183
	.byte	12,14,135,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14930
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON5',0,14,140,10,3
	.word	32248
	.byte	12,14,143,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15058
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6',0,14,148,10,3
	.word	32313
	.byte	12,14,151,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15158
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7',0,14,156,10,3
	.word	32378
	.byte	12,14,159,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15258
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CHIPID',0,14,164,10,3
	.word	32443
	.byte	12,14,167,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15466
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSCON',0,14,172,10,3
	.word	32507
	.byte	12,14,175,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15631
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSLIM',0,14,180,10,3
	.word	32571
	.byte	12,14,183,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15814
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSSTAT',0,14,188,10,3
	.word	32635
	.byte	12,14,191,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15968
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EICR',0,14,196,10,3
	.word	32700
	.byte	12,14,199,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16332
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR',0,14,204,10,3
	.word	32762
	.byte	12,14,207,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16543
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EMSR',0,14,212,10,3
	.word	32824
	.byte	12,14,215,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16795
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG',0,14,220,10,3
	.word	32886
	.byte	12,14,223,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16913
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG',0,14,228,10,3
	.word	32950
	.byte	12,14,231,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17024
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR13CON',0,14,236,10,3
	.word	33015
	.byte	12,14,239,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17187
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR33CON',0,14,244,10,3
	.word	33081
	.byte	12,14,247,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17350
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRADCSTAT',0,14,252,10,3
	.word	33147
	.byte	12,14,255,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17508
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRDVSTAT',0,14,132,11,3
	.word	33215
	.byte	12,14,135,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17673
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRMONCTRL',0,14,140,11,3
	.word	33282
	.byte	12,14,143,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18002
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROSCCTRL',0,14,148,11,3
	.word	33350
	.byte	12,14,151,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18223
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROVMON',0,14,156,11,3
	.word	33418
	.byte	12,14,159,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18386
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRRSTCON',0,14,164,11,3
	.word	33484
	.byte	12,14,167,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18658
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,14,172,11,3
	.word	33551
	.byte	12,14,175,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18811
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,14,180,11,3
	.word	33620
	.byte	12,14,183,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18967
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,14,188,11,3
	.word	33689
	.byte	12,14,191,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19129
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,14,196,11,3
	.word	33758
	.byte	12,14,199,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19272
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,14,204,11,3
	.word	33827
	.byte	12,14,207,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19437
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,14,212,11,3
	.word	33896
	.byte	12,14,215,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19582
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1',0,14,220,11,3
	.word	33965
	.byte	12,14,223,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19763
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2',0,14,228,11,3
	.word	34033
	.byte	12,14,231,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19937
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3',0,14,236,11,3
	.word	34101
	.byte	12,14,239,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20097
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4',0,14,244,11,3
	.word	34169
	.byte	12,14,247,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20241
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT',0,14,252,11,3
	.word	34237
	.byte	12,14,255,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20515
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRTRIM',0,14,132,12,3
	.word	34302
	.byte	12,14,135,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20654
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRUVMON',0,14,140,12,3
	.word	34367
	.byte	12,14,143,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20817
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EXTCON',0,14,148,12,3
	.word	34433
	.byte	12,14,151,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21035
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FDR',0,14,156,12,3
	.word	34497
	.byte	12,14,159,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21198
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FMR',0,14,164,12,3
	.word	34558
	.byte	12,14,167,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21534
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ID',0,14,172,12,3
	.word	34619
	.byte	12,14,175,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21641
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IGCR',0,14,180,12,3
	.word	34679
	.byte	12,14,183,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22093
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IN',0,14,188,12,3
	.word	34741
	.byte	12,14,191,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22192
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IOCR',0,14,196,12,3
	.word	34801
	.byte	12,14,199,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22342
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL0',0,14,204,12,3
	.word	34863
	.byte	12,14,207,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22491
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL1',0,14,212,12,3
	.word	34931
	.byte	12,14,215,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22652
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL2',0,14,220,12,3
	.word	34999
	.byte	12,14,223,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22782
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLCON',0,14,228,12,3
	.word	35067
	.byte	12,14,231,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22914
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST',0,14,236,12,3
	.word	35131
	.byte	12,14,239,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23029
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_MANID',0,14,244,12,3
	.word	35196
	.byte	12,14,247,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23140
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OMR',0,14,252,12,3
	.word	35259
	.byte	12,14,255,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23298
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OSCCON',0,14,132,13,3
	.word	35320
	.byte	12,14,135,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23710
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OUT',0,14,140,13,3
	.word	35384
	.byte	12,14,143,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23811
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCCON',0,14,148,13,3
	.word	35445
	.byte	12,14,151,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24078
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE',0,14,156,13,3
	.word	35509
	.byte	12,14,159,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24214
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC',0,14,164,13,3
	.word	35576
	.byte	12,14,167,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24325
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDR',0,14,172,13,3
	.word	35639
	.byte	12,14,175,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24458
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR',0,14,180,13,3
	.word	35700
	.byte	12,14,183,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24661
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON0',0,14,188,13,3
	.word	35762
	.byte	12,14,191,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25017
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON1',0,14,196,13,3
	.word	35827
	.byte	12,14,199,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25195
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON2',0,14,204,13,3
	.word	35892
	.byte	12,14,207,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25295
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON0',0,14,212,13,3
	.word	35957
	.byte	12,14,215,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25665
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON1',0,14,220,13,3
	.word	36026
	.byte	12,14,223,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25851
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT',0,14,228,13,3
	.word	36095
	.byte	12,14,231,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26049
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT',0,14,236,13,3
	.word	36164
	.byte	12,14,239,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26282
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR',0,14,244,13,3
	.word	36229
	.byte	12,14,247,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26434
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR0',0,14,252,13,3
	.word	36292
	.byte	12,14,255,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27001
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR1',0,14,132,14,3
	.word	36357
	.byte	12,14,135,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27295
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR2',0,14,140,14,3
	.word	36422
	.byte	12,14,143,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27573
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTAT',0,14,148,14,3
	.word	36487
	.byte	12,14,151,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28069
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR',0,14,156,14,3
	.word	36553
	.byte	12,14,159,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28591
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON',0,14,164,14,3
	.word	36622
	.byte	12,14,167,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28382
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON2',0,14,172,14,3
	.word	36686
	.byte	12,14,175,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28802
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTSTAT',0,14,180,14,3
	.word	36751
	.byte	12,14,183,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29234
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON',0,14,188,14,3
	.word	36816
	.byte	12,14,191,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29330
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_STSTAT',0,14,196,14,3
	.word	36881
	.byte	12,14,199,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29590
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON',0,14,204,14,3
	.word	36945
	.byte	12,14,207,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29715
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON',0,14,212,14,3
	.word	37011
	.byte	12,14,215,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29912
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR',0,14,220,14,3
	.word	37075
	.byte	12,14,223,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30065
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS',0,14,228,14,3
	.word	37140
	.byte	12,14,231,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30218
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET',0,14,236,14,3
	.word	37205
	.byte	12,14,239,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30371
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT',0,14,244,14,3
	.word	37270
	.byte	12,14,247,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30542
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0',0,14,252,14,3
	.word	37336
	.byte	12,14,255,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30672
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1',0,14,132,15,3
	.word	37405
	.byte	12,14,135,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30910
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_SR',0,14,140,15,3
	.word	37474
	.byte	12,14,143,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31133
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0',0,14,148,15,3
	.word	37541
	.byte	12,14,151,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31259
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON1',0,14,156,15,3
	.word	37608
	.byte	12,14,159,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31511
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_SR',0,14,164,15,3
	.word	37675
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,14,175,15,25,12,13
	.byte	'CON0',0
	.word	37336
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37405
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37474
	.byte	4,2,35,8,0,16
	.word	37740
	.byte	25
	.byte	'Ifx_SCU_WDTCPU',0,14,180,15,3
	.word	37803
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,14,183,15,25,12,13
	.byte	'CON0',0
	.word	37541
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37608
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37675
	.byte	4,2,35,8,0,16
	.word	37832
	.byte	25
	.byte	'Ifx_SCU_WDTS',0,14,188,15,3
	.word	37893
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,25
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	37920
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,25
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	38071
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,25
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	38315
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,25
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	38413
	.byte	25
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8258
	.byte	25
	.byte	'gpio_pin_enum',0,6,89,2
	.word	8596
	.byte	25
	.byte	'gpio_dir_enum',0,6,95,2
	.word	10663
	.byte	25
	.byte	'gpio_mode_enum',0,6,111,2
	.word	10681
	.byte	25
	.byte	'soft_iic_info_struct',0,7,50,2
	.word	10865
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,20
	.byte	46,1,49,19,0,0,21,5,0,49,19,0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,23,19
	.byte	1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,25,22,0,3,8,58,15,59,15,57,15,73,19,0,0,26,21,0,54,15
	.byte	0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L134:
	.word	.L804-.L803
.L803:
	.half	3
	.word	.L806-.L805
.L805:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_gpio.h',0,0,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_soft_iic.h',0,0,0,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'Platform_Types.h',0,5,0,0
	.byte	'ifx_types.h',0,5,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0,0
.L806:
.L804:
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_8bit')
	.sect	'.debug_info'
.L135:
	.word	306
	.half	3
	.word	.L136
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L138,.L137
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_8bit',0,1,229,1,6,1,1,1
	.word	.L90,.L270,.L89
	.byte	4
	.byte	'soft_iic_obj',0,1,229,1,49
	.word	.L271,.L272
	.byte	4
	.byte	'data',0,1,229,1,75
	.word	.L273,.L274
	.byte	5
	.word	.L90,.L270
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_8bit')
	.sect	'.debug_abbrev'
.L136:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_8bit')
	.sect	'.debug_line'
.L137:
	.word	.L808-.L807
.L807:
	.half	3
	.word	.L810-.L809
.L809:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L810:
	.byte	5,6,7,0,5,2
	.word	.L90
	.byte	3,228,1,1,5,20,9
	.half	.L494-.L90
	.byte	3,2,1,5,50,9
	.half	.L492-.L494
	.byte	3,1,1,5,57,9
	.half	.L811-.L492
	.byte	1,5,38,9
	.half	.L496-.L811
	.byte	3,1,1,5,19,9
	.half	.L498-.L496
	.byte	3,1,1,5,1,9
	.half	.L501-.L498
	.byte	3,1,1,7,9
	.half	.L139-.L501
	.byte	0,1,1
.L808:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_8bit')
	.sect	'.debug_ranges'
.L138:
	.word	-1,.L90,0,.L139-.L90,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_8bit_array')
	.sect	'.debug_info'
.L140:
	.word	329
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L143,.L142
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_8bit_array',0,1,246,1,6,1,1,1
	.word	.L92,.L275,.L91
	.byte	4
	.byte	'soft_iic_obj',0,1,246,1,55
	.word	.L271,.L276
	.byte	4
	.byte	'data',0,1,246,1,82
	.word	.L277,.L278
	.byte	4
	.byte	'len',0,1,246,1,95
	.word	.L279,.L280
	.byte	5
	.word	.L92,.L275
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_8bit_array')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_8bit_array')
	.sect	'.debug_line'
.L142:
	.word	.L813-.L812
.L812:
	.half	3
	.word	.L815-.L814
.L814:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L815:
	.byte	5,6,7,0,5,2
	.word	.L92
	.byte	3,245,1,1,5,20,9
	.half	.L505-.L92
	.byte	3,2,1,5,50,9
	.half	.L502-.L505
	.byte	3,1,1,5,57,9
	.half	.L816-.L502
	.byte	1,5,17,9
	.half	.L507-.L816
	.byte	3,1,1,5,42,9
	.half	.L47-.L507
	.byte	3,2,1,5,48,9
	.half	.L817-.L47
	.byte	1,5,15,9
	.half	.L46-.L817
	.byte	3,126,1,5,17,9
	.half	.L818-.L46
	.byte	1,5,19,7,9
	.half	.L819-.L818
	.byte	3,4,1,5,1,9
	.half	.L510-.L819
	.byte	3,1,1,7,9
	.half	.L144-.L510
	.byte	0,1,1
.L813:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_8bit_array')
	.sect	'.debug_ranges'
.L143:
	.word	-1,.L92,0,.L144-.L92,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_16bit')
	.sect	'.debug_info'
.L145:
	.word	307
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L148,.L147
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_16bit',0,1,137,2,6,1,1,1
	.word	.L94,.L281,.L93
	.byte	4
	.byte	'soft_iic_obj',0,1,137,2,50
	.word	.L271,.L282
	.byte	4
	.byte	'data',0,1,137,2,77
	.word	.L283,.L284
	.byte	5
	.word	.L94,.L281
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_16bit')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_16bit')
	.sect	'.debug_line'
.L147:
	.word	.L821-.L820
.L820:
	.half	3
	.word	.L823-.L822
.L822:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L823:
	.byte	5,6,7,0,5,2
	.word	.L94
	.byte	3,136,2,1,5,20,9
	.half	.L513-.L94
	.byte	3,2,1,5,50,9
	.half	.L511-.L513
	.byte	3,1,1,5,57,9
	.half	.L824-.L511
	.byte	1,5,54,9
	.half	.L515-.L824
	.byte	3,1,1,5,52,9
	.half	.L825-.L515
	.byte	1,5,62,9
	.half	.L826-.L825
	.byte	1,5,38,9
	.half	.L827-.L826
	.byte	1,5,51,9
	.half	.L517-.L827
	.byte	3,1,1,5,38,9
	.half	.L828-.L517
	.byte	1,5,19,9
	.half	.L519-.L828
	.byte	3,1,1,5,1,9
	.half	.L521-.L519
	.byte	3,1,1,7,9
	.half	.L149-.L521
	.byte	0,1,1
.L821:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_16bit')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L94,0,.L149-.L94,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_16bit_array')
	.sect	'.debug_info'
.L150:
	.word	330
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L153,.L152
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_16bit_array',0,1,155,2,6,1,1,1
	.word	.L96,.L285,.L95
	.byte	4
	.byte	'soft_iic_obj',0,1,155,2,56
	.word	.L271,.L286
	.byte	4
	.byte	'data',0,1,155,2,84
	.word	.L287,.L288
	.byte	4
	.byte	'len',0,1,155,2,97
	.word	.L279,.L289
	.byte	5
	.word	.L96,.L285
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_16bit_array')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_16bit_array')
	.sect	'.debug_line'
.L152:
	.word	.L830-.L829
.L829:
	.half	3
	.word	.L832-.L831
.L831:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L832:
	.byte	5,6,7,0,5,2
	.word	.L96
	.byte	3,154,2,1,5,20,9
	.half	.L525-.L96
	.byte	3,2,1,5,50,9
	.half	.L522-.L525
	.byte	3,1,1,5,57,9
	.half	.L833-.L522
	.byte	1,5,17,9
	.half	.L527-.L833
	.byte	3,1,1,5,51,9
	.half	.L49-.L527
	.byte	3,2,1,5,59,9
	.half	.L834-.L49
	.byte	1,5,57,9
	.half	.L835-.L834
	.byte	1,5,67,9
	.half	.L836-.L835
	.byte	1,5,42,9
	.half	.L837-.L836
	.byte	1,5,50,9
	.half	.L529-.L837
	.byte	3,1,1,5,59,9
	.half	.L838-.L529
	.byte	1,5,56,9
	.half	.L839-.L838
	.byte	1,5,15,9
	.half	.L48-.L839
	.byte	3,125,1,5,17,9
	.half	.L840-.L48
	.byte	1,5,19,7,9
	.half	.L841-.L840
	.byte	3,5,1,5,1,9
	.half	.L532-.L841
	.byte	3,1,1,7,9
	.half	.L154-.L532
	.byte	0,1,1
.L830:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_16bit_array')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L96,0,.L154-.L96,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_8bit_register')
	.sect	'.debug_info'
.L155:
	.word	342
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L158,.L157
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_8bit_register',0,1,176,2,6,1,1,1
	.word	.L98,.L290,.L97
	.byte	4
	.byte	'soft_iic_obj',0,1,176,2,58
	.word	.L271,.L291
	.byte	4
	.byte	'register_name',0,1,176,2,84
	.word	.L292,.L293
	.byte	4
	.byte	'data',0,1,176,2,111
	.word	.L294,.L295
	.byte	5
	.word	.L98,.L290
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_8bit_register')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_8bit_register')
	.sect	'.debug_line'
.L157:
	.word	.L843-.L842
.L842:
	.half	3
	.word	.L845-.L844
.L844:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L845:
	.byte	5,6,7,0,5,2
	.word	.L98
	.byte	3,175,2,1,5,20,9
	.half	.L846-.L98
	.byte	3,2,1,5,50,9
	.half	.L533-.L846
	.byte	3,1,1,5,57,9
	.half	.L847-.L533
	.byte	1,5,38,9
	.half	.L536-.L847
	.byte	3,1,1,9
	.half	.L539-.L536
	.byte	3,1,1,5,19,9
	.half	.L542-.L539
	.byte	3,1,1,5,1,9
	.half	.L544-.L542
	.byte	3,1,1,7,9
	.half	.L159-.L544
	.byte	0,1,1
.L843:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_8bit_register')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L98,0,.L159-.L98,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_8bit_registers')
	.sect	'.debug_info'
.L160:
	.word	360
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L163,.L162
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_8bit_registers',0,1,195,2,6,1,1,1
	.word	.L100,.L296,.L99
	.byte	4
	.byte	'soft_iic_obj',0,1,195,2,59
	.word	.L271,.L297
	.byte	4
	.byte	'register_name',0,1,195,2,85
	.word	.L298,.L299
	.byte	4
	.byte	'data',0,1,195,2,113
	.word	.L277,.L300
	.byte	4
	.byte	'len',0,1,195,2,126
	.word	.L279,.L301
	.byte	5
	.word	.L100,.L296
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_8bit_registers')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_8bit_registers')
	.sect	'.debug_line'
.L162:
	.word	.L849-.L848
.L848:
	.half	3
	.word	.L851-.L850
.L850:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L851:
	.byte	5,6,7,0,5,2
	.word	.L100
	.byte	3,194,2,1,5,20,9
	.half	.L549-.L100
	.byte	3,2,1,5,50,9
	.half	.L545-.L549
	.byte	3,1,1,5,57,9
	.half	.L852-.L545
	.byte	1,5,38,9
	.half	.L551-.L852
	.byte	3,1,1,5,17,9
	.half	.L553-.L551
	.byte	3,1,1,5,42,9
	.half	.L51-.L553
	.byte	3,2,1,5,48,9
	.half	.L853-.L51
	.byte	1,5,15,9
	.half	.L50-.L853
	.byte	3,126,1,5,17,9
	.half	.L854-.L50
	.byte	1,5,19,7,9
	.half	.L855-.L854
	.byte	3,4,1,5,1,9
	.half	.L557-.L855
	.byte	3,1,1,7,9
	.half	.L164-.L557
	.byte	0,1,1
.L849:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_8bit_registers')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L100,0,.L164-.L100,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_16bit_register')
	.sect	'.debug_info'
.L165:
	.word	343
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L168,.L167
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_16bit_register',0,1,216,2,6,1,1,1
	.word	.L102,.L302,.L101
	.byte	4
	.byte	'soft_iic_obj',0,1,216,2,59
	.word	.L271,.L303
	.byte	4
	.byte	'register_name',0,1,216,2,86
	.word	.L304,.L305
	.byte	4
	.byte	'data',0,1,216,2,114
	.word	.L306,.L307
	.byte	5
	.word	.L102,.L302
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_16bit_register')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_16bit_register')
	.sect	'.debug_line'
.L167:
	.word	.L857-.L856
.L856:
	.half	3
	.word	.L859-.L858
.L858:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L859:
	.byte	5,6,7,0,5,2
	.word	.L102
	.byte	3,215,2,1,5,20,9
	.half	.L860-.L102
	.byte	3,2,1,5,50,9
	.half	.L558-.L860
	.byte	3,1,1,5,57,9
	.half	.L861-.L558
	.byte	1,5,63,9
	.half	.L561-.L861
	.byte	3,1,1,5,61,9
	.half	.L562-.L561
	.byte	1,5,71,9
	.half	.L563-.L562
	.byte	1,5,38,9
	.half	.L862-.L563
	.byte	1,5,60,9
	.half	.L565-.L862
	.byte	3,1,1,5,38,9
	.half	.L566-.L565
	.byte	1,5,54,9
	.half	.L568-.L566
	.byte	3,1,1,5,52,9
	.half	.L569-.L568
	.byte	1,5,62,9
	.half	.L570-.L569
	.byte	1,5,38,9
	.half	.L863-.L570
	.byte	1,5,51,9
	.half	.L572-.L863
	.byte	3,1,1,5,38,9
	.half	.L573-.L572
	.byte	1,5,19,9
	.half	.L575-.L573
	.byte	3,1,1,5,1,9
	.half	.L577-.L575
	.byte	3,1,1,7,9
	.half	.L169-.L577
	.byte	0,1,1
.L857:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_16bit_register')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L102,0,.L169-.L102,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_write_16bit_registers')
	.sect	'.debug_info'
.L170:
	.word	362
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L173,.L172
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_write_16bit_registers',0,1,237,2,6,1,1,1
	.word	.L104,.L308,.L103
	.byte	4
	.byte	'soft_iic_obj',0,1,237,2,60
	.word	.L271,.L309
	.byte	4
	.byte	'register_name',0,1,237,2,87
	.word	.L310,.L311
	.byte	4
	.byte	'data',0,1,237,2,116
	.word	.L287,.L312
	.byte	4
	.byte	'len',0,1,237,2,129,1
	.word	.L279,.L313
	.byte	5
	.word	.L104,.L308
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_write_16bit_registers')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_write_16bit_registers')
	.sect	'.debug_line'
.L172:
	.word	.L865-.L864
.L864:
	.half	3
	.word	.L867-.L866
.L866:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L867:
	.byte	5,6,7,0,5,2
	.word	.L104
	.byte	3,236,2,1,5,20,9
	.half	.L582-.L104
	.byte	3,2,1,5,50,9
	.half	.L578-.L582
	.byte	3,1,1,5,57,9
	.half	.L868-.L578
	.byte	1,5,63,9
	.half	.L584-.L868
	.byte	3,1,1,5,61,9
	.half	.L869-.L584
	.byte	1,5,71,9
	.half	.L870-.L869
	.byte	1,5,38,9
	.half	.L871-.L870
	.byte	1,5,60,9
	.half	.L586-.L871
	.byte	3,1,1,5,38,9
	.half	.L872-.L586
	.byte	1,5,16,9
	.half	.L588-.L872
	.byte	3,1,1,5,51,9
	.half	.L53-.L588
	.byte	3,2,1,5,59,9
	.half	.L873-.L53
	.byte	1,5,57,9
	.half	.L874-.L873
	.byte	1,5,67,9
	.half	.L875-.L874
	.byte	1,5,42,9
	.half	.L876-.L875
	.byte	1,5,50,9
	.half	.L590-.L876
	.byte	3,1,1,5,59,9
	.half	.L877-.L590
	.byte	1,5,56,9
	.half	.L878-.L877
	.byte	1,5,14,9
	.half	.L52-.L878
	.byte	3,125,1,5,16,9
	.half	.L879-.L52
	.byte	1,5,19,7,9
	.half	.L880-.L879
	.byte	3,5,1,5,1,9
	.half	.L593-.L880
	.byte	3,1,1,7,9
	.half	.L174-.L593
	.byte	0,1,1
.L865:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_write_16bit_registers')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L104,0,.L174-.L104,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_8bit')
	.sect	'.debug_info'
.L175:
	.word	310
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L178,.L177
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_8bit',0,1,130,3,7
	.word	.L314
	.byte	1,1,1
	.word	.L106,.L315,.L105
	.byte	4
	.byte	'soft_iic_obj',0,1,130,3,49
	.word	.L271,.L316
	.byte	5
	.word	.L106,.L315
	.byte	6
	.byte	'temp',0,1,132,3,11
	.word	.L314,.L317
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_8bit')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_8bit')
	.sect	'.debug_line'
.L177:
	.word	.L882-.L881
.L881:
	.half	3
	.word	.L884-.L883
.L883:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L884:
	.byte	5,7,7,0,5,2
	.word	.L106
	.byte	3,129,3,1,5,20,9
	.half	.L595-.L106
	.byte	3,3,1,5,50,9
	.half	.L594-.L595
	.byte	3,1,1,5,57,9
	.half	.L885-.L594
	.byte	1,5,62,9
	.half	.L886-.L885
	.byte	1,5,45,9
	.half	.L597-.L886
	.byte	3,1,1,5,10,9
	.half	.L599-.L597
	.byte	1,5,19,9
	.half	.L601-.L599
	.byte	3,1,1,5,5,9
	.half	.L600-.L601
	.byte	3,1,1,5,1,9
	.half	.L54-.L600
	.byte	3,1,1,7,9
	.half	.L179-.L54
	.byte	0,1,1
.L882:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_8bit')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L106,0,.L179-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_8bit_array')
	.sect	'.debug_info'
.L180:
	.word	328
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L183,.L182
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_8bit_array',0,1,150,3,6,1,1,1
	.word	.L108,.L318,.L107
	.byte	4
	.byte	'soft_iic_obj',0,1,150,3,54
	.word	.L271,.L319
	.byte	4
	.byte	'data',0,1,150,3,75
	.word	.L320,.L321
	.byte	4
	.byte	'len',0,1,150,3,88
	.word	.L279,.L322
	.byte	5
	.word	.L108,.L318
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_8bit_array')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_8bit_array')
	.sect	'.debug_line'
.L182:
	.word	.L888-.L887
.L887:
	.half	3
	.word	.L890-.L889
.L889:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L890:
	.byte	5,6,7,0,5,2
	.word	.L108
	.byte	3,149,3,1,5,20,9
	.half	.L607-.L108
	.byte	3,2,1,5,50,9
	.half	.L604-.L607
	.byte	3,1,1,5,57,9
	.half	.L891-.L604
	.byte	1,5,62,9
	.half	.L892-.L891
	.byte	1,5,17,9
	.half	.L609-.L892
	.byte	3,1,1,5,57,9
	.half	.L56-.L609
	.byte	3,2,1,5,18,9
	.half	.L611-.L56
	.byte	1,5,15,9
	.half	.L893-.L611
	.byte	1,9
	.half	.L55-.L893
	.byte	3,126,1,5,17,9
	.half	.L894-.L55
	.byte	1,5,19,7,9
	.half	.L895-.L894
	.byte	3,4,1,5,1,9
	.half	.L613-.L895
	.byte	3,1,1,7,9
	.half	.L184-.L613
	.byte	0,1,1
.L888:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_8bit_array')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L108,0,.L184-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_16bit')
	.sect	'.debug_info'
.L185:
	.word	311
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L188,.L187
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_16bit',0,1,169,3,8
	.word	.L323
	.byte	1,1,1
	.word	.L110,.L324,.L109
	.byte	4
	.byte	'soft_iic_obj',0,1,169,3,51
	.word	.L271,.L325
	.byte	5
	.word	.L110,.L324
	.byte	6
	.byte	'temp',0,1,171,3,12
	.word	.L323,.L326
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_16bit')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_16bit')
	.sect	'.debug_line'
.L187:
	.word	.L897-.L896
.L896:
	.half	3
	.word	.L899-.L898
.L898:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L899:
	.byte	5,8,7,0,5,2
	.word	.L110
	.byte	3,168,3,1,5,20,9
	.half	.L615-.L110
	.byte	3,3,1,5,50,9
	.half	.L614-.L615
	.byte	3,1,1,5,57,9
	.half	.L900-.L614
	.byte	1,5,62,9
	.half	.L901-.L900
	.byte	1,5,45,9
	.half	.L617-.L901
	.byte	3,1,1,5,19,9
	.half	.L619-.L617
	.byte	3,1,1,5,59,9
	.half	.L902-.L619
	.byte	1,5,24,9
	.half	.L620-.L902
	.byte	1,5,19,9
	.half	.L903-.L620
	.byte	3,1,1,5,5,9
	.half	.L623-.L903
	.byte	3,1,1,5,1,9
	.half	.L57-.L623
	.byte	3,1,1,7,9
	.half	.L189-.L57
	.byte	0,1,1
.L897:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_16bit')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L110,0,.L189-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_16bit_array')
	.sect	'.debug_info'
.L190:
	.word	329
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L193,.L192
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_16bit_array',0,1,189,3,6,1,1,1
	.word	.L112,.L327,.L111
	.byte	4
	.byte	'soft_iic_obj',0,1,189,3,55
	.word	.L271,.L328
	.byte	4
	.byte	'data',0,1,189,3,77
	.word	.L329,.L330
	.byte	4
	.byte	'len',0,1,189,3,90
	.word	.L279,.L331
	.byte	5
	.word	.L112,.L327
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_16bit_array')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_16bit_array')
	.sect	'.debug_line'
.L192:
	.word	.L905-.L904
.L904:
	.half	3
	.word	.L907-.L906
.L906:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L907:
	.byte	5,6,7,0,5,2
	.word	.L112
	.byte	3,188,3,1,5,20,9
	.half	.L628-.L112
	.byte	3,2,1,5,50,9
	.half	.L625-.L628
	.byte	3,1,1,5,57,9
	.half	.L908-.L625
	.byte	1,5,62,9
	.half	.L909-.L908
	.byte	1,5,17,9
	.half	.L630-.L909
	.byte	3,1,1,5,50,9
	.half	.L59-.L630
	.byte	3,2,1,5,15,9
	.half	.L632-.L59
	.byte	1,5,19,9
	.half	.L910-.L632
	.byte	3,1,1,5,25,9
	.half	.L911-.L910
	.byte	1,5,69,9
	.half	.L912-.L911
	.byte	1,5,30,9
	.half	.L634-.L912
	.byte	1,5,15,9
	.half	.L913-.L634
	.byte	1,5,14,9
	.half	.L914-.L913
	.byte	3,1,1,5,15,9
	.half	.L58-.L914
	.byte	3,124,1,5,17,9
	.half	.L915-.L58
	.byte	1,5,19,7,9
	.half	.L916-.L915
	.byte	3,6,1,5,1,9
	.half	.L636-.L916
	.byte	3,1,1,7,9
	.half	.L194-.L636
	.byte	0,1,1
.L905:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_16bit_array')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L112,0,.L194-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_8bit_register')
	.sect	'.debug_info'
.L195:
	.word	346
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L198,.L197
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_8bit_register',0,1,210,3,7
	.word	.L314
	.byte	1,1,1
	.word	.L114,.L332,.L113
	.byte	4
	.byte	'soft_iic_obj',0,1,210,3,58
	.word	.L271,.L333
	.byte	4
	.byte	'register_name',0,1,210,3,84
	.word	.L334,.L335
	.byte	5
	.word	.L114,.L332
	.byte	6
	.byte	'temp',0,1,212,3,11
	.word	.L314,.L336
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_8bit_register')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_8bit_register')
	.sect	'.debug_line'
.L197:
	.word	.L918-.L917
.L917:
	.half	3
	.word	.L920-.L919
.L919:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L920:
	.byte	5,7,7,0,5,2
	.word	.L114
	.byte	3,209,3,1,5,20,9
	.half	.L639-.L114
	.byte	3,3,1,5,50,9
	.half	.L637-.L639
	.byte	3,1,1,5,57,9
	.half	.L921-.L637
	.byte	1,5,38,9
	.half	.L641-.L921
	.byte	3,1,1,5,20,9
	.half	.L643-.L641
	.byte	3,1,1,5,50,9
	.half	.L646-.L643
	.byte	3,1,1,5,57,9
	.half	.L922-.L646
	.byte	1,5,62,9
	.half	.L923-.L922
	.byte	1,5,45,9
	.half	.L648-.L923
	.byte	3,1,1,5,10,9
	.half	.L650-.L648
	.byte	1,5,19,9
	.half	.L652-.L650
	.byte	3,1,1,5,5,9
	.half	.L651-.L652
	.byte	3,1,1,5,1,9
	.half	.L60-.L651
	.byte	3,1,1,7,9
	.half	.L199-.L60
	.byte	0,1,1
.L918:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_8bit_register')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L114,0,.L199-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_8bit_registers')
	.sect	'.debug_info'
.L200:
	.word	359
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L203,.L202
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_8bit_registers',0,1,233,3,6,1,1,1
	.word	.L116,.L337,.L115
	.byte	4
	.byte	'soft_iic_obj',0,1,233,3,58
	.word	.L271,.L338
	.byte	4
	.byte	'register_name',0,1,233,3,84
	.word	.L339,.L340
	.byte	4
	.byte	'data',0,1,233,3,106
	.word	.L320,.L341
	.byte	4
	.byte	'len',0,1,233,3,119
	.word	.L279,.L342
	.byte	5
	.word	.L116,.L337
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_8bit_registers')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_8bit_registers')
	.sect	'.debug_line'
.L202:
	.word	.L925-.L924
.L924:
	.half	3
	.word	.L927-.L926
.L926:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L927:
	.byte	5,6,7,0,5,2
	.word	.L116
	.byte	3,232,3,1,5,20,9
	.half	.L659-.L116
	.byte	3,2,1,5,50,9
	.half	.L655-.L659
	.byte	3,1,1,5,57,9
	.half	.L928-.L655
	.byte	1,5,38,9
	.half	.L661-.L928
	.byte	3,1,1,5,20,9
	.half	.L663-.L661
	.byte	3,1,1,5,50,9
	.half	.L666-.L663
	.byte	3,1,1,5,57,9
	.half	.L929-.L666
	.byte	1,5,62,9
	.half	.L930-.L929
	.byte	1,5,17,9
	.half	.L668-.L930
	.byte	3,1,1,5,57,9
	.half	.L62-.L668
	.byte	3,2,1,5,18,9
	.half	.L670-.L62
	.byte	1,5,15,9
	.half	.L931-.L670
	.byte	1,9
	.half	.L61-.L931
	.byte	3,126,1,5,17,9
	.half	.L932-.L61
	.byte	1,5,19,7,9
	.half	.L933-.L932
	.byte	3,4,1,5,1,9
	.half	.L672-.L933
	.byte	3,1,1,7,9
	.half	.L204-.L672
	.byte	0,1,1
.L925:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_8bit_registers')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L116,0,.L204-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_16bit_register')
	.sect	'.debug_info'
.L205:
	.word	347
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L208,.L207
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_16bit_register',0,1,255,3,8
	.word	.L323
	.byte	1,1,1
	.word	.L118,.L343,.L117
	.byte	4
	.byte	'soft_iic_obj',0,1,255,3,60
	.word	.L271,.L344
	.byte	4
	.byte	'register_name',0,1,255,3,87
	.word	.L345,.L346
	.byte	5
	.word	.L118,.L343
	.byte	6
	.byte	'temp',0,1,129,4,12
	.word	.L323,.L347
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_16bit_register')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_16bit_register')
	.sect	'.debug_line'
.L207:
	.word	.L935-.L934
.L934:
	.half	3
	.word	.L937-.L936
.L936:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L937:
	.byte	5,8,7,0,5,2
	.word	.L118
	.byte	3,254,3,1,5,20,9
	.half	.L675-.L118
	.byte	3,3,1,5,50,9
	.half	.L673-.L675
	.byte	3,1,1,5,57,9
	.half	.L938-.L673
	.byte	1,5,63,9
	.half	.L677-.L938
	.byte	3,1,1,5,61,9
	.half	.L939-.L677
	.byte	1,5,71,9
	.half	.L940-.L939
	.byte	1,5,38,9
	.half	.L941-.L940
	.byte	1,5,60,9
	.half	.L679-.L941
	.byte	3,1,1,5,38,9
	.half	.L942-.L679
	.byte	1,5,20,9
	.half	.L681-.L942
	.byte	3,1,1,5,50,9
	.half	.L683-.L681
	.byte	3,1,1,5,57,9
	.half	.L943-.L683
	.byte	1,5,62,9
	.half	.L944-.L943
	.byte	1,5,45,9
	.half	.L685-.L944
	.byte	3,1,1,5,19,9
	.half	.L687-.L685
	.byte	3,1,1,5,59,9
	.half	.L945-.L687
	.byte	1,5,24,9
	.half	.L688-.L945
	.byte	1,5,19,9
	.half	.L946-.L688
	.byte	3,1,1,5,5,9
	.half	.L691-.L946
	.byte	3,1,1,5,1,9
	.half	.L63-.L691
	.byte	3,1,1,7,9
	.half	.L209-.L63
	.byte	0,1,1
.L935:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_16bit_register')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L118,0,.L209-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_16bit_registers')
	.sect	'.debug_info'
.L210:
	.word	360
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L213,.L212
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_16bit_registers',0,1,152,4,6,1,1,1
	.word	.L120,.L348,.L119
	.byte	4
	.byte	'soft_iic_obj',0,1,152,4,59
	.word	.L271,.L349
	.byte	4
	.byte	'register_name',0,1,152,4,86
	.word	.L350,.L351
	.byte	4
	.byte	'data',0,1,152,4,109
	.word	.L329,.L352
	.byte	4
	.byte	'len',0,1,152,4,122
	.word	.L279,.L353
	.byte	5
	.word	.L120,.L348
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_16bit_registers')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_16bit_registers')
	.sect	'.debug_line'
.L212:
	.word	.L948-.L947
.L947:
	.half	3
	.word	.L950-.L949
.L949:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L950:
	.byte	5,6,7,0,5,2
	.word	.L120
	.byte	3,151,4,1,5,20,9
	.half	.L697-.L120
	.byte	3,2,1,5,50,9
	.half	.L693-.L697
	.byte	3,1,1,5,57,9
	.half	.L951-.L693
	.byte	1,5,63,9
	.half	.L699-.L951
	.byte	3,1,1,5,61,9
	.half	.L952-.L699
	.byte	1,5,71,9
	.half	.L953-.L952
	.byte	1,5,38,9
	.half	.L954-.L953
	.byte	1,5,60,9
	.half	.L701-.L954
	.byte	3,1,1,5,38,9
	.half	.L955-.L701
	.byte	1,5,20,9
	.half	.L703-.L955
	.byte	3,1,1,5,50,9
	.half	.L705-.L703
	.byte	3,1,1,5,57,9
	.half	.L956-.L705
	.byte	1,5,62,9
	.half	.L957-.L956
	.byte	1,5,17,9
	.half	.L707-.L957
	.byte	3,1,1,5,50,9
	.half	.L65-.L707
	.byte	3,2,1,5,15,9
	.half	.L709-.L65
	.byte	1,5,19,9
	.half	.L958-.L709
	.byte	3,1,1,5,25,9
	.half	.L959-.L958
	.byte	1,5,69,9
	.half	.L960-.L959
	.byte	1,5,30,9
	.half	.L711-.L960
	.byte	1,5,15,9
	.half	.L961-.L711
	.byte	1,5,14,9
	.half	.L962-.L961
	.byte	3,1,1,5,15,9
	.half	.L64-.L962
	.byte	3,124,1,5,17,9
	.half	.L963-.L64
	.byte	1,5,19,7,9
	.half	.L964-.L963
	.byte	3,6,1,5,1,9
	.half	.L713-.L964
	.byte	3,1,1,7,9
	.half	.L214-.L713
	.byte	0,1,1
.L948:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_16bit_registers')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L120,0,.L214-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_transfer_8bit_array')
	.sect	'.debug_info'
.L215:
	.word	390
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L218,.L217
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_transfer_8bit_array',0,1,180,4,6,1,1,1
	.word	.L122,.L354,.L121
	.byte	4
	.byte	'soft_iic_obj',0,1,180,4,58
	.word	.L271,.L355
	.byte	4
	.byte	'write_data',0,1,180,4,85
	.word	.L277,.L356
	.byte	4
	.byte	'write_len',0,1,180,4,104
	.word	.L279,.L357
	.byte	4
	.byte	'read_data',0,1,180,4,122
	.word	.L320,.L358
	.byte	4
	.byte	'read_len',0,1,180,4,140,1
	.word	.L279,.L359
	.byte	5
	.word	.L122,.L354
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_transfer_8bit_array')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_transfer_8bit_array')
	.sect	'.debug_line'
.L217:
	.word	.L966-.L965
.L965:
	.half	3
	.word	.L968-.L967
.L967:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L968:
	.byte	5,6,7,0,5,2
	.word	.L122
	.byte	3,179,4,1,5,20,9
	.half	.L719-.L122
	.byte	3,2,1,5,50,9
	.half	.L714-.L719
	.byte	3,1,1,5,57,9
	.half	.L969-.L714
	.byte	1,5,23,9
	.half	.L721-.L969
	.byte	3,1,1,5,42,9
	.half	.L67-.L721
	.byte	3,2,1,5,54,9
	.half	.L970-.L67
	.byte	1,5,21,9
	.half	.L66-.L970
	.byte	3,126,1,5,23,9
	.half	.L971-.L66
	.byte	1,5,5,7,9
	.half	.L972-.L971
	.byte	3,4,1,5,24,7,9
	.half	.L973-.L972
	.byte	3,2,1,5,54,9
	.half	.L724-.L973
	.byte	3,1,1,5,61,9
	.half	.L974-.L724
	.byte	1,5,66,9
	.half	.L975-.L974
	.byte	1,5,26,9
	.half	.L726-.L975
	.byte	3,1,1,5,64,9
	.half	.L70-.L726
	.byte	3,2,1,5,27,9
	.half	.L728-.L70
	.byte	1,5,24,9
	.half	.L976-.L728
	.byte	1,9
	.half	.L69-.L976
	.byte	3,126,1,5,26,9
	.half	.L977-.L69
	.byte	1,5,19,7,9
	.half	.L68-.L977
	.byte	3,5,1,5,1,9
	.half	.L730-.L68
	.byte	3,2,1,7,9
	.half	.L219-.L730
	.byte	0,1,1
.L966:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_transfer_8bit_array')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L122,0,.L219-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_transfer_16bit_array')
	.sect	'.debug_info'
.L220:
	.word	391
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L223,.L222
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_transfer_16bit_array',0,1,212,4,6,1,1,1
	.word	.L124,.L360,.L123
	.byte	4
	.byte	'soft_iic_obj',0,1,212,4,59
	.word	.L271,.L361
	.byte	4
	.byte	'write_data',0,1,212,4,87
	.word	.L287,.L362
	.byte	4
	.byte	'write_len',0,1,212,4,106
	.word	.L279,.L363
	.byte	4
	.byte	'read_data',0,1,212,4,125
	.word	.L329,.L364
	.byte	4
	.byte	'read_len',0,1,212,4,143,1
	.word	.L279,.L365
	.byte	5
	.word	.L124,.L360
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_transfer_16bit_array')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_transfer_16bit_array')
	.sect	'.debug_line'
.L222:
	.word	.L979-.L978
.L978:
	.half	3
	.word	.L981-.L980
.L980:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L981:
	.byte	5,6,7,0,5,2
	.word	.L124
	.byte	3,211,4,1,5,20,9
	.half	.L736-.L124
	.byte	3,2,1,5,50,9
	.half	.L731-.L736
	.byte	3,1,1,5,57,9
	.half	.L982-.L731
	.byte	1,5,22,9
	.half	.L738-.L982
	.byte	3,1,1,5,51,9
	.half	.L72-.L738
	.byte	3,2,1,5,65,9
	.half	.L983-.L72
	.byte	1,5,63,9
	.half	.L984-.L983
	.byte	1,5,73,9
	.half	.L985-.L984
	.byte	1,5,42,9
	.half	.L986-.L985
	.byte	1,5,50,9
	.half	.L740-.L986
	.byte	3,1,1,5,65,9
	.half	.L987-.L740
	.byte	1,5,62,9
	.half	.L988-.L987
	.byte	1,5,20,9
	.half	.L71-.L988
	.byte	3,125,1,5,22,9
	.half	.L989-.L71
	.byte	1,5,5,7,9
	.half	.L990-.L989
	.byte	3,5,1,5,24,7,9
	.half	.L991-.L990
	.byte	3,2,1,5,54,9
	.half	.L743-.L991
	.byte	3,1,1,5,61,9
	.half	.L992-.L743
	.byte	1,5,66,9
	.half	.L993-.L992
	.byte	1,5,26,9
	.half	.L745-.L993
	.byte	3,1,1,5,59,9
	.half	.L75-.L745
	.byte	3,2,1,5,24,9
	.half	.L747-.L75
	.byte	1,5,28,9
	.half	.L994-.L747
	.byte	3,1,1,5,39,9
	.half	.L995-.L994
	.byte	1,5,81,9
	.half	.L996-.L995
	.byte	1,5,44,9
	.half	.L749-.L996
	.byte	1,5,24,9
	.half	.L997-.L749
	.byte	1,5,23,9
	.half	.L998-.L997
	.byte	3,1,1,5,24,9
	.half	.L74-.L998
	.byte	3,124,1,5,26,9
	.half	.L999-.L74
	.byte	1,5,19,7,9
	.half	.L73-.L999
	.byte	3,7,1,5,1,9
	.half	.L751-.L73
	.byte	3,1,1,7,9
	.half	.L224-.L751
	.byte	0,1,1
.L979:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_transfer_16bit_array')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L124,0,.L224-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_sccb_write_register')
	.sect	'.debug_info'
.L225:
	.word	342
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L228,.L227
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_sccb_write_register',0,1,244,4,6,1,1,1
	.word	.L126,.L366,.L125
	.byte	4
	.byte	'soft_iic_obj',0,1,244,4,58
	.word	.L271,.L367
	.byte	4
	.byte	'register_name',0,1,244,4,84
	.word	.L368,.L369
	.byte	4
	.byte	'data',0,1,244,4,105
	.word	.L314,.L370
	.byte	5
	.word	.L126,.L366
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_sccb_write_register')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_sccb_write_register')
	.sect	'.debug_line'
.L227:
	.word	.L1001-.L1000
.L1000:
	.half	3
	.word	.L1003-.L1002
.L1002:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1003:
	.byte	5,6,7,0,5,2
	.word	.L126
	.byte	3,243,4,1,5,20,9
	.half	.L1004-.L126
	.byte	3,2,1,5,50,9
	.half	.L752-.L1004
	.byte	3,1,1,5,57,9
	.half	.L1005-.L752
	.byte	1,5,38,9
	.half	.L755-.L1005
	.byte	3,1,1,9
	.half	.L758-.L755
	.byte	3,1,1,5,19,9
	.half	.L761-.L758
	.byte	3,1,1,5,1,9
	.half	.L763-.L761
	.byte	3,1,1,7,9
	.half	.L229-.L763
	.byte	0,1,1
.L1001:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_sccb_write_register')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L126,0,.L229-.L126,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_sccb_read_register')
	.sect	'.debug_info'
.L230:
	.word	346
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L233,.L232
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_sccb_read_register',0,1,133,5,7
	.word	.L314
	.byte	1,1,1
	.word	.L128,.L371,.L127
	.byte	4
	.byte	'soft_iic_obj',0,1,133,5,58
	.word	.L271,.L372
	.byte	4
	.byte	'register_name',0,1,133,5,84
	.word	.L373,.L374
	.byte	5
	.word	.L128,.L371
	.byte	6
	.byte	'temp',0,1,135,5,11
	.word	.L314,.L375
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_sccb_read_register')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_sccb_read_register')
	.sect	'.debug_line'
.L232:
	.word	.L1007-.L1006
.L1006:
	.half	3
	.word	.L1009-.L1008
.L1008:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1009:
	.byte	5,7,7,0,5,2
	.word	.L128
	.byte	3,132,5,1,5,20,9
	.half	.L766-.L128
	.byte	3,3,1,5,50,9
	.half	.L764-.L766
	.byte	3,1,1,5,57,9
	.half	.L1010-.L764
	.byte	1,5,38,9
	.half	.L768-.L1010
	.byte	3,1,1,5,19,9
	.half	.L770-.L768
	.byte	3,1,1,5,20,9
	.half	.L773-.L770
	.byte	3,2,1,5,50,9
	.half	.L775-.L773
	.byte	3,1,1,5,57,9
	.half	.L1011-.L775
	.byte	1,5,62,9
	.half	.L1012-.L1011
	.byte	1,5,45,9
	.half	.L777-.L1012
	.byte	3,1,1,5,10,9
	.half	.L779-.L777
	.byte	1,5,19,9
	.half	.L781-.L779
	.byte	3,1,1,5,5,9
	.half	.L780-.L781
	.byte	3,1,1,5,1,9
	.half	.L76-.L780
	.byte	3,1,1,7,9
	.half	.L234-.L76
	.byte	0,1,1
.L1007:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_sccb_read_register')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L128,0,.L234-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_init')
	.sect	'.debug_info'
.L235:
	.word	361
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L238,.L237
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_init',0,1,159,5,6,1,1,1
	.word	.L130,.L376,.L129
	.byte	4
	.byte	'soft_iic_obj',0,1,159,5,43
	.word	.L271,.L377
	.byte	4
	.byte	'addr',0,1,159,5,63
	.word	.L314,.L378
	.byte	4
	.byte	'delay',0,1,159,5,76
	.word	.L279,.L379
	.byte	4
	.byte	'scl_pin',0,1,159,5,97
	.word	.L380,.L381
	.byte	4
	.byte	'sda_pin',0,1,159,5,120
	.word	.L380,.L382
	.byte	5
	.word	.L130,.L376
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_init')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_init')
	.sect	'.debug_line'
.L237:
	.word	.L1014-.L1013
.L1013:
	.half	3
	.word	.L1016-.L1015
.L1015:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1016:
	.byte	5,6,7,0,5,2
	.word	.L130
	.byte	3,158,5,1,5,5,9
	.half	.L790-.L130
	.byte	3,2,1,5,27,9
	.half	.L786-.L790
	.byte	3,1,1,9
	.half	.L791-.L786
	.byte	3,1,1,5,24,9
	.half	.L1017-.L791
	.byte	3,1,1,5,25,9
	.half	.L792-.L1017
	.byte	3,1,1,5,46,9
	.half	.L793-.L792
	.byte	3,1,1,5,27,9
	.half	.L795-.L793
	.byte	1,5,46,9
	.half	.L1018-.L795
	.byte	3,1,1,5,27,9
	.half	.L797-.L1018
	.byte	1,5,24,9
	.half	.L1019-.L797
	.byte	3,1,1,5,29,9
	.half	.L1020-.L1019
	.byte	1,5,40,9
	.half	.L1021-.L1020
	.byte	1,5,24,9
	.half	.L800-.L1021
	.byte	3,1,1,5,29,9
	.half	.L1022-.L800
	.byte	1,5,40,9
	.half	.L1023-.L1022
	.byte	1,5,1,9
	.half	.L802-.L1023
	.byte	3,1,1,7,9
	.half	.L239-.L802
	.byte	0,1,1
.L1014:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_init')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L130,0,.L239-.L130,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_start')
	.sect	'.debug_info'
.L240:
	.word	329
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L243,.L242
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_start',0,1,72,13,1,1
	.word	.L78,.L383,.L77
	.byte	4
	.byte	'soft_iic_obj',0,1,72,51
	.word	.L271,.L384
	.byte	5
	.word	.L78,.L383
	.byte	5
	.word	.L385,.L386
	.byte	6
	.byte	'i',0,1,77,5
	.word	.L387,.L388
	.byte	0,5
	.word	.L389,.L390
	.byte	6
	.byte	'i',0,1,79,5
	.word	.L391,.L392
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_start')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_start')
	.sect	'.debug_line'
.L242:
	.word	.L1025-.L1024
.L1024:
	.half	3
	.word	.L1027-.L1026
.L1026:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1027:
	.byte	5,13,7,0,5,2
	.word	.L78
	.byte	3,199,0,1,5,5,9
	.half	.L466-.L78
	.byte	3,2,1,9
	.half	.L1028-.L466
	.byte	3,1,1,9
	.half	.L385-.L1028
	.byte	3,2,1,9
	.half	.L386-.L385
	.byte	3,1,1,9
	.half	.L389-.L386
	.byte	3,1,1,9
	.half	.L390-.L389
	.byte	3,1,1,5,1,9
	.half	.L1029-.L390
	.byte	3,1,1,7,9
	.half	.L244-.L1029
	.byte	0,1,1
.L1025:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_start')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L78,0,.L244-.L78,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_stop')
	.sect	'.debug_info'
.L245:
	.word	352
	.half	3
	.word	.L246
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L248,.L247
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_stop',0,1,90,13,1,1
	.word	.L80,.L393,.L79
	.byte	4
	.byte	'soft_iic_obj',0,1,90,50
	.word	.L271,.L394
	.byte	5
	.word	.L80,.L393
	.byte	5
	.word	.L395,.L396
	.byte	6
	.byte	'i',0,1,95,5
	.word	.L397,.L398
	.byte	0,5
	.word	.L399,.L400
	.byte	6
	.byte	'i',0,1,97,5
	.word	.L401,.L402
	.byte	0,5
	.word	.L403,.L404
	.byte	6
	.byte	'i',0,1,99,5
	.word	.L405,.L406
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_stop')
	.sect	'.debug_abbrev'
.L246:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_stop')
	.sect	'.debug_line'
.L247:
	.word	.L1031-.L1030
.L1030:
	.half	3
	.word	.L1033-.L1032
.L1032:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1033:
	.byte	5,13,7,0,5,2
	.word	.L80
	.byte	3,217,0,1,5,5,9
	.half	.L467-.L80
	.byte	3,2,1,9
	.half	.L1034-.L467
	.byte	3,1,1,9
	.half	.L395-.L1034
	.byte	3,2,1,9
	.half	.L396-.L395
	.byte	3,1,1,9
	.half	.L399-.L396
	.byte	3,1,1,9
	.half	.L400-.L399
	.byte	3,1,1,9
	.half	.L403-.L400
	.byte	3,1,1,5,1,9
	.half	.L404-.L403
	.byte	3,1,1,7,9
	.half	.L249-.L404
	.byte	0,1,1
.L1031:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_stop')
	.sect	'.debug_ranges'
.L248:
	.word	-1,.L80,0,.L249-.L80,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_send_ack')
	.sect	'.debug_info'
.L250:
	.word	348
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L253,.L252
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_send_ack',0,1,110,13,1,1
	.word	.L82,.L407,.L81
	.byte	4
	.byte	'soft_iic_obj',0,1,110,54
	.word	.L271,.L408
	.byte	4
	.byte	'ack',0,1,110,74
	.word	.L314,.L409
	.byte	5
	.word	.L82,.L407
	.byte	5
	.word	.L13,.L410
	.byte	6
	.byte	'i',0,1,123,5
	.word	.L411,.L412
	.byte	0,5
	.word	.L413,.L414
	.byte	6
	.byte	'i',0,1,125,5
	.word	.L415,.L416
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_send_ack')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_send_ack')
	.sect	'.debug_line'
.L252:
	.word	.L1036-.L1035
.L1035:
	.half	3
	.word	.L1038-.L1037
.L1037:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1038:
	.byte	5,13,7,0,5,2
	.word	.L82
	.byte	3,237,0,1,5,5,9
	.half	.L468-.L82
	.byte	3,2,1,9
	.half	.L1039-.L468
	.byte	3,2,1,5,9,7,9
	.half	.L1040-.L1039
	.byte	3,2,1,5,33,9
	.half	.L1041-.L1040
	.byte	1,5,9,9
	.half	.L12-.L1041
	.byte	3,4,1,5,5,9
	.half	.L13-.L12
	.byte	3,3,1,9
	.half	.L410-.L13
	.byte	3,1,1,9
	.half	.L413-.L410
	.byte	3,1,1,9
	.half	.L414-.L413
	.byte	3,1,1,9
	.half	.L1042-.L414
	.byte	3,1,1,5,1,9
	.half	.L1043-.L1042
	.byte	3,1,1,7,9
	.half	.L254-.L1043
	.byte	0,1,1
.L1036:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_send_ack')
	.sect	'.debug_ranges'
.L253:
	.word	-1,.L82,0,.L254-.L82,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_wait_ack')
	.sect	'.debug_info'
.L255:
	.word	383
	.half	3
	.word	.L256
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L258,.L257
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_wait_ack',0,1,137,1,14
	.word	.L314
	.byte	1,1
	.word	.L84,.L417,.L83
	.byte	4
	.byte	'soft_iic_obj',0,1,137,1,55
	.word	.L271,.L418
	.byte	5
	.word	.L84,.L417
	.byte	6
	.byte	'temp',0,1,139,1,11
	.word	.L314,.L419
	.byte	5
	.word	.L420,.L421
	.byte	6
	.byte	'i',0,1,145,1,5
	.word	.L422,.L423
	.byte	0,5
	.word	.L424,.L425
	.byte	6
	.byte	'i',0,1,148,1,5
	.word	.L426,.L427
	.byte	0,5
	.word	.L428,.L429
	.byte	6
	.byte	'i',0,1,158,1,5
	.word	.L430,.L431
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_wait_ack')
	.sect	'.debug_abbrev'
.L256:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_wait_ack')
	.sect	'.debug_line'
.L257:
	.word	.L1045-.L1044
.L1044:
	.half	3
	.word	.L1047-.L1046
.L1046:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1047:
	.byte	5,14,7,0,5,2
	.word	.L84
	.byte	3,136,1,1,5,16,9
	.half	.L471-.L84
	.byte	3,2,1,5,5,9
	.half	.L472-.L471
	.byte	3,1,1,9
	.half	.L1048-.L472
	.byte	3,1,1,9
	.half	.L420-.L1048
	.byte	3,4,1,9
	.half	.L421-.L420
	.byte	3,2,1,9
	.half	.L424-.L421
	.byte	3,1,1,5,35,9
	.half	.L425-.L424
	.byte	3,2,1,5,5,9
	.half	.L470-.L425
	.byte	1,5,14,7,9
	.half	.L1049-.L470
	.byte	3,2,1,5,5,9
	.half	.L22-.L1049
	.byte	3,2,1,9
	.half	.L428-.L22
	.byte	3,4,1,9
	.half	.L429-.L428
	.byte	3,2,1,5,1,9
	.half	.L25-.L429
	.byte	3,1,1,7,9
	.half	.L259-.L25
	.byte	0,1,1
.L1045:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_wait_ack')
	.sect	'.debug_ranges'
.L258:
	.word	-1,.L84,0,.L259-.L84,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_send_data')
	.sect	'.debug_info'
.L260:
	.word	377
	.half	3
	.word	.L261
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L263,.L262
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_send_data',0,1,170,1,14
	.word	.L314
	.byte	1,1
	.word	.L86,.L432,.L85
	.byte	4
	.byte	'soft_iic_obj',0,1,170,1,56
	.word	.L271,.L433
	.byte	4
	.byte	'data',0,1,170,1,82
	.word	.L434,.L435
	.byte	5
	.word	.L86,.L432
	.byte	6
	.byte	'temp',0,1,172,1,11
	.word	.L314,.L436
	.byte	5
	.word	.L437,.L438
	.byte	6
	.byte	'i',0,1,178,1,9
	.word	.L439,.L440
	.byte	0,5
	.word	.L441,.L442
	.byte	6
	.byte	'i',0,1,180,1,9
	.word	.L443,.L444
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_send_data')
	.sect	'.debug_abbrev'
.L261:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_send_data')
	.sect	'.debug_line'
.L262:
	.word	.L1051-.L1050
.L1050:
	.half	3
	.word	.L1053-.L1052
.L1052:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1053:
	.byte	5,14,7,0,5,2
	.word	.L86
	.byte	3,169,1,1,5,16,9
	.half	.L476-.L86
	.byte	3,2,1,5,15,9
	.half	.L477-.L476
	.byte	3,1,1,5,36,9
	.half	.L27-.L477
	.byte	3,2,1,5,52,9
	.half	.L1054-.L27
	.byte	1,5,14,9
	.half	.L1055-.L1054
	.byte	3,1,1,5,9,9
	.half	.L437-.L1055
	.byte	3,2,1,9
	.half	.L438-.L437
	.byte	3,1,1,9
	.half	.L441-.L438
	.byte	3,1,1,9
	.half	.L442-.L441
	.byte	3,1,1,5,15,9
	.half	.L26-.L442
	.byte	3,120,1,5,32,7,9
	.half	.L1056-.L26
	.byte	3,10,1,5,13,9
	.half	.L479-.L1056
	.byte	1,5,52,7,9
	.half	.L1057-.L479
	.byte	1,5,56,9
	.half	.L1058-.L1057
	.byte	1,5,52,9
	.half	.L32-.L1058
	.byte	1,5,5,9
	.half	.L33-.L32
	.byte	1,5,1,9
	.half	.L34-.L33
	.byte	3,1,1,7,9
	.half	.L264-.L34
	.byte	0,1,1
.L1051:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_send_data')
	.sect	'.debug_ranges'
.L263:
	.word	-1,.L86,0,.L264-.L86,0,0
	.sdecl	'.debug_info',debug,cluster('soft_iic_read_data')
	.sect	'.debug_info'
.L265:
	.word	444
	.half	3
	.word	.L266
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L268,.L267
	.byte	2
	.word	.L131
	.byte	3
	.byte	'soft_iic_read_data',0,1,193,1,14
	.word	.L314
	.byte	1,1
	.word	.L88,.L445,.L87
	.byte	4
	.byte	'soft_iic_obj',0,1,193,1,56
	.word	.L271,.L446
	.byte	4
	.byte	'ack',0,1,193,1,76
	.word	.L314,.L447
	.byte	5
	.word	.L88,.L445
	.byte	6
	.byte	'data',0,1,195,1,11
	.word	.L314,.L448
	.byte	6
	.byte	'temp',0,1,196,1,11
	.word	.L314,.L449
	.byte	5
	.word	.L450,.L451
	.byte	6
	.byte	'i',0,1,198,1,5
	.word	.L452,.L453
	.byte	0,5
	.word	.L454,.L455
	.byte	6
	.byte	'i',0,1,207,1,9
	.word	.L456,.L457
	.byte	0,5
	.word	.L458,.L459
	.byte	6
	.byte	'i',0,1,209,1,9
	.word	.L460,.L461
	.byte	0,5
	.word	.L462,.L463
	.byte	6
	.byte	'i',0,1,216,1,5
	.word	.L464,.L465
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_iic_read_data')
	.sect	'.debug_abbrev'
.L266:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_iic_read_data')
	.sect	'.debug_line'
.L267:
	.word	.L1060-.L1059
.L1059:
	.half	3
	.word	.L1062-.L1061
.L1061:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_iic.c',0,0,0,0,0
.L1062:
	.byte	5,14,7,0,5,2
	.word	.L88
	.byte	3,192,1,1,5,16,9
	.half	.L482-.L88
	.byte	3,2,1,9
	.half	.L483-.L482
	.byte	3,1,1,5,5,9
	.half	.L485-.L483
	.byte	3,1,1,9
	.half	.L450-.L485
	.byte	3,1,1,9
	.half	.L451-.L450
	.byte	3,1,1,5,18,9
	.half	.L1063-.L451
	.byte	3,5,1,5,9,9
	.half	.L38-.L1063
	.byte	3,2,1,9
	.half	.L454-.L38
	.byte	3,1,1,9
	.half	.L455-.L454
	.byte	3,1,1,9
	.half	.L458-.L455
	.byte	3,1,1,5,23,9
	.half	.L459-.L458
	.byte	3,1,1,5,58,9
	.half	.L484-.L459
	.byte	1,5,29,9
	.half	.L1064-.L484
	.byte	1,5,16,9
	.half	.L37-.L1064
	.byte	3,122,1,5,18,9
	.half	.L487-.L37
	.byte	1,5,5,7,9
	.half	.L1065-.L487
	.byte	3,8,1,9
	.half	.L462-.L1065
	.byte	3,4,1,5,37,9
	.half	.L463-.L462
	.byte	3,1,1,5,5,9
	.half	.L489-.L463
	.byte	3,1,1,5,1,9
	.half	.L45-.L489
	.byte	3,1,1,7,9
	.half	.L269-.L45
	.byte	0,1,1
.L1060:
	.sdecl	'.debug_ranges',debug,cluster('soft_iic_read_data')
	.sect	'.debug_ranges'
.L268:
	.word	-1,.L88,0,.L269-.L88,0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_init')
	.sect	'.debug_loc'
.L378:
	.word	-1,.L130,0,.L784-.L130
	.half	1
	.byte	84
	.word	.L789-.L130,.L376-.L130
	.half	1
	.byte	95
	.word	0,0
.L379:
	.word	-1,.L130,0,.L785-.L130
	.half	1
	.byte	85
	.word	.L792-.L130,.L793-.L130
	.half	1
	.byte	88
	.word	0,0
.L381:
	.word	-1,.L130,0,.L786-.L130
	.half	1
	.byte	86
	.word	.L790-.L130,.L784-.L130
	.half	1
	.byte	89
	.word	.L786-.L130,.L791-.L130
	.half	1
	.byte	89
	.word	.L793-.L130,.L794-.L130
	.half	1
	.byte	89
	.word	.L794-.L130,.L795-.L130
	.half	1
	.byte	84
	.word	.L798-.L130,.L799-.L130
	.half	1
	.byte	89
	.word	.L799-.L130,.L800-.L130
	.half	1
	.byte	84
	.word	0,0
.L382:
	.word	-1,.L130,0,.L786-.L130
	.half	1
	.byte	87
	.word	.L790-.L130,.L376-.L130
	.half	1
	.byte	90
	.word	.L796-.L130,.L797-.L130
	.half	1
	.byte	84
	.word	.L801-.L130,.L802-.L130
	.half	1
	.byte	84
	.word	0,0
.L129:
	.word	-1,.L130,0,.L376-.L130
	.half	2
	.byte	138,0
	.word	0,0
.L377:
	.word	-1,.L130,0,.L787-.L130
	.half	1
	.byte	100
	.word	.L788-.L130,.L376-.L130
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_16bit')
	.sect	'.debug_loc'
.L325:
	.word	-1,.L110,0,.L614-.L110
	.half	1
	.byte	100
	.word	.L615-.L110,.L324-.L110
	.half	1
	.byte	111
	.word	.L616-.L110,.L617-.L110
	.half	1
	.byte	100
	.word	.L618-.L110,.L619-.L110
	.half	1
	.byte	100
	.word	.L621-.L110,.L620-.L110
	.half	1
	.byte	100
	.word	.L622-.L110,.L623-.L110
	.half	1
	.byte	100
	.word	0,0
.L109:
	.word	-1,.L110,0,.L324-.L110
	.half	2
	.byte	138,0
	.word	0,0
.L326:
	.word	-1,.L110,.L619-.L110,.L620-.L110
	.half	1
	.byte	82
	.word	.L620-.L110,.L324-.L110
	.half	1
	.byte	95
	.word	.L624-.L110,.L324-.L110
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_16bit_array')
	.sect	'.debug_loc'
.L330:
	.word	-1,.L112,0,.L625-.L112
	.half	1
	.byte	101
	.word	.L627-.L112,.L327-.L112
	.half	1
	.byte	108
	.word	0,0
.L331:
	.word	-1,.L112,0,.L625-.L112
	.half	1
	.byte	84
	.word	.L628-.L112,.L327-.L112
	.half	1
	.byte	88
	.word	0,0
.L328:
	.word	-1,.L112,0,.L625-.L112
	.half	1
	.byte	100
	.word	.L626-.L112,.L327-.L112
	.half	1
	.byte	111
	.word	.L629-.L112,.L630-.L112
	.half	1
	.byte	100
	.word	.L631-.L112,.L632-.L112
	.half	1
	.byte	100
	.word	.L633-.L112,.L634-.L112
	.half	1
	.byte	100
	.word	.L635-.L112,.L636-.L112
	.half	1
	.byte	100
	.word	0,0
.L111:
	.word	-1,.L112,0,.L327-.L112
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_16bit_register')
	.sect	'.debug_loc'
.L346:
	.word	-1,.L118,0,.L673-.L118
	.half	1
	.byte	84
	.word	.L675-.L118,.L343-.L118
	.half	1
	.byte	88
	.word	0,0
.L344:
	.word	-1,.L118,0,.L673-.L118
	.half	1
	.byte	100
	.word	.L674-.L118,.L343-.L118
	.half	1
	.byte	111
	.word	.L676-.L118,.L677-.L118
	.half	1
	.byte	100
	.word	.L678-.L118,.L679-.L118
	.half	1
	.byte	100
	.word	.L680-.L118,.L681-.L118
	.half	1
	.byte	100
	.word	.L682-.L118,.L683-.L118
	.half	1
	.byte	100
	.word	.L684-.L118,.L685-.L118
	.half	1
	.byte	100
	.word	.L686-.L118,.L687-.L118
	.half	1
	.byte	100
	.word	.L689-.L118,.L688-.L118
	.half	1
	.byte	100
	.word	.L690-.L118,.L691-.L118
	.half	1
	.byte	100
	.word	0,0
.L117:
	.word	-1,.L118,0,.L343-.L118
	.half	2
	.byte	138,0
	.word	0,0
.L347:
	.word	-1,.L118,.L687-.L118,.L688-.L118
	.half	1
	.byte	82
	.word	.L688-.L118,.L343-.L118
	.half	1
	.byte	95
	.word	.L692-.L118,.L343-.L118
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_16bit_registers')
	.sect	'.debug_loc'
.L352:
	.word	-1,.L120,0,.L693-.L120
	.half	1
	.byte	101
	.word	.L696-.L120,.L348-.L120
	.half	1
	.byte	108
	.word	0,0
.L353:
	.word	-1,.L120,0,.L693-.L120
	.half	1
	.byte	85
	.word	.L697-.L120,.L348-.L120
	.half	1
	.byte	89
	.word	0,0
.L351:
	.word	-1,.L120,0,.L693-.L120
	.half	1
	.byte	84
	.word	.L695-.L120,.L348-.L120
	.half	1
	.byte	88
	.word	0,0
.L349:
	.word	-1,.L120,0,.L693-.L120
	.half	1
	.byte	100
	.word	.L694-.L120,.L348-.L120
	.half	1
	.byte	111
	.word	.L698-.L120,.L699-.L120
	.half	1
	.byte	100
	.word	.L700-.L120,.L701-.L120
	.half	1
	.byte	100
	.word	.L702-.L120,.L703-.L120
	.half	1
	.byte	100
	.word	.L704-.L120,.L705-.L120
	.half	1
	.byte	100
	.word	.L706-.L120,.L707-.L120
	.half	1
	.byte	100
	.word	.L708-.L120,.L709-.L120
	.half	1
	.byte	100
	.word	.L710-.L120,.L711-.L120
	.half	1
	.byte	100
	.word	.L712-.L120,.L713-.L120
	.half	1
	.byte	100
	.word	0,0
.L119:
	.word	-1,.L120,0,.L348-.L120
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_8bit')
	.sect	'.debug_loc'
.L316:
	.word	-1,.L106,0,.L594-.L106
	.half	1
	.byte	100
	.word	.L595-.L106,.L315-.L106
	.half	1
	.byte	111
	.word	.L596-.L106,.L597-.L106
	.half	1
	.byte	100
	.word	.L598-.L106,.L599-.L106
	.half	1
	.byte	100
	.word	.L602-.L106,.L600-.L106
	.half	1
	.byte	100
	.word	0,0
.L105:
	.word	-1,.L106,0,.L315-.L106
	.half	2
	.byte	138,0
	.word	0,0
.L317:
	.word	-1,.L106,.L599-.L106,.L600-.L106
	.half	1
	.byte	82
	.word	.L601-.L106,.L315-.L106
	.half	1
	.byte	95
	.word	.L603-.L106,.L315-.L106
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_8bit_array')
	.sect	'.debug_loc'
.L321:
	.word	-1,.L108,0,.L604-.L108
	.half	1
	.byte	101
	.word	.L606-.L108,.L318-.L108
	.half	1
	.byte	108
	.word	0,0
.L322:
	.word	-1,.L108,0,.L604-.L108
	.half	1
	.byte	84
	.word	.L607-.L108,.L318-.L108
	.half	1
	.byte	88
	.word	0,0
.L319:
	.word	-1,.L108,0,.L604-.L108
	.half	1
	.byte	100
	.word	.L605-.L108,.L318-.L108
	.half	1
	.byte	111
	.word	.L608-.L108,.L609-.L108
	.half	1
	.byte	100
	.word	.L610-.L108,.L611-.L108
	.half	1
	.byte	100
	.word	.L612-.L108,.L613-.L108
	.half	1
	.byte	100
	.word	0,0
.L107:
	.word	-1,.L108,0,.L318-.L108
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_8bit_register')
	.sect	'.debug_loc'
.L335:
	.word	-1,.L114,0,.L637-.L114
	.half	1
	.byte	84
	.word	.L639-.L114,.L332-.L114
	.half	1
	.byte	88
	.word	.L644-.L114,.L643-.L114
	.half	1
	.byte	84
	.word	0,0
.L333:
	.word	-1,.L114,0,.L637-.L114
	.half	1
	.byte	100
	.word	.L638-.L114,.L332-.L114
	.half	1
	.byte	111
	.word	.L640-.L114,.L641-.L114
	.half	1
	.byte	100
	.word	.L642-.L114,.L643-.L114
	.half	1
	.byte	100
	.word	.L645-.L114,.L646-.L114
	.half	1
	.byte	100
	.word	.L647-.L114,.L648-.L114
	.half	1
	.byte	100
	.word	.L649-.L114,.L650-.L114
	.half	1
	.byte	100
	.word	.L653-.L114,.L651-.L114
	.half	1
	.byte	100
	.word	0,0
.L113:
	.word	-1,.L114,0,.L332-.L114
	.half	2
	.byte	138,0
	.word	0,0
.L336:
	.word	-1,.L114,.L650-.L114,.L651-.L114
	.half	1
	.byte	82
	.word	.L652-.L114,.L332-.L114
	.half	1
	.byte	95
	.word	.L654-.L114,.L332-.L114
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_8bit_registers')
	.sect	'.debug_loc'
.L341:
	.word	-1,.L116,0,.L655-.L116
	.half	1
	.byte	101
	.word	.L658-.L116,.L337-.L116
	.half	1
	.byte	108
	.word	0,0
.L342:
	.word	-1,.L116,0,.L655-.L116
	.half	1
	.byte	85
	.word	.L659-.L116,.L337-.L116
	.half	1
	.byte	89
	.word	0,0
.L340:
	.word	-1,.L116,0,.L655-.L116
	.half	1
	.byte	84
	.word	.L657-.L116,.L337-.L116
	.half	1
	.byte	88
	.word	.L664-.L116,.L663-.L116
	.half	1
	.byte	84
	.word	0,0
.L338:
	.word	-1,.L116,0,.L655-.L116
	.half	1
	.byte	100
	.word	.L656-.L116,.L337-.L116
	.half	1
	.byte	111
	.word	.L660-.L116,.L661-.L116
	.half	1
	.byte	100
	.word	.L662-.L116,.L663-.L116
	.half	1
	.byte	100
	.word	.L665-.L116,.L666-.L116
	.half	1
	.byte	100
	.word	.L667-.L116,.L668-.L116
	.half	1
	.byte	100
	.word	.L669-.L116,.L670-.L116
	.half	1
	.byte	100
	.word	.L671-.L116,.L672-.L116
	.half	1
	.byte	100
	.word	0,0
.L115:
	.word	-1,.L116,0,.L337-.L116
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_read_data')
	.sect	'.debug_loc'
.L447:
	.word	-1,.L88,0,.L38-.L88
	.half	1
	.byte	84
	.word	.L482-.L88,.L445-.L88
	.half	1
	.byte	89
	.word	.L490-.L88,.L489-.L88
	.half	1
	.byte	84
	.word	0,0
.L448:
	.word	-1,.L88,.L483-.L88,.L484-.L88
	.half	1
	.byte	88
	.word	.L37-.L88,.L445-.L88
	.half	1
	.byte	88
	.word	.L491-.L88,.L445-.L88
	.half	1
	.byte	82
	.word	0,0
.L453:
	.word	-1,.L88,0,.L445-.L88
	.half	2
	.byte	145,120
	.word	0,0
.L457:
	.word	-1,.L88,0,.L445-.L88
	.half	2
	.byte	145,120
	.word	0,0
.L461:
	.word	-1,.L88,0,.L445-.L88
	.half	2
	.byte	145,120
	.word	0,0
.L465:
	.word	-1,.L88,0,.L445-.L88
	.half	2
	.byte	145,120
	.word	0,0
.L446:
	.word	-1,.L88,0,.L38-.L88
	.half	1
	.byte	100
	.word	.L481-.L88,.L445-.L88
	.half	1
	.byte	111
	.word	.L488-.L88,.L489-.L88
	.half	1
	.byte	100
	.word	0,0
.L87:
	.word	-1,.L88,0,.L480-.L88
	.half	2
	.byte	138,0
	.word	.L480-.L88,.L445-.L88
	.half	2
	.byte	138,8
	.word	.L445-.L88,.L445-.L88
	.half	2
	.byte	138,0
	.word	0,0
.L449:
	.word	-1,.L88,.L485-.L88,.L486-.L88
	.half	1
	.byte	90
	.word	.L487-.L88,.L445-.L88
	.half	1
	.byte	90
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_sccb_read_register')
	.sect	'.debug_loc'
.L374:
	.word	-1,.L128,0,.L764-.L128
	.half	1
	.byte	84
	.word	.L766-.L128,.L371-.L128
	.half	1
	.byte	88
	.word	.L771-.L128,.L770-.L128
	.half	1
	.byte	84
	.word	0,0
.L372:
	.word	-1,.L128,0,.L764-.L128
	.half	1
	.byte	100
	.word	.L765-.L128,.L371-.L128
	.half	1
	.byte	111
	.word	.L767-.L128,.L768-.L128
	.half	1
	.byte	100
	.word	.L769-.L128,.L770-.L128
	.half	1
	.byte	100
	.word	.L772-.L128,.L773-.L128
	.half	1
	.byte	100
	.word	.L774-.L128,.L775-.L128
	.half	1
	.byte	100
	.word	.L776-.L128,.L777-.L128
	.half	1
	.byte	100
	.word	.L778-.L128,.L779-.L128
	.half	1
	.byte	100
	.word	.L782-.L128,.L780-.L128
	.half	1
	.byte	100
	.word	0,0
.L127:
	.word	-1,.L128,0,.L371-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L375:
	.word	-1,.L128,.L779-.L128,.L780-.L128
	.half	1
	.byte	82
	.word	.L781-.L128,.L371-.L128
	.half	1
	.byte	95
	.word	.L783-.L128,.L371-.L128
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_sccb_write_register')
	.sect	'.debug_loc'
.L370:
	.word	-1,.L126,0,.L752-.L126
	.half	1
	.byte	85
	.word	.L759-.L126,.L760-.L126
	.half	1
	.byte	89
	.word	.L760-.L126,.L761-.L126
	.half	1
	.byte	84
	.word	0,0
.L369:
	.word	-1,.L126,0,.L752-.L126
	.half	1
	.byte	84
	.word	.L756-.L126,.L757-.L126
	.half	1
	.byte	88
	.word	.L757-.L126,.L758-.L126
	.half	1
	.byte	84
	.word	0,0
.L367:
	.word	-1,.L126,0,.L752-.L126
	.half	1
	.byte	100
	.word	.L753-.L126,.L366-.L126
	.half	1
	.byte	111
	.word	.L754-.L126,.L755-.L126
	.half	1
	.byte	100
	.word	.L756-.L126,.L758-.L126
	.half	1
	.byte	100
	.word	.L759-.L126,.L761-.L126
	.half	1
	.byte	100
	.word	.L762-.L126,.L763-.L126
	.half	1
	.byte	100
	.word	0,0
.L125:
	.word	-1,.L126,0,.L366-.L126
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_send_ack')
	.sect	'.debug_loc'
.L409:
	.word	-1,.L82,0,.L407-.L82
	.half	1
	.byte	84
	.word	0,0
.L412:
	.word	-1,.L82,0,.L407-.L82
	.half	2
	.byte	145,120
	.word	0,0
.L416:
	.word	-1,.L82,0,.L407-.L82
	.half	2
	.byte	145,120
	.word	0,0
.L408:
	.word	-1,.L82,0,.L407-.L82
	.half	1
	.byte	100
	.word	0,0
.L81:
	.word	-1,.L82,0,.L468-.L82
	.half	2
	.byte	138,0
	.word	.L468-.L82,.L407-.L82
	.half	2
	.byte	138,8
	.word	.L407-.L82,.L407-.L82
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_send_data')
	.sect	'.debug_loc'
.L435:
	.word	-1,.L86,0,.L27-.L86
	.half	1
	.byte	84
	.word	.L476-.L86,.L432-.L86
	.half	1
	.byte	88
	.word	0,0
.L440:
	.word	-1,.L86,0,.L432-.L86
	.half	2
	.byte	145,120
	.word	0,0
.L444:
	.word	-1,.L86,0,.L432-.L86
	.half	2
	.byte	145,120
	.word	0,0
.L433:
	.word	-1,.L86,0,.L27-.L86
	.half	1
	.byte	100
	.word	.L475-.L86,.L432-.L86
	.half	1
	.byte	111
	.word	.L478-.L86,.L479-.L86
	.half	1
	.byte	100
	.word	0,0
.L85:
	.word	-1,.L86,0,.L474-.L86
	.half	2
	.byte	138,0
	.word	.L474-.L86,.L432-.L86
	.half	2
	.byte	138,8
	.word	.L432-.L86,.L432-.L86
	.half	2
	.byte	138,0
	.word	0,0
.L436:
	.word	-1,.L86,.L477-.L86,.L432-.L86
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_start')
	.sect	'.debug_loc'
.L388:
	.word	-1,.L78,0,.L383-.L78
	.half	2
	.byte	145,120
	.word	0,0
.L392:
	.word	-1,.L78,0,.L383-.L78
	.half	2
	.byte	145,120
	.word	0,0
.L384:
	.word	-1,.L78,0,.L383-.L78
	.half	1
	.byte	100
	.word	0,0
.L77:
	.word	-1,.L78,0,.L466-.L78
	.half	2
	.byte	138,0
	.word	.L466-.L78,.L383-.L78
	.half	2
	.byte	138,8
	.word	.L383-.L78,.L383-.L78
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_stop')
	.sect	'.debug_loc'
.L398:
	.word	-1,.L80,0,.L393-.L80
	.half	2
	.byte	145,120
	.word	0,0
.L402:
	.word	-1,.L80,0,.L393-.L80
	.half	2
	.byte	145,120
	.word	0,0
.L406:
	.word	-1,.L80,0,.L393-.L80
	.half	2
	.byte	145,120
	.word	0,0
.L394:
	.word	-1,.L80,0,.L393-.L80
	.half	1
	.byte	100
	.word	0,0
.L79:
	.word	-1,.L80,0,.L467-.L80
	.half	2
	.byte	138,0
	.word	.L467-.L80,.L393-.L80
	.half	2
	.byte	138,8
	.word	.L393-.L80,.L393-.L80
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_transfer_16bit_array')
	.sect	'.debug_loc'
.L364:
	.word	-1,.L124,0,.L731-.L124
	.half	1
	.byte	102
	.word	.L735-.L124,.L360-.L124
	.half	1
	.byte	109
	.word	0,0
.L365:
	.word	-1,.L124,0,.L731-.L124
	.half	1
	.byte	85
	.word	.L736-.L124,.L360-.L124
	.half	1
	.byte	89
	.word	0,0
.L361:
	.word	-1,.L124,0,.L731-.L124
	.half	1
	.byte	100
	.word	.L732-.L124,.L360-.L124
	.half	1
	.byte	111
	.word	.L737-.L124,.L738-.L124
	.half	1
	.byte	100
	.word	.L739-.L124,.L740-.L124
	.half	1
	.byte	100
	.word	.L741-.L124,.L71-.L124
	.half	1
	.byte	100
	.word	.L742-.L124,.L743-.L124
	.half	1
	.byte	100
	.word	.L744-.L124,.L745-.L124
	.half	1
	.byte	100
	.word	.L746-.L124,.L747-.L124
	.half	1
	.byte	100
	.word	.L748-.L124,.L749-.L124
	.half	1
	.byte	100
	.word	.L750-.L124,.L751-.L124
	.half	1
	.byte	100
	.word	0,0
.L123:
	.word	-1,.L124,0,.L360-.L124
	.half	2
	.byte	138,0
	.word	0,0
.L362:
	.word	-1,.L124,0,.L731-.L124
	.half	1
	.byte	101
	.word	.L733-.L124,.L360-.L124
	.half	1
	.byte	108
	.word	0,0
.L363:
	.word	-1,.L124,0,.L731-.L124
	.half	1
	.byte	84
	.word	.L734-.L124,.L360-.L124
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_transfer_8bit_array')
	.sect	'.debug_loc'
.L358:
	.word	-1,.L122,0,.L714-.L122
	.half	1
	.byte	102
	.word	.L718-.L122,.L354-.L122
	.half	1
	.byte	109
	.word	0,0
.L359:
	.word	-1,.L122,0,.L714-.L122
	.half	1
	.byte	85
	.word	.L719-.L122,.L354-.L122
	.half	1
	.byte	89
	.word	0,0
.L355:
	.word	-1,.L122,0,.L714-.L122
	.half	1
	.byte	100
	.word	.L715-.L122,.L354-.L122
	.half	1
	.byte	111
	.word	.L720-.L122,.L721-.L122
	.half	1
	.byte	100
	.word	.L722-.L122,.L66-.L122
	.half	1
	.byte	100
	.word	.L723-.L122,.L724-.L122
	.half	1
	.byte	100
	.word	.L725-.L122,.L726-.L122
	.half	1
	.byte	100
	.word	.L727-.L122,.L728-.L122
	.half	1
	.byte	100
	.word	.L729-.L122,.L730-.L122
	.half	1
	.byte	100
	.word	0,0
.L121:
	.word	-1,.L122,0,.L354-.L122
	.half	2
	.byte	138,0
	.word	0,0
.L356:
	.word	-1,.L122,0,.L714-.L122
	.half	1
	.byte	101
	.word	.L716-.L122,.L354-.L122
	.half	1
	.byte	108
	.word	0,0
.L357:
	.word	-1,.L122,0,.L714-.L122
	.half	1
	.byte	84
	.word	.L717-.L122,.L354-.L122
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_wait_ack')
	.sect	'.debug_loc'
.L423:
	.word	-1,.L84,0,.L417-.L84
	.half	2
	.byte	145,120
	.word	0,0
.L427:
	.word	-1,.L84,0,.L417-.L84
	.half	2
	.byte	145,120
	.word	0,0
.L431:
	.word	-1,.L84,0,.L417-.L84
	.half	2
	.byte	145,120
	.word	0,0
.L418:
	.word	-1,.L84,0,.L470-.L84
	.half	1
	.byte	100
	.word	.L471-.L84,.L417-.L84
	.half	1
	.byte	111
	.word	0,0
.L83:
	.word	-1,.L84,0,.L469-.L84
	.half	2
	.byte	138,0
	.word	.L469-.L84,.L417-.L84
	.half	2
	.byte	138,8
	.word	.L417-.L84,.L417-.L84
	.half	2
	.byte	138,0
	.word	0,0
.L419:
	.word	-1,.L84,.L472-.L84,.L417-.L84
	.half	1
	.byte	88
	.word	.L473-.L84,.L417-.L84
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_16bit')
	.sect	'.debug_loc'
.L284:
	.word	-1,.L94,0,.L511-.L94
	.half	1
	.byte	84
	.word	.L513-.L94,.L281-.L94
	.half	1
	.byte	88
	.word	0,0
.L282:
	.word	-1,.L94,0,.L511-.L94
	.half	1
	.byte	100
	.word	.L512-.L94,.L281-.L94
	.half	1
	.byte	111
	.word	.L514-.L94,.L515-.L94
	.half	1
	.byte	100
	.word	.L516-.L94,.L517-.L94
	.half	1
	.byte	100
	.word	.L518-.L94,.L519-.L94
	.half	1
	.byte	100
	.word	.L520-.L94,.L521-.L94
	.half	1
	.byte	100
	.word	0,0
.L93:
	.word	-1,.L94,0,.L281-.L94
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_16bit_array')
	.sect	'.debug_loc'
.L288:
	.word	-1,.L96,0,.L522-.L96
	.half	1
	.byte	101
	.word	.L524-.L96,.L285-.L96
	.half	1
	.byte	108
	.word	0,0
.L289:
	.word	-1,.L96,0,.L522-.L96
	.half	1
	.byte	84
	.word	.L525-.L96,.L285-.L96
	.half	1
	.byte	88
	.word	0,0
.L286:
	.word	-1,.L96,0,.L522-.L96
	.half	1
	.byte	100
	.word	.L523-.L96,.L285-.L96
	.half	1
	.byte	111
	.word	.L526-.L96,.L527-.L96
	.half	1
	.byte	100
	.word	.L528-.L96,.L529-.L96
	.half	1
	.byte	100
	.word	.L530-.L96,.L48-.L96
	.half	1
	.byte	100
	.word	.L531-.L96,.L532-.L96
	.half	1
	.byte	100
	.word	0,0
.L95:
	.word	-1,.L96,0,.L285-.L96
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_16bit_register')
	.sect	'.debug_loc'
.L307:
	.word	-1,.L102,0,.L558-.L102
	.half	1
	.byte	85
	.word	.L569-.L102,.L570-.L102
	.half	1
	.byte	89
	.word	.L572-.L102,.L573-.L102
	.half	1
	.byte	89
	.word	0,0
.L305:
	.word	-1,.L102,0,.L558-.L102
	.half	1
	.byte	84
	.word	.L562-.L102,.L563-.L102
	.half	1
	.byte	88
	.word	.L565-.L102,.L566-.L102
	.half	1
	.byte	88
	.word	0,0
.L303:
	.word	-1,.L102,0,.L558-.L102
	.half	1
	.byte	100
	.word	.L559-.L102,.L302-.L102
	.half	1
	.byte	111
	.word	.L560-.L102,.L561-.L102
	.half	1
	.byte	100
	.word	.L564-.L102,.L565-.L102
	.half	1
	.byte	100
	.word	.L567-.L102,.L568-.L102
	.half	1
	.byte	100
	.word	.L571-.L102,.L572-.L102
	.half	1
	.byte	100
	.word	.L574-.L102,.L575-.L102
	.half	1
	.byte	100
	.word	.L576-.L102,.L577-.L102
	.half	1
	.byte	100
	.word	0,0
.L101:
	.word	-1,.L102,0,.L302-.L102
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_16bit_registers')
	.sect	'.debug_loc'
.L312:
	.word	-1,.L104,0,.L578-.L104
	.half	1
	.byte	101
	.word	.L581-.L104,.L308-.L104
	.half	1
	.byte	108
	.word	0,0
.L313:
	.word	-1,.L104,0,.L578-.L104
	.half	1
	.byte	85
	.word	.L582-.L104,.L308-.L104
	.half	1
	.byte	89
	.word	0,0
.L311:
	.word	-1,.L104,0,.L578-.L104
	.half	1
	.byte	84
	.word	.L580-.L104,.L308-.L104
	.half	1
	.byte	88
	.word	0,0
.L309:
	.word	-1,.L104,0,.L578-.L104
	.half	1
	.byte	100
	.word	.L579-.L104,.L308-.L104
	.half	1
	.byte	111
	.word	.L583-.L104,.L584-.L104
	.half	1
	.byte	100
	.word	.L585-.L104,.L586-.L104
	.half	1
	.byte	100
	.word	.L587-.L104,.L588-.L104
	.half	1
	.byte	100
	.word	.L589-.L104,.L590-.L104
	.half	1
	.byte	100
	.word	.L591-.L104,.L52-.L104
	.half	1
	.byte	100
	.word	.L592-.L104,.L593-.L104
	.half	1
	.byte	100
	.word	0,0
.L103:
	.word	-1,.L104,0,.L308-.L104
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_8bit')
	.sect	'.debug_loc'
.L274:
	.word	-1,.L90,0,.L492-.L90
	.half	1
	.byte	84
	.word	.L494-.L90,.L270-.L90
	.half	1
	.byte	88
	.word	.L499-.L90,.L498-.L90
	.half	1
	.byte	84
	.word	0,0
.L272:
	.word	-1,.L90,0,.L492-.L90
	.half	1
	.byte	100
	.word	.L493-.L90,.L270-.L90
	.half	1
	.byte	111
	.word	.L495-.L90,.L496-.L90
	.half	1
	.byte	100
	.word	.L497-.L90,.L498-.L90
	.half	1
	.byte	100
	.word	.L500-.L90,.L501-.L90
	.half	1
	.byte	100
	.word	0,0
.L89:
	.word	-1,.L90,0,.L270-.L90
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_8bit_array')
	.sect	'.debug_loc'
.L278:
	.word	-1,.L92,0,.L502-.L92
	.half	1
	.byte	101
	.word	.L504-.L92,.L275-.L92
	.half	1
	.byte	108
	.word	0,0
.L280:
	.word	-1,.L92,0,.L502-.L92
	.half	1
	.byte	84
	.word	.L505-.L92,.L275-.L92
	.half	1
	.byte	88
	.word	0,0
.L276:
	.word	-1,.L92,0,.L502-.L92
	.half	1
	.byte	100
	.word	.L503-.L92,.L275-.L92
	.half	1
	.byte	111
	.word	.L506-.L92,.L507-.L92
	.half	1
	.byte	100
	.word	.L508-.L92,.L46-.L92
	.half	1
	.byte	100
	.word	.L509-.L92,.L510-.L92
	.half	1
	.byte	100
	.word	0,0
.L91:
	.word	-1,.L92,0,.L275-.L92
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_8bit_register')
	.sect	'.debug_loc'
.L295:
	.word	-1,.L98,0,.L533-.L98
	.half	1
	.byte	85
	.word	.L540-.L98,.L541-.L98
	.half	1
	.byte	89
	.word	.L541-.L98,.L542-.L98
	.half	1
	.byte	84
	.word	0,0
.L293:
	.word	-1,.L98,0,.L533-.L98
	.half	1
	.byte	84
	.word	.L537-.L98,.L538-.L98
	.half	1
	.byte	88
	.word	.L538-.L98,.L539-.L98
	.half	1
	.byte	84
	.word	0,0
.L291:
	.word	-1,.L98,0,.L533-.L98
	.half	1
	.byte	100
	.word	.L534-.L98,.L290-.L98
	.half	1
	.byte	111
	.word	.L535-.L98,.L536-.L98
	.half	1
	.byte	100
	.word	.L537-.L98,.L539-.L98
	.half	1
	.byte	100
	.word	.L540-.L98,.L542-.L98
	.half	1
	.byte	100
	.word	.L543-.L98,.L544-.L98
	.half	1
	.byte	100
	.word	0,0
.L97:
	.word	-1,.L98,0,.L290-.L98
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_iic_write_8bit_registers')
	.sect	'.debug_loc'
.L300:
	.word	-1,.L100,0,.L545-.L100
	.half	1
	.byte	101
	.word	.L548-.L100,.L296-.L100
	.half	1
	.byte	108
	.word	0,0
.L301:
	.word	-1,.L100,0,.L545-.L100
	.half	1
	.byte	85
	.word	.L549-.L100,.L296-.L100
	.half	1
	.byte	89
	.word	0,0
.L299:
	.word	-1,.L100,0,.L545-.L100
	.half	1
	.byte	84
	.word	.L547-.L100,.L296-.L100
	.half	1
	.byte	88
	.word	.L554-.L100,.L553-.L100
	.half	1
	.byte	84
	.word	0,0
.L297:
	.word	-1,.L100,0,.L545-.L100
	.half	1
	.byte	100
	.word	.L546-.L100,.L296-.L100
	.half	1
	.byte	111
	.word	.L550-.L100,.L551-.L100
	.half	1
	.byte	100
	.word	.L552-.L100,.L553-.L100
	.half	1
	.byte	100
	.word	.L555-.L100,.L50-.L100
	.half	1
	.byte	100
	.word	.L556-.L100,.L557-.L100
	.half	1
	.byte	100
	.word	0,0
.L99:
	.word	-1,.L100,0,.L296-.L100
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1066:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('soft_iic_start')
	.sect	'.debug_frame'
	.word	44
	.word	.L1066,.L78,.L383-.L78
	.byte	8,18,8,19,8,21,8,22,8,23,4
	.word	(.L466-.L78)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L383-.L466)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('soft_iic_stop')
	.sect	'.debug_frame'
	.word	44
	.word	.L1066,.L80,.L393-.L80
	.byte	8,18,8,19,8,21,8,22,8,23,4
	.word	(.L467-.L80)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L393-.L467)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('soft_iic_send_ack')
	.sect	'.debug_frame'
	.word	44
	.word	.L1066,.L82,.L407-.L82
	.byte	8,18,8,19,8,21,8,22,8,23,4
	.word	(.L468-.L82)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L407-.L468)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('soft_iic_wait_ack')
	.sect	'.debug_frame'
	.word	36
	.word	.L1066,.L84,.L417-.L84
	.byte	4
	.word	(.L469-.L84)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L417-.L469)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('soft_iic_send_data')
	.sect	'.debug_frame'
	.word	36
	.word	.L1066,.L86,.L432-.L86
	.byte	4
	.word	(.L474-.L86)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L432-.L474)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_data')
	.sect	'.debug_frame'
	.word	36
	.word	.L1066,.L88,.L445-.L88
	.byte	4
	.word	(.L480-.L88)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L445-.L480)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_8bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L90,.L270-.L90
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_8bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L92,.L275-.L92
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_16bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L94,.L281-.L94
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_16bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L96,.L285-.L96
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_8bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L98,.L290-.L98
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_8bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L100,.L296-.L100
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_16bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L102,.L302-.L102
	.sdecl	'.debug_frame',debug,cluster('soft_iic_write_16bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L104,.L308-.L104
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_8bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L106,.L315-.L106
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_8bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L108,.L318-.L108
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_16bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L110,.L324-.L110
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_16bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L112,.L327-.L112
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_8bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L114,.L332-.L114
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_8bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L116,.L337-.L116
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_16bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L118,.L343-.L118
	.sdecl	'.debug_frame',debug,cluster('soft_iic_read_16bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L120,.L348-.L120
	.sdecl	'.debug_frame',debug,cluster('soft_iic_transfer_8bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L122,.L354-.L122
	.sdecl	'.debug_frame',debug,cluster('soft_iic_transfer_16bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L124,.L360-.L124
	.sdecl	'.debug_frame',debug,cluster('soft_iic_sccb_write_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L126,.L366-.L126
	.sdecl	'.debug_frame',debug,cluster('soft_iic_sccb_read_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L128,.L371-.L128
	.sdecl	'.debug_frame',debug,cluster('soft_iic_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L1066,.L130,.L376-.L130
	; Module end
