/**
 * \file IfxEmem_bf.h
 * \brief
 * \copyright Copyright (c) 2014 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC2XXED_TS_V1.0.R2
 * Specification: AurixED_TS_V1.0_CPU_VIEW_SFR.xml (Revision: V1.0)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Emem_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Emem
 * 
 */
#ifndef IFXEMEM_BF_H
#define IFXEMEM_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Emem_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_EMEM_CLC_Bits.DISR */
#define IFX_EMEM_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_EMEM_CLC_Bits.DISR */
#define IFX_EMEM_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_CLC_Bits.DISR */
#define IFX_EMEM_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_EMEM_CLC_Bits.DISS */
#define IFX_EMEM_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_EMEM_CLC_Bits.DISS */
#define IFX_EMEM_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_CLC_Bits.DISS */
#define IFX_EMEM_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_EMEM_ID_Bits.MOD_REV */
#define IFX_EMEM_ID_MOD_REV_LEN (8u)

/** \brief  Mask for Ifx_EMEM_ID_Bits.MOD_REV */
#define IFX_EMEM_ID_MOD_REV_MSK (0xffu)

/** \brief  Offset for Ifx_EMEM_ID_Bits.MOD_REV */
#define IFX_EMEM_ID_MOD_REV_OFF (0u)

/** \brief  Length for Ifx_EMEM_ID_Bits.MOD_TYPE */
#define IFX_EMEM_ID_MOD_TYPE_LEN (8u)

/** \brief  Mask for Ifx_EMEM_ID_Bits.MOD_TYPE */
#define IFX_EMEM_ID_MOD_TYPE_MSK (0xffu)

/** \brief  Offset for Ifx_EMEM_ID_Bits.MOD_TYPE */
#define IFX_EMEM_ID_MOD_TYPE_OFF (8u)

/** \brief  Length for Ifx_EMEM_ID_Bits.MODNUMBER */
#define IFX_EMEM_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_EMEM_ID_Bits.MODNUMBER */
#define IFX_EMEM_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_EMEM_ID_Bits.MODNUMBER */
#define IFX_EMEM_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGEN */
#define IFX_EMEM_SBRCTR_ACGEN_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGEN */
#define IFX_EMEM_SBRCTR_ACGEN_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGEN */
#define IFX_EMEM_SBRCTR_ACGEN_OFF (12u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST0 */
#define IFX_EMEM_SBRCTR_ACGST0_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST0 */
#define IFX_EMEM_SBRCTR_ACGST0_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST0 */
#define IFX_EMEM_SBRCTR_ACGST0_OFF (16u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST10 */
#define IFX_EMEM_SBRCTR_ACGST10_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST10 */
#define IFX_EMEM_SBRCTR_ACGST10_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST10 */
#define IFX_EMEM_SBRCTR_ACGST10_OFF (26u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST11 */
#define IFX_EMEM_SBRCTR_ACGST11_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST11 */
#define IFX_EMEM_SBRCTR_ACGST11_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST11 */
#define IFX_EMEM_SBRCTR_ACGST11_OFF (27u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST12 */
#define IFX_EMEM_SBRCTR_ACGST12_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST12 */
#define IFX_EMEM_SBRCTR_ACGST12_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST12 */
#define IFX_EMEM_SBRCTR_ACGST12_OFF (28u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST13 */
#define IFX_EMEM_SBRCTR_ACGST13_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST13 */
#define IFX_EMEM_SBRCTR_ACGST13_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST13 */
#define IFX_EMEM_SBRCTR_ACGST13_OFF (29u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST14 */
#define IFX_EMEM_SBRCTR_ACGST14_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST14 */
#define IFX_EMEM_SBRCTR_ACGST14_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST14 */
#define IFX_EMEM_SBRCTR_ACGST14_OFF (30u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST15 */
#define IFX_EMEM_SBRCTR_ACGST15_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST15 */
#define IFX_EMEM_SBRCTR_ACGST15_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST15 */
#define IFX_EMEM_SBRCTR_ACGST15_OFF (31u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST1 */
#define IFX_EMEM_SBRCTR_ACGST1_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST1 */
#define IFX_EMEM_SBRCTR_ACGST1_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST1 */
#define IFX_EMEM_SBRCTR_ACGST1_OFF (17u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST2 */
#define IFX_EMEM_SBRCTR_ACGST2_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST2 */
#define IFX_EMEM_SBRCTR_ACGST2_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST2 */
#define IFX_EMEM_SBRCTR_ACGST2_OFF (18u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST3 */
#define IFX_EMEM_SBRCTR_ACGST3_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST3 */
#define IFX_EMEM_SBRCTR_ACGST3_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST3 */
#define IFX_EMEM_SBRCTR_ACGST3_OFF (19u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST4 */
#define IFX_EMEM_SBRCTR_ACGST4_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST4 */
#define IFX_EMEM_SBRCTR_ACGST4_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST4 */
#define IFX_EMEM_SBRCTR_ACGST4_OFF (20u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST5 */
#define IFX_EMEM_SBRCTR_ACGST5_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST5 */
#define IFX_EMEM_SBRCTR_ACGST5_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST5 */
#define IFX_EMEM_SBRCTR_ACGST5_OFF (21u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST6 */
#define IFX_EMEM_SBRCTR_ACGST6_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST6 */
#define IFX_EMEM_SBRCTR_ACGST6_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST6 */
#define IFX_EMEM_SBRCTR_ACGST6_OFF (22u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST7 */
#define IFX_EMEM_SBRCTR_ACGST7_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST7 */
#define IFX_EMEM_SBRCTR_ACGST7_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST7 */
#define IFX_EMEM_SBRCTR_ACGST7_OFF (23u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST8 */
#define IFX_EMEM_SBRCTR_ACGST8_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST8 */
#define IFX_EMEM_SBRCTR_ACGST8_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST8 */
#define IFX_EMEM_SBRCTR_ACGST8_OFF (24u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGST9 */
#define IFX_EMEM_SBRCTR_ACGST9_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGST9 */
#define IFX_EMEM_SBRCTR_ACGST9_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGST9 */
#define IFX_EMEM_SBRCTR_ACGST9_OFF (25u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGSXCM0 */
#define IFX_EMEM_SBRCTR_ACGSXCM0_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGSXCM0 */
#define IFX_EMEM_SBRCTR_ACGSXCM0_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGSXCM0 */
#define IFX_EMEM_SBRCTR_ACGSXCM0_OFF (8u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGSXTM0 */
#define IFX_EMEM_SBRCTR_ACGSXTM0_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGSXTM0 */
#define IFX_EMEM_SBRCTR_ACGSXTM0_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGSXTM0 */
#define IFX_EMEM_SBRCTR_ACGSXTM0_OFF (13u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.ACGSXTM1 */
#define IFX_EMEM_SBRCTR_ACGSXTM1_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.ACGSXTM1 */
#define IFX_EMEM_SBRCTR_ACGSXTM1_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.ACGSXTM1 */
#define IFX_EMEM_SBRCTR_ACGSXTM1_OFF (14u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.STBLOCK */
#define IFX_EMEM_SBRCTR_STBLOCK_LEN (1u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.STBLOCK */
#define IFX_EMEM_SBRCTR_STBLOCK_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.STBLOCK */
#define IFX_EMEM_SBRCTR_STBLOCK_OFF (0u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.STBSLK */
#define IFX_EMEM_SBRCTR_STBSLK_LEN (4u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.STBSLK */
#define IFX_EMEM_SBRCTR_STBSLK_MSK (0xfu)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.STBSLK */
#define IFX_EMEM_SBRCTR_STBSLK_OFF (4u)

/** \brief  Length for Ifx_EMEM_SBRCTR_Bits.STBULK */
#define IFX_EMEM_SBRCTR_STBULK_LEN (3u)

/** \brief  Mask for Ifx_EMEM_SBRCTR_Bits.STBULK */
#define IFX_EMEM_SBRCTR_STBULK_MSK (0x7u)

/** \brief  Offset for Ifx_EMEM_SBRCTR_Bits.STBULK */
#define IFX_EMEM_SBRCTR_STBULK_OFF (1u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T0 */
#define IFX_EMEM_TILECC_T0_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T0 */
#define IFX_EMEM_TILECC_T0_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T0 */
#define IFX_EMEM_TILECC_T0_OFF (0u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T10 */
#define IFX_EMEM_TILECC_T10_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T10 */
#define IFX_EMEM_TILECC_T10_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T10 */
#define IFX_EMEM_TILECC_T10_OFF (10u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T11 */
#define IFX_EMEM_TILECC_T11_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T11 */
#define IFX_EMEM_TILECC_T11_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T11 */
#define IFX_EMEM_TILECC_T11_OFF (11u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T12 */
#define IFX_EMEM_TILECC_T12_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T12 */
#define IFX_EMEM_TILECC_T12_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T12 */
#define IFX_EMEM_TILECC_T12_OFF (12u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T13 */
#define IFX_EMEM_TILECC_T13_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T13 */
#define IFX_EMEM_TILECC_T13_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T13 */
#define IFX_EMEM_TILECC_T13_OFF (13u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T14 */
#define IFX_EMEM_TILECC_T14_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T14 */
#define IFX_EMEM_TILECC_T14_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T14 */
#define IFX_EMEM_TILECC_T14_OFF (14u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T15 */
#define IFX_EMEM_TILECC_T15_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T15 */
#define IFX_EMEM_TILECC_T15_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T15 */
#define IFX_EMEM_TILECC_T15_OFF (15u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T1 */
#define IFX_EMEM_TILECC_T1_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T1 */
#define IFX_EMEM_TILECC_T1_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T1 */
#define IFX_EMEM_TILECC_T1_OFF (1u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T2 */
#define IFX_EMEM_TILECC_T2_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T2 */
#define IFX_EMEM_TILECC_T2_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T2 */
#define IFX_EMEM_TILECC_T2_OFF (2u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T3 */
#define IFX_EMEM_TILECC_T3_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T3 */
#define IFX_EMEM_TILECC_T3_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T3 */
#define IFX_EMEM_TILECC_T3_OFF (3u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T4 */
#define IFX_EMEM_TILECC_T4_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T4 */
#define IFX_EMEM_TILECC_T4_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T4 */
#define IFX_EMEM_TILECC_T4_OFF (4u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T5 */
#define IFX_EMEM_TILECC_T5_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T5 */
#define IFX_EMEM_TILECC_T5_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T5 */
#define IFX_EMEM_TILECC_T5_OFF (5u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T6 */
#define IFX_EMEM_TILECC_T6_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T6 */
#define IFX_EMEM_TILECC_T6_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T6 */
#define IFX_EMEM_TILECC_T6_OFF (6u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T7 */
#define IFX_EMEM_TILECC_T7_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T7 */
#define IFX_EMEM_TILECC_T7_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T7 */
#define IFX_EMEM_TILECC_T7_OFF (7u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T8 */
#define IFX_EMEM_TILECC_T8_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T8 */
#define IFX_EMEM_TILECC_T8_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T8 */
#define IFX_EMEM_TILECC_T8_OFF (8u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.T9 */
#define IFX_EMEM_TILECC_T9_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.T9 */
#define IFX_EMEM_TILECC_T9_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.T9 */
#define IFX_EMEM_TILECC_T9_OFF (9u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.XTM0 */
#define IFX_EMEM_TILECC_XTM0_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.XTM0 */
#define IFX_EMEM_TILECC_XTM0_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.XTM0 */
#define IFX_EMEM_TILECC_XTM0_OFF (16u)

/** \brief  Length for Ifx_EMEM_TILECC_Bits.XTM1 */
#define IFX_EMEM_TILECC_XTM1_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECC_Bits.XTM1 */
#define IFX_EMEM_TILECC_XTM1_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECC_Bits.XTM1 */
#define IFX_EMEM_TILECC_XTM1_OFF (17u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T0 */
#define IFX_EMEM_TILECONFIG_T0_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T0 */
#define IFX_EMEM_TILECONFIG_T0_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T0 */
#define IFX_EMEM_TILECONFIG_T0_OFF (0u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T10 */
#define IFX_EMEM_TILECONFIG_T10_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T10 */
#define IFX_EMEM_TILECONFIG_T10_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T10 */
#define IFX_EMEM_TILECONFIG_T10_OFF (20u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T11 */
#define IFX_EMEM_TILECONFIG_T11_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T11 */
#define IFX_EMEM_TILECONFIG_T11_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T11 */
#define IFX_EMEM_TILECONFIG_T11_OFF (22u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T12 */
#define IFX_EMEM_TILECONFIG_T12_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T12 */
#define IFX_EMEM_TILECONFIG_T12_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T12 */
#define IFX_EMEM_TILECONFIG_T12_OFF (24u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T13 */
#define IFX_EMEM_TILECONFIG_T13_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T13 */
#define IFX_EMEM_TILECONFIG_T13_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T13 */
#define IFX_EMEM_TILECONFIG_T13_OFF (26u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T14 */
#define IFX_EMEM_TILECONFIG_T14_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T14 */
#define IFX_EMEM_TILECONFIG_T14_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T14 */
#define IFX_EMEM_TILECONFIG_T14_OFF (28u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T15 */
#define IFX_EMEM_TILECONFIG_T15_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T15 */
#define IFX_EMEM_TILECONFIG_T15_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T15 */
#define IFX_EMEM_TILECONFIG_T15_OFF (30u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T1 */
#define IFX_EMEM_TILECONFIG_T1_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T1 */
#define IFX_EMEM_TILECONFIG_T1_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T1 */
#define IFX_EMEM_TILECONFIG_T1_OFF (2u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T2 */
#define IFX_EMEM_TILECONFIG_T2_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T2 */
#define IFX_EMEM_TILECONFIG_T2_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T2 */
#define IFX_EMEM_TILECONFIG_T2_OFF (4u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T3 */
#define IFX_EMEM_TILECONFIG_T3_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T3 */
#define IFX_EMEM_TILECONFIG_T3_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T3 */
#define IFX_EMEM_TILECONFIG_T3_OFF (6u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T4 */
#define IFX_EMEM_TILECONFIG_T4_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T4 */
#define IFX_EMEM_TILECONFIG_T4_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T4 */
#define IFX_EMEM_TILECONFIG_T4_OFF (8u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T5 */
#define IFX_EMEM_TILECONFIG_T5_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T5 */
#define IFX_EMEM_TILECONFIG_T5_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T5 */
#define IFX_EMEM_TILECONFIG_T5_OFF (10u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T6 */
#define IFX_EMEM_TILECONFIG_T6_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T6 */
#define IFX_EMEM_TILECONFIG_T6_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T6 */
#define IFX_EMEM_TILECONFIG_T6_OFF (12u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T7 */
#define IFX_EMEM_TILECONFIG_T7_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T7 */
#define IFX_EMEM_TILECONFIG_T7_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T7 */
#define IFX_EMEM_TILECONFIG_T7_OFF (14u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T8 */
#define IFX_EMEM_TILECONFIG_T8_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T8 */
#define IFX_EMEM_TILECONFIG_T8_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T8 */
#define IFX_EMEM_TILECONFIG_T8_OFF (16u)

/** \brief  Length for Ifx_EMEM_TILECONFIG_Bits.T9 */
#define IFX_EMEM_TILECONFIG_T9_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIG_Bits.T9 */
#define IFX_EMEM_TILECONFIG_T9_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIG_Bits.T9 */
#define IFX_EMEM_TILECONFIG_T9_OFF (18u)

/** \brief  Length for Ifx_EMEM_TILECONFIGXM_Bits.XCM0 */
#define IFX_EMEM_TILECONFIGXM_XCM0_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIGXM_Bits.XCM0 */
#define IFX_EMEM_TILECONFIGXM_XCM0_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIGXM_Bits.XCM0 */
#define IFX_EMEM_TILECONFIGXM_XCM0_OFF (0u)

/** \brief  Length for Ifx_EMEM_TILECONFIGXM_Bits.XTM0 */
#define IFX_EMEM_TILECONFIGXM_XTM0_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIGXM_Bits.XTM0 */
#define IFX_EMEM_TILECONFIGXM_XTM0_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIGXM_Bits.XTM0 */
#define IFX_EMEM_TILECONFIGXM_XTM0_OFF (16u)

/** \brief  Length for Ifx_EMEM_TILECONFIGXM_Bits.XTM1 */
#define IFX_EMEM_TILECONFIGXM_XTM1_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILECONFIGXM_Bits.XTM1 */
#define IFX_EMEM_TILECONFIGXM_XTM1_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILECONFIGXM_Bits.XTM1 */
#define IFX_EMEM_TILECONFIGXM_XTM1_OFF (18u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T0 */
#define IFX_EMEM_TILECT_T0_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T0 */
#define IFX_EMEM_TILECT_T0_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T0 */
#define IFX_EMEM_TILECT_T0_OFF (0u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T10 */
#define IFX_EMEM_TILECT_T10_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T10 */
#define IFX_EMEM_TILECT_T10_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T10 */
#define IFX_EMEM_TILECT_T10_OFF (10u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T11 */
#define IFX_EMEM_TILECT_T11_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T11 */
#define IFX_EMEM_TILECT_T11_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T11 */
#define IFX_EMEM_TILECT_T11_OFF (11u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T12 */
#define IFX_EMEM_TILECT_T12_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T12 */
#define IFX_EMEM_TILECT_T12_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T12 */
#define IFX_EMEM_TILECT_T12_OFF (12u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T13 */
#define IFX_EMEM_TILECT_T13_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T13 */
#define IFX_EMEM_TILECT_T13_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T13 */
#define IFX_EMEM_TILECT_T13_OFF (13u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T14 */
#define IFX_EMEM_TILECT_T14_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T14 */
#define IFX_EMEM_TILECT_T14_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T14 */
#define IFX_EMEM_TILECT_T14_OFF (14u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T15 */
#define IFX_EMEM_TILECT_T15_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T15 */
#define IFX_EMEM_TILECT_T15_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T15 */
#define IFX_EMEM_TILECT_T15_OFF (15u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T1 */
#define IFX_EMEM_TILECT_T1_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T1 */
#define IFX_EMEM_TILECT_T1_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T1 */
#define IFX_EMEM_TILECT_T1_OFF (1u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T2 */
#define IFX_EMEM_TILECT_T2_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T2 */
#define IFX_EMEM_TILECT_T2_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T2 */
#define IFX_EMEM_TILECT_T2_OFF (2u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T3 */
#define IFX_EMEM_TILECT_T3_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T3 */
#define IFX_EMEM_TILECT_T3_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T3 */
#define IFX_EMEM_TILECT_T3_OFF (3u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T4 */
#define IFX_EMEM_TILECT_T4_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T4 */
#define IFX_EMEM_TILECT_T4_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T4 */
#define IFX_EMEM_TILECT_T4_OFF (4u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T5 */
#define IFX_EMEM_TILECT_T5_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T5 */
#define IFX_EMEM_TILECT_T5_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T5 */
#define IFX_EMEM_TILECT_T5_OFF (5u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T6 */
#define IFX_EMEM_TILECT_T6_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T6 */
#define IFX_EMEM_TILECT_T6_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T6 */
#define IFX_EMEM_TILECT_T6_OFF (6u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T7 */
#define IFX_EMEM_TILECT_T7_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T7 */
#define IFX_EMEM_TILECT_T7_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T7 */
#define IFX_EMEM_TILECT_T7_OFF (7u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T8 */
#define IFX_EMEM_TILECT_T8_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T8 */
#define IFX_EMEM_TILECT_T8_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T8 */
#define IFX_EMEM_TILECT_T8_OFF (8u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.T9 */
#define IFX_EMEM_TILECT_T9_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.T9 */
#define IFX_EMEM_TILECT_T9_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.T9 */
#define IFX_EMEM_TILECT_T9_OFF (9u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.XTM0 */
#define IFX_EMEM_TILECT_XTM0_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.XTM0 */
#define IFX_EMEM_TILECT_XTM0_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.XTM0 */
#define IFX_EMEM_TILECT_XTM0_OFF (16u)

/** \brief  Length for Ifx_EMEM_TILECT_Bits.XTM1 */
#define IFX_EMEM_TILECT_XTM1_LEN (1u)

/** \brief  Mask for Ifx_EMEM_TILECT_Bits.XTM1 */
#define IFX_EMEM_TILECT_XTM1_MSK (0x1u)

/** \brief  Offset for Ifx_EMEM_TILECT_Bits.XTM1 */
#define IFX_EMEM_TILECT_XTM1_OFF (17u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE0 */
#define IFX_EMEM_TILESTATE_TILE0_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE0 */
#define IFX_EMEM_TILESTATE_TILE0_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE0 */
#define IFX_EMEM_TILESTATE_TILE0_OFF (0u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE10 */
#define IFX_EMEM_TILESTATE_TILE10_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE10 */
#define IFX_EMEM_TILESTATE_TILE10_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE10 */
#define IFX_EMEM_TILESTATE_TILE10_OFF (20u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE11 */
#define IFX_EMEM_TILESTATE_TILE11_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE11 */
#define IFX_EMEM_TILESTATE_TILE11_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE11 */
#define IFX_EMEM_TILESTATE_TILE11_OFF (22u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE12 */
#define IFX_EMEM_TILESTATE_TILE12_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE12 */
#define IFX_EMEM_TILESTATE_TILE12_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE12 */
#define IFX_EMEM_TILESTATE_TILE12_OFF (24u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE13 */
#define IFX_EMEM_TILESTATE_TILE13_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE13 */
#define IFX_EMEM_TILESTATE_TILE13_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE13 */
#define IFX_EMEM_TILESTATE_TILE13_OFF (26u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE14 */
#define IFX_EMEM_TILESTATE_TILE14_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE14 */
#define IFX_EMEM_TILESTATE_TILE14_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE14 */
#define IFX_EMEM_TILESTATE_TILE14_OFF (28u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE15 */
#define IFX_EMEM_TILESTATE_TILE15_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE15 */
#define IFX_EMEM_TILESTATE_TILE15_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE15 */
#define IFX_EMEM_TILESTATE_TILE15_OFF (30u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE1 */
#define IFX_EMEM_TILESTATE_TILE1_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE1 */
#define IFX_EMEM_TILESTATE_TILE1_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE1 */
#define IFX_EMEM_TILESTATE_TILE1_OFF (2u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE2 */
#define IFX_EMEM_TILESTATE_TILE2_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE2 */
#define IFX_EMEM_TILESTATE_TILE2_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE2 */
#define IFX_EMEM_TILESTATE_TILE2_OFF (4u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE3 */
#define IFX_EMEM_TILESTATE_TILE3_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE3 */
#define IFX_EMEM_TILESTATE_TILE3_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE3 */
#define IFX_EMEM_TILESTATE_TILE3_OFF (6u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE4 */
#define IFX_EMEM_TILESTATE_TILE4_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE4 */
#define IFX_EMEM_TILESTATE_TILE4_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE4 */
#define IFX_EMEM_TILESTATE_TILE4_OFF (8u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE5 */
#define IFX_EMEM_TILESTATE_TILE5_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE5 */
#define IFX_EMEM_TILESTATE_TILE5_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE5 */
#define IFX_EMEM_TILESTATE_TILE5_OFF (10u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE6 */
#define IFX_EMEM_TILESTATE_TILE6_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE6 */
#define IFX_EMEM_TILESTATE_TILE6_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE6 */
#define IFX_EMEM_TILESTATE_TILE6_OFF (12u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE7 */
#define IFX_EMEM_TILESTATE_TILE7_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE7 */
#define IFX_EMEM_TILESTATE_TILE7_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE7 */
#define IFX_EMEM_TILESTATE_TILE7_OFF (14u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE8 */
#define IFX_EMEM_TILESTATE_TILE8_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE8 */
#define IFX_EMEM_TILESTATE_TILE8_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE8 */
#define IFX_EMEM_TILESTATE_TILE8_OFF (16u)

/** \brief  Length for Ifx_EMEM_TILESTATE_Bits.TILE9 */
#define IFX_EMEM_TILESTATE_TILE9_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATE_Bits.TILE9 */
#define IFX_EMEM_TILESTATE_TILE9_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATE_Bits.TILE9 */
#define IFX_EMEM_TILESTATE_TILE9_OFF (18u)

/** \brief  Length for Ifx_EMEM_TILESTATEXM_Bits.XCM0 */
#define IFX_EMEM_TILESTATEXM_XCM0_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATEXM_Bits.XCM0 */
#define IFX_EMEM_TILESTATEXM_XCM0_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATEXM_Bits.XCM0 */
#define IFX_EMEM_TILESTATEXM_XCM0_OFF (0u)

/** \brief  Length for Ifx_EMEM_TILESTATEXM_Bits.XTM0 */
#define IFX_EMEM_TILESTATEXM_XTM0_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATEXM_Bits.XTM0 */
#define IFX_EMEM_TILESTATEXM_XTM0_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATEXM_Bits.XTM0 */
#define IFX_EMEM_TILESTATEXM_XTM0_OFF (16u)

/** \brief  Length for Ifx_EMEM_TILESTATEXM_Bits.XTM1 */
#define IFX_EMEM_TILESTATEXM_XTM1_LEN (2u)

/** \brief  Mask for Ifx_EMEM_TILESTATEXM_Bits.XTM1 */
#define IFX_EMEM_TILESTATEXM_XTM1_MSK (0x3u)

/** \brief  Offset for Ifx_EMEM_TILESTATEXM_Bits.XTM1 */
#define IFX_EMEM_TILESTATEXM_XTM1_OFF (18u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXEMEM_BF_H */
