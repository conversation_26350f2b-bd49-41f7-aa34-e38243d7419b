	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc42000a --dep-file=zf_device_dl1b.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_dl1b.src ../libraries/zf_device/zf_device_dl1b.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_dl1b.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_dl1b.dl1b_get_distance',code,cluster('dl1b_get_distance')
	.sect	'.text.zf_device_dl1b.dl1b_get_distance'
	.align	2
	
	.global	dl1b_get_distance
; Function dl1b_get_distance
.L24:
dl1b_get_distance:	.type	func
	sub.a	a10,#8
.L71:
	movh.a	a15,#@his(dl1b_init_flag)
	lea	a15,[a15]@los(dl1b_init_flag)
	ld.bu	d15,[a15]
.L90:
	jeq	d15,#0,.L2
.L57:
	movh.a	a15,#@his(.1.ini)
	lea	a15,[a15]@los(.1.ini)
	lea	a15,[a15]0
.L91:
	lea	a2,[a10]0
	mov.a	a4,#2
.L3:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L3
.L92:
	mov	d15,#0
.L93:
	st.b	[a10],d15
.L94:
	mov	d15,#49
.L95:
	st.b	[a10]1,d15
.L96:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
	lea	a5,[a10]0
	mov	d4,#2
	lea	a6,[a10]2
	mov	d5,#1
	call	soft_iic_transfer_8bit_array
.L97:
	ld.bu	d15,[a10]2
.L98:
	jeq	d15,#0,.L4
.L99:
	mov	d15,#0
.L100:
	st.b	[a10],d15
.L101:
	mov	d15,#134
.L102:
	st.b	[a10]1,d15
.L103:
	mov	d15,#1
.L104:
	st.b	[a10]2,d15
.L105:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
	lea	a5,[a10]0
	mov	d4,#3
	lea	a6,[a10]0
	mov	d5,#0
	call	soft_iic_transfer_8bit_array
.L106:
	mov	d15,#0
.L107:
	st.b	[a10],d15
.L108:
	mov	d15,#137
.L109:
	st.b	[a10]1,d15
.L110:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
	lea	a5,[a10]0
	mov	d4,#2
	lea	a6,[a10]2
	mov	d5,#1
	call	soft_iic_transfer_8bit_array
.L111:
	ld.bu	d15,[a10]2
.L112:
	mov	d0,#137
.L113:
	jne	d15,d0,.L5
.L114:
	mov	d15,#0
.L115:
	st.b	[a10],d15
.L116:
	mov	d15,#150
.L117:
	st.b	[a10]1,d15
.L118:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
	lea	a5,[a10]0
	mov	d4,#2
	lea	a6,[a10]0
	mov	d5,#2
	call	soft_iic_transfer_8bit_array
.L119:
	ld.bu	d15,[a10]
.L72:
	sha	d0,d15,#8
.L120:
	ld.bu	d15,[a10]1
.L73:
	or	d0,d15
	extr	d15,d0,#0,#16
.L74:
	mov	d0,#4000
.L121:
	jlt	d0,d15,.L6
.L122:
	jge	d15,#0,.L7
.L6:
	movh.a	a15,#@his(dl1b_distance_mm)
	lea	a15,[a15]@los(dl1b_distance_mm)
.L123:
	mov	d15,#8192
.L75:
	st.h	[a15],d15
.L124:
	movh.a	a15,#@his(dl1b_finsh_flag)
	lea	a15,[a15]@los(dl1b_finsh_flag)
.L125:
	mov	d15,#0
.L126:
	st.b	[a15],d15
.L127:
	j	.L8
.L7:
	movh.a	a15,#@his(dl1b_distance_mm)
	lea	a15,[a15]@los(dl1b_distance_mm)
.L128:
	st.h	[a15],d15
.L129:
	movh.a	a15,#@his(dl1b_finsh_flag)
	lea	a15,[a15]@los(dl1b_finsh_flag)
.L130:
	mov	d15,#1
.L76:
	st.b	[a15],d15
.L8:
	j	.L9
.L5:
	movh.a	a15,#@his(dl1b_distance_mm)
	lea	a15,[a15]@los(dl1b_distance_mm)
.L131:
	mov	d15,#8192
.L132:
	st.h	[a15],d15
.L133:
	movh.a	a15,#@his(dl1b_finsh_flag)
	lea	a15,[a15]@los(dl1b_finsh_flag)
.L134:
	mov	d15,#0
.L135:
	st.b	[a15],d15
.L9:
	j	.L10
.L4:
	movh.a	a15,#@his(dl1b_distance_mm)
	lea	a15,[a15]@los(dl1b_distance_mm)
.L136:
	mov	d15,#8192
.L137:
	st.h	[a15],d15
.L138:
	movh.a	a15,#@his(dl1b_finsh_flag)
	lea	a15,[a15]@los(dl1b_finsh_flag)
.L139:
	mov	d15,#0
.L140:
	st.b	[a15],d15
.L10:
.L2:
	ret
.L56:
	
__dl1b_get_distance_function_end:
	.size	dl1b_get_distance,__dl1b_get_distance_function_end-dl1b_get_distance
.L37:
	; End of function
	
	.sdecl	'.text.zf_device_dl1b.dl1b_int_handler',code,cluster('dl1b_int_handler')
	.sect	'.text.zf_device_dl1b.dl1b_int_handler'
	.align	2
	
	.global	dl1b_int_handler
; Function dl1b_int_handler
.L26:
dl1b_int_handler:	.type	func
	ret
.L62:
	
__dl1b_int_handler_function_end:
	.size	dl1b_int_handler,__dl1b_int_handler_function_end-dl1b_int_handler
.L42:
	; End of function
	
	.sdecl	'.text.zf_device_dl1b.dl1b_init',code,cluster('dl1b_init')
	.sect	'.text.zf_device_dl1b.dl1b_init'
	.align	2
	
	.global	dl1b_init
; Function dl1b_init
.L28:
dl1b_init:	.type	func
	sub.a	a10,#144
.L77:
	mov	d8,#0
.L78:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
.L149:
	mov	d4,#41
.L150:
	mov	d5,#10
.L151:
	mov	d6,#1060
.L152:
	mov	d7,#1061
	call	soft_iic_init
.L153:
	mov	d4,#650
.L154:
	mov	d5,#1
.L155:
	mov	d6,#1
.L156:
	mov	d7,#3
	call	gpio_init
.L11:
	mov	d4,#50
	call	system_delay_ms
.L157:
	mov	d4,#650
	call	get_port
	add.a	a2,#4
	movh	d15,#1024
	st.w	[a2],d15
.L158:
	mov	d4,#10
	call	system_delay_ms
.L159:
	mov	d4,#650
	call	get_port
	add.a	a2,#4
	mov	d15,#1024
	st.w	[a2],d15
.L160:
	mov	d4,#50
	call	system_delay_ms
.L161:
	mov	d15,#0
.L162:
	st.b	[a10],d15
.L163:
	mov	d15,#229
.L164:
	st.b	[a10]1,d15
.L165:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
	lea	a5,[a10]0
	mov	d4,#2
	lea	a6,[a10]2
	mov	d5,#1
	call	soft_iic_transfer_8bit_array
.L166:
	ld.bu	d15,[a10]2
.L167:
	jz.t	d15:0,.L12
.L168:
	mov	d9,#0
.L80:
	j	.L13
.L12:
	mov	d9,#1
.L13:
	jne	d9,#1,.L14
.L169:
	j	.L15
.L14:
	mov	d0,#0
.L170:
	st.b	[a10],d0
.L171:
	mov	d15,#1
.L172:
	st.b	[a10]1,d15
.L173:
	lea	a4,[a10]2
.L174:
	movh.a	a5,#@his(dl1b_config_file)
	lea	a5,[a5]@los(dl1b_config_file)
.L175:
	mov	d4,#135
	call	memcpy
.L176:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
	lea	a5,[a10]0
	mov	d4,#137
	lea	a6,[a10]0
	mov	d5,#0
	call	soft_iic_transfer_8bit_array
.L177:
	j	.L16
.L17:
	mov	d0,#0
.L178:
	st.b	[a10],d0
.L179:
	mov	d15,#49
.L180:
	st.b	[a10]1,d15
.L181:
	movh.a	a4,#@his(dl1b_iic_struct)
	lea	a4,[a4]@los(dl1b_iic_struct)
	lea	a5,[a10]0
	mov	d4,#2
	lea	a6,[a10]2
	mov	d5,#1
	call	soft_iic_transfer_8bit_array
.L182:
	ld.bu	d15,[a10]2
.L183:
	jnz.t	d15:0,.L18
.L184:
	j	.L19
.L18:
	mov	d0,d8
	add	d8,#1
.L79:
	extr.u	d8,d8,#0,#16
.L81:
	mov	d15,#1000
.L185:
	jge.u	d15,d0,.L20
.L186:
	mov	d9,#1
.L187:
	j	.L21
.L20:
	mov	d4,#1
	call	system_delay_ms
.L16:
	j	.L17
.L21:
.L19:
	movh.a	a15,#@his(dl1b_init_flag)
	lea	a15,[a15]@los(dl1b_init_flag)
.L188:
	mov	d15,#1
.L189:
	st.b	[a15],d15
.L15:
	mov	d4,#2
.L190:
	movh.a	a4,#@his(dl1b_int_handler)
	lea	a4,[a4]@los(dl1b_int_handler)
	call	set_tof_type
.L191:
	mov	d2,d9
	j	.L22
.L22:
	ret
.L64:
	
__dl1b_init_function_end:
	.size	dl1b_init,__dl1b_init_function_end-dl1b_init
.L47:
	; End of function
	
	.sdecl	'.data.zf_device_dl1b.dl1b_init_flag',data,cluster('dl1b_init_flag')
	.sect	'.data.zf_device_dl1b.dl1b_init_flag'
	.global	dl1b_init_flag
dl1b_init_flag:	.type	object
	.size	dl1b_init_flag,1
	.space	1
	.sdecl	'.data.zf_device_dl1b.dl1b_finsh_flag',data,cluster('dl1b_finsh_flag')
	.sect	'.data.zf_device_dl1b.dl1b_finsh_flag'
	.global	dl1b_finsh_flag
dl1b_finsh_flag:	.type	object
	.size	dl1b_finsh_flag,1
	.space	1
	.sdecl	'.data.zf_device_dl1b.dl1b_distance_mm',data,cluster('dl1b_distance_mm')
	.sect	'.data.zf_device_dl1b.dl1b_distance_mm'
	.global	dl1b_distance_mm
	.align	2
dl1b_distance_mm:	.type	object
	.size	dl1b_distance_mm,2
	.half	8192
	.sdecl	'.bss.zf_device_dl1b.dl1b_iic_struct',data,cluster('dl1b_iic_struct')
	.sect	'.bss.zf_device_dl1b.dl1b_iic_struct'
	.align	4
dl1b_iic_struct:	.type	object
	.size	dl1b_iic_struct,24
	.space	24
	.sdecl	'.rodata.zf_device_dl1b..1.ini',data,rom
	.sect	'.rodata.zf_device_dl1b..1.ini'
.1.ini:	.type	object
	.size	.1.ini,3
	.space	3
	.calls	'__INDIRECT__','dl1b_int_handler'
	.calls	'dl1b_get_distance','soft_iic_transfer_8bit_array'
	.calls	'dl1b_init','soft_iic_init'
	.calls	'dl1b_init','gpio_init'
	.calls	'dl1b_init','system_delay_ms'
	.calls	'dl1b_init','get_port'
	.calls	'dl1b_init','soft_iic_transfer_8bit_array'
	.calls	'dl1b_init','memcpy'
	.calls	'dl1b_init','set_tof_type'
	.calls	'dl1b_get_distance','',8
	.calls	'dl1b_int_handler','',0
	.extern	memcpy
	.extern	system_delay_ms
	.extern	get_port
	.extern	gpio_init
	.extern	soft_iic_transfer_8bit_array
	.extern	soft_iic_init
	.extern	dl1b_config_file
	.extern	set_tof_type
	.extern	__INDIRECT__
	.calls	'dl1b_init','',144
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L30:
	.word	80617
	.half	3
	.word	.L31
	.byte	4
.L29:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L32
	.byte	2,1,1,3
	.word	202
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	205
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	250
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	262
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	374
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	348
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	380
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	380
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	348
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	505
	.byte	4,2,35,0,0
.L63:
	.byte	7
	.byte	'unsigned char',0,1,8
.L68:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	924
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	601
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	884
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1115
	.byte	4,2,35,8,0,14
	.word	1155
	.byte	3
	.word	1218
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1223
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	658
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1223
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	658
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	658
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1223
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1453
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1769
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2340
	.byte	4,2,35,0,0,15,4
	.word	641
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2468
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2683
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2898
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3335
	.byte	4,2,35,0,0,15,24
	.word	641
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3658
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3962
	.byte	4,2,35,0,0,15,8
	.word	641
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4287
	.byte	4,2,35,0,0,15,12
	.word	641
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4627
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4993
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5279
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5426
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5595
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5767
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	658
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5942
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6116
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6290
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6466
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6622
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6955
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7303
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7427
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7691
	.byte	4,2,35,0,0,15,76
	.word	641
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7944
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8031
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1729
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2300
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2419
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2459
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2643
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2858
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3075
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3295
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2459
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3609
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3649
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3922
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4238
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4278
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4578
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4618
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4953
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5239
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4278
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5386
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5555
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5727
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5902
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6076
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6250
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6426
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6582
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6915
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7263
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4278
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7387
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7636
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7895
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7935
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7991
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8558
	.byte	4,3,35,252,1,0,14
	.word	8598
	.byte	3
	.word	9201
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9206
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	641
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9211
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9206
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	641
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9416
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9597
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	641
	.byte	1,1,6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	9752
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	658
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	641
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	658
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	9752
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	9752
	.byte	19,6,0,0,20
	.word	380
	.byte	21
	.word	374
	.byte	3
	.word	9988
	.byte	20
	.word	9993
	.byte	22
	.byte	'memcpy',0,9,53,17
	.word	380
	.byte	1,1,1,1,23,9,53,33
	.word	9983
	.byte	23,9,53,56
	.word	9998
	.byte	23,9,53,68
	.word	466
	.byte	0,24
	.byte	'system_delay_ms',0,10,46,9,1,1,1,1,5
	.byte	'time',0,10,46,45
	.word	9752
	.byte	0,25
	.word	210
	.byte	26
	.word	236
	.byte	6,0,25
	.word	271
	.byte	26
	.word	303
	.byte	6,0,25
	.word	316
	.byte	6,0,25
	.word	385
	.byte	26
	.word	404
	.byte	6,0,25
	.word	420
	.byte	26
	.word	435
	.byte	26
	.word	449
	.byte	6,0,25
	.word	1228
	.byte	26
	.word	1268
	.byte	26
	.word	1286
	.byte	6,0,25
	.word	1306
	.byte	26
	.word	1344
	.byte	26
	.word	1362
	.byte	6,0,25
	.word	1382
	.byte	26
	.word	1433
	.byte	6,0,25
	.word	9336
	.byte	26
	.word	9368
	.byte	26
	.word	9382
	.byte	26
	.word	9400
	.byte	6,0,25
	.word	9519
	.byte	26
	.word	9547
	.byte	26
	.word	9561
	.byte	26
	.word	9579
	.byte	6,0,25
	.word	9676
	.byte	6,0,25
	.word	9710
	.byte	6,0,25
	.word	9773
	.byte	26
	.word	9814
	.byte	6,0,25
	.word	9833
	.byte	26
	.word	9888
	.byte	6,0,25
	.word	9907
	.byte	26
	.word	9947
	.byte	26
	.word	9964
	.byte	19,6,0,0,17,11,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'get_port',0,11,114,13
	.word	9206
	.byte	1,1,1,1,5
	.byte	'pin',0,11,114,56
	.word	10292
	.byte	0,17,11,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,11,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,24
	.byte	'gpio_init',0,11,143,1,7,1,1,1,1,5
	.byte	'pin',0,11,143,1,40
	.word	10292
	.byte	5
	.byte	'dir',0,11,143,1,59
	.word	12266
	.byte	5
	.byte	'dat',0,11,143,1,70
	.word	641
	.byte	5
	.byte	'pinconf',0,11,143,1,90
	.word	12284
	.byte	0
.L70:
	.byte	27,12,42,9,24,13
	.byte	'scl_pin',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'sda_pin',0
	.word	9752
	.byte	4,2,35,4,13
	.byte	'addr',0
	.word	641
	.byte	1,2,35,8,13
	.byte	'delay',0
	.word	9752
	.byte	4,2,35,10,13
	.byte	'iic_scl',0
	.word	380
	.byte	4,2,35,16,13
	.byte	'iic_sda',0
	.word	380
	.byte	4,2,35,20,0,3
	.word	12447
	.byte	21
	.word	641
	.byte	3
	.word	12555
	.byte	3
	.word	641
	.byte	24
	.byte	'soft_iic_transfer_8bit_array',0,12,77,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,12,77,68
	.word	12550
	.byte	5
	.byte	'write_data',0,12,77,95
	.word	12560
	.byte	5
	.byte	'write_len',0,12,77,114
	.word	9752
	.byte	5
	.byte	'read_data',0,12,77,132,1
	.word	12565
	.byte	5
	.byte	'read_len',0,12,77,150,1
	.word	9752
	.byte	0,24
	.byte	'soft_iic_init',0,12,83,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,12,83,68
	.word	12550
	.byte	5
	.byte	'addr',0,12,83,88
	.word	641
	.byte	5
	.byte	'delay',0,12,83,101
	.word	9752
	.byte	5
	.byte	'scl_pin',0,12,83,122
	.word	10292
	.byte	5
	.byte	'sda_pin',0,12,83,145,1
	.word	10292
	.byte	0
.L58:
	.byte	15,3
	.word	641
	.byte	16,2,0
.L60:
	.byte	7
	.byte	'short int',0,2,5
.L66:
	.byte	15,137,1
	.word	641
	.byte	16,136,1,0,17,13,63,9,1,18
	.byte	'NO_TOF',0,0,18
	.byte	'TOF_DL1A',0,1,18
	.byte	'TOF_DL1B',0,2,0,28
	.byte	'callback_function',0,13,73,16
	.word	205
	.byte	24
	.byte	'set_tof_type',0,13,92,8,1,1,1,1,5
	.byte	'type_set',0,13,92,48
	.word	12840
	.byte	5
	.byte	'exti_callback',0,13,92,76
	.word	12877
	.byte	0,3
	.word	202
	.byte	29
	.byte	'__INDIRECT__',0,14,1,1,1,1,1,28
	.byte	'__wchar_t',0,14,1,1
	.word	12816
	.byte	28
	.byte	'__size_t',0,14,1,1
	.word	466
	.byte	28
	.byte	'__ptrdiff_t',0,14,1,1
	.word	482
	.byte	30,1,3
	.word	13044
	.byte	28
	.byte	'__codeptr',0,14,1,1
	.word	13046
	.byte	28
	.byte	'__intptr_t',0,14,1,1
	.word	482
	.byte	28
	.byte	'__uintptr_t',0,14,1,1
	.word	466
	.byte	28
	.byte	'size_t',0,15,31,25
	.word	466
	.byte	28
	.byte	'_iob_flag_t',0,15,82,25
	.word	658
	.byte	28
	.byte	'boolean',0,16,101,29
	.word	641
	.byte	28
	.byte	'uint8',0,16,105,29
	.word	641
	.byte	28
	.byte	'uint16',0,16,109,29
	.word	658
	.byte	28
	.byte	'uint32',0,16,113,29
	.word	9752
	.byte	28
	.byte	'uint64',0,16,118,29
	.word	348
	.byte	28
	.byte	'sint16',0,16,126,29
	.word	12816
	.byte	7
	.byte	'long int',0,4,5,28
	.byte	'sint32',0,16,131,1,29
	.word	13233
	.byte	7
	.byte	'long long int',0,8,5,28
	.byte	'sint64',0,16,138,1,29
	.word	13261
	.byte	28
	.byte	'float32',0,16,167,1,29
	.word	262
	.byte	28
	.byte	'pvoid',0,17,57,28
	.word	380
	.byte	28
	.byte	'Ifx_TickTime',0,17,79,28
	.word	13261
	.byte	17,17,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,28
	.byte	'Ifx_RxSel',0,17,140,1,3
	.word	13346
	.byte	7
	.byte	'char',0,1,6,28
	.byte	'int8',0,18,54,29
	.word	13484
	.byte	28
	.byte	'int16',0,18,55,29
	.word	12816
	.byte	28
	.byte	'int32',0,18,56,29
	.word	482
	.byte	28
	.byte	'int64',0,18,57,29
	.word	13261
	.byte	17,19,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,28
	.byte	'IfxScu_CCUCON0_CLKSEL',0,19,240,10,3
	.word	13547
	.byte	17,19,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,28
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	13644
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	13766
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	14323
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	14400
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	14536
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	641
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	14816
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	15054
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	15182
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	15425
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	15660
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	15788
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	15888
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	641
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	15988
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	466
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	16196
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	16361
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	16544
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	466
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	16698
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	17062
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	17273
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	17525
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	17643
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	17754
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	17917
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	18080
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	18238
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	10,0,2,35,2,0,28
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	18403
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	641
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	658
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	18732
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	18953
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	19116
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	19388
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	19541
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	19697
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	19859
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	20002
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	20167
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	20312
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	20493
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	20667
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	20827
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	20971
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	21245
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	21384
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	658
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	641
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	641
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	21547
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	21765
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	21928
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	22264
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	641
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	22371
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	22823
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	22922
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	23072
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	466
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	641
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	23221
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	23382
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	658
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	23512
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	23644
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	658
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	23759
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	23870
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	641
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	24028
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	24440
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	658
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	6,0,2,35,3,0,28
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	24541
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	24808
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	24944
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	25055
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	25188
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	25391
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	25747
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	25925
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	26025
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	26395
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	26581
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	26779
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	27012
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	641
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	27164
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	27731
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	28025
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	641
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	641
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	28303
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	28799
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	29112
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	29321
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	29532
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	29964
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	641
	.byte	7,0,2,35,3,0,28
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	30060
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	30320
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	30445
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	30642
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	30795
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	30948
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	31101
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	505
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	680
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	924
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	31356
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	31482
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	31734
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13766
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	31953
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14323
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	32017
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14400
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	32081
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14536
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	32146
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14816
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	32211
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15054
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	32276
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15182
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	32341
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15425
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	32406
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15660
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	32471
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15788
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	32536
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15888
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	32601
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15988
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	32666
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16196
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	32730
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16361
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	32794
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16544
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	32858
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16698
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	32923
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17062
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	32985
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17273
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	33047
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17525
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	33109
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17643
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	33173
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17754
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	33238
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17917
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	33304
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18080
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	33370
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18238
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	33438
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18403
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	33505
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18732
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	33573
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18953
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	33641
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19116
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	33707
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19388
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	33774
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19541
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	33843
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19697
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	33912
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19859
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	33981
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20002
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	34050
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20167
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	34119
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20312
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	34188
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20493
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	34256
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20667
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	34324
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20827
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	34392
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20971
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	34460
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21245
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	34525
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21384
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	34590
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21547
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	34656
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21765
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	34720
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21928
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	34781
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22264
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	34842
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22371
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	34902
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22823
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	34964
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22922
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	35024
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23072
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	35086
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23221
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	35154
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23382
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	35222
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23512
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	35290
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23644
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	35354
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23759
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	35419
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23870
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	35482
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24028
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	35543
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24440
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	35607
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24541
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	35668
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24808
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	35732
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24944
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	35799
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25055
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	35862
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25188
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	35923
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25391
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	35985
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25747
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	36050
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25925
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	36115
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26025
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	36180
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26395
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	36249
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26581
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	36318
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26779
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	36387
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27012
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	36452
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27164
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	36515
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27731
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	36580
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28025
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	36645
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28303
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	36710
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28799
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	36776
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29321
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	36845
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29112
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	36909
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29532
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	36974
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29964
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	37039
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30060
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	37104
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30320
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	37168
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30445
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	37234
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30642
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	37298
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30795
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	37363
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30948
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	37428
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31101
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	37493
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	601
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	884
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1115
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31356
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	37644
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31482
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	37711
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31734
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	37778
	.byte	14
	.word	1155
	.byte	28
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	37843
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	37644
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37711
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37778
	.byte	4,2,35,8,0,14
	.word	37872
	.byte	28
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	37933
	.byte	15,8
	.word	33109
	.byte	16,1,0,15,20
	.word	641
	.byte	16,19,0,15,8
	.word	36452
	.byte	16,1,0,14
	.word	37872
	.byte	15,24
	.word	1155
	.byte	16,1,0,14
	.word	37992
	.byte	15,16
	.word	641
	.byte	16,15,0,15,28
	.word	641
	.byte	16,27,0,15,40
	.word	641
	.byte	16,39,0,15,16
	.word	32923
	.byte	16,3,0,15,16
	.word	34902
	.byte	16,3,0,15,180,3
	.word	641
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4278
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	34842
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2459
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	35543
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	36387
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	35985
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	36050
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	36115
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	36318
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	36180
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	36249
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	32146
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	32211
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	34720
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	34656
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	32276
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	32341
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	32406
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	32471
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	36974
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2459
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	36845
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	32081
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	37168
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	36909
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2459
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	33707
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	37960
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	33173
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	37234
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	32536
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	32601
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	37969
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	35862
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	35024
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	35607
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	35482
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	34964
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	34460
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	33438
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	33238
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	33304
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	37104
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2459
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	36515
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	36710
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	36776
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	37978
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2459
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	32858
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	32730
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	36580
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	36645
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	37987
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	33047
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	38001
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4618
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	37493
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	37428
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	37298
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	37363
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2459
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	35290
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	35354
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	32666
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	35419
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4278
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	37039
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	38006
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	35086
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	35154
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	35222
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	38015
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	35799
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4278
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	34525
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	33370
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	34590
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	33641
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	33505
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2459
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	34188
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	34256
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	34324
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	34392
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	33774
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	33843
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	33912
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	33981
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	34050
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	34119
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	33573
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2459
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	35732
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	35668
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	38024
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	38033
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	32985
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	34781
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	35923
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	38042
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2459
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	32794
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	38051
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	32017
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	31953
	.byte	4,3,35,252,7,0,14
	.word	38062
	.byte	28
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	40052
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,20,45,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_A_Bits',0,20,48,3
	.word	40074
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,20,51,16,4,11
	.byte	'VSS',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	489
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BIV_Bits',0,20,55,3
	.word	40135
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,20,58,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	489
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BTV_Bits',0,20,62,3
	.word	40214
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,20,65,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT_Bits',0,20,69,3
	.word	40300
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,20,72,16,4,11
	.byte	'CM',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	489
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	489
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	489
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL_Bits',0,20,80,3
	.word	40389
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,20,83,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT_Bits',0,20,89,3
	.word	40535
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,20,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID_Bits',0,20,96,3
	.word	40662
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,20,99,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L_Bits',0,20,103,3
	.word	40760
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,20,106,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U_Bits',0,20,110,3
	.word	40853
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,20,113,16,4,11
	.byte	'MODREV',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	489
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID_Bits',0,20,118,3
	.word	40946
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,20,121,16,4,11
	.byte	'XE',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE_Bits',0,20,125,3
	.word	41053
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,20,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT_Bits',0,20,136,1,3
	.word	41140
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,20,139,1,16,4,11
	.byte	'CID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID_Bits',0,20,143,1,3
	.word	41294
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,20,146,1,16,4,11
	.byte	'DATA',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_D_Bits',0,20,149,1,3
	.word	41388
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,20,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	489
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DATR_Bits',0,20,163,1,3
	.word	41451
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,20,166,1,16,4,11
	.byte	'DE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	489
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	489
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	19,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR_Bits',0,20,177,1,3
	.word	41669
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,20,180,1,16,4,11
	.byte	'DTA',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR_Bits',0,20,184,1,3
	.word	41884
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,20,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0_Bits',0,20,192,1,3
	.word	41978
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,20,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2_Bits',0,20,199,1,3
	.word	42094
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,20,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	489
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCX_Bits',0,20,206,1,3
	.word	42195
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,20,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD_Bits',0,20,212,1,3
	.word	42288
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,20,215,1,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR_Bits',0,20,218,1,3
	.word	42368
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,20,221,1,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR_Bits',0,20,233,1,3
	.word	42437
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,20,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	489
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DMS_Bits',0,20,240,1,3
	.word	42666
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,20,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L_Bits',0,20,247,1,3
	.word	42759
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,20,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U_Bits',0,20,254,1,3
	.word	42854
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,20,129,2,16,4,11
	.byte	'RE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE_Bits',0,20,133,2,3
	.word	42949
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,20,136,2,16,4,11
	.byte	'WE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE_Bits',0,20,140,2,3
	.word	43039
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,20,143,2,16,4,11
	.byte	'SRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	489
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	489
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR_Bits',0,20,161,2,3
	.word	43129
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,20,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT_Bits',0,20,172,2,3
	.word	43453
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,20,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FCX_Bits',0,20,180,2,3
	.word	43607
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,20,183,2,16,4,11
	.byte	'TST',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	489
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	489
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,20,202,2,3
	.word	43713
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,205,2,16,4,11
	.byte	'OPC',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,212,2,3
	.word	44062
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,20,215,2,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,20,218,2,3
	.word	44222
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,224,2,3
	.word	44303
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,230,2,3
	.word	44390
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,236,2,3
	.word	44477
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,20,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT_Bits',0,20,243,2,3
	.word	44564
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,20,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	489
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	489
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	489
	.byte	6,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICR_Bits',0,20,253,2,3
	.word	44655
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,20,128,3,16,4,11
	.byte	'ISP',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_ISP_Bits',0,20,131,3,3
	.word	44798
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,20,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_LCX_Bits',0,20,139,3,3
	.word	44864
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,20,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT_Bits',0,20,146,3,3
	.word	44970
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,20,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT_Bits',0,20,153,3,3
	.word	45063
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,20,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT_Bits',0,20,160,3,3
	.word	45156
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,20,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	489
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_PC_Bits',0,20,167,3,3
	.word	45249
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,20,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0_Bits',0,20,175,3,3
	.word	45334
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,20,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1_Bits',0,20,183,3,3
	.word	45450
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,20,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2_Bits',0,20,190,3,3
	.word	45561
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,20,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	489
	.byte	10,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI_Bits',0,20,200,3,3
	.word	45662
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,20,203,3,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR_Bits',0,20,206,3,3
	.word	45792
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,20,209,3,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR_Bits',0,20,221,3,3
	.word	45861
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,20,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	489
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0_Bits',0,20,229,3,3
	.word	46090
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,20,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1_Bits',0,20,237,3,3
	.word	46203
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,20,240,3,16,4,11
	.byte	'PSI',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2_Bits',0,20,244,3,3
	.word	46316
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,20,247,3,16,4,11
	.byte	'FRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	17,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR_Bits',0,20,129,4,3
	.word	46407
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,20,132,4,16,4,11
	.byte	'CDC',0,4
	.word	489
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	489
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	489
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSW_Bits',0,20,147,4,3
	.word	46610
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,20,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	489
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN_Bits',0,20,156,4,3
	.word	46853
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,20,159,4,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON_Bits',0,20,171,4,3
	.word	46981
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,20,174,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,20,177,4,3
	.word	47222
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,20,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,20,183,4,3
	.word	47305
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,186,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,189,4,3
	.word	47396
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,195,4,3
	.word	47487
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,20,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,20,202,4,3
	.word	47586
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,20,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,20,209,4,3
	.word	47693
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,20,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT_Bits',0,20,220,4,3
	.word	47800
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,20,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON_Bits',0,20,231,4,3
	.word	47954
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,20,234,4,16,4,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,20,238,4,3
	.word	48115
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,20,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	489
	.byte	15,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON_Bits',0,20,249,4,3
	.word	48213
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,20,252,4,16,4,11
	.byte	'Timer',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,20,255,4,3
	.word	48385
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,20,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR_Bits',0,20,133,5,3
	.word	48465
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,20,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	489
	.byte	3,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT_Bits',0,20,153,5,3
	.word	48538
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,20,156,5,16,4,11
	.byte	'T0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,20,167,5,3
	.word	48856
	.byte	12,20,175,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40074
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_A',0,20,180,5,3
	.word	49051
	.byte	12,20,183,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40135
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BIV',0,20,188,5,3
	.word	49110
	.byte	12,20,191,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40214
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BTV',0,20,196,5,3
	.word	49171
	.byte	12,20,199,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40300
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT',0,20,204,5,3
	.word	49232
	.byte	12,20,207,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40389
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL',0,20,212,5,3
	.word	49294
	.byte	12,20,215,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40535
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT',0,20,220,5,3
	.word	49357
	.byte	12,20,223,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40662
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID',0,20,228,5,3
	.word	49421
	.byte	12,20,231,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40760
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L',0,20,236,5,3
	.word	49486
	.byte	12,20,239,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40853
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U',0,20,244,5,3
	.word	49549
	.byte	12,20,247,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40946
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID',0,20,252,5,3
	.word	49612
	.byte	12,20,255,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41053
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE',0,20,132,6,3
	.word	49676
	.byte	12,20,135,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41140
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT',0,20,140,6,3
	.word	49738
	.byte	12,20,143,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41294
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID',0,20,148,6,3
	.word	49801
	.byte	12,20,151,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41388
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_D',0,20,156,6,3
	.word	49865
	.byte	12,20,159,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41451
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DATR',0,20,164,6,3
	.word	49924
	.byte	12,20,167,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41669
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR',0,20,172,6,3
	.word	49986
	.byte	12,20,175,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41884
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR',0,20,180,6,3
	.word	50049
	.byte	12,20,183,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41978
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0',0,20,188,6,3
	.word	50113
	.byte	12,20,191,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42094
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2',0,20,196,6,3
	.word	50176
	.byte	12,20,199,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42195
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCX',0,20,204,6,3
	.word	50239
	.byte	12,20,207,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42288
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD',0,20,212,6,3
	.word	50300
	.byte	12,20,215,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42368
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR',0,20,220,6,3
	.word	50363
	.byte	12,20,223,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42437
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR',0,20,228,6,3
	.word	50426
	.byte	12,20,231,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42666
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DMS',0,20,236,6,3
	.word	50489
	.byte	12,20,239,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42759
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L',0,20,244,6,3
	.word	50550
	.byte	12,20,247,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42854
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U',0,20,252,6,3
	.word	50613
	.byte	12,20,255,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42949
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE',0,20,132,7,3
	.word	50676
	.byte	12,20,135,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43039
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE',0,20,140,7,3
	.word	50738
	.byte	12,20,143,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43129
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR',0,20,148,7,3
	.word	50800
	.byte	12,20,151,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43453
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT',0,20,156,7,3
	.word	50862
	.byte	12,20,159,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43607
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FCX',0,20,164,7,3
	.word	50925
	.byte	12,20,167,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43713
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,20,172,7,3
	.word	50986
	.byte	12,20,175,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44062
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,20,180,7,3
	.word	51056
	.byte	12,20,183,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44222
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,20,188,7,3
	.word	51126
	.byte	12,20,191,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44303
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,20,196,7,3
	.word	51195
	.byte	12,20,199,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44390
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,20,204,7,3
	.word	51266
	.byte	12,20,207,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44477
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,20,212,7,3
	.word	51337
	.byte	12,20,215,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44564
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT',0,20,220,7,3
	.word	51408
	.byte	12,20,223,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44655
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICR',0,20,228,7,3
	.word	51470
	.byte	12,20,231,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44798
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ISP',0,20,236,7,3
	.word	51531
	.byte	12,20,239,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44864
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_LCX',0,20,244,7,3
	.word	51592
	.byte	12,20,247,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44970
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT',0,20,252,7,3
	.word	51653
	.byte	12,20,255,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45063
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT',0,20,132,8,3
	.word	51716
	.byte	12,20,135,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45156
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT',0,20,140,8,3
	.word	51779
	.byte	12,20,143,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45249
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PC',0,20,148,8,3
	.word	51842
	.byte	12,20,151,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45334
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0',0,20,156,8,3
	.word	51902
	.byte	12,20,159,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45450
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1',0,20,164,8,3
	.word	51965
	.byte	12,20,167,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45561
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2',0,20,172,8,3
	.word	52028
	.byte	12,20,175,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45662
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI',0,20,180,8,3
	.word	52091
	.byte	12,20,183,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45792
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR',0,20,188,8,3
	.word	52153
	.byte	12,20,191,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45861
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR',0,20,196,8,3
	.word	52216
	.byte	12,20,199,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46090
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0',0,20,204,8,3
	.word	52279
	.byte	12,20,207,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46203
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1',0,20,212,8,3
	.word	52341
	.byte	12,20,215,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46316
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2',0,20,220,8,3
	.word	52403
	.byte	12,20,223,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46407
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR',0,20,228,8,3
	.word	52465
	.byte	12,20,231,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46610
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSW',0,20,236,8,3
	.word	52527
	.byte	12,20,239,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46853
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN',0,20,244,8,3
	.word	52588
	.byte	12,20,247,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46981
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON',0,20,252,8,3
	.word	52651
	.byte	12,20,255,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47222
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA',0,20,132,9,3
	.word	52715
	.byte	12,20,135,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47305
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB',0,20,140,9,3
	.word	52785
	.byte	12,20,143,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47396
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,20,148,9,3
	.word	52855
	.byte	12,20,151,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47487
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,20,156,9,3
	.word	52929
	.byte	12,20,159,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47586
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,20,164,9,3
	.word	53003
	.byte	12,20,167,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47693
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,20,172,9,3
	.word	53073
	.byte	12,20,175,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47800
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT',0,20,180,9,3
	.word	53143
	.byte	12,20,183,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47954
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON',0,20,188,9,3
	.word	53206
	.byte	12,20,191,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48115
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI',0,20,196,9,3
	.word	53270
	.byte	12,20,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48213
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON',0,20,204,9,3
	.word	53336
	.byte	12,20,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48385
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER',0,20,212,9,3
	.word	53401
	.byte	12,20,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48465
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR',0,20,220,9,3
	.word	53468
	.byte	12,20,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48538
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT',0,20,228,9,3
	.word	53532
	.byte	12,20,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48856
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC',0,20,236,9,3
	.word	53596
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,20,247,9,25,8,13
	.byte	'L',0
	.word	49486
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	49549
	.byte	4,2,35,4,0,14
	.word	53662
	.byte	28
	.byte	'Ifx_CPU_CPR',0,20,251,9,3
	.word	53704
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,20,254,9,25,8,13
	.byte	'L',0
	.word	50550
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	50613
	.byte	4,2,35,4,0,14
	.word	53730
	.byte	28
	.byte	'Ifx_CPU_DPR',0,20,130,10,3
	.word	53772
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,20,133,10,25,16,13
	.byte	'LA',0
	.word	53003
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	53073
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	52855
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	52929
	.byte	4,2,35,12,0,14
	.word	53798
	.byte	28
	.byte	'Ifx_CPU_SPROT_RGN',0,20,139,10,3
	.word	53880
	.byte	15,12
	.word	53401
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,20,142,10,25,16,13
	.byte	'CON',0
	.word	53336
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	53912
	.byte	12,2,35,4,0,14
	.word	53921
	.byte	28
	.byte	'Ifx_CPU_TPS',0,20,146,10,3
	.word	53969
	.byte	10
	.byte	'_Ifx_CPU_TR',0,20,149,10,25,8,13
	.byte	'EVT',0
	.word	53532
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	53468
	.byte	4,2,35,4,0,14
	.word	53995
	.byte	28
	.byte	'Ifx_CPU_TR',0,20,153,10,3
	.word	54040
	.byte	15,176,32
	.word	641
	.byte	16,175,32,0,15,208,223,1
	.word	641
	.byte	16,207,223,1,0,15,248,1
	.word	641
	.byte	16,247,1,0,15,244,29
	.word	641
	.byte	16,243,29,0,15,188,3
	.word	641
	.byte	16,187,3,0,15,232,3
	.word	641
	.byte	16,231,3,0,15,252,23
	.word	641
	.byte	16,251,23,0,15,228,63
	.word	641
	.byte	16,227,63,0,15,128,1
	.word	53730
	.byte	16,15,0,14
	.word	54155
	.byte	15,128,31
	.word	641
	.byte	16,255,30,0,15,64
	.word	53662
	.byte	16,7,0,14
	.word	54181
	.byte	15,192,31
	.word	641
	.byte	16,191,31,0,15,16
	.word	49676
	.byte	16,3,0,15,16
	.word	50676
	.byte	16,3,0,15,16
	.word	50738
	.byte	16,3,0,15,208,7
	.word	641
	.byte	16,207,7,0,14
	.word	53921
	.byte	15,240,23
	.word	641
	.byte	16,239,23,0,15,64
	.word	53995
	.byte	16,7,0,14
	.word	54260
	.byte	15,192,23
	.word	641
	.byte	16,191,23,0,15,232,1
	.word	641
	.byte	16,231,1,0,15,180,1
	.word	641
	.byte	16,179,1,0,15,172,1
	.word	641
	.byte	16,171,1,0,15,64
	.word	49865
	.byte	16,15,0,15,64
	.word	641
	.byte	16,63,0,15,64
	.word	49051
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,20,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	54065
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	52588
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	54076
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	53270
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	54089
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	52279
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	52341
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	52403
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	54100
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	50176
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4278
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	52651
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	50800
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2459
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	49924
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	50300
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	50363
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	50426
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3649
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	50113
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	54111
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	52465
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	51965
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	52028
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	51902
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	52153
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	52216
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	54122
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	49357
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	54133
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	50986
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	51126
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	51056
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2459
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	51195
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	51266
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	51337
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	54144
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	54165
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	54170
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	54190
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	54195
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	54206
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	54215
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	54224
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	54233
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	54244
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	54249
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	54269
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	54274
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	49294
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	49232
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	51408
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	51653
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	51716
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	51779
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	54285
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	49986
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2459
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	50862
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	49738
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	53143
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	38015
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	53596
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4618
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	50489
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	50239
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	50049
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	54296
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	52091
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	52527
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	51842
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4278
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	53206
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	49612
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	49421
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	49110
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	49171
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	51531
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	51470
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4278
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	50925
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	51592
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	38006
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	49801
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	54307
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	54318
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	54327
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	54336
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	54327
	.byte	64,4,35,192,255,3,0,14
	.word	54345
	.byte	28
	.byte	'Ifx_CPU',0,20,130,11,3
	.word	56136
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,28
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	56158
	.byte	28
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9597
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,21,45,16,4,11
	.byte	'SRPN',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SRC_SRCR_Bits',0,21,62,3
	.word	56256
	.byte	12,21,70,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56256
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SRC_SRCR',0,21,75,3
	.word	56572
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,21,86,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	56632
	.byte	28
	.byte	'Ifx_SRC_AGBT',0,21,89,3
	.word	56664
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,21,92,25,12,13
	.byte	'TX',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	56572
	.byte	4,2,35,8,0,14
	.word	56690
	.byte	28
	.byte	'Ifx_SRC_ASCLIN',0,21,97,3
	.word	56749
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,21,100,25,4,13
	.byte	'SBSRC',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	56777
	.byte	28
	.byte	'Ifx_SRC_BCUSPB',0,21,103,3
	.word	56814
	.byte	15,64
	.word	56572
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,21,106,25,64,13
	.byte	'INT',0
	.word	56842
	.byte	64,2,35,0,0,14
	.word	56851
	.byte	28
	.byte	'Ifx_SRC_CAN',0,21,109,3
	.word	56883
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,21,112,25,16,13
	.byte	'SR0',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	56572
	.byte	4,2,35,12,0,14
	.word	56908
	.byte	28
	.byte	'Ifx_SRC_CCU6',0,21,118,3
	.word	56980
	.byte	15,8
	.word	56572
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,21,121,25,8,13
	.byte	'SR',0
	.word	57006
	.byte	8,2,35,0,0,14
	.word	57015
	.byte	28
	.byte	'Ifx_SRC_CERBERUS',0,21,124,3
	.word	57051
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,21,127,25,16,13
	.byte	'MI',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	56572
	.byte	4,2,35,12,0,14
	.word	57081
	.byte	28
	.byte	'Ifx_SRC_CIF',0,21,133,1,3
	.word	57154
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,21,136,1,25,4,13
	.byte	'SBSRC',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	57180
	.byte	28
	.byte	'Ifx_SRC_CPU',0,21,139,1,3
	.word	57215
	.byte	15,192,1
	.word	56572
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,21,142,1,25,208,1,13
	.byte	'ERR',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4618
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	57241
	.byte	192,1,2,35,16,0,14
	.word	57251
	.byte	28
	.byte	'Ifx_SRC_DMA',0,21,147,1,3
	.word	57318
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,21,150,1,25,8,13
	.byte	'SRM',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	56572
	.byte	4,2,35,4,0,14
	.word	57344
	.byte	28
	.byte	'Ifx_SRC_DSADC',0,21,154,1,3
	.word	57392
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,21,157,1,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	57420
	.byte	28
	.byte	'Ifx_SRC_EMEM',0,21,160,1,3
	.word	57453
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,21,163,1,25,80,13
	.byte	'INT',0
	.word	57006
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	57006
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	57006
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	57006
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	56572
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	56572
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	38024
	.byte	40,2,35,40,0,14
	.word	57480
	.byte	28
	.byte	'Ifx_SRC_ERAY',0,21,172,1,3
	.word	57607
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,21,175,1,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	57634
	.byte	28
	.byte	'Ifx_SRC_ETH',0,21,178,1,3
	.word	57666
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,21,181,1,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	57692
	.byte	28
	.byte	'Ifx_SRC_FCE',0,21,184,1,3
	.word	57724
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,21,187,1,25,12,13
	.byte	'DONE',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	56572
	.byte	4,2,35,8,0,14
	.word	57750
	.byte	28
	.byte	'Ifx_SRC_FFT',0,21,192,1,3
	.word	57810
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,21,195,1,25,32,13
	.byte	'SR0',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	56572
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	38006
	.byte	16,2,35,16,0,14
	.word	57836
	.byte	28
	.byte	'Ifx_SRC_GPSR',0,21,202,1,3
	.word	57930
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,21,205,1,25,48,13
	.byte	'CIRQ',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	56572
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	56572
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	56572
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3649
	.byte	24,2,35,24,0,14
	.word	57957
	.byte	28
	.byte	'Ifx_SRC_GPT12',0,21,214,1,3
	.word	58074
	.byte	15,12
	.word	56572
	.byte	16,2,0,15,32
	.word	56572
	.byte	16,7,0,15,32
	.word	58111
	.byte	16,0,0,15,88
	.word	641
	.byte	16,87,0,15,108
	.word	56572
	.byte	16,26,0,15,96
	.word	641
	.byte	16,95,0,15,96
	.word	58111
	.byte	16,2,0,15,160,3
	.word	641
	.byte	16,159,3,0,15,64
	.word	58111
	.byte	16,1,0,15,192,3
	.word	641
	.byte	16,191,3,0,15,16
	.word	56572
	.byte	16,3,0,15,64
	.word	58196
	.byte	16,3,0,15,192,2
	.word	641
	.byte	16,191,2,0,15,52
	.word	641
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,21,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	58102
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2459
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	56572
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	56572
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	57006
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4278
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	58120
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	58129
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	58138
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	58147
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	56572
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4618
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	58156
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	58165
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	58156
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	58165
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	58176
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	58185
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	58205
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	58214
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	58102
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	58225
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	58102
	.byte	12,3,35,192,18,0,14
	.word	58234
	.byte	28
	.byte	'Ifx_SRC_GTM',0,21,243,1,3
	.word	58694
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,21,246,1,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	58720
	.byte	28
	.byte	'Ifx_SRC_HSCT',0,21,249,1,3
	.word	58753
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,21,252,1,25,16,13
	.byte	'COK',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	56572
	.byte	4,2,35,12,0,14
	.word	58780
	.byte	28
	.byte	'Ifx_SRC_HSSL',0,21,130,2,3
	.word	58853
	.byte	15,56
	.word	641
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,21,133,2,25,80,13
	.byte	'BREQ',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	56572
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	56572
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	56572
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	58880
	.byte	56,2,35,24,0,14
	.word	58889
	.byte	28
	.byte	'Ifx_SRC_I2C',0,21,142,2,3
	.word	59012
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,21,145,2,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	59038
	.byte	28
	.byte	'Ifx_SRC_LMU',0,21,148,2,3
	.word	59070
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,21,151,2,25,20,13
	.byte	'SR0',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	56572
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	56572
	.byte	4,2,35,16,0,14
	.word	59096
	.byte	28
	.byte	'Ifx_SRC_MSC',0,21,158,2,3
	.word	59181
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,21,161,2,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	59207
	.byte	28
	.byte	'Ifx_SRC_PMU',0,21,164,2,3
	.word	59239
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,21,167,2,25,32,13
	.byte	'SR',0
	.word	58111
	.byte	32,2,35,0,0,14
	.word	59265
	.byte	28
	.byte	'Ifx_SRC_PSI5',0,21,170,2,3
	.word	59298
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,21,173,2,25,32,13
	.byte	'SR',0
	.word	58111
	.byte	32,2,35,0,0,14
	.word	59325
	.byte	28
	.byte	'Ifx_SRC_PSI5S',0,21,176,2,3
	.word	59359
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,21,179,2,25,24,13
	.byte	'TX',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	56572
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	56572
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	56572
	.byte	4,2,35,20,0,14
	.word	59387
	.byte	28
	.byte	'Ifx_SRC_QSPI',0,21,187,2,3
	.word	59480
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,21,190,2,25,4,13
	.byte	'SR',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	59507
	.byte	28
	.byte	'Ifx_SRC_SCR',0,21,193,2,3
	.word	59539
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,21,196,2,25,20,13
	.byte	'DTS',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	58196
	.byte	16,2,35,4,0,14
	.word	59565
	.byte	28
	.byte	'Ifx_SRC_SCU',0,21,200,2,3
	.word	59611
	.byte	15,24
	.word	56572
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,21,203,2,25,24,13
	.byte	'SR',0
	.word	59637
	.byte	24,2,35,0,0,14
	.word	59646
	.byte	28
	.byte	'Ifx_SRC_SENT',0,21,206,2,3
	.word	59679
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,21,209,2,25,12,13
	.byte	'SR',0
	.word	58102
	.byte	12,2,35,0,0,14
	.word	59706
	.byte	28
	.byte	'Ifx_SRC_SMU',0,21,212,2,3
	.word	59738
	.byte	10
	.byte	'_Ifx_SRC_STM',0,21,215,2,25,8,13
	.byte	'SR0',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	56572
	.byte	4,2,35,4,0,14
	.word	59764
	.byte	28
	.byte	'Ifx_SRC_STM',0,21,219,2,3
	.word	59810
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,21,222,2,25,16,13
	.byte	'SR0',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	56572
	.byte	4,2,35,12,0,14
	.word	59836
	.byte	28
	.byte	'Ifx_SRC_VADCCG',0,21,228,2,3
	.word	59911
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,21,231,2,25,16,13
	.byte	'SR0',0
	.word	56572
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	56572
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	56572
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	56572
	.byte	4,2,35,12,0,14
	.word	59940
	.byte	28
	.byte	'Ifx_SRC_VADCG',0,21,237,2,3
	.word	60014
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,21,240,2,25,4,13
	.byte	'SRC',0
	.word	56572
	.byte	4,2,35,0,0,14
	.word	60042
	.byte	28
	.byte	'Ifx_SRC_XBAR',0,21,243,2,3
	.word	60076
	.byte	15,4
	.word	56632
	.byte	16,0,0,14
	.word	60103
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,21,128,3,25,4,13
	.byte	'AGBT',0
	.word	60112
	.byte	4,2,35,0,0,14
	.word	60117
	.byte	28
	.byte	'Ifx_SRC_GAGBT',0,21,131,3,3
	.word	60153
	.byte	15,48
	.word	56690
	.byte	16,3,0,14
	.word	60181
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,21,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	60190
	.byte	48,2,35,0,0,14
	.word	60195
	.byte	28
	.byte	'Ifx_SRC_GASCLIN',0,21,137,3,3
	.word	60235
	.byte	14
	.word	56777
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,21,140,3,25,4,13
	.byte	'SPB',0
	.word	60265
	.byte	4,2,35,0,0,14
	.word	60270
	.byte	28
	.byte	'Ifx_SRC_GBCU',0,21,143,3,3
	.word	60304
	.byte	15,64
	.word	56851
	.byte	16,0,0,14
	.word	60331
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,21,146,3,25,64,13
	.byte	'CAN',0
	.word	60340
	.byte	64,2,35,0,0,14
	.word	60345
	.byte	28
	.byte	'Ifx_SRC_GCAN',0,21,149,3,3
	.word	60379
	.byte	15,32
	.word	56908
	.byte	16,1,0,14
	.word	60406
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,21,152,3,25,32,13
	.byte	'CCU6',0
	.word	60415
	.byte	32,2,35,0,0,14
	.word	60420
	.byte	28
	.byte	'Ifx_SRC_GCCU6',0,21,155,3,3
	.word	60456
	.byte	14
	.word	57015
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,21,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	60484
	.byte	8,2,35,0,0,14
	.word	60489
	.byte	28
	.byte	'Ifx_SRC_GCERBERUS',0,21,161,3,3
	.word	60533
	.byte	15,16
	.word	57081
	.byte	16,0,0,14
	.word	60565
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,21,164,3,25,16,13
	.byte	'CIF',0
	.word	60574
	.byte	16,2,35,0,0,14
	.word	60579
	.byte	28
	.byte	'Ifx_SRC_GCIF',0,21,167,3,3
	.word	60613
	.byte	15,8
	.word	57180
	.byte	16,1,0,14
	.word	60640
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,21,170,3,25,8,13
	.byte	'CPU',0
	.word	60649
	.byte	8,2,35,0,0,14
	.word	60654
	.byte	28
	.byte	'Ifx_SRC_GCPU',0,21,173,3,3
	.word	60688
	.byte	15,208,1
	.word	57251
	.byte	16,0,0,14
	.word	60715
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,21,176,3,25,208,1,13
	.byte	'DMA',0
	.word	60725
	.byte	208,1,2,35,0,0,14
	.word	60730
	.byte	28
	.byte	'Ifx_SRC_GDMA',0,21,179,3,3
	.word	60766
	.byte	14
	.word	57344
	.byte	14
	.word	57344
	.byte	14
	.word	57344
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,21,182,3,25,32,13
	.byte	'DSADC0',0
	.word	60793
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4278
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	60798
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	60803
	.byte	8,2,35,24,0,14
	.word	60808
	.byte	28
	.byte	'Ifx_SRC_GDSADC',0,21,188,3,3
	.word	60899
	.byte	15,4
	.word	57420
	.byte	16,0,0,14
	.word	60928
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,21,191,3,25,4,13
	.byte	'EMEM',0
	.word	60937
	.byte	4,2,35,0,0,14
	.word	60942
	.byte	28
	.byte	'Ifx_SRC_GEMEM',0,21,194,3,3
	.word	60978
	.byte	15,80
	.word	57480
	.byte	16,0,0,14
	.word	61006
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,21,197,3,25,80,13
	.byte	'ERAY',0
	.word	61015
	.byte	80,2,35,0,0,14
	.word	61020
	.byte	28
	.byte	'Ifx_SRC_GERAY',0,21,200,3,3
	.word	61056
	.byte	15,4
	.word	57634
	.byte	16,0,0,14
	.word	61084
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,21,203,3,25,4,13
	.byte	'ETH',0
	.word	61093
	.byte	4,2,35,0,0,14
	.word	61098
	.byte	28
	.byte	'Ifx_SRC_GETH',0,21,206,3,3
	.word	61132
	.byte	15,4
	.word	57692
	.byte	16,0,0,14
	.word	61159
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,21,209,3,25,4,13
	.byte	'FCE',0
	.word	61168
	.byte	4,2,35,0,0,14
	.word	61173
	.byte	28
	.byte	'Ifx_SRC_GFCE',0,21,212,3,3
	.word	61207
	.byte	15,12
	.word	57750
	.byte	16,0,0,14
	.word	61234
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,21,215,3,25,12,13
	.byte	'FFT',0
	.word	61243
	.byte	12,2,35,0,0,14
	.word	61248
	.byte	28
	.byte	'Ifx_SRC_GFFT',0,21,218,3,3
	.word	61282
	.byte	15,64
	.word	57836
	.byte	16,1,0,14
	.word	61309
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,21,221,3,25,64,13
	.byte	'GPSR',0
	.word	61318
	.byte	64,2,35,0,0,14
	.word	61323
	.byte	28
	.byte	'Ifx_SRC_GGPSR',0,21,224,3,3
	.word	61359
	.byte	15,48
	.word	57957
	.byte	16,0,0,14
	.word	61387
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,21,227,3,25,48,13
	.byte	'GPT12',0
	.word	61396
	.byte	48,2,35,0,0,14
	.word	61401
	.byte	28
	.byte	'Ifx_SRC_GGPT12',0,21,230,3,3
	.word	61439
	.byte	15,204,18
	.word	58234
	.byte	16,0,0,14
	.word	61468
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,21,233,3,25,204,18,13
	.byte	'GTM',0
	.word	61478
	.byte	204,18,2,35,0,0,14
	.word	61483
	.byte	28
	.byte	'Ifx_SRC_GGTM',0,21,236,3,3
	.word	61519
	.byte	15,4
	.word	58720
	.byte	16,0,0,14
	.word	61546
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,21,239,3,25,4,13
	.byte	'HSCT',0
	.word	61555
	.byte	4,2,35,0,0,14
	.word	61560
	.byte	28
	.byte	'Ifx_SRC_GHSCT',0,21,242,3,3
	.word	61596
	.byte	15,64
	.word	58780
	.byte	16,3,0,14
	.word	61624
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,21,245,3,25,68,13
	.byte	'HSSL',0
	.word	61633
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	56572
	.byte	4,2,35,64,0,14
	.word	61638
	.byte	28
	.byte	'Ifx_SRC_GHSSL',0,21,249,3,3
	.word	61687
	.byte	15,80
	.word	58889
	.byte	16,0,0,14
	.word	61715
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,21,252,3,25,80,13
	.byte	'I2C',0
	.word	61724
	.byte	80,2,35,0,0,14
	.word	61729
	.byte	28
	.byte	'Ifx_SRC_GI2C',0,21,255,3,3
	.word	61763
	.byte	15,4
	.word	59038
	.byte	16,0,0,14
	.word	61790
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,21,130,4,25,4,13
	.byte	'LMU',0
	.word	61799
	.byte	4,2,35,0,0,14
	.word	61804
	.byte	28
	.byte	'Ifx_SRC_GLMU',0,21,133,4,3
	.word	61838
	.byte	15,40
	.word	59096
	.byte	16,1,0,14
	.word	61865
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,21,136,4,25,40,13
	.byte	'MSC',0
	.word	61874
	.byte	40,2,35,0,0,14
	.word	61879
	.byte	28
	.byte	'Ifx_SRC_GMSC',0,21,139,4,3
	.word	61913
	.byte	15,8
	.word	59207
	.byte	16,1,0,14
	.word	61940
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,21,142,4,25,8,13
	.byte	'PMU',0
	.word	61949
	.byte	8,2,35,0,0,14
	.word	61954
	.byte	28
	.byte	'Ifx_SRC_GPMU',0,21,145,4,3
	.word	61988
	.byte	15,32
	.word	59265
	.byte	16,0,0,14
	.word	62015
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,21,148,4,25,32,13
	.byte	'PSI5',0
	.word	62024
	.byte	32,2,35,0,0,14
	.word	62029
	.byte	28
	.byte	'Ifx_SRC_GPSI5',0,21,151,4,3
	.word	62065
	.byte	15,32
	.word	59325
	.byte	16,0,0,14
	.word	62093
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,21,154,4,25,32,13
	.byte	'PSI5S',0
	.word	62102
	.byte	32,2,35,0,0,14
	.word	62107
	.byte	28
	.byte	'Ifx_SRC_GPSI5S',0,21,157,4,3
	.word	62145
	.byte	15,96
	.word	59387
	.byte	16,3,0,14
	.word	62174
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,21,160,4,25,96,13
	.byte	'QSPI',0
	.word	62183
	.byte	96,2,35,0,0,14
	.word	62188
	.byte	28
	.byte	'Ifx_SRC_GQSPI',0,21,163,4,3
	.word	62224
	.byte	15,4
	.word	59507
	.byte	16,0,0,14
	.word	62252
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,21,166,4,25,4,13
	.byte	'SCR',0
	.word	62261
	.byte	4,2,35,0,0,14
	.word	62266
	.byte	28
	.byte	'Ifx_SRC_GSCR',0,21,169,4,3
	.word	62300
	.byte	14
	.word	59565
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,21,172,4,25,20,13
	.byte	'SCU',0
	.word	62327
	.byte	20,2,35,0,0,14
	.word	62332
	.byte	28
	.byte	'Ifx_SRC_GSCU',0,21,175,4,3
	.word	62366
	.byte	15,24
	.word	59646
	.byte	16,0,0,14
	.word	62393
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,21,178,4,25,24,13
	.byte	'SENT',0
	.word	62402
	.byte	24,2,35,0,0,14
	.word	62407
	.byte	28
	.byte	'Ifx_SRC_GSENT',0,21,181,4,3
	.word	62443
	.byte	15,12
	.word	59706
	.byte	16,0,0,14
	.word	62471
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,21,184,4,25,12,13
	.byte	'SMU',0
	.word	62480
	.byte	12,2,35,0,0,14
	.word	62485
	.byte	28
	.byte	'Ifx_SRC_GSMU',0,21,187,4,3
	.word	62519
	.byte	15,16
	.word	59764
	.byte	16,1,0,14
	.word	62546
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,21,190,4,25,16,13
	.byte	'STM',0
	.word	62555
	.byte	16,2,35,0,0,14
	.word	62560
	.byte	28
	.byte	'Ifx_SRC_GSTM',0,21,193,4,3
	.word	62594
	.byte	15,64
	.word	59940
	.byte	16,3,0,14
	.word	62621
	.byte	15,224,1
	.word	641
	.byte	16,223,1,0,15,32
	.word	59836
	.byte	16,1,0,14
	.word	62646
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,21,196,4,25,192,2,13
	.byte	'G',0
	.word	62630
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	62635
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	62655
	.byte	32,3,35,160,2,0,14
	.word	62660
	.byte	28
	.byte	'Ifx_SRC_GVADC',0,21,201,4,3
	.word	62729
	.byte	14
	.word	60042
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,21,204,4,25,4,13
	.byte	'XBAR',0
	.word	62757
	.byte	4,2,35,0,0,14
	.word	62762
	.byte	28
	.byte	'Ifx_SRC_GXBAR',0,21,207,4,3
	.word	62798
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,22,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_STM_ACCEN0_Bits',0,22,79,3
	.word	62826
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,22,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_ACCEN1_Bits',0,22,85,3
	.word	63383
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,22,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_CAP_Bits',0,22,91,3
	.word	63460
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,22,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_CAPSV_Bits',0,22,97,3
	.word	63532
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,22,100,16,4,11
	.byte	'DISR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_STM_CLC_Bits',0,22,107,3
	.word	63608
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,22,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_STM_CMCON_Bits',0,22,120,3
	.word	63749
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,22,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_CMP_Bits',0,22,126,3
	.word	63967
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,22,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	466
	.byte	25,0,2,35,0,0,28
	.byte	'Ifx_STM_ICR_Bits',0,22,139,1,3
	.word	64034
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,22,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_STM_ID_Bits',0,22,147,1,3
	.word	64237
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,22,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_STM_ISCR_Bits',0,22,157,1,3
	.word	64344
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,22,160,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_STM_KRST0_Bits',0,22,165,1,3
	.word	64495
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,22,168,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_STM_KRST1_Bits',0,22,172,1,3
	.word	64606
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,22,175,1,16,4,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_STM_KRSTCLR_Bits',0,22,179,1,3
	.word	64698
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,22,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_STM_OCS_Bits',0,22,189,1,3
	.word	64794
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,22,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM0_Bits',0,22,195,1,3
	.word	64940
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,22,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM0SV_Bits',0,22,201,1,3
	.word	65012
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,22,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM1_Bits',0,22,207,1,3
	.word	65088
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,22,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM2_Bits',0,22,213,1,3
	.word	65160
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,22,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM3_Bits',0,22,219,1,3
	.word	65232
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,22,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM4_Bits',0,22,225,1,3
	.word	65305
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,22,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM5_Bits',0,22,231,1,3
	.word	65378
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,22,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_STM_TIM6_Bits',0,22,237,1,3
	.word	65451
	.byte	12,22,245,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62826
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ACCEN0',0,22,250,1,3
	.word	65524
	.byte	12,22,253,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63383
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ACCEN1',0,22,130,2,3
	.word	65588
	.byte	12,22,133,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63460
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CAP',0,22,138,2,3
	.word	65652
	.byte	12,22,141,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63532
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CAPSV',0,22,146,2,3
	.word	65713
	.byte	12,22,149,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63608
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CLC',0,22,154,2,3
	.word	65776
	.byte	12,22,157,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63749
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CMCON',0,22,162,2,3
	.word	65837
	.byte	12,22,165,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63967
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_CMP',0,22,170,2,3
	.word	65900
	.byte	12,22,173,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64034
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ICR',0,22,178,2,3
	.word	65961
	.byte	12,22,181,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64237
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ID',0,22,186,2,3
	.word	66022
	.byte	12,22,189,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64344
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_ISCR',0,22,194,2,3
	.word	66082
	.byte	12,22,197,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64495
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_KRST0',0,22,202,2,3
	.word	66144
	.byte	12,22,205,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64606
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_KRST1',0,22,210,2,3
	.word	66207
	.byte	12,22,213,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64698
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_KRSTCLR',0,22,218,2,3
	.word	66270
	.byte	12,22,221,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64794
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_OCS',0,22,226,2,3
	.word	66335
	.byte	12,22,229,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64940
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM0',0,22,234,2,3
	.word	66396
	.byte	12,22,237,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65012
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM0SV',0,22,242,2,3
	.word	66458
	.byte	12,22,245,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65088
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM1',0,22,250,2,3
	.word	66522
	.byte	12,22,253,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65160
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM2',0,22,130,3,3
	.word	66584
	.byte	12,22,133,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65232
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM3',0,22,138,3,3
	.word	66646
	.byte	12,22,141,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65305
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM4',0,22,146,3,3
	.word	66708
	.byte	12,22,149,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65378
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM5',0,22,154,3,3
	.word	66770
	.byte	12,22,157,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65451
	.byte	4,2,35,0,0,28
	.byte	'Ifx_STM_TIM6',0,22,162,3,3
	.word	66832
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,23,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,23,79,3
	.word	66894
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,23,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,23,85,3
	.word	67455
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,23,88,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,23,95,3
	.word	67536
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,23,98,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,23,111,3
	.word	67689
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,23,114,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,23,121,3
	.word	67937
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,23,124,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0_Bits',0,23,128,1,3
	.word	68083
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,23,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM1_Bits',0,23,136,1,3
	.word	68181
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,23,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM2_Bits',0,23,144,1,3
	.word	68297
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,23,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRD_Bits',0,23,153,1,3
	.word	68413
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,23,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRP_Bits',0,23,162,1,3
	.word	68553
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,23,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCW_Bits',0,23,171,1,3
	.word	68693
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,23,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	658
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FCON_Bits',0,23,193,1,3
	.word	68832
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,23,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FPRO_Bits',0,23,218,1,3
	.word	69194
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,23,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FSR_Bits',0,23,254,1,3
	.word	69635
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,23,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_ID_Bits',0,23,134,2,3
	.word	70241
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,23,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	658
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARD_Bits',0,23,147,2,3
	.word	70352
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,23,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARP_Bits',0,23,159,2,3
	.word	70566
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,23,162,2,16,4,11
	.byte	'L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCOND_Bits',0,23,179,2,3
	.word	70753
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,23,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,23,188,2,3
	.word	71077
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,23,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,23,199,2,3
	.word	71220
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,219,2,3
	.word	71409
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,23,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,23,254,2,3
	.word	71772
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,23,129,3,16,4,11
	.byte	'S0L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONP_Bits',0,23,160,3,3
	.word	72367
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,23,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,23,194,3,3
	.word	72891
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,23,197,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,23,201,3,3
	.word	73473
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,23,204,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,23,208,3,3
	.word	73575
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,23,211,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,23,215,3,3
	.word	73677
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,23,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	466
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD_Bits',0,23,222,3,3
	.word	73779
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,23,225,3,16,4,11
	.byte	'STRT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	658
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_RRCT_Bits',0,23,236,3,3
	.word	73873
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,23,239,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0_Bits',0,23,242,3,3
	.word	74083
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,23,245,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1_Bits',0,23,248,3,3
	.word	74156
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,23,251,3,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,23,130,4,3
	.word	74229
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,23,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,23,137,4,3
	.word	74384
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,23,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,23,147,4,3
	.word	74489
	.byte	12,23,155,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66894
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN0',0,23,160,4,3
	.word	74637
	.byte	12,23,163,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67455
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1',0,23,168,4,3
	.word	74703
	.byte	12,23,171,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67536
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG',0,23,176,4,3
	.word	74769
	.byte	12,23,179,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67689
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT',0,23,184,4,3
	.word	74837
	.byte	12,23,187,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67937
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_TOP',0,23,192,4,3
	.word	74906
	.byte	12,23,195,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68083
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0',0,23,200,4,3
	.word	74974
	.byte	12,23,203,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68181
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM1',0,23,208,4,3
	.word	75039
	.byte	12,23,211,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68297
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM2',0,23,216,4,3
	.word	75104
	.byte	12,23,219,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68413
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRD',0,23,224,4,3
	.word	75169
	.byte	12,23,227,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68553
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRP',0,23,232,4,3
	.word	75234
	.byte	12,23,235,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68693
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCW',0,23,240,4,3
	.word	75299
	.byte	12,23,243,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68832
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FCON',0,23,248,4,3
	.word	75363
	.byte	12,23,251,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69194
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FPRO',0,23,128,5,3
	.word	75427
	.byte	12,23,131,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69635
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FSR',0,23,136,5,3
	.word	75491
	.byte	12,23,139,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70241
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ID',0,23,144,5,3
	.word	75554
	.byte	12,23,147,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70352
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARD',0,23,152,5,3
	.word	75616
	.byte	12,23,155,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70566
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARP',0,23,160,5,3
	.word	75680
	.byte	12,23,163,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70753
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCOND',0,23,168,5,3
	.word	75744
	.byte	12,23,171,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71077
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG',0,23,176,5,3
	.word	75811
	.byte	12,23,179,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71220
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSM',0,23,184,5,3
	.word	75880
	.byte	12,23,187,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71409
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,23,192,5,3
	.word	75949
	.byte	12,23,195,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71772
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONOTP',0,23,200,5,3
	.word	76022
	.byte	12,23,203,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72367
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONP',0,23,208,5,3
	.word	76091
	.byte	12,23,211,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72891
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONWOP',0,23,216,5,3
	.word	76158
	.byte	12,23,219,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73473
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0',0,23,224,5,3
	.word	76227
	.byte	12,23,227,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73575
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1',0,23,232,5,3
	.word	76295
	.byte	12,23,235,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73677
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2',0,23,240,5,3
	.word	76363
	.byte	12,23,243,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73779
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD',0,23,248,5,3
	.word	76431
	.byte	12,23,251,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73873
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRCT',0,23,128,6,3
	.word	76495
	.byte	12,23,131,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74083
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0',0,23,136,6,3
	.word	76559
	.byte	12,23,139,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74156
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1',0,23,144,6,3
	.word	76623
	.byte	12,23,147,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74229
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG',0,23,152,6,3
	.word	76687
	.byte	12,23,155,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74384
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT',0,23,160,6,3
	.word	76755
	.byte	12,23,163,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74489
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_TOP',0,23,168,6,3
	.word	76824
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,23,179,6,25,12,13
	.byte	'CFG',0
	.word	74769
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	74837
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	74906
	.byte	4,2,35,8,0,14
	.word	76892
	.byte	28
	.byte	'Ifx_FLASH_CBAB',0,23,184,6,3
	.word	76955
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,23,187,6,25,12,13
	.byte	'CFG0',0
	.word	76227
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	76295
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	76363
	.byte	4,2,35,8,0,14
	.word	76984
	.byte	28
	.byte	'Ifx_FLASH_RDB',0,23,192,6,3
	.word	77048
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,23,195,6,25,12,13
	.byte	'CFG',0
	.word	76687
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	76755
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	76824
	.byte	4,2,35,8,0,14
	.word	77076
	.byte	28
	.byte	'Ifx_FLASH_UBAB',0,23,200,6,3
	.word	77139
	.byte	28
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8031
	.byte	28
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7944
	.byte	28
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4287
	.byte	28
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2340
	.byte	28
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3335
	.byte	28
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2468
	.byte	28
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3115
	.byte	28
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2683
	.byte	28
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2898
	.byte	28
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7303
	.byte	28
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7427
	.byte	28
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7511
	.byte	28
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7691
	.byte	28
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5942
	.byte	28
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6466
	.byte	28
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6116
	.byte	28
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6290
	.byte	28
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6955
	.byte	28
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1769
	.byte	28
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5279
	.byte	28
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5767
	.byte	28
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5426
	.byte	28
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5595
	.byte	28
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6622
	.byte	28
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1453
	.byte	28
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4993
	.byte	28
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4627
	.byte	28
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3658
	.byte	28
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3962
	.byte	28
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8558
	.byte	28
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7991
	.byte	28
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4578
	.byte	28
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2419
	.byte	28
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3609
	.byte	28
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2643
	.byte	28
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3295
	.byte	28
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2858
	.byte	28
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3075
	.byte	28
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7387
	.byte	28
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7636
	.byte	28
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7895
	.byte	28
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7263
	.byte	28
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6076
	.byte	28
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6582
	.byte	28
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6250
	.byte	28
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6426
	.byte	28
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2300
	.byte	28
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6915
	.byte	28
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5386
	.byte	28
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5902
	.byte	28
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5555
	.byte	28
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5727
	.byte	28
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1729
	.byte	28
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5239
	.byte	28
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4953
	.byte	28
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3922
	.byte	28
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4238
	.byte	14
	.word	8598
	.byte	28
	.byte	'Ifx_P',0,6,139,6,3
	.word	78486
	.byte	28
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9211
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,28
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	78532
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,28
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	78776
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,28
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	78874
	.byte	28
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9416
	.byte	27,5,190,1,9,8,13
	.byte	'port',0
	.word	9206
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	641
	.byte	1,2,35,4,0,28
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	79339
	.byte	14
	.word	38062
	.byte	3
	.word	79399
	.byte	27,24,74,15,20,13
	.byte	'module',0
	.word	79404
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	641
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	79339
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	13346
	.byte	1,2,35,16,0,21
	.word	79409
	.byte	28
	.byte	'IfxScu_Req_In',0,24,80,3
	.word	79479
	.byte	28
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,25,148,1,16
	.word	205
	.byte	27,25,212,5,9,8,13
	.byte	'value',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9752
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_CcuconRegConfig',0,25,216,5,3
	.word	79546
	.byte	27,25,221,5,9,8,13
	.byte	'pDivider',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	641
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	641
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_InitialStepConfig',0,25,227,5,3
	.word	79617
	.byte	27,25,231,5,9,12,13
	.byte	'k2Step',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	79506
	.byte	4,2,35,8,0,28
	.byte	'IfxScuCcu_PllStepsConfig',0,25,236,5,3
	.word	79734
	.byte	3
	.word	202
	.byte	27,25,244,5,9,48,13
	.byte	'ccucon0',0
	.word	79546
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	79546
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	79546
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	79546
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	79546
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	79546
	.byte	8,2,35,40,0,28
	.byte	'IfxScuCcu_ClockDistributionConfig',0,25,252,5,3
	.word	79836
	.byte	27,25,128,6,9,8,13
	.byte	'value',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9752
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,25,132,6,3
	.word	79988
	.byte	3
	.word	79734
	.byte	27,25,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	80064
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	79617
	.byte	8,2,35,8,0,28
	.byte	'IfxScuCcu_SysPllConfig',0,25,142,6,3
	.word	80069
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,28
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	80186
	.byte	27,7,160,1,9,6,13
	.byte	'counter',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	641
	.byte	1,2,35,4,0,28
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	80275
	.byte	27,7,172,1,9,32,13
	.byte	'instruction',0
	.word	80275
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	80275
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	80275
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	80275
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	80275
	.byte	6,2,35,24,0,28
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	80341
	.byte	28
	.byte	'gpio_pin_enum',0,11,89,2
	.word	10292
	.byte	28
	.byte	'gpio_dir_enum',0,11,95,2
	.word	12266
	.byte	28
	.byte	'gpio_mode_enum',0,11,111,2
	.word	12284
	.byte	28
	.byte	'soft_iic_info_struct',0,12,50,2
	.word	12447
	.byte	15,135,1
	.word	641
	.byte	16,134,1,0,21
	.word	80555
	.byte	31
	.byte	'dl1b_config_file',0,26,41,28
	.word	80566
	.byte	1,1,28
	.byte	'tof_type_enum',0,13,68,2
	.word	12840
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,55,0,73,19,0,0,21,38,0,73,19,0,0,22,46,1,3,8,58
	.byte	15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,23,5,0,58,15,59,15,57,15,73,19,0,0,24,46,1,3,8,58,15
	.byte	59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,46,1,49,19,0,0,26,5,0,49,19,0,0,27,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,28,22,0,3,8,58,15,59,15,57,15,73,19,0,0,29,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,30
	.byte	21,0,54,15,0,0,31,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L32:
	.word	.L83-.L82
.L82:
	.half	3
	.word	.L85-.L84
.L84:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'string.h',0,2,0,0
	.byte	'zf_driver_delay.h',0,3,0,0
	.byte	'zf_driver_gpio.h',0,3,0,0
	.byte	'zf_driver_soft_iic.h',0,3,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_type.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0,0,0,0
	.byte	'stdio.h',0,2,0,0
	.byte	'Platform_Types.h',0,4,0,0
	.byte	'ifx_types.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,5,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_config.h',0,0,0,0,0
.L85:
.L83:
	.sdecl	'.debug_info',debug,cluster('dl1b_get_distance')
	.sect	'.debug_info'
.L33:
	.word	321
	.half	3
	.word	.L34
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L36,.L35
	.byte	2
	.word	.L29
	.byte	3
	.byte	'dl1b_get_distance',0,1,76,6,1,1,1
	.word	.L24,.L56,.L23
	.byte	4
	.word	.L24,.L56
	.byte	4
	.word	.L57,.L2
	.byte	5
	.byte	'data_buffer',0,1,80,15
	.word	.L58,.L59
	.byte	5
	.byte	'dl1b_distance_temp',0,1,81,15
	.word	.L60,.L61
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1b_get_distance')
	.sect	'.debug_abbrev'
.L34:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1b_get_distance')
	.sect	'.debug_line'
.L35:
	.word	.L87-.L86
.L86:
	.half	3
	.word	.L89-.L88
.L88:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0,0,0,0,0
.L89:
	.byte	5,6,7,0,5,2
	.word	.L24
	.byte	3,203,0,1,5,8,9
	.half	.L71-.L24
	.byte	3,2,1,5,5,9
	.half	.L90-.L71
	.byte	1,5,32,7,9
	.half	.L57-.L90
	.byte	3,2,1,5,30,9
	.half	.L91-.L57
	.byte	1,5,51,9
	.half	.L92-.L91
	.byte	3,3,1,5,24,9
	.half	.L93-.L92
	.byte	1,5,51,9
	.half	.L94-.L93
	.byte	3,1,1,5,24,9
	.half	.L95-.L94
	.byte	1,5,9,9
	.half	.L96-.L95
	.byte	3,1,1,5,23,9
	.half	.L97-.L96
	.byte	3,2,1,5,9,9
	.half	.L98-.L97
	.byte	1,5,59,7,9
	.half	.L99-.L98
	.byte	3,3,1,5,28,9
	.half	.L100-.L99
	.byte	1,5,59,9
	.half	.L101-.L100
	.byte	3,1,1,5,28,9
	.half	.L102-.L101
	.byte	1,5,30,9
	.half	.L103-.L102
	.byte	3,1,1,5,28,9
	.half	.L104-.L103
	.byte	1,5,13,9
	.half	.L105-.L104
	.byte	3,1,1,5,56,9
	.half	.L106-.L105
	.byte	3,2,1,5,28,9
	.half	.L107-.L106
	.byte	1,5,56,9
	.half	.L108-.L107
	.byte	3,1,1,5,28,9
	.half	.L109-.L108
	.byte	1,5,13,9
	.half	.L110-.L109
	.byte	3,1,1,5,35,9
	.half	.L111-.L110
	.byte	3,2,1,5,16,9
	.half	.L112-.L111
	.byte	1,5,13,9
	.half	.L113-.L112
	.byte	1,5,86,7,9
	.half	.L114-.L113
	.byte	3,2,1,5,32,9
	.half	.L115-.L114
	.byte	1,5,86,9
	.half	.L116-.L115
	.byte	3,1,1,5,32,9
	.half	.L117-.L116
	.byte	1,5,17,9
	.half	.L118-.L117
	.byte	3,1,1,5,49,9
	.half	.L119-.L118
	.byte	3,1,1,5,58,9
	.half	.L72-.L119
	.byte	3,1,1,5,77,9
	.half	.L120-.L72
	.byte	1,5,64,9
	.half	.L73-.L120
	.byte	1,5,41,9
	.half	.L74-.L73
	.byte	3,2,1,5,20,9
	.half	.L121-.L74
	.byte	1,5,68,7,9
	.half	.L122-.L121
	.byte	1,5,21,7,9
	.half	.L6-.L122
	.byte	3,2,1,5,40,9
	.half	.L123-.L6
	.byte	1,5,38,9
	.half	.L75-.L123
	.byte	1,5,21,9
	.half	.L124-.L75
	.byte	3,1,1,5,39,9
	.half	.L125-.L124
	.byte	1,5,37,9
	.half	.L126-.L125
	.byte	1,5,44,9
	.half	.L127-.L126
	.byte	3,127,1,5,21,9
	.half	.L7-.L127
	.byte	3,5,1,5,38,9
	.half	.L128-.L7
	.byte	1,5,21,9
	.half	.L129-.L128
	.byte	3,1,1,5,39,9
	.half	.L130-.L129
	.byte	1,5,37,9
	.half	.L76-.L130
	.byte	1,5,17,9
	.half	.L8-.L76
	.byte	3,125,1,9
	.half	.L5-.L8
	.byte	3,8,1,5,36,9
	.half	.L131-.L5
	.byte	1,5,34,9
	.half	.L132-.L131
	.byte	1,5,17,9
	.half	.L133-.L132
	.byte	3,1,1,5,35,9
	.half	.L134-.L133
	.byte	1,5,33,9
	.half	.L135-.L134
	.byte	1,5,13,9
	.half	.L9-.L135
	.byte	3,125,1,9
	.half	.L4-.L9
	.byte	3,8,1,5,32,9
	.half	.L136-.L4
	.byte	1,5,30,9
	.half	.L137-.L136
	.byte	1,5,13,9
	.half	.L138-.L137
	.byte	3,1,1,5,31,9
	.half	.L139-.L138
	.byte	1,5,29,9
	.half	.L140-.L139
	.byte	1,5,1,9
	.half	.L2-.L140
	.byte	3,3,1,7,9
	.half	.L37-.L2
	.byte	0,1,1
.L87:
	.sdecl	'.debug_ranges',debug,cluster('dl1b_get_distance')
	.sect	'.debug_ranges'
.L36:
	.word	-1,.L24,0,.L37-.L24,0,0
	.sdecl	'.debug_info',debug,cluster('dl1b_int_handler')
	.sect	'.debug_info'
.L38:
	.word	255
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L41,.L40
	.byte	2
	.word	.L29
	.byte	3
	.byte	'dl1b_int_handler',0,1,139,1,6,1,1,1
	.word	.L26,.L62,.L25
	.byte	4
	.word	.L26,.L62
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1b_int_handler')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1b_int_handler')
	.sect	'.debug_line'
.L40:
	.word	.L142-.L141
.L141:
	.half	3
	.word	.L144-.L143
.L143:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0,0,0,0,0
.L144:
	.byte	5,1,7,0,5,2
	.word	.L26
	.byte	3,143,1,1,7,9
	.half	.L42-.L26
	.byte	0,1,1
.L142:
	.sdecl	'.debug_ranges',debug,cluster('dl1b_int_handler')
	.sect	'.debug_ranges'
.L41:
	.word	-1,.L26,0,.L42-.L26,0,0
	.sdecl	'.debug_info',debug,cluster('dl1b_init')
	.sect	'.debug_info'
.L43:
	.word	332
	.half	3
	.word	.L44
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L46,.L45
	.byte	2
	.word	.L29
	.byte	3
	.byte	'dl1b_init',0,1,153,1,7
	.word	.L63
	.byte	1,1,1
	.word	.L28,.L64,.L27
	.byte	4
	.word	.L28,.L64
	.byte	5
	.byte	'return_state',0,1,155,1,13
	.word	.L63,.L65
	.byte	5
	.byte	'data_buffer',0,1,156,1,13
	.word	.L66,.L67
	.byte	5
	.byte	'time_out_count',0,1,157,1,13
	.word	.L68,.L69
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1b_init')
	.sect	'.debug_abbrev'
.L44:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1b_init')
	.sect	'.debug_line'
.L45:
	.word	.L146-.L145
.L145:
	.half	3
	.word	.L148-.L147
.L147:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0,0,0,0,0
.L148:
	.byte	5,7,7,0,5,2
	.word	.L28
	.byte	3,152,1,1,5,29,9
	.half	.L77-.L28
	.byte	3,4,1,5,20,9
	.half	.L78-.L77
	.byte	3,3,1,5,37,9
	.half	.L149-.L78
	.byte	1,5,52,9
	.half	.L150-.L149
	.byte	1,5,73,9
	.half	.L151-.L150
	.byte	1,5,87,9
	.half	.L152-.L151
	.byte	1,5,15,9
	.half	.L153-.L152
	.byte	3,4,1,5,28,9
	.half	.L154-.L153
	.byte	1,5,33,9
	.half	.L155-.L154
	.byte	1,5,44,9
	.half	.L156-.L155
	.byte	1,5,25,9
	.half	.L11-.L156
	.byte	3,4,1,5,9,9
	.half	.L157-.L11
	.byte	3,1,1,5,25,9
	.half	.L158-.L157
	.byte	3,1,1,5,9,9
	.half	.L159-.L158
	.byte	3,1,1,5,25,9
	.half	.L160-.L159
	.byte	3,1,1,5,55,9
	.half	.L161-.L160
	.byte	3,2,1,5,24,9
	.half	.L162-.L161
	.byte	1,5,55,9
	.half	.L163-.L162
	.byte	3,1,1,5,24,9
	.half	.L164-.L163
	.byte	1,5,9,9
	.half	.L165-.L164
	.byte	3,1,1,5,45,9
	.half	.L166-.L165
	.byte	3,1,1,5,24,9
	.half	.L167-.L166
	.byte	1,5,58,7,9
	.half	.L168-.L167
	.byte	1,5,64,9
	.half	.L80-.L168
	.byte	1,5,58,9
	.half	.L12-.L80
	.byte	1,5,9,9
	.half	.L13-.L12
	.byte	3,1,1,5,13,7,9
	.half	.L169-.L13
	.byte	3,2,1,5,57,9
	.half	.L14-.L169
	.byte	3,3,1,5,24,9
	.half	.L170-.L14
	.byte	1,5,57,9
	.half	.L171-.L170
	.byte	3,1,1,5,24,9
	.half	.L172-.L171
	.byte	1,5,28,9
	.half	.L173-.L172
	.byte	3,1,1,5,42,9
	.half	.L174-.L173
	.byte	1,5,60,9
	.half	.L175-.L174
	.byte	1,5,9,9
	.half	.L176-.L175
	.byte	3,1,1,5,16,9
	.half	.L177-.L176
	.byte	3,2,1,5,55,9
	.half	.L17-.L177
	.byte	3,2,1,5,28,9
	.half	.L178-.L17
	.byte	1,5,55,9
	.half	.L179-.L178
	.byte	3,1,1,5,28,9
	.half	.L180-.L179
	.byte	1,5,13,9
	.half	.L181-.L180
	.byte	3,1,1,5,36,9
	.half	.L182-.L181
	.byte	3,1,1,5,13,9
	.half	.L183-.L182
	.byte	1,5,17,7,9
	.half	.L184-.L183
	.byte	3,3,1,5,52,9
	.half	.L18-.L184
	.byte	3,2,1,5,16,9
	.half	.L81-.L18
	.byte	1,5,13,9
	.half	.L185-.L81
	.byte	1,5,30,7,9
	.half	.L186-.L185
	.byte	3,2,1,5,17,9
	.half	.L187-.L186
	.byte	3,1,1,5,29,9
	.half	.L20-.L187
	.byte	3,2,1,5,16,9
	.half	.L16-.L20
	.byte	3,113,1,5,9,9
	.half	.L19-.L16
	.byte	3,18,1,5,26,9
	.half	.L188-.L19
	.byte	1,5,24,9
	.half	.L189-.L188
	.byte	1,5,18,9
	.half	.L15-.L189
	.byte	3,8,1,5,28,9
	.half	.L190-.L15
	.byte	1,5,5,9
	.half	.L191-.L190
	.byte	3,2,1,5,1,9
	.half	.L22-.L191
	.byte	3,1,1,7,9
	.half	.L47-.L22
	.byte	0,1,1
.L146:
	.sdecl	'.debug_ranges',debug,cluster('dl1b_init')
	.sect	'.debug_ranges'
.L46:
	.word	-1,.L28,0,.L47-.L28,0,0
	.sdecl	'.debug_info',debug,cluster('dl1b_finsh_flag')
	.sect	'.debug_info'
.L48:
	.word	231
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L29
	.byte	3
	.byte	'dl1b_finsh_flag',0,14,58,7
	.word	.L63
	.byte	1,5,3
	.word	dl1b_finsh_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dl1b_finsh_flag')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('dl1b_distance_mm')
	.sect	'.debug_info'
.L50:
	.word	232
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L29
	.byte	3
	.byte	'dl1b_distance_mm',0,14,59,8
	.word	.L68
	.byte	1,5,3
	.word	dl1b_distance_mm
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dl1b_distance_mm')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('dl1b_init_flag')
	.sect	'.debug_info'
.L52:
	.word	230
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L29
	.byte	3
	.byte	'dl1b_init_flag',0,14,57,7
	.word	.L63
	.byte	1,5,3
	.word	dl1b_init_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dl1b_init_flag')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('dl1b_iic_struct')
	.sect	'.debug_info'
.L54:
	.word	230
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1b.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L29
	.byte	3
	.byte	'dl1b_iic_struct',0,14,62,29
	.word	.L70
	.byte	5,3
	.word	dl1b_iic_struct
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dl1b_iic_struct')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('dl1b_get_distance')
	.sect	'.debug_loc'
.L59:
	.word	-1,.L24,0,.L56-.L24
	.half	2
	.byte	145,120
	.word	0,0
.L61:
	.word	-1,.L24,.L72-.L24,.L73-.L24
	.half	1
	.byte	95
	.word	.L74-.L24,.L75-.L24
	.half	1
	.byte	95
	.word	.L7-.L24,.L76-.L24
	.half	1
	.byte	95
	.word	0,0
.L23:
	.word	-1,.L24,0,.L71-.L24
	.half	2
	.byte	138,0
	.word	.L71-.L24,.L56-.L24
	.half	2
	.byte	138,8
	.word	.L56-.L24,.L56-.L24
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1b_init')
	.sect	'.debug_loc'
.L67:
	.word	-1,.L28,0,.L64-.L28
	.half	3
	.byte	145,240,126
	.word	0,0
.L27:
	.word	-1,.L28,0,.L77-.L28
	.half	2
	.byte	138,0
	.word	.L77-.L28,.L64-.L28
	.half	3
	.byte	138,144,1
	.word	.L64-.L28,.L64-.L28
	.half	2
	.byte	138,0
	.word	0,0
.L65:
	.word	-1,.L28,.L80-.L28,.L12-.L28
	.half	1
	.byte	89
	.word	.L13-.L28,.L64-.L28
	.half	1
	.byte	89
	.word	0,0
.L69:
	.word	-1,.L28,.L78-.L28,.L79-.L28
	.half	1
	.byte	88
	.word	.L81-.L28,.L64-.L28
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1b_int_handler')
	.sect	'.debug_loc'
.L25:
	.word	-1,.L26,0,.L62-.L26
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L192:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('dl1b_get_distance')
	.sect	'.debug_frame'
	.word	36
	.word	.L192,.L24,.L56-.L24
	.byte	4
	.word	(.L71-.L24)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L56-.L71)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1b_int_handler')
	.sect	'.debug_frame'
	.word	24
	.word	.L192,.L26,.L62-.L26
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('dl1b_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L192,.L28,.L64-.L28
	.byte	4
	.word	(.L77-.L28)/2
	.byte	19,144,1,22,26,4,19,138,144,1,4
	.word	(.L64-.L77)/2
	.byte	19,0,8,26
	; Module end
