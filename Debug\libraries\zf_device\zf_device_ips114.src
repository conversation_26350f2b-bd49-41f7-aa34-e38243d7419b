	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc9792a --dep-file=zf_device_ips114.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_ips114.src ../libraries/zf_device/zf_device_ips114.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_ips114.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_ips114.ips114_write_index',code,cluster('ips114_write_index')
	.sect	'.text.zf_device_ips114.ips114_write_index'
	.align	2
	
; Function ips114_write_index
.L187:
ips114_write_index:	.type	func
	mov	d8,d4
.L548:
	mov	d15,#0
	jeq	d15,#0,.L2
	mov	d4,#480
.L547:
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L3
.L2:
	mov	d4,#480
.L549:
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L3:
	mov	d4,#2
	mov	d5,d8
.L550:
	call	spi_write_8bit
.L551:
	mov	d15,#1
	jeq	d15,#0,.L4
	mov	d4,#480
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L5
.L4:
	mov	d4,#480
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L5:
	ret
.L532:
	
__ips114_write_index_function_end:
	.size	ips114_write_index,__ips114_write_index_function_end-ips114_write_index
.L326:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_set_region',code,cluster('ips114_set_region')
	.sect	'.text.zf_device_ips114.ips114_set_region'
	.align	2
	
; Function ips114_set_region
.L189:
ips114_set_region:	.type	func
	mov	e8,d5,d4
	mov	e10,d7,d6
.L1382:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L555:
	lt.u	d4,d8,d15
.L552:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#178
.L554:
	call	debug_assert_handler
.L553:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L556:
	lt.u	d4,d9,d15
.L557:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#179
	call	debug_assert_handler
.L1383:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L558:
	lt.u	d4,d10,d15
.L559:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#180
	call	debug_assert_handler
.L1384:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L560:
	lt.u	d4,d11,d15
.L561:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#181
	call	debug_assert_handler
.L1385:
	movh.a	a15,#@his(ips114_display_dir)
	lea	a15,[a15]@los(ips114_display_dir)
	ld.bu	d0,[a15]
.L1386:
	mov	d15,#0
	jeq	d15,d0,.L6
.L1387:
	mov	d15,#1
	jeq	d15,d0,.L7
.L1388:
	mov	d15,#2
	jeq	d15,d0,.L8
.L1389:
	mov	d15,#3
	jeq	d15,d0,.L9
	j	.L10
.L6:
	mov	d4,#42
	call	ips114_write_index
.L1390:
	mov	d4,#2
.L562:
	add	d15,d8,#40
.L563:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1391:
	mov	d4,#2
.L564:
	add	d15,d10,#40
.L565:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1392:
	mov	d4,#43
	call	ips114_write_index
.L1393:
	mov	d4,#2
.L566:
	add	d15,d9,#52
.L567:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1394:
	mov	d4,#2
.L568:
	add	d15,d11,#52
.L569:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1395:
	mov	d4,#44
	call	ips114_write_index
.L1396:
	j	.L11
.L7:
	mov	d4,#42
	call	ips114_write_index
.L1397:
	mov	d4,#2
.L570:
	add	d15,d8,#40
.L571:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1398:
	mov	d4,#2
.L572:
	add	d15,d10,#40
.L573:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1399:
	mov	d4,#43
	call	ips114_write_index
.L1400:
	mov	d4,#2
.L574:
	add	d15,d9,#53
.L575:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1401:
	mov	d4,#2
.L576:
	add	d15,d11,#53
.L577:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1402:
	mov	d4,#44
	call	ips114_write_index
.L1403:
	j	.L12
.L8:
	mov	d4,#42
	call	ips114_write_index
.L1404:
	mov	d4,#2
.L578:
	add	d15,d8,#52
.L579:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1405:
	mov	d4,#2
.L580:
	add	d15,d10,#52
.L581:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1406:
	mov	d4,#43
	call	ips114_write_index
.L1407:
	mov	d4,#2
.L582:
	add	d15,d9,#40
.L583:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1408:
	mov	d4,#2
.L584:
	add	d15,d11,#40
.L585:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1409:
	mov	d4,#44
	call	ips114_write_index
.L1410:
	j	.L13
.L9:
	mov	d4,#42
	call	ips114_write_index
.L1411:
	mov	d4,#2
.L586:
	add	d15,d8,#53
.L587:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1412:
	mov	d4,#2
.L588:
	add	d15,d10,#53
.L589:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1413:
	mov	d4,#43
	call	ips114_write_index
.L1414:
	mov	d4,#2
.L590:
	add	d15,d9,#40
.L591:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1415:
	mov	d4,#2
.L592:
	add	d15,d11,#40
.L593:
	extr.u	d5,d15,#0,#16
	call	spi_write_16bit
.L1416:
	mov	d4,#44
	call	ips114_write_index
.L1417:
	j	.L14
.L10:
.L14:
.L13:
.L12:
.L11:
	ret
.L535:
	
__ips114_set_region_function_end:
	.size	ips114_set_region,__ips114_set_region_function_end-ips114_set_region
.L331:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_debug_init',code,cluster('ips114_debug_init')
	.sect	'.text.zf_device_ips114.ips114_debug_init'
	.align	2
	
; Function ips114_debug_init
.L191:
ips114_debug_init:	.type	func
	sub.a	a10,#24
.L594:
	lea	a4,[a10]0
	call	debug_output_struct_init
.L1422:
	mov	d15,#1
.L1423:
	st.h	[a10],d15
.L1424:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L1425:
	st.h	[a10]2,d15
.L1426:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L1427:
	st.h	[a10]4,d15
.L1428:
	movh.a	a15,#@his(ips114_display_font)
	lea	a15,[a15]@los(ips114_display_font)
	ld.bu	d15,[a15]
.L1429:
	mov	d0,#0
	jeq	d15,d0,.L15
.L1430:
	mov	d0,#1
	jeq	d15,d0,.L16
.L1431:
	mov	d0,#2
	jeq	d15,d0,.L17
	j	.L18
.L15:
	mov	d15,#6
.L1432:
	st.b	[a10]6,d15
.L1433:
	mov	d15,#8
.L1434:
	st.b	[a10]7,d15
.L1435:
	j	.L19
.L16:
	mov	d15,#8
.L1436:
	st.b	[a10]6,d15
.L1437:
	mov	d15,#16
.L1438:
	st.b	[a10]7,d15
.L1439:
	j	.L20
.L17:
	j	.L21
.L18:
.L21:
.L20:
.L19:
	movh.a	a15,#@his(ips114_show_string)
	lea	a15,[a15]@los(ips114_show_string)
.L1440:
	st.a	[a10]12,a15
.L1441:
	movh.a	a15,#@his(ips114_clear)
	lea	a15,[a15]@los(ips114_clear)
.L1442:
	st.a	[a10]16,a15
.L1443:
	lea	a4,[a10]0
	call	debug_output_init
.L1444:
	ret
.L544:
	
__ips114_debug_init_function_end:
	.size	ips114_debug_init,__ips114_debug_init_function_end-ips114_debug_init
.L336:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_clear',code,cluster('ips114_clear')
	.sect	'.text.zf_device_ips114.ips114_clear'
	.align	2
	
	.global	ips114_clear
; Function ips114_clear
.L193:
ips114_clear:	.type	func
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L971:
	mul	d4,d15,#2
	call	malloc
.L595:
	mov.aa	a12,a2
.L597:
	mov	d15,#0
	jeq	d15,#0,.L22
	mov	d4,#482
	call	get_port
.L596:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L23
.L22:
	mov	d4,#482
	call	get_port
.L598:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L23:
	mov	d4,#0
.L972:
	mov	d5,#0
.L973:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L974:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L975:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L976:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
	call	ips114_set_region
.L977:
	mov	d0,#0
.L599:
	j	.L24
.L25:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L978:
	movh.a	a2,#@his(ips114_bgcolor)
	lea	a2,[a2]@los(ips114_bgcolor)
	ld.hu	d15,[a2]0
.L979:
	st.h	[a15],d15
.L980:
	add	d0,#1
.L24:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L981:
	jlt.u	d0,d15,.L25
.L982:
	mov	d15,#0
.L600:
	j	.L26
.L27:
	mov	d4,#2
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d5,[a15]0
	mov.aa	a4,a12
.L602:
	call	spi_write_16bit_array
.L603:
	add	d15,#1
.L26:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L983:
	jlt.u	d15,d0,.L27
.L984:
	mov	d15,#1
.L601:
	jeq	d15,#0,.L28
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L29
.L28:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L29:
	mov.aa	a4,a12
.L604:
	call	free
.L605:
	ret
.L349:
	
__ips114_clear_function_end:
	.size	ips114_clear,__ips114_clear_function_end-ips114_clear
.L236:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_full',code,cluster('ips114_full')
	.sect	'.text.zf_device_ips114.ips114_full'
	.align	2
	
	.global	ips114_full
; Function ips114_full
.L195:
ips114_full:	.type	func
	mov	d8,d4
.L607:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L989:
	mul	d4,d15,#2
.L606:
	call	malloc
.L608:
	mov.aa	a12,a2
.L610:
	mov	d15,#0
	jeq	d15,#0,.L30
	mov	d4,#482
	call	get_port
.L609:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L31
.L30:
	mov	d4,#482
	call	get_port
.L611:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L31:
	mov	d4,#0
.L990:
	mov	d5,#0
.L991:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L992:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L993:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L994:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
	call	ips114_set_region
.L995:
	mov	d0,#0
.L612:
	j	.L32
.L33:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L996:
	st.h	[a15],d8
.L997:
	add	d0,#1
.L32:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L998:
	jlt.u	d0,d15,.L33
.L999:
	mov	d15,#0
.L613:
	j	.L34
.L35:
	mov	d4,#2
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d5,[a15]0
	mov.aa	a4,a12
.L615:
	call	spi_write_16bit_array
.L616:
	add	d15,#1
.L34:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L1000:
	jlt.u	d15,d0,.L35
.L1001:
	mov	d15,#1
.L614:
	jeq	d15,#0,.L36
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L37
.L36:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L37:
	mov.aa	a4,a12
.L617:
	call	free
.L618:
	ret
.L355:
	
__ips114_full_function_end:
	.size	ips114_full,__ips114_full_function_end-ips114_full
.L241:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_set_dir',code,cluster('ips114_set_dir')
	.sect	'.text.zf_device_ips114.ips114_set_dir'
	.align	2
	
	.global	ips114_set_dir
; Function ips114_set_dir
.L197:
ips114_set_dir:	.type	func
	movh.a	a15,#@his(ips114_display_dir)
	lea	a15,[a15]@los(ips114_display_dir)
.L1006:
	st.b	[a15],d4
.L1007:
	mov	d15,#0
	jeq	d15,d4,.L38
.L1008:
	mov	d15,#1
	jeq	d15,d4,.L39
.L1009:
	mov	d15,#2
	jeq	d15,d4,.L40
.L1010:
	mov	d15,#3
	jeq	d15,d4,.L41
	j	.L42
.L38:
.L39:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
.L1011:
	mov	d15,#240
.L1012:
	st.h	[a15],d15
.L1013:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
.L1014:
	mov	d15,#135
.L1015:
	st.h	[a15],d15
.L1016:
	j	.L43
.L40:
.L41:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
.L1017:
	mov	d15,#135
.L1018:
	st.h	[a15],d15
.L1019:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
.L1020:
	mov	d15,#240
.L1021:
	st.h	[a15],d15
.L1022:
	j	.L44
.L42:
.L44:
.L43:
	ret
.L361:
	
__ips114_set_dir_function_end:
	.size	ips114_set_dir,__ips114_set_dir_function_end-ips114_set_dir
.L246:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_set_font',code,cluster('ips114_set_font')
	.sect	'.text.zf_device_ips114.ips114_set_font'
	.align	2
	
	.global	ips114_set_font
; Function ips114_set_font
.L199:
ips114_set_font:	.type	func
	movh.a	a15,#@his(ips114_display_font)
	lea	a15,[a15]@los(ips114_display_font)
.L1027:
	st.b	[a15],d4
.L1028:
	ret
.L364:
	
__ips114_set_font_function_end:
	.size	ips114_set_font,__ips114_set_font_function_end-ips114_set_font
.L251:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_set_color',code,cluster('ips114_set_color')
	.sect	'.text.zf_device_ips114.ips114_set_color'
	.align	2
	
	.global	ips114_set_color
; Function ips114_set_color
.L201:
ips114_set_color:	.type	func
	movh.a	a15,#@his(ips114_pencolor)
	lea	a15,[a15]@los(ips114_pencolor)
.L1033:
	st.h	[a15],d4
.L1034:
	movh.a	a15,#@his(ips114_bgcolor)
	lea	a15,[a15]@los(ips114_bgcolor)
.L1035:
	st.h	[a15],d5
.L1036:
	ret
.L367:
	
__ips114_set_color_function_end:
	.size	ips114_set_color,__ips114_set_color_function_end-ips114_set_color
.L256:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_draw_point',code,cluster('ips114_draw_point')
	.sect	'.text.zf_device_ips114.ips114_draw_point'
	.align	2
	
	.global	ips114_draw_point
; Function ips114_draw_point
.L203:
ips114_draw_point:	.type	func
	mov	e8,d5,d4
	mov	d10,d6
.L622:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L623:
	lt.u	d4,d8,d15
.L620:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#383
.L621:
	call	debug_assert_handler
.L619:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L624:
	lt.u	d4,d9,d15
.L625:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#384
	call	debug_assert_handler
.L1041:
	mov	d15,#0
	jeq	d15,#0,.L45
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L46
.L45:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L46:
	mov	e4,d9,d8
	mov	e6,d9,d8
.L626:
	call	ips114_set_region
.L1042:
	mov	d4,#2
	mov	d5,d10
.L627:
	call	spi_write_16bit
.L628:
	mov	d15,#1
	jeq	d15,#0,.L47
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L48
.L47:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L48:
	ret
.L372:
	
__ips114_draw_point_function_end:
	.size	ips114_draw_point,__ips114_draw_point_function_end-ips114_draw_point
.L261:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_draw_line',code,cluster('ips114_draw_line')
	.sect	'.text.zf_device_ips114.ips114_draw_line'
	.align	2
	
	.global	ips114_draw_line
; Function ips114_draw_line
.L205:
ips114_draw_line:	.type	func
	sub.a	a10,#8
.L629:
	mov	d15,d4
.L633:
	mov	e8,d6,d5
	mov	d10,d7
.L635:
	ld.hu	d11,[a10]8
.L636:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d15,d0
.L631:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#407
.L632:
	call	debug_assert_handler
.L630:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L637:
	lt.u	d4,d8,d0
.L638:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#408
	call	debug_assert_handler
.L1047:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L639:
	lt.u	d4,d9,d0
.L640:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#409
	call	debug_assert_handler
.L1048:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d10,d0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#410
	call	debug_assert_handler
.L385:
	jge.u	d15,d9,.L49
.L641:
	mov	d0,#1
	st.w	[a10],d0
.L1049:
	j	.L50
.L49:
	mov	d0,#-1
	st.w	[a10],d0
.L50:
	jge.u	d8,d10,.L51
.L642:
	mov	d14,#1
.L643:
	j	.L52
.L51:
	mov	d14,#-1
.L52:
.L53:
	jeq	d15,d9,.L54
.L644:
	sub	d0,d8,d10
.L645:
	itof	d0,d0
.L646:
	sub	d1,d15,d9
.L647:
	itof	d1,d1
.L1050:
	div.f	d13,d0,d1
.L648:
	utof	d0,d8
.L649:
	utof	d1,d15
.L1051:
	msub.f	d12,d0,d1,d13
.L650:
	j	.L55
.L54:
	j	.L56
.L57:
	mov	e4,d8,d9
.L651:
	mov	d6,d11
.L652:
	call	ips114_draw_point
.L653:
	add	d8,d14
	extr.u	d8,d8,#0,#16
.L56:
	jne	d8,d10,.L57
.L654:
	mov	e4,d10,d9
.L655:
	mov	d6,d11
.L656:
	call	ips114_draw_point
.L657:
	j	.L58
.L55:
	sub	d0,d8,d10
.L658:
	jlt	d0,#0,.L59
.L659:
	sub	d0,d8,d10
.L660:
	j	.L60
.L59:
	sub	d0,d8,d10
.L661:
	rsub	d0,#0
.L60:
	sub	d1,d15,d9
.L662:
	jlt	d1,#0,.L61
.L663:
	sub	d1,d15,d9
.L664:
	j	.L62
.L61:
	sub	d1,d15,d9
.L665:
	rsub	d1,#0
.L62:
	jge	d1,d0,.L63
.L1052:
	j	.L64
.L65:
	mov	e4,d8,d15
.L666:
	mov	d6,d11
.L667:
	call	ips114_draw_point
.L668:
	add	d8,d14
	extr.u	d8,d8,#0,#16
.L669:
	utof	d15,d8
.L634:
	sub.f	d15,d15,d12
.L1053:
	div.f	d4,d15,d13
.L1054:
	call	__f_ftos
	extr.u	d15,d2,#0,#16
.L64:
	jne	d8,d10,.L65
.L670:
	mov	e4,d10,d15
	mov	d6,d11
.L672:
	call	ips114_draw_point
.L673:
	j	.L66
.L63:
	j	.L67
.L68:
	mov	e4,d8,d15
.L674:
	mov	d6,d11
.L675:
	call	ips114_draw_point
.L676:
	ld.w	d0,[a10]
.L677:
	add	d15,d0
.L671:
	extr.u	d15,d15,#0,#16
.L679:
	utof	d0,d15
.L678:
	madd.f	d4,d12,d0,d13
.L1055:
	call	__f_ftos
	extr.u	d8,d2,#0,#16
.L67:
	jne	d15,d9,.L68
.L681:
	mov	e4,d8,d9
.L680:
	mov	d6,d11
.L682:
	call	ips114_draw_point
.L66:
.L58:
	ret
.L378:
	
__ips114_draw_line_function_end:
	.size	ips114_draw_line,__ips114_draw_line_function_end-ips114_draw_line
.L266:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_char',code,cluster('ips114_show_char')
	.sect	'.text.zf_device_ips114.ips114_show_char'
	.align	2
	
	.global	ips114_show_char
; Function ips114_show_char
.L207:
ips114_show_char:	.type	func
	lea	a10,[a10]-256
.L683:
	mov	e8,d5,d4
	mov	d10,d6
.L687:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L688:
	lt.u	d4,d8,d0
.L685:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#470
.L686:
	call	debug_assert_handler
.L684:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L689:
	lt.u	d4,d9,d0
.L690:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#471
	call	debug_assert_handler
.L397:
	mov	d15,#0
	jeq	d15,#0,.L69
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L70
.L69:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L70:
	movh.a	a15,#@his(ips114_display_font)
	lea	a15,[a15]@los(ips114_display_font)
	ld.bu	d0,[a15]
.L1060:
	mov	d15,#0
	jeq	d15,d0,.L71
.L1061:
	mov	d15,#1
	jeq	d15,d0,.L72
.L1062:
	mov	d15,#2
	jeq	d15,d0,.L73
	j	.L74
.L71:
	add	d15,d8,#5
.L691:
	extr.u	d6,d15,#0,#16
.L692:
	add	d15,d9,#7
.L693:
	extr.u	d7,d15,#0,#16
.L694:
	mov	e4,d9,d8
.L695:
	call	ips114_set_region
.L1063:
	mov	d0,#0
.L696:
	j	.L75
.L76:
	add	d15,d10,#-32
.L1064:
	mul	d15,d15,#6
.L1065:
	movh.a	a15,#@his(ascii_font_6x8)
	lea	a15,[a15]@los(ascii_font_6x8)
.L1066:
	addsc.a	a15,a15,d15,#0
.L1067:
	addsc.a	a15,a15,d0,#0
	ld.bu	d1,[a15]
.L697:
	mov	d2,#0
.L698:
	j	.L77
.L78:
	jz.t	d1:0,.L79
.L1068:
	mov	d15,#6
.L1069:
	madd	d15,d0,d2,d15
.L1070:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1071:
	movh.a	a2,#@his(ips114_pencolor)
	lea	a2,[a2]@los(ips114_pencolor)
	ld.hu	d15,[a2]0
.L1072:
	st.h	[a15],d15
.L1073:
	j	.L80
.L79:
	mov	d15,#6
.L1074:
	madd	d15,d0,d2,d15
.L1075:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1076:
	movh.a	a2,#@his(ips114_bgcolor)
	lea	a2,[a2]@los(ips114_bgcolor)
	ld.hu	d15,[a2]0
.L1077:
	st.h	[a15],d15
.L80:
	sha	d1,#-1
.L1078:
	add	d2,#1
.L77:
	jlt.u	d2,#8,.L78
.L404:
	add	d0,#1
.L75:
	jlt.u	d0,#6,.L76
.L1079:
	mov	d4,#2
	lea	a4,[a10]0
	mov	d5,#48
	call	spi_write_16bit_array
.L401:
	j	.L81
.L72:
	add	d15,d8,#7
.L699:
	extr.u	d6,d15,#0,#16
.L700:
	add	d15,d9,#15
.L701:
	extr.u	d7,d15,#0,#16
.L702:
	mov	e4,d9,d8
.L703:
	call	ips114_set_region
.L1080:
	mov	d3,#0
.L704:
	j	.L82
.L83:
	add	d0,d10,#-32
.L1081:
	mul	d15,d0,#16
.L1082:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L1083:
	addsc.a	a15,a15,d15,#0
.L1084:
	addsc.a	a15,a15,d3,#0
	ld.bu	d0,[a15]
.L705:
	add	d1,d10,#-32
.L1085:
	mul	d15,d1,#16
.L1086:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L1087:
	addsc.a	a15,a15,d15,#0
.L1088:
	addsc.a	a15,a15,d3,#0
	ld.bu	d4,[a15]8
.L707:
	mov	d1,#0
.L708:
	j	.L84
.L85:
	jz.t	d0:0,.L86
.L1089:
	mov	d15,#8
.L1090:
	madd	d2,d3,d1,d15
.L1091:
	mul	d15,d2,#2
	addsc.a	a15,a10,d15,#0
.L1092:
	movh.a	a2,#@his(ips114_pencolor)
	lea	a2,[a2]@los(ips114_pencolor)
	ld.hu	d2,[a2]0
.L1093:
	st.h	[a15],d2
.L1094:
	j	.L87
.L86:
	mov	d15,#8
.L1095:
	madd	d2,d3,d1,d15
.L1096:
	mul	d15,d2,#2
	addsc.a	a15,a10,d15,#0
.L1097:
	movh.a	a2,#@his(ips114_bgcolor)
	lea	a2,[a2]@los(ips114_bgcolor)
	ld.hu	d2,[a2]0
.L1098:
	st.h	[a15],d2
.L87:
	sha	d0,#-1
.L1099:
	add	d1,#1
.L84:
	jlt.u	d1,#8,.L85
.L1100:
	mov	d0,#0
.L706:
	j	.L88
.L89:
	jz.t	d4:0,.L90
.L1101:
	mov	d15,#8
.L1102:
	madd	d1,d3,d0,d15
.L1103:
	add	d15,d1,#64
.L1104:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1105:
	movh.a	a2,#@his(ips114_pencolor)
	lea	a2,[a2]@los(ips114_pencolor)
	ld.hu	d15,[a2]0
.L1106:
	st.h	[a15],d15
.L1107:
	j	.L91
.L90:
	mov	d15,#8
.L1108:
	madd	d15,d3,d0,d15
.L1109:
	add	d15,d15,#64
.L1110:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1111:
	movh.a	a2,#@his(ips114_bgcolor)
	lea	a2,[a2]@los(ips114_bgcolor)
	ld.hu	d15,[a2]0
.L1112:
	st.h	[a15],d15
.L91:
	sha	d4,#-1
.L1113:
	add	d0,#1
.L88:
	jlt.u	d0,#8,.L89
.L409:
	add	d3,#1
.L82:
	jlt.u	d3,#8,.L83
.L1114:
	mov	d4,#2
	lea	a4,[a10]0
	mov	d5,#128
	call	spi_write_16bit_array
.L406:
	j	.L92
.L73:
	j	.L93
.L74:
.L93:
.L92:
.L81:
	mov	d15,#1
	jeq	d15,#0,.L94
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L95
.L94:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L95:
	ret
.L392:
	
__ips114_show_char_function_end:
	.size	ips114_show_char,__ips114_show_char_function_end-ips114_show_char
.L271:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_string',code,cluster('ips114_show_string')
	.sect	'.text.zf_device_ips114.ips114_show_string'
	.align	2
	
	.global	ips114_show_string
; Function ips114_show_string
.L209:
ips114_show_string:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L712:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L713:
	lt.u	d4,d8,d15
.L710:
	movh.a	a4,#@his(.1.str)
.L709:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#557
.L711:
	call	debug_assert_handler
.L1119:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L714:
	lt.u	d4,d9,d15
.L715:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#558
	call	debug_assert_handler
.L417:
	mov	d10,#0
.L716:
	j	.L96
.L97:
	movh.a	a15,#@his(ips114_display_font)
	lea	a15,[a15]@los(ips114_display_font)
	ld.bu	d0,[a15]
.L1120:
	mov	d15,#0
	jeq	d15,d0,.L98
.L1121:
	mov	d15,#1
	jeq	d15,d0,.L99
.L1122:
	mov	d1,#2
	jeq	d1,d0,.L100
	j	.L101
.L98:
	mov	d0,#6
.L718:
	madd	d15,d8,d10,d0
.L719:
	extr.u	d4,d15,#0,#16
.L1123:
	addsc.a	a15,a12,d10,#0
	ld.b	d6,[a15]0
.L720:
	mov	d5,d9
.L721:
	call	ips114_show_char
.L722:
	j	.L102
.L99:
	mov	d15,#8
.L723:
	madd	d15,d8,d10,d15
.L724:
	extr.u	d4,d15,#0,#16
.L1124:
	addsc.a	a15,a12,d10,#0
	ld.b	d6,[a15]0
.L725:
	mov	d5,d9
.L726:
	call	ips114_show_char
.L727:
	j	.L103
.L100:
	j	.L104
.L101:
.L104:
.L103:
.L102:
	add	d10,#1
.L717:
	extr.u	d10,d10,#0,#16
.L96:
	addsc.a	a15,a12,d10,#0
	ld.b	d15,[a15]0
.L1125:
	jne	d15,#0,.L97
.L1126:
	ret
.L412:
	
__ips114_show_string_function_end:
	.size	ips114_show_string,__ips114_show_string_function_end-ips114_show_string
.L276:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_int',code,cluster('ips114_show_int')
	.sect	'.text.zf_device_ips114.ips114_show_int'
	.align	2
	
	.global	ips114_show_int
; Function ips114_show_int
.L211:
ips114_show_int:	.type	func
	sub.a	a10,#16
.L728:
	mov	e10,d5,d4
	mov	d9,d6
.L732:
	mov	d12,d7
.L733:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L734:
	lt.u	d4,d10,d0
.L730:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#587
.L731:
	call	debug_assert_handler
.L729:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L735:
	lt.u	d4,d11,d0
.L736:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#588
	call	debug_assert_handler
.L1131:
	mov	d0,#0
	lt.u	d4,d0,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#589
	call	debug_assert_handler
.L1132:
	mov	d15,#10
	ge.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#590
	call	debug_assert_handler
.L425:
	mov	d15,#1
.L737:
	lea	a4,[a10]0
.L1133:
	mov	d4,#0
.L1134:
	mov	d5,#12
	call	memset
.L1135:
	lea	a4,[a10]0
.L1136:
	mov	d4,#32
.L1137:
	add	d5,d12,#1
	call	memset
.L1138:
	jge.u	d12,#10,.L105
.L1139:
	j	.L106
.L107:
	mul	d15,d15,#10
.L1140:
	add	d12,#-1
.L106:
	jge.u	d12,#1,.L107
.L1141:
	div	e8,d9,d15
.L105:
	lea	a4,[a10]0
.L1142:
	mov	d4,d9
.L738:
	call	func_int_to_str
.L739:
	lea	a4,[a10]0
.L740:
	mov	e4,d11,d10
.L741:
	call	ips114_show_string
.L1143:
	ret
.L419:
	
__ips114_show_int_function_end:
	.size	ips114_show_int,__ips114_show_int_function_end-ips114_show_int
.L281:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_uint',code,cluster('ips114_show_uint')
	.sect	'.text.zf_device_ips114.ips114_show_uint'
	.align	2
	
	.global	ips114_show_uint
; Function ips114_show_uint
.L213:
ips114_show_uint:	.type	func
	sub.a	a10,#16
.L742:
	mov	e10,d5,d4
	mov	d9,d6
.L746:
	mov	d12,d7
.L747:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d15,[a15]0
.L748:
	lt.u	d4,d10,d15
.L744:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#626
.L745:
	call	debug_assert_handler
.L743:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L749:
	lt.u	d4,d11,d15
.L750:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#627
	call	debug_assert_handler
.L1148:
	mov	d15,#0
	lt.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#628
	call	debug_assert_handler
.L1149:
	mov	d15,#10
	ge.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#629
	call	debug_assert_handler
.L437:
	mov	d15,#1
.L751:
	lea	a4,[a10]0
.L1150:
	mov	d4,#0
.L1151:
	mov	d5,#12
	call	memset
.L1152:
	lea	a4,[a10]0
.L1153:
	mov	d4,#32
.L1154:
	mov	d5,d12
.L752:
	call	memset
.L753:
	jge.u	d12,#10,.L108
.L1155:
	j	.L109
.L110:
	mul	d15,d15,#10
.L1156:
	add	d12,#-1
.L109:
	jge.u	d12,#1,.L110
.L1157:
	div.u	e8,d9,d15
.L108:
	lea	a4,[a10]0
.L1158:
	mov	d4,d9
.L754:
	call	func_uint_to_str
.L755:
	lea	a4,[a10]0
.L756:
	mov	e4,d11,d10
.L757:
	call	ips114_show_string
.L1159:
	ret
.L431:
	
__ips114_show_uint_function_end:
	.size	ips114_show_uint,__ips114_show_uint_function_end-ips114_show_uint
.L286:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_float',code,cluster('ips114_show_float')
	.sect	'.text.zf_device_ips114.ips114_show_float'
	.align	2
	
	.global	ips114_show_float
; Function ips114_show_float
.L215:
ips114_show_float:	.type	func
	sub.a	a10,#24
.L758:
	st.w	[a10]20,d4
.L762:
	mov	d14,d5
.L763:
	mov	e10,d7,d6
	ld.bu	d9,[a10]24
.L764:
	ld.bu	d8,[a10]28
.L765:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
	ld.w	d15,[a10]20
.L760:
	lt.u	d4,d15,d0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#668
.L761:
	call	debug_assert_handler
.L759:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L766:
	lt.u	d4,d14,d15
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#669
	call	debug_assert_handler
.L1164:
	mov	d15,#0
	lt.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#670
	call	debug_assert_handler
.L1165:
	mov	d15,#8
	ge.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#671
	call	debug_assert_handler
.L1166:
	mov	d15,#0
	lt.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#672
	call	debug_assert_handler
.L1167:
	mov	d15,#6
	ge.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#673
	call	debug_assert_handler
.L448:
	mov	d12,#0
	mov	d13,#0
.L767:
	addih	d13,d13,#16368
.L1168:
	lea	a4,[a10]0
.L1169:
	mov	d4,#0
.L1170:
	mov	d5,#17
	call	memset
.L1171:
	lea	a4,[a10]0
.L1172:
	mov	d4,#32
.L1173:
	add	d15,d9,d8
.L1174:
	add	d5,d15,#2
	call	memset
.L1175:
	j	.L111
.L112:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L768:
	mov	e4,d13,d12
.L769:
	call	__d_mul
	mov	e12,d3,d2
.L1176:
	add	d9,#-1
.L111:
	jge.u	d9,#1,.L112
.L770:
	mov	e4,d11,d10
.L771:
	call	__d_dtoi
	mov	d15,d2
.L772:
	mov	e4,d13,d12
.L773:
	call	__d_dtoi
.L1177:
	div	e4,d15,d2
	call	__d_itod
	mov	e4,d3,d2
.L774:
	mov	e6,d13,d12
.L775:
	call	__d_mul
	mov	e6,d3,d2
.L776:
	mov	e4,d11,d10
.L777:
	call	__d_sub
	mov	e4,d3,d2
.L1178:
	lea	a4,[a10]0
.L1179:
	mov	d6,d8
.L778:
	call	func_double_to_str
.L779:
	lea	a4,[a10]0
	ld.w	d4,[a10]20
.L780:
	mov	d5,d14
.L782:
	call	ips114_show_string
.L781:
	ret
.L441:
	
__ips114_show_float_function_end:
	.size	ips114_show_float,__ips114_show_float_function_end-ips114_show_float
.L291:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_binary_image',code,cluster('ips114_show_binary_image')
	.sect	'.text.zf_device_ips114.ips114_show_binary_image'
	.align	2
	
	.global	ips114_show_binary_image
; Function ips114_show_binary_image
.L217:
ips114_show_binary_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L787:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L788:
	ld.hu	d13,[a10]4
.L789:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L790:
	lt.u	d4,d8,d0
.L785:
	movh.a	a4,#@his(.1.str)
.L784:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#711
.L786:
	call	debug_assert_handler
.L783:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L791:
	lt.u	d4,d9,d0
.L792:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#712
	call	debug_assert_handler
.L1184:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#713
	call	debug_assert_handler
.L463:
	mul	d4,d12,#2
	call	malloc
.L793:
	mov.aa	a13,a2
.L795:
	mov	d15,#0
	jeq	d15,#0,.L113
	mov	d4,#482
	call	get_port
.L794:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L114
.L113:
	mov	d4,#482
	call	get_port
.L796:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L114:
	add	d15,d8,d12
.L797:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L798:
	add	d15,d9,d13
.L799:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L800:
	mov	e4,d9,d8
.L801:
	call	ips114_set_region
.L1185:
	mov	d8,#0
.L802:
	j	.L115
.L116:
	mul	d15,d8,d11
.L803:
	div.u	e0,d15,d13
.L804:
	mul	d0,d10
.L805:
	mov	d15,#8
.L1186:
	div.u	e0,d0,d15
.L1187:
	addsc.a	a15,a12,d0,#0
.L806:
	mov	d4,#0
.L807:
	j	.L117
.L118:
	mul	d15,d4,d10
.L809:
	div.u	e2,d15,d12
.L810:
	mov	d15,#8
.L1188:
	div.u	e0,d2,d15
.L1189:
	addsc.a	a2,a15,d0,#0
.L1190:
	ld.bu	d0,[a2]
.L812:
	mov	d15,#8
.L1191:
	div.u	e2,d2,d15
.L811:
	sha	d0,d0,d3
.L813:
	jz.t	d0:7,.L119
.L1192:
	mul	d15,d4,#2
	addsc.a	a2,a13,d15,#0
.L1193:
	mov.u	d15,#65535
.L1194:
	st.h	[a2],d15
.L1195:
	j	.L120
.L119:
	mul	d15,d4,#2
	addsc.a	a2,a13,d15,#0
.L1196:
	mov	d15,#0
.L1197:
	st.h	[a2],d15
.L120:
	add	d4,#1
.L117:
	jlt.u	d4,d12,.L118
.L1198:
	mov	d4,#2
.L808:
	mov.aa	a4,a13
.L814:
	mov	d5,d12
.L816:
	call	spi_write_16bit_array
.L815:
	add	d8,#1
.L115:
	jlt.u	d8,d13,.L116
.L1199:
	mov	d15,#1
	jeq	d15,#0,.L121
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L122
.L121:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L122:
	mov.aa	a4,a13
.L817:
	call	free
.L818:
	ret
.L454:
	
__ips114_show_binary_image_function_end:
	.size	ips114_show_binary_image,__ips114_show_binary_image_function_end-ips114_show_binary_image
.L296:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_gray_image',code,cluster('ips114_show_gray_image')
	.sect	'.text.zf_device_ips114.ips114_show_gray_image'
	.align	2
	
	.global	ips114_show_gray_image
; Function ips114_show_gray_image
.L219:
ips114_show_gray_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L823:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L824:
	ld.hu	d13,[a10]4
.L825:
	ld.bu	d14,[a10]8
.L826:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L827:
	lt.u	d4,d8,d0
.L821:
	movh.a	a4,#@his(.1.str)
.L820:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#766
.L822:
	call	debug_assert_handler
.L819:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L828:
	lt.u	d4,d9,d15
.L829:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#767
	call	debug_assert_handler
.L1204:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#768
	call	debug_assert_handler
.L479:
	mul	d4,d12,#2
	call	malloc
.L830:
	mov.aa	a13,a2
.L832:
	mov	d15,#0
	jeq	d15,#0,.L123
	mov	d4,#482
	call	get_port
.L831:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L124
.L123:
	mov	d4,#482
	call	get_port
.L833:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L124:
	add	d15,d8,d12
.L834:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L835:
	add	d15,d9,d13
.L836:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L837:
	mov	e4,d9,d8
.L838:
	call	ips114_set_region
.L1205:
	mov	d8,#0
.L839:
	j	.L125
.L126:
	mul	d15,d8,d11
.L840:
	div.u	e0,d15,d13
.L841:
	mul	d15,d0,d10
.L842:
	addsc.a	a15,a12,d15,#0
.L843:
	mov	d2,#0
.L844:
	j	.L127
.L128:
	mul	d15,d2,d10
.L846:
	div.u	e0,d15,d12
.L1206:
	addsc.a	a2,a15,d0,#0
.L1207:
	ld.bu	d0,[a2]
.L847:
	jne	d14,#0,.L129
.L1208:
	sha	d15,d0,#-3
.L1209:
	and	d15,#31
.L1210:
	sha	d1,d15,#11
.L849:
	sha	d15,d0,#-2
.L1211:
	and	d15,#63
.L1212:
	sha	d15,#5
.L1213:
	or	d1,d15
.L1214:
	sha	d0,#-3
.L848:
	and	d15,d0,#31
.L1215:
	or	d1,d15
.L1216:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1217:
	st.h	[a2],d1
.L1218:
	j	.L130
.L129:
	jge.u	d0,d14,.L131
.L1219:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1220:
	mov	d15,#0
.L1221:
	st.h	[a2],d15
.L1222:
	j	.L132
.L131:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1223:
	mov.u	d15,#65535
.L1224:
	st.h	[a2],d15
.L132:
.L130:
	add	d2,#1
.L127:
	jlt.u	d2,d12,.L128
.L1225:
	mov	d4,#2
	mov.aa	a4,a13
.L850:
	mov	d5,d12
.L851:
	call	spi_write_16bit_array
.L845:
	add	d8,#1
.L125:
	jlt.u	d8,d13,.L126
.L1226:
	mov	d15,#1
	jeq	d15,#0,.L133
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L134
.L133:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L134:
	mov.aa	a4,a13
.L852:
	call	free
.L853:
	ret
.L470:
	
__ips114_show_gray_image_function_end:
	.size	ips114_show_gray_image,__ips114_show_gray_image_function_end-ips114_show_gray_image
.L301:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_rgb565_image',code,cluster('ips114_show_rgb565_image')
	.sect	'.text.zf_device_ips114.ips114_show_rgb565_image'
	.align	2
	
	.global	ips114_show_rgb565_image
; Function ips114_show_rgb565_image
.L221:
ips114_show_rgb565_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L858:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L859:
	ld.hu	d13,[a10]4
.L860:
	ld.bu	d14,[a10]8
.L861:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L862:
	lt.u	d4,d8,d0
.L856:
	movh.a	a4,#@his(.1.str)
.L855:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#826
.L857:
	call	debug_assert_handler
.L854:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d15,[a15]0
.L863:
	lt.u	d4,d9,d15
.L864:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#827
	call	debug_assert_handler
.L1231:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#828
	call	debug_assert_handler
.L496:
	mul	d4,d12,#2
	call	malloc
.L865:
	mov.aa	a13,a2
.L867:
	mov	d15,#0
	jeq	d15,#0,.L135
	mov	d4,#482
	call	get_port
.L866:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L136
.L135:
	mov	d4,#482
	call	get_port
.L868:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L136:
	add	d15,d8,d12
.L869:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L870:
	add	d15,d9,d13
.L871:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L872:
	mov	e4,d9,d8
.L873:
	call	ips114_set_region
.L1232:
	mov	d8,#0
.L874:
	j	.L137
.L138:
	mul	d15,d8,d11
.L875:
	div.u	e0,d15,d13
.L876:
	mul	d15,d0,d10
.L877:
	mul	d15,d15,#2
	addsc.a	a15,a12,d15,#0
.L878:
	mov	d0,#0
.L879:
	j	.L139
.L140:
	mul	d15,d0,#2
	addsc.a	a2,a13,d15,#0
.L881:
	mul	d15,d0,d10
.L882:
	div.u	e2,d15,d12
.L1233:
	mul	d15,d2,#2
	addsc.a	a4,a15,d15,#0
.L1234:
	ld.hu	d15,[a4]0
.L1235:
	st.h	[a2],d15
.L1236:
	add	d0,#1
.L139:
	jlt.u	d0,d12,.L140
.L1237:
	jeq	d14,#0,.L141
.L1238:
	mov	d4,#2
	mul	d5,d12,#2
	mov.aa	a4,a13
.L883:
	call	spi_write_8bit_array
.L880:
	j	.L142
.L141:
	mov	d4,#2
	mov.aa	a4,a13
.L884:
	mov	d5,d12
.L885:
	call	spi_write_16bit_array
.L142:
	add	d8,#1
.L137:
	jlt.u	d8,d13,.L138
.L1239:
	mov	d15,#1
	jeq	d15,#0,.L143
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L144
.L143:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L144:
	mov.aa	a4,a13
.L886:
	call	free
.L887:
	ret
.L486:
	
__ips114_show_rgb565_image_function_end:
	.size	ips114_show_rgb565_image,__ips114_show_rgb565_image_function_end-ips114_show_rgb565_image
.L306:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_wave',code,cluster('ips114_show_wave')
	.sect	'.text.zf_device_ips114.ips114_show_wave'
	.align	2
	
	.global	ips114_show_wave
; Function ips114_show_wave
.L223:
ips114_show_wave:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L892:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L893:
	ld.hu	d13,[a10]4
.L894:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L895:
	lt.u	d4,d8,d0
.L890:
	movh.a	a4,#@his(.1.str)
.L889:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#873
.L891:
	call	debug_assert_handler
.L888:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L896:
	lt.u	d4,d9,d0
.L897:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#874
	call	debug_assert_handler
.L1244:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#875
	call	debug_assert_handler
.L509:
	mul	d4,d12,#2
	call	malloc
.L898:
	mov.aa	a13,a2
.L900:
	mov	d15,#0
	jeq	d15,#0,.L145
	mov	d4,#482
	call	get_port
.L899:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L146
.L145:
	mov	d4,#482
	call	get_port
.L901:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L146:
	add	d15,d8,d12
.L902:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L903:
	add	d15,d9,d13
.L904:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L905:
	mov	e4,d9,d8
.L906:
	call	ips114_set_region
.L1245:
	mov	d14,#0
.L907:
	j	.L147
.L148:
	mov	d0,#0
.L909:
	j	.L149
.L150:
	mul	d15,d0,#2
	addsc.a	a15,a13,d15,#0
.L1246:
	movh.a	a2,#@his(ips114_bgcolor)
	lea	a2,[a2]@los(ips114_bgcolor)
	ld.hu	d15,[a2]0
.L1247:
	st.h	[a15],d15
.L1248:
	add	d0,#1
.L149:
	jlt.u	d0,d12,.L150
.L1249:
	mov	d4,#2
	mov.aa	a4,a13
.L911:
	mov	d5,d12
.L912:
	call	spi_write_16bit_array
.L910:
	add	d14,#1
.L147:
	jlt.u	d14,d13,.L148
.L1250:
	mov	d15,#1
	jeq	d15,#0,.L151
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L152
.L151:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L152:
	mov	d14,#0
.L908:
	j	.L153
.L154:
	mul	d15,d14,d10
.L913:
	div.u	e0,d15,d12
.L914:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L1251:
	ld.hu	d0,[a15]0
.L915:
	add	d15,d13,#-1
.L1252:
	mul	d0,d15
.L916:
	div	e0,d0,d11
.L917:
	add	d15,d14,d8
.L919:
	extr.u	d4,d15,#0,#16
.L1253:
	add	d15,d13,#-1
.L1254:
	sub	d15,d0
.L920:
	add	d15,d9
.L921:
	extr.u	d5,d15,#0,#16
.L1255:
	movh.a	a15,#@his(ips114_pencolor)
	lea	a15,[a15]@los(ips114_pencolor)
	ld.hu	d6,[a15]0
	call	ips114_draw_point
.L918:
	add	d14,#1
.L153:
	jlt.u	d14,d12,.L154
.L1256:
	mov.aa	a4,a13
.L922:
	call	free
.L923:
	ret
.L501:
	
__ips114_show_wave_function_end:
	.size	ips114_show_wave,__ips114_show_wave_function_end-ips114_show_wave
.L311:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_show_chinese',code,cluster('ips114_show_chinese')
	.sect	'.text.zf_device_ips114.ips114_show_chinese'
	.align	2
	
	.global	ips114_show_chinese
; Function ips114_show_chinese
.L225:
ips114_show_chinese:	.type	func
	sub.a	a10,#8
.L924:
	mov	e8,d5,d4
	mov	d10,d6
.L929:
	mov.aa	a12,a4
.L930:
	st.w	[a10]4,d7
.L931:
	ld.hu	d11,[a10]8
.L932:
	movh.a	a15,#@his(ips114_width_max)
	lea	a15,[a15]@los(ips114_width_max)
	ld.hu	d0,[a15]0
.L933:
	lt.u	d4,d8,d0
.L927:
	movh.a	a4,#@his(.1.str)
.L925:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#917
.L928:
	call	debug_assert_handler
.L926:
	movh.a	a15,#@his(ips114_height_max)
	lea	a15,[a15]@los(ips114_height_max)
	ld.hu	d0,[a15]0
.L934:
	lt.u	d4,d9,d0
.L935:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#918
	call	debug_assert_handler
.L1261:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#919
	call	debug_assert_handler
.L523:
	mov	d15,#8
.L1262:
	div	e12,d10,d15
.L936:
	mov	d15,#0
	jeq	d15,#0,.L155
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L156
.L155:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L156:
	ld.w	d15,[a10]4
.L937:
	mul	d15,d10
.L938:
	add	d15,#-1
.L939:
	add	d15,d8
.L940:
	extr.u	d6,d15,#0,#16
.L941:
	add	d15,d9,d10
.L942:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L943:
	mov	e4,d9,d8
.L944:
	call	ips114_set_region
.L1263:
	mov	d8,#0
.L945:
	j	.L157
.L158:
	ld.w	d15,[a10]4
.L946:
	st.w	[a10],d15
.L948:
	mul	d15,d8,d12
.L947:
	addsc.a	a15,a12,d15,#0
.L949:
	j	.L159
.L160:
	mov	d9,#0
.L951:
	j	.L161
.L162:
	mov	d14,#8
.L952:
	j	.L163
.L164:
	ld.bu	d0,[a15]
.L1264:
	add	d15,d14,#-1
.L1265:
	rsub	d15,#0
	sha	d0,d0,d15
.L1266:
	and	d15,d0,#1
.L953:
	jeq	d15,#0,.L165
.L1267:
	mov	d4,#2
	mov	d5,d11
.L954:
	call	spi_write_16bit
.L955:
	j	.L166
.L165:
	mov	d4,#2
	movh.a	a2,#@his(ips114_bgcolor)
	lea	a2,[a2]@los(ips114_bgcolor)
	ld.hu	d5,[a2]0
	call	spi_write_16bit
.L166:
	add	d14,#-1
.L163:
	jge	d14,#1,.L164
.L1268:
	add.a	a15,#1
.L1269:
	add	d9,#1
.L161:
	jlt	d9,d12,.L162
.L1270:
	mul	d15,d12,d10
.L1271:
	mov.d	d0,a15
.L956:
	sub	d0,d12
.L957:
	add	d15,d0
.L950:
	mov.a	a15,d15
.L159:
	ld.w	d15,[a10]
.L958:
	add	d0,d15,#-1
	extr.u	d0,d0,#0,#8
.L959:
	st.w	[a10],d0
.L960:
	jne	d15,#0,.L160
.L961:
	add	d8,#1
.L157:
	jlt	d8,d10,.L158
.L1272:
	mov	d15,#1
	jeq	d15,#0,.L167
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L168
.L167:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L168:
	ret
.L515:
	
__ips114_show_chinese_function_end:
	.size	ips114_show_chinese,__ips114_show_chinese_function_end-ips114_show_chinese
.L316:
	; End of function
	
	.sdecl	'.text.zf_device_ips114.ips114_init',code,cluster('ips114_init')
	.sect	'.text.zf_device_ips114.ips114_init'
	.align	2
	
	.global	ips114_init
; Function ips114_init
.L227:
ips114_init:	.type	func
	sub.a	a10,#16
.L962:
	mov	d15,#211
	st.h	[a10],d15
.L1277:
	mov	d15,#217
	st.h	[a10]4,d15
.L1278:
	mov	d15,#403
	st.h	[a10]8,d15
.L1279:
	mov	d4,#2
.L1280:
	mov	d5,#0
.L1281:
	mov.u	d6,#34560
	addih	d6,d6,#915
.L1282:
	mov	d7,#206
	call	spi_init
.L1283:
	mov	d4,#480
.L1284:
	mov	d5,#1
.L1285:
	mov	d6,#0
.L1286:
	mov	d7,#3
	call	gpio_init
.L1287:
	mov	d4,#481
.L1288:
	mov	d5,#1
.L1289:
	mov	d6,#0
.L1290:
	mov	d7,#3
	call	gpio_init
.L1291:
	mov	d4,#482
.L1292:
	mov	d5,#1
.L1293:
	mov	d6,#1
.L1294:
	mov	d7,#3
	call	gpio_init
.L1295:
	mov	d4,#484
.L1296:
	mov	d5,#1
.L1297:
	mov	d6,#1
.L1298:
	mov	d7,#3
	call	gpio_init
.L1299:
	movh.a	a15,#@his(ips114_display_dir)
	lea	a15,[a15]@los(ips114_display_dir)
	ld.bu	d4,[a15]
	call	ips114_set_dir
.L1300:
	movh.a	a15,#@his(ips114_pencolor)
	lea	a15,[a15]@los(ips114_pencolor)
	ld.hu	d4,[a15]0
.L1301:
	movh.a	a15,#@his(ips114_bgcolor)
	lea	a15,[a15]@los(ips114_bgcolor)
	ld.hu	d5,[a15]0
	call	ips114_set_color
.L1302:
	mov	d15,#0
	jeq	d15,#0,.L169
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L170
.L169:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L170:
	mov	d4,#5
	call	system_delay_ms
.L1303:
	mov	d15,#1
	jeq	d15,#0,.L171
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L172
.L171:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L172:
	mov	d4,#120
	call	system_delay_ms
.L1304:
	mov	d15,#0
	jeq	d15,#0,.L173
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L174
.L173:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L174:
	mov	d4,#17
	call	ips114_write_index
.L1305:
	mov	d4,#54
	call	ips114_write_index
.L1306:
	movh.a	a15,#@his(ips114_display_dir)
	lea	a15,[a15]@los(ips114_display_dir)
	ld.bu	d15,[a15]
.L1307:
	mov	d0,#0
	jeq	d15,d0,.L175
.L1308:
	mov	d0,#1
	jeq	d15,d0,.L176
.L1309:
	mov	d0,#2
	jeq	d15,d0,.L177
.L1310:
	mov	d0,#3
	jeq	d15,d0,.L178
	j	.L179
.L175:
	mov	d4,#2
	mov	d5,#160
	call	spi_write_8bit
.L1311:
	j	.L180
.L176:
	mov	d4,#2
	mov	d5,#112
	call	spi_write_8bit
.L1312:
	j	.L181
.L177:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1313:
	j	.L182
.L178:
	mov	d4,#2
	mov	d5,#192
	call	spi_write_8bit
.L1314:
	j	.L183
.L179:
.L183:
.L182:
.L181:
.L180:
	mov	d4,#58
	call	ips114_write_index
.L1315:
	mov	d4,#2
	mov	d5,#5
	call	spi_write_8bit
.L1316:
	mov	d4,#178
	call	ips114_write_index
.L1317:
	mov	d4,#2
	mov	d5,#12
	call	spi_write_8bit
.L1318:
	mov	d4,#2
	mov	d5,#12
	call	spi_write_8bit
.L1319:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1320:
	mov	d4,#2
	mov	d5,#51
	call	spi_write_8bit
.L1321:
	mov	d4,#2
	mov	d5,#51
	call	spi_write_8bit
.L1322:
	mov	d4,#183
	call	ips114_write_index
.L1323:
	mov	d4,#2
	mov	d5,#53
	call	spi_write_8bit
.L1324:
	mov	d4,#187
	call	ips114_write_index
.L1325:
	mov	d4,#2
	mov	d5,#55
	call	spi_write_8bit
.L1326:
	mov	d4,#192
	call	ips114_write_index
.L1327:
	mov	d4,#2
	mov	d5,#44
	call	spi_write_8bit
.L1328:
	mov	d4,#194
	call	ips114_write_index
.L1329:
	mov	d4,#2
	mov	d5,#1
	call	spi_write_8bit
.L1330:
	mov	d4,#195
	call	ips114_write_index
.L1331:
	mov	d4,#2
	mov	d5,#18
	call	spi_write_8bit
.L1332:
	mov	d4,#196
	call	ips114_write_index
.L1333:
	mov	d4,#2
	mov	d5,#32
	call	spi_write_8bit
.L1334:
	mov	d4,#198
	call	ips114_write_index
.L1335:
	mov	d4,#2
	mov	d5,#15
	call	spi_write_8bit
.L1336:
	mov	d4,#208
	call	ips114_write_index
.L1337:
	mov	d4,#2
	mov	d5,#164
	call	spi_write_8bit
.L1338:
	mov	d4,#2
	mov	d5,#161
	call	spi_write_8bit
.L1339:
	mov	d4,#224
	call	ips114_write_index
.L1340:
	mov	d4,#2
	mov	d5,#208
	call	spi_write_8bit
.L1341:
	mov	d4,#2
	mov	d5,#4
	call	spi_write_8bit
.L1342:
	mov	d4,#2
	mov	d5,#13
	call	spi_write_8bit
.L1343:
	mov	d4,#2
	mov	d5,#17
	call	spi_write_8bit
.L1344:
	mov	d4,#2
	mov	d5,#19
	call	spi_write_8bit
.L1345:
	mov	d4,#2
	mov	d5,#43
	call	spi_write_8bit
.L1346:
	mov	d4,#2
	mov	d5,#63
	call	spi_write_8bit
.L1347:
	mov	d4,#2
	mov	d5,#84
	call	spi_write_8bit
.L1348:
	mov	d4,#2
	mov	d5,#76
	call	spi_write_8bit
.L1349:
	mov	d4,#2
	mov	d5,#24
	call	spi_write_8bit
.L1350:
	mov	d4,#2
	mov	d5,#13
	call	spi_write_8bit
.L1351:
	mov	d4,#2
	mov	d5,#11
	call	spi_write_8bit
.L1352:
	mov	d4,#2
	mov	d5,#31
	call	spi_write_8bit
.L1353:
	mov	d4,#2
	mov	d5,#35
	call	spi_write_8bit
.L1354:
	mov	d4,#225
	call	ips114_write_index
.L1355:
	mov	d4,#2
	mov	d5,#208
	call	spi_write_8bit
.L1356:
	mov	d4,#2
	mov	d5,#4
	call	spi_write_8bit
.L1357:
	mov	d4,#2
	mov	d5,#12
	call	spi_write_8bit
.L1358:
	mov	d4,#2
	mov	d5,#17
	call	spi_write_8bit
.L1359:
	mov	d4,#2
	mov	d5,#19
	call	spi_write_8bit
.L1360:
	mov	d4,#2
	mov	d5,#44
	call	spi_write_8bit
.L1361:
	mov	d4,#2
	mov	d5,#63
	call	spi_write_8bit
.L1362:
	mov	d4,#2
	mov	d5,#68
	call	spi_write_8bit
.L1363:
	mov	d4,#2
	mov	d5,#81
	call	spi_write_8bit
.L1364:
	mov	d4,#2
	mov	d5,#47
	call	spi_write_8bit
.L1365:
	mov	d4,#2
	mov	d5,#31
	call	spi_write_8bit
.L1366:
	mov	d4,#2
	mov	d5,#31
	call	spi_write_8bit
.L1367:
	mov	d4,#2
	mov	d5,#32
	call	spi_write_8bit
.L1368:
	mov	d4,#2
	mov	d5,#35
	call	spi_write_8bit
.L1369:
	mov	d4,#33
	call	ips114_write_index
.L1370:
	mov	d4,#41
	call	ips114_write_index
.L1371:
	mov	d15,#1
	jeq	d15,#0,.L184
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L185
.L184:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L185:
	call	ips114_clear
.L1372:
	call	ips114_debug_init
.L1373:
	ret
.L531:
	
__ips114_init_function_end:
	.size	ips114_init,__ips114_init_function_end-ips114_init
.L321:
	; End of function
	
	.sdecl	'.data.zf_device_ips114.ips114_width_max',data,cluster('ips114_width_max')
	.sect	'.data.zf_device_ips114.ips114_width_max'
	.global	ips114_width_max
	.align	2
ips114_width_max:	.type	object
	.size	ips114_width_max,2
	.half	240
	.sdecl	'.data.zf_device_ips114.ips114_height_max',data,cluster('ips114_height_max')
	.sect	'.data.zf_device_ips114.ips114_height_max'
	.global	ips114_height_max
	.align	2
ips114_height_max:	.type	object
	.size	ips114_height_max,2
	.half	135
	.sdecl	'.data.zf_device_ips114.ips114_pencolor',data,cluster('ips114_pencolor')
	.sect	'.data.zf_device_ips114.ips114_pencolor'
	.align	2
ips114_pencolor:	.type	object
	.size	ips114_pencolor,2
	.half	63488
	.sdecl	'.data.zf_device_ips114.ips114_bgcolor',data,cluster('ips114_bgcolor')
	.sect	'.data.zf_device_ips114.ips114_bgcolor'
	.align	2
ips114_bgcolor:	.type	object
	.size	ips114_bgcolor,2
	.half	65535
	.sdecl	'.data.zf_device_ips114.ips114_display_dir',data,cluster('ips114_display_dir')
	.sect	'.data.zf_device_ips114.ips114_display_dir'
ips114_display_dir:	.type	object
	.size	ips114_display_dir,1
	.space	1
	.sdecl	'.data.zf_device_ips114.ips114_display_font',data,cluster('ips114_display_font')
	.sect	'.data.zf_device_ips114.ips114_display_font'
ips114_display_font:	.type	object
	.size	ips114_display_font,1
	.byte	1
	.sdecl	'.rodata.zf_device_ips114..1.str',data,rom
	.sect	'.rodata.zf_device_ips114..1.str'
.1.str:	.type	object
	.size	.1.str,42
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,105,112,115,49,49,52,46
	.byte	99
	.space	1
	.calls	'ips114_draw_line','__f_ftos'
	.calls	'ips114_show_float','__d_mul'
	.calls	'ips114_show_float','__d_dtoi'
	.calls	'ips114_show_float','__d_itod'
	.calls	'ips114_show_float','__d_sub'
	.calls	'__INDIRECT__','ips114_clear'
	.calls	'__INDIRECT__','ips114_show_string'
	.calls	'ips114_write_index','get_port'
	.calls	'ips114_write_index','spi_write_8bit'
	.calls	'ips114_set_region','debug_assert_handler'
	.calls	'ips114_set_region','ips114_write_index'
	.calls	'ips114_set_region','spi_write_16bit'
	.calls	'ips114_debug_init','debug_output_struct_init'
	.calls	'ips114_debug_init','debug_output_init'
	.calls	'ips114_clear','get_port'
	.calls	'ips114_clear','ips114_set_region'
	.calls	'ips114_clear','spi_write_16bit_array'
	.calls	'ips114_full','get_port'
	.calls	'ips114_full','ips114_set_region'
	.calls	'ips114_full','spi_write_16bit_array'
	.calls	'ips114_draw_point','debug_assert_handler'
	.calls	'ips114_draw_point','get_port'
	.calls	'ips114_draw_point','ips114_set_region'
	.calls	'ips114_draw_point','spi_write_16bit'
	.calls	'ips114_draw_line','debug_assert_handler'
	.calls	'ips114_draw_line','ips114_draw_point'
	.calls	'ips114_show_char','debug_assert_handler'
	.calls	'ips114_show_char','get_port'
	.calls	'ips114_show_char','ips114_set_region'
	.calls	'ips114_show_char','spi_write_16bit_array'
	.calls	'ips114_show_string','debug_assert_handler'
	.calls	'ips114_show_string','ips114_show_char'
	.calls	'ips114_show_int','debug_assert_handler'
	.calls	'ips114_show_int','memset'
	.calls	'ips114_show_int','func_int_to_str'
	.calls	'ips114_show_int','ips114_show_string'
	.calls	'ips114_show_uint','debug_assert_handler'
	.calls	'ips114_show_uint','memset'
	.calls	'ips114_show_uint','func_uint_to_str'
	.calls	'ips114_show_uint','ips114_show_string'
	.calls	'ips114_show_float','debug_assert_handler'
	.calls	'ips114_show_float','memset'
	.calls	'ips114_show_float','func_double_to_str'
	.calls	'ips114_show_float','ips114_show_string'
	.calls	'ips114_show_binary_image','debug_assert_handler'
	.calls	'ips114_show_binary_image','get_port'
	.calls	'ips114_show_binary_image','ips114_set_region'
	.calls	'ips114_show_binary_image','spi_write_16bit_array'
	.calls	'ips114_show_gray_image','debug_assert_handler'
	.calls	'ips114_show_gray_image','get_port'
	.calls	'ips114_show_gray_image','ips114_set_region'
	.calls	'ips114_show_gray_image','spi_write_16bit_array'
	.calls	'ips114_show_rgb565_image','debug_assert_handler'
	.calls	'ips114_show_rgb565_image','get_port'
	.calls	'ips114_show_rgb565_image','ips114_set_region'
	.calls	'ips114_show_rgb565_image','spi_write_8bit_array'
	.calls	'ips114_show_rgb565_image','spi_write_16bit_array'
	.calls	'ips114_show_wave','debug_assert_handler'
	.calls	'ips114_show_wave','get_port'
	.calls	'ips114_show_wave','ips114_set_region'
	.calls	'ips114_show_wave','spi_write_16bit_array'
	.calls	'ips114_show_wave','ips114_draw_point'
	.calls	'ips114_show_chinese','debug_assert_handler'
	.calls	'ips114_show_chinese','get_port'
	.calls	'ips114_show_chinese','ips114_set_region'
	.calls	'ips114_show_chinese','spi_write_16bit'
	.calls	'ips114_init','spi_init'
	.calls	'ips114_init','gpio_init'
	.calls	'ips114_init','ips114_set_dir'
	.calls	'ips114_init','ips114_set_color'
	.calls	'ips114_init','get_port'
	.calls	'ips114_init','system_delay_ms'
	.calls	'ips114_init','ips114_write_index'
	.calls	'ips114_init','spi_write_8bit'
	.calls	'ips114_init','ips114_clear'
	.calls	'ips114_init','ips114_debug_init'
	.calls	'ips114_write_index','',0
	.calls	'ips114_set_region','',0
	.calls	'ips114_debug_init','',24
	.calls	'ips114_clear','',0
	.calls	'ips114_full','',0
	.calls	'ips114_set_dir','',0
	.calls	'ips114_set_font','',0
	.calls	'ips114_set_color','',0
	.calls	'ips114_draw_point','',0
	.calls	'ips114_draw_line','',8
	.calls	'ips114_show_char','',256
	.calls	'ips114_show_string','',0
	.calls	'ips114_show_int','',16
	.calls	'ips114_show_uint','',16
	.calls	'ips114_show_float','',24
	.calls	'ips114_show_binary_image','',0
	.calls	'ips114_show_gray_image','',0
	.calls	'ips114_show_rgb565_image','',0
	.calls	'ips114_show_wave','',0
	.calls	'ips114_show_chinese','',8
	.extern	memset
	.extern	debug_assert_handler
	.extern	debug_output_struct_init
	.extern	debug_output_init
	.extern	ascii_font_8x16
	.extern	ascii_font_6x8
	.extern	func_int_to_str
	.extern	func_uint_to_str
	.extern	func_double_to_str
	.extern	system_delay_ms
	.extern	get_port
	.extern	gpio_init
	.extern	spi_write_8bit
	.extern	spi_write_8bit_array
	.extern	spi_write_16bit
	.extern	spi_write_16bit_array
	.extern	spi_init
	.extern	malloc
	.extern	free
	.extern	__f_ftos
	.extern	__d_mul
	.extern	__d_dtoi
	.extern	__d_itod
	.extern	__d_sub
	.extern	__INDIRECT__
	.calls	'ips114_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L229:
	.word	42042
	.half	3
	.word	.L230
	.byte	4
.L228:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L231
	.byte	2,1,1,3
	.word	204
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	207
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L389:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	252
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	264
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	344
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	318
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	350
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	350
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	318
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L426:
	.byte	7
	.byte	'int',0,4,5
.L398:
	.byte	7
	.byte	'unsigned char',0,1,8
.L373:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1385
	.byte	4,2,35,0,0,14,4
	.word	459
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1728
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1943
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2160
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2380
	.byte	4,2,35,0,0,14,24
	.word	459
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2703
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3007
	.byte	4,2,35,0,0,14,8
	.word	459
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3332
	.byte	4,2,35,0,0,14,12
	.word	459
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3672
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	436
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4038
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4324
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4471
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	436
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4640
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4812
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	476
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5161
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5335
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6000
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6348
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6472
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6556
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6736
	.byte	4,2,35,0,0,14,76
	.word	459
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7076
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	774
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1345
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1464
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1504
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1688
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1903
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2120
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2340
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1504
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2654
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2694
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2967
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3283
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3323
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3623
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3663
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	3998
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4284
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3323
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4431
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4600
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4772
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4947
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5121
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5295
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5471
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5627
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5960
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6308
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3323
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6432
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6681
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6940
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6980
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7036
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7603
	.byte	4,3,35,252,1,0,16
	.word	7643
	.byte	3
	.word	8246
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8251
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	459
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8256
	.byte	6,0,19
	.byte	'__free',0,1,1,1,1,20
	.word	350
	.byte	0,16
	.word	350
	.byte	21
	.byte	'__alloc',0
	.word	8455
	.byte	1,1,1,1,20
	.word	436
	.byte	0,22
	.byte	'memset',0,5,56,17
	.word	350
	.byte	1,1,1,1,23,5,56,33
	.word	350
	.byte	23,5,56,36
	.word	452
	.byte	23,5,56,41
	.word	436
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	8527
	.byte	24
	.byte	'debug_assert_handler',0,6,112,9,1,1,1,1,5
	.byte	'pass',0,6,112,47
	.word	459
	.byte	5
	.byte	'file',0,6,112,59
	.word	8535
	.byte	5
	.byte	'line',0,6,112,69
	.word	452
	.byte	0,25
	.word	8527
.L415:
	.byte	3
	.word	8609
	.byte	26,1,1,20
	.word	8614
	.byte	0,3
	.word	8619
	.byte	26,1,1,20
	.word	476
	.byte	20
	.word	476
	.byte	20
	.word	8614
	.byte	0,3
	.word	8633
.L545:
	.byte	27,6,86,9,20,13
	.byte	'type_index',0
	.word	476
	.byte	2,2,35,0,13
	.byte	'display_x_max',0
	.word	476
	.byte	2,2,35,2,13
	.byte	'display_y_max',0
	.word	476
	.byte	2,2,35,4,13
	.byte	'font_x_size',0
	.word	459
	.byte	1,2,35,6,13
	.byte	'font_y_size',0
	.word	459
	.byte	1,2,35,7,13
	.byte	'output_uart',0
	.word	8628
	.byte	4,2,35,8,13
	.byte	'output_screen',0
	.word	8652
	.byte	4,2,35,12,13
	.byte	'output_screen_clear',0
	.word	207
	.byte	4,2,35,16,0,3
	.word	8657
	.byte	24
	.byte	'debug_output_struct_init',0,6,114,9,1,1,1,1,5
	.byte	'info',0,6,114,62
	.word	8844
	.byte	0,24
	.byte	'debug_output_init',0,6,115,9,1,1,1,1,5
	.byte	'info',0,6,115,62
	.word	8844
	.byte	0,24
	.byte	'func_int_to_str',0,7,80,13,1,1,1,1,5
	.byte	'str',0,7,80,56
	.word	8535
	.byte	5
	.byte	'number',0,7,80,67
	.word	452
	.byte	0
.L352:
	.byte	7
	.byte	'unsigned long int',0,4,7,24
	.byte	'func_uint_to_str',0,7,82,13,1,1,1,1,5
	.byte	'str',0,7,82,56
	.word	8535
	.byte	5
	.byte	'number',0,7,82,68
	.word	8988
	.byte	0
.L449:
	.byte	7
	.byte	'double',0,8,4,24
	.byte	'func_double_to_str',0,7,86,13,1,1,1,1,5
	.byte	'str',0,7,86,56
	.word	8535
	.byte	5
	.byte	'number',0,7,86,68
	.word	9062
	.byte	5
	.byte	'point_bit',0,7,86,82
	.word	459
	.byte	0,24
	.byte	'system_delay_ms',0,8,46,9,1,1,1,1,5
	.byte	'time',0,8,46,45
	.word	8988
	.byte	0,28
	.word	212
	.byte	29
	.word	238
	.byte	6,0,28
	.word	273
	.byte	29
	.word	305
	.byte	6,0,28
	.word	355
	.byte	29
	.word	374
	.byte	6,0,28
	.word	390
	.byte	29
	.word	405
	.byte	29
	.word	419
	.byte	6,0,28
	.word	8359
	.byte	29
	.word	8387
	.byte	29
	.word	8401
	.byte	29
	.word	8419
	.byte	6,0,17,9,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'get_port',0,9,114,13
	.word	8251
	.byte	1,1,1,1,5
	.byte	'pin',0,9,114,56
	.word	9258
	.byte	0,17,9,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,9,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,24
	.byte	'gpio_init',0,9,143,1,7,1,1,1,1,5
	.byte	'pin',0,9,143,1,40
	.word	9258
	.byte	5
	.byte	'dir',0,9,143,1,59
	.word	11232
	.byte	5
	.byte	'dat',0,9,143,1,70
	.word	459
	.byte	5
	.byte	'pinconf',0,9,143,1,90
	.word	11250
	.byte	0,17,10,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,25
	.word	459
	.byte	24
	.byte	'spi_write_8bit',0,10,143,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,143,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,143,1,80
	.word	11451
	.byte	0,25
	.word	459
.L457:
	.byte	3
	.word	11510
	.byte	24
	.byte	'spi_write_8bit_array',0,10,144,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,144,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,144,1,81
	.word	11515
	.byte	5
	.byte	'len',0,10,144,1,94
	.word	8988
	.byte	0,25
	.word	476
	.byte	24
	.byte	'spi_write_16bit',0,10,146,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,146,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,146,1,81
	.word	11593
	.byte	0,25
	.word	476
.L489:
	.byte	3
	.word	11653
	.byte	24
	.byte	'spi_write_16bit_array',0,10,147,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,147,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,147,1,82
	.word	11658
	.byte	5
	.byte	'len',0,10,147,1,95
	.word	8988
	.byte	0,17,10,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,10,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,10,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,10,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,10,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,24
	.byte	'spi_init',0,10,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,170,1,61
	.word	11413
	.byte	5
	.byte	'mode',0,10,170,1,82
	.word	11737
	.byte	5
	.byte	'baud',0,10,170,1,95
	.word	8988
	.byte	5
	.byte	'sck_pin',0,10,170,1,118
	.word	11791
	.byte	5
	.byte	'mosi_pin',0,10,170,1,145,1
	.word	12064
	.byte	5
	.byte	'miso_pin',0,10,170,1,173,1
	.word	12318
	.byte	5
	.byte	'cs_pin',0,10,170,1,199,1
	.word	12572
	.byte	0
.L350:
	.byte	3
	.word	476
.L356:
	.byte	25
	.word	476
.L362:
	.byte	17,11,93,9,1,18
	.byte	'IPS114_PORTAIT',0,0,18
	.byte	'IPS114_PORTAIT_180',0,1,18
	.byte	'IPS114_CROSSWISE',0,2,18
	.byte	'IPS114_CROSSWISE_180',0,3,0
.L365:
	.byte	17,11,101,9,1,18
	.byte	'IPS114_6X8_FONT',0,0,18
	.byte	'IPS114_8X16_FONT',0,1,18
	.byte	'IPS114_16X16_FONT',0,2,0
.L368:
	.byte	25
	.word	476
.L370:
	.byte	25
	.word	476
.L376:
	.byte	25
	.word	476
.L383:
	.byte	25
	.word	476
.L386:
	.byte	7
	.byte	'short int',0,2,5
.L395:
	.byte	25
	.word	8527
.L402:
	.byte	14,96
	.word	476
	.byte	15,47,0
.L407:
	.byte	14,128,2
	.word	476
	.byte	15,127,0
.L422:
	.byte	25
	.word	452
.L429:
	.byte	14,12
	.word	8527
	.byte	15,11,0
.L434:
	.byte	25
	.word	8988
.L444:
	.byte	25
	.word	9062
.L452:
	.byte	14,17
	.word	8527
	.byte	15,16,0
.L521:
	.byte	25
	.word	476
.L533:
	.byte	25
	.word	459
.L536:
	.byte	25
	.word	476
.L538:
	.byte	25
	.word	476
.L540:
	.byte	25
	.word	476
.L542:
	.byte	25
	.word	476
	.byte	30
	.byte	'__INDIRECT__',0,12,1,1,1,1,1,31
	.byte	'__wchar_t',0,12,1,1
	.word	13723
	.byte	31
	.byte	'__size_t',0,12,1,1
	.word	436
	.byte	31
	.byte	'__ptrdiff_t',0,12,1,1
	.word	452
	.byte	32,1,3
	.word	13898
	.byte	31
	.byte	'__codeptr',0,12,1,1
	.word	13900
	.byte	31
	.byte	'__intptr_t',0,12,1,1
	.word	452
	.byte	31
	.byte	'__uintptr_t',0,12,1,1
	.word	436
	.byte	31
	.byte	'size_t',0,13,31,25
	.word	436
	.byte	31
	.byte	'_iob_flag_t',0,13,82,25
	.word	476
	.byte	31
	.byte	'boolean',0,14,101,29
	.word	459
	.byte	31
	.byte	'uint8',0,14,105,29
	.word	459
	.byte	31
	.byte	'uint16',0,14,109,29
	.word	476
	.byte	31
	.byte	'uint32',0,14,113,29
	.word	8988
	.byte	31
	.byte	'uint64',0,14,118,29
	.word	318
	.byte	31
	.byte	'sint16',0,14,126,29
	.word	13723
	.byte	7
	.byte	'long int',0,4,5,31
	.byte	'sint32',0,14,131,1,29
	.word	14087
	.byte	7
	.byte	'long long int',0,8,5,31
	.byte	'sint64',0,14,138,1,29
	.word	14115
	.byte	31
	.byte	'float32',0,14,167,1,29
	.word	264
	.byte	31
	.byte	'pvoid',0,15,57,28
	.word	350
	.byte	31
	.byte	'Ifx_TickTime',0,15,79,28
	.word	14115
	.byte	7
	.byte	'char',0,1,6,31
	.byte	'int8',0,16,54,29
	.word	14200
	.byte	31
	.byte	'int16',0,16,55,29
	.word	13723
	.byte	31
	.byte	'int32',0,16,56,29
	.word	452
	.byte	31
	.byte	'int64',0,16,57,29
	.word	14115
	.byte	31
	.byte	'debug_output_struct',0,6,99,2
	.word	8657
	.byte	14,16
	.word	459
	.byte	15,15,0,33
	.word	14291
	.byte	34,0,25
	.word	14300
	.byte	35
	.byte	'ascii_font_8x16',0,17,61,25
	.word	14307
	.byte	1,1,14,6
	.word	459
	.byte	15,5,0,33
	.word	14338
	.byte	34,0,25
	.word	14347
	.byte	35
	.byte	'ascii_font_6x8',0,17,62,25
	.word	14354
	.byte	1,1,31
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7076
	.byte	31
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6989
	.byte	31
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3332
	.byte	31
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1385
	.byte	31
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2380
	.byte	31
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1513
	.byte	31
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2160
	.byte	31
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1728
	.byte	31
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1943
	.byte	31
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6348
	.byte	31
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6472
	.byte	31
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6556
	.byte	31
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6736
	.byte	31
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4987
	.byte	31
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5511
	.byte	31
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5161
	.byte	31
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5335
	.byte	31
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6000
	.byte	31
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	814
	.byte	31
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4324
	.byte	31
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4812
	.byte	31
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4471
	.byte	31
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4640
	.byte	31
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5667
	.byte	31
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	498
	.byte	31
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4038
	.byte	31
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3672
	.byte	31
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2703
	.byte	31
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3007
	.byte	31
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7603
	.byte	31
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7036
	.byte	31
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3623
	.byte	31
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1464
	.byte	31
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2654
	.byte	31
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1688
	.byte	31
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2340
	.byte	31
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1903
	.byte	31
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2120
	.byte	31
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6432
	.byte	31
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6681
	.byte	31
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6940
	.byte	31
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6308
	.byte	31
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5121
	.byte	31
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5627
	.byte	31
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5295
	.byte	31
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5471
	.byte	31
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1345
	.byte	31
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5960
	.byte	31
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4431
	.byte	31
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4947
	.byte	31
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4600
	.byte	31
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4772
	.byte	31
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	774
	.byte	31
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4284
	.byte	31
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	3998
	.byte	31
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2967
	.byte	31
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3283
	.byte	16
	.word	7643
	.byte	31
	.byte	'Ifx_P',0,4,139,6,3
	.word	15702
	.byte	17,18,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,31
	.byte	'IfxScu_WDTCON1_IR',0,18,255,10,3
	.word	15722
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,19,45,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_ACCEN0_Bits',0,19,79,3
	.word	15844
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,19,82,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1_Bits',0,19,85,3
	.word	16401
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,19,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	436
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,19,94,3
	.word	16478
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,19,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	459
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	459
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON0_Bits',0,19,111,3
	.word	16614
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,19,114,16,4,11
	.byte	'CANDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	459
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON1_Bits',0,19,126,3
	.word	16894
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,19,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON2_Bits',0,19,135,1,3
	.word	17132
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,19,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	459
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	459
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON3_Bits',0,19,150,1,3
	.word	17260
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,19,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	459
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	459
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON4_Bits',0,19,165,1,3
	.word	17503
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,19,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON5_Bits',0,19,174,1,3
	.word	17738
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,19,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6_Bits',0,19,181,1,3
	.word	17866
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,19,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7_Bits',0,19,188,1,3
	.word	17966
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,19,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	459
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CHIPID_Bits',0,19,202,1,3
	.word	18066
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,19,205,1,16,4,11
	.byte	'PWD',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	436
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSCON_Bits',0,19,213,1,3
	.word	18274
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,19,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	476
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSLIM_Bits',0,19,225,1,3
	.word	18439
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,19,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,19,235,1,3
	.word	18622
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,19,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	436
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EICR_Bits',0,19,129,2,3
	.word	18776
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,19,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR_Bits',0,19,143,2,3
	.word	19140
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,19,146,2,16,4,11
	.byte	'POL',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	476
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	459
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_EMSR_Bits',0,19,159,2,3
	.word	19351
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,19,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	436
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG_Bits',0,19,167,2,3
	.word	19603
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,19,170,2,16,4,11
	.byte	'ARI',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG_Bits',0,19,175,2,3
	.word	19721
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,19,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR13CON_Bits',0,19,185,2,3
	.word	19832
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,19,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR33CON_Bits',0,19,195,2,3
	.word	19995
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,19,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,19,205,2,3
	.word	20158
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,19,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,19,215,2,3
	.word	20316
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,19,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	459
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	459
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	459
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	459
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	476
	.byte	10,0,2,35,2,0,31
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,19,232,2,3
	.word	20481
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,19,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	459
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	476
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	459
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,19,245,2,3
	.word	20810
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,19,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROVMON_Bits',0,19,255,2,3
	.word	21031
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,19,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,19,142,3,3
	.word	21194
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,19,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,19,152,3,3
	.word	21466
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,19,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,19,162,3,3
	.word	21619
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,19,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,19,172,3,3
	.word	21775
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,19,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,19,181,3,3
	.word	21937
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,19,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,19,191,3,3
	.word	22080
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,19,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,19,200,3,3
	.word	22245
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,19,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,19,211,3,3
	.word	22390
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,19,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	459
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,19,222,3,3
	.word	22571
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,19,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,19,232,3,3
	.word	22745
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,19,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,19,241,3,3
	.word	22905
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,19,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,19,130,4,3
	.word	23049
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,19,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,19,139,4,3
	.word	23323
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,19,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,19,149,4,3
	.word	23462
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,19,152,4,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	459
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	476
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	459
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	459
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_EXTCON_Bits',0,19,163,4,3
	.word	23625
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,19,166,4,16,4,11
	.byte	'STEP',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	476
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_FDR_Bits',0,19,174,4,3
	.word	23843
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,19,177,4,16,4,11
	.byte	'FS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_FMR_Bits',0,19,197,4,3
	.word	24006
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,19,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_ID_Bits',0,19,205,4,3
	.word	24342
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,19,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	459
	.byte	2,0,2,35,3,0,31
	.byte	'Ifx_SCU_IGCR_Bits',0,19,232,4,3
	.word	24449
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,19,235,4,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_IN_Bits',0,19,240,4,3
	.word	24901
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,19,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_IOCR_Bits',0,19,250,4,3
	.word	25000
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,19,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	476
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,19,131,5,3
	.word	25150
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,19,134,5,16,4,11
	.byte	'SEED',0,4
	.word	436
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,19,141,5,3
	.word	25299
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,19,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	436
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,19,149,5,3
	.word	25460
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,19,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	476
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LCLCON_Bits',0,19,158,5,3
	.word	25590
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,19,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST_Bits',0,19,166,5,3
	.word	25722
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,19,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	459
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	476
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_MANID_Bits',0,19,174,5,3
	.word	25837
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,19,177,5,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	476
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	476
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_OMR_Bits',0,19,185,5,3
	.word	25948
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,19,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	459
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	459
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	459
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_OSCCON_Bits',0,19,209,5,3
	.word	26106
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,19,212,5,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_OUT_Bits',0,19,217,5,3
	.word	26518
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,19,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	476
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	6,0,2,35,3,0,31
	.byte	'Ifx_SCU_OVCCON_Bits',0,19,233,5,3
	.word	26619
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,19,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	436
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,19,242,5,3
	.word	26886
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,19,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC_Bits',0,19,250,5,3
	.word	27022
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,19,253,5,16,4,11
	.byte	'PD0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDR_Bits',0,19,132,6,3
	.word	27133
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,19,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR_Bits',0,19,146,6,3
	.word	27266
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,19,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLCON0_Bits',0,19,166,6,3
	.word	27469
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,19,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	459
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	459
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON1_Bits',0,19,177,6,3
	.word	27825
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,19,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON2_Bits',0,19,184,6,3
	.word	28003
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,19,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	459
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,19,204,6,3
	.word	28103
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,19,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	459
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,19,215,6,3
	.word	28473
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,19,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,19,227,6,3
	.word	28659
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,19,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,19,241,6,3
	.word	28857
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,19,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR_Bits',0,19,251,6,3
	.word	29090
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,19,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	459
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	459
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	459
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,19,153,7,3
	.word	29242
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,19,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	459
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,19,170,7,3
	.word	29809
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,19,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	459
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,19,187,7,3
	.word	30103
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,19,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	459
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	459
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,19,214,7,3
	.word	30381
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,19,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	476
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,19,230,7,3
	.word	30877
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,19,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	476
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON2_Bits',0,19,243,7,3
	.word	31190
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,19,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	459
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON_Bits',0,19,129,8,3
	.word	31399
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,19,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	459
	.byte	3,0,2,35,3,0,31
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,19,155,8,3
	.word	31610
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,19,158,8,16,4,11
	.byte	'HBT',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	436
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON_Bits',0,19,162,8,3
	.word	32042
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,19,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	459
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	459
	.byte	7,0,2,35,3,0,31
	.byte	'Ifx_SCU_STSTAT_Bits',0,19,178,8,3
	.word	32138
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,19,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,19,186,8,3
	.word	32398
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,19,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	459
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	436
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON_Bits',0,19,198,8,3
	.word	32523
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,19,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,19,208,8,3
	.word	32720
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,19,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,19,218,8,3
	.word	32873
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,19,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET_Bits',0,19,228,8,3
	.word	33026
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,19,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,19,238,8,3
	.word	33179
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,19,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	33334
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33334
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33334
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33334
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,19,247,8,3
	.word	33350
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,19,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,19,134,9,3
	.word	33480
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,19,137,9,16,4,11
	.byte	'AE',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,19,150,9,3
	.word	33718
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,19,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	33334
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33334
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33334
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33334
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,19,159,9,3
	.word	33941
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,19,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,19,175,9,3
	.word	34067
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,19,178,9,16,4,11
	.byte	'AE',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,19,191,9,3
	.word	34319
	.byte	12,19,199,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15844
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN0',0,19,204,9,3
	.word	34538
	.byte	12,19,207,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16401
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1',0,19,212,9,3
	.word	34602
	.byte	12,19,215,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16478
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS',0,19,220,9,3
	.word	34666
	.byte	12,19,223,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16614
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON0',0,19,228,9,3
	.word	34731
	.byte	12,19,231,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16894
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON1',0,19,236,9,3
	.word	34796
	.byte	12,19,239,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17132
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON2',0,19,244,9,3
	.word	34861
	.byte	12,19,247,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17260
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON3',0,19,252,9,3
	.word	34926
	.byte	12,19,255,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17503
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON4',0,19,132,10,3
	.word	34991
	.byte	12,19,135,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17738
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON5',0,19,140,10,3
	.word	35056
	.byte	12,19,143,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17866
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6',0,19,148,10,3
	.word	35121
	.byte	12,19,151,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17966
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7',0,19,156,10,3
	.word	35186
	.byte	12,19,159,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18066
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CHIPID',0,19,164,10,3
	.word	35251
	.byte	12,19,167,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18274
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSCON',0,19,172,10,3
	.word	35315
	.byte	12,19,175,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18439
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSLIM',0,19,180,10,3
	.word	35379
	.byte	12,19,183,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18622
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSSTAT',0,19,188,10,3
	.word	35443
	.byte	12,19,191,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18776
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EICR',0,19,196,10,3
	.word	35508
	.byte	12,19,199,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19140
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR',0,19,204,10,3
	.word	35570
	.byte	12,19,207,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19351
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EMSR',0,19,212,10,3
	.word	35632
	.byte	12,19,215,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19603
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG',0,19,220,10,3
	.word	35694
	.byte	12,19,223,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19721
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG',0,19,228,10,3
	.word	35758
	.byte	12,19,231,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19832
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR13CON',0,19,236,10,3
	.word	35823
	.byte	12,19,239,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19995
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR33CON',0,19,244,10,3
	.word	35889
	.byte	12,19,247,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20158
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRADCSTAT',0,19,252,10,3
	.word	35955
	.byte	12,19,255,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20316
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRDVSTAT',0,19,132,11,3
	.word	36023
	.byte	12,19,135,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20481
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRMONCTRL',0,19,140,11,3
	.word	36090
	.byte	12,19,143,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20810
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROSCCTRL',0,19,148,11,3
	.word	36158
	.byte	12,19,151,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21031
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROVMON',0,19,156,11,3
	.word	36226
	.byte	12,19,159,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21194
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRRSTCON',0,19,164,11,3
	.word	36292
	.byte	12,19,167,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21466
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,19,172,11,3
	.word	36359
	.byte	12,19,175,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21619
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,19,180,11,3
	.word	36428
	.byte	12,19,183,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21775
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,19,188,11,3
	.word	36497
	.byte	12,19,191,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21937
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,19,196,11,3
	.word	36566
	.byte	12,19,199,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22080
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,19,204,11,3
	.word	36635
	.byte	12,19,207,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22245
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,19,212,11,3
	.word	36704
	.byte	12,19,215,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22390
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1',0,19,220,11,3
	.word	36773
	.byte	12,19,223,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22571
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2',0,19,228,11,3
	.word	36841
	.byte	12,19,231,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22745
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3',0,19,236,11,3
	.word	36909
	.byte	12,19,239,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22905
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4',0,19,244,11,3
	.word	36977
	.byte	12,19,247,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23049
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT',0,19,252,11,3
	.word	37045
	.byte	12,19,255,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23323
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRTRIM',0,19,132,12,3
	.word	37110
	.byte	12,19,135,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23462
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRUVMON',0,19,140,12,3
	.word	37175
	.byte	12,19,143,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23625
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EXTCON',0,19,148,12,3
	.word	37241
	.byte	12,19,151,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23843
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FDR',0,19,156,12,3
	.word	37305
	.byte	12,19,159,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24006
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FMR',0,19,164,12,3
	.word	37366
	.byte	12,19,167,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24342
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ID',0,19,172,12,3
	.word	37427
	.byte	12,19,175,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24449
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IGCR',0,19,180,12,3
	.word	37487
	.byte	12,19,183,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24901
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IN',0,19,188,12,3
	.word	37549
	.byte	12,19,191,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25000
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IOCR',0,19,196,12,3
	.word	37609
	.byte	12,19,199,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25150
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL0',0,19,204,12,3
	.word	37671
	.byte	12,19,207,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25299
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL1',0,19,212,12,3
	.word	37739
	.byte	12,19,215,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25460
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL2',0,19,220,12,3
	.word	37807
	.byte	12,19,223,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25590
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLCON',0,19,228,12,3
	.word	37875
	.byte	12,19,231,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25722
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST',0,19,236,12,3
	.word	37939
	.byte	12,19,239,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25837
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_MANID',0,19,244,12,3
	.word	38004
	.byte	12,19,247,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25948
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OMR',0,19,252,12,3
	.word	38067
	.byte	12,19,255,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26106
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OSCCON',0,19,132,13,3
	.word	38128
	.byte	12,19,135,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26518
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OUT',0,19,140,13,3
	.word	38192
	.byte	12,19,143,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26619
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCCON',0,19,148,13,3
	.word	38253
	.byte	12,19,151,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26886
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE',0,19,156,13,3
	.word	38317
	.byte	12,19,159,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27022
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC',0,19,164,13,3
	.word	38384
	.byte	12,19,167,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27133
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDR',0,19,172,13,3
	.word	38447
	.byte	12,19,175,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27266
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR',0,19,180,13,3
	.word	38508
	.byte	12,19,183,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27469
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON0',0,19,188,13,3
	.word	38570
	.byte	12,19,191,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27825
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON1',0,19,196,13,3
	.word	38635
	.byte	12,19,199,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28003
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON2',0,19,204,13,3
	.word	38700
	.byte	12,19,207,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28103
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON0',0,19,212,13,3
	.word	38765
	.byte	12,19,215,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28473
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON1',0,19,220,13,3
	.word	38834
	.byte	12,19,223,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28659
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT',0,19,228,13,3
	.word	38903
	.byte	12,19,231,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28857
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT',0,19,236,13,3
	.word	38972
	.byte	12,19,239,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29090
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR',0,19,244,13,3
	.word	39037
	.byte	12,19,247,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29242
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR0',0,19,252,13,3
	.word	39100
	.byte	12,19,255,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29809
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR1',0,19,132,14,3
	.word	39165
	.byte	12,19,135,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30103
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR2',0,19,140,14,3
	.word	39230
	.byte	12,19,143,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30381
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTAT',0,19,148,14,3
	.word	39295
	.byte	12,19,151,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30877
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR',0,19,156,14,3
	.word	39361
	.byte	12,19,159,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31399
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON',0,19,164,14,3
	.word	39430
	.byte	12,19,167,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31190
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON2',0,19,172,14,3
	.word	39494
	.byte	12,19,175,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31610
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTSTAT',0,19,180,14,3
	.word	39559
	.byte	12,19,183,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32042
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON',0,19,188,14,3
	.word	39624
	.byte	12,19,191,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32138
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_STSTAT',0,19,196,14,3
	.word	39689
	.byte	12,19,199,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32398
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON',0,19,204,14,3
	.word	39753
	.byte	12,19,207,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32523
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON',0,19,212,14,3
	.word	39819
	.byte	12,19,215,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32720
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR',0,19,220,14,3
	.word	39883
	.byte	12,19,223,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32873
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS',0,19,228,14,3
	.word	39948
	.byte	12,19,231,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33026
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET',0,19,236,14,3
	.word	40013
	.byte	12,19,239,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33179
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT',0,19,244,14,3
	.word	40078
	.byte	12,19,247,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33350
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON0',0,19,252,14,3
	.word	40144
	.byte	12,19,255,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33480
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON1',0,19,132,15,3
	.word	40213
	.byte	12,19,135,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33718
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_SR',0,19,140,15,3
	.word	40282
	.byte	12,19,143,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33941
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0',0,19,148,15,3
	.word	40349
	.byte	12,19,151,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34067
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON1',0,19,156,15,3
	.word	40416
	.byte	12,19,159,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34319
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_SR',0,19,164,15,3
	.word	40483
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,19,175,15,25,12,13
	.byte	'CON0',0
	.word	40144
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40213
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40282
	.byte	4,2,35,8,0,16
	.word	40548
	.byte	31
	.byte	'Ifx_SCU_WDTCPU',0,19,180,15,3
	.word	40611
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,19,183,15,25,12,13
	.byte	'CON0',0
	.word	40349
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40416
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40483
	.byte	4,2,35,8,0,16
	.word	40640
	.byte	31
	.byte	'Ifx_SCU_WDTS',0,19,188,15,3
	.word	40701
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,31
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	40728
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,31
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	40879
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,31
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	41123
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,31
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	41221
	.byte	31
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8256
	.byte	31
	.byte	'gpio_pin_enum',0,9,89,2
	.word	9258
	.byte	31
	.byte	'gpio_dir_enum',0,9,95,2
	.word	11232
	.byte	31
	.byte	'gpio_mode_enum',0,9,111,2
	.word	11250
	.byte	27,20,45,9,1,11
	.byte	'mode',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'use_miso',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'use_cs',0,1
	.word	459
	.byte	1,0,2,35,0,0,31
	.byte	'spi_config_info_struct',0,20,50,2
	.word	41753
	.byte	31
	.byte	'spi_index_enum',0,10,48,2
	.word	11413
	.byte	31
	.byte	'spi_mode_enum',0,10,56,2
	.word	11737
	.byte	31
	.byte	'spi_sck_pin_enum',0,10,67,2
	.word	11791
	.byte	31
	.byte	'spi_mosi_pin_enum',0,10,78,2
	.word	12064
	.byte	31
	.byte	'spi_miso_pin_enum',0,10,89,2
	.word	12318
	.byte	31
	.byte	'spi_cs_pin_enum',0,10,140,1,2
	.word	12572
	.byte	31
	.byte	'ips114_dir_enum',0,11,99,2
	.word	13554
	.byte	31
	.byte	'ips114_font_size_enum',0,11,106,2
	.word	13640
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L230:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,54,15,39,12,63,12,60,12,0,0,20,5,0,73,19,0,0,21,46
	.byte	1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0
	.byte	0,23,5,0,58,15,59,15,57,15,73,19,0,0,24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,38,0
	.byte	73,19,0,0,26,21,1,54,15,39,12,0,0,27,19,1,58,15,59,15,57,15,11,15,0,0,28,46,1,49,19,0,0,29,5,0,49,19,0
	.byte	0,30,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,31,22,0,3,8,58,15,59,15,57,15,73,19,0,0,32,21,0
	.byte	54,15,0,0,33,1,1,73,19,0,0,34,33,0,0,0,35,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L231:
	.word	.L964-.L963
.L963:
	.half	3
	.word	.L966-.L965
.L965:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'string.h',0,3,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'zf_common_function.h',0,4,0,0
	.byte	'zf_driver_delay.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,5,0,0
	.byte	'zf_driver_spi.h',0,5,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_ips114.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'zf_common_font.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0
	.byte	'zf_driver_soft_spi.h',0,5,0,0,0
.L966:
.L964:
	.sdecl	'.debug_info',debug,cluster('ips114_clear')
	.sect	'.debug_info'
.L232:
	.word	310
	.half	3
	.word	.L233
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L235,.L234
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_clear',0,1,146,2,6,1,1,1
	.word	.L193,.L349,.L192
	.byte	4
	.word	.L193,.L349
	.byte	5
	.byte	'color_buffer',0,1,148,2,12
	.word	.L350,.L351
	.byte	5
	.byte	'i',0,1,149,2,12
	.word	.L352,.L353
	.byte	5
	.byte	'j',0,1,149,2,19
	.word	.L352,.L354
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_clear')
	.sect	'.debug_abbrev'
.L233:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_clear')
	.sect	'.debug_line'
.L234:
	.word	.L968-.L967
.L967:
	.half	3
	.word	.L970-.L969
.L969:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L970:
	.byte	5,25,7,0,5,2
	.word	.L193
	.byte	3,147,2,1,5,41,9
	.half	.L971-.L193
	.byte	1,5,5,9
	.half	.L597-.L971
	.byte	3,3,1,5,23,9
	.half	.L23-.L597
	.byte	3,1,1,5,26,9
	.half	.L972-.L23
	.byte	1,5,29,9
	.half	.L973-.L972
	.byte	1,5,46,9
	.half	.L974-.L973
	.byte	1,5,51,9
	.half	.L975-.L974
	.byte	1,5,69,9
	.half	.L976-.L975
	.byte	1,5,11,9
	.half	.L977-.L976
	.byte	3,1,1,5,36,9
	.half	.L599-.L977
	.byte	1,5,21,9
	.half	.L25-.L599
	.byte	3,2,1,5,27,9
	.half	.L978-.L25
	.byte	1,5,25,9
	.half	.L979-.L978
	.byte	1,5,40,9
	.half	.L980-.L979
	.byte	3,126,1,5,20,9
	.half	.L24-.L980
	.byte	1,5,36,9
	.half	.L981-.L24
	.byte	1,5,12,7,9
	.half	.L982-.L981
	.byte	3,4,1,5,38,9
	.half	.L600-.L982
	.byte	1,5,9,9
	.half	.L27-.L600
	.byte	3,2,1,5,42,9
	.half	.L603-.L27
	.byte	3,126,1,5,21,9
	.half	.L26-.L603
	.byte	1,5,38,9
	.half	.L983-.L26
	.byte	1,5,5,7,9
	.half	.L984-.L983
	.byte	3,4,1,5,1,9
	.half	.L29-.L984
	.byte	3,1,1,9
	.half	.L236-.L29
	.byte	0,1,1
.L968:
	.sdecl	'.debug_ranges',debug,cluster('ips114_clear')
	.sect	'.debug_ranges'
.L235:
	.word	-1,.L193,0,.L236-.L193,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_full')
	.sect	'.debug_info'
.L237:
	.word	328
	.half	3
	.word	.L238
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L240,.L239
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_full',0,1,171,2,6,1,1,1
	.word	.L195,.L355,.L194
	.byte	4
	.byte	'color',0,1,171,2,32
	.word	.L356,.L357
	.byte	5
	.word	.L195,.L355
	.byte	6
	.byte	'color_buffer',0,1,173,2,12
	.word	.L350,.L358
	.byte	6
	.byte	'i',0,1,174,2,12
	.word	.L352,.L359
	.byte	6
	.byte	'j',0,1,174,2,19
	.word	.L352,.L360
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_full')
	.sect	'.debug_abbrev'
.L238:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_full')
	.sect	'.debug_line'
.L239:
	.word	.L986-.L985
.L985:
	.half	3
	.word	.L988-.L987
.L987:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L988:
	.byte	5,6,7,0,5,2
	.word	.L195
	.byte	3,170,2,1,5,25,9
	.half	.L607-.L195
	.byte	3,2,1,5,41,9
	.half	.L989-.L607
	.byte	1,5,5,9
	.half	.L610-.L989
	.byte	3,3,1,5,23,9
	.half	.L31-.L610
	.byte	3,1,1,5,26,9
	.half	.L990-.L31
	.byte	1,5,29,9
	.half	.L991-.L990
	.byte	1,5,46,9
	.half	.L992-.L991
	.byte	1,5,51,9
	.half	.L993-.L992
	.byte	1,5,69,9
	.half	.L994-.L993
	.byte	1,5,11,9
	.half	.L995-.L994
	.byte	3,1,1,5,36,9
	.half	.L612-.L995
	.byte	1,5,21,9
	.half	.L33-.L612
	.byte	3,2,1,5,25,9
	.half	.L996-.L33
	.byte	1,5,40,9
	.half	.L997-.L996
	.byte	3,126,1,5,20,9
	.half	.L32-.L997
	.byte	1,5,36,9
	.half	.L998-.L32
	.byte	1,5,12,7,9
	.half	.L999-.L998
	.byte	3,4,1,5,38,9
	.half	.L613-.L999
	.byte	1,5,9,9
	.half	.L35-.L613
	.byte	3,2,1,5,42,9
	.half	.L616-.L35
	.byte	3,126,1,5,21,9
	.half	.L34-.L616
	.byte	1,5,38,9
	.half	.L1000-.L34
	.byte	1,5,5,7,9
	.half	.L1001-.L1000
	.byte	3,4,1,5,1,9
	.half	.L37-.L1001
	.byte	3,1,1,9
	.half	.L241-.L37
	.byte	0,1,1
.L986:
	.sdecl	'.debug_ranges',debug,cluster('ips114_full')
	.sect	'.debug_ranges'
.L240:
	.word	-1,.L195,0,.L241-.L195,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_set_dir')
	.sect	'.debug_info'
.L242:
	.word	272
	.half	3
	.word	.L243
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L245,.L244
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_set_dir',0,1,196,2,6,1,1,1
	.word	.L197,.L361,.L196
	.byte	4
	.byte	'dir',0,1,196,2,38
	.word	.L362,.L363
	.byte	5
	.word	.L197,.L361
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_set_dir')
	.sect	'.debug_abbrev'
.L243:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_set_dir')
	.sect	'.debug_line'
.L244:
	.word	.L1003-.L1002
.L1002:
	.half	3
	.word	.L1005-.L1004
.L1004:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1005:
	.byte	5,5,7,0,5,2
	.word	.L197
	.byte	3,197,2,1,5,24,9
	.half	.L1006-.L197
	.byte	1,5,14,9
	.half	.L1007-.L1006
	.byte	3,3,1,9
	.half	.L1008-.L1007
	.byte	3,1,1,9
	.half	.L1009-.L1008
	.byte	3,5,1,9
	.half	.L1010-.L1009
	.byte	3,1,1,5,13,9
	.half	.L39-.L1010
	.byte	3,124,1,5,32,9
	.half	.L1011-.L39
	.byte	1,5,30,9
	.half	.L1012-.L1011
	.byte	1,5,13,9
	.half	.L1013-.L1012
	.byte	3,1,1,5,33,9
	.half	.L1014-.L1013
	.byte	1,5,31,9
	.half	.L1015-.L1014
	.byte	1,5,10,9
	.half	.L1016-.L1015
	.byte	3,1,1,5,13,9
	.half	.L41-.L1016
	.byte	3,4,1,5,32,9
	.half	.L1017-.L41
	.byte	1,5,30,9
	.half	.L1018-.L1017
	.byte	1,5,13,9
	.half	.L1019-.L1018
	.byte	3,1,1,5,33,9
	.half	.L1020-.L1019
	.byte	1,5,31,9
	.half	.L1021-.L1020
	.byte	1,5,10,9
	.half	.L1022-.L1021
	.byte	3,1,1,5,1,9
	.half	.L43-.L1022
	.byte	3,2,1,7,9
	.half	.L246-.L43
	.byte	0,1,1
.L1003:
	.sdecl	'.debug_ranges',debug,cluster('ips114_set_dir')
	.sect	'.debug_ranges'
.L245:
	.word	-1,.L197,0,.L246-.L197,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_set_font')
	.sect	'.debug_info'
.L247:
	.word	274
	.half	3
	.word	.L248
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L250,.L249
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_set_font',0,1,223,2,6,1,1,1
	.word	.L199,.L364,.L198
	.byte	4
	.byte	'font',0,1,223,2,45
	.word	.L365,.L366
	.byte	5
	.word	.L199,.L364
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_set_font')
	.sect	'.debug_abbrev'
.L248:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_set_font')
	.sect	'.debug_line'
.L249:
	.word	.L1024-.L1023
.L1023:
	.half	3
	.word	.L1026-.L1025
.L1025:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1026:
	.byte	5,5,7,0,5,2
	.word	.L199
	.byte	3,224,2,1,5,25,9
	.half	.L1027-.L199
	.byte	1,5,1,9
	.half	.L1028-.L1027
	.byte	3,1,1,7,9
	.half	.L251-.L1028
	.byte	0,1,1
.L1024:
	.sdecl	'.debug_ranges',debug,cluster('ips114_set_font')
	.sect	'.debug_ranges'
.L250:
	.word	-1,.L199,0,.L251-.L199,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_set_color')
	.sect	'.debug_info'
.L252:
	.word	295
	.half	3
	.word	.L253
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L255,.L254
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_set_color',0,1,236,2,6,1,1,1
	.word	.L201,.L367,.L200
	.byte	4
	.byte	'pen',0,1,236,2,37
	.word	.L368,.L369
	.byte	4
	.byte	'bgcolor',0,1,236,2,55
	.word	.L370,.L371
	.byte	5
	.word	.L201,.L367
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_set_color')
	.sect	'.debug_abbrev'
.L253:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_set_color')
	.sect	'.debug_line'
.L254:
	.word	.L1030-.L1029
.L1029:
	.half	3
	.word	.L1032-.L1031
.L1031:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1032:
	.byte	5,5,7,0,5,2
	.word	.L201
	.byte	3,237,2,1,5,21,9
	.half	.L1033-.L201
	.byte	1,5,5,9
	.half	.L1034-.L1033
	.byte	3,1,1,5,20,9
	.half	.L1035-.L1034
	.byte	1,5,1,9
	.half	.L1036-.L1035
	.byte	3,1,1,7,9
	.half	.L256-.L1036
	.byte	0,1,1
.L1030:
	.sdecl	'.debug_ranges',debug,cluster('ips114_set_color')
	.sect	'.debug_ranges'
.L255:
	.word	-1,.L201,0,.L256-.L201,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_draw_point')
	.sect	'.debug_info'
.L257:
	.word	307
	.half	3
	.word	.L258
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L260,.L259
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_draw_point',0,1,251,2,6,1,1,1
	.word	.L203,.L372,.L202
	.byte	4
	.byte	'x',0,1,251,2,32
	.word	.L373,.L374
	.byte	4
	.byte	'y',0,1,251,2,42
	.word	.L373,.L375
	.byte	4
	.byte	'color',0,1,251,2,58
	.word	.L376,.L377
	.byte	5
	.word	.L203,.L372
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_draw_point')
	.sect	'.debug_abbrev'
.L258:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_draw_point')
	.sect	'.debug_line'
.L259:
	.word	.L1038-.L1037
.L1037:
	.half	3
	.word	.L1040-.L1039
.L1039:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1040:
	.byte	5,6,7,0,5,2
	.word	.L203
	.byte	3,250,2,1,5,5,9
	.half	.L622-.L203
	.byte	3,4,1,9
	.half	.L619-.L622
	.byte	3,1,1,9
	.half	.L1041-.L619
	.byte	3,2,1,5,32,9
	.half	.L46-.L1041
	.byte	3,1,1,5,5,9
	.half	.L1042-.L46
	.byte	3,1,1,9
	.half	.L628-.L1042
	.byte	3,1,1,5,1,9
	.half	.L48-.L628
	.byte	3,1,1,7,9
	.half	.L261-.L48
	.byte	0,1,1
.L1038:
	.sdecl	'.debug_ranges',debug,cluster('ips114_draw_point')
	.sect	'.debug_ranges'
.L260:
	.word	-1,.L203,0,.L261-.L203,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_draw_line')
	.sect	'.debug_info'
.L262:
	.word	448
	.half	3
	.word	.L263
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L265,.L264
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_draw_line',0,1,147,3,6,1,1,1
	.word	.L205,.L378,.L204
	.byte	4
	.byte	'x_start',0,1,147,3,31
	.word	.L373,.L379
	.byte	4
	.byte	'y_start',0,1,147,3,47
	.word	.L373,.L380
	.byte	4
	.byte	'x_end',0,1,147,3,63
	.word	.L373,.L381
	.byte	4
	.byte	'y_end',0,1,147,3,77
	.word	.L373,.L382
	.byte	4
	.byte	'color',0,1,147,3,97
	.word	.L383,.L384
	.byte	5
	.word	.L205,.L378
	.byte	5
	.word	.L385,.L378
	.byte	6
	.byte	'x_dir',0,1,156,3,11
	.word	.L386,.L387
	.byte	6
	.byte	'y_dir',0,1,157,3,11
	.word	.L386,.L388
	.byte	6
	.byte	'temp_rate',0,1,158,3,11
	.word	.L389,.L390
	.byte	6
	.byte	'temp_b',0,1,159,3,11
	.word	.L389,.L391
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_draw_line')
	.sect	'.debug_abbrev'
.L263:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_draw_line')
	.sect	'.debug_line'
.L264:
	.word	.L1044-.L1043
.L1043:
	.half	3
	.word	.L1046-.L1045
.L1045:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1046:
	.byte	5,6,7,0,5,2
	.word	.L205
	.byte	3,146,3,1,5,5,9
	.half	.L636-.L205
	.byte	3,4,1,9
	.half	.L630-.L636
	.byte	3,1,1,9
	.half	.L1047-.L630
	.byte	3,1,1,9
	.half	.L1048-.L1047
	.byte	3,1,1,5,20,9
	.half	.L385-.L1048
	.byte	3,2,1,5,36,7,9
	.half	.L641-.L385
	.byte	1,5,40,9
	.half	.L1049-.L641
	.byte	1,5,36,9
	.half	.L49-.L1049
	.byte	1,5,20,9
	.half	.L50-.L49
	.byte	3,1,1,5,36,7,9
	.half	.L642-.L50
	.byte	1,5,40,9
	.half	.L643-.L642
	.byte	1,5,36,9
	.half	.L51-.L643
	.byte	1,5,9,9
	.half	.L53-.L51
	.byte	3,6,1,5,41,7,9
	.half	.L644-.L53
	.byte	3,2,1,5,25,9
	.half	.L645-.L644
	.byte	1,5,68,9
	.half	.L646-.L645
	.byte	1,5,52,9
	.half	.L647-.L646
	.byte	1,5,50,9
	.half	.L1050-.L647
	.byte	1,5,22,9
	.half	.L648-.L1050
	.byte	3,1,1,5,39,9
	.half	.L649-.L648
	.byte	1,5,37,9
	.half	.L1051-.L649
	.byte	1,5,76,9
	.half	.L650-.L1051
	.byte	3,127,1,5,35,9
	.half	.L54-.L650
	.byte	3,5,1,5,53,9
	.half	.L57-.L54
	.byte	3,2,1,5,25,9
	.half	.L653-.L57
	.byte	3,1,1,5,35,9
	.half	.L56-.L653
	.byte	3,125,1,5,49,7,9
	.half	.L654-.L56
	.byte	3,5,1,5,13,9
	.half	.L657-.L654
	.byte	3,1,1,5,12,9
	.half	.L55-.L657
	.byte	3,2,1,5,40,9
	.half	.L60-.L55
	.byte	1,5,9,9
	.half	.L62-.L60
	.byte	1,5,35,7,9
	.half	.L1052-.L62
	.byte	3,2,1,5,53,9
	.half	.L65-.L1052
	.byte	3,2,1,5,25,9
	.half	.L668-.L65
	.byte	3,1,1,5,36,9
	.half	.L669-.L668
	.byte	3,1,1,5,51,9
	.half	.L634-.L669
	.byte	1,5,61,9
	.half	.L1053-.L634
	.byte	1,5,27,9
	.half	.L1054-.L1053
	.byte	1,5,35,9
	.half	.L64-.L1054
	.byte	3,124,1,5,49,7,9
	.half	.L670-.L64
	.byte	3,6,1,5,55,9
	.half	.L673-.L670
	.byte	1,5,35,9
	.half	.L63-.L673
	.byte	3,4,1,5,53,9
	.half	.L68-.L63
	.byte	3,2,1,5,25,9
	.half	.L676-.L68
	.byte	3,1,1,5,35,9
	.half	.L679-.L676
	.byte	3,1,1,5,62,9
	.half	.L678-.L679
	.byte	1,5,27,9
	.half	.L1055-.L678
	.byte	1,5,35,9
	.half	.L67-.L1055
	.byte	3,124,1,5,49,7,9
	.half	.L681-.L67
	.byte	3,6,1,5,1,9
	.half	.L58-.L681
	.byte	3,3,1,7,9
	.half	.L266-.L58
	.byte	0,1,1
.L1044:
	.sdecl	'.debug_ranges',debug,cluster('ips114_draw_line')
	.sect	'.debug_ranges'
.L265:
	.word	-1,.L205,0,.L266-.L205,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_char')
	.sect	'.debug_info'
.L267:
	.word	510
	.half	3
	.word	.L268
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L270,.L269
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_char',0,1,210,3,6,1,1,1
	.word	.L207,.L392,.L206
	.byte	4
	.byte	'x',0,1,210,3,31
	.word	.L373,.L393
	.byte	4
	.byte	'y',0,1,210,3,41
	.word	.L373,.L394
	.byte	4
	.byte	'dat',0,1,210,3,55
	.word	.L395,.L396
	.byte	5
	.word	.L207,.L392
	.byte	5
	.word	.L397,.L392
	.byte	6
	.byte	'i',0,1,217,3,11
	.word	.L398,.L399
	.byte	6
	.byte	'j',0,1,217,3,18
	.word	.L398,.L400
	.byte	5
	.word	.L71,.L401
	.byte	6
	.byte	'display_buffer',0,1,224,3,20
	.word	.L402,.L403
	.byte	5
	.word	.L76,.L404
	.byte	6
	.byte	'temp_top',0,1,229,3,23
	.word	.L398,.L405
	.byte	0,0,5
	.word	.L72,.L406
	.byte	6
	.byte	'display_buffer',0,1,247,3,20
	.word	.L407,.L408
	.byte	5
	.word	.L83,.L409
	.byte	6
	.byte	'temp_top',0,1,251,3,23
	.word	.L398,.L410
	.byte	6
	.byte	'temp_bottom',0,1,252,3,23
	.word	.L398,.L411
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_char')
	.sect	'.debug_abbrev'
.L268:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_char')
	.sect	'.debug_line'
.L269:
	.word	.L1057-.L1056
.L1056:
	.half	3
	.word	.L1059-.L1058
.L1058:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1059:
	.byte	5,6,7,0,5,2
	.word	.L207
	.byte	3,209,3,1,5,5,9
	.half	.L687-.L207
	.byte	3,4,1,9
	.half	.L684-.L687
	.byte	3,1,1,9
	.half	.L397-.L684
	.byte	3,4,1,5,12,9
	.half	.L70-.L397
	.byte	3,1,1,5,14,9
	.half	.L1060-.L70
	.byte	3,2,1,9
	.half	.L1061-.L1060
	.byte	3,23,1,9
	.half	.L1062-.L1061
	.byte	3,35,1,5,39,9
	.half	.L71-.L1062
	.byte	3,73,1,5,46,9
	.half	.L692-.L71
	.byte	1,5,19,9
	.half	.L1063-.L692
	.byte	3,1,1,5,29,9
	.half	.L696-.L1063
	.byte	1,5,53,9
	.half	.L76-.L696
	.byte	3,3,1,5,48,9
	.half	.L1064-.L76
	.byte	1,5,34,9
	.half	.L1065-.L1064
	.byte	1,5,48,9
	.half	.L1066-.L1065
	.byte	1,5,58,9
	.half	.L1067-.L1066
	.byte	1,5,23,9
	.half	.L697-.L1067
	.byte	3,1,1,5,33,9
	.half	.L698-.L697
	.byte	1,5,21,9
	.half	.L78-.L698
	.byte	3,2,1,5,48,7,9
	.half	.L1068-.L78
	.byte	3,2,1,5,42,9
	.half	.L1069-.L1068
	.byte	1,5,39,9
	.half	.L1070-.L1069
	.byte	1,5,54,9
	.half	.L1071-.L1070
	.byte	1,5,51,9
	.half	.L1072-.L1071
	.byte	1,5,70,9
	.half	.L1073-.L1072
	.byte	1,5,48,9
	.half	.L79-.L1073
	.byte	3,4,1,5,42,9
	.half	.L1074-.L79
	.byte	1,5,39,9
	.half	.L1075-.L1074
	.byte	1,5,54,9
	.half	.L1076-.L1075
	.byte	1,5,51,9
	.half	.L1077-.L1076
	.byte	1,5,30,9
	.half	.L80-.L1077
	.byte	3,2,1,5,37,9
	.half	.L1078-.L80
	.byte	3,118,1,5,33,9
	.half	.L77-.L1078
	.byte	1,7,9
	.half	.L404-.L77
	.byte	3,124,1,5,29,9
	.half	.L75-.L404
	.byte	1,5,13,7,9
	.half	.L1079-.L75
	.byte	3,17,1,5,10,9
	.half	.L401-.L1079
	.byte	3,1,1,5,39,9
	.half	.L72-.L401
	.byte	3,4,1,5,46,9
	.half	.L700-.L72
	.byte	1,5,19,9
	.half	.L1080-.L700
	.byte	3,1,1,5,29,9
	.half	.L704-.L1080
	.byte	1,5,54,9
	.half	.L83-.L704
	.byte	3,2,1,5,49,9
	.half	.L1081-.L83
	.byte	1,5,34,9
	.half	.L1082-.L1081
	.byte	1,5,49,9
	.half	.L1083-.L1082
	.byte	1,5,59,9
	.half	.L1084-.L1083
	.byte	1,5,57,9
	.half	.L705-.L1084
	.byte	3,1,1,5,52,9
	.half	.L1085-.L705
	.byte	1,5,37,9
	.half	.L1086-.L1085
	.byte	1,5,52,9
	.half	.L1087-.L1086
	.byte	1,5,62,9
	.half	.L1088-.L1087
	.byte	1,5,23,9
	.half	.L707-.L1088
	.byte	3,1,1,5,33,9
	.half	.L708-.L707
	.byte	1,5,21,9
	.half	.L85-.L708
	.byte	3,2,1,5,48,7,9
	.half	.L1089-.L85
	.byte	3,2,1,5,42,9
	.half	.L1090-.L1089
	.byte	1,5,39,9
	.half	.L1091-.L1090
	.byte	1,5,54,9
	.half	.L1092-.L1091
	.byte	1,5,51,9
	.half	.L1093-.L1092
	.byte	1,5,70,9
	.half	.L1094-.L1093
	.byte	1,5,48,9
	.half	.L86-.L1094
	.byte	3,4,1,5,42,9
	.half	.L1095-.L86
	.byte	1,5,39,9
	.half	.L1096-.L1095
	.byte	1,5,54,9
	.half	.L1097-.L1096
	.byte	1,5,51,9
	.half	.L1098-.L1097
	.byte	1,5,30,9
	.half	.L87-.L1098
	.byte	3,2,1,5,37,9
	.half	.L1099-.L87
	.byte	3,118,1,5,33,9
	.half	.L84-.L1099
	.byte	1,5,23,7,9
	.half	.L1100-.L84
	.byte	3,12,1,5,33,9
	.half	.L706-.L1100
	.byte	1,5,21,9
	.half	.L89-.L706
	.byte	3,2,1,5,48,7,9
	.half	.L1101-.L89
	.byte	3,2,1,5,42,9
	.half	.L1102-.L1101
	.byte	1,5,50,9
	.half	.L1103-.L1102
	.byte	1,5,39,9
	.half	.L1104-.L1103
	.byte	1,5,63,9
	.half	.L1105-.L1104
	.byte	1,5,60,9
	.half	.L1106-.L1105
	.byte	1,5,79,9
	.half	.L1107-.L1106
	.byte	1,5,48,9
	.half	.L90-.L1107
	.byte	3,4,1,5,42,9
	.half	.L1108-.L90
	.byte	1,5,50,9
	.half	.L1109-.L1108
	.byte	1,5,39,9
	.half	.L1110-.L1109
	.byte	1,5,63,9
	.half	.L1111-.L1110
	.byte	1,5,60,9
	.half	.L1112-.L1111
	.byte	1,5,33,9
	.half	.L91-.L1112
	.byte	3,2,1,5,37,9
	.half	.L1113-.L91
	.byte	3,118,1,5,33,9
	.half	.L88-.L1113
	.byte	1,7,9
	.half	.L409-.L88
	.byte	3,112,1,5,29,9
	.half	.L82-.L409
	.byte	1,5,13,7,9
	.half	.L1114-.L82
	.byte	3,29,1,5,10,9
	.half	.L406-.L1114
	.byte	3,1,1,9
	.half	.L73-.L406
	.byte	3,4,1,5,5,9
	.half	.L81-.L73
	.byte	3,2,1,5,1,9
	.half	.L95-.L81
	.byte	3,1,1,7,9
	.half	.L271-.L95
	.byte	0,1,1
.L1057:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_char')
	.sect	'.debug_ranges'
.L270:
	.word	-1,.L207,0,.L271-.L207,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_string')
	.sect	'.debug_info'
.L272:
	.word	332
	.half	3
	.word	.L273
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L275,.L274
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_string',0,1,169,4,6,1,1,1
	.word	.L209,.L412,.L208
	.byte	4
	.byte	'x',0,1,169,4,33
	.word	.L373,.L413
	.byte	4
	.byte	'y',0,1,169,4,43
	.word	.L373,.L414
	.byte	4
	.byte	'dat',0,1,169,4,57
	.word	.L415,.L416
	.byte	5
	.word	.L209,.L412
	.byte	5
	.word	.L417,.L412
	.byte	6
	.byte	'j',0,1,176,4,12
	.word	.L373,.L418
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_string')
	.sect	'.debug_abbrev'
.L273:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_string')
	.sect	'.debug_line'
.L274:
	.word	.L1116-.L1115
.L1115:
	.half	3
	.word	.L1118-.L1117
.L1117:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1118:
	.byte	5,6,7,0,5,2
	.word	.L209
	.byte	3,168,4,1,5,5,9
	.half	.L712-.L209
	.byte	3,4,1,9
	.half	.L1119-.L712
	.byte	3,1,1,5,14,9
	.half	.L417-.L1119
	.byte	3,2,1,5,25,9
	.half	.L716-.L417
	.byte	3,1,1,5,16,9
	.half	.L97-.L716
	.byte	3,2,1,5,18,9
	.half	.L1120-.L97
	.byte	3,2,1,9
	.half	.L1121-.L1120
	.byte	3,1,1,9
	.half	.L1122-.L1121
	.byte	3,1,1,5,58,9
	.half	.L98-.L1122
	.byte	3,126,1,5,56,9
	.half	.L718-.L98
	.byte	1,5,71,9
	.half	.L1123-.L718
	.byte	1,5,77,9
	.half	.L722-.L1123
	.byte	1,5,58,9
	.half	.L99-.L722
	.byte	3,1,1,5,56,9
	.half	.L723-.L99
	.byte	1,5,71,9
	.half	.L1124-.L723
	.byte	1,5,77,9
	.half	.L727-.L1124
	.byte	1,5,37,9
	.half	.L100-.L727
	.byte	3,1,1,5,11,9
	.half	.L102-.L100
	.byte	3,2,1,5,22,9
	.half	.L96-.L102
	.byte	3,120,1,5,25,9
	.half	.L1125-.L96
	.byte	1,5,1,7,9
	.half	.L1126-.L1125
	.byte	3,10,1,7,9
	.half	.L276-.L1126
	.byte	0,1,1
.L1116:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_string')
	.sect	'.debug_ranges'
.L275:
	.word	-1,.L209,0,.L276-.L209,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_int')
	.sect	'.debug_info'
.L277:
	.word	398
	.half	3
	.word	.L278
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L280,.L279
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_int',0,1,199,4,6,1,1,1
	.word	.L211,.L419,.L210
	.byte	4
	.byte	'x',0,1,199,4,30
	.word	.L373,.L420
	.byte	4
	.byte	'y',0,1,199,4,40
	.word	.L373,.L421
	.byte	4
	.byte	'dat',0,1,199,4,55
	.word	.L422,.L423
	.byte	4
	.byte	'num',0,1,199,4,66
	.word	.L398,.L424
	.byte	5
	.word	.L211,.L419
	.byte	5
	.word	.L425,.L419
	.byte	6
	.byte	'dat_temp',0,1,208,4,11
	.word	.L426,.L427
	.byte	6
	.byte	'offset',0,1,209,4,11
	.word	.L426,.L428
	.byte	6
	.byte	'data_buffer',0,1,210,4,10
	.word	.L429,.L430
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_int')
	.sect	'.debug_abbrev'
.L278:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_int')
	.sect	'.debug_line'
.L279:
	.word	.L1128-.L1127
.L1127:
	.half	3
	.word	.L1130-.L1129
.L1129:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1130:
	.byte	5,6,7,0,5,2
	.word	.L211
	.byte	3,198,4,1,5,5,9
	.half	.L733-.L211
	.byte	3,4,1,9
	.half	.L729-.L733
	.byte	3,1,1,9
	.half	.L1131-.L729
	.byte	3,1,1,9
	.half	.L1132-.L1131
	.byte	3,1,1,5,18,9
	.half	.L425-.L1132
	.byte	3,3,1,5,12,9
	.half	.L737-.L425
	.byte	3,3,1,5,25,9
	.half	.L1133-.L737
	.byte	1,5,28,9
	.half	.L1134-.L1133
	.byte	1,5,12,9
	.half	.L1135-.L1134
	.byte	3,1,1,5,25,9
	.half	.L1136-.L1135
	.byte	1,5,34,9
	.half	.L1137-.L1136
	.byte	1,5,5,9
	.half	.L1138-.L1137
	.byte	3,3,1,5,22,7,9
	.half	.L1139-.L1138
	.byte	3,2,1,5,20,9
	.half	.L107-.L1139
	.byte	3,2,1,5,28,9
	.half	.L1140-.L107
	.byte	3,126,1,5,22,9
	.half	.L106-.L1140
	.byte	1,5,18,7,9
	.half	.L1141-.L106
	.byte	3,4,1,5,21,9
	.half	.L105-.L1141
	.byte	3,2,1,5,34,9
	.half	.L1142-.L105
	.byte	1,5,45,9
	.half	.L739-.L1142
	.byte	3,1,1,5,30,9
	.half	.L740-.L739
	.byte	1,5,1,9
	.half	.L1143-.L740
	.byte	3,1,1,7,9
	.half	.L281-.L1143
	.byte	0,1,1
.L1128:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_int')
	.sect	'.debug_ranges'
.L280:
	.word	-1,.L211,0,.L281-.L211,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_uint')
	.sect	'.debug_info'
.L282:
	.word	399
	.half	3
	.word	.L283
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L285,.L284
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_uint',0,1,238,4,6,1,1,1
	.word	.L213,.L431,.L212
	.byte	4
	.byte	'x',0,1,238,4,31
	.word	.L373,.L432
	.byte	4
	.byte	'y',0,1,238,4,41
	.word	.L373,.L433
	.byte	4
	.byte	'dat',0,1,238,4,57
	.word	.L434,.L435
	.byte	4
	.byte	'num',0,1,238,4,68
	.word	.L398,.L436
	.byte	5
	.word	.L213,.L431
	.byte	5
	.word	.L437,.L431
	.byte	6
	.byte	'dat_temp',0,1,247,4,12
	.word	.L352,.L438
	.byte	6
	.byte	'offset',0,1,248,4,11
	.word	.L426,.L439
	.byte	6
	.byte	'data_buffer',0,1,249,4,10
	.word	.L429,.L440
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_uint')
	.sect	'.debug_abbrev'
.L283:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_uint')
	.sect	'.debug_line'
.L284:
	.word	.L1145-.L1144
.L1144:
	.half	3
	.word	.L1147-.L1146
.L1146:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1147:
	.byte	5,6,7,0,5,2
	.word	.L213
	.byte	3,237,4,1,5,5,9
	.half	.L747-.L213
	.byte	3,4,1,9
	.half	.L743-.L747
	.byte	3,1,1,9
	.half	.L1148-.L743
	.byte	3,1,1,9
	.half	.L1149-.L1148
	.byte	3,1,1,5,18,9
	.half	.L437-.L1149
	.byte	3,3,1,5,12,9
	.half	.L751-.L437
	.byte	3,2,1,5,25,9
	.half	.L1150-.L751
	.byte	1,5,28,9
	.half	.L1151-.L1150
	.byte	1,5,12,9
	.half	.L1152-.L1151
	.byte	3,1,1,5,25,9
	.half	.L1153-.L1152
	.byte	1,5,30,9
	.half	.L1154-.L1153
	.byte	1,5,5,9
	.half	.L753-.L1154
	.byte	3,3,1,5,22,7,9
	.half	.L1155-.L753
	.byte	3,2,1,5,20,9
	.half	.L110-.L1155
	.byte	3,2,1,5,28,9
	.half	.L1156-.L110
	.byte	3,126,1,5,22,9
	.half	.L109-.L1156
	.byte	1,5,18,7,9
	.half	.L1157-.L109
	.byte	3,4,1,5,22,9
	.half	.L108-.L1157
	.byte	3,2,1,5,35,9
	.half	.L1158-.L108
	.byte	1,5,45,9
	.half	.L755-.L1158
	.byte	3,1,1,5,30,9
	.half	.L756-.L755
	.byte	1,5,1,9
	.half	.L1159-.L756
	.byte	3,1,1,7,9
	.half	.L286-.L1159
	.byte	0,1,1
.L1145:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_uint')
	.sect	'.debug_ranges'
.L285:
	.word	-1,.L213,0,.L286-.L213,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_float')
	.sect	'.debug_info'
.L287:
	.word	422
	.half	3
	.word	.L288
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L290,.L289
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_float',0,1,152,5,6,1,1,1
	.word	.L215,.L441,.L214
	.byte	4
	.byte	'x',0,1,152,5,32
	.word	.L373,.L442
	.byte	4
	.byte	'y',0,1,152,5,42
	.word	.L373,.L443
	.byte	4
	.byte	'dat',0,1,152,5,58
	.word	.L444,.L445
	.byte	4
	.byte	'num',0,1,152,5,69
	.word	.L398,.L446
	.byte	4
	.byte	'pointnum',0,1,152,5,80
	.word	.L398,.L447
	.byte	5
	.word	.L215,.L441
	.byte	5
	.word	.L448,.L441
	.byte	6
	.byte	'dat_temp',0,1,163,5,12
	.word	.L449,.L450
	.byte	6
	.byte	'offset',0,1,164,5,12
	.word	.L449,.L451
	.byte	6
	.byte	'data_buffer',0,1,165,5,10
	.word	.L452,.L453
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_float')
	.sect	'.debug_abbrev'
.L288:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_float')
	.sect	'.debug_line'
.L289:
	.word	.L1161-.L1160
.L1160:
	.half	3
	.word	.L1163-.L1162
.L1162:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1163:
	.byte	5,6,7,0,5,2
	.word	.L215
	.byte	3,151,5,1,5,5,9
	.half	.L765-.L215
	.byte	3,4,1,9
	.half	.L759-.L765
	.byte	3,1,1,9
	.half	.L1164-.L759
	.byte	3,1,1,9
	.half	.L1165-.L1164
	.byte	3,1,1,9
	.half	.L1166-.L1165
	.byte	3,1,1,9
	.half	.L1167-.L1166
	.byte	3,1,1,5,21,9
	.half	.L448-.L1167
	.byte	3,3,1,5,12,9
	.half	.L1168-.L448
	.byte	3,2,1,5,25,9
	.half	.L1169-.L1168
	.byte	1,5,28,9
	.half	.L1170-.L1169
	.byte	1,5,12,9
	.half	.L1171-.L1170
	.byte	3,1,1,5,25,9
	.half	.L1172-.L1171
	.byte	1,5,34,9
	.half	.L1173-.L1172
	.byte	1,5,45,9
	.half	.L1174-.L1173
	.byte	1,5,18,9
	.half	.L1175-.L1174
	.byte	3,3,1,5,19,9
	.half	.L112-.L1175
	.byte	3,2,1,5,16,9
	.half	.L768-.L112
	.byte	1,5,24,9
	.half	.L1176-.L768
	.byte	3,126,1,5,18,9
	.half	.L111-.L1176
	.byte	1,5,28,7,9
	.half	.L770-.L111
	.byte	3,4,1,5,44,9
	.half	.L772-.L770
	.byte	1,5,42,9
	.half	.L1177-.L772
	.byte	1,5,57,9
	.half	.L774-.L1177
	.byte	1,5,25,9
	.half	.L776-.L774
	.byte	1,5,24,9
	.half	.L1178-.L776
	.byte	3,1,1,5,47,9
	.half	.L1179-.L1178
	.byte	1,5,30,9
	.half	.L779-.L1179
	.byte	3,1,1,5,1,9
	.half	.L781-.L779
	.byte	3,1,1,7,9
	.half	.L291-.L781
	.byte	0,1,1
.L1161:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_float')
	.sect	'.debug_ranges'
.L290:
	.word	-1,.L215,0,.L291-.L215,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_binary_image')
	.sect	'.debug_info'
.L292:
	.word	533
	.half	3
	.word	.L293
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L295,.L294
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_binary_image',0,1,195,5,6,1,1,1
	.word	.L217,.L454,.L216
	.byte	4
	.byte	'x',0,1,195,5,39
	.word	.L373,.L455
	.byte	4
	.byte	'y',0,1,195,5,49
	.word	.L373,.L456
	.byte	4
	.byte	'image',0,1,195,5,65
	.word	.L457,.L458
	.byte	4
	.byte	'width',0,1,195,5,79
	.word	.L373,.L459
	.byte	4
	.byte	'height',0,1,195,5,93
	.word	.L373,.L460
	.byte	4
	.byte	'dis_width',0,1,195,5,108
	.word	.L373,.L461
	.byte	4
	.byte	'dis_height',0,1,195,5,126
	.word	.L373,.L462
	.byte	5
	.word	.L217,.L454
	.byte	5
	.word	.L463,.L454
	.byte	6
	.byte	'i',0,1,203,5,12
	.word	.L352,.L464
	.byte	6
	.byte	'j',0,1,203,5,19
	.word	.L352,.L465
	.byte	6
	.byte	'temp',0,1,204,5,11
	.word	.L398,.L466
	.byte	6
	.byte	'width_index',0,1,205,5,12
	.word	.L352,.L467
	.byte	6
	.byte	'data_buffer',0,1,206,5,12
	.word	.L350,.L468
	.byte	6
	.byte	'image_temp',0,1,207,5,18
	.word	.L457,.L469
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_binary_image')
	.sect	'.debug_abbrev'
.L293:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_binary_image')
	.sect	'.debug_line'
.L294:
	.word	.L1181-.L1180
.L1180:
	.half	3
	.word	.L1183-.L1182
.L1182:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1183:
	.byte	5,6,7,0,5,2
	.word	.L217
	.byte	3,194,5,1,5,5,9
	.half	.L789-.L217
	.byte	3,4,1,9
	.half	.L783-.L789
	.byte	3,1,1,9
	.half	.L1184-.L783
	.byte	3,1,1,5,33,9
	.half	.L463-.L1184
	.byte	3,5,1,5,5,9
	.half	.L795-.L463
	.byte	3,3,1,5,31,9
	.half	.L114-.L795
	.byte	3,1,1,5,43,9
	.half	.L797-.L114
	.byte	1,5,50,9
	.half	.L798-.L797
	.byte	1,5,63,9
	.half	.L799-.L798
	.byte	1,5,11,9
	.half	.L1185-.L799
	.byte	3,2,1,5,30,9
	.half	.L802-.L1185
	.byte	1,5,32,9
	.half	.L116-.L802
	.byte	3,2,1,5,41,9
	.half	.L803-.L116
	.byte	1,5,54,9
	.half	.L804-.L803
	.byte	1,5,64,9
	.half	.L805-.L804
	.byte	1,5,62,9
	.half	.L1186-.L805
	.byte	1,5,28,9
	.half	.L1187-.L1186
	.byte	1,5,15,9
	.half	.L806-.L1187
	.byte	3,1,1,5,33,9
	.half	.L807-.L806
	.byte	1,5,29,9
	.half	.L118-.L807
	.byte	3,2,1,5,37,9
	.half	.L809-.L118
	.byte	1,5,49,9
	.half	.L810-.L809
	.byte	3,1,1,5,47,9
	.half	.L1188-.L810
	.byte	1,5,33,9
	.half	.L1189-.L1188
	.byte	1,5,20,9
	.half	.L1190-.L1189
	.byte	1,5,47,9
	.half	.L812-.L1190
	.byte	3,1,1,5,45,9
	.half	.L1191-.L812
	.byte	1,5,29,9
	.half	.L811-.L1191
	.byte	1,5,13,9
	.half	.L813-.L811
	.byte	1,5,28,7,9
	.half	.L1192-.L813
	.byte	3,2,1,5,35,9
	.half	.L1193-.L1192
	.byte	1,5,32,9
	.half	.L1194-.L1193
	.byte	1,5,48,9
	.half	.L1195-.L1194
	.byte	1,5,28,9
	.half	.L119-.L1195
	.byte	3,4,1,5,35,9
	.half	.L1196-.L119
	.byte	1,5,32,9
	.half	.L1197-.L1196
	.byte	1,5,37,9
	.half	.L120-.L1197
	.byte	3,118,1,5,33,9
	.half	.L117-.L120
	.byte	1,5,9,7,9
	.half	.L1198-.L117
	.byte	3,13,1,5,34,9
	.half	.L815-.L1198
	.byte	3,112,1,5,30,9
	.half	.L115-.L815
	.byte	1,5,5,7,9
	.half	.L1199-.L115
	.byte	3,18,1,5,1,9
	.half	.L122-.L1199
	.byte	3,1,1,9
	.half	.L296-.L122
	.byte	0,1,1
.L1181:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_binary_image')
	.sect	'.debug_ranges'
.L295:
	.word	-1,.L217,0,.L296-.L217,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_gray_image')
	.sect	'.debug_info'
.L297:
	.word	549
	.half	3
	.word	.L298
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L300,.L299
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_gray_image',0,1,250,5,6,1,1,1
	.word	.L219,.L470,.L218
	.byte	4
	.byte	'x',0,1,250,5,37
	.word	.L373,.L471
	.byte	4
	.byte	'y',0,1,250,5,47
	.word	.L373,.L472
	.byte	4
	.byte	'image',0,1,250,5,63
	.word	.L457,.L473
	.byte	4
	.byte	'width',0,1,250,5,77
	.word	.L373,.L474
	.byte	4
	.byte	'height',0,1,250,5,91
	.word	.L373,.L475
	.byte	4
	.byte	'dis_width',0,1,250,5,106
	.word	.L373,.L476
	.byte	4
	.byte	'dis_height',0,1,250,5,124
	.word	.L373,.L477
	.byte	4
	.byte	'threshold',0,1,250,5,142,1
	.word	.L398,.L478
	.byte	5
	.word	.L219,.L470
	.byte	5
	.word	.L479,.L470
	.byte	6
	.byte	'i',0,1,130,6,12
	.word	.L352,.L480
	.byte	6
	.byte	'j',0,1,130,6,19
	.word	.L352,.L481
	.byte	6
	.byte	'color',0,1,131,6,12
	.word	.L373,.L482
	.byte	6
	.byte	'temp',0,1,131,6,22
	.word	.L373,.L483
	.byte	6
	.byte	'data_buffer',0,1,132,6,12
	.word	.L350,.L484
	.byte	6
	.byte	'image_temp',0,1,133,6,18
	.word	.L457,.L485
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_gray_image')
	.sect	'.debug_abbrev'
.L298:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_gray_image')
	.sect	'.debug_line'
.L299:
	.word	.L1201-.L1200
.L1200:
	.half	3
	.word	.L1203-.L1202
.L1202:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1203:
	.byte	5,6,7,0,5,2
	.word	.L219
	.byte	3,249,5,1,5,5,9
	.half	.L826-.L219
	.byte	3,4,1,9
	.half	.L819-.L826
	.byte	3,1,1,9
	.half	.L1204-.L819
	.byte	3,1,1,5,33,9
	.half	.L479-.L1204
	.byte	3,4,1,5,5,9
	.half	.L832-.L479
	.byte	3,3,1,5,31,9
	.half	.L124-.L832
	.byte	3,1,1,5,43,9
	.half	.L834-.L124
	.byte	1,5,50,9
	.half	.L835-.L834
	.byte	1,5,63,9
	.half	.L836-.L835
	.byte	1,5,11,9
	.half	.L1205-.L836
	.byte	3,2,1,5,30,9
	.half	.L839-.L1205
	.byte	1,5,32,9
	.half	.L126-.L839
	.byte	3,2,1,5,41,9
	.half	.L840-.L126
	.byte	1,5,54,9
	.half	.L841-.L840
	.byte	1,5,28,9
	.half	.L842-.L841
	.byte	1,5,15,9
	.half	.L843-.L842
	.byte	3,1,1,5,33,9
	.half	.L844-.L843
	.byte	1,5,37,9
	.half	.L128-.L844
	.byte	3,2,1,5,45,9
	.half	.L846-.L128
	.byte	1,5,33,9
	.half	.L1206-.L846
	.byte	1,5,20,9
	.half	.L1207-.L1206
	.byte	1,5,13,9
	.half	.L847-.L1207
	.byte	3,1,1,5,43,7,9
	.half	.L1208-.L847
	.byte	3,2,1,5,33,9
	.half	.L1209-.L1208
	.byte	1,5,50,9
	.half	.L1210-.L1209
	.byte	1,5,54,9
	.half	.L849-.L1210
	.byte	3,1,1,5,44,9
	.half	.L1211-.L849
	.byte	1,5,61,9
	.half	.L1212-.L1211
	.byte	1,5,31,9
	.half	.L1213-.L1212
	.byte	1,5,51,9
	.half	.L1214-.L1213
	.byte	3,1,1,5,41,9
	.half	.L848-.L1214
	.byte	1,5,31,9
	.half	.L1215-.L848
	.byte	1,5,28,9
	.half	.L1216-.L1215
	.byte	3,1,1,5,32,9
	.half	.L1217-.L1216
	.byte	1,5,55,9
	.half	.L1218-.L1217
	.byte	3,125,1,5,18,9
	.half	.L129-.L1218
	.byte	3,5,1,5,28,7,9
	.half	.L1219-.L129
	.byte	3,2,1,5,35,9
	.half	.L1220-.L1219
	.byte	1,5,32,9
	.half	.L1221-.L1220
	.byte	1,5,48,9
	.half	.L1222-.L1221
	.byte	1,5,28,9
	.half	.L131-.L1222
	.byte	3,4,1,5,35,9
	.half	.L1223-.L131
	.byte	1,5,32,9
	.half	.L1224-.L1223
	.byte	1,5,37,9
	.half	.L130-.L1224
	.byte	3,112,1,5,33,9
	.half	.L127-.L130
	.byte	1,5,9,7,9
	.half	.L1225-.L127
	.byte	3,19,1,5,34,9
	.half	.L845-.L1225
	.byte	3,106,1,5,30,9
	.half	.L125-.L845
	.byte	1,5,5,7,9
	.half	.L1226-.L125
	.byte	3,24,1,5,1,9
	.half	.L134-.L1226
	.byte	3,1,1,9
	.half	.L301-.L134
	.byte	0,1,1
.L1201:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_gray_image')
	.sect	'.debug_ranges'
.L300:
	.word	-1,.L219,0,.L301-.L219,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_rgb565_image')
	.sect	'.debug_info'
.L302:
	.word	515
	.half	3
	.word	.L303
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L305,.L304
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_rgb565_image',0,1,182,6,6,1,1,1
	.word	.L221,.L486,.L220
	.byte	4
	.byte	'x',0,1,182,6,39
	.word	.L373,.L487
	.byte	4
	.byte	'y',0,1,182,6,49
	.word	.L373,.L488
	.byte	4
	.byte	'image',0,1,182,6,66
	.word	.L489,.L490
	.byte	4
	.byte	'width',0,1,182,6,80
	.word	.L373,.L491
	.byte	4
	.byte	'height',0,1,182,6,94
	.word	.L373,.L492
	.byte	4
	.byte	'dis_width',0,1,182,6,109
	.word	.L373,.L493
	.byte	4
	.byte	'dis_height',0,1,182,6,127
	.word	.L373,.L494
	.byte	4
	.byte	'color_mode',0,1,182,6,145,1
	.word	.L398,.L495
	.byte	5
	.word	.L221,.L486
	.byte	5
	.word	.L496,.L486
	.byte	6
	.byte	'i',0,1,190,6,12
	.word	.L352,.L497
	.byte	6
	.byte	'j',0,1,190,6,19
	.word	.L352,.L498
	.byte	6
	.byte	'data_buffer',0,1,191,6,12
	.word	.L350,.L499
	.byte	6
	.byte	'image_temp',0,1,192,6,19
	.word	.L489,.L500
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_rgb565_image')
	.sect	'.debug_abbrev'
.L303:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_rgb565_image')
	.sect	'.debug_line'
.L304:
	.word	.L1228-.L1227
.L1227:
	.half	3
	.word	.L1230-.L1229
.L1229:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1230:
	.byte	5,6,7,0,5,2
	.word	.L221
	.byte	3,181,6,1,5,5,9
	.half	.L861-.L221
	.byte	3,4,1,9
	.half	.L854-.L861
	.byte	3,1,1,9
	.half	.L1231-.L854
	.byte	3,1,1,5,33,9
	.half	.L496-.L1231
	.byte	3,3,1,5,5,9
	.half	.L867-.L496
	.byte	3,3,1,5,31,9
	.half	.L136-.L867
	.byte	3,1,1,5,43,9
	.half	.L869-.L136
	.byte	1,5,50,9
	.half	.L870-.L869
	.byte	1,5,63,9
	.half	.L871-.L870
	.byte	1,5,11,9
	.half	.L1232-.L871
	.byte	3,2,1,5,30,9
	.half	.L874-.L1232
	.byte	1,5,32,9
	.half	.L138-.L874
	.byte	3,2,1,5,41,9
	.half	.L875-.L138
	.byte	1,5,54,9
	.half	.L876-.L875
	.byte	1,5,28,9
	.half	.L877-.L876
	.byte	1,5,15,9
	.half	.L878-.L877
	.byte	3,1,1,5,33,9
	.half	.L879-.L878
	.byte	1,5,24,9
	.half	.L140-.L879
	.byte	3,2,1,5,47,9
	.half	.L881-.L140
	.byte	1,5,55,9
	.half	.L882-.L881
	.byte	1,5,43,9
	.half	.L1233-.L882
	.byte	1,5,30,9
	.half	.L1234-.L1233
	.byte	1,5,28,9
	.half	.L1235-.L1234
	.byte	1,5,37,9
	.half	.L1236-.L1235
	.byte	3,126,1,5,33,9
	.half	.L139-.L1236
	.byte	1,5,9,7,9
	.half	.L1237-.L139
	.byte	3,4,1,5,13,7,9
	.half	.L1238-.L1237
	.byte	3,2,1,5,78,9
	.half	.L880-.L1238
	.byte	1,5,13,9
	.half	.L141-.L880
	.byte	3,4,1,5,34,9
	.half	.L142-.L141
	.byte	3,115,1,5,30,9
	.half	.L137-.L142
	.byte	1,5,5,7,9
	.half	.L1239-.L137
	.byte	3,16,1,5,1,9
	.half	.L144-.L1239
	.byte	3,1,1,9
	.half	.L306-.L144
	.byte	0,1,1
.L1228:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_rgb565_image')
	.sect	'.debug_ranges'
.L305:
	.word	-1,.L221,0,.L306-.L221,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_wave')
	.sect	'.debug_info'
.L307:
	.word	517
	.half	3
	.word	.L308
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L310,.L309
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_wave',0,1,229,6,6,1,1,1
	.word	.L223,.L501,.L222
	.byte	4
	.byte	'x',0,1,229,6,31
	.word	.L373,.L502
	.byte	4
	.byte	'y',0,1,229,6,41
	.word	.L373,.L503
	.byte	4
	.byte	'wave',0,1,229,6,58
	.word	.L489,.L504
	.byte	4
	.byte	'width',0,1,229,6,71
	.word	.L373,.L505
	.byte	4
	.byte	'value_max',0,1,229,6,85
	.word	.L373,.L506
	.byte	4
	.byte	'dis_width',0,1,229,6,103
	.word	.L373,.L507
	.byte	4
	.byte	'dis_value_max',0,1,229,6,121
	.word	.L373,.L508
	.byte	5
	.word	.L223,.L501
	.byte	5
	.word	.L509,.L501
	.byte	6
	.byte	'i',0,1,237,6,12
	.word	.L352,.L510
	.byte	6
	.byte	'j',0,1,237,6,19
	.word	.L352,.L511
	.byte	6
	.byte	'width_index',0,1,238,6,12
	.word	.L352,.L512
	.byte	6
	.byte	'value_max_index',0,1,238,6,29
	.word	.L352,.L513
	.byte	6
	.byte	'data_buffer',0,1,239,6,12
	.word	.L350,.L514
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_wave')
	.sect	'.debug_abbrev'
.L308:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_wave')
	.sect	'.debug_line'
.L309:
	.word	.L1241-.L1240
.L1240:
	.half	3
	.word	.L1243-.L1242
.L1242:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1243:
	.byte	5,6,7,0,5,2
	.word	.L223
	.byte	3,228,6,1,5,5,9
	.half	.L894-.L223
	.byte	3,4,1,9
	.half	.L888-.L894
	.byte	3,1,1,9
	.half	.L1244-.L888
	.byte	3,1,1,5,33,9
	.half	.L509-.L1244
	.byte	3,4,1,5,5,9
	.half	.L900-.L509
	.byte	3,2,1,5,31,9
	.half	.L146-.L900
	.byte	3,1,1,5,43,9
	.half	.L902-.L146
	.byte	1,5,50,9
	.half	.L903-.L902
	.byte	1,5,66,9
	.half	.L904-.L903
	.byte	1,5,11,9
	.half	.L1245-.L904
	.byte	3,1,1,5,33,9
	.half	.L907-.L1245
	.byte	1,5,15,9
	.half	.L148-.L907
	.byte	3,2,1,5,33,9
	.half	.L909-.L148
	.byte	1,5,24,9
	.half	.L150-.L909
	.byte	3,2,1,5,31,9
	.half	.L1246-.L150
	.byte	1,5,28,9
	.half	.L1247-.L1246
	.byte	1,5,37,9
	.half	.L1248-.L1247
	.byte	3,126,1,5,33,9
	.half	.L149-.L1248
	.byte	1,5,9,7,9
	.half	.L1249-.L149
	.byte	3,4,1,5,37,9
	.half	.L910-.L1249
	.byte	3,122,1,5,33,9
	.half	.L147-.L910
	.byte	1,5,5,7,9
	.half	.L1250-.L147
	.byte	3,8,1,5,11,9
	.half	.L152-.L1250
	.byte	3,2,1,5,29,9
	.half	.L908-.L152
	.byte	1,5,25,9
	.half	.L154-.L908
	.byte	3,2,1,5,33,9
	.half	.L913-.L154
	.byte	1,5,34,9
	.half	.L914-.L913
	.byte	3,1,1,5,27,9
	.half	.L1251-.L914
	.byte	1,5,66,9
	.half	.L915-.L1251
	.byte	1,5,49,9
	.half	.L1252-.L915
	.byte	1,5,71,9
	.half	.L916-.L1252
	.byte	1,5,38,9
	.half	.L917-.L916
	.byte	3,1,1,5,27,9
	.half	.L919-.L917
	.byte	1,5,68,9
	.half	.L1253-.L919
	.byte	1,5,73,9
	.half	.L1254-.L1253
	.byte	1,5,91,9
	.half	.L920-.L1254
	.byte	1,5,44,9
	.half	.L921-.L920
	.byte	1,5,97,9
	.half	.L1255-.L921
	.byte	1,5,33,9
	.half	.L918-.L1255
	.byte	3,124,1,5,29,9
	.half	.L153-.L918
	.byte	1,5,1,7,9
	.half	.L1256-.L153
	.byte	3,6,1,9
	.half	.L311-.L1256
	.byte	0,1,1
.L1241:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_wave')
	.sect	'.debug_ranges'
.L310:
	.word	-1,.L223,0,.L311-.L223,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_show_chinese')
	.sect	'.debug_info'
.L312:
	.word	507
	.half	3
	.word	.L313
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L315,.L314
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_show_chinese',0,1,145,7,6,1,1,1
	.word	.L225,.L515,.L224
	.byte	4
	.byte	'x',0,1,145,7,34
	.word	.L373,.L516
	.byte	4
	.byte	'y',0,1,145,7,44
	.word	.L373,.L517
	.byte	4
	.byte	'size',0,1,145,7,53
	.word	.L398,.L518
	.byte	4
	.byte	'chinese_buffer',0,1,145,7,72
	.word	.L457,.L519
	.byte	4
	.byte	'number',0,1,145,7,94
	.word	.L398,.L520
	.byte	4
	.byte	'color',0,1,145,7,115
	.word	.L521,.L522
	.byte	5
	.word	.L225,.L515
	.byte	5
	.word	.L523,.L515
	.byte	6
	.byte	'i',0,1,153,7,9
	.word	.L426,.L524
	.byte	6
	.byte	'j',0,1,153,7,16
	.word	.L426,.L525
	.byte	6
	.byte	'k',0,1,153,7,23
	.word	.L426,.L526
	.byte	6
	.byte	'temp',0,1,154,7,11
	.word	.L398,.L527
	.byte	6
	.byte	'temp1',0,1,154,7,21
	.word	.L398,.L528
	.byte	6
	.byte	'temp2',0,1,154,7,32
	.word	.L398,.L529
	.byte	6
	.byte	'p_data',0,1,155,7,18
	.word	.L457,.L530
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_show_chinese')
	.sect	'.debug_abbrev'
.L313:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_show_chinese')
	.sect	'.debug_line'
.L314:
	.word	.L1258-.L1257
.L1257:
	.half	3
	.word	.L1260-.L1259
.L1259:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1260:
	.byte	5,6,7,0,5,2
	.word	.L225
	.byte	3,144,7,1,5,5,9
	.half	.L932-.L225
	.byte	3,4,1,9
	.half	.L926-.L932
	.byte	3,1,1,9
	.half	.L1261-.L926
	.byte	3,1,1,5,20,9
	.half	.L523-.L1261
	.byte	3,6,1,5,18,9
	.half	.L1262-.L523
	.byte	1,5,5,9
	.half	.L936-.L1262
	.byte	3,2,1,5,36,9
	.half	.L156-.L936
	.byte	3,1,1,5,43,9
	.half	.L938-.L156
	.byte	1,5,47,9
	.half	.L939-.L938
	.byte	1,5,54,9
	.half	.L941-.L939
	.byte	1,5,61,9
	.half	.L942-.L941
	.byte	1,5,11,9
	.half	.L1263-.L942
	.byte	3,2,1,5,24,9
	.half	.L945-.L1263
	.byte	1,5,15,9
	.half	.L158-.L945
	.byte	3,2,1,5,37,9
	.half	.L948-.L158
	.byte	3,1,1,5,33,9
	.half	.L947-.L948
	.byte	1,5,23,9
	.half	.L949-.L947
	.byte	3,1,1,5,19,9
	.half	.L160-.L949
	.byte	3,2,1,5,33,9
	.half	.L951-.L160
	.byte	1,5,23,9
	.half	.L162-.L951
	.byte	3,2,1,5,33,9
	.half	.L952-.L162
	.byte	1,5,29,9
	.half	.L164-.L952
	.byte	3,2,1,5,43,9
	.half	.L1264-.L164
	.byte	1,5,37,9
	.half	.L1265-.L1264
	.byte	1,5,49,9
	.half	.L1266-.L1265
	.byte	1,5,21,9
	.half	.L953-.L1266
	.byte	3,1,1,5,25,7,9
	.half	.L1267-.L953
	.byte	3,2,1,5,55,9
	.half	.L955-.L1267
	.byte	1,5,25,9
	.half	.L165-.L955
	.byte	3,4,1,5,37,9
	.half	.L166-.L165
	.byte	3,119,1,5,33,9
	.half	.L163-.L166
	.byte	1,5,24,7,9
	.half	.L1268-.L163
	.byte	3,12,1,5,37,9
	.half	.L1269-.L1268
	.byte	3,114,1,5,33,9
	.half	.L161-.L1269
	.byte	1,5,45,7,9
	.half	.L1270-.L161
	.byte	3,16,1,5,22,9
	.half	.L1271-.L1270
	.byte	1,5,29,9
	.half	.L956-.L1271
	.byte	1,5,37,9
	.half	.L957-.L956
	.byte	1,5,20,9
	.half	.L950-.L957
	.byte	1,5,21,9
	.half	.L159-.L950
	.byte	3,110,1,5,23,9
	.half	.L960-.L159
	.byte	1,5,28,7,9
	.half	.L961-.L960
	.byte	3,124,1,5,24,9
	.half	.L157-.L961
	.byte	1,5,5,7,9
	.half	.L1272-.L157
	.byte	3,25,1,5,1,9
	.half	.L168-.L1272
	.byte	3,1,1,7,9
	.half	.L316-.L168
	.byte	0,1,1
.L1258:
	.sdecl	'.debug_ranges',debug,cluster('ips114_show_chinese')
	.sect	'.debug_ranges'
.L315:
	.word	-1,.L225,0,.L316-.L225,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_init')
	.sect	'.debug_info'
.L317:
	.word	252
	.half	3
	.word	.L318
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L320,.L319
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_init',0,1,197,7,6,1,1,1
	.word	.L227,.L531,.L226
	.byte	4
	.word	.L227,.L531
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_init')
	.sect	'.debug_abbrev'
.L318:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_init')
	.sect	'.debug_line'
.L319:
	.word	.L1274-.L1273
.L1273:
	.half	3
	.word	.L1276-.L1275
.L1275:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1276:
	.byte	5,6,7,0,5,2
	.word	.L227
	.byte	3,196,7,1,5,71,9
	.half	.L962-.L227
	.byte	3,5,1,5,87,9
	.half	.L1277-.L962
	.byte	1,5,106,9
	.half	.L1278-.L1277
	.byte	1,5,14,9
	.half	.L1279-.L1278
	.byte	1,5,26,9
	.half	.L1280-.L1279
	.byte	1,5,37,9
	.half	.L1281-.L1280
	.byte	1,5,55,9
	.half	.L1282-.L1281
	.byte	1,5,15,9
	.half	.L1283-.L1282
	.byte	3,3,1,5,30,9
	.half	.L1284-.L1283
	.byte	1,5,35,9
	.half	.L1285-.L1284
	.byte	1,5,45,9
	.half	.L1286-.L1285
	.byte	1,5,15,9
	.half	.L1287-.L1286
	.byte	3,1,1,5,31,9
	.half	.L1288-.L1287
	.byte	1,5,36,9
	.half	.L1289-.L1288
	.byte	1,5,46,9
	.half	.L1290-.L1289
	.byte	1,5,15,9
	.half	.L1291-.L1290
	.byte	3,1,1,5,30,9
	.half	.L1292-.L1291
	.byte	1,5,35,9
	.half	.L1293-.L1292
	.byte	1,5,46,9
	.half	.L1294-.L1293
	.byte	1,5,15,9
	.half	.L1295-.L1294
	.byte	3,1,1,5,31,9
	.half	.L1296-.L1295
	.byte	1,5,36,9
	.half	.L1297-.L1296
	.byte	1,5,47,9
	.half	.L1298-.L1297
	.byte	1,5,20,9
	.half	.L1299-.L1298
	.byte	3,2,1,5,22,9
	.half	.L1300-.L1299
	.byte	3,1,1,5,39,9
	.half	.L1301-.L1300
	.byte	1,5,5,9
	.half	.L1302-.L1301
	.byte	3,2,1,5,21,9
	.half	.L170-.L1302
	.byte	3,1,1,5,5,9
	.half	.L1303-.L170
	.byte	3,2,1,5,21,9
	.half	.L172-.L1303
	.byte	3,1,1,5,5,9
	.half	.L1304-.L172
	.byte	3,2,1,5,24,9
	.half	.L174-.L1304
	.byte	3,1,1,9
	.half	.L1305-.L174
	.byte	3,1,1,5,12,9
	.half	.L1306-.L1305
	.byte	3,1,1,5,14,9
	.half	.L1307-.L1306
	.byte	3,2,1,9
	.half	.L1308-.L1307
	.byte	3,1,1,9
	.half	.L1309-.L1308
	.byte	3,1,1,9
	.half	.L1310-.L1309
	.byte	3,1,1,5,37,9
	.half	.L175-.L1310
	.byte	3,125,1,5,69,9
	.half	.L1311-.L175
	.byte	1,5,37,9
	.half	.L176-.L1311
	.byte	3,1,1,5,69,9
	.half	.L1312-.L176
	.byte	1,5,37,9
	.half	.L177-.L1312
	.byte	3,1,1,5,69,9
	.half	.L1313-.L177
	.byte	1,5,37,9
	.half	.L178-.L1313
	.byte	3,1,1,5,69,9
	.half	.L1314-.L178
	.byte	1,5,24,9
	.half	.L180-.L1314
	.byte	3,3,1,5,5,9
	.half	.L1315-.L180
	.byte	3,1,1,5,24,9
	.half	.L1316-.L1315
	.byte	3,2,1,5,5,9
	.half	.L1317-.L1316
	.byte	3,1,1,9
	.half	.L1318-.L1317
	.byte	3,1,1,9
	.half	.L1319-.L1318
	.byte	3,1,1,9
	.half	.L1320-.L1319
	.byte	3,1,1,9
	.half	.L1321-.L1320
	.byte	3,1,1,5,24,9
	.half	.L1322-.L1321
	.byte	3,2,1,5,5,9
	.half	.L1323-.L1322
	.byte	3,1,1,5,24,9
	.half	.L1324-.L1323
	.byte	3,2,1,5,5,9
	.half	.L1325-.L1324
	.byte	3,1,1,5,24,9
	.half	.L1326-.L1325
	.byte	3,2,1,5,5,9
	.half	.L1327-.L1326
	.byte	3,1,1,5,24,9
	.half	.L1328-.L1327
	.byte	3,2,1,5,5,9
	.half	.L1329-.L1328
	.byte	3,1,1,5,24,9
	.half	.L1330-.L1329
	.byte	3,2,1,5,5,9
	.half	.L1331-.L1330
	.byte	3,1,1,5,24,9
	.half	.L1332-.L1331
	.byte	3,2,1,5,5,9
	.half	.L1333-.L1332
	.byte	3,1,1,5,24,9
	.half	.L1334-.L1333
	.byte	3,2,1,5,5,9
	.half	.L1335-.L1334
	.byte	3,1,1,5,24,9
	.half	.L1336-.L1335
	.byte	3,2,1,5,5,9
	.half	.L1337-.L1336
	.byte	3,1,1,9
	.half	.L1338-.L1337
	.byte	3,1,1,5,24,9
	.half	.L1339-.L1338
	.byte	3,2,1,5,5,9
	.half	.L1340-.L1339
	.byte	3,1,1,9
	.half	.L1341-.L1340
	.byte	3,1,1,9
	.half	.L1342-.L1341
	.byte	3,1,1,9
	.half	.L1343-.L1342
	.byte	3,1,1,9
	.half	.L1344-.L1343
	.byte	3,1,1,9
	.half	.L1345-.L1344
	.byte	3,1,1,9
	.half	.L1346-.L1345
	.byte	3,1,1,9
	.half	.L1347-.L1346
	.byte	3,1,1,9
	.half	.L1348-.L1347
	.byte	3,1,1,9
	.half	.L1349-.L1348
	.byte	3,1,1,9
	.half	.L1350-.L1349
	.byte	3,1,1,9
	.half	.L1351-.L1350
	.byte	3,1,1,9
	.half	.L1352-.L1351
	.byte	3,1,1,9
	.half	.L1353-.L1352
	.byte	3,1,1,5,24,9
	.half	.L1354-.L1353
	.byte	3,2,1,5,5,9
	.half	.L1355-.L1354
	.byte	3,1,1,9
	.half	.L1356-.L1355
	.byte	3,1,1,9
	.half	.L1357-.L1356
	.byte	3,1,1,9
	.half	.L1358-.L1357
	.byte	3,1,1,9
	.half	.L1359-.L1358
	.byte	3,1,1,9
	.half	.L1360-.L1359
	.byte	3,1,1,9
	.half	.L1361-.L1360
	.byte	3,1,1,9
	.half	.L1362-.L1361
	.byte	3,1,1,9
	.half	.L1363-.L1362
	.byte	3,1,1,9
	.half	.L1364-.L1363
	.byte	3,1,1,9
	.half	.L1365-.L1364
	.byte	3,1,1,9
	.half	.L1366-.L1365
	.byte	3,1,1,9
	.half	.L1367-.L1366
	.byte	3,1,1,9
	.half	.L1368-.L1367
	.byte	3,1,1,5,24,9
	.half	.L1369-.L1368
	.byte	3,2,1,9
	.half	.L1370-.L1369
	.byte	3,2,1,5,5,9
	.half	.L1371-.L1370
	.byte	3,1,1,5,17,9
	.half	.L185-.L1371
	.byte	3,2,1,5,22,9
	.half	.L1372-.L185
	.byte	3,1,1,5,1,9
	.half	.L1373-.L1372
	.byte	3,1,1,7,9
	.half	.L321-.L1373
	.byte	0,1,1
.L1274:
	.sdecl	'.debug_ranges',debug,cluster('ips114_init')
	.sect	'.debug_ranges'
.L320:
	.word	-1,.L227,0,.L321-.L227,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_write_index')
	.sect	'.debug_info'
.L322:
	.word	275
	.half	3
	.word	.L323
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L325,.L324
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_write_index',0,1,156,1,13,1,1
	.word	.L187,.L532,.L186
	.byte	4
	.byte	'dat',0,1,156,1,45
	.word	.L533,.L534
	.byte	5
	.word	.L187,.L532
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_write_index')
	.sect	'.debug_abbrev'
.L323:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_write_index')
	.sect	'.debug_line'
.L324:
	.word	.L1375-.L1374
.L1374:
	.half	3
	.word	.L1377-.L1376
.L1376:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1377:
	.byte	5,13,7,0,5,2
	.word	.L187
	.byte	3,155,1,1,5,5,9
	.half	.L548-.L187
	.byte	3,2,1,9
	.half	.L3-.L548
	.byte	3,1,1,9
	.half	.L551-.L3
	.byte	3,1,1,5,1,9
	.half	.L5-.L551
	.byte	3,1,1,7,9
	.half	.L326-.L5
	.byte	0,1,1
.L1375:
	.sdecl	'.debug_ranges',debug,cluster('ips114_write_index')
	.sect	'.debug_ranges'
.L325:
	.word	-1,.L187,0,.L326-.L187,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_set_region')
	.sect	'.debug_info'
.L327:
	.word	321
	.half	3
	.word	.L328
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L330,.L329
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_set_region',0,1,173,1,13,1,1
	.word	.L189,.L535,.L188
	.byte	4
	.byte	'x1',0,1,173,1,45
	.word	.L536,.L537
	.byte	4
	.byte	'y1',0,1,173,1,62
	.word	.L538,.L539
	.byte	4
	.byte	'x2',0,1,173,1,79
	.word	.L540,.L541
	.byte	4
	.byte	'y2',0,1,173,1,96
	.word	.L542,.L543
	.byte	5
	.word	.L189,.L535
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_set_region')
	.sect	'.debug_abbrev'
.L328:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_set_region')
	.sect	'.debug_line'
.L329:
	.word	.L1379-.L1378
.L1378:
	.half	3
	.word	.L1381-.L1380
.L1380:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1381:
	.byte	5,13,7,0,5,2
	.word	.L189
	.byte	3,172,1,1,5,5,9
	.half	.L1382-.L189
	.byte	3,5,1,9
	.half	.L553-.L1382
	.byte	3,1,1,9
	.half	.L1383-.L553
	.byte	3,1,1,9
	.half	.L1384-.L1383
	.byte	3,1,1,5,12,9
	.half	.L1385-.L1384
	.byte	3,2,1,5,14,9
	.half	.L1386-.L1385
	.byte	3,2,1,9
	.half	.L1387-.L1386
	.byte	3,10,1,9
	.half	.L1388-.L1387
	.byte	3,10,1,9
	.half	.L1389-.L1388
	.byte	3,10,1,5,32,9
	.half	.L6-.L1389
	.byte	3,100,1,5,13,9
	.half	.L1390-.L6
	.byte	3,1,1,9
	.half	.L1391-.L1390
	.byte	3,1,1,5,32,9
	.half	.L1392-.L1391
	.byte	3,1,1,5,13,9
	.half	.L1393-.L1392
	.byte	3,1,1,9
	.half	.L1394-.L1393
	.byte	3,1,1,5,32,9
	.half	.L1395-.L1394
	.byte	3,1,1,5,10,9
	.half	.L1396-.L1395
	.byte	3,1,1,5,32,9
	.half	.L7-.L1396
	.byte	3,3,1,5,13,9
	.half	.L1397-.L7
	.byte	3,1,1,9
	.half	.L1398-.L1397
	.byte	3,1,1,5,32,9
	.half	.L1399-.L1398
	.byte	3,1,1,5,13,9
	.half	.L1400-.L1399
	.byte	3,1,1,9
	.half	.L1401-.L1400
	.byte	3,1,1,5,32,9
	.half	.L1402-.L1401
	.byte	3,1,1,5,10,9
	.half	.L1403-.L1402
	.byte	3,1,1,5,32,9
	.half	.L8-.L1403
	.byte	3,3,1,5,13,9
	.half	.L1404-.L8
	.byte	3,1,1,9
	.half	.L1405-.L1404
	.byte	3,1,1,5,32,9
	.half	.L1406-.L1405
	.byte	3,1,1,5,13,9
	.half	.L1407-.L1406
	.byte	3,1,1,9
	.half	.L1408-.L1407
	.byte	3,1,1,5,32,9
	.half	.L1409-.L1408
	.byte	3,1,1,5,10,9
	.half	.L1410-.L1409
	.byte	3,1,1,5,32,9
	.half	.L9-.L1410
	.byte	3,3,1,5,13,9
	.half	.L1411-.L9
	.byte	3,1,1,9
	.half	.L1412-.L1411
	.byte	3,1,1,5,32,9
	.half	.L1413-.L1412
	.byte	3,1,1,5,13,9
	.half	.L1414-.L1413
	.byte	3,1,1,9
	.half	.L1415-.L1414
	.byte	3,1,1,5,32,9
	.half	.L1416-.L1415
	.byte	3,1,1,5,10,9
	.half	.L1417-.L1416
	.byte	3,1,1,5,1,9
	.half	.L11-.L1417
	.byte	3,2,1,7,9
	.half	.L331-.L11
	.byte	0,1,1
.L1379:
	.sdecl	'.debug_ranges',debug,cluster('ips114_set_region')
	.sect	'.debug_ranges'
.L330:
	.word	-1,.L189,0,.L331-.L189,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_debug_init')
	.sect	'.debug_info'
.L332:
	.word	276
	.half	3
	.word	.L333
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L335,.L334
	.byte	2
	.word	.L228
	.byte	3
	.byte	'ips114_debug_init',0,1,235,1,13,1,1
	.word	.L191,.L544,.L190
	.byte	4
	.word	.L191,.L544
	.byte	5
	.byte	'info',0,1,237,1,25
	.word	.L545,.L546
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_debug_init')
	.sect	'.debug_abbrev'
.L333:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips114_debug_init')
	.sect	'.debug_line'
.L334:
	.word	.L1419-.L1418
.L1418:
	.half	3
	.word	.L1421-.L1420
.L1420:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips114.c',0,0,0,0,0
.L1421:
	.byte	5,13,7,0,5,2
	.word	.L191
	.byte	3,234,1,1,5,31,9
	.half	.L594-.L191
	.byte	3,3,1,5,23,9
	.half	.L1422-.L594
	.byte	3,2,1,5,21,9
	.half	.L1423-.L1422
	.byte	1,5,26,9
	.half	.L1424-.L1423
	.byte	3,1,1,5,24,9
	.half	.L1425-.L1424
	.byte	1,5,26,9
	.half	.L1426-.L1425
	.byte	3,1,1,5,24,9
	.half	.L1427-.L1426
	.byte	1,5,12,9
	.half	.L1428-.L1427
	.byte	3,2,1,5,14,9
	.half	.L1429-.L1428
	.byte	3,2,1,9
	.half	.L1430-.L1429
	.byte	3,5,1,9
	.half	.L1431-.L1430
	.byte	3,5,1,5,32,9
	.half	.L15-.L1431
	.byte	3,120,1,5,30,9
	.half	.L1432-.L15
	.byte	1,5,32,9
	.half	.L1433-.L1432
	.byte	3,1,1,5,30,9
	.half	.L1434-.L1433
	.byte	1,5,10,9
	.half	.L1435-.L1434
	.byte	3,1,1,5,32,9
	.half	.L16-.L1435
	.byte	3,3,1,5,30,9
	.half	.L1436-.L16
	.byte	1,5,32,9
	.half	.L1437-.L1436
	.byte	3,1,1,5,30,9
	.half	.L1438-.L1437
	.byte	1,5,10,9
	.half	.L1439-.L1438
	.byte	3,1,1,9
	.half	.L17-.L1439
	.byte	3,4,1,5,26,9
	.half	.L19-.L17
	.byte	3,2,1,5,24,9
	.half	.L1440-.L19
	.byte	1,5,32,9
	.half	.L1441-.L1440
	.byte	3,1,1,5,30,9
	.half	.L1442-.L1441
	.byte	1,5,24,9
	.half	.L1443-.L1442
	.byte	3,2,1,5,1,9
	.half	.L1444-.L1443
	.byte	3,1,1,7,9
	.half	.L336-.L1444
	.byte	0,1,1
.L1419:
	.sdecl	'.debug_ranges',debug,cluster('ips114_debug_init')
	.sect	'.debug_ranges'
.L335:
	.word	-1,.L191,0,.L336-.L191,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_width_max')
	.sect	'.debug_info'
.L337:
	.word	234
	.half	3
	.word	.L338
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L228
	.byte	3
	.byte	'ips114_width_max',0,12,62,33
	.word	.L373
	.byte	1,5,3
	.word	ips114_width_max
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_width_max')
	.sect	'.debug_abbrev'
.L338:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_height_max')
	.sect	'.debug_info'
.L339:
	.word	235
	.half	3
	.word	.L340
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L228
	.byte	3
	.byte	'ips114_height_max',0,12,63,33
	.word	.L373
	.byte	1,5,3
	.word	ips114_height_max
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_height_max')
	.sect	'.debug_abbrev'
.L340:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_pencolor')
	.sect	'.debug_info'
.L341:
	.word	232
	.half	3
	.word	.L342
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L228
	.byte	3
	.byte	'ips114_pencolor',0,12,64,33
	.word	.L373
	.byte	5,3
	.word	ips114_pencolor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_pencolor')
	.sect	'.debug_abbrev'
.L342:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_bgcolor')
	.sect	'.debug_info'
.L343:
	.word	231
	.half	3
	.word	.L344
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L228
	.byte	3
	.byte	'ips114_bgcolor',0,12,65,33
	.word	.L373
	.byte	5,3
	.word	ips114_bgcolor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_bgcolor')
	.sect	'.debug_abbrev'
.L344:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_display_dir')
	.sect	'.debug_info'
.L345:
	.word	235
	.half	3
	.word	.L346
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L228
	.byte	3
	.byte	'ips114_display_dir',0,12,67,33
	.word	.L362
	.byte	5,3
	.word	ips114_display_dir
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_display_dir')
	.sect	'.debug_abbrev'
.L346:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips114_display_font')
	.sect	'.debug_info'
.L347:
	.word	236
	.half	3
	.word	.L348
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips114.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L228
	.byte	3
	.byte	'ips114_display_font',0,12,68,33
	.word	.L365
	.byte	5,3
	.word	ips114_display_font
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips114_display_font')
	.sect	'.debug_abbrev'
.L348:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_clear')
	.sect	'.debug_loc'
.L351:
	.word	-1,.L193,.L595-.L193,.L596-.L193
	.half	1
	.byte	98
	.word	.L597-.L193,.L349-.L193
	.half	1
	.byte	108
	.word	.L22-.L193,.L598-.L193
	.half	1
	.byte	98
	.word	.L602-.L193,.L603-.L193
	.half	1
	.byte	100
	.word	.L604-.L193,.L605-.L193
	.half	1
	.byte	100
	.word	0,0
.L353:
	.word	-1,.L193,.L599-.L193,.L27-.L193
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L192:
	.word	-1,.L193,0,.L349-.L193
	.half	2
	.byte	138,0
	.word	0,0
.L354:
	.word	-1,.L193,.L600-.L193,.L601-.L193
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_debug_init')
	.sect	'.debug_loc'
.L546:
	.word	-1,.L191,0,.L544-.L191
	.half	2
	.byte	145,104
	.word	0,0
.L190:
	.word	-1,.L191,0,.L594-.L191
	.half	2
	.byte	138,0
	.word	.L594-.L191,.L544-.L191
	.half	2
	.byte	138,24
	.word	.L544-.L191,.L544-.L191
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_draw_line')
	.sect	'.debug_loc'
.L384:
	.word	-1,.L205,0,.L378-.L205
	.half	2
	.byte	145,0
	.word	.L636-.L205,.L378-.L205
	.half	1
	.byte	91
	.word	.L652-.L205,.L653-.L205
	.half	1
	.byte	86
	.word	.L656-.L205,.L657-.L205
	.half	1
	.byte	86
	.word	.L667-.L205,.L668-.L205
	.half	1
	.byte	86
	.word	.L672-.L205,.L673-.L205
	.half	1
	.byte	86
	.word	.L675-.L205,.L676-.L205
	.half	1
	.byte	86
	.word	.L682-.L205,.L58-.L205
	.half	1
	.byte	86
	.word	0,0
.L204:
	.word	-1,.L205,0,.L629-.L205
	.half	2
	.byte	138,0
	.word	.L629-.L205,.L378-.L205
	.half	2
	.byte	138,8
	.word	.L378-.L205,.L378-.L205
	.half	2
	.byte	138,0
	.word	0,0
.L391:
	.word	-1,.L205,.L650-.L205,.L54-.L205
	.half	1
	.byte	92
	.word	.L55-.L205,.L58-.L205
	.half	1
	.byte	92
	.word	0,0
.L390:
	.word	-1,.L205,.L648-.L205,.L54-.L205
	.half	1
	.byte	93
	.word	.L55-.L205,.L58-.L205
	.half	1
	.byte	93
	.word	0,0
.L387:
	.word	-1,.L205,.L677-.L205,.L678-.L205
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L381:
	.word	-1,.L205,0,.L630-.L205
	.half	1
	.byte	86
	.word	.L639-.L205,.L640-.L205
	.half	1
	.byte	89
	.word	.L385-.L205,.L641-.L205
	.half	1
	.byte	89
	.word	.L53-.L205,.L644-.L205
	.half	1
	.byte	89
	.word	.L646-.L205,.L647-.L205
	.half	1
	.byte	89
	.word	.L57-.L205,.L651-.L205
	.half	1
	.byte	89
	.word	.L654-.L205,.L655-.L205
	.half	1
	.byte	89
	.word	.L60-.L205,.L662-.L205
	.half	1
	.byte	89
	.word	.L663-.L205,.L664-.L205
	.half	1
	.byte	89
	.word	.L61-.L205,.L665-.L205
	.half	1
	.byte	89
	.word	.L67-.L205,.L680-.L205
	.half	1
	.byte	89
	.word	0,0
.L379:
	.word	-1,.L205,0,.L631-.L205
	.half	1
	.byte	84
	.word	.L633-.L205,.L634-.L205
	.half	1
	.byte	95
	.word	.L64-.L205,.L671-.L205
	.half	1
	.byte	95
	.word	.L679-.L205,.L378-.L205
	.half	1
	.byte	95
	.word	0,0
.L388:
	.word	-1,.L205,.L643-.L205,.L51-.L205
	.half	1
	.byte	94
	.word	.L53-.L205,.L378-.L205
	.half	1
	.byte	94
	.word	0,0
.L382:
	.word	-1,.L205,0,.L630-.L205
	.half	1
	.byte	87
	.word	.L635-.L205,.L378-.L205
	.half	1
	.byte	90
	.word	0,0
.L380:
	.word	-1,.L205,0,.L632-.L205
	.half	1
	.byte	85
	.word	.L637-.L205,.L638-.L205
	.half	1
	.byte	88
	.word	.L50-.L205,.L642-.L205
	.half	1
	.byte	88
	.word	.L644-.L205,.L645-.L205
	.half	1
	.byte	88
	.word	.L648-.L205,.L649-.L205
	.half	1
	.byte	88
	.word	.L57-.L205,.L651-.L205
	.half	1
	.byte	88
	.word	.L56-.L205,.L654-.L205
	.half	1
	.byte	88
	.word	.L55-.L205,.L658-.L205
	.half	1
	.byte	88
	.word	.L659-.L205,.L660-.L205
	.half	1
	.byte	88
	.word	.L59-.L205,.L661-.L205
	.half	1
	.byte	88
	.word	.L65-.L205,.L666-.L205
	.half	1
	.byte	88
	.word	.L669-.L205,.L670-.L205
	.half	1
	.byte	88
	.word	.L68-.L205,.L674-.L205
	.half	1
	.byte	88
	.word	.L681-.L205,.L680-.L205
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_draw_point')
	.sect	'.debug_loc'
.L377:
	.word	-1,.L203,0,.L619-.L203
	.half	1
	.byte	86
	.word	.L622-.L203,.L372-.L203
	.half	1
	.byte	90
	.word	.L627-.L203,.L628-.L203
	.half	1
	.byte	85
	.word	0,0
.L202:
	.word	-1,.L203,0,.L372-.L203
	.half	2
	.byte	138,0
	.word	0,0
.L374:
	.word	-1,.L203,0,.L620-.L203
	.half	1
	.byte	84
	.word	.L623-.L203,.L620-.L203
	.half	1
	.byte	88
	.word	.L46-.L203,.L626-.L203
	.half	1
	.byte	88
	.word	0,0
.L375:
	.word	-1,.L203,0,.L621-.L203
	.half	1
	.byte	85
	.word	.L624-.L203,.L625-.L203
	.half	1
	.byte	89
	.word	.L46-.L203,.L626-.L203
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_full')
	.sect	'.debug_loc'
.L357:
	.word	-1,.L195,0,.L606-.L195
	.half	1
	.byte	84
	.word	.L607-.L195,.L355-.L195
	.half	1
	.byte	88
	.word	0,0
.L358:
	.word	-1,.L195,.L608-.L195,.L609-.L195
	.half	1
	.byte	98
	.word	.L610-.L195,.L355-.L195
	.half	1
	.byte	108
	.word	.L30-.L195,.L611-.L195
	.half	1
	.byte	98
	.word	.L615-.L195,.L616-.L195
	.half	1
	.byte	100
	.word	.L617-.L195,.L618-.L195
	.half	1
	.byte	100
	.word	0,0
.L359:
	.word	-1,.L195,.L612-.L195,.L35-.L195
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L194:
	.word	-1,.L195,0,.L355-.L195
	.half	2
	.byte	138,0
	.word	0,0
.L360:
	.word	-1,.L195,.L613-.L195,.L614-.L195
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_init')
	.sect	'.debug_loc'
.L226:
	.word	-1,.L227,0,.L962-.L227
	.half	2
	.byte	138,0
	.word	.L962-.L227,.L531-.L227
	.half	2
	.byte	138,16
	.word	.L531-.L227,.L531-.L227
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_set_color')
	.sect	'.debug_loc'
.L371:
	.word	-1,.L201,0,.L367-.L201
	.half	1
	.byte	85
	.word	0,0
.L200:
	.word	-1,.L201,0,.L367-.L201
	.half	2
	.byte	138,0
	.word	0,0
.L369:
	.word	-1,.L201,0,.L367-.L201
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_set_dir')
	.sect	'.debug_loc'
.L363:
	.word	-1,.L197,0,.L361-.L197
	.half	1
	.byte	84
	.word	0,0
.L196:
	.word	-1,.L197,0,.L361-.L197
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_set_font')
	.sect	'.debug_loc'
.L366:
	.word	-1,.L199,0,.L364-.L199
	.half	1
	.byte	84
	.word	0,0
.L198:
	.word	-1,.L199,0,.L364-.L199
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_set_region')
	.sect	'.debug_loc'
.L188:
	.word	-1,.L189,0,.L535-.L189
	.half	2
	.byte	138,0
	.word	0,0
.L537:
	.word	-1,.L189,0,.L552-.L189
	.half	1
	.byte	84
	.word	.L555-.L189,.L552-.L189
	.half	1
	.byte	88
	.word	.L562-.L189,.L563-.L189
	.half	1
	.byte	88
	.word	.L570-.L189,.L571-.L189
	.half	1
	.byte	88
	.word	.L578-.L189,.L579-.L189
	.half	1
	.byte	88
	.word	.L586-.L189,.L587-.L189
	.half	1
	.byte	88
	.word	0,0
.L541:
	.word	-1,.L189,0,.L553-.L189
	.half	1
	.byte	86
	.word	.L558-.L189,.L559-.L189
	.half	1
	.byte	90
	.word	.L564-.L189,.L565-.L189
	.half	1
	.byte	90
	.word	.L572-.L189,.L573-.L189
	.half	1
	.byte	90
	.word	.L580-.L189,.L581-.L189
	.half	1
	.byte	90
	.word	.L588-.L189,.L589-.L189
	.half	1
	.byte	90
	.word	0,0
.L539:
	.word	-1,.L189,0,.L554-.L189
	.half	1
	.byte	85
	.word	.L556-.L189,.L557-.L189
	.half	1
	.byte	89
	.word	.L566-.L189,.L567-.L189
	.half	1
	.byte	89
	.word	.L574-.L189,.L575-.L189
	.half	1
	.byte	89
	.word	.L582-.L189,.L583-.L189
	.half	1
	.byte	89
	.word	.L590-.L189,.L591-.L189
	.half	1
	.byte	89
	.word	0,0
.L543:
	.word	-1,.L189,0,.L553-.L189
	.half	1
	.byte	87
	.word	.L560-.L189,.L561-.L189
	.half	1
	.byte	91
	.word	.L568-.L189,.L569-.L189
	.half	1
	.byte	91
	.word	.L576-.L189,.L577-.L189
	.half	1
	.byte	91
	.word	.L584-.L189,.L585-.L189
	.half	1
	.byte	91
	.word	.L592-.L189,.L593-.L189
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_binary_image')
	.sect	'.debug_loc'
.L468:
	.word	-1,.L217,.L793-.L217,.L794-.L217
	.half	1
	.byte	98
	.word	.L795-.L217,.L454-.L217
	.half	1
	.byte	109
	.word	.L113-.L217,.L796-.L217
	.half	1
	.byte	98
	.word	.L814-.L217,.L815-.L217
	.half	1
	.byte	100
	.word	.L817-.L217,.L818-.L217
	.half	1
	.byte	100
	.word	0,0
.L462:
	.word	-1,.L217,0,.L454-.L217
	.half	2
	.byte	145,4
	.word	.L789-.L217,.L454-.L217
	.half	1
	.byte	93
	.word	0,0
.L461:
	.word	-1,.L217,0,.L454-.L217
	.half	2
	.byte	145,0
	.word	.L788-.L217,.L454-.L217
	.half	1
	.byte	92
	.word	.L816-.L217,.L815-.L217
	.half	1
	.byte	85
	.word	0,0
.L460:
	.word	-1,.L217,0,.L783-.L217
	.half	1
	.byte	87
	.word	.L116-.L217,.L803-.L217
	.half	1
	.byte	91
	.word	0,0
.L464:
	.word	-1,.L217,.L807-.L217,.L808-.L217
	.half	1
	.byte	84
	.word	0,0
.L458:
	.word	-1,.L217,0,.L784-.L217
	.half	1
	.byte	100
	.word	.L787-.L217,.L454-.L217
	.half	1
	.byte	108
	.word	0,0
.L469:
	.word	-1,.L217,.L806-.L217,.L115-.L217
	.half	1
	.byte	111
	.word	0,0
.L216:
	.word	-1,.L217,0,.L454-.L217
	.half	2
	.byte	138,0
	.word	0,0
.L465:
	.word	-1,.L217,.L802-.L217,.L454-.L217
	.half	1
	.byte	88
	.word	0,0
.L466:
	.word	-1,.L217,.L812-.L217,.L813-.L217
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L459:
	.word	-1,.L217,0,.L783-.L217
	.half	1
	.byte	86
	.word	.L804-.L217,.L805-.L217
	.half	1
	.byte	90
	.word	.L118-.L217,.L809-.L217
	.half	1
	.byte	90
	.word	0,0
.L467:
	.word	-1,.L217,.L810-.L217,.L811-.L217
	.half	1
	.byte	82
	.word	0,0
.L455:
	.word	-1,.L217,0,.L785-.L217
	.half	1
	.byte	84
	.word	.L790-.L217,.L785-.L217
	.half	1
	.byte	88
	.word	.L114-.L217,.L797-.L217
	.half	1
	.byte	88
	.word	.L800-.L217,.L801-.L217
	.half	1
	.byte	88
	.word	0,0
.L456:
	.word	-1,.L217,0,.L786-.L217
	.half	1
	.byte	85
	.word	.L791-.L217,.L792-.L217
	.half	1
	.byte	89
	.word	.L798-.L217,.L799-.L217
	.half	1
	.byte	89
	.word	.L800-.L217,.L801-.L217
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_char')
	.sect	'.debug_loc'
.L396:
	.word	-1,.L207,0,.L684-.L207
	.half	1
	.byte	86
	.word	.L687-.L207,.L392-.L207
	.half	1
	.byte	90
	.word	0,0
.L403:
	.word	-1,.L207,0,.L392-.L207
	.half	3
	.byte	145,128,126
	.word	0,0
.L408:
	.word	-1,.L207,0,.L392-.L207
	.half	3
	.byte	145,128,126
	.word	0,0
.L399:
	.word	-1,.L207,.L696-.L207,.L401-.L207
	.half	5
	.byte	144,32,157,32,0
	.word	.L704-.L207,.L406-.L207
	.half	1
	.byte	83
	.word	0,0
.L206:
	.word	-1,.L207,0,.L683-.L207
	.half	2
	.byte	138,0
	.word	.L683-.L207,.L392-.L207
	.half	3
	.byte	138,128,2
	.word	.L392-.L207,.L392-.L207
	.half	2
	.byte	138,0
	.word	0,0
.L400:
	.word	-1,.L207,.L698-.L207,.L75-.L207
	.half	1
	.byte	82
	.word	.L708-.L207,.L706-.L207
	.half	1
	.byte	81
	.word	.L706-.L207,.L82-.L207
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L411:
	.word	-1,.L207,.L707-.L207,.L82-.L207
	.half	1
	.byte	84
	.word	0,0
.L405:
	.word	-1,.L207,.L697-.L207,.L75-.L207
	.half	1
	.byte	81
	.word	0,0
.L410:
	.word	-1,.L207,.L705-.L207,.L706-.L207
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L393:
	.word	-1,.L207,0,.L685-.L207
	.half	1
	.byte	84
	.word	.L688-.L207,.L685-.L207
	.half	1
	.byte	88
	.word	.L71-.L207,.L691-.L207
	.half	1
	.byte	88
	.word	.L694-.L207,.L695-.L207
	.half	1
	.byte	88
	.word	.L72-.L207,.L699-.L207
	.half	1
	.byte	88
	.word	.L702-.L207,.L703-.L207
	.half	1
	.byte	88
	.word	0,0
.L394:
	.word	-1,.L207,0,.L686-.L207
	.half	1
	.byte	85
	.word	.L689-.L207,.L690-.L207
	.half	1
	.byte	89
	.word	.L692-.L207,.L693-.L207
	.half	1
	.byte	89
	.word	.L694-.L207,.L695-.L207
	.half	1
	.byte	89
	.word	.L700-.L207,.L701-.L207
	.half	1
	.byte	89
	.word	.L702-.L207,.L703-.L207
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_chinese')
	.sect	'.debug_loc'
.L519:
	.word	-1,.L225,0,.L925-.L225
	.half	1
	.byte	100
	.word	.L930-.L225,.L515-.L225
	.half	1
	.byte	108
	.word	0,0
.L522:
	.word	-1,.L225,0,.L515-.L225
	.half	2
	.byte	145,0
	.word	.L932-.L225,.L515-.L225
	.half	1
	.byte	91
	.word	.L954-.L225,.L955-.L225
	.half	1
	.byte	85
	.word	0,0
.L524:
	.word	-1,.L225,.L945-.L225,.L515-.L225
	.half	1
	.byte	88
	.word	0,0
.L224:
	.word	-1,.L225,0,.L924-.L225
	.half	2
	.byte	138,0
	.word	.L924-.L225,.L515-.L225
	.half	2
	.byte	138,8
	.word	.L515-.L225,.L515-.L225
	.half	2
	.byte	138,0
	.word	0,0
.L525:
	.word	-1,.L225,.L952-.L225,.L161-.L225
	.half	1
	.byte	94
	.word	0,0
.L526:
	.word	-1,.L225,.L951-.L225,.L159-.L225
	.half	1
	.byte	89
	.word	0,0
.L520:
	.word	-1,.L225,0,.L926-.L225
	.half	1
	.byte	87
	.word	.L931-.L225,.L515-.L225
	.half	2
	.byte	145,124
	.word	.L937-.L225,.L938-.L225
	.half	1
	.byte	95
	.word	.L946-.L225,.L947-.L225
	.half	1
	.byte	95
	.word	0,0
.L530:
	.word	-1,.L225,.L949-.L225,.L950-.L225
	.half	1
	.byte	111
	.word	.L956-.L225,.L957-.L225
	.half	5
	.byte	144,32,157,32,0
	.word	.L957-.L225,.L159-.L225
	.half	1
	.byte	95
	.word	.L159-.L225,.L157-.L225
	.half	1
	.byte	111
	.word	0,0
.L518:
	.word	-1,.L225,0,.L926-.L225
	.half	1
	.byte	86
	.word	.L929-.L225,.L515-.L225
	.half	1
	.byte	90
	.word	0,0
.L527:
	.word	-1,.L225,.L953-.L225,.L163-.L225
	.half	1
	.byte	95
	.word	0,0
.L528:
	.word	-1,.L225,.L948-.L225,.L157-.L225
	.half	2
	.byte	145,120
	.word	.L160-.L225,.L162-.L225
	.half	5
	.byte	144,32,157,32,0
	.word	.L958-.L225,.L959-.L225
	.half	1
	.byte	95
	.word	.L959-.L225,.L157-.L225
	.half	5
	.byte	144,32,157,32,0
	.word	.L960-.L225,.L961-.L225
	.half	1
	.byte	95
	.word	0,0
.L529:
	.word	-1,.L225,.L936-.L225,.L515-.L225
	.half	1
	.byte	92
	.word	0,0
.L516:
	.word	-1,.L225,0,.L927-.L225
	.half	1
	.byte	84
	.word	.L933-.L225,.L927-.L225
	.half	1
	.byte	88
	.word	.L939-.L225,.L940-.L225
	.half	1
	.byte	88
	.word	.L943-.L225,.L944-.L225
	.half	1
	.byte	88
	.word	0,0
.L517:
	.word	-1,.L225,0,.L928-.L225
	.half	1
	.byte	85
	.word	.L934-.L225,.L935-.L225
	.half	1
	.byte	89
	.word	.L941-.L225,.L942-.L225
	.half	1
	.byte	89
	.word	.L943-.L225,.L944-.L225
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_float')
	.sect	'.debug_loc'
.L445:
	.word	-1,.L215,0,.L759-.L215
	.half	2
	.byte	144,35
	.word	.L770-.L215,.L771-.L215
	.half	2
	.byte	144,37
	.word	.L776-.L215,.L777-.L215
	.half	2
	.byte	144,37
	.word	0,0
.L450:
	.word	-1,.L215,.L778-.L215,.L779-.L215
	.half	2
	.byte	144,34
	.word	0,0
.L453:
	.word	-1,.L215,0,.L441-.L215
	.half	2
	.byte	145,104
	.word	0,0
.L214:
	.word	-1,.L215,0,.L758-.L215
	.half	2
	.byte	138,0
	.word	.L758-.L215,.L441-.L215
	.half	2
	.byte	138,24
	.word	.L441-.L215,.L441-.L215
	.half	2
	.byte	138,0
	.word	0,0
.L446:
	.word	-1,.L215,0,.L441-.L215
	.half	2
	.byte	145,0
	.word	.L764-.L215,.L441-.L215
	.half	1
	.byte	89
	.word	0,0
.L451:
	.word	-1,.L215,.L767-.L215,.L112-.L215
	.half	2
	.byte	144,38
	.word	.L768-.L215,.L769-.L215
	.half	2
	.byte	144,38
	.word	.L772-.L215,.L773-.L215
	.half	2
	.byte	144,38
	.word	.L774-.L215,.L775-.L215
	.half	2
	.byte	144,38
	.word	0,0
.L447:
	.word	-1,.L215,0,.L441-.L215
	.half	2
	.byte	145,4
	.word	.L765-.L215,.L441-.L215
	.half	1
	.byte	88
	.word	.L778-.L215,.L779-.L215
	.half	1
	.byte	86
	.word	0,0
.L442:
	.word	-1,.L215,0,.L760-.L215
	.half	1
	.byte	84
	.word	.L762-.L215,.L441-.L215
	.half	2
	.byte	145,124
	.word	.L760-.L215,.L766-.L215
	.half	1
	.byte	95
	.word	.L780-.L215,.L781-.L215
	.half	1
	.byte	84
	.word	0,0
.L443:
	.word	-1,.L215,0,.L761-.L215
	.half	1
	.byte	85
	.word	.L763-.L215,.L441-.L215
	.half	1
	.byte	94
	.word	.L782-.L215,.L781-.L215
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_gray_image')
	.sect	'.debug_loc'
.L482:
	.word	-1,.L219,.L849-.L219,.L129-.L219
	.half	1
	.byte	81
	.word	0,0
.L484:
	.word	-1,.L219,.L830-.L219,.L831-.L219
	.half	1
	.byte	98
	.word	.L832-.L219,.L470-.L219
	.half	1
	.byte	109
	.word	.L123-.L219,.L833-.L219
	.half	1
	.byte	98
	.word	.L850-.L219,.L845-.L219
	.half	1
	.byte	100
	.word	.L852-.L219,.L853-.L219
	.half	1
	.byte	100
	.word	0,0
.L477:
	.word	-1,.L219,0,.L470-.L219
	.half	2
	.byte	145,4
	.word	.L825-.L219,.L470-.L219
	.half	1
	.byte	93
	.word	0,0
.L476:
	.word	-1,.L219,0,.L470-.L219
	.half	2
	.byte	145,0
	.word	.L824-.L219,.L470-.L219
	.half	1
	.byte	92
	.word	.L851-.L219,.L845-.L219
	.half	1
	.byte	85
	.word	0,0
.L475:
	.word	-1,.L219,0,.L819-.L219
	.half	1
	.byte	87
	.word	.L126-.L219,.L840-.L219
	.half	1
	.byte	91
	.word	0,0
.L480:
	.word	-1,.L219,.L844-.L219,.L845-.L219
	.half	1
	.byte	82
	.word	0,0
.L473:
	.word	-1,.L219,0,.L820-.L219
	.half	1
	.byte	100
	.word	.L823-.L219,.L470-.L219
	.half	1
	.byte	108
	.word	0,0
.L485:
	.word	-1,.L219,.L843-.L219,.L125-.L219
	.half	1
	.byte	111
	.word	0,0
.L218:
	.word	-1,.L219,0,.L470-.L219
	.half	2
	.byte	138,0
	.word	0,0
.L481:
	.word	-1,.L219,.L839-.L219,.L470-.L219
	.half	1
	.byte	88
	.word	0,0
.L483:
	.word	-1,.L219,.L847-.L219,.L848-.L219
	.half	5
	.byte	144,32,157,32,0
	.word	.L129-.L219,.L130-.L219
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L478:
	.word	-1,.L219,0,.L470-.L219
	.half	2
	.byte	145,8
	.word	.L826-.L219,.L470-.L219
	.half	1
	.byte	94
	.word	0,0
.L474:
	.word	-1,.L219,0,.L819-.L219
	.half	1
	.byte	86
	.word	.L841-.L219,.L842-.L219
	.half	1
	.byte	90
	.word	.L128-.L219,.L846-.L219
	.half	1
	.byte	90
	.word	0,0
.L471:
	.word	-1,.L219,0,.L821-.L219
	.half	1
	.byte	84
	.word	.L827-.L219,.L821-.L219
	.half	1
	.byte	88
	.word	.L124-.L219,.L834-.L219
	.half	1
	.byte	88
	.word	.L837-.L219,.L838-.L219
	.half	1
	.byte	88
	.word	0,0
.L472:
	.word	-1,.L219,0,.L822-.L219
	.half	1
	.byte	85
	.word	.L828-.L219,.L829-.L219
	.half	1
	.byte	89
	.word	.L835-.L219,.L836-.L219
	.half	1
	.byte	89
	.word	.L837-.L219,.L838-.L219
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_int')
	.sect	'.debug_loc'
.L423:
	.word	-1,.L211,0,.L729-.L211
	.half	1
	.byte	86
	.word	.L732-.L211,.L419-.L211
	.half	1
	.byte	89
	.word	.L738-.L211,.L739-.L211
	.half	1
	.byte	84
	.word	0,0
.L427:
	.word	0,0
.L430:
	.word	-1,.L211,0,.L419-.L211
	.half	2
	.byte	145,112
	.word	0,0
.L210:
	.word	-1,.L211,0,.L728-.L211
	.half	2
	.byte	138,0
	.word	.L728-.L211,.L419-.L211
	.half	2
	.byte	138,16
	.word	.L419-.L211,.L419-.L211
	.half	2
	.byte	138,0
	.word	0,0
.L424:
	.word	-1,.L211,0,.L729-.L211
	.half	1
	.byte	87
	.word	.L733-.L211,.L419-.L211
	.half	1
	.byte	92
	.word	0,0
.L428:
	.word	-1,.L211,.L737-.L211,.L419-.L211
	.half	1
	.byte	95
	.word	0,0
.L420:
	.word	-1,.L211,0,.L730-.L211
	.half	1
	.byte	84
	.word	.L734-.L211,.L730-.L211
	.half	1
	.byte	90
	.word	.L740-.L211,.L741-.L211
	.half	1
	.byte	90
	.word	0,0
.L421:
	.word	-1,.L211,0,.L731-.L211
	.half	1
	.byte	85
	.word	.L735-.L211,.L736-.L211
	.half	1
	.byte	91
	.word	.L740-.L211,.L741-.L211
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_rgb565_image')
	.sect	'.debug_loc'
.L495:
	.word	-1,.L221,0,.L486-.L221
	.half	2
	.byte	145,8
	.word	.L861-.L221,.L486-.L221
	.half	1
	.byte	94
	.word	0,0
.L499:
	.word	-1,.L221,.L865-.L221,.L866-.L221
	.half	1
	.byte	98
	.word	.L867-.L221,.L486-.L221
	.half	1
	.byte	109
	.word	.L135-.L221,.L868-.L221
	.half	1
	.byte	98
	.word	.L883-.L221,.L880-.L221
	.half	1
	.byte	100
	.word	.L884-.L221,.L142-.L221
	.half	1
	.byte	100
	.word	.L886-.L221,.L887-.L221
	.half	1
	.byte	100
	.word	0,0
.L494:
	.word	-1,.L221,0,.L486-.L221
	.half	2
	.byte	145,4
	.word	.L860-.L221,.L486-.L221
	.half	1
	.byte	93
	.word	0,0
.L493:
	.word	-1,.L221,0,.L486-.L221
	.half	2
	.byte	145,0
	.word	.L859-.L221,.L486-.L221
	.half	1
	.byte	92
	.word	.L885-.L221,.L142-.L221
	.half	1
	.byte	85
	.word	0,0
.L492:
	.word	-1,.L221,0,.L854-.L221
	.half	1
	.byte	87
	.word	.L138-.L221,.L875-.L221
	.half	1
	.byte	91
	.word	0,0
.L497:
	.word	-1,.L221,.L879-.L221,.L880-.L221
	.half	5
	.byte	144,32,157,32,0
	.word	.L141-.L221,.L142-.L221
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L490:
	.word	-1,.L221,0,.L855-.L221
	.half	1
	.byte	100
	.word	.L858-.L221,.L486-.L221
	.half	1
	.byte	108
	.word	0,0
.L500:
	.word	-1,.L221,.L878-.L221,.L137-.L221
	.half	1
	.byte	111
	.word	0,0
.L220:
	.word	-1,.L221,0,.L486-.L221
	.half	2
	.byte	138,0
	.word	0,0
.L498:
	.word	-1,.L221,.L874-.L221,.L486-.L221
	.half	1
	.byte	88
	.word	0,0
.L491:
	.word	-1,.L221,0,.L854-.L221
	.half	1
	.byte	86
	.word	.L876-.L221,.L877-.L221
	.half	1
	.byte	90
	.word	.L881-.L221,.L882-.L221
	.half	1
	.byte	90
	.word	0,0
.L487:
	.word	-1,.L221,0,.L856-.L221
	.half	1
	.byte	84
	.word	.L862-.L221,.L856-.L221
	.half	1
	.byte	88
	.word	.L136-.L221,.L869-.L221
	.half	1
	.byte	88
	.word	.L872-.L221,.L873-.L221
	.half	1
	.byte	88
	.word	0,0
.L488:
	.word	-1,.L221,0,.L857-.L221
	.half	1
	.byte	85
	.word	.L863-.L221,.L864-.L221
	.half	1
	.byte	89
	.word	.L870-.L221,.L871-.L221
	.half	1
	.byte	89
	.word	.L872-.L221,.L873-.L221
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_string')
	.sect	'.debug_loc'
.L416:
	.word	-1,.L209,0,.L709-.L209
	.half	1
	.byte	100
	.word	.L712-.L209,.L412-.L209
	.half	1
	.byte	108
	.word	0,0
.L208:
	.word	-1,.L209,0,.L412-.L209
	.half	2
	.byte	138,0
	.word	0,0
.L418:
	.word	-1,.L209,.L716-.L209,.L717-.L209
	.half	1
	.byte	90
	.word	.L96-.L209,.L412-.L209
	.half	1
	.byte	90
	.word	0,0
.L413:
	.word	-1,.L209,0,.L710-.L209
	.half	1
	.byte	84
	.word	.L713-.L209,.L710-.L209
	.half	1
	.byte	88
	.word	.L718-.L209,.L719-.L209
	.half	1
	.byte	88
	.word	.L723-.L209,.L724-.L209
	.half	1
	.byte	88
	.word	0,0
.L414:
	.word	-1,.L209,0,.L711-.L209
	.half	1
	.byte	85
	.word	.L714-.L209,.L715-.L209
	.half	1
	.byte	89
	.word	.L720-.L209,.L721-.L209
	.half	1
	.byte	89
	.word	.L721-.L209,.L722-.L209
	.half	1
	.byte	85
	.word	.L725-.L209,.L726-.L209
	.half	1
	.byte	89
	.word	.L726-.L209,.L727-.L209
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_uint')
	.sect	'.debug_loc'
.L435:
	.word	-1,.L213,0,.L743-.L213
	.half	1
	.byte	86
	.word	.L746-.L213,.L431-.L213
	.half	1
	.byte	89
	.word	.L754-.L213,.L755-.L213
	.half	1
	.byte	84
	.word	0,0
.L438:
	.word	0,0
.L440:
	.word	-1,.L213,0,.L431-.L213
	.half	2
	.byte	145,112
	.word	0,0
.L212:
	.word	-1,.L213,0,.L742-.L213
	.half	2
	.byte	138,0
	.word	.L742-.L213,.L431-.L213
	.half	2
	.byte	138,16
	.word	.L431-.L213,.L431-.L213
	.half	2
	.byte	138,0
	.word	0,0
.L436:
	.word	-1,.L213,0,.L743-.L213
	.half	1
	.byte	87
	.word	.L747-.L213,.L431-.L213
	.half	1
	.byte	92
	.word	.L752-.L213,.L753-.L213
	.half	1
	.byte	85
	.word	0,0
.L439:
	.word	-1,.L213,.L751-.L213,.L431-.L213
	.half	1
	.byte	95
	.word	0,0
.L432:
	.word	-1,.L213,0,.L744-.L213
	.half	1
	.byte	84
	.word	.L748-.L213,.L744-.L213
	.half	1
	.byte	90
	.word	.L756-.L213,.L757-.L213
	.half	1
	.byte	90
	.word	0,0
.L433:
	.word	-1,.L213,0,.L745-.L213
	.half	1
	.byte	85
	.word	.L749-.L213,.L750-.L213
	.half	1
	.byte	91
	.word	.L756-.L213,.L757-.L213
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_show_wave')
	.sect	'.debug_loc'
.L514:
	.word	-1,.L223,.L898-.L223,.L899-.L223
	.half	1
	.byte	98
	.word	.L900-.L223,.L501-.L223
	.half	1
	.byte	109
	.word	.L145-.L223,.L901-.L223
	.half	1
	.byte	98
	.word	.L911-.L223,.L910-.L223
	.half	1
	.byte	100
	.word	.L922-.L223,.L923-.L223
	.half	1
	.byte	100
	.word	0,0
.L508:
	.word	-1,.L223,0,.L501-.L223
	.half	2
	.byte	145,4
	.word	.L894-.L223,.L501-.L223
	.half	1
	.byte	93
	.word	0,0
.L507:
	.word	-1,.L223,0,.L501-.L223
	.half	2
	.byte	145,0
	.word	.L893-.L223,.L501-.L223
	.half	1
	.byte	92
	.word	.L912-.L223,.L910-.L223
	.half	1
	.byte	85
	.word	0,0
.L510:
	.word	-1,.L223,.L909-.L223,.L910-.L223
	.half	5
	.byte	144,32,157,32,0
	.word	.L908-.L223,.L501-.L223
	.half	1
	.byte	94
	.word	0,0
.L222:
	.word	-1,.L223,0,.L501-.L223
	.half	2
	.byte	138,0
	.word	0,0
.L511:
	.word	-1,.L223,.L907-.L223,.L908-.L223
	.half	1
	.byte	94
	.word	0,0
.L506:
	.word	-1,.L223,0,.L888-.L223
	.half	1
	.byte	87
	.word	.L916-.L223,.L917-.L223
	.half	1
	.byte	91
	.word	0,0
.L513:
	.word	-1,.L223,.L917-.L223,.L918-.L223
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L504:
	.word	-1,.L223,0,.L889-.L223
	.half	1
	.byte	100
	.word	.L892-.L223,.L501-.L223
	.half	1
	.byte	108
	.word	0,0
.L505:
	.word	-1,.L223,0,.L888-.L223
	.half	1
	.byte	86
	.word	.L154-.L223,.L913-.L223
	.half	1
	.byte	90
	.word	0,0
.L512:
	.word	-1,.L223,.L914-.L223,.L915-.L223
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L502:
	.word	-1,.L223,0,.L890-.L223
	.half	1
	.byte	84
	.word	.L895-.L223,.L890-.L223
	.half	1
	.byte	88
	.word	.L146-.L223,.L902-.L223
	.half	1
	.byte	88
	.word	.L905-.L223,.L906-.L223
	.half	1
	.byte	88
	.word	.L917-.L223,.L919-.L223
	.half	1
	.byte	88
	.word	0,0
.L503:
	.word	-1,.L223,0,.L891-.L223
	.half	1
	.byte	85
	.word	.L896-.L223,.L897-.L223
	.half	1
	.byte	89
	.word	.L903-.L223,.L904-.L223
	.half	1
	.byte	89
	.word	.L905-.L223,.L906-.L223
	.half	1
	.byte	89
	.word	.L920-.L223,.L921-.L223
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips114_write_index')
	.sect	'.debug_loc'
.L534:
	.word	-1,.L187,0,.L547-.L187
	.half	1
	.byte	84
	.word	.L548-.L187,.L532-.L187
	.half	1
	.byte	88
	.word	.L2-.L187,.L549-.L187
	.half	1
	.byte	84
	.word	.L550-.L187,.L551-.L187
	.half	1
	.byte	85
	.word	0,0
.L186:
	.word	-1,.L187,0,.L532-.L187
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1445:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('ips114_write_index')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L187,.L532-.L187
	.sdecl	'.debug_frame',debug,cluster('ips114_set_region')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L189,.L535-.L189
	.sdecl	'.debug_frame',debug,cluster('ips114_debug_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L191,.L544-.L191
	.byte	4
	.word	(.L594-.L191)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L544-.L594)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips114_clear')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L193,.L349-.L193
	.sdecl	'.debug_frame',debug,cluster('ips114_full')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L195,.L355-.L195
	.sdecl	'.debug_frame',debug,cluster('ips114_set_dir')
	.sect	'.debug_frame'
	.word	24
	.word	.L1445,.L197,.L361-.L197
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('ips114_set_font')
	.sect	'.debug_frame'
	.word	24
	.word	.L1445,.L199,.L364-.L199
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('ips114_set_color')
	.sect	'.debug_frame'
	.word	24
	.word	.L1445,.L201,.L367-.L201
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('ips114_draw_point')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L203,.L372-.L203
	.sdecl	'.debug_frame',debug,cluster('ips114_draw_line')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L205,.L378-.L205
	.byte	4
	.word	(.L629-.L205)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L378-.L629)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips114_show_char')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L207,.L392-.L207
	.byte	4
	.word	(.L683-.L207)/2
	.byte	19,128,2,22,26,4,19,138,128,2,4
	.word	(.L392-.L683)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('ips114_show_string')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L209,.L412-.L209
	.sdecl	'.debug_frame',debug,cluster('ips114_show_int')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L211,.L419-.L211
	.byte	4
	.word	(.L728-.L211)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L419-.L728)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips114_show_uint')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L213,.L431-.L213
	.byte	4
	.word	(.L742-.L213)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L431-.L742)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips114_show_float')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L215,.L441-.L215
	.byte	4
	.word	(.L758-.L215)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L441-.L758)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips114_show_binary_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L217,.L454-.L217
	.sdecl	'.debug_frame',debug,cluster('ips114_show_gray_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L219,.L470-.L219
	.sdecl	'.debug_frame',debug,cluster('ips114_show_rgb565_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L221,.L486-.L221
	.sdecl	'.debug_frame',debug,cluster('ips114_show_wave')
	.sect	'.debug_frame'
	.word	12
	.word	.L1445,.L223,.L501-.L223
	.sdecl	'.debug_frame',debug,cluster('ips114_show_chinese')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L225,.L515-.L225
	.byte	4
	.word	(.L924-.L225)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L515-.L924)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips114_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1445,.L227,.L531-.L227
	.byte	4
	.word	(.L962-.L227)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L531-.L962)/2
	.byte	19,0,8,26,0,0
	; Module end
