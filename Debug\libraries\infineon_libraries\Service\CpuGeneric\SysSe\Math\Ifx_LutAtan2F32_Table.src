	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc17556a --dep-file=Ifx_LutAtan2F32_Table.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32_Table.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32_Table.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32_Table.c'

	
$TC16X
	
	.sdecl	'.rodata.Ifx_LutAtan2F32_Table.Ifx_g_LutAtan2F32_FxpAngle_table',data,rom,cluster('Ifx_g_LutAtan2F32_FxpAngle_table')
	.sect	'.rodata.Ifx_LutAtan2F32_Table.Ifx_g_LutAtan2F32_FxpAngle_table'
	.global	Ifx_g_LutAtan2F32_FxpAngle_table
	.align	2
Ifx_g_LutAtan2F32_FxpAngle_table:	.type	object
	.size	Ifx_g_LutAtan2F32_FxpAngle_table,4100
	.space	8
	.word	1,1,2,3,3,4,5,5
	.word	6,7,7,8,8,9,10,10
	.word	11,12,12,13,14,14,15,15
	.word	16,17,17,18,19,19,20,21
	.word	21,22,22,23,24,24,25,26
	.word	26,27,27,28,29,29,30,31
	.word	31,32,33,33,34,34,35,36
	.word	36,37,38,38,39,40,40,41
	.word	41,42,43,43,44,45,45,46
	.word	47,47,48,48,49,50,50,51
	.word	52,52,53,53,54,55,55,56
	.word	57,57,58,59,59,60,60,61
	.word	62,62,63,64,64,65,65,66
	.word	67,67,68,69,69,70,71,71
	.word	72,72,73,74,74,75,76,76
	.word	77,77,78,79,79,80,81,81
	.word	82,82,83,84,84,85,86,86
	.word	87,87,88,89,89,90,91,91
	.word	92,92,93,94,94,95,96,96
	.word	97,97,98,99,99,100,101,101
	.word	102,102,103,104,104,105,106,106
	.word	107,107,108,109,109,110,110,111
	.word	112,112,113,114,114,115,115,116
	.word	117,117,118,118,119,120,120,121
	.word	122,122,123,123,124,125,125,126
	.word	126,127,128,128,129,130,130,131
	.word	131,132,133,133,134,134,135,136
	.word	136,137,137,138,139,139,140,140
	.word	141,142,142,143,144,144,145,145
	.word	146,147,147,148,148,149,150,150
	.word	151,151,152,153,153,154,154,155
	.word	156,156,157,157,158,159,159,160
	.word	160,161,162,162,163,163,164,165
	.word	165,166,166,167,168,168,169,169
	.word	170,171,171,172,172,173,174,174
	.word	175,175,176,176,177,178,178,179
	.word	179,180,181,181,182,182,183,184
	.word	184,185,185,186,186,187,188,188
	.word	189,189,190,191,191,192,192,193
	.word	193,194,195,195,196,196,197,198
	.word	198,199,199,200,200,201,202,202
	.word	203,203,204,204,205,206,206,207
	.word	207,208,208,209,210,210,211,211
	.word	212,212,213,214,214,215,215,216
	.word	216,217,218,218,219,219,220,220
	.word	221,222,222,223,223,224,224,225
	.word	226,226,227,227,228,228,229,229
	.word	230,231,231,232,232,233,233,234
	.word	234,235,236,236,237,237,238,238
	.word	239,239,240,241,241,242,242,243
	.word	243,244,244,245,246,246,247,247
	.word	248,248,249,249,250,251,251,252
	.word	252,253,253,254,254,255,255,256
	.word	257,257,258,258,259,259,260,260
	.word	261,261,262,262,263,264,264,265
	.word	265,266,266,267,267,268,268,269
	.word	269,270,270,271,272,272,273,273
	.word	274,274,275,275,276,276,277,277
	.word	278,278,279,279,280,281,281,282
	.word	282,283,283,284,284,285,285,286
	.word	286,287,287,288,288,289,289,290
	.word	290,291,291,292,293,293,294,294
	.word	295,295,296,296,297,297,298,298
	.word	299,299,300,300,301,301,302,302
	.word	303,303,304,304,305,305,306,306
	.word	307,307,308,308,309,309,310,310
	.word	311,311,312,312,313,313,314,314
	.word	315,315,316,316,317,317,318,318
	.word	319,319,320,320,321,321,322,322
	.word	323,323,324,324,325,325,326,326
	.word	327,327,328,328,329,329,330,330
	.word	331,331,332,332,333,333,334,334
	.word	334,335,335,336,336,337,337,338
	.word	338,339,339,340,340,341,341,342
	.word	342,343,343,344,344,345,345,345
	.word	346,346,347,347,348,348,349,349
	.word	350,350,351,351,352,352,353,353
	.word	353,354,354,355,355,356,356,357
	.word	357,358,358,359,359,360,360,360
	.word	361,361,362,362,363,363,364,364
	.word	365,365,365,366,366,367,367,368
	.word	368,369,369,370,370,370,371,371
	.word	372,372,373,373,374,374,375,375
	.word	375,376,376,377,377,378,378,379
	.word	379,379,380,380,381,381,382,382
	.word	383,383,383,384,384,385,385,386
	.word	386,386,387,387,388,388,389,389
	.word	390,390,390,391,391,392,392,393
	.word	393,393,394,394,395,395,396,396
	.word	396,397,397,398,398,399,399,399
	.word	400,400,401,401,402,402,402,403
	.word	403,404,404,405,405,405,406,406
	.word	407,407,407,408,408,409,409,410
	.word	410,410,411,411,412,412,412,413
	.word	413,414,414,414,415,415,416,416
	.word	417,417,417,418,418,419,419,419
	.word	420,420,421,421,421,422,422,423
	.word	423,423,424,424,425,425,425,426
	.word	426,427,427,427,428,428,429,429
	.word	429,430,430,431,431,431,432,432
	.word	433,433,433,434,434,435,435,435
	.word	436,436,437,437,437,438,438,439
	.word	439,439,440,440,440,441,441,442
	.word	442,442,443,443,444,444,444,445
	.word	445,445,446,446,447,447,447,448
	.word	448,448,449,449,450,450,450,451
	.word	451,452,452,452,453,453,453,454
	.word	454,455,455,455,456,456,456,457
	.word	457,458,458,458,459,459,459,460
	.word	460,460,461,461,462,462,462,463
	.word	463,463,464,464,464,465,465,466
	.word	466,466,467,467,467,468,468,468
	.word	469,469,470,470,470,471,471,471
	.word	472,472,472,473,473,473,474,474
	.word	475,475,475,476,476,476,477,477
	.word	477,478,478,478,479,479,479,480
	.word	480,481,481,481,482,482,482,483
	.word	483,483,484,484,484,485,485,485
	.word	486,486,486,487,487,487,488,488
	.word	488,489,489,489,490,490,490,491
	.word	491,491,492,492,493,493,493,494
	.word	494,494,495,495,495,496,496,496
	.word	497,497,497,498,498,498,499,499
	.word	499,500,500,500,500,501,501,501
	.word	502,502,502,503,503,503,504,504
	.word	504,505,505,505,506,506,506,507
	.word	507,507,508,508,508,509,509,509
	.word	510,510,510,511
	.word	511,511,511
	.sdecl	'.rodata.Ifx_LutAtan2F32_Table.Ifx_g_LutAtan2F32_table',data,rom,cluster('Ifx_g_LutAtan2F32_table')
	.sect	'.rodata.Ifx_LutAtan2F32_Table.Ifx_g_LutAtan2F32_table'
	.global	Ifx_g_LutAtan2F32_table
	.align	2
Ifx_g_LutAtan2F32_table:	.type	object
	.size	Ifx_g_LutAtan2F32_table,4100
	.space	4
	.word	981467131,989855723,994050012,998244267,1000341421,1002438512,1004535579,1006632619
	.word	1007681293,1008729779,1009778244,1010826688,1011875108,1012923501,1013971867,1015020203
	.word	1015545037,1016069172,1016593289,1017117387,1017641465,1018165522,1018689557,1019213569
	.word	1019737557,1020261520,1020785457,1021309367,1021833249,1022357102,1022880926,1023404718
	.word	1023669327,1023931191,1024193038,1024454867,1024716678,1024978471,1025240245,1025502000
	.word	1025763734,1026025448,1026287141,1026548813,1026810463,1027072090,1027333695,1027595276
	.word	1027856833,1028118366,1028379874,1028641357,1028902813,1029164244,1029425647,1029687024
	.word	1029948372,1030209692,1030470983,1030732245,1030993477,1031254679,1031515850,1031776990
	.word	1031918441,1032048979,1032179500,1032310005,1032440493,1032570964,1032701418,1032831854
	.word	1032962272,1033092672,1033223054,1033353418,1033483762,1033614088,1033744394,1033874680
	.word	1034004947,1034135194,1034265421,1034395627,1034525813,1034655977,1034786121,1034916242
	.word	1035046343,1035176421,1035306477,1035436511,1035566522,1035696510,1035826475,1035956417
	.word	1036086335,1036216230,1036346100,1036475946,1036605768,1036735564,1036865336,1036995083
	.word	1037124804,1037254499,1037384168,1037513812,1037643428,1037773019,1037902582,1038032118
	.word	1038161627,1038291108,1038420561,1038549987,1038679384,1038808753,1038938093,1039067404
	.word	1039196685,1039325938,1039455160,1039584353,1039713516,1039842648,1039971750,1040100821
	.word	1040208626,1040273131,1040337619,1040402092,1040466549,1040530990,1040595414,1040659822
	.word	1040724214,1040788589,1040852948,1040917290,1040981615,1041045923,1041110214,1041174488
	.word	1041238744,1041302983,1041367205,1041431409,1041495595,1041559763,1041623913,1041688046
	.word	1041752160,1041816255,1041880333,1041944391,1042008432,1042072453,1042136456,1042200439
	.word	1042264404,1042328349,1042392275,1042456182,1042520069,1042583937,1042647785,1042711613
	.word	1042775422,1042839210,1042902978,1042966726,1043030453,1043094160,1043157847,1043221513
	.word	1043285158,1043348782,1043412386,1043475968,1043539529,1043603069,1043666587,1043730084
	.word	1043793560,1043857013,1043920445,1043983855,1044047243,1044110609,1044173953,1044237274
	.word	1044300573,1044363850,1044427104,1044490335,1044553544,1044616729,1044679892,1044743031
	.word	1044806147,1044869240,1044932310,1044995356,1045058378,1045121377,1045184352,1045247303
	.word	1045310230,1045373133,1045436012,1045498867,1045561697,1045624503,1045687284,1045750041
	.word	1045812773,1045875480,1045938162,1046000819,1046063451,1046126057,1046188639,1046251195
	.word	1046313725,1046376230,1046438709,1046501162,1046563590,1046625991,1046688367,1046750716
	.word	1046813039,1046875336,1046937607,1046999850,1047062068,1047124258,1047186422,1047248559
	.word	1047310669,1047372752,1047434808,1047496836,1047558837,1047620811,1047682758,1047744676
	.word	1047806568,1047868431,1047930267,1047992074,1048053854,1048115605,1048177329,1048239024
	.word	1048300691,1048362329,1048423939,1048485520,1048547072,1048592298,1048623046,1048653779
	.word	1048684497,1048715201,1048745890,1048776565,1048807225,1048837870,1048868501,1048899117
	.word	1048929718,1048960304,1048990875,1049021431,1049051972,1049082499,1049113010,1049143506
	.word	1049173987,1049204452,1049234903,1049265338,1049295758,1049326162,1049356551,1049386925
	.word	1049417283,1049447626,1049477953,1049508265,1049538561,1049568842,1049599106,1049629355
	.word	1049659588,1049689806,1049720008,1049750193,1049780363,1049810517,1049840655,1049870777
	.word	1049900883,1049930973,1049961046,1049991104,1050021145,1050051170,1050081179,1050111172
	.word	1050141148,1050171108,1050201051,1050230978,1050260889,1050290783,1050320661,1050350522
	.word	1050380366,1050410194,1050440005,1050469799,1050499577,1050529338,1050559082,1050588809
	.word	1050618520,1050648213,1050677890,1050707549,1050737192,1050766818,1050796426,1050826018
	.word	1050855592,1050885149,1050914689,1050944212,1050973718,1051003206,1051032677,1051062131
	.word	1051091567,1051120986,1051150388,1051179772,1051209138,1051238487,1051267819,1051297133
	.word	1051326429,1051355708,1051384969,1051414212,1051443438,1051472646,1051501836,1051531009
	.word	1051560163,1051589300,1051618419,1051647520,1051676603,1051705668,1051734715,1051763744
	.word	1051792755,1051821748,1051850723,1051879680,1051908618,1051937539,1051966441,1051995325
	.word	1052024191,1052053038,1052081867,1052110678,1052139471,1052168245,1052197001,1052225738
	.word	1052254457,1052283157,1052311839,1052340503,1052369148,1052397774,1052426381,1052454971
	.word	1052483541,1052512093,1052540626,1052569140,1052597636,1052626113,1052654571,1052683010
	.word	1052711431,1052739833,1052768215,1052796579,1052824924,1052853250,1052881558,1052909846
	.word	1052938115,1052966365,1052994596,1053022808,1053051001,1053079175,1053107330,1053135466
	.word	1053163582,1053191680,1053219758,1053247817,1053275856,1053303877,1053331878,1053359860
	.word	1053387822,1053415766,1053443690,1053471594,1053499479,1053527345,1053555191,1053583018
	.word	1053610826,1053638614,1053666382,1053694131,1053721860,1053749570,1053777260,1053804931
	.word	1053832582,1053860214,1053887826,1053915418,1053942991,1053970543,1053998077,1054025590
	.word	1054053084,1054080558,1054108012,1054135447,1054162861,1054190256,1054217631,1054244987
	.word	1054272322,1054299638,1054326933,1054354209,1054381465,1054408701,1054435917,1054463113
	.word	1054490289,1054517445,1054544581,1054571697,1054598794,1054625870,1054652926,1054679962
	.word	1054706978,1054733974,1054760949,1054787905,1054814840,1054841756,1054868651,1054895526
	.word	1054922381,1054949216,1054976031,1055002825,1055029599,1055056353,1055083087,1055109800
	.word	1055136494,1055163167,1055189819,1055216452,1055243064,1055269655,1055296227,1055322778
	.word	1055349309,1055375819,1055402309,1055428779,1055455229,1055481658,1055508066,1055534454
	.word	1055560822,1055587169,1055613496,1055639803,1055666089,1055692354,1055718600,1055744824
	.word	1055771028,1055797212,1055823375,1055849518,1055875640,1055901742,1055927823,1055953883
	.word	1055979923,1056005943,1056031942,1056057920,1056083878,1056109815,1056135732,1056161628
	.word	1056187503,1056213358,1056239193,1056265006,1056290799,1056316572,1056342323,1056368055
	.word	1056393765,1056419455,1056445124,1056470773,1056496401,1056522008,1056547594,1056573160
	.word	1056598706,1056624230,1056649734,1056675217,1056700679,1056726121,1056751542,1056776943
	.word	1056802322,1056827681,1056853019,1056878337,1056903633,1056928909,1056954165,1056972004
	.word	1056984611,1056997207,1057009793,1057022369,1057034934,1057047489,1057060034,1057072568
	.word	1057085092,1057097606,1057110109,1057122602,1057135084,1057147556,1057160017,1057172469
	.word	1057184909,1057197340,1057209760,1057222170,1057234569,1057246958,1057259336,1057271704
	.word	1057284062,1057296409,1057308746,1057321073,1057333389,1057345695,1057357990,1057370275
	.word	1057382549,1057394814,1057407067,1057419311,1057431544,1057443766,1057455979,1057468180
	.word	1057480372,1057492553,1057504723,1057516884,1057529034,1057541173,1057553302,1057565421
	.word	1057577529,1057589627,1057601715,1057613792,1057625859,1057637915,1057649961,1057661997
	.word	1057674022,1057686037,1057698041,1057710035,1057722019,1057733993,1057745956,1057757908
	.word	1057769851,1057781782,1057793704,1057805615,1057817516,1057829406,1057841287,1057853156
	.word	1057865016,1057876865,1057888703,1057900532,1057912350,1057924157,1057935954,1057947741
	.word	1057959518,1057971284,1057983040,1057994786,1058006521,1058018246,1058029960,1058041665
	.word	1058053359,1058065042,1058076715,1058088378,1058100031,1058111673,1058123305,1058134927
	.word	1058146538,1058158140,1058169730,1058181311,1058192881,1058204441,1058215991,1058227530
	.word	1058239059,1058250578,1058262086,1058273585,1058285073,1058296550,1058308018,1058319475
	.word	1058330922,1058342358,1058353785,1058365201,1058376607,1058388002,1058399388,1058410763
	.word	1058422128,1058433483,1058444827,1058456161,1058467485,1058478799,1058490103,1058501396
	.word	1058512679,1058523952,1058535215,1058546468,1058557710,1058568942,1058580164,1058591376
	.word	1058602578,1058613769,1058624951,1058636122,1058647283,1058658434,1058669574,1058680705
	.word	1058691825,1058702936,1058714036,1058725126,1058736206,1058747275,1058758335,1058769384
	.word	1058780424,1058791453,1058802472,1058813481,1058824480,1058835469,1058846448,1058857417
	.word	1058868376,1058879324,1058890263,1058901191,1058912110,1058923018,1058933916,1058944805
	.word	1058955683,1058966551,1058977409,1058988257,1058999095,1059009924,1059020742,1059031550
	.word	1059042348,1059053136,1059063914,1059074682,1059085440,1059096189,1059106927,1059117655
	.word	1059128373,1059139082,1059149780,1059160468,1059171147,1059181816,1059192474,1059203123
	.word	1059213762,1059224391,1059235010,1059245619,1059256218,1059266807,1059277386,1059287956
	.word	1059298516,1059309065,1059319605,1059330135,1059340656,1059351166,1059361666,1059372157
	.word	1059382638,1059393109,1059403570,1059414022,1059424463,1059434895,1059445317,1059455729
	.word	1059466131,1059476524,1059486907,1059497280,1059507643,1059517997,1059528341,1059538675
	.word	1059548999,1059559314,1059569619,1059579914,1059590199,1059600475,1059610741,1059620997
	.word	1059631244,1059641481,1059651708,1059661926,1059672134,1059682332,1059692521,1059702700
	.word	1059712869,1059723029,1059733179,1059743320,1059753450,1059763572,1059773683,1059783785
	.word	1059793878,1059803961,1059814034,1059824098,1059834152,1059844196,1059854231,1059864257
	.word	1059874273,1059884279,1059894276,1059904264,1059914242,1059924210,1059934169,1059944118
	.word	1059954058,1059963988,1059973909,1059983821,1059993723,1060003615,1060013499,1060023372
	.word	1060033236,1060043091,1060052937,1060062773,1060072599,1060082416,1060092224,1060102023
	.word	1060111812,1060121591,1060131361,1060141122,1060150874,1060160616,1060170349,1060180073
	.word	1060189787,1060199492,1060209187,1060218874,1060228551,1060238218,1060247877,1060257526
	.word	1060267166,1060276796,1060286418,1060296030,1060305633,1060315226,1060324811,1060334386
	.word	1060343952,1060353509,1060363056,1060372594,1060382124,1060391644,1060401154,1060410656
	.word	1060420149,1060429632,1060439106,1060448571,1060458027,1060467474,1060476912,1060486341
	.word	1060495760,1060505171,1060514572,1060523964,1060533347,1060542722,1060552087,1060561443
	.word	1060570790,1060580128,1060589457,1060598777,1060608087,1060617389,1060626682,1060635966
	.word	1060645241,1060654507,1060663764,1060673012,1060682251,1060691481,1060700703,1060709915
	.word	1060719118,1060728313,1060737498,1060746675,1060755843,1060765001,1060774151,1060783292
	.word	1060792425,1060801548,1060810663,1060819768,1060828865,1060837953,1060847032,1060856103
	.word	1060865165,1060874217,1060883261,1060892297,1060901323,1060910341,1060919350,1060928350
	.word	1060937341,1060946324,1060955298,1060964263,1060973220,1060982168,1060991107,1061000037
	.word	1061008959,1061017872,1061026777,1061035673,1061044560,1061053438,1061062308,1061071169
	.word	1061080022,1061088866,1061097701,1061106528,1061115346,1061124156,1061132957,1061141749
	.word	1061150533,1061159309,1061168076,1061176834,1061185584,1061194325,1061203058,1061211782
	.word	1061220497,1061229205,1061237903,1061246594,1061255275,1061263949,1061272614,1061281270
	.word	1061289918,1061298558,1061307189,1061315812,1061324426,1061333032,1061341630,1061350219
	.word	1061358800,1061367372,1061375936,1061384492,1061393039,1061401578,1061410109,1061418631
	.word	1061427145,1061435651,1061444149,1061452638,1061461119,1061469591,1061478056,1061486512
	.word	1061494960,1061503399,1061511831,1061520254,1061528669,1061537076,1061545474,1061553865
	.word	1061562247,1061570621,1061578987,1061587344,1061595694,1061604035,1061612368,1061620693
	.word	1061629010,1061637319,1061645620,1061653912,1061662197,1061670473,1061678742,1061687002
	.word	1061695254,1061703498,1061711734,1061719963,1061728183,1061736395,1061744599,1061752795
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	950
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32_Table.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	248
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	251
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	296
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	308
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	388
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	362
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	394
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	394
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	362
	.byte	6,0,10
	.word	256
	.byte	11
	.word	282
	.byte	6,0,10
	.word	317
	.byte	11
	.word	349
	.byte	6,0,10
	.word	399
	.byte	11
	.word	418
	.byte	6,0,10
	.word	434
	.byte	11
	.word	449
	.byte	11
	.word	463
	.byte	6,0,7
	.byte	'short int',0,2,5,12
	.byte	'__wchar_t',0,3,1,1
	.word	533
	.byte	7
	.byte	'unsigned int',0,4,7,12
	.byte	'__size_t',0,3,1,1
	.word	564
	.byte	7
	.byte	'int',0,4,5,12
	.byte	'__ptrdiff_t',0,3,1,1
	.word	597
	.byte	13,1,3
	.word	624
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	626
	.byte	7
	.byte	'unsigned char',0,1,8,12
	.byte	'uint8',0,4,105,29
	.word	649
	.byte	7
	.byte	'unsigned short int',0,2,7,12
	.byte	'uint16',0,4,109,29
	.word	680
	.byte	7
	.byte	'unsigned long int',0,4,7,12
	.byte	'uint32',0,4,113,29
	.word	717
	.byte	12
	.byte	'uint64',0,4,118,29
	.word	362
	.byte	12
	.byte	'sint16',0,4,126,29
	.word	533
	.byte	7
	.byte	'long int',0,4,5,12
	.byte	'sint32',0,4,131,1,29
	.word	783
	.byte	7
	.byte	'long long int',0,8,5,12
	.byte	'sint64',0,4,138,1,29
	.word	811
	.byte	12
	.byte	'float32',0,4,167,1,29
	.word	308
	.byte	12
	.byte	'pvoid',0,5,57,28
	.word	394
	.byte	12
	.byte	'Ifx_TickTime',0,5,79,28
	.word	811
	.byte	12
	.byte	'Ifx_Lut_FxpAngle',0,6,84,16
	.word	783
	.byte	14,132,32
	.word	783
	.byte	15,128,8,0
.L10:
	.byte	16
	.word	921
	.byte	14,132,32
	.word	308
	.byte	15,128,8,0
.L11:
	.byte	16
	.word	937
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,46,1,49,19,0,0,11,5
	.byte	0,49,19,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47
	.byte	15,0,0,16,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32_Table.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Lut.h',0,0,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('Ifx_g_LutAtan2F32_FxpAngle_table')
	.sect	'.debug_info'
.L6:
	.word	294
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32_Table.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'Ifx_g_LutAtan2F32_FxpAngle_table',0,3,57,24
	.word	.L10
	.byte	1,5,3
	.word	Ifx_g_LutAtan2F32_FxpAngle_table
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_g_LutAtan2F32_FxpAngle_table')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_g_LutAtan2F32_table')
	.sect	'.debug_info'
.L8:
	.word	286
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutAtan2F32_Table.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'Ifx_g_LutAtan2F32_table',0,3,190,8,24
	.word	.L11
	.byte	1,5,3
	.word	Ifx_g_LutAtan2F32_table
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_g_LutAtan2F32_table')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
