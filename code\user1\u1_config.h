/*
 * u1_config.h - 统一配置文件
 * 作�? BMW_智能车组_Baiyuyu_Xiaohuihui_Wzz
 * 创建时间: 2025�?7�?6�?
 * 描述: 合并所有配置项，统一管理4G模块相关配置
 */

#ifndef U1_CONFIG_H
#define U1_CONFIG_H

/*
 * ========================================================================
 * 讯飞语音识别服务 (ASR) 配置
 * ========================================================================
 */

/* 
 * [!! 重要 !!] 请将此处的值替换为您在讯飞开放平台申请的真实凭证 
 * 官方申请地址: https://www.xfyun.cn/
 */
#define U1_XF_APP_ID       "5d8cb6e1"                    /* 修改此处 -> 讯飞应用 APPID */
#define U1_XF_API_KEY      "b5c6e8f7a9d1e2f3a4b5c6d7e8f9a0b1"  /* 修改此处 -> 讯飞API Key */
#define U1_XF_API_SECRET   "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"  /* 修改此处 -> 讯飞API Secret */

/* 
 * ========================================================================
 * 硬件配置
 * ========================================================================
 */
#define U1_UART_INDEX      UART_3                       // 4G模块串口
#define U1_UART_BAUDRATE   115200                       // 波特�?
#define U1_UART_TX_PIN     UART3_TX_P15_6               // 发送引�?
#define U1_UART_RX_PIN     UART3_RX_P15_7               // 接收引脚

/* 音频采集硬件配置 */
#define U1_AUDIO_ADC       ADC0_CH4_A4                  // 音频ADC通道
#define U1_RECORD_BUTTON   P11_3                        // 录音按键引脚
#define U1_AUDIO_TIMER     CCU60_CH0                    // 音频采样定时器
#define U1_RANDOM_ADC      ADC0_CH5_A5                  // 随机数生成ADC通道

/* 
 * ========================================================================
 * 缓冲区配�?
 * ========================================================================
 */
#define U1_RX_BUFFER_SIZE       1024                    // 接收缓冲区大�?
#define U1_MAX_AUDIO_SIZE       8192                    // 最大音频数据长�?
#define U1_AT_BUFFER_SIZE       512                     // AT指令缓冲区大�?
#define U1_RESULT_BUFFER_SIZE   256                     // 结果缓冲区大�?

/* 音频采集配置 */
#define U1_AUDIO_SAMPLE_RATE    8000                    // 音频采样频率(Hz)
#define U1_AUDIO_BUFFER_SIZE    4000                    // 音频缓冲区大小(采样点)
#define U1_AUDIO_SEND_SIZE      4000                    // 单次发送音频数据大小
#define U1_MAX_RECORD_TIME      60                      // 最大录音时长(秒)

/* 
 * ========================================================================
 * 超时配置
 * ========================================================================
 */
#define U1_CONN_TIMEOUT_MS      10000                   // 连接超时(ms)
#define U1_AT_TIMEOUT_MS        5000                    // AT指令超时(ms)
#define U1_RECOGNITION_TIMEOUT_MS 15000                 // 识别超时(ms)
#define U1_RETRY_DELAY_MS       1000                    // 重试间隔(ms)

/* 
 * ========================================================================
 * 功能配置
 * ========================================================================
 */
#define U1_MAX_RETRIES          3                       // 最大重试次�?
#define U1_AUTO_RECONNECT       1                       // 自动重连
#define U1_AUTO_RESET           1                       // 自动重置
#define U1_HARDWARE_WATCHDOG    0                       // 硬件看门�?

/* 
 * ========================================================================
 * 调试配置
 * ========================================================================
 */
#define U1_DEBUG_ENABLE         0                       // 禁用调试输出，避免中文字符处理问题
#define U1_DEBUG_VERBOSE        0                       // 详细调试信息

/* 
 * ========================================================================
 * 条件编译配置
 * ========================================================================
 */
#ifndef U1_FEATURE_ASR
#define U1_FEATURE_ASR          1                       // 语音识别功能
#endif

#ifndef U1_FEATURE_WEBSOCKET
#define U1_FEATURE_WEBSOCKET    1                       // WebSocket功能
#endif

#ifndef U1_FEATURE_AUDIO
#define U1_FEATURE_AUDIO        1                       // 音频采集功能
#endif

#ifndef U1_FEATURE_ERROR_HANDLING
#define U1_FEATURE_ERROR_HANDLING 1                     // 错误处理功能
#endif

/* 
 * ========================================================================
 * 网络配置
 * ========================================================================
 */
#define U1_WEBSOCKET_HOST       "iat-api.xfyun.cn"      // WebSocket服务器
#define U1_WEBSOCKET_PORT       443                     // WebSocket端口
#define U1_WEBSOCKET_PATH       "/v2/iat"               // WebSocket路径
#define U1_MAX_FRAME_SIZE       8192                    // 最大帧大小

#endif /* U1_CONFIG_H */