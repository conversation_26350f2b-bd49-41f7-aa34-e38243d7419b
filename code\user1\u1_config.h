/*
 * u1_config.h - 统一配置文件
 * 作�? BMW_智能车组_Baiyuyu_Xiaohuihui_Wzz
 * 创建时间: 2025�?7�?6�?
 * 描述: 合并所有配置项，统一管理4G模块相关配置
 */

#ifndef U1_CONFIG_H
#define U1_CONFIG_H

/*
 * ========================================================================
 * 讯飞语音识别服务 (ASR) 配置
 * ========================================================================
 */

/* 
 * [!! 重要 !!] 请将此处的值替换为您在讯飞开放平台申请的真实凭证 
 * 官方申请地址: https://www.xfyun.cn/
 */
#define U1_XF_APP_ID       "5d8cb6e1"                    /* 修改此处 -> 讯飞应用 APPID */
#define U1_XF_API_KEY      "b5c6e8f7a9d1e2f3a4b5c6d7e8f9a0b1"  /* 修改此处 -> 讯飞API Key */
#define U1_XF_API_SECRET   "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"  /* 修改此处 -> 讯飞API Secret */

/* 
 * ========================================================================
 * 硬件配置
 * ========================================================================
 */
#define U1_UART_INDEX      UART_3                       // 4G模块串口
#define U1_UART_BAUDRATE   115200                       // 波特�?
#define U1_UART_TX_PIN     UART3_TX_P15_6               // 发送引�?
#define U1_UART_RX_PIN     UART3_RX_P15_7               // 接收引脚

/* 音频采集硬件配置 */
#define U1_AUDIO_ADC       ADC0_CH4_A4                  // 音频ADC通道
#define U1_RECORD_BUTTON   P11_3                        // 录音按键引脚
#define U1_AUDIO_TIMER     CCU60_CH0                    // 音频采样定时器
#define U1_RANDOM_ADC      ADC0_CH5_A5                  // 随机数生成ADC通道

/* 
 * ========================================================================
 * 缓冲区配�?
 * ========================================================================
 */
#define U1_RX_BUFFER_SIZE       2048                    // 接收缓冲区大�?
#define U1_MAX_AUDIO_SIZE       8192                    // 最大音频数据长�?
#define U1_AT_BUFFER_SIZE       512                     // AT指令缓冲区大�?
#define U1_RESULT_BUFFER_SIZE   256                     // 结果缓冲区大�?

/* 音频采集配置 */
#define U1_AUDIO_SAMPLE_RATE    8000                    // 音频采样频率(Hz)
#define U1_AUDIO_BUFFER_SIZE    4000                    // 音频缓冲区大小(采样点)
#define U1_AUDIO_SEND_SIZE      4000                    // 单次发送音频数据大小
#define U1_MAX_RECORD_TIME      60                      // 最大录音时长(秒)

/* 
 * ========================================================================
 * 超时配置
 * ========================================================================
 */
#define U1_CONN_TIMEOUT_MS      10000                   // 连接超时(ms)
#define U1_AT_TIMEOUT_MS        5000                    // AT指令超时(ms)
#define U1_RECOGNITION_TIMEOUT_MS 15000                 // 识别超时(ms)
#define U1_RETRY_DELAY_MS       1000                    // 重试间隔(ms)

/* 
 * ========================================================================
 * 功能配置
 * ========================================================================
 */
#define U1_MAX_RETRIES          3                       // 最大重试次�?
#define U1_AUTO_RECONNECT       1                       // 自动重连
#define U1_AUTO_RESET           1                       // 自动重置
#define U1_HARDWARE_WATCHDOG    0                       // 硬件看门�?

/* 
 * ========================================================================
 * 调试配置
 * ========================================================================
 */
#define U1_DEBUG_ENABLE         0                       // 禁用调试输出，避免中文字符处理问题
#define U1_DEBUG_VERBOSE        0                       // 详细调试信息

/* 
 * ========================================================================
 * 条件编译配置
 * ========================================================================
 */
#ifndef U1_FEATURE_ASR
#define U1_FEATURE_ASR          1                       // 语音识别功能
#endif

#ifndef U1_FEATURE_WEBSOCKET
#define U1_FEATURE_WEBSOCKET    1                       // WebSocket功能
#endif

#ifndef U1_FEATURE_AUDIO
#define U1_FEATURE_AUDIO        1                       // 音频采集功能
#endif

#ifndef U1_FEATURE_ERROR_HANDLING
#define U1_FEATURE_ERROR_HANDLING 1                     // 错误处理功能
#endif

#ifndef U1_FEATURE_VOICE_COMMANDS
#define U1_FEATURE_VOICE_COMMANDS 1                     // 语音命令库功能
#endif

/* 
 * ========================================================================
 * 网络配置
 * ========================================================================
 */
#define U1_WEBSOCKET_HOST       "iat-api.xfyun.cn"      // WebSocket服务器
#define U1_WEBSOCKET_PORT       443                     // WebSocket端口
#define U1_WEBSOCKET_PATH       "/v2/iat"               // WebSocket路径
#define U1_MAX_FRAME_SIZE       8192                    // 最大帧大小

/*
 * ========================================================================
 * 语音命令库配置
 * ========================================================================
 */
#if U1_FEATURE_VOICE_COMMANDS
/* 语音命令数量 */
#define U1_VOICE_COMMAND_COUNT  15                      // 支持的语音命令总数

/* 语音命令库 - 15条指令 */
#define U1_CMD_LIGHT_ON         "打开双灯"              // 命令1
#define U1_CMD_LEFT_LIGHT_ON    "打开左转灯"            // 命令2
#define U1_CMD_RIGHT_LIGHT_ON   "打开右转灯"            // 命令3
#define U1_CMD_DISTANCE_LIGHT_ON "打开远光灯"           // 命令4
#define U1_CMD_NEAR_LIGHT_ON    "打开近光灯"            // 命令5
#define U1_CMD_BRAKE_LIGHT_ON   "打开刹灯"              // 命令6
#define U1_CMD_FORWARD_10M      "向前直行10米"          // 命令7
#define U1_CMD_BACKWARD_10M     "后退直行10米"          // 命令8
#define U1_CMD_SERPENTINE_10M   "蛇形前进10米"          // 命令9
#define U1_CMD_SERPENTINE_BACK_10M "蛇形后退10米"       // 命令10
#define U1_CMD_REVERSE_TURN     "逆时针转一圈"          // 命令11
#define U1_CMD_CLOCKWISE_TURN   "顺时针转一圈"          // 命令12
#define U1_CMD_STOP_AREA_1      "停进停车区1"           // 命令13
#define U1_CMD_STOP_AREA_2      "停进停车区2"           // 命令14
#define U1_CMD_STOP_AREA_3      "停进停车区3"           // 命令15

/* 命令ID枚举 */
typedef enum {
    U1_VOICE_CMD_NONE = 0,                              // 无命令
    U1_VOICE_CMD_LIGHT_ON = 1,                          // 打开双灯
    U1_VOICE_CMD_LEFT_LIGHT_ON = 2,                     // 打开左转灯
    U1_VOICE_CMD_RIGHT_LIGHT_ON = 3,                    // 打开右转灯
    U1_VOICE_CMD_DISTANCE_LIGHT_ON = 4,                 // 打开远光灯
    U1_VOICE_CMD_NEAR_LIGHT_ON = 5,                     // 打开近光灯
    U1_VOICE_CMD_BRAKE_LIGHT_ON = 6,                    // 打开刹灯
    U1_VOICE_CMD_FORWARD_10M = 7,                       // 向前直行10米
    U1_VOICE_CMD_BACKWARD_10M = 8,                      // 后退直行10米
    U1_VOICE_CMD_SERPENTINE_10M = 9,                    // 蛇形前进10米
    U1_VOICE_CMD_SERPENTINE_BACK_10M = 10,              // 蛇形后退10米
    U1_VOICE_CMD_REVERSE_TURN = 11,                     // 逆时针转一圈
    U1_VOICE_CMD_CLOCKWISE_TURN = 12,                   // 顺时针转一圈
    U1_VOICE_CMD_STOP_AREA_1 = 13,                      // 停进停车区1
    U1_VOICE_CMD_STOP_AREA_2 = 14,                      // 停进停车区2
    U1_VOICE_CMD_STOP_AREA_3 = 15                       // 停进停车区3
} u1_voice_command_t;
#endif

#endif /* U1_CONFIG_H */