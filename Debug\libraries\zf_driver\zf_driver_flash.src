	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc32920a --dep-file=zf_driver_flash.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_flash.src ../libraries/zf_driver/zf_driver_flash.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_flash.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_flash.flash_check',code,cluster('flash_check')
	.sect	'.text.zf_driver_flash.flash_check'
	.align	2
	
	.global	flash_check
; Function flash_check
.L42:
flash_check:	.type	func
	mov	d15,d5
.L216:
	lt.u	d4,d15,#12
.L215:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#58
.L214:
	call	debug_assert_handler
.L103:
	mul	d15,d15,#8
.L217:
	movh.a	a15,#@his(IfxFlash_dFlashTableEepLog)
	lea	a15,[a15]@los(IfxFlash_dFlashTableEepLog)
.L285:
	addsc.a	a15,a15,d15,#0
.L286:
	ld.w	d0,[a15]
.L218:
	mov	d1,#0
.L219:
	j	.L2
.L3:
	add	d1,#1
.L2:
	mov	d15,#1024
.L287:
	jge.u	d1,d15,.L4
.L288:
	mov	d15,#8
.L289:
	madd	d15,d0,d1,d15
.L290:
	mov.a	a15,d15
.L291:
	ld.w	d15,[a15]
.L292:
	jeq	d15,#0,.L3
.L4:
	mov	d15,#1024
.L293:
	jne	d15,d1,.L5
.L294:
	mov	d2,#0
.L295:
	j	.L6
.L5:
	mov	d2,#1
.L6:
	j	.L7
.L7:
	ret
.L99:
	
__flash_check_function_end:
	.size	flash_check,__flash_check_function_end-flash_check
.L63:
	; End of function
	
	.sdecl	'.text.zf_driver_flash.flash_erase_page',code,cluster('flash_erase_page')
	.sect	'.text.zf_driver_flash.flash_erase_page'
	.align	2
	
	.global	flash_erase_page
; Function flash_erase_page
.L44:
flash_erase_page:	.type	func
	mov	d8,d5
.L222:
	lt.u	d4,d8,#12
.L221:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#79
.L220:
	call	debug_assert_handler
.L109:
	mov	d10,#0
.L224:
	mul	d15,d8,#8
.L300:
	movh.a	a15,#@his(IfxFlash_dFlashTableEepLog)
	lea	a15,[a15]@los(IfxFlash_dFlashTableEepLog)
.L301:
	addsc.a	a15,a15,d15,#0
.L302:
	ld.w	d8,[a15]
.L223:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L225:
	mov	d9,d2
.L227:
	mov	d4,d9
.L226:
	call	IfxScuWdt_clearSafetyEndinit
.L115:
	movh.a	a15,#44801
.L228:
	lea	a15,[a15]@los(0xaf00aa50)
.L303:
	movh.a	a2,#44801
.L229:
	lea	a2,[a2]@los(0xaf00aa58)
.L304:
	movh.a	a4,#44801
.L231:
	lea	a4,[a4]@los(0xaf00aaa8)
.L305:
	movh.a	a5,#44801
.L232:
	lea	a5,[a5]@los(0xaf00aaa8)
.L306:
	st.w	[a15],d8
.L307:
	mov	d0,#1
.L308:
	st.w	[a2],d0
.L309:
	mov	d15,#128
.L310:
	st.w	[a4],d15
.L311:
	mov	d15,#80
.L312:
	st.w	[a5],d15
.L313:
	dsync
.L116:
	mov	d4,d9
.L233:
	call	IfxScuWdt_setSafetyEndinit
.L230:
	mov	d0,#1
.L126:
	jne	d10,#0,.L8
.L314:
	j	.L9
.L10:
.L9:
	movh.a	a15,#63488
	ld.w	d15,[a15]@los(0xf8002010)
.L315:
	mov	d1,#1
.L316:
	sha	d1,d1,d0
.L317:
	and	d15,d1
.L318:
	jne	d15,#0,.L10
.L319:
	j	.L11
.L8:
	j	.L12
.L11:
	dsync
.L320:
	j	.L13
.L13:
.L12:
	movh.a	a15,#@his(flash_erase_page_flag)
	lea	a15,[a15]@los(flash_erase_page_flag)
.L321:
	mov	d15,#1
.L322:
	st.b	[a15],d15
.L323:
	ret
.L106:
	
__flash_erase_page_function_end:
	.size	flash_erase_page,__flash_erase_page_function_end-flash_erase_page
.L68:
	; End of function
	
	.sdecl	'.text.zf_driver_flash.flash_read_page',code,cluster('flash_read_page')
	.sect	'.text.zf_driver_flash.flash_read_page'
	.align	2
	
	.global	flash_read_page
; Function flash_read_page
.L46:
flash_read_page:	.type	func
	mov	d8,d5
.L238:
	mov.aa	a15,a4
.L239:
	mov	d9,d6
.L240:
	lt.u	d4,d8,#12
.L237:
	movh.a	a4,#@his(.1.str)
.L234:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#109
.L236:
	call	debug_assert_handler
.L235:
	mov	d15,#1024
	ge	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#110
	call	debug_assert_handler
.L328:
	mov	d0,#0
.L241:
	j	.L14
.L15:
	mov	d15,#8
.L329:
	mov	d1,#8192
.L330:
	movh	d2,#44800
.L331:
	madd	d1,d2,d8,d1
.L332:
	madd	d15,d1,d0,d15
.L333:
	mov.a	a2,d15
.L334:
	ld.w	d15,[a2]
.L335:
	st.w	[a15],d15
.L336:
	add.a	a15,#4
.L337:
	add	d0,#1
.L14:
	jlt.u	d0,d9,.L15
.L338:
	ret
.L132:
	
__flash_read_page_function_end:
	.size	flash_read_page,__flash_read_page_function_end-flash_read_page
.L73:
	; End of function
	
	.sdecl	'.text.zf_driver_flash.flash_write_page',code,cluster('flash_write_page')
	.sect	'.text.zf_driver_flash.flash_write_page'
	.align	2
	
	.global	flash_write_page
; Function flash_write_page
.L48:
flash_write_page:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L246:
	mov	d10,d6
.L247:
	lt.u	d4,d9,#12
.L245:
	movh.a	a4,#@his(.1.str)
.L242:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#130
.L244:
	call	debug_assert_handler
.L243:
	mov	d15,#1024
	ge	d4,d15,d10
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#131
	call	debug_assert_handler
.L145:
	mul	d15,d9,#8
.L248:
	movh.a	a15,#@his(IfxFlash_dFlashTableEepLog)
	lea	a15,[a15]@los(IfxFlash_dFlashTableEepLog)
.L343:
	addsc.a	a15,a15,d15,#0
.L344:
	ld.w	d11,[a15]
.L249:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L250:
	mov	d12,d2
.L252:
	movh.a	a15,#@his(flash_erase_page_flag)
	lea	a15,[a15]@los(flash_erase_page_flag)
	ld.bu	d15,[a15]
	jne	d15,#0,.L16
.L253:
	mov	e4,d9,d8
.L254:
	call	flash_check
.L251:
	jeq	d2,#0,.L17
.L255:
	mov	e4,d9,d8
.L256:
	call	flash_erase_page
.L17:
.L16:
	mov	d8,#0
.L257:
	j	.L18
.L19:
	mov	d15,#8
.L345:
	madd	d9,d11,d8,d15
.L151:
	movh.a	a15,#44800
.L258:
	lea	a15,[a15]@los(0xaf005554)
.L346:
	insert	d0,d9,#0,#0,#24
.L347:
	movh	d15,#40960
.L348:
	jne	d15,d0,.L20
.L349:
	mov	d15,#80
.L350:
	st.w	[a15],d15
.L351:
	mov	d15,#0
	j	.L21
.L20:
	insert	d0,d9,#0,#0,#24
.L352:
	movh	d15,#44800
.L353:
	jne	d15,d0,.L22
.L354:
	mov	d15,#93
.L355:
	st.w	[a15],d15
.L356:
	mov	d15,#0
	j	.L23
.L22:
	dsync
.L357:
	mov	d15,#1
	j	.L24
.L24:
.L23:
.L21:
	eq	d4,d15,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#148
	call	debug_assert_handler
.L358:
	mov	d15,#0
.L359:
	mov	d0,#1
.L156:
	jne	d15,#0,.L25
.L360:
	j	.L26
.L27:
.L26:
	movh.a	a15,#63488
	ld.w	d15,[a15]@los(0xf8002010)
.L361:
	mov	d1,#1
.L362:
	sha	d1,d1,d0
.L363:
	and	d15,d1
.L364:
	jne	d15,#0,.L27
.L365:
	j	.L28
.L25:
	j	.L29
.L28:
	dsync
.L366:
	j	.L30
.L30:
.L29:
	ld.w	d15,[a12]
.L367:
	add.a	a12,#4
.L368:
	mov	d1,#0
.L160:
	jeq	d9,#0,.L31
.L31:
	movh.a	a15,#44800
.L259:
	lea	a15,[a15]@los(0xaf0055f0)
.L173:
	mov	d0,#0
.L369:
	mov	d3,#0
	mov	d2,d15
.L370:
	or	d0,d2
.L261:
	or	d1,d3
.L185:
	st.d	[a15]0,e0
.L174:
	dsync
.L161:
	mov	d4,d12
.L262:
	call	IfxScuWdt_clearSafetyEndinit
.L192:
	movh.a	a15,#44801
.L260:
	lea	a15,[a15]@los(0xaf00aa50)
.L371:
	movh.a	a2,#44801
.L263:
	lea	a2,[a2]@los(0xaf00aa58)
.L372:
	movh.a	a4,#44801
.L265:
	lea	a4,[a4]@los(0xaf00aaa8)
.L373:
	movh.a	a5,#44801
.L266:
	lea	a5,[a5]@los(0xaf00aaa8)
.L374:
	st.w	[a15],d9
.L375:
	mov	d15,#0
.L376:
	st.w	[a2],d15
.L377:
	mov	d15,#160
.L378:
	st.w	[a4],d15
.L379:
	mov	d15,#170
.L380:
	st.w	[a5],d15
.L381:
	dsync
.L193:
	mov	d4,d12
.L267:
	call	IfxScuWdt_setSafetyEndinit
.L264:
	mov	d15,#0
.L382:
	mov	d0,#1
.L201:
	jne	d15,#0,.L32
.L383:
	j	.L33
.L34:
.L33:
	movh.a	a15,#63488
	ld.w	d15,[a15]@los(0xf8002010)
.L384:
	mov	d1,#1
.L385:
	sha	d1,d1,d0
.L386:
	and	d15,d1
.L387:
	jne	d15,#0,.L34
.L388:
	j	.L35
.L32:
	j	.L36
.L35:
	dsync
.L389:
	j	.L37
.L37:
.L36:
	add	d8,#1
.L18:
	jlt.u	d8,d10,.L19
.L390:
	movh.a	a15,#@his(flash_erase_page_flag)
	lea	a15,[a15]@los(flash_erase_page_flag)
.L391:
	mov	d15,#0
.L392:
	st.b	[a15],d15
.L393:
	ret
.L139:
	
__flash_write_page_function_end:
	.size	flash_write_page,__flash_write_page_function_end-flash_write_page
.L78:
	; End of function
	
	.sdecl	'.text.zf_driver_flash.flash_read_page_to_buffer',code,cluster('flash_read_page_to_buffer')
	.sect	'.text.zf_driver_flash.flash_read_page_to_buffer'
	.align	2
	
	.global	flash_read_page_to_buffer
; Function flash_read_page_to_buffer
.L50:
flash_read_page_to_buffer:	.type	func
	mov	d15,d5
.L270:
	lt.u	d4,d15,#12
.L269:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#175
.L268:
	call	debug_assert_handler
.L206:
	mul	d15,d15,#8
.L271:
	movh.a	a15,#@his(IfxFlash_dFlashTableEepLog)
	lea	a15,[a15]@los(IfxFlash_dFlashTableEepLog)
.L398:
	addsc.a	a15,a15,d15,#0
.L399:
	ld.w	d0,[a15]
.L272:
	mov	d1,#0
.L273:
	j	.L38
.L39:
	mul	d15,d1,#4
.L400:
	movh.a	a15,#@his(flash_union_buffer)
	lea	a15,[a15]@los(flash_union_buffer)
.L401:
	addsc.a	a15,a15,d15,#0
.L402:
	mov	d15,#8
.L403:
	madd	d15,d0,d1,d15
.L404:
	mov.a	a2,d15
.L405:
	ld.w	d15,[a2]
.L406:
	st.w	[a15],d15
.L407:
	add	d1,#1
.L38:
	mov	d15,#1024
.L408:
	jlt.u	d1,d15,.L39
.L409:
	ret
.L202:
	
__flash_read_page_to_buffer_function_end:
	.size	flash_read_page_to_buffer,__flash_read_page_to_buffer_function_end-flash_read_page_to_buffer
.L83:
	; End of function
	
	.sdecl	'.text.zf_driver_flash.flash_write_page_from_buffer',code,cluster('flash_write_page_from_buffer')
	.sect	'.text.zf_driver_flash.flash_write_page_from_buffer'
	.align	2
	
	.global	flash_write_page_from_buffer
; Function flash_write_page_from_buffer
.L52:
flash_write_page_from_buffer:	.type	func
	movh.a	a4,#@his(flash_union_buffer)
.L276:
	lea	a4,[a4]@los(flash_union_buffer)
.L414:
	mov	d4,#0
.L275:
	mov	d6,#1024
	call	flash_write_page
.L274:
	mov	d2,#0
.L415:
	j	.L40
.L40:
	ret
.L208:
	
__flash_write_page_from_buffer_function_end:
	.size	flash_write_page_from_buffer,__flash_write_page_from_buffer_function_end-flash_write_page_from_buffer
.L88:
	; End of function
	
	.sdecl	'.text.zf_driver_flash.flash_buffer_clear',code,cluster('flash_buffer_clear')
	.sect	'.text.zf_driver_flash.flash_buffer_clear'
	.align	2
	
	.global	flash_buffer_clear
; Function flash_buffer_clear
.L54:
flash_buffer_clear:	.type	func
	movh.a	a4,#@his(flash_union_buffer)
	lea	a4,[a4]@los(flash_union_buffer)
.L420:
	mov	d4,#255
.L421:
	mov	d5,#1024
	call	memset
.L422:
	ret
.L212:
	
__flash_buffer_clear_function_end:
	.size	flash_buffer_clear,__flash_buffer_clear_function_end-flash_buffer_clear
.L93:
	; End of function
	
	.sdecl	'.bss.zf_driver_flash.flash_union_buffer',data,cluster('flash_union_buffer')
	.sect	'.bss.zf_driver_flash.flash_union_buffer'
	.global	flash_union_buffer
	.align	2
flash_union_buffer:	.type	object
	.size	flash_union_buffer,4096
	.space	4096
	.sdecl	'.bss.zf_driver_flash.flash_erase_page_flag',data,cluster('flash_erase_page_flag')
	.sect	'.bss.zf_driver_flash.flash_erase_page_flag'
flash_erase_page_flag:	.type	object
	.size	flash_erase_page_flag,1
	.space	1
	.sdecl	'.rodata.zf_driver_flash..1.str',data,rom
	.sect	'.rodata.zf_driver_flash..1.str'
.1.str:	.type	object
	.size	.1.str,41
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,102,108,97,115,104,46,99
	.space	1
	.calls	'flash_check','debug_assert_handler'
	.calls	'flash_erase_page','debug_assert_handler'
	.calls	'flash_erase_page','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'flash_erase_page','IfxScuWdt_clearSafetyEndinit'
	.calls	'flash_erase_page','IfxScuWdt_setSafetyEndinit'
	.calls	'flash_read_page','debug_assert_handler'
	.calls	'flash_write_page','debug_assert_handler'
	.calls	'flash_write_page','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'flash_write_page','flash_check'
	.calls	'flash_write_page','flash_erase_page'
	.calls	'flash_write_page','IfxScuWdt_clearSafetyEndinit'
	.calls	'flash_write_page','IfxScuWdt_setSafetyEndinit'
	.calls	'flash_read_page_to_buffer','debug_assert_handler'
	.calls	'flash_write_page_from_buffer','flash_write_page'
	.calls	'flash_buffer_clear','memset'
	.calls	'flash_check','',0
	.calls	'flash_erase_page','',0
	.calls	'flash_read_page','',0
	.calls	'flash_write_page','',0
	.calls	'flash_read_page_to_buffer','',0
	.calls	'flash_write_page_from_buffer','',0
	.extern	IfxScuWdt_clearSafetyEndinit
	.extern	IfxScuWdt_setSafetyEndinit
	.extern	IfxScuWdt_getSafetyWatchdogPassword
	.extern	IfxFlash_dFlashTableEepLog
	.extern	memset
	.extern	debug_assert_handler
	.calls	'flash_buffer_clear','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L56:
	.word	37941
	.half	3
	.word	.L57
	.byte	4
.L55:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L58
	.byte	2,1,1,3
	.word	203
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	206
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	251
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	263
	.byte	6,0
.L182:
	.byte	7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	343
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	317
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	349
	.byte	6,0
.L184:
	.byte	4
	.byte	'__st64',0,3,2,143,1,17,1,1
.L186:
	.byte	5
	.byte	'addr',0,2,143,1,30
	.word	349
.L188:
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	317
.L190:
	.byte	6,0
.L100:
	.byte	7
	.byte	'unsigned long int',0,4,7
.L172:
	.byte	4
	.byte	'__st64_lu',0,3,2,162,1,17,1,1
.L175:
	.byte	5
	.byte	'addr',0,2,162,1,33
	.word	349
.L177:
	.byte	5
	.byte	'valueLower',0,2,162,1,46
	.word	435
.L179:
	.byte	5
	.byte	'valueUpper',0,2,162,1,65
	.word	435
.L181:
	.byte	10,6,0,0
.L98:
	.byte	7
	.byte	'unsigned char',0,1,8
.L150:
	.byte	8
	.byte	'IfxFlash_enterPageMode',0,3,3,129,4,18
	.word	532
	.byte	1,1
.L152:
	.byte	5
	.byte	'pageAddr',0,3,129,4,48
	.word	435
.L154:
	.byte	6,0
.L114:
	.byte	4
	.byte	'IfxFlash_eraseSector',0,3,3,197,4,17,1,1
.L117:
	.byte	5
	.byte	'sectorAddr',0,3,197,4,45
	.word	435
.L119:
	.byte	6,0
.L159:
	.byte	4
	.byte	'IfxFlash_loadPage',0,3,3,245,4,17,1,1
.L162:
	.byte	5
	.byte	'pageAddr',0,3,245,4,42
	.word	435
.L164:
	.byte	5
	.byte	'wordL',0,3,245,4,59
	.word	435
.L166:
	.byte	5
	.byte	'wordU',0,3,245,4,73
	.word	435
.L168:
	.byte	10
.L169:
	.byte	6,6,0,0,11,4,150,1,9,1,12
	.byte	'IfxFlash_FlashType_Fa',0,0,12
	.byte	'IfxFlash_FlashType_D0',0,1,12
	.byte	'IfxFlash_FlashType_D1',0,2,12
	.byte	'IfxFlash_FlashType_P0',0,3,12
	.byte	'IfxFlash_FlashType_P1',0,4,12
	.byte	'IfxFlash_FlashType_P2',0,5,12
	.byte	'IfxFlash_FlashType_P3',0,6,0
.L125:
	.byte	8
	.byte	'IfxFlash_waitUnbusy',0,3,3,196,5,18
	.word	532
	.byte	1,1
.L127:
	.byte	5
	.byte	'flash',0,3,196,5,45
	.word	435
.L129:
	.byte	5
	.byte	'flashType',0,3,196,5,71
	.word	734
.L131:
	.byte	6,0
.L191:
	.byte	4
	.byte	'IfxFlash_writePage',0,3,3,236,5,17,1,1
.L194:
	.byte	5
	.byte	'pageAddr',0,3,236,5,43
	.word	435
.L196:
	.byte	6,0,13
	.byte	'__dsync',0,1,1,1,1,14
	.word	211
	.byte	15
	.word	237
	.byte	6,0,14
	.word	272
	.byte	15
	.word	304
	.byte	6,0,14
	.word	354
	.byte	15
	.word	373
	.byte	6,0,14
	.word	389
	.byte	15
	.word	404
	.byte	15
	.word	418
	.byte	6,0,14
	.word	456
	.byte	15
	.word	474
	.byte	15
	.word	488
	.byte	15
	.word	508
	.byte	10,16
	.word	389
	.byte	15
	.word	404
	.byte	15
	.word	418
	.byte	17
	.word	433
	.byte	0,6,0,0
.L111:
	.byte	7
	.byte	'unsigned short int',0,2,7,18
	.byte	'IfxScuWdt_clearSafetyEndinit',0,5,229,1,17,1,1,1,1,5
	.byte	'password',0,5,229,1,53
	.word	1135
	.byte	0,18
	.byte	'IfxScuWdt_setSafetyEndinit',0,5,249,1,17,1,1,1,1,5
	.byte	'password',0,5,249,1,51
	.word	1135
	.byte	0,19
	.byte	'IfxScuWdt_getSafetyWatchdogPassword',0,5,143,3,19
	.word	1135
	.byte	1,1,1,1,14
	.word	549
	.byte	15
	.word	584
	.byte	6,0,14
	.word	604
	.byte	15
	.word	633
	.byte	6,0,14
	.word	655
	.byte	15
	.word	681
	.byte	15
	.word	699
	.byte	15
	.word	714
	.byte	10,10,16
	.word	456
	.byte	15
	.word	474
	.byte	15
	.word	488
	.byte	15
	.word	508
	.byte	20
	.word	528
	.byte	16
	.word	389
	.byte	15
	.word	404
	.byte	15
	.word	418
	.byte	17
	.word	433
	.byte	0,17
	.word	529
	.byte	0,0,0,6,0,0,14
	.word	909
	.byte	15
	.word	941
	.byte	15
	.word	956
	.byte	6,0,14
	.word	977
	.byte	15
	.word	1004
	.byte	6,0,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,21
	.byte	'memset',0,6,56,17
	.word	349
	.byte	1,1,1,1,22,6,56,33
	.word	349
	.byte	22,6,56,36
	.word	1450
	.byte	22,6,56,41
	.word	1457
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	1517
	.byte	18
	.byte	'debug_assert_handler',0,7,112,9,1,1,1,1,5
	.byte	'pass',0,7,112,47
	.word	532
	.byte	5
	.byte	'file',0,7,112,59
	.word	1525
	.byte	5
	.byte	'line',0,7,112,69
	.word	1450
	.byte	0,23
	.word	435
.L120:
	.byte	3
	.word	1599
.L135:
	.byte	3
	.word	435
	.byte	24
	.word	435
.L142:
	.byte	3
	.word	1614
.L170:
	.byte	3
	.word	317
	.byte	7
	.byte	'short int',0,2,5,25
	.byte	'__wchar_t',0,8,1,1
	.word	1629
	.byte	25
	.byte	'__size_t',0,8,1,1
	.word	1457
	.byte	25
	.byte	'__ptrdiff_t',0,8,1,1
	.word	1450
	.byte	26,1,3
	.word	1697
	.byte	25
	.byte	'__codeptr',0,8,1,1
	.word	1699
	.byte	25
	.byte	'__intptr_t',0,8,1,1
	.word	1450
	.byte	25
	.byte	'__uintptr_t',0,8,1,1
	.word	1457
	.byte	11,9,250,10,9,1,12
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,12
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,12
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,25
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	1761
	.byte	25
	.byte	'size_t',0,10,24,25
	.word	1457
	.byte	25
	.byte	'boolean',0,11,101,29
	.word	532
	.byte	25
	.byte	'uint8',0,11,105,29
	.word	532
	.byte	25
	.byte	'uint16',0,11,109,29
	.word	1135
	.byte	25
	.byte	'uint32',0,11,113,29
	.word	435
	.byte	25
	.byte	'uint64',0,11,118,29
	.word	317
	.byte	25
	.byte	'sint16',0,11,126,29
	.word	1629
	.byte	7
	.byte	'long int',0,4,5,25
	.byte	'sint32',0,11,131,1,29
	.word	1988
	.byte	7
	.byte	'long long int',0,8,5,25
	.byte	'sint64',0,11,138,1,29
	.word	2016
	.byte	25
	.byte	'float32',0,11,167,1,29
	.word	263
	.byte	25
	.byte	'pvoid',0,12,57,28
	.word	349
	.byte	25
	.byte	'Ifx_TickTime',0,12,79,28
	.word	2016
	.byte	27
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,13,45,16,4,28
	.byte	'EN0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'EN1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'EN2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'EN3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'EN4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'EN5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'EN6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'EN7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'EN8',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'EN9',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'EN10',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'EN11',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'EN12',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'EN13',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'EN14',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'EN15',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'EN16',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'EN17',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'EN18',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'EN19',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'EN20',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'EN21',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'EN22',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'EN23',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'EN24',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'EN25',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'EN26',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'EN27',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'EN28',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'EN29',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'EN30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'EN31',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_ACCEN0_Bits',0,13,79,3
	.word	2101
	.byte	27
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,13,82,16,4,28
	.byte	'reserved_0',0,4
	.word	1457
	.byte	32,0,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1_Bits',0,13,85,3
	.word	2658
	.byte	27
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,13,88,16,4,28
	.byte	'STM0DIS',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'STM1DIS',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'STM2DIS',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'reserved_3',0,4
	.word	1457
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,13,94,3
	.word	2735
	.byte	27
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,13,97,16,4,28
	.byte	'BAUD1DIV',0,1
	.word	532
	.byte	4,4,2,35,0,28
	.byte	'BAUD2DIV',0,1
	.word	532
	.byte	4,0,2,35,0,28
	.byte	'SRIDIV',0,1
	.word	532
	.byte	4,4,2,35,1,28
	.byte	'LPDIV',0,1
	.word	532
	.byte	4,0,2,35,1,28
	.byte	'SPBDIV',0,1
	.word	532
	.byte	4,4,2,35,2,28
	.byte	'FSI2DIV',0,1
	.word	532
	.byte	2,2,2,35,2,28
	.byte	'reserved_22',0,1
	.word	532
	.byte	2,0,2,35,2,28
	.byte	'FSIDIV',0,1
	.word	532
	.byte	2,6,2,35,3,28
	.byte	'reserved_26',0,1
	.word	532
	.byte	2,4,2,35,3,28
	.byte	'CLKSEL',0,1
	.word	532
	.byte	2,2,2,35,3,28
	.byte	'UP',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON0_Bits',0,13,111,3
	.word	2871
	.byte	27
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,13,114,16,4,28
	.byte	'CANDIV',0,1
	.word	532
	.byte	4,4,2,35,0,28
	.byte	'ERAYDIV',0,1
	.word	532
	.byte	4,0,2,35,0,28
	.byte	'STMDIV',0,1
	.word	532
	.byte	4,4,2,35,1,28
	.byte	'GTMDIV',0,1
	.word	532
	.byte	4,0,2,35,1,28
	.byte	'ETHDIV',0,1
	.word	532
	.byte	4,4,2,35,2,28
	.byte	'ASCLINFDIV',0,1
	.word	532
	.byte	4,0,2,35,2,28
	.byte	'ASCLINSDIV',0,1
	.word	532
	.byte	4,4,2,35,3,28
	.byte	'INSEL',0,1
	.word	532
	.byte	2,2,2,35,3,28
	.byte	'UP',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON1_Bits',0,13,126,3
	.word	3151
	.byte	27
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,13,129,1,16,4,28
	.byte	'BBBDIV',0,1
	.word	532
	.byte	4,4,2,35,0,28
	.byte	'reserved_4',0,4
	.word	1457
	.byte	26,2,2,35,0,28
	.byte	'UP',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON2_Bits',0,13,135,1,3
	.word	3389
	.byte	27
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,13,138,1,16,4,28
	.byte	'PLLDIV',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'PLLSEL',0,1
	.word	532
	.byte	2,0,2,35,0,28
	.byte	'PLLERAYDIV',0,1
	.word	532
	.byte	6,2,2,35,1,28
	.byte	'PLLERAYSEL',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'SRIDIV',0,1
	.word	532
	.byte	6,2,2,35,2,28
	.byte	'SRISEL',0,1
	.word	532
	.byte	2,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	5,3,2,35,3,28
	.byte	'SLCK',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'UP',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON3_Bits',0,13,150,1,3
	.word	3517
	.byte	27
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,13,153,1,16,4,28
	.byte	'SPBDIV',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'SPBSEL',0,1
	.word	532
	.byte	2,0,2,35,0,28
	.byte	'GTMDIV',0,1
	.word	532
	.byte	6,2,2,35,1,28
	.byte	'GTMSEL',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'STMDIV',0,1
	.word	532
	.byte	6,2,2,35,2,28
	.byte	'STMSEL',0,1
	.word	532
	.byte	2,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	5,3,2,35,3,28
	.byte	'SLCK',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'UP',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON4_Bits',0,13,165,1,3
	.word	3760
	.byte	27
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,13,168,1,16,4,28
	.byte	'MAXDIV',0,1
	.word	532
	.byte	4,4,2,35,0,28
	.byte	'reserved_4',0,4
	.word	1457
	.byte	26,2,2,35,0,28
	.byte	'UP',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON5_Bits',0,13,174,1,3
	.word	3995
	.byte	27
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,13,177,1,16,4,28
	.byte	'CPU0DIV',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'reserved_6',0,4
	.word	1457
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6_Bits',0,13,181,1,3
	.word	4123
	.byte	27
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,13,184,1,16,4,28
	.byte	'CPU1DIV',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'reserved_6',0,4
	.word	1457
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7_Bits',0,13,188,1,3
	.word	4223
	.byte	27
	.byte	'_Ifx_SCU_CHIPID_Bits',0,13,191,1,16,4,28
	.byte	'CHREV',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'CHTEC',0,1
	.word	532
	.byte	2,0,2,35,0,28
	.byte	'CHID',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'EEA',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'UCODE',0,1
	.word	532
	.byte	7,0,2,35,2,28
	.byte	'FSIZE',0,1
	.word	532
	.byte	4,4,2,35,3,28
	.byte	'SP',0,1
	.word	532
	.byte	2,2,2,35,3,28
	.byte	'SEC',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'reserved_31',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CHIPID_Bits',0,13,202,1,3
	.word	4323
	.byte	27
	.byte	'_Ifx_SCU_DTSCON_Bits',0,13,205,1,16,4,28
	.byte	'PWD',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'START',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	2,4,2,35,0,28
	.byte	'CAL',0,4
	.word	1457
	.byte	20,8,2,35,0,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'SLCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSCON_Bits',0,13,213,1,3
	.word	4531
	.byte	27
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,13,216,1,16,4,28
	.byte	'LOWER',0,2
	.word	1135
	.byte	10,6,2,35,0,28
	.byte	'reserved_10',0,1
	.word	532
	.byte	5,1,2,35,1,28
	.byte	'LLU',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'UPPER',0,2
	.word	1135
	.byte	10,6,2,35,2,28
	.byte	'reserved_26',0,1
	.word	532
	.byte	4,2,2,35,3,28
	.byte	'SLCK',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'UOF',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSLIM_Bits',0,13,225,1,3
	.word	4696
	.byte	27
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,13,228,1,16,4,28
	.byte	'RESULT',0,2
	.word	1135
	.byte	10,6,2,35,0,28
	.byte	'reserved_10',0,1
	.word	532
	.byte	4,2,2,35,1,28
	.byte	'RDY',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'BUSY',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,13,235,1,3
	.word	4879
	.byte	27
	.byte	'_Ifx_SCU_EICR_Bits',0,13,238,1,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	4,4,2,35,0,28
	.byte	'EXIS0',0,1
	.word	532
	.byte	3,1,2,35,0,28
	.byte	'reserved_7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'FEN0',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'REN0',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'LDEN0',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'EIEN0',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'INP0',0,1
	.word	532
	.byte	3,1,2,35,1,28
	.byte	'reserved_15',0,4
	.word	1457
	.byte	5,12,2,35,0,28
	.byte	'EXIS1',0,1
	.word	532
	.byte	3,1,2,35,2,28
	.byte	'reserved_23',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'FEN1',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'REN1',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'LDEN1',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'EIEN1',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'INP1',0,1
	.word	532
	.byte	3,1,2,35,3,28
	.byte	'reserved_31',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EICR_Bits',0,13,129,2,3
	.word	5033
	.byte	27
	.byte	'_Ifx_SCU_EIFR_Bits',0,13,132,2,16,4,28
	.byte	'INTF0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'INTF1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'INTF2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'INTF3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'INTF4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'INTF5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'INTF6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'INTF7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,4
	.word	1457
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR_Bits',0,13,143,2,3
	.word	5397
	.byte	27
	.byte	'_Ifx_SCU_EMSR_Bits',0,13,146,2,16,4,28
	.byte	'POL',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'MODE',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'ENON',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'PSEL',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,2
	.word	1135
	.byte	12,0,2,35,0,28
	.byte	'EMSF',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'SEMSF',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'reserved_18',0,1
	.word	532
	.byte	6,0,2,35,2,28
	.byte	'EMSFM',0,1
	.word	532
	.byte	2,6,2,35,3,28
	.byte	'SEMSFM',0,1
	.word	532
	.byte	2,4,2,35,3,28
	.byte	'reserved_28',0,1
	.word	532
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_EMSR_Bits',0,13,159,2,3
	.word	5608
	.byte	27
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,13,162,2,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	7,1,2,35,0,28
	.byte	'EDCON',0,2
	.word	1135
	.byte	2,7,2,35,0,28
	.byte	'reserved_9',0,4
	.word	1457
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG_Bits',0,13,167,2,3
	.word	5860
	.byte	27
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,13,170,2,16,4,28
	.byte	'ARI',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'ARC',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,4
	.word	1457
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG_Bits',0,13,175,2,3
	.word	5978
	.byte	27
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,13,178,2,16,4,28
	.byte	'reserved_0',0,4
	.word	1457
	.byte	28,4,2,35,0,28
	.byte	'EVR13OFF',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'BPEVR13OFF',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'reserved_30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR13CON_Bits',0,13,185,2,3
	.word	6089
	.byte	27
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,13,188,2,16,4,28
	.byte	'reserved_0',0,4
	.word	1457
	.byte	28,4,2,35,0,28
	.byte	'EVR33OFF',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'BPEVR33OFF',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'reserved_30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR33CON_Bits',0,13,195,2,3
	.word	6252
	.byte	27
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,13,198,2,16,4,28
	.byte	'ADC13V',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'ADC33V',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'ADCSWDV',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'VAL',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,13,205,2,3
	.word	6415
	.byte	27
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,13,208,2,16,4,28
	.byte	'DVS13TRIM',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'reserved_8',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'DVS33TRIM',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'VAL',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,13,215,2,3
	.word	6573
	.byte	27
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,13,218,2,16,4,28
	.byte	'EVR13OVMOD',0,1
	.word	532
	.byte	2,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	2,4,2,35,0,28
	.byte	'EVR13UVMOD',0,1
	.word	532
	.byte	2,2,2,35,0,28
	.byte	'reserved_6',0,1
	.word	532
	.byte	2,0,2,35,0,28
	.byte	'EVR33OVMOD',0,1
	.word	532
	.byte	2,6,2,35,1,28
	.byte	'reserved_10',0,1
	.word	532
	.byte	2,4,2,35,1,28
	.byte	'EVR33UVMOD',0,1
	.word	532
	.byte	2,2,2,35,1,28
	.byte	'reserved_14',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'SWDOVMOD',0,1
	.word	532
	.byte	2,6,2,35,2,28
	.byte	'reserved_18',0,1
	.word	532
	.byte	2,4,2,35,2,28
	.byte	'SWDUVMOD',0,1
	.word	532
	.byte	2,2,2,35,2,28
	.byte	'reserved_22',0,2
	.word	1135
	.byte	10,0,2,35,2,0,25
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,13,232,2,3
	.word	6738
	.byte	27
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,13,235,2,16,4,28
	.byte	'OSCTRIM',0,2
	.word	1135
	.byte	10,6,2,35,0,28
	.byte	'OSCPTAT',0,1
	.word	532
	.byte	6,0,2,35,1,28
	.byte	'OSCANASEL',0,1
	.word	532
	.byte	4,4,2,35,2,28
	.byte	'HPBGTRIM',0,2
	.word	1135
	.byte	7,5,2,35,2,28
	.byte	'HPBGCLKEN',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'OSC3V3',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'reserved_29',0,1
	.word	532
	.byte	2,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,13,245,2,3
	.word	7067
	.byte	27
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,13,248,2,16,4,28
	.byte	'EVR13OVVAL',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'EVR33OVVAL',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'SWDOVVAL',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROVMON_Bits',0,13,255,2,3
	.word	7288
	.byte	27
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,13,130,3,16,4,28
	.byte	'RST13TRIM',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'reserved_8',0,4
	.word	1457
	.byte	16,8,2,35,0,28
	.byte	'RST13OFF',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'BPRST13OFF',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'RST33OFF',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'BPRST33OFF',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'RSTSWDOFF',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'BPRSTSWDOFF',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'reserved_30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,13,142,3,3
	.word	7451
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,13,145,3,16,4,28
	.byte	'SD5P',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'SD5I',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'SD5D',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,13,152,3,3
	.word	7723
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,13,155,3,16,4,28
	.byte	'SD33P',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'SD33I',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'SD33D',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,13,162,3,3
	.word	7876
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,13,165,3,16,4,28
	.byte	'CT5REG0',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'CT5REG1',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'CT5REG2',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,13,172,3,3
	.word	8032
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,13,175,3,16,4,28
	.byte	'CT5REG3',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'CT5REG4',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	15,1,2,35,2,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,13,181,3,3
	.word	8194
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,13,184,3,16,4,28
	.byte	'CT33REG0',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'CT33REG1',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'CT33REG2',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,13,191,3,3
	.word	8337
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,13,194,3,16,4,28
	.byte	'CT33REG3',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'CT33REG4',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	15,1,2,35,2,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,13,200,3,3
	.word	8502
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,13,203,3,16,4,28
	.byte	'SDFREQSPRD',0,2
	.word	1135
	.byte	16,0,2,35,0,28
	.byte	'SDFREQ',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'SDSTEP',0,1
	.word	532
	.byte	4,4,2,35,3,28
	.byte	'reserved_28',0,1
	.word	532
	.byte	2,2,2,35,3,28
	.byte	'SDSAMPLE',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,13,211,3,3
	.word	8647
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,13,214,3,16,4,28
	.byte	'DRVP',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'SDMINMAXDC',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'DRVN',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'SDLUT',0,1
	.word	532
	.byte	6,2,2,35,3,28
	.byte	'reserved_30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,13,222,3,3
	.word	8828
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,13,225,3,16,4,28
	.byte	'SDPWMPRE',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'SDPID',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'SDVOKLVL',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,13,232,3,3
	.word	9002
	.byte	27
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,13,235,3,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'SYNCDIV',0,1
	.word	532
	.byte	3,5,2,35,1,28
	.byte	'reserved_11',0,4
	.word	1457
	.byte	20,1,2,35,0,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,13,241,3,3
	.word	9162
	.byte	27
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,13,244,3,16,4,28
	.byte	'EVR13',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'OV13',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'EVR33',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'OV33',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'OVSWD',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'UV13',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'UV33',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'UVSWD',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'EXTPASS13',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'EXTPASS33',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'BGPROK',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'reserved_11',0,4
	.word	1457
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,13,130,4,3
	.word	9306
	.byte	27
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,13,133,4,16,4,28
	.byte	'EVR13TRIM',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'SDVOUTSEL',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	15,1,2,35,2,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,13,139,4,3
	.word	9580
	.byte	27
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,13,142,4,16,4,28
	.byte	'EVR13UVVAL',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'EVR33UVVAL',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'SWDUVVAL',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,13,149,4,3
	.word	9719
	.byte	27
	.byte	'_Ifx_SCU_EXTCON_Bits',0,13,152,4,16,4,28
	.byte	'EN0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'reserved_1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'SEL0',0,1
	.word	532
	.byte	4,2,2,35,0,28
	.byte	'reserved_6',0,2
	.word	1135
	.byte	10,0,2,35,0,28
	.byte	'EN1',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'NSEL',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'SEL1',0,1
	.word	532
	.byte	4,2,2,35,2,28
	.byte	'reserved_22',0,1
	.word	532
	.byte	2,0,2,35,2,28
	.byte	'DIV1',0,1
	.word	532
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_EXTCON_Bits',0,13,163,4,3
	.word	9882
	.byte	27
	.byte	'_Ifx_SCU_FDR_Bits',0,13,166,4,16,4,28
	.byte	'STEP',0,2
	.word	1135
	.byte	10,6,2,35,0,28
	.byte	'reserved_10',0,1
	.word	532
	.byte	4,2,2,35,1,28
	.byte	'DM',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'RESULT',0,2
	.word	1135
	.byte	10,6,2,35,2,28
	.byte	'reserved_26',0,1
	.word	532
	.byte	5,1,2,35,3,28
	.byte	'DISCLK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_FDR_Bits',0,13,174,4,3
	.word	10100
	.byte	27
	.byte	'_Ifx_SCU_FMR_Bits',0,13,177,4,16,4,28
	.byte	'FS0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'FS1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'FS2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'FS3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'FS4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'FS5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'FS6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'FS7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'FC0',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'FC1',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'FC2',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'FC3',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'FC4',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'FC5',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'FC6',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'FC7',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_FMR_Bits',0,13,197,4,3
	.word	10263
	.byte	27
	.byte	'_Ifx_SCU_ID_Bits',0,13,200,4,16,4,28
	.byte	'MODREV',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'MODTYPE',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'MODNUMBER',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_ID_Bits',0,13,205,4,3
	.word	10599
	.byte	27
	.byte	'_Ifx_SCU_IGCR_Bits',0,13,208,4,16,4,28
	.byte	'IPEN00',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'IPEN01',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'IPEN02',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'IPEN03',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'IPEN04',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'IPEN05',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'IPEN06',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'IPEN07',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,1
	.word	532
	.byte	5,3,2,35,1,28
	.byte	'GEEN0',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'IGP0',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'IPEN10',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'IPEN11',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'IPEN12',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'IPEN13',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'IPEN14',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'IPEN15',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'IPEN16',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'IPEN17',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	5,3,2,35,3,28
	.byte	'GEEN1',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'IGP1',0,1
	.word	532
	.byte	2,0,2,35,3,0,25
	.byte	'Ifx_SCU_IGCR_Bits',0,13,232,4,3
	.word	10706
	.byte	27
	.byte	'_Ifx_SCU_IN_Bits',0,13,235,4,16,4,28
	.byte	'P0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'P1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,4
	.word	1457
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_IN_Bits',0,13,240,4,3
	.word	11158
	.byte	27
	.byte	'_Ifx_SCU_IOCR_Bits',0,13,243,4,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	4,4,2,35,0,28
	.byte	'PC0',0,1
	.word	532
	.byte	4,0,2,35,0,28
	.byte	'reserved_8',0,1
	.word	532
	.byte	4,4,2,35,1,28
	.byte	'PC1',0,1
	.word	532
	.byte	4,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_IOCR_Bits',0,13,250,4,3
	.word	11257
	.byte	27
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,13,253,4,16,4,28
	.byte	'LBISTREQ',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'LBISTREQP',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'PATTERNS',0,2
	.word	1135
	.byte	14,0,2,35,0,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,13,131,5,3
	.word	11407
	.byte	27
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,13,134,5,16,4,28
	.byte	'SEED',0,4
	.word	1457
	.byte	23,9,2,35,0,28
	.byte	'reserved_23',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'SPLITSH',0,1
	.word	532
	.byte	3,5,2,35,3,28
	.byte	'BODY',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'LBISTFREQU',0,1
	.word	532
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,13,141,5,3
	.word	11556
	.byte	27
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,13,144,5,16,4,28
	.byte	'SIGNATURE',0,4
	.word	1457
	.byte	24,8,2,35,0,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	7,1,2,35,3,28
	.byte	'LBISTDONE',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,13,149,5,3
	.word	11717
	.byte	27
	.byte	'_Ifx_SCU_LCLCON_Bits',0,13,152,5,16,4,28
	.byte	'reserved_0',0,2
	.word	1135
	.byte	16,0,2,35,0,28
	.byte	'LS',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'reserved_17',0,2
	.word	1135
	.byte	14,1,2,35,2,28
	.byte	'LSEN',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LCLCON_Bits',0,13,158,5,3
	.word	11847
	.byte	27
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,13,161,5,16,4,28
	.byte	'LCLT0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'LCLT1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,4
	.word	1457
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST_Bits',0,13,166,5,3
	.word	11979
	.byte	27
	.byte	'_Ifx_SCU_MANID_Bits',0,13,169,5,16,4,28
	.byte	'DEPT',0,1
	.word	532
	.byte	5,3,2,35,0,28
	.byte	'MANUF',0,2
	.word	1135
	.byte	11,0,2,35,0,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_MANID_Bits',0,13,174,5,3
	.word	12094
	.byte	27
	.byte	'_Ifx_SCU_OMR_Bits',0,13,177,5,16,4,28
	.byte	'PS0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'PS1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,2
	.word	1135
	.byte	14,0,2,35,0,28
	.byte	'PCL0',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'PCL1',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'reserved_18',0,2
	.word	1135
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_OMR_Bits',0,13,185,5,3
	.word	12205
	.byte	27
	.byte	'_Ifx_SCU_OSCCON_Bits',0,13,188,5,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'PLLLV',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'OSCRES',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'GAINSEL',0,1
	.word	532
	.byte	2,3,2,35,0,28
	.byte	'MODE',0,1
	.word	532
	.byte	2,1,2,35,0,28
	.byte	'SHBY',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'PLLHV',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'reserved_9',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'X1D',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'X1DEN',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'reserved_12',0,1
	.word	532
	.byte	4,0,2,35,1,28
	.byte	'OSCVAL',0,1
	.word	532
	.byte	5,3,2,35,2,28
	.byte	'reserved_21',0,1
	.word	532
	.byte	2,1,2,35,2,28
	.byte	'APREN',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'CAP0EN',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'CAP1EN',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'CAP2EN',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'CAP3EN',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'reserved_28',0,1
	.word	532
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_OSCCON_Bits',0,13,209,5,3
	.word	12363
	.byte	27
	.byte	'_Ifx_SCU_OUT_Bits',0,13,212,5,16,4,28
	.byte	'P0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'P1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,4
	.word	1457
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_OUT_Bits',0,13,217,5,3
	.word	12775
	.byte	27
	.byte	'_Ifx_SCU_OVCCON_Bits',0,13,220,5,16,4,28
	.byte	'CSEL0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'CSEL1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'CSEL2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'reserved_3',0,2
	.word	1135
	.byte	13,0,2,35,0,28
	.byte	'OVSTRT',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'OVSTP',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'DCINVAL',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'reserved_19',0,1
	.word	532
	.byte	5,0,2,35,2,28
	.byte	'OVCONF',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'POVCONF',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'reserved_26',0,1
	.word	532
	.byte	6,0,2,35,3,0,25
	.byte	'Ifx_SCU_OVCCON_Bits',0,13,233,5,3
	.word	12876
	.byte	27
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,13,236,5,16,4,28
	.byte	'OVEN0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'OVEN1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'OVEN2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'reserved_3',0,4
	.word	1457
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,13,242,5,3
	.word	13143
	.byte	27
	.byte	'_Ifx_SCU_PDISC_Bits',0,13,245,5,16,4,28
	.byte	'PDIS0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'PDIS1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,4
	.word	1457
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC_Bits',0,13,250,5,3
	.word	13279
	.byte	27
	.byte	'_Ifx_SCU_PDR_Bits',0,13,253,5,16,4,28
	.byte	'PD0',0,1
	.word	532
	.byte	3,5,2,35,0,28
	.byte	'PL0',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'PD1',0,1
	.word	532
	.byte	3,1,2,35,0,28
	.byte	'PL1',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,4
	.word	1457
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDR_Bits',0,13,132,6,3
	.word	13390
	.byte	27
	.byte	'_Ifx_SCU_PDRR_Bits',0,13,135,6,16,4,28
	.byte	'PDR0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'PDR1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'PDR2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'PDR3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'PDR4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'PDR5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'PDR6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PDR7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,4
	.word	1457
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR_Bits',0,13,146,6,3
	.word	13523
	.byte	27
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,13,149,6,16,4,28
	.byte	'VCOBYP',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'VCOPWD',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'MODEN',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'reserved_3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'SETFINDIS',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'CLRFINDIS',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'OSCDISCDIS',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'reserved_7',0,2
	.word	1135
	.byte	2,7,2,35,0,28
	.byte	'NDIV',0,1
	.word	532
	.byte	7,0,2,35,1,28
	.byte	'PLLPWD',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'reserved_17',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'RESLD',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'reserved_19',0,1
	.word	532
	.byte	5,0,2,35,2,28
	.byte	'PDIV',0,1
	.word	532
	.byte	4,4,2,35,3,28
	.byte	'reserved_28',0,1
	.word	532
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLCON0_Bits',0,13,166,6,3
	.word	13726
	.byte	27
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,13,169,6,16,4,28
	.byte	'K2DIV',0,1
	.word	532
	.byte	7,1,2,35,0,28
	.byte	'reserved_7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'K3DIV',0,1
	.word	532
	.byte	7,1,2,35,1,28
	.byte	'reserved_15',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'K1DIV',0,1
	.word	532
	.byte	7,1,2,35,2,28
	.byte	'reserved_23',0,2
	.word	1135
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON1_Bits',0,13,177,6,3
	.word	14082
	.byte	27
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,13,180,6,16,4,28
	.byte	'MODCFG',0,2
	.word	1135
	.byte	16,0,2,35,0,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON2_Bits',0,13,184,6,3
	.word	14260
	.byte	27
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,13,187,6,16,4,28
	.byte	'VCOBYP',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'VCOPWD',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	2,4,2,35,0,28
	.byte	'SETFINDIS',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'CLRFINDIS',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'OSCDISCDIS',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'reserved_7',0,2
	.word	1135
	.byte	2,7,2,35,0,28
	.byte	'NDIV',0,1
	.word	532
	.byte	5,2,2,35,1,28
	.byte	'reserved_14',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'PLLPWD',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'reserved_17',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'RESLD',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'reserved_19',0,1
	.word	532
	.byte	5,0,2,35,2,28
	.byte	'PDIV',0,1
	.word	532
	.byte	4,4,2,35,3,28
	.byte	'reserved_28',0,1
	.word	532
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,13,204,6,3
	.word	14360
	.byte	27
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,13,207,6,16,4,28
	.byte	'K2DIV',0,1
	.word	532
	.byte	7,1,2,35,0,28
	.byte	'reserved_7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'K3DIV',0,1
	.word	532
	.byte	4,4,2,35,1,28
	.byte	'reserved_12',0,1
	.word	532
	.byte	4,0,2,35,1,28
	.byte	'K1DIV',0,1
	.word	532
	.byte	7,1,2,35,2,28
	.byte	'reserved_23',0,2
	.word	1135
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,13,215,6,3
	.word	14730
	.byte	27
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,13,218,6,16,4,28
	.byte	'VCOBYST',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'PWDSTAT',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'VCOLOCK',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'FINDIS',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'K1RDY',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'K2RDY',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'reserved_6',0,4
	.word	1457
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,13,227,6,3
	.word	14916
	.byte	27
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,13,230,6,16,4,28
	.byte	'VCOBYST',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'reserved_1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'VCOLOCK',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'FINDIS',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'K1RDY',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'K2RDY',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'reserved_6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'MODRUN',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,4
	.word	1457
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,13,241,6,3
	.word	15114
	.byte	27
	.byte	'_Ifx_SCU_PMCSR_Bits',0,13,244,6,16,4,28
	.byte	'REQSLP',0,1
	.word	532
	.byte	2,6,2,35,0,28
	.byte	'SMUSLP',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'reserved_3',0,1
	.word	532
	.byte	5,0,2,35,0,28
	.byte	'PMST',0,1
	.word	532
	.byte	3,5,2,35,1,28
	.byte	'reserved_11',0,4
	.word	1457
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR_Bits',0,13,251,6,3
	.word	15347
	.byte	27
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,13,254,6,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'ESR1WKEN',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'PINAWKEN',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'PINBWKEN',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'ESR0DFEN',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'ESR0EDCON',0,1
	.word	532
	.byte	2,1,2,35,0,28
	.byte	'ESR1DFEN',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'ESR1EDCON',0,1
	.word	532
	.byte	2,6,2,35,1,28
	.byte	'PINADFEN',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'PINAEDCON',0,1
	.word	532
	.byte	2,3,2,35,1,28
	.byte	'PINBDFEN',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'PINBEDCON',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'SCREN',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'STBYRAMSEL',0,1
	.word	532
	.byte	2,5,2,35,2,28
	.byte	'SCRCLKSEL',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'SCRWKEN',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'TRISTEN',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'TRISTREQ',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'PORSTDF',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'DCDCSYNC',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'reserved_26',0,1
	.word	532
	.byte	3,3,2,35,3,28
	.byte	'ESR0TRIST',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'reserved_30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,13,153,7,3
	.word	15499
	.byte	27
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,13,156,7,16,4,28
	.byte	'SCRSTEN',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'SCRSTREQ',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	6,0,2,35,0,28
	.byte	'CPUIDLSEL',0,1
	.word	532
	.byte	3,5,2,35,1,28
	.byte	'reserved_11',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'IRADIS',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'reserved_13',0,1
	.word	532
	.byte	3,0,2,35,1,28
	.byte	'SCRCFG',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'CPUSEL',0,1
	.word	532
	.byte	3,5,2,35,3,28
	.byte	'STBYEVEN',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'STBYEV',0,1
	.word	532
	.byte	3,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,13,170,7,3
	.word	16066
	.byte	27
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,13,173,7,16,4,28
	.byte	'SCRINT',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'BUSY',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'SCRECC',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'SCRWDT',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'SCRRST',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'reserved_12',0,1
	.word	532
	.byte	4,0,2,35,1,28
	.byte	'TCINT',0,1
	.word	532
	.byte	8,0,2,35,2,28
	.byte	'TCINTREQ',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'SMURST',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'RST',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'reserved_27',0,1
	.word	532
	.byte	4,1,2,35,3,28
	.byte	'LCK',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,13,187,7,3
	.word	16360
	.byte	27
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,13,190,7,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	2,6,2,35,0,28
	.byte	'ESR1WKP',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'ESR1OVRUN',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'PINAWKP',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'PINAOVRUN',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'PINBWKP',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PINBOVRUN',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'PORSTDF',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'HWCFGEVR',0,1
	.word	532
	.byte	3,3,2,35,1,28
	.byte	'STBYRAM',0,1
	.word	532
	.byte	2,1,2,35,1,28
	.byte	'TRIST',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'SCRST',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'SCRWKP',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'SCR',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'SCRWKEN',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'ESR1WKEN',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'PINAWKEN',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'PINBWKEN',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'reserved_23',0,2
	.word	1135
	.byte	4,5,2,35,2,28
	.byte	'ESR0TRIST',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'reserved_28',0,1
	.word	532
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,13,214,7,3
	.word	16638
	.byte	27
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,13,217,7,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	2,6,2,35,0,28
	.byte	'ESR1WKPCLR',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'ESR1OVRUNCLR',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'PINAWKPCLR',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'PINAOVRUNCLR',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'PINBWKPCLR',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PINBOVRUNCLR',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'SCRSTCLR',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'SCRWKPCLR',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'reserved_18',0,2
	.word	1135
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,13,230,7,3
	.word	17134
	.byte	27
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,13,233,7,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'CLRC',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,2
	.word	1135
	.byte	10,4,2,35,0,28
	.byte	'CSS0',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'CSS1',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'CSS2',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'reserved_15',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'USRINFO',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON2_Bits',0,13,243,7,3
	.word	17447
	.byte	27
	.byte	'_Ifx_SCU_RSTCON_Bits',0,13,246,7,16,4,28
	.byte	'ESR0',0,1
	.word	532
	.byte	2,6,2,35,0,28
	.byte	'ESR1',0,1
	.word	532
	.byte	2,4,2,35,0,28
	.byte	'reserved_4',0,1
	.word	532
	.byte	2,2,2,35,0,28
	.byte	'SMU',0,1
	.word	532
	.byte	2,0,2,35,0,28
	.byte	'SW',0,1
	.word	532
	.byte	2,6,2,35,1,28
	.byte	'STM0',0,1
	.word	532
	.byte	2,4,2,35,1,28
	.byte	'STM1',0,1
	.word	532
	.byte	2,2,2,35,1,28
	.byte	'STM2',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON_Bits',0,13,129,8,3
	.word	17656
	.byte	27
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,13,132,8,16,4,28
	.byte	'ESR0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'ESR1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'SMU',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'SW',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'STM0',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'STM1',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'STM2',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'reserved_8',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'PORST',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'reserved_17',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'CB0',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'CB1',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'CB3',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'reserved_21',0,1
	.word	532
	.byte	2,1,2,35,2,28
	.byte	'EVR13',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'EVR33',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'SWD',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'reserved_26',0,1
	.word	532
	.byte	2,4,2,35,3,28
	.byte	'STBYR',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'reserved_29',0,1
	.word	532
	.byte	3,0,2,35,3,0,25
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,13,155,8,3
	.word	17867
	.byte	27
	.byte	'_Ifx_SCU_SAFECON_Bits',0,13,158,8,16,4,28
	.byte	'HBT',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'reserved_1',0,4
	.word	1457
	.byte	31,0,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON_Bits',0,13,162,8,3
	.word	18299
	.byte	27
	.byte	'_Ifx_SCU_STSTAT_Bits',0,13,165,8,16,4,28
	.byte	'HWCFG',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'FTM',0,1
	.word	532
	.byte	7,1,2,35,1,28
	.byte	'MODE',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'FCBAE',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'LUDIS',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'reserved_18',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'TRSTL',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'SPDEN',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'reserved_21',0,1
	.word	532
	.byte	3,0,2,35,2,28
	.byte	'RAMINT',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'reserved_25',0,1
	.word	532
	.byte	7,0,2,35,3,0,25
	.byte	'Ifx_SCU_STSTAT_Bits',0,13,178,8,3
	.word	18395
	.byte	27
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,13,181,8,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'SWRSTREQ',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,4
	.word	1457
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,13,186,8,3
	.word	18655
	.byte	27
	.byte	'_Ifx_SCU_SYSCON_Bits',0,13,189,8,16,4,28
	.byte	'CCTRIG0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'reserved_1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'RAMINTM',0,1
	.word	532
	.byte	2,4,2,35,0,28
	.byte	'SETLUDIS',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'reserved_5',0,1
	.word	532
	.byte	3,0,2,35,0,28
	.byte	'DATM',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'reserved_9',0,4
	.word	1457
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON_Bits',0,13,198,8,3
	.word	18780
	.byte	27
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,13,201,8,16,4,28
	.byte	'ESR0T',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'ESR1T',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'SMUT',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,4
	.word	1457
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,13,208,8,3
	.word	18977
	.byte	27
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,13,211,8,16,4,28
	.byte	'ESR0T',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'ESR1T',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'SMUT',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,4
	.word	1457
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,13,218,8,3
	.word	19130
	.byte	27
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,13,221,8,16,4,28
	.byte	'ESR0T',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'ESR1T',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'SMUT',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,4
	.word	1457
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET_Bits',0,13,228,8,3
	.word	19283
	.byte	27
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,13,231,8,16,4,28
	.byte	'ESR0T',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'ESR1T',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'SMUT',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,4
	.word	1457
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,13,238,8,3
	.word	19436
	.byte	7
	.byte	'unsigned int',0,4,7,27
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,13,241,8,16,4,28
	.byte	'ENDINIT',0,4
	.word	19591
	.byte	1,31,2,35,0,28
	.byte	'LCK',0,4
	.word	19591
	.byte	1,30,2,35,0,28
	.byte	'PW',0,4
	.word	19591
	.byte	14,16,2,35,0,28
	.byte	'REL',0,4
	.word	19591
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,13,247,8,3
	.word	19607
	.byte	27
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,13,250,8,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	2,6,2,35,0,28
	.byte	'IR0',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'DR',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'IR1',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'UR',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PAR',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'TCR',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'TCTR',0,1
	.word	532
	.byte	7,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,13,134,9,3
	.word	19737
	.byte	27
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,13,137,9,16,4,28
	.byte	'AE',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'OE',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'IS0',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'DS',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'TO',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'IS1',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'US',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PAS',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'TCS',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'TCT',0,1
	.word	532
	.byte	7,0,2,35,1,28
	.byte	'TIM',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,13,150,9,3
	.word	19975
	.byte	27
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,13,153,9,16,4,28
	.byte	'ENDINIT',0,4
	.word	19591
	.byte	1,31,2,35,0,28
	.byte	'LCK',0,4
	.word	19591
	.byte	1,30,2,35,0,28
	.byte	'PW',0,4
	.word	19591
	.byte	14,16,2,35,0,28
	.byte	'REL',0,4
	.word	19591
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,13,159,9,3
	.word	20198
	.byte	27
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,13,162,9,16,4,28
	.byte	'CLRIRF',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'reserved_1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'IR0',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'DR',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'IR1',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'UR',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PAR',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'TCR',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'TCTR',0,1
	.word	532
	.byte	7,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,13,175,9,3
	.word	20324
	.byte	27
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,13,178,9,16,4,28
	.byte	'AE',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'OE',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'IS0',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'DS',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'TO',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'IS1',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'US',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PAS',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'TCS',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'TCT',0,1
	.word	532
	.byte	7,0,2,35,1,28
	.byte	'TIM',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,13,191,9,3
	.word	20576
	.byte	29,13,199,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	2101
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN0',0,13,204,9,3
	.word	20795
	.byte	29,13,207,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	2658
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1',0,13,212,9,3
	.word	20859
	.byte	29,13,215,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	2735
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS',0,13,220,9,3
	.word	20923
	.byte	29,13,223,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	2871
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON0',0,13,228,9,3
	.word	20988
	.byte	29,13,231,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	3151
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON1',0,13,236,9,3
	.word	21053
	.byte	29,13,239,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	3389
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON2',0,13,244,9,3
	.word	21118
	.byte	29,13,247,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	3517
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON3',0,13,252,9,3
	.word	21183
	.byte	29,13,255,9,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	3760
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON4',0,13,132,10,3
	.word	21248
	.byte	29,13,135,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	3995
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON5',0,13,140,10,3
	.word	21313
	.byte	29,13,143,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	4123
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6',0,13,148,10,3
	.word	21378
	.byte	29,13,151,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	4223
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7',0,13,156,10,3
	.word	21443
	.byte	29,13,159,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	4323
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CHIPID',0,13,164,10,3
	.word	21508
	.byte	29,13,167,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	4531
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSCON',0,13,172,10,3
	.word	21572
	.byte	29,13,175,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	4696
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSLIM',0,13,180,10,3
	.word	21636
	.byte	29,13,183,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	4879
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSSTAT',0,13,188,10,3
	.word	21700
	.byte	29,13,191,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	5033
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EICR',0,13,196,10,3
	.word	21765
	.byte	29,13,199,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	5397
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR',0,13,204,10,3
	.word	21827
	.byte	29,13,207,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	5608
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EMSR',0,13,212,10,3
	.word	21889
	.byte	29,13,215,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	5860
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG',0,13,220,10,3
	.word	21951
	.byte	29,13,223,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	5978
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG',0,13,228,10,3
	.word	22015
	.byte	29,13,231,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	6089
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR13CON',0,13,236,10,3
	.word	22080
	.byte	29,13,239,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	6252
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR33CON',0,13,244,10,3
	.word	22146
	.byte	29,13,247,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	6415
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRADCSTAT',0,13,252,10,3
	.word	22212
	.byte	29,13,255,10,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	6573
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRDVSTAT',0,13,132,11,3
	.word	22280
	.byte	29,13,135,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	6738
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRMONCTRL',0,13,140,11,3
	.word	22347
	.byte	29,13,143,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	7067
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROSCCTRL',0,13,148,11,3
	.word	22415
	.byte	29,13,151,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	7288
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROVMON',0,13,156,11,3
	.word	22483
	.byte	29,13,159,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	7451
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRRSTCON',0,13,164,11,3
	.word	22549
	.byte	29,13,167,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	7723
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,13,172,11,3
	.word	22616
	.byte	29,13,175,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	7876
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,13,180,11,3
	.word	22685
	.byte	29,13,183,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	8032
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,13,188,11,3
	.word	22754
	.byte	29,13,191,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	8194
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,13,196,11,3
	.word	22823
	.byte	29,13,199,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	8337
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,13,204,11,3
	.word	22892
	.byte	29,13,207,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	8502
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,13,212,11,3
	.word	22961
	.byte	29,13,215,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	8647
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1',0,13,220,11,3
	.word	23030
	.byte	29,13,223,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	8828
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2',0,13,228,11,3
	.word	23098
	.byte	29,13,231,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	9002
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3',0,13,236,11,3
	.word	23166
	.byte	29,13,239,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	9162
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4',0,13,244,11,3
	.word	23234
	.byte	29,13,247,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	9306
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT',0,13,252,11,3
	.word	23302
	.byte	29,13,255,11,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	9580
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRTRIM',0,13,132,12,3
	.word	23367
	.byte	29,13,135,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	9719
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRUVMON',0,13,140,12,3
	.word	23432
	.byte	29,13,143,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	9882
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EXTCON',0,13,148,12,3
	.word	23498
	.byte	29,13,151,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	10100
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FDR',0,13,156,12,3
	.word	23562
	.byte	29,13,159,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	10263
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FMR',0,13,164,12,3
	.word	23623
	.byte	29,13,167,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	10599
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ID',0,13,172,12,3
	.word	23684
	.byte	29,13,175,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	10706
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IGCR',0,13,180,12,3
	.word	23744
	.byte	29,13,183,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	11158
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IN',0,13,188,12,3
	.word	23806
	.byte	29,13,191,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	11257
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IOCR',0,13,196,12,3
	.word	23866
	.byte	29,13,199,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	11407
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL0',0,13,204,12,3
	.word	23928
	.byte	29,13,207,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	11556
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL1',0,13,212,12,3
	.word	23996
	.byte	29,13,215,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	11717
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL2',0,13,220,12,3
	.word	24064
	.byte	29,13,223,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	11847
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLCON',0,13,228,12,3
	.word	24132
	.byte	29,13,231,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	11979
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST',0,13,236,12,3
	.word	24196
	.byte	29,13,239,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	12094
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_MANID',0,13,244,12,3
	.word	24261
	.byte	29,13,247,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	12205
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OMR',0,13,252,12,3
	.word	24324
	.byte	29,13,255,12,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	12363
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OSCCON',0,13,132,13,3
	.word	24385
	.byte	29,13,135,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	12775
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OUT',0,13,140,13,3
	.word	24449
	.byte	29,13,143,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	12876
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCCON',0,13,148,13,3
	.word	24510
	.byte	29,13,151,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	13143
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE',0,13,156,13,3
	.word	24574
	.byte	29,13,159,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	13279
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC',0,13,164,13,3
	.word	24641
	.byte	29,13,167,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	13390
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDR',0,13,172,13,3
	.word	24704
	.byte	29,13,175,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	13523
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR',0,13,180,13,3
	.word	24765
	.byte	29,13,183,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	13726
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON0',0,13,188,13,3
	.word	24827
	.byte	29,13,191,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	14082
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON1',0,13,196,13,3
	.word	24892
	.byte	29,13,199,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	14260
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON2',0,13,204,13,3
	.word	24957
	.byte	29,13,207,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	14360
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON0',0,13,212,13,3
	.word	25022
	.byte	29,13,215,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	14730
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON1',0,13,220,13,3
	.word	25091
	.byte	29,13,223,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	14916
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT',0,13,228,13,3
	.word	25160
	.byte	29,13,231,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	15114
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT',0,13,236,13,3
	.word	25229
	.byte	29,13,239,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	15347
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR',0,13,244,13,3
	.word	25294
	.byte	29,13,247,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	15499
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR0',0,13,252,13,3
	.word	25357
	.byte	29,13,255,13,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	16066
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR1',0,13,132,14,3
	.word	25422
	.byte	29,13,135,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	16360
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR2',0,13,140,14,3
	.word	25487
	.byte	29,13,143,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	16638
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTAT',0,13,148,14,3
	.word	25552
	.byte	29,13,151,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	17134
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR',0,13,156,14,3
	.word	25618
	.byte	29,13,159,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	17656
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON',0,13,164,14,3
	.word	25687
	.byte	29,13,167,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	17447
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON2',0,13,172,14,3
	.word	25751
	.byte	29,13,175,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	17867
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTSTAT',0,13,180,14,3
	.word	25816
	.byte	29,13,183,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	18299
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON',0,13,188,14,3
	.word	25881
	.byte	29,13,191,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	18395
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_STSTAT',0,13,196,14,3
	.word	25946
	.byte	29,13,199,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	18655
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON',0,13,204,14,3
	.word	26010
	.byte	29,13,207,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	18780
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON',0,13,212,14,3
	.word	26076
	.byte	29,13,215,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	18977
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR',0,13,220,14,3
	.word	26140
	.byte	29,13,223,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	19130
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS',0,13,228,14,3
	.word	26205
	.byte	29,13,231,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	19283
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET',0,13,236,14,3
	.word	26270
	.byte	29,13,239,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	19436
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT',0,13,244,14,3
	.word	26335
	.byte	29,13,247,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	19607
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0',0,13,252,14,3
	.word	26401
	.byte	29,13,255,14,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	19737
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1',0,13,132,15,3
	.word	26470
	.byte	29,13,135,15,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	19975
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_SR',0,13,140,15,3
	.word	26539
	.byte	29,13,143,15,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	20198
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0',0,13,148,15,3
	.word	26606
	.byte	29,13,151,15,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	20324
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON1',0,13,156,15,3
	.word	26673
	.byte	29,13,159,15,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	20576
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_SR',0,13,164,15,3
	.word	26740
	.byte	27
	.byte	'_Ifx_SCU_WDTCPU',0,13,175,15,25,12,30
	.byte	'CON0',0
	.word	26401
	.byte	4,2,35,0,30
	.byte	'CON1',0
	.word	26470
	.byte	4,2,35,4,30
	.byte	'SR',0
	.word	26539
	.byte	4,2,35,8,0,23
	.word	26805
	.byte	25
	.byte	'Ifx_SCU_WDTCPU',0,13,180,15,3
	.word	26868
	.byte	27
	.byte	'_Ifx_SCU_WDTS',0,13,183,15,25,12,30
	.byte	'CON0',0
	.word	26606
	.byte	4,2,35,0,30
	.byte	'CON1',0
	.word	26673
	.byte	4,2,35,4,30
	.byte	'SR',0
	.word	26740
	.byte	4,2,35,8,0,23
	.word	26897
	.byte	25
	.byte	'Ifx_SCU_WDTS',0,13,188,15,3
	.word	26958
	.byte	25
	.byte	'IfxFlash_FlashType',0,4,159,1,3
	.word	734
	.byte	31,4,176,1,9,8,30
	.byte	'start',0
	.word	435
	.byte	4,2,35,0,30
	.byte	'end',0
	.word	435
	.byte	4,2,35,4,0,25
	.byte	'IfxFlash_flashSector',0,4,180,1,3
	.word	27013
	.byte	32,96
	.word	27013
	.byte	33,11,0,24
	.word	27078
	.byte	34
	.byte	'IfxFlash_dFlashTableEepLog',0,4,186,1,43
	.word	27087
	.byte	1,1,27
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,14,45,16,4,28
	.byte	'EN0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'EN1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'EN2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'EN3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'EN4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'EN5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'EN6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'EN7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'EN8',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'EN9',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'EN10',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'EN11',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'EN12',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'EN13',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'EN14',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'EN15',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'EN16',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'EN17',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'EN18',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'EN19',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'EN20',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'EN21',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'EN22',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'EN23',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'EN24',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'EN25',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'EN26',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'EN27',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'EN28',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'EN29',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'EN30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'EN31',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,14,79,3
	.word	27130
	.byte	27
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,14,82,16,4,28
	.byte	'reserved_0',0,4
	.word	1457
	.byte	32,0,2,35,0,0,25
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,14,85,3
	.word	27691
	.byte	27
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,14,88,16,4,28
	.byte	'SEL',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'reserved_6',0,1
	.word	532
	.byte	2,0,2,35,0,28
	.byte	'CLR',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'DIS',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'reserved_10',0,4
	.word	1457
	.byte	22,0,2,35,0,0,25
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,14,95,3
	.word	27772
	.byte	27
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,14,98,16,4,28
	.byte	'VLD0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'VLD1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'VLD2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'VLD3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'VLD4',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'VLD5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'VLD6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'VLD7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'VLD8',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'VLD9',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'reserved_10',0,4
	.word	1457
	.byte	22,0,2,35,0,0,25
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,14,111,3
	.word	27925
	.byte	27
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,14,114,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	5,3,2,35,0,28
	.byte	'ADDR',0,4
	.word	1457
	.byte	19,8,2,35,0,28
	.byte	'ERR',0,1
	.word	532
	.byte	6,2,2,35,3,28
	.byte	'VLD',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'CLR',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,14,121,3
	.word	28173
	.byte	27
	.byte	'_Ifx_FLASH_COMM0_Bits',0,14,124,16,4,28
	.byte	'STATUS',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'reserved_8',0,4
	.word	1457
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_FLASH_COMM0_Bits',0,14,128,1,3
	.word	28319
	.byte	27
	.byte	'_Ifx_FLASH_COMM1_Bits',0,14,131,1,16,4,28
	.byte	'STATUS',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'DATA',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_FLASH_COMM1_Bits',0,14,136,1,3
	.word	28417
	.byte	27
	.byte	'_Ifx_FLASH_COMM2_Bits',0,14,139,1,16,4,28
	.byte	'STATUS',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'DATA',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_FLASH_COMM2_Bits',0,14,144,1,3
	.word	28533
	.byte	27
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,14,147,1,16,4,28
	.byte	'RCODE',0,4
	.word	1457
	.byte	22,10,2,35,0,28
	.byte	'reserved_22',0,2
	.word	1135
	.byte	8,2,2,35,2,28
	.byte	'EDCERRINJ',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'ECCORDIS',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_ECCRD_Bits',0,14,153,1,3
	.word	28649
	.byte	27
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,14,156,1,16,4,28
	.byte	'RCODE',0,4
	.word	1457
	.byte	22,10,2,35,0,28
	.byte	'reserved_22',0,2
	.word	1135
	.byte	8,2,2,35,2,28
	.byte	'EDCERRINJ',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'ECCORDIS',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_ECCRP_Bits',0,14,162,1,3
	.word	28789
	.byte	27
	.byte	'_Ifx_FLASH_ECCW_Bits',0,14,165,1,16,4,28
	.byte	'WCODE',0,4
	.word	1457
	.byte	22,10,2,35,0,28
	.byte	'reserved_22',0,2
	.word	1135
	.byte	8,2,2,35,2,28
	.byte	'DECENCDIS',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'PECENCDIS',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_ECCW_Bits',0,14,171,1,3
	.word	28929
	.byte	27
	.byte	'_Ifx_FLASH_FCON_Bits',0,14,174,1,16,4,28
	.byte	'WSPFLASH',0,1
	.word	532
	.byte	4,4,2,35,0,28
	.byte	'WSECPF',0,1
	.word	532
	.byte	2,2,2,35,0,28
	.byte	'WSDFLASH',0,2
	.word	1135
	.byte	6,4,2,35,0,28
	.byte	'WSECDF',0,1
	.word	532
	.byte	3,1,2,35,1,28
	.byte	'IDLE',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'ESLDIS',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'SLEEP',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'NSAFECC',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'STALL',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'RES21',0,1
	.word	532
	.byte	2,2,2,35,2,28
	.byte	'RES23',0,1
	.word	532
	.byte	2,0,2,35,2,28
	.byte	'VOPERM',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'SQERM',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'PROERM',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'reserved_27',0,1
	.word	532
	.byte	3,2,2,35,3,28
	.byte	'PR5V',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'EOBM',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_FCON_Bits',0,14,193,1,3
	.word	29068
	.byte	27
	.byte	'_Ifx_FLASH_FPRO_Bits',0,14,196,1,16,4,28
	.byte	'PROINP',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'PRODISP',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'PROIND',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'PRODISD',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'PROINHSMCOTP',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'RES5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'PROINOTP',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'RES7',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'PROINDBG',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'PRODISDBG',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'PROINHSM',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'reserved_11',0,1
	.word	532
	.byte	5,0,2,35,1,28
	.byte	'DCFP',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'DDFP',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'DDFPX',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'reserved_19',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'DDFD',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'reserved_21',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'ENPE',0,1
	.word	532
	.byte	2,0,2,35,2,28
	.byte	'reserved_24',0,1
	.word	532
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_FLASH_FPRO_Bits',0,14,218,1,3
	.word	29430
	.byte	27
	.byte	'_Ifx_FLASH_FSR_Bits',0,14,221,1,16,4,28
	.byte	'FABUSY',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'D0BUSY',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'RES1',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'P0BUSY',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'P1BUSY',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'RES5',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'RES6',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'PROG',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'ERASE',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'PFPAGE',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'DFPAGE',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'OPER',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'SQER',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'PROER',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'PFSBER',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'PFDBER',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'PFMBER',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'RES17',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'DFSBER',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'DFDBER',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'DFTBER',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'DFMBER',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'SRIADDERR',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'reserved_23',0,2
	.word	1135
	.byte	2,7,2,35,2,28
	.byte	'PVER',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'EVER',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'SPND',0,1
	.word	532
	.byte	1,4,2,35,3,28
	.byte	'SLM',0,1
	.word	532
	.byte	1,3,2,35,3,28
	.byte	'reserved_29',0,1
	.word	532
	.byte	1,2,2,35,3,28
	.byte	'ORIER',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'reserved_31',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_FSR_Bits',0,14,254,1,3
	.word	29871
	.byte	27
	.byte	'_Ifx_FLASH_ID_Bits',0,14,129,2,16,4,28
	.byte	'MODREV',0,1
	.word	532
	.byte	8,0,2,35,0,28
	.byte	'MODTYPE',0,1
	.word	532
	.byte	8,0,2,35,1,28
	.byte	'MODNUMBER',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_FLASH_ID_Bits',0,14,134,2,3
	.word	30477
	.byte	27
	.byte	'_Ifx_FLASH_MARD_Bits',0,14,137,2,16,4,28
	.byte	'HMARGIN',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'SELD0',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'reserved_2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'SPND',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'SPNDERR',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'reserved_5',0,2
	.word	1135
	.byte	10,1,2,35,0,28
	.byte	'TRAPDIS',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_FLASH_MARD_Bits',0,14,147,2,3
	.word	30588
	.byte	27
	.byte	'_Ifx_FLASH_MARP_Bits',0,14,150,2,16,4,28
	.byte	'SELP0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'SELP1',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'RES2',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'RES3',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'reserved_4',0,2
	.word	1135
	.byte	11,1,2,35,0,28
	.byte	'TRAPDIS',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_FLASH_MARP_Bits',0,14,159,2,3
	.word	30802
	.byte	27
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,14,162,2,16,4,28
	.byte	'L',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'NSAFECC',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'RAMIN',0,1
	.word	532
	.byte	2,4,2,35,0,28
	.byte	'RAMINSEL',0,1
	.word	532
	.byte	4,0,2,35,0,28
	.byte	'OSCCFG',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'MODE',0,1
	.word	532
	.byte	2,5,2,35,1,28
	.byte	'APREN',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'CAP0EN',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'CAP1EN',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'CAP2EN',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'CAP3EN',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'ESR0CNT',0,2
	.word	1135
	.byte	12,4,2,35,2,28
	.byte	'RES29',0,1
	.word	532
	.byte	2,2,2,35,3,28
	.byte	'RES30',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'RPRO',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_PROCOND_Bits',0,14,179,2,3
	.word	30989
	.byte	27
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,14,182,2,16,4,28
	.byte	'OCDSDIS',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'DBGIFLCK',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'EDM',0,1
	.word	532
	.byte	2,4,2,35,0,28
	.byte	'reserved_4',0,4
	.word	1457
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,14,188,2,3
	.word	31313
	.byte	27
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,14,191,2,16,4,28
	.byte	'HSMDBGDIS',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'DBGIFLCK',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'TSTIFLCK',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'HSMTSTDIS',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'RES15',0,2
	.word	1135
	.byte	12,0,2,35,0,28
	.byte	'reserved_16',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,14,199,2,3
	.word	31456
	.byte	27
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,14,202,2,16,4,28
	.byte	'HSMBOOTEN',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'SSWWAIT',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'HSMDX',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'HSM6X',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'HSM16X',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'HSM17X',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'S6ROM',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'HSMENPINS',0,2
	.word	1135
	.byte	2,7,2,35,0,28
	.byte	'HSMENRES',0,1
	.word	532
	.byte	2,5,2,35,1,28
	.byte	'DESTDBG',0,1
	.word	532
	.byte	2,3,2,35,1,28
	.byte	'BLKFLAN',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'reserved_14',0,1
	.word	532
	.byte	2,0,2,35,1,28
	.byte	'S16ROM',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'S17ROM',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'reserved_18',0,2
	.word	1135
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,14,219,2,3
	.word	31645
	.byte	27
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,14,222,2,16,4,28
	.byte	'S0ROM',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'S1ROM',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'S2ROM',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'S3ROM',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'S4ROM',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'S5ROM',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'S6ROM',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'S7ROM',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'S8ROM',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'S9ROM',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'S10ROM',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'S11ROM',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'S12ROM',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'S13ROM',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'S14ROM',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'S15ROM',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'S16ROM',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'S17ROM',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'S18ROM',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'S19ROM',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'S20ROM',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'S21ROM',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'S22ROM',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'S23ROM',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'S24ROM',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'S25ROM',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'S26ROM',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'reserved_27',0,1
	.word	532
	.byte	2,3,2,35,3,28
	.byte	'BML',0,1
	.word	532
	.byte	2,1,2,35,3,28
	.byte	'TP',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,14,254,2,3
	.word	32008
	.byte	27
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,14,129,3,16,4,28
	.byte	'S0L',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'S1L',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'S2L',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'S3L',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'S4L',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'S5L',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'S6L',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'S7L',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'S8L',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'S9L',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'S10L',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'S11L',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'S12L',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'S13L',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'S14L',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'S15L',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'S16L',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'S17L',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'S18L',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'S19L',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'S20L',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'S21L',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'S22L',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'S23L',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'S24L',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'S25L',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'S26L',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'reserved_27',0,1
	.word	532
	.byte	4,1,2,35,3,28
	.byte	'RPRO',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_PROCONP_Bits',0,14,160,3,3
	.word	32603
	.byte	27
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,14,163,3,16,4,28
	.byte	'S0WOP',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'S1WOP',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'S2WOP',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'S3WOP',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'S4WOP',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'S5WOP',0,1
	.word	532
	.byte	1,2,2,35,0,28
	.byte	'S6WOP',0,1
	.word	532
	.byte	1,1,2,35,0,28
	.byte	'S7WOP',0,1
	.word	532
	.byte	1,0,2,35,0,28
	.byte	'S8WOP',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'S9WOP',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'S10WOP',0,1
	.word	532
	.byte	1,5,2,35,1,28
	.byte	'S11WOP',0,1
	.word	532
	.byte	1,4,2,35,1,28
	.byte	'S12WOP',0,1
	.word	532
	.byte	1,3,2,35,1,28
	.byte	'S13WOP',0,1
	.word	532
	.byte	1,2,2,35,1,28
	.byte	'S14WOP',0,1
	.word	532
	.byte	1,1,2,35,1,28
	.byte	'S15WOP',0,1
	.word	532
	.byte	1,0,2,35,1,28
	.byte	'S16WOP',0,1
	.word	532
	.byte	1,7,2,35,2,28
	.byte	'S17WOP',0,1
	.word	532
	.byte	1,6,2,35,2,28
	.byte	'S18WOP',0,1
	.word	532
	.byte	1,5,2,35,2,28
	.byte	'S19WOP',0,1
	.word	532
	.byte	1,4,2,35,2,28
	.byte	'S20WOP',0,1
	.word	532
	.byte	1,3,2,35,2,28
	.byte	'S21WOP',0,1
	.word	532
	.byte	1,2,2,35,2,28
	.byte	'S22WOP',0,1
	.word	532
	.byte	1,1,2,35,2,28
	.byte	'S23WOP',0,1
	.word	532
	.byte	1,0,2,35,2,28
	.byte	'S24WOP',0,1
	.word	532
	.byte	1,7,2,35,3,28
	.byte	'S25WOP',0,1
	.word	532
	.byte	1,6,2,35,3,28
	.byte	'S26WOP',0,1
	.word	532
	.byte	1,5,2,35,3,28
	.byte	'reserved_27',0,1
	.word	532
	.byte	4,1,2,35,3,28
	.byte	'DATM',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,14,194,3,3
	.word	33127
	.byte	27
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,14,197,3,16,4,28
	.byte	'TAG',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'reserved_6',0,4
	.word	1457
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,14,201,3,3
	.word	33709
	.byte	27
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,14,204,3,16,4,28
	.byte	'TAG',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'reserved_6',0,4
	.word	1457
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,14,208,3,3
	.word	33811
	.byte	27
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,14,211,3,16,4,28
	.byte	'TAG',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'reserved_6',0,4
	.word	1457
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,14,215,3,3
	.word	33913
	.byte	27
	.byte	'_Ifx_FLASH_RRAD_Bits',0,14,218,3,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	3,5,2,35,0,28
	.byte	'ADD',0,4
	.word	1457
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_FLASH_RRAD_Bits',0,14,222,3,3
	.word	34015
	.byte	27
	.byte	'_Ifx_FLASH_RRCT_Bits',0,14,225,3,16,4,28
	.byte	'STRT',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'STP',0,1
	.word	532
	.byte	1,6,2,35,0,28
	.byte	'BUSY',0,1
	.word	532
	.byte	1,5,2,35,0,28
	.byte	'DONE',0,1
	.word	532
	.byte	1,4,2,35,0,28
	.byte	'ERR',0,1
	.word	532
	.byte	1,3,2,35,0,28
	.byte	'reserved_5',0,1
	.word	532
	.byte	3,0,2,35,0,28
	.byte	'EOBM',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'reserved_9',0,1
	.word	532
	.byte	7,0,2,35,1,28
	.byte	'CNT',0,2
	.word	1135
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_FLASH_RRCT_Bits',0,14,236,3,3
	.word	34109
	.byte	27
	.byte	'_Ifx_FLASH_RRD0_Bits',0,14,239,3,16,4,28
	.byte	'DATA',0,4
	.word	1457
	.byte	32,0,2,35,0,0,25
	.byte	'Ifx_FLASH_RRD0_Bits',0,14,242,3,3
	.word	34319
	.byte	27
	.byte	'_Ifx_FLASH_RRD1_Bits',0,14,245,3,16,4,28
	.byte	'DATA',0,4
	.word	1457
	.byte	32,0,2,35,0,0,25
	.byte	'Ifx_FLASH_RRD1_Bits',0,14,248,3,3
	.word	34392
	.byte	27
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,14,251,3,16,4,28
	.byte	'SEL',0,1
	.word	532
	.byte	6,2,2,35,0,28
	.byte	'reserved_6',0,1
	.word	532
	.byte	2,0,2,35,0,28
	.byte	'CLR',0,1
	.word	532
	.byte	1,7,2,35,1,28
	.byte	'DIS',0,1
	.word	532
	.byte	1,6,2,35,1,28
	.byte	'reserved_10',0,4
	.word	1457
	.byte	22,0,2,35,0,0,25
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,14,130,4,3
	.word	34465
	.byte	27
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,14,133,4,16,4,28
	.byte	'VLD0',0,1
	.word	532
	.byte	1,7,2,35,0,28
	.byte	'reserved_1',0,4
	.word	1457
	.byte	31,0,2,35,0,0,25
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,14,137,4,3
	.word	34620
	.byte	27
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,14,140,4,16,4,28
	.byte	'reserved_0',0,1
	.word	532
	.byte	5,3,2,35,0,28
	.byte	'ADDR',0,4
	.word	1457
	.byte	19,8,2,35,0,28
	.byte	'ERR',0,1
	.word	532
	.byte	6,2,2,35,3,28
	.byte	'VLD',0,1
	.word	532
	.byte	1,1,2,35,3,28
	.byte	'CLR',0,1
	.word	532
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,14,147,4,3
	.word	34725
	.byte	29,14,155,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	27130
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_ACCEN0',0,14,160,4,3
	.word	34873
	.byte	29,14,163,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	27691
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_ACCEN1',0,14,168,4,3
	.word	34939
	.byte	29,14,171,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	27772
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_CBAB_CFG',0,14,176,4,3
	.word	35005
	.byte	29,14,179,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	27925
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_CBAB_STAT',0,14,184,4,3
	.word	35073
	.byte	29,14,187,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	28173
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_CBAB_TOP',0,14,192,4,3
	.word	35142
	.byte	29,14,195,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	28319
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_COMM0',0,14,200,4,3
	.word	35210
	.byte	29,14,203,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	28417
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_COMM1',0,14,208,4,3
	.word	35275
	.byte	29,14,211,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	28533
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_COMM2',0,14,216,4,3
	.word	35340
	.byte	29,14,219,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	28649
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_ECCRD',0,14,224,4,3
	.word	35405
	.byte	29,14,227,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	28789
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_ECCRP',0,14,232,4,3
	.word	35470
	.byte	29,14,235,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	28929
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_ECCW',0,14,240,4,3
	.word	35535
	.byte	29,14,243,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	29068
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_FCON',0,14,248,4,3
	.word	35599
	.byte	29,14,251,4,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	29430
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_FPRO',0,14,128,5,3
	.word	35663
	.byte	29,14,131,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	29871
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_FSR',0,14,136,5,3
	.word	35727
	.byte	29,14,139,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	30477
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_ID',0,14,144,5,3
	.word	35790
	.byte	29,14,147,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	30588
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_MARD',0,14,152,5,3
	.word	35852
	.byte	29,14,155,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	30802
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_MARP',0,14,160,5,3
	.word	35916
	.byte	29,14,163,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	30989
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCOND',0,14,168,5,3
	.word	35980
	.byte	29,14,171,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	31313
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCONDBG',0,14,176,5,3
	.word	36047
	.byte	29,14,179,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	31456
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCONHSM',0,14,184,5,3
	.word	36116
	.byte	29,14,187,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	31645
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,14,192,5,3
	.word	36185
	.byte	29,14,195,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	32008
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCONOTP',0,14,200,5,3
	.word	36258
	.byte	29,14,203,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	32603
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCONP',0,14,208,5,3
	.word	36327
	.byte	29,14,211,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	33127
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_PROCONWOP',0,14,216,5,3
	.word	36394
	.byte	29,14,219,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	33709
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_RDB_CFG0',0,14,224,5,3
	.word	36463
	.byte	29,14,227,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	33811
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_RDB_CFG1',0,14,232,5,3
	.word	36531
	.byte	29,14,235,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	33913
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_RDB_CFG2',0,14,240,5,3
	.word	36599
	.byte	29,14,243,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	34015
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_RRAD',0,14,248,5,3
	.word	36667
	.byte	29,14,251,5,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	34109
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_RRCT',0,14,128,6,3
	.word	36731
	.byte	29,14,131,6,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	34319
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_RRD0',0,14,136,6,3
	.word	36795
	.byte	29,14,139,6,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	34392
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_RRD1',0,14,144,6,3
	.word	36859
	.byte	29,14,147,6,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	34465
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_UBAB_CFG',0,14,152,6,3
	.word	36923
	.byte	29,14,155,6,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	34620
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_UBAB_STAT',0,14,160,6,3
	.word	36991
	.byte	29,14,163,6,9,4,30
	.byte	'U',0
	.word	1457
	.byte	4,2,35,0,30
	.byte	'I',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'B',0
	.word	34725
	.byte	4,2,35,0,0,25
	.byte	'Ifx_FLASH_UBAB_TOP',0,14,168,6,3
	.word	37060
	.byte	27
	.byte	'_Ifx_FLASH_CBAB',0,14,179,6,25,12,30
	.byte	'CFG',0
	.word	35005
	.byte	4,2,35,0,30
	.byte	'STAT',0
	.word	35073
	.byte	4,2,35,4,30
	.byte	'TOP',0
	.word	35142
	.byte	4,2,35,8,0,23
	.word	37128
	.byte	25
	.byte	'Ifx_FLASH_CBAB',0,14,184,6,3
	.word	37191
	.byte	27
	.byte	'_Ifx_FLASH_RDB',0,14,187,6,25,12,30
	.byte	'CFG0',0
	.word	36463
	.byte	4,2,35,0,30
	.byte	'CFG1',0
	.word	36531
	.byte	4,2,35,4,30
	.byte	'CFG2',0
	.word	36599
	.byte	4,2,35,8,0,23
	.word	37220
	.byte	25
	.byte	'Ifx_FLASH_RDB',0,14,192,6,3
	.word	37284
	.byte	27
	.byte	'_Ifx_FLASH_UBAB',0,14,195,6,25,12,30
	.byte	'CFG',0
	.word	36923
	.byte	4,2,35,0,30
	.byte	'STAT',0
	.word	36991
	.byte	4,2,35,4,30
	.byte	'TOP',0
	.word	37060
	.byte	4,2,35,8,0,23
	.word	37312
	.byte	25
	.byte	'Ifx_FLASH_UBAB',0,14,200,6,3
	.word	37375
	.byte	11,3,72,9,1,12
	.byte	'IfxFlash_ErrorTracking_none',0,0,12
	.byte	'IfxFlash_ErrorTracking_correctedSingleBitError',0,1,12
	.byte	'IfxFlash_ErrorTracking_correctedDoubleBitError',0,2,12
	.byte	'IfxFlash_ErrorTracking_correctedSingleOrDoubleBitError',0,3,12
	.byte	'IfxFlash_ErrorTracking_uncorrectableMultiBitError',0,4,0,25
	.byte	'IfxFlash_ErrorTracking',0,3,79,3
	.word	37404
	.byte	25
	.byte	'_iob_flag_t',0,15,82,25
	.word	1135
	.byte	7
	.byte	'char',0,1,6,25
	.byte	'int8',0,16,54,29
	.word	37698
	.byte	25
	.byte	'int16',0,16,55,29
	.word	1629
	.byte	25
	.byte	'int32',0,16,56,29
	.word	1450
	.byte	25
	.byte	'int64',0,16,57,29
	.word	2016
	.byte	29,17,52,9,4,30
	.byte	'float_type',0
	.word	263
	.byte	4,2,35,0,30
	.byte	'uint32_type',0
	.word	435
	.byte	4,2,35,0,30
	.byte	'int32_type',0
	.word	1450
	.byte	4,2,35,0,30
	.byte	'uint16_type',0
	.word	1135
	.byte	2,2,35,0,30
	.byte	'int16_type',0
	.word	1629
	.byte	2,2,35,0,30
	.byte	'uint8_type',0
	.word	532
	.byte	1,2,35,0,30
	.byte	'int8_type',0
	.word	37698
	.byte	1,2,35,0,0,25
	.byte	'flash_data_union',0,17,61,2
	.word	37761
.L213:
	.byte	32,128,32
	.word	37761
	.byte	33,255,7,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,11,1,0,0,11,4,1,58,15
	.byte	59,15,57,15,11,15,0,0,12,40,0,3,8,28,13,0,0,13,46,0,3,8,54,15,39,12,63,12,60,12,0,0,14,46,1,49,19,0,0
	.byte	15,5,0,49,19,0,0,16,29,1,49,19,0,0,17,11,0,49,19,0,0,18,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60
	.byte	12,0,0,19,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,20,11,1,49,19,0,0,21,46,1,3,8,58
	.byte	15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,22,5,0,58,15,59,15,57,15,73,19,0,0,23,53,0,73,19,0,0
	.byte	24,38,0,73,19,0,0,25,22,0,3,8,58,15,59,15,57,15,73,19,0,0,26,21,0,54,15,0,0,27,19,1,3,8,58,15,59,15,57
	.byte	15,11,15,0,0,28,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,29,23,1,58,15,59,15,57,15,11,15,0,0,30,13,0
	.byte	3,8,73,19,11,15,56,9,0,0,31,19,1,58,15,59,15,57,15,11,15,0,0,32,1,1,11,15,73,19,0,0,33,33,0,47,15,0,0
	.byte	34,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L58:
	.word	.L278-.L277
.L277:
	.half	3
	.word	.L280-.L279
.L279:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IfxFlash.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxFlash_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScuWdt.h',0,2,0,0
	.byte	'string.h',0,3,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'stddef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,5,0,0
	.byte	'IfxFlash_regdef.h',0,5,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_flash.h',0,0,0,0,0
.L280:
.L278:
	.sdecl	'.debug_info',debug,cluster('flash_check')
	.sect	'.debug_info'
.L59:
	.word	349
	.half	3
	.word	.L60
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L62,.L61
	.byte	2
	.word	.L55
	.byte	3
	.byte	'flash_check',0,1,56,7
	.word	.L98
	.byte	1,1,1
	.word	.L42,.L99,.L41
	.byte	4
	.byte	'sector_num',0,1,56,27
	.word	.L100,.L101
	.byte	4
	.byte	'page_num',0,1,56,46
	.word	.L100,.L102
	.byte	5
	.word	.L42,.L99
	.byte	5
	.word	.L103,.L99
	.byte	6
	.byte	'sector_addr',0,1,60,12
	.word	.L100,.L104
	.byte	6
	.byte	'num',0,1,62,12
	.word	.L100,.L105
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('flash_check')
	.sect	'.debug_abbrev'
.L60:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('flash_check')
	.sect	'.debug_line'
.L61:
	.word	.L282-.L281
.L281:
	.half	3
	.word	.L284-.L283
.L283:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0,0
.L284:
	.byte	5,7,7,0,5,2
	.word	.L42
	.byte	3,55,1,5,5,9
	.half	.L216-.L42
	.byte	3,2,1,5,52,9
	.half	.L103-.L216
	.byte	3,2,1,5,26,9
	.half	.L217-.L103
	.byte	1,5,52,9
	.half	.L285-.L217
	.byte	1,5,62,9
	.half	.L286-.L285
	.byte	1,5,13,9
	.half	.L218-.L286
	.byte	3,4,1,5,100,9
	.half	.L219-.L218
	.byte	1,5,106,9
	.half	.L3-.L219
	.byte	1,5,24,9
	.half	.L2-.L3
	.byte	1,5,18,9
	.half	.L287-.L2
	.byte	1,5,79,7,9
	.half	.L288-.L287
	.byte	1,5,70,9
	.half	.L289-.L288
	.byte	1,5,47,9
	.half	.L290-.L289
	.byte	1,5,46,9
	.half	.L291-.L290
	.byte	1,5,96,9
	.half	.L292-.L291
	.byte	1,5,19,7,9
	.half	.L4-.L292
	.byte	3,2,1,5,12,9
	.half	.L293-.L4
	.byte	1,5,38,7,9
	.half	.L294-.L293
	.byte	1,5,42,9
	.half	.L295-.L294
	.byte	1,5,38,9
	.half	.L5-.L295
	.byte	1,5,5,9
	.half	.L6-.L5
	.byte	1,5,1,9
	.half	.L7-.L6
	.byte	3,1,1,7,9
	.half	.L63-.L7
	.byte	0,1,1
.L282:
	.sdecl	'.debug_ranges',debug,cluster('flash_check')
	.sect	'.debug_ranges'
.L62:
	.word	-1,.L42,0,.L63-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('flash_erase_page')
	.sect	'.debug_info'
.L64:
	.word	539
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L67,.L66
	.byte	2
	.word	.L55
	.byte	3
	.byte	'flash_erase_page',0,1,77,6,1,1,1
	.word	.L44,.L106,.L43
	.byte	4
	.byte	'sector_num',0,1,77,31
	.word	.L100,.L107
	.byte	4
	.byte	'page_num',0,1,77,50
	.word	.L100,.L108
	.byte	5
	.word	.L44,.L106
	.byte	5
	.word	.L109,.L106
	.byte	6
	.byte	'flash',0,1,81,12
	.word	.L100,.L110
	.byte	6
	.byte	'end_init_sfty_pw',0,1,82,12
	.word	.L111,.L112
	.byte	6
	.byte	'sector_addr',0,1,83,12
	.word	.L100,.L113
	.byte	7
	.word	.L114,.L115,.L116
	.byte	8
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L115,.L116
	.byte	6
	.byte	'addr1',0,2,199,4,22
	.word	.L120,.L121
	.byte	6
	.byte	'addr2',0,2,200,4,22
	.word	.L120,.L122
	.byte	6
	.byte	'addr3',0,2,201,4,22
	.word	.L120,.L123
	.byte	6
	.byte	'addr4',0,2,202,4,22
	.word	.L120,.L124
	.byte	0,0,7
	.word	.L125,.L126,.L12
	.byte	8
	.word	.L127,.L128
	.byte	8
	.word	.L129,.L130
	.byte	10
	.word	.L131,.L126,.L12
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('flash_erase_page')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('flash_erase_page')
	.sect	'.debug_line'
.L66:
	.word	.L297-.L296
.L296:
	.half	3
	.word	.L299-.L298
.L298:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0
	.byte	'IfxFlash.h',0,1,0,0,0
.L299:
	.byte	5,6,7,0,5,2
	.word	.L44
	.byte	3,204,0,1,5,5,9
	.half	.L222-.L44
	.byte	3,2,1,5,18,9
	.half	.L109-.L222
	.byte	3,2,1,5,52,9
	.half	.L224-.L109
	.byte	3,2,1,5,26,9
	.half	.L300-.L224
	.byte	1,5,52,9
	.half	.L301-.L300
	.byte	1,5,62,9
	.half	.L302-.L301
	.byte	1,5,61,9
	.half	.L223-.L302
	.byte	3,2,1,5,24,9
	.half	.L225-.L223
	.byte	1,5,34,9
	.half	.L227-.L225
	.byte	3,2,1,4,2,5,30,9
	.half	.L115-.L227
	.byte	3,240,3,1,9
	.half	.L303-.L115
	.byte	3,1,1,9
	.half	.L304-.L303
	.byte	3,1,1,9
	.half	.L305-.L304
	.byte	3,1,1,5,12,9
	.half	.L306-.L305
	.byte	3,2,1,5,14,9
	.half	.L307-.L306
	.byte	3,1,1,5,12,9
	.half	.L308-.L307
	.byte	1,5,14,9
	.half	.L309-.L308
	.byte	3,1,1,5,12,9
	.half	.L310-.L309
	.byte	1,5,14,9
	.half	.L311-.L310
	.byte	3,1,1,5,12,9
	.half	.L312-.L311
	.byte	1,9
	.half	.L313-.L312
	.byte	3,2,1,4,1,5,34,9
	.half	.L116-.L313
	.byte	3,136,124,1,5,32,9
	.half	.L230-.L116
	.byte	3,2,1,4,2,5,5,9
	.half	.L126-.L230
	.byte	3,235,4,1,5,47,7,9
	.half	.L314-.L126
	.byte	3,2,1,5,26,9
	.half	.L9-.L314
	.byte	1,5,32,9
	.half	.L315-.L9
	.byte	1,5,34,9
	.half	.L316-.L315
	.byte	1,5,29,9
	.half	.L317-.L316
	.byte	1,5,47,9
	.half	.L318-.L317
	.byte	1,7,9
	.half	.L319-.L318
	.byte	1,5,9,9
	.half	.L8-.L319
	.byte	3,13,1,5,12,9
	.half	.L11-.L8
	.byte	3,2,1,5,5,9
	.half	.L320-.L11
	.byte	3,1,1,4,1,9
	.half	.L12-.L320
	.byte	3,133,123,1,5,29,9
	.half	.L321-.L12
	.byte	1,5,27,9
	.half	.L322-.L321
	.byte	1,5,1,9
	.half	.L323-.L322
	.byte	3,1,1,7,9
	.half	.L68-.L323
	.byte	0,1,1
.L297:
	.sdecl	'.debug_ranges',debug,cluster('flash_erase_page')
	.sect	'.debug_ranges'
.L67:
	.word	-1,.L44,0,.L68-.L44,0,0
	.sdecl	'.debug_info',debug,cluster('flash_read_page')
	.sect	'.debug_info'
.L69:
	.word	353
	.half	3
	.word	.L70
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L72,.L71
	.byte	2
	.word	.L55
	.byte	3
	.byte	'flash_read_page',0,1,106,6,1,1,1
	.word	.L46,.L132,.L45
	.byte	4
	.byte	'sector_num',0,1,106,29
	.word	.L100,.L133
	.byte	4
	.byte	'page_num',0,1,106,48
	.word	.L100,.L134
	.byte	4
	.byte	'buf',0,1,106,66
	.word	.L135,.L136
	.byte	4
	.byte	'len',0,1,106,78
	.word	.L111,.L137
	.byte	5
	.word	.L46,.L132
	.byte	6
	.byte	'data_cont',0,1,108,12
	.word	.L100,.L138
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('flash_read_page')
	.sect	'.debug_abbrev'
.L70:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('flash_read_page')
	.sect	'.debug_line'
.L71:
	.word	.L325-.L324
.L324:
	.half	3
	.word	.L327-.L326
.L326:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0,0
.L327:
	.byte	5,6,7,0,5,2
	.word	.L46
	.byte	3,233,0,1,5,5,9
	.half	.L240-.L46
	.byte	3,3,1,9
	.half	.L235-.L240
	.byte	3,1,1,5,19,9
	.half	.L328-.L235
	.byte	3,2,1,5,39,9
	.half	.L241-.L328
	.byte	1,5,96,9
	.half	.L15-.L241
	.byte	3,2,1,5,63,9
	.half	.L329-.L15
	.byte	1,5,33,9
	.half	.L330-.L329
	.byte	1,5,50,9
	.half	.L331-.L330
	.byte	1,5,81,9
	.half	.L332-.L331
	.byte	1,5,21,9
	.half	.L333-.L332
	.byte	1,5,19,9
	.half	.L334-.L333
	.byte	1,5,17,9
	.half	.L335-.L334
	.byte	1,5,14,9
	.half	.L336-.L335
	.byte	1,5,51,9
	.half	.L337-.L336
	.byte	3,126,1,5,39,9
	.half	.L14-.L337
	.byte	1,5,1,7,9
	.half	.L338-.L14
	.byte	3,4,1,7,9
	.half	.L73-.L338
	.byte	0,1,1
.L325:
	.sdecl	'.debug_ranges',debug,cluster('flash_read_page')
	.sect	'.debug_ranges'
.L72:
	.word	-1,.L46,0,.L73-.L46,0,0
	.sdecl	'.debug_info',debug,cluster('flash_write_page')
	.sect	'.debug_info'
.L74:
	.word	913
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L77,.L76
	.byte	2
	.word	.L55
	.byte	3
	.byte	'flash_write_page',0,1,128,1,6,1,1,1
	.word	.L48,.L139,.L47
	.byte	4
	.byte	'sector_num',0,1,128,1,31
	.word	.L100,.L140
	.byte	4
	.byte	'page_num',0,1,128,1,50
	.word	.L100,.L141
	.byte	4
	.byte	'buf',0,1,128,1,74
	.word	.L142,.L143
	.byte	4
	.byte	'len',0,1,128,1,86
	.word	.L111,.L144
	.byte	5
	.word	.L48,.L139
	.byte	5
	.word	.L145,.L139
	.byte	6
	.byte	'end_init_sfty_pw',0,1,133,1,12
	.word	.L111,.L146
	.byte	6
	.byte	'flash_addr',0,1,134,1,12
	.word	.L100,.L147
	.byte	6
	.byte	'data_addr',0,1,135,1,12
	.word	.L100,.L148
	.byte	6
	.byte	'data_cont',0,1,136,1,12
	.word	.L100,.L149
	.byte	7
	.word	.L150,.L151,.L21
	.byte	8
	.word	.L152,.L153
	.byte	9
	.word	.L154,.L151,.L21
	.byte	6
	.byte	'addr1',0,2,131,4,22
	.word	.L120,.L155
	.byte	0,0,7
	.word	.L125,.L156,.L29
	.byte	8
	.word	.L127,.L157
	.byte	8
	.word	.L129,.L158
	.byte	10
	.word	.L131,.L156,.L29
	.byte	0,7
	.word	.L159,.L160,.L161
	.byte	8
	.word	.L162,.L163
	.byte	8
	.word	.L164,.L165
	.byte	8
	.word	.L166,.L167
	.byte	9
	.word	.L168,.L160,.L161
	.byte	9
	.word	.L169,.L31,.L161
	.byte	6
	.byte	'addr1',0,2,248,4,13
	.word	.L170,.L171
	.byte	7
	.word	.L172,.L173,.L174
	.byte	8
	.word	.L175,.L176
	.byte	8
	.word	.L177,.L178
	.byte	8
	.word	.L179,.L180
	.byte	9
	.word	.L181,.L173,.L174
	.byte	6
	.byte	'value',0,3,164,1,21
	.word	.L182,.L183
	.byte	7
	.word	.L184,.L185,.L174
	.byte	8
	.word	.L186,.L187
	.byte	8
	.word	.L188,.L189
	.byte	10
	.word	.L190,.L185,.L174
	.byte	0,0,0,0,0,0,7
	.word	.L191,.L192,.L193
	.byte	8
	.word	.L194,.L195
	.byte	9
	.word	.L196,.L192,.L193
	.byte	6
	.byte	'addr1',0,2,238,5,22
	.word	.L120,.L197
	.byte	6
	.byte	'addr2',0,2,239,5,22
	.word	.L120,.L198
	.byte	6
	.byte	'addr3',0,2,240,5,22
	.word	.L120,.L199
	.byte	6
	.byte	'addr4',0,2,241,5,22
	.word	.L120,.L200
	.byte	0,0,7
	.word	.L125,.L201,.L36
	.byte	8
	.word	.L127,.L157
	.byte	8
	.word	.L129,.L158
	.byte	10
	.word	.L131,.L201,.L36
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('flash_write_page')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('flash_write_page')
	.sect	'.debug_line'
.L76:
	.word	.L340-.L339
.L339:
	.half	3
	.word	.L342-.L341
.L341:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0
	.byte	'IfxFlash.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0,0
.L342:
	.byte	5,6,7,0,5,2
	.word	.L48
	.byte	3,255,0,1,5,5,9
	.half	.L247-.L48
	.byte	3,2,1,9
	.half	.L243-.L247
	.byte	3,1,1,5,51,9
	.half	.L145-.L243
	.byte	3,3,1,5,25,9
	.half	.L248-.L145
	.byte	1,5,51,9
	.half	.L343-.L248
	.byte	1,5,61,9
	.half	.L344-.L343
	.byte	1,9
	.half	.L249-.L344
	.byte	3,3,1,5,24,9
	.half	.L250-.L249
	.byte	1,5,8,9
	.half	.L252-.L250
	.byte	3,2,1,5,62,9
	.half	.L253-.L252
	.byte	1,5,49,9
	.half	.L251-.L253
	.byte	1,5,38,7,9
	.half	.L255-.L251
	.byte	3,2,1,5,19,9
	.half	.L16-.L255
	.byte	3,3,1,5,39,9
	.half	.L257-.L16
	.byte	1,5,47,9
	.half	.L19-.L257
	.byte	3,2,1,5,33,9
	.half	.L345-.L19
	.byte	1,4,2,5,30,9
	.half	.L151-.L345
	.byte	3,241,2,1,5,19,9
	.half	.L346-.L151
	.byte	3,2,1,5,36,9
	.half	.L347-.L346
	.byte	1,5,5,9
	.half	.L348-.L347
	.byte	1,5,18,7,9
	.half	.L349-.L348
	.byte	3,2,1,5,16,9
	.half	.L350-.L349
	.byte	1,5,9,9
	.half	.L351-.L350
	.byte	3,1,1,5,24,9
	.half	.L20-.L351
	.byte	3,2,1,5,41,9
	.half	.L352-.L20
	.byte	1,5,10,9
	.half	.L353-.L352
	.byte	1,5,18,7,9
	.half	.L354-.L353
	.byte	3,2,1,5,16,9
	.half	.L355-.L354
	.byte	1,5,9,9
	.half	.L356-.L355
	.byte	3,1,1,5,12,9
	.half	.L22-.L356
	.byte	3,3,1,5,5,9
	.half	.L357-.L22
	.byte	3,1,1,4,1,5,9,9
	.half	.L21-.L357
	.byte	3,131,125,1,5,29,9
	.half	.L358-.L21
	.byte	3,2,1,5,32,9
	.half	.L359-.L358
	.byte	1,4,2,5,5,9
	.half	.L156-.L359
	.byte	3,176,4,1,5,47,7,9
	.half	.L360-.L156
	.byte	3,2,1,5,26,9
	.half	.L26-.L360
	.byte	1,5,32,9
	.half	.L361-.L26
	.byte	1,5,34,9
	.half	.L362-.L361
	.byte	1,5,29,9
	.half	.L363-.L362
	.byte	1,5,47,9
	.half	.L364-.L363
	.byte	1,7,9
	.half	.L365-.L364
	.byte	1,5,9,9
	.half	.L25-.L365
	.byte	3,13,1,5,12,9
	.half	.L28-.L25
	.byte	3,2,1,5,5,9
	.half	.L366-.L28
	.byte	3,1,1,4,1,5,38,9
	.half	.L29-.L366
	.byte	3,192,123,1,5,43,9
	.half	.L367-.L29
	.byte	1,5,47,9
	.half	.L368-.L367
	.byte	1,4,2,5,5,9
	.half	.L160-.L368
	.byte	3,223,3,1,5,21,7,9
	.half	.L31-.L160
	.byte	3,1,1,4,3,5,49,9
	.half	.L173-.L31
	.byte	3,172,124,1,5,58,9
	.half	.L369-.L173
	.byte	1,5,56,9
	.half	.L370-.L369
	.byte	1,5,30,9
	.half	.L185-.L370
	.byte	3,109,1,4,2,5,12,9
	.half	.L174-.L185
	.byte	3,235,3,1,4,1,5,38,9
	.half	.L161-.L174
	.byte	3,158,124,1,4,2,5,30,9
	.half	.L192-.L161
	.byte	3,212,4,1,9
	.half	.L371-.L192
	.byte	3,1,1,9
	.half	.L372-.L371
	.byte	3,1,1,9
	.half	.L373-.L372
	.byte	3,1,1,5,12,9
	.half	.L374-.L373
	.byte	3,2,1,5,14,9
	.half	.L375-.L374
	.byte	3,1,1,5,12,9
	.half	.L376-.L375
	.byte	1,5,14,9
	.half	.L377-.L376
	.byte	3,1,1,5,12,9
	.half	.L378-.L377
	.byte	1,5,14,9
	.half	.L379-.L378
	.byte	3,1,1,5,12,9
	.half	.L380-.L379
	.byte	1,9
	.half	.L381-.L380
	.byte	3,2,1,4,1,5,38,9
	.half	.L193-.L381
	.byte	3,164,123,1,5,29,9
	.half	.L264-.L193
	.byte	3,2,1,5,32,9
	.half	.L382-.L264
	.byte	1,4,2,5,5,9
	.half	.L201-.L382
	.byte	3,168,4,1,5,47,7,9
	.half	.L383-.L201
	.byte	3,2,1,5,26,9
	.half	.L33-.L383
	.byte	1,5,32,9
	.half	.L384-.L33
	.byte	1,5,34,9
	.half	.L385-.L384
	.byte	1,5,29,9
	.half	.L386-.L385
	.byte	1,5,47,9
	.half	.L387-.L386
	.byte	1,7,9
	.half	.L388-.L387
	.byte	1,5,9,9
	.half	.L32-.L388
	.byte	3,13,1,5,12,9
	.half	.L35-.L32
	.byte	3,2,1,5,5,9
	.half	.L389-.L35
	.byte	3,1,1,4,1,5,51,9
	.half	.L36-.L389
	.byte	3,184,123,1,5,39,9
	.half	.L18-.L36
	.byte	1,5,5,7,9
	.half	.L390-.L18
	.byte	3,17,1,5,29,9
	.half	.L391-.L390
	.byte	1,5,27,9
	.half	.L392-.L391
	.byte	1,5,1,9
	.half	.L393-.L392
	.byte	3,1,1,7,9
	.half	.L78-.L393
	.byte	0,1,1
.L340:
	.sdecl	'.debug_ranges',debug,cluster('flash_write_page')
	.sect	'.debug_ranges'
.L77:
	.word	-1,.L48,0,.L78-.L48,0,0
	.sdecl	'.debug_info',debug,cluster('flash_read_page_to_buffer')
	.sect	'.debug_info'
.L79:
	.word	369
	.half	3
	.word	.L80
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L82,.L81
	.byte	2
	.word	.L55
	.byte	3
	.byte	'flash_read_page_to_buffer',0,1,172,1,6,1,1,1
	.word	.L50,.L202,.L49
	.byte	4
	.byte	'sector_num',0,1,172,1,40
	.word	.L100,.L203
	.byte	4
	.byte	'page_num',0,1,172,1,59
	.word	.L100,.L204
	.byte	5
	.word	.L50,.L202
	.byte	6
	.byte	'data_cont',0,1,174,1,12
	.word	.L100,.L205
	.byte	5
	.word	.L206,.L202
	.byte	6
	.byte	'flash_addr',0,1,177,1,12
	.word	.L100,.L207
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('flash_read_page_to_buffer')
	.sect	'.debug_abbrev'
.L80:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('flash_read_page_to_buffer')
	.sect	'.debug_line'
.L81:
	.word	.L395-.L394
.L394:
	.half	3
	.word	.L397-.L396
.L396:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0,0
.L397:
	.byte	5,6,7,0,5,2
	.word	.L50
	.byte	3,171,1,1,5,5,9
	.half	.L270-.L50
	.byte	3,3,1,5,51,9
	.half	.L206-.L270
	.byte	3,2,1,5,25,9
	.half	.L271-.L206
	.byte	1,5,51,9
	.half	.L398-.L271
	.byte	1,5,61,9
	.half	.L399-.L398
	.byte	1,5,19,9
	.half	.L272-.L399
	.byte	3,2,1,5,54,9
	.half	.L273-.L272
	.byte	1,5,27,9
	.half	.L39-.L273
	.byte	3,2,1,5,9,9
	.half	.L400-.L39
	.byte	1,5,27,9
	.half	.L401-.L400
	.byte	1,5,92,9
	.half	.L402-.L401
	.byte	1,5,77,9
	.half	.L403-.L402
	.byte	1,5,55,9
	.half	.L404-.L403
	.byte	1,5,53,9
	.half	.L405-.L404
	.byte	1,5,51,9
	.half	.L406-.L405
	.byte	1,5,66,9
	.half	.L407-.L406
	.byte	3,126,1,5,36,9
	.half	.L38-.L407
	.byte	1,5,54,9
	.half	.L408-.L38
	.byte	1,5,1,7,9
	.half	.L409-.L408
	.byte	3,4,1,7,9
	.half	.L83-.L409
	.byte	0,1,1
.L395:
	.sdecl	'.debug_ranges',debug,cluster('flash_read_page_to_buffer')
	.sect	'.debug_ranges'
.L82:
	.word	-1,.L50,0,.L83-.L50,0,0
	.sdecl	'.debug_info',debug,cluster('flash_write_page_from_buffer')
	.sect	'.debug_info'
.L84:
	.word	345
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L87,.L86
	.byte	2
	.word	.L55
	.byte	3
	.byte	'flash_write_page_from_buffer',0,1,193,1,7
	.word	.L98
	.byte	1,1,1
	.word	.L52,.L208,.L51
	.byte	4
	.byte	'sector_num',0,1,193,1,44
	.word	.L100,.L209
	.byte	4
	.byte	'page_num',0,1,193,1,63
	.word	.L100,.L210
	.byte	5
	.word	.L52,.L208
	.byte	6
	.byte	'data_pointer',0,1,195,1,13
	.word	.L135,.L211
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('flash_write_page_from_buffer')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('flash_write_page_from_buffer')
	.sect	'.debug_line'
.L86:
	.word	.L411-.L410
.L410:
	.half	3
	.word	.L413-.L412
.L412:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0,0
.L413:
	.byte	5,38,7,0,5,2
	.word	.L52
	.byte	3,194,1,1,5,22,9
	.half	.L414-.L52
	.byte	3,2,1,5,49,9
	.half	.L275-.L414
	.byte	1,5,12,9
	.half	.L274-.L275
	.byte	3,2,1,5,5,9
	.half	.L415-.L274
	.byte	1,5,1,9
	.half	.L40-.L415
	.byte	3,1,1,7,9
	.half	.L88-.L40
	.byte	0,1,1
.L411:
	.sdecl	'.debug_ranges',debug,cluster('flash_write_page_from_buffer')
	.sect	'.debug_ranges'
.L87:
	.word	-1,.L52,0,.L88-.L52,0,0
	.sdecl	'.debug_info',debug,cluster('flash_buffer_clear')
	.sect	'.debug_info'
.L89:
	.word	258
	.half	3
	.word	.L90
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L92,.L91
	.byte	2
	.word	.L55
	.byte	3
	.byte	'flash_buffer_clear',0,1,209,1,6,1,1,1
	.word	.L54,.L212,.L53
	.byte	4
	.word	.L54,.L212
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('flash_buffer_clear')
	.sect	'.debug_abbrev'
.L90:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('flash_buffer_clear')
	.sect	'.debug_line'
.L91:
	.word	.L417-.L416
.L416:
	.half	3
	.word	.L419-.L418
.L418:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0,0,0,0,0
.L419:
	.byte	5,12,7,0,5,2
	.word	.L54
	.byte	3,210,1,1,5,32,9
	.half	.L420-.L54
	.byte	1,5,38,9
	.half	.L421-.L420
	.byte	1,5,1,9
	.half	.L422-.L421
	.byte	3,1,1,7,9
	.half	.L93-.L422
	.byte	0,1,1
.L417:
	.sdecl	'.debug_ranges',debug,cluster('flash_buffer_clear')
	.sect	'.debug_ranges'
.L92:
	.word	-1,.L54,0,.L93-.L54,0,0
	.sdecl	'.debug_info',debug,cluster('flash_union_buffer')
	.sect	'.debug_info'
.L94:
	.word	235
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L55
	.byte	3
	.byte	'flash_union_buffer',0,8,45,18
	.word	.L213
	.byte	1,5,3
	.word	flash_union_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('flash_union_buffer')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('flash_erase_page_flag')
	.sect	'.debug_info'
.L96:
	.word	237
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_flash.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L55
	.byte	3
	.byte	'flash_erase_page_flag',0,8,46,18
	.word	.L98
	.byte	5,3
	.word	flash_erase_page_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('flash_erase_page_flag')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('flash_buffer_clear')
	.sect	'.debug_loc'
.L53:
	.word	-1,.L54,0,.L212-.L54
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('flash_check')
	.sect	'.debug_loc'
.L41:
	.word	-1,.L42,0,.L99-.L42
	.half	2
	.byte	138,0
	.word	0,0
.L105:
	.word	-1,.L42,.L219-.L42,.L99-.L42
	.half	1
	.byte	81
	.word	0,0
.L102:
	.word	-1,.L42,0,.L214-.L42
	.half	1
	.byte	85
	.word	.L216-.L42,.L217-.L42
	.half	1
	.byte	95
	.word	0,0
.L104:
	.word	-1,.L42,.L218-.L42,.L99-.L42
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L101:
	.word	-1,.L42,0,.L215-.L42
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('flash_erase_page')
	.sect	'.debug_loc'
.L121:
	.word	-1,.L44,.L228-.L44,.L9-.L44
	.half	1
	.byte	111
	.word	.L8-.L44,.L11-.L44
	.half	1
	.byte	111
	.word	0,0
.L122:
	.word	-1,.L44,.L229-.L44,.L230-.L44
	.half	1
	.byte	98
	.word	0,0
.L123:
	.word	-1,.L44,.L231-.L44,.L230-.L44
	.half	1
	.byte	100
	.word	0,0
.L124:
	.word	-1,.L44,.L232-.L44,.L230-.L44
	.half	1
	.byte	101
	.word	0,0
.L112:
	.word	-1,.L44,.L225-.L44,.L226-.L44
	.half	1
	.byte	82
	.word	.L227-.L44,.L106-.L44
	.half	1
	.byte	89
	.word	.L226-.L44,.L115-.L44
	.half	1
	.byte	84
	.word	.L233-.L44,.L230-.L44
	.half	1
	.byte	84
	.word	0,0
.L110:
	.word	-1,.L44,.L224-.L44,.L106-.L44
	.half	1
	.byte	90
	.word	0,0
.L128:
	.word	0,0
.L130:
	.word	0,0
.L43:
	.word	-1,.L44,0,.L106-.L44
	.half	2
	.byte	138,0
	.word	0,0
.L108:
	.word	-1,.L44,0,.L220-.L44
	.half	1
	.byte	85
	.word	.L222-.L44,.L223-.L44
	.half	1
	.byte	88
	.word	0,0
.L118:
	.word	0,0
.L113:
	.word	-1,.L44,.L223-.L44,.L106-.L44
	.half	1
	.byte	88
	.word	0,0
.L107:
	.word	-1,.L44,0,.L221-.L44
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('flash_read_page')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L46,0,.L234-.L46
	.half	1
	.byte	100
	.word	.L239-.L46,.L132-.L46
	.half	1
	.byte	111
	.word	0,0
.L138:
	.word	-1,.L46,.L241-.L46,.L132-.L46
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L45:
	.word	-1,.L46,0,.L132-.L46
	.half	2
	.byte	138,0
	.word	0,0
.L137:
	.word	-1,.L46,0,.L235-.L46
	.half	1
	.byte	86
	.word	.L240-.L46,.L132-.L46
	.half	1
	.byte	89
	.word	0,0
.L134:
	.word	-1,.L46,0,.L236-.L46
	.half	1
	.byte	85
	.word	.L238-.L46,.L132-.L46
	.half	1
	.byte	88
	.word	0,0
.L133:
	.word	-1,.L46,0,.L237-.L46
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('flash_read_page_to_buffer')
	.sect	'.debug_loc'
.L205:
	.word	-1,.L50,.L273-.L50,.L202-.L50
	.half	1
	.byte	81
	.word	0,0
.L207:
	.word	-1,.L50,.L272-.L50,.L202-.L50
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L49:
	.word	-1,.L50,0,.L202-.L50
	.half	2
	.byte	138,0
	.word	0,0
.L204:
	.word	-1,.L50,0,.L268-.L50
	.half	1
	.byte	85
	.word	.L270-.L50,.L271-.L50
	.half	1
	.byte	95
	.word	0,0
.L203:
	.word	-1,.L50,0,.L269-.L50
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('flash_write_page')
	.sect	'.debug_loc'
.L187:
	.word	0,0
.L176:
	.word	0,0
.L155:
	.word	-1,.L48,.L258-.L48,.L26-.L48
	.half	1
	.byte	111
	.word	.L25-.L48,.L28-.L48
	.half	1
	.byte	111
	.word	0,0
.L171:
	.word	-1,.L48,.L259-.L48,.L260-.L48
	.half	1
	.byte	111
	.word	0,0
.L197:
	.word	-1,.L48,.L260-.L48,.L33-.L48
	.half	1
	.byte	111
	.word	.L32-.L48,.L35-.L48
	.half	1
	.byte	111
	.word	0,0
.L198:
	.word	-1,.L48,.L263-.L48,.L264-.L48
	.half	1
	.byte	98
	.word	0,0
.L199:
	.word	-1,.L48,.L265-.L48,.L264-.L48
	.half	1
	.byte	100
	.word	0,0
.L200:
	.word	-1,.L48,.L266-.L48,.L264-.L48
	.half	1
	.byte	101
	.word	0,0
.L143:
	.word	-1,.L48,0,.L242-.L48
	.half	1
	.byte	100
	.word	.L246-.L48,.L139-.L48
	.half	1
	.byte	108
	.word	0,0
.L148:
	.word	-1,.L48,.L151-.L48,.L18-.L48
	.half	1
	.byte	89
	.word	0,0
.L149:
	.word	-1,.L48,.L257-.L48,.L139-.L48
	.half	1
	.byte	88
	.word	0,0
.L146:
	.word	-1,.L48,.L250-.L48,.L251-.L48
	.half	1
	.byte	82
	.word	.L252-.L48,.L139-.L48
	.half	1
	.byte	92
	.word	.L262-.L48,.L192-.L48
	.half	1
	.byte	84
	.word	.L267-.L48,.L264-.L48
	.half	1
	.byte	84
	.word	0,0
.L157:
	.word	0,0
.L158:
	.word	0,0
.L147:
	.word	-1,.L48,.L249-.L48,.L139-.L48
	.half	1
	.byte	91
	.word	0,0
.L47:
	.word	-1,.L48,0,.L139-.L48
	.half	2
	.byte	138,0
	.word	0,0
.L144:
	.word	-1,.L48,0,.L243-.L48
	.half	1
	.byte	86
	.word	.L247-.L48,.L139-.L48
	.half	1
	.byte	90
	.word	0,0
.L153:
	.word	0,0
.L163:
	.word	0,0
.L195:
	.word	0,0
.L141:
	.word	-1,.L48,0,.L244-.L48
	.half	1
	.byte	85
	.word	.L247-.L48,.L245-.L48
	.half	1
	.byte	89
	.word	.L145-.L48,.L248-.L48
	.half	1
	.byte	89
	.word	.L253-.L48,.L254-.L48
	.half	1
	.byte	89
	.word	.L255-.L48,.L256-.L48
	.half	1
	.byte	89
	.word	0,0
.L140:
	.word	-1,.L48,0,.L245-.L48
	.half	1
	.byte	84
	.word	.L253-.L48,.L254-.L48
	.half	1
	.byte	88
	.word	.L255-.L48,.L256-.L48
	.half	1
	.byte	88
	.word	0,0
.L189:
	.word	0,0
.L183:
	.word	-1,.L48,.L261-.L48,.L192-.L48
	.half	2
	.byte	144,32
	.word	0,0
.L178:
	.word	0,0
.L180:
	.word	0,0
.L165:
	.word	0,0
.L167:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('flash_write_page_from_buffer')
	.sect	'.debug_loc'
.L211:
	.word	-1,.L52,.L276-.L52,.L274-.L52
	.half	1
	.byte	100
	.word	0,0
.L51:
	.word	-1,.L52,0,.L208-.L52
	.half	2
	.byte	138,0
	.word	0,0
.L210:
	.word	-1,.L52,0,.L274-.L52
	.half	1
	.byte	85
	.word	0,0
.L209:
	.word	-1,.L52,0,.L275-.L52
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L423:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('flash_check')
	.sect	'.debug_frame'
	.word	12
	.word	.L423,.L42,.L99-.L42
	.sdecl	'.debug_frame',debug,cluster('flash_erase_page')
	.sect	'.debug_frame'
	.word	12
	.word	.L423,.L44,.L106-.L44
	.sdecl	'.debug_frame',debug,cluster('flash_read_page')
	.sect	'.debug_frame'
	.word	12
	.word	.L423,.L46,.L132-.L46
	.sdecl	'.debug_frame',debug,cluster('flash_write_page')
	.sect	'.debug_frame'
	.word	12
	.word	.L423,.L48,.L139-.L48
	.sdecl	'.debug_frame',debug,cluster('flash_read_page_to_buffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L423,.L50,.L202-.L50
	.sdecl	'.debug_frame',debug,cluster('flash_write_page_from_buffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L423,.L52,.L208-.L52
	.sdecl	'.debug_frame',debug,cluster('flash_buffer_clear')
	.sect	'.debug_frame'
	.word	12
	.word	.L423,.L54,.L212-.L54
	; Module end
