LOADING	C:\Users\<USER>\Desktop\ORRN_Code_01\ORRN_Code\Mark_1\Debug\Mark_1.elf
EMULATION	Probe - System; Root
EMULATION	Initial debug frequency set
EMULATION	Asserting reset
EMU<PERSON><PERSON><PERSON>	Connected to SoC/core(s)
EMULA<PERSON>ON	Finished connecting to SoC
EMULATION	Releasing reset
EMULATION	Initialization script (if any) done
No RAM which needs initialization found
EMULATION	Final debug frequency set
EMULATION	SoC ID match OK
SoC	Detected device: TC260 series, rev. BC
EMULATION	STARTED
PROGRAMMING 	76228 bytes
PROGRAMMING	Infineon TC26x_PFLASH_2MB5 (7 sectors)
SoC	Detected device: TC260 series, rev. BC
EMULATION	END - Without cleanup
