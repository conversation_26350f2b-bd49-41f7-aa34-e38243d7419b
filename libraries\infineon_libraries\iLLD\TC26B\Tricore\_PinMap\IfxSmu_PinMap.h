/**
 * \file IfxSmu_PinMap.h
 * \brief SMU I/O map
 * \ingroup IfxLld_Smu
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup IfxLld_Smu_pinmap SMU Pin Mapping
 * \ingroup IfxLld_Smu
 */

#ifndef IFXSMU_PINMAP_H
#define IFXSMU_PINMAP_H

#include <IfxSmu_reg.h>
#include <Port/Std/IfxPort.h>

/** \addtogroup IfxLld_Smu_pinmap
 * \{ */

/** \brief Fault Signal Protocol Pin */
typedef const struct
{
    Ifx_SMU*          module;    /**< \brief Base address */
    IfxPort_Pin       pin;       /**< \brief Port pin */
    IfxPort_OutputIdx select;    /**< \brief Port control code */
} IfxSmu_Fsp_Out;

IFX_EXTERN IfxSmu_Fsp_Out IfxSmu_FSP_P33_8_OUT;  /**< \brief SMU_FSP: SMU */

/** \brief Table dimensions */
#define IFXSMU_PINMAP_NUM_MODULES 1
#define IFXSMU_PINMAP_FSP_OUT_NUM_ITEMS 1


/** \brief IfxSmu_Fsp_Out table */
IFX_EXTERN const IfxSmu_Fsp_Out *IfxSmu_Fsp_Out_pinTable[IFXSMU_PINMAP_NUM_MODULES][IFXSMU_PINMAP_FSP_OUT_NUM_ITEMS];

/** \} */

#endif /* IFXSMU_PINMAP_H */
