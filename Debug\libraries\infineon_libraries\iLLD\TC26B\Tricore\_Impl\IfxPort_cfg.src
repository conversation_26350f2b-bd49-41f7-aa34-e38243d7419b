	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc24572a --dep-file=IfxPort_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxPort_cfg.IfxPort_cfg_esrMasks',data,rom,cluster('IfxPort_cfg_esrMasks')
	.sect	'.rodata.IfxPort_cfg.IfxPort_cfg_esrMasks'
	.global	IfxPort_cfg_esrMasks
	.align	4
IfxPort_cfg_esrMasks:	.type	object
	.size	IfxPort_cfg_esrMasks,104
	.word	-268197888
	.half	65535
	.space	2
	.word	-268197376
	.half	65535
	.space	2
	.word	-268193792
	.half	65535
	.space	2
	.word	-268193536
	.half	65535
	.space	2
	.word	-268193024
	.half	65535
	.space	2
	.word	-268192768
	.half	65535
	.space	2
	.word	-268192512
	.half	65535
	.space	2
	.word	-268189696
	.half	65535
	.space	2
	.word	-268189440
	.half	65535
	.space	2
	.word	-268189184
	.half	65535
	.space	2
	.word	-268188928
	.half	65535
	.space	2
	.word	-268185088
	.half	65535
	.space	2
	.word	-268184832
	.half	65535
	.space	2
	.sdecl	'.rodata.IfxPort_cfg.IfxPort_cfg_indexMap',data,rom,cluster('IfxPort_cfg_indexMap')
	.sect	'.rodata.IfxPort_cfg.IfxPort_cfg_indexMap'
	.global	IfxPort_cfg_indexMap
	.align	4
IfxPort_cfg_indexMap:	.type	object
	.size	IfxPort_cfg_indexMap,104
	.word	-268197888
	.space	4
	.word	-268197376,2,-268193792,10,-268193536,11,-268193024,13
	.word	-268192768,14,-268192512,15,-268189696,20,-268189440,21
	.word	-268189184,22,-268188928,23,-268185088,32,-268184832,33
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	10139
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	233
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	236
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	281
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	293
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	373
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	347
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	379
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	379
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	347
	.byte	6,0,10
	.word	241
	.byte	11
	.word	267
	.byte	6,0,10
	.word	302
	.byte	11
	.word	334
	.byte	6,0,10
	.word	384
	.byte	11
	.word	403
	.byte	6,0,10
	.word	419
	.byte	11
	.word	434
	.byte	11
	.word	448
	.byte	6,0,7
	.byte	'short int',0,2,5,12
	.byte	'__wchar_t',0,3,1,1
	.word	518
	.byte	7
	.byte	'unsigned int',0,4,7,12
	.byte	'__size_t',0,3,1,1
	.word	549
	.byte	7
	.byte	'int',0,4,5,12
	.byte	'__ptrdiff_t',0,3,1,1
	.word	582
	.byte	13,1,3
	.word	609
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	611
	.byte	7
	.byte	'unsigned char',0,1,8,12
	.byte	'uint8',0,4,105,29
	.word	634
	.byte	7
	.byte	'unsigned short int',0,2,7,12
	.byte	'uint16',0,4,109,29
	.word	665
	.byte	7
	.byte	'unsigned long int',0,4,7,12
	.byte	'uint32',0,4,113,29
	.word	702
	.byte	12
	.byte	'uint64',0,4,118,29
	.word	347
	.byte	12
	.byte	'sint16',0,4,126,29
	.word	518
	.byte	7
	.byte	'long int',0,4,5,12
	.byte	'sint32',0,4,131,1,29
	.word	768
	.byte	7
	.byte	'long long int',0,8,5,12
	.byte	'sint64',0,4,138,1,29
	.word	796
	.byte	12
	.byte	'float32',0,4,167,1,29
	.word	293
	.byte	12
	.byte	'pvoid',0,5,57,28
	.word	379
	.byte	12
	.byte	'Ifx_TickTime',0,5,79,28
	.word	796
	.byte	14
	.word	373
	.byte	3
	.word	881
	.byte	15,5,143,1,9,8,16
	.byte	'module',0
	.word	886
	.byte	4,2,35,0,16
	.byte	'index',0
	.word	768
	.byte	4,2,35,4,0,12
	.byte	'IfxModule_IndexMap',0,5,147,1,3
	.word	891
	.byte	17
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,18
	.byte	'EN0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'EN1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'EN2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'EN3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'EN4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'EN5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'EN6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'EN7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'EN8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'EN9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'EN10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'EN11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'EN12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'EN13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'EN14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'EN15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'EN16',0,1
	.word	634
	.byte	1,7,2,35,2,18
	.byte	'EN17',0,1
	.word	634
	.byte	1,6,2,35,2,18
	.byte	'EN18',0,1
	.word	634
	.byte	1,5,2,35,2,18
	.byte	'EN19',0,1
	.word	634
	.byte	1,4,2,35,2,18
	.byte	'EN20',0,1
	.word	634
	.byte	1,3,2,35,2,18
	.byte	'EN21',0,1
	.word	634
	.byte	1,2,2,35,2,18
	.byte	'EN22',0,1
	.word	634
	.byte	1,1,2,35,2,18
	.byte	'EN23',0,1
	.word	634
	.byte	1,0,2,35,2,18
	.byte	'EN24',0,1
	.word	634
	.byte	1,7,2,35,3,18
	.byte	'EN25',0,1
	.word	634
	.byte	1,6,2,35,3,18
	.byte	'EN26',0,1
	.word	634
	.byte	1,5,2,35,3,18
	.byte	'EN27',0,1
	.word	634
	.byte	1,4,2,35,3,18
	.byte	'EN28',0,1
	.word	634
	.byte	1,3,2,35,3,18
	.byte	'EN29',0,1
	.word	634
	.byte	1,2,2,35,3,18
	.byte	'EN30',0,1
	.word	634
	.byte	1,1,2,35,3,18
	.byte	'EN31',0,1
	.word	634
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	957
	.byte	17
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,18
	.byte	'reserved_0',0,4
	.word	549
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	1510
	.byte	17
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,18
	.byte	'EN0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'EN1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'EN2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'EN3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'EN4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'EN5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'EN6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'EN7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'EN8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'EN9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'EN10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'EN11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'EN12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'EN13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'EN14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'EN15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'reserved_16',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	1583
	.byte	17
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,18
	.byte	'MODREV',0,1
	.word	634
	.byte	8,0,2,35,0,18
	.byte	'MODTYPE',0,1
	.word	634
	.byte	8,0,2,35,1,18
	.byte	'MODNUMBER',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	1897
	.byte	17
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,18
	.byte	'P0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'P1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'P2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'P3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'P4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'P5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'P6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'P7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'P8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'P9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'P10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'P11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'P12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'P13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'P14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'P15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'reserved_16',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	1998
	.byte	17
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	3,5,2,35,0,18
	.byte	'PC0',0,1
	.word	634
	.byte	5,0,2,35,0,18
	.byte	'reserved_8',0,1
	.word	634
	.byte	3,5,2,35,1,18
	.byte	'PC1',0,1
	.word	634
	.byte	5,0,2,35,1,18
	.byte	'reserved_16',0,1
	.word	634
	.byte	3,5,2,35,2,18
	.byte	'PC2',0,1
	.word	634
	.byte	5,0,2,35,2,18
	.byte	'reserved_24',0,1
	.word	634
	.byte	3,5,2,35,3,18
	.byte	'PC3',0,1
	.word	634
	.byte	5,0,2,35,3,0,12
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2295
	.byte	17
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	3,5,2,35,0,18
	.byte	'PC12',0,1
	.word	634
	.byte	5,0,2,35,0,18
	.byte	'reserved_8',0,1
	.word	634
	.byte	3,5,2,35,1,18
	.byte	'PC13',0,1
	.word	634
	.byte	5,0,2,35,1,18
	.byte	'reserved_16',0,1
	.word	634
	.byte	3,5,2,35,2,18
	.byte	'PC14',0,1
	.word	634
	.byte	5,0,2,35,2,18
	.byte	'reserved_24',0,1
	.word	634
	.byte	3,5,2,35,3,18
	.byte	'PC15',0,1
	.word	634
	.byte	5,0,2,35,3,0,12
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	2496
	.byte	17
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	3,5,2,35,0,18
	.byte	'PC4',0,1
	.word	634
	.byte	5,0,2,35,0,18
	.byte	'reserved_8',0,1
	.word	634
	.byte	3,5,2,35,1,18
	.byte	'PC5',0,1
	.word	634
	.byte	5,0,2,35,1,18
	.byte	'reserved_16',0,1
	.word	634
	.byte	3,5,2,35,2,18
	.byte	'PC6',0,1
	.word	634
	.byte	5,0,2,35,2,18
	.byte	'reserved_24',0,1
	.word	634
	.byte	3,5,2,35,3,18
	.byte	'PC7',0,1
	.word	634
	.byte	5,0,2,35,3,0,12
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2703
	.byte	17
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	3,5,2,35,0,18
	.byte	'PC8',0,1
	.word	634
	.byte	5,0,2,35,0,18
	.byte	'reserved_8',0,1
	.word	634
	.byte	3,5,2,35,1,18
	.byte	'PC9',0,1
	.word	634
	.byte	5,0,2,35,1,18
	.byte	'reserved_16',0,1
	.word	634
	.byte	3,5,2,35,2,18
	.byte	'PC10',0,1
	.word	634
	.byte	5,0,2,35,2,18
	.byte	'reserved_24',0,1
	.word	634
	.byte	3,5,2,35,3,18
	.byte	'PC11',0,1
	.word	634
	.byte	5,0,2,35,3,0,12
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2904
	.byte	17
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'PS1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'reserved_2',0,4
	.word	549
	.byte	30,0,2,35,0,0,12
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	3107
	.byte	17
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'PS1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'reserved_2',0,4
	.word	549
	.byte	30,0,2,35,0,0,12
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	3217
	.byte	17
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,18
	.byte	'RDIS_CTRL',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'RX_DIS',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'TERM',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'LRXTERM',0,1
	.word	634
	.byte	5,0,2,35,0,18
	.byte	'reserved_8',0,4
	.word	549
	.byte	24,0,2,35,0,0,12
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	3327
	.byte	17
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	8,0,2,35,0,18
	.byte	'LVDSR',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'LVDSRL',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'reserved_10',0,1
	.word	634
	.byte	2,4,2,35,1,18
	.byte	'TDIS_CTRL',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'TX_DIS',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'TX_PD',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'TX_PWDPD',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'reserved_16',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	3482
	.byte	17
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,18
	.byte	'reserved_0',0,2
	.word	665
	.byte	16,0,2,35,0,18
	.byte	'PCL0',0,1
	.word	634
	.byte	1,7,2,35,2,18
	.byte	'PCL1',0,1
	.word	634
	.byte	1,6,2,35,2,18
	.byte	'PCL2',0,1
	.word	634
	.byte	1,5,2,35,2,18
	.byte	'PCL3',0,1
	.word	634
	.byte	1,4,2,35,2,18
	.byte	'reserved_20',0,2
	.word	665
	.byte	12,0,2,35,2,0,12
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	3712
	.byte	17
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,18
	.byte	'reserved_0',0,4
	.word	549
	.byte	28,4,2,35,0,18
	.byte	'PCL12',0,1
	.word	634
	.byte	1,3,2,35,3,18
	.byte	'PCL13',0,1
	.word	634
	.byte	1,2,2,35,3,18
	.byte	'PCL14',0,1
	.word	634
	.byte	1,1,2,35,3,18
	.byte	'PCL15',0,1
	.word	634
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	3872
	.byte	17
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,18
	.byte	'reserved_0',0,4
	.word	549
	.byte	20,12,2,35,0,18
	.byte	'PCL4',0,1
	.word	634
	.byte	1,3,2,35,2,18
	.byte	'PCL5',0,1
	.word	634
	.byte	1,2,2,35,2,18
	.byte	'PCL6',0,1
	.word	634
	.byte	1,1,2,35,2,18
	.byte	'PCL7',0,1
	.word	634
	.byte	1,0,2,35,2,18
	.byte	'reserved_24',0,1
	.word	634
	.byte	8,0,2,35,3,0,12
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	4015
	.byte	17
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,18
	.byte	'reserved_0',0,4
	.word	549
	.byte	24,8,2,35,0,18
	.byte	'PCL8',0,1
	.word	634
	.byte	1,7,2,35,3,18
	.byte	'PCL9',0,1
	.word	634
	.byte	1,6,2,35,3,18
	.byte	'PCL10',0,1
	.word	634
	.byte	1,5,2,35,3,18
	.byte	'PCL11',0,1
	.word	634
	.byte	1,4,2,35,3,18
	.byte	'reserved_28',0,1
	.word	634
	.byte	4,0,2,35,3,0,12
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	4175
	.byte	17
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,18
	.byte	'reserved_0',0,2
	.word	665
	.byte	16,0,2,35,0,18
	.byte	'PCL0',0,1
	.word	634
	.byte	1,7,2,35,2,18
	.byte	'PCL1',0,1
	.word	634
	.byte	1,6,2,35,2,18
	.byte	'PCL2',0,1
	.word	634
	.byte	1,5,2,35,2,18
	.byte	'PCL3',0,1
	.word	634
	.byte	1,4,2,35,2,18
	.byte	'PCL4',0,1
	.word	634
	.byte	1,3,2,35,2,18
	.byte	'PCL5',0,1
	.word	634
	.byte	1,2,2,35,2,18
	.byte	'PCL6',0,1
	.word	634
	.byte	1,1,2,35,2,18
	.byte	'PCL7',0,1
	.word	634
	.byte	1,0,2,35,2,18
	.byte	'PCL8',0,1
	.word	634
	.byte	1,7,2,35,3,18
	.byte	'PCL9',0,1
	.word	634
	.byte	1,6,2,35,3,18
	.byte	'PCL10',0,1
	.word	634
	.byte	1,5,2,35,3,18
	.byte	'PCL11',0,1
	.word	634
	.byte	1,4,2,35,3,18
	.byte	'PCL12',0,1
	.word	634
	.byte	1,3,2,35,3,18
	.byte	'PCL13',0,1
	.word	634
	.byte	1,2,2,35,3,18
	.byte	'PCL14',0,1
	.word	634
	.byte	1,1,2,35,3,18
	.byte	'PCL15',0,1
	.word	634
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	4337
	.byte	17
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,18
	.byte	'PS0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'PS1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'PS2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'PS3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'PS4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'PS5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'PS6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'PS7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'PS8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'PS9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'PS10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'PS11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'PS12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'PS13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'PS14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'PS15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'PCL0',0,1
	.word	634
	.byte	1,7,2,35,2,18
	.byte	'PCL1',0,1
	.word	634
	.byte	1,6,2,35,2,18
	.byte	'PCL2',0,1
	.word	634
	.byte	1,5,2,35,2,18
	.byte	'PCL3',0,1
	.word	634
	.byte	1,4,2,35,2,18
	.byte	'PCL4',0,1
	.word	634
	.byte	1,3,2,35,2,18
	.byte	'PCL5',0,1
	.word	634
	.byte	1,2,2,35,2,18
	.byte	'PCL6',0,1
	.word	634
	.byte	1,1,2,35,2,18
	.byte	'PCL7',0,1
	.word	634
	.byte	1,0,2,35,2,18
	.byte	'PCL8',0,1
	.word	634
	.byte	1,7,2,35,3,18
	.byte	'PCL9',0,1
	.word	634
	.byte	1,6,2,35,3,18
	.byte	'PCL10',0,1
	.word	634
	.byte	1,5,2,35,3,18
	.byte	'PCL11',0,1
	.word	634
	.byte	1,4,2,35,3,18
	.byte	'PCL12',0,1
	.word	634
	.byte	1,3,2,35,3,18
	.byte	'PCL13',0,1
	.word	634
	.byte	1,2,2,35,3,18
	.byte	'PCL14',0,1
	.word	634
	.byte	1,1,2,35,3,18
	.byte	'PCL15',0,1
	.word	634
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	4670
	.byte	17
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,18
	.byte	'PS0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'PS1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'PS2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'PS3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'reserved_4',0,4
	.word	549
	.byte	28,0,2,35,0,0,12
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5225
	.byte	17
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,18
	.byte	'reserved_0',0,2
	.word	665
	.byte	12,4,2,35,0,18
	.byte	'PS12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'PS13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'PS14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'PS15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'reserved_16',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5358
	.byte	17
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	4,4,2,35,0,18
	.byte	'PS4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'PS5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'PS6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'PS7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'reserved_8',0,4
	.word	549
	.byte	24,0,2,35,0,0,12
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5520
	.byte	17
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,18
	.byte	'reserved_0',0,1
	.word	634
	.byte	8,0,2,35,0,18
	.byte	'PS8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'PS9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'PS10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'PS11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'reserved_12',0,4
	.word	549
	.byte	20,0,2,35,0,0,12
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5675
	.byte	17
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,18
	.byte	'PS0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'PS1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'PS2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'PS3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'PS4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'PS5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'PS6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'PS7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'PS8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'PS9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'PS10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'PS11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'PS12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'PS13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'PS14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'PS15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'reserved_16',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	5833
	.byte	17
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,18
	.byte	'P0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'P1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'P2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'P3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'P4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'P5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'P6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'P7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'P8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'P9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'P10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'P11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'P12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'P13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'P14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'P15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'reserved_16',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	6151
	.byte	17
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,18
	.byte	'SEL0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'SEL1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'SEL2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'SEL3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'SEL4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'SEL5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'SEL6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'SEL7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'reserved_8',0,1
	.word	634
	.byte	2,6,2,35,1,18
	.byte	'SEL10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'SEL11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'reserved_12',0,4
	.word	549
	.byte	19,1,2,35,0,18
	.byte	'LCK',0,1
	.word	634
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	6451
	.byte	17
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,18
	.byte	'PDIS0',0,1
	.word	634
	.byte	1,7,2,35,0,18
	.byte	'PDIS1',0,1
	.word	634
	.byte	1,6,2,35,0,18
	.byte	'PDIS2',0,1
	.word	634
	.byte	1,5,2,35,0,18
	.byte	'PDIS3',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'PDIS4',0,1
	.word	634
	.byte	1,3,2,35,0,18
	.byte	'PDIS5',0,1
	.word	634
	.byte	1,2,2,35,0,18
	.byte	'PDIS6',0,1
	.word	634
	.byte	1,1,2,35,0,18
	.byte	'PDIS7',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'PDIS8',0,1
	.word	634
	.byte	1,7,2,35,1,18
	.byte	'PDIS9',0,1
	.word	634
	.byte	1,6,2,35,1,18
	.byte	'PDIS10',0,1
	.word	634
	.byte	1,5,2,35,1,18
	.byte	'PDIS11',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'PDIS12',0,1
	.word	634
	.byte	1,3,2,35,1,18
	.byte	'PDIS13',0,1
	.word	634
	.byte	1,2,2,35,1,18
	.byte	'PDIS14',0,1
	.word	634
	.byte	1,1,2,35,1,18
	.byte	'PDIS15',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'reserved_16',0,2
	.word	665
	.byte	16,0,2,35,2,0,12
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	6722
	.byte	17
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,18
	.byte	'PD0',0,1
	.word	634
	.byte	3,5,2,35,0,18
	.byte	'PL0',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'PD1',0,1
	.word	634
	.byte	3,1,2,35,0,18
	.byte	'PL1',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'PD2',0,1
	.word	634
	.byte	3,5,2,35,1,18
	.byte	'PL2',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'PD3',0,1
	.word	634
	.byte	3,1,2,35,1,18
	.byte	'PL3',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'PD4',0,1
	.word	634
	.byte	3,5,2,35,2,18
	.byte	'PL4',0,1
	.word	634
	.byte	1,4,2,35,2,18
	.byte	'PD5',0,1
	.word	634
	.byte	3,1,2,35,2,18
	.byte	'PL5',0,1
	.word	634
	.byte	1,0,2,35,2,18
	.byte	'PD6',0,1
	.word	634
	.byte	3,5,2,35,3,18
	.byte	'PL6',0,1
	.word	634
	.byte	1,4,2,35,3,18
	.byte	'PD7',0,1
	.word	634
	.byte	3,1,2,35,3,18
	.byte	'PL7',0,1
	.word	634
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	7074
	.byte	17
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,18
	.byte	'PD8',0,1
	.word	634
	.byte	3,5,2,35,0,18
	.byte	'PL8',0,1
	.word	634
	.byte	1,4,2,35,0,18
	.byte	'PD9',0,1
	.word	634
	.byte	3,1,2,35,0,18
	.byte	'PL9',0,1
	.word	634
	.byte	1,0,2,35,0,18
	.byte	'PD10',0,1
	.word	634
	.byte	3,5,2,35,1,18
	.byte	'PL10',0,1
	.word	634
	.byte	1,4,2,35,1,18
	.byte	'PD11',0,1
	.word	634
	.byte	3,1,2,35,1,18
	.byte	'PL11',0,1
	.word	634
	.byte	1,0,2,35,1,18
	.byte	'PD12',0,1
	.word	634
	.byte	3,5,2,35,2,18
	.byte	'PL12',0,1
	.word	634
	.byte	1,4,2,35,2,18
	.byte	'PD13',0,1
	.word	634
	.byte	3,1,2,35,2,18
	.byte	'PL13',0,1
	.word	634
	.byte	1,0,2,35,2,18
	.byte	'PD14',0,1
	.word	634
	.byte	3,5,2,35,3,18
	.byte	'PL14',0,1
	.word	634
	.byte	1,4,2,35,3,18
	.byte	'PD15',0,1
	.word	634
	.byte	3,1,2,35,3,18
	.byte	'PL15',0,1
	.word	634
	.byte	1,0,2,35,3,0,12
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	7363
	.byte	19,6,252,3,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	957
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	7664
	.byte	19,6,132,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1510
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7726
	.byte	19,6,140,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1583
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	7788
	.byte	19,6,148,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1897
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	7847
	.byte	19,6,156,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1998
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	7905
	.byte	19,6,164,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2295
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	7963
	.byte	19,6,172,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2496
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	8024
	.byte	19,6,180,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2703
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	8086
	.byte	19,6,188,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2904
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	8147
	.byte	19,6,196,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3107
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	8208
	.byte	19,6,204,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3217
	.byte	4,2,35,0,16
	.byte	'B_P21',0
	.word	3327
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	8269
	.byte	19,6,213,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3482
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	8345
	.byte	19,6,221,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4337
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	8406
	.byte	19,6,229,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3712
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	8466
	.byte	19,6,237,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3872
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	8527
	.byte	19,6,245,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4015
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	8589
	.byte	19,6,253,4,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4175
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	8650
	.byte	19,6,133,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4670
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	8711
	.byte	19,6,141,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5833
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	8770
	.byte	19,6,149,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5225
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	8830
	.byte	19,6,157,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5358
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	8891
	.byte	19,6,165,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5520
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	8953
	.byte	19,6,173,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5675
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	9014
	.byte	19,6,181,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6151
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	9075
	.byte	19,6,189,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6451
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	9134
	.byte	19,6,197,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6722
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	9194
	.byte	19,6,205,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7074
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	9255
	.byte	19,6,213,5,9,4,16
	.byte	'U',0
	.word	549
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	582
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7363
	.byte	4,2,35,0,0,12
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	9315
	.byte	20,4
	.word	634
	.byte	21,3,0,20,24
	.word	634
	.byte	21,23,0,20,8
	.word	634
	.byte	21,7,0,20,12
	.word	634
	.byte	21,11,0,20,76
	.word	634
	.byte	21,75,0,17
	.byte	'_Ifx_P',0,6,229,5,25,128,2,16
	.byte	'OUT',0
	.word	9075
	.byte	4,2,35,0,16
	.byte	'OMR',0
	.word	8711
	.byte	4,2,35,4,16
	.byte	'ID',0
	.word	7847
	.byte	4,2,35,8,16
	.byte	'reserved_C',0
	.word	9375
	.byte	4,2,35,12,16
	.byte	'IOCR0',0
	.word	7963
	.byte	4,2,35,16,16
	.byte	'IOCR4',0
	.word	8086
	.byte	4,2,35,20,16
	.byte	'IOCR8',0
	.word	8147
	.byte	4,2,35,24,16
	.byte	'IOCR12',0
	.word	8024
	.byte	4,2,35,28,16
	.byte	'reserved_20',0
	.word	9375
	.byte	4,2,35,32,16
	.byte	'IN',0
	.word	7905
	.byte	4,2,35,36,16
	.byte	'reserved_28',0
	.word	9384
	.byte	24,2,35,40,16
	.byte	'PDR0',0
	.word	9255
	.byte	4,2,35,64,16
	.byte	'PDR1',0
	.word	9315
	.byte	4,2,35,68,16
	.byte	'reserved_48',0
	.word	9393
	.byte	8,2,35,72,16
	.byte	'ESR',0
	.word	7788
	.byte	4,2,35,80,16
	.byte	'reserved_54',0
	.word	9402
	.byte	12,2,35,84,16
	.byte	'PDISC',0
	.word	9194
	.byte	4,2,35,96,16
	.byte	'PCSR',0
	.word	9134
	.byte	4,2,35,100,16
	.byte	'reserved_68',0
	.word	9393
	.byte	8,2,35,104,16
	.byte	'OMSR0',0
	.word	8830
	.byte	4,2,35,112,16
	.byte	'OMSR4',0
	.word	8953
	.byte	4,2,35,116,16
	.byte	'OMSR8',0
	.word	9014
	.byte	4,2,35,120,16
	.byte	'OMSR12',0
	.word	8891
	.byte	4,2,35,124,16
	.byte	'OMCR0',0
	.word	8466
	.byte	4,3,35,128,1,16
	.byte	'OMCR4',0
	.word	8589
	.byte	4,3,35,132,1,16
	.byte	'OMCR8',0
	.word	8650
	.byte	4,3,35,136,1,16
	.byte	'OMCR12',0
	.word	8527
	.byte	4,3,35,140,1,16
	.byte	'OMSR',0
	.word	8770
	.byte	4,3,35,144,1,16
	.byte	'OMCR',0
	.word	8406
	.byte	4,3,35,148,1,16
	.byte	'reserved_98',0
	.word	9393
	.byte	8,3,35,152,1,16
	.byte	'LPCR0',0
	.word	8208
	.byte	4,3,35,160,1,16
	.byte	'LPCR1',0
	.word	8269
	.byte	4,3,35,164,1,16
	.byte	'LPCR2',0
	.word	8345
	.byte	4,3,35,168,1,16
	.byte	'reserved_A4',0
	.word	9411
	.byte	76,3,35,172,1,16
	.byte	'ACCEN1',0
	.word	7726
	.byte	4,3,35,248,1,16
	.byte	'ACCEN0',0
	.word	7664
	.byte	4,3,35,252,1,0,14
	.word	9420
	.byte	12
	.byte	'Ifx_P',0,6,139,6,3
	.word	10023
	.byte	14
	.word	9420
	.byte	3
	.word	10043
	.byte	15,7,103,9,8,16
	.byte	'port',0
	.word	10048
	.byte	4,2,35,0,16
	.byte	'masks',0
	.word	665
	.byte	2,2,35,4,0,12
	.byte	'IfxPort_Esr_Masks',0,7,107,3
	.word	10053
	.byte	20,104
	.word	10053
	.byte	21,12,0
.L10:
	.byte	22
	.word	10114
	.byte	20,104
	.word	891
	.byte	21,12,0
.L11:
	.byte	22
	.word	10128
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,46,1,49,19,0,0,11,5
	.byte	0,49,19,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,53,0,73,19,0,0,15,19,1,58,15
	.byte	59,15,57,15,11,15,0,0,16,13,0,3,8,73,19,11,15,56,9,0,0,17,19,1,3,8,58,15,59,15,57,15,11,15,0,0,18,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,19,23,1,58,15,59,15,57,15,11,15,0,0,20,1,1,11,15,73,19,0,0,21,33
	.byte	0,47,15,0,0,22,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Impl\\IfxPort_cfg.h',0,0,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('IfxPort_cfg_esrMasks')
	.sect	'.debug_info'
.L6:
	.word	267
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_cfg_esrMasks',0,3,56,30
	.word	.L10
	.byte	1,5,3
	.word	IfxPort_cfg_esrMasks
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_cfg_esrMasks')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_cfg_indexMap')
	.sect	'.debug_info'
.L8:
	.word	267
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxPort_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_cfg_indexMap',0,3,73,30
	.word	.L11
	.byte	1,5,3
	.word	IfxPort_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_cfg_indexMap')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
