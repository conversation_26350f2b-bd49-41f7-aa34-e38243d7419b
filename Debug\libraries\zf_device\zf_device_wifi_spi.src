	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc19628a --dep-file=zf_device_wifi_spi.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_wifi_spi.src ../libraries/zf_device/zf_device_wifi_spi.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_wifi_spi.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_wait_idle',code,cluster('wifi_spi_wait_idle')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_wait_idle'
	.align	2
	
; Function wifi_spi_wait_idle
.L87:
wifi_spi_wait_idle:	.type	func
	mov	d15,#0
.L313:
	mul	d8,d4,#100
.L312:
	j	.L2
.L3:
	mov	d4,#10
	call	system_delay_us
.L639:
	add	d15,#1
.L640:
	jlt.u	d15,d8,.L4
.L641:
	j	.L5
.L4:
.L2:
	mov	d4,#488
	call	gpio_get_level
.L642:
	jeq	d2,#0,.L3
.L5:
	ge.u	d2,d15,d8
.L643:
	j	.L6
.L6:
	ret
.L264:
	
__wifi_spi_wait_idle_function_end:
	.size	wifi_spi_wait_idle,__wifi_spi_wait_idle_function_end-wifi_spi_wait_idle
.L163:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_write',code,cluster('wifi_spi_write')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_write'
	.align	2
	
; Function wifi_spi_write
.L89:
wifi_spi_write:	.type	func
	mov.aa	a15,a4
.L316:
	mov	d15,d4
.L318:
	mov.aa	a12,a5
.L320:
	mov	d8,d5
.L321:
	mov	d4,#706
.L315:
	call	get_port
.L314:
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L648:
	mov.a	a2,#0
.L649:
	jeq.a	a2,a15,.L7
.L650:
	mov	d4,#3
.L651:
	mov.aa	a4,a15
.L322:
	mov	d5,d15
.L323:
	call	spi_write_8bit_array
.L7:
	mov.a	a15,#0
.L317:
	jeq.a	a15,a12,.L8
.L652:
	mov	d4,#3
.L653:
	mov.aa	a4,a12
.L324:
	mov	d5,d8
.L325:
	call	spi_write_8bit_array
.L8:
	mov	d4,#706
	call	get_port
	add.a	a2,#4
	mov	d15,#4
.L319:
	st.w	[a2],d15
.L654:
	ret
.L267:
	
__wifi_spi_write_function_end:
	.size	wifi_spi_write,__wifi_spi_write_function_end-wifi_spi_write
.L168:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_transfer_command',code,cluster('wifi_spi_transfer_command')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_transfer_command'
	.align	2
	
; Function wifi_spi_transfer_command
.L91:
wifi_spi_transfer_command:	.type	func
	mov.aa	a15,a4
.L328:
	mov	d15,d4
.L329:
	mov	d4,#706
.L326:
	call	get_port
.L327:
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L659:
	mov	d4,#3
.L660:
	mov	d5,#4
	mov.aa	a4,a15
.L331:
	mov.aa	a5,a15
.L333:
	call	spi_transfer_8bit
.L332:
	jeq	d15,#0,.L9
.L661:
	mov	d4,#3
.L662:
	mov.aa	a4,a15
.L334:
	add.a	a4,#4
.L663:
	add.a	a15,#4
.L335:
	mov.aa	a5,a15
.L336:
	mov	d5,d15
.L337:
	call	spi_transfer_8bit
.L9:
	mov	d4,#706
	call	get_port
	add.a	a2,#4
	mov	d15,#4
.L330:
	st.w	[a2],d15
.L664:
	ret
.L272:
	
__wifi_spi_transfer_command_function_end:
	.size	wifi_spi_transfer_command,__wifi_spi_transfer_command_function_end-wifi_spi_transfer_command
.L173:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_transfer_data',code,cluster('wifi_spi_transfer_data')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_transfer_data'
	.align	2
	
; Function wifi_spi_transfer_data
.L93:
wifi_spi_transfer_data:	.type	func
	mov.aa	a12,a4
.L340:
	mov.aa	a15,a5
.L341:
	mov	d8,d4
.L343:
	mov	d4,#706
.L338:
	call	get_port
.L339:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L669:
	mov	d15,#2
.L670:
	st.b	[a15],d15
.L671:
	st.h	[a15]2,d8
.L672:
	mov	d4,#3
.L673:
	mov	d5,#4
	mov.aa	a4,a15
.L344:
	mov.aa	a5,a15
.L346:
	call	spi_transfer_8bit
.L345:
	mov	d15,#32
.L674:
	jge.u	d15,d8,.L10
.L675:
	mov	d4,#3
.L676:
	add.a	a15,#4
.L677:
	mov	d5,#32
	mov.aa	a4,a12
.L347:
	mov.aa	a5,a15
.L349:
	call	spi_transfer_8bit
.L348:
	mov	d4,#3
.L678:
	lea	a4,[a12]32
.L679:
	add	d5,d8,#-32
	call	spi_write_8bit_array
.L680:
	j	.L11
.L10:
	mov.aa	a4,a15
.L350:
	add.a	a4,#4
.L342:
	mov.aa	a5,a12
.L352:
	mov	d4,d8
.L353:
	call	memcpy
.L351:
	mov	d4,#3
.L354:
	mov.aa	a4,a15
.L355:
	add.a	a4,#4
.L357:
	mov.aa	a5,a15
.L356:
	add.a	a5,#4
.L681:
	mov	d5,#32
.L358:
	call	spi_transfer_8bit
.L11:
	mov	d4,#706
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
.L682:
	ret
.L276:
	
__wifi_spi_transfer_data_function_end:
	.size	wifi_spi_transfer_data,__wifi_spi_transfer_data_function_end-wifi_spi_transfer_data
.L178:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_set_parameter',code,cluster('wifi_spi_set_parameter')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_set_parameter'
	.align	2
	
; Function wifi_spi_set_parameter
.L95:
wifi_spi_set_parameter:	.type	func
	sub.a	a10,#8
.L359:
	mov.aa	a15,a4
.L362:
	mov	d15,d5
.L363:
	mov	d8,d6
.L365:
	mov	d9,#1
.L12:
	st.b	[a10],d4
.L687:
	st.h	[a10]2,d15
.L688:
	mov	d4,d8
.L361:
	call	wifi_spi_wait_idle
.L360:
	jeq	d2,#0,.L13
.L689:
	j	.L14
.L13:
	lea	a4,[a10]0
.L690:
	mov	d4,#4
.L691:
	mov.aa	a5,a15
.L366:
	mov	d5,d15
.L368:
	call	wifi_spi_write
.L367:
	mov	d4,d8
.L369:
	call	wifi_spi_wait_idle
.L370:
	jeq	d2,#0,.L15
.L692:
	j	.L16
.L15:
	mov	d15,#2
.L364:
	st.b	[a10],d15
.L693:
	mov	d15,#0
.L694:
	st.h	[a10]2,d15
.L695:
	lea	a4,[a10]0
.L696:
	mov	d4,#0
	call	wifi_spi_transfer_command
.L697:
	mov	d4,#20
	call	system_delay_us
.L698:
	ld.bu	d0,[a10]
.L699:
	mov	d15,#128
.L700:
	jne	d15,d0,.L17
.L701:
	mov	d9,#0
.L17:
.L16:
.L14:
	mov	d2,d9
.L371:
	j	.L18
.L18:
	ret
.L280:
	
__wifi_spi_set_parameter_function_end:
	.size	wifi_spi_set_parameter,__wifi_spi_set_parameter_function_end-wifi_spi_set_parameter
.L183:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_get_parameter',code,cluster('wifi_spi_get_parameter')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_get_parameter'
	.align	2
	
; Function wifi_spi_get_parameter
.L97:
wifi_spi_get_parameter:	.type	func
	mov	d15,d4
.L374:
	mov.aa	a15,a4
.L376:
	mov	d8,d5
.L377:
	mov	d9,#1
.L19:
	mov	d4,d8
.L372:
	call	wifi_spi_wait_idle
.L373:
	jeq	d2,#0,.L20
.L706:
	j	.L21
.L20:
	st.b	[a15],d15
.L707:
	mov	d4,#32
.L708:
	mov.a	a5,#0
.L709:
	mov	d5,#0
	mov.aa	a4,a15
.L378:
	call	wifi_spi_write
.L379:
	mov	d4,d8
.L380:
	call	wifi_spi_wait_idle
.L381:
	jeq	d2,#0,.L22
.L710:
	j	.L23
.L22:
	mov	d15,#2
.L375:
	st.b	[a15],d15
.L711:
	mov	d15,#0
.L712:
	st.h	[a15]2,d15
.L713:
	mov	d4,#32
	mov.aa	a4,a15
.L382:
	call	wifi_spi_transfer_command
.L383:
	mov	d9,#0
.L23:
.L21:
	mov	d2,d9
.L384:
	j	.L24
.L24:
	ret
.L289:
	
__wifi_spi_get_parameter_function_end:
	.size	wifi_spi_get_parameter,__wifi_spi_get_parameter_function_end-wifi_spi_get_parameter
.L188:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_get_version',code,cluster('wifi_spi_get_version')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_get_version'
	.align	2
	
; Function wifi_spi_get_version
.L99:
wifi_spi_get_version:	.type	func
	sub.a	a10,#40
.L385:
	mov	d4,#32
.L718:
	lea	a4,[a10]0
.L719:
	mov	d5,#1000
	call	wifi_spi_get_parameter
.L386:
	mov	d8,d2
.L387:
	jne	d8,#0,.L25
.L720:
	ld.bu	d0,[a10]
.L721:
	mov	d15,#160
.L722:
	jne	d15,d0,.L26
.L723:
	movh.a	a4,#@his(wifi_spi_version)
	lea	a4,[a4]@los(wifi_spi_version)
.L724:
	lea	a5,[a10]4
.L725:
	ld.hu	d4,[a10]2
	call	memcpy
.L26:
.L25:
	mov	d2,d8
.L388:
	j	.L27
.L27:
	ret
.L294:
	
__wifi_spi_get_version_function_end:
	.size	wifi_spi_get_version,__wifi_spi_get_version_function_end-wifi_spi_get_version
.L193:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_get_mac_addr',code,cluster('wifi_spi_get_mac_addr')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_get_mac_addr'
	.align	2
	
; Function wifi_spi_get_mac_addr
.L101:
wifi_spi_get_mac_addr:	.type	func
	sub.a	a10,#40
.L389:
	mov	d4,#33
.L730:
	lea	a4,[a10]0
.L731:
	mov	d5,#1000
	call	wifi_spi_get_parameter
.L390:
	mov	d8,d2
.L391:
	jne	d8,#0,.L28
.L732:
	ld.bu	d0,[a10]
.L733:
	mov	d15,#161
.L734:
	jne	d15,d0,.L29
.L735:
	movh.a	a4,#@his(wifi_spi_mac_addr)
	lea	a4,[a4]@los(wifi_spi_mac_addr)
.L736:
	lea	a5,[a10]4
.L737:
	ld.hu	d4,[a10]2
	call	memcpy
.L29:
.L28:
	mov	d2,d8
.L392:
	j	.L30
.L30:
	ret
.L297:
	
__wifi_spi_get_mac_addr_function_end:
	.size	wifi_spi_get_mac_addr,__wifi_spi_get_mac_addr_function_end-wifi_spi_get_mac_addr
.L198:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_get_ip_addr_port',code,cluster('wifi_spi_get_ip_addr_port')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_get_ip_addr_port'
	.align	2
	
; Function wifi_spi_get_ip_addr_port
.L103:
wifi_spi_get_ip_addr_port:	.type	func
	sub.a	a10,#40
.L393:
	mov	d4,#34
.L742:
	lea	a4,[a10]0
.L743:
	mov	d5,#1000
	call	wifi_spi_get_parameter
.L394:
	mov	d8,d2
.L395:
	jne	d8,#0,.L31
.L744:
	ld.bu	d0,[a10]
.L745:
	mov	d15,#162
.L746:
	jne	d15,d0,.L32
.L747:
	movh.a	a4,#@his(wifi_spi_ip_addr_port)
	lea	a4,[a4]@los(wifi_spi_ip_addr_port)
.L748:
	lea	a5,[a10]4
.L749:
	ld.hu	d4,[a10]2
	call	memcpy
.L32:
.L31:
	mov	d2,d8
.L396:
	j	.L33
.L33:
	ret
.L300:
	
__wifi_spi_get_ip_addr_port_function_end:
	.size	wifi_spi_get_ip_addr_port,__wifi_spi_get_ip_addr_port_function_end-wifi_spi_get_ip_addr_port
.L203:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_wifi_connect',code,cluster('wifi_spi_wifi_connect')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_wifi_connect'
	.align	2
	
	.global	wifi_spi_wifi_connect
; Function wifi_spi_wifi_connect
.L105:
wifi_spi_wifi_connect:	.type	func
	sub.a	a10,#72
.L397:
	mov.a	a15,#0
.L457:
	jeq.a	a15,a5,.L34
.L458:
	st.a	[a10],a4
.L459:
	st.a	[a10]4,a5
.L460:
	lea	a4,[a10]8
.L399:
	movh.a	a5,#@his(.1.str)
.L398:
	lea	a5,[a5]@los(.1.str)
	call	sprintf
.L461:
	extr.u	d5,d2,#0,#16
.L400:
	j	.L35
.L34:
	st.a	[a10],a4
.L462:
	lea	a4,[a10]8
.L402:
	movh.a	a5,#@his(.2.str)
.L401:
	lea	a5,[a5]@los(.2.str)
	call	sprintf
.L463:
	extr.u	d5,d2,#0,#16
.L35:
	mov	d4,#16
.L464:
	lea	a4,[a10]8
.L465:
	mov	d6,#10000
	call	wifi_spi_set_parameter
.L403:
	mov	d15,d2
.L405:
	call	wifi_spi_get_ip_addr_port
.L404:
	mov	d2,d15
.L406:
	j	.L36
.L36:
	ret
.L222:
	
__wifi_spi_wifi_connect_function_end:
	.size	wifi_spi_wifi_connect,__wifi_spi_wifi_connect_function_end-wifi_spi_wifi_connect
.L128:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_socket_connect',code,cluster('wifi_spi_socket_connect')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_socket_connect'
	.align	2
	
	.global	wifi_spi_socket_connect
; Function wifi_spi_socket_connect
.L107:
wifi_spi_socket_connect:	.type	func
	sub.a	a10,#64
.L407:
	st.a	[a10],a4
.L470:
	st.a	[a10]4,a5
.L471:
	st.a	[a10]8,a6
.L472:
	st.a	[a10]12,a7
.L473:
	lea	a4,[a10]16
.L410:
	movh.a	a5,#@his(.3.str)
.L408:
	lea	a5,[a5]@los(.3.str)
	call	sprintf
.L409:
	extr.u	d5,d2,#0,#16
.L411:
	mov	d4,#17
.L474:
	lea	a4,[a10]16
.L475:
	mov.u	d6,#50000
	call	wifi_spi_set_parameter
.L412:
	mov	d15,d2
.L414:
	call	wifi_spi_get_ip_addr_port
.L413:
	mov	d2,d15
.L415:
	j	.L37
.L37:
	ret
.L231:
	
__wifi_spi_socket_connect_function_end:
	.size	wifi_spi_socket_connect,__wifi_spi_socket_connect_function_end-wifi_spi_socket_connect
.L133:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_socket_disconnect',code,cluster('wifi_spi_socket_disconnect')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_socket_disconnect'
	.align	2
	
	.global	wifi_spi_socket_disconnect
; Function wifi_spi_socket_disconnect
.L109:
wifi_spi_socket_disconnect:	.type	func
	sub.a	a10,#40
.L416:
	mov	d4,#4
.L480:
	lea	a4,[a10]0
.L481:
	mov	d5,#1000
	call	wifi_spi_get_parameter
.L482:
	j	.L38
.L38:
	ret
.L240:
	
__wifi_spi_socket_disconnect_function_end:
	.size	wifi_spi_socket_disconnect,__wifi_spi_socket_disconnect_function_end-wifi_spi_socket_disconnect
.L138:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_reset',code,cluster('wifi_spi_reset')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_reset'
	.align	2
	
	.global	wifi_spi_reset
; Function wifi_spi_reset
.L111:
wifi_spi_reset:	.type	func
	sub.a	a10,#8
.L39:
	mov	d15,#1
.L754:
	st.b	[a10],d15
.L755:
	mov.u	d15,#42405
.L756:
	st.h	[a10]2,d15
.L757:
	mov	d4,#1000
	call	wifi_spi_wait_idle
.L417:
	mov	d15,d2
.L418:
	jeq	d15,#0,.L40
.L758:
	j	.L41
.L40:
	lea	a4,[a10]0
.L759:
	mov	d4,#4
.L760:
	mov.a	a5,#0
.L761:
	mov	d5,#0
	call	wifi_spi_write
.L41:
	mov	d2,d15
.L419:
	j	.L42
.L42:
	ret
.L303:
	
__wifi_spi_reset_function_end:
	.size	wifi_spi_reset,__wifi_spi_reset_function_end-wifi_spi_reset
.L208:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_udp_send_now',code,cluster('wifi_spi_udp_send_now')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_udp_send_now'
	.align	2
	
	.global	wifi_spi_udp_send_now
; Function wifi_spi_udp_send_now
.L113:
wifi_spi_udp_send_now:	.type	func
	sub.a	a10,#40
.L420:
	mov	d8,#1
.L421:
	movh.a	a15,#@his(wifi_spi_mutex)
	lea	a15,[a15]@los(wifi_spi_mutex)
	ld.bu	d15,[a15]
.L487:
	jne	d15,#0,.L43
.L488:
	movh.a	a15,#@his(wifi_spi_mutex)
	lea	a15,[a15]@los(wifi_spi_mutex)
.L489:
	mov	d15,#1
.L490:
	st.b	[a15],d15
.L44:
	mov	d4,#1000
	call	wifi_spi_wait_idle
.L491:
	jeq	d2,#0,.L45
.L492:
	j	.L46
.L45:
	mov	d15,#3
.L493:
	st.b	[a10],d15
.L494:
	mov	d15,#0
.L495:
	st.h	[a10]2,d15
.L496:
	lea	a4,[a10]0
.L497:
	mov	d4,#32
	call	wifi_spi_transfer_command
.L498:
	ld.bu	d0,[a10]
.L499:
	mov	d15,#144
.L500:
	jeq	d15,d0,.L47
.L501:
	ld.bu	d0,[a10]
.L502:
	mov	d15,#145
.L503:
	jne	d15,d0,.L48
.L47:
	ld.hu	d15,[a10]2
.L504:
	jeq	d15,#0,.L49
.L505:
	movh.a	a4,#@his(wifi_spi_fifo)
	lea	a4,[a4]@los(wifi_spi_fifo)
.L506:
	lea	a5,[a10]4
.L507:
	ld.hu	d4,[a10]2
	call	fifo_write_buffer
.L49:
.L48:
	mov	d4,#1000
	call	wifi_spi_wait_idle
.L508:
	jeq	d2,#0,.L50
.L509:
	j	.L51
.L50:
	mov	d15,#2
.L510:
	st.b	[a10],d15
.L511:
	mov	d15,#0
.L512:
	st.h	[a10]2,d15
.L513:
	lea	a4,[a10]0
.L514:
	mov	d4,#0
	call	wifi_spi_transfer_command
.L515:
	ld.bu	d0,[a10]
.L516:
	mov	d15,#128
.L517:
	jne	d15,d0,.L52
.L518:
	mov	d8,#0
.L52:
.L51:
.L46:
	movh.a	a15,#@his(wifi_spi_mutex)
	lea	a15,[a15]@los(wifi_spi_mutex)
.L519:
	mov	d15,#0
.L520:
	st.b	[a15],d15
.L43:
	mov	d2,d8
.L422:
	j	.L53
.L53:
	ret
.L243:
	
__wifi_spi_udp_send_now_function_end:
	.size	wifi_spi_udp_send_now,__wifi_spi_udp_send_now_function_end-wifi_spi_udp_send_now
.L143:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_send_buffer',code,cluster('wifi_spi_send_buffer')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_send_buffer'
	.align	2
	
	.global	wifi_spi_send_buffer
; Function wifi_spi_send_buffer
.L115:
wifi_spi_send_buffer:	.type	func
	sub.a	a10,#40
.L423:
	mov.aa	a15,a4
.L424:
	mov	d9,d4
.L426:
	movh.a	a2,#@his(wifi_spi_mutex)
	lea	a2,[a2]@los(wifi_spi_mutex)
	ld.bu	d15,[a2]
.L525:
	jne	d15,#0,.L54
.L526:
	movh.a	a2,#@his(wifi_spi_mutex)
	lea	a2,[a2]@los(wifi_spi_mutex)
.L527:
	mov	d0,#1
.L528:
	st.b	[a2],d0
.L529:
	j	.L55
.L56:
	mov	d0,#4088
.L530:
	jge.u	d0,d9,.L57
.L531:
	mov	d8,#4088
.L427:
	j	.L58
.L57:
	mov	d8,d9
.L58:
	mov	d4,#1000
	call	wifi_spi_wait_idle
.L532:
	jeq	d2,#0,.L59
.L533:
	j	.L60
.L59:
	lea	a5,[a10]0
.L534:
	mov.aa	a4,a15
.L428:
	mov	d4,d8
	call	wifi_spi_transfer_data
.L429:
	ld.bu	d0,[a10]
.L535:
	mov	d15,#144
.L536:
	jeq	d15,d0,.L61
.L537:
	ld.bu	d0,[a10]
.L538:
	mov	d15,#145
.L539:
	jne	d15,d0,.L62
.L61:
	ld.hu	d15,[a10]2
.L540:
	jeq	d15,#0,.L63
.L541:
	movh.a	a4,#@his(wifi_spi_fifo)
	lea	a4,[a4]@los(wifi_spi_fifo)
.L542:
	lea	a5,[a10]4
.L543:
	ld.hu	d4,[a10]2
	call	fifo_write_buffer
.L63:
.L62:
	sub	d9,d8
.L544:
	addsc.a	a15,a15,d8,#0
.L55:
	jne	d9,#0,.L56
.L60:
	j	.L64
.L65:
	mov	d4,#1000
	call	wifi_spi_wait_idle
.L545:
	jeq	d2,#0,.L66
.L546:
	j	.L67
.L66:
	mov	d15,#2
.L547:
	st.b	[a10],d15
.L548:
	mov	d15,#0
.L549:
	st.h	[a10]2,d15
.L550:
	lea	a4,[a10]0
.L551:
	mov	d4,#32
	call	wifi_spi_transfer_command
.L552:
	ld.bu	d0,[a10]
.L553:
	mov	d15,#144
.L554:
	jeq	d15,d0,.L68
.L555:
	ld.bu	d0,[a10]
.L556:
	mov	d15,#145
.L557:
	jne	d15,d0,.L69
.L68:
	ld.hu	d15,[a10]2
.L558:
	jeq	d15,#0,.L70
.L559:
	movh.a	a4,#@his(wifi_spi_fifo)
	lea	a4,[a4]@los(wifi_spi_fifo)
.L560:
	lea	a5,[a10]4
.L561:
	ld.hu	d4,[a10]2
	call	fifo_write_buffer
.L70:
.L69:
.L64:
	ld.bu	d0,[a10]
.L562:
	mov	d15,#144
.L563:
	jeq	d15,d0,.L65
.L67:
	movh.a	a15,#@his(wifi_spi_mutex)
.L425:
	lea	a15,[a15]@los(wifi_spi_mutex)
.L564:
	mov	d15,#0
.L565:
	st.b	[a15],d15
.L54:
	mov	d2,d9
.L430:
	j	.L71
.L71:
	ret
.L247:
	
__wifi_spi_send_buffer_function_end:
	.size	wifi_spi_send_buffer,__wifi_spi_send_buffer_function_end-wifi_spi_send_buffer
.L148:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_read_buffer',code,cluster('wifi_spi_read_buffer')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_read_buffer'
	.align	2
	
	.global	wifi_spi_read_buffer
; Function wifi_spi_read_buffer
.L117:
wifi_spi_read_buffer:	.type	func
	sub.a	a10,#40
.L431:
	mov.aa	a12,a4
.L434:
	mov	d15,d4
.L435:
	mov.a	a15,#0
	ne.a	d4,a15,a12
.L433:
	movh.a	a4,#@his(.4.str)
.L432:
	lea	a4,[a4]@los(.4.str)
	mov	d5,#562
	call	debug_assert_handler
.L257:
	st.w	[a10],d15
.L570:
	movh.a	a15,#@his(wifi_spi_mutex)
	lea	a15,[a15]@los(wifi_spi_mutex)
	ld.bu	d15,[a15]
.L436:
	jne	d15,#0,.L72
.L571:
	movh.a	a15,#@his(wifi_spi_mutex)
	lea	a15,[a15]@los(wifi_spi_mutex)
.L572:
	mov	d15,#1
.L573:
	st.b	[a15],d15
.L73:
	mov	d4,#1000
	call	wifi_spi_wait_idle
.L574:
	jeq	d2,#0,.L74
.L575:
	j	.L75
.L74:
	mov	d15,#2
.L576:
	st.b	[a10]4,d15
.L577:
	mov	d15,#0
.L578:
	st.h	[a10]6,d15
.L579:
	lea	a4,[a10]4
.L580:
	mov	d4,#32
	call	wifi_spi_transfer_command
.L581:
	ld.bu	d15,[a10]4
.L582:
	mov	d0,#144
.L583:
	jeq	d15,d0,.L76
.L584:
	ld.bu	d15,[a10]4
.L585:
	mov	d0,#145
.L586:
	jne	d15,d0,.L77
.L76:
	ld.hu	d15,[a10]6
.L587:
	jeq	d15,#0,.L78
.L588:
	movh.a	a4,#@his(wifi_spi_fifo)
	lea	a4,[a4]@los(wifi_spi_fifo)
.L589:
	lea	a5,[a10]8
.L590:
	ld.hu	d4,[a10]6
	call	fifo_write_buffer
.L78:
.L77:
	ld.bu	d15,[a10]4
.L591:
	mov	d0,#144
.L592:
	jeq	d15,d0,.L73
.L75:
	movh.a	a15,#@his(wifi_spi_mutex)
	lea	a15,[a15]@los(wifi_spi_mutex)
.L593:
	mov	d15,#0
.L594:
	st.b	[a15],d15
.L72:
	movh.a	a4,#@his(wifi_spi_fifo)
	lea	a4,[a4]@los(wifi_spi_fifo)
.L595:
	lea	a6,[a10]0
.L596:
	mov	d4,#0
	mov.aa	a5,a12
.L437:
	call	fifo_read_buffer
.L438:
	ld.w	d2,[a10]
.L597:
	j	.L79
.L79:
	ret
.L253:
	
__wifi_spi_read_buffer_function_end:
	.size	wifi_spi_read_buffer,__wifi_spi_read_buffer_function_end-wifi_spi_read_buffer
.L153:
	; End of function
	
	.sdecl	'.text.zf_device_wifi_spi.wifi_spi_init',code,cluster('wifi_spi_init')
	.sect	'.text.zf_device_wifi_spi.wifi_spi_init'
	.align	2
	
	.global	wifi_spi_init
; Function wifi_spi_init
.L119:
wifi_spi_init:	.type	func
	sub.a	a10,#16
.L439:
	mov.aa	a15,a4
.L442:
	mov.aa	a12,a5
.L443:
	movh.a	a4,#@his(wifi_spi_fifo)
.L441:
	lea	a4,[a4]@los(wifi_spi_fifo)
.L602:
	mov	d4,#0
.L603:
	movh.a	a5,#@his(wifi_spi_buffer)
.L440:
	lea	a5,[a5]@los(wifi_spi_buffer)
.L604:
	mov	d5,#1024
	call	fifo_init
.L605:
	mov	d15,#314
	st.h	[a10],d15
.L606:
	mov	d15,#319
	st.h	[a10]4,d15
.L607:
	mov	d15,#403
	st.h	[a10]8,d15
.L608:
	mov	d4,#3
.L609:
	mov	d5,#0
.L610:
	mov.u	d6,#50048
	addih	d6,d6,#457
.L611:
	mov	d7,#309
	call	spi_init
.L612:
	mov	d4,#706
.L613:
	mov	d5,#1
.L614:
	mov	d6,#1
.L615:
	mov	d7,#3
	call	gpio_init
.L616:
	mov	d4,#737
.L617:
	mov	d5,#1
.L618:
	mov	d6,#1
.L619:
	mov	d7,#3
	call	gpio_init
.L620:
	mov	d4,#488
.L621:
	mov	d5,#0
.L622:
	mov	d6,#0
.L623:
	mov	d7,#2
	call	gpio_init
.L624:
	mov	d4,#737
.L625:
	mov	d5,#0
	call	gpio_set_level
.L626:
	mov	d4,#10
	call	system_delay_ms
.L627:
	mov	d4,#737
.L628:
	mov	d5,#1
	call	gpio_set_level
.L629:
	mov	d4,#100
	call	system_delay_ms
.L630:
	movh.a	a2,#@his(wifi_spi_mutex)
	lea	a2,[a2]@los(wifi_spi_mutex)
.L631:
	mov	d15,#0
.L632:
	st.b	[a2],d15
.L80:
	call	wifi_spi_get_version
.L444:
	jeq	d2,#0,.L81
.L633:
	j	.L82
.L81:
	call	wifi_spi_get_mac_addr
.L445:
	mov.aa	a4,a15
.L446:
	mov.aa	a5,a12
.L448:
	call	wifi_spi_wifi_connect
.L447:
	jeq	d2,#0,.L83
.L634:
	j	.L84
.L83:
.L84:
.L82:
	j	.L85
.L85:
	ret
.L260:
	
__wifi_spi_init_function_end:
	.size	wifi_spi_init,__wifi_spi_init_function_end-wifi_spi_init
.L158:
	; End of function
	
	.sdecl	'.bss.zf_device_wifi_spi.wifi_spi_version',data,cluster('wifi_spi_version')
	.sect	'.bss.zf_device_wifi_spi.wifi_spi_version'
	.global	wifi_spi_version
wifi_spi_version:	.type	object
	.size	wifi_spi_version,12
	.space	12
	.sdecl	'.bss.zf_device_wifi_spi.wifi_spi_mac_addr',data,cluster('wifi_spi_mac_addr')
	.sect	'.bss.zf_device_wifi_spi.wifi_spi_mac_addr'
	.global	wifi_spi_mac_addr
wifi_spi_mac_addr:	.type	object
	.size	wifi_spi_mac_addr,20
	.space	20
	.sdecl	'.bss.zf_device_wifi_spi.wifi_spi_ip_addr_port',data,cluster('wifi_spi_ip_addr_port')
	.sect	'.bss.zf_device_wifi_spi.wifi_spi_ip_addr_port'
	.global	wifi_spi_ip_addr_port
wifi_spi_ip_addr_port:	.type	object
	.size	wifi_spi_ip_addr_port,25
	.space	25
	.sdecl	'.bss.zf_device_wifi_spi.wifi_spi_fifo',data,cluster('wifi_spi_fifo')
	.sect	'.bss.zf_device_wifi_spi.wifi_spi_fifo'
	.align	4
wifi_spi_fifo:	.type	object
	.size	wifi_spi_fifo,24
	.space	24
	.sdecl	'.bss.zf_device_wifi_spi.wifi_spi_buffer',data,cluster('wifi_spi_buffer')
	.sect	'.bss.zf_device_wifi_spi.wifi_spi_buffer'
wifi_spi_buffer:	.type	object
	.size	wifi_spi_buffer,1024
	.space	1024
	.sdecl	'.bss.zf_device_wifi_spi.wifi_spi_mutex',data,cluster('wifi_spi_mutex')
	.sect	'.bss.zf_device_wifi_spi.wifi_spi_mutex'
wifi_spi_mutex:	.type	object
	.size	wifi_spi_mutex,1
	.space	1
	.sdecl	'.rodata.zf_device_wifi_spi..1.str',data,rom
	.sect	'.rodata.zf_device_wifi_spi..1.str'
.1.str:	.type	object
	.size	.1.str,9
	.byte	37,115,13,10,37,115,13,10
	.space	1
	.sdecl	'.rodata.zf_device_wifi_spi..2.str',data,rom
	.sect	'.rodata.zf_device_wifi_spi..2.str'
.2.str:	.type	object
	.size	.2.str,5
	.byte	37,115,13,10
	.space	1
	.sdecl	'.rodata.zf_device_wifi_spi..3.str',data,rom
	.sect	'.rodata.zf_device_wifi_spi..3.str'
.3.str:	.type	object
	.size	.3.str,17
	.byte	37,115,13,10,37,115,13,10
	.byte	37,115,13,10,37,115,13,10
	.space	1
	.sdecl	'.rodata.zf_device_wifi_spi..4.str',data,rom
	.sect	'.rodata.zf_device_wifi_spi..4.str'
.4.str:	.type	object
	.size	.4.str,44
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,119,105,102,105,95,115,112
	.byte	105,46,99
	.space	1
	.calls	'wifi_spi_wait_idle','system_delay_us'
	.calls	'wifi_spi_wait_idle','gpio_get_level'
	.calls	'wifi_spi_write','get_port'
	.calls	'wifi_spi_write','spi_write_8bit_array'
	.calls	'wifi_spi_transfer_command','get_port'
	.calls	'wifi_spi_transfer_command','spi_transfer_8bit'
	.calls	'wifi_spi_transfer_data','get_port'
	.calls	'wifi_spi_transfer_data','spi_transfer_8bit'
	.calls	'wifi_spi_transfer_data','spi_write_8bit_array'
	.calls	'wifi_spi_transfer_data','memcpy'
	.calls	'wifi_spi_set_parameter','wifi_spi_wait_idle'
	.calls	'wifi_spi_set_parameter','wifi_spi_write'
	.calls	'wifi_spi_set_parameter','wifi_spi_transfer_command'
	.calls	'wifi_spi_set_parameter','system_delay_us'
	.calls	'wifi_spi_get_parameter','wifi_spi_wait_idle'
	.calls	'wifi_spi_get_parameter','wifi_spi_write'
	.calls	'wifi_spi_get_parameter','wifi_spi_transfer_command'
	.calls	'wifi_spi_get_version','wifi_spi_get_parameter'
	.calls	'wifi_spi_get_version','memcpy'
	.calls	'wifi_spi_get_mac_addr','wifi_spi_get_parameter'
	.calls	'wifi_spi_get_mac_addr','memcpy'
	.calls	'wifi_spi_get_ip_addr_port','wifi_spi_get_parameter'
	.calls	'wifi_spi_get_ip_addr_port','memcpy'
	.calls	'wifi_spi_wifi_connect','sprintf'
	.calls	'wifi_spi_wifi_connect','wifi_spi_set_parameter'
	.calls	'wifi_spi_wifi_connect','wifi_spi_get_ip_addr_port'
	.calls	'wifi_spi_socket_connect','sprintf'
	.calls	'wifi_spi_socket_connect','wifi_spi_set_parameter'
	.calls	'wifi_spi_socket_connect','wifi_spi_get_ip_addr_port'
	.calls	'wifi_spi_socket_disconnect','wifi_spi_get_parameter'
	.calls	'wifi_spi_reset','wifi_spi_wait_idle'
	.calls	'wifi_spi_reset','wifi_spi_write'
	.calls	'wifi_spi_udp_send_now','wifi_spi_wait_idle'
	.calls	'wifi_spi_udp_send_now','wifi_spi_transfer_command'
	.calls	'wifi_spi_udp_send_now','fifo_write_buffer'
	.calls	'wifi_spi_send_buffer','wifi_spi_wait_idle'
	.calls	'wifi_spi_send_buffer','wifi_spi_transfer_data'
	.calls	'wifi_spi_send_buffer','fifo_write_buffer'
	.calls	'wifi_spi_send_buffer','wifi_spi_transfer_command'
	.calls	'wifi_spi_read_buffer','debug_assert_handler'
	.calls	'wifi_spi_read_buffer','wifi_spi_wait_idle'
	.calls	'wifi_spi_read_buffer','wifi_spi_transfer_command'
	.calls	'wifi_spi_read_buffer','fifo_write_buffer'
	.calls	'wifi_spi_read_buffer','fifo_read_buffer'
	.calls	'wifi_spi_init','fifo_init'
	.calls	'wifi_spi_init','spi_init'
	.calls	'wifi_spi_init','gpio_init'
	.calls	'wifi_spi_init','gpio_set_level'
	.calls	'wifi_spi_init','system_delay_ms'
	.calls	'wifi_spi_init','wifi_spi_get_version'
	.calls	'wifi_spi_init','wifi_spi_get_mac_addr'
	.calls	'wifi_spi_init','wifi_spi_wifi_connect'
	.calls	'wifi_spi_wait_idle','',0
	.calls	'wifi_spi_write','',0
	.calls	'wifi_spi_transfer_command','',0
	.calls	'wifi_spi_transfer_data','',0
	.calls	'wifi_spi_set_parameter','',8
	.calls	'wifi_spi_get_parameter','',0
	.calls	'wifi_spi_get_version','',40
	.calls	'wifi_spi_get_mac_addr','',40
	.calls	'wifi_spi_get_ip_addr_port','',40
	.calls	'wifi_spi_wifi_connect','',72
	.calls	'wifi_spi_socket_connect','',64
	.calls	'wifi_spi_socket_disconnect','',40
	.calls	'wifi_spi_reset','',8
	.calls	'wifi_spi_udp_send_now','',40
	.calls	'wifi_spi_send_buffer','',40
	.calls	'wifi_spi_read_buffer','',40
	.extern	sprintf
	.extern	memcpy
	.extern	debug_assert_handler
	.extern	fifo_write_buffer
	.extern	fifo_read_buffer
	.extern	fifo_init
	.extern	system_delay_us
	.extern	system_delay_ms
	.extern	get_port
	.extern	gpio_set_level
	.extern	gpio_get_level
	.extern	gpio_init
	.extern	spi_write_8bit_array
	.extern	spi_transfer_8bit
	.extern	spi_init
	.extern	__printf_simple
	.calls	'wifi_spi_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L121:
	.word	42538
	.half	3
	.word	.L122
	.byte	4
.L120:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L123
	.byte	2,1,1,3
	.word	206
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	209
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	254
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	266
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	346
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	320
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	352
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	352
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	320
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L221:
	.byte	7
	.byte	'unsigned char',0,1,8
.L229:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	816
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1387
	.byte	4,2,35,0,0,14,4
	.word	461
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1515
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1730
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2382
	.byte	4,2,35,0,0,14,24
	.word	461
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3009
	.byte	4,2,35,0,0,14,8
	.word	461
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3334
	.byte	4,2,35,0,0,14,12
	.word	461
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4326
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4642
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	478
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5163
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5337
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6002
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6350
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6474
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6738
	.byte	4,2,35,0,0,14,76
	.word	461
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6991
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	776
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1347
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1466
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1506
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1690
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1905
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2122
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2342
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1506
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2656
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2696
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2969
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3285
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3325
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3625
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3665
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4000
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4286
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3325
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4433
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4602
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4774
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4949
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5123
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5297
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5473
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5629
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5962
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6310
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3325
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6434
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6683
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6942
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6982
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7038
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7605
	.byte	4,3,35,252,1,0,16
	.word	7645
	.byte	3
	.word	8248
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8253
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	461
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8258
	.byte	6,0,7
	.byte	'char',0,1,6
.L223:
	.byte	3
	.word	8439
	.byte	19
	.word	8447
	.byte	20
	.word	8439
	.byte	3
	.word	8457
	.byte	19
	.word	8462
	.byte	21
	.byte	'sprintf',0,5,146,1,16
	.word	454
	.byte	1,1,1,1,22,5,146,1,32
	.word	8452
	.byte	22,5,146,1,55
	.word	8467
	.byte	23,5,146,1,67,0,19
	.word	352
	.byte	20
	.word	346
	.byte	3
	.word	8522
	.byte	19
	.word	8527
	.byte	21
	.byte	'memcpy',0,6,53,17
	.word	352
	.byte	1,1,1,1,22,6,53,33
	.word	8517
	.byte	22,6,53,56
	.word	8532
	.byte	22,6,53,68
	.word	438
	.byte	0,24
	.byte	'debug_assert_handler',0,7,112,9,1,1,1,1,5
	.byte	'pass',0,7,112,47
	.word	461
	.byte	5
	.byte	'file',0,7,112,59
	.word	8447
	.byte	5
	.byte	'line',0,7,112,69
	.word	454
	.byte	0,17,8,42,9,1,18
	.byte	'FIFO_SUCCESS',0,0,18
	.byte	'FIFO_RESET_UNDO',0,1,18
	.byte	'FIFO_CLEAR_UNDO',0,2,18
	.byte	'FIFO_BUFFER_NULL',0,3,18
	.byte	'FIFO_WRITE_UNDO',0,4,18
	.byte	'FIFO_SPACE_NO_ENOUGH',0,5,18
	.byte	'FIFO_READ_UNDO',0,6,18
	.byte	'FIFO_DATA_NO_ENOUGH',0,7,0,17,8,78,9,1,18
	.byte	'FIFO_DATA_8BIT',0,0,18
	.byte	'FIFO_DATA_16BIT',0,1,18
	.byte	'FIFO_DATA_32BIT',0,2,0
.L246:
	.byte	7
	.byte	'unsigned long int',0,4,7
.L309:
	.byte	25,8,85,9,24,13
	.byte	'execution',0
	.word	461
	.byte	1,2,35,0,13
	.byte	'type',0
	.word	8806
	.byte	1,2,35,1,13
	.byte	'buffer',0
	.word	352
	.byte	4,2,35,4,13
	.byte	'head',0
	.word	8865
	.byte	4,2,35,8,13
	.byte	'end',0
	.word	8865
	.byte	4,2,35,12,13
	.byte	'size',0
	.word	8865
	.byte	4,2,35,16,13
	.byte	'max',0
	.word	8865
	.byte	4,2,35,20,0,3
	.word	8886
	.byte	21
	.byte	'fifo_write_buffer',0,8,100,17
	.word	8650
	.byte	1,1,1,1,5
	.byte	'fifo',0,8,100,55
	.word	8995
	.byte	5
	.byte	'dat',0,8,100,67
	.word	352
	.byte	5
	.byte	'length',0,8,100,79
	.word	8865
	.byte	0,3
	.word	8865
	.byte	17,8,72,9,1,18
	.byte	'FIFO_READ_AND_CLEAN',0,0,18
	.byte	'FIFO_READ_ONLY',0,1,0,21
	.byte	'fifo_read_buffer',0,8,102,17
	.word	8650
	.byte	1,1,1,1,5
	.byte	'fifo',0,8,102,55
	.word	8995
	.byte	5
	.byte	'dat',0,8,102,67
	.word	352
	.byte	5
	.byte	'length',0,8,102,80
	.word	9071
	.byte	5
	.byte	'flag',0,8,102,108
	.word	9076
	.byte	0,21
	.byte	'fifo_init',0,8,105,17
	.word	8650
	.byte	1,1,1,1,5
	.byte	'fifo',0,8,105,55
	.word	8995
	.byte	5
	.byte	'type',0,8,105,81
	.word	8806
	.byte	5
	.byte	'buffer_addr',0,8,105,93
	.word	352
	.byte	5
	.byte	'size',0,8,105,113
	.word	8865
	.byte	0,24
	.byte	'system_delay_us',0,9,45,9,1,1,1,1,5
	.byte	'time',0,9,45,45
	.word	8865
	.byte	0,24
	.byte	'system_delay_ms',0,9,46,9,1,1,1,1,5
	.byte	'time',0,9,46,45
	.word	8865
	.byte	0,26
	.word	214
	.byte	27
	.word	240
	.byte	6,0,26
	.word	275
	.byte	27
	.word	307
	.byte	6,0,26
	.word	357
	.byte	27
	.word	376
	.byte	6,0,26
	.word	392
	.byte	27
	.word	407
	.byte	27
	.word	421
	.byte	6,0,26
	.word	8361
	.byte	27
	.word	8389
	.byte	27
	.word	8403
	.byte	27
	.word	8421
	.byte	6,0,17,10,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,21
	.byte	'get_port',0,10,114,13
	.word	8253
	.byte	1,1,1,1,5
	.byte	'pin',0,10,114,56
	.word	9437
	.byte	0,24
	.byte	'gpio_set_level',0,10,139,1,7,1,1,1,1,5
	.byte	'pin',0,10,139,1,40
	.word	9437
	.byte	5
	.byte	'dat',0,10,139,1,51
	.word	461
	.byte	0,21
	.byte	'gpio_get_level',0,10,140,1,7
	.word	461
	.byte	1,1,1,1,5
	.byte	'pin',0,10,140,1,40
	.word	9437
	.byte	0,17,10,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,10,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,24
	.byte	'gpio_init',0,10,143,1,7,1,1,1,1,5
	.byte	'pin',0,10,143,1,40
	.word	9437
	.byte	5
	.byte	'dir',0,10,143,1,59
	.word	11504
	.byte	5
	.byte	'dat',0,10,143,1,70
	.word	461
	.byte	5
	.byte	'pinconf',0,10,143,1,90
	.word	11522
	.byte	0,17,11,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,20
	.word	461
.L248:
	.byte	3
	.word	11723
	.byte	24
	.byte	'spi_write_8bit_array',0,11,144,1,13,1,1,1,1,5
	.byte	'spi_n',0,11,144,1,61
	.word	11685
	.byte	5
	.byte	'data',0,11,144,1,81
	.word	11728
	.byte	5
	.byte	'len',0,11,144,1,94
	.word	8865
	.byte	0
.L254:
	.byte	3
	.word	461
	.byte	24
	.byte	'spi_transfer_8bit',0,11,167,1,13,1,1,1,1,5
	.byte	'spi_n',0,11,167,1,61
	.word	11685
	.byte	5
	.byte	'write_buffer',0,11,167,1,81
	.word	11728
	.byte	5
	.byte	'read_buffer',0,11,167,1,102
	.word	11806
	.byte	5
	.byte	'len',0,11,167,1,122
	.word	8865
	.byte	0,17,11,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,11,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,11,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,11,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,11,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,24
	.byte	'spi_init',0,11,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,11,170,1,61
	.word	11685
	.byte	5
	.byte	'mode',0,11,170,1,82
	.word	11910
	.byte	5
	.byte	'baud',0,11,170,1,95
	.word	8865
	.byte	5
	.byte	'sck_pin',0,11,170,1,118
	.word	11964
	.byte	5
	.byte	'mosi_pin',0,11,170,1,145,1
	.word	12237
	.byte	5
	.byte	'miso_pin',0,11,170,1,173,1
	.word	12491
	.byte	5
	.byte	'cs_pin',0,11,170,1,199,1
	.word	12745
	.byte	0
.L227:
	.byte	14,64
	.word	461
	.byte	15,63,0
.L237:
	.byte	14,41
	.word	461
	.byte	15,40,0
.L287:
	.byte	25,12,123,9,4,13
	.byte	'command',0
	.word	461
	.byte	1,2,35,0,13
	.byte	'reserve',0
	.word	461
	.byte	1,2,35,1,13
	.byte	'length',0
	.word	478
	.byte	2,2,35,2,0,14,32
	.word	461
	.byte	15,31,0
.L241:
	.byte	25,12,131,1,9,36,13
	.byte	'head',0
	.word	13735
	.byte	4,2,35,0,13
	.byte	'buffer',0
	.word	13791
	.byte	32,2,35,4,0
.L273:
	.byte	3
	.word	13800
.L281:
	.byte	17,12,87,9,1,18
	.byte	'WIFI_SPI_INVALID1',0,0,18
	.byte	'WIFI_SPI_RESET',0,1,18
	.byte	'WIFI_SPI_DATA',0,2,18
	.byte	'WIFI_SPI_UDP_SEND',0,3,18
	.byte	'WIFI_SPI_CLOSE_SOCKET',0,4,18
	.byte	'WIFI_SPI_SET_WIFI_INFORMATION',0,16,18
	.byte	'WIFI_SPI_SET_SOCKET_INFORMATION',0,17,18
	.byte	'WIFI_SPI_GET_VERSION',0,32,18
	.byte	'WIFI_SPI_GET_MAC_ADDR',0,33,18
	.byte	'WIFI_SPI_GET_IP_ADDR',0,34,18
	.byte	'WIFI_SPI_REPLY_OK',0,128,1,18
	.byte	'WIFI_SPI_REPLY_ERROR',0,129,1,18
	.byte	'WIFI_SPI_REPLY_DATA_START',0,144,1,18
	.byte	'WIFI_SPI_REPLY_DATA_END',0,145,1,18
	.byte	'WIFI_SPI_REPLY_VERSION',0,160,1,18
	.byte	'WIFI_SPI_REPLY_MAC_ADDR',0,161,1,18
	.byte	'WIFI_SPI_REPLY_IP_ADDR',0,162,1,18
	.byte	'WIFI_SPI_INVALID2',0,255,1,0,7
	.byte	'short int',0,2,5,28
	.byte	'__wchar_t',0,13,1,1
	.word	14282
	.byte	28
	.byte	'__size_t',0,13,1,1
	.word	438
	.byte	28
	.byte	'__ptrdiff_t',0,13,1,1
	.word	454
	.byte	29,1,3
	.word	14350
	.byte	28
	.byte	'__codeptr',0,13,1,1
	.word	14352
	.byte	28
	.byte	'__intptr_t',0,13,1,1
	.word	454
	.byte	28
	.byte	'__uintptr_t',0,13,1,1
	.word	438
	.byte	28
	.byte	'size_t',0,5,31,25
	.word	438
	.byte	28
	.byte	'_iob_flag_t',0,5,82,25
	.word	478
	.byte	28
	.byte	'boolean',0,14,101,29
	.word	461
	.byte	28
	.byte	'uint8',0,14,105,29
	.word	461
	.byte	28
	.byte	'uint16',0,14,109,29
	.word	478
	.byte	28
	.byte	'uint32',0,14,113,29
	.word	8865
	.byte	28
	.byte	'uint64',0,14,118,29
	.word	320
	.byte	28
	.byte	'sint16',0,14,126,29
	.word	14282
	.byte	7
	.byte	'long int',0,4,5,28
	.byte	'sint32',0,14,131,1,29
	.word	14539
	.byte	7
	.byte	'long long int',0,8,5,28
	.byte	'sint64',0,14,138,1,29
	.word	14567
	.byte	28
	.byte	'float32',0,14,167,1,29
	.word	266
	.byte	28
	.byte	'pvoid',0,15,57,28
	.word	352
	.byte	28
	.byte	'Ifx_TickTime',0,15,79,28
	.word	14567
	.byte	7
	.byte	'char',0,1,6,28
	.byte	'int8',0,16,54,29
	.word	14652
	.byte	28
	.byte	'int16',0,16,55,29
	.word	14282
	.byte	28
	.byte	'int32',0,16,56,29
	.word	454
	.byte	28
	.byte	'int64',0,16,57,29
	.word	14567
	.byte	28
	.byte	'fifo_state_enum',0,8,53,2
	.word	8650
	.byte	28
	.byte	'fifo_operation_enum',0,8,76,2
	.word	9076
	.byte	28
	.byte	'fifo_data_type_enum',0,8,83,2
	.word	8806
	.byte	28
	.byte	'fifo_struct',0,8,94,2
	.word	8886
	.byte	28
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7078
	.byte	28
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6991
	.byte	28
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3334
	.byte	28
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1387
	.byte	28
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2382
	.byte	28
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1515
	.byte	28
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2162
	.byte	28
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1730
	.byte	28
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1945
	.byte	28
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6350
	.byte	28
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6474
	.byte	28
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6558
	.byte	28
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6738
	.byte	28
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4989
	.byte	28
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5513
	.byte	28
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5163
	.byte	28
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5337
	.byte	28
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6002
	.byte	28
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	816
	.byte	28
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4326
	.byte	28
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4814
	.byte	28
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4473
	.byte	28
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4642
	.byte	28
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5669
	.byte	28
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	500
	.byte	28
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4040
	.byte	28
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3674
	.byte	28
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2705
	.byte	28
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3009
	.byte	28
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7605
	.byte	28
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7038
	.byte	28
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3625
	.byte	28
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1466
	.byte	28
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2656
	.byte	28
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1690
	.byte	28
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2342
	.byte	28
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1905
	.byte	28
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2122
	.byte	28
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6434
	.byte	28
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6683
	.byte	28
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6942
	.byte	28
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6310
	.byte	28
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5123
	.byte	28
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5629
	.byte	28
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5297
	.byte	28
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5473
	.byte	28
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1347
	.byte	28
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5962
	.byte	28
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4433
	.byte	28
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4949
	.byte	28
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4602
	.byte	28
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4774
	.byte	28
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	776
	.byte	28
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4286
	.byte	28
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4000
	.byte	28
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2969
	.byte	28
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3285
	.byte	16
	.word	7645
	.byte	28
	.byte	'Ifx_P',0,4,139,6,3
	.word	16133
	.byte	17,17,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,28
	.byte	'IfxScu_WDTCON1_IR',0,17,255,10,3
	.word	16153
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,18,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_ACCEN0_Bits',0,18,79,3
	.word	16275
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,18,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1_Bits',0,18,85,3
	.word	16832
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,18,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,18,94,3
	.word	16909
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,18,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON0_Bits',0,18,111,3
	.word	17045
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,18,114,16,4,11
	.byte	'CANDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	461
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON1_Bits',0,18,126,3
	.word	17325
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,18,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON2_Bits',0,18,135,1,3
	.word	17563
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,18,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON3_Bits',0,18,150,1,3
	.word	17691
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,18,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON4_Bits',0,18,165,1,3
	.word	17934
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,18,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON5_Bits',0,18,174,1,3
	.word	18169
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,18,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6_Bits',0,18,181,1,3
	.word	18297
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,18,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7_Bits',0,18,188,1,3
	.word	18397
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,18,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	461
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CHIPID_Bits',0,18,202,1,3
	.word	18497
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,18,205,1,16,4,11
	.byte	'PWD',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	438
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSCON_Bits',0,18,213,1,3
	.word	18705
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,18,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSLIM_Bits',0,18,225,1,3
	.word	18870
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,18,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,18,235,1,3
	.word	19053
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,18,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	438
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EICR_Bits',0,18,129,2,3
	.word	19207
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,18,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR_Bits',0,18,143,2,3
	.word	19571
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,18,146,2,16,4,11
	.byte	'POL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	478
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_EMSR_Bits',0,18,159,2,3
	.word	19782
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,18,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG_Bits',0,18,167,2,3
	.word	20034
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,18,170,2,16,4,11
	.byte	'ARI',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG_Bits',0,18,175,2,3
	.word	20152
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,18,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR13CON_Bits',0,18,185,2,3
	.word	20263
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,18,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR33CON_Bits',0,18,195,2,3
	.word	20426
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,18,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,18,205,2,3
	.word	20589
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,18,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,18,215,2,3
	.word	20747
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,18,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	461
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	478
	.byte	10,0,2,35,2,0,28
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,18,232,2,3
	.word	20912
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,18,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	461
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	478
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,18,245,2,3
	.word	21241
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,18,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROVMON_Bits',0,18,255,2,3
	.word	21462
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,18,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,18,142,3,3
	.word	21625
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,18,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,18,152,3,3
	.word	21897
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,18,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,18,162,3,3
	.word	22050
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,18,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,18,172,3,3
	.word	22206
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,18,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,18,181,3,3
	.word	22368
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,18,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,18,191,3,3
	.word	22511
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,18,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,18,200,3,3
	.word	22676
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,18,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,18,211,3,3
	.word	22821
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,18,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	461
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,18,222,3,3
	.word	23002
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,18,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,18,232,3,3
	.word	23176
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,18,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,18,241,3,3
	.word	23336
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,18,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,18,130,4,3
	.word	23480
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,18,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,18,139,4,3
	.word	23754
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,18,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,18,149,4,3
	.word	23893
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,18,152,4,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	461
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	478
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	461
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	461
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_EXTCON_Bits',0,18,163,4,3
	.word	24056
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,18,166,4,16,4,11
	.byte	'STEP',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_FDR_Bits',0,18,174,4,3
	.word	24274
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,18,177,4,16,4,11
	.byte	'FS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_FMR_Bits',0,18,197,4,3
	.word	24437
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,18,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_ID_Bits',0,18,205,4,3
	.word	24773
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,18,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	461
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_SCU_IGCR_Bits',0,18,232,4,3
	.word	24880
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,18,235,4,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_IN_Bits',0,18,240,4,3
	.word	25332
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,18,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_IOCR_Bits',0,18,250,4,3
	.word	25431
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,18,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,18,131,5,3
	.word	25581
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,18,134,5,16,4,11
	.byte	'SEED',0,4
	.word	438
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	461
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,18,141,5,3
	.word	25730
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,18,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,18,149,5,3
	.word	25891
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,18,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	478
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LCLCON_Bits',0,18,158,5,3
	.word	26021
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,18,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST_Bits',0,18,166,5,3
	.word	26153
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,18,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	461
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	478
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_MANID_Bits',0,18,174,5,3
	.word	26268
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,18,177,5,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_OMR_Bits',0,18,185,5,3
	.word	26379
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,18,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	461
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	461
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_OSCCON_Bits',0,18,209,5,3
	.word	26537
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,18,212,5,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_OUT_Bits',0,18,217,5,3
	.word	26949
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,18,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	478
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	6,0,2,35,3,0,28
	.byte	'Ifx_SCU_OVCCON_Bits',0,18,233,5,3
	.word	27050
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,18,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,18,242,5,3
	.word	27317
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,18,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC_Bits',0,18,250,5,3
	.word	27453
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,18,253,5,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDR_Bits',0,18,132,6,3
	.word	27564
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,18,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR_Bits',0,18,146,6,3
	.word	27697
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,18,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLCON0_Bits',0,18,166,6,3
	.word	27900
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,18,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON1_Bits',0,18,177,6,3
	.word	28256
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,18,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON2_Bits',0,18,184,6,3
	.word	28434
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,18,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,18,204,6,3
	.word	28534
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,18,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,18,215,6,3
	.word	28904
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,18,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,18,227,6,3
	.word	29090
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,18,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,18,241,6,3
	.word	29288
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,18,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR_Bits',0,18,251,6,3
	.word	29521
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,18,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	461
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	461
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,18,153,7,3
	.word	29673
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,18,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	461
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,18,170,7,3
	.word	30240
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,18,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	461
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,18,187,7,3
	.word	30534
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,18,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	461
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	461
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,18,214,7,3
	.word	30812
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,18,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,18,230,7,3
	.word	31308
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,18,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON2_Bits',0,18,243,7,3
	.word	31621
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,18,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON_Bits',0,18,129,8,3
	.word	31830
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,18,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,18,155,8,3
	.word	32041
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,18,158,8,16,4,11
	.byte	'HBT',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	438
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON_Bits',0,18,162,8,3
	.word	32473
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,18,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	461
	.byte	7,0,2,35,3,0,28
	.byte	'Ifx_SCU_STSTAT_Bits',0,18,178,8,3
	.word	32569
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,18,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,18,186,8,3
	.word	32829
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,18,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	461
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON_Bits',0,18,198,8,3
	.word	32954
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,18,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,18,208,8,3
	.word	33151
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,18,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,18,218,8,3
	.word	33304
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,18,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET_Bits',0,18,228,8,3
	.word	33457
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,18,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,18,238,8,3
	.word	33610
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,18,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	33765
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33765
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33765
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33765
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,18,247,8,3
	.word	33781
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,18,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,18,134,9,3
	.word	33911
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,18,137,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,18,150,9,3
	.word	34149
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,18,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	33765
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33765
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33765
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33765
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,18,159,9,3
	.word	34372
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,18,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,18,175,9,3
	.word	34498
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,18,178,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,18,191,9,3
	.word	34750
	.byte	12,18,199,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16275
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN0',0,18,204,9,3
	.word	34969
	.byte	12,18,207,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16832
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1',0,18,212,9,3
	.word	35033
	.byte	12,18,215,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16909
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS',0,18,220,9,3
	.word	35097
	.byte	12,18,223,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17045
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON0',0,18,228,9,3
	.word	35162
	.byte	12,18,231,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17325
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON1',0,18,236,9,3
	.word	35227
	.byte	12,18,239,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17563
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON2',0,18,244,9,3
	.word	35292
	.byte	12,18,247,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17691
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON3',0,18,252,9,3
	.word	35357
	.byte	12,18,255,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17934
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON4',0,18,132,10,3
	.word	35422
	.byte	12,18,135,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18169
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON5',0,18,140,10,3
	.word	35487
	.byte	12,18,143,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18297
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6',0,18,148,10,3
	.word	35552
	.byte	12,18,151,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18397
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7',0,18,156,10,3
	.word	35617
	.byte	12,18,159,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18497
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CHIPID',0,18,164,10,3
	.word	35682
	.byte	12,18,167,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18705
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSCON',0,18,172,10,3
	.word	35746
	.byte	12,18,175,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18870
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSLIM',0,18,180,10,3
	.word	35810
	.byte	12,18,183,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19053
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSSTAT',0,18,188,10,3
	.word	35874
	.byte	12,18,191,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19207
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EICR',0,18,196,10,3
	.word	35939
	.byte	12,18,199,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19571
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR',0,18,204,10,3
	.word	36001
	.byte	12,18,207,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19782
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EMSR',0,18,212,10,3
	.word	36063
	.byte	12,18,215,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20034
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG',0,18,220,10,3
	.word	36125
	.byte	12,18,223,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20152
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG',0,18,228,10,3
	.word	36189
	.byte	12,18,231,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20263
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR13CON',0,18,236,10,3
	.word	36254
	.byte	12,18,239,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20426
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR33CON',0,18,244,10,3
	.word	36320
	.byte	12,18,247,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20589
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRADCSTAT',0,18,252,10,3
	.word	36386
	.byte	12,18,255,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20747
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRDVSTAT',0,18,132,11,3
	.word	36454
	.byte	12,18,135,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20912
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRMONCTRL',0,18,140,11,3
	.word	36521
	.byte	12,18,143,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21241
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROSCCTRL',0,18,148,11,3
	.word	36589
	.byte	12,18,151,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21462
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROVMON',0,18,156,11,3
	.word	36657
	.byte	12,18,159,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21625
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRRSTCON',0,18,164,11,3
	.word	36723
	.byte	12,18,167,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21897
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,18,172,11,3
	.word	36790
	.byte	12,18,175,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22050
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,18,180,11,3
	.word	36859
	.byte	12,18,183,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22206
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,18,188,11,3
	.word	36928
	.byte	12,18,191,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22368
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,18,196,11,3
	.word	36997
	.byte	12,18,199,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22511
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,18,204,11,3
	.word	37066
	.byte	12,18,207,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22676
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,18,212,11,3
	.word	37135
	.byte	12,18,215,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22821
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1',0,18,220,11,3
	.word	37204
	.byte	12,18,223,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23002
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2',0,18,228,11,3
	.word	37272
	.byte	12,18,231,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23176
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3',0,18,236,11,3
	.word	37340
	.byte	12,18,239,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23336
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4',0,18,244,11,3
	.word	37408
	.byte	12,18,247,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23480
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT',0,18,252,11,3
	.word	37476
	.byte	12,18,255,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23754
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRTRIM',0,18,132,12,3
	.word	37541
	.byte	12,18,135,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23893
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRUVMON',0,18,140,12,3
	.word	37606
	.byte	12,18,143,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24056
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EXTCON',0,18,148,12,3
	.word	37672
	.byte	12,18,151,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24274
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FDR',0,18,156,12,3
	.word	37736
	.byte	12,18,159,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24437
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FMR',0,18,164,12,3
	.word	37797
	.byte	12,18,167,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24773
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ID',0,18,172,12,3
	.word	37858
	.byte	12,18,175,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24880
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IGCR',0,18,180,12,3
	.word	37918
	.byte	12,18,183,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25332
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IN',0,18,188,12,3
	.word	37980
	.byte	12,18,191,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25431
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IOCR',0,18,196,12,3
	.word	38040
	.byte	12,18,199,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25581
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL0',0,18,204,12,3
	.word	38102
	.byte	12,18,207,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25730
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL1',0,18,212,12,3
	.word	38170
	.byte	12,18,215,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25891
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL2',0,18,220,12,3
	.word	38238
	.byte	12,18,223,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26021
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLCON',0,18,228,12,3
	.word	38306
	.byte	12,18,231,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26153
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST',0,18,236,12,3
	.word	38370
	.byte	12,18,239,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26268
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_MANID',0,18,244,12,3
	.word	38435
	.byte	12,18,247,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26379
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OMR',0,18,252,12,3
	.word	38498
	.byte	12,18,255,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26537
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OSCCON',0,18,132,13,3
	.word	38559
	.byte	12,18,135,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26949
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OUT',0,18,140,13,3
	.word	38623
	.byte	12,18,143,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27050
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCCON',0,18,148,13,3
	.word	38684
	.byte	12,18,151,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27317
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE',0,18,156,13,3
	.word	38748
	.byte	12,18,159,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27453
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC',0,18,164,13,3
	.word	38815
	.byte	12,18,167,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27564
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDR',0,18,172,13,3
	.word	38878
	.byte	12,18,175,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27697
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR',0,18,180,13,3
	.word	38939
	.byte	12,18,183,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27900
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON0',0,18,188,13,3
	.word	39001
	.byte	12,18,191,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28256
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON1',0,18,196,13,3
	.word	39066
	.byte	12,18,199,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28434
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON2',0,18,204,13,3
	.word	39131
	.byte	12,18,207,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28534
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON0',0,18,212,13,3
	.word	39196
	.byte	12,18,215,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28904
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON1',0,18,220,13,3
	.word	39265
	.byte	12,18,223,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29090
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT',0,18,228,13,3
	.word	39334
	.byte	12,18,231,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29288
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT',0,18,236,13,3
	.word	39403
	.byte	12,18,239,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29521
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR',0,18,244,13,3
	.word	39468
	.byte	12,18,247,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29673
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR0',0,18,252,13,3
	.word	39531
	.byte	12,18,255,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30240
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR1',0,18,132,14,3
	.word	39596
	.byte	12,18,135,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30534
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR2',0,18,140,14,3
	.word	39661
	.byte	12,18,143,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30812
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTAT',0,18,148,14,3
	.word	39726
	.byte	12,18,151,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31308
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR',0,18,156,14,3
	.word	39792
	.byte	12,18,159,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31830
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON',0,18,164,14,3
	.word	39861
	.byte	12,18,167,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31621
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON2',0,18,172,14,3
	.word	39925
	.byte	12,18,175,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32041
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTSTAT',0,18,180,14,3
	.word	39990
	.byte	12,18,183,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32473
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON',0,18,188,14,3
	.word	40055
	.byte	12,18,191,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32569
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_STSTAT',0,18,196,14,3
	.word	40120
	.byte	12,18,199,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32829
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON',0,18,204,14,3
	.word	40184
	.byte	12,18,207,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32954
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON',0,18,212,14,3
	.word	40250
	.byte	12,18,215,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33151
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR',0,18,220,14,3
	.word	40314
	.byte	12,18,223,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33304
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS',0,18,228,14,3
	.word	40379
	.byte	12,18,231,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33457
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET',0,18,236,14,3
	.word	40444
	.byte	12,18,239,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33610
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT',0,18,244,14,3
	.word	40509
	.byte	12,18,247,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33781
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTCPU_CON0',0,18,252,14,3
	.word	40575
	.byte	12,18,255,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33911
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTCPU_CON1',0,18,132,15,3
	.word	40644
	.byte	12,18,135,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34149
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTCPU_SR',0,18,140,15,3
	.word	40713
	.byte	12,18,143,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34372
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0',0,18,148,15,3
	.word	40780
	.byte	12,18,151,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34498
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON1',0,18,156,15,3
	.word	40847
	.byte	12,18,159,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34750
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_SR',0,18,164,15,3
	.word	40914
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,18,175,15,25,12,13
	.byte	'CON0',0
	.word	40575
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40644
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40713
	.byte	4,2,35,8,0,16
	.word	40979
	.byte	28
	.byte	'Ifx_SCU_WDTCPU',0,18,180,15,3
	.word	41042
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,18,183,15,25,12,13
	.byte	'CON0',0
	.word	40780
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40847
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40914
	.byte	4,2,35,8,0,16
	.word	41071
	.byte	28
	.byte	'Ifx_SCU_WDTS',0,18,188,15,3
	.word	41132
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,28
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	41159
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,28
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	41310
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,28
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	41554
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,28
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	41652
	.byte	28
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8258
	.byte	28
	.byte	'gpio_pin_enum',0,10,89,2
	.word	9437
	.byte	28
	.byte	'gpio_dir_enum',0,10,95,2
	.word	11504
	.byte	28
	.byte	'gpio_mode_enum',0,10,111,2
	.word	11522
	.byte	28
	.byte	'spi_index_enum',0,11,48,2
	.word	11685
	.byte	28
	.byte	'spi_mode_enum',0,11,56,2
	.word	11910
	.byte	28
	.byte	'spi_sck_pin_enum',0,11,67,2
	.word	11964
	.byte	28
	.byte	'spi_mosi_pin_enum',0,11,78,2
	.word	12237
	.byte	28
	.byte	'spi_miso_pin_enum',0,11,89,2
	.word	12491
	.byte	28
	.byte	'spi_cs_pin_enum',0,11,140,1,2
	.word	12745
	.byte	28
	.byte	'wifi_spi_packets_command_enum',0,12,114,2
	.word	13842
	.byte	17,12,116,9,1,18
	.byte	'WIFI_SPI_IDLE',0,0,18
	.byte	'WIFI_SPI_BUSY',0,1,0,28
	.byte	'wifi_spi_state_enum',0,12,120,2
	.word	42369
	.byte	28
	.byte	'wifi_spi_head_struct',0,12,128,1,2
	.word	13735
	.byte	28
	.byte	'wifi_spi_packets_struct',0,12,135,1,2
	.word	13800
.L306:
	.byte	14,12
	.word	8439
	.byte	15,11,0
.L307:
	.byte	14,20
	.word	8439
	.byte	15,19,0
.L308:
	.byte	14,25
	.word	8439
	.byte	15,24,0
.L310:
	.byte	14,128,8
	.word	461
	.byte	15,255,7,0
.L311:
	.byte	16
	.word	42369
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L122:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,55,0,73,19,0,0,20,38,0,73,19,0,0,21,46,1,3,8,58,15,59,15,57
	.byte	15,73,19,54,15,39,12,63,12,60,12,0,0,22,5,0,58,15,59,15,57,15,73,19,0,0,23,24,0,58,15,59,15,57,15,0,0
	.byte	24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,19,1,58,15,59,15,57,15,11,15,0,0,26,46,1
	.byte	49,19,0,0,27,5,0,49,19,0,0,28,22,0,3,8,58,15,59,15,57,15,73,19,0,0,29,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L123:
	.word	.L450-.L449
.L449:
	.half	3
	.word	.L452-.L451
.L451:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'string.h',0,3,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'zf_common_fifo.h',0,4,0,0
	.byte	'zf_driver_delay.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,5,0,0
	.byte	'zf_driver_spi.h',0,5,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_wifi_spi.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0,0
.L452:
.L450:
	.sdecl	'.debug_info',debug,cluster('wifi_spi_wifi_connect')
	.sect	'.debug_info'
.L124:
	.word	386
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L127,.L126
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_wifi_connect',0,1,198,2,7
	.word	.L221
	.byte	1,1,1
	.word	.L105,.L222,.L104
	.byte	4
	.byte	'wifi_ssid',0,1,198,2,36
	.word	.L223,.L224
	.byte	4
	.byte	'pass_word',0,1,198,2,53
	.word	.L223,.L225
	.byte	5
	.word	.L105,.L222
	.byte	6
	.byte	'return_state',0,1,200,2,11
	.word	.L221,.L226
	.byte	6
	.byte	'temp_buffer',0,1,201,2,11
	.word	.L227,.L228
	.byte	6
	.byte	'length',0,1,202,2,12
	.word	.L229,.L230
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_wifi_connect')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_wifi_connect')
	.sect	'.debug_line'
.L126:
	.word	.L454-.L453
.L453:
	.half	3
	.word	.L456-.L455
.L455:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L456:
	.byte	5,7,7,0,5,2
	.word	.L105
	.byte	3,197,2,1,5,8,9
	.half	.L397-.L105
	.byte	3,6,1,5,5,9
	.half	.L457-.L397
	.byte	1,5,71,7,9
	.half	.L458-.L457
	.byte	3,3,1,5,82,9
	.half	.L459-.L458
	.byte	1,5,42,9
	.half	.L460-.L459
	.byte	1,5,55,9
	.half	.L399-.L460
	.byte	1,5,18,9
	.half	.L461-.L399
	.byte	1,5,92,9
	.half	.L400-.L461
	.byte	1,5,65,9
	.half	.L34-.L400
	.byte	3,5,1,5,42,9
	.half	.L462-.L34
	.byte	1,5,55,9
	.half	.L402-.L462
	.byte	1,5,18,9
	.half	.L463-.L402
	.byte	1,5,43,9
	.half	.L35-.L463
	.byte	3,3,1,5,74,9
	.half	.L464-.L35
	.byte	1,5,95,9
	.half	.L465-.L464
	.byte	1,5,18,9
	.half	.L403-.L465
	.byte	1,5,30,9
	.half	.L405-.L403
	.byte	3,3,1,5,5,9
	.half	.L404-.L405
	.byte	3,2,1,5,1,9
	.half	.L36-.L404
	.byte	3,1,1,7,9
	.half	.L128-.L36
	.byte	0,1,1
.L454:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_wifi_connect')
	.sect	'.debug_ranges'
.L127:
	.word	-1,.L105,0,.L128-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_socket_connect')
	.sect	'.debug_info'
.L129:
	.word	433
	.half	3
	.word	.L130
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L132,.L131
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_socket_connect',0,1,233,2,7
	.word	.L221
	.byte	1,1,1
	.word	.L107,.L231,.L106
	.byte	4
	.byte	'transport_type',0,1,233,2,38
	.word	.L223,.L232
	.byte	4
	.byte	'ip_addr',0,1,233,2,60
	.word	.L223,.L233
	.byte	4
	.byte	'port',0,1,233,2,75
	.word	.L223,.L234
	.byte	4
	.byte	'local_port',0,1,233,2,87
	.word	.L223,.L235
	.byte	5
	.word	.L107,.L231
	.byte	6
	.byte	'return_state',0,1,235,2,11
	.word	.L221,.L236
	.byte	6
	.byte	'temp_buffer',0,1,236,2,11
	.word	.L237,.L238
	.byte	6
	.byte	'length',0,1,237,2,12
	.word	.L229,.L239
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_socket_connect')
	.sect	'.debug_abbrev'
.L130:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_socket_connect')
	.sect	'.debug_line'
.L131:
	.word	.L467-.L466
.L466:
	.half	3
	.word	.L469-.L468
.L468:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L469:
	.byte	5,7,7,0,5,2
	.word	.L107
	.byte	3,232,2,1,5,79,9
	.half	.L407-.L107
	.byte	3,6,1,5,95,9
	.half	.L470-.L407
	.byte	1,5,104,9
	.half	.L471-.L470
	.byte	1,5,110,9
	.half	.L472-.L471
	.byte	1,5,38,9
	.half	.L473-.L472
	.byte	1,5,51,9
	.half	.L410-.L473
	.byte	1,5,14,9
	.half	.L409-.L410
	.byte	1,5,43,9
	.half	.L411-.L409
	.byte	3,2,1,5,76,9
	.half	.L474-.L411
	.byte	1,5,97,9
	.half	.L475-.L474
	.byte	1,5,18,9
	.half	.L412-.L475
	.byte	1,5,30,9
	.half	.L414-.L412
	.byte	3,3,1,5,5,9
	.half	.L413-.L414
	.byte	3,2,1,5,1,9
	.half	.L37-.L413
	.byte	3,1,1,7,9
	.half	.L133-.L37
	.byte	0,1,1
.L467:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_socket_connect')
	.sect	'.debug_ranges'
.L132:
	.word	-1,.L107,0,.L133-.L107,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_socket_disconnect')
	.sect	'.debug_info'
.L134:
	.word	300
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L137,.L136
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_socket_disconnect',0,1,128,3,7
	.word	.L221
	.byte	1,1,1
	.word	.L109,.L240,.L108
	.byte	4
	.word	.L109,.L240
	.byte	5
	.byte	'temp_packets',0,1,130,3,29
	.word	.L241,.L242
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_socket_disconnect')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_socket_disconnect')
	.sect	'.debug_line'
.L136:
	.word	.L477-.L476
.L476:
	.half	3
	.word	.L479-.L478
.L478:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L479:
	.byte	5,7,7,0,5,2
	.word	.L109
	.byte	3,255,2,1,5,35,9
	.half	.L416-.L109
	.byte	3,4,1,5,59,9
	.half	.L480-.L416
	.byte	1,5,73,9
	.half	.L481-.L480
	.byte	1,5,5,9
	.half	.L482-.L481
	.byte	1,5,1,9
	.half	.L38-.L482
	.byte	3,1,1,7,9
	.half	.L138-.L38
	.byte	0,1,1
.L477:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_socket_disconnect')
	.sect	'.debug_ranges'
.L137:
	.word	-1,.L109,0,.L138-.L109,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_udp_send_now')
	.sect	'.debug_info'
.L139:
	.word	321
	.half	3
	.word	.L140
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L142,.L141
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_udp_send_now',0,1,169,3,7
	.word	.L221
	.byte	1,1,1
	.word	.L113,.L243,.L112
	.byte	4
	.word	.L113,.L243
	.byte	5
	.byte	'return_state',0,1,171,3,11
	.word	.L221,.L244
	.byte	5
	.byte	'temp_packets',0,1,172,3,29
	.word	.L241,.L245
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_udp_send_now')
	.sect	'.debug_abbrev'
.L140:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_udp_send_now')
	.sect	'.debug_line'
.L141:
	.word	.L484-.L483
.L483:
	.half	3
	.word	.L486-.L485
.L485:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L486:
	.byte	5,7,7,0,5,2
	.word	.L113
	.byte	3,168,3,1,5,24,9
	.half	.L420-.L113
	.byte	3,2,1,5,25,9
	.half	.L421-.L420
	.byte	3,3,1,5,5,9
	.half	.L487-.L421
	.byte	1,5,9,7,9
	.half	.L488-.L487
	.byte	3,3,1,5,26,9
	.half	.L489-.L488
	.byte	1,5,24,9
	.half	.L490-.L489
	.byte	1,5,35,9
	.half	.L44-.L490
	.byte	3,3,1,5,13,9
	.half	.L491-.L44
	.byte	1,5,17,7,9
	.half	.L492-.L491
	.byte	3,2,1,5,41,9
	.half	.L45-.L492
	.byte	3,4,1,5,39,9
	.half	.L493-.L45
	.byte	1,5,40,9
	.half	.L494-.L493
	.byte	3,1,1,5,38,9
	.half	.L495-.L494
	.byte	1,5,40,9
	.half	.L496-.L495
	.byte	3,1,1,5,54,9
	.half	.L497-.L496
	.byte	1,5,63,9
	.half	.L498-.L497
	.byte	3,3,1,5,17,9
	.half	.L499-.L498
	.byte	1,5,16,9
	.half	.L500-.L499
	.byte	1,5,121,7,9
	.half	.L501-.L500
	.byte	1,5,77,9
	.half	.L502-.L501
	.byte	1,5,101,9
	.half	.L503-.L502
	.byte	1,5,37,7,9
	.half	.L47-.L503
	.byte	3,3,1,5,17,9
	.half	.L504-.L47
	.byte	1,5,40,7,9
	.half	.L505-.L504
	.byte	3,2,1,5,67,9
	.half	.L506-.L505
	.byte	1,5,93,9
	.half	.L507-.L506
	.byte	1,5,35,9
	.half	.L48-.L507
	.byte	3,5,1,5,13,9
	.half	.L508-.L48
	.byte	1,5,17,7,9
	.half	.L509-.L508
	.byte	3,2,1,5,41,9
	.half	.L50-.L509
	.byte	3,4,1,5,39,9
	.half	.L510-.L50
	.byte	1,5,40,9
	.half	.L511-.L510
	.byte	3,1,1,5,38,9
	.half	.L512-.L511
	.byte	1,5,40,9
	.half	.L513-.L512
	.byte	3,1,1,5,71,9
	.half	.L514-.L513
	.byte	1,5,54,9
	.half	.L515-.L514
	.byte	3,2,1,5,16,9
	.half	.L516-.L515
	.byte	1,5,13,9
	.half	.L517-.L516
	.byte	1,5,30,7,9
	.half	.L518-.L517
	.byte	3,2,1,5,9,9
	.half	.L46-.L518
	.byte	3,6,1,5,26,9
	.half	.L519-.L46
	.byte	1,5,24,9
	.half	.L520-.L519
	.byte	1,5,5,9
	.half	.L43-.L520
	.byte	3,3,1,5,1,9
	.half	.L53-.L43
	.byte	3,1,1,7,9
	.half	.L143-.L53
	.byte	0,1,1
.L484:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_udp_send_now')
	.sect	'.debug_ranges'
.L142:
	.word	-1,.L113,0,.L143-.L113,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_send_buffer')
	.sect	'.debug_info'
.L144:
	.word	359
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L147,.L146
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_send_buffer',0,1,233,3,8
	.word	.L246
	.byte	1,1,1
	.word	.L115,.L247,.L114
	.byte	4
	.byte	'buffer',0,1,233,3,43
	.word	.L248,.L249
	.byte	4
	.byte	'length',0,1,233,3,58
	.word	.L246,.L250
	.byte	5
	.word	.L115,.L247
	.byte	6
	.byte	'send_length',0,1,235,3,12
	.word	.L229,.L251
	.byte	6
	.byte	'temp_packets',0,1,236,3,29
	.word	.L241,.L252
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_send_buffer')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_send_buffer')
	.sect	'.debug_line'
.L146:
	.word	.L522-.L521
.L521:
	.half	3
	.word	.L524-.L523
.L523:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L524:
	.byte	5,8,7,0,5,2
	.word	.L115
	.byte	3,232,3,1,5,25,9
	.half	.L426-.L115
	.byte	3,6,1,5,5,9
	.half	.L525-.L426
	.byte	1,5,9,7,9
	.half	.L526-.L525
	.byte	3,3,1,5,26,9
	.half	.L527-.L526
	.byte	1,5,24,9
	.half	.L528-.L527
	.byte	1,5,21,9
	.half	.L529-.L528
	.byte	3,2,1,5,36,9
	.half	.L56-.L529
	.byte	3,2,1,5,27,9
	.half	.L530-.L56
	.byte	1,5,59,7,9
	.half	.L531-.L530
	.byte	1,5,92,9
	.half	.L427-.L531
	.byte	1,5,59,9
	.half	.L57-.L427
	.byte	1,5,35,9
	.half	.L58-.L57
	.byte	3,2,1,5,13,9
	.half	.L532-.L58
	.byte	1,5,17,7,9
	.half	.L533-.L532
	.byte	3,2,1,5,45,9
	.half	.L59-.L533
	.byte	3,3,1,5,59,9
	.half	.L534-.L59
	.byte	1,5,63,9
	.half	.L429-.L534
	.byte	3,3,1,5,17,9
	.half	.L535-.L429
	.byte	1,5,16,9
	.half	.L536-.L535
	.byte	1,5,121,7,9
	.half	.L537-.L536
	.byte	1,5,77,9
	.half	.L538-.L537
	.byte	1,5,101,9
	.half	.L539-.L538
	.byte	1,5,37,7,9
	.half	.L61-.L539
	.byte	3,3,1,5,17,9
	.half	.L540-.L61
	.byte	1,5,40,7,9
	.half	.L541-.L540
	.byte	3,2,1,5,67,9
	.half	.L542-.L541
	.byte	1,5,93,9
	.half	.L543-.L542
	.byte	1,5,20,9
	.half	.L62-.L543
	.byte	3,4,1,9
	.half	.L544-.L62
	.byte	3,1,1,5,21,9
	.half	.L55-.L544
	.byte	3,106,1,5,69,7,9
	.half	.L60-.L55
	.byte	3,26,1,5,35,9
	.half	.L65-.L60
	.byte	3,2,1,5,13,9
	.half	.L545-.L65
	.byte	1,5,17,7,9
	.half	.L546-.L545
	.byte	3,2,1,5,41,9
	.half	.L66-.L546
	.byte	3,4,1,5,39,9
	.half	.L547-.L66
	.byte	1,5,41,9
	.half	.L548-.L547
	.byte	3,1,1,5,39,9
	.half	.L549-.L548
	.byte	1,5,40,9
	.half	.L550-.L549
	.byte	3,1,1,5,54,9
	.half	.L551-.L550
	.byte	1,5,63,9
	.half	.L552-.L551
	.byte	3,2,1,5,17,9
	.half	.L553-.L552
	.byte	1,5,16,9
	.half	.L554-.L553
	.byte	1,5,121,7,9
	.half	.L555-.L554
	.byte	1,5,77,9
	.half	.L556-.L555
	.byte	1,5,101,9
	.half	.L557-.L556
	.byte	1,5,37,7,9
	.half	.L68-.L557
	.byte	3,3,1,5,17,9
	.half	.L558-.L68
	.byte	1,5,40,7,9
	.half	.L559-.L558
	.byte	3,2,1,5,67,9
	.half	.L560-.L559
	.byte	1,5,93,9
	.half	.L561-.L560
	.byte	1,5,61,9
	.half	.L64-.L561
	.byte	3,111,1,5,15,9
	.half	.L562-.L64
	.byte	1,5,69,9
	.half	.L563-.L562
	.byte	1,5,9,7,9
	.half	.L67-.L563
	.byte	3,21,1,5,26,9
	.half	.L564-.L67
	.byte	1,5,24,9
	.half	.L565-.L564
	.byte	1,5,5,9
	.half	.L54-.L565
	.byte	3,2,1,5,1,9
	.half	.L71-.L54
	.byte	3,1,1,7,9
	.half	.L148-.L71
	.byte	0,1,1
.L522:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_send_buffer')
	.sect	'.debug_ranges'
.L147:
	.word	-1,.L115,0,.L148-.L115,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_read_buffer')
	.sect	'.debug_info'
.L149:
	.word	366
	.half	3
	.word	.L150
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L152,.L151
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_read_buffer',0,1,176,4,8
	.word	.L246
	.byte	1,1,1
	.word	.L117,.L253,.L116
	.byte	4
	.byte	'buffer',0,1,176,4,37
	.word	.L254,.L255
	.byte	4
	.byte	'length',0,1,176,4,52
	.word	.L246,.L256
	.byte	5
	.word	.L117,.L253
	.byte	5
	.word	.L257,.L253
	.byte	6
	.byte	'data_len',0,1,179,4,12
	.word	.L246,.L258
	.byte	6
	.byte	'temp_packets',0,1,183,4,29
	.word	.L241,.L259
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_read_buffer')
	.sect	'.debug_abbrev'
.L150:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_read_buffer')
	.sect	'.debug_line'
.L151:
	.word	.L567-.L566
.L566:
	.half	3
	.word	.L569-.L568
.L568:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L569:
	.byte	5,8,7,0,5,2
	.word	.L117
	.byte	3,175,4,1,5,5,9
	.half	.L435-.L117
	.byte	3,2,1,5,21,9
	.half	.L257-.L435
	.byte	3,1,1,5,25,9
	.half	.L570-.L257
	.byte	3,6,1,5,5,9
	.half	.L436-.L570
	.byte	1,5,9,7,9
	.half	.L571-.L436
	.byte	3,3,1,5,26,9
	.half	.L572-.L571
	.byte	1,5,24,9
	.half	.L573-.L572
	.byte	1,5,35,9
	.half	.L73-.L573
	.byte	3,5,1,5,13,9
	.half	.L574-.L73
	.byte	1,5,17,7,9
	.half	.L575-.L574
	.byte	3,2,1,5,41,9
	.half	.L74-.L575
	.byte	3,2,1,5,39,9
	.half	.L576-.L74
	.byte	1,5,41,9
	.half	.L577-.L576
	.byte	3,1,1,5,39,9
	.half	.L578-.L577
	.byte	1,5,40,9
	.half	.L579-.L578
	.byte	3,1,1,5,54,9
	.half	.L580-.L579
	.byte	1,5,63,9
	.half	.L581-.L580
	.byte	3,2,1,5,17,9
	.half	.L582-.L581
	.byte	1,5,16,9
	.half	.L583-.L582
	.byte	1,5,121,7,9
	.half	.L584-.L583
	.byte	1,5,77,9
	.half	.L585-.L584
	.byte	1,5,101,9
	.half	.L586-.L585
	.byte	1,5,37,7,9
	.half	.L76-.L586
	.byte	3,3,1,5,17,9
	.half	.L587-.L76
	.byte	1,5,40,7,9
	.half	.L588-.L587
	.byte	3,2,1,5,67,9
	.half	.L589-.L588
	.byte	1,5,93,9
	.half	.L590-.L589
	.byte	1,5,62,9
	.half	.L77-.L590
	.byte	3,3,1,5,16,9
	.half	.L591-.L77
	.byte	1,5,71,9
	.half	.L592-.L591
	.byte	1,5,9,7,9
	.half	.L75-.L592
	.byte	3,1,1,5,26,9
	.half	.L593-.L75
	.byte	1,5,24,9
	.half	.L594-.L593
	.byte	1,5,23,9
	.half	.L72-.L594
	.byte	3,4,1,5,47,9
	.half	.L595-.L72
	.byte	1,5,57,9
	.half	.L596-.L595
	.byte	1,5,12,9
	.half	.L438-.L596
	.byte	3,1,1,5,5,9
	.half	.L597-.L438
	.byte	1,5,1,9
	.half	.L79-.L597
	.byte	3,1,1,7,9
	.half	.L153-.L79
	.byte	0,1,1
.L567:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_read_buffer')
	.sect	'.debug_ranges'
.L152:
	.word	-1,.L117,0,.L153-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_init')
	.sect	'.debug_info'
.L154:
	.word	333
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L157,.L156
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_init',0,1,226,4,7
	.word	.L221
	.byte	1,1,1
	.word	.L119,.L260,.L118
	.byte	4
	.byte	'wifi_ssid',0,1,226,4,28
	.word	.L223,.L261
	.byte	4
	.byte	'pass_word',0,1,226,4,45
	.word	.L223,.L262
	.byte	5
	.word	.L119,.L260
	.byte	6
	.byte	'return_state',0,1,228,4,11
	.word	.L221,.L263
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_init')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_init')
	.sect	'.debug_line'
.L156:
	.word	.L599-.L598
.L598:
	.half	3
	.word	.L601-.L600
.L600:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L601:
	.byte	5,7,7,0,5,2
	.word	.L119
	.byte	3,225,4,1,5,16,9
	.half	.L443-.L119
	.byte	3,4,1,5,31,9
	.half	.L602-.L443
	.byte	1,5,47,9
	.half	.L603-.L602
	.byte	1,5,64,9
	.half	.L604-.L603
	.byte	1,5,75,9
	.half	.L605-.L604
	.byte	3,1,1,5,94,9
	.half	.L606-.L605
	.byte	1,5,113,9
	.half	.L607-.L606
	.byte	1,5,14,9
	.half	.L608-.L607
	.byte	1,5,30,9
	.half	.L609-.L608
	.byte	1,5,41,9
	.half	.L610-.L609
	.byte	1,5,57,9
	.half	.L611-.L610
	.byte	1,5,15,9
	.half	.L612-.L611
	.byte	3,1,1,5,33,9
	.half	.L613-.L612
	.byte	1,5,38,9
	.half	.L614-.L613
	.byte	1,5,41,9
	.half	.L615-.L614
	.byte	1,5,15,9
	.half	.L616-.L615
	.byte	3,1,1,5,33,9
	.half	.L617-.L616
	.byte	1,5,38,9
	.half	.L618-.L617
	.byte	1,5,41,9
	.half	.L619-.L618
	.byte	1,5,15,9
	.half	.L620-.L619
	.byte	3,1,1,5,33,9
	.half	.L621-.L620
	.byte	1,5,38,9
	.half	.L622-.L621
	.byte	1,5,41,9
	.half	.L623-.L622
	.byte	1,5,20,9
	.half	.L624-.L623
	.byte	3,3,1,5,38,9
	.half	.L625-.L624
	.byte	1,5,21,9
	.half	.L626-.L625
	.byte	3,1,1,5,20,9
	.half	.L627-.L626
	.byte	3,1,1,5,38,9
	.half	.L628-.L627
	.byte	1,5,21,9
	.half	.L629-.L628
	.byte	3,3,1,5,5,9
	.half	.L630-.L629
	.byte	3,1,1,5,22,9
	.half	.L631-.L630
	.byte	1,5,20,9
	.half	.L632-.L631
	.byte	1,5,44,9
	.half	.L80-.L632
	.byte	3,5,1,5,9,9
	.half	.L444-.L80
	.byte	3,1,1,5,13,7,9
	.half	.L633-.L444
	.byte	3,2,1,5,30,9
	.half	.L81-.L633
	.byte	3,4,1,5,57,9
	.half	.L445-.L81
	.byte	3,3,1,5,9,9
	.half	.L447-.L445
	.byte	3,1,1,5,13,7,9
	.half	.L634-.L447
	.byte	3,2,1,5,5,9
	.half	.L82-.L634
	.byte	3,20,1,5,1,9
	.half	.L85-.L82
	.byte	3,1,1,7,9
	.half	.L158-.L85
	.byte	0,1,1
.L599:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_init')
	.sect	'.debug_ranges'
.L157:
	.word	-1,.L119,0,.L158-.L119,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_wait_idle')
	.sect	'.debug_info'
.L159:
	.word	303
	.half	3
	.word	.L160
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L162,.L161
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_wait_idle',0,1,80,14
	.word	.L221
	.byte	1,1
	.word	.L87,.L264,.L86
	.byte	4
	.byte	'wait_time',0,1,80,41
	.word	.L246,.L265
	.byte	5
	.word	.L87,.L264
	.byte	6
	.byte	'time',0,1,82,12
	.word	.L246,.L266
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_wait_idle')
	.sect	'.debug_abbrev'
.L160:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_wait_idle')
	.sect	'.debug_line'
.L161:
	.word	.L636-.L635
.L635:
	.half	3
	.word	.L638-.L637
.L637:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L638:
	.byte	5,17,7,0,5,2
	.word	.L87
	.byte	3,209,0,1,5,26,9
	.half	.L313-.L87
	.byte	3,2,1,5,48,9
	.half	.L312-.L313
	.byte	3,1,1,5,25,9
	.half	.L3-.L312
	.byte	3,2,1,5,13,9
	.half	.L639-.L3
	.byte	3,1,1,5,9,9
	.half	.L640-.L639
	.byte	3,1,1,5,13,7,9
	.half	.L641-.L640
	.byte	3,2,1,5,31,9
	.half	.L2-.L641
	.byte	3,122,1,5,48,9
	.half	.L642-.L2
	.byte	1,5,23,7,9
	.half	.L5-.L642
	.byte	3,9,1,5,5,9
	.half	.L643-.L5
	.byte	1,5,1,9
	.half	.L6-.L643
	.byte	3,1,1,7,9
	.half	.L163-.L6
	.byte	0,1,1
.L636:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_wait_idle')
	.sect	'.debug_ranges'
.L162:
	.word	-1,.L87,0,.L163-.L87,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_write')
	.sect	'.debug_info'
.L164:
	.word	335
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L167,.L166
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_write',0,1,107,13,1,1
	.word	.L89,.L267,.L88
	.byte	4
	.byte	'buffer1',0,1,107,42
	.word	.L248,.L268
	.byte	4
	.byte	'length1',0,1,107,58
	.word	.L229,.L269
	.byte	4
	.byte	'buffer2',0,1,107,80
	.word	.L248,.L270
	.byte	4
	.byte	'length2',0,1,107,96
	.word	.L229,.L271
	.byte	5
	.word	.L89,.L267
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_write')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_write')
	.sect	'.debug_line'
.L166:
	.word	.L645-.L644
.L644:
	.half	3
	.word	.L647-.L646
.L646:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L647:
	.byte	5,13,7,0,5,2
	.word	.L89
	.byte	3,234,0,1,5,5,9
	.half	.L321-.L89
	.byte	3,2,1,5,8,9
	.half	.L648-.L321
	.byte	3,1,1,5,5,9
	.half	.L649-.L648
	.byte	1,5,30,7,9
	.half	.L650-.L649
	.byte	3,2,1,5,55,9
	.half	.L651-.L650
	.byte	1,5,8,9
	.half	.L7-.L651
	.byte	3,2,1,5,5,9
	.half	.L317-.L7
	.byte	1,5,30,7,9
	.half	.L652-.L317
	.byte	3,2,1,5,55,9
	.half	.L653-.L652
	.byte	1,5,5,9
	.half	.L8-.L653
	.byte	3,2,1,5,1,9
	.half	.L654-.L8
	.byte	3,1,1,7,9
	.half	.L168-.L654
	.byte	0,1,1
.L645:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_write')
	.sect	'.debug_ranges'
.L167:
	.word	-1,.L89,0,.L168-.L89,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_transfer_command')
	.sect	'.debug_info'
.L169:
	.word	308
	.half	3
	.word	.L170
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L172,.L171
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_transfer_command',0,1,129,1,13,1,1
	.word	.L91,.L272,.L90
	.byte	4
	.byte	'packets',0,1,129,1,65
	.word	.L273,.L274
	.byte	4
	.byte	'length',0,1,129,1,81
	.word	.L229,.L275
	.byte	5
	.word	.L91,.L272
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_transfer_command')
	.sect	'.debug_abbrev'
.L170:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_transfer_command')
	.sect	'.debug_line'
.L171:
	.word	.L656-.L655
.L655:
	.half	3
	.word	.L658-.L657
.L657:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L658:
	.byte	5,13,7,0,5,2
	.word	.L91
	.byte	3,128,1,1,5,5,9
	.half	.L329-.L91
	.byte	3,2,1,5,23,9
	.half	.L659-.L329
	.byte	3,2,1,5,93,9
	.half	.L660-.L659
	.byte	1,5,5,9
	.half	.L332-.L660
	.byte	3,2,1,5,27,7,9
	.half	.L661-.L332
	.byte	3,2,1,5,66,9
	.half	.L662-.L661
	.byte	1,5,84,9
	.half	.L663-.L662
	.byte	1,5,94,9
	.half	.L335-.L663
	.byte	1,5,5,9
	.half	.L9-.L335
	.byte	3,3,1,5,1,9
	.half	.L664-.L9
	.byte	3,1,1,7,9
	.half	.L173-.L664
	.byte	0,1,1
.L656:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_transfer_command')
	.sect	'.debug_ranges'
.L172:
	.word	-1,.L91,0,.L173-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_transfer_data')
	.sect	'.debug_info'
.L174:
	.word	331
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L177,.L176
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_transfer_data',0,1,152,1,13,1,1
	.word	.L93,.L276,.L92
	.byte	4
	.byte	'write_data',0,1,152,1,50
	.word	.L248,.L277
	.byte	4
	.byte	'read_data',0,1,152,1,87
	.word	.L273,.L278
	.byte	4
	.byte	'length',0,1,152,1,105
	.word	.L229,.L279
	.byte	5
	.word	.L93,.L276
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_transfer_data')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_transfer_data')
	.sect	'.debug_line'
.L176:
	.word	.L666-.L665
.L665:
	.half	3
	.word	.L668-.L667
.L667:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L668:
	.byte	5,13,7,0,5,2
	.word	.L93
	.byte	3,151,1,1,5,5,9
	.half	.L343-.L93
	.byte	3,2,1,5,31,9
	.half	.L669-.L343
	.byte	3,2,1,5,29,9
	.half	.L670-.L669
	.byte	1,9
	.half	.L671-.L670
	.byte	3,1,1,5,23,9
	.half	.L672-.L671
	.byte	3,2,1,5,97,9
	.half	.L673-.L672
	.byte	1,5,8,9
	.half	.L345-.L673
	.byte	3,2,1,5,5,9
	.half	.L674-.L345
	.byte	1,5,27,7,9
	.half	.L675-.L674
	.byte	3,2,1,5,64,9
	.half	.L676-.L675
	.byte	1,5,74,9
	.half	.L677-.L676
	.byte	1,5,30,9
	.half	.L348-.L677
	.byte	3,1,1,5,57,9
	.half	.L678-.L348
	.byte	1,5,89,9
	.half	.L679-.L678
	.byte	1,5,96,9
	.half	.L680-.L679
	.byte	3,127,1,5,25,9
	.half	.L10-.L680
	.byte	3,6,1,5,47,9
	.half	.L342-.L10
	.byte	1,5,27,9
	.half	.L351-.L342
	.byte	3,1,1,5,52,9
	.half	.L354-.L351
	.byte	1,5,71,9
	.half	.L357-.L354
	.byte	1,5,81,9
	.half	.L681-.L357
	.byte	1,5,5,9
	.half	.L11-.L681
	.byte	3,2,1,5,1,9
	.half	.L682-.L11
	.byte	3,1,1,7,9
	.half	.L178-.L682
	.byte	0,1,1
.L666:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_transfer_data')
	.sect	'.debug_ranges'
.L177:
	.word	-1,.L93,0,.L178-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_set_parameter')
	.sect	'.debug_info'
.L179:
	.word	397
	.half	3
	.word	.L180
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L182,.L181
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_set_parameter',0,1,185,1,14
	.word	.L221
	.byte	1,1
	.word	.L95,.L280,.L94
	.byte	4
	.byte	'command',0,1,185,1,68
	.word	.L281,.L282
	.byte	4
	.byte	'buffer',0,1,185,1,84
	.word	.L254,.L283
	.byte	4
	.byte	'length',0,1,185,1,99
	.word	.L229,.L284
	.byte	4
	.byte	'wait_time',0,1,185,1,114
	.word	.L246,.L285
	.byte	5
	.word	.L95,.L280
	.byte	6
	.byte	'return_state',0,1,187,1,11
	.word	.L221,.L286
	.byte	6
	.byte	'head',0,1,188,1,26
	.word	.L287,.L288
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_set_parameter')
	.sect	'.debug_abbrev'
.L180:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_set_parameter')
	.sect	'.debug_line'
.L181:
	.word	.L684-.L683
.L683:
	.half	3
	.word	.L686-.L685
.L685:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L686:
	.byte	5,14,7,0,5,2
	.word	.L95
	.byte	3,184,1,1,5,18,9
	.half	.L365-.L95
	.byte	3,4,1,5,22,9
	.half	.L12-.L365
	.byte	3,3,1,9
	.half	.L687-.L12
	.byte	3,1,1,5,31,9
	.half	.L688-.L687
	.byte	3,3,1,5,9,9
	.half	.L360-.L688
	.byte	1,5,13,7,9
	.half	.L689-.L360
	.byte	3,2,1,5,25,9
	.half	.L13-.L689
	.byte	3,3,1,5,39,9
	.half	.L690-.L13
	.byte	1,5,77,9
	.half	.L691-.L690
	.byte	1,5,31,9
	.half	.L367-.L691
	.byte	3,1,1,5,9,9
	.half	.L370-.L367
	.byte	1,5,13,7,9
	.half	.L692-.L370
	.byte	3,2,1,5,24,9
	.half	.L15-.L692
	.byte	3,4,1,5,22,9
	.half	.L364-.L15
	.byte	1,5,23,9
	.half	.L693-.L364
	.byte	3,1,1,5,21,9
	.half	.L694-.L693
	.byte	1,5,63,9
	.half	.L695-.L694
	.byte	3,1,1,5,73,9
	.half	.L696-.L695
	.byte	1,5,25,9
	.half	.L697-.L696
	.byte	3,1,1,5,37,9
	.half	.L698-.L697
	.byte	3,1,1,5,12,9
	.half	.L699-.L698
	.byte	1,5,9,9
	.half	.L700-.L699
	.byte	1,5,26,7,9
	.half	.L701-.L700
	.byte	3,2,1,5,5,9
	.half	.L14-.L701
	.byte	3,4,1,5,1,9
	.half	.L18-.L14
	.byte	3,1,1,7,9
	.half	.L183-.L18
	.byte	0,1,1
.L684:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_set_parameter')
	.sect	'.debug_ranges'
.L182:
	.word	-1,.L95,0,.L183-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_get_parameter')
	.sect	'.debug_info'
.L184:
	.word	362
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L187,.L186
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_get_parameter',0,1,230,1,14
	.word	.L221
	.byte	1,1
	.word	.L97,.L289,.L96
	.byte	4
	.byte	'command',0,1,230,1,68
	.word	.L281,.L290
	.byte	4
	.byte	'read_data',0,1,230,1,102
	.word	.L273,.L291
	.byte	4
	.byte	'wait_time',0,1,230,1,120
	.word	.L246,.L292
	.byte	5
	.word	.L97,.L289
	.byte	6
	.byte	'return_state',0,1,232,1,11
	.word	.L221,.L293
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_get_parameter')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_get_parameter')
	.sect	'.debug_line'
.L186:
	.word	.L703-.L702
.L702:
	.half	3
	.word	.L705-.L704
.L704:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L705:
	.byte	5,14,7,0,5,2
	.word	.L97
	.byte	3,229,1,1,5,18,9
	.half	.L377-.L97
	.byte	3,4,1,5,31,9
	.half	.L19-.L377
	.byte	3,4,1,5,9,9
	.half	.L373-.L19
	.byte	1,5,13,7,9
	.half	.L706-.L373
	.byte	3,2,1,5,33,9
	.half	.L20-.L706
	.byte	3,2,1,5,52,9
	.half	.L707-.L20
	.byte	3,1,1,5,75,9
	.half	.L708-.L707
	.byte	1,5,81,9
	.half	.L709-.L708
	.byte	1,5,31,9
	.half	.L379-.L709
	.byte	3,2,1,5,9,9
	.half	.L381-.L379
	.byte	1,5,13,7,9
	.half	.L710-.L381
	.byte	3,2,1,5,35,9
	.half	.L22-.L710
	.byte	3,2,1,5,33,9
	.half	.L375-.L22
	.byte	1,5,34,9
	.half	.L711-.L375
	.byte	3,1,1,5,32,9
	.half	.L712-.L711
	.byte	1,5,46,9
	.half	.L713-.L712
	.byte	3,1,1,5,22,9
	.half	.L383-.L713
	.byte	3,1,1,5,5,9
	.half	.L21-.L383
	.byte	3,2,1,5,1,9
	.half	.L24-.L21
	.byte	3,1,1,7,9
	.half	.L188-.L24
	.byte	0,1,1
.L703:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_get_parameter')
	.sect	'.debug_ranges'
.L187:
	.word	-1,.L97,0,.L188-.L97,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_get_version')
	.sect	'.debug_info'
.L189:
	.word	319
	.half	3
	.word	.L190
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L192,.L191
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_get_version',0,1,136,2,14
	.word	.L221
	.byte	1,1
	.word	.L99,.L294,.L98
	.byte	4
	.word	.L99,.L294
	.byte	5
	.byte	'return_state',0,1,138,2,11
	.word	.L221,.L295
	.byte	5
	.byte	'temp_packets',0,1,139,2,29
	.word	.L241,.L296
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_get_version')
	.sect	'.debug_abbrev'
.L190:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_get_version')
	.sect	'.debug_line'
.L191:
	.word	.L715-.L714
.L714:
	.half	3
	.word	.L717-.L716
.L716:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L717:
	.byte	5,14,7,0,5,2
	.word	.L99
	.byte	3,135,2,1,5,43,9
	.half	.L385-.L99
	.byte	3,5,1,5,66,9
	.half	.L718-.L385
	.byte	1,5,80,9
	.half	.L719-.L718
	.byte	1,5,18,9
	.half	.L386-.L719
	.byte	1,5,8,9
	.half	.L387-.L386
	.byte	3,1,1,5,75,7,9
	.half	.L720-.L387
	.byte	1,5,32,9
	.half	.L721-.L720
	.byte	1,5,55,9
	.half	.L722-.L721
	.byte	1,5,16,7,9
	.half	.L723-.L722
	.byte	3,2,1,5,46,9
	.half	.L724-.L723
	.byte	1,5,72,9
	.half	.L725-.L724
	.byte	1,5,5,9
	.half	.L25-.L725
	.byte	3,2,1,5,1,9
	.half	.L27-.L25
	.byte	3,1,1,7,9
	.half	.L193-.L27
	.byte	0,1,1
.L715:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_get_version')
	.sect	'.debug_ranges'
.L192:
	.word	-1,.L99,0,.L193-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_get_mac_addr')
	.sect	'.debug_info'
.L194:
	.word	320
	.half	3
	.word	.L195
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L197,.L196
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_get_mac_addr',0,1,156,2,14
	.word	.L221
	.byte	1,1
	.word	.L101,.L297,.L100
	.byte	4
	.word	.L101,.L297
	.byte	5
	.byte	'return_state',0,1,158,2,11
	.word	.L221,.L298
	.byte	5
	.byte	'temp_packets',0,1,159,2,29
	.word	.L241,.L299
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_get_mac_addr')
	.sect	'.debug_abbrev'
.L195:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_get_mac_addr')
	.sect	'.debug_line'
.L196:
	.word	.L727-.L726
.L726:
	.half	3
	.word	.L729-.L728
.L728:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L729:
	.byte	5,14,7,0,5,2
	.word	.L101
	.byte	3,155,2,1,5,43,9
	.half	.L389-.L101
	.byte	3,5,1,5,67,9
	.half	.L730-.L389
	.byte	1,5,81,9
	.half	.L731-.L730
	.byte	1,5,18,9
	.half	.L390-.L731
	.byte	1,5,8,9
	.half	.L391-.L390
	.byte	3,1,1,5,76,7,9
	.half	.L732-.L391
	.byte	1,5,32,9
	.half	.L733-.L732
	.byte	1,5,56,9
	.half	.L734-.L733
	.byte	1,5,16,7,9
	.half	.L735-.L734
	.byte	3,2,1,5,47,9
	.half	.L736-.L735
	.byte	1,5,73,9
	.half	.L737-.L736
	.byte	1,5,5,9
	.half	.L28-.L737
	.byte	3,2,1,5,1,9
	.half	.L30-.L28
	.byte	3,1,1,7,9
	.half	.L198-.L30
	.byte	0,1,1
.L727:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_get_mac_addr')
	.sect	'.debug_ranges'
.L197:
	.word	-1,.L101,0,.L198-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_get_ip_addr_port')
	.sect	'.debug_info'
.L199:
	.word	324
	.half	3
	.word	.L200
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L202,.L201
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_get_ip_addr_port',0,1,177,2,14
	.word	.L221
	.byte	1,1
	.word	.L103,.L300,.L102
	.byte	4
	.word	.L103,.L300
	.byte	5
	.byte	'return_state',0,1,179,2,11
	.word	.L221,.L301
	.byte	5
	.byte	'temp_packets',0,1,180,2,29
	.word	.L241,.L302
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_get_ip_addr_port')
	.sect	'.debug_abbrev'
.L200:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_get_ip_addr_port')
	.sect	'.debug_line'
.L201:
	.word	.L739-.L738
.L738:
	.half	3
	.word	.L741-.L740
.L740:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L741:
	.byte	5,14,7,0,5,2
	.word	.L103
	.byte	3,176,2,1,5,43,9
	.half	.L393-.L103
	.byte	3,5,1,5,66,9
	.half	.L742-.L393
	.byte	1,5,80,9
	.half	.L743-.L742
	.byte	1,5,18,9
	.half	.L394-.L743
	.byte	1,5,8,9
	.half	.L395-.L394
	.byte	3,1,1,5,75,7,9
	.half	.L744-.L395
	.byte	1,5,32,9
	.half	.L745-.L744
	.byte	1,5,55,9
	.half	.L746-.L745
	.byte	1,5,16,7,9
	.half	.L747-.L746
	.byte	3,2,1,5,51,9
	.half	.L748-.L747
	.byte	1,5,77,9
	.half	.L749-.L748
	.byte	1,5,5,9
	.half	.L31-.L749
	.byte	3,2,1,5,1,9
	.half	.L33-.L31
	.byte	3,1,1,7,9
	.half	.L203-.L33
	.byte	0,1,1
.L739:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_get_ip_addr_port')
	.sect	'.debug_ranges'
.L202:
	.word	-1,.L103,0,.L203-.L103,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_reset')
	.sect	'.debug_info'
.L204:
	.word	306
	.half	3
	.word	.L205
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L207,.L206
	.byte	2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_reset',0,1,142,3,7
	.word	.L221
	.byte	1,1,1
	.word	.L111,.L303,.L110
	.byte	4
	.word	.L111,.L303
	.byte	5
	.byte	'return_state',0,1,144,3,11
	.word	.L221,.L304
	.byte	5
	.byte	'head',0,1,145,3,26
	.word	.L287,.L305
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_reset')
	.sect	'.debug_abbrev'
.L205:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('wifi_spi_reset')
	.sect	'.debug_line'
.L206:
	.word	.L751-.L750
.L750:
	.half	3
	.word	.L753-.L752
.L752:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0,0,0,0,0
.L753:
	.byte	5,7,7,0,5,2
	.word	.L111
	.byte	3,141,3,1,5,24,9
	.half	.L39-.L111
	.byte	3,7,1,5,22,9
	.half	.L754-.L39
	.byte	1,5,24,9
	.half	.L755-.L754
	.byte	3,1,1,5,22,9
	.half	.L756-.L755
	.byte	1,5,43,9
	.half	.L757-.L756
	.byte	3,1,1,5,22,9
	.half	.L417-.L757
	.byte	1,5,9,9
	.half	.L418-.L417
	.byte	3,1,1,5,13,7,9
	.half	.L758-.L418
	.byte	3,2,1,5,25,9
	.half	.L40-.L758
	.byte	3,2,1,5,39,9
	.half	.L759-.L40
	.byte	1,5,69,9
	.half	.L760-.L759
	.byte	1,5,75,9
	.half	.L761-.L760
	.byte	1,5,5,9
	.half	.L41-.L761
	.byte	3,3,1,5,1,9
	.half	.L42-.L41
	.byte	3,1,1,7,9
	.half	.L208-.L42
	.byte	0,1,1
.L751:
	.sdecl	'.debug_ranges',debug,cluster('wifi_spi_reset')
	.sect	'.debug_ranges'
.L207:
	.word	-1,.L111,0,.L208-.L111,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_version')
	.sect	'.debug_info'
.L209:
	.word	236
	.half	3
	.word	.L210
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_version',0,13,66,6
	.word	.L306
	.byte	1,5,3
	.word	wifi_spi_version
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_version')
	.sect	'.debug_abbrev'
.L210:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_mac_addr')
	.sect	'.debug_info'
.L211:
	.word	237
	.half	3
	.word	.L212
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_mac_addr',0,13,67,6
	.word	.L307
	.byte	1,5,3
	.word	wifi_spi_mac_addr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_mac_addr')
	.sect	'.debug_abbrev'
.L212:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_ip_addr_port')
	.sect	'.debug_info'
.L213:
	.word	241
	.half	3
	.word	.L214
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_ip_addr_port',0,13,68,6
	.word	.L308
	.byte	1,5,3
	.word	wifi_spi_ip_addr_port
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_ip_addr_port')
	.sect	'.debug_abbrev'
.L214:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_fifo')
	.sect	'.debug_info'
.L215:
	.word	232
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_fifo',0,13,70,21
	.word	.L309
	.byte	5,3
	.word	wifi_spi_fifo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_fifo')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_buffer')
	.sect	'.debug_info'
.L217:
	.word	234
	.half	3
	.word	.L218
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_buffer',0,13,71,21
	.word	.L310
	.byte	5,3
	.word	wifi_spi_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_buffer')
	.sect	'.debug_abbrev'
.L218:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('wifi_spi_mutex')
	.sect	'.debug_info'
.L219:
	.word	233
	.half	3
	.word	.L220
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_wifi_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L120
	.byte	3
	.byte	'wifi_spi_mutex',0,13,72,41
	.word	.L311
	.byte	5,3
	.word	wifi_spi_mutex
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('wifi_spi_mutex')
	.sect	'.debug_abbrev'
.L220:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_get_ip_addr_port')
	.sect	'.debug_loc'
.L301:
	.word	-1,.L103,.L394-.L103,.L31-.L103
	.half	1
	.byte	82
	.word	.L395-.L103,.L300-.L103
	.half	1
	.byte	88
	.word	.L396-.L103,.L300-.L103
	.half	1
	.byte	82
	.word	0,0
.L302:
	.word	-1,.L103,0,.L300-.L103
	.half	2
	.byte	145,88
	.word	0,0
.L102:
	.word	-1,.L103,0,.L393-.L103
	.half	2
	.byte	138,0
	.word	.L393-.L103,.L300-.L103
	.half	2
	.byte	138,40
	.word	.L300-.L103,.L300-.L103
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_get_mac_addr')
	.sect	'.debug_loc'
.L298:
	.word	-1,.L101,.L390-.L101,.L28-.L101
	.half	1
	.byte	82
	.word	.L391-.L101,.L297-.L101
	.half	1
	.byte	88
	.word	.L392-.L101,.L297-.L101
	.half	1
	.byte	82
	.word	0,0
.L299:
	.word	-1,.L101,0,.L297-.L101
	.half	2
	.byte	145,88
	.word	0,0
.L100:
	.word	-1,.L101,0,.L389-.L101
	.half	2
	.byte	138,0
	.word	.L389-.L101,.L297-.L101
	.half	2
	.byte	138,40
	.word	.L297-.L101,.L297-.L101
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_get_parameter')
	.sect	'.debug_loc'
.L290:
	.word	-1,.L97,0,.L372-.L97
	.half	1
	.byte	84
	.word	.L374-.L97,.L375-.L97
	.half	1
	.byte	95
	.word	0,0
.L291:
	.word	-1,.L97,0,.L373-.L97
	.half	1
	.byte	100
	.word	.L376-.L97,.L289-.L97
	.half	1
	.byte	111
	.word	.L378-.L97,.L379-.L97
	.half	1
	.byte	100
	.word	.L382-.L97,.L383-.L97
	.half	1
	.byte	100
	.word	0,0
.L293:
	.word	-1,.L97,.L19-.L97,.L289-.L97
	.half	1
	.byte	89
	.word	.L384-.L97,.L289-.L97
	.half	1
	.byte	82
	.word	0,0
.L292:
	.word	-1,.L97,0,.L372-.L97
	.half	1
	.byte	85
	.word	.L377-.L97,.L289-.L97
	.half	1
	.byte	88
	.word	.L372-.L97,.L373-.L97
	.half	1
	.byte	84
	.word	.L380-.L97,.L381-.L97
	.half	1
	.byte	84
	.word	0,0
.L96:
	.word	-1,.L97,0,.L289-.L97
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_get_version')
	.sect	'.debug_loc'
.L295:
	.word	-1,.L99,.L386-.L99,.L25-.L99
	.half	1
	.byte	82
	.word	.L387-.L99,.L294-.L99
	.half	1
	.byte	88
	.word	.L388-.L99,.L294-.L99
	.half	1
	.byte	82
	.word	0,0
.L296:
	.word	-1,.L99,0,.L294-.L99
	.half	2
	.byte	145,88
	.word	0,0
.L98:
	.word	-1,.L99,0,.L385-.L99
	.half	2
	.byte	138,0
	.word	.L385-.L99,.L294-.L99
	.half	2
	.byte	138,40
	.word	.L294-.L99,.L294-.L99
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_init')
	.sect	'.debug_loc'
.L262:
	.word	-1,.L119,0,.L440-.L119
	.half	1
	.byte	101
	.word	.L443-.L119,.L260-.L119
	.half	1
	.byte	108
	.word	.L448-.L119,.L447-.L119
	.half	1
	.byte	101
	.word	0,0
.L263:
	.word	-1,.L119,.L444-.L119,.L445-.L119
	.half	1
	.byte	82
	.word	.L447-.L119,.L260-.L119
	.half	1
	.byte	82
	.word	0,0
.L118:
	.word	-1,.L119,0,.L439-.L119
	.half	2
	.byte	138,0
	.word	.L439-.L119,.L260-.L119
	.half	2
	.byte	138,16
	.word	.L260-.L119,.L260-.L119
	.half	2
	.byte	138,0
	.word	0,0
.L261:
	.word	-1,.L119,0,.L441-.L119
	.half	1
	.byte	100
	.word	.L442-.L119,.L260-.L119
	.half	1
	.byte	111
	.word	.L446-.L119,.L447-.L119
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_read_buffer')
	.sect	'.debug_loc'
.L255:
	.word	-1,.L117,0,.L432-.L117
	.half	1
	.byte	100
	.word	.L434-.L117,.L253-.L117
	.half	1
	.byte	108
	.word	.L437-.L117,.L438-.L117
	.half	1
	.byte	101
	.word	0,0
.L258:
	.word	-1,.L117,0,.L253-.L117
	.half	2
	.byte	145,88
	.word	0,0
.L256:
	.word	-1,.L117,0,.L433-.L117
	.half	1
	.byte	84
	.word	.L435-.L117,.L436-.L117
	.half	1
	.byte	95
	.word	0,0
.L259:
	.word	-1,.L117,0,.L253-.L117
	.half	2
	.byte	145,92
	.word	0,0
.L116:
	.word	-1,.L117,0,.L431-.L117
	.half	2
	.byte	138,0
	.word	.L431-.L117,.L253-.L117
	.half	2
	.byte	138,40
	.word	.L253-.L117,.L253-.L117
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_reset')
	.sect	'.debug_loc'
.L305:
	.word	-1,.L111,0,.L303-.L111
	.half	2
	.byte	145,120
	.word	0,0
.L304:
	.word	-1,.L111,.L417-.L111,.L41-.L111
	.half	1
	.byte	82
	.word	.L418-.L111,.L303-.L111
	.half	1
	.byte	95
	.word	.L419-.L111,.L303-.L111
	.half	1
	.byte	82
	.word	0,0
.L110:
	.word	-1,.L111,0,.L39-.L111
	.half	2
	.byte	138,0
	.word	.L39-.L111,.L303-.L111
	.half	2
	.byte	138,8
	.word	.L303-.L111,.L303-.L111
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_send_buffer')
	.sect	'.debug_loc'
.L249:
	.word	-1,.L115,0,.L56-.L115
	.half	1
	.byte	100
	.word	.L424-.L115,.L425-.L115
	.half	1
	.byte	111
	.word	.L428-.L115,.L429-.L115
	.half	1
	.byte	100
	.word	0,0
.L250:
	.word	-1,.L115,0,.L56-.L115
	.half	1
	.byte	84
	.word	.L426-.L115,.L247-.L115
	.half	1
	.byte	89
	.word	.L430-.L115,.L247-.L115
	.half	1
	.byte	82
	.word	0,0
.L251:
	.word	-1,.L115,.L427-.L115,.L57-.L115
	.half	1
	.byte	88
	.word	.L58-.L115,.L55-.L115
	.half	1
	.byte	88
	.word	0,0
.L252:
	.word	-1,.L115,0,.L247-.L115
	.half	2
	.byte	145,88
	.word	0,0
.L114:
	.word	-1,.L115,0,.L423-.L115
	.half	2
	.byte	138,0
	.word	.L423-.L115,.L247-.L115
	.half	2
	.byte	138,40
	.word	.L247-.L115,.L247-.L115
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_set_parameter')
	.sect	'.debug_loc'
.L283:
	.word	-1,.L95,0,.L360-.L95
	.half	1
	.byte	100
	.word	.L362-.L95,.L280-.L95
	.half	1
	.byte	111
	.word	.L366-.L95,.L367-.L95
	.half	1
	.byte	101
	.word	0,0
.L282:
	.word	-1,.L95,0,.L361-.L95
	.half	1
	.byte	84
	.word	0,0
.L288:
	.word	-1,.L95,0,.L280-.L95
	.half	2
	.byte	145,120
	.word	0,0
.L284:
	.word	-1,.L95,0,.L360-.L95
	.half	1
	.byte	85
	.word	.L363-.L95,.L364-.L95
	.half	1
	.byte	95
	.word	.L368-.L95,.L367-.L95
	.half	1
	.byte	85
	.word	0,0
.L286:
	.word	-1,.L95,.L12-.L95,.L280-.L95
	.half	1
	.byte	89
	.word	.L371-.L95,.L280-.L95
	.half	1
	.byte	82
	.word	0,0
.L285:
	.word	-1,.L95,0,.L361-.L95
	.half	1
	.byte	86
	.word	.L365-.L95,.L280-.L95
	.half	1
	.byte	88
	.word	.L361-.L95,.L360-.L95
	.half	1
	.byte	84
	.word	.L369-.L95,.L370-.L95
	.half	1
	.byte	84
	.word	0,0
.L94:
	.word	-1,.L95,0,.L359-.L95
	.half	2
	.byte	138,0
	.word	.L359-.L95,.L280-.L95
	.half	2
	.byte	138,8
	.word	.L280-.L95,.L280-.L95
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_socket_connect')
	.sect	'.debug_loc'
.L233:
	.word	-1,.L107,0,.L408-.L107
	.half	1
	.byte	101
	.word	0,0
.L239:
	.word	-1,.L107,.L411-.L107,.L412-.L107
	.half	1
	.byte	85
	.word	0,0
.L235:
	.word	-1,.L107,0,.L409-.L107
	.half	1
	.byte	103
	.word	0,0
.L234:
	.word	-1,.L107,0,.L409-.L107
	.half	1
	.byte	102
	.word	0,0
.L236:
	.word	-1,.L107,.L412-.L107,.L413-.L107
	.half	1
	.byte	82
	.word	.L414-.L107,.L231-.L107
	.half	1
	.byte	95
	.word	.L415-.L107,.L231-.L107
	.half	1
	.byte	82
	.word	0,0
.L238:
	.word	-1,.L107,0,.L231-.L107
	.half	2
	.byte	145,80
	.word	0,0
.L232:
	.word	-1,.L107,0,.L410-.L107
	.half	1
	.byte	100
	.word	0,0
.L106:
	.word	-1,.L107,0,.L407-.L107
	.half	2
	.byte	138,0
	.word	.L407-.L107,.L231-.L107
	.half	3
	.byte	138,192,0
	.word	.L231-.L107,.L231-.L107
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_socket_disconnect')
	.sect	'.debug_loc'
.L242:
	.word	-1,.L109,0,.L240-.L109
	.half	2
	.byte	145,88
	.word	0,0
.L108:
	.word	-1,.L109,0,.L416-.L109
	.half	2
	.byte	138,0
	.word	.L416-.L109,.L240-.L109
	.half	2
	.byte	138,40
	.word	.L240-.L109,.L240-.L109
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_transfer_command')
	.sect	'.debug_loc'
.L275:
	.word	-1,.L91,0,.L326-.L91
	.half	1
	.byte	84
	.word	.L329-.L91,.L330-.L91
	.half	1
	.byte	95
	.word	.L337-.L91,.L9-.L91
	.half	1
	.byte	85
	.word	0,0
.L274:
	.word	-1,.L91,0,.L327-.L91
	.half	1
	.byte	100
	.word	.L328-.L91,.L272-.L91
	.half	1
	.byte	111
	.word	.L331-.L91,.L332-.L91
	.half	1
	.byte	100
	.word	.L333-.L91,.L332-.L91
	.half	1
	.byte	101
	.word	.L334-.L91,.L335-.L91
	.half	1
	.byte	100
	.word	.L336-.L91,.L9-.L91
	.half	1
	.byte	101
	.word	.L337-.L91,.L9-.L91
	.half	1
	.byte	100
	.word	0,0
.L90:
	.word	-1,.L91,0,.L272-.L91
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_transfer_data')
	.sect	'.debug_loc'
.L279:
	.word	-1,.L93,0,.L338-.L93
	.half	1
	.byte	84
	.word	.L343-.L93,.L276-.L93
	.half	1
	.byte	88
	.word	.L353-.L93,.L351-.L93
	.half	1
	.byte	84
	.word	0,0
.L278:
	.word	-1,.L93,0,.L339-.L93
	.half	1
	.byte	101
	.word	.L341-.L93,.L342-.L93
	.half	1
	.byte	111
	.word	.L344-.L93,.L345-.L93
	.half	1
	.byte	100
	.word	.L346-.L93,.L345-.L93
	.half	1
	.byte	101
	.word	.L349-.L93,.L348-.L93
	.half	1
	.byte	101
	.word	.L350-.L93,.L351-.L93
	.half	1
	.byte	100
	.word	.L354-.L93,.L355-.L93
	.half	1
	.byte	111
	.word	.L355-.L93,.L356-.L93
	.half	1
	.byte	100
	.word	.L357-.L93,.L356-.L93
	.half	1
	.byte	111
	.word	.L356-.L93,.L11-.L93
	.half	1
	.byte	101
	.word	.L358-.L93,.L11-.L93
	.half	1
	.byte	100
	.word	0,0
.L92:
	.word	-1,.L93,0,.L276-.L93
	.half	2
	.byte	138,0
	.word	0,0
.L277:
	.word	-1,.L93,0,.L339-.L93
	.half	1
	.byte	100
	.word	.L340-.L93,.L276-.L93
	.half	1
	.byte	108
	.word	.L347-.L93,.L348-.L93
	.half	1
	.byte	100
	.word	.L352-.L93,.L351-.L93
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_udp_send_now')
	.sect	'.debug_loc'
.L244:
	.word	-1,.L113,.L421-.L113,.L243-.L113
	.half	1
	.byte	88
	.word	.L422-.L113,.L243-.L113
	.half	1
	.byte	82
	.word	0,0
.L245:
	.word	-1,.L113,0,.L243-.L113
	.half	2
	.byte	145,88
	.word	0,0
.L112:
	.word	-1,.L113,0,.L420-.L113
	.half	2
	.byte	138,0
	.word	.L420-.L113,.L243-.L113
	.half	2
	.byte	138,40
	.word	.L243-.L113,.L243-.L113
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_wait_idle')
	.sect	'.debug_loc'
.L266:
	.word	-1,.L87,.L313-.L87,.L264-.L87
	.half	1
	.byte	95
	.word	0,0
.L265:
	.word	-1,.L87,0,.L312-.L87
	.half	1
	.byte	84
	.word	.L312-.L87,.L264-.L87
	.half	1
	.byte	88
	.word	0,0
.L86:
	.word	-1,.L87,0,.L264-.L87
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_wifi_connect')
	.sect	'.debug_loc'
.L230:
	.word	-1,.L105,.L400-.L105,.L34-.L105
	.half	1
	.byte	85
	.word	.L35-.L105,.L403-.L105
	.half	1
	.byte	85
	.word	0,0
.L225:
	.word	-1,.L105,0,.L398-.L105
	.half	1
	.byte	101
	.word	.L34-.L105,.L401-.L105
	.half	1
	.byte	101
	.word	0,0
.L226:
	.word	-1,.L105,.L403-.L105,.L404-.L105
	.half	1
	.byte	82
	.word	.L405-.L105,.L222-.L105
	.half	1
	.byte	95
	.word	.L406-.L105,.L222-.L105
	.half	1
	.byte	82
	.word	0,0
.L228:
	.word	-1,.L105,0,.L222-.L105
	.half	2
	.byte	145,64
	.word	0,0
.L104:
	.word	-1,.L105,0,.L397-.L105
	.half	2
	.byte	138,0
	.word	.L397-.L105,.L222-.L105
	.half	3
	.byte	138,200,0
	.word	.L222-.L105,.L222-.L105
	.half	2
	.byte	138,0
	.word	0,0
.L224:
	.word	-1,.L105,0,.L399-.L105
	.half	1
	.byte	100
	.word	.L34-.L105,.L402-.L105
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('wifi_spi_write')
	.sect	'.debug_loc'
.L268:
	.word	-1,.L89,0,.L314-.L89
	.half	1
	.byte	100
	.word	.L316-.L89,.L317-.L89
	.half	1
	.byte	111
	.word	.L322-.L89,.L7-.L89
	.half	1
	.byte	100
	.word	0,0
.L270:
	.word	-1,.L89,0,.L314-.L89
	.half	1
	.byte	101
	.word	.L320-.L89,.L267-.L89
	.half	1
	.byte	108
	.word	.L324-.L89,.L8-.L89
	.half	1
	.byte	100
	.word	0,0
.L269:
	.word	-1,.L89,0,.L315-.L89
	.half	1
	.byte	84
	.word	.L318-.L89,.L319-.L89
	.half	1
	.byte	95
	.word	.L323-.L89,.L7-.L89
	.half	1
	.byte	85
	.word	0,0
.L271:
	.word	-1,.L89,0,.L314-.L89
	.half	1
	.byte	85
	.word	.L321-.L89,.L267-.L89
	.half	1
	.byte	88
	.word	.L325-.L89,.L8-.L89
	.half	1
	.byte	85
	.word	0,0
.L88:
	.word	-1,.L89,0,.L267-.L89
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L762:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_wait_idle')
	.sect	'.debug_frame'
	.word	12
	.word	.L762,.L87,.L264-.L87
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_write')
	.sect	'.debug_frame'
	.word	12
	.word	.L762,.L89,.L267-.L89
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_transfer_command')
	.sect	'.debug_frame'
	.word	12
	.word	.L762,.L91,.L272-.L91
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_transfer_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L762,.L93,.L276-.L93
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_set_parameter')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L95,.L280-.L95
	.byte	4
	.word	(.L359-.L95)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L280-.L359)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_get_parameter')
	.sect	'.debug_frame'
	.word	12
	.word	.L762,.L97,.L289-.L97
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_get_version')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L99,.L294-.L99
	.byte	4
	.word	(.L385-.L99)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L294-.L385)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_get_mac_addr')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L101,.L297-.L101
	.byte	4
	.word	(.L389-.L101)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L297-.L389)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_get_ip_addr_port')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L103,.L300-.L103
	.byte	4
	.word	(.L393-.L103)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L300-.L393)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_wifi_connect')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L105,.L222-.L105
	.byte	4
	.word	(.L397-.L105)/2
	.byte	19,200,0,22,26,4,19,138,200,0,4
	.word	(.L222-.L397)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_socket_connect')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L107,.L231-.L107
	.byte	4
	.word	(.L407-.L107)/2
	.byte	19,192,0,22,26,4,19,138,192,0,4
	.word	(.L231-.L407)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_socket_disconnect')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L109,.L240-.L109
	.byte	4
	.word	(.L416-.L109)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L240-.L416)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_reset')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L111,.L303-.L111
	.byte	4
	.word	(.L39-.L111)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L303-.L39)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_udp_send_now')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L113,.L243-.L113
	.byte	4
	.word	(.L420-.L113)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L243-.L420)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_send_buffer')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L115,.L247-.L115
	.byte	4
	.word	(.L423-.L115)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L247-.L423)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_read_buffer')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L117,.L253-.L117
	.byte	4
	.word	(.L431-.L117)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L253-.L431)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('wifi_spi_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L762,.L119,.L260-.L119
	.byte	4
	.word	(.L439-.L119)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L260-.L439)/2
	.byte	19,0,8,26,0,0
	; Module end
