	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc22736a --dep-file=zf_device_absolute_encoder.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_absolute_encoder.src ../libraries/zf_device/zf_device_absolute_encoder.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_absolute_encoder.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_absolute_encoder.absolute_encoder_write_register',code,cluster('absolute_encoder_write_register')
	.sect	'.text.zf_device_absolute_encoder.absolute_encoder_write_register'
	.align	2
	
; Function absolute_encoder_write_register
.L45:
absolute_encoder_write_register:	.type	func
	mov	e8,d5,d4
.L220:
	mov	d15,#0
	jeq	d15,#0,.L2
	mov	d4,#653
.L125:
	call	get_port
.L124:
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L3
.L2:
	mov	d4,#653
.L127:
	call	get_port
.L126:
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L3:
	mov	d4,#0
.L128:
	or	d5,d8,#128
.L129:
	call	spi_write_8bit
.L221:
	mov	d4,#0
.L130:
	mov	d5,d9
.L131:
	call	spi_write_8bit
.L132:
	mov	d15,#1
	jeq	d15,#0,.L4
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L5
.L4:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L5:
	mov	d4,#1
	call	system_delay_us
.L222:
	mov	d15,#0
	jeq	d15,#0,.L6
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L7
.L6:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L7:
	mov	d4,#0
	call	spi_read_8bit
.L223:
	mov	d4,#0
	call	spi_read_8bit
.L224:
	mov	d15,#1
	jeq	d15,#0,.L8
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L9
.L8:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L9:
	ret
.L110:
	
__absolute_encoder_write_register_function_end:
	.size	absolute_encoder_write_register,__absolute_encoder_write_register_function_end-absolute_encoder_write_register
.L81:
	; End of function
	
	.sdecl	'.text.zf_device_absolute_encoder.absolute_encoder_read_register',code,cluster('absolute_encoder_read_register')
	.sect	'.text.zf_device_absolute_encoder.absolute_encoder_read_register'
	.align	2
	
; Function absolute_encoder_read_register
.L47:
absolute_encoder_read_register:	.type	func
	mov	d8,d4
.L134:
	mov	d15,#0
	jeq	d15,#0,.L10
	mov	d4,#653
.L133:
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L11
.L10:
	mov	d4,#653
.L136:
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L11:
	mov	d4,#0
	or	d5,d8,#64
	call	spi_write_8bit
.L229:
	mov	d4,#0
	mov	d5,#0
	call	spi_write_8bit
.L230:
	mov	d15,#1
	jeq	d15,#0,.L12
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L13
.L12:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L13:
	mov	d4,#1
	call	system_delay_us
.L231:
	mov	d15,#0
	jeq	d15,#0,.L14
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L15
.L14:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L15:
	mov	d4,#0
	call	spi_read_8bit
.L137:
	mov	d8,d2
.L135:
	mov	d4,#0
	call	spi_read_8bit
.L138:
	mov	d15,#1
	jeq	d15,#0,.L16
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L17
.L16:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L17:
	mov	d2,d8
.L139:
	j	.L18
.L18:
	ret
.L113:
	
__absolute_encoder_read_register_function_end:
	.size	absolute_encoder_read_register,__absolute_encoder_read_register_function_end-absolute_encoder_read_register
.L86:
	; End of function
	
	.sdecl	'.text.zf_device_absolute_encoder.absolute_encoder_read_data',code,cluster('absolute_encoder_read_data')
	.sect	'.text.zf_device_absolute_encoder.absolute_encoder_read_data'
	.align	2
	
; Function absolute_encoder_read_data
.L49:
absolute_encoder_read_data:	.type	func
	mov	d15,#0
	jeq	d15,#0,.L19
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L20
.L19:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L20:
	mov	d4,#0
	call	spi_read_8bit
.L140:
	and	d15,d2,#255
.L236:
	sha	d8,d15,#8
.L141:
	mov	d4,#0
	call	spi_read_8bit
.L237:
	or	d8,d2
.L238:
	mov	d15,#1
	jeq	d15,#0,.L21
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	mov	d15,#8192
	st.w	[a2],d15
	j	.L22
.L21:
	mov	d4,#653
	call	get_port
	add.a	a2,#4
	movh	d15,#8192
	st.w	[a2],d15
.L22:
	mov	d2,d8
.L142:
	j	.L23
.L23:
	ret
.L116:
	
__absolute_encoder_read_data_function_end:
	.size	absolute_encoder_read_data,__absolute_encoder_read_data_function_end-absolute_encoder_read_data
.L91:
	; End of function
	
	.sdecl	'.text.zf_device_absolute_encoder.absolute_encoder_self_check',code,cluster('absolute_encoder_self_check')
	.sect	'.text.zf_device_absolute_encoder.absolute_encoder_self_check'
	.align	2
	
; Function absolute_encoder_self_check
.L51:
absolute_encoder_self_check:	.type	func
	sub.a	a10,#8
.L143:
	mov	d8,#0
.L144:
	movh.a	a15,#@his(.1.ini)
	lea	a15,[a15]@los(.1.ini)
	lea	a15,[a15]0
.L243:
	lea	a2,[a10]0
	mov.a	a4,#5
.L24:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L24
.L244:
	mov	d9,#0
.L145:
	j	.L25
.L26:
	mov	d15,#0
.L147:
	j	.L27
.L28:
	add	d4,d15,#1
.L245:
	addsc.a	a15,a10,d15,#0
	ld.bu	d5,[a15]
	call	absolute_encoder_write_register
.L246:
	mov	d4,#1
	call	system_delay_ms
.L247:
	add	d15,#1
.L27:
	jlt.u	d15,#6,.L28
.L248:
	mov	d0,d9
	add	d9,#1
.L146:
	extr.u	d9,d9,#0,#16
.L149:
	mov	d15,#100
.L148:
	jge.u	d15,d0,.L29
.L249:
	mov	d8,#1
.L250:
	j	.L30
.L29:
.L25:
	mov	d4,#6
	call	absolute_encoder_read_register
.L251:
	mov	d15,#28
.L252:
	jne	d15,d2,.L26
.L30:
	mov	d2,d8
.L150:
	j	.L31
.L31:
	ret
.L118:
	
__absolute_encoder_self_check_function_end:
	.size	absolute_encoder_self_check,__absolute_encoder_self_check_function_end-absolute_encoder_self_check
.L96:
	; End of function
	
	.sdecl	'.text.zf_device_absolute_encoder.absolute_encoder_get_location',code,cluster('absolute_encoder_get_location')
	.sect	'.text.zf_device_absolute_encoder.absolute_encoder_get_location'
	.align	2
	
	.global	absolute_encoder_get_location
; Function absolute_encoder_get_location
.L53:
absolute_encoder_get_location:	.type	func
	movh.a	a15,#@his(last_location)
	lea	a15,[a15]@los(last_location)
.L168:
	movh.a	a2,#@his(now_location)
	lea	a2,[a2]@los(now_location)
	ld.h	d15,[a2]
.L169:
	st.h	[a15],d15
.L170:
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
.L171:
	call	absolute_encoder_read_data
.L172:
	sha	d2,#-4
.L173:
	st.h	[a15],d2
.L174:
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d2,[a15]
.L175:
	j	.L32
.L32:
	ret
.L102:
	
__absolute_encoder_get_location_function_end:
	.size	absolute_encoder_get_location,__absolute_encoder_get_location_function_end-absolute_encoder_get_location
.L66:
	; End of function
	
	.sdecl	'.text.zf_device_absolute_encoder.absolute_encoder_get_offset',code,cluster('absolute_encoder_get_offset')
	.sect	'.text.zf_device_absolute_encoder.absolute_encoder_get_offset'
	.align	2
	
	.global	absolute_encoder_get_offset
; Function absolute_encoder_get_offset
.L55:
absolute_encoder_get_offset:	.type	func
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d15,[a15]
	movh.a	a15,#@his(last_location)
	lea	a15,[a15]@los(last_location)
	ld.h	d0,[a15]
	sub	d15,d0
	jlt	d15,#0,.L33
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d15,[a15]
	movh.a	a15,#@his(last_location)
	lea	a15,[a15]@los(last_location)
	ld.h	d0,[a15]
	sub	d15,d0
	j	.L34
.L33:
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d15,[a15]
	movh.a	a15,#@his(last_location)
	lea	a15,[a15]@los(last_location)
	ld.h	d0,[a15]
	sub	d15,d0
	rsub	d15,#0
.L34:
	mov	d0,#2048
.L180:
	jge	d0,d15,.L35
.L181:
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d15,[a15]
.L182:
	mov	d0,#2048
	jge	d0,d15,.L36
.L183:
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d15,[a15]
.L184:
	addi	d15,d15,#-4096
.L185:
	movh.a	a15,#@his(last_location)
	lea	a15,[a15]@los(last_location)
	ld.h	d0,[a15]
.L186:
	sub	d15,d0
.L187:
	j	.L37
.L36:
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d15,[a15]
.L188:
	addi	d15,d15,#4096
.L189:
	movh.a	a15,#@his(last_location)
	lea	a15,[a15]@los(last_location)
	ld.h	d0,[a15]
.L190:
	sub	d15,d0
.L37:
	extr	d2,d15,#0,#16
.L151:
	j	.L38
.L35:
	movh.a	a15,#@his(now_location)
	lea	a15,[a15]@los(now_location)
	ld.h	d15,[a15]
.L191:
	movh.a	a15,#@his(last_location)
	lea	a15,[a15]@los(last_location)
	ld.h	d0,[a15]
.L192:
	sub	d15,d0
	extr	d2,d15,#0,#16
.L38:
	j	.L39
.L39:
	ret
.L103:
	
__absolute_encoder_get_offset_function_end:
	.size	absolute_encoder_get_offset,__absolute_encoder_get_offset_function_end-absolute_encoder_get_offset
.L71:
	; End of function
	
	.sdecl	'.text.zf_device_absolute_encoder.absolute_encoder_init',code,cluster('absolute_encoder_init')
	.sect	'.text.zf_device_absolute_encoder.absolute_encoder_init'
	.align	2
	
	.global	absolute_encoder_init
; Function absolute_encoder_init
.L57:
absolute_encoder_init:	.type	func
	sub.a	a10,#16
.L152:
	mov	d8,#0
.L153:
	mov	d15,#7
	st.h	[a10],d15
.L197:
	mov	d15,#12
	st.h	[a10]4,d15
.L198:
	mov	d15,#403
	st.h	[a10]8,d15
.L199:
	mov	d4,#0
.L200:
	mov	d5,#0
.L201:
	mov.u	d6,#38528
	addih	d6,d6,#152
.L202:
	mov	d7,#0
	call	spi_init
.L203:
	mov	d4,#653
.L204:
	mov	d5,#1
.L205:
	mov	d6,#0
.L206:
	mov	d7,#3
	call	gpio_init
.L40:
	call	absolute_encoder_self_check
.L207:
	jeq	d2,#0,.L41
.L208:
	mov	d8,#1
.L209:
	mov	d4,#0
	movh.a	a4,#@his(.2.str)
	lea	a4,[a4]@los(.2.str)
	movh.a	a5,#@his(.3.str)
	lea	a5,[a5]@los(.3.str)
	mov	d5,#220
	call	debug_log_handler
.L210:
	j	.L42
.L41:
	mov	d4,#9
.L211:
	mov	d5,#0
	call	absolute_encoder_write_register
.L212:
	mov	d15,#4096
.L154:
	add	d15,#0
.L213:
	sha	d15,#4
.L155:
	extr.u	d15,d15,#0,#16
.L156:
	mov	d4,#0
.L214:
	mov	d5,d15
.L157:
	call	absolute_encoder_write_register
.L158:
	mov	d4,#1
.L215:
	sha	d5,d15,#-8
	call	absolute_encoder_write_register
.L42:
	mov	d2,d8
.L159:
	j	.L43
.L43:
	ret
.L106:
	
__absolute_encoder_init_function_end:
	.size	absolute_encoder_init,__absolute_encoder_init_function_end-absolute_encoder_init
.L76:
	; End of function
	
	.sdecl	'.data.zf_device_absolute_encoder.now_location',data,cluster('now_location')
	.sect	'.data.zf_device_absolute_encoder.now_location'
	.align	2
now_location:	.type	object
	.size	now_location,2
	.space	2
	.sdecl	'.data.zf_device_absolute_encoder.last_location',data,cluster('last_location')
	.sect	'.data.zf_device_absolute_encoder.last_location'
	.align	2
last_location:	.type	object
	.size	last_location,2
	.space	2
	.sdecl	'.rodata.zf_device_absolute_encoder..1.ini',data,rom
	.sect	'.rodata.zf_device_absolute_encoder..1.ini'
.1.ini:	.type	object
	.size	.1.ini,6
	.space	3
	.byte	192,255,28
	.sdecl	'.rodata.zf_device_absolute_encoder..2.str',data,rom
	.sect	'.rodata.zf_device_absolute_encoder..2.str'
.2.str:	.type	object
	.size	.2.str,30
	.byte	97,98,115,111,108,117,116,101
	.byte	32,101,110,99,111,100,101,114
	.byte	32,105,110,105,116,32,101,114
	.byte	114,114,111,114
	.byte	46
	.space	1
	.sdecl	'.rodata.zf_device_absolute_encoder..3.str',data,rom
	.sect	'.rodata.zf_device_absolute_encoder..3.str'
.3.str:	.type	object
	.size	.3.str,52
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,97,98,115,111,108,117,116
	.byte	101,95,101,110,99,111,100,101
	.byte	114,46,99
	.space	1
	.calls	'absolute_encoder_write_register','get_port'
	.calls	'absolute_encoder_write_register','spi_write_8bit'
	.calls	'absolute_encoder_write_register','system_delay_us'
	.calls	'absolute_encoder_write_register','spi_read_8bit'
	.calls	'absolute_encoder_read_register','get_port'
	.calls	'absolute_encoder_read_register','spi_write_8bit'
	.calls	'absolute_encoder_read_register','system_delay_us'
	.calls	'absolute_encoder_read_register','spi_read_8bit'
	.calls	'absolute_encoder_read_data','get_port'
	.calls	'absolute_encoder_read_data','spi_read_8bit'
	.calls	'absolute_encoder_self_check','absolute_encoder_write_register'
	.calls	'absolute_encoder_self_check','system_delay_ms'
	.calls	'absolute_encoder_self_check','absolute_encoder_read_register'
	.calls	'absolute_encoder_get_location','absolute_encoder_read_data'
	.calls	'absolute_encoder_init','spi_init'
	.calls	'absolute_encoder_init','gpio_init'
	.calls	'absolute_encoder_init','absolute_encoder_self_check'
	.calls	'absolute_encoder_init','debug_log_handler'
	.calls	'absolute_encoder_init','absolute_encoder_write_register'
	.calls	'absolute_encoder_write_register','',0
	.calls	'absolute_encoder_read_register','',0
	.calls	'absolute_encoder_read_data','',0
	.calls	'absolute_encoder_self_check','',8
	.calls	'absolute_encoder_get_location','',0
	.calls	'absolute_encoder_get_offset','',0
	.extern	debug_log_handler
	.extern	system_delay_us
	.extern	system_delay_ms
	.extern	get_port
	.extern	gpio_init
	.extern	spi_write_8bit
	.extern	spi_read_8bit
	.extern	spi_init
	.calls	'absolute_encoder_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L59:
	.word	40843
	.half	3
	.word	.L60
	.byte	4
.L58:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L61
	.byte	2,1,1,3
	.word	214
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	217
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	262
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	274
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	354
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	328
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	360
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	360
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	328
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L105:
	.byte	7
	.byte	'unsigned char',0,1,8
.L108:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	508
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	469
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	824
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1395
	.byte	4,2,35,0,0,14,4
	.word	469
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	469
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	469
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	469
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	469
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	469
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1523
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	469
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	469
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	469
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	469
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	469
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	469
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	469
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	469
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	469
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	469
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1953
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	469
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	469
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	469
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	469
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	469
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2170
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2390
	.byte	4,2,35,0,0,14,24
	.word	469
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	469
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	469
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	469
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	469
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	469
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	469
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	469
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2713
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	469
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	469
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	469
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	469
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	469
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	469
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	469
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3017
	.byte	4,2,35,0,0,14,8
	.word	469
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3342
	.byte	4,2,35,0,0,14,12
	.word	469
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3682
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	446
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4048
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4334
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4481
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	446
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4650
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	486
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4822
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	486
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	486
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4997
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5171
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	469
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5345
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	469
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5521
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5677
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	486
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	469
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6010
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	469
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6482
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6566
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	469
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6746
	.byte	4,2,35,0,0,14,76
	.word	469
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6999
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	469
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7086
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	784
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1355
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1474
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1514
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1698
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1913
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2130
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2350
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1514
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2664
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2704
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2977
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3293
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3333
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3633
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3673
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4008
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4294
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3333
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4441
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4610
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4782
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4957
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5131
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5305
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5481
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5637
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5970
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6318
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3333
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6442
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6691
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6950
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6990
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7046
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7613
	.byte	4,3,35,252,1,0,16
	.word	7653
	.byte	3
	.word	8256
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8261
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	469
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8266
	.byte	6,0,7
	.byte	'char',0,1,6,3
	.word	8447
	.byte	19
	.byte	'debug_log_handler',0,5,113,9,1,1,1,1,5
	.byte	'pass',0,5,113,47
	.word	469
	.byte	5
	.byte	'str',0,5,113,59
	.word	8455
	.byte	5
	.byte	'file',0,5,113,70
	.word	8455
	.byte	5
	.byte	'line',0,5,113,80
	.word	462
	.byte	0,7
	.byte	'unsigned long int',0,4,7,19
	.byte	'system_delay_us',0,6,45,9,1,1,1,1,5
	.byte	'time',0,6,45,45
	.word	8538
	.byte	0,19
	.byte	'system_delay_ms',0,6,46,9,1,1,1,1,5
	.byte	'time',0,6,46,45
	.word	8538
	.byte	0,20
	.word	222
	.byte	21
	.word	248
	.byte	6,0,20
	.word	283
	.byte	21
	.word	315
	.byte	6,0,20
	.word	365
	.byte	21
	.word	384
	.byte	6,0,20
	.word	400
	.byte	21
	.word	415
	.byte	21
	.word	429
	.byte	6,0,20
	.word	8369
	.byte	21
	.word	8397
	.byte	21
	.word	8411
	.byte	21
	.word	8429
	.byte	6,0,17,7,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'get_port',0,7,114,13
	.word	8261
	.byte	1,1,1,1,5
	.byte	'pin',0,7,114,56
	.word	8710
	.byte	0,17,7,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,7,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,19
	.byte	'gpio_init',0,7,143,1,7,1,1,1,1,5
	.byte	'pin',0,7,143,1,40
	.word	8710
	.byte	5
	.byte	'dir',0,7,143,1,59
	.word	10684
	.byte	5
	.byte	'dat',0,7,143,1,70
	.word	469
	.byte	5
	.byte	'pinconf',0,7,143,1,90
	.word	10702
	.byte	0,17,8,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,23
	.word	469
	.byte	19
	.byte	'spi_write_8bit',0,8,143,1,13,1,1,1,1,5
	.byte	'spi_n',0,8,143,1,61
	.word	10865
	.byte	5
	.byte	'data',0,8,143,1,80
	.word	10903
	.byte	0,22
	.byte	'spi_read_8bit',0,8,155,1,13
	.word	469
	.byte	1,1,1,1,5
	.byte	'spi_n',0,8,155,1,61
	.word	10865
	.byte	0,17,8,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,8,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,8,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,8,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,8,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,19
	.byte	'spi_init',0,8,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,8,170,1,61
	.word	10865
	.byte	5
	.byte	'mode',0,8,170,1,82
	.word	11005
	.byte	5
	.byte	'baud',0,8,170,1,95
	.word	8538
	.byte	5
	.byte	'sck_pin',0,8,170,1,118
	.word	11059
	.byte	5
	.byte	'mosi_pin',0,8,170,1,145,1
	.word	11332
	.byte	5
	.byte	'miso_pin',0,8,170,1,173,1
	.word	11586
	.byte	5
	.byte	'cs_pin',0,8,170,1,199,1
	.word	11840
	.byte	0
.L101:
	.byte	7
	.byte	'short int',0,2,5
.L121:
	.byte	14,6
	.word	469
	.byte	15,5,0,24
	.byte	'__wchar_t',0,9,1,1
	.word	12812
	.byte	24
	.byte	'__size_t',0,9,1,1
	.word	446
	.byte	24
	.byte	'__ptrdiff_t',0,9,1,1
	.word	462
	.byte	25,1,3
	.word	12889
	.byte	24
	.byte	'__codeptr',0,9,1,1
	.word	12891
	.byte	24
	.byte	'__intptr_t',0,9,1,1
	.word	462
	.byte	24
	.byte	'__uintptr_t',0,9,1,1
	.word	446
	.byte	24
	.byte	'_iob_flag_t',0,10,82,25
	.word	486
	.byte	24
	.byte	'boolean',0,11,101,29
	.word	469
	.byte	24
	.byte	'uint8',0,11,105,29
	.word	469
	.byte	24
	.byte	'uint16',0,11,109,29
	.word	486
	.byte	24
	.byte	'uint32',0,11,113,29
	.word	8538
	.byte	24
	.byte	'uint64',0,11,118,29
	.word	328
	.byte	24
	.byte	'sint16',0,11,126,29
	.word	12812
	.byte	7
	.byte	'long int',0,4,5,24
	.byte	'sint32',0,11,131,1,29
	.word	13063
	.byte	7
	.byte	'long long int',0,8,5,24
	.byte	'sint64',0,11,138,1,29
	.word	13091
	.byte	24
	.byte	'float32',0,11,167,1,29
	.word	274
	.byte	24
	.byte	'pvoid',0,12,57,28
	.word	360
	.byte	24
	.byte	'Ifx_TickTime',0,12,79,28
	.word	13091
	.byte	7
	.byte	'char',0,1,6,24
	.byte	'int8',0,13,54,29
	.word	13176
	.byte	24
	.byte	'int16',0,13,55,29
	.word	12812
	.byte	24
	.byte	'int32',0,13,56,29
	.word	462
	.byte	24
	.byte	'int64',0,13,57,29
	.word	13091
	.byte	24
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7086
	.byte	24
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6999
	.byte	24
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3342
	.byte	24
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1395
	.byte	24
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2390
	.byte	24
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1523
	.byte	24
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2170
	.byte	24
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1738
	.byte	24
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1953
	.byte	24
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6358
	.byte	24
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6482
	.byte	24
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6566
	.byte	24
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6746
	.byte	24
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4997
	.byte	24
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5521
	.byte	24
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5171
	.byte	24
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5345
	.byte	24
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6010
	.byte	24
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	824
	.byte	24
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4334
	.byte	24
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4822
	.byte	24
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4481
	.byte	24
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4650
	.byte	24
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5677
	.byte	24
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	508
	.byte	24
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4048
	.byte	24
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3682
	.byte	24
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2713
	.byte	24
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3017
	.byte	24
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7613
	.byte	24
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7046
	.byte	24
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3633
	.byte	24
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1474
	.byte	24
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2664
	.byte	24
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1698
	.byte	24
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2350
	.byte	24
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1913
	.byte	24
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2130
	.byte	24
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6442
	.byte	24
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6691
	.byte	24
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6950
	.byte	24
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6318
	.byte	24
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5131
	.byte	24
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5637
	.byte	24
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5305
	.byte	24
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5481
	.byte	24
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1355
	.byte	24
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5970
	.byte	24
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4441
	.byte	24
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4957
	.byte	24
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4610
	.byte	24
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4782
	.byte	24
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	784
	.byte	24
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4294
	.byte	24
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4008
	.byte	24
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2977
	.byte	24
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3293
	.byte	16
	.word	7653
	.byte	24
	.byte	'Ifx_P',0,4,139,6,3
	.word	14557
	.byte	17,14,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,24
	.byte	'IfxScu_WDTCON1_IR',0,14,255,10,3
	.word	14577
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_ACCEN0_Bits',0,15,79,3
	.word	14699
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN1_Bits',0,15,85,3
	.word	15256
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,15,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	446
	.byte	29,0,2,35,0,0,24
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,15,94,3
	.word	15333
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,15,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	469
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	469
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	469
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	469
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	469
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	469
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	469
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	469
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	469
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	469
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON0_Bits',0,15,111,3
	.word	15469
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,15,114,16,4,11
	.byte	'CANDIV',0,1
	.word	469
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	469
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	469
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	469
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	469
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	469
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	469
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	469
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON1_Bits',0,15,126,3
	.word	15749
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,15,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	469
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON2_Bits',0,15,135,1,3
	.word	15987
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,15,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	469
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	469
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	469
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	469
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	469
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON3_Bits',0,15,150,1,3
	.word	16115
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,15,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	469
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	469
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	469
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	469
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	469
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON4_Bits',0,15,165,1,3
	.word	16358
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,15,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	469
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CCUCON5_Bits',0,15,174,1,3
	.word	16593
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,15,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	469
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON6_Bits',0,15,181,1,3
	.word	16721
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,15,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	469
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON7_Bits',0,15,188,1,3
	.word	16821
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,15,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	469
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	469
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	469
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	469
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	469
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_CHIPID_Bits',0,15,202,1,3
	.word	16921
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,15,205,1,16,4,11
	.byte	'PWD',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	446
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_DTSCON_Bits',0,15,213,1,3
	.word	17129
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,15,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	486
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	469
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	486
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	469
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_DTSLIM_Bits',0,15,225,1,3
	.word	17294
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,15,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	486
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	469
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,15,235,1,3
	.word	17477
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,15,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	469
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	469
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	446
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	469
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	469
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EICR_Bits',0,15,129,2,3
	.word	17631
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,15,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_EIFR_Bits',0,15,143,2,3
	.word	17995
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,15,146,2,16,4,11
	.byte	'POL',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	486
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	469
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	469
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	469
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	469
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_EMSR_Bits',0,15,159,2,3
	.word	18206
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,15,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	486
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	446
	.byte	23,0,2,35,0,0,24
	.byte	'Ifx_SCU_ESRCFG_Bits',0,15,167,2,3
	.word	18458
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,15,170,2,16,4,11
	.byte	'ARI',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_ESROCFG_Bits',0,15,175,2,3
	.word	18576
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,15,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVR13CON_Bits',0,15,185,2,3
	.word	18687
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,15,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVR33CON_Bits',0,15,195,2,3
	.word	18850
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,15,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,15,205,2,3
	.word	19013
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,15,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,15,215,2,3
	.word	19171
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,15,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	469
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	469
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	469
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	469
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	469
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	469
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	469
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	469
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	469
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	486
	.byte	10,0,2,35,2,0,24
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,15,232,2,3
	.word	19336
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,15,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	486
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	469
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	469
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	486
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	469
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,15,245,2,3
	.word	19665
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,15,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVROVMON_Bits',0,15,255,2,3
	.word	19886
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,15,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,15,142,3,3
	.word	20049
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,15,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,15,152,3,3
	.word	20321
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,15,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,15,162,3,3
	.word	20474
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,15,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,15,172,3,3
	.word	20630
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,15,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,15,181,3,3
	.word	20792
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,15,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,15,191,3,3
	.word	20935
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,15,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,15,200,3,3
	.word	21100
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,15,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	486
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	469
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	469
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,15,211,3,3
	.word	21245
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,15,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	469
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,15,222,3,3
	.word	21426
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,15,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,15,232,3,3
	.word	21600
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,15,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	446
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,15,241,3,3
	.word	21760
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,15,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	446
	.byte	21,0,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,15,130,4,3
	.word	21904
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,15,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,15,139,4,3
	.word	22178
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,15,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,15,149,4,3
	.word	22317
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,15,152,4,16,4,11
	.byte	'EN0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	469
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	486
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	469
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	469
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	469
	.byte	8,0,2,35,3,0,24
	.byte	'Ifx_SCU_EXTCON_Bits',0,15,163,4,3
	.word	22480
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,15,166,4,16,4,11
	.byte	'STEP',0,2
	.word	486
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	469
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	486
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	469
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_FDR_Bits',0,15,174,4,3
	.word	22698
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,15,177,4,16,4,11
	.byte	'FS0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	8,0,2,35,3,0,24
	.byte	'Ifx_SCU_FMR_Bits',0,15,197,4,3
	.word	22861
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,15,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_ID_Bits',0,15,205,4,3
	.word	23197
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,15,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	469
	.byte	2,0,2,35,3,0,24
	.byte	'Ifx_SCU_IGCR_Bits',0,15,232,4,3
	.word	23304
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,15,235,4,16,4,11
	.byte	'P0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_IN_Bits',0,15,240,4,3
	.word	23756
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,15,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	469
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	469
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_IOCR_Bits',0,15,250,4,3
	.word	23855
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,15,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	486
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,15,131,5,3
	.word	24005
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,15,134,5,16,4,11
	.byte	'SEED',0,4
	.word	446
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	469
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,15,141,5,3
	.word	24154
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,15,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	446
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,15,149,5,3
	.word	24315
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,15,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	486
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	486
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_LCLCON_Bits',0,15,158,5,3
	.word	24445
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,15,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_LCLTEST_Bits',0,15,166,5,3
	.word	24577
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,15,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	469
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	486
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_MANID_Bits',0,15,174,5,3
	.word	24692
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,15,177,5,16,4,11
	.byte	'PS0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	486
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	486
	.byte	14,0,2,35,2,0,24
	.byte	'Ifx_SCU_OMR_Bits',0,15,185,5,3
	.word	24803
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,15,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	469
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	469
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	469
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	469
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	469
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	469
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_OSCCON_Bits',0,15,209,5,3
	.word	24961
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,15,212,5,16,4,11
	.byte	'P0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_OUT_Bits',0,15,217,5,3
	.word	25373
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,15,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	486
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	469
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	469
	.byte	6,0,2,35,3,0,24
	.byte	'Ifx_SCU_OVCCON_Bits',0,15,233,5,3
	.word	25474
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,15,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	446
	.byte	29,0,2,35,0,0,24
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,15,242,5,3
	.word	25741
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,15,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDISC_Bits',0,15,250,5,3
	.word	25877
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,15,253,5,16,4,11
	.byte	'PD0',0,1
	.word	469
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	469
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDR_Bits',0,15,132,6,3
	.word	25988
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,15,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PDRR_Bits',0,15,146,6,3
	.word	26121
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,15,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	486
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	469
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	469
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	469
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	469
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PLLCON0_Bits',0,15,166,6,3
	.word	26324
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,15,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	469
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	469
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	469
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	486
	.byte	9,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLCON1_Bits',0,15,177,6,3
	.word	26680
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,15,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	486
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLCON2_Bits',0,15,184,6,3
	.word	26858
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,15,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	486
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	469
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	469
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	469
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	469
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,15,204,6,3
	.word	26958
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,15,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	469
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	469
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	469
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	469
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	486
	.byte	9,0,2,35,2,0,24
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,15,215,6,3
	.word	27328
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,15,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,15,227,6,3
	.word	27514
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,15,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,24
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,15,241,6,3
	.word	27712
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,15,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	469
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	469
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	446
	.byte	21,0,2,35,0,0,24
	.byte	'Ifx_SCU_PMCSR_Bits',0,15,251,6,3
	.word	27945
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,15,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	469
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	469
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	469
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	469
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	469
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	469
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	469
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,15,153,7,3
	.word	28097
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,15,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	469
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	469
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	469
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	469
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,15,170,7,3
	.word	28664
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,15,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	469
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	469
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	469
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	469
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	469
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	469
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	469
	.byte	1,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,15,187,7,3
	.word	28958
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,15,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	469
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	469
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	469
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	469
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	469
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	486
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	469
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	469
	.byte	4,0,2,35,3,0,24
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,15,214,7,3
	.word	29236
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,15,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	486
	.byte	14,0,2,35,2,0,24
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,15,230,7,3
	.word	29732
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,15,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	486
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	469
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	469
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	469
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_RSTCON2_Bits',0,15,243,7,3
	.word	30045
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,15,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	469
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	469
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	469
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	469
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	469
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	469
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	469
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	469
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_RSTCON_Bits',0,15,129,8,3
	.word	30254
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,15,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	469
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	469
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	469
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	469
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	469
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	469
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	469
	.byte	3,0,2,35,3,0,24
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,15,155,8,3
	.word	30465
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,15,158,8,16,4,11
	.byte	'HBT',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,24
	.byte	'Ifx_SCU_SAFECON_Bits',0,15,162,8,3
	.word	30897
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,15,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	469
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	469
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	469
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	469
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	469
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	469
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	469
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	469
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	469
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	469
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	469
	.byte	7,0,2,35,3,0,24
	.byte	'Ifx_SCU_STSTAT_Bits',0,15,178,8,3
	.word	30993
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,15,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,24
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,15,186,8,3
	.word	31253
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,15,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	469
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	469
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	446
	.byte	23,0,2,35,0,0,24
	.byte	'Ifx_SCU_SYSCON_Bits',0,15,198,8,3
	.word	31378
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,15,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,15,208,8,3
	.word	31575
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,15,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,15,218,8,3
	.word	31728
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,15,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSET_Bits',0,15,228,8,3
	.word	31881
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,15,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,15,238,8,3
	.word	32034
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,15,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	32189
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32189
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32189
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32189
	.byte	16,0,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,15,247,8,3
	.word	32205
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,15,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	469
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	469
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,15,134,9,3
	.word	32335
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,15,137,9,16,4,11
	.byte	'AE',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	469
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,15,150,9,3
	.word	32573
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,15,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	32189
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32189
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32189
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32189
	.byte	16,0,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,15,159,9,3
	.word	32796
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,15,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	469
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,15,175,9,3
	.word	32922
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,15,178,9,16,4,11
	.byte	'AE',0,1
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	469
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	469
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	469
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	469
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	486
	.byte	16,0,2,35,2,0,24
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,15,191,9,3
	.word	33174
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14699
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN0',0,15,204,9,3
	.word	33393
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15256
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ACCEN1',0,15,212,9,3
	.word	33457
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15333
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ARSTDIS',0,15,220,9,3
	.word	33521
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15469
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON0',0,15,228,9,3
	.word	33586
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15749
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON1',0,15,236,9,3
	.word	33651
	.byte	12,15,239,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15987
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON2',0,15,244,9,3
	.word	33716
	.byte	12,15,247,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16115
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON3',0,15,252,9,3
	.word	33781
	.byte	12,15,255,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16358
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON4',0,15,132,10,3
	.word	33846
	.byte	12,15,135,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16593
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON5',0,15,140,10,3
	.word	33911
	.byte	12,15,143,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16721
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON6',0,15,148,10,3
	.word	33976
	.byte	12,15,151,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16821
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CCUCON7',0,15,156,10,3
	.word	34041
	.byte	12,15,159,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16921
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_CHIPID',0,15,164,10,3
	.word	34106
	.byte	12,15,167,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17129
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSCON',0,15,172,10,3
	.word	34170
	.byte	12,15,175,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17294
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSLIM',0,15,180,10,3
	.word	34234
	.byte	12,15,183,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17477
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_DTSSTAT',0,15,188,10,3
	.word	34298
	.byte	12,15,191,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17631
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EICR',0,15,196,10,3
	.word	34363
	.byte	12,15,199,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17995
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EIFR',0,15,204,10,3
	.word	34425
	.byte	12,15,207,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18206
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EMSR',0,15,212,10,3
	.word	34487
	.byte	12,15,215,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18458
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ESRCFG',0,15,220,10,3
	.word	34549
	.byte	12,15,223,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18576
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ESROCFG',0,15,228,10,3
	.word	34613
	.byte	12,15,231,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18687
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVR13CON',0,15,236,10,3
	.word	34678
	.byte	12,15,239,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18850
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVR33CON',0,15,244,10,3
	.word	34744
	.byte	12,15,247,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19013
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRADCSTAT',0,15,252,10,3
	.word	34810
	.byte	12,15,255,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19171
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRDVSTAT',0,15,132,11,3
	.word	34878
	.byte	12,15,135,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19336
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRMONCTRL',0,15,140,11,3
	.word	34945
	.byte	12,15,143,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19665
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVROSCCTRL',0,15,148,11,3
	.word	35013
	.byte	12,15,151,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19886
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVROVMON',0,15,156,11,3
	.word	35081
	.byte	12,15,159,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20049
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRRSTCON',0,15,164,11,3
	.word	35147
	.byte	12,15,167,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20321
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,15,172,11,3
	.word	35214
	.byte	12,15,175,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20474
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,15,180,11,3
	.word	35283
	.byte	12,15,183,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20630
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,15,188,11,3
	.word	35352
	.byte	12,15,191,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20792
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,15,196,11,3
	.word	35421
	.byte	12,15,199,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20935
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,15,204,11,3
	.word	35490
	.byte	12,15,207,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21100
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,15,212,11,3
	.word	35559
	.byte	12,15,215,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21245
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL1',0,15,220,11,3
	.word	35628
	.byte	12,15,223,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21426
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL2',0,15,228,11,3
	.word	35696
	.byte	12,15,231,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21600
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL3',0,15,236,11,3
	.word	35764
	.byte	12,15,239,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21760
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSDCTRL4',0,15,244,11,3
	.word	35832
	.byte	12,15,247,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21904
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRSTAT',0,15,252,11,3
	.word	35900
	.byte	12,15,255,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22178
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRTRIM',0,15,132,12,3
	.word	35965
	.byte	12,15,135,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22317
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EVRUVMON',0,15,140,12,3
	.word	36030
	.byte	12,15,143,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22480
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_EXTCON',0,15,148,12,3
	.word	36096
	.byte	12,15,151,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22698
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_FDR',0,15,156,12,3
	.word	36160
	.byte	12,15,159,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22861
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_FMR',0,15,164,12,3
	.word	36221
	.byte	12,15,167,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23197
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_ID',0,15,172,12,3
	.word	36282
	.byte	12,15,175,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23304
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IGCR',0,15,180,12,3
	.word	36342
	.byte	12,15,183,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23756
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IN',0,15,188,12,3
	.word	36404
	.byte	12,15,191,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23855
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_IOCR',0,15,196,12,3
	.word	36464
	.byte	12,15,199,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24005
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL0',0,15,204,12,3
	.word	36526
	.byte	12,15,207,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24154
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL1',0,15,212,12,3
	.word	36594
	.byte	12,15,215,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24315
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LBISTCTRL2',0,15,220,12,3
	.word	36662
	.byte	12,15,223,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24445
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LCLCON',0,15,228,12,3
	.word	36730
	.byte	12,15,231,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24577
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_LCLTEST',0,15,236,12,3
	.word	36794
	.byte	12,15,239,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24692
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_MANID',0,15,244,12,3
	.word	36859
	.byte	12,15,247,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24803
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OMR',0,15,252,12,3
	.word	36922
	.byte	12,15,255,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24961
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OSCCON',0,15,132,13,3
	.word	36983
	.byte	12,15,135,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25373
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OUT',0,15,140,13,3
	.word	37047
	.byte	12,15,143,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25474
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OVCCON',0,15,148,13,3
	.word	37108
	.byte	12,15,151,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25741
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_OVCENABLE',0,15,156,13,3
	.word	37172
	.byte	12,15,159,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25877
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDISC',0,15,164,13,3
	.word	37239
	.byte	12,15,167,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25988
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDR',0,15,172,13,3
	.word	37302
	.byte	12,15,175,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26121
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PDRR',0,15,180,13,3
	.word	37363
	.byte	12,15,183,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26324
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON0',0,15,188,13,3
	.word	37425
	.byte	12,15,191,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26680
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON1',0,15,196,13,3
	.word	37490
	.byte	12,15,199,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26858
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLCON2',0,15,204,13,3
	.word	37555
	.byte	12,15,207,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26958
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYCON0',0,15,212,13,3
	.word	37620
	.byte	12,15,215,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27328
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYCON1',0,15,220,13,3
	.word	37689
	.byte	12,15,223,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27514
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLERAYSTAT',0,15,228,13,3
	.word	37758
	.byte	12,15,231,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27712
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PLLSTAT',0,15,236,13,3
	.word	37827
	.byte	12,15,239,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27945
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMCSR',0,15,244,13,3
	.word	37892
	.byte	12,15,247,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28097
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR0',0,15,252,13,3
	.word	37955
	.byte	12,15,255,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28664
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR1',0,15,132,14,3
	.word	38020
	.byte	12,15,135,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28958
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWCR2',0,15,140,14,3
	.word	38085
	.byte	12,15,143,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29236
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWSTAT',0,15,148,14,3
	.word	38150
	.byte	12,15,151,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29732
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_PMSWSTATCLR',0,15,156,14,3
	.word	38216
	.byte	12,15,159,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30254
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTCON',0,15,164,14,3
	.word	38285
	.byte	12,15,167,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30045
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTCON2',0,15,172,14,3
	.word	38349
	.byte	12,15,175,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30465
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_RSTSTAT',0,15,180,14,3
	.word	38414
	.byte	12,15,183,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30897
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SAFECON',0,15,188,14,3
	.word	38479
	.byte	12,15,191,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30993
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_STSTAT',0,15,196,14,3
	.word	38544
	.byte	12,15,199,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31253
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SWRSTCON',0,15,204,14,3
	.word	38608
	.byte	12,15,207,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31378
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_SYSCON',0,15,212,14,3
	.word	38674
	.byte	12,15,215,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31575
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPCLR',0,15,220,14,3
	.word	38738
	.byte	12,15,223,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31728
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPDIS',0,15,228,14,3
	.word	38803
	.byte	12,15,231,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31881
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSET',0,15,236,14,3
	.word	38868
	.byte	12,15,239,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32034
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_TRAPSTAT',0,15,244,14,3
	.word	38933
	.byte	12,15,247,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32205
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON0',0,15,252,14,3
	.word	38999
	.byte	12,15,255,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32335
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_CON1',0,15,132,15,3
	.word	39068
	.byte	12,15,135,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32573
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTCPU_SR',0,15,140,15,3
	.word	39137
	.byte	12,15,143,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32796
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON0',0,15,148,15,3
	.word	39204
	.byte	12,15,151,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32922
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_CON1',0,15,156,15,3
	.word	39271
	.byte	12,15,159,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33174
	.byte	4,2,35,0,0,24
	.byte	'Ifx_SCU_WDTS_SR',0,15,164,15,3
	.word	39338
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,15,175,15,25,12,13
	.byte	'CON0',0
	.word	38999
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39068
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39137
	.byte	4,2,35,8,0,16
	.word	39403
	.byte	24
	.byte	'Ifx_SCU_WDTCPU',0,15,180,15,3
	.word	39466
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,15,183,15,25,12,13
	.byte	'CON0',0
	.word	39204
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39271
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39338
	.byte	4,2,35,8,0,16
	.word	39495
	.byte	24
	.byte	'Ifx_SCU_WDTS',0,15,188,15,3
	.word	39556
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,24
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	39583
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,24
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	39734
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,24
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	39978
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,24
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	40076
	.byte	24
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8266
	.byte	24
	.byte	'gpio_pin_enum',0,7,89,2
	.word	8710
	.byte	24
	.byte	'gpio_dir_enum',0,7,95,2
	.word	10684
	.byte	24
	.byte	'gpio_mode_enum',0,7,111,2
	.word	10702
	.byte	26,16,45,9,1,11
	.byte	'mode',0,1
	.word	469
	.byte	6,2,2,35,0,11
	.byte	'use_miso',0,1
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'use_cs',0,1
	.word	469
	.byte	1,0,2,35,0,0,24
	.byte	'spi_config_info_struct',0,16,50,2
	.word	40608
	.byte	24
	.byte	'spi_index_enum',0,8,48,2
	.word	10865
	.byte	24
	.byte	'spi_mode_enum',0,8,56,2
	.word	11005
	.byte	24
	.byte	'spi_sck_pin_enum',0,8,67,2
	.word	11059
	.byte	24
	.byte	'spi_mosi_pin_enum',0,8,78,2
	.word	11332
	.byte	24
	.byte	'spi_miso_pin_enum',0,8,89,2
	.word	11586
	.byte	24
	.byte	'spi_cs_pin_enum',0,8,140,1,2
	.word	11840
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L60:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,20
	.byte	46,1,49,19,0,0,21,5,0,49,19,0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,23,38
	.byte	0,73,19,0,0,24,22,0,3,8,58,15,59,15,57,15,73,19,0,0,25,21,0,54,15,0,0,26,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L61:
	.word	.L161-.L160
.L160:
	.half	3
	.word	.L163-.L162
.L162:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'zf_driver_delay.h',0,4,0,0
	.byte	'zf_driver_gpio.h',0,4,0,0
	.byte	'zf_driver_spi.h',0,4,0,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0
	.byte	'zf_driver_soft_spi.h',0,4,0,0,0
.L163:
.L161:
	.sdecl	'.debug_info',debug,cluster('absolute_encoder_get_location')
	.sect	'.debug_info'
.L62:
	.word	284
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L65,.L64
	.byte	2
	.word	.L58
	.byte	3
	.byte	'absolute_encoder_get_location',0,1,166,1,7
	.word	.L101
	.byte	1,1,1
	.word	.L53,.L102,.L52
	.byte	4
	.word	.L53,.L102
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('absolute_encoder_get_location')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('absolute_encoder_get_location')
	.sect	'.debug_line'
.L64:
	.word	.L165-.L164
.L164:
	.half	3
	.word	.L167-.L166
.L166:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0,0
.L167:
	.byte	5,5,7,0,5,2
	.word	.L53
	.byte	3,167,1,1,5,21,9
	.half	.L168-.L53
	.byte	1,5,19,9
	.half	.L169-.L168
	.byte	1,5,5,9
	.half	.L170-.L169
	.byte	3,1,1,5,46,9
	.half	.L171-.L170
	.byte	1,5,49,9
	.half	.L172-.L171
	.byte	1,5,18,9
	.half	.L173-.L172
	.byte	1,5,12,9
	.half	.L174-.L173
	.byte	3,1,1,5,5,9
	.half	.L175-.L174
	.byte	1,5,1,9
	.half	.L32-.L175
	.byte	3,1,1,7,9
	.half	.L66-.L32
	.byte	0,1,1
.L165:
	.sdecl	'.debug_ranges',debug,cluster('absolute_encoder_get_location')
	.sect	'.debug_ranges'
.L65:
	.word	-1,.L53,0,.L66-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('absolute_encoder_get_offset')
	.sect	'.debug_info'
.L67:
	.word	308
	.half	3
	.word	.L68
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L70,.L69
	.byte	2
	.word	.L58
	.byte	3
	.byte	'absolute_encoder_get_offset',0,1,180,1,7
	.word	.L101
	.byte	1,1,1
	.word	.L55,.L103,.L54
	.byte	4
	.word	.L55,.L103
	.byte	5
	.byte	'result_data',0,1,182,1,11
	.word	.L101,.L104
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('absolute_encoder_get_offset')
	.sect	'.debug_abbrev'
.L68:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('absolute_encoder_get_offset')
	.sect	'.debug_line'
.L69:
	.word	.L177-.L176
.L176:
	.half	3
	.word	.L179-.L178
.L178:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0,0
.L179:
	.byte	5,15,7,0,5,2
	.word	.L55
	.byte	3,182,1,1,5,8,9
	.half	.L34-.L55
	.byte	1,5,5,9
	.half	.L180-.L34
	.byte	1,5,31,7,9
	.half	.L181-.L180
	.byte	3,2,1,5,24,9
	.half	.L182-.L181
	.byte	1,5,47,9
	.half	.L183-.L182
	.byte	1,5,60,9
	.half	.L184-.L183
	.byte	1,5,69,9
	.half	.L185-.L184
	.byte	1,5,67,9
	.half	.L186-.L185
	.byte	1,5,84,9
	.half	.L187-.L186
	.byte	1,5,87,9
	.half	.L36-.L187
	.byte	1,5,100,9
	.half	.L188-.L36
	.byte	1,5,109,9
	.half	.L189-.L188
	.byte	1,5,107,9
	.half	.L190-.L189
	.byte	1,5,44,9
	.half	.L37-.L190
	.byte	1,5,124,9
	.half	.L151-.L37
	.byte	1,5,24,9
	.half	.L35-.L151
	.byte	3,4,1,5,39,9
	.half	.L191-.L35
	.byte	1,5,37,9
	.half	.L192-.L191
	.byte	1,5,5,9
	.half	.L38-.L192
	.byte	3,2,1,5,1,9
	.half	.L39-.L38
	.byte	3,1,1,7,9
	.half	.L71-.L39
	.byte	0,1,1
.L177:
	.sdecl	'.debug_ranges',debug,cluster('absolute_encoder_get_offset')
	.sect	'.debug_ranges'
.L70:
	.word	-1,.L55,0,.L71-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('absolute_encoder_init')
	.sect	'.debug_info'
.L72:
	.word	330
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L75,.L74
	.byte	2
	.word	.L58
	.byte	3
	.byte	'absolute_encoder_init',0,1,201,1,7
	.word	.L105
	.byte	1,1,1
	.word	.L57,.L106,.L56
	.byte	4
	.word	.L57,.L106
	.byte	5
	.byte	'return_state',0,1,203,1,11
	.word	.L105,.L107
	.byte	5
	.byte	'zero_position',0,1,204,1,12
	.word	.L108,.L109
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('absolute_encoder_init')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('absolute_encoder_init')
	.sect	'.debug_line'
.L74:
	.word	.L194-.L193
.L193:
	.half	3
	.word	.L196-.L195
.L195:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0,0
.L196:
	.byte	5,7,7,0,5,2
	.word	.L57
	.byte	3,200,1,1,5,24,9
	.half	.L152-.L57
	.byte	3,2,1,5,102,9
	.half	.L153-.L152
	.byte	3,5,1,5,129,1,9
	.half	.L197-.L153
	.byte	1,5,156,1,9
	.half	.L198-.L197
	.byte	1,5,14,9
	.half	.L199-.L198
	.byte	1,5,36,9
	.half	.L200-.L199
	.byte	1,5,47,9
	.half	.L201-.L200
	.byte	1,5,75,9
	.half	.L202-.L201
	.byte	1,5,15,9
	.half	.L203-.L202
	.byte	3,2,1,5,40,9
	.half	.L204-.L203
	.byte	1,5,45,9
	.half	.L205-.L204
	.byte	1,5,55,9
	.half	.L206-.L205
	.byte	1,5,39,9
	.half	.L40-.L206
	.byte	3,4,1,5,9,9
	.half	.L207-.L40
	.byte	1,5,26,7,9
	.half	.L208-.L207
	.byte	3,5,1,5,13,9
	.half	.L209-.L208
	.byte	3,1,1,9
	.half	.L210-.L209
	.byte	3,1,1,5,41,9
	.half	.L41-.L210
	.byte	3,2,1,5,67,9
	.half	.L211-.L41
	.byte	1,5,34,9
	.half	.L212-.L211
	.byte	3,1,1,5,39,9
	.half	.L154-.L212
	.byte	1,9
	.half	.L213-.L154
	.byte	3,1,1,5,41,9
	.half	.L156-.L213
	.byte	3,1,1,5,70,9
	.half	.L214-.L156
	.byte	1,5,41,9
	.half	.L158-.L214
	.byte	3,1,1,5,84,9
	.half	.L215-.L158
	.byte	1,5,5,9
	.half	.L42-.L215
	.byte	3,2,1,5,1,9
	.half	.L43-.L42
	.byte	3,1,1,7,9
	.half	.L76-.L43
	.byte	0,1,1
.L194:
	.sdecl	'.debug_ranges',debug,cluster('absolute_encoder_init')
	.sect	'.debug_ranges'
.L75:
	.word	-1,.L57,0,.L76-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('absolute_encoder_write_register')
	.sect	'.debug_info'
.L77:
	.word	313
	.half	3
	.word	.L78
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L80,.L79
	.byte	2
	.word	.L58
	.byte	3
	.byte	'absolute_encoder_write_register',0,1,78,13,1,1
	.word	.L45,.L110,.L44
	.byte	4
	.byte	'reg',0,1,78,51
	.word	.L105,.L111
	.byte	4
	.byte	'data',0,1,78,62
	.word	.L105,.L112
	.byte	5
	.word	.L45,.L110
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('absolute_encoder_write_register')
	.sect	'.debug_abbrev'
.L78:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('absolute_encoder_write_register')
	.sect	'.debug_line'
.L79:
	.word	.L217-.L216
.L216:
	.half	3
	.word	.L219-.L218
.L218:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0,0
.L219:
	.byte	5,13,7,0,5,2
	.word	.L45
	.byte	3,205,0,1,5,5,9
	.half	.L220-.L45
	.byte	3,2,1,9
	.half	.L3-.L220
	.byte	3,1,1,9
	.half	.L221-.L3
	.byte	3,1,1,9
	.half	.L132-.L221
	.byte	3,1,1,5,21,9
	.half	.L5-.L132
	.byte	3,1,1,5,5,9
	.half	.L222-.L5
	.byte	3,1,1,9
	.half	.L7-.L222
	.byte	3,1,1,9
	.half	.L223-.L7
	.byte	3,1,1,9
	.half	.L224-.L223
	.byte	3,1,1,5,1,9
	.half	.L9-.L224
	.byte	3,1,1,7,9
	.half	.L81-.L9
	.byte	0,1,1
.L217:
	.sdecl	'.debug_ranges',debug,cluster('absolute_encoder_write_register')
	.sect	'.debug_ranges'
.L80:
	.word	-1,.L45,0,.L81-.L45,0,0
	.sdecl	'.debug_info',debug,cluster('absolute_encoder_read_register')
	.sect	'.debug_info'
.L82:
	.word	317
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L85,.L84
	.byte	2
	.word	.L58
	.byte	3
	.byte	'absolute_encoder_read_register',0,1,98,14
	.word	.L105
	.byte	1,1
	.word	.L47,.L113,.L46
	.byte	4
	.byte	'reg',0,1,98,51
	.word	.L105,.L114
	.byte	5
	.word	.L47,.L113
	.byte	6
	.byte	'data',0,1,100,11
	.word	.L105,.L115
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('absolute_encoder_read_register')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('absolute_encoder_read_register')
	.sect	'.debug_line'
.L84:
	.word	.L226-.L225
.L225:
	.half	3
	.word	.L228-.L227
.L227:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0,0
.L228:
	.byte	5,14,7,0,5,2
	.word	.L47
	.byte	3,225,0,1,5,5,9
	.half	.L134-.L47
	.byte	3,3,1,9
	.half	.L11-.L134
	.byte	3,1,1,9
	.half	.L229-.L11
	.byte	3,1,1,9
	.half	.L230-.L229
	.byte	3,1,1,5,21,9
	.half	.L13-.L230
	.byte	3,1,1,5,5,9
	.half	.L231-.L13
	.byte	3,1,1,5,12,9
	.half	.L15-.L231
	.byte	3,1,1,5,10,9
	.half	.L137-.L15
	.byte	1,5,5,9
	.half	.L135-.L137
	.byte	3,1,1,9
	.half	.L138-.L135
	.byte	3,1,1,9
	.half	.L17-.L138
	.byte	3,1,1,5,1,9
	.half	.L18-.L17
	.byte	3,1,1,7,9
	.half	.L86-.L18
	.byte	0,1,1
.L226:
	.sdecl	'.debug_ranges',debug,cluster('absolute_encoder_read_register')
	.sect	'.debug_ranges'
.L85:
	.word	-1,.L47,0,.L86-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('absolute_encoder_read_data')
	.sect	'.debug_info'
.L87:
	.word	297
	.half	3
	.word	.L88
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L90,.L89
	.byte	2
	.word	.L58
	.byte	3
	.byte	'absolute_encoder_read_data',0,1,120,15
	.word	.L108
	.byte	1,1
	.word	.L49,.L116,.L48
	.byte	4
	.word	.L49,.L116
	.byte	5
	.byte	'data',0,1,122,12
	.word	.L108,.L117
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('absolute_encoder_read_data')
	.sect	'.debug_abbrev'
.L88:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('absolute_encoder_read_data')
	.sect	'.debug_line'
.L89:
	.word	.L233-.L232
.L232:
	.half	3
	.word	.L235-.L234
.L234:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0,0
.L235:
	.byte	5,5,7,0,5,2
	.word	.L49
	.byte	3,250,0,1,5,12,9
	.half	.L20-.L49
	.byte	3,1,1,5,18,9
	.half	.L140-.L20
	.byte	3,1,1,5,28,9
	.half	.L236-.L140
	.byte	1,5,13,9
	.half	.L141-.L236
	.byte	3,1,1,5,10,9
	.half	.L237-.L141
	.byte	1,5,5,9
	.half	.L238-.L237
	.byte	3,1,1,9
	.half	.L22-.L238
	.byte	3,1,1,5,1,9
	.half	.L23-.L22
	.byte	3,1,1,7,9
	.half	.L91-.L23
	.byte	0,1,1
.L233:
	.sdecl	'.debug_ranges',debug,cluster('absolute_encoder_read_data')
	.sect	'.debug_ranges'
.L90:
	.word	-1,.L49,0,.L91-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('absolute_encoder_self_check')
	.sect	'.debug_info'
.L92:
	.word	364
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L95,.L94
	.byte	2
	.word	.L58
	.byte	3
	.byte	'absolute_encoder_self_check',0,1,138,1,14
	.word	.L105
	.byte	1,1
	.word	.L51,.L118,.L50
	.byte	4
	.word	.L51,.L118
	.byte	5
	.byte	'i',0,1,140,1,11
	.word	.L105,.L119
	.byte	5
	.byte	'return_state',0,1,140,1,18
	.word	.L105,.L120
	.byte	5
	.byte	'dat',0,1,141,1,11
	.word	.L121,.L122
	.byte	5
	.byte	'time_count',0,1,142,1,12
	.word	.L108,.L123
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('absolute_encoder_self_check')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('absolute_encoder_self_check')
	.sect	'.debug_line'
.L94:
	.word	.L240-.L239
.L239:
	.half	3
	.word	.L242-.L241
.L241:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0,0,0,0,0
.L242:
	.byte	5,14,7,0,5,2
	.word	.L51
	.byte	3,137,1,1,5,31,9
	.half	.L143-.L51
	.byte	3,2,1,5,20,9
	.half	.L144-.L143
	.byte	3,1,1,5,18,9
	.half	.L243-.L144
	.byte	1,5,23,9
	.half	.L244-.L243
	.byte	3,1,1,5,52,9
	.half	.L145-.L244
	.byte	3,1,1,5,15,9
	.half	.L26-.L145
	.byte	3,2,1,5,25,9
	.half	.L147-.L26
	.byte	1,5,47,9
	.half	.L28-.L147
	.byte	3,2,1,5,55,9
	.half	.L245-.L28
	.byte	1,5,29,9
	.half	.L246-.L245
	.byte	3,1,1,9
	.half	.L247-.L246
	.byte	3,125,1,5,25,9
	.half	.L27-.L247
	.byte	1,5,56,7,9
	.half	.L248-.L27
	.byte	3,5,1,5,12,9
	.half	.L149-.L248
	.byte	1,5,9,9
	.half	.L148-.L149
	.byte	1,5,26,7,9
	.half	.L249-.L148
	.byte	3,2,1,5,13,9
	.half	.L250-.L249
	.byte	3,1,1,5,50,9
	.half	.L25-.L250
	.byte	3,118,1,5,11,9
	.half	.L251-.L25
	.byte	1,5,52,9
	.half	.L252-.L251
	.byte	1,5,5,7,9
	.half	.L30-.L252
	.byte	3,13,1,5,1,9
	.half	.L31-.L30
	.byte	3,1,1,7,9
	.half	.L96-.L31
	.byte	0,1,1
.L240:
	.sdecl	'.debug_ranges',debug,cluster('absolute_encoder_self_check')
	.sect	'.debug_ranges'
.L95:
	.word	-1,.L51,0,.L96-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('now_location')
	.sect	'.debug_info'
.L97:
	.word	239
	.half	3
	.word	.L98
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L58
	.byte	3
	.byte	'now_location',0,9,58,14
	.word	.L101
	.byte	5,3
	.word	now_location
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('now_location')
	.sect	'.debug_abbrev'
.L98:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('last_location')
	.sect	'.debug_info'
.L99:
	.word	240
	.half	3
	.word	.L100
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_absolute_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L58
	.byte	3
	.byte	'last_location',0,9,59,14
	.word	.L101
	.byte	5,3
	.word	last_location
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('last_location')
	.sect	'.debug_abbrev'
.L100:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('absolute_encoder_get_location')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L102-.L53
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('absolute_encoder_get_offset')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L103-.L55
	.half	2
	.byte	138,0
	.word	0,0
.L104:
	.word	-1,.L55,.L151-.L55,.L35-.L55
	.half	1
	.byte	82
	.word	.L38-.L55,.L103-.L55
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('absolute_encoder_init')
	.sect	'.debug_loc'
.L56:
	.word	-1,.L57,0,.L152-.L57
	.half	2
	.byte	138,0
	.word	.L152-.L57,.L106-.L57
	.half	2
	.byte	138,16
	.word	.L106-.L57,.L106-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L107:
	.word	-1,.L57,.L153-.L57,.L106-.L57
	.half	1
	.byte	88
	.word	.L159-.L57,.L106-.L57
	.half	1
	.byte	82
	.word	0,0
.L109:
	.word	-1,.L57,.L154-.L57,.L155-.L57
	.half	1
	.byte	95
	.word	.L156-.L57,.L42-.L57
	.half	1
	.byte	95
	.word	.L157-.L57,.L158-.L57
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('absolute_encoder_read_data')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L116-.L49
	.half	2
	.byte	138,0
	.word	0,0
.L117:
	.word	-1,.L49,.L140-.L49,.L141-.L49
	.half	1
	.byte	82
	.word	.L141-.L49,.L116-.L49
	.half	1
	.byte	88
	.word	.L142-.L49,.L116-.L49
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('absolute_encoder_read_register')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L47,0,.L113-.L47
	.half	2
	.byte	138,0
	.word	0,0
.L115:
	.word	-1,.L47,.L137-.L47,.L138-.L47
	.half	1
	.byte	82
	.word	.L135-.L47,.L113-.L47
	.half	1
	.byte	88
	.word	.L139-.L47,.L113-.L47
	.half	1
	.byte	82
	.word	0,0
.L114:
	.word	-1,.L47,0,.L133-.L47
	.half	1
	.byte	84
	.word	.L134-.L47,.L135-.L47
	.half	1
	.byte	88
	.word	.L10-.L47,.L136-.L47
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('absolute_encoder_self_check')
	.sect	'.debug_loc'
.L50:
	.word	-1,.L51,0,.L143-.L51
	.half	2
	.byte	138,0
	.word	.L143-.L51,.L118-.L51
	.half	2
	.byte	138,8
	.word	.L118-.L51,.L118-.L51
	.half	2
	.byte	138,0
	.word	0,0
.L122:
	.word	-1,.L51,0,.L118-.L51
	.half	2
	.byte	145,120
	.word	0,0
.L119:
	.word	-1,.L51,.L147-.L51,.L148-.L51
	.half	1
	.byte	95
	.word	0,0
.L120:
	.word	-1,.L51,.L144-.L51,.L118-.L51
	.half	1
	.byte	88
	.word	.L150-.L51,.L118-.L51
	.half	1
	.byte	82
	.word	0,0
.L123:
	.word	-1,.L51,.L145-.L51,.L146-.L51
	.half	1
	.byte	89
	.word	.L149-.L51,.L118-.L51
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('absolute_encoder_write_register')
	.sect	'.debug_loc'
.L44:
	.word	-1,.L45,0,.L110-.L45
	.half	2
	.byte	138,0
	.word	0,0
.L112:
	.word	-1,.L45,0,.L124-.L45
	.half	1
	.byte	85
	.word	.L2-.L45,.L126-.L45
	.half	1
	.byte	85
	.word	.L130-.L45,.L131-.L45
	.half	1
	.byte	89
	.word	.L131-.L45,.L132-.L45
	.half	1
	.byte	85
	.word	0,0
.L111:
	.word	-1,.L45,0,.L125-.L45
	.half	1
	.byte	84
	.word	.L2-.L45,.L127-.L45
	.half	1
	.byte	84
	.word	.L128-.L45,.L129-.L45
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L253:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('absolute_encoder_write_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L253,.L45,.L110-.L45
	.sdecl	'.debug_frame',debug,cluster('absolute_encoder_read_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L253,.L47,.L113-.L47
	.sdecl	'.debug_frame',debug,cluster('absolute_encoder_read_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L253,.L49,.L116-.L49
	.sdecl	'.debug_frame',debug,cluster('absolute_encoder_self_check')
	.sect	'.debug_frame'
	.word	36
	.word	.L253,.L51,.L118-.L51
	.byte	4
	.word	(.L143-.L51)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L118-.L143)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('absolute_encoder_get_location')
	.sect	'.debug_frame'
	.word	12
	.word	.L253,.L53,.L102-.L53
	.sdecl	'.debug_frame',debug,cluster('absolute_encoder_get_offset')
	.sect	'.debug_frame'
	.word	24
	.word	.L253,.L55,.L103-.L55
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('absolute_encoder_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L253,.L57,.L106-.L57
	.byte	4
	.word	(.L152-.L57)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L106-.L152)/2
	.byte	19,0,8,26,0,0
	; Module end
