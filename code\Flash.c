/*
 * Flash.c
 *
 *  Created on: 2025��3��30��
 *      Author: lenovo
 */
#include "zf_common_headfile.h"


int16 GL_NUM=0;//�ɼ���ǰ���������룩
double GPS_GET_LAT[40];//γ��
double GPS_GET_LON[40];//����

int N = 0;
int Num_flag = 0; //���ڰ����޸ľ�γ�ȵı�־λ
double r = 0.000001;


void GPS_GL_GET()//�ɼ���λ
{
    GPS_GET_LAT[GL_NUM]=gnss.latitude;// γ��
    GPS_GET_LON[GL_NUM]=gnss.longitude;// ����

    tft180_show_string(0, 5*8, "Cai_Lat");     tft180_show_float(80,5*8,GPS_GET_LAT[GL_NUM],3,6);//ǰγ
    tft180_show_string(0, 6*8, "Cai_Lon");     tft180_show_float(80,6*8,GPS_GET_LON[GL_NUM],3,6);//ǰ

    tft180_show_string(0, 8*8, "NUM:");        tft180_show_uint(80,8*8,GL_NUM+1,3);//ʾѲɼ
    GL_NUM++;
    if(GL_NUM>40)//ɼɼ
    {
        GL_NUM=40;//��ֹ����Խ��
    }
}

int e;

void GL_CRC(void)//�����ɵ�
{
    if(key1_flag)
    {
        while(key1_flag)//����ѭ��
        {
          gnss_data_parse();
          tft180_show_string(0, 1*8, "Now_Lat");   tft180_show_float(80, 1*8,  gnss.latitude, 3, 6);
          tft180_show_string(0, 2*8, "Now_Lon");   tft180_show_float(80, 2*8,  gnss.longitude, 3, 6);

          if(key2_flag)//ɼλ
          {
             GPS_GL_GET();//�ɼ���λ
             key2_flag=0;//����2��λ
          }

          if(key3_flag)//�˳�ѭ��
          {
            key3_flag=0;//����3��λ
            key1_flag=0;//����1��λ,�˳�ѭ��
            tft180_clear();
            break;
          }
        }
        GPS_point_access();
    }
    if(key2_flag)
    {
        while(key2_flag)
        {
            if(key1_flag)
            {
                key1_flag=0;
                for(int num=0;num<=35;num++)
               {
                  tft180_show_float(50,10*8,GPS_GET_LAT[num],3,6);
                  tft180_show_float(50,11*8,GPS_GET_LON[num],3,6);
                  system_delay_ms(50);
               }
            }
            if(key3_flag)
            {
                key3_flag=0;
                flash_erase_page(FLASH_SECTION_INDEX,10);
                flash_erase_page(FLASH_SECTION_INDEX,11);
            }
            if(key4_flag)
            {
                tft180_clear();
                key4_flag=key2_flag=0;
                break;
            }
        }
    }

    if(key4_flag)//������־λ��
    {
       tft180_show_string(100, 15*8, "ok");
       e=1;
       key4_flag=0;//4λ
    }
}
void GPS_point_num(void)//鿴λľγ
{
    int clat = 0, clon = 0;  // ����γ�Ⱥ;��ȵ����ݵ�����
    uint8 tag = 0;           // ���ڿ�����ʾ�ı�ǩ

    for(int j = 0; j < 15; j++) {
        if(GPS_GET_LAT[j] != 0) { clat++; }  // ͳ����Чγ�����ݵ�
    }
    for(int i = 0; i < 15; i++) {
        if(GPS_GET_LON[i] != 0) { clon++; }  // ͳ����Ч�������ݵ�
    }

    while(TRUE)
    {
        //��Ļ����ʾ��λ�ľ�γ�ȣ�������ʾ���ǵڼ�����λ��ֻ����ʾһ����λ�������ð���������Ҫ��ʾ�ĸ���λ
        tft180_show_string(0, 8*8, "weidu:");
        tft180_show_string(0, 9*8, "jingdu:");
        tft180_show_int(90, 8*8, clat, 3);
        tft180_show_int(90, 9*8, clon, 3);

        tft180_show_string(0, 10*8, "latitude:");
        tft180_show_float(90,10*8,GPS_GET_LAT[tag],3,6);


        tft180_show_string(0, 11*8, "longitude:");
        tft180_show_float(90,11*8,GPS_GET_LON[tag],3,6);

        tft180_show_string(0, 12*8, "tag:");
        tft180_show_uint(90, 12*8, tag, 2);

        if(key1_flag == 1) { key1_flag = 0; tag++; }
        if(key2_flag == 1) { key2_flag = 0; tag--; }
        if(key3_flag == 1) { key3_flag = 0;tft180_clear(); break; }
        if(tag > 10) { tag = 0; }
    }
}
void Key_Change_LAT_LON(void)
{
    if(key1_flag)
    {
        N=0;  Num_flag =0; tft180_clear();
        while(key1_flag)
        {
            if(key2_flag)
            {
                if(Num_flag ==0)  {N++;}
                if(Num_flag ==1)  {N--;}
                if(N == 4)  {Num_flag =1;}
                if(N == 0)  {Num_flag =0;}
                key2_flag =0;       tft180_show_int(1, 3*8, Num_flag, 3);
            }
            if(key3_flag)   {GPS_GET_LAT[N] +=r;  key3_flag = 0;}
            if(key4_flag)   {GPS_GET_LAT[N] -=r;  key4_flag = 0;}
            if(gpio_get_level(SWITCH1)) { key1_flag = 0;}
            tft180_show_string(0, 1*8, "N");           tft180_show_int(100, 1*8, N, 2);
            tft180_show_string(0, 2*8, "LAT");         tft180_show_float(100, 2*8,GPS_GET_LAT[N], 3, 6);
        }
    }

    if(key2_flag)
    {
        N=0;    Num_flag =0;    tft180_clear();
        while(key2_flag)
        {
            if(key1_flag)
            {
                if(Num_flag ==0)  {N++;}
                if(Num_flag ==1)  {N--;}
                if(N == 4)  {Num_flag =1;}
                if(N == 0)  {Num_flag =0;}
                key1_flag =0;   tft180_show_int(1, 3*8, Num_flag, 3);
            }
            if(key3_flag)   {GPS_GET_LON[N] +=r;  key3_flag = 0;}
            if(key4_flag)   {GPS_GET_LON[N] -=r;  key4_flag = 0;}
            if(gpio_get_level(SWITCH1))               { key2_flag = 0;}
            tft180_show_string(0, 1*8, "N");           tft180_show_int(100, 1*8, N, 2);
            tft180_show_string(0, 2*8, "LON");         tft180_show_float(100, 2*8,GPS_GET_LON[N], 3, 6);
        }
    }
}


void GPS_point_access(void)//ȡGPSλ
{
    int num;

    flash_buffer_clear();
//////////////////////////////////////////////////////////LAT////////////////////////////////////////////////////////////
    for(num=0;num<=35;num++)
    {
        flash_union_buffer[num].uint32_type = (uint32)(GPS_GET_LAT[num]*10000000);
    }

    flash_union_buffer[45].int16_type=GL_NUM;

    if(flash_check(FLASH_SECTION_INDEX, 11))
    {
        flash_erase_page(FLASH_SECTION_INDEX, 11);
    }

    flash_write_page_from_buffer(FLASH_SECTION_INDEX, 11);
//////////////////////////////////////////////////////////LAT////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////LOT////////////////////////////////////////////////////////////
    for(num=0;num<=35;num++)
    {
        flash_union_buffer[num].uint32_type = (uint32)(GPS_GET_LON[num]*10000000);
    }

    if(flash_check(FLASH_SECTION_INDEX, 10))
    {
        flash_erase_page(FLASH_SECTION_INDEX, 10);
    }

    flash_write_page_from_buffer(FLASH_SECTION_INDEX, 10);
 //////////////////////////////////////////////////////////LOT////////////////////////////////////////////////////////////
}

void GPS_point_read(void)
{
    int num;
    uint32 value = 0;
//////////////////////////////////////////////////////////LAT////////////////////////////////////////////////////////////
    if(flash_check(FLASH_SECTION_INDEX, 11))
    {
        flash_read_page_to_buffer(FLASH_SECTION_INDEX, 11);

        for(num=0;num<=35;num++)
        {
            value = flash_union_buffer[num].uint32_type;
            GPS_GET_LAT[num] = (double)value/10000000;
        }

        GL_NUM=flash_union_buffer[45].int16_type;
    }
//////////////////////////////////////////////////////////LAT////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////LOT////////////////////////////////////////////////////////////
    if(flash_check(FLASH_SECTION_INDEX, 10))
    {
        flash_read_page_to_buffer(FLASH_SECTION_INDEX, 10);

        for(num=0;num<=35;num++)
        {
            value = flash_union_buffer[num].uint32_type;
            GPS_GET_LON[num] = (double)value/10000000;
        }
    }
//////////////////////////////////////////////////////////LOT////////////////////////////////////////////////////////////
}








///*Flash��㣬�ݲ�����*/
//int GPS_Work_Flag = 0;
//int Clear_Flag = 0;
//double Gnss_Target_Points[2][15];//���ڽ���flash�ж�ȡ��Ŀ�����������ݸ��ƹ���,��һ��Ϊγ�ȣ��ڶ���Ϊ����
//union GPS_flash
//{
//    double double_type;
//    uint8 uint8_array[8];
//};
////GPS_flash_buffer��һ�����������飬ÿ��Ԫ�ؼȿ�����double����ʽ���ʣ�Ҳ������8�ֽ��������ʽ����
//union GPS_flash GPS_flash_buffer[11];//���������͵����飬���ڴ�ȡ11��GPS���ݵ㣬�����Լ���
//
//void GPS_point_access(void)//��ȡGPS��λ
//{
//    //1.��ջ���������2.�����ݷŵ�����������3.����Flash�ж�Ӧ�ľ���γ����ҳ����4.���������е����ݴ浽Flash��Ӧ��ҳ��
//    /*��ȡγ��*/
//
//    for(uint8 i=0; i<11; i++) {
//        GPS_flash_buffer[i].double_type = Gnss_Target_Points[0][i];  // ��γ�����ݴ���GPS_flash_buffer
//    }
//    flash_buffer_clear ();  // ���Flash������
//    for(uint8 i=0; i<11; i++) {
//        for(uint8 y=0; y<8; y++) {
//            flash_union_buffer[8*i + y].uint8_type = GPS_flash_buffer[i].uint8_array[y];  // ������д�뻺����flash_union_buffer
//        }
//    }
//    if(flash_check(0, LAT_PAGE)) {
//        flash_erase_page(0, LAT_PAGE);  // ���Flashγ��ҳ���Ѿ������ݣ��Ȳ���γ��ҳ��д��
//    }
//    flash_write_page_from_buffer(0, LAT_PAGE);  // ��γ������д��Flash
//
//
//    /*��ȡ����*/
//    for(uint8 i=0; i<11; i++) {
//        GPS_flash_buffer[i].double_type = Gnss_Target_Points[1][i];  // ���������ݴ���GPS_flash_buffer
//    }
//    flash_buffer_clear ();  // ���Flash������
//    for(uint8 i=0; i<11; i++) {
//        for(uint8 y=0; y<8; y++) {
//            flash_union_buffer[8*i + y].uint8_type = GPS_flash_buffer[i].uint8_array[y];  // ������д�뻺����flash_union_buffer
//        }
//    }
//    if(flash_check(0, LON_PAGE)) {
//        flash_erase_page(0, LON_PAGE);  // ���Flash����ҳ���Ѿ������ݣ��Ȳ�����д��
//    }
//    flash_write_page_from_buffer(0, LON_PAGE);  // ����������д��Flash
//}
//
//
//
//void GPS_point_read(void)//��ȡGPS��λ������11����ľ�γ����Ϣ���浽Gnss_Target_Points��ά������
//{
//
//    /*��ȡγ��*/
//
//    flash_buffer_clear ();  // ���Flash������
//
//    flash_read_page_to_buffer(0,LAT_PAGE);//�����ݴ�flash��γ��ҳ�����ݷŵ�������
//    for(uint8 i=0; i<11; i++) {
//        for(uint8 y=0; y<8; y++) {
//            GPS_flash_buffer[i].uint8_array[y] = flash_union_buffer[8*i + y].uint8_type;  // ��Flash���������ݸ��Ƶ�GPS_flash_buffer��
//        }
//    }
//    for(uint8 i=0; i<11; i++) {
//        Gnss_Target_Points[0][i] = GPS_flash_buffer[i].double_type;  // ��γ�����ݴ洢��Gnss_Target_Points[0]
//    }
//
//    /*��ȡ����*/
//
//    flash_buffer_clear ();  // ���Flash������
//
//    flash_read_page_to_buffer(0,LON_PAGE);//�����ݴ�flash�ľ���ҳ�����ݷŵ�������
//    for(uint8 i=0; i<11; i++) {
//        for(uint8 y=0; y<8; y++) {
//            GPS_flash_buffer[i].uint8_array[y] = flash_union_buffer[8*i + y].uint8_type;  // ��Flash���������ݸ��Ƶ�GPS_flash_buffer��
//        }
//    }
//    for(uint8 i=0; i<11; i++) {
//        Gnss_Target_Points[1][i] = GPS_flash_buffer[i].double_type;  // ���������ݴ洢��Gnss_Target_Points[1]
//    }
//
//    GPS_Work_Flag = 1;
//}


//void Flash_Get_Lat_Point(void)
//{
//    unsigned int num;
//
//    //��ջ�����
//    flash_buffer_clear();//ȷ���������Ǹɾ���
//
//    //�����ݴ浽������
//    for(num=0;num<20;num++)
//    {
//        flash_union_buffer[num].uint32_type=(uint32)(GPS_GET_LAT[num]*10000000);
//    }
//
//    //�ж�flash������û�����ݣ�����У�����
//    if(flash_check(FLASH_SECTION_INDEX, 11))                      // �ж��Ƿ�������
//            flash_erase_page(FLASH_SECTION_INDEX, 11);                // ������һҳ
//
//    //�ѻ����������ݴ浽flashָ��������ҳ��
//    flash_write_page_from_buffer(FLASH_SECTION_INDEX,11);
//    system_delay_ms(100);
//}
//
//void Flash_Get_Lot_Point(void)
//{
//    unsigned int num;
//
//    //��ջ�����
//    flash_buffer_clear();//ȷ���������Ǹɾ���
//
//    //�����ݴ浽������
//    for(num=0;num<20;num++)
//    {
//        flash_union_buffer[num].uint32_type=(uint32)(GPS_GET_LON[num]*10000000);
//    }
//
//    //�ж�flash������û�����ݣ�����У�����
//    if(flash_check(FLASH_SECTION_INDEX, 10))                      // �ж��Ƿ�������
//            flash_erase_page(FLASH_SECTION_INDEX, 10);                // ������һҳ
//
//    //�ѻ����������ݴ浽flashָ��������ҳ��
//    flash_write_page_from_buffer(FLASH_SECTION_INDEX,10);
//    system_delay_ms(100);
//}
//
//void Flash_Read_Lat_Point(void)
//{
//    //ȡ��flash�е�����
//    unsigned char num;
//    uint32 value=0;
//
//    if(flash_check(FLASH_SECTION_INDEX,11))//��������ݾͰ�flash�е����ݷŻػ�����
//    {
//        flash_read_page_to_buffer(FLASH_SECTION_INDEX,11);
//        for(num=0;num<20;num++)
//        {
//            value=flash_union_buffer[num].uint32_type;
//            GPS_GET_LAT[num]=(double)value/10000000.0;
//        }
//    }
//    system_delay_ms(100);
//}
//
//void Flash_Read_Lot_Point(void)
//{
//    //ȡ��flash�е�����
//    unsigned char num;
//    uint32 value=0;
//
//    if(flash_check(FLASH_SECTION_INDEX,10))//��������ݾͰ�flash�е����ݷŻػ�����
//    {
//        flash_read_page_to_buffer(FLASH_SECTION_INDEX,10);
//        for(num=0;num<20;num++)
//        {
//            value=flash_union_buffer[num].uint32_type;
//            GPS_GET_LON[num]=(double)value/10000000.0;
//        }
//    }
//    system_delay_ms(100);
//}

