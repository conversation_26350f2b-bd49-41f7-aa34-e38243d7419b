	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc29824a --dep-file=IfxQspi_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxQspi_cfg.IfxQspi_cfg_indexMap',data,rom,cluster('IfxQspi_cfg_indexMap')
	.sect	'.rodata.IfxQspi_cfg.IfxQspi_cfg_indexMap'
	.global	IfxQspi_cfg_indexMap
	.align	4
IfxQspi_cfg_indexMap:	.type	object
	.size	IfxQspi_cfg_indexMap,32
	.word	-268428288
	.space	4
	.word	-268428032,1,-268427776,2
	.word	-268427520,3
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	6337
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	233
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	264
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	297
	.byte	4,1,5
	.word	324
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	326
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	349
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	380
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	417
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	233
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	468
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	496
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	529
	.byte	6
	.byte	'void',0,5
	.word	555
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	561
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	496
	.byte	7
	.word	555
	.byte	5
	.word	601
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	606
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	468
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	611
	.byte	10
	.byte	'_Ifx_QSPI_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_QSPI_ACCEN0_Bits',0,4,79,3
	.word	677
	.byte	10
	.byte	'_Ifx_QSPI_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_QSPI_ACCEN1_Bits',0,4,85,3
	.word	1236
	.byte	10
	.byte	'_Ifx_QSPI_BACON_Bits',0,4,88,16,4,11
	.byte	'LAST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'IPRE',0,1
	.word	349
	.byte	3,4,2,35,0,11
	.byte	'IDLE',0,1
	.word	349
	.byte	3,1,2,35,0,11
	.byte	'LPRE',0,2
	.word	380
	.byte	3,6,2,35,0,11
	.byte	'LEAD',0,1
	.word	349
	.byte	3,3,2,35,1,11
	.byte	'TPRE',0,1
	.word	349
	.byte	3,0,2,35,1,11
	.byte	'TRAIL',0,1
	.word	349
	.byte	3,5,2,35,2,11
	.byte	'PARTYP',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'UINT',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'MSB',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'BYTE',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'DL',0,2
	.word	380
	.byte	5,4,2,35,2,11
	.byte	'CS',0,1
	.word	349
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_QSPI_BACON_Bits',0,4,103,3
	.word	1315
	.byte	10
	.byte	'_Ifx_QSPI_BACONENTRY_Bits',0,4,106,16,4,11
	.byte	'E',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_QSPI_BACONENTRY_Bits',0,4,109,3
	.word	1576
	.byte	10
	.byte	'_Ifx_QSPI_CAPCON_Bits',0,4,112,16,4,11
	.byte	'CAP',0,2
	.word	380
	.byte	15,1,2,35,0,11
	.byte	'OVF',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'EDGECON',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'INS',0,1
	.word	349
	.byte	2,4,2,35,2,11
	.byte	'EN',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	380
	.byte	7,4,2,35,2,11
	.byte	'CAPC',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'CAPS',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'CAPF',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'CAPSEL',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_QSPI_CAPCON_Bits',0,4,124,3
	.word	1654
	.byte	10
	.byte	'_Ifx_QSPI_CLC_Bits',0,4,127,16,4,11
	.byte	'DISR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_QSPI_CLC_Bits',0,4,134,1,3
	.word	1878
	.byte	10
	.byte	'_Ifx_QSPI_DATAENTRY_Bits',0,4,137,1,16,4,11
	.byte	'E',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_QSPI_DATAENTRY_Bits',0,4,140,1,3
	.word	2022
	.byte	10
	.byte	'_Ifx_QSPI_ECON_Bits',0,4,143,1,16,4,11
	.byte	'Q',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'A',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'B',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'C',0,1
	.word	349
	.byte	2,4,2,35,1,11
	.byte	'CPH',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'CPOL',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'PAREN',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	264
	.byte	15,2,2,35,0,11
	.byte	'BE',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_QSPI_ECON_Bits',0,4,154,1,3
	.word	2100
	.byte	10
	.byte	'_Ifx_QSPI_FLAGSCLEAR_Bits',0,4,157,1,16,4,11
	.byte	'ERRORCLEARS',0,2
	.word	380
	.byte	9,7,2,35,0,11
	.byte	'TXC',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'RXC',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'PT1C',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'PT2C',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	349
	.byte	2,1,2,35,1,11
	.byte	'USRC',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_QSPI_FLAGSCLEAR_Bits',0,4,167,1,3
	.word	2292
	.byte	10
	.byte	'_Ifx_QSPI_GLOBALCON1_Bits',0,4,170,1,16,4,11
	.byte	'ERRORENS',0,2
	.word	380
	.byte	9,7,2,35,0,11
	.byte	'TXEN',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'RXEN',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'PT1EN',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'PT2EN',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	349
	.byte	2,1,2,35,1,11
	.byte	'USREN',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'TXFIFOINT',0,1
	.word	349
	.byte	2,6,2,35,2,11
	.byte	'RXFIFOINT',0,1
	.word	349
	.byte	2,4,2,35,2,11
	.byte	'PT1',0,1
	.word	349
	.byte	3,1,2,35,2,11
	.byte	'PT2',0,2
	.word	380
	.byte	3,6,2,35,2,11
	.byte	'TXFM',0,1
	.word	349
	.byte	2,4,2,35,3,11
	.byte	'RXFM',0,1
	.word	349
	.byte	2,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_QSPI_GLOBALCON1_Bits',0,4,186,1,3
	.word	2506
	.byte	10
	.byte	'_Ifx_QSPI_GLOBALCON_Bits',0,4,189,1,16,4,11
	.byte	'TQ',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'SI',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'EXPECT',0,1
	.word	349
	.byte	4,2,2,35,1,11
	.byte	'LB',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'DEL0',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'STROBE',0,1
	.word	349
	.byte	5,3,2,35,2,11
	.byte	'SRF',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'STIP',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'EN',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'MS',0,1
	.word	349
	.byte	2,5,2,35,3,11
	.byte	'AREN',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'RESETS',0,1
	.word	349
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_QSPI_GLOBALCON_Bits',0,4,205,1,3
	.word	2826
	.byte	10
	.byte	'_Ifx_QSPI_ID_Bits',0,4,208,1,16,4,11
	.byte	'MODREV',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_QSPI_ID_Bits',0,4,213,1,3
	.word	3123
	.byte	10
	.byte	'_Ifx_QSPI_KRST0_Bits',0,4,216,1,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	264
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_QSPI_KRST0_Bits',0,4,221,1,3
	.word	3232
	.byte	10
	.byte	'_Ifx_QSPI_KRST1_Bits',0,4,224,1,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_QSPI_KRST1_Bits',0,4,228,1,3
	.word	3345
	.byte	10
	.byte	'_Ifx_QSPI_KRSTCLR_Bits',0,4,231,1,16,4,11
	.byte	'CLR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_QSPI_KRSTCLR_Bits',0,4,235,1,3
	.word	3439
	.byte	10
	.byte	'_Ifx_QSPI_MIXENTRY_Bits',0,4,238,1,16,4,11
	.byte	'E',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_QSPI_MIXENTRY_Bits',0,4,241,1,3
	.word	3537
	.byte	10
	.byte	'_Ifx_QSPI_OCS_Bits',0,4,244,1,16,4,11
	.byte	'reserved_0',0,4
	.word	264
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_QSPI_OCS_Bits',0,4,251,1,3
	.word	3613
	.byte	10
	.byte	'_Ifx_QSPI_PISEL_Bits',0,4,254,1,16,4,11
	.byte	'MRIS',0,1
	.word	349
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'SRIS',0,1
	.word	349
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'SCIS',0,1
	.word	349
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SLSIS',0,1
	.word	349
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	264
	.byte	17,0,2,35,0,0,3
	.byte	'Ifx_QSPI_PISEL_Bits',0,4,136,2,3
	.word	3761
	.byte	10
	.byte	'_Ifx_QSPI_RXEXIT_Bits',0,4,139,2,16,4,11
	.byte	'E',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_QSPI_RXEXIT_Bits',0,4,142,2,3
	.word	3973
	.byte	10
	.byte	'_Ifx_QSPI_RXEXITD_Bits',0,4,145,2,16,4,11
	.byte	'E',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_QSPI_RXEXITD_Bits',0,4,148,2,3
	.word	4045
	.byte	10
	.byte	'_Ifx_QSPI_SSOC_Bits',0,4,151,2,16,4,11
	.byte	'AOL',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'OEN',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_QSPI_SSOC_Bits',0,4,155,2,3
	.word	4119
	.byte	10
	.byte	'_Ifx_QSPI_STATUS1_Bits',0,4,158,2,16,4,11
	.byte	'BITCOUNT',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	264
	.byte	20,4,2,35,0,11
	.byte	'BRDEN',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'BRD',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'SPDEN',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'SPD',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_QSPI_STATUS1_Bits',0,4,166,2,3
	.word	4204
	.byte	10
	.byte	'_Ifx_QSPI_STATUS_Bits',0,4,169,2,16,4,11
	.byte	'ERRORFLAGS',0,2
	.word	380
	.byte	9,7,2,35,0,11
	.byte	'TXF',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'RXF',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'PT1F',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'PT2F',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	349
	.byte	2,1,2,35,1,11
	.byte	'USRF',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'TXFIFOLEVEL',0,1
	.word	349
	.byte	3,5,2,35,2,11
	.byte	'RXFIFOLEVEL',0,1
	.word	349
	.byte	3,2,2,35,2,11
	.byte	'SLAVESEL',0,2
	.word	380
	.byte	4,6,2,35,2,11
	.byte	'RPV',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'TPV',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'PHASE',0,1
	.word	349
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_QSPI_STATUS_Bits',0,4,184,2,3
	.word	4371
	.byte	10
	.byte	'_Ifx_QSPI_XXLCON_Bits',0,4,187,2,16,4,11
	.byte	'XDL',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'BYTECOUNT',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_QSPI_XXLCON_Bits',0,4,191,2,3
	.word	4666
	.byte	12,4,199,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	677
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_ACCEN0',0,4,204,2,3
	.word	4761
	.byte	12,4,207,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1236
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_ACCEN1',0,4,212,2,3
	.word	4826
	.byte	12,4,215,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1315
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_BACON',0,4,220,2,3
	.word	4891
	.byte	12,4,223,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1576
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_BACONENTRY',0,4,228,2,3
	.word	4955
	.byte	12,4,231,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1654
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_CAPCON',0,4,236,2,3
	.word	5024
	.byte	12,4,239,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1878
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_CLC',0,4,244,2,3
	.word	5089
	.byte	12,4,247,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2022
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_DATAENTRY',0,4,252,2,3
	.word	5151
	.byte	12,4,255,2,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2100
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_ECON',0,4,132,3,3
	.word	5219
	.byte	12,4,135,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2292
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_FLAGSCLEAR',0,4,140,3,3
	.word	5282
	.byte	12,4,143,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2826
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_GLOBALCON',0,4,148,3,3
	.word	5351
	.byte	12,4,151,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2506
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_GLOBALCON1',0,4,156,3,3
	.word	5419
	.byte	12,4,159,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3123
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_ID',0,4,164,3,3
	.word	5488
	.byte	12,4,167,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3232
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_KRST0',0,4,172,3,3
	.word	5549
	.byte	12,4,175,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3345
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_KRST1',0,4,180,3,3
	.word	5613
	.byte	12,4,183,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3439
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_KRSTCLR',0,4,188,3,3
	.word	5677
	.byte	12,4,191,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3537
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_MIXENTRY',0,4,196,3,3
	.word	5743
	.byte	12,4,199,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3613
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_OCS',0,4,204,3,3
	.word	5810
	.byte	12,4,207,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3761
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_PISEL',0,4,212,3,3
	.word	5872
	.byte	12,4,215,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3973
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_RXEXIT',0,4,220,3,3
	.word	5936
	.byte	12,4,223,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4045
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_RXEXITD',0,4,228,3,3
	.word	6001
	.byte	12,4,231,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4119
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_SSOC',0,4,236,3,3
	.word	6067
	.byte	12,4,239,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4371
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_STATUS',0,4,244,3,3
	.word	6130
	.byte	12,4,247,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4204
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_STATUS1',0,4,252,3,3
	.word	6195
	.byte	12,4,255,3,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4666
	.byte	4,2,35,0,0,3
	.byte	'Ifx_QSPI_XXLCON',0,4,132,4,3
	.word	6261
	.byte	13,32
	.word	611
	.byte	14,3,0
.L8:
	.byte	15
	.word	6326
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxQspi_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxQspi_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	267
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxQspi_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_cfg_indexMap',0,1,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxQspi_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
