/**
 * \file IfxFlash_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Flash_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Flash
 * 
 */
#ifndef IFXFLASH_BF_H
#define IFXFLASH_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Flash_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN0 */
#define IFX_FLASH_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN0 */
#define IFX_FLASH_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN0 */
#define IFX_FLASH_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN10 */
#define IFX_FLASH_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN10 */
#define IFX_FLASH_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN10 */
#define IFX_FLASH_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN11 */
#define IFX_FLASH_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN11 */
#define IFX_FLASH_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN11 */
#define IFX_FLASH_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN12 */
#define IFX_FLASH_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN12 */
#define IFX_FLASH_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN12 */
#define IFX_FLASH_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN13 */
#define IFX_FLASH_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN13 */
#define IFX_FLASH_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN13 */
#define IFX_FLASH_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN14 */
#define IFX_FLASH_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN14 */
#define IFX_FLASH_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN14 */
#define IFX_FLASH_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN15 */
#define IFX_FLASH_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN15 */
#define IFX_FLASH_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN15 */
#define IFX_FLASH_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN16 */
#define IFX_FLASH_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN16 */
#define IFX_FLASH_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN16 */
#define IFX_FLASH_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN17 */
#define IFX_FLASH_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN17 */
#define IFX_FLASH_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN17 */
#define IFX_FLASH_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN18 */
#define IFX_FLASH_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN18 */
#define IFX_FLASH_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN18 */
#define IFX_FLASH_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN19 */
#define IFX_FLASH_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN19 */
#define IFX_FLASH_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN19 */
#define IFX_FLASH_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN1 */
#define IFX_FLASH_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN1 */
#define IFX_FLASH_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN1 */
#define IFX_FLASH_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN20 */
#define IFX_FLASH_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN20 */
#define IFX_FLASH_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN20 */
#define IFX_FLASH_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN21 */
#define IFX_FLASH_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN21 */
#define IFX_FLASH_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN21 */
#define IFX_FLASH_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN22 */
#define IFX_FLASH_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN22 */
#define IFX_FLASH_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN22 */
#define IFX_FLASH_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN23 */
#define IFX_FLASH_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN23 */
#define IFX_FLASH_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN23 */
#define IFX_FLASH_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN24 */
#define IFX_FLASH_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN24 */
#define IFX_FLASH_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN24 */
#define IFX_FLASH_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN25 */
#define IFX_FLASH_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN25 */
#define IFX_FLASH_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN25 */
#define IFX_FLASH_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN26 */
#define IFX_FLASH_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN26 */
#define IFX_FLASH_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN26 */
#define IFX_FLASH_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN27 */
#define IFX_FLASH_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN27 */
#define IFX_FLASH_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN27 */
#define IFX_FLASH_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN28 */
#define IFX_FLASH_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN28 */
#define IFX_FLASH_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN28 */
#define IFX_FLASH_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN29 */
#define IFX_FLASH_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN29 */
#define IFX_FLASH_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN29 */
#define IFX_FLASH_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN2 */
#define IFX_FLASH_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN2 */
#define IFX_FLASH_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN2 */
#define IFX_FLASH_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN30 */
#define IFX_FLASH_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN30 */
#define IFX_FLASH_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN30 */
#define IFX_FLASH_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN31 */
#define IFX_FLASH_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN31 */
#define IFX_FLASH_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN31 */
#define IFX_FLASH_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN3 */
#define IFX_FLASH_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN3 */
#define IFX_FLASH_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN3 */
#define IFX_FLASH_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN4 */
#define IFX_FLASH_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN4 */
#define IFX_FLASH_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN4 */
#define IFX_FLASH_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN5 */
#define IFX_FLASH_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN5 */
#define IFX_FLASH_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN5 */
#define IFX_FLASH_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN6 */
#define IFX_FLASH_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN6 */
#define IFX_FLASH_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN6 */
#define IFX_FLASH_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN7 */
#define IFX_FLASH_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN7 */
#define IFX_FLASH_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN7 */
#define IFX_FLASH_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN8 */
#define IFX_FLASH_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN8 */
#define IFX_FLASH_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN8 */
#define IFX_FLASH_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_FLASH_ACCEN0_Bits.EN9 */
#define IFX_FLASH_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ACCEN0_Bits.EN9 */
#define IFX_FLASH_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ACCEN0_Bits.EN9 */
#define IFX_FLASH_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_FLASH_CBAB_CFG_Bits.CLR */
#define IFX_FLASH_CBAB_CFG_CLR_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_CFG_Bits.CLR */
#define IFX_FLASH_CBAB_CFG_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_CFG_Bits.CLR */
#define IFX_FLASH_CBAB_CFG_CLR_OFF (8u)

/** \brief  Length for Ifx_FLASH_CBAB_CFG_Bits.DIS */
#define IFX_FLASH_CBAB_CFG_DIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_CFG_Bits.DIS */
#define IFX_FLASH_CBAB_CFG_DIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_CFG_Bits.DIS */
#define IFX_FLASH_CBAB_CFG_DIS_OFF (9u)

/** \brief  Length for Ifx_FLASH_CBAB_CFG_Bits.SEL */
#define IFX_FLASH_CBAB_CFG_SEL_LEN (6u)

/** \brief  Mask for Ifx_FLASH_CBAB_CFG_Bits.SEL */
#define IFX_FLASH_CBAB_CFG_SEL_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_CBAB_CFG_Bits.SEL */
#define IFX_FLASH_CBAB_CFG_SEL_OFF (0u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD0 */
#define IFX_FLASH_CBAB_STAT_VLD0_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD0 */
#define IFX_FLASH_CBAB_STAT_VLD0_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD0 */
#define IFX_FLASH_CBAB_STAT_VLD0_OFF (0u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD1 */
#define IFX_FLASH_CBAB_STAT_VLD1_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD1 */
#define IFX_FLASH_CBAB_STAT_VLD1_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD1 */
#define IFX_FLASH_CBAB_STAT_VLD1_OFF (1u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD2 */
#define IFX_FLASH_CBAB_STAT_VLD2_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD2 */
#define IFX_FLASH_CBAB_STAT_VLD2_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD2 */
#define IFX_FLASH_CBAB_STAT_VLD2_OFF (2u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD3 */
#define IFX_FLASH_CBAB_STAT_VLD3_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD3 */
#define IFX_FLASH_CBAB_STAT_VLD3_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD3 */
#define IFX_FLASH_CBAB_STAT_VLD3_OFF (3u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD4 */
#define IFX_FLASH_CBAB_STAT_VLD4_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD4 */
#define IFX_FLASH_CBAB_STAT_VLD4_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD4 */
#define IFX_FLASH_CBAB_STAT_VLD4_OFF (4u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD5 */
#define IFX_FLASH_CBAB_STAT_VLD5_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD5 */
#define IFX_FLASH_CBAB_STAT_VLD5_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD5 */
#define IFX_FLASH_CBAB_STAT_VLD5_OFF (5u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD6 */
#define IFX_FLASH_CBAB_STAT_VLD6_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD6 */
#define IFX_FLASH_CBAB_STAT_VLD6_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD6 */
#define IFX_FLASH_CBAB_STAT_VLD6_OFF (6u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD7 */
#define IFX_FLASH_CBAB_STAT_VLD7_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD7 */
#define IFX_FLASH_CBAB_STAT_VLD7_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD7 */
#define IFX_FLASH_CBAB_STAT_VLD7_OFF (7u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD8 */
#define IFX_FLASH_CBAB_STAT_VLD8_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD8 */
#define IFX_FLASH_CBAB_STAT_VLD8_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD8 */
#define IFX_FLASH_CBAB_STAT_VLD8_OFF (8u)

/** \brief  Length for Ifx_FLASH_CBAB_STAT_Bits.VLD9 */
#define IFX_FLASH_CBAB_STAT_VLD9_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_STAT_Bits.VLD9 */
#define IFX_FLASH_CBAB_STAT_VLD9_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_STAT_Bits.VLD9 */
#define IFX_FLASH_CBAB_STAT_VLD9_OFF (9u)

/** \brief  Length for Ifx_FLASH_CBAB_TOP_Bits.ADDR */
#define IFX_FLASH_CBAB_TOP_ADDR_LEN (19u)

/** \brief  Mask for Ifx_FLASH_CBAB_TOP_Bits.ADDR */
#define IFX_FLASH_CBAB_TOP_ADDR_MSK (0x7ffffu)

/** \brief  Offset for Ifx_FLASH_CBAB_TOP_Bits.ADDR */
#define IFX_FLASH_CBAB_TOP_ADDR_OFF (5u)

/** \brief  Length for Ifx_FLASH_CBAB_TOP_Bits.CLR */
#define IFX_FLASH_CBAB_TOP_CLR_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_TOP_Bits.CLR */
#define IFX_FLASH_CBAB_TOP_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_TOP_Bits.CLR */
#define IFX_FLASH_CBAB_TOP_CLR_OFF (31u)

/** \brief  Length for Ifx_FLASH_CBAB_TOP_Bits.ERR */
#define IFX_FLASH_CBAB_TOP_ERR_LEN (6u)

/** \brief  Mask for Ifx_FLASH_CBAB_TOP_Bits.ERR */
#define IFX_FLASH_CBAB_TOP_ERR_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_CBAB_TOP_Bits.ERR */
#define IFX_FLASH_CBAB_TOP_ERR_OFF (24u)

/** \brief  Length for Ifx_FLASH_CBAB_TOP_Bits.VLD */
#define IFX_FLASH_CBAB_TOP_VLD_LEN (1u)

/** \brief  Mask for Ifx_FLASH_CBAB_TOP_Bits.VLD */
#define IFX_FLASH_CBAB_TOP_VLD_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_CBAB_TOP_Bits.VLD */
#define IFX_FLASH_CBAB_TOP_VLD_OFF (30u)

/** \brief  Length for Ifx_FLASH_COMM0_Bits.STATUS */
#define IFX_FLASH_COMM0_STATUS_LEN (8u)

/** \brief  Mask for Ifx_FLASH_COMM0_Bits.STATUS */
#define IFX_FLASH_COMM0_STATUS_MSK (0xffu)

/** \brief  Offset for Ifx_FLASH_COMM0_Bits.STATUS */
#define IFX_FLASH_COMM0_STATUS_OFF (0u)

/** \brief  Length for Ifx_FLASH_COMM1_Bits.DATA */
#define IFX_FLASH_COMM1_DATA_LEN (8u)

/** \brief  Mask for Ifx_FLASH_COMM1_Bits.DATA */
#define IFX_FLASH_COMM1_DATA_MSK (0xffu)

/** \brief  Offset for Ifx_FLASH_COMM1_Bits.DATA */
#define IFX_FLASH_COMM1_DATA_OFF (8u)

/** \brief  Length for Ifx_FLASH_COMM1_Bits.STATUS */
#define IFX_FLASH_COMM1_STATUS_LEN (8u)

/** \brief  Mask for Ifx_FLASH_COMM1_Bits.STATUS */
#define IFX_FLASH_COMM1_STATUS_MSK (0xffu)

/** \brief  Offset for Ifx_FLASH_COMM1_Bits.STATUS */
#define IFX_FLASH_COMM1_STATUS_OFF (0u)

/** \brief  Length for Ifx_FLASH_COMM2_Bits.DATA */
#define IFX_FLASH_COMM2_DATA_LEN (8u)

/** \brief  Mask for Ifx_FLASH_COMM2_Bits.DATA */
#define IFX_FLASH_COMM2_DATA_MSK (0xffu)

/** \brief  Offset for Ifx_FLASH_COMM2_Bits.DATA */
#define IFX_FLASH_COMM2_DATA_OFF (8u)

/** \brief  Length for Ifx_FLASH_COMM2_Bits.STATUS */
#define IFX_FLASH_COMM2_STATUS_LEN (8u)

/** \brief  Mask for Ifx_FLASH_COMM2_Bits.STATUS */
#define IFX_FLASH_COMM2_STATUS_MSK (0xffu)

/** \brief  Offset for Ifx_FLASH_COMM2_Bits.STATUS */
#define IFX_FLASH_COMM2_STATUS_OFF (0u)

/** \brief  Length for Ifx_FLASH_ECCRD_Bits.ECCORDIS */
#define IFX_FLASH_ECCRD_ECCORDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ECCRD_Bits.ECCORDIS */
#define IFX_FLASH_ECCRD_ECCORDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ECCRD_Bits.ECCORDIS */
#define IFX_FLASH_ECCRD_ECCORDIS_OFF (31u)

/** \brief  Length for Ifx_FLASH_ECCRD_Bits.EDCERRINJ */
#define IFX_FLASH_ECCRD_EDCERRINJ_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ECCRD_Bits.EDCERRINJ */
#define IFX_FLASH_ECCRD_EDCERRINJ_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ECCRD_Bits.EDCERRINJ */
#define IFX_FLASH_ECCRD_EDCERRINJ_OFF (30u)

/** \brief  Length for Ifx_FLASH_ECCRD_Bits.RCODE */
#define IFX_FLASH_ECCRD_RCODE_LEN (22u)

/** \brief  Mask for Ifx_FLASH_ECCRD_Bits.RCODE */
#define IFX_FLASH_ECCRD_RCODE_MSK (0x3fffffu)

/** \brief  Offset for Ifx_FLASH_ECCRD_Bits.RCODE */
#define IFX_FLASH_ECCRD_RCODE_OFF (0u)

/** \brief  Length for Ifx_FLASH_ECCRP_Bits.ECCORDIS */
#define IFX_FLASH_ECCRP_ECCORDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ECCRP_Bits.ECCORDIS */
#define IFX_FLASH_ECCRP_ECCORDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ECCRP_Bits.ECCORDIS */
#define IFX_FLASH_ECCRP_ECCORDIS_OFF (31u)

/** \brief  Length for Ifx_FLASH_ECCRP_Bits.EDCERRINJ */
#define IFX_FLASH_ECCRP_EDCERRINJ_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ECCRP_Bits.EDCERRINJ */
#define IFX_FLASH_ECCRP_EDCERRINJ_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ECCRP_Bits.EDCERRINJ */
#define IFX_FLASH_ECCRP_EDCERRINJ_OFF (30u)

/** \brief  Length for Ifx_FLASH_ECCRP_Bits.RCODE */
#define IFX_FLASH_ECCRP_RCODE_LEN (22u)

/** \brief  Mask for Ifx_FLASH_ECCRP_Bits.RCODE */
#define IFX_FLASH_ECCRP_RCODE_MSK (0x3fffffu)

/** \brief  Offset for Ifx_FLASH_ECCRP_Bits.RCODE */
#define IFX_FLASH_ECCRP_RCODE_OFF (0u)

/** \brief  Length for Ifx_FLASH_ECCW_Bits.DECENCDIS */
#define IFX_FLASH_ECCW_DECENCDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ECCW_Bits.DECENCDIS */
#define IFX_FLASH_ECCW_DECENCDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ECCW_Bits.DECENCDIS */
#define IFX_FLASH_ECCW_DECENCDIS_OFF (30u)

/** \brief  Length for Ifx_FLASH_ECCW_Bits.PECENCDIS */
#define IFX_FLASH_ECCW_PECENCDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_ECCW_Bits.PECENCDIS */
#define IFX_FLASH_ECCW_PECENCDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_ECCW_Bits.PECENCDIS */
#define IFX_FLASH_ECCW_PECENCDIS_OFF (31u)

/** \brief  Length for Ifx_FLASH_ECCW_Bits.WCODE */
#define IFX_FLASH_ECCW_WCODE_LEN (22u)

/** \brief  Mask for Ifx_FLASH_ECCW_Bits.WCODE */
#define IFX_FLASH_ECCW_WCODE_MSK (0x3fffffu)

/** \brief  Offset for Ifx_FLASH_ECCW_Bits.WCODE */
#define IFX_FLASH_ECCW_WCODE_OFF (0u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.EOBM */
#define IFX_FLASH_FCON_EOBM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.EOBM */
#define IFX_FLASH_FCON_EOBM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.EOBM */
#define IFX_FLASH_FCON_EOBM_OFF (31u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.ESLDIS */
#define IFX_FLASH_FCON_ESLDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.ESLDIS */
#define IFX_FLASH_FCON_ESLDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.ESLDIS */
#define IFX_FLASH_FCON_ESLDIS_OFF (16u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.IDLE */
#define IFX_FLASH_FCON_IDLE_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.IDLE */
#define IFX_FLASH_FCON_IDLE_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.IDLE */
#define IFX_FLASH_FCON_IDLE_OFF (15u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.NSAFECC */
#define IFX_FLASH_FCON_NSAFECC_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.NSAFECC */
#define IFX_FLASH_FCON_NSAFECC_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.NSAFECC */
#define IFX_FLASH_FCON_NSAFECC_OFF (18u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.PR5V */
#define IFX_FLASH_FCON_PR5V_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.PR5V */
#define IFX_FLASH_FCON_PR5V_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.PR5V */
#define IFX_FLASH_FCON_PR5V_OFF (30u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.PROERM */
#define IFX_FLASH_FCON_PROERM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.PROERM */
#define IFX_FLASH_FCON_PROERM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.PROERM */
#define IFX_FLASH_FCON_PROERM_OFF (26u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.RES21 */
#define IFX_FLASH_FCON_RES21_LEN (2u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.RES21 */
#define IFX_FLASH_FCON_RES21_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.RES21 */
#define IFX_FLASH_FCON_RES21_OFF (20u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.RES23 */
#define IFX_FLASH_FCON_RES23_LEN (2u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.RES23 */
#define IFX_FLASH_FCON_RES23_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.RES23 */
#define IFX_FLASH_FCON_RES23_OFF (22u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.SLEEP */
#define IFX_FLASH_FCON_SLEEP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.SLEEP */
#define IFX_FLASH_FCON_SLEEP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.SLEEP */
#define IFX_FLASH_FCON_SLEEP_OFF (17u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.SQERM */
#define IFX_FLASH_FCON_SQERM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.SQERM */
#define IFX_FLASH_FCON_SQERM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.SQERM */
#define IFX_FLASH_FCON_SQERM_OFF (25u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.STALL */
#define IFX_FLASH_FCON_STALL_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.STALL */
#define IFX_FLASH_FCON_STALL_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.STALL */
#define IFX_FLASH_FCON_STALL_OFF (19u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.VOPERM */
#define IFX_FLASH_FCON_VOPERM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.VOPERM */
#define IFX_FLASH_FCON_VOPERM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.VOPERM */
#define IFX_FLASH_FCON_VOPERM_OFF (24u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.WSDFLASH */
#define IFX_FLASH_FCON_WSDFLASH_LEN (6u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.WSDFLASH */
#define IFX_FLASH_FCON_WSDFLASH_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.WSDFLASH */
#define IFX_FLASH_FCON_WSDFLASH_OFF (6u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.WSECDF */
#define IFX_FLASH_FCON_WSECDF_LEN (3u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.WSECDF */
#define IFX_FLASH_FCON_WSECDF_MSK (0x7u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.WSECDF */
#define IFX_FLASH_FCON_WSECDF_OFF (12u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.WSECPF */
#define IFX_FLASH_FCON_WSECPF_LEN (2u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.WSECPF */
#define IFX_FLASH_FCON_WSECPF_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.WSECPF */
#define IFX_FLASH_FCON_WSECPF_OFF (4u)

/** \brief  Length for Ifx_FLASH_FCON_Bits.WSPFLASH */
#define IFX_FLASH_FCON_WSPFLASH_LEN (4u)

/** \brief  Mask for Ifx_FLASH_FCON_Bits.WSPFLASH */
#define IFX_FLASH_FCON_WSPFLASH_MSK (0xfu)

/** \brief  Offset for Ifx_FLASH_FCON_Bits.WSPFLASH */
#define IFX_FLASH_FCON_WSPFLASH_OFF (0u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.DCFP */
#define IFX_FLASH_FPRO_DCFP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.DCFP */
#define IFX_FLASH_FPRO_DCFP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.DCFP */
#define IFX_FLASH_FPRO_DCFP_OFF (16u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.DDFD */
#define IFX_FLASH_FPRO_DDFD_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.DDFD */
#define IFX_FLASH_FPRO_DDFD_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.DDFD */
#define IFX_FLASH_FPRO_DDFD_OFF (20u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.DDFP */
#define IFX_FLASH_FPRO_DDFP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.DDFP */
#define IFX_FLASH_FPRO_DDFP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.DDFP */
#define IFX_FLASH_FPRO_DDFP_OFF (17u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.DDFPX */
#define IFX_FLASH_FPRO_DDFPX_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.DDFPX */
#define IFX_FLASH_FPRO_DDFPX_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.DDFPX */
#define IFX_FLASH_FPRO_DDFPX_OFF (18u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.ENPE */
#define IFX_FLASH_FPRO_ENPE_LEN (2u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.ENPE */
#define IFX_FLASH_FPRO_ENPE_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.ENPE */
#define IFX_FLASH_FPRO_ENPE_OFF (22u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PRODISD */
#define IFX_FLASH_FPRO_PRODISD_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PRODISD */
#define IFX_FLASH_FPRO_PRODISD_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PRODISD */
#define IFX_FLASH_FPRO_PRODISD_OFF (3u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PRODISDBG */
#define IFX_FLASH_FPRO_PRODISDBG_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PRODISDBG */
#define IFX_FLASH_FPRO_PRODISDBG_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PRODISDBG */
#define IFX_FLASH_FPRO_PRODISDBG_OFF (9u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PRODISP */
#define IFX_FLASH_FPRO_PRODISP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PRODISP */
#define IFX_FLASH_FPRO_PRODISP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PRODISP */
#define IFX_FLASH_FPRO_PRODISP_OFF (1u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PROIND */
#define IFX_FLASH_FPRO_PROIND_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PROIND */
#define IFX_FLASH_FPRO_PROIND_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PROIND */
#define IFX_FLASH_FPRO_PROIND_OFF (2u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PROINDBG */
#define IFX_FLASH_FPRO_PROINDBG_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PROINDBG */
#define IFX_FLASH_FPRO_PROINDBG_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PROINDBG */
#define IFX_FLASH_FPRO_PROINDBG_OFF (8u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PROINHSM */
#define IFX_FLASH_FPRO_PROINHSM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PROINHSM */
#define IFX_FLASH_FPRO_PROINHSM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PROINHSM */
#define IFX_FLASH_FPRO_PROINHSM_OFF (10u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PROINHSMCOTP */
#define IFX_FLASH_FPRO_PROINHSMCOTP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PROINHSMCOTP */
#define IFX_FLASH_FPRO_PROINHSMCOTP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PROINHSMCOTP */
#define IFX_FLASH_FPRO_PROINHSMCOTP_OFF (4u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PROINOTP */
#define IFX_FLASH_FPRO_PROINOTP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PROINOTP */
#define IFX_FLASH_FPRO_PROINOTP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PROINOTP */
#define IFX_FLASH_FPRO_PROINOTP_OFF (6u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.PROINP */
#define IFX_FLASH_FPRO_PROINP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.PROINP */
#define IFX_FLASH_FPRO_PROINP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.PROINP */
#define IFX_FLASH_FPRO_PROINP_OFF (0u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.RES5 */
#define IFX_FLASH_FPRO_RES5_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.RES5 */
#define IFX_FLASH_FPRO_RES5_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.RES5 */
#define IFX_FLASH_FPRO_RES5_OFF (5u)

/** \brief  Length for Ifx_FLASH_FPRO_Bits.RES7 */
#define IFX_FLASH_FPRO_RES7_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FPRO_Bits.RES7 */
#define IFX_FLASH_FPRO_RES7_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FPRO_Bits.RES7 */
#define IFX_FLASH_FPRO_RES7_OFF (7u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.D0BUSY */
#define IFX_FLASH_FSR_D0BUSY_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.D0BUSY */
#define IFX_FLASH_FSR_D0BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.D0BUSY */
#define IFX_FLASH_FSR_D0BUSY_OFF (1u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.DFDBER */
#define IFX_FLASH_FSR_DFDBER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.DFDBER */
#define IFX_FLASH_FSR_DFDBER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.DFDBER */
#define IFX_FLASH_FSR_DFDBER_OFF (19u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.DFMBER */
#define IFX_FLASH_FSR_DFMBER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.DFMBER */
#define IFX_FLASH_FSR_DFMBER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.DFMBER */
#define IFX_FLASH_FSR_DFMBER_OFF (21u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.DFPAGE */
#define IFX_FLASH_FSR_DFPAGE_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.DFPAGE */
#define IFX_FLASH_FSR_DFPAGE_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.DFPAGE */
#define IFX_FLASH_FSR_DFPAGE_OFF (10u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.DFSBER */
#define IFX_FLASH_FSR_DFSBER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.DFSBER */
#define IFX_FLASH_FSR_DFSBER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.DFSBER */
#define IFX_FLASH_FSR_DFSBER_OFF (18u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.DFTBER */
#define IFX_FLASH_FSR_DFTBER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.DFTBER */
#define IFX_FLASH_FSR_DFTBER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.DFTBER */
#define IFX_FLASH_FSR_DFTBER_OFF (20u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.ERASE */
#define IFX_FLASH_FSR_ERASE_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.ERASE */
#define IFX_FLASH_FSR_ERASE_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.ERASE */
#define IFX_FLASH_FSR_ERASE_OFF (8u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.EVER */
#define IFX_FLASH_FSR_EVER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.EVER */
#define IFX_FLASH_FSR_EVER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.EVER */
#define IFX_FLASH_FSR_EVER_OFF (26u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.FABUSY */
#define IFX_FLASH_FSR_FABUSY_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.FABUSY */
#define IFX_FLASH_FSR_FABUSY_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.FABUSY */
#define IFX_FLASH_FSR_FABUSY_OFF (0u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.OPER */
#define IFX_FLASH_FSR_OPER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.OPER */
#define IFX_FLASH_FSR_OPER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.OPER */
#define IFX_FLASH_FSR_OPER_OFF (11u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.ORIER */
#define IFX_FLASH_FSR_ORIER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.ORIER */
#define IFX_FLASH_FSR_ORIER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.ORIER */
#define IFX_FLASH_FSR_ORIER_OFF (30u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.P0BUSY */
#define IFX_FLASH_FSR_P0BUSY_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.P0BUSY */
#define IFX_FLASH_FSR_P0BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.P0BUSY */
#define IFX_FLASH_FSR_P0BUSY_OFF (3u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.P1BUSY */
#define IFX_FLASH_FSR_P1BUSY_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.P1BUSY */
#define IFX_FLASH_FSR_P1BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.P1BUSY */
#define IFX_FLASH_FSR_P1BUSY_OFF (4u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.PFDBER */
#define IFX_FLASH_FSR_PFDBER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.PFDBER */
#define IFX_FLASH_FSR_PFDBER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.PFDBER */
#define IFX_FLASH_FSR_PFDBER_OFF (15u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.PFMBER */
#define IFX_FLASH_FSR_PFMBER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.PFMBER */
#define IFX_FLASH_FSR_PFMBER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.PFMBER */
#define IFX_FLASH_FSR_PFMBER_OFF (16u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.PFPAGE */
#define IFX_FLASH_FSR_PFPAGE_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.PFPAGE */
#define IFX_FLASH_FSR_PFPAGE_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.PFPAGE */
#define IFX_FLASH_FSR_PFPAGE_OFF (9u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.PFSBER */
#define IFX_FLASH_FSR_PFSBER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.PFSBER */
#define IFX_FLASH_FSR_PFSBER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.PFSBER */
#define IFX_FLASH_FSR_PFSBER_OFF (14u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.PROER */
#define IFX_FLASH_FSR_PROER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.PROER */
#define IFX_FLASH_FSR_PROER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.PROER */
#define IFX_FLASH_FSR_PROER_OFF (13u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.PROG */
#define IFX_FLASH_FSR_PROG_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.PROG */
#define IFX_FLASH_FSR_PROG_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.PROG */
#define IFX_FLASH_FSR_PROG_OFF (7u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.PVER */
#define IFX_FLASH_FSR_PVER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.PVER */
#define IFX_FLASH_FSR_PVER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.PVER */
#define IFX_FLASH_FSR_PVER_OFF (25u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.RES17 */
#define IFX_FLASH_FSR_RES17_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.RES17 */
#define IFX_FLASH_FSR_RES17_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.RES17 */
#define IFX_FLASH_FSR_RES17_OFF (17u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.RES1 */
#define IFX_FLASH_FSR_RES1_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.RES1 */
#define IFX_FLASH_FSR_RES1_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.RES1 */
#define IFX_FLASH_FSR_RES1_OFF (2u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.RES5 */
#define IFX_FLASH_FSR_RES5_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.RES5 */
#define IFX_FLASH_FSR_RES5_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.RES5 */
#define IFX_FLASH_FSR_RES5_OFF (5u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.RES6 */
#define IFX_FLASH_FSR_RES6_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.RES6 */
#define IFX_FLASH_FSR_RES6_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.RES6 */
#define IFX_FLASH_FSR_RES6_OFF (6u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.SLM */
#define IFX_FLASH_FSR_SLM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.SLM */
#define IFX_FLASH_FSR_SLM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.SLM */
#define IFX_FLASH_FSR_SLM_OFF (28u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.SPND */
#define IFX_FLASH_FSR_SPND_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.SPND */
#define IFX_FLASH_FSR_SPND_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.SPND */
#define IFX_FLASH_FSR_SPND_OFF (27u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.SQER */
#define IFX_FLASH_FSR_SQER_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.SQER */
#define IFX_FLASH_FSR_SQER_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.SQER */
#define IFX_FLASH_FSR_SQER_OFF (12u)

/** \brief  Length for Ifx_FLASH_FSR_Bits.SRIADDERR */
#define IFX_FLASH_FSR_SRIADDERR_LEN (1u)

/** \brief  Mask for Ifx_FLASH_FSR_Bits.SRIADDERR */
#define IFX_FLASH_FSR_SRIADDERR_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_FSR_Bits.SRIADDERR */
#define IFX_FLASH_FSR_SRIADDERR_OFF (22u)

/** \brief  Length for Ifx_FLASH_ID_Bits.MODNUMBER */
#define IFX_FLASH_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_FLASH_ID_Bits.MODNUMBER */
#define IFX_FLASH_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_FLASH_ID_Bits.MODNUMBER */
#define IFX_FLASH_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_FLASH_ID_Bits.MODREV */
#define IFX_FLASH_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_FLASH_ID_Bits.MODREV */
#define IFX_FLASH_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_FLASH_ID_Bits.MODREV */
#define IFX_FLASH_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_FLASH_ID_Bits.MODTYPE */
#define IFX_FLASH_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_FLASH_ID_Bits.MODTYPE */
#define IFX_FLASH_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_FLASH_ID_Bits.MODTYPE */
#define IFX_FLASH_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_FLASH_MARD_Bits.HMARGIN */
#define IFX_FLASH_MARD_HMARGIN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARD_Bits.HMARGIN */
#define IFX_FLASH_MARD_HMARGIN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARD_Bits.HMARGIN */
#define IFX_FLASH_MARD_HMARGIN_OFF (0u)

/** \brief  Length for Ifx_FLASH_MARD_Bits.SELD0 */
#define IFX_FLASH_MARD_SELD0_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARD_Bits.SELD0 */
#define IFX_FLASH_MARD_SELD0_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARD_Bits.SELD0 */
#define IFX_FLASH_MARD_SELD0_OFF (1u)

/** \brief  Length for Ifx_FLASH_MARD_Bits.SPND */
#define IFX_FLASH_MARD_SPND_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARD_Bits.SPND */
#define IFX_FLASH_MARD_SPND_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARD_Bits.SPND */
#define IFX_FLASH_MARD_SPND_OFF (3u)

/** \brief  Length for Ifx_FLASH_MARD_Bits.SPNDERR */
#define IFX_FLASH_MARD_SPNDERR_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARD_Bits.SPNDERR */
#define IFX_FLASH_MARD_SPNDERR_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARD_Bits.SPNDERR */
#define IFX_FLASH_MARD_SPNDERR_OFF (4u)

/** \brief  Length for Ifx_FLASH_MARD_Bits.TRAPDIS */
#define IFX_FLASH_MARD_TRAPDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARD_Bits.TRAPDIS */
#define IFX_FLASH_MARD_TRAPDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARD_Bits.TRAPDIS */
#define IFX_FLASH_MARD_TRAPDIS_OFF (15u)

/** \brief  Length for Ifx_FLASH_MARP_Bits.RES2 */
#define IFX_FLASH_MARP_RES2_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARP_Bits.RES2 */
#define IFX_FLASH_MARP_RES2_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARP_Bits.RES2 */
#define IFX_FLASH_MARP_RES2_OFF (2u)

/** \brief  Length for Ifx_FLASH_MARP_Bits.RES3 */
#define IFX_FLASH_MARP_RES3_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARP_Bits.RES3 */
#define IFX_FLASH_MARP_RES3_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARP_Bits.RES3 */
#define IFX_FLASH_MARP_RES3_OFF (3u)

/** \brief  Length for Ifx_FLASH_MARP_Bits.SELP0 */
#define IFX_FLASH_MARP_SELP0_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARP_Bits.SELP0 */
#define IFX_FLASH_MARP_SELP0_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARP_Bits.SELP0 */
#define IFX_FLASH_MARP_SELP0_OFF (0u)

/** \brief  Length for Ifx_FLASH_MARP_Bits.SELP1 */
#define IFX_FLASH_MARP_SELP1_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARP_Bits.SELP1 */
#define IFX_FLASH_MARP_SELP1_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARP_Bits.SELP1 */
#define IFX_FLASH_MARP_SELP1_OFF (1u)

/** \brief  Length for Ifx_FLASH_MARP_Bits.TRAPDIS */
#define IFX_FLASH_MARP_TRAPDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_MARP_Bits.TRAPDIS */
#define IFX_FLASH_MARP_TRAPDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_MARP_Bits.TRAPDIS */
#define IFX_FLASH_MARP_TRAPDIS_OFF (15u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.APREN */
#define IFX_FLASH_PROCOND_APREN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.APREN */
#define IFX_FLASH_PROCOND_APREN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.APREN */
#define IFX_FLASH_PROCOND_APREN_OFF (11u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.CAP0EN */
#define IFX_FLASH_PROCOND_CAP0EN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.CAP0EN */
#define IFX_FLASH_PROCOND_CAP0EN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.CAP0EN */
#define IFX_FLASH_PROCOND_CAP0EN_OFF (12u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.CAP1EN */
#define IFX_FLASH_PROCOND_CAP1EN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.CAP1EN */
#define IFX_FLASH_PROCOND_CAP1EN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.CAP1EN */
#define IFX_FLASH_PROCOND_CAP1EN_OFF (13u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.CAP2EN */
#define IFX_FLASH_PROCOND_CAP2EN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.CAP2EN */
#define IFX_FLASH_PROCOND_CAP2EN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.CAP2EN */
#define IFX_FLASH_PROCOND_CAP2EN_OFF (14u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.CAP3EN */
#define IFX_FLASH_PROCOND_CAP3EN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.CAP3EN */
#define IFX_FLASH_PROCOND_CAP3EN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.CAP3EN */
#define IFX_FLASH_PROCOND_CAP3EN_OFF (15u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.ESR0CNT */
#define IFX_FLASH_PROCOND_ESR0CNT_LEN (12u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.ESR0CNT */
#define IFX_FLASH_PROCOND_ESR0CNT_MSK (0xfffu)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.ESR0CNT */
#define IFX_FLASH_PROCOND_ESR0CNT_OFF (16u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.L */
#define IFX_FLASH_PROCOND_L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.L */
#define IFX_FLASH_PROCOND_L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.L */
#define IFX_FLASH_PROCOND_L_OFF (0u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.MODE */
#define IFX_FLASH_PROCOND_MODE_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.MODE */
#define IFX_FLASH_PROCOND_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.MODE */
#define IFX_FLASH_PROCOND_MODE_OFF (9u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.NSAFECC */
#define IFX_FLASH_PROCOND_NSAFECC_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.NSAFECC */
#define IFX_FLASH_PROCOND_NSAFECC_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.NSAFECC */
#define IFX_FLASH_PROCOND_NSAFECC_OFF (1u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.OSCCFG */
#define IFX_FLASH_PROCOND_OSCCFG_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.OSCCFG */
#define IFX_FLASH_PROCOND_OSCCFG_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.OSCCFG */
#define IFX_FLASH_PROCOND_OSCCFG_OFF (8u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.RAMIN */
#define IFX_FLASH_PROCOND_RAMIN_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.RAMIN */
#define IFX_FLASH_PROCOND_RAMIN_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.RAMIN */
#define IFX_FLASH_PROCOND_RAMIN_OFF (2u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.RAMINSEL */
#define IFX_FLASH_PROCOND_RAMINSEL_LEN (4u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.RAMINSEL */
#define IFX_FLASH_PROCOND_RAMINSEL_MSK (0xfu)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.RAMINSEL */
#define IFX_FLASH_PROCOND_RAMINSEL_OFF (4u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.RES29 */
#define IFX_FLASH_PROCOND_RES29_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.RES29 */
#define IFX_FLASH_PROCOND_RES29_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.RES29 */
#define IFX_FLASH_PROCOND_RES29_OFF (28u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.RES30 */
#define IFX_FLASH_PROCOND_RES30_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.RES30 */
#define IFX_FLASH_PROCOND_RES30_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.RES30 */
#define IFX_FLASH_PROCOND_RES30_OFF (30u)

/** \brief  Length for Ifx_FLASH_PROCOND_Bits.RPRO */
#define IFX_FLASH_PROCOND_RPRO_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCOND_Bits.RPRO */
#define IFX_FLASH_PROCOND_RPRO_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCOND_Bits.RPRO */
#define IFX_FLASH_PROCOND_RPRO_OFF (31u)

/** \brief  Length for Ifx_FLASH_PROCONDBG_Bits.DBGIFLCK */
#define IFX_FLASH_PROCONDBG_DBGIFLCK_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONDBG_Bits.DBGIFLCK */
#define IFX_FLASH_PROCONDBG_DBGIFLCK_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONDBG_Bits.DBGIFLCK */
#define IFX_FLASH_PROCONDBG_DBGIFLCK_OFF (1u)

/** \brief  Length for Ifx_FLASH_PROCONDBG_Bits.EDM */
#define IFX_FLASH_PROCONDBG_EDM_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCONDBG_Bits.EDM */
#define IFX_FLASH_PROCONDBG_EDM_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCONDBG_Bits.EDM */
#define IFX_FLASH_PROCONDBG_EDM_OFF (2u)

/** \brief  Length for Ifx_FLASH_PROCONDBG_Bits.OCDSDIS */
#define IFX_FLASH_PROCONDBG_OCDSDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONDBG_Bits.OCDSDIS */
#define IFX_FLASH_PROCONDBG_OCDSDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONDBG_Bits.OCDSDIS */
#define IFX_FLASH_PROCONDBG_OCDSDIS_OFF (0u)

/** \brief  Length for Ifx_FLASH_PROCONHSM_Bits.DBGIFLCK */
#define IFX_FLASH_PROCONHSM_DBGIFLCK_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSM_Bits.DBGIFLCK */
#define IFX_FLASH_PROCONHSM_DBGIFLCK_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSM_Bits.DBGIFLCK */
#define IFX_FLASH_PROCONHSM_DBGIFLCK_OFF (1u)

/** \brief  Length for Ifx_FLASH_PROCONHSM_Bits.HSMDBGDIS */
#define IFX_FLASH_PROCONHSM_HSMDBGDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSM_Bits.HSMDBGDIS */
#define IFX_FLASH_PROCONHSM_HSMDBGDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSM_Bits.HSMDBGDIS */
#define IFX_FLASH_PROCONHSM_HSMDBGDIS_OFF (0u)

/** \brief  Length for Ifx_FLASH_PROCONHSM_Bits.HSMTSTDIS */
#define IFX_FLASH_PROCONHSM_HSMTSTDIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSM_Bits.HSMTSTDIS */
#define IFX_FLASH_PROCONHSM_HSMTSTDIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSM_Bits.HSMTSTDIS */
#define IFX_FLASH_PROCONHSM_HSMTSTDIS_OFF (3u)

/** \brief  Length for Ifx_FLASH_PROCONHSM_Bits.RES15 */
#define IFX_FLASH_PROCONHSM_RES15_LEN (12u)

/** \brief  Mask for Ifx_FLASH_PROCONHSM_Bits.RES15 */
#define IFX_FLASH_PROCONHSM_RES15_MSK (0xfffu)

/** \brief  Offset for Ifx_FLASH_PROCONHSM_Bits.RES15 */
#define IFX_FLASH_PROCONHSM_RES15_OFF (4u)

/** \brief  Length for Ifx_FLASH_PROCONHSM_Bits.TSTIFLCK */
#define IFX_FLASH_PROCONHSM_TSTIFLCK_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSM_Bits.TSTIFLCK */
#define IFX_FLASH_PROCONHSM_TSTIFLCK_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSM_Bits.TSTIFLCK */
#define IFX_FLASH_PROCONHSM_TSTIFLCK_OFF (2u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.BLKFLAN */
#define IFX_FLASH_PROCONHSMCOTP_BLKFLAN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.BLKFLAN */
#define IFX_FLASH_PROCONHSMCOTP_BLKFLAN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.BLKFLAN */
#define IFX_FLASH_PROCONHSMCOTP_BLKFLAN_OFF (13u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.DESTDBG */
#define IFX_FLASH_PROCONHSMCOTP_DESTDBG_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.DESTDBG */
#define IFX_FLASH_PROCONHSMCOTP_DESTDBG_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.DESTDBG */
#define IFX_FLASH_PROCONHSMCOTP_DESTDBG_OFF (11u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM16X */
#define IFX_FLASH_PROCONHSMCOTP_HSM16X_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM16X */
#define IFX_FLASH_PROCONHSMCOTP_HSM16X_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM16X */
#define IFX_FLASH_PROCONHSMCOTP_HSM16X_OFF (4u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM17X */
#define IFX_FLASH_PROCONHSMCOTP_HSM17X_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM17X */
#define IFX_FLASH_PROCONHSMCOTP_HSM17X_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM17X */
#define IFX_FLASH_PROCONHSMCOTP_HSM17X_OFF (5u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM6X */
#define IFX_FLASH_PROCONHSMCOTP_HSM6X_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM6X */
#define IFX_FLASH_PROCONHSMCOTP_HSM6X_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.HSM6X */
#define IFX_FLASH_PROCONHSMCOTP_HSM6X_OFF (3u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMBOOTEN */
#define IFX_FLASH_PROCONHSMCOTP_HSMBOOTEN_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMBOOTEN */
#define IFX_FLASH_PROCONHSMCOTP_HSMBOOTEN_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMBOOTEN */
#define IFX_FLASH_PROCONHSMCOTP_HSMBOOTEN_OFF (0u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMDX */
#define IFX_FLASH_PROCONHSMCOTP_HSMDX_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMDX */
#define IFX_FLASH_PROCONHSMCOTP_HSMDX_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMDX */
#define IFX_FLASH_PROCONHSMCOTP_HSMDX_OFF (2u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMENPINS */
#define IFX_FLASH_PROCONHSMCOTP_HSMENPINS_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMENPINS */
#define IFX_FLASH_PROCONHSMCOTP_HSMENPINS_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMENPINS */
#define IFX_FLASH_PROCONHSMCOTP_HSMENPINS_OFF (7u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMENRES */
#define IFX_FLASH_PROCONHSMCOTP_HSMENRES_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMENRES */
#define IFX_FLASH_PROCONHSMCOTP_HSMENRES_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.HSMENRES */
#define IFX_FLASH_PROCONHSMCOTP_HSMENRES_OFF (9u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.S16ROM */
#define IFX_FLASH_PROCONHSMCOTP_S16ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.S16ROM */
#define IFX_FLASH_PROCONHSMCOTP_S16ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.S16ROM */
#define IFX_FLASH_PROCONHSMCOTP_S16ROM_OFF (16u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.S17ROM */
#define IFX_FLASH_PROCONHSMCOTP_S17ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.S17ROM */
#define IFX_FLASH_PROCONHSMCOTP_S17ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.S17ROM */
#define IFX_FLASH_PROCONHSMCOTP_S17ROM_OFF (17u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.S6ROM */
#define IFX_FLASH_PROCONHSMCOTP_S6ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.S6ROM */
#define IFX_FLASH_PROCONHSMCOTP_S6ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.S6ROM */
#define IFX_FLASH_PROCONHSMCOTP_S6ROM_OFF (6u)

/** \brief  Length for Ifx_FLASH_PROCONHSMCOTP_Bits.SSWWAIT */
#define IFX_FLASH_PROCONHSMCOTP_SSWWAIT_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONHSMCOTP_Bits.SSWWAIT */
#define IFX_FLASH_PROCONHSMCOTP_SSWWAIT_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONHSMCOTP_Bits.SSWWAIT */
#define IFX_FLASH_PROCONHSMCOTP_SSWWAIT_OFF (1u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.BML */
#define IFX_FLASH_PROCONOTP_BML_LEN (2u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.BML */
#define IFX_FLASH_PROCONOTP_BML_MSK (0x3u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.BML */
#define IFX_FLASH_PROCONOTP_BML_OFF (29u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S0ROM */
#define IFX_FLASH_PROCONOTP_S0ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S0ROM */
#define IFX_FLASH_PROCONOTP_S0ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S0ROM */
#define IFX_FLASH_PROCONOTP_S0ROM_OFF (0u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S10ROM */
#define IFX_FLASH_PROCONOTP_S10ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S10ROM */
#define IFX_FLASH_PROCONOTP_S10ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S10ROM */
#define IFX_FLASH_PROCONOTP_S10ROM_OFF (10u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S11ROM */
#define IFX_FLASH_PROCONOTP_S11ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S11ROM */
#define IFX_FLASH_PROCONOTP_S11ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S11ROM */
#define IFX_FLASH_PROCONOTP_S11ROM_OFF (11u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S12ROM */
#define IFX_FLASH_PROCONOTP_S12ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S12ROM */
#define IFX_FLASH_PROCONOTP_S12ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S12ROM */
#define IFX_FLASH_PROCONOTP_S12ROM_OFF (12u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S13ROM */
#define IFX_FLASH_PROCONOTP_S13ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S13ROM */
#define IFX_FLASH_PROCONOTP_S13ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S13ROM */
#define IFX_FLASH_PROCONOTP_S13ROM_OFF (13u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S14ROM */
#define IFX_FLASH_PROCONOTP_S14ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S14ROM */
#define IFX_FLASH_PROCONOTP_S14ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S14ROM */
#define IFX_FLASH_PROCONOTP_S14ROM_OFF (14u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S15ROM */
#define IFX_FLASH_PROCONOTP_S15ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S15ROM */
#define IFX_FLASH_PROCONOTP_S15ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S15ROM */
#define IFX_FLASH_PROCONOTP_S15ROM_OFF (15u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S16ROM */
#define IFX_FLASH_PROCONOTP_S16ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S16ROM */
#define IFX_FLASH_PROCONOTP_S16ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S16ROM */
#define IFX_FLASH_PROCONOTP_S16ROM_OFF (16u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S17ROM */
#define IFX_FLASH_PROCONOTP_S17ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S17ROM */
#define IFX_FLASH_PROCONOTP_S17ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S17ROM */
#define IFX_FLASH_PROCONOTP_S17ROM_OFF (17u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S18ROM */
#define IFX_FLASH_PROCONOTP_S18ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S18ROM */
#define IFX_FLASH_PROCONOTP_S18ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S18ROM */
#define IFX_FLASH_PROCONOTP_S18ROM_OFF (18u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S19ROM */
#define IFX_FLASH_PROCONOTP_S19ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S19ROM */
#define IFX_FLASH_PROCONOTP_S19ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S19ROM */
#define IFX_FLASH_PROCONOTP_S19ROM_OFF (19u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S1ROM */
#define IFX_FLASH_PROCONOTP_S1ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S1ROM */
#define IFX_FLASH_PROCONOTP_S1ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S1ROM */
#define IFX_FLASH_PROCONOTP_S1ROM_OFF (1u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S20ROM */
#define IFX_FLASH_PROCONOTP_S20ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S20ROM */
#define IFX_FLASH_PROCONOTP_S20ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S20ROM */
#define IFX_FLASH_PROCONOTP_S20ROM_OFF (20u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S21ROM */
#define IFX_FLASH_PROCONOTP_S21ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S21ROM */
#define IFX_FLASH_PROCONOTP_S21ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S21ROM */
#define IFX_FLASH_PROCONOTP_S21ROM_OFF (21u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S22ROM */
#define IFX_FLASH_PROCONOTP_S22ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S22ROM */
#define IFX_FLASH_PROCONOTP_S22ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S22ROM */
#define IFX_FLASH_PROCONOTP_S22ROM_OFF (22u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S23ROM */
#define IFX_FLASH_PROCONOTP_S23ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S23ROM */
#define IFX_FLASH_PROCONOTP_S23ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S23ROM */
#define IFX_FLASH_PROCONOTP_S23ROM_OFF (23u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S24ROM */
#define IFX_FLASH_PROCONOTP_S24ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S24ROM */
#define IFX_FLASH_PROCONOTP_S24ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S24ROM */
#define IFX_FLASH_PROCONOTP_S24ROM_OFF (24u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S25ROM */
#define IFX_FLASH_PROCONOTP_S25ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S25ROM */
#define IFX_FLASH_PROCONOTP_S25ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S25ROM */
#define IFX_FLASH_PROCONOTP_S25ROM_OFF (25u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S26ROM */
#define IFX_FLASH_PROCONOTP_S26ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S26ROM */
#define IFX_FLASH_PROCONOTP_S26ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S26ROM */
#define IFX_FLASH_PROCONOTP_S26ROM_OFF (26u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S2ROM */
#define IFX_FLASH_PROCONOTP_S2ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S2ROM */
#define IFX_FLASH_PROCONOTP_S2ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S2ROM */
#define IFX_FLASH_PROCONOTP_S2ROM_OFF (2u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S3ROM */
#define IFX_FLASH_PROCONOTP_S3ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S3ROM */
#define IFX_FLASH_PROCONOTP_S3ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S3ROM */
#define IFX_FLASH_PROCONOTP_S3ROM_OFF (3u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S4ROM */
#define IFX_FLASH_PROCONOTP_S4ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S4ROM */
#define IFX_FLASH_PROCONOTP_S4ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S4ROM */
#define IFX_FLASH_PROCONOTP_S4ROM_OFF (4u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S5ROM */
#define IFX_FLASH_PROCONOTP_S5ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S5ROM */
#define IFX_FLASH_PROCONOTP_S5ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S5ROM */
#define IFX_FLASH_PROCONOTP_S5ROM_OFF (5u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S6ROM */
#define IFX_FLASH_PROCONOTP_S6ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S6ROM */
#define IFX_FLASH_PROCONOTP_S6ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S6ROM */
#define IFX_FLASH_PROCONOTP_S6ROM_OFF (6u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S7ROM */
#define IFX_FLASH_PROCONOTP_S7ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S7ROM */
#define IFX_FLASH_PROCONOTP_S7ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S7ROM */
#define IFX_FLASH_PROCONOTP_S7ROM_OFF (7u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S8ROM */
#define IFX_FLASH_PROCONOTP_S8ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S8ROM */
#define IFX_FLASH_PROCONOTP_S8ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S8ROM */
#define IFX_FLASH_PROCONOTP_S8ROM_OFF (8u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.S9ROM */
#define IFX_FLASH_PROCONOTP_S9ROM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.S9ROM */
#define IFX_FLASH_PROCONOTP_S9ROM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.S9ROM */
#define IFX_FLASH_PROCONOTP_S9ROM_OFF (9u)

/** \brief  Length for Ifx_FLASH_PROCONOTP_Bits.TP */
#define IFX_FLASH_PROCONOTP_TP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONOTP_Bits.TP */
#define IFX_FLASH_PROCONOTP_TP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONOTP_Bits.TP */
#define IFX_FLASH_PROCONOTP_TP_OFF (31u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.RPRO */
#define IFX_FLASH_PROCONP_RPRO_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.RPRO */
#define IFX_FLASH_PROCONP_RPRO_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.RPRO */
#define IFX_FLASH_PROCONP_RPRO_OFF (31u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S0L */
#define IFX_FLASH_PROCONP_S0L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S0L */
#define IFX_FLASH_PROCONP_S0L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S0L */
#define IFX_FLASH_PROCONP_S0L_OFF (0u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S10L */
#define IFX_FLASH_PROCONP_S10L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S10L */
#define IFX_FLASH_PROCONP_S10L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S10L */
#define IFX_FLASH_PROCONP_S10L_OFF (10u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S11L */
#define IFX_FLASH_PROCONP_S11L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S11L */
#define IFX_FLASH_PROCONP_S11L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S11L */
#define IFX_FLASH_PROCONP_S11L_OFF (11u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S12L */
#define IFX_FLASH_PROCONP_S12L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S12L */
#define IFX_FLASH_PROCONP_S12L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S12L */
#define IFX_FLASH_PROCONP_S12L_OFF (12u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S13L */
#define IFX_FLASH_PROCONP_S13L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S13L */
#define IFX_FLASH_PROCONP_S13L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S13L */
#define IFX_FLASH_PROCONP_S13L_OFF (13u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S14L */
#define IFX_FLASH_PROCONP_S14L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S14L */
#define IFX_FLASH_PROCONP_S14L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S14L */
#define IFX_FLASH_PROCONP_S14L_OFF (14u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S15L */
#define IFX_FLASH_PROCONP_S15L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S15L */
#define IFX_FLASH_PROCONP_S15L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S15L */
#define IFX_FLASH_PROCONP_S15L_OFF (15u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S16L */
#define IFX_FLASH_PROCONP_S16L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S16L */
#define IFX_FLASH_PROCONP_S16L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S16L */
#define IFX_FLASH_PROCONP_S16L_OFF (16u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S17L */
#define IFX_FLASH_PROCONP_S17L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S17L */
#define IFX_FLASH_PROCONP_S17L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S17L */
#define IFX_FLASH_PROCONP_S17L_OFF (17u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S18L */
#define IFX_FLASH_PROCONP_S18L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S18L */
#define IFX_FLASH_PROCONP_S18L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S18L */
#define IFX_FLASH_PROCONP_S18L_OFF (18u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S19L */
#define IFX_FLASH_PROCONP_S19L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S19L */
#define IFX_FLASH_PROCONP_S19L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S19L */
#define IFX_FLASH_PROCONP_S19L_OFF (19u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S1L */
#define IFX_FLASH_PROCONP_S1L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S1L */
#define IFX_FLASH_PROCONP_S1L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S1L */
#define IFX_FLASH_PROCONP_S1L_OFF (1u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S20L */
#define IFX_FLASH_PROCONP_S20L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S20L */
#define IFX_FLASH_PROCONP_S20L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S20L */
#define IFX_FLASH_PROCONP_S20L_OFF (20u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S21L */
#define IFX_FLASH_PROCONP_S21L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S21L */
#define IFX_FLASH_PROCONP_S21L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S21L */
#define IFX_FLASH_PROCONP_S21L_OFF (21u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S22L */
#define IFX_FLASH_PROCONP_S22L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S22L */
#define IFX_FLASH_PROCONP_S22L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S22L */
#define IFX_FLASH_PROCONP_S22L_OFF (22u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S23L */
#define IFX_FLASH_PROCONP_S23L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S23L */
#define IFX_FLASH_PROCONP_S23L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S23L */
#define IFX_FLASH_PROCONP_S23L_OFF (23u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S24L */
#define IFX_FLASH_PROCONP_S24L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S24L */
#define IFX_FLASH_PROCONP_S24L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S24L */
#define IFX_FLASH_PROCONP_S24L_OFF (24u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S25L */
#define IFX_FLASH_PROCONP_S25L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S25L */
#define IFX_FLASH_PROCONP_S25L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S25L */
#define IFX_FLASH_PROCONP_S25L_OFF (25u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S26L */
#define IFX_FLASH_PROCONP_S26L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S26L */
#define IFX_FLASH_PROCONP_S26L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S26L */
#define IFX_FLASH_PROCONP_S26L_OFF (26u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S2L */
#define IFX_FLASH_PROCONP_S2L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S2L */
#define IFX_FLASH_PROCONP_S2L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S2L */
#define IFX_FLASH_PROCONP_S2L_OFF (2u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S3L */
#define IFX_FLASH_PROCONP_S3L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S3L */
#define IFX_FLASH_PROCONP_S3L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S3L */
#define IFX_FLASH_PROCONP_S3L_OFF (3u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S4L */
#define IFX_FLASH_PROCONP_S4L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S4L */
#define IFX_FLASH_PROCONP_S4L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S4L */
#define IFX_FLASH_PROCONP_S4L_OFF (4u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S5L */
#define IFX_FLASH_PROCONP_S5L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S5L */
#define IFX_FLASH_PROCONP_S5L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S5L */
#define IFX_FLASH_PROCONP_S5L_OFF (5u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S6L */
#define IFX_FLASH_PROCONP_S6L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S6L */
#define IFX_FLASH_PROCONP_S6L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S6L */
#define IFX_FLASH_PROCONP_S6L_OFF (6u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S7L */
#define IFX_FLASH_PROCONP_S7L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S7L */
#define IFX_FLASH_PROCONP_S7L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S7L */
#define IFX_FLASH_PROCONP_S7L_OFF (7u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S8L */
#define IFX_FLASH_PROCONP_S8L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S8L */
#define IFX_FLASH_PROCONP_S8L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S8L */
#define IFX_FLASH_PROCONP_S8L_OFF (8u)

/** \brief  Length for Ifx_FLASH_PROCONP_Bits.S9L */
#define IFX_FLASH_PROCONP_S9L_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONP_Bits.S9L */
#define IFX_FLASH_PROCONP_S9L_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONP_Bits.S9L */
#define IFX_FLASH_PROCONP_S9L_OFF (9u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.DATM */
#define IFX_FLASH_PROCONWOP_DATM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.DATM */
#define IFX_FLASH_PROCONWOP_DATM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.DATM */
#define IFX_FLASH_PROCONWOP_DATM_OFF (31u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S0WOP */
#define IFX_FLASH_PROCONWOP_S0WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S0WOP */
#define IFX_FLASH_PROCONWOP_S0WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S0WOP */
#define IFX_FLASH_PROCONWOP_S0WOP_OFF (0u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S10WOP */
#define IFX_FLASH_PROCONWOP_S10WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S10WOP */
#define IFX_FLASH_PROCONWOP_S10WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S10WOP */
#define IFX_FLASH_PROCONWOP_S10WOP_OFF (10u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S11WOP */
#define IFX_FLASH_PROCONWOP_S11WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S11WOP */
#define IFX_FLASH_PROCONWOP_S11WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S11WOP */
#define IFX_FLASH_PROCONWOP_S11WOP_OFF (11u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S12WOP */
#define IFX_FLASH_PROCONWOP_S12WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S12WOP */
#define IFX_FLASH_PROCONWOP_S12WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S12WOP */
#define IFX_FLASH_PROCONWOP_S12WOP_OFF (12u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S13WOP */
#define IFX_FLASH_PROCONWOP_S13WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S13WOP */
#define IFX_FLASH_PROCONWOP_S13WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S13WOP */
#define IFX_FLASH_PROCONWOP_S13WOP_OFF (13u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S14WOP */
#define IFX_FLASH_PROCONWOP_S14WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S14WOP */
#define IFX_FLASH_PROCONWOP_S14WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S14WOP */
#define IFX_FLASH_PROCONWOP_S14WOP_OFF (14u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S15WOP */
#define IFX_FLASH_PROCONWOP_S15WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S15WOP */
#define IFX_FLASH_PROCONWOP_S15WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S15WOP */
#define IFX_FLASH_PROCONWOP_S15WOP_OFF (15u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S16WOP */
#define IFX_FLASH_PROCONWOP_S16WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S16WOP */
#define IFX_FLASH_PROCONWOP_S16WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S16WOP */
#define IFX_FLASH_PROCONWOP_S16WOP_OFF (16u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S17WOP */
#define IFX_FLASH_PROCONWOP_S17WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S17WOP */
#define IFX_FLASH_PROCONWOP_S17WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S17WOP */
#define IFX_FLASH_PROCONWOP_S17WOP_OFF (17u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S18WOP */
#define IFX_FLASH_PROCONWOP_S18WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S18WOP */
#define IFX_FLASH_PROCONWOP_S18WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S18WOP */
#define IFX_FLASH_PROCONWOP_S18WOP_OFF (18u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S19WOP */
#define IFX_FLASH_PROCONWOP_S19WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S19WOP */
#define IFX_FLASH_PROCONWOP_S19WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S19WOP */
#define IFX_FLASH_PROCONWOP_S19WOP_OFF (19u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S1WOP */
#define IFX_FLASH_PROCONWOP_S1WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S1WOP */
#define IFX_FLASH_PROCONWOP_S1WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S1WOP */
#define IFX_FLASH_PROCONWOP_S1WOP_OFF (1u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S20WOP */
#define IFX_FLASH_PROCONWOP_S20WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S20WOP */
#define IFX_FLASH_PROCONWOP_S20WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S20WOP */
#define IFX_FLASH_PROCONWOP_S20WOP_OFF (20u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S21WOP */
#define IFX_FLASH_PROCONWOP_S21WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S21WOP */
#define IFX_FLASH_PROCONWOP_S21WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S21WOP */
#define IFX_FLASH_PROCONWOP_S21WOP_OFF (21u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S22WOP */
#define IFX_FLASH_PROCONWOP_S22WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S22WOP */
#define IFX_FLASH_PROCONWOP_S22WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S22WOP */
#define IFX_FLASH_PROCONWOP_S22WOP_OFF (22u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S23WOP */
#define IFX_FLASH_PROCONWOP_S23WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S23WOP */
#define IFX_FLASH_PROCONWOP_S23WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S23WOP */
#define IFX_FLASH_PROCONWOP_S23WOP_OFF (23u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S24WOP */
#define IFX_FLASH_PROCONWOP_S24WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S24WOP */
#define IFX_FLASH_PROCONWOP_S24WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S24WOP */
#define IFX_FLASH_PROCONWOP_S24WOP_OFF (24u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S25WOP */
#define IFX_FLASH_PROCONWOP_S25WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S25WOP */
#define IFX_FLASH_PROCONWOP_S25WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S25WOP */
#define IFX_FLASH_PROCONWOP_S25WOP_OFF (25u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S26WOP */
#define IFX_FLASH_PROCONWOP_S26WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S26WOP */
#define IFX_FLASH_PROCONWOP_S26WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S26WOP */
#define IFX_FLASH_PROCONWOP_S26WOP_OFF (26u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S2WOP */
#define IFX_FLASH_PROCONWOP_S2WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S2WOP */
#define IFX_FLASH_PROCONWOP_S2WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S2WOP */
#define IFX_FLASH_PROCONWOP_S2WOP_OFF (2u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S3WOP */
#define IFX_FLASH_PROCONWOP_S3WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S3WOP */
#define IFX_FLASH_PROCONWOP_S3WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S3WOP */
#define IFX_FLASH_PROCONWOP_S3WOP_OFF (3u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S4WOP */
#define IFX_FLASH_PROCONWOP_S4WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S4WOP */
#define IFX_FLASH_PROCONWOP_S4WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S4WOP */
#define IFX_FLASH_PROCONWOP_S4WOP_OFF (4u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S5WOP */
#define IFX_FLASH_PROCONWOP_S5WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S5WOP */
#define IFX_FLASH_PROCONWOP_S5WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S5WOP */
#define IFX_FLASH_PROCONWOP_S5WOP_OFF (5u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S6WOP */
#define IFX_FLASH_PROCONWOP_S6WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S6WOP */
#define IFX_FLASH_PROCONWOP_S6WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S6WOP */
#define IFX_FLASH_PROCONWOP_S6WOP_OFF (6u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S7WOP */
#define IFX_FLASH_PROCONWOP_S7WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S7WOP */
#define IFX_FLASH_PROCONWOP_S7WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S7WOP */
#define IFX_FLASH_PROCONWOP_S7WOP_OFF (7u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S8WOP */
#define IFX_FLASH_PROCONWOP_S8WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S8WOP */
#define IFX_FLASH_PROCONWOP_S8WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S8WOP */
#define IFX_FLASH_PROCONWOP_S8WOP_OFF (8u)

/** \brief  Length for Ifx_FLASH_PROCONWOP_Bits.S9WOP */
#define IFX_FLASH_PROCONWOP_S9WOP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_PROCONWOP_Bits.S9WOP */
#define IFX_FLASH_PROCONWOP_S9WOP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_PROCONWOP_Bits.S9WOP */
#define IFX_FLASH_PROCONWOP_S9WOP_OFF (9u)

/** \brief  Length for Ifx_FLASH_RDB_CFG0_Bits.TAG */
#define IFX_FLASH_RDB_CFG0_TAG_LEN (6u)

/** \brief  Mask for Ifx_FLASH_RDB_CFG0_Bits.TAG */
#define IFX_FLASH_RDB_CFG0_TAG_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_RDB_CFG0_Bits.TAG */
#define IFX_FLASH_RDB_CFG0_TAG_OFF (0u)

/** \brief  Length for Ifx_FLASH_RDB_CFG1_Bits.TAG */
#define IFX_FLASH_RDB_CFG1_TAG_LEN (6u)

/** \brief  Mask for Ifx_FLASH_RDB_CFG1_Bits.TAG */
#define IFX_FLASH_RDB_CFG1_TAG_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_RDB_CFG1_Bits.TAG */
#define IFX_FLASH_RDB_CFG1_TAG_OFF (0u)

/** \brief  Length for Ifx_FLASH_RDB_CFG2_Bits.TAG */
#define IFX_FLASH_RDB_CFG2_TAG_LEN (6u)

/** \brief  Mask for Ifx_FLASH_RDB_CFG2_Bits.TAG */
#define IFX_FLASH_RDB_CFG2_TAG_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_RDB_CFG2_Bits.TAG */
#define IFX_FLASH_RDB_CFG2_TAG_OFF (0u)

/** \brief  Length for Ifx_FLASH_RRAD_Bits.ADD */
#define IFX_FLASH_RRAD_ADD_LEN (29u)

/** \brief  Mask for Ifx_FLASH_RRAD_Bits.ADD */
#define IFX_FLASH_RRAD_ADD_MSK (0x1fffffffu)

/** \brief  Offset for Ifx_FLASH_RRAD_Bits.ADD */
#define IFX_FLASH_RRAD_ADD_OFF (3u)

/** \brief  Length for Ifx_FLASH_RRCT_Bits.BUSY */
#define IFX_FLASH_RRCT_BUSY_LEN (1u)

/** \brief  Mask for Ifx_FLASH_RRCT_Bits.BUSY */
#define IFX_FLASH_RRCT_BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_RRCT_Bits.BUSY */
#define IFX_FLASH_RRCT_BUSY_OFF (2u)

/** \brief  Length for Ifx_FLASH_RRCT_Bits.CNT */
#define IFX_FLASH_RRCT_CNT_LEN (16u)

/** \brief  Mask for Ifx_FLASH_RRCT_Bits.CNT */
#define IFX_FLASH_RRCT_CNT_MSK (0xffffu)

/** \brief  Offset for Ifx_FLASH_RRCT_Bits.CNT */
#define IFX_FLASH_RRCT_CNT_OFF (16u)

/** \brief  Length for Ifx_FLASH_RRCT_Bits.DONE */
#define IFX_FLASH_RRCT_DONE_LEN (1u)

/** \brief  Mask for Ifx_FLASH_RRCT_Bits.DONE */
#define IFX_FLASH_RRCT_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_RRCT_Bits.DONE */
#define IFX_FLASH_RRCT_DONE_OFF (3u)

/** \brief  Length for Ifx_FLASH_RRCT_Bits.EOBM */
#define IFX_FLASH_RRCT_EOBM_LEN (1u)

/** \brief  Mask for Ifx_FLASH_RRCT_Bits.EOBM */
#define IFX_FLASH_RRCT_EOBM_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_RRCT_Bits.EOBM */
#define IFX_FLASH_RRCT_EOBM_OFF (8u)

/** \brief  Length for Ifx_FLASH_RRCT_Bits.ERR */
#define IFX_FLASH_RRCT_ERR_LEN (1u)

/** \brief  Mask for Ifx_FLASH_RRCT_Bits.ERR */
#define IFX_FLASH_RRCT_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_RRCT_Bits.ERR */
#define IFX_FLASH_RRCT_ERR_OFF (4u)

/** \brief  Length for Ifx_FLASH_RRCT_Bits.STP */
#define IFX_FLASH_RRCT_STP_LEN (1u)

/** \brief  Mask for Ifx_FLASH_RRCT_Bits.STP */
#define IFX_FLASH_RRCT_STP_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_RRCT_Bits.STP */
#define IFX_FLASH_RRCT_STP_OFF (1u)

/** \brief  Length for Ifx_FLASH_RRCT_Bits.STRT */
#define IFX_FLASH_RRCT_STRT_LEN (1u)

/** \brief  Mask for Ifx_FLASH_RRCT_Bits.STRT */
#define IFX_FLASH_RRCT_STRT_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_RRCT_Bits.STRT */
#define IFX_FLASH_RRCT_STRT_OFF (0u)

/** \brief  Length for Ifx_FLASH_RRD0_Bits.DATA */
#define IFX_FLASH_RRD0_DATA_LEN (32u)

/** \brief  Mask for Ifx_FLASH_RRD0_Bits.DATA */
#define IFX_FLASH_RRD0_DATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FLASH_RRD0_Bits.DATA */
#define IFX_FLASH_RRD0_DATA_OFF (0u)

/** \brief  Length for Ifx_FLASH_RRD1_Bits.DATA */
#define IFX_FLASH_RRD1_DATA_LEN (32u)

/** \brief  Mask for Ifx_FLASH_RRD1_Bits.DATA */
#define IFX_FLASH_RRD1_DATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_FLASH_RRD1_Bits.DATA */
#define IFX_FLASH_RRD1_DATA_OFF (0u)

/** \brief  Length for Ifx_FLASH_UBAB_CFG_Bits.CLR */
#define IFX_FLASH_UBAB_CFG_CLR_LEN (1u)

/** \brief  Mask for Ifx_FLASH_UBAB_CFG_Bits.CLR */
#define IFX_FLASH_UBAB_CFG_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_UBAB_CFG_Bits.CLR */
#define IFX_FLASH_UBAB_CFG_CLR_OFF (8u)

/** \brief  Length for Ifx_FLASH_UBAB_CFG_Bits.DIS */
#define IFX_FLASH_UBAB_CFG_DIS_LEN (1u)

/** \brief  Mask for Ifx_FLASH_UBAB_CFG_Bits.DIS */
#define IFX_FLASH_UBAB_CFG_DIS_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_UBAB_CFG_Bits.DIS */
#define IFX_FLASH_UBAB_CFG_DIS_OFF (9u)

/** \brief  Length for Ifx_FLASH_UBAB_CFG_Bits.SEL */
#define IFX_FLASH_UBAB_CFG_SEL_LEN (6u)

/** \brief  Mask for Ifx_FLASH_UBAB_CFG_Bits.SEL */
#define IFX_FLASH_UBAB_CFG_SEL_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_UBAB_CFG_Bits.SEL */
#define IFX_FLASH_UBAB_CFG_SEL_OFF (0u)

/** \brief  Length for Ifx_FLASH_UBAB_STAT_Bits.VLD0 */
#define IFX_FLASH_UBAB_STAT_VLD0_LEN (1u)

/** \brief  Mask for Ifx_FLASH_UBAB_STAT_Bits.VLD0 */
#define IFX_FLASH_UBAB_STAT_VLD0_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_UBAB_STAT_Bits.VLD0 */
#define IFX_FLASH_UBAB_STAT_VLD0_OFF (0u)

/** \brief  Length for Ifx_FLASH_UBAB_TOP_Bits.ADDR */
#define IFX_FLASH_UBAB_TOP_ADDR_LEN (19u)

/** \brief  Mask for Ifx_FLASH_UBAB_TOP_Bits.ADDR */
#define IFX_FLASH_UBAB_TOP_ADDR_MSK (0x7ffffu)

/** \brief  Offset for Ifx_FLASH_UBAB_TOP_Bits.ADDR */
#define IFX_FLASH_UBAB_TOP_ADDR_OFF (5u)

/** \brief  Length for Ifx_FLASH_UBAB_TOP_Bits.CLR */
#define IFX_FLASH_UBAB_TOP_CLR_LEN (1u)

/** \brief  Mask for Ifx_FLASH_UBAB_TOP_Bits.CLR */
#define IFX_FLASH_UBAB_TOP_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_UBAB_TOP_Bits.CLR */
#define IFX_FLASH_UBAB_TOP_CLR_OFF (31u)

/** \brief  Length for Ifx_FLASH_UBAB_TOP_Bits.ERR */
#define IFX_FLASH_UBAB_TOP_ERR_LEN (6u)

/** \brief  Mask for Ifx_FLASH_UBAB_TOP_Bits.ERR */
#define IFX_FLASH_UBAB_TOP_ERR_MSK (0x3fu)

/** \brief  Offset for Ifx_FLASH_UBAB_TOP_Bits.ERR */
#define IFX_FLASH_UBAB_TOP_ERR_OFF (24u)

/** \brief  Length for Ifx_FLASH_UBAB_TOP_Bits.VLD */
#define IFX_FLASH_UBAB_TOP_VLD_LEN (1u)

/** \brief  Mask for Ifx_FLASH_UBAB_TOP_Bits.VLD */
#define IFX_FLASH_UBAB_TOP_VLD_MSK (0x1u)

/** \brief  Offset for Ifx_FLASH_UBAB_TOP_Bits.VLD */
#define IFX_FLASH_UBAB_TOP_VLD_OFF (30u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXFLASH_BF_H */
