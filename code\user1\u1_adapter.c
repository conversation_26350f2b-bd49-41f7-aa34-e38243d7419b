/*
 * u1_adapter.h - 硬件适配层接口
 * 作者: BMW_智能车组_Baiyuyu_Xiaohuihui_Wzz
 * 创建时间: 2025年07月06日
 * 描述: 精简的硬件适配层，专注于UART通信和硬件抽象
 */

#include "zf_common_headfile.h"
#include "u1_adapter.h"
#include "zf_driver_timer.h"
#include <string.h>

/*  */
static uint8 rx_buffer[U1_RX_BUFFER_SIZE];        /* 接收缓冲区*/
static volatile uint32 rx_head = 0;               /*  */
static volatile uint32 rx_tail = 0;               /*  */
static volatile boolean rx_overflow = FALSE;      /*  */
static boolean is_initialized = FALSE;            /* 初始化标区*/
static u1_adapter_status_t adapter_status = U1_ADAPTER_OK;

#if U1_FEATURE_AUDIO
/* 音频采集相关变量 */
static int16 audio_buffer[U1_AUDIO_BUFFER_SIZE];   /* 音频数据缓冲区 */
static volatile uint32 audio_head = 0;             /* 音频缓冲区写指针 */
static volatile uint32 audio_tail = 0;             /* 音频缓冲区读指针 */
static volatile boolean audio_overflow = FALSE;    /* 音频缓冲区溢出标志 */
static boolean audio_initialized = FALSE;          /* 音频初始化标志 */
static boolean audio_running = FALSE;              /* 音频采集运行标志 */
#endif

#if U1_FEATURE_AUDIO
/* 音频采样定时器中断处理 */
void u1_adapter_audio_timer_handler(void)
{
    int16 audio_sample;
    uint32 next_head;

    if(!audio_running) return;

    /* 读取ADC采样值并进行偏移校正 */
    audio_sample = (int16)(adc_convert(U1_AUDIO_ADC) - 1870);

    /* 计算下一个写指针位置 */
    next_head = (audio_head + 1) % U1_AUDIO_BUFFER_SIZE;

    /* 检查缓冲区是否已满 */
    if(next_head != audio_tail)
    {
        audio_buffer[audio_head] = audio_sample;
        audio_head = next_head;
    }
    else
    {
        audio_overflow = TRUE;
    }
}
#endif

/*  */
void u1_adapter_uart_rx_handler(void)
{
    uint8 data;
    
    /*  */
    while(uart_query_byte(U1_UART_INDEX, &data))
    {
        /*  */
        uint32 next_head = (rx_head + 1) % U1_RX_BUFFER_SIZE;
        
        /*  */
        if(next_head != rx_tail)
        {
            /*  */
            rx_buffer[rx_head] = data;
            rx_head = next_head;
        }
        else
        {
            /*  */
            rx_tail = (rx_tail + U1_RX_BUFFER_SIZE / 4) % U1_RX_BUFFER_SIZE;
            rx_overflow = TRUE;
            adapter_status = U1_ADAPTER_ERROR_RX_OVERFLOW;
        }
    }
}

/*  */
boolean u1_adapter_init(void)
{
    /*  */
    uart_init(U1_UART_INDEX, U1_UART_BAUDRATE, U1_UART_TX_PIN, U1_UART_RX_PIN);
    
    /*  */
    uart_rx_interrupt(U1_UART_INDEX, 1);
    
    /*  */
    rx_head = 0;
    rx_tail = 0;
    rx_overflow = FALSE;
    
    /*  */
    is_initialized = TRUE;
    adapter_status = U1_ADAPTER_OK;

#if U1_FEATURE_AUDIO
    /* 初始化音频采集功能 */
    u1_adapter_audio_init();
#endif

#if U1_DEBUG_ENABLE
    u1_adapter_debug_output("U1适配层初始化成功");
#endif

    return TRUE;
}

/*  */
boolean u1_adapter_is_ready(void)
{
    return is_initialized;
}

/*  */
boolean u1_adapter_send_data(const uint8* data, uint32 len)
{
    /*  */
    if(!data || len == 0)
    {
        adapter_status = U1_ADAPTER_ERROR_INVALID_PARAM;
        return FALSE;
    }
    
    /*  */
    if(!is_initialized)
    {
        adapter_status = U1_ADAPTER_ERROR_INIT_FAILED;
        return FALSE;
    }
    
    /*  */
    uart_write_buffer(U1_UART_INDEX, data, len);
    
    return TRUE;
}

/*  */
uint32 u1_adapter_receive_data(uint8* buffer, uint32 max_len, uint32 timeout_ms)
{
    uint32 recv_count = 0;
    uint32 start_time = system_getval_ms();
    
    /*  */
    if(!buffer || max_len == 0)
    {
        adapter_status = U1_ADAPTER_ERROR_INVALID_PARAM;
        return 0;
    }
    
    /*  */
    if(!is_initialized)
    {
        adapter_status = U1_ADAPTER_ERROR_INIT_FAILED;
        return 0;
    }
    
    while(recv_count < max_len)
    {
        /*  */
        if(timeout_ms > 0 && (system_getval_ms() - start_time) > timeout_ms)
        {
            break;
        }
        
        /*  */
        if(rx_head != rx_tail)
        {
            /*  */
            buffer[recv_count++] = rx_buffer[rx_tail];
            rx_tail = (rx_tail + 1) % U1_RX_BUFFER_SIZE;
        }
        else
        {
            /*  */
            if(timeout_ms > 0)
            {
                system_delay_ms(1);
            }
            else
            {
                break; // 非阻塞模式，立即返回
            }
        }
    }
    
    return recv_count;
}

/*  */
uint32 u1_adapter_get_rx_count(void)
{
    if(rx_head >= rx_tail)
    {
        return rx_head - rx_tail;
    }
    else
    {
        return U1_RX_BUFFER_SIZE - rx_tail + rx_head;
    }
}

/*  */
void u1_adapter_clear_rx_buffer(void)
{
    rx_head = 0;
    rx_tail = 0;
    rx_overflow = FALSE;
    
    if(adapter_status == U1_ADAPTER_ERROR_RX_OVERFLOW)
    {
        adapter_status = U1_ADAPTER_OK;
    }
}

/*  */
boolean u1_adapter_check_rx_overflow(void)
{
    boolean overflow = rx_overflow;
    rx_overflow = FALSE; // 读取后清除标�?
    return overflow;
}

/*  */
u1_adapter_status_t u1_adapter_get_status(void)
{
    return adapter_status;
}

/*  */
void u1_adapter_debug_output(const char* msg)
{
#if U1_DEBUG_ENABLE
    if(msg)
    {
        debug_output_struct info;
        debug_output_struct_init(&info);
        info.output_uart(msg);
        info.output_uart("\r\n");
    }
#endif
}

#if U1_FEATURE_AUDIO
/* 音频采集初始化 */
boolean u1_adapter_audio_init(void)
{
    if(audio_initialized) return TRUE;

    /* 初始化ADC */
    adc_init(U1_AUDIO_ADC, ADC_12BIT);
    adc_init(U1_RANDOM_ADC, ADC_12BIT);

    /* 初始化按键 */
    gpio_init(U1_RECORD_BUTTON, GPI, GPIO_HIGH, GPI_PULL_UP);

    /* 初始化定时器（8KHz采样率，125us周期） */
    pit_us_init(U1_AUDIO_TIMER, 125);
    pit_disable(U1_AUDIO_TIMER);

    /* 清空音频缓冲区 */
    audio_head = 0;
    audio_tail = 0;
    audio_overflow = FALSE;
    audio_running = FALSE;

    audio_initialized = TRUE;
    return TRUE;
}

/* 开始音频采集 */
boolean u1_adapter_audio_start(void)
{
    if(!audio_initialized) return FALSE;

    /* 清空缓冲区 */
    audio_head = 0;
    audio_tail = 0;
    audio_overflow = FALSE;

    /* 启动定时器 */
    audio_running = TRUE;
    pit_enable(U1_AUDIO_TIMER);

    return TRUE;
}

/* 停止音频采集 */
boolean u1_adapter_audio_stop(void)
{
    if(!audio_initialized) return FALSE;

    /* 停止定时器 */
    pit_disable(U1_AUDIO_TIMER);
    audio_running = FALSE;

    return TRUE;
}

/* 检查按键是否按下 */
boolean u1_adapter_button_pressed(void)
{
    if(!audio_initialized) return FALSE;

    return (gpio_get_level(U1_RECORD_BUTTON) == 0);
}

/* 读取音频数据 */
uint32 u1_adapter_audio_read(int16* buffer, uint32 max_samples)
{
    uint32 read_count = 0;

    if(!buffer || max_samples == 0 || !audio_initialized) return 0;

    /* 从环形缓冲区读取数据 */
    while(read_count < max_samples && audio_tail != audio_head)
    {
        buffer[read_count++] = audio_buffer[audio_tail];
        audio_tail = (audio_tail + 1) % U1_AUDIO_BUFFER_SIZE;
    }

    return read_count;
}

/* 检查是否有音频数据可用 */
boolean u1_adapter_audio_available(void)
{
    if(!audio_initialized) return FALSE;

    return (audio_tail != audio_head);
}
#endif
