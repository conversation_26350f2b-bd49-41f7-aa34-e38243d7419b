<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxCif" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Port/Std/IfxPort.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxPort_cfg.c</iLLD:file>
  <iLLD:file class="mchal">Cif/Std/IfxCif.c</iLLD:file>
  <iLLD:file class="mchal">Cif/Cam/IfxCif_Cam.c</iLLD:file>
  <iLLD:file class="mchal">Emem/Std/IfxEmem.c</iLLD:file>
  <iLLD:file class="mchal">_PinMap/IfxCif_PinMap.c</iLLD:file>
  <iLLD:file class="mchal">_PinMap/IfxI2c_PinMap.c</iLLD:file>
  <iLLD:file class="mchal">I2c/Std/IfxI2c.c</iLLD:file>
  <iLLD:file class="mchal">I2c/I2c/IfxI2c_I2c.c</iLLD:file>
</iLLD:filelist>
