	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc26856a --dep-file=IfxStdIf_DPipe.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_DPipe.src ../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_DPipe.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_DPipe.c'

	
$TC16X
	.sdecl	'.rodata.IfxStdIf_DPipe..1.cnt',data,rom
	.sect	'.rodata.IfxStdIf_DPipe..1.cnt'
	.align	4
.1.cnt:	.type	object
	.size	.1.cnt,8
	.word	-1,2147483647
	
	.sdecl	'.text.IfxStdIf_DPipe.IfxStdIf_DPipe_print',code,cluster('IfxStdIf_DPipe_print')
	.sect	'.text.IfxStdIf_DPipe.IfxStdIf_DPipe_print'
	.align	2
	
	.global	IfxStdIf_DPipe_print
; Function IfxStdIf_DPipe_print
.L6:
IfxStdIf_DPipe_print:	.type	func
	lea	a10,[a10]-264
.L39:
	mov.aa	a15,a4
.L42:
	ld.bu	d15,[a15]4
.L52:
	jne	d15,#0,.L2
.L21:
	lea	a6,[a10]264
.L43:
	lea	a4,[a10]0
.L41:
	call	vsprintf
.L40:
	lea	a4,[a10]0
	call	strlen
.L53:
	st.h	[a10]256,d2
.L54:
	lea	a5,[a10]0
.L55:
	lea	a6,[a10]256
.L56:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e4,[a2]0
.L29:
	ld.a	a2,[a15]8
.L57:
	ld.a	a4,[a15]
.L58:
	calli	a2
.L59:
	j	.L3
.L3:
	j	.L4
.L2:
.L4:
	ret
.L16:
	
__IfxStdIf_DPipe_print_function_end:
	.size	IfxStdIf_DPipe_print,__IfxStdIf_DPipe_print_function_end-IfxStdIf_DPipe_print
.L15:
	; End of function
	
	.calls	'IfxStdIf_DPipe_print','vsprintf'
	.calls	'IfxStdIf_DPipe_print','strlen'
	.calls	'IfxStdIf_DPipe_print','__INDIRECT__'
	.extern	strlen
	.extern	vsprintf
	.extern	__printf_float
	.extern	__printf_llong
	.extern	__INDIRECT__
	.calls	'IfxStdIf_DPipe_print','',264
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L8:
	.word	2418
	.half	3
	.word	.L9
	.byte	4
.L7:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_DPipe.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L10
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'void',0,4
	.word	253
	.byte	5
	.byte	'IfxStdIf_InterfaceDriver',0,2,118,15
	.word	259
.L24:
	.byte	2
	.byte	'short int',0,2,5,4
	.word	297
	.byte	2
	.byte	'long long int',0,8,5,6
	.word	236
	.byte	1,1,7
	.word	259
	.byte	7
	.word	259
	.byte	7
	.word	310
	.byte	7
	.word	315
	.byte	0,4
	.word	332
	.byte	5
	.byte	'IfxStdIf_DPipe_Write',0,1,92,19
	.word	360
	.byte	5
	.byte	'IfxStdIf_DPipe_Read',0,1,107,19
	.word	360
	.byte	2
	.byte	'long int',0,4,5,6
	.word	422
	.byte	1,1,7
	.word	259
	.byte	0,4
	.word	434
	.byte	5
	.byte	'IfxStdIf_DPipe_GetReadCount',0,1,115,18
	.word	447
	.byte	8
	.word	236
	.byte	4
	.word	488
	.byte	6
	.word	493
	.byte	1,1,7
	.word	259
	.byte	0,4
	.word	498
	.byte	5
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,1,123,36
	.word	511
	.byte	5
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,1,147,1,18
	.word	447
	.byte	4
	.word	498
	.byte	5
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,1,155,1,37
	.word	590
	.byte	6
	.word	236
	.byte	1,1,7
	.word	259
	.byte	7
	.word	297
	.byte	7
	.word	315
	.byte	0,4
	.word	633
	.byte	5
	.byte	'IfxStdIf_DPipe_CanReadCount',0,1,166,1,19
	.word	656
	.byte	5
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,1,177,1,19
	.word	656
	.byte	6
	.word	236
	.byte	1,1,7
	.word	259
	.byte	7
	.word	315
	.byte	0,4
	.word	736
	.byte	5
	.byte	'IfxStdIf_DPipe_FlushTx',0,1,186,1,19
	.word	754
	.byte	9,1,1,7
	.word	259
	.byte	0,4
	.word	791
	.byte	5
	.byte	'IfxStdIf_DPipe_ClearTx',0,1,200,1,16
	.word	800
	.byte	5
	.byte	'IfxStdIf_DPipe_ClearRx',0,1,193,1,16
	.word	800
	.byte	5
	.byte	'IfxStdIf_DPipe_OnReceive',0,1,208,1,16
	.word	800
	.byte	5
	.byte	'IfxStdIf_DPipe_OnTransmit',0,1,215,1,16
	.word	800
	.byte	5
	.byte	'IfxStdIf_DPipe_OnError',0,1,222,1,16
	.word	800
	.byte	2
	.byte	'unsigned long int',0,4,7,6
	.word	970
	.byte	1,1,7
	.word	259
	.byte	0,4
	.word	991
	.byte	5
	.byte	'IfxStdIf_DPipe_GetSendCount',0,1,131,1,18
	.word	1004
	.byte	6
	.word	315
	.byte	1,1,7
	.word	259
	.byte	0,4
	.word	1046
	.byte	5
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,1,139,1,24
	.word	1059
	.byte	5
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,1,229,1,16
	.word	800
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,1,233,1,8,76,11
	.byte	'driver',0
	.word	264
	.byte	4,2,35,0,11
	.byte	'txDisabled',0
	.word	236
	.byte	1,2,35,4,11
	.byte	'write',0
	.word	365
	.byte	4,2,35,8,11
	.byte	'read',0
	.word	394
	.byte	4,2,35,12,11
	.byte	'getReadCount',0
	.word	452
	.byte	4,2,35,16,11
	.byte	'getReadEvent',0
	.word	516
	.byte	4,2,35,20,11
	.byte	'getWriteCount',0
	.word	552
	.byte	4,2,35,24,11
	.byte	'getWriteEvent',0
	.word	595
	.byte	4,2,35,28,11
	.byte	'canReadCount',0
	.word	661
	.byte	4,2,35,32,11
	.byte	'canWriteCount',0
	.word	698
	.byte	4,2,35,36,11
	.byte	'flushTx',0
	.word	759
	.byte	4,2,35,40,11
	.byte	'clearTx',0
	.word	805
	.byte	4,2,35,44,11
	.byte	'clearRx',0
	.word	837
	.byte	4,2,35,48,11
	.byte	'onReceive',0
	.word	869
	.byte	4,2,35,52,11
	.byte	'onTransmit',0
	.word	903
	.byte	4,2,35,56,11
	.byte	'onError',0
	.word	938
	.byte	4,2,35,60,11
	.byte	'getSendCount',0
	.word	1009
	.byte	4,2,35,64,11
	.byte	'getTxTimeStamp',0
	.word	1064
	.byte	4,2,35,68,11
	.byte	'resetSendCount',0
	.word	1103
	.byte	4,2,35,72,0
.L17:
	.byte	4
	.word	1142
	.byte	4
	.word	297
.L28:
	.byte	12
	.byte	'IfxStdIf_DPipe_write',0,3,1,134,2,20
	.word	236
	.byte	1,1
.L30:
	.byte	13
	.byte	'stdIf',0,1,134,2,57
	.word	1542
.L32:
	.byte	13
	.byte	'data',0,1,134,2,70
	.word	259
.L34:
	.byte	13
	.byte	'count',0,1,134,2,87
	.word	1547
.L36:
	.byte	13
	.byte	'timeout',0,1,134,2,107
	.word	315
.L38:
	.byte	14,0,4
	.word	253
	.byte	4
	.word	332
	.byte	4
	.word	332
	.byte	4
	.word	434
	.byte	4
	.word	498
	.byte	4
	.word	434
	.byte	4
	.word	498
	.byte	4
	.word	633
	.byte	4
	.word	633
	.byte	4
	.word	736
	.byte	4
	.word	791
	.byte	4
	.word	791
	.byte	4
	.word	791
	.byte	4
	.word	791
	.byte	4
	.word	791
	.byte	4
	.word	991
	.byte	4
	.word	1046
	.byte	4
	.word	791
	.byte	15
	.byte	'__dotdotdot__',0
	.word	259
	.byte	1,1,1,1,16
	.word	1552
	.byte	17
	.word	1585
	.byte	17
	.word	1600
	.byte	17
	.word	1614
	.byte	17
	.word	1629
	.byte	14,0,2
	.byte	'char',0,1,6,18
	.word	1788
	.byte	4
	.word	1796
	.byte	18
	.word	1788
	.byte	4
	.word	1806
.L19:
	.byte	5
	.byte	'pchar',0,3,56,28
	.word	1811
.L22:
	.byte	19,128,2
	.word	1788
	.byte	20,255,1,0,4
	.word	1788
	.byte	4
	.word	1788
.L26:
	.byte	5
	.byte	'va_list',0,4,24,16
	.word	1846
	.byte	2
	.byte	'unsigned int',0,4,7,21
	.byte	'strlen',0,5,52,17
	.word	1867
	.byte	1,1,1,1,22,5,52,39
	.word	1811
	.byte	0,2
	.byte	'int',0,4,5,23
	.word	1846
	.byte	23
	.word	1811
	.byte	21
	.byte	'vsprintf',0,6,153,1,16
	.word	1911
	.byte	1,1,1,1,22,6,153,1,32
	.word	1918
	.byte	22,6,153,1,55
	.word	1923
	.byte	22,6,153,1,67
	.word	1851
	.byte	0,24
	.byte	'__INDIRECT__',0,7,1,1,1,1,1,5
	.byte	'__wchar_t',0,7,1,1
	.word	297
	.byte	5
	.byte	'__size_t',0,7,1,1
	.word	1867
	.byte	5
	.byte	'__ptrdiff_t',0,7,1,1
	.word	1911
	.byte	25,1,4
	.word	2053
	.byte	5
	.byte	'__codeptr',0,7,1,1
	.word	2055
	.byte	5
	.byte	'size_t',0,8,24,25
	.word	1867
	.byte	5
	.byte	'boolean',0,9,101,29
	.word	236
	.byte	5
	.byte	'uint8',0,9,105,29
	.word	236
	.byte	2
	.byte	'unsigned short int',0,2,7,5
	.byte	'uint16',0,9,109,29
	.word	2123
	.byte	5
	.byte	'uint32',0,9,113,29
	.word	970
	.byte	5
	.byte	'sint16',0,9,126,29
	.word	297
	.byte	5
	.byte	'sint32',0,9,131,1,29
	.word	422
	.byte	5
	.byte	'sint64',0,9,138,1,29
	.word	315
	.byte	2
	.byte	'float',0,4,4,5
	.byte	'float32',0,9,167,1,29
	.word	2222
	.byte	5
	.byte	'pvoid',0,3,57,28
	.word	259
	.byte	5
	.byte	'Ifx_TickTime',0,3,79,28
	.word	315
	.byte	5
	.byte	'Ifx_SizeT',0,3,92,16
	.word	297
	.byte	5
	.byte	'IfxStdIf_DPipe',0,1,71,32
	.word	1142
	.byte	8
	.word	236
	.byte	4
	.word	2324
	.byte	5
	.byte	'IfxStdIf_DPipe_WriteEvent',0,1,73,32
	.word	2329
	.byte	5
	.byte	'IfxStdIf_DPipe_ReadEvent',0,1,74,32
	.word	2329
	.byte	5
	.byte	'_iob_flag_t',0,6,82,25
	.word	2123
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,59,0,3,8,0,0,4,15,0,73,19,0
	.byte	0,5,22,0,3,8,58,15,59,15,57,15,73,19,0,0,6,21,1,73,19,54,15,39,12,0,0,7,5,0,73,19,0,0,8,53,0,73,19,0,0
	.byte	9,21,1,54,15,39,12,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,73,19,11,15,56,9,0,0,12,46
	.byte	1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,13,5,0,3,8,58,15,59,15,57,15,73,19,0,0,14,11,0,0,0
	.byte	15,46,0,3,8,73,19,54,15,39,12,63,12,60,12,0,0,16,46,1,49,19,0,0,17,5,0,49,19,0,0,18,38,0,73,19,0,0,19
	.byte	1,1,11,15,73,19,0,0,20,33,0,47,15,0,0,21,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0
	.byte	22,5,0,58,15,59,15,57,15,73,19,0,0,23,55,0,73,19,0,0,24,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0
	.byte	0,25,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L10:
	.word	.L45-.L44
.L44:
	.half	3
	.word	.L47-.L46
.L46:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'stdarg.h',0,1,0,0
	.byte	'string.h',0,1,0,0
	.byte	'stdio.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_DPipe.c',0,0,0,0
	.byte	'stddef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0,0
.L47:
.L45:
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_DPipe_print')
	.sect	'.debug_info'
.L11:
	.word	462
	.half	3
	.word	.L12
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_DPipe.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L14,.L13
	.byte	2
	.word	.L7
	.byte	3
	.byte	'IfxStdIf_DPipe_print',0,1,53,6,1,1,1
	.word	.L6,.L16,.L5
	.byte	4
	.byte	'stdIf',0,1,53,43
	.word	.L17,.L18
	.byte	4
	.byte	'format',0,1,53,56
	.word	.L19,.L20
	.byte	5,1,53,64,6
	.word	.L6,.L16
	.byte	6
	.word	.L21,.L3
	.byte	7
	.byte	'message',0,1,57,19
	.word	.L22,.L23
	.byte	7
	.byte	'count',0,1,58,19
	.word	.L24,.L25
	.byte	7
	.byte	'args',0,1,59,19
	.word	.L26,.L27
	.byte	8
	.word	.L28,.L29,.L3
	.byte	9
	.word	.L30,.L31
	.byte	9
	.word	.L32,.L33
	.byte	9
	.word	.L34,.L35
	.byte	9
	.word	.L36,.L37
	.byte	10
	.word	.L38,.L29,.L3
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_DPipe_print')
	.sect	'.debug_abbrev'
.L12:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,24,0,58,15,59,15,57,15,0,0
	.byte	6,11,1,17,1,18,1,0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,8,29,1,49,16,17,1,18,1,0,0,9,5,0,49,16
	.byte	2,6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_DPipe_print')
	.sect	'.debug_line'
.L13:
	.word	.L49-.L48
.L48:
	.half	3
	.word	.L51-.L50
.L50:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_DPipe.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0,0,0,0,0
.L51:
	.byte	5,6,7,0,5,2
	.word	.L6
	.byte	3,52,1,5,15,9
	.half	.L42-.L6
	.byte	3,2,1,5,10,9
	.half	.L52-.L42
	.byte	1,5,9,7,9
	.half	.L21-.L52
	.byte	3,5,1,5,26,9
	.half	.L43-.L21
	.byte	3,1,1,5,43,9
	.half	.L41-.L43
	.byte	1,5,35,9
	.half	.L40-.L41
	.byte	3,2,1,5,15,9
	.half	.L53-.L40
	.byte	1,5,45,9
	.half	.L54-.L53
	.byte	3,3,1,5,55,9
	.half	.L55-.L54
	.byte	1,5,62,9
	.half	.L56-.L55
	.byte	1,4,2,5,17,9
	.half	.L29-.L56
	.byte	3,198,1,1,5,30,9
	.half	.L57-.L29
	.byte	1,5,53,9
	.half	.L58-.L57
	.byte	1,5,5,9
	.half	.L59-.L58
	.byte	1,4,1,5,31,9
	.half	.L3-.L59
	.byte	3,180,126,1,5,1,9
	.half	.L4-.L3
	.byte	3,12,1,7,9
	.half	.L15-.L4
	.byte	0,1,1
.L49:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_DPipe_print')
	.sect	'.debug_ranges'
.L14:
	.word	-1,.L6,0,.L15-.L6,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_DPipe_print')
	.sect	'.debug_loc'
.L5:
	.word	-1,.L6,0,.L39-.L6
	.half	2
	.byte	138,0
	.word	.L39-.L6,.L16-.L6
	.half	3
	.byte	138,136,2
	.word	.L16-.L6,.L16-.L6
	.half	2
	.byte	138,0
	.word	0,0
.L27:
	.word	-1,.L6,.L43-.L6,.L40-.L6
	.half	1
	.byte	102
	.word	0,0
.L25:
	.word	-1,.L6,0,.L16-.L6
	.half	2
	.byte	145,120
	.word	0,0
.L35:
	.word	0,0
.L33:
	.word	0,0
.L20:
	.word	-1,.L6,0,.L40-.L6
	.half	1
	.byte	101
	.word	0,0
.L23:
	.word	-1,.L6,0,.L16-.L6
	.half	3
	.byte	145,248,125
	.word	0,0
.L18:
	.word	-1,.L6,0,.L41-.L6
	.half	1
	.byte	100
	.word	.L42-.L6,.L16-.L6
	.half	1
	.byte	111
	.word	0,0
.L31:
	.word	0,0
.L37:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L60:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_DPipe_print')
	.sect	'.debug_frame'
	.word	36
	.word	.L60,.L6,.L16-.L6
	.byte	4
	.word	(.L39-.L6)/2
	.byte	19,136,2,22,26,4,19,138,136,2,4
	.word	(.L16-.L39)/2
	.byte	19,0,8,26
	; Module end
