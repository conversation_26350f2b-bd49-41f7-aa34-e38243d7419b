/*
 * camera.c
 *
 *  Created on: 2025��4��3��
 *      Author: lenovo
 */
/*
#include "zf_common_headfile.h"

//void Camera_Init(void)
//{
//    mt9v03x_init();
//}
//void Camera_Show(void)
//{
//    if(mt9v03x_finish_flag)
//    {
//        ips200_displayimage03x((const uint8 *)mt9v03x_image, MT9V03X_W, MT9V03X_H);
//        mt9v03x_finish_flag = 0;//һҪڲɼ
//    }
//}

uint8 image_03x[MT9V03X_H][MT9V03X_W];

int side_left[120];
int side_right[120];
uint8 mid[120];
uint8 target_mid[120];
uint8 error_mt;

uint8 GetOSTU(uint8 tmImage[MT9V03X_H][MT9V03X_W])
{
  int16 i,j;
  uint32 Amount = 0;
  uint32 PixelBack = 0;
  uint32 PixelIntegralBack = 0;
  uint32 PixelIntegral = 0;
  int32 PixelIntegralFore = 0;
  int32 PixelFore = 0;
  double OmegaBack, OmegaFore, MicroBack, MicroFore, SigmaB, Sigma; // ䷽;
  int16 MinValue, MaxValue;
  uint8 Threshold = 0;
  uint8 HistoGram[256];              //

  for (j = 0; j < 256; j++)  HistoGram[j] = 0; //ʼҶֱͼ

  for (j = 0; j < MT9V03X_H; j++)
  {
    for (i = 0; i < MT9V03X_W; i++)
    {
      HistoGram[tmImage[j][i]]++; //ͳƻҶȼÿͼеĸ
    }
  }

  for (MinValue = 0; MinValue < 256 && HistoGram[MinValue] == 0; MinValue++) ;        //ȡСҶȵֵ
  for (MaxValue = 255; MaxValue > MinValue && HistoGram[MinValue] == 0; MaxValue--) ; //ȡҶȵֵ

  if (MaxValue == MinValue)     return MaxValue;         // ͼֻһɫ
  if (MinValue + 1 == MaxValue)  return MinValue;        // ͼֻжɫ

  for (j = MinValue; j <= MaxValue; j++)    Amount += HistoGram[j];        //  

  PixelIntegral = 0;
  for (j = MinValue; j <= MaxValue; j++)
  {
    PixelIntegral += HistoGram[j] * j;//Ҷֵ
  }
  SigmaB = -1;
  for (j = MinValue; j < MaxValue; j++)
  {
    PixelBack = PixelBack + HistoGram[j];   //ǰص
    PixelFore = Amount - PixelBack;         //ص
    OmegaBack = (double)PixelBack / Amount;//ǰذٷֱ
    OmegaFore = (double)PixelFore / Amount;//ذٷֱ
    PixelIntegralBack += HistoGram[j] * j;  //ǰҶֵ
    PixelIntegralFore = PixelIntegral - PixelIntegralBack;//Ҷֵ
    MicroBack = (double)PixelIntegralBack / PixelBack;   //ǰҶȰٷֱ
    MicroFore = (double)PixelIntegralFore / PixelFore;   //ҶȰٷֱ
    Sigma = OmegaBack * OmegaFore * (MicroBack - MicroFore) * (MicroBack - MicroFore);//䷽
    if (Sigma > SigmaB)                    //䷽g //ҳ䷽ԼӦֵ
    {
      SigmaB = Sigma;
      Threshold = j;
    }
  }
  return Threshold;                        //ֵ;
    return 0;
}

void Set_two_image(void)
{
    uint8 i,j;
    uint8 dat;//ݴҶֵ

    dat=GetOSTU(mt9v03x_image);

    for(i=0;i<MT9V03X_H;i++)
    {
        for(j=0;j<MT9V03X_W;j++)
        {
            if(mt9v03x_image[i][j]>dat)
            {
                image_03x[i][j]=255;
            }
            else
            {
                image_03x[i][j]=0;
            }
        }
    }
}

void Find_zhong(void)
{
    int i,j;

    for(i=0;i<120;i++)
    {
        for(j=94;j<188;j++)
        {
            if(image_03x[i][j]==0)
            {
                side_right[i]=j;
                break;
            }
        }

        for(j=94;j>0;j--)
        {
            if(image_03x[i][j]==0)
            {
                side_left[i]=j;
                break;
            }
        }

        mid[i]=(side_right[i]+side_left[i])/2;
    }
}

void Show_line(void)
{
    int i;
    for(i=120-1;i>=0;i--)
    {
        if(mid[i]<188&&mid[i]>=0)
        {
            image_03x[i][side_left[i]]=0;
            image_03x[i][side_right[i]]=0;
            image_03x[i][mid[i]]=0;
        }
    }
}

float k=1.5;
float p=1.2;

void Find_error(void)
{
    int i;
    float error1,error2;
    float value;

//    for(i=0;i<120;i++)
//    {
//        target_mid[i]=image_03x[i][94];
//    }

    for(i=0;i<=30;i++)
    {
        error1+=(94-mid[i]);
    }

    for(i=31;i<=60;i++)
    {
        error2+=(94-mid[i]);
    }

    value=(k*error1+p*error2)/30;

    steer_duty=SERVO_MOTOR_MID+value;

    if(steer_duty>=125){steer_duty=125;}
    if(steer_duty<=55){steer_duty=55;}

    Steer_Set(steer_duty);

    tft180_show_float(0, 120, value, 3, 2);
    tft180_show_float(48, 120, error1, 3, 2);
    tft180_show_float(96, 120, error2, 3, 2);

}

void Mt9v03x_Init(void)
{
    if(mt9v03x_finish_flag)
    {
        Set_two_image();
        Find_zhong();
        Show_line();
        Find_error();
        tft180_show_gray_image(0, 0, (const uint8 *)image_03x, MT9V03X_W, MT9V03X_H, 160, 120, 0);
        mt9v03x_finish_flag=0;
    }
}

*/

