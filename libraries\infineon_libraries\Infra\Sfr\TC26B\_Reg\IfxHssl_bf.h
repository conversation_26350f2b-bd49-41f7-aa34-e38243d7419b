/**
 * \file IfxHssl_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Hssl_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Hssl
 * 
 */
#ifndef IFXHSSL_BF_H
#define IFXHSSL_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Hssl_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN0 */
#define IFX_HSSL_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN0 */
#define IFX_HSSL_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN0 */
#define IFX_HSSL_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN10 */
#define IFX_HSSL_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN10 */
#define IFX_HSSL_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN10 */
#define IFX_HSSL_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN11 */
#define IFX_HSSL_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN11 */
#define IFX_HSSL_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN11 */
#define IFX_HSSL_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN12 */
#define IFX_HSSL_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN12 */
#define IFX_HSSL_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN12 */
#define IFX_HSSL_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN13 */
#define IFX_HSSL_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN13 */
#define IFX_HSSL_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN13 */
#define IFX_HSSL_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN14 */
#define IFX_HSSL_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN14 */
#define IFX_HSSL_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN14 */
#define IFX_HSSL_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN15 */
#define IFX_HSSL_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN15 */
#define IFX_HSSL_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN15 */
#define IFX_HSSL_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN16 */
#define IFX_HSSL_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN16 */
#define IFX_HSSL_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN16 */
#define IFX_HSSL_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN17 */
#define IFX_HSSL_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN17 */
#define IFX_HSSL_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN17 */
#define IFX_HSSL_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN18 */
#define IFX_HSSL_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN18 */
#define IFX_HSSL_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN18 */
#define IFX_HSSL_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN19 */
#define IFX_HSSL_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN19 */
#define IFX_HSSL_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN19 */
#define IFX_HSSL_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN1 */
#define IFX_HSSL_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN1 */
#define IFX_HSSL_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN1 */
#define IFX_HSSL_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN20 */
#define IFX_HSSL_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN20 */
#define IFX_HSSL_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN20 */
#define IFX_HSSL_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN21 */
#define IFX_HSSL_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN21 */
#define IFX_HSSL_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN21 */
#define IFX_HSSL_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN22 */
#define IFX_HSSL_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN22 */
#define IFX_HSSL_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN22 */
#define IFX_HSSL_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN23 */
#define IFX_HSSL_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN23 */
#define IFX_HSSL_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN23 */
#define IFX_HSSL_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN24 */
#define IFX_HSSL_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN24 */
#define IFX_HSSL_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN24 */
#define IFX_HSSL_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN25 */
#define IFX_HSSL_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN25 */
#define IFX_HSSL_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN25 */
#define IFX_HSSL_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN26 */
#define IFX_HSSL_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN26 */
#define IFX_HSSL_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN26 */
#define IFX_HSSL_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN27 */
#define IFX_HSSL_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN27 */
#define IFX_HSSL_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN27 */
#define IFX_HSSL_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN28 */
#define IFX_HSSL_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN28 */
#define IFX_HSSL_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN28 */
#define IFX_HSSL_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN29 */
#define IFX_HSSL_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN29 */
#define IFX_HSSL_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN29 */
#define IFX_HSSL_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN2 */
#define IFX_HSSL_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN2 */
#define IFX_HSSL_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN2 */
#define IFX_HSSL_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN30 */
#define IFX_HSSL_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN30 */
#define IFX_HSSL_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN30 */
#define IFX_HSSL_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN31 */
#define IFX_HSSL_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN31 */
#define IFX_HSSL_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN31 */
#define IFX_HSSL_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN3 */
#define IFX_HSSL_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN3 */
#define IFX_HSSL_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN3 */
#define IFX_HSSL_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN4 */
#define IFX_HSSL_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN4 */
#define IFX_HSSL_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN4 */
#define IFX_HSSL_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN5 */
#define IFX_HSSL_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN5 */
#define IFX_HSSL_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN5 */
#define IFX_HSSL_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN6 */
#define IFX_HSSL_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN6 */
#define IFX_HSSL_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN6 */
#define IFX_HSSL_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN7 */
#define IFX_HSSL_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN7 */
#define IFX_HSSL_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN7 */
#define IFX_HSSL_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN8 */
#define IFX_HSSL_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN8 */
#define IFX_HSSL_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN8 */
#define IFX_HSSL_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_HSSL_ACCEN0_Bits.EN9 */
#define IFX_HSSL_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_HSSL_ACCEN0_Bits.EN9 */
#define IFX_HSSL_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_ACCEN0_Bits.EN9 */
#define IFX_HSSL_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_HSSL_AR_Bits.ARW0 */
#define IFX_HSSL_AR_ARW0_LEN (2u)

/** \brief  Mask for Ifx_HSSL_AR_Bits.ARW0 */
#define IFX_HSSL_AR_ARW0_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_AR_Bits.ARW0 */
#define IFX_HSSL_AR_ARW0_OFF (0u)

/** \brief  Length for Ifx_HSSL_AR_Bits.ARW1 */
#define IFX_HSSL_AR_ARW1_LEN (2u)

/** \brief  Mask for Ifx_HSSL_AR_Bits.ARW1 */
#define IFX_HSSL_AR_ARW1_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_AR_Bits.ARW1 */
#define IFX_HSSL_AR_ARW1_OFF (2u)

/** \brief  Length for Ifx_HSSL_AR_Bits.ARW2 */
#define IFX_HSSL_AR_ARW2_LEN (2u)

/** \brief  Mask for Ifx_HSSL_AR_Bits.ARW2 */
#define IFX_HSSL_AR_ARW2_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_AR_Bits.ARW2 */
#define IFX_HSSL_AR_ARW2_OFF (4u)

/** \brief  Length for Ifx_HSSL_AR_Bits.ARW3 */
#define IFX_HSSL_AR_ARW3_LEN (2u)

/** \brief  Mask for Ifx_HSSL_AR_Bits.ARW3 */
#define IFX_HSSL_AR_ARW3_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_AR_Bits.ARW3 */
#define IFX_HSSL_AR_ARW3_OFF (6u)

/** \brief  Length for Ifx_HSSL_AR_Bits.MAVCH */
#define IFX_HSSL_AR_MAVCH_LEN (2u)

/** \brief  Mask for Ifx_HSSL_AR_Bits.MAVCH */
#define IFX_HSSL_AR_MAVCH_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_AR_Bits.MAVCH */
#define IFX_HSSL_AR_MAVCH_OFF (16u)

/** \brief  Length for Ifx_HSSL_AW_AWEND_Bits.AWE */
#define IFX_HSSL_AW_AWEND_AWE_LEN (24u)

/** \brief  Mask for Ifx_HSSL_AW_AWEND_Bits.AWE */
#define IFX_HSSL_AW_AWEND_AWE_MSK (0xffffffu)

/** \brief  Offset for Ifx_HSSL_AW_AWEND_Bits.AWE */
#define IFX_HSSL_AW_AWEND_AWE_OFF (8u)

/** \brief  Length for Ifx_HSSL_AW_AWSTART_Bits.AWS */
#define IFX_HSSL_AW_AWSTART_AWS_LEN (24u)

/** \brief  Mask for Ifx_HSSL_AW_AWSTART_Bits.AWS */
#define IFX_HSSL_AW_AWSTART_AWS_MSK (0xffffffu)

/** \brief  Offset for Ifx_HSSL_AW_AWSTART_Bits.AWS */
#define IFX_HSSL_AW_AWSTART_AWS_OFF (8u)

/** \brief  Length for Ifx_HSSL_CFG_Bits.CCC */
#define IFX_HSSL_CFG_CCC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CFG_Bits.CCC */
#define IFX_HSSL_CFG_CCC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CFG_Bits.CCC */
#define IFX_HSSL_CFG_CCC_OFF (19u)

/** \brief  Length for Ifx_HSSL_CFG_Bits.PREDIV */
#define IFX_HSSL_CFG_PREDIV_LEN (14u)

/** \brief  Mask for Ifx_HSSL_CFG_Bits.PREDIV */
#define IFX_HSSL_CFG_PREDIV_MSK (0x3fffu)

/** \brief  Offset for Ifx_HSSL_CFG_Bits.PREDIV */
#define IFX_HSSL_CFG_PREDIV_OFF (0u)

/** \brief  Length for Ifx_HSSL_CFG_Bits.SCM */
#define IFX_HSSL_CFG_SCM_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CFG_Bits.SCM */
#define IFX_HSSL_CFG_SCM_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CFG_Bits.SCM */
#define IFX_HSSL_CFG_SCM_OFF (18u)

/** \brief  Length for Ifx_HSSL_CFG_Bits.SMR */
#define IFX_HSSL_CFG_SMR_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CFG_Bits.SMR */
#define IFX_HSSL_CFG_SMR_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CFG_Bits.SMR */
#define IFX_HSSL_CFG_SMR_OFF (17u)

/** \brief  Length for Ifx_HSSL_CFG_Bits.SMT */
#define IFX_HSSL_CFG_SMT_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CFG_Bits.SMT */
#define IFX_HSSL_CFG_SMT_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CFG_Bits.SMT */
#define IFX_HSSL_CFG_SMT_OFF (16u)

/** \brief  Length for Ifx_HSSL_CLC_Bits.DISR */
#define IFX_HSSL_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CLC_Bits.DISR */
#define IFX_HSSL_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CLC_Bits.DISR */
#define IFX_HSSL_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_HSSL_CLC_Bits.DISS */
#define IFX_HSSL_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CLC_Bits.DISS */
#define IFX_HSSL_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CLC_Bits.DISS */
#define IFX_HSSL_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_HSSL_CLC_Bits.EDIS */
#define IFX_HSSL_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CLC_Bits.EDIS */
#define IFX_HSSL_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CLC_Bits.EDIS */
#define IFX_HSSL_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_HSSL_CRC_Bits.XEN */
#define IFX_HSSL_CRC_XEN_LEN (1u)

/** \brief  Mask for Ifx_HSSL_CRC_Bits.XEN */
#define IFX_HSSL_CRC_XEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_CRC_Bits.XEN */
#define IFX_HSSL_CRC_XEN_OFF (16u)

/** \brief  Length for Ifx_HSSL_CRC_Bits.XORMASK */
#define IFX_HSSL_CRC_XORMASK_LEN (16u)

/** \brief  Mask for Ifx_HSSL_CRC_Bits.XORMASK */
#define IFX_HSSL_CRC_XORMASK_MSK (0xffffu)

/** \brief  Offset for Ifx_HSSL_CRC_Bits.XORMASK */
#define IFX_HSSL_CRC_XORMASK_OFF (0u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.BSY */
#define IFX_HSSL_I_ICON_BSY_LEN (1u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.BSY */
#define IFX_HSSL_I_ICON_BSY_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.BSY */
#define IFX_HSSL_I_ICON_BSY_OFF (20u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.CETT */
#define IFX_HSSL_I_ICON_CETT_LEN (3u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.CETT */
#define IFX_HSSL_I_ICON_CETT_MSK (0x7u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.CETT */
#define IFX_HSSL_I_ICON_CETT_OFF (5u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.DATLEN */
#define IFX_HSSL_I_ICON_DATLEN_LEN (2u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.DATLEN */
#define IFX_HSSL_I_ICON_DATLEN_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.DATLEN */
#define IFX_HSSL_I_ICON_DATLEN_OFF (16u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.IDQ */
#define IFX_HSSL_I_ICON_IDQ_LEN (1u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.IDQ */
#define IFX_HSSL_I_ICON_IDQ_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.IDQ */
#define IFX_HSSL_I_ICON_IDQ_OFF (0u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.ITTAG */
#define IFX_HSSL_I_ICON_ITTAG_LEN (3u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.ITTAG */
#define IFX_HSSL_I_ICON_ITTAG_MSK (0x7u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.ITTAG */
#define IFX_HSSL_I_ICON_ITTAG_OFF (21u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.LETT */
#define IFX_HSSL_I_ICON_LETT_LEN (3u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.LETT */
#define IFX_HSSL_I_ICON_LETT_MSK (0x7u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.LETT */
#define IFX_HSSL_I_ICON_LETT_OFF (2u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.RWT */
#define IFX_HSSL_I_ICON_RWT_LEN (2u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.RWT */
#define IFX_HSSL_I_ICON_RWT_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.RWT */
#define IFX_HSSL_I_ICON_RWT_OFF (18u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.TOCV */
#define IFX_HSSL_I_ICON_TOCV_LEN (8u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.TOCV */
#define IFX_HSSL_I_ICON_TOCV_MSK (0xffu)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.TOCV */
#define IFX_HSSL_I_ICON_TOCV_OFF (8u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.TOREL */
#define IFX_HSSL_I_ICON_TOREL_LEN (8u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.TOREL */
#define IFX_HSSL_I_ICON_TOREL_MSK (0xffu)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.TOREL */
#define IFX_HSSL_I_ICON_TOREL_OFF (24u)

/** \brief  Length for Ifx_HSSL_I_ICON_Bits.TQ */
#define IFX_HSSL_I_ICON_TQ_LEN (1u)

/** \brief  Mask for Ifx_HSSL_I_ICON_Bits.TQ */
#define IFX_HSSL_I_ICON_TQ_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_I_ICON_Bits.TQ */
#define IFX_HSSL_I_ICON_TQ_OFF (1u)

/** \brief  Length for Ifx_HSSL_I_IRD_Bits.DATA */
#define IFX_HSSL_I_IRD_DATA_LEN (32u)

/** \brief  Mask for Ifx_HSSL_I_IRD_Bits.DATA */
#define IFX_HSSL_I_IRD_DATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSSL_I_IRD_Bits.DATA */
#define IFX_HSSL_I_IRD_DATA_OFF (0u)

/** \brief  Length for Ifx_HSSL_I_IRWA_Bits.ADDRESS */
#define IFX_HSSL_I_IRWA_ADDRESS_LEN (32u)

/** \brief  Mask for Ifx_HSSL_I_IRWA_Bits.ADDRESS */
#define IFX_HSSL_I_IRWA_ADDRESS_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSSL_I_IRWA_Bits.ADDRESS */
#define IFX_HSSL_I_IRWA_ADDRESS_OFF (0u)

/** \brief  Length for Ifx_HSSL_I_IWD_Bits.DATA */
#define IFX_HSSL_I_IWD_DATA_LEN (32u)

/** \brief  Mask for Ifx_HSSL_I_IWD_Bits.DATA */
#define IFX_HSSL_I_IWD_DATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSSL_I_IWD_Bits.DATA */
#define IFX_HSSL_I_IWD_DATA_OFF (0u)

/** \brief  Length for Ifx_HSSL_ID_Bits.MODNUMBER */
#define IFX_HSSL_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_HSSL_ID_Bits.MODNUMBER */
#define IFX_HSSL_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_HSSL_ID_Bits.MODNUMBER */
#define IFX_HSSL_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_HSSL_ID_Bits.MODREV */
#define IFX_HSSL_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_HSSL_ID_Bits.MODREV */
#define IFX_HSSL_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_HSSL_ID_Bits.MODREV */
#define IFX_HSSL_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_HSSL_ID_Bits.MODTYPE */
#define IFX_HSSL_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_HSSL_ID_Bits.MODTYPE */
#define IFX_HSSL_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_HSSL_ID_Bits.MODTYPE */
#define IFX_HSSL_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_HSSL_IS_CA_Bits.CURR */
#define IFX_HSSL_IS_CA_CURR_LEN (27u)

/** \brief  Mask for Ifx_HSSL_IS_CA_Bits.CURR */
#define IFX_HSSL_IS_CA_CURR_MSK (0x7ffffffu)

/** \brief  Offset for Ifx_HSSL_IS_CA_Bits.CURR */
#define IFX_HSSL_IS_CA_CURR_OFF (5u)

/** \brief  Length for Ifx_HSSL_IS_FC_Bits.CURCOUNT */
#define IFX_HSSL_IS_FC_CURCOUNT_LEN (16u)

/** \brief  Mask for Ifx_HSSL_IS_FC_Bits.CURCOUNT */
#define IFX_HSSL_IS_FC_CURCOUNT_MSK (0xffffu)

/** \brief  Offset for Ifx_HSSL_IS_FC_Bits.CURCOUNT */
#define IFX_HSSL_IS_FC_CURCOUNT_OFF (16u)

/** \brief  Length for Ifx_HSSL_IS_FC_Bits.RELCOUNT */
#define IFX_HSSL_IS_FC_RELCOUNT_LEN (16u)

/** \brief  Mask for Ifx_HSSL_IS_FC_Bits.RELCOUNT */
#define IFX_HSSL_IS_FC_RELCOUNT_MSK (0xffffu)

/** \brief  Offset for Ifx_HSSL_IS_FC_Bits.RELCOUNT */
#define IFX_HSSL_IS_FC_RELCOUNT_OFF (0u)

/** \brief  Length for Ifx_HSSL_ISSA_Bits.START */
#define IFX_HSSL_ISSA_START_LEN (27u)

/** \brief  Mask for Ifx_HSSL_ISSA_Bits.START */
#define IFX_HSSL_ISSA_START_MSK (0x7ffffffu)

/** \brief  Offset for Ifx_HSSL_ISSA_Bits.START */
#define IFX_HSSL_ISSA_START_OFF (5u)

/** \brief  Length for Ifx_HSSL_KRST0_Bits.RST */
#define IFX_HSSL_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_HSSL_KRST0_Bits.RST */
#define IFX_HSSL_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_KRST0_Bits.RST */
#define IFX_HSSL_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_HSSL_KRST0_Bits.RSTSTAT */
#define IFX_HSSL_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_HSSL_KRST0_Bits.RSTSTAT */
#define IFX_HSSL_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_KRST0_Bits.RSTSTAT */
#define IFX_HSSL_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_HSSL_KRST1_Bits.RST */
#define IFX_HSSL_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_HSSL_KRST1_Bits.RST */
#define IFX_HSSL_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_KRST1_Bits.RST */
#define IFX_HSSL_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_HSSL_KRSTCLR_Bits.CLR */
#define IFX_HSSL_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_HSSL_KRSTCLR_Bits.CLR */
#define IFX_HSSL_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_KRSTCLR_Bits.CLR */
#define IFX_HSSL_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.CRCE */
#define IFX_HSSL_MFLAGS_CRCE_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.CRCE */
#define IFX_HSSL_MFLAGS_CRCE_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.CRCE */
#define IFX_HSSL_MFLAGS_CRCE_OFF (25u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.IMB */
#define IFX_HSSL_MFLAGS_IMB_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.IMB */
#define IFX_HSSL_MFLAGS_IMB_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.IMB */
#define IFX_HSSL_MFLAGS_IMB_OFF (19u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.INI */
#define IFX_HSSL_MFLAGS_INI_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.INI */
#define IFX_HSSL_MFLAGS_INI_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.INI */
#define IFX_HSSL_MFLAGS_INI_OFF (31u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.ISB */
#define IFX_HSSL_MFLAGS_ISB_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.ISB */
#define IFX_HSSL_MFLAGS_ISB_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.ISB */
#define IFX_HSSL_MFLAGS_ISB_OFF (20u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.MAV */
#define IFX_HSSL_MFLAGS_MAV_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.MAV */
#define IFX_HSSL_MFLAGS_MAV_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.MAV */
#define IFX_HSSL_MFLAGS_MAV_OFF (21u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.NACK */
#define IFX_HSSL_MFLAGS_NACK_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.NACK */
#define IFX_HSSL_MFLAGS_NACK_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.NACK */
#define IFX_HSSL_MFLAGS_NACK_OFF (0u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.PIE1 */
#define IFX_HSSL_MFLAGS_PIE1_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.PIE1 */
#define IFX_HSSL_MFLAGS_PIE1_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.PIE1 */
#define IFX_HSSL_MFLAGS_PIE1_OFF (23u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.PIE2 */
#define IFX_HSSL_MFLAGS_PIE2_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.PIE2 */
#define IFX_HSSL_MFLAGS_PIE2_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.PIE2 */
#define IFX_HSSL_MFLAGS_PIE2_OFF (24u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.SRIE */
#define IFX_HSSL_MFLAGS_SRIE_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.SRIE */
#define IFX_HSSL_MFLAGS_SRIE_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.SRIE */
#define IFX_HSSL_MFLAGS_SRIE_OFF (22u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.TEI */
#define IFX_HSSL_MFLAGS_TEI_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.TEI */
#define IFX_HSSL_MFLAGS_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.TEI */
#define IFX_HSSL_MFLAGS_TEI_OFF (29u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.TEO */
#define IFX_HSSL_MFLAGS_TEO_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.TEO */
#define IFX_HSSL_MFLAGS_TEO_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.TEO */
#define IFX_HSSL_MFLAGS_TEO_OFF (30u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.TIMEOUT */
#define IFX_HSSL_MFLAGS_TIMEOUT_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.TIMEOUT */
#define IFX_HSSL_MFLAGS_TIMEOUT_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.TIMEOUT */
#define IFX_HSSL_MFLAGS_TIMEOUT_OFF (8u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.TMB */
#define IFX_HSSL_MFLAGS_TMB_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.TMB */
#define IFX_HSSL_MFLAGS_TMB_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.TMB */
#define IFX_HSSL_MFLAGS_TMB_OFF (18u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.TSE */
#define IFX_HSSL_MFLAGS_TSE_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.TSE */
#define IFX_HSSL_MFLAGS_TSE_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.TSE */
#define IFX_HSSL_MFLAGS_TSE_OFF (28u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.TTE */
#define IFX_HSSL_MFLAGS_TTE_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.TTE */
#define IFX_HSSL_MFLAGS_TTE_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.TTE */
#define IFX_HSSL_MFLAGS_TTE_OFF (4u)

/** \brief  Length for Ifx_HSSL_MFLAGS_Bits.UNEXPECTED */
#define IFX_HSSL_MFLAGS_UNEXPECTED_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGS_Bits.UNEXPECTED */
#define IFX_HSSL_MFLAGS_UNEXPECTED_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGS_Bits.UNEXPECTED */
#define IFX_HSSL_MFLAGS_UNEXPECTED_OFF (12u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.CRCEC */
#define IFX_HSSL_MFLAGSCL_CRCEC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.CRCEC */
#define IFX_HSSL_MFLAGSCL_CRCEC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.CRCEC */
#define IFX_HSSL_MFLAGSCL_CRCEC_OFF (25u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.IMBC */
#define IFX_HSSL_MFLAGSCL_IMBC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.IMBC */
#define IFX_HSSL_MFLAGSCL_IMBC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.IMBC */
#define IFX_HSSL_MFLAGSCL_IMBC_OFF (19u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.INIC */
#define IFX_HSSL_MFLAGSCL_INIC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.INIC */
#define IFX_HSSL_MFLAGSCL_INIC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.INIC */
#define IFX_HSSL_MFLAGSCL_INIC_OFF (31u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.ISBC */
#define IFX_HSSL_MFLAGSCL_ISBC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.ISBC */
#define IFX_HSSL_MFLAGSCL_ISBC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.ISBC */
#define IFX_HSSL_MFLAGSCL_ISBC_OFF (20u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.MAVC */
#define IFX_HSSL_MFLAGSCL_MAVC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.MAVC */
#define IFX_HSSL_MFLAGSCL_MAVC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.MAVC */
#define IFX_HSSL_MFLAGSCL_MAVC_OFF (21u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.NACKC */
#define IFX_HSSL_MFLAGSCL_NACKC_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.NACKC */
#define IFX_HSSL_MFLAGSCL_NACKC_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.NACKC */
#define IFX_HSSL_MFLAGSCL_NACKC_OFF (0u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.PIE1C */
#define IFX_HSSL_MFLAGSCL_PIE1C_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.PIE1C */
#define IFX_HSSL_MFLAGSCL_PIE1C_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.PIE1C */
#define IFX_HSSL_MFLAGSCL_PIE1C_OFF (23u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.PIE2C */
#define IFX_HSSL_MFLAGSCL_PIE2C_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.PIE2C */
#define IFX_HSSL_MFLAGSCL_PIE2C_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.PIE2C */
#define IFX_HSSL_MFLAGSCL_PIE2C_OFF (24u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.SRIEC */
#define IFX_HSSL_MFLAGSCL_SRIEC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.SRIEC */
#define IFX_HSSL_MFLAGSCL_SRIEC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.SRIEC */
#define IFX_HSSL_MFLAGSCL_SRIEC_OFF (22u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.TEOC */
#define IFX_HSSL_MFLAGSCL_TEOC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.TEOC */
#define IFX_HSSL_MFLAGSCL_TEOC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.TEOC */
#define IFX_HSSL_MFLAGSCL_TEOC_OFF (30u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.TIMEOUTC */
#define IFX_HSSL_MFLAGSCL_TIMEOUTC_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.TIMEOUTC */
#define IFX_HSSL_MFLAGSCL_TIMEOUTC_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.TIMEOUTC */
#define IFX_HSSL_MFLAGSCL_TIMEOUTC_OFF (8u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.TMBC */
#define IFX_HSSL_MFLAGSCL_TMBC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.TMBC */
#define IFX_HSSL_MFLAGSCL_TMBC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.TMBC */
#define IFX_HSSL_MFLAGSCL_TMBC_OFF (18u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.TSEC */
#define IFX_HSSL_MFLAGSCL_TSEC_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.TSEC */
#define IFX_HSSL_MFLAGSCL_TSEC_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.TSEC */
#define IFX_HSSL_MFLAGSCL_TSEC_OFF (28u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.TTEC */
#define IFX_HSSL_MFLAGSCL_TTEC_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.TTEC */
#define IFX_HSSL_MFLAGSCL_TTEC_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.TTEC */
#define IFX_HSSL_MFLAGSCL_TTEC_OFF (4u)

/** \brief  Length for Ifx_HSSL_MFLAGSCL_Bits.UNEXPECTEDC */
#define IFX_HSSL_MFLAGSCL_UNEXPECTEDC_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSCL_Bits.UNEXPECTEDC */
#define IFX_HSSL_MFLAGSCL_UNEXPECTEDC_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSCL_Bits.UNEXPECTEDC */
#define IFX_HSSL_MFLAGSCL_UNEXPECTEDC_OFF (12u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.CRCEEN */
#define IFX_HSSL_MFLAGSEN_CRCEEN_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.CRCEEN */
#define IFX_HSSL_MFLAGSEN_CRCEEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.CRCEEN */
#define IFX_HSSL_MFLAGSEN_CRCEEN_OFF (25u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.MAVEN */
#define IFX_HSSL_MFLAGSEN_MAVEN_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.MAVEN */
#define IFX_HSSL_MFLAGSEN_MAVEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.MAVEN */
#define IFX_HSSL_MFLAGSEN_MAVEN_OFF (21u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.NACKEN */
#define IFX_HSSL_MFLAGSEN_NACKEN_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.NACKEN */
#define IFX_HSSL_MFLAGSEN_NACKEN_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.NACKEN */
#define IFX_HSSL_MFLAGSEN_NACKEN_OFF (0u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.PIE1EN */
#define IFX_HSSL_MFLAGSEN_PIE1EN_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.PIE1EN */
#define IFX_HSSL_MFLAGSEN_PIE1EN_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.PIE1EN */
#define IFX_HSSL_MFLAGSEN_PIE1EN_OFF (23u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.PIE2EN */
#define IFX_HSSL_MFLAGSEN_PIE2EN_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.PIE2EN */
#define IFX_HSSL_MFLAGSEN_PIE2EN_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.PIE2EN */
#define IFX_HSSL_MFLAGSEN_PIE2EN_OFF (24u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.SRIEEN */
#define IFX_HSSL_MFLAGSEN_SRIEEN_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.SRIEEN */
#define IFX_HSSL_MFLAGSEN_SRIEEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.SRIEEN */
#define IFX_HSSL_MFLAGSEN_SRIEEN_OFF (22u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.TEIEN */
#define IFX_HSSL_MFLAGSEN_TEIEN_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.TEIEN */
#define IFX_HSSL_MFLAGSEN_TEIEN_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.TEIEN */
#define IFX_HSSL_MFLAGSEN_TEIEN_OFF (29u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.TIMEOUTEN */
#define IFX_HSSL_MFLAGSEN_TIMEOUTEN_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.TIMEOUTEN */
#define IFX_HSSL_MFLAGSEN_TIMEOUTEN_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.TIMEOUTEN */
#define IFX_HSSL_MFLAGSEN_TIMEOUTEN_OFF (8u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.TTEEN */
#define IFX_HSSL_MFLAGSEN_TTEEN_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.TTEEN */
#define IFX_HSSL_MFLAGSEN_TTEEN_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.TTEEN */
#define IFX_HSSL_MFLAGSEN_TTEEN_OFF (4u)

/** \brief  Length for Ifx_HSSL_MFLAGSEN_Bits.UNEXPECTEDEN */
#define IFX_HSSL_MFLAGSEN_UNEXPECTEDEN_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSEN_Bits.UNEXPECTEDEN */
#define IFX_HSSL_MFLAGSEN_UNEXPECTEDEN_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSEN_Bits.UNEXPECTEDEN */
#define IFX_HSSL_MFLAGSEN_UNEXPECTEDEN_OFF (12u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.CRCES */
#define IFX_HSSL_MFLAGSSET_CRCES_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.CRCES */
#define IFX_HSSL_MFLAGSSET_CRCES_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.CRCES */
#define IFX_HSSL_MFLAGSSET_CRCES_OFF (25u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.IMBS */
#define IFX_HSSL_MFLAGSSET_IMBS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.IMBS */
#define IFX_HSSL_MFLAGSSET_IMBS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.IMBS */
#define IFX_HSSL_MFLAGSSET_IMBS_OFF (19u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.INIS */
#define IFX_HSSL_MFLAGSSET_INIS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.INIS */
#define IFX_HSSL_MFLAGSSET_INIS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.INIS */
#define IFX_HSSL_MFLAGSSET_INIS_OFF (31u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.ISBS */
#define IFX_HSSL_MFLAGSSET_ISBS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.ISBS */
#define IFX_HSSL_MFLAGSSET_ISBS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.ISBS */
#define IFX_HSSL_MFLAGSSET_ISBS_OFF (20u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.MAVS */
#define IFX_HSSL_MFLAGSSET_MAVS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.MAVS */
#define IFX_HSSL_MFLAGSSET_MAVS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.MAVS */
#define IFX_HSSL_MFLAGSSET_MAVS_OFF (21u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.NACKS */
#define IFX_HSSL_MFLAGSSET_NACKS_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.NACKS */
#define IFX_HSSL_MFLAGSSET_NACKS_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.NACKS */
#define IFX_HSSL_MFLAGSSET_NACKS_OFF (0u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.PIE1S */
#define IFX_HSSL_MFLAGSSET_PIE1S_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.PIE1S */
#define IFX_HSSL_MFLAGSSET_PIE1S_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.PIE1S */
#define IFX_HSSL_MFLAGSSET_PIE1S_OFF (23u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.PIE2S */
#define IFX_HSSL_MFLAGSSET_PIE2S_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.PIE2S */
#define IFX_HSSL_MFLAGSSET_PIE2S_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.PIE2S */
#define IFX_HSSL_MFLAGSSET_PIE2S_OFF (24u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.SRIES */
#define IFX_HSSL_MFLAGSSET_SRIES_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.SRIES */
#define IFX_HSSL_MFLAGSSET_SRIES_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.SRIES */
#define IFX_HSSL_MFLAGSSET_SRIES_OFF (22u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.TEOS */
#define IFX_HSSL_MFLAGSSET_TEOS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.TEOS */
#define IFX_HSSL_MFLAGSSET_TEOS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.TEOS */
#define IFX_HSSL_MFLAGSSET_TEOS_OFF (30u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.TIMEOUTS */
#define IFX_HSSL_MFLAGSSET_TIMEOUTS_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.TIMEOUTS */
#define IFX_HSSL_MFLAGSSET_TIMEOUTS_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.TIMEOUTS */
#define IFX_HSSL_MFLAGSSET_TIMEOUTS_OFF (8u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.TMBS */
#define IFX_HSSL_MFLAGSSET_TMBS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.TMBS */
#define IFX_HSSL_MFLAGSSET_TMBS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.TMBS */
#define IFX_HSSL_MFLAGSSET_TMBS_OFF (18u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.TSES */
#define IFX_HSSL_MFLAGSSET_TSES_LEN (1u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.TSES */
#define IFX_HSSL_MFLAGSSET_TSES_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.TSES */
#define IFX_HSSL_MFLAGSSET_TSES_OFF (28u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.TTES */
#define IFX_HSSL_MFLAGSSET_TTES_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.TTES */
#define IFX_HSSL_MFLAGSSET_TTES_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.TTES */
#define IFX_HSSL_MFLAGSSET_TTES_OFF (4u)

/** \brief  Length for Ifx_HSSL_MFLAGSSET_Bits.UNEXPECTEDS */
#define IFX_HSSL_MFLAGSSET_UNEXPECTEDS_LEN (4u)

/** \brief  Mask for Ifx_HSSL_MFLAGSSET_Bits.UNEXPECTEDS */
#define IFX_HSSL_MFLAGSSET_UNEXPECTEDS_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_MFLAGSSET_Bits.UNEXPECTEDS */
#define IFX_HSSL_MFLAGSSET_UNEXPECTEDS_OFF (12u)

/** \brief  Length for Ifx_HSSL_OCS_Bits.SUS */
#define IFX_HSSL_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_HSSL_OCS_Bits.SUS */
#define IFX_HSSL_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_OCS_Bits.SUS */
#define IFX_HSSL_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_HSSL_OCS_Bits.SUS_P */
#define IFX_HSSL_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_HSSL_OCS_Bits.SUS_P */
#define IFX_HSSL_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_OCS_Bits.SUS_P */
#define IFX_HSSL_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_HSSL_OCS_Bits.SUSSTA */
#define IFX_HSSL_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_HSSL_OCS_Bits.SUSSTA */
#define IFX_HSSL_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_OCS_Bits.SUSSTA */
#define IFX_HSSL_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_HSSL_OCS_Bits.TG_P */
#define IFX_HSSL_OCS_TG_P_LEN (1u)

/** \brief  Mask for Ifx_HSSL_OCS_Bits.TG_P */
#define IFX_HSSL_OCS_TG_P_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_OCS_Bits.TG_P */
#define IFX_HSSL_OCS_TG_P_OFF (3u)

/** \brief  Length for Ifx_HSSL_OCS_Bits.TGB */
#define IFX_HSSL_OCS_TGB_LEN (1u)

/** \brief  Mask for Ifx_HSSL_OCS_Bits.TGB */
#define IFX_HSSL_OCS_TGB_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_OCS_Bits.TGB */
#define IFX_HSSL_OCS_TGB_OFF (2u)

/** \brief  Length for Ifx_HSSL_OCS_Bits.TGS */
#define IFX_HSSL_OCS_TGS_LEN (2u)

/** \brief  Mask for Ifx_HSSL_OCS_Bits.TGS */
#define IFX_HSSL_OCS_TGS_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_OCS_Bits.TGS */
#define IFX_HSSL_OCS_TGS_OFF (0u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.E0 */
#define IFX_HSSL_QFLAGS_E0_LEN (2u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.E0 */
#define IFX_HSSL_QFLAGS_E0_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.E0 */
#define IFX_HSSL_QFLAGS_E0_OFF (16u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.E1 */
#define IFX_HSSL_QFLAGS_E1_LEN (2u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.E1 */
#define IFX_HSSL_QFLAGS_E1_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.E1 */
#define IFX_HSSL_QFLAGS_E1_OFF (18u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.E2 */
#define IFX_HSSL_QFLAGS_E2_LEN (2u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.E2 */
#define IFX_HSSL_QFLAGS_E2_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.E2 */
#define IFX_HSSL_QFLAGS_E2_OFF (20u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.E3 */
#define IFX_HSSL_QFLAGS_E3_LEN (2u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.E3 */
#define IFX_HSSL_QFLAGS_E3_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.E3 */
#define IFX_HSSL_QFLAGS_E3_OFF (22u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.ES */
#define IFX_HSSL_QFLAGS_ES_LEN (1u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.ES */
#define IFX_HSSL_QFLAGS_ES_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.ES */
#define IFX_HSSL_QFLAGS_ES_OFF (31u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.I */
#define IFX_HSSL_QFLAGS_I_LEN (4u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.I */
#define IFX_HSSL_QFLAGS_I_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.I */
#define IFX_HSSL_QFLAGS_I_OFF (0u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.IS */
#define IFX_HSSL_QFLAGS_IS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.IS */
#define IFX_HSSL_QFLAGS_IS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.IS */
#define IFX_HSSL_QFLAGS_IS_OFF (28u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.R */
#define IFX_HSSL_QFLAGS_R_LEN (4u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.R */
#define IFX_HSSL_QFLAGS_R_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.R */
#define IFX_HSSL_QFLAGS_R_OFF (8u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.RS */
#define IFX_HSSL_QFLAGS_RS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.RS */
#define IFX_HSSL_QFLAGS_RS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.RS */
#define IFX_HSSL_QFLAGS_RS_OFF (29u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.T */
#define IFX_HSSL_QFLAGS_T_LEN (4u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.T */
#define IFX_HSSL_QFLAGS_T_MSK (0xfu)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.T */
#define IFX_HSSL_QFLAGS_T_OFF (4u)

/** \brief  Length for Ifx_HSSL_QFLAGS_Bits.TS */
#define IFX_HSSL_QFLAGS_TS_LEN (1u)

/** \brief  Mask for Ifx_HSSL_QFLAGS_Bits.TS */
#define IFX_HSSL_QFLAGS_TS_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_QFLAGS_Bits.TS */
#define IFX_HSSL_QFLAGS_TS_OFF (30u)

/** \brief  Length for Ifx_HSSL_SFSFLAGS_Bits.EXFL */
#define IFX_HSSL_SFSFLAGS_EXFL_LEN (2u)

/** \brief  Mask for Ifx_HSSL_SFSFLAGS_Bits.EXFL */
#define IFX_HSSL_SFSFLAGS_EXFL_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_SFSFLAGS_Bits.EXFL */
#define IFX_HSSL_SFSFLAGS_EXFL_OFF (4u)

/** \brief  Length for Ifx_HSSL_SFSFLAGS_Bits.ISF */
#define IFX_HSSL_SFSFLAGS_ISF_LEN (1u)

/** \brief  Mask for Ifx_HSSL_SFSFLAGS_Bits.ISF */
#define IFX_HSSL_SFSFLAGS_ISF_MSK (0x1u)

/** \brief  Offset for Ifx_HSSL_SFSFLAGS_Bits.ISF */
#define IFX_HSSL_SFSFLAGS_ISF_OFF (15u)

/** \brief  Length for Ifx_HSSL_SFSFLAGS_Bits.RXFL */
#define IFX_HSSL_SFSFLAGS_RXFL_LEN (2u)

/** \brief  Mask for Ifx_HSSL_SFSFLAGS_Bits.RXFL */
#define IFX_HSSL_SFSFLAGS_RXFL_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_SFSFLAGS_Bits.RXFL */
#define IFX_HSSL_SFSFLAGS_RXFL_OFF (0u)

/** \brief  Length for Ifx_HSSL_SFSFLAGS_Bits.TXFL */
#define IFX_HSSL_SFSFLAGS_TXFL_LEN (2u)

/** \brief  Mask for Ifx_HSSL_SFSFLAGS_Bits.TXFL */
#define IFX_HSSL_SFSFLAGS_TXFL_MSK (0x3u)

/** \brief  Offset for Ifx_HSSL_SFSFLAGS_Bits.TXFL */
#define IFX_HSSL_SFSFLAGS_TXFL_OFF (2u)

/** \brief  Length for Ifx_HSSL_T_TCA_Bits.A */
#define IFX_HSSL_T_TCA_A_LEN (32u)

/** \brief  Mask for Ifx_HSSL_T_TCA_Bits.A */
#define IFX_HSSL_T_TCA_A_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSSL_T_TCA_Bits.A */
#define IFX_HSSL_T_TCA_A_OFF (0u)

/** \brief  Length for Ifx_HSSL_T_TCD_Bits.D */
#define IFX_HSSL_T_TCD_D_LEN (32u)

/** \brief  Mask for Ifx_HSSL_T_TCD_Bits.D */
#define IFX_HSSL_T_TCD_D_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSSL_T_TCD_Bits.D */
#define IFX_HSSL_T_TCD_D_OFF (0u)

/** \brief  Length for Ifx_HSSL_TIDADD_Bits.A */
#define IFX_HSSL_TIDADD_A_LEN (32u)

/** \brief  Mask for Ifx_HSSL_TIDADD_Bits.A */
#define IFX_HSSL_TIDADD_A_MSK (0xffffffffu)

/** \brief  Offset for Ifx_HSSL_TIDADD_Bits.A */
#define IFX_HSSL_TIDADD_A_OFF (0u)

/** \brief  Length for Ifx_HSSL_TS_CA_Bits.CURR */
#define IFX_HSSL_TS_CA_CURR_LEN (27u)

/** \brief  Mask for Ifx_HSSL_TS_CA_Bits.CURR */
#define IFX_HSSL_TS_CA_CURR_MSK (0x7ffffffu)

/** \brief  Offset for Ifx_HSSL_TS_CA_Bits.CURR */
#define IFX_HSSL_TS_CA_CURR_OFF (5u)

/** \brief  Length for Ifx_HSSL_TS_FC_Bits.CURCOUNT */
#define IFX_HSSL_TS_FC_CURCOUNT_LEN (16u)

/** \brief  Mask for Ifx_HSSL_TS_FC_Bits.CURCOUNT */
#define IFX_HSSL_TS_FC_CURCOUNT_MSK (0xffffu)

/** \brief  Offset for Ifx_HSSL_TS_FC_Bits.CURCOUNT */
#define IFX_HSSL_TS_FC_CURCOUNT_OFF (16u)

/** \brief  Length for Ifx_HSSL_TS_FC_Bits.RELCOUNT */
#define IFX_HSSL_TS_FC_RELCOUNT_LEN (16u)

/** \brief  Mask for Ifx_HSSL_TS_FC_Bits.RELCOUNT */
#define IFX_HSSL_TS_FC_RELCOUNT_MSK (0xffffu)

/** \brief  Offset for Ifx_HSSL_TS_FC_Bits.RELCOUNT */
#define IFX_HSSL_TS_FC_RELCOUNT_OFF (0u)

/** \brief  Length for Ifx_HSSL_TSSA_Bits.ADDR */
#define IFX_HSSL_TSSA_ADDR_LEN (27u)

/** \brief  Mask for Ifx_HSSL_TSSA_Bits.ADDR */
#define IFX_HSSL_TSSA_ADDR_MSK (0x7ffffffu)

/** \brief  Offset for Ifx_HSSL_TSSA_Bits.ADDR */
#define IFX_HSSL_TSSA_ADDR_OFF (5u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTCC0 */
#define IFX_HSSL_TSTAT_LASTCC0_LEN (5u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTCC0 */
#define IFX_HSSL_TSTAT_LASTCC0_MSK (0x1fu)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTCC0 */
#define IFX_HSSL_TSTAT_LASTCC0_OFF (0u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTCC1 */
#define IFX_HSSL_TSTAT_LASTCC1_LEN (5u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTCC1 */
#define IFX_HSSL_TSTAT_LASTCC1_MSK (0x1fu)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTCC1 */
#define IFX_HSSL_TSTAT_LASTCC1_OFF (8u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTCC2 */
#define IFX_HSSL_TSTAT_LASTCC2_LEN (5u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTCC2 */
#define IFX_HSSL_TSTAT_LASTCC2_MSK (0x1fu)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTCC2 */
#define IFX_HSSL_TSTAT_LASTCC2_OFF (16u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTCC3 */
#define IFX_HSSL_TSTAT_LASTCC3_LEN (5u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTCC3 */
#define IFX_HSSL_TSTAT_LASTCC3_MSK (0x1fu)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTCC3 */
#define IFX_HSSL_TSTAT_LASTCC3_OFF (24u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTTT0 */
#define IFX_HSSL_TSTAT_LASTTT0_LEN (3u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTTT0 */
#define IFX_HSSL_TSTAT_LASTTT0_MSK (0x7u)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTTT0 */
#define IFX_HSSL_TSTAT_LASTTT0_OFF (5u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTTT1 */
#define IFX_HSSL_TSTAT_LASTTT1_LEN (3u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTTT1 */
#define IFX_HSSL_TSTAT_LASTTT1_MSK (0x7u)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTTT1 */
#define IFX_HSSL_TSTAT_LASTTT1_OFF (13u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTTT2 */
#define IFX_HSSL_TSTAT_LASTTT2_LEN (3u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTTT2 */
#define IFX_HSSL_TSTAT_LASTTT2_MSK (0x7u)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTTT2 */
#define IFX_HSSL_TSTAT_LASTTT2_OFF (21u)

/** \brief  Length for Ifx_HSSL_TSTAT_Bits.LASTTT3 */
#define IFX_HSSL_TSTAT_LASTTT3_LEN (3u)

/** \brief  Mask for Ifx_HSSL_TSTAT_Bits.LASTTT3 */
#define IFX_HSSL_TSTAT_LASTTT3_MSK (0x7u)

/** \brief  Offset for Ifx_HSSL_TSTAT_Bits.LASTTT3 */
#define IFX_HSSL_TSTAT_LASTTT3_OFF (29u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXHSSL_BF_H */
