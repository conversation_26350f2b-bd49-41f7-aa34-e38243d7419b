################################################################################
# Automatically-generated file. Do not edit!
################################################################################

C++_SRCS := 
CC_SRCS := 
CPP_SRCS := 
CXX_SRCS := 
C_SRCS := 
C_UPPER_SRCS := 
OBJ_SRCS := 
O_SRCS := 
SRC_SRCS := 
C++_DEPS := 
CC_DEPS := 
COMPILED_SRCS := 
CPP_DEPS := 
CXX_DEPS := 
C_DEPS := 
C_UPPER_DEPS := 
EXECUTABLES := 
OBJS := 
SECONDARY_SIZE := 

# Every subdirectory with source files must be described here
SUBDIRS := \
code \
code/user1 \
libraries/infineon_libraries/Infra/Platform/Tricore/Compilers \
libraries/infineon_libraries/Service/CpuGeneric/If \
libraries/infineon_libraries/Service/CpuGeneric/StdIf \
libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp \
libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Asc \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Irq \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Flash/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Src/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Vadc/Adc \
libraries/infineon_libraries/iLLD/TC26B/Tricore/Vadc/Std \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling \
libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap \
libraries/zf_common \
libraries/zf_components \
libraries/zf_device \
libraries/zf_driver \
user \

