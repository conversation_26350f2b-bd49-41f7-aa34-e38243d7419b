/**
 * \file IfxDsadc_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Dsadc_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Dsadc
 * 
 */
#ifndef IFXDSADC_BF_H
#define IFXDSADC_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Dsadc_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN0 */
#define IFX_DSADC_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN0 */
#define IFX_DSADC_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN0 */
#define IFX_DSADC_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN10 */
#define IFX_DSADC_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN10 */
#define IFX_DSADC_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN10 */
#define IFX_DSADC_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN11 */
#define IFX_DSADC_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN11 */
#define IFX_DSADC_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN11 */
#define IFX_DSADC_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN12 */
#define IFX_DSADC_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN12 */
#define IFX_DSADC_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN12 */
#define IFX_DSADC_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN13 */
#define IFX_DSADC_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN13 */
#define IFX_DSADC_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN13 */
#define IFX_DSADC_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN14 */
#define IFX_DSADC_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN14 */
#define IFX_DSADC_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN14 */
#define IFX_DSADC_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN15 */
#define IFX_DSADC_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN15 */
#define IFX_DSADC_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN15 */
#define IFX_DSADC_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN16 */
#define IFX_DSADC_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN16 */
#define IFX_DSADC_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN16 */
#define IFX_DSADC_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN17 */
#define IFX_DSADC_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN17 */
#define IFX_DSADC_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN17 */
#define IFX_DSADC_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN18 */
#define IFX_DSADC_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN18 */
#define IFX_DSADC_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN18 */
#define IFX_DSADC_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN19 */
#define IFX_DSADC_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN19 */
#define IFX_DSADC_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN19 */
#define IFX_DSADC_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN1 */
#define IFX_DSADC_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN1 */
#define IFX_DSADC_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN1 */
#define IFX_DSADC_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN20 */
#define IFX_DSADC_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN20 */
#define IFX_DSADC_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN20 */
#define IFX_DSADC_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN21 */
#define IFX_DSADC_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN21 */
#define IFX_DSADC_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN21 */
#define IFX_DSADC_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN22 */
#define IFX_DSADC_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN22 */
#define IFX_DSADC_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN22 */
#define IFX_DSADC_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN23 */
#define IFX_DSADC_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN23 */
#define IFX_DSADC_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN23 */
#define IFX_DSADC_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN24 */
#define IFX_DSADC_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN24 */
#define IFX_DSADC_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN24 */
#define IFX_DSADC_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN25 */
#define IFX_DSADC_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN25 */
#define IFX_DSADC_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN25 */
#define IFX_DSADC_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN26 */
#define IFX_DSADC_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN26 */
#define IFX_DSADC_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN26 */
#define IFX_DSADC_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN27 */
#define IFX_DSADC_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN27 */
#define IFX_DSADC_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN27 */
#define IFX_DSADC_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN28 */
#define IFX_DSADC_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN28 */
#define IFX_DSADC_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN28 */
#define IFX_DSADC_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN29 */
#define IFX_DSADC_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN29 */
#define IFX_DSADC_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN29 */
#define IFX_DSADC_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN2 */
#define IFX_DSADC_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN2 */
#define IFX_DSADC_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN2 */
#define IFX_DSADC_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN30 */
#define IFX_DSADC_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN30 */
#define IFX_DSADC_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN30 */
#define IFX_DSADC_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN31 */
#define IFX_DSADC_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN31 */
#define IFX_DSADC_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN31 */
#define IFX_DSADC_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN3 */
#define IFX_DSADC_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN3 */
#define IFX_DSADC_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN3 */
#define IFX_DSADC_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN4 */
#define IFX_DSADC_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN4 */
#define IFX_DSADC_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN4 */
#define IFX_DSADC_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN5 */
#define IFX_DSADC_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN5 */
#define IFX_DSADC_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN5 */
#define IFX_DSADC_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN6 */
#define IFX_DSADC_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN6 */
#define IFX_DSADC_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN6 */
#define IFX_DSADC_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN7 */
#define IFX_DSADC_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN7 */
#define IFX_DSADC_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN7 */
#define IFX_DSADC_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN8 */
#define IFX_DSADC_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN8 */
#define IFX_DSADC_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN8 */
#define IFX_DSADC_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_DSADC_ACCEN0_Bits.EN9 */
#define IFX_DSADC_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCEN0_Bits.EN9 */
#define IFX_DSADC_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCEN0_Bits.EN9 */
#define IFX_DSADC_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_DSADC_ACCPROT_Bits.RG00 */
#define IFX_DSADC_ACCPROT_RG00_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCPROT_Bits.RG00 */
#define IFX_DSADC_ACCPROT_RG00_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCPROT_Bits.RG00 */
#define IFX_DSADC_ACCPROT_RG00_OFF (0u)

/** \brief  Length for Ifx_DSADC_ACCPROT_Bits.RG01 */
#define IFX_DSADC_ACCPROT_RG01_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCPROT_Bits.RG01 */
#define IFX_DSADC_ACCPROT_RG01_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCPROT_Bits.RG01 */
#define IFX_DSADC_ACCPROT_RG01_OFF (1u)

/** \brief  Length for Ifx_DSADC_ACCPROT_Bits.RG02 */
#define IFX_DSADC_ACCPROT_RG02_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCPROT_Bits.RG02 */
#define IFX_DSADC_ACCPROT_RG02_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCPROT_Bits.RG02 */
#define IFX_DSADC_ACCPROT_RG02_OFF (2u)

/** \brief  Length for Ifx_DSADC_ACCPROT_Bits.RG03 */
#define IFX_DSADC_ACCPROT_RG03_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCPROT_Bits.RG03 */
#define IFX_DSADC_ACCPROT_RG03_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCPROT_Bits.RG03 */
#define IFX_DSADC_ACCPROT_RG03_OFF (3u)

/** \brief  Length for Ifx_DSADC_ACCPROT_Bits.RG04 */
#define IFX_DSADC_ACCPROT_RG04_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCPROT_Bits.RG04 */
#define IFX_DSADC_ACCPROT_RG04_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCPROT_Bits.RG04 */
#define IFX_DSADC_ACCPROT_RG04_OFF (4u)

/** \brief  Length for Ifx_DSADC_ACCPROT_Bits.RG10 */
#define IFX_DSADC_ACCPROT_RG10_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCPROT_Bits.RG10 */
#define IFX_DSADC_ACCPROT_RG10_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCPROT_Bits.RG10 */
#define IFX_DSADC_ACCPROT_RG10_OFF (14u)

/** \brief  Length for Ifx_DSADC_ACCPROT_Bits.RG11 */
#define IFX_DSADC_ACCPROT_RG11_LEN (1u)

/** \brief  Mask for Ifx_DSADC_ACCPROT_Bits.RG11 */
#define IFX_DSADC_ACCPROT_RG11_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_ACCPROT_Bits.RG11 */
#define IFX_DSADC_ACCPROT_RG11_OFF (15u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.BITCOUNT */
#define IFX_DSADC_CGCFG_BITCOUNT_LEN (5u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.BITCOUNT */
#define IFX_DSADC_CGCFG_BITCOUNT_MSK (0x1fu)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.BITCOUNT */
#define IFX_DSADC_CGCFG_BITCOUNT_OFF (16u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.BREV */
#define IFX_DSADC_CGCFG_BREV_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.BREV */
#define IFX_DSADC_CGCFG_BREV_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.BREV */
#define IFX_DSADC_CGCFG_BREV_OFF (2u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.CGMOD */
#define IFX_DSADC_CGCFG_CGMOD_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.CGMOD */
#define IFX_DSADC_CGCFG_CGMOD_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.CGMOD */
#define IFX_DSADC_CGCFG_CGMOD_OFF (0u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.DIVCG */
#define IFX_DSADC_CGCFG_DIVCG_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.DIVCG */
#define IFX_DSADC_CGCFG_DIVCG_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.DIVCG */
#define IFX_DSADC_CGCFG_DIVCG_OFF (4u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.RUN */
#define IFX_DSADC_CGCFG_RUN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.RUN */
#define IFX_DSADC_CGCFG_RUN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.RUN */
#define IFX_DSADC_CGCFG_RUN_OFF (15u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.SGNCG */
#define IFX_DSADC_CGCFG_SGNCG_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.SGNCG */
#define IFX_DSADC_CGCFG_SGNCG_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.SGNCG */
#define IFX_DSADC_CGCFG_SGNCG_OFF (30u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.SIGPOL */
#define IFX_DSADC_CGCFG_SIGPOL_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.SIGPOL */
#define IFX_DSADC_CGCFG_SIGPOL_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.SIGPOL */
#define IFX_DSADC_CGCFG_SIGPOL_OFF (3u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.STEPCOUNT */
#define IFX_DSADC_CGCFG_STEPCOUNT_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.STEPCOUNT */
#define IFX_DSADC_CGCFG_STEPCOUNT_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.STEPCOUNT */
#define IFX_DSADC_CGCFG_STEPCOUNT_OFF (24u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.STEPD */
#define IFX_DSADC_CGCFG_STEPD_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.STEPD */
#define IFX_DSADC_CGCFG_STEPD_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.STEPD */
#define IFX_DSADC_CGCFG_STEPD_OFF (29u)

/** \brief  Length for Ifx_DSADC_CGCFG_Bits.STEPS */
#define IFX_DSADC_CGCFG_STEPS_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CGCFG_Bits.STEPS */
#define IFX_DSADC_CGCFG_STEPS_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CGCFG_Bits.STEPS */
#define IFX_DSADC_CGCFG_STEPS_OFF (28u)

/** \brief  Length for Ifx_DSADC_CH_BOUNDSEL_Bits.BOUNDARYL */
#define IFX_DSADC_CH_BOUNDSEL_BOUNDARYL_LEN (16u)

/** \brief  Mask for Ifx_DSADC_CH_BOUNDSEL_Bits.BOUNDARYL */
#define IFX_DSADC_CH_BOUNDSEL_BOUNDARYL_MSK (0xffffu)

/** \brief  Offset for Ifx_DSADC_CH_BOUNDSEL_Bits.BOUNDARYL */
#define IFX_DSADC_CH_BOUNDSEL_BOUNDARYL_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_BOUNDSEL_Bits.BOUNDARYU */
#define IFX_DSADC_CH_BOUNDSEL_BOUNDARYU_LEN (16u)

/** \brief  Mask for Ifx_DSADC_CH_BOUNDSEL_Bits.BOUNDARYU */
#define IFX_DSADC_CH_BOUNDSEL_BOUNDARYU_MSK (0xffffu)

/** \brief  Offset for Ifx_DSADC_CH_BOUNDSEL_Bits.BOUNDARYU */
#define IFX_DSADC_CH_BOUNDSEL_BOUNDARYU_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_CGSYNC_Bits.SDCAP */
#define IFX_DSADC_CH_CGSYNC_SDCAP_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_CGSYNC_Bits.SDCAP */
#define IFX_DSADC_CH_CGSYNC_SDCAP_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_CGSYNC_Bits.SDCAP */
#define IFX_DSADC_CH_CGSYNC_SDCAP_OFF (8u)

/** \brief  Length for Ifx_DSADC_CH_CGSYNC_Bits.SDCOUNT */
#define IFX_DSADC_CH_CGSYNC_SDCOUNT_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_CGSYNC_Bits.SDCOUNT */
#define IFX_DSADC_CH_CGSYNC_SDCOUNT_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_CGSYNC_Bits.SDCOUNT */
#define IFX_DSADC_CH_CGSYNC_SDCOUNT_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_CGSYNC_Bits.SDNEG */
#define IFX_DSADC_CH_CGSYNC_SDNEG_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_CGSYNC_Bits.SDNEG */
#define IFX_DSADC_CH_CGSYNC_SDNEG_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_CGSYNC_Bits.SDNEG */
#define IFX_DSADC_CH_CGSYNC_SDNEG_OFF (24u)

/** \brief  Length for Ifx_DSADC_CH_CGSYNC_Bits.SDPOS */
#define IFX_DSADC_CH_CGSYNC_SDPOS_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_CGSYNC_Bits.SDPOS */
#define IFX_DSADC_CH_CGSYNC_SDPOS_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_CGSYNC_Bits.SDPOS */
#define IFX_DSADC_CH_CGSYNC_SDPOS_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.CSRC */
#define IFX_DSADC_CH_DICFG_CSRC_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.CSRC */
#define IFX_DSADC_CH_DICFG_CSRC_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.CSRC */
#define IFX_DSADC_CH_DICFG_CSRC_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.DSRC */
#define IFX_DSADC_CH_DICFG_DSRC_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.DSRC */
#define IFX_DSADC_CH_DICFG_DSRC_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.DSRC */
#define IFX_DSADC_CH_DICFG_DSRC_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.DSWC */
#define IFX_DSADC_CH_DICFG_DSWC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.DSWC */
#define IFX_DSADC_CH_DICFG_DSWC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.DSWC */
#define IFX_DSADC_CH_DICFG_DSWC_OFF (7u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.ITRMODE */
#define IFX_DSADC_CH_DICFG_ITRMODE_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.ITRMODE */
#define IFX_DSADC_CH_DICFG_ITRMODE_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.ITRMODE */
#define IFX_DSADC_CH_DICFG_ITRMODE_OFF (8u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.SCWC */
#define IFX_DSADC_CH_DICFG_SCWC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.SCWC */
#define IFX_DSADC_CH_DICFG_SCWC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.SCWC */
#define IFX_DSADC_CH_DICFG_SCWC_OFF (31u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.STROBE */
#define IFX_DSADC_CH_DICFG_STROBE_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.STROBE */
#define IFX_DSADC_CH_DICFG_STROBE_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.STROBE */
#define IFX_DSADC_CH_DICFG_STROBE_OFF (20u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.TRSEL */
#define IFX_DSADC_CH_DICFG_TRSEL_LEN (3u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.TRSEL */
#define IFX_DSADC_CH_DICFG_TRSEL_MSK (0x7u)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.TRSEL */
#define IFX_DSADC_CH_DICFG_TRSEL_OFF (12u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.TRWC */
#define IFX_DSADC_CH_DICFG_TRWC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.TRWC */
#define IFX_DSADC_CH_DICFG_TRWC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.TRWC */
#define IFX_DSADC_CH_DICFG_TRWC_OFF (15u)

/** \brief  Length for Ifx_DSADC_CH_DICFG_Bits.TSTRMODE */
#define IFX_DSADC_CH_DICFG_TSTRMODE_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_DICFG_Bits.TSTRMODE */
#define IFX_DSADC_CH_DICFG_TSTRMODE_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_DICFG_Bits.TSTRMODE */
#define IFX_DSADC_CH_DICFG_TSTRMODE_OFF (10u)

/** \brief  Length for Ifx_DSADC_CH_FCFGA_Bits.AFSC */
#define IFX_DSADC_CH_FCFGA_AFSC_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGA_Bits.AFSC */
#define IFX_DSADC_CH_FCFGA_AFSC_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGA_Bits.AFSC */
#define IFX_DSADC_CH_FCFGA_AFSC_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_FCFGA_Bits.CFAC */
#define IFX_DSADC_CH_FCFGA_CFAC_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGA_Bits.CFAC */
#define IFX_DSADC_CH_FCFGA_CFAC_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGA_Bits.CFAC */
#define IFX_DSADC_CH_FCFGA_CFAC_OFF (8u)

/** \brief  Length for Ifx_DSADC_CH_FCFGA_Bits.CFADCNT */
#define IFX_DSADC_CH_FCFGA_CFADCNT_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGA_Bits.CFADCNT */
#define IFX_DSADC_CH_FCFGA_CFADCNT_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_FCFGA_Bits.CFADCNT */
#define IFX_DSADC_CH_FCFGA_CFADCNT_OFF (24u)

/** \brief  Length for Ifx_DSADC_CH_FCFGA_Bits.CFADF */
#define IFX_DSADC_CH_FCFGA_CFADF_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGA_Bits.CFADF */
#define IFX_DSADC_CH_FCFGA_CFADF_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_FCFGA_Bits.CFADF */
#define IFX_DSADC_CH_FCFGA_CFADF_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_FCFGA_Bits.EGT */
#define IFX_DSADC_CH_FCFGA_EGT_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGA_Bits.EGT */
#define IFX_DSADC_CH_FCFGA_EGT_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGA_Bits.EGT */
#define IFX_DSADC_CH_FCFGA_EGT_OFF (14u)

/** \brief  Length for Ifx_DSADC_CH_FCFGA_Bits.ESEL */
#define IFX_DSADC_CH_FCFGA_ESEL_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGA_Bits.ESEL */
#define IFX_DSADC_CH_FCFGA_ESEL_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGA_Bits.ESEL */
#define IFX_DSADC_CH_FCFGA_ESEL_OFF (12u)

/** \brief  Length for Ifx_DSADC_CH_FCFGA_Bits.SRGA */
#define IFX_DSADC_CH_FCFGA_SRGA_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGA_Bits.SRGA */
#define IFX_DSADC_CH_FCFGA_SRGA_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGA_Bits.SRGA */
#define IFX_DSADC_CH_FCFGA_SRGA_OFF (10u)

/** \brief  Length for Ifx_DSADC_CH_FCFGC_Bits.CFEN */
#define IFX_DSADC_CH_FCFGC_CFEN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGC_Bits.CFEN */
#define IFX_DSADC_CH_FCFGC_CFEN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGC_Bits.CFEN */
#define IFX_DSADC_CH_FCFGC_CFEN_OFF (10u)

/** \brief  Length for Ifx_DSADC_CH_FCFGC_Bits.CFMC */
#define IFX_DSADC_CH_FCFGC_CFMC_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGC_Bits.CFMC */
#define IFX_DSADC_CH_FCFGC_CFMC_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGC_Bits.CFMC */
#define IFX_DSADC_CH_FCFGC_CFMC_OFF (8u)

/** \brief  Length for Ifx_DSADC_CH_FCFGC_Bits.CFMDCNT */
#define IFX_DSADC_CH_FCFGC_CFMDCNT_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGC_Bits.CFMDCNT */
#define IFX_DSADC_CH_FCFGC_CFMDCNT_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_FCFGC_Bits.CFMDCNT */
#define IFX_DSADC_CH_FCFGC_CFMDCNT_OFF (24u)

/** \brief  Length for Ifx_DSADC_CH_FCFGC_Bits.CFMDF */
#define IFX_DSADC_CH_FCFGC_CFMDF_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGC_Bits.CFMDF */
#define IFX_DSADC_CH_FCFGC_CFMDF_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_FCFGC_Bits.CFMDF */
#define IFX_DSADC_CH_FCFGC_CFMDF_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_FCFGC_Bits.CFMSV */
#define IFX_DSADC_CH_FCFGC_CFMSV_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGC_Bits.CFMSV */
#define IFX_DSADC_CH_FCFGC_CFMSV_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_FCFGC_Bits.CFMSV */
#define IFX_DSADC_CH_FCFGC_CFMSV_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_FCFGC_Bits.MFSC */
#define IFX_DSADC_CH_FCFGC_MFSC_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGC_Bits.MFSC */
#define IFX_DSADC_CH_FCFGC_MFSC_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGC_Bits.MFSC */
#define IFX_DSADC_CH_FCFGC_MFSC_OFF (12u)

/** \brief  Length for Ifx_DSADC_CH_FCFGC_Bits.SRGM */
#define IFX_DSADC_CH_FCFGC_SRGM_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGC_Bits.SRGM */
#define IFX_DSADC_CH_FCFGC_SRGM_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGC_Bits.SRGM */
#define IFX_DSADC_CH_FCFGC_SRGM_OFF (14u)

/** \brief  Length for Ifx_DSADC_CH_FCFGM_Bits.DSH */
#define IFX_DSADC_CH_FCFGM_DSH_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGM_Bits.DSH */
#define IFX_DSADC_CH_FCFGM_DSH_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGM_Bits.DSH */
#define IFX_DSADC_CH_FCFGM_DSH_OFF (3u)

/** \brief  Length for Ifx_DSADC_CH_FCFGM_Bits.FIR0EN */
#define IFX_DSADC_CH_FCFGM_FIR0EN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGM_Bits.FIR0EN */
#define IFX_DSADC_CH_FCFGM_FIR0EN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGM_Bits.FIR0EN */
#define IFX_DSADC_CH_FCFGM_FIR0EN_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_FCFGM_Bits.FIR1EN */
#define IFX_DSADC_CH_FCFGM_FIR1EN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGM_Bits.FIR1EN */
#define IFX_DSADC_CH_FCFGM_FIR1EN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGM_Bits.FIR1EN */
#define IFX_DSADC_CH_FCFGM_FIR1EN_OFF (1u)

/** \brief  Length for Ifx_DSADC_CH_FCFGM_Bits.FSH */
#define IFX_DSADC_CH_FCFGM_FSH_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGM_Bits.FSH */
#define IFX_DSADC_CH_FCFGM_FSH_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGM_Bits.FSH */
#define IFX_DSADC_CH_FCFGM_FSH_OFF (5u)

/** \brief  Length for Ifx_DSADC_CH_FCFGM_Bits.OCEN */
#define IFX_DSADC_CH_FCFGM_OCEN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_FCFGM_Bits.OCEN */
#define IFX_DSADC_CH_FCFGM_OCEN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_FCFGM_Bits.OCEN */
#define IFX_DSADC_CH_FCFGM_OCEN_OFF (2u)

/** \brief  Length for Ifx_DSADC_CH_ICCFG_Bits.DI0 */
#define IFX_DSADC_CH_ICCFG_DI0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_ICCFG_Bits.DI0 */
#define IFX_DSADC_CH_ICCFG_DI0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_ICCFG_Bits.DI0 */
#define IFX_DSADC_CH_ICCFG_DI0_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_ICCFG_Bits.DI1 */
#define IFX_DSADC_CH_ICCFG_DI1_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_ICCFG_Bits.DI1 */
#define IFX_DSADC_CH_ICCFG_DI1_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_ICCFG_Bits.DI1 */
#define IFX_DSADC_CH_ICCFG_DI1_OFF (1u)

/** \brief  Length for Ifx_DSADC_CH_ICCFG_Bits.IREN */
#define IFX_DSADC_CH_ICCFG_IREN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_ICCFG_Bits.IREN */
#define IFX_DSADC_CH_ICCFG_IREN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_ICCFG_Bits.IREN */
#define IFX_DSADC_CH_ICCFG_IREN_OFF (4u)

/** \brief  Length for Ifx_DSADC_CH_ICCFG_Bits.TWINSP */
#define IFX_DSADC_CH_ICCFG_TWINSP_LEN (6u)

/** \brief  Mask for Ifx_DSADC_CH_ICCFG_Bits.TWINSP */
#define IFX_DSADC_CH_ICCFG_TWINSP_MSK (0x3fu)

/** \brief  Offset for Ifx_DSADC_CH_ICCFG_Bits.TWINSP */
#define IFX_DSADC_CH_ICCFG_TWINSP_OFF (8u)

/** \brief  Length for Ifx_DSADC_CH_ICCFG_Bits.WREN */
#define IFX_DSADC_CH_ICCFG_WREN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_ICCFG_Bits.WREN */
#define IFX_DSADC_CH_ICCFG_WREN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_ICCFG_Bits.WREN */
#define IFX_DSADC_CH_ICCFG_WREN_OFF (31u)

/** \brief  Length for Ifx_DSADC_CH_IWCTR_Bits.INTEN */
#define IFX_DSADC_CH_IWCTR_INTEN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_IWCTR_Bits.INTEN */
#define IFX_DSADC_CH_IWCTR_INTEN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_IWCTR_Bits.INTEN */
#define IFX_DSADC_CH_IWCTR_INTEN_OFF (7u)

/** \brief  Length for Ifx_DSADC_CH_IWCTR_Bits.IWS */
#define IFX_DSADC_CH_IWCTR_IWS_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_IWCTR_Bits.IWS */
#define IFX_DSADC_CH_IWCTR_IWS_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_IWCTR_Bits.IWS */
#define IFX_DSADC_CH_IWCTR_IWS_OFF (23u)

/** \brief  Length for Ifx_DSADC_CH_IWCTR_Bits.NVALCNT */
#define IFX_DSADC_CH_IWCTR_NVALCNT_LEN (6u)

/** \brief  Mask for Ifx_DSADC_CH_IWCTR_Bits.NVALCNT */
#define IFX_DSADC_CH_IWCTR_NVALCNT_MSK (0x3fu)

/** \brief  Offset for Ifx_DSADC_CH_IWCTR_Bits.NVALCNT */
#define IFX_DSADC_CH_IWCTR_NVALCNT_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_IWCTR_Bits.NVALDIS */
#define IFX_DSADC_CH_IWCTR_NVALDIS_LEN (6u)

/** \brief  Mask for Ifx_DSADC_CH_IWCTR_Bits.NVALDIS */
#define IFX_DSADC_CH_IWCTR_NVALDIS_MSK (0x3fu)

/** \brief  Offset for Ifx_DSADC_CH_IWCTR_Bits.NVALDIS */
#define IFX_DSADC_CH_IWCTR_NVALDIS_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_IWCTR_Bits.NVALINT */
#define IFX_DSADC_CH_IWCTR_NVALINT_LEN (6u)

/** \brief  Mask for Ifx_DSADC_CH_IWCTR_Bits.NVALINT */
#define IFX_DSADC_CH_IWCTR_NVALINT_MSK (0x3fu)

/** \brief  Offset for Ifx_DSADC_CH_IWCTR_Bits.NVALINT */
#define IFX_DSADC_CH_IWCTR_NVALINT_OFF (24u)

/** \brief  Length for Ifx_DSADC_CH_IWCTR_Bits.REPCNT */
#define IFX_DSADC_CH_IWCTR_REPCNT_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CH_IWCTR_Bits.REPCNT */
#define IFX_DSADC_CH_IWCTR_REPCNT_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CH_IWCTR_Bits.REPCNT */
#define IFX_DSADC_CH_IWCTR_REPCNT_OFF (8u)

/** \brief  Length for Ifx_DSADC_CH_IWCTR_Bits.REPVAL */
#define IFX_DSADC_CH_IWCTR_REPVAL_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CH_IWCTR_Bits.REPVAL */
#define IFX_DSADC_CH_IWCTR_REPVAL_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CH_IWCTR_Bits.REPVAL */
#define IFX_DSADC_CH_IWCTR_REPVAL_OFF (12u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.APC */
#define IFX_DSADC_CH_MODCFG_APC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.APC */
#define IFX_DSADC_CH_MODCFG_APC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.APC */
#define IFX_DSADC_CH_MODCFG_APC_OFF (29u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.CMVS */
#define IFX_DSADC_CH_MODCFG_CMVS_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.CMVS */
#define IFX_DSADC_CH_MODCFG_CMVS_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.CMVS */
#define IFX_DSADC_CH_MODCFG_CMVS_OFF (24u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.DIVM */
#define IFX_DSADC_CH_MODCFG_DIVM_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.DIVM */
#define IFX_DSADC_CH_MODCFG_DIVM_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.DIVM */
#define IFX_DSADC_CH_MODCFG_DIVM_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.DWC */
#define IFX_DSADC_CH_MODCFG_DWC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.DWC */
#define IFX_DSADC_CH_MODCFG_DWC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.DWC */
#define IFX_DSADC_CH_MODCFG_DWC_OFF (23u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.GAINSEL */
#define IFX_DSADC_CH_MODCFG_GAINSEL_LEN (4u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.GAINSEL */
#define IFX_DSADC_CH_MODCFG_GAINSEL_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.GAINSEL */
#define IFX_DSADC_CH_MODCFG_GAINSEL_OFF (4u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.GCEN */
#define IFX_DSADC_CH_MODCFG_GCEN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.GCEN */
#define IFX_DSADC_CH_MODCFG_GCEN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.GCEN */
#define IFX_DSADC_CH_MODCFG_GCEN_OFF (28u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.INCFGN */
#define IFX_DSADC_CH_MODCFG_INCFGN_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.INCFGN */
#define IFX_DSADC_CH_MODCFG_INCFGN_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.INCFGN */
#define IFX_DSADC_CH_MODCFG_INCFGN_OFF (2u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.INCFGP */
#define IFX_DSADC_CH_MODCFG_INCFGP_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.INCFGP */
#define IFX_DSADC_CH_MODCFG_INCFGP_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.INCFGP */
#define IFX_DSADC_CH_MODCFG_INCFGP_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.INCWC */
#define IFX_DSADC_CH_MODCFG_INCWC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.INCWC */
#define IFX_DSADC_CH_MODCFG_INCWC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.INCWC */
#define IFX_DSADC_CH_MODCFG_INCWC_OFF (15u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.INMAC */
#define IFX_DSADC_CH_MODCFG_INMAC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.INMAC */
#define IFX_DSADC_CH_MODCFG_INMAC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.INMAC */
#define IFX_DSADC_CH_MODCFG_INMAC_OFF (14u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.INMODE */
#define IFX_DSADC_CH_MODCFG_INMODE_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.INMODE */
#define IFX_DSADC_CH_MODCFG_INMODE_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.INMODE */
#define IFX_DSADC_CH_MODCFG_INMODE_OFF (12u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.INMUX */
#define IFX_DSADC_CH_MODCFG_INMUX_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.INMUX */
#define IFX_DSADC_CH_MODCFG_INMUX_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.INMUX */
#define IFX_DSADC_CH_MODCFG_INMUX_OFF (10u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.INSEL */
#define IFX_DSADC_CH_MODCFG_INSEL_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.INSEL */
#define IFX_DSADC_CH_MODCFG_INSEL_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.INSEL */
#define IFX_DSADC_CH_MODCFG_INSEL_OFF (8u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.MCFG */
#define IFX_DSADC_CH_MODCFG_MCFG_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.MCFG */
#define IFX_DSADC_CH_MODCFG_MCFG_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.MCFG */
#define IFX_DSADC_CH_MODCFG_MCFG_OFF (26u)

/** \brief  Length for Ifx_DSADC_CH_MODCFG_Bits.MWC */
#define IFX_DSADC_CH_MODCFG_MWC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_MODCFG_Bits.MWC */
#define IFX_DSADC_CH_MODCFG_MWC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_MODCFG_Bits.MWC */
#define IFX_DSADC_CH_MODCFG_MWC_OFF (31u)

/** \brief  Length for Ifx_DSADC_CH_OFFM_Bits.OFFSET */
#define IFX_DSADC_CH_OFFM_OFFSET_LEN (16u)

/** \brief  Mask for Ifx_DSADC_CH_OFFM_Bits.OFFSET */
#define IFX_DSADC_CH_OFFM_OFFSET_MSK (0xffffu)

/** \brief  Offset for Ifx_DSADC_CH_OFFM_Bits.OFFSET */
#define IFX_DSADC_CH_OFFM_OFFSET_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_RECTCFG_Bits.RFEN */
#define IFX_DSADC_CH_RECTCFG_RFEN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_RECTCFG_Bits.RFEN */
#define IFX_DSADC_CH_RECTCFG_RFEN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_RECTCFG_Bits.RFEN */
#define IFX_DSADC_CH_RECTCFG_RFEN_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_RECTCFG_Bits.SDCVAL */
#define IFX_DSADC_CH_RECTCFG_SDCVAL_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_RECTCFG_Bits.SDCVAL */
#define IFX_DSADC_CH_RECTCFG_SDCVAL_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_RECTCFG_Bits.SDCVAL */
#define IFX_DSADC_CH_RECTCFG_SDCVAL_OFF (15u)

/** \brief  Length for Ifx_DSADC_CH_RECTCFG_Bits.SGNCS */
#define IFX_DSADC_CH_RECTCFG_SGNCS_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_RECTCFG_Bits.SGNCS */
#define IFX_DSADC_CH_RECTCFG_SGNCS_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_RECTCFG_Bits.SGNCS */
#define IFX_DSADC_CH_RECTCFG_SGNCS_OFF (30u)

/** \brief  Length for Ifx_DSADC_CH_RECTCFG_Bits.SGND */
#define IFX_DSADC_CH_RECTCFG_SGND_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_RECTCFG_Bits.SGND */
#define IFX_DSADC_CH_RECTCFG_SGND_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_RECTCFG_Bits.SGND */
#define IFX_DSADC_CH_RECTCFG_SGND_OFF (31u)

/** \brief  Length for Ifx_DSADC_CH_RECTCFG_Bits.SSRC */
#define IFX_DSADC_CH_RECTCFG_SSRC_LEN (2u)

/** \brief  Mask for Ifx_DSADC_CH_RECTCFG_Bits.SSRC */
#define IFX_DSADC_CH_RECTCFG_SSRC_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_CH_RECTCFG_Bits.SSRC */
#define IFX_DSADC_CH_RECTCFG_SSRC_OFF (4u)

/** \brief  Length for Ifx_DSADC_CH_RESA_Bits.RESULT */
#define IFX_DSADC_CH_RESA_RESULT_LEN (16u)

/** \brief  Mask for Ifx_DSADC_CH_RESA_Bits.RESULT */
#define IFX_DSADC_CH_RESA_RESULT_MSK (0xffffu)

/** \brief  Offset for Ifx_DSADC_CH_RESA_Bits.RESULT */
#define IFX_DSADC_CH_RESA_RESULT_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_RESM_Bits.RESULT */
#define IFX_DSADC_CH_RESM_RESULT_LEN (16u)

/** \brief  Mask for Ifx_DSADC_CH_RESM_Bits.RESULT */
#define IFX_DSADC_CH_RESM_RESULT_MSK (0xffffu)

/** \brief  Offset for Ifx_DSADC_CH_RESM_Bits.RESULT */
#define IFX_DSADC_CH_RESM_RESULT_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_TSTMP_Bits.CFMDCNT */
#define IFX_DSADC_CH_TSTMP_CFMDCNT_LEN (8u)

/** \brief  Mask for Ifx_DSADC_CH_TSTMP_Bits.CFMDCNT */
#define IFX_DSADC_CH_TSTMP_CFMDCNT_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_CH_TSTMP_Bits.CFMDCNT */
#define IFX_DSADC_CH_TSTMP_CFMDCNT_OFF (16u)

/** \brief  Length for Ifx_DSADC_CH_TSTMP_Bits.NVALCNT */
#define IFX_DSADC_CH_TSTMP_NVALCNT_LEN (6u)

/** \brief  Mask for Ifx_DSADC_CH_TSTMP_Bits.NVALCNT */
#define IFX_DSADC_CH_TSTMP_NVALCNT_MSK (0x3fu)

/** \brief  Offset for Ifx_DSADC_CH_TSTMP_Bits.NVALCNT */
#define IFX_DSADC_CH_TSTMP_NVALCNT_OFF (24u)

/** \brief  Length for Ifx_DSADC_CH_TSTMP_Bits.RESULT */
#define IFX_DSADC_CH_TSTMP_RESULT_LEN (16u)

/** \brief  Mask for Ifx_DSADC_CH_TSTMP_Bits.RESULT */
#define IFX_DSADC_CH_TSTMP_RESULT_MSK (0xffffu)

/** \brief  Offset for Ifx_DSADC_CH_TSTMP_Bits.RESULT */
#define IFX_DSADC_CH_TSTMP_RESULT_OFF (0u)

/** \brief  Length for Ifx_DSADC_CH_TSTMP_Bits.TSSR */
#define IFX_DSADC_CH_TSTMP_TSSR_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_TSTMP_Bits.TSSR */
#define IFX_DSADC_CH_TSTMP_TSSR_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_TSTMP_Bits.TSSR */
#define IFX_DSADC_CH_TSTMP_TSSR_OFF (31u)

/** \brief  Length for Ifx_DSADC_CH_TSTMP_Bits.TSVAL */
#define IFX_DSADC_CH_TSTMP_TSVAL_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CH_TSTMP_Bits.TSVAL */
#define IFX_DSADC_CH_TSTMP_TSVAL_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CH_TSTMP_Bits.TSVAL */
#define IFX_DSADC_CH_TSTMP_TSVAL_OFF (30u)

/** \brief  Length for Ifx_DSADC_CLC_Bits.DISR */
#define IFX_DSADC_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CLC_Bits.DISR */
#define IFX_DSADC_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CLC_Bits.DISR */
#define IFX_DSADC_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_DSADC_CLC_Bits.DISS */
#define IFX_DSADC_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CLC_Bits.DISS */
#define IFX_DSADC_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CLC_Bits.DISS */
#define IFX_DSADC_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_DSADC_CLC_Bits.EDIS */
#define IFX_DSADC_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_DSADC_CLC_Bits.EDIS */
#define IFX_DSADC_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_CLC_Bits.EDIS */
#define IFX_DSADC_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_DSADC_EVFLAG_Bits.ALEV0 */
#define IFX_DSADC_EVFLAG_ALEV0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAG_Bits.ALEV0 */
#define IFX_DSADC_EVFLAG_ALEV0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAG_Bits.ALEV0 */
#define IFX_DSADC_EVFLAG_ALEV0_OFF (16u)

/** \brief  Length for Ifx_DSADC_EVFLAG_Bits.ALEV2 */
#define IFX_DSADC_EVFLAG_ALEV2_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAG_Bits.ALEV2 */
#define IFX_DSADC_EVFLAG_ALEV2_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAG_Bits.ALEV2 */
#define IFX_DSADC_EVFLAG_ALEV2_OFF (18u)

/** \brief  Length for Ifx_DSADC_EVFLAG_Bits.ALEV3 */
#define IFX_DSADC_EVFLAG_ALEV3_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAG_Bits.ALEV3 */
#define IFX_DSADC_EVFLAG_ALEV3_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAG_Bits.ALEV3 */
#define IFX_DSADC_EVFLAG_ALEV3_OFF (19u)

/** \brief  Length for Ifx_DSADC_EVFLAG_Bits.RESEV0 */
#define IFX_DSADC_EVFLAG_RESEV0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAG_Bits.RESEV0 */
#define IFX_DSADC_EVFLAG_RESEV0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAG_Bits.RESEV0 */
#define IFX_DSADC_EVFLAG_RESEV0_OFF (0u)

/** \brief  Length for Ifx_DSADC_EVFLAG_Bits.RESEV2 */
#define IFX_DSADC_EVFLAG_RESEV2_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAG_Bits.RESEV2 */
#define IFX_DSADC_EVFLAG_RESEV2_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAG_Bits.RESEV2 */
#define IFX_DSADC_EVFLAG_RESEV2_OFF (2u)

/** \brief  Length for Ifx_DSADC_EVFLAG_Bits.RESEV3 */
#define IFX_DSADC_EVFLAG_RESEV3_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAG_Bits.RESEV3 */
#define IFX_DSADC_EVFLAG_RESEV3_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAG_Bits.RESEV3 */
#define IFX_DSADC_EVFLAG_RESEV3_OFF (3u)

/** \brief  Length for Ifx_DSADC_EVFLAGCLR_Bits.ALEC0 */
#define IFX_DSADC_EVFLAGCLR_ALEC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAGCLR_Bits.ALEC0 */
#define IFX_DSADC_EVFLAGCLR_ALEC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAGCLR_Bits.ALEC0 */
#define IFX_DSADC_EVFLAGCLR_ALEC0_OFF (16u)

/** \brief  Length for Ifx_DSADC_EVFLAGCLR_Bits.ALEC2 */
#define IFX_DSADC_EVFLAGCLR_ALEC2_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAGCLR_Bits.ALEC2 */
#define IFX_DSADC_EVFLAGCLR_ALEC2_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAGCLR_Bits.ALEC2 */
#define IFX_DSADC_EVFLAGCLR_ALEC2_OFF (18u)

/** \brief  Length for Ifx_DSADC_EVFLAGCLR_Bits.ALEC3 */
#define IFX_DSADC_EVFLAGCLR_ALEC3_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAGCLR_Bits.ALEC3 */
#define IFX_DSADC_EVFLAGCLR_ALEC3_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAGCLR_Bits.ALEC3 */
#define IFX_DSADC_EVFLAGCLR_ALEC3_OFF (19u)

/** \brief  Length for Ifx_DSADC_EVFLAGCLR_Bits.RESEC0 */
#define IFX_DSADC_EVFLAGCLR_RESEC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAGCLR_Bits.RESEC0 */
#define IFX_DSADC_EVFLAGCLR_RESEC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAGCLR_Bits.RESEC0 */
#define IFX_DSADC_EVFLAGCLR_RESEC0_OFF (0u)

/** \brief  Length for Ifx_DSADC_EVFLAGCLR_Bits.RESEC2 */
#define IFX_DSADC_EVFLAGCLR_RESEC2_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAGCLR_Bits.RESEC2 */
#define IFX_DSADC_EVFLAGCLR_RESEC2_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAGCLR_Bits.RESEC2 */
#define IFX_DSADC_EVFLAGCLR_RESEC2_OFF (2u)

/** \brief  Length for Ifx_DSADC_EVFLAGCLR_Bits.RESEC3 */
#define IFX_DSADC_EVFLAGCLR_RESEC3_LEN (1u)

/** \brief  Mask for Ifx_DSADC_EVFLAGCLR_Bits.RESEC3 */
#define IFX_DSADC_EVFLAGCLR_RESEC3_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_EVFLAGCLR_Bits.RESEC3 */
#define IFX_DSADC_EVFLAGCLR_RESEC3_OFF (3u)

/** \brief  Length for Ifx_DSADC_GLOBCFG_Bits.IBSEL */
#define IFX_DSADC_GLOBCFG_IBSEL_LEN (4u)

/** \brief  Mask for Ifx_DSADC_GLOBCFG_Bits.IBSEL */
#define IFX_DSADC_GLOBCFG_IBSEL_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_GLOBCFG_Bits.IBSEL */
#define IFX_DSADC_GLOBCFG_IBSEL_OFF (16u)

/** \brief  Length for Ifx_DSADC_GLOBCFG_Bits.ICT */
#define IFX_DSADC_GLOBCFG_ICT_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBCFG_Bits.ICT */
#define IFX_DSADC_GLOBCFG_ICT_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBCFG_Bits.ICT */
#define IFX_DSADC_GLOBCFG_ICT_OFF (22u)

/** \brief  Length for Ifx_DSADC_GLOBCFG_Bits.IRM0 */
#define IFX_DSADC_GLOBCFG_IRM0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBCFG_Bits.IRM0 */
#define IFX_DSADC_GLOBCFG_IRM0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBCFG_Bits.IRM0 */
#define IFX_DSADC_GLOBCFG_IRM0_OFF (11u)

/** \brief  Length for Ifx_DSADC_GLOBCFG_Bits.LOSUP */
#define IFX_DSADC_GLOBCFG_LOSUP_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBCFG_Bits.LOSUP */
#define IFX_DSADC_GLOBCFG_LOSUP_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBCFG_Bits.LOSUP */
#define IFX_DSADC_GLOBCFG_LOSUP_OFF (20u)

/** \brief  Length for Ifx_DSADC_GLOBCFG_Bits.MCSEL */
#define IFX_DSADC_GLOBCFG_MCSEL_LEN (3u)

/** \brief  Mask for Ifx_DSADC_GLOBCFG_Bits.MCSEL */
#define IFX_DSADC_GLOBCFG_MCSEL_MSK (0x7u)

/** \brief  Offset for Ifx_DSADC_GLOBCFG_Bits.MCSEL */
#define IFX_DSADC_GLOBCFG_MCSEL_OFF (0u)

/** \brief  Length for Ifx_DSADC_GLOBCFG_Bits.PSWC */
#define IFX_DSADC_GLOBCFG_PSWC_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBCFG_Bits.PSWC */
#define IFX_DSADC_GLOBCFG_PSWC_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBCFG_Bits.PSWC */
#define IFX_DSADC_GLOBCFG_PSWC_OFF (23u)

/** \brief  Length for Ifx_DSADC_GLOBRC_Bits.CH0RUN */
#define IFX_DSADC_GLOBRC_CH0RUN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBRC_Bits.CH0RUN */
#define IFX_DSADC_GLOBRC_CH0RUN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBRC_Bits.CH0RUN */
#define IFX_DSADC_GLOBRC_CH0RUN_OFF (0u)

/** \brief  Length for Ifx_DSADC_GLOBRC_Bits.CH2RUN */
#define IFX_DSADC_GLOBRC_CH2RUN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBRC_Bits.CH2RUN */
#define IFX_DSADC_GLOBRC_CH2RUN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBRC_Bits.CH2RUN */
#define IFX_DSADC_GLOBRC_CH2RUN_OFF (2u)

/** \brief  Length for Ifx_DSADC_GLOBRC_Bits.CH3RUN */
#define IFX_DSADC_GLOBRC_CH3RUN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBRC_Bits.CH3RUN */
#define IFX_DSADC_GLOBRC_CH3RUN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBRC_Bits.CH3RUN */
#define IFX_DSADC_GLOBRC_CH3RUN_OFF (3u)

/** \brief  Length for Ifx_DSADC_GLOBRC_Bits.M0RUN */
#define IFX_DSADC_GLOBRC_M0RUN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBRC_Bits.M0RUN */
#define IFX_DSADC_GLOBRC_M0RUN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBRC_Bits.M0RUN */
#define IFX_DSADC_GLOBRC_M0RUN_OFF (16u)

/** \brief  Length for Ifx_DSADC_GLOBRC_Bits.M2RUN */
#define IFX_DSADC_GLOBRC_M2RUN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBRC_Bits.M2RUN */
#define IFX_DSADC_GLOBRC_M2RUN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBRC_Bits.M2RUN */
#define IFX_DSADC_GLOBRC_M2RUN_OFF (18u)

/** \brief  Length for Ifx_DSADC_GLOBRC_Bits.M3RUN */
#define IFX_DSADC_GLOBRC_M3RUN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBRC_Bits.M3RUN */
#define IFX_DSADC_GLOBRC_M3RUN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBRC_Bits.M3RUN */
#define IFX_DSADC_GLOBRC_M3RUN_OFF (19u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN0NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN0NVC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN0NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN0NVC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN0NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN0NVC0_OFF (4u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN0NVC1 */
#define IFX_DSADC_GLOBVCMH0_IN0NVC1_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN0NVC1 */
#define IFX_DSADC_GLOBVCMH0_IN0NVC1_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN0NVC1 */
#define IFX_DSADC_GLOBVCMH0_IN0NVC1_OFF (5u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN0PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN0PVC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN0PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN0PVC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN0PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN0PVC0_OFF (0u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN0PVC1 */
#define IFX_DSADC_GLOBVCMH0_IN0PVC1_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN0PVC1 */
#define IFX_DSADC_GLOBVCMH0_IN0PVC1_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN0PVC1 */
#define IFX_DSADC_GLOBVCMH0_IN0PVC1_OFF (1u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN2NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN2NVC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN2NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN2NVC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN2NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN2NVC0_OFF (20u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN2PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN2PVC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN2PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN2PVC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN2PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN2PVC0_OFF (16u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC0 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC0_OFF (28u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC1 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC1_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC1 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC1_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC1 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC1_OFF (29u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC2 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC2_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC2 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC2_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC2 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC2_OFF (30u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC3 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC3_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC3 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC3_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3NVC3 */
#define IFX_DSADC_GLOBVCMH0_IN3NVC3_OFF (31u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC0_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC0_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC0 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC0_OFF (24u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC1 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC1_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC1 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC1_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC1 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC1_OFF (25u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC2 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC2_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC2 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC2_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC2 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC2_OFF (26u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC3 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC3_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC3 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC3_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH0_Bits.IN3PVC3 */
#define IFX_DSADC_GLOBVCMH0_IN3PVC3_OFF (27u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH2_Bits.VCMHS */
#define IFX_DSADC_GLOBVCMH2_VCMHS_LEN (2u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH2_Bits.VCMHS */
#define IFX_DSADC_GLOBVCMH2_VCMHS_MSK (0x3u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH2_Bits.VCMHS */
#define IFX_DSADC_GLOBVCMH2_VCMHS_OFF (30u)

/** \brief  Length for Ifx_DSADC_GLOBVCMH2_Bits.VHON */
#define IFX_DSADC_GLOBVCMH2_VHON_LEN (1u)

/** \brief  Mask for Ifx_DSADC_GLOBVCMH2_Bits.VHON */
#define IFX_DSADC_GLOBVCMH2_VHON_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_GLOBVCMH2_Bits.VHON */
#define IFX_DSADC_GLOBVCMH2_VHON_OFF (29u)

/** \brief  Length for Ifx_DSADC_ID_Bits.MODNUMBER */
#define IFX_DSADC_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_DSADC_ID_Bits.MODNUMBER */
#define IFX_DSADC_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_DSADC_ID_Bits.MODNUMBER */
#define IFX_DSADC_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_DSADC_ID_Bits.MODREV */
#define IFX_DSADC_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_DSADC_ID_Bits.MODREV */
#define IFX_DSADC_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_ID_Bits.MODREV */
#define IFX_DSADC_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_DSADC_ID_Bits.MODTYPE */
#define IFX_DSADC_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_DSADC_ID_Bits.MODTYPE */
#define IFX_DSADC_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_DSADC_ID_Bits.MODTYPE */
#define IFX_DSADC_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_DSADC_IGCFG_Bits.DITRIM */
#define IFX_DSADC_IGCFG_DITRIM_LEN (3u)

/** \brief  Mask for Ifx_DSADC_IGCFG_Bits.DITRIM */
#define IFX_DSADC_IGCFG_DITRIM_MSK (0x7u)

/** \brief  Offset for Ifx_DSADC_IGCFG_Bits.DITRIM */
#define IFX_DSADC_IGCFG_DITRIM_OFF (0u)

/** \brief  Length for Ifx_DSADC_IGCFG_Bits.GLOBSP */
#define IFX_DSADC_IGCFG_GLOBSP_LEN (10u)

/** \brief  Mask for Ifx_DSADC_IGCFG_Bits.GLOBSP */
#define IFX_DSADC_IGCFG_GLOBSP_MSK (0x3ffu)

/** \brief  Offset for Ifx_DSADC_IGCFG_Bits.GLOBSP */
#define IFX_DSADC_IGCFG_GLOBSP_OFF (16u)

/** \brief  Length for Ifx_DSADC_IGCFG_Bits.WREN */
#define IFX_DSADC_IGCFG_WREN_LEN (1u)

/** \brief  Mask for Ifx_DSADC_IGCFG_Bits.WREN */
#define IFX_DSADC_IGCFG_WREN_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_IGCFG_Bits.WREN */
#define IFX_DSADC_IGCFG_WREN_OFF (31u)

/** \brief  Length for Ifx_DSADC_KRST0_Bits.RST */
#define IFX_DSADC_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_DSADC_KRST0_Bits.RST */
#define IFX_DSADC_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_KRST0_Bits.RST */
#define IFX_DSADC_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_DSADC_KRST0_Bits.RSTSTAT */
#define IFX_DSADC_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_DSADC_KRST0_Bits.RSTSTAT */
#define IFX_DSADC_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_KRST0_Bits.RSTSTAT */
#define IFX_DSADC_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_DSADC_KRST1_Bits.RST */
#define IFX_DSADC_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_DSADC_KRST1_Bits.RST */
#define IFX_DSADC_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_KRST1_Bits.RST */
#define IFX_DSADC_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_DSADC_KRSTCLR_Bits.CLR */
#define IFX_DSADC_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_DSADC_KRSTCLR_Bits.CLR */
#define IFX_DSADC_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_KRSTCLR_Bits.CLR */
#define IFX_DSADC_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_DSADC_OCS_Bits.SUS */
#define IFX_DSADC_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_DSADC_OCS_Bits.SUS */
#define IFX_DSADC_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_DSADC_OCS_Bits.SUS */
#define IFX_DSADC_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_DSADC_OCS_Bits.SUS_P */
#define IFX_DSADC_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_DSADC_OCS_Bits.SUS_P */
#define IFX_DSADC_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_OCS_Bits.SUS_P */
#define IFX_DSADC_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_DSADC_OCS_Bits.SUSSTA */
#define IFX_DSADC_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_DSADC_OCS_Bits.SUSSTA */
#define IFX_DSADC_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_DSADC_OCS_Bits.SUSSTA */
#define IFX_DSADC_OCS_SUSSTA_OFF (29u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXDSADC_BF_H */
