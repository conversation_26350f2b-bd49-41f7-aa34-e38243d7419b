	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc31720a --dep-file=zf_device_tft180.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_tft180.src ../libraries/zf_device/zf_device_tft180.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_tft180.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_tft180.tft180_write_index',code,cluster('tft180_write_index')
	.sect	'.text.zf_device_tft180.tft180_write_index'
	.align	2
	
; Function tft180_write_index
.L185:
tft180_write_index:	.type	func
	mov	d8,d4
.L541:
	mov	d15,#0
	jeq	d15,#0,.L2
	mov	d4,#480
.L540:
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L3
.L2:
	mov	d4,#480
.L542:
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L3:
	mov	d4,#2
	mov	d5,d8
.L543:
	call	spi_write_8bit
.L544:
	mov	d15,#1
	jeq	d15,#0,.L4
	mov	d4,#480
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L5
.L4:
	mov	d4,#480
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L5:
	ret
.L529:
	
__tft180_write_index_function_end:
	.size	tft180_write_index,__tft180_write_index_function_end-tft180_write_index
.L324:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_set_region',code,cluster('tft180_set_region')
	.sect	'.text.zf_device_tft180.tft180_set_region'
	.align	2
	
; Function tft180_set_region
.L187:
tft180_set_region:	.type	func
	mov	e8,d5,d4
	mov	e10,d7,d6
.L1376:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L548:
	lt.u	d4,d8,d0
.L545:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#176
.L547:
	call	debug_assert_handler
.L546:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L549:
	lt.u	d4,d9,d0
.L550:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#177
	call	debug_assert_handler
.L1377:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L551:
	lt.u	d4,d10,d0
.L552:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#178
	call	debug_assert_handler
.L1378:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L553:
	lt.u	d4,d11,d0
.L554:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#179
	call	debug_assert_handler
.L1379:
	movh.a	a15,#@his(tft180_display_dir)
	lea	a15,[a15]@los(tft180_display_dir)
	ld.bu	d0,[a15]
.L1380:
	mov	d15,#0
	jeq	d15,d0,.L6
.L1381:
	mov	d15,#1
	jeq	d15,d0,.L7
.L1382:
	mov	d15,#2
	jeq	d15,d0,.L8
.L1383:
	mov	d15,#3
	jeq	d15,d0,.L9
	j	.L10
.L6:
.L7:
	mov	d4,#42
	call	tft180_write_index
.L1384:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1385:
	mov	d4,#2
	add	d8,#2
	extr.u	d5,d8,#0,#8
	call	spi_write_8bit
.L1386:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1387:
	mov	d4,#2
	add	d10,#2
	extr.u	d5,d10,#0,#8
	call	spi_write_8bit
.L1388:
	mov	d4,#43
	call	tft180_write_index
.L1389:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1390:
	mov	d4,#2
	add	d9,#1
	extr.u	d5,d9,#0,#8
	call	spi_write_8bit
.L1391:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1392:
	mov	d4,#2
	add	d11,#1
	extr.u	d5,d11,#0,#8
	call	spi_write_8bit
.L1393:
	j	.L11
.L8:
.L9:
	mov	d4,#42
	call	tft180_write_index
.L1394:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1395:
	mov	d4,#2
	add	d8,#1
	extr.u	d5,d8,#0,#8
	call	spi_write_8bit
.L1396:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1397:
	mov	d4,#2
	add	d10,#1
	extr.u	d5,d10,#0,#8
	call	spi_write_8bit
.L1398:
	mov	d4,#43
	call	tft180_write_index
.L1399:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1400:
	mov	d4,#2
	add	d9,#2
	extr.u	d5,d9,#0,#8
	call	spi_write_8bit
.L1401:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1402:
	mov	d4,#2
	add	d11,#2
	extr.u	d5,d11,#0,#8
	call	spi_write_8bit
.L1403:
	j	.L12
.L10:
.L12:
.L11:
	mov	d4,#44
	call	tft180_write_index
.L1404:
	ret
.L532:
	
__tft180_set_region_function_end:
	.size	tft180_set_region,__tft180_set_region_function_end-tft180_set_region
.L329:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_debug_init',code,cluster('tft180_debug_init')
	.sect	'.text.zf_device_tft180.tft180_debug_init'
	.align	2
	
; Function tft180_debug_init
.L189:
tft180_debug_init:	.type	func
	sub.a	a10,#24
.L555:
	lea	a4,[a10]0
	call	debug_output_struct_init
.L1409:
	mov	d15,#1
.L1410:
	st.h	[a10],d15
.L1411:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L1412:
	st.h	[a10]2,d15
.L1413:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L1414:
	st.h	[a10]4,d15
.L1415:
	movh.a	a15,#@his(tft180_display_font)
	lea	a15,[a15]@los(tft180_display_font)
	ld.bu	d15,[a15]
.L1416:
	mov	d0,#0
	jeq	d15,d0,.L13
.L1417:
	mov	d0,#1
	jeq	d15,d0,.L14
.L1418:
	mov	d0,#2
	jeq	d15,d0,.L15
	j	.L16
.L13:
	mov	d15,#6
.L1419:
	st.b	[a10]6,d15
.L1420:
	mov	d15,#8
.L1421:
	st.b	[a10]7,d15
.L1422:
	j	.L17
.L14:
	mov	d15,#8
.L1423:
	st.b	[a10]6,d15
.L1424:
	mov	d15,#16
.L1425:
	st.b	[a10]7,d15
.L1426:
	j	.L18
.L15:
	j	.L19
.L16:
.L19:
.L18:
.L17:
	movh.a	a15,#@his(tft180_show_string)
	lea	a15,[a15]@los(tft180_show_string)
.L1427:
	st.a	[a10]12,a15
.L1428:
	movh.a	a15,#@his(tft180_clear)
	lea	a15,[a15]@los(tft180_clear)
.L1429:
	st.a	[a10]16,a15
.L1430:
	lea	a4,[a10]0
	call	debug_output_init
.L1431:
	ret
.L537:
	
__tft180_debug_init_function_end:
	.size	tft180_debug_init,__tft180_debug_init_function_end-tft180_debug_init
.L334:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_clear',code,cluster('tft180_clear')
	.sect	'.text.zf_device_tft180.tft180_clear'
	.align	2
	
	.global	tft180_clear
; Function tft180_clear
.L191:
tft180_clear:	.type	func
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L936:
	mul	d4,d15,#2
	call	malloc
.L556:
	mov.aa	a12,a2
.L558:
	mov	d15,#0
	jeq	d15,#0,.L20
	mov	d4,#482
	call	get_port
.L557:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L21
.L20:
	mov	d4,#482
	call	get_port
.L559:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L21:
	mov	d4,#0
.L937:
	mov	d5,#0
.L938:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L939:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L940:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L941:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
	call	tft180_set_region
.L942:
	mov	d0,#0
.L560:
	j	.L22
.L23:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L943:
	movh.a	a2,#@his(tft180_bgcolor)
	lea	a2,[a2]@los(tft180_bgcolor)
	ld.hu	d15,[a2]0
.L944:
	st.h	[a15],d15
.L945:
	add	d0,#1
.L561:
	extr.u	d0,d0,#0,#16
.L22:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L946:
	jlt.u	d0,d15,.L23
.L947:
	mov	d15,#0
.L562:
	j	.L24
.L25:
	mov	d4,#2
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d5,[a15]0
	mov.aa	a4,a12
.L564:
	call	spi_write_16bit_array
.L565:
	add	d15,#1
.L563:
	extr.u	d15,d15,#0,#16
.L24:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L948:
	jlt.u	d15,d0,.L25
.L949:
	mov	d15,#1
.L566:
	jeq	d15,#0,.L26
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L27
.L26:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L27:
	mov.aa	a4,a12
.L567:
	call	free
.L568:
	ret
.L347:
	
__tft180_clear_function_end:
	.size	tft180_clear,__tft180_clear_function_end-tft180_clear
.L234:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_full',code,cluster('tft180_full')
	.sect	'.text.zf_device_tft180.tft180_full'
	.align	2
	
	.global	tft180_full
; Function tft180_full
.L193:
tft180_full:	.type	func
	mov	d8,d4
.L570:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L954:
	mul	d4,d15,#2
.L569:
	call	malloc
.L571:
	mov.aa	a12,a2
.L573:
	mov	d15,#0
	jeq	d15,#0,.L28
	mov	d4,#482
	call	get_port
.L572:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L29
.L28:
	mov	d4,#482
	call	get_port
.L574:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L29:
	mov	d4,#0
.L955:
	mov	d5,#0
.L956:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L957:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L958:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L959:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
	call	tft180_set_region
.L960:
	mov	d0,#0
.L575:
	j	.L30
.L31:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L961:
	st.h	[a15],d8
.L962:
	add	d0,#1
.L576:
	extr.u	d0,d0,#0,#16
.L30:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L963:
	jlt.u	d0,d15,.L31
.L964:
	mov	d15,#0
.L577:
	j	.L32
.L33:
	mov	d4,#2
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d5,[a15]0
	mov.aa	a4,a12
.L579:
	call	spi_write_16bit_array
.L580:
	add	d15,#1
.L578:
	extr.u	d15,d15,#0,#16
.L32:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L965:
	jlt.u	d15,d0,.L33
.L966:
	mov	d15,#1
.L581:
	jeq	d15,#0,.L34
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L35
.L34:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L35:
	mov.aa	a4,a12
.L582:
	call	free
.L583:
	ret
.L353:
	
__tft180_full_function_end:
	.size	tft180_full,__tft180_full_function_end-tft180_full
.L239:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_set_dir',code,cluster('tft180_set_dir')
	.sect	'.text.zf_device_tft180.tft180_set_dir'
	.align	2
	
	.global	tft180_set_dir
; Function tft180_set_dir
.L195:
tft180_set_dir:	.type	func
	movh.a	a15,#@his(tft180_display_dir)
	lea	a15,[a15]@los(tft180_display_dir)
.L971:
	st.b	[a15],d4
.L972:
	mov	d15,#0
	jeq	d15,d4,.L36
.L973:
	mov	d15,#1
	jeq	d15,d4,.L37
.L974:
	mov	d15,#2
	jeq	d15,d4,.L38
.L975:
	mov	d15,#3
	jeq	d15,d4,.L39
	j	.L40
.L36:
.L37:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
.L976:
	mov	d15,#128
.L977:
	st.h	[a15],d15
.L978:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
.L979:
	mov	d15,#160
.L980:
	st.h	[a15],d15
.L981:
	j	.L41
.L38:
.L39:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
.L982:
	mov	d15,#160
.L983:
	st.h	[a15],d15
.L984:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
.L985:
	mov	d15,#128
.L986:
	st.h	[a15],d15
.L987:
	j	.L42
.L40:
.L42:
.L41:
	ret
.L359:
	
__tft180_set_dir_function_end:
	.size	tft180_set_dir,__tft180_set_dir_function_end-tft180_set_dir
.L244:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_set_font',code,cluster('tft180_set_font')
	.sect	'.text.zf_device_tft180.tft180_set_font'
	.align	2
	
	.global	tft180_set_font
; Function tft180_set_font
.L197:
tft180_set_font:	.type	func
	movh.a	a15,#@his(tft180_display_font)
	lea	a15,[a15]@los(tft180_display_font)
.L992:
	st.b	[a15],d4
.L993:
	ret
.L362:
	
__tft180_set_font_function_end:
	.size	tft180_set_font,__tft180_set_font_function_end-tft180_set_font
.L249:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_set_color',code,cluster('tft180_set_color')
	.sect	'.text.zf_device_tft180.tft180_set_color'
	.align	2
	
	.global	tft180_set_color
; Function tft180_set_color
.L199:
tft180_set_color:	.type	func
	movh.a	a15,#@his(tft180_pencolor)
	lea	a15,[a15]@los(tft180_pencolor)
.L998:
	st.h	[a15],d4
.L999:
	movh.a	a15,#@his(tft180_bgcolor)
	lea	a15,[a15]@los(tft180_bgcolor)
.L1000:
	st.h	[a15],d5
.L1001:
	ret
.L365:
	
__tft180_set_color_function_end:
	.size	tft180_set_color,__tft180_set_color_function_end-tft180_set_color
.L254:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_draw_point',code,cluster('tft180_draw_point')
	.sect	'.text.zf_device_tft180.tft180_draw_point'
	.align	2
	
	.global	tft180_draw_point
; Function tft180_draw_point
.L201:
tft180_draw_point:	.type	func
	mov	e8,d5,d4
	mov	d10,d6
.L587:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L588:
	lt.u	d4,d8,d15
.L585:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#372
.L586:
	call	debug_assert_handler
.L584:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L589:
	lt.u	d4,d9,d15
.L590:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#373
	call	debug_assert_handler
.L1006:
	mov	d15,#0
	jeq	d15,#0,.L43
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L44
.L43:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L44:
	mov	e4,d9,d8
	mov	e6,d9,d8
.L591:
	call	tft180_set_region
.L1007:
	mov	d4,#2
	mov	d5,d10
.L592:
	call	spi_write_16bit
.L593:
	mov	d15,#1
	jeq	d15,#0,.L45
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L46
.L45:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L46:
	ret
.L369:
	
__tft180_draw_point_function_end:
	.size	tft180_draw_point,__tft180_draw_point_function_end-tft180_draw_point
.L259:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_draw_line',code,cluster('tft180_draw_line')
	.sect	'.text.zf_device_tft180.tft180_draw_line'
	.align	2
	
	.global	tft180_draw_line
; Function tft180_draw_line
.L203:
tft180_draw_line:	.type	func
	sub.a	a10,#8
.L594:
	mov	d15,d4
.L598:
	mov	e8,d6,d5
	mov	d10,d7
.L600:
	ld.hu	d11,[a10]8
.L601:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d15,d0
.L596:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#396
.L597:
	call	debug_assert_handler
.L595:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L602:
	lt.u	d4,d8,d0
.L603:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#397
	call	debug_assert_handler
.L1012:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L604:
	lt.u	d4,d9,d0
.L605:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#398
	call	debug_assert_handler
.L1013:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d10,d0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#399
	call	debug_assert_handler
.L381:
	jge.u	d15,d9,.L47
.L606:
	mov	d0,#1
	st.w	[a10],d0
.L1014:
	j	.L48
.L47:
	mov	d0,#-1
	st.w	[a10],d0
.L48:
	jge.u	d8,d10,.L49
.L607:
	mov	d14,#1
.L608:
	j	.L50
.L49:
	mov	d14,#-1
.L50:
.L51:
	jeq	d15,d9,.L52
.L609:
	sub	d0,d8,d10
.L610:
	itof	d0,d0
.L611:
	sub	d1,d15,d9
.L612:
	itof	d1,d1
.L1015:
	div.f	d13,d0,d1
.L613:
	utof	d0,d8
.L614:
	utof	d1,d15
.L1016:
	msub.f	d12,d0,d1,d13
.L615:
	j	.L53
.L52:
	j	.L54
.L55:
	mov	e4,d8,d9
.L616:
	mov	d6,d11
.L617:
	call	tft180_draw_point
.L618:
	add	d8,d14
	extr.u	d8,d8,#0,#16
.L54:
	jne	d8,d10,.L55
.L619:
	mov	e4,d10,d9
.L620:
	mov	d6,d11
.L621:
	call	tft180_draw_point
.L622:
	j	.L56
.L53:
	sub	d0,d8,d10
.L623:
	jlt	d0,#0,.L57
.L624:
	sub	d0,d8,d10
.L625:
	j	.L58
.L57:
	sub	d0,d8,d10
.L626:
	rsub	d0,#0
.L58:
	sub	d1,d15,d9
.L627:
	jlt	d1,#0,.L59
.L628:
	sub	d1,d15,d9
.L629:
	j	.L60
.L59:
	sub	d1,d15,d9
.L630:
	rsub	d1,#0
.L60:
	jge	d1,d0,.L61
.L1017:
	j	.L62
.L63:
	mov	e4,d8,d15
.L631:
	mov	d6,d11
.L632:
	call	tft180_draw_point
.L633:
	add	d8,d14
	extr.u	d8,d8,#0,#16
.L634:
	utof	d15,d8
.L599:
	sub.f	d15,d15,d12
.L1018:
	div.f	d4,d15,d13
.L1019:
	call	__f_ftos
	extr.u	d15,d2,#0,#16
.L62:
	jne	d8,d10,.L63
.L635:
	mov	e4,d10,d15
	mov	d6,d11
.L637:
	call	tft180_draw_point
.L638:
	j	.L64
.L61:
	j	.L65
.L66:
	mov	e4,d8,d15
.L639:
	mov	d6,d11
.L640:
	call	tft180_draw_point
.L641:
	ld.w	d0,[a10]
.L642:
	add	d15,d0
.L636:
	extr.u	d15,d15,#0,#16
.L644:
	utof	d0,d15
.L643:
	madd.f	d4,d12,d0,d13
.L1020:
	call	__f_ftos
	extr.u	d8,d2,#0,#16
.L65:
	jne	d15,d9,.L66
.L646:
	mov	e4,d8,d9
.L645:
	mov	d6,d11
.L647:
	call	tft180_draw_point
.L64:
.L56:
	ret
.L374:
	
__tft180_draw_line_function_end:
	.size	tft180_draw_line,__tft180_draw_line_function_end-tft180_draw_line
.L264:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_char',code,cluster('tft180_show_char')
	.sect	'.text.zf_device_tft180.tft180_show_char'
	.align	2
	
	.global	tft180_show_char
; Function tft180_show_char
.L205:
tft180_show_char:	.type	func
	lea	a10,[a10]-256
.L648:
	mov	e8,d5,d4
	mov	d10,d6
.L652:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L653:
	lt.u	d4,d8,d0
.L650:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#460
.L651:
	call	debug_assert_handler
.L649:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L654:
	lt.u	d4,d9,d0
.L655:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#461
	call	debug_assert_handler
.L393:
	mov	d15,#0
	jeq	d15,#0,.L67
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L68
.L67:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L68:
	movh.a	a15,#@his(tft180_display_font)
	lea	a15,[a15]@los(tft180_display_font)
	ld.bu	d0,[a15]
.L1025:
	mov	d15,#0
	jeq	d15,d0,.L69
.L1026:
	mov	d15,#1
	jeq	d15,d0,.L70
.L1027:
	mov	d15,#2
	jeq	d15,d0,.L71
	j	.L72
.L69:
	add	d15,d8,#5
.L656:
	extr.u	d6,d15,#0,#16
.L657:
	add	d15,d9,#7
.L658:
	extr.u	d7,d15,#0,#16
.L659:
	mov	e4,d9,d8
.L660:
	call	tft180_set_region
.L1028:
	mov	d0,#0
.L661:
	j	.L73
.L74:
	add	d15,d10,#-32
.L1029:
	mul	d15,d15,#6
.L1030:
	movh.a	a15,#@his(ascii_font_6x8)
	lea	a15,[a15]@los(ascii_font_6x8)
.L1031:
	addsc.a	a15,a15,d15,#0
.L1032:
	addsc.a	a15,a15,d0,#0
	ld.bu	d1,[a15]
.L662:
	mov	d2,#0
.L663:
	j	.L75
.L76:
	jz.t	d1:0,.L77
.L1033:
	mov	d15,#6
.L1034:
	madd	d15,d0,d2,d15
.L1035:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1036:
	movh.a	a2,#@his(tft180_pencolor)
	lea	a2,[a2]@los(tft180_pencolor)
	ld.hu	d15,[a2]0
.L1037:
	st.h	[a15],d15
.L1038:
	j	.L78
.L77:
	mov	d15,#6
.L1039:
	madd	d15,d0,d2,d15
.L1040:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1041:
	movh.a	a2,#@his(tft180_bgcolor)
	lea	a2,[a2]@los(tft180_bgcolor)
	ld.hu	d15,[a2]0
.L1042:
	st.h	[a15],d15
.L78:
	sha	d1,#-1
.L1043:
	add	d2,#1
.L75:
	jlt.u	d2,#8,.L76
.L400:
	add	d0,#1
.L73:
	jlt.u	d0,#6,.L74
.L1044:
	mov	d4,#2
	lea	a4,[a10]0
	mov	d5,#48
	call	spi_write_16bit_array
.L397:
	j	.L79
.L70:
	add	d15,d8,#7
.L664:
	extr.u	d6,d15,#0,#16
.L665:
	add	d15,d9,#15
.L666:
	extr.u	d7,d15,#0,#16
.L667:
	mov	e4,d9,d8
.L668:
	call	tft180_set_region
.L1045:
	mov	d3,#0
.L669:
	j	.L80
.L81:
	add	d0,d10,#-32
.L1046:
	mul	d15,d0,#16
.L1047:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L1048:
	addsc.a	a15,a15,d15,#0
.L1049:
	addsc.a	a15,a15,d3,#0
	ld.bu	d0,[a15]
.L670:
	add	d1,d10,#-32
.L1050:
	mul	d15,d1,#16
.L1051:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L1052:
	addsc.a	a15,a15,d15,#0
.L1053:
	addsc.a	a15,a15,d3,#0
	ld.bu	d4,[a15]8
.L672:
	mov	d1,#0
.L673:
	j	.L82
.L83:
	jz.t	d0:0,.L84
.L1054:
	mov	d15,#8
.L1055:
	madd	d2,d3,d1,d15
.L1056:
	mul	d15,d2,#2
	addsc.a	a15,a10,d15,#0
.L1057:
	movh.a	a2,#@his(tft180_pencolor)
	lea	a2,[a2]@los(tft180_pencolor)
	ld.hu	d2,[a2]0
.L1058:
	st.h	[a15],d2
.L1059:
	j	.L85
.L84:
	mov	d15,#8
.L1060:
	madd	d2,d3,d1,d15
.L1061:
	mul	d15,d2,#2
	addsc.a	a15,a10,d15,#0
.L1062:
	movh.a	a2,#@his(tft180_bgcolor)
	lea	a2,[a2]@los(tft180_bgcolor)
	ld.hu	d2,[a2]0
.L1063:
	st.h	[a15],d2
.L85:
	sha	d0,#-1
.L1064:
	add	d1,#1
.L82:
	jlt.u	d1,#8,.L83
.L1065:
	mov	d0,#0
.L671:
	j	.L86
.L87:
	jz.t	d4:0,.L88
.L1066:
	mov	d15,#8
.L1067:
	madd	d1,d3,d0,d15
.L1068:
	add	d15,d1,#64
.L1069:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1070:
	movh.a	a2,#@his(tft180_pencolor)
	lea	a2,[a2]@los(tft180_pencolor)
	ld.hu	d15,[a2]0
.L1071:
	st.h	[a15],d15
.L1072:
	j	.L89
.L88:
	mov	d15,#8
.L1073:
	madd	d15,d3,d0,d15
.L1074:
	add	d15,d15,#64
.L1075:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1076:
	movh.a	a2,#@his(tft180_bgcolor)
	lea	a2,[a2]@los(tft180_bgcolor)
	ld.hu	d15,[a2]0
.L1077:
	st.h	[a15],d15
.L89:
	sha	d4,#-1
.L1078:
	add	d0,#1
.L86:
	jlt.u	d0,#8,.L87
.L405:
	add	d3,#1
.L80:
	jlt.u	d3,#8,.L81
.L1079:
	mov	d4,#2
	lea	a4,[a10]0
	mov	d5,#128
	call	spi_write_16bit_array
.L402:
	j	.L90
.L71:
	j	.L91
.L72:
.L91:
.L90:
.L79:
	mov	d15,#1
	jeq	d15,#0,.L92
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L93
.L92:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L93:
	ret
.L388:
	
__tft180_show_char_function_end:
	.size	tft180_show_char,__tft180_show_char_function_end-tft180_show_char
.L269:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_string',code,cluster('tft180_show_string')
	.sect	'.text.zf_device_tft180.tft180_show_string'
	.align	2
	
	.global	tft180_show_string
; Function tft180_show_string
.L207:
tft180_show_string:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L677:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L678:
	lt.u	d4,d8,d15
.L675:
	movh.a	a4,#@his(.1.str)
.L674:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#547
.L676:
	call	debug_assert_handler
.L1084:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L679:
	lt.u	d4,d9,d15
.L680:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#548
	call	debug_assert_handler
.L413:
	mov	d10,#0
.L681:
	j	.L94
.L95:
	movh.a	a15,#@his(tft180_display_font)
	lea	a15,[a15]@los(tft180_display_font)
	ld.bu	d0,[a15]
.L1085:
	mov	d15,#0
	jeq	d15,d0,.L96
.L1086:
	mov	d15,#1
	jeq	d15,d0,.L97
.L1087:
	mov	d1,#2
	jeq	d1,d0,.L98
	j	.L99
.L96:
	mov	d0,#6
.L683:
	madd	d15,d8,d10,d0
.L684:
	extr.u	d4,d15,#0,#16
.L1088:
	addsc.a	a15,a12,d10,#0
	ld.b	d6,[a15]0
.L685:
	mov	d5,d9
.L686:
	call	tft180_show_char
.L687:
	j	.L100
.L97:
	mov	d15,#8
.L688:
	madd	d15,d8,d10,d15
.L689:
	extr.u	d4,d15,#0,#16
.L1089:
	addsc.a	a15,a12,d10,#0
	ld.b	d6,[a15]0
.L690:
	mov	d5,d9
.L691:
	call	tft180_show_char
.L692:
	j	.L101
.L98:
	j	.L102
.L99:
.L102:
.L101:
.L100:
	add	d10,#1
.L682:
	extr.u	d10,d10,#0,#16
.L94:
	addsc.a	a15,a12,d10,#0
	ld.b	d15,[a15]0
.L1090:
	jne	d15,#0,.L95
.L1091:
	ret
.L408:
	
__tft180_show_string_function_end:
	.size	tft180_show_string,__tft180_show_string_function_end-tft180_show_string
.L274:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_int',code,cluster('tft180_show_int')
	.sect	'.text.zf_device_tft180.tft180_show_int'
	.align	2
	
	.global	tft180_show_int
; Function tft180_show_int
.L209:
tft180_show_int:	.type	func
	sub.a	a10,#16
.L693:
	mov	e10,d5,d4
	mov	d9,d6
.L697:
	mov	d12,d7
.L698:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L699:
	lt.u	d4,d10,d0
.L695:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#577
.L696:
	call	debug_assert_handler
.L694:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L700:
	lt.u	d4,d11,d0
.L701:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#578
	call	debug_assert_handler
.L1096:
	mov	d0,#0
	lt.u	d4,d0,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#579
	call	debug_assert_handler
.L1097:
	mov	d15,#10
	ge.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#580
	call	debug_assert_handler
.L421:
	mov	d15,#1
.L702:
	lea	a4,[a10]0
.L1098:
	mov	d4,#0
.L1099:
	mov	d5,#12
	call	memset
.L1100:
	lea	a4,[a10]0
.L1101:
	mov	d4,#32
.L1102:
	add	d5,d12,#1
	call	memset
.L1103:
	jge.u	d12,#10,.L103
.L1104:
	j	.L104
.L105:
	mul	d15,d15,#10
.L1105:
	add	d12,#-1
.L104:
	jge.u	d12,#1,.L105
.L1106:
	div	e8,d9,d15
.L103:
	lea	a4,[a10]0
.L1107:
	mov	d4,d9
.L703:
	call	func_int_to_str
.L704:
	lea	a4,[a10]0
.L705:
	mov	e4,d11,d10
.L706:
	call	tft180_show_string
.L1108:
	ret
.L415:
	
__tft180_show_int_function_end:
	.size	tft180_show_int,__tft180_show_int_function_end-tft180_show_int
.L279:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_uint',code,cluster('tft180_show_uint')
	.sect	'.text.zf_device_tft180.tft180_show_uint'
	.align	2
	
	.global	tft180_show_uint
; Function tft180_show_uint
.L211:
tft180_show_uint:	.type	func
	sub.a	a10,#16
.L707:
	mov	e10,d5,d4
	mov	d9,d6
.L711:
	mov	d12,d7
.L712:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d15,[a15]0
.L713:
	lt.u	d4,d10,d15
.L709:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#616
.L710:
	call	debug_assert_handler
.L708:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L714:
	lt.u	d4,d11,d15
.L715:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#617
	call	debug_assert_handler
.L1113:
	mov	d15,#0
	lt.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#618
	call	debug_assert_handler
.L1114:
	mov	d15,#10
	ge.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#619
	call	debug_assert_handler
.L433:
	mov	d15,#1
.L716:
	lea	a4,[a10]0
.L1115:
	mov	d4,#0
.L1116:
	mov	d5,#12
	call	memset
.L1117:
	lea	a4,[a10]0
.L1118:
	mov	d4,#32
.L1119:
	mov	d5,d12
.L717:
	call	memset
.L718:
	jge.u	d12,#10,.L106
.L1120:
	j	.L107
.L108:
	mul	d15,d15,#10
.L1121:
	add	d12,#-1
.L107:
	jge.u	d12,#1,.L108
.L1122:
	div.u	e8,d9,d15
.L106:
	lea	a4,[a10]0
.L1123:
	mov	d4,d9
.L719:
	call	func_uint_to_str
.L720:
	lea	a4,[a10]0
.L721:
	mov	e4,d11,d10
.L722:
	call	tft180_show_string
.L1124:
	ret
.L427:
	
__tft180_show_uint_function_end:
	.size	tft180_show_uint,__tft180_show_uint_function_end-tft180_show_uint
.L284:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_float',code,cluster('tft180_show_float')
	.sect	'.text.zf_device_tft180.tft180_show_float'
	.align	2
	
	.global	tft180_show_float
; Function tft180_show_float
.L213:
tft180_show_float:	.type	func
	sub.a	a10,#24
.L723:
	st.w	[a10]20,d4
.L727:
	mov	d14,d5
.L728:
	mov	e10,d7,d6
	ld.bu	d9,[a10]24
.L729:
	ld.bu	d8,[a10]28
.L730:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
	ld.w	d15,[a10]20
.L725:
	lt.u	d4,d15,d0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#658
.L726:
	call	debug_assert_handler
.L724:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L731:
	lt.u	d4,d14,d15
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#659
	call	debug_assert_handler
.L1129:
	mov	d15,#0
	lt.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#660
	call	debug_assert_handler
.L1130:
	mov	d15,#8
	ge.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#661
	call	debug_assert_handler
.L1131:
	mov	d15,#0
	lt.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#662
	call	debug_assert_handler
.L1132:
	mov	d15,#6
	ge.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#663
	call	debug_assert_handler
.L445:
	mov	d12,#0
	mov	d13,#0
.L732:
	addih	d13,d13,#16368
.L1133:
	lea	a4,[a10]0
.L1134:
	mov	d4,#0
.L1135:
	mov	d5,#17
	call	memset
.L1136:
	lea	a4,[a10]0
.L1137:
	mov	d4,#32
.L1138:
	add	d15,d9,d8
.L1139:
	add	d5,d15,#2
	call	memset
.L1140:
	j	.L109
.L110:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L733:
	mov	e4,d13,d12
.L734:
	call	__d_mul
	mov	e12,d3,d2
.L1141:
	add	d9,#-1
.L109:
	jge.u	d9,#1,.L110
.L735:
	mov	e4,d11,d10
.L736:
	call	__d_dtoi
	mov	d15,d2
.L737:
	mov	e4,d13,d12
.L738:
	call	__d_dtoi
.L1142:
	div	e4,d15,d2
	call	__d_itod
	mov	e4,d3,d2
.L739:
	mov	e6,d13,d12
.L740:
	call	__d_mul
	mov	e6,d3,d2
.L741:
	mov	e4,d11,d10
.L742:
	call	__d_sub
	mov	e4,d3,d2
.L1143:
	lea	a4,[a10]0
.L1144:
	mov	d6,d8
.L743:
	call	func_double_to_str
.L744:
	lea	a4,[a10]0
	ld.w	d4,[a10]20
.L745:
	mov	d5,d14
.L747:
	call	tft180_show_string
.L746:
	ret
.L438:
	
__tft180_show_float_function_end:
	.size	tft180_show_float,__tft180_show_float_function_end-tft180_show_float
.L289:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_binary_image',code,cluster('tft180_show_binary_image')
	.sect	'.text.zf_device_tft180.tft180_show_binary_image'
	.align	2
	
	.global	tft180_show_binary_image
; Function tft180_show_binary_image
.L215:
tft180_show_binary_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L752:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L753:
	ld.hu	d13,[a10]4
.L754:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L755:
	lt.u	d4,d8,d0
.L750:
	movh.a	a4,#@his(.1.str)
.L749:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#701
.L751:
	call	debug_assert_handler
.L748:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L756:
	lt.u	d4,d9,d0
.L757:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#702
	call	debug_assert_handler
.L1149:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#703
	call	debug_assert_handler
.L460:
	mul	d4,d12,#2
	call	malloc
.L758:
	mov.aa	a13,a2
.L760:
	mov	d15,#0
	jeq	d15,#0,.L111
	mov	d4,#482
	call	get_port
.L759:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L112
.L111:
	mov	d4,#482
	call	get_port
.L761:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L112:
	add	d15,d8,d12
.L762:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L763:
	add	d15,d9,d13
.L764:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L765:
	mov	e4,d9,d8
.L766:
	call	tft180_set_region
.L1150:
	mov	d8,#0
.L767:
	j	.L113
.L114:
	mul	d15,d8,d11
.L768:
	div.u	e0,d15,d13
.L769:
	mul	d0,d10
.L770:
	mov	d15,#8
.L1151:
	div.u	e0,d0,d15
.L1152:
	addsc.a	a15,a12,d0,#0
.L771:
	mov	d4,#0
.L772:
	j	.L115
.L116:
	mul	d15,d4,d10
.L774:
	div.u	e2,d15,d12
.L775:
	mov	d15,#8
.L1153:
	div.u	e0,d2,d15
.L1154:
	addsc.a	a2,a15,d0,#0
.L1155:
	ld.bu	d0,[a2]
.L777:
	mov	d15,#8
.L1156:
	div.u	e2,d2,d15
.L776:
	sha	d0,d0,d3
.L778:
	jz.t	d0:7,.L117
.L1157:
	mul	d15,d4,#2
	addsc.a	a2,a13,d15,#0
.L1158:
	mov.u	d15,#65535
.L1159:
	st.h	[a2],d15
.L1160:
	j	.L118
.L117:
	mul	d15,d4,#2
	addsc.a	a2,a13,d15,#0
.L1161:
	mov	d15,#0
.L1162:
	st.h	[a2],d15
.L118:
	add	d4,#1
.L115:
	jlt.u	d4,d12,.L116
.L1163:
	mov	d4,#2
.L773:
	mov.aa	a4,a13
.L779:
	mov	d5,d12
.L781:
	call	spi_write_16bit_array
.L780:
	add	d8,#1
.L113:
	jlt.u	d8,d13,.L114
.L1164:
	mov	d15,#1
	jeq	d15,#0,.L119
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L120
.L119:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L120:
	mov.aa	a4,a13
.L782:
	call	free
.L783:
	ret
.L451:
	
__tft180_show_binary_image_function_end:
	.size	tft180_show_binary_image,__tft180_show_binary_image_function_end-tft180_show_binary_image
.L294:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_gray_image',code,cluster('tft180_show_gray_image')
	.sect	'.text.zf_device_tft180.tft180_show_gray_image'
	.align	2
	
	.global	tft180_show_gray_image
; Function tft180_show_gray_image
.L217:
tft180_show_gray_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L788:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L789:
	ld.hu	d13,[a10]4
.L790:
	ld.bu	d14,[a10]8
.L791:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L792:
	lt.u	d4,d8,d0
.L786:
	movh.a	a4,#@his(.1.str)
.L785:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#756
.L787:
	call	debug_assert_handler
.L784:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L793:
	lt.u	d4,d9,d15
.L794:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#757
	call	debug_assert_handler
.L1169:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#758
	call	debug_assert_handler
.L476:
	mul	d4,d12,#2
	call	malloc
.L795:
	mov.aa	a13,a2
.L797:
	mov	d15,#0
	jeq	d15,#0,.L121
	mov	d4,#482
	call	get_port
.L796:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L122
.L121:
	mov	d4,#482
	call	get_port
.L798:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L122:
	add	d15,d8,d12
.L799:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L800:
	add	d15,d9,d13
.L801:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L802:
	mov	e4,d9,d8
.L803:
	call	tft180_set_region
.L1170:
	mov	d8,#0
.L804:
	j	.L123
.L124:
	mul	d15,d8,d11
.L805:
	div.u	e0,d15,d13
.L806:
	mul	d15,d0,d10
.L807:
	addsc.a	a15,a12,d15,#0
.L808:
	mov	d2,#0
.L809:
	j	.L125
.L126:
	mul	d15,d2,d10
.L811:
	div.u	e0,d15,d12
.L1171:
	addsc.a	a2,a15,d0,#0
.L1172:
	ld.bu	d0,[a2]
.L812:
	jne	d14,#0,.L127
.L1173:
	sha	d15,d0,#-3
.L1174:
	and	d15,#31
.L1175:
	sha	d1,d15,#11
.L814:
	sha	d15,d0,#-2
.L1176:
	and	d15,#63
.L1177:
	sha	d15,#5
.L1178:
	or	d1,d15
.L1179:
	sha	d0,#-3
.L813:
	and	d15,d0,#31
.L1180:
	or	d1,d15
.L1181:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1182:
	st.h	[a2],d1
.L1183:
	j	.L128
.L127:
	jge.u	d0,d14,.L129
.L1184:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1185:
	mov	d15,#0
.L1186:
	st.h	[a2],d15
.L1187:
	j	.L130
.L129:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1188:
	mov.u	d15,#65535
.L1189:
	st.h	[a2],d15
.L130:
.L128:
	add	d2,#1
.L125:
	jlt.u	d2,d12,.L126
.L1190:
	mov	d4,#2
	mov.aa	a4,a13
.L815:
	mov	d5,d12
.L816:
	call	spi_write_16bit_array
.L810:
	add	d8,#1
.L123:
	jlt.u	d8,d13,.L124
.L1191:
	mov	d15,#1
	jeq	d15,#0,.L131
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L132
.L131:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L132:
	mov.aa	a4,a13
.L817:
	call	free
.L818:
	ret
.L467:
	
__tft180_show_gray_image_function_end:
	.size	tft180_show_gray_image,__tft180_show_gray_image_function_end-tft180_show_gray_image
.L299:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_rgb565_image',code,cluster('tft180_show_rgb565_image')
	.sect	'.text.zf_device_tft180.tft180_show_rgb565_image'
	.align	2
	
	.global	tft180_show_rgb565_image
; Function tft180_show_rgb565_image
.L219:
tft180_show_rgb565_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L823:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L824:
	ld.hu	d13,[a10]4
.L825:
	ld.bu	d14,[a10]8
.L826:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L827:
	lt.u	d4,d8,d0
.L821:
	movh.a	a4,#@his(.1.str)
.L820:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#816
.L822:
	call	debug_assert_handler
.L819:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d15,[a15]0
.L828:
	lt.u	d4,d9,d15
.L829:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#817
	call	debug_assert_handler
.L1196:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#818
	call	debug_assert_handler
.L493:
	mul	d4,d12,#2
	call	malloc
.L830:
	mov.aa	a13,a2
.L832:
	mov	d15,#0
	jeq	d15,#0,.L133
	mov	d4,#482
	call	get_port
.L831:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L134
.L133:
	mov	d4,#482
	call	get_port
.L833:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L134:
	add	d15,d8,d12
.L834:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L835:
	add	d15,d9,d13
.L836:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L837:
	mov	e4,d9,d8
.L838:
	call	tft180_set_region
.L1197:
	mov	d8,#0
.L839:
	j	.L135
.L136:
	mul	d15,d8,d11
.L840:
	div.u	e0,d15,d13
.L841:
	mul	d15,d0,d10
.L842:
	mul	d15,d15,#2
	addsc.a	a15,a12,d15,#0
.L843:
	mov	d0,#0
.L844:
	j	.L137
.L138:
	mul	d15,d0,#2
	addsc.a	a2,a13,d15,#0
.L846:
	mul	d15,d0,d10
.L847:
	div.u	e2,d15,d12
.L1198:
	mul	d15,d2,#2
	addsc.a	a4,a15,d15,#0
.L1199:
	ld.hu	d15,[a4]0
.L1200:
	st.h	[a2],d15
.L1201:
	add	d0,#1
.L137:
	jlt.u	d0,d12,.L138
.L1202:
	jeq	d14,#0,.L139
.L1203:
	mov	d4,#2
	mul	d5,d12,#2
	mov.aa	a4,a13
.L848:
	call	spi_write_8bit_array
.L845:
	j	.L140
.L139:
	mov	d4,#2
	mov.aa	a4,a13
.L849:
	mov	d5,d12
.L850:
	call	spi_write_16bit_array
.L140:
	add	d8,#1
.L135:
	jlt.u	d8,d13,.L136
.L1204:
	mov	d15,#1
	jeq	d15,#0,.L141
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L142
.L141:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L142:
	mov.aa	a4,a13
.L851:
	call	free
.L852:
	ret
.L483:
	
__tft180_show_rgb565_image_function_end:
	.size	tft180_show_rgb565_image,__tft180_show_rgb565_image_function_end-tft180_show_rgb565_image
.L304:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_wave',code,cluster('tft180_show_wave')
	.sect	'.text.zf_device_tft180.tft180_show_wave'
	.align	2
	
	.global	tft180_show_wave
; Function tft180_show_wave
.L221:
tft180_show_wave:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L857:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L858:
	ld.hu	d13,[a10]4
.L859:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L860:
	lt.u	d4,d8,d0
.L855:
	movh.a	a4,#@his(.1.str)
.L854:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#863
.L856:
	call	debug_assert_handler
.L853:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L861:
	lt.u	d4,d9,d0
.L862:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#864
	call	debug_assert_handler
.L1209:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#865
	call	debug_assert_handler
.L506:
	mul	d4,d12,#2
	call	malloc
.L863:
	mov.aa	a13,a2
.L865:
	mov	d15,#0
	jeq	d15,#0,.L143
	mov	d4,#482
	call	get_port
.L864:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L144
.L143:
	mov	d4,#482
	call	get_port
.L866:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L144:
	add	d15,d8,d12
.L867:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L868:
	add	d15,d9,d13
.L869:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L870:
	mov	e4,d9,d8
.L871:
	call	tft180_set_region
.L1210:
	mov	d14,#0
.L872:
	j	.L145
.L146:
	mov	d0,#0
.L874:
	j	.L147
.L148:
	mul	d15,d0,#2
	addsc.a	a15,a13,d15,#0
.L1211:
	movh.a	a2,#@his(tft180_bgcolor)
	lea	a2,[a2]@los(tft180_bgcolor)
	ld.hu	d15,[a2]0
.L1212:
	st.h	[a15],d15
.L1213:
	add	d0,#1
.L147:
	jlt.u	d0,d12,.L148
.L1214:
	mov	d4,#2
	mov.aa	a4,a13
.L876:
	mov	d5,d12
.L877:
	call	spi_write_16bit_array
.L875:
	add	d14,#1
.L145:
	jlt.u	d14,d13,.L146
.L1215:
	mov	d15,#1
	jeq	d15,#0,.L149
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L150
.L149:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L150:
	mov	d14,#0
.L873:
	j	.L151
.L152:
	mul	d15,d14,d10
.L878:
	div.u	e0,d15,d12
.L879:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L1216:
	ld.hu	d0,[a15]0
.L880:
	add	d15,d13,#-1
.L1217:
	mul	d0,d15
.L881:
	div	e0,d0,d11
.L882:
	add	d15,d14,d8
.L884:
	extr.u	d4,d15,#0,#16
.L1218:
	add	d15,d13,#-1
.L1219:
	sub	d15,d0
.L885:
	add	d15,d9
.L886:
	extr.u	d5,d15,#0,#16
.L1220:
	movh.a	a15,#@his(tft180_pencolor)
	lea	a15,[a15]@los(tft180_pencolor)
	ld.hu	d6,[a15]0
	call	tft180_draw_point
.L883:
	add	d14,#1
.L151:
	jlt.u	d14,d12,.L152
.L1221:
	mov.aa	a4,a13
.L887:
	call	free
.L888:
	ret
.L498:
	
__tft180_show_wave_function_end:
	.size	tft180_show_wave,__tft180_show_wave_function_end-tft180_show_wave
.L309:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_show_chinese',code,cluster('tft180_show_chinese')
	.sect	'.text.zf_device_tft180.tft180_show_chinese'
	.align	2
	
	.global	tft180_show_chinese
; Function tft180_show_chinese
.L223:
tft180_show_chinese:	.type	func
	sub.a	a10,#8
.L889:
	mov	e8,d5,d4
	mov	d10,d6
.L894:
	mov.aa	a12,a4
.L895:
	st.w	[a10]4,d7
.L896:
	ld.hu	d11,[a10]8
.L897:
	movh.a	a15,#@his(tft180_width_max)
	lea	a15,[a15]@los(tft180_width_max)
	ld.hu	d0,[a15]0
.L898:
	lt.u	d4,d8,d0
.L892:
	movh.a	a4,#@his(.1.str)
.L890:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#907
.L893:
	call	debug_assert_handler
.L891:
	movh.a	a15,#@his(tft180_height_max)
	lea	a15,[a15]@los(tft180_height_max)
	ld.hu	d0,[a15]0
.L899:
	lt.u	d4,d9,d0
.L900:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#908
	call	debug_assert_handler
.L1226:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#909
	call	debug_assert_handler
.L520:
	mov	d15,#8
.L1227:
	div	e12,d10,d15
.L901:
	mov	d15,#0
	jeq	d15,#0,.L153
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L154
.L153:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L154:
	ld.w	d15,[a10]4
.L902:
	mul	d15,d10
.L903:
	add	d15,#-1
.L904:
	add	d15,d8
.L905:
	extr.u	d6,d15,#0,#16
.L906:
	add	d15,d9,d10
.L907:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L908:
	mov	e4,d9,d8
.L909:
	call	tft180_set_region
.L1228:
	mov	d8,#0
.L910:
	j	.L155
.L156:
	ld.w	d15,[a10]4
.L911:
	st.w	[a10],d15
.L913:
	mul	d15,d8,d12
.L912:
	addsc.a	a15,a12,d15,#0
.L914:
	j	.L157
.L158:
	mov	d9,#0
.L916:
	j	.L159
.L160:
	mov	d14,#8
.L917:
	j	.L161
.L162:
	ld.bu	d0,[a15]
.L1229:
	add	d15,d14,#-1
.L1230:
	rsub	d15,#0
	sha	d0,d0,d15
.L1231:
	and	d15,d0,#1
.L918:
	jeq	d15,#0,.L163
.L1232:
	mov	d4,#2
	mov	d5,d11
.L919:
	call	spi_write_16bit
.L920:
	j	.L164
.L163:
	mov	d4,#2
	movh.a	a2,#@his(tft180_bgcolor)
	lea	a2,[a2]@los(tft180_bgcolor)
	ld.hu	d5,[a2]0
	call	spi_write_16bit
.L164:
	add	d14,#-1
.L161:
	jge	d14,#1,.L162
.L1233:
	add.a	a15,#1
.L1234:
	add	d9,#1
.L159:
	jlt	d9,d12,.L160
.L1235:
	mul	d15,d12,d10
.L1236:
	mov.d	d0,a15
.L921:
	sub	d0,d12
.L922:
	add	d15,d0
.L915:
	mov.a	a15,d15
.L157:
	ld.w	d15,[a10]
.L923:
	add	d0,d15,#-1
	extr.u	d0,d0,#0,#8
.L924:
	st.w	[a10],d0
.L925:
	jne	d15,#0,.L158
.L926:
	add	d8,#1
.L155:
	jlt	d8,d10,.L156
.L1237:
	mov	d15,#1
	jeq	d15,#0,.L165
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L166
.L165:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L166:
	ret
.L512:
	
__tft180_show_chinese_function_end:
	.size	tft180_show_chinese,__tft180_show_chinese_function_end-tft180_show_chinese
.L314:
	; End of function
	
	.sdecl	'.text.zf_device_tft180.tft180_init',code,cluster('tft180_init')
	.sect	'.text.zf_device_tft180.tft180_init'
	.align	2
	
	.global	tft180_init
; Function tft180_init
.L225:
tft180_init:	.type	func
	sub.a	a10,#16
.L927:
	mov	d15,#211
	st.h	[a10],d15
.L1242:
	mov	d15,#217
	st.h	[a10]4,d15
.L1243:
	mov	d15,#403
	st.h	[a10]8,d15
.L1244:
	mov	d4,#2
.L1245:
	mov	d5,#0
.L1246:
	mov.u	d6,#34560
	addih	d6,d6,#915
.L1247:
	mov	d7,#206
	call	spi_init
.L1248:
	mov	d4,#480
.L1249:
	mov	d5,#1
.L1250:
	mov	d6,#0
.L1251:
	mov	d7,#3
	call	gpio_init
.L1252:
	mov	d4,#481
.L1253:
	mov	d5,#1
.L1254:
	mov	d6,#0
.L1255:
	mov	d7,#3
	call	gpio_init
.L1256:
	mov	d4,#482
.L1257:
	mov	d5,#1
.L1258:
	mov	d6,#1
.L1259:
	mov	d7,#3
	call	gpio_init
.L1260:
	mov	d4,#484
.L1261:
	mov	d5,#1
.L1262:
	mov	d6,#1
.L1263:
	mov	d7,#3
	call	gpio_init
.L1264:
	movh.a	a15,#@his(tft180_display_dir)
	lea	a15,[a15]@los(tft180_display_dir)
	ld.bu	d4,[a15]
	call	tft180_set_dir
.L1265:
	movh.a	a15,#@his(tft180_pencolor)
	lea	a15,[a15]@los(tft180_pencolor)
	ld.hu	d4,[a15]0
.L1266:
	movh.a	a15,#@his(tft180_bgcolor)
	lea	a15,[a15]@los(tft180_bgcolor)
	ld.hu	d5,[a15]0
	call	tft180_set_color
.L1267:
	mov	d15,#0
	jeq	d15,#0,.L167
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L168
.L167:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L168:
	mov	d4,#10
	call	system_delay_ms
.L1268:
	mov	d15,#1
	jeq	d15,#0,.L169
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L170
.L169:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L170:
	mov	d4,#120
	call	system_delay_ms
.L1269:
	mov	d15,#0
	jeq	d15,#0,.L171
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L172
.L171:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L172:
	mov	d4,#17
	call	tft180_write_index
.L1270:
	mov	d4,#120
	call	system_delay_ms
.L1271:
	mov	d4,#177
	call	tft180_write_index
.L1272:
	mov	d4,#2
	mov	d5,#1
	call	spi_write_8bit
.L1273:
	mov	d4,#2
	mov	d5,#44
	call	spi_write_8bit
.L1274:
	mov	d4,#2
	mov	d5,#45
	call	spi_write_8bit
.L1275:
	mov	d4,#178
	call	tft180_write_index
.L1276:
	mov	d4,#2
	mov	d5,#1
	call	spi_write_8bit
.L1277:
	mov	d4,#2
	mov	d5,#44
	call	spi_write_8bit
.L1278:
	mov	d4,#2
	mov	d5,#45
	call	spi_write_8bit
.L1279:
	mov	d4,#179
	call	tft180_write_index
.L1280:
	mov	d4,#2
	mov	d5,#1
	call	spi_write_8bit
.L1281:
	mov	d4,#2
	mov	d5,#44
	call	spi_write_8bit
.L1282:
	mov	d4,#2
	mov	d5,#45
	call	spi_write_8bit
.L1283:
	mov	d4,#2
	mov	d5,#1
	call	spi_write_8bit
.L1284:
	mov	d4,#2
	mov	d5,#44
	call	spi_write_8bit
.L1285:
	mov	d4,#2
	mov	d5,#45
	call	spi_write_8bit
.L1286:
	mov	d4,#180
	call	tft180_write_index
.L1287:
	mov	d4,#2
	mov	d5,#7
	call	spi_write_8bit
.L1288:
	mov	d4,#192
	call	tft180_write_index
.L1289:
	mov	d4,#2
	mov	d5,#162
	call	spi_write_8bit
.L1290:
	mov	d4,#2
	mov	d5,#2
	call	spi_write_8bit
.L1291:
	mov	d4,#2
	mov	d5,#132
	call	spi_write_8bit
.L1292:
	mov	d4,#193
	call	tft180_write_index
.L1293:
	mov	d4,#2
	mov	d5,#197
	call	spi_write_8bit
.L1294:
	mov	d4,#194
	call	tft180_write_index
.L1295:
	mov	d4,#2
	mov	d5,#10
	call	spi_write_8bit
.L1296:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1297:
	mov	d4,#195
	call	tft180_write_index
.L1298:
	mov	d4,#2
	mov	d5,#138
	call	spi_write_8bit
.L1299:
	mov	d4,#2
	mov	d5,#42
	call	spi_write_8bit
.L1300:
	mov	d4,#196
	call	tft180_write_index
.L1301:
	mov	d4,#2
	mov	d5,#138
	call	spi_write_8bit
.L1302:
	mov	d4,#2
	mov	d5,#238
	call	spi_write_8bit
.L1303:
	mov	d4,#197
	call	tft180_write_index
.L1304:
	mov	d4,#2
	mov	d5,#14
	call	spi_write_8bit
.L1305:
	mov	d4,#54
	call	tft180_write_index
.L1306:
	movh.a	a15,#@his(tft180_display_dir)
	lea	a15,[a15]@los(tft180_display_dir)
	ld.bu	d15,[a15]
.L1307:
	mov	d0,#0
	jeq	d15,d0,.L173
.L1308:
	mov	d0,#1
	jeq	d15,d0,.L174
.L1309:
	mov	d0,#2
	jeq	d15,d0,.L175
.L1310:
	mov	d0,#3
	jeq	d15,d0,.L176
	j	.L177
.L173:
	mov	d4,#2
	mov	d5,#192
	call	spi_write_8bit
.L1311:
	j	.L178
.L174:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1312:
	j	.L179
.L175:
	mov	d4,#2
	mov	d5,#160
	call	spi_write_8bit
.L1313:
	j	.L180
.L176:
	mov	d4,#2
	mov	d5,#96
	call	spi_write_8bit
.L1314:
	j	.L181
.L177:
.L181:
.L180:
.L179:
.L178:
	mov	d4,#224
	call	tft180_write_index
.L1315:
	mov	d4,#2
	mov	d5,#15
	call	spi_write_8bit
.L1316:
	mov	d4,#2
	mov	d5,#26
	call	spi_write_8bit
.L1317:
	mov	d4,#2
	mov	d5,#15
	call	spi_write_8bit
.L1318:
	mov	d4,#2
	mov	d5,#24
	call	spi_write_8bit
.L1319:
	mov	d4,#2
	mov	d5,#47
	call	spi_write_8bit
.L1320:
	mov	d4,#2
	mov	d5,#40
	call	spi_write_8bit
.L1321:
	mov	d4,#2
	mov	d5,#32
	call	spi_write_8bit
.L1322:
	mov	d4,#2
	mov	d5,#34
	call	spi_write_8bit
.L1323:
	mov	d4,#2
	mov	d5,#31
	call	spi_write_8bit
.L1324:
	mov	d4,#2
	mov	d5,#27
	call	spi_write_8bit
.L1325:
	mov	d4,#2
	mov	d5,#35
	call	spi_write_8bit
.L1326:
	mov	d4,#2
	mov	d5,#55
	call	spi_write_8bit
.L1327:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1328:
	mov	d4,#2
	mov	d5,#7
	call	spi_write_8bit
.L1329:
	mov	d4,#2
	mov	d5,#2
	call	spi_write_8bit
.L1330:
	mov	d4,#2
	mov	d5,#16
	call	spi_write_8bit
.L1331:
	mov	d4,#225
	call	tft180_write_index
.L1332:
	mov	d4,#2
	mov	d5,#15
	call	spi_write_8bit
.L1333:
	mov	d4,#2
	mov	d5,#27
	call	spi_write_8bit
.L1334:
	mov	d4,#2
	mov	d5,#15
	call	spi_write_8bit
.L1335:
	mov	d4,#2
	mov	d5,#23
	call	spi_write_8bit
.L1336:
	mov	d4,#2
	mov	d5,#51
	call	spi_write_8bit
.L1337:
	mov	d4,#2
	mov	d5,#44
	call	spi_write_8bit
.L1338:
	mov	d4,#2
	mov	d5,#41
	call	spi_write_8bit
.L1339:
	mov	d4,#2
	mov	d5,#46
	call	spi_write_8bit
.L1340:
	mov	d4,#2
	mov	d5,#48
	call	spi_write_8bit
.L1341:
	mov	d4,#2
	mov	d5,#48
	call	spi_write_8bit
.L1342:
	mov	d4,#2
	mov	d5,#57
	call	spi_write_8bit
.L1343:
	mov	d4,#2
	mov	d5,#63
	call	spi_write_8bit
.L1344:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1345:
	mov	d4,#2
	mov	d5,#7
	call	spi_write_8bit
.L1346:
	mov	d4,#2
	mov	d5,#3
	call	spi_write_8bit
.L1347:
	mov	d4,#2
	mov	d5,#16
	call	spi_write_8bit
.L1348:
	mov	d4,#42
	call	tft180_write_index
.L1349:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1350:
	mov	d4,#2
	mov	d5,#2
	call	spi_write_8bit
.L1351:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1352:
	mov	d4,#2
	mov	d5,#130
	call	spi_write_8bit
.L1353:
	mov	d4,#43
	call	tft180_write_index
.L1354:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1355:
	mov	d4,#2
	mov	d5,#3
	call	spi_write_8bit
.L1356:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1357:
	mov	d4,#2
	mov	d5,#131
	call	spi_write_8bit
.L1358:
	mov	d4,#240
	call	tft180_write_index
.L1359:
	mov	d4,#2
	mov	d5,#1
	call	spi_write_8bit
.L1360:
	mov	d4,#246
	call	tft180_write_index
.L1361:
	mov	d4,#2
	mov	d5,#0
	call	spi_write_8bit
.L1362:
	mov	d4,#58
	call	tft180_write_index
.L1363:
	mov	d4,#2
	mov	d5,#5
	call	spi_write_8bit
.L1364:
	mov	d4,#41
	call	tft180_write_index
.L1365:
	mov	d15,#1
	jeq	d15,#0,.L182
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L183
.L182:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L183:
	call	tft180_clear
.L1366:
	call	tft180_debug_init
.L1367:
	ret
.L528:
	
__tft180_init_function_end:
	.size	tft180_init,__tft180_init_function_end-tft180_init
.L319:
	; End of function
	
	.sdecl	'.data.zf_device_tft180.tft180_width_max',data,cluster('tft180_width_max')
	.sect	'.data.zf_device_tft180.tft180_width_max'
	.global	tft180_width_max
	.align	2
tft180_width_max:	.type	object
	.size	tft180_width_max,2
	.half	160
	.sdecl	'.data.zf_device_tft180.tft180_height_max',data,cluster('tft180_height_max')
	.sect	'.data.zf_device_tft180.tft180_height_max'
	.global	tft180_height_max
	.align	2
tft180_height_max:	.type	object
	.size	tft180_height_max,2
	.half	128
	.sdecl	'.data.zf_device_tft180.tft180_pencolor',data,cluster('tft180_pencolor')
	.sect	'.data.zf_device_tft180.tft180_pencolor'
	.align	2
tft180_pencolor:	.type	object
	.size	tft180_pencolor,2
	.half	63488
	.sdecl	'.data.zf_device_tft180.tft180_bgcolor',data,cluster('tft180_bgcolor')
	.sect	'.data.zf_device_tft180.tft180_bgcolor'
	.align	2
tft180_bgcolor:	.type	object
	.size	tft180_bgcolor,2
	.half	65535
	.sdecl	'.data.zf_device_tft180.tft180_display_dir',data,cluster('tft180_display_dir')
	.sect	'.data.zf_device_tft180.tft180_display_dir'
tft180_display_dir:	.type	object
	.size	tft180_display_dir,1
	.byte	2
	.sdecl	'.data.zf_device_tft180.tft180_display_font',data,cluster('tft180_display_font')
	.sect	'.data.zf_device_tft180.tft180_display_font'
tft180_display_font:	.type	object
	.size	tft180_display_font,1
	.byte	1
	.sdecl	'.rodata.zf_device_tft180..1.str',data,rom
	.sect	'.rodata.zf_device_tft180..1.str'
.1.str:	.type	object
	.size	.1.str,42
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,116,102,116,49,56,48,46
	.byte	99
	.space	1
	.calls	'tft180_draw_line','__f_ftos'
	.calls	'tft180_show_float','__d_mul'
	.calls	'tft180_show_float','__d_dtoi'
	.calls	'tft180_show_float','__d_itod'
	.calls	'tft180_show_float','__d_sub'
	.calls	'__INDIRECT__','tft180_clear'
	.calls	'__INDIRECT__','tft180_show_string'
	.calls	'tft180_write_index','get_port'
	.calls	'tft180_write_index','spi_write_8bit'
	.calls	'tft180_set_region','debug_assert_handler'
	.calls	'tft180_set_region','tft180_write_index'
	.calls	'tft180_set_region','spi_write_8bit'
	.calls	'tft180_debug_init','debug_output_struct_init'
	.calls	'tft180_debug_init','debug_output_init'
	.calls	'tft180_clear','get_port'
	.calls	'tft180_clear','tft180_set_region'
	.calls	'tft180_clear','spi_write_16bit_array'
	.calls	'tft180_full','get_port'
	.calls	'tft180_full','tft180_set_region'
	.calls	'tft180_full','spi_write_16bit_array'
	.calls	'tft180_draw_point','debug_assert_handler'
	.calls	'tft180_draw_point','get_port'
	.calls	'tft180_draw_point','tft180_set_region'
	.calls	'tft180_draw_point','spi_write_16bit'
	.calls	'tft180_draw_line','debug_assert_handler'
	.calls	'tft180_draw_line','tft180_draw_point'
	.calls	'tft180_show_char','debug_assert_handler'
	.calls	'tft180_show_char','get_port'
	.calls	'tft180_show_char','tft180_set_region'
	.calls	'tft180_show_char','spi_write_16bit_array'
	.calls	'tft180_show_string','debug_assert_handler'
	.calls	'tft180_show_string','tft180_show_char'
	.calls	'tft180_show_int','debug_assert_handler'
	.calls	'tft180_show_int','memset'
	.calls	'tft180_show_int','func_int_to_str'
	.calls	'tft180_show_int','tft180_show_string'
	.calls	'tft180_show_uint','debug_assert_handler'
	.calls	'tft180_show_uint','memset'
	.calls	'tft180_show_uint','func_uint_to_str'
	.calls	'tft180_show_uint','tft180_show_string'
	.calls	'tft180_show_float','debug_assert_handler'
	.calls	'tft180_show_float','memset'
	.calls	'tft180_show_float','func_double_to_str'
	.calls	'tft180_show_float','tft180_show_string'
	.calls	'tft180_show_binary_image','debug_assert_handler'
	.calls	'tft180_show_binary_image','get_port'
	.calls	'tft180_show_binary_image','tft180_set_region'
	.calls	'tft180_show_binary_image','spi_write_16bit_array'
	.calls	'tft180_show_gray_image','debug_assert_handler'
	.calls	'tft180_show_gray_image','get_port'
	.calls	'tft180_show_gray_image','tft180_set_region'
	.calls	'tft180_show_gray_image','spi_write_16bit_array'
	.calls	'tft180_show_rgb565_image','debug_assert_handler'
	.calls	'tft180_show_rgb565_image','get_port'
	.calls	'tft180_show_rgb565_image','tft180_set_region'
	.calls	'tft180_show_rgb565_image','spi_write_8bit_array'
	.calls	'tft180_show_rgb565_image','spi_write_16bit_array'
	.calls	'tft180_show_wave','debug_assert_handler'
	.calls	'tft180_show_wave','get_port'
	.calls	'tft180_show_wave','tft180_set_region'
	.calls	'tft180_show_wave','spi_write_16bit_array'
	.calls	'tft180_show_wave','tft180_draw_point'
	.calls	'tft180_show_chinese','debug_assert_handler'
	.calls	'tft180_show_chinese','get_port'
	.calls	'tft180_show_chinese','tft180_set_region'
	.calls	'tft180_show_chinese','spi_write_16bit'
	.calls	'tft180_init','spi_init'
	.calls	'tft180_init','gpio_init'
	.calls	'tft180_init','tft180_set_dir'
	.calls	'tft180_init','tft180_set_color'
	.calls	'tft180_init','get_port'
	.calls	'tft180_init','system_delay_ms'
	.calls	'tft180_init','tft180_write_index'
	.calls	'tft180_init','spi_write_8bit'
	.calls	'tft180_init','tft180_clear'
	.calls	'tft180_init','tft180_debug_init'
	.calls	'tft180_write_index','',0
	.calls	'tft180_set_region','',0
	.calls	'tft180_debug_init','',24
	.calls	'tft180_clear','',0
	.calls	'tft180_full','',0
	.calls	'tft180_set_dir','',0
	.calls	'tft180_set_font','',0
	.calls	'tft180_set_color','',0
	.calls	'tft180_draw_point','',0
	.calls	'tft180_draw_line','',8
	.calls	'tft180_show_char','',256
	.calls	'tft180_show_string','',0
	.calls	'tft180_show_int','',16
	.calls	'tft180_show_uint','',16
	.calls	'tft180_show_float','',24
	.calls	'tft180_show_binary_image','',0
	.calls	'tft180_show_gray_image','',0
	.calls	'tft180_show_rgb565_image','',0
	.calls	'tft180_show_wave','',0
	.calls	'tft180_show_chinese','',8
	.extern	memset
	.extern	debug_assert_handler
	.extern	debug_output_struct_init
	.extern	debug_output_init
	.extern	ascii_font_8x16
	.extern	ascii_font_6x8
	.extern	func_int_to_str
	.extern	func_uint_to_str
	.extern	func_double_to_str
	.extern	system_delay_ms
	.extern	get_port
	.extern	gpio_init
	.extern	spi_write_8bit
	.extern	spi_write_8bit_array
	.extern	spi_write_16bit
	.extern	spi_write_16bit_array
	.extern	spi_init
	.extern	malloc
	.extern	free
	.extern	__f_ftos
	.extern	__d_mul
	.extern	__d_dtoi
	.extern	__d_itod
	.extern	__d_sub
	.extern	__INDIRECT__
	.calls	'tft180_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L227:
	.word	42017
	.half	3
	.word	.L228
	.byte	4
.L226:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L229
	.byte	2,1,1,3
	.word	204
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	207
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L385:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	252
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	264
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	344
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	318
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	350
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	350
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	318
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L422:
	.byte	7
	.byte	'int',0,4,5
.L394:
	.byte	7
	.byte	'unsigned char',0,1,8
.L350:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1385
	.byte	4,2,35,0,0,14,4
	.word	459
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1728
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1943
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2160
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2380
	.byte	4,2,35,0,0,14,24
	.word	459
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2703
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3007
	.byte	4,2,35,0,0,14,8
	.word	459
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3332
	.byte	4,2,35,0,0,14,12
	.word	459
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3672
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	436
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4038
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4324
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4471
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	436
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4640
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4812
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	476
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5161
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5335
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6000
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6348
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6472
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6556
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6736
	.byte	4,2,35,0,0,14,76
	.word	459
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7076
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	774
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1345
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1464
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1504
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1688
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1903
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2120
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2340
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1504
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2654
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2694
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2967
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3283
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3323
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3623
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3663
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	3998
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4284
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3323
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4431
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4600
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4772
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4947
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5121
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5295
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5471
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5627
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5960
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6308
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3323
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6432
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6681
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6940
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6980
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7036
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7603
	.byte	4,3,35,252,1,0,16
	.word	7643
	.byte	3
	.word	8246
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8251
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	459
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8256
	.byte	6,0,19
	.byte	'__free',0,1,1,1,1,20
	.word	350
	.byte	0,16
	.word	350
	.byte	21
	.byte	'__alloc',0
	.word	8455
	.byte	1,1,1,1,20
	.word	436
	.byte	0,22
	.byte	'memset',0,5,56,17
	.word	350
	.byte	1,1,1,1,23,5,56,33
	.word	350
	.byte	23,5,56,36
	.word	452
	.byte	23,5,56,41
	.word	436
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	8527
	.byte	24
	.byte	'debug_assert_handler',0,6,112,9,1,1,1,1,5
	.byte	'pass',0,6,112,47
	.word	459
	.byte	5
	.byte	'file',0,6,112,59
	.word	8535
	.byte	5
	.byte	'line',0,6,112,69
	.word	452
	.byte	0,25
	.word	8527
.L411:
	.byte	3
	.word	8609
	.byte	26,1,1,20
	.word	8614
	.byte	0,3
	.word	8619
	.byte	26,1,1,20
	.word	476
	.byte	20
	.word	476
	.byte	20
	.word	8614
	.byte	0,3
	.word	8633
.L538:
	.byte	27,6,86,9,20,13
	.byte	'type_index',0
	.word	476
	.byte	2,2,35,0,13
	.byte	'display_x_max',0
	.word	476
	.byte	2,2,35,2,13
	.byte	'display_y_max',0
	.word	476
	.byte	2,2,35,4,13
	.byte	'font_x_size',0
	.word	459
	.byte	1,2,35,6,13
	.byte	'font_y_size',0
	.word	459
	.byte	1,2,35,7,13
	.byte	'output_uart',0
	.word	8628
	.byte	4,2,35,8,13
	.byte	'output_screen',0
	.word	8652
	.byte	4,2,35,12,13
	.byte	'output_screen_clear',0
	.word	207
	.byte	4,2,35,16,0,3
	.word	8657
	.byte	24
	.byte	'debug_output_struct_init',0,6,114,9,1,1,1,1,5
	.byte	'info',0,6,114,62
	.word	8844
	.byte	0,24
	.byte	'debug_output_init',0,6,115,9,1,1,1,1,5
	.byte	'info',0,6,115,62
	.word	8844
	.byte	0,24
	.byte	'func_int_to_str',0,7,80,13,1,1,1,1,5
	.byte	'str',0,7,80,56
	.word	8535
	.byte	5
	.byte	'number',0,7,80,67
	.word	452
	.byte	0
.L434:
	.byte	7
	.byte	'unsigned long int',0,4,7,24
	.byte	'func_uint_to_str',0,7,82,13,1,1,1,1,5
	.byte	'str',0,7,82,56
	.word	8535
	.byte	5
	.byte	'number',0,7,82,68
	.word	8988
	.byte	0
.L446:
	.byte	7
	.byte	'double',0,8,4,24
	.byte	'func_double_to_str',0,7,86,13,1,1,1,1,5
	.byte	'str',0,7,86,56
	.word	8535
	.byte	5
	.byte	'number',0,7,86,68
	.word	9062
	.byte	5
	.byte	'point_bit',0,7,86,82
	.word	459
	.byte	0,24
	.byte	'system_delay_ms',0,8,46,9,1,1,1,1,5
	.byte	'time',0,8,46,45
	.word	8988
	.byte	0,28
	.word	212
	.byte	29
	.word	238
	.byte	6,0,28
	.word	273
	.byte	29
	.word	305
	.byte	6,0,28
	.word	355
	.byte	29
	.word	374
	.byte	6,0,28
	.word	390
	.byte	29
	.word	405
	.byte	29
	.word	419
	.byte	6,0,28
	.word	8359
	.byte	29
	.word	8387
	.byte	29
	.word	8401
	.byte	29
	.word	8419
	.byte	6,0,17,9,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'get_port',0,9,114,13
	.word	8251
	.byte	1,1,1,1,5
	.byte	'pin',0,9,114,56
	.word	9258
	.byte	0,17,9,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,9,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,24
	.byte	'gpio_init',0,9,143,1,7,1,1,1,1,5
	.byte	'pin',0,9,143,1,40
	.word	9258
	.byte	5
	.byte	'dir',0,9,143,1,59
	.word	11232
	.byte	5
	.byte	'dat',0,9,143,1,70
	.word	459
	.byte	5
	.byte	'pinconf',0,9,143,1,90
	.word	11250
	.byte	0,17,10,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,25
	.word	459
	.byte	24
	.byte	'spi_write_8bit',0,10,143,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,143,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,143,1,80
	.word	11451
	.byte	0,25
	.word	459
.L454:
	.byte	3
	.word	11510
	.byte	24
	.byte	'spi_write_8bit_array',0,10,144,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,144,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,144,1,81
	.word	11515
	.byte	5
	.byte	'len',0,10,144,1,94
	.word	8988
	.byte	0,25
	.word	476
	.byte	24
	.byte	'spi_write_16bit',0,10,146,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,146,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,146,1,81
	.word	11593
	.byte	0,25
	.word	476
.L486:
	.byte	3
	.word	11653
	.byte	24
	.byte	'spi_write_16bit_array',0,10,147,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,147,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,147,1,82
	.word	11658
	.byte	5
	.byte	'len',0,10,147,1,95
	.word	8988
	.byte	0,17,10,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,10,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,10,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,10,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,10,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,24
	.byte	'spi_init',0,10,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,170,1,61
	.word	11413
	.byte	5
	.byte	'mode',0,10,170,1,82
	.word	11737
	.byte	5
	.byte	'baud',0,10,170,1,95
	.word	8988
	.byte	5
	.byte	'sck_pin',0,10,170,1,118
	.word	11791
	.byte	5
	.byte	'mosi_pin',0,10,170,1,145,1
	.word	12064
	.byte	5
	.byte	'miso_pin',0,10,170,1,173,1
	.word	12318
	.byte	5
	.byte	'cs_pin',0,10,170,1,199,1
	.word	12572
	.byte	0
.L348:
	.byte	3
	.word	476
.L354:
	.byte	25
	.word	476
.L360:
	.byte	17,11,93,9,1,18
	.byte	'TFT180_PORTAIT',0,0,18
	.byte	'TFT180_PORTAIT_180',0,1,18
	.byte	'TFT180_CROSSWISE',0,2,18
	.byte	'TFT180_CROSSWISE_180',0,3,0
.L363:
	.byte	17,11,101,9,1,18
	.byte	'TFT180_6X8_FONT',0,0,18
	.byte	'TFT180_8X16_FONT',0,1,18
	.byte	'TFT180_16X16_FONT',0,2,0
.L367:
	.byte	25
	.word	476
.L372:
	.byte	25
	.word	476
.L379:
	.byte	25
	.word	476
.L382:
	.byte	7
	.byte	'short int',0,2,5
.L391:
	.byte	25
	.word	8527
.L398:
	.byte	14,96
	.word	476
	.byte	15,47,0
.L403:
	.byte	14,128,2
	.word	476
	.byte	15,127,0
.L418:
	.byte	25
	.word	452
.L425:
	.byte	14,12
	.word	8527
	.byte	15,11,0
.L430:
	.byte	25
	.word	8988
.L441:
	.byte	25
	.word	9062
.L449:
	.byte	14,17
	.word	8527
	.byte	15,16,0
.L518:
	.byte	25
	.word	476
.L530:
	.byte	25
	.word	459
	.byte	30
	.byte	'__INDIRECT__',0,12,1,1,1,1,1,31
	.byte	'__wchar_t',0,12,1,1
	.word	13718
	.byte	31
	.byte	'__size_t',0,12,1,1
	.word	436
	.byte	31
	.byte	'__ptrdiff_t',0,12,1,1
	.word	452
	.byte	32,1,3
	.word	13873
	.byte	31
	.byte	'__codeptr',0,12,1,1
	.word	13875
	.byte	31
	.byte	'__intptr_t',0,12,1,1
	.word	452
	.byte	31
	.byte	'__uintptr_t',0,12,1,1
	.word	436
	.byte	31
	.byte	'size_t',0,13,31,25
	.word	436
	.byte	31
	.byte	'_iob_flag_t',0,13,82,25
	.word	476
	.byte	31
	.byte	'boolean',0,14,101,29
	.word	459
	.byte	31
	.byte	'uint8',0,14,105,29
	.word	459
	.byte	31
	.byte	'uint16',0,14,109,29
	.word	476
	.byte	31
	.byte	'uint32',0,14,113,29
	.word	8988
	.byte	31
	.byte	'uint64',0,14,118,29
	.word	318
	.byte	31
	.byte	'sint16',0,14,126,29
	.word	13718
	.byte	7
	.byte	'long int',0,4,5,31
	.byte	'sint32',0,14,131,1,29
	.word	14062
	.byte	7
	.byte	'long long int',0,8,5,31
	.byte	'sint64',0,14,138,1,29
	.word	14090
	.byte	31
	.byte	'float32',0,14,167,1,29
	.word	264
	.byte	31
	.byte	'pvoid',0,15,57,28
	.word	350
	.byte	31
	.byte	'Ifx_TickTime',0,15,79,28
	.word	14090
	.byte	7
	.byte	'char',0,1,6,31
	.byte	'int8',0,16,54,29
	.word	14175
	.byte	31
	.byte	'int16',0,16,55,29
	.word	13718
	.byte	31
	.byte	'int32',0,16,56,29
	.word	452
	.byte	31
	.byte	'int64',0,16,57,29
	.word	14090
	.byte	31
	.byte	'debug_output_struct',0,6,99,2
	.word	8657
	.byte	14,16
	.word	459
	.byte	15,15,0,33
	.word	14266
	.byte	34,0,25
	.word	14275
	.byte	35
	.byte	'ascii_font_8x16',0,17,61,25
	.word	14282
	.byte	1,1,14,6
	.word	459
	.byte	15,5,0,33
	.word	14313
	.byte	34,0,25
	.word	14322
	.byte	35
	.byte	'ascii_font_6x8',0,17,62,25
	.word	14329
	.byte	1,1,31
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7076
	.byte	31
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6989
	.byte	31
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3332
	.byte	31
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1385
	.byte	31
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2380
	.byte	31
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1513
	.byte	31
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2160
	.byte	31
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1728
	.byte	31
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1943
	.byte	31
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6348
	.byte	31
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6472
	.byte	31
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6556
	.byte	31
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6736
	.byte	31
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4987
	.byte	31
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5511
	.byte	31
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5161
	.byte	31
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5335
	.byte	31
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6000
	.byte	31
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	814
	.byte	31
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4324
	.byte	31
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4812
	.byte	31
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4471
	.byte	31
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4640
	.byte	31
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5667
	.byte	31
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	498
	.byte	31
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4038
	.byte	31
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3672
	.byte	31
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2703
	.byte	31
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3007
	.byte	31
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7603
	.byte	31
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7036
	.byte	31
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3623
	.byte	31
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1464
	.byte	31
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2654
	.byte	31
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1688
	.byte	31
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2340
	.byte	31
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1903
	.byte	31
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2120
	.byte	31
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6432
	.byte	31
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6681
	.byte	31
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6940
	.byte	31
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6308
	.byte	31
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5121
	.byte	31
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5627
	.byte	31
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5295
	.byte	31
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5471
	.byte	31
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1345
	.byte	31
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5960
	.byte	31
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4431
	.byte	31
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4947
	.byte	31
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4600
	.byte	31
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4772
	.byte	31
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	774
	.byte	31
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4284
	.byte	31
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	3998
	.byte	31
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2967
	.byte	31
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3283
	.byte	16
	.word	7643
	.byte	31
	.byte	'Ifx_P',0,4,139,6,3
	.word	15677
	.byte	17,18,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,31
	.byte	'IfxScu_WDTCON1_IR',0,18,255,10,3
	.word	15697
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,19,45,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_ACCEN0_Bits',0,19,79,3
	.word	15819
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,19,82,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1_Bits',0,19,85,3
	.word	16376
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,19,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	436
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,19,94,3
	.word	16453
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,19,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	459
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	459
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON0_Bits',0,19,111,3
	.word	16589
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,19,114,16,4,11
	.byte	'CANDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	459
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON1_Bits',0,19,126,3
	.word	16869
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,19,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON2_Bits',0,19,135,1,3
	.word	17107
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,19,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	459
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	459
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON3_Bits',0,19,150,1,3
	.word	17235
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,19,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	459
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	459
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON4_Bits',0,19,165,1,3
	.word	17478
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,19,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON5_Bits',0,19,174,1,3
	.word	17713
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,19,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6_Bits',0,19,181,1,3
	.word	17841
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,19,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7_Bits',0,19,188,1,3
	.word	17941
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,19,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	459
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CHIPID_Bits',0,19,202,1,3
	.word	18041
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,19,205,1,16,4,11
	.byte	'PWD',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	436
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSCON_Bits',0,19,213,1,3
	.word	18249
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,19,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	476
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSLIM_Bits',0,19,225,1,3
	.word	18414
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,19,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,19,235,1,3
	.word	18597
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,19,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	436
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EICR_Bits',0,19,129,2,3
	.word	18751
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,19,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR_Bits',0,19,143,2,3
	.word	19115
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,19,146,2,16,4,11
	.byte	'POL',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	476
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	459
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_EMSR_Bits',0,19,159,2,3
	.word	19326
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,19,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	436
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG_Bits',0,19,167,2,3
	.word	19578
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,19,170,2,16,4,11
	.byte	'ARI',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG_Bits',0,19,175,2,3
	.word	19696
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,19,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR13CON_Bits',0,19,185,2,3
	.word	19807
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,19,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR33CON_Bits',0,19,195,2,3
	.word	19970
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,19,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,19,205,2,3
	.word	20133
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,19,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,19,215,2,3
	.word	20291
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,19,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	459
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	459
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	459
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	459
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	476
	.byte	10,0,2,35,2,0,31
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,19,232,2,3
	.word	20456
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,19,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	459
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	476
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	459
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,19,245,2,3
	.word	20785
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,19,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROVMON_Bits',0,19,255,2,3
	.word	21006
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,19,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,19,142,3,3
	.word	21169
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,19,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,19,152,3,3
	.word	21441
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,19,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,19,162,3,3
	.word	21594
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,19,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,19,172,3,3
	.word	21750
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,19,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,19,181,3,3
	.word	21912
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,19,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,19,191,3,3
	.word	22055
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,19,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,19,200,3,3
	.word	22220
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,19,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,19,211,3,3
	.word	22365
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,19,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	459
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,19,222,3,3
	.word	22546
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,19,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,19,232,3,3
	.word	22720
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,19,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,19,241,3,3
	.word	22880
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,19,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,19,130,4,3
	.word	23024
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,19,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,19,139,4,3
	.word	23298
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,19,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,19,149,4,3
	.word	23437
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,19,152,4,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	459
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	476
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	459
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	459
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_EXTCON_Bits',0,19,163,4,3
	.word	23600
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,19,166,4,16,4,11
	.byte	'STEP',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	476
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_FDR_Bits',0,19,174,4,3
	.word	23818
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,19,177,4,16,4,11
	.byte	'FS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_FMR_Bits',0,19,197,4,3
	.word	23981
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,19,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_ID_Bits',0,19,205,4,3
	.word	24317
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,19,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	459
	.byte	2,0,2,35,3,0,31
	.byte	'Ifx_SCU_IGCR_Bits',0,19,232,4,3
	.word	24424
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,19,235,4,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_IN_Bits',0,19,240,4,3
	.word	24876
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,19,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_IOCR_Bits',0,19,250,4,3
	.word	24975
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,19,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	476
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,19,131,5,3
	.word	25125
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,19,134,5,16,4,11
	.byte	'SEED',0,4
	.word	436
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,19,141,5,3
	.word	25274
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,19,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	436
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,19,149,5,3
	.word	25435
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,19,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	476
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LCLCON_Bits',0,19,158,5,3
	.word	25565
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,19,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST_Bits',0,19,166,5,3
	.word	25697
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,19,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	459
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	476
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_MANID_Bits',0,19,174,5,3
	.word	25812
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,19,177,5,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	476
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	476
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_OMR_Bits',0,19,185,5,3
	.word	25923
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,19,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	459
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	459
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	459
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_OSCCON_Bits',0,19,209,5,3
	.word	26081
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,19,212,5,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_OUT_Bits',0,19,217,5,3
	.word	26493
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,19,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	476
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	6,0,2,35,3,0,31
	.byte	'Ifx_SCU_OVCCON_Bits',0,19,233,5,3
	.word	26594
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,19,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	436
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,19,242,5,3
	.word	26861
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,19,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC_Bits',0,19,250,5,3
	.word	26997
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,19,253,5,16,4,11
	.byte	'PD0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDR_Bits',0,19,132,6,3
	.word	27108
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,19,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR_Bits',0,19,146,6,3
	.word	27241
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,19,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLCON0_Bits',0,19,166,6,3
	.word	27444
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,19,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	459
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	459
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON1_Bits',0,19,177,6,3
	.word	27800
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,19,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON2_Bits',0,19,184,6,3
	.word	27978
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,19,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	459
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,19,204,6,3
	.word	28078
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,19,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	459
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,19,215,6,3
	.word	28448
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,19,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,19,227,6,3
	.word	28634
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,19,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,19,241,6,3
	.word	28832
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,19,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR_Bits',0,19,251,6,3
	.word	29065
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,19,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	459
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	459
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	459
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,19,153,7,3
	.word	29217
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,19,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	459
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,19,170,7,3
	.word	29784
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,19,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	459
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,19,187,7,3
	.word	30078
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,19,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	459
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	459
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,19,214,7,3
	.word	30356
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,19,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	476
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,19,230,7,3
	.word	30852
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,19,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	476
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON2_Bits',0,19,243,7,3
	.word	31165
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,19,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	459
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON_Bits',0,19,129,8,3
	.word	31374
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,19,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	459
	.byte	3,0,2,35,3,0,31
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,19,155,8,3
	.word	31585
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,19,158,8,16,4,11
	.byte	'HBT',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	436
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON_Bits',0,19,162,8,3
	.word	32017
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,19,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	459
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	459
	.byte	7,0,2,35,3,0,31
	.byte	'Ifx_SCU_STSTAT_Bits',0,19,178,8,3
	.word	32113
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,19,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,19,186,8,3
	.word	32373
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,19,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	459
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	436
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON_Bits',0,19,198,8,3
	.word	32498
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,19,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,19,208,8,3
	.word	32695
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,19,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,19,218,8,3
	.word	32848
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,19,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET_Bits',0,19,228,8,3
	.word	33001
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,19,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,19,238,8,3
	.word	33154
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,19,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	33309
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33309
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33309
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33309
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,19,247,8,3
	.word	33325
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,19,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,19,134,9,3
	.word	33455
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,19,137,9,16,4,11
	.byte	'AE',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,19,150,9,3
	.word	33693
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,19,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	33309
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33309
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33309
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33309
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,19,159,9,3
	.word	33916
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,19,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,19,175,9,3
	.word	34042
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,19,178,9,16,4,11
	.byte	'AE',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,19,191,9,3
	.word	34294
	.byte	12,19,199,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15819
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN0',0,19,204,9,3
	.word	34513
	.byte	12,19,207,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16376
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1',0,19,212,9,3
	.word	34577
	.byte	12,19,215,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16453
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS',0,19,220,9,3
	.word	34641
	.byte	12,19,223,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16589
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON0',0,19,228,9,3
	.word	34706
	.byte	12,19,231,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16869
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON1',0,19,236,9,3
	.word	34771
	.byte	12,19,239,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17107
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON2',0,19,244,9,3
	.word	34836
	.byte	12,19,247,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17235
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON3',0,19,252,9,3
	.word	34901
	.byte	12,19,255,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17478
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON4',0,19,132,10,3
	.word	34966
	.byte	12,19,135,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17713
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON5',0,19,140,10,3
	.word	35031
	.byte	12,19,143,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17841
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6',0,19,148,10,3
	.word	35096
	.byte	12,19,151,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17941
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7',0,19,156,10,3
	.word	35161
	.byte	12,19,159,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18041
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CHIPID',0,19,164,10,3
	.word	35226
	.byte	12,19,167,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18249
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSCON',0,19,172,10,3
	.word	35290
	.byte	12,19,175,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18414
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSLIM',0,19,180,10,3
	.word	35354
	.byte	12,19,183,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18597
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSSTAT',0,19,188,10,3
	.word	35418
	.byte	12,19,191,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18751
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EICR',0,19,196,10,3
	.word	35483
	.byte	12,19,199,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19115
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR',0,19,204,10,3
	.word	35545
	.byte	12,19,207,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19326
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EMSR',0,19,212,10,3
	.word	35607
	.byte	12,19,215,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19578
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG',0,19,220,10,3
	.word	35669
	.byte	12,19,223,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19696
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG',0,19,228,10,3
	.word	35733
	.byte	12,19,231,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19807
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR13CON',0,19,236,10,3
	.word	35798
	.byte	12,19,239,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19970
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR33CON',0,19,244,10,3
	.word	35864
	.byte	12,19,247,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20133
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRADCSTAT',0,19,252,10,3
	.word	35930
	.byte	12,19,255,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20291
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRDVSTAT',0,19,132,11,3
	.word	35998
	.byte	12,19,135,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20456
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRMONCTRL',0,19,140,11,3
	.word	36065
	.byte	12,19,143,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20785
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROSCCTRL',0,19,148,11,3
	.word	36133
	.byte	12,19,151,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21006
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROVMON',0,19,156,11,3
	.word	36201
	.byte	12,19,159,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21169
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRRSTCON',0,19,164,11,3
	.word	36267
	.byte	12,19,167,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21441
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,19,172,11,3
	.word	36334
	.byte	12,19,175,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21594
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,19,180,11,3
	.word	36403
	.byte	12,19,183,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21750
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,19,188,11,3
	.word	36472
	.byte	12,19,191,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21912
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,19,196,11,3
	.word	36541
	.byte	12,19,199,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22055
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,19,204,11,3
	.word	36610
	.byte	12,19,207,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22220
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,19,212,11,3
	.word	36679
	.byte	12,19,215,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22365
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1',0,19,220,11,3
	.word	36748
	.byte	12,19,223,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22546
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2',0,19,228,11,3
	.word	36816
	.byte	12,19,231,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22720
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3',0,19,236,11,3
	.word	36884
	.byte	12,19,239,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22880
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4',0,19,244,11,3
	.word	36952
	.byte	12,19,247,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23024
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT',0,19,252,11,3
	.word	37020
	.byte	12,19,255,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23298
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRTRIM',0,19,132,12,3
	.word	37085
	.byte	12,19,135,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23437
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRUVMON',0,19,140,12,3
	.word	37150
	.byte	12,19,143,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23600
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EXTCON',0,19,148,12,3
	.word	37216
	.byte	12,19,151,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23818
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FDR',0,19,156,12,3
	.word	37280
	.byte	12,19,159,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23981
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FMR',0,19,164,12,3
	.word	37341
	.byte	12,19,167,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24317
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ID',0,19,172,12,3
	.word	37402
	.byte	12,19,175,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24424
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IGCR',0,19,180,12,3
	.word	37462
	.byte	12,19,183,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24876
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IN',0,19,188,12,3
	.word	37524
	.byte	12,19,191,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24975
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IOCR',0,19,196,12,3
	.word	37584
	.byte	12,19,199,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25125
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL0',0,19,204,12,3
	.word	37646
	.byte	12,19,207,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25274
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL1',0,19,212,12,3
	.word	37714
	.byte	12,19,215,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25435
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL2',0,19,220,12,3
	.word	37782
	.byte	12,19,223,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25565
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLCON',0,19,228,12,3
	.word	37850
	.byte	12,19,231,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25697
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST',0,19,236,12,3
	.word	37914
	.byte	12,19,239,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25812
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_MANID',0,19,244,12,3
	.word	37979
	.byte	12,19,247,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25923
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OMR',0,19,252,12,3
	.word	38042
	.byte	12,19,255,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26081
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OSCCON',0,19,132,13,3
	.word	38103
	.byte	12,19,135,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26493
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OUT',0,19,140,13,3
	.word	38167
	.byte	12,19,143,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26594
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCCON',0,19,148,13,3
	.word	38228
	.byte	12,19,151,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26861
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE',0,19,156,13,3
	.word	38292
	.byte	12,19,159,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26997
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC',0,19,164,13,3
	.word	38359
	.byte	12,19,167,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27108
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDR',0,19,172,13,3
	.word	38422
	.byte	12,19,175,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27241
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR',0,19,180,13,3
	.word	38483
	.byte	12,19,183,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27444
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON0',0,19,188,13,3
	.word	38545
	.byte	12,19,191,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27800
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON1',0,19,196,13,3
	.word	38610
	.byte	12,19,199,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27978
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON2',0,19,204,13,3
	.word	38675
	.byte	12,19,207,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28078
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON0',0,19,212,13,3
	.word	38740
	.byte	12,19,215,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28448
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON1',0,19,220,13,3
	.word	38809
	.byte	12,19,223,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28634
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT',0,19,228,13,3
	.word	38878
	.byte	12,19,231,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28832
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT',0,19,236,13,3
	.word	38947
	.byte	12,19,239,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29065
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR',0,19,244,13,3
	.word	39012
	.byte	12,19,247,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29217
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR0',0,19,252,13,3
	.word	39075
	.byte	12,19,255,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29784
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR1',0,19,132,14,3
	.word	39140
	.byte	12,19,135,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30078
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR2',0,19,140,14,3
	.word	39205
	.byte	12,19,143,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30356
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTAT',0,19,148,14,3
	.word	39270
	.byte	12,19,151,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30852
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR',0,19,156,14,3
	.word	39336
	.byte	12,19,159,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31374
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON',0,19,164,14,3
	.word	39405
	.byte	12,19,167,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31165
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON2',0,19,172,14,3
	.word	39469
	.byte	12,19,175,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31585
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTSTAT',0,19,180,14,3
	.word	39534
	.byte	12,19,183,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32017
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON',0,19,188,14,3
	.word	39599
	.byte	12,19,191,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32113
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_STSTAT',0,19,196,14,3
	.word	39664
	.byte	12,19,199,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32373
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON',0,19,204,14,3
	.word	39728
	.byte	12,19,207,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32498
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON',0,19,212,14,3
	.word	39794
	.byte	12,19,215,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32695
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR',0,19,220,14,3
	.word	39858
	.byte	12,19,223,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32848
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS',0,19,228,14,3
	.word	39923
	.byte	12,19,231,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33001
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET',0,19,236,14,3
	.word	39988
	.byte	12,19,239,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33154
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT',0,19,244,14,3
	.word	40053
	.byte	12,19,247,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33325
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON0',0,19,252,14,3
	.word	40119
	.byte	12,19,255,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33455
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON1',0,19,132,15,3
	.word	40188
	.byte	12,19,135,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33693
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_SR',0,19,140,15,3
	.word	40257
	.byte	12,19,143,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33916
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0',0,19,148,15,3
	.word	40324
	.byte	12,19,151,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34042
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON1',0,19,156,15,3
	.word	40391
	.byte	12,19,159,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34294
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_SR',0,19,164,15,3
	.word	40458
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,19,175,15,25,12,13
	.byte	'CON0',0
	.word	40119
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40188
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40257
	.byte	4,2,35,8,0,16
	.word	40523
	.byte	31
	.byte	'Ifx_SCU_WDTCPU',0,19,180,15,3
	.word	40586
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,19,183,15,25,12,13
	.byte	'CON0',0
	.word	40324
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40391
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40458
	.byte	4,2,35,8,0,16
	.word	40615
	.byte	31
	.byte	'Ifx_SCU_WDTS',0,19,188,15,3
	.word	40676
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,31
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	40703
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,31
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	40854
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,31
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	41098
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,31
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	41196
	.byte	31
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8256
	.byte	31
	.byte	'gpio_pin_enum',0,9,89,2
	.word	9258
	.byte	31
	.byte	'gpio_dir_enum',0,9,95,2
	.word	11232
	.byte	31
	.byte	'gpio_mode_enum',0,9,111,2
	.word	11250
	.byte	27,20,45,9,1,11
	.byte	'mode',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'use_miso',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'use_cs',0,1
	.word	459
	.byte	1,0,2,35,0,0,31
	.byte	'spi_config_info_struct',0,20,50,2
	.word	41728
	.byte	31
	.byte	'spi_index_enum',0,10,48,2
	.word	11413
	.byte	31
	.byte	'spi_mode_enum',0,10,56,2
	.word	11737
	.byte	31
	.byte	'spi_sck_pin_enum',0,10,67,2
	.word	11791
	.byte	31
	.byte	'spi_mosi_pin_enum',0,10,78,2
	.word	12064
	.byte	31
	.byte	'spi_miso_pin_enum',0,10,89,2
	.word	12318
	.byte	31
	.byte	'spi_cs_pin_enum',0,10,140,1,2
	.word	12572
	.byte	31
	.byte	'tft180_dir_enum',0,11,99,2
	.word	13554
	.byte	31
	.byte	'tft180_font_size_enum',0,11,106,2
	.word	13640
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L228:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,54,15,39,12,63,12,60,12,0,0,20,5,0,73,19,0,0,21,46
	.byte	1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0
	.byte	0,23,5,0,58,15,59,15,57,15,73,19,0,0,24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,38,0
	.byte	73,19,0,0,26,21,1,54,15,39,12,0,0,27,19,1,58,15,59,15,57,15,11,15,0,0,28,46,1,49,19,0,0,29,5,0,49,19,0
	.byte	0,30,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,31,22,0,3,8,58,15,59,15,57,15,73,19,0,0,32,21,0
	.byte	54,15,0,0,33,1,1,73,19,0,0,34,33,0,0,0,35,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L229:
	.word	.L929-.L928
.L928:
	.half	3
	.word	.L931-.L930
.L930:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'string.h',0,3,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'zf_common_function.h',0,4,0,0
	.byte	'zf_driver_delay.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,5,0,0
	.byte	'zf_driver_spi.h',0,5,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_tft180.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'zf_common_font.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0
	.byte	'zf_driver_soft_spi.h',0,5,0,0,0
.L931:
.L929:
	.sdecl	'.debug_info',debug,cluster('tft180_clear')
	.sect	'.debug_info'
.L230:
	.word	310
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L233,.L232
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_clear',0,1,135,2,6,1,1,1
	.word	.L191,.L347,.L190
	.byte	4
	.word	.L191,.L347
	.byte	5
	.byte	'color_buffer',0,1,137,2,12
	.word	.L348,.L349
	.byte	5
	.byte	'i',0,1,138,2,12
	.word	.L350,.L351
	.byte	5
	.byte	'j',0,1,138,2,19
	.word	.L350,.L352
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_clear')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_clear')
	.sect	'.debug_line'
.L232:
	.word	.L933-.L932
.L932:
	.half	3
	.word	.L935-.L934
.L934:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L935:
	.byte	5,25,7,0,5,2
	.word	.L191
	.byte	3,136,2,1,5,41,9
	.half	.L936-.L191
	.byte	1,5,5,9
	.half	.L558-.L936
	.byte	3,3,1,5,23,9
	.half	.L21-.L558
	.byte	3,1,1,5,26,9
	.half	.L937-.L21
	.byte	1,5,29,9
	.half	.L938-.L937
	.byte	1,5,46,9
	.half	.L939-.L938
	.byte	1,5,51,9
	.half	.L940-.L939
	.byte	1,5,69,9
	.half	.L941-.L940
	.byte	1,5,11,9
	.half	.L942-.L941
	.byte	3,1,1,5,36,9
	.half	.L560-.L942
	.byte	1,5,21,9
	.half	.L23-.L560
	.byte	3,2,1,5,27,9
	.half	.L943-.L23
	.byte	1,5,25,9
	.half	.L944-.L943
	.byte	1,5,40,9
	.half	.L945-.L944
	.byte	3,126,1,5,20,9
	.half	.L22-.L945
	.byte	1,5,36,9
	.half	.L946-.L22
	.byte	1,5,12,7,9
	.half	.L947-.L946
	.byte	3,4,1,5,38,9
	.half	.L562-.L947
	.byte	1,5,9,9
	.half	.L25-.L562
	.byte	3,2,1,5,42,9
	.half	.L565-.L25
	.byte	3,126,1,5,21,9
	.half	.L24-.L565
	.byte	1,5,38,9
	.half	.L948-.L24
	.byte	1,5,5,7,9
	.half	.L949-.L948
	.byte	3,4,1,5,1,9
	.half	.L27-.L949
	.byte	3,1,1,9
	.half	.L234-.L27
	.byte	0,1,1
.L933:
	.sdecl	'.debug_ranges',debug,cluster('tft180_clear')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L191,0,.L234-.L191,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_full')
	.sect	'.debug_info'
.L235:
	.word	328
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L238,.L237
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_full',0,1,160,2,6,1,1,1
	.word	.L193,.L353,.L192
	.byte	4
	.byte	'color',0,1,160,2,32
	.word	.L354,.L355
	.byte	5
	.word	.L193,.L353
	.byte	6
	.byte	'color_buffer',0,1,162,2,12
	.word	.L348,.L356
	.byte	6
	.byte	'i',0,1,163,2,12
	.word	.L350,.L357
	.byte	6
	.byte	'j',0,1,163,2,19
	.word	.L350,.L358
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_full')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_full')
	.sect	'.debug_line'
.L237:
	.word	.L951-.L950
.L950:
	.half	3
	.word	.L953-.L952
.L952:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L953:
	.byte	5,6,7,0,5,2
	.word	.L193
	.byte	3,159,2,1,5,25,9
	.half	.L570-.L193
	.byte	3,2,1,5,41,9
	.half	.L954-.L570
	.byte	1,5,5,9
	.half	.L573-.L954
	.byte	3,3,1,5,23,9
	.half	.L29-.L573
	.byte	3,1,1,5,26,9
	.half	.L955-.L29
	.byte	1,5,29,9
	.half	.L956-.L955
	.byte	1,5,46,9
	.half	.L957-.L956
	.byte	1,5,51,9
	.half	.L958-.L957
	.byte	1,5,69,9
	.half	.L959-.L958
	.byte	1,5,11,9
	.half	.L960-.L959
	.byte	3,1,1,5,36,9
	.half	.L575-.L960
	.byte	1,5,21,9
	.half	.L31-.L575
	.byte	3,2,1,5,25,9
	.half	.L961-.L31
	.byte	1,5,40,9
	.half	.L962-.L961
	.byte	3,126,1,5,20,9
	.half	.L30-.L962
	.byte	1,5,36,9
	.half	.L963-.L30
	.byte	1,5,12,7,9
	.half	.L964-.L963
	.byte	3,4,1,5,38,9
	.half	.L577-.L964
	.byte	1,5,9,9
	.half	.L33-.L577
	.byte	3,2,1,5,42,9
	.half	.L580-.L33
	.byte	3,126,1,5,21,9
	.half	.L32-.L580
	.byte	1,5,38,9
	.half	.L965-.L32
	.byte	1,5,5,7,9
	.half	.L966-.L965
	.byte	3,4,1,5,1,9
	.half	.L35-.L966
	.byte	3,1,1,9
	.half	.L239-.L35
	.byte	0,1,1
.L951:
	.sdecl	'.debug_ranges',debug,cluster('tft180_full')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L193,0,.L239-.L193,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_set_dir')
	.sect	'.debug_info'
.L240:
	.word	272
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L243,.L242
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_set_dir',0,1,185,2,6,1,1,1
	.word	.L195,.L359,.L194
	.byte	4
	.byte	'dir',0,1,185,2,38
	.word	.L360,.L361
	.byte	5
	.word	.L195,.L359
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_set_dir')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_set_dir')
	.sect	'.debug_line'
.L242:
	.word	.L968-.L967
.L967:
	.half	3
	.word	.L970-.L969
.L969:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L970:
	.byte	5,5,7,0,5,2
	.word	.L195
	.byte	3,186,2,1,5,24,9
	.half	.L971-.L195
	.byte	1,5,14,9
	.half	.L972-.L971
	.byte	3,3,1,9
	.half	.L973-.L972
	.byte	3,1,1,9
	.half	.L974-.L973
	.byte	3,5,1,9
	.half	.L975-.L974
	.byte	3,1,1,5,13,9
	.half	.L37-.L975
	.byte	3,124,1,5,32,9
	.half	.L976-.L37
	.byte	1,5,30,9
	.half	.L977-.L976
	.byte	1,5,13,9
	.half	.L978-.L977
	.byte	3,1,1,5,33,9
	.half	.L979-.L978
	.byte	1,5,31,9
	.half	.L980-.L979
	.byte	1,5,10,9
	.half	.L981-.L980
	.byte	3,1,1,5,13,9
	.half	.L39-.L981
	.byte	3,4,1,5,32,9
	.half	.L982-.L39
	.byte	1,5,30,9
	.half	.L983-.L982
	.byte	1,5,13,9
	.half	.L984-.L983
	.byte	3,1,1,5,33,9
	.half	.L985-.L984
	.byte	1,5,31,9
	.half	.L986-.L985
	.byte	1,5,10,9
	.half	.L987-.L986
	.byte	3,1,1,5,1,9
	.half	.L41-.L987
	.byte	3,2,1,7,9
	.half	.L244-.L41
	.byte	0,1,1
.L968:
	.sdecl	'.debug_ranges',debug,cluster('tft180_set_dir')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L195,0,.L244-.L195,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_set_font')
	.sect	'.debug_info'
.L245:
	.word	274
	.half	3
	.word	.L246
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L248,.L247
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_set_font',0,1,212,2,6,1,1,1
	.word	.L197,.L362,.L196
	.byte	4
	.byte	'font',0,1,212,2,45
	.word	.L363,.L364
	.byte	5
	.word	.L197,.L362
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_set_font')
	.sect	'.debug_abbrev'
.L246:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_set_font')
	.sect	'.debug_line'
.L247:
	.word	.L989-.L988
.L988:
	.half	3
	.word	.L991-.L990
.L990:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L991:
	.byte	5,5,7,0,5,2
	.word	.L197
	.byte	3,213,2,1,5,25,9
	.half	.L992-.L197
	.byte	1,5,1,9
	.half	.L993-.L992
	.byte	3,1,1,7,9
	.half	.L249-.L993
	.byte	0,1,1
.L989:
	.sdecl	'.debug_ranges',debug,cluster('tft180_set_font')
	.sect	'.debug_ranges'
.L248:
	.word	-1,.L197,0,.L249-.L197,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_set_color')
	.sect	'.debug_info'
.L250:
	.word	295
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L253,.L252
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_set_color',0,1,225,2,6,1,1,1
	.word	.L199,.L365,.L198
	.byte	4
	.byte	'pen',0,1,225,2,31
	.word	.L350,.L366
	.byte	4
	.byte	'bgcolor',0,1,225,2,49
	.word	.L367,.L368
	.byte	5
	.word	.L199,.L365
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_set_color')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_set_color')
	.sect	'.debug_line'
.L252:
	.word	.L995-.L994
.L994:
	.half	3
	.word	.L997-.L996
.L996:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L997:
	.byte	5,5,7,0,5,2
	.word	.L199
	.byte	3,226,2,1,5,21,9
	.half	.L998-.L199
	.byte	1,5,5,9
	.half	.L999-.L998
	.byte	3,1,1,5,20,9
	.half	.L1000-.L999
	.byte	1,5,1,9
	.half	.L1001-.L1000
	.byte	3,1,1,7,9
	.half	.L254-.L1001
	.byte	0,1,1
.L995:
	.sdecl	'.debug_ranges',debug,cluster('tft180_set_color')
	.sect	'.debug_ranges'
.L253:
	.word	-1,.L199,0,.L254-.L199,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_draw_point')
	.sect	'.debug_info'
.L255:
	.word	307
	.half	3
	.word	.L256
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L258,.L257
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_draw_point',0,1,240,2,6,1,1,1
	.word	.L201,.L369,.L200
	.byte	4
	.byte	'x',0,1,240,2,32
	.word	.L350,.L370
	.byte	4
	.byte	'y',0,1,240,2,42
	.word	.L350,.L371
	.byte	4
	.byte	'color',0,1,240,2,58
	.word	.L372,.L373
	.byte	5
	.word	.L201,.L369
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_draw_point')
	.sect	'.debug_abbrev'
.L256:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_draw_point')
	.sect	'.debug_line'
.L257:
	.word	.L1003-.L1002
.L1002:
	.half	3
	.word	.L1005-.L1004
.L1004:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1005:
	.byte	5,6,7,0,5,2
	.word	.L201
	.byte	3,239,2,1,5,5,9
	.half	.L587-.L201
	.byte	3,4,1,9
	.half	.L584-.L587
	.byte	3,1,1,9
	.half	.L1006-.L584
	.byte	3,2,1,5,32,9
	.half	.L44-.L1006
	.byte	3,1,1,5,5,9
	.half	.L1007-.L44
	.byte	3,1,1,9
	.half	.L593-.L1007
	.byte	3,1,1,5,1,9
	.half	.L46-.L593
	.byte	3,1,1,7,9
	.half	.L259-.L46
	.byte	0,1,1
.L1003:
	.sdecl	'.debug_ranges',debug,cluster('tft180_draw_point')
	.sect	'.debug_ranges'
.L258:
	.word	-1,.L201,0,.L259-.L201,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_draw_line')
	.sect	'.debug_info'
.L260:
	.word	448
	.half	3
	.word	.L261
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L263,.L262
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_draw_line',0,1,136,3,6,1,1,1
	.word	.L203,.L374,.L202
	.byte	4
	.byte	'x_start',0,1,136,3,31
	.word	.L350,.L375
	.byte	4
	.byte	'y_start',0,1,136,3,47
	.word	.L350,.L376
	.byte	4
	.byte	'x_end',0,1,136,3,63
	.word	.L350,.L377
	.byte	4
	.byte	'y_end',0,1,136,3,77
	.word	.L350,.L378
	.byte	4
	.byte	'color',0,1,136,3,97
	.word	.L379,.L380
	.byte	5
	.word	.L203,.L374
	.byte	5
	.word	.L381,.L374
	.byte	6
	.byte	'x_dir',0,1,145,3,11
	.word	.L382,.L383
	.byte	6
	.byte	'y_dir',0,1,146,3,11
	.word	.L382,.L384
	.byte	6
	.byte	'temp_rate',0,1,147,3,11
	.word	.L385,.L386
	.byte	6
	.byte	'temp_b',0,1,148,3,11
	.word	.L385,.L387
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_draw_line')
	.sect	'.debug_abbrev'
.L261:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_draw_line')
	.sect	'.debug_line'
.L262:
	.word	.L1009-.L1008
.L1008:
	.half	3
	.word	.L1011-.L1010
.L1010:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1011:
	.byte	5,6,7,0,5,2
	.word	.L203
	.byte	3,135,3,1,5,5,9
	.half	.L601-.L203
	.byte	3,4,1,9
	.half	.L595-.L601
	.byte	3,1,1,9
	.half	.L1012-.L595
	.byte	3,1,1,9
	.half	.L1013-.L1012
	.byte	3,1,1,5,20,9
	.half	.L381-.L1013
	.byte	3,2,1,5,36,7,9
	.half	.L606-.L381
	.byte	1,5,40,9
	.half	.L1014-.L606
	.byte	1,5,36,9
	.half	.L47-.L1014
	.byte	1,5,20,9
	.half	.L48-.L47
	.byte	3,1,1,5,36,7,9
	.half	.L607-.L48
	.byte	1,5,40,9
	.half	.L608-.L607
	.byte	1,5,36,9
	.half	.L49-.L608
	.byte	1,5,9,9
	.half	.L51-.L49
	.byte	3,6,1,5,41,7,9
	.half	.L609-.L51
	.byte	3,2,1,5,25,9
	.half	.L610-.L609
	.byte	1,5,68,9
	.half	.L611-.L610
	.byte	1,5,52,9
	.half	.L612-.L611
	.byte	1,5,50,9
	.half	.L1015-.L612
	.byte	1,5,22,9
	.half	.L613-.L1015
	.byte	3,1,1,5,39,9
	.half	.L614-.L613
	.byte	1,5,37,9
	.half	.L1016-.L614
	.byte	1,5,76,9
	.half	.L615-.L1016
	.byte	3,127,1,5,35,9
	.half	.L52-.L615
	.byte	3,5,1,5,53,9
	.half	.L55-.L52
	.byte	3,2,1,5,25,9
	.half	.L618-.L55
	.byte	3,1,1,5,35,9
	.half	.L54-.L618
	.byte	3,125,1,5,49,7,9
	.half	.L619-.L54
	.byte	3,5,1,5,13,9
	.half	.L622-.L619
	.byte	3,1,1,5,12,9
	.half	.L53-.L622
	.byte	3,3,1,5,40,9
	.half	.L58-.L53
	.byte	1,5,9,9
	.half	.L60-.L58
	.byte	1,5,35,7,9
	.half	.L1017-.L60
	.byte	3,2,1,5,53,9
	.half	.L63-.L1017
	.byte	3,2,1,5,25,9
	.half	.L633-.L63
	.byte	3,1,1,5,36,9
	.half	.L634-.L633
	.byte	3,1,1,5,51,9
	.half	.L599-.L634
	.byte	1,5,61,9
	.half	.L1018-.L599
	.byte	1,5,27,9
	.half	.L1019-.L1018
	.byte	1,5,35,9
	.half	.L62-.L1019
	.byte	3,124,1,5,49,7,9
	.half	.L635-.L62
	.byte	3,6,1,5,55,9
	.half	.L638-.L635
	.byte	1,5,35,9
	.half	.L61-.L638
	.byte	3,4,1,5,53,9
	.half	.L66-.L61
	.byte	3,2,1,5,25,9
	.half	.L641-.L66
	.byte	3,1,1,5,35,9
	.half	.L644-.L641
	.byte	3,1,1,5,62,9
	.half	.L643-.L644
	.byte	1,5,27,9
	.half	.L1020-.L643
	.byte	1,5,35,9
	.half	.L65-.L1020
	.byte	3,124,1,5,49,7,9
	.half	.L646-.L65
	.byte	3,6,1,5,1,9
	.half	.L56-.L646
	.byte	3,3,1,7,9
	.half	.L264-.L56
	.byte	0,1,1
.L1009:
	.sdecl	'.debug_ranges',debug,cluster('tft180_draw_line')
	.sect	'.debug_ranges'
.L263:
	.word	-1,.L203,0,.L264-.L203,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_char')
	.sect	'.debug_info'
.L265:
	.word	510
	.half	3
	.word	.L266
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L268,.L267
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_char',0,1,200,3,6,1,1,1
	.word	.L205,.L388,.L204
	.byte	4
	.byte	'x',0,1,200,3,31
	.word	.L350,.L389
	.byte	4
	.byte	'y',0,1,200,3,41
	.word	.L350,.L390
	.byte	4
	.byte	'dat',0,1,200,3,55
	.word	.L391,.L392
	.byte	5
	.word	.L205,.L388
	.byte	5
	.word	.L393,.L388
	.byte	6
	.byte	'i',0,1,207,3,11
	.word	.L394,.L395
	.byte	6
	.byte	'j',0,1,207,3,18
	.word	.L394,.L396
	.byte	5
	.word	.L69,.L397
	.byte	6
	.byte	'display_buffer',0,1,214,3,20
	.word	.L398,.L399
	.byte	5
	.word	.L74,.L400
	.byte	6
	.byte	'temp_top',0,1,219,3,23
	.word	.L394,.L401
	.byte	0,0,5
	.word	.L70,.L402
	.byte	6
	.byte	'display_buffer',0,1,237,3,20
	.word	.L403,.L404
	.byte	5
	.word	.L81,.L405
	.byte	6
	.byte	'temp_top',0,1,241,3,23
	.word	.L394,.L406
	.byte	6
	.byte	'temp_bottom',0,1,242,3,23
	.word	.L394,.L407
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_char')
	.sect	'.debug_abbrev'
.L266:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_char')
	.sect	'.debug_line'
.L267:
	.word	.L1022-.L1021
.L1021:
	.half	3
	.word	.L1024-.L1023
.L1023:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1024:
	.byte	5,6,7,0,5,2
	.word	.L205
	.byte	3,199,3,1,5,5,9
	.half	.L652-.L205
	.byte	3,4,1,9
	.half	.L649-.L652
	.byte	3,1,1,9
	.half	.L393-.L649
	.byte	3,4,1,5,12,9
	.half	.L68-.L393
	.byte	3,1,1,5,14,9
	.half	.L1025-.L68
	.byte	3,2,1,9
	.half	.L1026-.L1025
	.byte	3,23,1,9
	.half	.L1027-.L1026
	.byte	3,35,1,5,39,9
	.half	.L69-.L1027
	.byte	3,73,1,5,46,9
	.half	.L657-.L69
	.byte	1,5,19,9
	.half	.L1028-.L657
	.byte	3,1,1,5,29,9
	.half	.L661-.L1028
	.byte	1,5,53,9
	.half	.L74-.L661
	.byte	3,3,1,5,48,9
	.half	.L1029-.L74
	.byte	1,5,34,9
	.half	.L1030-.L1029
	.byte	1,5,48,9
	.half	.L1031-.L1030
	.byte	1,5,58,9
	.half	.L1032-.L1031
	.byte	1,5,23,9
	.half	.L662-.L1032
	.byte	3,1,1,5,33,9
	.half	.L663-.L662
	.byte	1,5,21,9
	.half	.L76-.L663
	.byte	3,2,1,5,48,7,9
	.half	.L1033-.L76
	.byte	3,2,1,5,42,9
	.half	.L1034-.L1033
	.byte	1,5,39,9
	.half	.L1035-.L1034
	.byte	1,5,54,9
	.half	.L1036-.L1035
	.byte	1,5,51,9
	.half	.L1037-.L1036
	.byte	1,5,70,9
	.half	.L1038-.L1037
	.byte	1,5,48,9
	.half	.L77-.L1038
	.byte	3,4,1,5,42,9
	.half	.L1039-.L77
	.byte	1,5,39,9
	.half	.L1040-.L1039
	.byte	1,5,54,9
	.half	.L1041-.L1040
	.byte	1,5,51,9
	.half	.L1042-.L1041
	.byte	1,5,30,9
	.half	.L78-.L1042
	.byte	3,2,1,5,37,9
	.half	.L1043-.L78
	.byte	3,118,1,5,33,9
	.half	.L75-.L1043
	.byte	1,7,9
	.half	.L400-.L75
	.byte	3,124,1,5,29,9
	.half	.L73-.L400
	.byte	1,5,13,7,9
	.half	.L1044-.L73
	.byte	3,17,1,5,10,9
	.half	.L397-.L1044
	.byte	3,1,1,5,39,9
	.half	.L70-.L397
	.byte	3,4,1,5,46,9
	.half	.L665-.L70
	.byte	1,5,19,9
	.half	.L1045-.L665
	.byte	3,1,1,5,29,9
	.half	.L669-.L1045
	.byte	1,5,54,9
	.half	.L81-.L669
	.byte	3,2,1,5,49,9
	.half	.L1046-.L81
	.byte	1,5,34,9
	.half	.L1047-.L1046
	.byte	1,5,49,9
	.half	.L1048-.L1047
	.byte	1,5,59,9
	.half	.L1049-.L1048
	.byte	1,5,57,9
	.half	.L670-.L1049
	.byte	3,1,1,5,52,9
	.half	.L1050-.L670
	.byte	1,5,37,9
	.half	.L1051-.L1050
	.byte	1,5,52,9
	.half	.L1052-.L1051
	.byte	1,5,62,9
	.half	.L1053-.L1052
	.byte	1,5,23,9
	.half	.L672-.L1053
	.byte	3,1,1,5,33,9
	.half	.L673-.L672
	.byte	1,5,21,9
	.half	.L83-.L673
	.byte	3,2,1,5,48,7,9
	.half	.L1054-.L83
	.byte	3,2,1,5,42,9
	.half	.L1055-.L1054
	.byte	1,5,39,9
	.half	.L1056-.L1055
	.byte	1,5,54,9
	.half	.L1057-.L1056
	.byte	1,5,51,9
	.half	.L1058-.L1057
	.byte	1,5,70,9
	.half	.L1059-.L1058
	.byte	1,5,48,9
	.half	.L84-.L1059
	.byte	3,4,1,5,42,9
	.half	.L1060-.L84
	.byte	1,5,39,9
	.half	.L1061-.L1060
	.byte	1,5,54,9
	.half	.L1062-.L1061
	.byte	1,5,51,9
	.half	.L1063-.L1062
	.byte	1,5,30,9
	.half	.L85-.L1063
	.byte	3,2,1,5,37,9
	.half	.L1064-.L85
	.byte	3,118,1,5,33,9
	.half	.L82-.L1064
	.byte	1,5,23,7,9
	.half	.L1065-.L82
	.byte	3,12,1,5,33,9
	.half	.L671-.L1065
	.byte	1,5,21,9
	.half	.L87-.L671
	.byte	3,2,1,5,48,7,9
	.half	.L1066-.L87
	.byte	3,2,1,5,42,9
	.half	.L1067-.L1066
	.byte	1,5,50,9
	.half	.L1068-.L1067
	.byte	1,5,39,9
	.half	.L1069-.L1068
	.byte	1,5,63,9
	.half	.L1070-.L1069
	.byte	1,5,60,9
	.half	.L1071-.L1070
	.byte	1,5,79,9
	.half	.L1072-.L1071
	.byte	1,5,48,9
	.half	.L88-.L1072
	.byte	3,4,1,5,42,9
	.half	.L1073-.L88
	.byte	1,5,50,9
	.half	.L1074-.L1073
	.byte	1,5,39,9
	.half	.L1075-.L1074
	.byte	1,5,63,9
	.half	.L1076-.L1075
	.byte	1,5,60,9
	.half	.L1077-.L1076
	.byte	1,5,33,9
	.half	.L89-.L1077
	.byte	3,2,1,5,37,9
	.half	.L1078-.L89
	.byte	3,118,1,5,33,9
	.half	.L86-.L1078
	.byte	1,7,9
	.half	.L405-.L86
	.byte	3,112,1,5,29,9
	.half	.L80-.L405
	.byte	1,5,13,7,9
	.half	.L1079-.L80
	.byte	3,29,1,5,10,9
	.half	.L402-.L1079
	.byte	3,1,1,9
	.half	.L71-.L402
	.byte	3,4,1,5,5,9
	.half	.L79-.L71
	.byte	3,2,1,5,1,9
	.half	.L93-.L79
	.byte	3,1,1,7,9
	.half	.L269-.L93
	.byte	0,1,1
.L1022:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_char')
	.sect	'.debug_ranges'
.L268:
	.word	-1,.L205,0,.L269-.L205,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_string')
	.sect	'.debug_info'
.L270:
	.word	332
	.half	3
	.word	.L271
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L273,.L272
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_string',0,1,159,4,6,1,1,1
	.word	.L207,.L408,.L206
	.byte	4
	.byte	'x',0,1,159,4,33
	.word	.L350,.L409
	.byte	4
	.byte	'y',0,1,159,4,43
	.word	.L350,.L410
	.byte	4
	.byte	'dat',0,1,159,4,57
	.word	.L411,.L412
	.byte	5
	.word	.L207,.L408
	.byte	5
	.word	.L413,.L408
	.byte	6
	.byte	'j',0,1,166,4,12
	.word	.L350,.L414
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_string')
	.sect	'.debug_abbrev'
.L271:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_string')
	.sect	'.debug_line'
.L272:
	.word	.L1081-.L1080
.L1080:
	.half	3
	.word	.L1083-.L1082
.L1082:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1083:
	.byte	5,6,7,0,5,2
	.word	.L207
	.byte	3,158,4,1,5,5,9
	.half	.L677-.L207
	.byte	3,4,1,9
	.half	.L1084-.L677
	.byte	3,1,1,5,14,9
	.half	.L413-.L1084
	.byte	3,2,1,5,25,9
	.half	.L681-.L413
	.byte	3,1,1,5,16,9
	.half	.L95-.L681
	.byte	3,2,1,5,18,9
	.half	.L1085-.L95
	.byte	3,2,1,9
	.half	.L1086-.L1085
	.byte	3,1,1,9
	.half	.L1087-.L1086
	.byte	3,1,1,5,58,9
	.half	.L96-.L1087
	.byte	3,126,1,5,56,9
	.half	.L683-.L96
	.byte	1,5,71,9
	.half	.L1088-.L683
	.byte	1,5,77,9
	.half	.L687-.L1088
	.byte	1,5,58,9
	.half	.L97-.L687
	.byte	3,1,1,5,56,9
	.half	.L688-.L97
	.byte	1,5,71,9
	.half	.L1089-.L688
	.byte	1,5,77,9
	.half	.L692-.L1089
	.byte	1,5,37,9
	.half	.L98-.L692
	.byte	3,1,1,5,11,9
	.half	.L100-.L98
	.byte	3,2,1,5,22,9
	.half	.L94-.L100
	.byte	3,120,1,5,25,9
	.half	.L1090-.L94
	.byte	1,5,1,7,9
	.half	.L1091-.L1090
	.byte	3,10,1,7,9
	.half	.L274-.L1091
	.byte	0,1,1
.L1081:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_string')
	.sect	'.debug_ranges'
.L273:
	.word	-1,.L207,0,.L274-.L207,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_int')
	.sect	'.debug_info'
.L275:
	.word	398
	.half	3
	.word	.L276
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L278,.L277
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_int',0,1,189,4,6,1,1,1
	.word	.L209,.L415,.L208
	.byte	4
	.byte	'x',0,1,189,4,30
	.word	.L350,.L416
	.byte	4
	.byte	'y',0,1,189,4,40
	.word	.L350,.L417
	.byte	4
	.byte	'dat',0,1,189,4,55
	.word	.L418,.L419
	.byte	4
	.byte	'num',0,1,189,4,66
	.word	.L394,.L420
	.byte	5
	.word	.L209,.L415
	.byte	5
	.word	.L421,.L415
	.byte	6
	.byte	'dat_temp',0,1,198,4,11
	.word	.L422,.L423
	.byte	6
	.byte	'offset',0,1,199,4,11
	.word	.L422,.L424
	.byte	6
	.byte	'data_buffer',0,1,200,4,10
	.word	.L425,.L426
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_int')
	.sect	'.debug_abbrev'
.L276:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_int')
	.sect	'.debug_line'
.L277:
	.word	.L1093-.L1092
.L1092:
	.half	3
	.word	.L1095-.L1094
.L1094:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1095:
	.byte	5,6,7,0,5,2
	.word	.L209
	.byte	3,188,4,1,5,5,9
	.half	.L698-.L209
	.byte	3,4,1,9
	.half	.L694-.L698
	.byte	3,1,1,9
	.half	.L1096-.L694
	.byte	3,1,1,9
	.half	.L1097-.L1096
	.byte	3,1,1,5,18,9
	.half	.L421-.L1097
	.byte	3,3,1,5,12,9
	.half	.L702-.L421
	.byte	3,3,1,5,25,9
	.half	.L1098-.L702
	.byte	1,5,28,9
	.half	.L1099-.L1098
	.byte	1,5,12,9
	.half	.L1100-.L1099
	.byte	3,1,1,5,25,9
	.half	.L1101-.L1100
	.byte	1,5,34,9
	.half	.L1102-.L1101
	.byte	1,5,5,9
	.half	.L1103-.L1102
	.byte	3,3,1,5,22,7,9
	.half	.L1104-.L1103
	.byte	3,2,1,5,20,9
	.half	.L105-.L1104
	.byte	3,2,1,5,28,9
	.half	.L1105-.L105
	.byte	3,126,1,5,22,9
	.half	.L104-.L1105
	.byte	1,5,18,7,9
	.half	.L1106-.L104
	.byte	3,4,1,5,21,9
	.half	.L103-.L1106
	.byte	3,2,1,5,34,9
	.half	.L1107-.L103
	.byte	1,5,45,9
	.half	.L704-.L1107
	.byte	3,1,1,5,30,9
	.half	.L705-.L704
	.byte	1,5,1,9
	.half	.L1108-.L705
	.byte	3,1,1,7,9
	.half	.L279-.L1108
	.byte	0,1,1
.L1093:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_int')
	.sect	'.debug_ranges'
.L278:
	.word	-1,.L209,0,.L279-.L209,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_uint')
	.sect	'.debug_info'
.L280:
	.word	399
	.half	3
	.word	.L281
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L283,.L282
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_uint',0,1,228,4,6,1,1,1
	.word	.L211,.L427,.L210
	.byte	4
	.byte	'x',0,1,228,4,31
	.word	.L350,.L428
	.byte	4
	.byte	'y',0,1,228,4,41
	.word	.L350,.L429
	.byte	4
	.byte	'dat',0,1,228,4,57
	.word	.L430,.L431
	.byte	4
	.byte	'num',0,1,228,4,68
	.word	.L394,.L432
	.byte	5
	.word	.L211,.L427
	.byte	5
	.word	.L433,.L427
	.byte	6
	.byte	'dat_temp',0,1,237,4,12
	.word	.L434,.L435
	.byte	6
	.byte	'offset',0,1,238,4,11
	.word	.L422,.L436
	.byte	6
	.byte	'data_buffer',0,1,239,4,10
	.word	.L425,.L437
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_uint')
	.sect	'.debug_abbrev'
.L281:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_uint')
	.sect	'.debug_line'
.L282:
	.word	.L1110-.L1109
.L1109:
	.half	3
	.word	.L1112-.L1111
.L1111:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1112:
	.byte	5,6,7,0,5,2
	.word	.L211
	.byte	3,227,4,1,5,5,9
	.half	.L712-.L211
	.byte	3,4,1,9
	.half	.L708-.L712
	.byte	3,1,1,9
	.half	.L1113-.L708
	.byte	3,1,1,9
	.half	.L1114-.L1113
	.byte	3,1,1,5,18,9
	.half	.L433-.L1114
	.byte	3,3,1,5,12,9
	.half	.L716-.L433
	.byte	3,2,1,5,25,9
	.half	.L1115-.L716
	.byte	1,5,28,9
	.half	.L1116-.L1115
	.byte	1,5,12,9
	.half	.L1117-.L1116
	.byte	3,1,1,5,25,9
	.half	.L1118-.L1117
	.byte	1,5,30,9
	.half	.L1119-.L1118
	.byte	1,5,5,9
	.half	.L718-.L1119
	.byte	3,3,1,5,22,7,9
	.half	.L1120-.L718
	.byte	3,2,1,5,20,9
	.half	.L108-.L1120
	.byte	3,2,1,5,28,9
	.half	.L1121-.L108
	.byte	3,126,1,5,22,9
	.half	.L107-.L1121
	.byte	1,5,18,7,9
	.half	.L1122-.L107
	.byte	3,4,1,5,22,9
	.half	.L106-.L1122
	.byte	3,2,1,5,35,9
	.half	.L1123-.L106
	.byte	1,5,45,9
	.half	.L720-.L1123
	.byte	3,1,1,5,30,9
	.half	.L721-.L720
	.byte	1,5,1,9
	.half	.L1124-.L721
	.byte	3,1,1,7,9
	.half	.L284-.L1124
	.byte	0,1,1
.L1110:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_uint')
	.sect	'.debug_ranges'
.L283:
	.word	-1,.L211,0,.L284-.L211,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_float')
	.sect	'.debug_info'
.L285:
	.word	422
	.half	3
	.word	.L286
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L288,.L287
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_float',0,1,142,5,6,1,1,1
	.word	.L213,.L438,.L212
	.byte	4
	.byte	'x',0,1,142,5,32
	.word	.L350,.L439
	.byte	4
	.byte	'y',0,1,142,5,42
	.word	.L350,.L440
	.byte	4
	.byte	'dat',0,1,142,5,58
	.word	.L441,.L442
	.byte	4
	.byte	'num',0,1,142,5,69
	.word	.L394,.L443
	.byte	4
	.byte	'pointnum',0,1,142,5,80
	.word	.L394,.L444
	.byte	5
	.word	.L213,.L438
	.byte	5
	.word	.L445,.L438
	.byte	6
	.byte	'dat_temp',0,1,153,5,12
	.word	.L446,.L447
	.byte	6
	.byte	'offset',0,1,154,5,12
	.word	.L446,.L448
	.byte	6
	.byte	'data_buffer',0,1,155,5,10
	.word	.L449,.L450
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_float')
	.sect	'.debug_abbrev'
.L286:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_float')
	.sect	'.debug_line'
.L287:
	.word	.L1126-.L1125
.L1125:
	.half	3
	.word	.L1128-.L1127
.L1127:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1128:
	.byte	5,6,7,0,5,2
	.word	.L213
	.byte	3,141,5,1,5,5,9
	.half	.L730-.L213
	.byte	3,4,1,9
	.half	.L724-.L730
	.byte	3,1,1,9
	.half	.L1129-.L724
	.byte	3,1,1,9
	.half	.L1130-.L1129
	.byte	3,1,1,9
	.half	.L1131-.L1130
	.byte	3,1,1,9
	.half	.L1132-.L1131
	.byte	3,1,1,5,21,9
	.half	.L445-.L1132
	.byte	3,3,1,5,12,9
	.half	.L1133-.L445
	.byte	3,2,1,5,25,9
	.half	.L1134-.L1133
	.byte	1,5,28,9
	.half	.L1135-.L1134
	.byte	1,5,12,9
	.half	.L1136-.L1135
	.byte	3,1,1,5,25,9
	.half	.L1137-.L1136
	.byte	1,5,34,9
	.half	.L1138-.L1137
	.byte	1,5,45,9
	.half	.L1139-.L1138
	.byte	1,5,18,9
	.half	.L1140-.L1139
	.byte	3,3,1,5,19,9
	.half	.L110-.L1140
	.byte	3,2,1,5,16,9
	.half	.L733-.L110
	.byte	1,5,24,9
	.half	.L1141-.L733
	.byte	3,126,1,5,18,9
	.half	.L109-.L1141
	.byte	1,5,28,7,9
	.half	.L735-.L109
	.byte	3,4,1,5,44,9
	.half	.L737-.L735
	.byte	1,5,42,9
	.half	.L1142-.L737
	.byte	1,5,57,9
	.half	.L739-.L1142
	.byte	1,5,25,9
	.half	.L741-.L739
	.byte	1,5,24,9
	.half	.L1143-.L741
	.byte	3,1,1,5,47,9
	.half	.L1144-.L1143
	.byte	1,5,30,9
	.half	.L744-.L1144
	.byte	3,1,1,5,1,9
	.half	.L746-.L744
	.byte	3,1,1,7,9
	.half	.L289-.L746
	.byte	0,1,1
.L1126:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_float')
	.sect	'.debug_ranges'
.L288:
	.word	-1,.L213,0,.L289-.L213,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_binary_image')
	.sect	'.debug_info'
.L290:
	.word	533
	.half	3
	.word	.L291
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L293,.L292
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_binary_image',0,1,185,5,6,1,1,1
	.word	.L215,.L451,.L214
	.byte	4
	.byte	'x',0,1,185,5,39
	.word	.L350,.L452
	.byte	4
	.byte	'y',0,1,185,5,49
	.word	.L350,.L453
	.byte	4
	.byte	'image',0,1,185,5,65
	.word	.L454,.L455
	.byte	4
	.byte	'width',0,1,185,5,79
	.word	.L350,.L456
	.byte	4
	.byte	'height',0,1,185,5,93
	.word	.L350,.L457
	.byte	4
	.byte	'dis_width',0,1,185,5,108
	.word	.L350,.L458
	.byte	4
	.byte	'dis_height',0,1,185,5,126
	.word	.L350,.L459
	.byte	5
	.word	.L215,.L451
	.byte	5
	.word	.L460,.L451
	.byte	6
	.byte	'i',0,1,193,5,12
	.word	.L434,.L461
	.byte	6
	.byte	'j',0,1,193,5,19
	.word	.L434,.L462
	.byte	6
	.byte	'temp',0,1,194,5,11
	.word	.L394,.L463
	.byte	6
	.byte	'width_index',0,1,195,5,12
	.word	.L434,.L464
	.byte	6
	.byte	'data_buffer',0,1,196,5,12
	.word	.L348,.L465
	.byte	6
	.byte	'image_temp',0,1,197,5,18
	.word	.L454,.L466
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_binary_image')
	.sect	'.debug_abbrev'
.L291:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_binary_image')
	.sect	'.debug_line'
.L292:
	.word	.L1146-.L1145
.L1145:
	.half	3
	.word	.L1148-.L1147
.L1147:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1148:
	.byte	5,6,7,0,5,2
	.word	.L215
	.byte	3,184,5,1,5,5,9
	.half	.L754-.L215
	.byte	3,4,1,9
	.half	.L748-.L754
	.byte	3,1,1,9
	.half	.L1149-.L748
	.byte	3,1,1,5,33,9
	.half	.L460-.L1149
	.byte	3,5,1,5,5,9
	.half	.L760-.L460
	.byte	3,3,1,5,31,9
	.half	.L112-.L760
	.byte	3,1,1,5,43,9
	.half	.L762-.L112
	.byte	1,5,50,9
	.half	.L763-.L762
	.byte	1,5,63,9
	.half	.L764-.L763
	.byte	1,5,11,9
	.half	.L1150-.L764
	.byte	3,2,1,5,30,9
	.half	.L767-.L1150
	.byte	1,5,32,9
	.half	.L114-.L767
	.byte	3,2,1,5,41,9
	.half	.L768-.L114
	.byte	1,5,54,9
	.half	.L769-.L768
	.byte	1,5,64,9
	.half	.L770-.L769
	.byte	1,5,62,9
	.half	.L1151-.L770
	.byte	1,5,28,9
	.half	.L1152-.L1151
	.byte	1,5,15,9
	.half	.L771-.L1152
	.byte	3,1,1,5,33,9
	.half	.L772-.L771
	.byte	1,5,29,9
	.half	.L116-.L772
	.byte	3,2,1,5,37,9
	.half	.L774-.L116
	.byte	1,5,49,9
	.half	.L775-.L774
	.byte	3,1,1,5,47,9
	.half	.L1153-.L775
	.byte	1,5,33,9
	.half	.L1154-.L1153
	.byte	1,5,20,9
	.half	.L1155-.L1154
	.byte	1,5,47,9
	.half	.L777-.L1155
	.byte	3,1,1,5,45,9
	.half	.L1156-.L777
	.byte	1,5,29,9
	.half	.L776-.L1156
	.byte	1,5,13,9
	.half	.L778-.L776
	.byte	1,5,28,7,9
	.half	.L1157-.L778
	.byte	3,2,1,5,35,9
	.half	.L1158-.L1157
	.byte	1,5,32,9
	.half	.L1159-.L1158
	.byte	1,5,48,9
	.half	.L1160-.L1159
	.byte	1,5,28,9
	.half	.L117-.L1160
	.byte	3,4,1,5,35,9
	.half	.L1161-.L117
	.byte	1,5,32,9
	.half	.L1162-.L1161
	.byte	1,5,37,9
	.half	.L118-.L1162
	.byte	3,118,1,5,33,9
	.half	.L115-.L118
	.byte	1,5,9,7,9
	.half	.L1163-.L115
	.byte	3,13,1,5,34,9
	.half	.L780-.L1163
	.byte	3,112,1,5,30,9
	.half	.L113-.L780
	.byte	1,5,5,7,9
	.half	.L1164-.L113
	.byte	3,18,1,5,1,9
	.half	.L120-.L1164
	.byte	3,1,1,9
	.half	.L294-.L120
	.byte	0,1,1
.L1146:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_binary_image')
	.sect	'.debug_ranges'
.L293:
	.word	-1,.L215,0,.L294-.L215,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_gray_image')
	.sect	'.debug_info'
.L295:
	.word	549
	.half	3
	.word	.L296
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L298,.L297
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_gray_image',0,1,240,5,6,1,1,1
	.word	.L217,.L467,.L216
	.byte	4
	.byte	'x',0,1,240,5,37
	.word	.L350,.L468
	.byte	4
	.byte	'y',0,1,240,5,47
	.word	.L350,.L469
	.byte	4
	.byte	'image',0,1,240,5,63
	.word	.L454,.L470
	.byte	4
	.byte	'width',0,1,240,5,77
	.word	.L350,.L471
	.byte	4
	.byte	'height',0,1,240,5,91
	.word	.L350,.L472
	.byte	4
	.byte	'dis_width',0,1,240,5,106
	.word	.L350,.L473
	.byte	4
	.byte	'dis_height',0,1,240,5,124
	.word	.L350,.L474
	.byte	4
	.byte	'threshold',0,1,240,5,142,1
	.word	.L394,.L475
	.byte	5
	.word	.L217,.L467
	.byte	5
	.word	.L476,.L467
	.byte	6
	.byte	'i',0,1,248,5,12
	.word	.L434,.L477
	.byte	6
	.byte	'j',0,1,248,5,19
	.word	.L434,.L478
	.byte	6
	.byte	'color',0,1,249,5,12
	.word	.L350,.L479
	.byte	6
	.byte	'temp',0,1,249,5,22
	.word	.L350,.L480
	.byte	6
	.byte	'data_buffer',0,1,250,5,12
	.word	.L348,.L481
	.byte	6
	.byte	'image_temp',0,1,251,5,18
	.word	.L454,.L482
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_gray_image')
	.sect	'.debug_abbrev'
.L296:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_gray_image')
	.sect	'.debug_line'
.L297:
	.word	.L1166-.L1165
.L1165:
	.half	3
	.word	.L1168-.L1167
.L1167:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1168:
	.byte	5,6,7,0,5,2
	.word	.L217
	.byte	3,239,5,1,5,5,9
	.half	.L791-.L217
	.byte	3,4,1,9
	.half	.L784-.L791
	.byte	3,1,1,9
	.half	.L1169-.L784
	.byte	3,1,1,5,33,9
	.half	.L476-.L1169
	.byte	3,4,1,5,5,9
	.half	.L797-.L476
	.byte	3,3,1,5,31,9
	.half	.L122-.L797
	.byte	3,1,1,5,43,9
	.half	.L799-.L122
	.byte	1,5,50,9
	.half	.L800-.L799
	.byte	1,5,63,9
	.half	.L801-.L800
	.byte	1,5,11,9
	.half	.L1170-.L801
	.byte	3,2,1,5,30,9
	.half	.L804-.L1170
	.byte	1,5,32,9
	.half	.L124-.L804
	.byte	3,2,1,5,41,9
	.half	.L805-.L124
	.byte	1,5,54,9
	.half	.L806-.L805
	.byte	1,5,28,9
	.half	.L807-.L806
	.byte	1,5,15,9
	.half	.L808-.L807
	.byte	3,1,1,5,33,9
	.half	.L809-.L808
	.byte	1,5,37,9
	.half	.L126-.L809
	.byte	3,2,1,5,45,9
	.half	.L811-.L126
	.byte	1,5,33,9
	.half	.L1171-.L811
	.byte	1,5,20,9
	.half	.L1172-.L1171
	.byte	1,5,13,9
	.half	.L812-.L1172
	.byte	3,1,1,5,43,7,9
	.half	.L1173-.L812
	.byte	3,2,1,5,33,9
	.half	.L1174-.L1173
	.byte	1,5,50,9
	.half	.L1175-.L1174
	.byte	1,5,54,9
	.half	.L814-.L1175
	.byte	3,1,1,5,44,9
	.half	.L1176-.L814
	.byte	1,5,61,9
	.half	.L1177-.L1176
	.byte	1,5,31,9
	.half	.L1178-.L1177
	.byte	1,5,51,9
	.half	.L1179-.L1178
	.byte	3,1,1,5,41,9
	.half	.L813-.L1179
	.byte	1,5,31,9
	.half	.L1180-.L813
	.byte	1,5,28,9
	.half	.L1181-.L1180
	.byte	3,1,1,5,32,9
	.half	.L1182-.L1181
	.byte	1,5,55,9
	.half	.L1183-.L1182
	.byte	3,125,1,5,18,9
	.half	.L127-.L1183
	.byte	3,5,1,5,28,7,9
	.half	.L1184-.L127
	.byte	3,2,1,5,35,9
	.half	.L1185-.L1184
	.byte	1,5,32,9
	.half	.L1186-.L1185
	.byte	1,5,48,9
	.half	.L1187-.L1186
	.byte	1,5,28,9
	.half	.L129-.L1187
	.byte	3,4,1,5,35,9
	.half	.L1188-.L129
	.byte	1,5,32,9
	.half	.L1189-.L1188
	.byte	1,5,37,9
	.half	.L128-.L1189
	.byte	3,112,1,5,33,9
	.half	.L125-.L128
	.byte	1,5,9,7,9
	.half	.L1190-.L125
	.byte	3,19,1,5,34,9
	.half	.L810-.L1190
	.byte	3,106,1,5,30,9
	.half	.L123-.L810
	.byte	1,5,5,7,9
	.half	.L1191-.L123
	.byte	3,24,1,5,1,9
	.half	.L132-.L1191
	.byte	3,1,1,9
	.half	.L299-.L132
	.byte	0,1,1
.L1166:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_gray_image')
	.sect	'.debug_ranges'
.L298:
	.word	-1,.L217,0,.L299-.L217,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_rgb565_image')
	.sect	'.debug_info'
.L300:
	.word	515
	.half	3
	.word	.L301
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L303,.L302
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_rgb565_image',0,1,172,6,6,1,1,1
	.word	.L219,.L483,.L218
	.byte	4
	.byte	'x',0,1,172,6,39
	.word	.L350,.L484
	.byte	4
	.byte	'y',0,1,172,6,49
	.word	.L350,.L485
	.byte	4
	.byte	'image',0,1,172,6,66
	.word	.L486,.L487
	.byte	4
	.byte	'width',0,1,172,6,80
	.word	.L350,.L488
	.byte	4
	.byte	'height',0,1,172,6,94
	.word	.L350,.L489
	.byte	4
	.byte	'dis_width',0,1,172,6,109
	.word	.L350,.L490
	.byte	4
	.byte	'dis_height',0,1,172,6,127
	.word	.L350,.L491
	.byte	4
	.byte	'color_mode',0,1,172,6,145,1
	.word	.L394,.L492
	.byte	5
	.word	.L219,.L483
	.byte	5
	.word	.L493,.L483
	.byte	6
	.byte	'i',0,1,180,6,12
	.word	.L434,.L494
	.byte	6
	.byte	'j',0,1,180,6,19
	.word	.L434,.L495
	.byte	6
	.byte	'data_buffer',0,1,181,6,12
	.word	.L348,.L496
	.byte	6
	.byte	'image_temp',0,1,182,6,19
	.word	.L486,.L497
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_rgb565_image')
	.sect	'.debug_abbrev'
.L301:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_rgb565_image')
	.sect	'.debug_line'
.L302:
	.word	.L1193-.L1192
.L1192:
	.half	3
	.word	.L1195-.L1194
.L1194:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1195:
	.byte	5,6,7,0,5,2
	.word	.L219
	.byte	3,171,6,1,5,5,9
	.half	.L826-.L219
	.byte	3,4,1,9
	.half	.L819-.L826
	.byte	3,1,1,9
	.half	.L1196-.L819
	.byte	3,1,1,5,33,9
	.half	.L493-.L1196
	.byte	3,3,1,5,5,9
	.half	.L832-.L493
	.byte	3,3,1,5,31,9
	.half	.L134-.L832
	.byte	3,1,1,5,43,9
	.half	.L834-.L134
	.byte	1,5,50,9
	.half	.L835-.L834
	.byte	1,5,63,9
	.half	.L836-.L835
	.byte	1,5,11,9
	.half	.L1197-.L836
	.byte	3,2,1,5,30,9
	.half	.L839-.L1197
	.byte	1,5,32,9
	.half	.L136-.L839
	.byte	3,2,1,5,41,9
	.half	.L840-.L136
	.byte	1,5,54,9
	.half	.L841-.L840
	.byte	1,5,28,9
	.half	.L842-.L841
	.byte	1,5,15,9
	.half	.L843-.L842
	.byte	3,1,1,5,33,9
	.half	.L844-.L843
	.byte	1,5,24,9
	.half	.L138-.L844
	.byte	3,2,1,5,47,9
	.half	.L846-.L138
	.byte	1,5,55,9
	.half	.L847-.L846
	.byte	1,5,43,9
	.half	.L1198-.L847
	.byte	1,5,30,9
	.half	.L1199-.L1198
	.byte	1,5,28,9
	.half	.L1200-.L1199
	.byte	1,5,37,9
	.half	.L1201-.L1200
	.byte	3,126,1,5,33,9
	.half	.L137-.L1201
	.byte	1,5,9,7,9
	.half	.L1202-.L137
	.byte	3,4,1,5,13,7,9
	.half	.L1203-.L1202
	.byte	3,2,1,5,78,9
	.half	.L845-.L1203
	.byte	1,5,13,9
	.half	.L139-.L845
	.byte	3,4,1,5,34,9
	.half	.L140-.L139
	.byte	3,115,1,5,30,9
	.half	.L135-.L140
	.byte	1,5,5,7,9
	.half	.L1204-.L135
	.byte	3,16,1,5,1,9
	.half	.L142-.L1204
	.byte	3,1,1,9
	.half	.L304-.L142
	.byte	0,1,1
.L1193:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_rgb565_image')
	.sect	'.debug_ranges'
.L303:
	.word	-1,.L219,0,.L304-.L219,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_wave')
	.sect	'.debug_info'
.L305:
	.word	517
	.half	3
	.word	.L306
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L308,.L307
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_wave',0,1,219,6,6,1,1,1
	.word	.L221,.L498,.L220
	.byte	4
	.byte	'x',0,1,219,6,31
	.word	.L350,.L499
	.byte	4
	.byte	'y',0,1,219,6,41
	.word	.L350,.L500
	.byte	4
	.byte	'wave',0,1,219,6,58
	.word	.L486,.L501
	.byte	4
	.byte	'width',0,1,219,6,71
	.word	.L350,.L502
	.byte	4
	.byte	'value_max',0,1,219,6,85
	.word	.L350,.L503
	.byte	4
	.byte	'dis_width',0,1,219,6,103
	.word	.L350,.L504
	.byte	4
	.byte	'dis_value_max',0,1,219,6,121
	.word	.L350,.L505
	.byte	5
	.word	.L221,.L498
	.byte	5
	.word	.L506,.L498
	.byte	6
	.byte	'i',0,1,227,6,12
	.word	.L434,.L507
	.byte	6
	.byte	'j',0,1,227,6,19
	.word	.L434,.L508
	.byte	6
	.byte	'width_index',0,1,228,6,12
	.word	.L434,.L509
	.byte	6
	.byte	'value_max_index',0,1,228,6,29
	.word	.L434,.L510
	.byte	6
	.byte	'data_buffer',0,1,229,6,12
	.word	.L348,.L511
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_wave')
	.sect	'.debug_abbrev'
.L306:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_wave')
	.sect	'.debug_line'
.L307:
	.word	.L1206-.L1205
.L1205:
	.half	3
	.word	.L1208-.L1207
.L1207:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1208:
	.byte	5,6,7,0,5,2
	.word	.L221
	.byte	3,218,6,1,5,5,9
	.half	.L859-.L221
	.byte	3,4,1,9
	.half	.L853-.L859
	.byte	3,1,1,9
	.half	.L1209-.L853
	.byte	3,1,1,5,33,9
	.half	.L506-.L1209
	.byte	3,4,1,5,5,9
	.half	.L865-.L506
	.byte	3,2,1,5,31,9
	.half	.L144-.L865
	.byte	3,1,1,5,43,9
	.half	.L867-.L144
	.byte	1,5,50,9
	.half	.L868-.L867
	.byte	1,5,66,9
	.half	.L869-.L868
	.byte	1,5,11,9
	.half	.L1210-.L869
	.byte	3,1,1,5,33,9
	.half	.L872-.L1210
	.byte	1,5,15,9
	.half	.L146-.L872
	.byte	3,2,1,5,33,9
	.half	.L874-.L146
	.byte	1,5,24,9
	.half	.L148-.L874
	.byte	3,2,1,5,31,9
	.half	.L1211-.L148
	.byte	1,5,28,9
	.half	.L1212-.L1211
	.byte	1,5,37,9
	.half	.L1213-.L1212
	.byte	3,126,1,5,33,9
	.half	.L147-.L1213
	.byte	1,5,9,7,9
	.half	.L1214-.L147
	.byte	3,4,1,5,37,9
	.half	.L875-.L1214
	.byte	3,122,1,5,33,9
	.half	.L145-.L875
	.byte	1,5,5,7,9
	.half	.L1215-.L145
	.byte	3,8,1,5,11,9
	.half	.L150-.L1215
	.byte	3,2,1,5,29,9
	.half	.L873-.L150
	.byte	1,5,25,9
	.half	.L152-.L873
	.byte	3,2,1,5,33,9
	.half	.L878-.L152
	.byte	1,5,34,9
	.half	.L879-.L878
	.byte	3,1,1,5,27,9
	.half	.L1216-.L879
	.byte	1,5,66,9
	.half	.L880-.L1216
	.byte	1,5,49,9
	.half	.L1217-.L880
	.byte	1,5,71,9
	.half	.L881-.L1217
	.byte	1,5,38,9
	.half	.L882-.L881
	.byte	3,1,1,5,27,9
	.half	.L884-.L882
	.byte	1,5,68,9
	.half	.L1218-.L884
	.byte	1,5,73,9
	.half	.L1219-.L1218
	.byte	1,5,91,9
	.half	.L885-.L1219
	.byte	1,5,44,9
	.half	.L886-.L885
	.byte	1,5,97,9
	.half	.L1220-.L886
	.byte	1,5,33,9
	.half	.L883-.L1220
	.byte	3,124,1,5,29,9
	.half	.L151-.L883
	.byte	1,5,1,7,9
	.half	.L1221-.L151
	.byte	3,6,1,9
	.half	.L309-.L1221
	.byte	0,1,1
.L1206:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_wave')
	.sect	'.debug_ranges'
.L308:
	.word	-1,.L221,0,.L309-.L221,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_show_chinese')
	.sect	'.debug_info'
.L310:
	.word	507
	.half	3
	.word	.L311
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L313,.L312
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_show_chinese',0,1,135,7,6,1,1,1
	.word	.L223,.L512,.L222
	.byte	4
	.byte	'x',0,1,135,7,34
	.word	.L350,.L513
	.byte	4
	.byte	'y',0,1,135,7,44
	.word	.L350,.L514
	.byte	4
	.byte	'size',0,1,135,7,53
	.word	.L394,.L515
	.byte	4
	.byte	'chinese_buffer',0,1,135,7,72
	.word	.L454,.L516
	.byte	4
	.byte	'number',0,1,135,7,94
	.word	.L394,.L517
	.byte	4
	.byte	'color',0,1,135,7,115
	.word	.L518,.L519
	.byte	5
	.word	.L223,.L512
	.byte	5
	.word	.L520,.L512
	.byte	6
	.byte	'i',0,1,143,7,9
	.word	.L422,.L521
	.byte	6
	.byte	'j',0,1,143,7,16
	.word	.L422,.L522
	.byte	6
	.byte	'k',0,1,143,7,23
	.word	.L422,.L523
	.byte	6
	.byte	'temp',0,1,144,7,11
	.word	.L394,.L524
	.byte	6
	.byte	'temp1',0,1,144,7,21
	.word	.L394,.L525
	.byte	6
	.byte	'temp2',0,1,144,7,32
	.word	.L394,.L526
	.byte	6
	.byte	'p_data',0,1,145,7,18
	.word	.L454,.L527
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_show_chinese')
	.sect	'.debug_abbrev'
.L311:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_show_chinese')
	.sect	'.debug_line'
.L312:
	.word	.L1223-.L1222
.L1222:
	.half	3
	.word	.L1225-.L1224
.L1224:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1225:
	.byte	5,6,7,0,5,2
	.word	.L223
	.byte	3,134,7,1,5,5,9
	.half	.L897-.L223
	.byte	3,4,1,9
	.half	.L891-.L897
	.byte	3,1,1,9
	.half	.L1226-.L891
	.byte	3,1,1,5,20,9
	.half	.L520-.L1226
	.byte	3,6,1,5,18,9
	.half	.L1227-.L520
	.byte	1,5,5,9
	.half	.L901-.L1227
	.byte	3,2,1,5,36,9
	.half	.L154-.L901
	.byte	3,1,1,5,43,9
	.half	.L903-.L154
	.byte	1,5,47,9
	.half	.L904-.L903
	.byte	1,5,54,9
	.half	.L906-.L904
	.byte	1,5,61,9
	.half	.L907-.L906
	.byte	1,5,11,9
	.half	.L1228-.L907
	.byte	3,2,1,5,24,9
	.half	.L910-.L1228
	.byte	1,5,15,9
	.half	.L156-.L910
	.byte	3,2,1,5,37,9
	.half	.L913-.L156
	.byte	3,1,1,5,33,9
	.half	.L912-.L913
	.byte	1,5,23,9
	.half	.L914-.L912
	.byte	3,1,1,5,19,9
	.half	.L158-.L914
	.byte	3,2,1,5,33,9
	.half	.L916-.L158
	.byte	1,5,23,9
	.half	.L160-.L916
	.byte	3,2,1,5,33,9
	.half	.L917-.L160
	.byte	1,5,29,9
	.half	.L162-.L917
	.byte	3,2,1,5,43,9
	.half	.L1229-.L162
	.byte	1,5,37,9
	.half	.L1230-.L1229
	.byte	1,5,49,9
	.half	.L1231-.L1230
	.byte	1,5,21,9
	.half	.L918-.L1231
	.byte	3,1,1,5,25,7,9
	.half	.L1232-.L918
	.byte	3,2,1,5,55,9
	.half	.L920-.L1232
	.byte	1,5,25,9
	.half	.L163-.L920
	.byte	3,4,1,5,37,9
	.half	.L164-.L163
	.byte	3,119,1,5,33,9
	.half	.L161-.L164
	.byte	1,5,24,7,9
	.half	.L1233-.L161
	.byte	3,12,1,5,37,9
	.half	.L1234-.L1233
	.byte	3,114,1,5,33,9
	.half	.L159-.L1234
	.byte	1,5,45,7,9
	.half	.L1235-.L159
	.byte	3,16,1,5,22,9
	.half	.L1236-.L1235
	.byte	1,5,29,9
	.half	.L921-.L1236
	.byte	1,5,37,9
	.half	.L922-.L921
	.byte	1,5,20,9
	.half	.L915-.L922
	.byte	1,5,21,9
	.half	.L157-.L915
	.byte	3,110,1,5,23,9
	.half	.L925-.L157
	.byte	1,5,28,7,9
	.half	.L926-.L925
	.byte	3,124,1,5,24,9
	.half	.L155-.L926
	.byte	1,5,5,7,9
	.half	.L1237-.L155
	.byte	3,25,1,5,1,9
	.half	.L166-.L1237
	.byte	3,1,1,7,9
	.half	.L314-.L166
	.byte	0,1,1
.L1223:
	.sdecl	'.debug_ranges',debug,cluster('tft180_show_chinese')
	.sect	'.debug_ranges'
.L313:
	.word	-1,.L223,0,.L314-.L223,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_init')
	.sect	'.debug_info'
.L315:
	.word	252
	.half	3
	.word	.L316
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L318,.L317
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_init',0,1,187,7,6,1,1,1
	.word	.L225,.L528,.L224
	.byte	4
	.word	.L225,.L528
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_init')
	.sect	'.debug_abbrev'
.L316:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_init')
	.sect	'.debug_line'
.L317:
	.word	.L1239-.L1238
.L1238:
	.half	3
	.word	.L1241-.L1240
.L1240:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1241:
	.byte	5,6,7,0,5,2
	.word	.L225
	.byte	3,186,7,1,5,71,9
	.half	.L927-.L225
	.byte	3,5,1,5,87,9
	.half	.L1242-.L927
	.byte	1,5,106,9
	.half	.L1243-.L1242
	.byte	1,5,14,9
	.half	.L1244-.L1243
	.byte	1,5,26,9
	.half	.L1245-.L1244
	.byte	1,5,37,9
	.half	.L1246-.L1245
	.byte	1,5,55,9
	.half	.L1247-.L1246
	.byte	1,5,15,9
	.half	.L1248-.L1247
	.byte	3,3,1,5,30,9
	.half	.L1249-.L1248
	.byte	1,5,35,9
	.half	.L1250-.L1249
	.byte	1,5,45,9
	.half	.L1251-.L1250
	.byte	1,5,15,9
	.half	.L1252-.L1251
	.byte	3,1,1,5,31,9
	.half	.L1253-.L1252
	.byte	1,5,36,9
	.half	.L1254-.L1253
	.byte	1,5,46,9
	.half	.L1255-.L1254
	.byte	1,5,15,9
	.half	.L1256-.L1255
	.byte	3,1,1,5,30,9
	.half	.L1257-.L1256
	.byte	1,5,35,9
	.half	.L1258-.L1257
	.byte	1,5,46,9
	.half	.L1259-.L1258
	.byte	1,5,15,9
	.half	.L1260-.L1259
	.byte	3,1,1,5,30,9
	.half	.L1261-.L1260
	.byte	1,5,35,9
	.half	.L1262-.L1261
	.byte	1,5,46,9
	.half	.L1263-.L1262
	.byte	1,5,20,9
	.half	.L1264-.L1263
	.byte	3,2,1,5,22,9
	.half	.L1265-.L1264
	.byte	3,1,1,5,39,9
	.half	.L1266-.L1265
	.byte	1,5,5,9
	.half	.L1267-.L1266
	.byte	3,2,1,5,21,9
	.half	.L168-.L1267
	.byte	3,1,1,5,5,9
	.half	.L1268-.L168
	.byte	3,2,1,5,21,9
	.half	.L170-.L1268
	.byte	3,1,1,5,5,9
	.half	.L1269-.L170
	.byte	3,1,1,5,24,9
	.half	.L172-.L1269
	.byte	3,2,1,5,21,9
	.half	.L1270-.L172
	.byte	3,1,1,5,24,9
	.half	.L1271-.L1270
	.byte	3,2,1,5,5,9
	.half	.L1272-.L1271
	.byte	3,1,1,9
	.half	.L1273-.L1272
	.byte	3,1,1,9
	.half	.L1274-.L1273
	.byte	3,1,1,5,24,9
	.half	.L1275-.L1274
	.byte	3,2,1,5,5,9
	.half	.L1276-.L1275
	.byte	3,1,1,9
	.half	.L1277-.L1276
	.byte	3,1,1,9
	.half	.L1278-.L1277
	.byte	3,1,1,5,24,9
	.half	.L1279-.L1278
	.byte	3,2,1,5,5,9
	.half	.L1280-.L1279
	.byte	3,1,1,9
	.half	.L1281-.L1280
	.byte	3,1,1,9
	.half	.L1282-.L1281
	.byte	3,1,1,9
	.half	.L1283-.L1282
	.byte	3,1,1,9
	.half	.L1284-.L1283
	.byte	3,1,1,9
	.half	.L1285-.L1284
	.byte	3,1,1,5,24,9
	.half	.L1286-.L1285
	.byte	3,2,1,5,5,9
	.half	.L1287-.L1286
	.byte	3,1,1,5,24,9
	.half	.L1288-.L1287
	.byte	3,2,1,5,5,9
	.half	.L1289-.L1288
	.byte	3,1,1,9
	.half	.L1290-.L1289
	.byte	3,1,1,9
	.half	.L1291-.L1290
	.byte	3,1,1,5,24,9
	.half	.L1292-.L1291
	.byte	3,1,1,5,5,9
	.half	.L1293-.L1292
	.byte	3,1,1,5,24,9
	.half	.L1294-.L1293
	.byte	3,2,1,5,5,9
	.half	.L1295-.L1294
	.byte	3,1,1,9
	.half	.L1296-.L1295
	.byte	3,1,1,5,24,9
	.half	.L1297-.L1296
	.byte	3,2,1,5,5,9
	.half	.L1298-.L1297
	.byte	3,1,1,9
	.half	.L1299-.L1298
	.byte	3,1,1,5,24,9
	.half	.L1300-.L1299
	.byte	3,1,1,5,5,9
	.half	.L1301-.L1300
	.byte	3,1,1,9
	.half	.L1302-.L1301
	.byte	3,1,1,5,24,9
	.half	.L1303-.L1302
	.byte	3,2,1,5,5,9
	.half	.L1304-.L1303
	.byte	3,1,1,5,24,9
	.half	.L1305-.L1304
	.byte	3,2,1,5,12,9
	.half	.L1306-.L1305
	.byte	3,1,1,5,14,9
	.half	.L1307-.L1306
	.byte	3,2,1,9
	.half	.L1308-.L1307
	.byte	3,1,1,9
	.half	.L1309-.L1308
	.byte	3,1,1,9
	.half	.L1310-.L1309
	.byte	3,1,1,5,37,9
	.half	.L173-.L1310
	.byte	3,125,1,5,82,9
	.half	.L1311-.L173
	.byte	1,5,37,9
	.half	.L174-.L1311
	.byte	3,1,1,5,82,9
	.half	.L1312-.L174
	.byte	1,5,37,9
	.half	.L175-.L1312
	.byte	3,1,1,5,82,9
	.half	.L1313-.L175
	.byte	1,5,37,9
	.half	.L176-.L1313
	.byte	3,1,1,5,82,9
	.half	.L1314-.L176
	.byte	1,5,24,9
	.half	.L178-.L1314
	.byte	3,3,1,5,5,9
	.half	.L1315-.L178
	.byte	3,1,1,9
	.half	.L1316-.L1315
	.byte	3,1,1,9
	.half	.L1317-.L1316
	.byte	3,1,1,9
	.half	.L1318-.L1317
	.byte	3,1,1,9
	.half	.L1319-.L1318
	.byte	3,1,1,9
	.half	.L1320-.L1319
	.byte	3,1,1,9
	.half	.L1321-.L1320
	.byte	3,1,1,9
	.half	.L1322-.L1321
	.byte	3,1,1,9
	.half	.L1323-.L1322
	.byte	3,1,1,9
	.half	.L1324-.L1323
	.byte	3,1,1,9
	.half	.L1325-.L1324
	.byte	3,1,1,9
	.half	.L1326-.L1325
	.byte	3,1,1,9
	.half	.L1327-.L1326
	.byte	3,1,1,9
	.half	.L1328-.L1327
	.byte	3,1,1,9
	.half	.L1329-.L1328
	.byte	3,1,1,9
	.half	.L1330-.L1329
	.byte	3,1,1,5,24,9
	.half	.L1331-.L1330
	.byte	3,2,1,5,5,9
	.half	.L1332-.L1331
	.byte	3,1,1,9
	.half	.L1333-.L1332
	.byte	3,1,1,9
	.half	.L1334-.L1333
	.byte	3,1,1,9
	.half	.L1335-.L1334
	.byte	3,1,1,9
	.half	.L1336-.L1335
	.byte	3,1,1,9
	.half	.L1337-.L1336
	.byte	3,1,1,9
	.half	.L1338-.L1337
	.byte	3,1,1,9
	.half	.L1339-.L1338
	.byte	3,1,1,9
	.half	.L1340-.L1339
	.byte	3,1,1,9
	.half	.L1341-.L1340
	.byte	3,1,1,9
	.half	.L1342-.L1341
	.byte	3,1,1,9
	.half	.L1343-.L1342
	.byte	3,1,1,9
	.half	.L1344-.L1343
	.byte	3,1,1,9
	.half	.L1345-.L1344
	.byte	3,1,1,9
	.half	.L1346-.L1345
	.byte	3,1,1,9
	.half	.L1347-.L1346
	.byte	3,1,1,5,24,9
	.half	.L1348-.L1347
	.byte	3,2,1,5,5,9
	.half	.L1349-.L1348
	.byte	3,1,1,9
	.half	.L1350-.L1349
	.byte	3,1,1,9
	.half	.L1351-.L1350
	.byte	3,1,1,9
	.half	.L1352-.L1351
	.byte	3,1,1,5,24,9
	.half	.L1353-.L1352
	.byte	3,2,1,5,5,9
	.half	.L1354-.L1353
	.byte	3,1,1,9
	.half	.L1355-.L1354
	.byte	3,1,1,9
	.half	.L1356-.L1355
	.byte	3,1,1,9
	.half	.L1357-.L1356
	.byte	3,1,1,5,24,9
	.half	.L1358-.L1357
	.byte	3,2,1,5,5,9
	.half	.L1359-.L1358
	.byte	3,1,1,5,24,9
	.half	.L1360-.L1359
	.byte	3,1,1,5,5,9
	.half	.L1361-.L1360
	.byte	3,1,1,5,24,9
	.half	.L1362-.L1361
	.byte	3,2,1,5,5,9
	.half	.L1363-.L1362
	.byte	3,1,1,5,24,9
	.half	.L1364-.L1363
	.byte	3,1,1,5,5,9
	.half	.L1365-.L1364
	.byte	3,1,1,5,17,9
	.half	.L183-.L1365
	.byte	3,2,1,5,22,9
	.half	.L1366-.L183
	.byte	3,1,1,5,1,9
	.half	.L1367-.L1366
	.byte	3,1,1,7,9
	.half	.L319-.L1367
	.byte	0,1,1
.L1239:
	.sdecl	'.debug_ranges',debug,cluster('tft180_init')
	.sect	'.debug_ranges'
.L318:
	.word	-1,.L225,0,.L319-.L225,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_write_index')
	.sect	'.debug_info'
.L320:
	.word	275
	.half	3
	.word	.L321
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L323,.L322
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_write_index',0,1,154,1,13,1,1
	.word	.L185,.L529,.L184
	.byte	4
	.byte	'dat',0,1,154,1,45
	.word	.L530,.L531
	.byte	5
	.word	.L185,.L529
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_write_index')
	.sect	'.debug_abbrev'
.L321:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_write_index')
	.sect	'.debug_line'
.L322:
	.word	.L1369-.L1368
.L1368:
	.half	3
	.word	.L1371-.L1370
.L1370:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1371:
	.byte	5,13,7,0,5,2
	.word	.L185
	.byte	3,153,1,1,5,5,9
	.half	.L541-.L185
	.byte	3,2,1,9
	.half	.L3-.L541
	.byte	3,1,1,9
	.half	.L544-.L3
	.byte	3,1,1,5,1,9
	.half	.L5-.L544
	.byte	3,1,1,7,9
	.half	.L324-.L5
	.byte	0,1,1
.L1369:
	.sdecl	'.debug_ranges',debug,cluster('tft180_write_index')
	.sect	'.debug_ranges'
.L323:
	.word	-1,.L185,0,.L324-.L185,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_set_region')
	.sect	'.debug_info'
.L325:
	.word	321
	.half	3
	.word	.L326
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L328,.L327
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_set_region',0,1,171,1,13,1,1
	.word	.L187,.L532,.L186
	.byte	4
	.byte	'x1',0,1,171,1,39
	.word	.L350,.L533
	.byte	4
	.byte	'y1',0,1,171,1,50
	.word	.L350,.L534
	.byte	4
	.byte	'x2',0,1,171,1,61
	.word	.L350,.L535
	.byte	4
	.byte	'y2',0,1,171,1,72
	.word	.L350,.L536
	.byte	5
	.word	.L187,.L532
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_set_region')
	.sect	'.debug_abbrev'
.L326:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_set_region')
	.sect	'.debug_line'
.L327:
	.word	.L1373-.L1372
.L1372:
	.half	3
	.word	.L1375-.L1374
.L1374:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1375:
	.byte	5,13,7,0,5,2
	.word	.L187
	.byte	3,170,1,1,5,5,9
	.half	.L1376-.L187
	.byte	3,5,1,9
	.half	.L546-.L1376
	.byte	3,1,1,9
	.half	.L1377-.L546
	.byte	3,1,1,9
	.half	.L1378-.L1377
	.byte	3,1,1,5,12,9
	.half	.L1379-.L1378
	.byte	3,2,1,5,14,9
	.half	.L1380-.L1379
	.byte	3,2,1,9
	.half	.L1381-.L1380
	.byte	3,1,1,9
	.half	.L1382-.L1381
	.byte	3,14,1,9
	.half	.L1383-.L1382
	.byte	3,1,1,5,32,9
	.half	.L7-.L1383
	.byte	3,115,1,5,13,9
	.half	.L1384-.L7
	.byte	3,1,1,9
	.half	.L1385-.L1384
	.byte	3,1,1,9
	.half	.L1386-.L1385
	.byte	3,1,1,9
	.half	.L1387-.L1386
	.byte	3,1,1,5,32,9
	.half	.L1388-.L1387
	.byte	3,2,1,5,13,9
	.half	.L1389-.L1388
	.byte	3,1,1,9
	.half	.L1390-.L1389
	.byte	3,1,1,9
	.half	.L1391-.L1390
	.byte	3,1,1,9
	.half	.L1392-.L1391
	.byte	3,1,1,5,10,9
	.half	.L1393-.L1392
	.byte	3,1,1,5,32,9
	.half	.L9-.L1393
	.byte	3,4,1,5,13,9
	.half	.L1394-.L9
	.byte	3,1,1,9
	.half	.L1395-.L1394
	.byte	3,1,1,9
	.half	.L1396-.L1395
	.byte	3,1,1,9
	.half	.L1397-.L1396
	.byte	3,1,1,5,32,9
	.half	.L1398-.L1397
	.byte	3,2,1,5,13,9
	.half	.L1399-.L1398
	.byte	3,1,1,9
	.half	.L1400-.L1399
	.byte	3,1,1,9
	.half	.L1401-.L1400
	.byte	3,1,1,9
	.half	.L1402-.L1401
	.byte	3,1,1,5,10,9
	.half	.L1403-.L1402
	.byte	3,1,1,5,24,9
	.half	.L11-.L1403
	.byte	3,2,1,5,1,9
	.half	.L1404-.L11
	.byte	3,1,1,7,9
	.half	.L329-.L1404
	.byte	0,1,1
.L1373:
	.sdecl	'.debug_ranges',debug,cluster('tft180_set_region')
	.sect	'.debug_ranges'
.L328:
	.word	-1,.L187,0,.L329-.L187,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_debug_init')
	.sect	'.debug_info'
.L330:
	.word	276
	.half	3
	.word	.L331
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L333,.L332
	.byte	2
	.word	.L226
	.byte	3
	.byte	'tft180_debug_init',0,1,224,1,13,1,1
	.word	.L189,.L537,.L188
	.byte	4
	.word	.L189,.L537
	.byte	5
	.byte	'info',0,1,226,1,25
	.word	.L538,.L539
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_debug_init')
	.sect	'.debug_abbrev'
.L331:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('tft180_debug_init')
	.sect	'.debug_line'
.L332:
	.word	.L1406-.L1405
.L1405:
	.half	3
	.word	.L1408-.L1407
.L1407:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_tft180.c',0,0,0,0,0
.L1408:
	.byte	5,13,7,0,5,2
	.word	.L189
	.byte	3,223,1,1,5,31,9
	.half	.L555-.L189
	.byte	3,3,1,5,23,9
	.half	.L1409-.L555
	.byte	3,2,1,5,21,9
	.half	.L1410-.L1409
	.byte	1,5,26,9
	.half	.L1411-.L1410
	.byte	3,1,1,5,24,9
	.half	.L1412-.L1411
	.byte	1,5,26,9
	.half	.L1413-.L1412
	.byte	3,1,1,5,24,9
	.half	.L1414-.L1413
	.byte	1,5,12,9
	.half	.L1415-.L1414
	.byte	3,2,1,5,14,9
	.half	.L1416-.L1415
	.byte	3,2,1,9
	.half	.L1417-.L1416
	.byte	3,5,1,9
	.half	.L1418-.L1417
	.byte	3,5,1,5,32,9
	.half	.L13-.L1418
	.byte	3,120,1,5,30,9
	.half	.L1419-.L13
	.byte	1,5,32,9
	.half	.L1420-.L1419
	.byte	3,1,1,5,30,9
	.half	.L1421-.L1420
	.byte	1,5,10,9
	.half	.L1422-.L1421
	.byte	3,1,1,5,32,9
	.half	.L14-.L1422
	.byte	3,3,1,5,30,9
	.half	.L1423-.L14
	.byte	1,5,32,9
	.half	.L1424-.L1423
	.byte	3,1,1,5,30,9
	.half	.L1425-.L1424
	.byte	1,5,10,9
	.half	.L1426-.L1425
	.byte	3,1,1,9
	.half	.L15-.L1426
	.byte	3,4,1,5,26,9
	.half	.L17-.L15
	.byte	3,2,1,5,24,9
	.half	.L1427-.L17
	.byte	1,5,32,9
	.half	.L1428-.L1427
	.byte	3,1,1,5,30,9
	.half	.L1429-.L1428
	.byte	1,5,24,9
	.half	.L1430-.L1429
	.byte	3,2,1,5,1,9
	.half	.L1431-.L1430
	.byte	3,1,1,7,9
	.half	.L334-.L1431
	.byte	0,1,1
.L1406:
	.sdecl	'.debug_ranges',debug,cluster('tft180_debug_init')
	.sect	'.debug_ranges'
.L333:
	.word	-1,.L189,0,.L334-.L189,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_width_max')
	.sect	'.debug_info'
.L335:
	.word	234
	.half	3
	.word	.L336
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L226
	.byte	3
	.byte	'tft180_width_max',0,12,60,34
	.word	.L350
	.byte	1,5,3
	.word	tft180_width_max
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_width_max')
	.sect	'.debug_abbrev'
.L336:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_height_max')
	.sect	'.debug_info'
.L337:
	.word	235
	.half	3
	.word	.L338
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L226
	.byte	3
	.byte	'tft180_height_max',0,12,61,28
	.word	.L350
	.byte	1,5,3
	.word	tft180_height_max
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_height_max')
	.sect	'.debug_abbrev'
.L338:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_pencolor')
	.sect	'.debug_info'
.L339:
	.word	232
	.half	3
	.word	.L340
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L226
	.byte	3
	.byte	'tft180_pencolor',0,12,62,34
	.word	.L350
	.byte	5,3
	.word	tft180_pencolor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_pencolor')
	.sect	'.debug_abbrev'
.L340:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_bgcolor')
	.sect	'.debug_info'
.L341:
	.word	231
	.half	3
	.word	.L342
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L226
	.byte	3
	.byte	'tft180_bgcolor',0,12,63,34
	.word	.L350
	.byte	5,3
	.word	tft180_bgcolor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_bgcolor')
	.sect	'.debug_abbrev'
.L342:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_display_dir')
	.sect	'.debug_info'
.L343:
	.word	235
	.half	3
	.word	.L344
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L226
	.byte	3
	.byte	'tft180_display_dir',0,12,65,34
	.word	.L360
	.byte	5,3
	.word	tft180_display_dir
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_display_dir')
	.sect	'.debug_abbrev'
.L344:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('tft180_display_font')
	.sect	'.debug_info'
.L345:
	.word	236
	.half	3
	.word	.L346
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_tft180.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L226
	.byte	3
	.byte	'tft180_display_font',0,12,66,34
	.word	.L363
	.byte	5,3
	.word	tft180_display_font
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('tft180_display_font')
	.sect	'.debug_abbrev'
.L346:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_clear')
	.sect	'.debug_loc'
.L349:
	.word	-1,.L191,.L556-.L191,.L557-.L191
	.half	1
	.byte	98
	.word	.L558-.L191,.L347-.L191
	.half	1
	.byte	108
	.word	.L20-.L191,.L559-.L191
	.half	1
	.byte	98
	.word	.L564-.L191,.L565-.L191
	.half	1
	.byte	100
	.word	.L567-.L191,.L568-.L191
	.half	1
	.byte	100
	.word	0,0
.L351:
	.word	-1,.L191,.L560-.L191,.L561-.L191
	.half	5
	.byte	144,32,157,32,0
	.word	.L22-.L191,.L25-.L191
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L352:
	.word	-1,.L191,.L562-.L191,.L563-.L191
	.half	1
	.byte	95
	.word	.L24-.L191,.L566-.L191
	.half	1
	.byte	95
	.word	0,0
.L190:
	.word	-1,.L191,0,.L347-.L191
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_debug_init')
	.sect	'.debug_loc'
.L539:
	.word	-1,.L189,0,.L537-.L189
	.half	2
	.byte	145,104
	.word	0,0
.L188:
	.word	-1,.L189,0,.L555-.L189
	.half	2
	.byte	138,0
	.word	.L555-.L189,.L537-.L189
	.half	2
	.byte	138,24
	.word	.L537-.L189,.L537-.L189
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_draw_line')
	.sect	'.debug_loc'
.L380:
	.word	-1,.L203,0,.L374-.L203
	.half	2
	.byte	145,0
	.word	.L601-.L203,.L374-.L203
	.half	1
	.byte	91
	.word	.L617-.L203,.L618-.L203
	.half	1
	.byte	86
	.word	.L621-.L203,.L622-.L203
	.half	1
	.byte	86
	.word	.L632-.L203,.L633-.L203
	.half	1
	.byte	86
	.word	.L637-.L203,.L638-.L203
	.half	1
	.byte	86
	.word	.L640-.L203,.L641-.L203
	.half	1
	.byte	86
	.word	.L647-.L203,.L56-.L203
	.half	1
	.byte	86
	.word	0,0
.L387:
	.word	-1,.L203,.L615-.L203,.L52-.L203
	.half	1
	.byte	92
	.word	.L53-.L203,.L56-.L203
	.half	1
	.byte	92
	.word	0,0
.L386:
	.word	-1,.L203,.L613-.L203,.L52-.L203
	.half	1
	.byte	93
	.word	.L53-.L203,.L56-.L203
	.half	1
	.byte	93
	.word	0,0
.L202:
	.word	-1,.L203,0,.L594-.L203
	.half	2
	.byte	138,0
	.word	.L594-.L203,.L374-.L203
	.half	2
	.byte	138,8
	.word	.L374-.L203,.L374-.L203
	.half	2
	.byte	138,0
	.word	0,0
.L383:
	.word	-1,.L203,.L642-.L203,.L643-.L203
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L377:
	.word	-1,.L203,0,.L595-.L203
	.half	1
	.byte	86
	.word	.L604-.L203,.L605-.L203
	.half	1
	.byte	89
	.word	.L381-.L203,.L606-.L203
	.half	1
	.byte	89
	.word	.L51-.L203,.L609-.L203
	.half	1
	.byte	89
	.word	.L611-.L203,.L612-.L203
	.half	1
	.byte	89
	.word	.L55-.L203,.L616-.L203
	.half	1
	.byte	89
	.word	.L619-.L203,.L620-.L203
	.half	1
	.byte	89
	.word	.L58-.L203,.L627-.L203
	.half	1
	.byte	89
	.word	.L628-.L203,.L629-.L203
	.half	1
	.byte	89
	.word	.L59-.L203,.L630-.L203
	.half	1
	.byte	89
	.word	.L65-.L203,.L645-.L203
	.half	1
	.byte	89
	.word	0,0
.L375:
	.word	-1,.L203,0,.L596-.L203
	.half	1
	.byte	84
	.word	.L598-.L203,.L599-.L203
	.half	1
	.byte	95
	.word	.L62-.L203,.L636-.L203
	.half	1
	.byte	95
	.word	.L644-.L203,.L374-.L203
	.half	1
	.byte	95
	.word	0,0
.L384:
	.word	-1,.L203,.L608-.L203,.L49-.L203
	.half	1
	.byte	94
	.word	.L51-.L203,.L374-.L203
	.half	1
	.byte	94
	.word	0,0
.L378:
	.word	-1,.L203,0,.L595-.L203
	.half	1
	.byte	87
	.word	.L600-.L203,.L374-.L203
	.half	1
	.byte	90
	.word	0,0
.L376:
	.word	-1,.L203,0,.L597-.L203
	.half	1
	.byte	85
	.word	.L602-.L203,.L603-.L203
	.half	1
	.byte	88
	.word	.L48-.L203,.L607-.L203
	.half	1
	.byte	88
	.word	.L609-.L203,.L610-.L203
	.half	1
	.byte	88
	.word	.L613-.L203,.L614-.L203
	.half	1
	.byte	88
	.word	.L55-.L203,.L616-.L203
	.half	1
	.byte	88
	.word	.L54-.L203,.L619-.L203
	.half	1
	.byte	88
	.word	.L53-.L203,.L623-.L203
	.half	1
	.byte	88
	.word	.L624-.L203,.L625-.L203
	.half	1
	.byte	88
	.word	.L57-.L203,.L626-.L203
	.half	1
	.byte	88
	.word	.L63-.L203,.L631-.L203
	.half	1
	.byte	88
	.word	.L634-.L203,.L635-.L203
	.half	1
	.byte	88
	.word	.L66-.L203,.L639-.L203
	.half	1
	.byte	88
	.word	.L646-.L203,.L645-.L203
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_draw_point')
	.sect	'.debug_loc'
.L373:
	.word	-1,.L201,0,.L584-.L201
	.half	1
	.byte	86
	.word	.L587-.L201,.L369-.L201
	.half	1
	.byte	90
	.word	.L592-.L201,.L593-.L201
	.half	1
	.byte	85
	.word	0,0
.L200:
	.word	-1,.L201,0,.L369-.L201
	.half	2
	.byte	138,0
	.word	0,0
.L370:
	.word	-1,.L201,0,.L585-.L201
	.half	1
	.byte	84
	.word	.L588-.L201,.L585-.L201
	.half	1
	.byte	88
	.word	.L44-.L201,.L591-.L201
	.half	1
	.byte	88
	.word	0,0
.L371:
	.word	-1,.L201,0,.L586-.L201
	.half	1
	.byte	85
	.word	.L589-.L201,.L590-.L201
	.half	1
	.byte	89
	.word	.L44-.L201,.L591-.L201
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_full')
	.sect	'.debug_loc'
.L355:
	.word	-1,.L193,0,.L569-.L193
	.half	1
	.byte	84
	.word	.L570-.L193,.L353-.L193
	.half	1
	.byte	88
	.word	0,0
.L356:
	.word	-1,.L193,.L571-.L193,.L572-.L193
	.half	1
	.byte	98
	.word	.L573-.L193,.L353-.L193
	.half	1
	.byte	108
	.word	.L28-.L193,.L574-.L193
	.half	1
	.byte	98
	.word	.L579-.L193,.L580-.L193
	.half	1
	.byte	100
	.word	.L582-.L193,.L583-.L193
	.half	1
	.byte	100
	.word	0,0
.L357:
	.word	-1,.L193,.L575-.L193,.L576-.L193
	.half	5
	.byte	144,32,157,32,0
	.word	.L30-.L193,.L33-.L193
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L358:
	.word	-1,.L193,.L577-.L193,.L578-.L193
	.half	1
	.byte	95
	.word	.L32-.L193,.L581-.L193
	.half	1
	.byte	95
	.word	0,0
.L192:
	.word	-1,.L193,0,.L353-.L193
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_init')
	.sect	'.debug_loc'
.L224:
	.word	-1,.L225,0,.L927-.L225
	.half	2
	.byte	138,0
	.word	.L927-.L225,.L528-.L225
	.half	2
	.byte	138,16
	.word	.L528-.L225,.L528-.L225
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_set_color')
	.sect	'.debug_loc'
.L368:
	.word	-1,.L199,0,.L365-.L199
	.half	1
	.byte	85
	.word	0,0
.L366:
	.word	-1,.L199,0,.L365-.L199
	.half	1
	.byte	84
	.word	0,0
.L198:
	.word	-1,.L199,0,.L365-.L199
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_set_dir')
	.sect	'.debug_loc'
.L361:
	.word	-1,.L195,0,.L359-.L195
	.half	1
	.byte	84
	.word	0,0
.L194:
	.word	-1,.L195,0,.L359-.L195
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_set_font')
	.sect	'.debug_loc'
.L364:
	.word	-1,.L197,0,.L362-.L197
	.half	1
	.byte	84
	.word	0,0
.L196:
	.word	-1,.L197,0,.L362-.L197
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_set_region')
	.sect	'.debug_loc'
.L186:
	.word	-1,.L187,0,.L532-.L187
	.half	2
	.byte	138,0
	.word	0,0
.L533:
	.word	-1,.L187,0,.L545-.L187
	.half	1
	.byte	84
	.word	.L548-.L187,.L545-.L187
	.half	1
	.byte	88
	.word	0,0
.L535:
	.word	-1,.L187,0,.L546-.L187
	.half	1
	.byte	86
	.word	.L551-.L187,.L552-.L187
	.half	1
	.byte	90
	.word	0,0
.L534:
	.word	-1,.L187,0,.L547-.L187
	.half	1
	.byte	85
	.word	.L549-.L187,.L550-.L187
	.half	1
	.byte	89
	.word	0,0
.L536:
	.word	-1,.L187,0,.L546-.L187
	.half	1
	.byte	87
	.word	.L553-.L187,.L554-.L187
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_binary_image')
	.sect	'.debug_loc'
.L465:
	.word	-1,.L215,.L758-.L215,.L759-.L215
	.half	1
	.byte	98
	.word	.L760-.L215,.L451-.L215
	.half	1
	.byte	109
	.word	.L111-.L215,.L761-.L215
	.half	1
	.byte	98
	.word	.L779-.L215,.L780-.L215
	.half	1
	.byte	100
	.word	.L782-.L215,.L783-.L215
	.half	1
	.byte	100
	.word	0,0
.L459:
	.word	-1,.L215,0,.L451-.L215
	.half	2
	.byte	145,4
	.word	.L754-.L215,.L451-.L215
	.half	1
	.byte	93
	.word	0,0
.L458:
	.word	-1,.L215,0,.L451-.L215
	.half	2
	.byte	145,0
	.word	.L753-.L215,.L451-.L215
	.half	1
	.byte	92
	.word	.L781-.L215,.L780-.L215
	.half	1
	.byte	85
	.word	0,0
.L457:
	.word	-1,.L215,0,.L748-.L215
	.half	1
	.byte	87
	.word	.L114-.L215,.L768-.L215
	.half	1
	.byte	91
	.word	0,0
.L461:
	.word	-1,.L215,.L772-.L215,.L773-.L215
	.half	1
	.byte	84
	.word	0,0
.L455:
	.word	-1,.L215,0,.L749-.L215
	.half	1
	.byte	100
	.word	.L752-.L215,.L451-.L215
	.half	1
	.byte	108
	.word	0,0
.L466:
	.word	-1,.L215,.L771-.L215,.L113-.L215
	.half	1
	.byte	111
	.word	0,0
.L462:
	.word	-1,.L215,.L767-.L215,.L451-.L215
	.half	1
	.byte	88
	.word	0,0
.L463:
	.word	-1,.L215,.L777-.L215,.L778-.L215
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L214:
	.word	-1,.L215,0,.L451-.L215
	.half	2
	.byte	138,0
	.word	0,0
.L456:
	.word	-1,.L215,0,.L748-.L215
	.half	1
	.byte	86
	.word	.L769-.L215,.L770-.L215
	.half	1
	.byte	90
	.word	.L116-.L215,.L774-.L215
	.half	1
	.byte	90
	.word	0,0
.L464:
	.word	-1,.L215,.L775-.L215,.L776-.L215
	.half	1
	.byte	82
	.word	0,0
.L452:
	.word	-1,.L215,0,.L750-.L215
	.half	1
	.byte	84
	.word	.L755-.L215,.L750-.L215
	.half	1
	.byte	88
	.word	.L112-.L215,.L762-.L215
	.half	1
	.byte	88
	.word	.L765-.L215,.L766-.L215
	.half	1
	.byte	88
	.word	0,0
.L453:
	.word	-1,.L215,0,.L751-.L215
	.half	1
	.byte	85
	.word	.L756-.L215,.L757-.L215
	.half	1
	.byte	89
	.word	.L763-.L215,.L764-.L215
	.half	1
	.byte	89
	.word	.L765-.L215,.L766-.L215
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_char')
	.sect	'.debug_loc'
.L392:
	.word	-1,.L205,0,.L649-.L205
	.half	1
	.byte	86
	.word	.L652-.L205,.L388-.L205
	.half	1
	.byte	90
	.word	0,0
.L399:
	.word	-1,.L205,0,.L388-.L205
	.half	3
	.byte	145,128,126
	.word	0,0
.L404:
	.word	-1,.L205,0,.L388-.L205
	.half	3
	.byte	145,128,126
	.word	0,0
.L395:
	.word	-1,.L205,.L661-.L205,.L397-.L205
	.half	5
	.byte	144,32,157,32,0
	.word	.L669-.L205,.L402-.L205
	.half	1
	.byte	83
	.word	0,0
.L396:
	.word	-1,.L205,.L663-.L205,.L73-.L205
	.half	1
	.byte	82
	.word	.L673-.L205,.L671-.L205
	.half	1
	.byte	81
	.word	.L671-.L205,.L80-.L205
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L407:
	.word	-1,.L205,.L672-.L205,.L80-.L205
	.half	1
	.byte	84
	.word	0,0
.L401:
	.word	-1,.L205,.L662-.L205,.L73-.L205
	.half	1
	.byte	81
	.word	0,0
.L406:
	.word	-1,.L205,.L670-.L205,.L671-.L205
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L204:
	.word	-1,.L205,0,.L648-.L205
	.half	2
	.byte	138,0
	.word	.L648-.L205,.L388-.L205
	.half	3
	.byte	138,128,2
	.word	.L388-.L205,.L388-.L205
	.half	2
	.byte	138,0
	.word	0,0
.L389:
	.word	-1,.L205,0,.L650-.L205
	.half	1
	.byte	84
	.word	.L653-.L205,.L650-.L205
	.half	1
	.byte	88
	.word	.L69-.L205,.L656-.L205
	.half	1
	.byte	88
	.word	.L659-.L205,.L660-.L205
	.half	1
	.byte	88
	.word	.L70-.L205,.L664-.L205
	.half	1
	.byte	88
	.word	.L667-.L205,.L668-.L205
	.half	1
	.byte	88
	.word	0,0
.L390:
	.word	-1,.L205,0,.L651-.L205
	.half	1
	.byte	85
	.word	.L654-.L205,.L655-.L205
	.half	1
	.byte	89
	.word	.L657-.L205,.L658-.L205
	.half	1
	.byte	89
	.word	.L659-.L205,.L660-.L205
	.half	1
	.byte	89
	.word	.L665-.L205,.L666-.L205
	.half	1
	.byte	89
	.word	.L667-.L205,.L668-.L205
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_chinese')
	.sect	'.debug_loc'
.L516:
	.word	-1,.L223,0,.L890-.L223
	.half	1
	.byte	100
	.word	.L895-.L223,.L512-.L223
	.half	1
	.byte	108
	.word	0,0
.L519:
	.word	-1,.L223,0,.L512-.L223
	.half	2
	.byte	145,0
	.word	.L897-.L223,.L512-.L223
	.half	1
	.byte	91
	.word	.L919-.L223,.L920-.L223
	.half	1
	.byte	85
	.word	0,0
.L521:
	.word	-1,.L223,.L910-.L223,.L512-.L223
	.half	1
	.byte	88
	.word	0,0
.L522:
	.word	-1,.L223,.L917-.L223,.L159-.L223
	.half	1
	.byte	94
	.word	0,0
.L523:
	.word	-1,.L223,.L916-.L223,.L157-.L223
	.half	1
	.byte	89
	.word	0,0
.L517:
	.word	-1,.L223,0,.L891-.L223
	.half	1
	.byte	87
	.word	.L896-.L223,.L512-.L223
	.half	2
	.byte	145,124
	.word	.L902-.L223,.L903-.L223
	.half	1
	.byte	95
	.word	.L911-.L223,.L912-.L223
	.half	1
	.byte	95
	.word	0,0
.L527:
	.word	-1,.L223,.L914-.L223,.L915-.L223
	.half	1
	.byte	111
	.word	.L921-.L223,.L922-.L223
	.half	5
	.byte	144,32,157,32,0
	.word	.L922-.L223,.L157-.L223
	.half	1
	.byte	95
	.word	.L157-.L223,.L155-.L223
	.half	1
	.byte	111
	.word	0,0
.L515:
	.word	-1,.L223,0,.L891-.L223
	.half	1
	.byte	86
	.word	.L894-.L223,.L512-.L223
	.half	1
	.byte	90
	.word	0,0
.L524:
	.word	-1,.L223,.L918-.L223,.L161-.L223
	.half	1
	.byte	95
	.word	0,0
.L525:
	.word	-1,.L223,.L913-.L223,.L155-.L223
	.half	2
	.byte	145,120
	.word	.L158-.L223,.L160-.L223
	.half	5
	.byte	144,32,157,32,0
	.word	.L923-.L223,.L924-.L223
	.half	1
	.byte	95
	.word	.L924-.L223,.L155-.L223
	.half	5
	.byte	144,32,157,32,0
	.word	.L925-.L223,.L926-.L223
	.half	1
	.byte	95
	.word	0,0
.L526:
	.word	-1,.L223,.L901-.L223,.L512-.L223
	.half	1
	.byte	92
	.word	0,0
.L222:
	.word	-1,.L223,0,.L889-.L223
	.half	2
	.byte	138,0
	.word	.L889-.L223,.L512-.L223
	.half	2
	.byte	138,8
	.word	.L512-.L223,.L512-.L223
	.half	2
	.byte	138,0
	.word	0,0
.L513:
	.word	-1,.L223,0,.L892-.L223
	.half	1
	.byte	84
	.word	.L898-.L223,.L892-.L223
	.half	1
	.byte	88
	.word	.L904-.L223,.L905-.L223
	.half	1
	.byte	88
	.word	.L908-.L223,.L909-.L223
	.half	1
	.byte	88
	.word	0,0
.L514:
	.word	-1,.L223,0,.L893-.L223
	.half	1
	.byte	85
	.word	.L899-.L223,.L900-.L223
	.half	1
	.byte	89
	.word	.L906-.L223,.L907-.L223
	.half	1
	.byte	89
	.word	.L908-.L223,.L909-.L223
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_float')
	.sect	'.debug_loc'
.L442:
	.word	-1,.L213,0,.L724-.L213
	.half	2
	.byte	144,35
	.word	.L735-.L213,.L736-.L213
	.half	2
	.byte	144,37
	.word	.L741-.L213,.L742-.L213
	.half	2
	.byte	144,37
	.word	0,0
.L447:
	.word	-1,.L213,.L743-.L213,.L744-.L213
	.half	2
	.byte	144,34
	.word	0,0
.L450:
	.word	-1,.L213,0,.L438-.L213
	.half	2
	.byte	145,104
	.word	0,0
.L443:
	.word	-1,.L213,0,.L438-.L213
	.half	2
	.byte	145,0
	.word	.L729-.L213,.L438-.L213
	.half	1
	.byte	89
	.word	0,0
.L448:
	.word	-1,.L213,.L732-.L213,.L110-.L213
	.half	2
	.byte	144,38
	.word	.L733-.L213,.L734-.L213
	.half	2
	.byte	144,38
	.word	.L737-.L213,.L738-.L213
	.half	2
	.byte	144,38
	.word	.L739-.L213,.L740-.L213
	.half	2
	.byte	144,38
	.word	0,0
.L444:
	.word	-1,.L213,0,.L438-.L213
	.half	2
	.byte	145,4
	.word	.L730-.L213,.L438-.L213
	.half	1
	.byte	88
	.word	.L743-.L213,.L744-.L213
	.half	1
	.byte	86
	.word	0,0
.L212:
	.word	-1,.L213,0,.L723-.L213
	.half	2
	.byte	138,0
	.word	.L723-.L213,.L438-.L213
	.half	2
	.byte	138,24
	.word	.L438-.L213,.L438-.L213
	.half	2
	.byte	138,0
	.word	0,0
.L439:
	.word	-1,.L213,0,.L725-.L213
	.half	1
	.byte	84
	.word	.L727-.L213,.L438-.L213
	.half	2
	.byte	145,124
	.word	.L725-.L213,.L731-.L213
	.half	1
	.byte	95
	.word	.L745-.L213,.L746-.L213
	.half	1
	.byte	84
	.word	0,0
.L440:
	.word	-1,.L213,0,.L726-.L213
	.half	1
	.byte	85
	.word	.L728-.L213,.L438-.L213
	.half	1
	.byte	94
	.word	.L747-.L213,.L746-.L213
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_gray_image')
	.sect	'.debug_loc'
.L479:
	.word	-1,.L217,.L814-.L217,.L127-.L217
	.half	1
	.byte	81
	.word	0,0
.L481:
	.word	-1,.L217,.L795-.L217,.L796-.L217
	.half	1
	.byte	98
	.word	.L797-.L217,.L467-.L217
	.half	1
	.byte	109
	.word	.L121-.L217,.L798-.L217
	.half	1
	.byte	98
	.word	.L815-.L217,.L810-.L217
	.half	1
	.byte	100
	.word	.L817-.L217,.L818-.L217
	.half	1
	.byte	100
	.word	0,0
.L474:
	.word	-1,.L217,0,.L467-.L217
	.half	2
	.byte	145,4
	.word	.L790-.L217,.L467-.L217
	.half	1
	.byte	93
	.word	0,0
.L473:
	.word	-1,.L217,0,.L467-.L217
	.half	2
	.byte	145,0
	.word	.L789-.L217,.L467-.L217
	.half	1
	.byte	92
	.word	.L816-.L217,.L810-.L217
	.half	1
	.byte	85
	.word	0,0
.L472:
	.word	-1,.L217,0,.L784-.L217
	.half	1
	.byte	87
	.word	.L124-.L217,.L805-.L217
	.half	1
	.byte	91
	.word	0,0
.L477:
	.word	-1,.L217,.L809-.L217,.L810-.L217
	.half	1
	.byte	82
	.word	0,0
.L470:
	.word	-1,.L217,0,.L785-.L217
	.half	1
	.byte	100
	.word	.L788-.L217,.L467-.L217
	.half	1
	.byte	108
	.word	0,0
.L482:
	.word	-1,.L217,.L808-.L217,.L123-.L217
	.half	1
	.byte	111
	.word	0,0
.L478:
	.word	-1,.L217,.L804-.L217,.L467-.L217
	.half	1
	.byte	88
	.word	0,0
.L480:
	.word	-1,.L217,.L812-.L217,.L813-.L217
	.half	5
	.byte	144,32,157,32,0
	.word	.L127-.L217,.L128-.L217
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L216:
	.word	-1,.L217,0,.L467-.L217
	.half	2
	.byte	138,0
	.word	0,0
.L475:
	.word	-1,.L217,0,.L467-.L217
	.half	2
	.byte	145,8
	.word	.L791-.L217,.L467-.L217
	.half	1
	.byte	94
	.word	0,0
.L471:
	.word	-1,.L217,0,.L784-.L217
	.half	1
	.byte	86
	.word	.L806-.L217,.L807-.L217
	.half	1
	.byte	90
	.word	.L126-.L217,.L811-.L217
	.half	1
	.byte	90
	.word	0,0
.L468:
	.word	-1,.L217,0,.L786-.L217
	.half	1
	.byte	84
	.word	.L792-.L217,.L786-.L217
	.half	1
	.byte	88
	.word	.L122-.L217,.L799-.L217
	.half	1
	.byte	88
	.word	.L802-.L217,.L803-.L217
	.half	1
	.byte	88
	.word	0,0
.L469:
	.word	-1,.L217,0,.L787-.L217
	.half	1
	.byte	85
	.word	.L793-.L217,.L794-.L217
	.half	1
	.byte	89
	.word	.L800-.L217,.L801-.L217
	.half	1
	.byte	89
	.word	.L802-.L217,.L803-.L217
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_int')
	.sect	'.debug_loc'
.L419:
	.word	-1,.L209,0,.L694-.L209
	.half	1
	.byte	86
	.word	.L697-.L209,.L415-.L209
	.half	1
	.byte	89
	.word	.L703-.L209,.L704-.L209
	.half	1
	.byte	84
	.word	0,0
.L423:
	.word	0,0
.L426:
	.word	-1,.L209,0,.L415-.L209
	.half	2
	.byte	145,112
	.word	0,0
.L420:
	.word	-1,.L209,0,.L694-.L209
	.half	1
	.byte	87
	.word	.L698-.L209,.L415-.L209
	.half	1
	.byte	92
	.word	0,0
.L424:
	.word	-1,.L209,.L702-.L209,.L415-.L209
	.half	1
	.byte	95
	.word	0,0
.L208:
	.word	-1,.L209,0,.L693-.L209
	.half	2
	.byte	138,0
	.word	.L693-.L209,.L415-.L209
	.half	2
	.byte	138,16
	.word	.L415-.L209,.L415-.L209
	.half	2
	.byte	138,0
	.word	0,0
.L416:
	.word	-1,.L209,0,.L695-.L209
	.half	1
	.byte	84
	.word	.L699-.L209,.L695-.L209
	.half	1
	.byte	90
	.word	.L705-.L209,.L706-.L209
	.half	1
	.byte	90
	.word	0,0
.L417:
	.word	-1,.L209,0,.L696-.L209
	.half	1
	.byte	85
	.word	.L700-.L209,.L701-.L209
	.half	1
	.byte	91
	.word	.L705-.L209,.L706-.L209
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_rgb565_image')
	.sect	'.debug_loc'
.L492:
	.word	-1,.L219,0,.L483-.L219
	.half	2
	.byte	145,8
	.word	.L826-.L219,.L483-.L219
	.half	1
	.byte	94
	.word	0,0
.L496:
	.word	-1,.L219,.L830-.L219,.L831-.L219
	.half	1
	.byte	98
	.word	.L832-.L219,.L483-.L219
	.half	1
	.byte	109
	.word	.L133-.L219,.L833-.L219
	.half	1
	.byte	98
	.word	.L848-.L219,.L845-.L219
	.half	1
	.byte	100
	.word	.L849-.L219,.L140-.L219
	.half	1
	.byte	100
	.word	.L851-.L219,.L852-.L219
	.half	1
	.byte	100
	.word	0,0
.L491:
	.word	-1,.L219,0,.L483-.L219
	.half	2
	.byte	145,4
	.word	.L825-.L219,.L483-.L219
	.half	1
	.byte	93
	.word	0,0
.L490:
	.word	-1,.L219,0,.L483-.L219
	.half	2
	.byte	145,0
	.word	.L824-.L219,.L483-.L219
	.half	1
	.byte	92
	.word	.L850-.L219,.L140-.L219
	.half	1
	.byte	85
	.word	0,0
.L489:
	.word	-1,.L219,0,.L819-.L219
	.half	1
	.byte	87
	.word	.L136-.L219,.L840-.L219
	.half	1
	.byte	91
	.word	0,0
.L494:
	.word	-1,.L219,.L844-.L219,.L845-.L219
	.half	5
	.byte	144,32,157,32,0
	.word	.L139-.L219,.L140-.L219
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L487:
	.word	-1,.L219,0,.L820-.L219
	.half	1
	.byte	100
	.word	.L823-.L219,.L483-.L219
	.half	1
	.byte	108
	.word	0,0
.L497:
	.word	-1,.L219,.L843-.L219,.L135-.L219
	.half	1
	.byte	111
	.word	0,0
.L495:
	.word	-1,.L219,.L839-.L219,.L483-.L219
	.half	1
	.byte	88
	.word	0,0
.L218:
	.word	-1,.L219,0,.L483-.L219
	.half	2
	.byte	138,0
	.word	0,0
.L488:
	.word	-1,.L219,0,.L819-.L219
	.half	1
	.byte	86
	.word	.L841-.L219,.L842-.L219
	.half	1
	.byte	90
	.word	.L846-.L219,.L847-.L219
	.half	1
	.byte	90
	.word	0,0
.L484:
	.word	-1,.L219,0,.L821-.L219
	.half	1
	.byte	84
	.word	.L827-.L219,.L821-.L219
	.half	1
	.byte	88
	.word	.L134-.L219,.L834-.L219
	.half	1
	.byte	88
	.word	.L837-.L219,.L838-.L219
	.half	1
	.byte	88
	.word	0,0
.L485:
	.word	-1,.L219,0,.L822-.L219
	.half	1
	.byte	85
	.word	.L828-.L219,.L829-.L219
	.half	1
	.byte	89
	.word	.L835-.L219,.L836-.L219
	.half	1
	.byte	89
	.word	.L837-.L219,.L838-.L219
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_string')
	.sect	'.debug_loc'
.L412:
	.word	-1,.L207,0,.L674-.L207
	.half	1
	.byte	100
	.word	.L677-.L207,.L408-.L207
	.half	1
	.byte	108
	.word	0,0
.L414:
	.word	-1,.L207,.L681-.L207,.L682-.L207
	.half	1
	.byte	90
	.word	.L94-.L207,.L408-.L207
	.half	1
	.byte	90
	.word	0,0
.L206:
	.word	-1,.L207,0,.L408-.L207
	.half	2
	.byte	138,0
	.word	0,0
.L409:
	.word	-1,.L207,0,.L675-.L207
	.half	1
	.byte	84
	.word	.L678-.L207,.L675-.L207
	.half	1
	.byte	88
	.word	.L683-.L207,.L684-.L207
	.half	1
	.byte	88
	.word	.L688-.L207,.L689-.L207
	.half	1
	.byte	88
	.word	0,0
.L410:
	.word	-1,.L207,0,.L676-.L207
	.half	1
	.byte	85
	.word	.L679-.L207,.L680-.L207
	.half	1
	.byte	89
	.word	.L685-.L207,.L686-.L207
	.half	1
	.byte	89
	.word	.L686-.L207,.L687-.L207
	.half	1
	.byte	85
	.word	.L690-.L207,.L691-.L207
	.half	1
	.byte	89
	.word	.L691-.L207,.L692-.L207
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_uint')
	.sect	'.debug_loc'
.L431:
	.word	-1,.L211,0,.L708-.L211
	.half	1
	.byte	86
	.word	.L711-.L211,.L427-.L211
	.half	1
	.byte	89
	.word	.L719-.L211,.L720-.L211
	.half	1
	.byte	84
	.word	0,0
.L435:
	.word	0,0
.L437:
	.word	-1,.L211,0,.L427-.L211
	.half	2
	.byte	145,112
	.word	0,0
.L432:
	.word	-1,.L211,0,.L708-.L211
	.half	1
	.byte	87
	.word	.L712-.L211,.L427-.L211
	.half	1
	.byte	92
	.word	.L717-.L211,.L718-.L211
	.half	1
	.byte	85
	.word	0,0
.L436:
	.word	-1,.L211,.L716-.L211,.L427-.L211
	.half	1
	.byte	95
	.word	0,0
.L210:
	.word	-1,.L211,0,.L707-.L211
	.half	2
	.byte	138,0
	.word	.L707-.L211,.L427-.L211
	.half	2
	.byte	138,16
	.word	.L427-.L211,.L427-.L211
	.half	2
	.byte	138,0
	.word	0,0
.L428:
	.word	-1,.L211,0,.L709-.L211
	.half	1
	.byte	84
	.word	.L713-.L211,.L709-.L211
	.half	1
	.byte	90
	.word	.L721-.L211,.L722-.L211
	.half	1
	.byte	90
	.word	0,0
.L429:
	.word	-1,.L211,0,.L710-.L211
	.half	1
	.byte	85
	.word	.L714-.L211,.L715-.L211
	.half	1
	.byte	91
	.word	.L721-.L211,.L722-.L211
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_show_wave')
	.sect	'.debug_loc'
.L511:
	.word	-1,.L221,.L863-.L221,.L864-.L221
	.half	1
	.byte	98
	.word	.L865-.L221,.L498-.L221
	.half	1
	.byte	109
	.word	.L143-.L221,.L866-.L221
	.half	1
	.byte	98
	.word	.L876-.L221,.L875-.L221
	.half	1
	.byte	100
	.word	.L887-.L221,.L888-.L221
	.half	1
	.byte	100
	.word	0,0
.L505:
	.word	-1,.L221,0,.L498-.L221
	.half	2
	.byte	145,4
	.word	.L859-.L221,.L498-.L221
	.half	1
	.byte	93
	.word	0,0
.L504:
	.word	-1,.L221,0,.L498-.L221
	.half	2
	.byte	145,0
	.word	.L858-.L221,.L498-.L221
	.half	1
	.byte	92
	.word	.L877-.L221,.L875-.L221
	.half	1
	.byte	85
	.word	0,0
.L507:
	.word	-1,.L221,.L874-.L221,.L875-.L221
	.half	5
	.byte	144,32,157,32,0
	.word	.L873-.L221,.L498-.L221
	.half	1
	.byte	94
	.word	0,0
.L508:
	.word	-1,.L221,.L872-.L221,.L873-.L221
	.half	1
	.byte	94
	.word	0,0
.L220:
	.word	-1,.L221,0,.L498-.L221
	.half	2
	.byte	138,0
	.word	0,0
.L503:
	.word	-1,.L221,0,.L853-.L221
	.half	1
	.byte	87
	.word	.L881-.L221,.L882-.L221
	.half	1
	.byte	91
	.word	0,0
.L510:
	.word	-1,.L221,.L882-.L221,.L883-.L221
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L501:
	.word	-1,.L221,0,.L854-.L221
	.half	1
	.byte	100
	.word	.L857-.L221,.L498-.L221
	.half	1
	.byte	108
	.word	0,0
.L502:
	.word	-1,.L221,0,.L853-.L221
	.half	1
	.byte	86
	.word	.L152-.L221,.L878-.L221
	.half	1
	.byte	90
	.word	0,0
.L509:
	.word	-1,.L221,.L879-.L221,.L880-.L221
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L499:
	.word	-1,.L221,0,.L855-.L221
	.half	1
	.byte	84
	.word	.L860-.L221,.L855-.L221
	.half	1
	.byte	88
	.word	.L144-.L221,.L867-.L221
	.half	1
	.byte	88
	.word	.L870-.L221,.L871-.L221
	.half	1
	.byte	88
	.word	.L882-.L221,.L884-.L221
	.half	1
	.byte	88
	.word	0,0
.L500:
	.word	-1,.L221,0,.L856-.L221
	.half	1
	.byte	85
	.word	.L861-.L221,.L862-.L221
	.half	1
	.byte	89
	.word	.L868-.L221,.L869-.L221
	.half	1
	.byte	89
	.word	.L870-.L221,.L871-.L221
	.half	1
	.byte	89
	.word	.L885-.L221,.L886-.L221
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('tft180_write_index')
	.sect	'.debug_loc'
.L531:
	.word	-1,.L185,0,.L540-.L185
	.half	1
	.byte	84
	.word	.L541-.L185,.L529-.L185
	.half	1
	.byte	88
	.word	.L2-.L185,.L542-.L185
	.half	1
	.byte	84
	.word	.L543-.L185,.L544-.L185
	.half	1
	.byte	85
	.word	0,0
.L184:
	.word	-1,.L185,0,.L529-.L185
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1432:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('tft180_write_index')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L185,.L529-.L185
	.sdecl	'.debug_frame',debug,cluster('tft180_set_region')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L187,.L532-.L187
	.sdecl	'.debug_frame',debug,cluster('tft180_debug_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L189,.L537-.L189
	.byte	4
	.word	(.L555-.L189)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L537-.L555)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('tft180_clear')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L191,.L347-.L191
	.sdecl	'.debug_frame',debug,cluster('tft180_full')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L193,.L353-.L193
	.sdecl	'.debug_frame',debug,cluster('tft180_set_dir')
	.sect	'.debug_frame'
	.word	24
	.word	.L1432,.L195,.L359-.L195
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('tft180_set_font')
	.sect	'.debug_frame'
	.word	24
	.word	.L1432,.L197,.L362-.L197
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('tft180_set_color')
	.sect	'.debug_frame'
	.word	24
	.word	.L1432,.L199,.L365-.L199
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('tft180_draw_point')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L201,.L369-.L201
	.sdecl	'.debug_frame',debug,cluster('tft180_draw_line')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L203,.L374-.L203
	.byte	4
	.word	(.L594-.L203)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L374-.L594)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('tft180_show_char')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L205,.L388-.L205
	.byte	4
	.word	(.L648-.L205)/2
	.byte	19,128,2,22,26,4,19,138,128,2,4
	.word	(.L388-.L648)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('tft180_show_string')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L207,.L408-.L207
	.sdecl	'.debug_frame',debug,cluster('tft180_show_int')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L209,.L415-.L209
	.byte	4
	.word	(.L693-.L209)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L415-.L693)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('tft180_show_uint')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L211,.L427-.L211
	.byte	4
	.word	(.L707-.L211)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L427-.L707)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('tft180_show_float')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L213,.L438-.L213
	.byte	4
	.word	(.L723-.L213)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L438-.L723)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('tft180_show_binary_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L215,.L451-.L215
	.sdecl	'.debug_frame',debug,cluster('tft180_show_gray_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L217,.L467-.L217
	.sdecl	'.debug_frame',debug,cluster('tft180_show_rgb565_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L219,.L483-.L219
	.sdecl	'.debug_frame',debug,cluster('tft180_show_wave')
	.sect	'.debug_frame'
	.word	12
	.word	.L1432,.L221,.L498-.L221
	.sdecl	'.debug_frame',debug,cluster('tft180_show_chinese')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L223,.L512-.L223
	.byte	4
	.word	(.L889-.L223)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L512-.L889)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('tft180_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1432,.L225,.L528-.L225
	.byte	4
	.word	(.L927-.L225)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L528-.L927)/2
	.byte	19,0,8,26,0,0
	; Module end
