	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc35456a --dep-file=IfxEray_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDA0_P14_8_IN',data,rom,cluster('IfxEray0_RXDA0_P14_8_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDA0_P14_8_IN'
	.global	IfxEray0_RXDA0_P14_8_IN
	.align	4
IfxEray0_RXDA0_P14_8_IN:	.type	object
	.size	IfxEray0_RXDA0_P14_8_IN,20
	.word	-268320768
	.space	4
	.word	-268192768
	.byte	8
	.space	7
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDA1_P11_9_IN',data,rom,cluster('IfxEray0_RXDA1_P11_9_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDA1_P11_9_IN'
	.global	IfxEray0_RXDA1_P11_9_IN
	.align	4
IfxEray0_RXDA1_P11_9_IN:	.type	object
	.size	IfxEray0_RXDA1_P11_9_IN,20
	.word	-268320768
	.space	4
	.word	-268193536
	.byte	9
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDA2_P02_1_IN',data,rom,cluster('IfxEray0_RXDA2_P02_1_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDA2_P02_1_IN'
	.global	IfxEray0_RXDA2_P02_1_IN
	.align	4
IfxEray0_RXDA2_P02_1_IN:	.type	object
	.size	IfxEray0_RXDA2_P02_1_IN,20
	.word	-268320768
	.space	4
	.word	-268197376
	.byte	1
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDA3_P14_1_IN',data,rom,cluster('IfxEray0_RXDA3_P14_1_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDA3_P14_1_IN'
	.global	IfxEray0_RXDA3_P14_1_IN
	.align	4
IfxEray0_RXDA3_P14_1_IN:	.type	object
	.size	IfxEray0_RXDA3_P14_1_IN,20
	.word	-268320768
	.space	4
	.word	-268192768
	.byte	1
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDB0_P14_7_IN',data,rom,cluster('IfxEray0_RXDB0_P14_7_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDB0_P14_7_IN'
	.global	IfxEray0_RXDB0_P14_7_IN
	.align	4
IfxEray0_RXDB0_P14_7_IN:	.type	object
	.size	IfxEray0_RXDB0_P14_7_IN,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268192768
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDB1_P11_10_IN',data,rom,cluster('IfxEray0_RXDB1_P11_10_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDB1_P11_10_IN'
	.global	IfxEray0_RXDB1_P11_10_IN
	.align	4
IfxEray0_RXDB1_P11_10_IN:	.type	object
	.size	IfxEray0_RXDB1_P11_10_IN,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268193536
	.byte	10
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDB2_P02_3_IN',data,rom,cluster('IfxEray0_RXDB2_P02_3_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDB2_P02_3_IN'
	.global	IfxEray0_RXDB2_P02_3_IN
	.align	4
IfxEray0_RXDB2_P02_3_IN:	.type	object
	.size	IfxEray0_RXDB2_P02_3_IN,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268197376
	.byte	3
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_RXDB3_P14_1_IN',data,rom,cluster('IfxEray0_RXDB3_P14_1_IN')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_RXDB3_P14_1_IN'
	.global	IfxEray0_RXDB3_P14_1_IN
	.align	4
IfxEray0_RXDB3_P14_1_IN:	.type	object
	.size	IfxEray0_RXDB3_P14_1_IN,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268192768
	.byte	1
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P02_0_OUT',data,rom,cluster('IfxEray0_TXDA_P02_0_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P02_0_OUT'
	.global	IfxEray0_TXDA_P02_0_OUT
	.align	4
IfxEray0_TXDA_P02_0_OUT:	.type	object
	.size	IfxEray0_TXDA_P02_0_OUT,20
	.word	-268320768
	.space	4
	.word	-268197376
	.space	4
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P11_3_OUT',data,rom,cluster('IfxEray0_TXDA_P11_3_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P11_3_OUT'
	.global	IfxEray0_TXDA_P11_3_OUT
	.align	4
IfxEray0_TXDA_P11_3_OUT:	.type	object
	.size	IfxEray0_TXDA_P11_3_OUT,20
	.word	-268320768
	.space	4
	.word	-268193536
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P14_0_OUT',data,rom,cluster('IfxEray0_TXDA_P14_0_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P14_0_OUT'
	.global	IfxEray0_TXDA_P14_0_OUT
	.align	4
IfxEray0_TXDA_P14_0_OUT:	.type	object
	.size	IfxEray0_TXDA_P14_0_OUT,20
	.word	-268320768
	.space	4
	.word	-268192768
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P14_10_OUT',data,rom,cluster('IfxEray0_TXDA_P14_10_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDA_P14_10_OUT'
	.global	IfxEray0_TXDA_P14_10_OUT
	.align	4
IfxEray0_TXDA_P14_10_OUT:	.type	object
	.size	IfxEray0_TXDA_P14_10_OUT,20
	.word	-268320768
	.space	4
	.word	-268192768
	.byte	10
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P02_2_OUT',data,rom,cluster('IfxEray0_TXDB_P02_2_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P02_2_OUT'
	.global	IfxEray0_TXDB_P02_2_OUT
	.align	4
IfxEray0_TXDB_P02_2_OUT:	.type	object
	.size	IfxEray0_TXDB_P02_2_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268197376
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P11_12_OUT',data,rom,cluster('IfxEray0_TXDB_P11_12_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P11_12_OUT'
	.global	IfxEray0_TXDB_P11_12_OUT
	.align	4
IfxEray0_TXDB_P11_12_OUT:	.type	object
	.size	IfxEray0_TXDB_P11_12_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268193536
	.byte	12
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P14_0_OUT',data,rom,cluster('IfxEray0_TXDB_P14_0_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P14_0_OUT'
	.global	IfxEray0_TXDB_P14_0_OUT
	.align	4
IfxEray0_TXDB_P14_0_OUT:	.type	object
	.size	IfxEray0_TXDB_P14_0_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268192768
	.space	4
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P14_5_OUT',data,rom,cluster('IfxEray0_TXDB_P14_5_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXDB_P14_5_OUT'
	.global	IfxEray0_TXDB_P14_5_OUT
	.align	4
IfxEray0_TXDB_P14_5_OUT:	.type	object
	.size	IfxEray0_TXDB_P14_5_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268192768
	.byte	5
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENA_P02_4_OUT',data,rom,cluster('IfxEray0_TXENA_P02_4_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENA_P02_4_OUT'
	.global	IfxEray0_TXENA_P02_4_OUT
	.align	4
IfxEray0_TXENA_P02_4_OUT:	.type	object
	.size	IfxEray0_TXENA_P02_4_OUT,20
	.word	-268320768
	.space	4
	.word	-268197376
	.byte	4
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENA_P11_6_OUT',data,rom,cluster('IfxEray0_TXENA_P11_6_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENA_P11_6_OUT'
	.global	IfxEray0_TXENA_P11_6_OUT
	.align	4
IfxEray0_TXENA_P11_6_OUT:	.type	object
	.size	IfxEray0_TXENA_P11_6_OUT,20
	.word	-268320768
	.space	4
	.word	-268193536
	.byte	6
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENA_P14_9_OUT',data,rom,cluster('IfxEray0_TXENA_P14_9_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENA_P14_9_OUT'
	.global	IfxEray0_TXENA_P14_9_OUT
	.align	4
IfxEray0_TXENA_P14_9_OUT:	.type	object
	.size	IfxEray0_TXENA_P14_9_OUT,20
	.word	-268320768
	.space	4
	.word	-268192768
	.byte	9
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P02_5_OUT',data,rom,cluster('IfxEray0_TXENB_P02_5_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P02_5_OUT'
	.global	IfxEray0_TXENB_P02_5_OUT
	.align	4
IfxEray0_TXENB_P02_5_OUT:	.type	object
	.size	IfxEray0_TXENB_P02_5_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268197376
	.byte	5
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P11_11_OUT',data,rom,cluster('IfxEray0_TXENB_P11_11_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P11_11_OUT'
	.global	IfxEray0_TXENB_P11_11_OUT
	.align	4
IfxEray0_TXENB_P11_11_OUT:	.type	object
	.size	IfxEray0_TXENB_P11_11_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268193536
	.byte	11
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P11_6_OUT',data,rom,cluster('IfxEray0_TXENB_P11_6_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P11_6_OUT'
	.global	IfxEray0_TXENB_P11_6_OUT
	.align	4
IfxEray0_TXENB_P11_6_OUT:	.type	object
	.size	IfxEray0_TXENB_P11_6_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268193536
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P14_6_OUT',data,rom,cluster('IfxEray0_TXENB_P14_6_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P14_6_OUT'
	.global	IfxEray0_TXENB_P14_6_OUT
	.align	4
IfxEray0_TXENB_P14_6_OUT:	.type	object
	.size	IfxEray0_TXENB_P14_6_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268192768
	.byte	6
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P14_9_OUT',data,rom,cluster('IfxEray0_TXENB_P14_9_OUT')
	.sect	'.rodata.IfxEray_PinMap.IfxEray0_TXENB_P14_9_OUT'
	.global	IfxEray0_TXENB_P14_9_OUT
	.align	4
IfxEray0_TXENB_P14_9_OUT:	.type	object
	.size	IfxEray0_TXENB_P14_9_OUT,20
	.word	-268320768
	.byte	1
	.space	3
	.word	-268192768
	.byte	9
	.space	3
	.byte	168
	.space	3
	.sdecl	'.data.IfxEray_PinMap.IfxEray_Rxd_In_pinTable',data,cluster('IfxEray_Rxd_In_pinTable')
	.sect	'.data.IfxEray_PinMap.IfxEray_Rxd_In_pinTable'
	.global	IfxEray_Rxd_In_pinTable
	.align	4
IfxEray_Rxd_In_pinTable:	.type	object
	.size	IfxEray_Rxd_In_pinTable,32
	.word	IfxEray0_RXDA0_P14_8_IN,IfxEray0_RXDA1_P11_9_IN,IfxEray0_RXDA2_P02_1_IN,IfxEray0_RXDA3_P14_1_IN,IfxEray0_RXDB0_P14_7_IN,IfxEray0_RXDB1_P11_10_IN,IfxEray0_RXDB2_P02_3_IN,IfxEray0_RXDB3_P14_1_IN
	.sdecl	'.data.IfxEray_PinMap.IfxEray_Txd_Out_pinTable',data,cluster('IfxEray_Txd_Out_pinTable')
	.sect	'.data.IfxEray_PinMap.IfxEray_Txd_Out_pinTable'
	.global	IfxEray_Txd_Out_pinTable
	.align	4
IfxEray_Txd_Out_pinTable:	.type	object
	.size	IfxEray_Txd_Out_pinTable,32
	.word	IfxEray0_TXDA_P02_0_OUT,IfxEray0_TXDA_P11_3_OUT,IfxEray0_TXDA_P14_0_OUT,IfxEray0_TXDA_P14_10_OUT,IfxEray0_TXDB_P02_2_OUT,IfxEray0_TXDB_P11_12_OUT,IfxEray0_TXDB_P14_0_OUT,IfxEray0_TXDB_P14_5_OUT
	.sdecl	'.data.IfxEray_PinMap.IfxEray_Txen_Out_pinTable',data,cluster('IfxEray_Txen_Out_pinTable')
	.sect	'.data.IfxEray_PinMap.IfxEray_Txen_Out_pinTable'
	.global	IfxEray_Txen_Out_pinTable
	.align	4
IfxEray_Txen_Out_pinTable:	.type	object
	.size	IfxEray_Txen_Out_pinTable,40
	.word	IfxEray0_TXENA_P02_4_OUT,IfxEray0_TXENA_P11_6_OUT,IfxEray0_TXENA_P14_9_OUT
	.space	8
	.word	IfxEray0_TXENB_P02_5_OUT,IfxEray0_TXENB_P11_6_OUT,IfxEray0_TXENB_P11_11_OUT,IfxEray0_TXENB_P14_6_OUT
	.word	IfxEray0_TXENB_P14_9_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	73311
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	238
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	241
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	286
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	298
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	352
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	384
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	384
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	352
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1419
	.byte	4,2,35,0,0,14,4
	.word	493
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1977
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2414
	.byte	4,2,35,0,0,14,24
	.word	493
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3041
	.byte	4,2,35,0,0,14,8
	.word	493
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3366
	.byte	4,2,35,0,0,14,12
	.word	493
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3706
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5369
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6382
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6506
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6770
	.byte	4,2,35,0,0,14,76
	.word	493
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	808
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1379
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1498
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1538
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1722
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1937
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2154
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2374
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1538
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2688
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2728
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3001
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3317
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3357
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3657
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3697
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4032
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4318
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3357
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4465
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4634
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4806
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4981
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5155
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5329
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5505
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5661
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5994
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6342
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3357
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6466
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6715
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6974
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7014
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7070
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7637
	.byte	4,3,35,252,1,0,16
	.word	7677
	.byte	3
	.word	8280
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8285
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	493
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8290
	.byte	6,0,19
	.word	246
	.byte	20
	.word	272
	.byte	6,0,19
	.word	307
	.byte	20
	.word	339
	.byte	6,0,19
	.word	389
	.byte	20
	.word	408
	.byte	6,0,19
	.word	424
	.byte	20
	.word	439
	.byte	20
	.word	453
	.byte	6,0,19
	.word	8393
	.byte	20
	.word	8421
	.byte	20
	.word	8435
	.byte	20
	.word	8453
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8546
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	470
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	486
	.byte	22,1,3
	.word	8614
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8616
	.byte	10
	.byte	'_Ifx_ERAY_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_ACCEN0_Bits',0,6,79,3
	.word	8639
	.byte	10
	.byte	'_Ifx_ERAY_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ERAY_ACCEN1_Bits',0,6,85,3
	.word	9198
	.byte	10
	.byte	'_Ifx_ERAY_ACS_Bits',0,6,88,16,4,11
	.byte	'VFRA',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEDA',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CEDA',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'CIA',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SBVA',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'VFRB',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SEDB',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CEDB',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CIB',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SBVB',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	470
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_ERAY_ACS_Bits',0,6,102,3
	.word	9277
	.byte	10
	.byte	'_Ifx_ERAY_CCEV_Bits',0,6,105,16,4,11
	.byte	'CCFC',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'ERRM',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PTAC',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	470
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_ERAY_CCEV_Bits',0,6,112,3
	.word	9531
	.byte	10
	.byte	'_Ifx_ERAY_CCSV_Bits',0,6,115,16,4,11
	.byte	'POCS',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'FSI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'HRQ',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SLM',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'CSNI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSAI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSI',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'WSV',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'RCA',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PSL',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_CCSV_Bits',0,6,130,1,3
	.word	9677
	.byte	10
	.byte	'_Ifx_ERAY_CLC_Bits',0,6,133,1,16,4,11
	.byte	'DISR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'RMC',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_ERAY_CLC_Bits',0,6,142,1,3
	.word	9953
	.byte	10
	.byte	'_Ifx_ERAY_CREL_Bits',0,6,145,1,16,4,11
	.byte	'DAY',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MON',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'YEAR',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'SUBSTEP',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'STEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'REL',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_ERAY_CREL_Bits',0,6,153,1,3
	.word	10136
	.byte	10
	.byte	'_Ifx_ERAY_CUST1_Bits',0,6,156,1,16,4,11
	.byte	'INT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IBFS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IBF1PAG',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'IBF2PAG',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'RISA',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'RISB',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STPWTS',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ERAY_CUST1_Bits',0,6,170,1,3
	.word	10287
	.byte	10
	.byte	'_Ifx_ERAY_CUST3_Bits',0,6,173,1,16,4,11
	.byte	'TO',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ERAY_CUST3_Bits',0,6,176,1,3
	.word	10561
	.byte	10
	.byte	'_Ifx_ERAY_EIER_Bits',0,6,179,1,16,4,11
	.byte	'PEMCE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CNAE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SFBME',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SFOE',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CCFE',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CCLE',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EERRE',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RFOE',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EFAE',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'IIBAE',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'IOBAE',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MHFE',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'EDAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LTVAE',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TABAE',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'EDBE',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'LTVBE',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TABBE',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_EIER_Bits',0,6,202,1,3
	.word	10632
	.byte	10
	.byte	'_Ifx_ERAY_EIES_Bits',0,6,205,1,16,4,11
	.byte	'PEMCE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CNAE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SFBME',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SFOE',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CCFE',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CCLE',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EERRE',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RFOE',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EFAE',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'IIBAE',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'IOBAE',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MHFE',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'EDAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LTVAE',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TABAE',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'EDBE',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'LTVBE',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TABBE',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_EIES_Bits',0,6,228,1,3
	.word	11053
	.byte	10
	.byte	'_Ifx_ERAY_EILS_Bits',0,6,231,1,16,4,11
	.byte	'PEMCL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CNAL',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SFBML',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SFOL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CCFL',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CCLL',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EERRL',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RFOL',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EFAL',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'IIBAL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'IOBAL',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MHFL',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'EDAL',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LTVAL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TABAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'EDBL',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'LTVBL',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TABBL',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_EILS_Bits',0,6,254,1,3
	.word	11474
	.byte	10
	.byte	'_Ifx_ERAY_EIR_Bits',0,6,129,2,16,4,11
	.byte	'PEMC',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CNA',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SFBM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SFO',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CCF',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CCL',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EERR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'RFO',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EFA',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'IIBA',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'IOBA',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MHF',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'EDA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LTVA',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TABA',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'EDB',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'LTVB',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TABB',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_EIR_Bits',0,6,152,2,3
	.word	11895
	.byte	10
	.byte	'_Ifx_ERAY_ENDN_Bits',0,6,155,2,16,4,11
	.byte	'ETV',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ERAY_ENDN_Bits',0,6,158,2,3
	.word	12296
	.byte	10
	.byte	'_Ifx_ERAY_ESID_Bits',0,6,161,2,16,4,11
	.byte	'EID',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RXEA',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RXEB',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ERAY_ESID_Bits',0,6,168,2,3
	.word	12366
	.byte	10
	.byte	'_Ifx_ERAY_FCL_Bits',0,6,171,2,16,4,11
	.byte	'CL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_ERAY_FCL_Bits',0,6,175,2,3
	.word	12514
	.byte	10
	.byte	'_Ifx_ERAY_FRF_Bits',0,6,178,2,16,4,11
	.byte	'CH',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'FID',0,2
	.word	510
	.byte	11,3,2,35,0,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'CYF',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'RSS',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RNF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_ERAY_FRF_Bits',0,6,187,2,3
	.word	12603
	.byte	10
	.byte	'_Ifx_ERAY_FRFM_Bits',0,6,190,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'MFID',0,2
	.word	510
	.byte	11,3,2,35,0,11
	.byte	'reserved_13',0,4
	.word	470
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_ERAY_FRFM_Bits',0,6,195,2,3
	.word	12776
	.byte	10
	.byte	'_Ifx_ERAY_FSR_Bits',0,6,198,2,16,4,11
	.byte	'RFNE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RFCL',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RFO',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'RFFL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ERAY_FSR_Bits',0,6,206,2,3
	.word	12892
	.byte	10
	.byte	'_Ifx_ERAY_GTUC01_Bits',0,6,209,2,16,4,11
	.byte	'UT',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_ERAY_GTUC01_Bits',0,6,213,2,3
	.word	13053
	.byte	10
	.byte	'_Ifx_ERAY_GTUC02_Bits',0,6,216,2,16,4,11
	.byte	'MPC',0,2
	.word	510
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SNM',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_ERAY_GTUC02_Bits',0,6,222,2,3
	.word	13149
	.byte	10
	.byte	'_Ifx_ERAY_GTUC03_Bits',0,6,225,2,16,4,11
	.byte	'UIOA',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'UIOB',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MIOA',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MIOB',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC03_Bits',0,6,233,2,3
	.word	13284
	.byte	10
	.byte	'_Ifx_ERAY_GTUC04_Bits',0,6,236,2,16,4,11
	.byte	'NIT',0,2
	.word	510
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'OCS',0,2
	.word	510
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC04_Bits',0,6,242,2,3
	.word	13453
	.byte	10
	.byte	'_Ifx_ERAY_GTUC05_Bits',0,6,245,2,16,4,11
	.byte	'DCA',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'DCB',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CDD',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'DEC',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC05_Bits',0,6,252,2,3
	.word	13588
	.byte	10
	.byte	'_Ifx_ERAY_GTUC06_Bits',0,6,255,2,16,4,11
	.byte	'ASR',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'MOD',0,2
	.word	510
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC06_Bits',0,6,133,3,3
	.word	13730
	.byte	10
	.byte	'_Ifx_ERAY_GTUC07_Bits',0,6,136,3,16,4,11
	.byte	'SSL',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'NSS',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC07_Bits',0,6,142,3,3
	.word	13865
	.byte	10
	.byte	'_Ifx_ERAY_GTUC08_Bits',0,6,145,3,16,4,11
	.byte	'MSL',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	510
	.byte	10,0,2,35,0,11
	.byte	'NMS',0,2
	.word	510
	.byte	13,3,2,35,2,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC08_Bits',0,6,151,3,3
	.word	14000
	.byte	10
	.byte	'_Ifx_ERAY_GTUC09_Bits',0,6,154,3,16,4,11
	.byte	'APO',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'MAPO',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'DSI',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_ERAY_GTUC09_Bits',0,6,162,3,3
	.word	14134
	.byte	10
	.byte	'_Ifx_ERAY_GTUC10_Bits',0,6,165,3,16,4,11
	.byte	'MOC',0,2
	.word	510
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'MRC',0,2
	.word	510
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC10_Bits',0,6,171,3,3
	.word	14307
	.byte	10
	.byte	'_Ifx_ERAY_GTUC11_Bits',0,6,174,3,16,4,11
	.byte	'EOCC',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'ERCC',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'EOC',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'ERC',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_GTUC11_Bits',0,6,184,3,3
	.word	14442
	.byte	10
	.byte	'_Ifx_ERAY_IBCM_Bits',0,6,187,3,16,4,11
	.byte	'LHSH',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LDSH',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STXRH',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	510
	.byte	13,0,2,35,0,11
	.byte	'LHSS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LDSS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'STXRS',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,2
	.word	510
	.byte	13,0,2,35,2,0,21
	.byte	'Ifx_ERAY_IBCM_Bits',0,6,197,3,3
	.word	14654
	.byte	10
	.byte	'_Ifx_ERAY_IBCR_Bits',0,6,200,3,16,4,11
	.byte	'IBRH',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	8,1,2,35,0,11
	.byte	'IBSYH',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'IBRS',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	8,1,2,35,2,11
	.byte	'IBSYS',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_IBCR_Bits',0,6,208,3,3
	.word	14852
	.byte	10
	.byte	'_Ifx_ERAY_ID_Bits',0,6,211,3,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ERAY_ID_Bits',0,6,216,3,3
	.word	15018
	.byte	10
	.byte	'_Ifx_ERAY_ILE_Bits',0,6,219,3,16,4,11
	.byte	'EINT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EINT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ERAY_ILE_Bits',0,6,224,3,3
	.word	15127
	.byte	10
	.byte	'_Ifx_ERAY_KRST0_Bits',0,6,227,3,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ERAY_KRST0_Bits',0,6,232,3,3
	.word	15236
	.byte	10
	.byte	'_Ifx_ERAY_KRST1_Bits',0,6,235,3,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_ERAY_KRST1_Bits',0,6,239,3,3
	.word	15349
	.byte	10
	.byte	'_Ifx_ERAY_KRSTCLR_Bits',0,6,242,3,16,4,11
	.byte	'CLR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_ERAY_KRSTCLR_Bits',0,6,246,3,3
	.word	15443
	.byte	10
	.byte	'_Ifx_ERAY_LCK_Bits',0,6,249,3,16,4,11
	.byte	'CLK',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'TMK',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ERAY_LCK_Bits',0,6,254,3,3
	.word	15541
	.byte	10
	.byte	'_Ifx_ERAY_LDTS_Bits',0,6,129,4,16,4,11
	.byte	'LDTA',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'LDTB',0,2
	.word	510
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_LDTS_Bits',0,6,135,4,3
	.word	15647
	.byte	10
	.byte	'_Ifx_ERAY_MBS_Bits',0,6,138,4,16,4,11
	.byte	'VFRA',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VFRB',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEOA',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEOB',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'CEOA',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CEOB',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SVOA',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SVOB',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCIA',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCIB',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ESA',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ESB',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MLST',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'FTA',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'FTB',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'CCS',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'RCIS',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SFIS',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SYNS',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NFIS',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PPIS',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RESS',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MBS_Bits',0,6,165,4,3
	.word	15780
	.byte	10
	.byte	'_Ifx_ERAY_MBSC1_Bits',0,6,168,4,16,4,11
	.byte	'MBC0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MBC1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MBC2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MBC3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MBC4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MBC5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MBC6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MBC7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MBC8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MBC9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MBC10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MBC11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MBC12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MBC13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBC14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MBC15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MBC16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MBC17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MBC18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MBC19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MBC20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MBC21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MBC22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MBC23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MBC24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MBC25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MBC26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MBC27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MBC28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MBC29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MBC30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MBC31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MBSC1_Bits',0,6,202,4,3
	.word	16249
	.byte	10
	.byte	'_Ifx_ERAY_MBSC2_Bits',0,6,205,4,16,4,11
	.byte	'MBC32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MBC33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MBC34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MBC35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MBC36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MBC37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MBC38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MBC39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MBC40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MBC41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MBC42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MBC43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MBC44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MBC45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBC46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MBC47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MBC48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MBC49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MBC50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MBC51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MBC52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MBC53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MBC54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MBC55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MBC56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MBC57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MBC58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MBC59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MBC60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MBC61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MBC62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MBC63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MBSC2_Bits',0,6,239,4,3
	.word	16840
	.byte	10
	.byte	'_Ifx_ERAY_MBSC3_Bits',0,6,242,4,16,4,11
	.byte	'MBC64',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MBC65',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MBC66',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MBC67',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MBC68',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MBC69',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MBC70',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MBC71',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MBC72',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MBC73',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MBC74',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MBC75',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MBC76',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MBC77',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBC78',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MBC79',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MBC80',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MBC81',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MBC82',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MBC83',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MBC84',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MBC85',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MBC86',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MBC87',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MBC88',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MBC89',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MBC90',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MBC91',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MBC92',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MBC93',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MBC94',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MBC95',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MBSC3_Bits',0,6,148,5,3
	.word	17441
	.byte	10
	.byte	'_Ifx_ERAY_MBSC4_Bits',0,6,151,5,16,4,11
	.byte	'MBC96',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MBC97',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MBC98',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MBC99',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MBC100',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MBC101',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MBC102',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MBC103',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MBC104',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MBC105',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MBC106',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MBC107',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MBC108',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MBC109',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBC110',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MBC111',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MBC112',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MBC113',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MBC114',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MBC115',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MBC116',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MBC117',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MBC118',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MBC119',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MBC120',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MBC121',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MBC122',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MBC123',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MBC124',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MBC125',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MBC126',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MBC127',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MBSC4_Bits',0,6,185,5,3
	.word	18042
	.byte	10
	.byte	'_Ifx_ERAY_MHDC_Bits',0,6,188,5,16,4,11
	.byte	'SFDL',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	9,0,2,35,0,11
	.byte	'SLT',0,2
	.word	510
	.byte	13,3,2,35,2,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MHDC_Bits',0,6,194,5,3
	.word	18671
	.byte	10
	.byte	'_Ifx_ERAY_MHDF_Bits',0,6,197,5,16,4,11
	.byte	'SNUA',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SNUB',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FNFA',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FNFB',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TBFA',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TBFB',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TNSA',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TNSB',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'WAHP',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_ERAY_MHDF_Bits',0,6,209,5,3
	.word	18802
	.byte	10
	.byte	'_Ifx_ERAY_MHDS_Bits',0,6,212,5,16,4,11
	.byte	'EIBF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EOBF',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EMR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ETBF1',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ETBF2',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FMBD',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MFMB',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CRAM',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FMB',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MBT',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MBU',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MHDS_Bits',0,6,228,5,3
	.word	19023
	.byte	10
	.byte	'_Ifx_ERAY_MRC_Bits',0,6,231,5,16,4,11
	.byte	'FDB',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FFB',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'LCB',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SEC',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SPLM',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MRC_Bits',0,6,239,5,3
	.word	19321
	.byte	10
	.byte	'_Ifx_ERAY_MSIC1_Bits',0,6,242,5,16,4,11
	.byte	'MSIP0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MSIP1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MSIP2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MSIP3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MSIP4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MSIP5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MSIP6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MSIP7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MSIP8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MSIP9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MSIP10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MSIP11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MSIP12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MSIP13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MSIP14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MSIP15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MSIP16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MSIP17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MSIP18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MSIP19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MSIP20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MSIP21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MSIP22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MSIP23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MSIP24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MSIP25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MSIP26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MSIP27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MSIP28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MSIP29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MSIP30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MSIP31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MSIC1_Bits',0,6,148,6,3
	.word	19473
	.byte	10
	.byte	'_Ifx_ERAY_MSIC2_Bits',0,6,151,6,16,4,11
	.byte	'MSIP32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MSIP33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MSIP34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MSIP35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MSIP36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MSIP37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MSIP38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MSIP39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MSIP40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MSIP41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MSIP42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MSIP43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MSIP44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MSIP45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MSIP46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MSIP47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MSIP48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MSIP49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MSIP50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MSIP51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MSIP52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MSIP53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MSIP54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MSIP55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MSIP56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MSIP57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MSIP58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MSIP59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MSIP60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MSIP61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MSIP62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MSIP63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MSIC2_Bits',0,6,185,6,3
	.word	20096
	.byte	10
	.byte	'_Ifx_ERAY_MSIC3_Bits',0,6,188,6,16,4,11
	.byte	'MSIP64',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MSIP65',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MSIP66',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MSIP67',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MSIP68',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MSIP69',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MSIP70',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MSIP71',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MSIP72',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MSIP73',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MSIP74',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MSIP75',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MSIP76',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MSIP77',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MSIP78',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MSIP79',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MSIP80',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MSIP81',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MSIP82',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MSIP83',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MSIP84',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MSIP85',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MSIP86',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MSIP87',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MSIP88',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MSIP89',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MSIP90',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MSIP91',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MSIP92',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MSIP93',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MSIP94',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MSIP95',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MSIC3_Bits',0,6,222,6,3
	.word	20729
	.byte	10
	.byte	'_Ifx_ERAY_MSIC4_Bits',0,6,225,6,16,4,11
	.byte	'MSIP96',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MSIP97',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MSIP98',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'MSIP99',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'MSIP100',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'MSIP101',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MSIP102',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MSIP103',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'MSIP104',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'MSIP105',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'MSIP106',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'MSIP107',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'MSIP108',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'MSIP109',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MSIP110',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'MSIP111',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'MSIP112',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MSIP113',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MSIP114',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'MSIP115',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'MSIP116',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MSIP117',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'MSIP118',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'MSIP119',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MSIP120',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MSIP121',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'MSIP122',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'MSIP123',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'MSIP124',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MSIP125',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'MSIP126',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'MSIP127',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_MSIC4_Bits',0,6,131,7,3
	.word	21362
	.byte	10
	.byte	'_Ifx_ERAY_MTCCV_Bits',0,6,134,7,16,4,11
	.byte	'MTV',0,2
	.word	510
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'CCV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	510
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_ERAY_MTCCV_Bits',0,6,140,7,3
	.word	22023
	.byte	10
	.byte	'_Ifx_ERAY_NDAT1_Bits',0,6,143,7,16,4,11
	.byte	'ND0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ND1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ND2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ND3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ND4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ND5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ND6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ND7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ND8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ND9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ND10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ND11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ND12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'ND13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'ND14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ND15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'ND16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'ND17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'ND18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'ND19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ND20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'ND21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'ND22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'ND23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'ND24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'ND25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'ND26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'ND27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'ND28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'ND29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'ND30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'ND31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDAT1_Bits',0,6,177,7,3
	.word	22156
	.byte	10
	.byte	'_Ifx_ERAY_NDAT2_Bits',0,6,180,7,16,4,11
	.byte	'ND32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ND33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ND34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ND35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ND36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ND37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ND38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ND39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ND40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ND41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ND42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ND43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ND44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'ND45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'ND46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ND47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'ND48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'ND49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'ND50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'ND51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ND52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'ND53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'ND54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'ND55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'ND56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'ND57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'ND58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'ND59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'ND60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'ND61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'ND62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'ND63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDAT2_Bits',0,6,214,7,3
	.word	22715
	.byte	10
	.byte	'_Ifx_ERAY_NDAT3_Bits',0,6,217,7,16,4,11
	.byte	'ND64',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ND65',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ND66',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ND67',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ND68',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ND69',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ND70',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ND71',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ND72',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ND73',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ND74',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ND75',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ND76',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'ND77',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'ND78',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ND79',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'ND80',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'ND81',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'ND82',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'ND83',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ND84',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'ND85',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'ND86',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'ND87',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'ND88',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'ND89',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'ND90',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'ND91',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'ND92',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'ND93',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'ND94',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'ND95',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDAT3_Bits',0,6,251,7,3
	.word	23284
	.byte	10
	.byte	'_Ifx_ERAY_NDAT4_Bits',0,6,254,7,16,4,11
	.byte	'ND96',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ND97',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ND98',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ND99',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ND100',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ND101',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'ND102',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'ND103',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ND104',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'ND105',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'ND106',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'ND107',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'ND108',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'ND109',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'ND110',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'ND111',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'ND112',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'ND113',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'ND114',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'ND115',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ND116',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'ND117',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'ND118',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'ND119',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'ND120',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'ND121',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'ND122',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'ND123',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'ND124',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'ND125',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'ND126',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'ND127',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDAT4_Bits',0,6,160,8,3
	.word	23853
	.byte	10
	.byte	'_Ifx_ERAY_NDIC1_Bits',0,6,163,8,16,4,11
	.byte	'NDIP0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NDIP1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NDIP2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NDIP3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NDIP4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NDIP5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NDIP6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NDIP7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NDIP8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NDIP9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NDIP10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NDIP11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NDIP12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NDIP13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NDIP14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NDIP15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NDIP16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NDIP17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NDIP18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NDIP19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NDIP20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NDIP21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NDIP22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NDIP23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NDIP24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NDIP25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NDIP26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NDIP27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NDIP28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NDIP29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NDIP30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NDIP31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDIC1_Bits',0,6,197,8,3
	.word	24450
	.byte	10
	.byte	'_Ifx_ERAY_NDIC2_Bits',0,6,200,8,16,4,11
	.byte	'NDIP32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NDIP33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NDIP34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NDIP35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NDIP36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NDIP37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NDIP38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NDIP39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NDIP40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NDIP41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NDIP42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NDIP43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NDIP44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NDIP45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NDIP46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NDIP47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NDIP48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NDIP49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NDIP50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NDIP51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NDIP52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NDIP53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NDIP54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NDIP55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NDIP56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NDIP57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NDIP58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NDIP59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NDIP60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NDIP61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NDIP62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NDIP63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDIC2_Bits',0,6,234,8,3
	.word	25073
	.byte	10
	.byte	'_Ifx_ERAY_NDIC3_Bits',0,6,237,8,16,4,11
	.byte	'NDIP64',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NDIP65',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NDIP66',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NDIP67',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NDIP68',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NDIP69',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NDIP70',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NDIP71',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NDIP72',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NDIP73',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NDIP74',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NDIP75',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NDIP76',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NDIP77',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NDIP78',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NDIP79',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NDIP80',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NDIP81',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NDIP82',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NDIP83',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NDIP84',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NDIP85',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NDIP86',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NDIP87',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NDIP88',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NDIP89',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NDIP90',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NDIP91',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NDIP92',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NDIP93',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NDIP94',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NDIP95',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDIC3_Bits',0,6,143,9,3
	.word	25706
	.byte	10
	.byte	'_Ifx_ERAY_NDIC4_Bits',0,6,146,9,16,4,11
	.byte	'NDIP96',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'NDIP97',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'NDIP98',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'NDIP99',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'NDIP100',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'NDIP101',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'NDIP102',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NDIP103',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NDIP104',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'NDIP105',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'NDIP106',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'NDIP107',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'NDIP108',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'NDIP109',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'NDIP110',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'NDIP111',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'NDIP112',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NDIP113',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'NDIP114',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'NDIP115',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'NDIP116',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'NDIP117',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'NDIP118',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'NDIP119',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'NDIP120',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'NDIP121',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'NDIP122',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NDIP123',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'NDIP124',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'NDIP125',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'NDIP126',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'NDIP127',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_NDIC4_Bits',0,6,180,9,3
	.word	26339
	.byte	10
	.byte	'_Ifx_ERAY_NEMC_Bits',0,6,183,9,16,4,11
	.byte	'NML',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_ERAY_NEMC_Bits',0,6,187,9,3
	.word	27000
	.byte	10
	.byte	'_Ifx_ERAY_NMV_Bits',0,6,190,9,16,4,11
	.byte	'NM',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ERAY_NMV_Bits',0,6,193,9,3
	.word	27092
	.byte	10
	.byte	'_Ifx_ERAY_OBCM_Bits',0,6,196,9,16,4,11
	.byte	'RHSS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDSS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'RHSH',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RDSH',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_ERAY_OBCM_Bits',0,6,204,9,3
	.word	27159
	.byte	10
	.byte	'_Ifx_ERAY_OBCR_Bits',0,6,207,9,16,4,11
	.byte	'OBRS',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'VIEW',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REQ',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'OBSYS',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'OBRH',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_ERAY_OBCR_Bits',0,6,217,9,3
	.word	27323
	.byte	10
	.byte	'_Ifx_ERAY_OCS_Bits',0,6,220,9,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_OCS_Bits',0,6,227,9,3
	.word	27526
	.byte	10
	.byte	'_Ifx_ERAY_OCV_Bits',0,6,230,9,16,4,11
	.byte	'OCV',0,4
	.word	470
	.byte	19,13,2,35,0,11
	.byte	'reserved_19',0,2
	.word	510
	.byte	13,0,2,35,2,0,21
	.byte	'Ifx_ERAY_OCV_Bits',0,6,234,9,3
	.word	27674
	.byte	10
	.byte	'_Ifx_ERAY_OSID_Bits',0,6,237,9,16,4,11
	.byte	'OID',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RXOA',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'RXOB',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ERAY_OSID_Bits',0,6,244,9,3
	.word	27765
	.byte	10
	.byte	'_Ifx_ERAY_OTSS_Bits',0,6,247,9,16,4,11
	.byte	'OTGB0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'OTGB1',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OTGB2',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_ERAY_OTSS_Bits',0,6,255,9,3
	.word	27913
	.byte	10
	.byte	'_Ifx_ERAY_PRTC1_Bits',0,6,130,10,16,4,11
	.byte	'TSST',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'CASM',0,2
	.word	510
	.byte	7,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SPP',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'BRP',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RXW',0,2
	.word	510
	.byte	9,7,2,35,2,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RWP',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ERAY_PRTC1_Bits',0,6,140,10,3
	.word	28087
	.byte	10
	.byte	'_Ifx_ERAY_PRTC2_Bits',0,6,143,10,16,4,11
	.byte	'RXI',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'RXL',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'TXI',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TXL',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_PRTC2_Bits',0,6,152,10,3
	.word	28282
	.byte	10
	.byte	'_Ifx_ERAY_RCV_Bits',0,6,155,10,16,4,11
	.byte	'RCV',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_ERAY_RCV_Bits',0,6,159,10,3
	.word	28467
	.byte	10
	.byte	'_Ifx_ERAY_RDDS_Bits',0,6,162,10,16,4,11
	.byte	'MDRB0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MDRB1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MDRB2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'MDRB3',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_ERAY_RDDS_Bits',0,6,168,10,3
	.word	28558
	.byte	10
	.byte	'_Ifx_ERAY_RDHS1_Bits',0,6,171,10,16,4,11
	.byte	'FID',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'CYC',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CHA',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CHB',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CFG',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PPIT',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TXM',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MBI',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_RDHS1_Bits',0,6,184,10,3
	.word	28681
	.byte	10
	.byte	'_Ifx_ERAY_RDHS2_Bits',0,6,187,10,16,4,11
	.byte	'CRC',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'PLC',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PLR',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_RDHS2_Bits',0,6,195,10,3
	.word	28928
	.byte	10
	.byte	'_Ifx_ERAY_RDHS3_Bits',0,6,198,10,16,4,11
	.byte	'DP',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'RCC',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'RCI',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SFI',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'SYN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'NFI',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PPI',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'RES',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_RDHS3_Bits',0,6,211,10,3
	.word	29099
	.byte	10
	.byte	'_Ifx_ERAY_SCV_Bits',0,6,214,10,16,4,11
	.byte	'SCCA',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'SCCB',0,2
	.word	510
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_SCV_Bits',0,6,220,10,3
	.word	29344
	.byte	10
	.byte	'_Ifx_ERAY_SFS_Bits',0,6,223,10,16,4,11
	.byte	'VSAE',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'VSAO',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'VSBE',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'VSBO',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'MOCS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'MRCS',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'RCLR',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_ERAY_SFS_Bits',0,6,234,10,3
	.word	29475
	.byte	10
	.byte	'_Ifx_ERAY_SIER_Bits',0,6,237,10,16,4,11
	.byte	'WSTE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CASE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CYCSE',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXIE',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RXIE',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RFNEE',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RFCLE',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NMVCE',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TI0E',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TI1E',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TIBCE',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TOBCE',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SWEE',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SUCSE',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBSIE',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SDSE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'WUPAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MTSAE',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'WUPBE',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MTSBE',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ERAY_SIER_Bits',0,6,133,11,3
	.word	29679
	.byte	10
	.byte	'_Ifx_ERAY_SIES_Bits',0,6,136,11,16,4,11
	.byte	'WSTE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CASE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CYCSE',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXIE',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RXIE',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RFNEE',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RFCLE',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NMVCE',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TI0E',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TI1E',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TIBCE',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TOBCE',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SWEE',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SUCSE',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBSIE',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SDSE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'WUPAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MTSAE',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'WUPBE',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MTSBE',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ERAY_SIES_Bits',0,6,160,11,3
	.word	30112
	.byte	10
	.byte	'_Ifx_ERAY_SILS_Bits',0,6,163,11,16,4,11
	.byte	'WSTL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CASL',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CYCSL',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXIL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RXIL',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RFNEL',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RFCLL',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NMVCL',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TI0L',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TI1L',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TIBCL',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TOBCL',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SWEL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SUCSL',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBSIL',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SDSL',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'WUPAL',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MTSAL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'WUPBL',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MTSBL',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ERAY_SILS_Bits',0,6,187,11,3
	.word	30545
	.byte	10
	.byte	'_Ifx_ERAY_SIR_Bits',0,6,190,11,16,4,11
	.byte	'WST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CAS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CYCS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RXI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'RFNE',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'RFCL',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NMVC',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TI0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TI1',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TIBC',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TOBC',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SWE',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'SUCS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'MBSI',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'SDS',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'WUPA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'MTSA',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'WUPB',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MTSB',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ERAY_SIR_Bits',0,6,214,11,3
	.word	30978
	.byte	10
	.byte	'_Ifx_ERAY_STPW1_Bits',0,6,217,11,16,4,11
	.byte	'ESWT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWMS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EDGE',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SSWT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EETP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EINT0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EINT1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SCCV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SMTV',0,2
	.word	510
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_STPW1_Bits',0,6,231,11,3
	.word	31389
	.byte	10
	.byte	'_Ifx_ERAY_STPW2_Bits',0,6,234,11,16,4,11
	.byte	'SSCVA',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'SSCVB',0,2
	.word	510
	.byte	11,5,2,35,2,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ERAY_STPW2_Bits',0,6,240,11,3
	.word	31660
	.byte	10
	.byte	'_Ifx_ERAY_SUCC1_Bits',0,6,243,11,16,4,11
	.byte	'CMD',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PBSY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TXST',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TXSY',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'CSA',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'PTA',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'WUCS',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TSM',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'HCSE',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'MTSA',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MTSB',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CCHA',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CCHB',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_ERAY_SUCC1_Bits',0,6,133,12,3
	.word	31797
	.byte	10
	.byte	'_Ifx_ERAY_SUCC2_Bits',0,6,136,12,16,4,11
	.byte	'LT',0,4
	.word	470
	.byte	21,11,2,35,0,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'LTN',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_ERAY_SUCC2_Bits',0,6,142,12,3
	.word	32126
	.byte	10
	.byte	'_Ifx_ERAY_SUCC3_Bits',0,6,145,12,16,4,11
	.byte	'WCP',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'WCF',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_ERAY_SUCC3_Bits',0,6,150,12,3
	.word	32258
	.byte	10
	.byte	'_Ifx_ERAY_SWNIT_Bits',0,6,153,12,16,4,11
	.byte	'SESA',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SBSA',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TCSA',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SESB',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SBSB',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TCSB',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'MTSA',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MTSB',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SENA',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SBNA',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SENB',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SBNB',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_ERAY_SWNIT_Bits',0,6,168,12,3
	.word	32367
	.byte	10
	.byte	'_Ifx_ERAY_T0C_Bits',0,6,171,12,16,4,11
	.byte	'T0RC',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'T0MS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'T0CC',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'T0MO',0,2
	.word	510
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_T0C_Bits',0,6,180,12,3
	.word	32639
	.byte	10
	.byte	'_Ifx_ERAY_T1C_Bits',0,6,183,12,16,4,11
	.byte	'T1RC',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'T1MS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'T1MC',0,2
	.word	510
	.byte	14,2,2,35,2,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_T1C_Bits',0,6,190,12,3
	.word	32824
	.byte	10
	.byte	'_Ifx_ERAY_TEST1_Bits',0,6,193,12,16,4,11
	.byte	'WRTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ELBE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'TMC',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'AOA',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'AOB',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'RXA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'RXB',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TXA',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TXB',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TXENA',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TXENB',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'CERA',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'CERB',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_ERAY_TEST1_Bits',0,6,212,12,3
	.word	32970
	.byte	10
	.byte	'_Ifx_ERAY_TEST2_Bits',0,6,215,12,16,4,11
	.byte	'RS',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SSEL',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	7,2,2,35,0,11
	.byte	'WRECC',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_ERAY_TEST2_Bits',0,6,223,12,3
	.word	33321
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ1_Bits',0,6,226,12,16,4,11
	.byte	'TXR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TXR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TXR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TXR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TXR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TXR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TXR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TXR8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TXR9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TXR10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TXR11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TXR12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TXR13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TXR14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TXR15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TXR16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'TXR17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TXR18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TXR19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TXR20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TXR21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TXR22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'TXR23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'TXR24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'TXR25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TXR26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TXR27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TXR28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TXR29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'TXR30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TXR31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_TXRQ1_Bits',0,6,132,13,3
	.word	33492
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ2_Bits',0,6,135,13,16,4,11
	.byte	'TXR32',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TXR33',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TXR34',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXR35',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TXR36',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TXR37',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TXR38',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TXR39',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TXR40',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TXR41',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TXR42',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TXR43',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TXR44',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TXR45',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TXR46',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TXR47',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TXR48',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'TXR49',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TXR50',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TXR51',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TXR52',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TXR53',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TXR54',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'TXR55',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'TXR56',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'TXR57',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TXR58',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TXR59',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TXR60',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TXR61',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'TXR62',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TXR63',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_TXRQ2_Bits',0,6,169,13,3
	.word	34083
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ3_Bits',0,6,172,13,16,4,11
	.byte	'TXR64',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TXR65',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TXR66',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXR67',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TXR68',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TXR69',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TXR70',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TXR71',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TXR72',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TXR73',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TXR74',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TXR75',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TXR76',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TXR77',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TXR78',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TXR79',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TXR80',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'TXR81',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TXR82',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TXR83',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TXR84',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TXR85',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TXR86',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'TXR87',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'TXR88',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'TXR89',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TXR90',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TXR91',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TXR92',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TXR93',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'TXR94',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TXR95',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_TXRQ3_Bits',0,6,206,13,3
	.word	34684
	.byte	10
	.byte	'_Ifx_ERAY_TXRQ4_Bits',0,6,209,13,16,4,11
	.byte	'TXR96',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'TXR97',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TXR98',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TXR99',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TXR100',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'TXR101',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'TXR102',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'TXR103',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TXR104',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TXR105',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'TXR106',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'TXR107',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'TXR108',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TXR109',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TXR110',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TXR111',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TXR112',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'TXR113',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'TXR114',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TXR115',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'TXR116',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TXR117',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TXR118',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'TXR119',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'TXR120',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'TXR121',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'TXR122',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TXR123',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TXR124',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TXR125',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'TXR126',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TXR127',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ERAY_TXRQ4_Bits',0,6,243,13,3
	.word	35285
	.byte	10
	.byte	'_Ifx_ERAY_WRDS_Bits',0,6,246,13,16,4,11
	.byte	'MDWB0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MDWB1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MDWB2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'MDWB3',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_ERAY_WRDS_Bits',0,6,252,13,3
	.word	35914
	.byte	10
	.byte	'_Ifx_ERAY_WRHS1_Bits',0,6,255,13,16,4,11
	.byte	'FID',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'CYC',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CHA',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CHB',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CFG',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PPIT',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'TXM',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'MBI',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ERAY_WRHS1_Bits',0,6,140,14,3
	.word	36037
	.byte	10
	.byte	'_Ifx_ERAY_WRHS2_Bits',0,6,143,14,16,4,11
	.byte	'CRC',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'PLC',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_ERAY_WRHS2_Bits',0,6,149,14,3
	.word	36284
	.byte	10
	.byte	'_Ifx_ERAY_WRHS3_Bits',0,6,152,14,16,4,11
	.byte	'DP',0,2
	.word	510
	.byte	11,5,2,35,0,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_ERAY_WRHS3_Bits',0,6,156,14,3
	.word	36417
	.byte	12,6,164,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_ACCEN0',0,6,169,14,3
	.word	36511
	.byte	12,6,172,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_ACCEN1',0,6,177,14,3
	.word	36576
	.byte	12,6,180,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_ACS',0,6,185,14,3
	.word	36641
	.byte	12,6,188,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9531
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_CCEV',0,6,193,14,3
	.word	36703
	.byte	12,6,196,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9677
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_CCSV',0,6,201,14,3
	.word	36766
	.byte	12,6,204,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9953
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_CLC',0,6,209,14,3
	.word	36829
	.byte	12,6,212,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10136
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_CREL',0,6,217,14,3
	.word	36891
	.byte	12,6,220,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10287
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_CUST1',0,6,225,14,3
	.word	36954
	.byte	12,6,228,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10561
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_CUST3',0,6,233,14,3
	.word	37018
	.byte	12,6,236,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10632
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_EIER',0,6,241,14,3
	.word	37082
	.byte	12,6,244,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11053
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_EIES',0,6,249,14,3
	.word	37145
	.byte	12,6,252,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11474
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_EILS',0,6,129,15,3
	.word	37208
	.byte	12,6,132,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11895
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_EIR',0,6,137,15,3
	.word	37271
	.byte	12,6,140,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12296
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_ENDN',0,6,145,15,3
	.word	37333
	.byte	12,6,148,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12366
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_ESID',0,6,153,15,3
	.word	37396
	.byte	12,6,156,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12514
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_FCL',0,6,161,15,3
	.word	37459
	.byte	12,6,164,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12603
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_FRF',0,6,169,15,3
	.word	37521
	.byte	12,6,172,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12776
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_FRFM',0,6,177,15,3
	.word	37583
	.byte	12,6,180,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12892
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_FSR',0,6,185,15,3
	.word	37646
	.byte	12,6,188,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13053
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC01',0,6,193,15,3
	.word	37708
	.byte	12,6,196,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13149
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC02',0,6,201,15,3
	.word	37773
	.byte	12,6,204,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13284
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC03',0,6,209,15,3
	.word	37838
	.byte	12,6,212,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13453
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC04',0,6,217,15,3
	.word	37903
	.byte	12,6,220,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13588
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC05',0,6,225,15,3
	.word	37968
	.byte	12,6,228,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13730
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC06',0,6,233,15,3
	.word	38033
	.byte	12,6,236,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13865
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC07',0,6,241,15,3
	.word	38098
	.byte	12,6,244,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14000
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC08',0,6,249,15,3
	.word	38163
	.byte	12,6,252,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14134
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC09',0,6,129,16,3
	.word	38228
	.byte	12,6,132,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14307
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC10',0,6,137,16,3
	.word	38293
	.byte	12,6,140,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14442
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_GTUC11',0,6,145,16,3
	.word	38358
	.byte	12,6,148,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14654
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_IBCM',0,6,153,16,3
	.word	38423
	.byte	12,6,156,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14852
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_IBCR',0,6,161,16,3
	.word	38486
	.byte	12,6,164,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15018
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_ID',0,6,169,16,3
	.word	38549
	.byte	12,6,172,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15127
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_ILE',0,6,177,16,3
	.word	38610
	.byte	12,6,180,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15236
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_KRST0',0,6,185,16,3
	.word	38672
	.byte	12,6,188,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15349
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_KRST1',0,6,193,16,3
	.word	38736
	.byte	12,6,196,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15443
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_KRSTCLR',0,6,201,16,3
	.word	38800
	.byte	12,6,204,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15541
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_LCK',0,6,209,16,3
	.word	38866
	.byte	12,6,212,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15647
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_LDTS',0,6,217,16,3
	.word	38928
	.byte	12,6,220,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15780
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MBS',0,6,225,16,3
	.word	38991
	.byte	12,6,228,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16249
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MBSC1',0,6,233,16,3
	.word	39053
	.byte	12,6,236,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16840
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MBSC2',0,6,241,16,3
	.word	39117
	.byte	12,6,244,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17441
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MBSC3',0,6,249,16,3
	.word	39181
	.byte	12,6,252,16,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18042
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MBSC4',0,6,129,17,3
	.word	39245
	.byte	12,6,132,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18671
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MHDC',0,6,137,17,3
	.word	39309
	.byte	12,6,140,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18802
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MHDF',0,6,145,17,3
	.word	39372
	.byte	12,6,148,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19023
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MHDS',0,6,153,17,3
	.word	39435
	.byte	12,6,156,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19321
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MRC',0,6,161,17,3
	.word	39498
	.byte	12,6,164,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19473
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MSIC1',0,6,169,17,3
	.word	39560
	.byte	12,6,172,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20096
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MSIC2',0,6,177,17,3
	.word	39624
	.byte	12,6,180,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20729
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MSIC3',0,6,185,17,3
	.word	39688
	.byte	12,6,188,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21362
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MSIC4',0,6,193,17,3
	.word	39752
	.byte	12,6,196,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22023
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_MTCCV',0,6,201,17,3
	.word	39816
	.byte	12,6,204,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22156
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDAT1',0,6,209,17,3
	.word	39880
	.byte	12,6,212,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22715
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDAT2',0,6,217,17,3
	.word	39944
	.byte	12,6,220,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23284
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDAT3',0,6,225,17,3
	.word	40008
	.byte	12,6,228,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23853
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDAT4',0,6,233,17,3
	.word	40072
	.byte	12,6,236,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24450
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDIC1',0,6,241,17,3
	.word	40136
	.byte	12,6,244,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25073
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDIC2',0,6,249,17,3
	.word	40200
	.byte	12,6,252,17,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25706
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDIC3',0,6,129,18,3
	.word	40264
	.byte	12,6,132,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26339
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NDIC4',0,6,137,18,3
	.word	40328
	.byte	12,6,140,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27000
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NEMC',0,6,145,18,3
	.word	40392
	.byte	12,6,148,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27092
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_NMV',0,6,153,18,3
	.word	40455
	.byte	12,6,156,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27159
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_OBCM',0,6,161,18,3
	.word	40517
	.byte	12,6,164,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27323
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_OBCR',0,6,169,18,3
	.word	40580
	.byte	12,6,172,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27526
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_OCS',0,6,177,18,3
	.word	40643
	.byte	12,6,180,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27674
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_OCV',0,6,185,18,3
	.word	40705
	.byte	12,6,188,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27765
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_OSID',0,6,193,18,3
	.word	40767
	.byte	12,6,196,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27913
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_OTSS',0,6,201,18,3
	.word	40830
	.byte	12,6,204,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28087
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_PRTC1',0,6,209,18,3
	.word	40893
	.byte	12,6,212,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28282
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_PRTC2',0,6,217,18,3
	.word	40957
	.byte	12,6,220,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28467
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_RCV',0,6,225,18,3
	.word	41021
	.byte	12,6,228,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28558
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_RDDS',0,6,233,18,3
	.word	41083
	.byte	12,6,236,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28681
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_RDHS1',0,6,241,18,3
	.word	41146
	.byte	12,6,244,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28928
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_RDHS2',0,6,249,18,3
	.word	41210
	.byte	12,6,252,18,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29099
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_RDHS3',0,6,129,19,3
	.word	41274
	.byte	12,6,132,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29344
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SCV',0,6,137,19,3
	.word	41338
	.byte	12,6,140,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29475
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SFS',0,6,145,19,3
	.word	41400
	.byte	12,6,148,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29679
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SIER',0,6,153,19,3
	.word	41462
	.byte	12,6,156,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30112
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SIES',0,6,161,19,3
	.word	41525
	.byte	12,6,164,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30545
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SILS',0,6,169,19,3
	.word	41588
	.byte	12,6,172,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30978
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SIR',0,6,177,19,3
	.word	41651
	.byte	12,6,180,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31389
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_STPW1',0,6,185,19,3
	.word	41713
	.byte	12,6,188,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31660
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_STPW2',0,6,193,19,3
	.word	41777
	.byte	12,6,196,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31797
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SUCC1',0,6,201,19,3
	.word	41841
	.byte	12,6,204,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32126
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SUCC2',0,6,209,19,3
	.word	41905
	.byte	12,6,212,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32258
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SUCC3',0,6,217,19,3
	.word	41969
	.byte	12,6,220,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32367
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_SWNIT',0,6,225,19,3
	.word	42033
	.byte	12,6,228,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_T0C',0,6,233,19,3
	.word	42097
	.byte	12,6,236,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32824
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_T1C',0,6,241,19,3
	.word	42159
	.byte	12,6,244,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32970
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_TEST1',0,6,249,19,3
	.word	42221
	.byte	12,6,252,19,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33321
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_TEST2',0,6,129,20,3
	.word	42285
	.byte	12,6,132,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33492
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_TXRQ1',0,6,137,20,3
	.word	42349
	.byte	12,6,140,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34083
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_TXRQ2',0,6,145,20,3
	.word	42413
	.byte	12,6,148,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34684
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_TXRQ3',0,6,153,20,3
	.word	42477
	.byte	12,6,156,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35285
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_TXRQ4',0,6,161,20,3
	.word	42541
	.byte	12,6,164,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35914
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_WRDS',0,6,169,20,3
	.word	42605
	.byte	12,6,172,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36037
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_WRHS1',0,6,177,20,3
	.word	42668
	.byte	12,6,180,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36284
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_WRHS2',0,6,185,20,3
	.word	42732
	.byte	12,6,188,20,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36417
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ERAY_WRHS3',0,6,193,20,3
	.word	42796
	.byte	14,44
	.word	493
	.byte	15,43,0,14,52
	.word	493
	.byte	15,51,0,14,60
	.word	37396
	.byte	15,14,0,14,60
	.word	40767
	.byte	15,14,0,14,12
	.word	40455
	.byte	15,2,0,14,196,2
	.word	493
	.byte	15,195,2,0,14,88
	.word	493
	.byte	15,87,0,14,40
	.word	493
	.byte	15,39,0,14,128,2
	.word	42605
	.byte	15,63,0,14,232,1
	.word	493
	.byte	15,231,1,0,14,128,2
	.word	41083
	.byte	15,63,0,14,216,2
	.word	493
	.byte	15,215,2,0,14,116
	.word	493
	.byte	15,115,0,14,128,14
	.word	493
	.byte	15,255,13,0,10
	.byte	'_Ifx_ERAY',0,6,204,20,25,128,32,13
	.byte	'CLC',0
	.word	36829
	.byte	4,2,35,0,13
	.byte	'CUST1',0
	.word	36954
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	38549
	.byte	4,2,35,8,13
	.byte	'CUST3',0
	.word	37018
	.byte	4,2,35,12,13
	.byte	'TEST1',0
	.word	42221
	.byte	4,2,35,16,13
	.byte	'TEST2',0
	.word	42285
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	1538
	.byte	4,2,35,24,13
	.byte	'LCK',0
	.word	38866
	.byte	4,2,35,28,13
	.byte	'EIR',0
	.word	37271
	.byte	4,2,35,32,13
	.byte	'SIR',0
	.word	41651
	.byte	4,2,35,36,13
	.byte	'EILS',0
	.word	37208
	.byte	4,2,35,40,13
	.byte	'SILS',0
	.word	41588
	.byte	4,2,35,44,13
	.byte	'EIES',0
	.word	37145
	.byte	4,2,35,48,13
	.byte	'EIER',0
	.word	37082
	.byte	4,2,35,52,13
	.byte	'SIES',0
	.word	41525
	.byte	4,2,35,56,13
	.byte	'SIER',0
	.word	41462
	.byte	4,2,35,60,13
	.byte	'ILE',0
	.word	38610
	.byte	4,2,35,64,13
	.byte	'T0C',0
	.word	42097
	.byte	4,2,35,68,13
	.byte	'T1C',0
	.word	42159
	.byte	4,2,35,72,13
	.byte	'STPW1',0
	.word	41713
	.byte	4,2,35,76,13
	.byte	'STPW2',0
	.word	41777
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	42860
	.byte	44,2,35,84,13
	.byte	'SUCC1',0
	.word	41841
	.byte	4,3,35,128,1,13
	.byte	'SUCC2',0
	.word	41905
	.byte	4,3,35,132,1,13
	.byte	'SUCC3',0
	.word	41969
	.byte	4,3,35,136,1,13
	.byte	'NEMC',0
	.word	40392
	.byte	4,3,35,140,1,13
	.byte	'PRTC1',0
	.word	40893
	.byte	4,3,35,144,1,13
	.byte	'PRTC2',0
	.word	40957
	.byte	4,3,35,148,1,13
	.byte	'MHDC',0
	.word	39309
	.byte	4,3,35,152,1,13
	.byte	'reserved_9C',0
	.word	1538
	.byte	4,3,35,156,1,13
	.byte	'GTUC01',0
	.word	37708
	.byte	4,3,35,160,1,13
	.byte	'GTUC02',0
	.word	37773
	.byte	4,3,35,164,1,13
	.byte	'GTUC03',0
	.word	37838
	.byte	4,3,35,168,1,13
	.byte	'GTUC04',0
	.word	37903
	.byte	4,3,35,172,1,13
	.byte	'GTUC05',0
	.word	37968
	.byte	4,3,35,176,1,13
	.byte	'GTUC06',0
	.word	38033
	.byte	4,3,35,180,1,13
	.byte	'GTUC07',0
	.word	38098
	.byte	4,3,35,184,1,13
	.byte	'GTUC08',0
	.word	38163
	.byte	4,3,35,188,1,13
	.byte	'GTUC09',0
	.word	38228
	.byte	4,3,35,192,1,13
	.byte	'GTUC10',0
	.word	38293
	.byte	4,3,35,196,1,13
	.byte	'GTUC11',0
	.word	38358
	.byte	4,3,35,200,1,13
	.byte	'reserved_CC',0
	.word	42869
	.byte	52,3,35,204,1,13
	.byte	'CCSV',0
	.word	36766
	.byte	4,3,35,128,2,13
	.byte	'CCEV',0
	.word	36703
	.byte	4,3,35,132,2,13
	.byte	'reserved_108',0
	.word	3357
	.byte	8,3,35,136,2,13
	.byte	'SCV',0
	.word	41338
	.byte	4,3,35,144,2,13
	.byte	'MTCCV',0
	.word	39816
	.byte	4,3,35,148,2,13
	.byte	'RCV',0
	.word	41021
	.byte	4,3,35,152,2,13
	.byte	'OCV',0
	.word	40705
	.byte	4,3,35,156,2,13
	.byte	'SFS',0
	.word	41400
	.byte	4,3,35,160,2,13
	.byte	'SWNIT',0
	.word	42033
	.byte	4,3,35,164,2,13
	.byte	'ACS',0
	.word	36641
	.byte	4,3,35,168,2,13
	.byte	'reserved_12C',0
	.word	1538
	.byte	4,3,35,172,2,13
	.byte	'ESID_1S',0
	.word	42878
	.byte	60,3,35,176,2,13
	.byte	'reserved_16C',0
	.word	1538
	.byte	4,3,35,236,2,13
	.byte	'OSID_1S',0
	.word	42887
	.byte	60,3,35,240,2,13
	.byte	'reserved_1AC',0
	.word	1538
	.byte	4,3,35,172,3,13
	.byte	'NMV_1S',0
	.word	42896
	.byte	12,3,35,176,3,13
	.byte	'reserved_1BC',0
	.word	42905
	.byte	196,2,3,35,188,3,13
	.byte	'MRC',0
	.word	39498
	.byte	4,3,35,128,6,13
	.byte	'FRF',0
	.word	37521
	.byte	4,3,35,132,6,13
	.byte	'FRFM',0
	.word	37583
	.byte	4,3,35,136,6,13
	.byte	'FCL',0
	.word	37459
	.byte	4,3,35,140,6,13
	.byte	'MHDS',0
	.word	39435
	.byte	4,3,35,144,6,13
	.byte	'LDTS',0
	.word	38928
	.byte	4,3,35,148,6,13
	.byte	'FSR',0
	.word	37646
	.byte	4,3,35,152,6,13
	.byte	'MHDF',0
	.word	39372
	.byte	4,3,35,156,6,13
	.byte	'TXRQ1',0
	.word	42349
	.byte	4,3,35,160,6,13
	.byte	'TXRQ2',0
	.word	42413
	.byte	4,3,35,164,6,13
	.byte	'TXRQ3',0
	.word	42477
	.byte	4,3,35,168,6,13
	.byte	'TXRQ4',0
	.word	42541
	.byte	4,3,35,172,6,13
	.byte	'NDAT1',0
	.word	39880
	.byte	4,3,35,176,6,13
	.byte	'NDAT2',0
	.word	39944
	.byte	4,3,35,180,6,13
	.byte	'NDAT3',0
	.word	40008
	.byte	4,3,35,184,6,13
	.byte	'NDAT4',0
	.word	40072
	.byte	4,3,35,188,6,13
	.byte	'MBSC1',0
	.word	39053
	.byte	4,3,35,192,6,13
	.byte	'MBSC2',0
	.word	39117
	.byte	4,3,35,196,6,13
	.byte	'MBSC3',0
	.word	39181
	.byte	4,3,35,200,6,13
	.byte	'MBSC4',0
	.word	39245
	.byte	4,3,35,204,6,13
	.byte	'reserved_350',0
	.word	42916
	.byte	88,3,35,208,6,13
	.byte	'NDIC1',0
	.word	40136
	.byte	4,3,35,168,7,13
	.byte	'NDIC2',0
	.word	40200
	.byte	4,3,35,172,7,13
	.byte	'NDIC3',0
	.word	40264
	.byte	4,3,35,176,7,13
	.byte	'NDIC4',0
	.word	40328
	.byte	4,3,35,180,7,13
	.byte	'MSIC1',0
	.word	39560
	.byte	4,3,35,184,7,13
	.byte	'MSIC2',0
	.word	39624
	.byte	4,3,35,188,7,13
	.byte	'MSIC3',0
	.word	39688
	.byte	4,3,35,192,7,13
	.byte	'MSIC4',0
	.word	39752
	.byte	4,3,35,196,7,13
	.byte	'reserved_3C8',0
	.word	42925
	.byte	40,3,35,200,7,13
	.byte	'CREL',0
	.word	36891
	.byte	4,3,35,240,7,13
	.byte	'ENDN',0
	.word	37333
	.byte	4,3,35,244,7,13
	.byte	'reserved_3F8',0
	.word	3357
	.byte	8,3,35,248,7,13
	.byte	'WRDS_1S',0
	.word	42934
	.byte	128,2,3,35,128,8,13
	.byte	'WRHS1',0
	.word	42668
	.byte	4,3,35,128,10,13
	.byte	'WRHS2',0
	.word	42732
	.byte	4,3,35,132,10,13
	.byte	'WRHS3',0
	.word	42796
	.byte	4,3,35,136,10,13
	.byte	'reserved_50C',0
	.word	1538
	.byte	4,3,35,140,10,13
	.byte	'IBCM',0
	.word	38423
	.byte	4,3,35,144,10,13
	.byte	'IBCR',0
	.word	38486
	.byte	4,3,35,148,10,13
	.byte	'reserved_518',0
	.word	42944
	.byte	232,1,3,35,152,10,13
	.byte	'RDDS_1S',0
	.word	42955
	.byte	128,2,3,35,128,12,13
	.byte	'RDHS1',0
	.word	41146
	.byte	4,3,35,128,14,13
	.byte	'RDHS2',0
	.word	41210
	.byte	4,3,35,132,14,13
	.byte	'RDHS3',0
	.word	41274
	.byte	4,3,35,136,14,13
	.byte	'MBS',0
	.word	38991
	.byte	4,3,35,140,14,13
	.byte	'OBCM',0
	.word	40517
	.byte	4,3,35,144,14,13
	.byte	'OBCR',0
	.word	40580
	.byte	4,3,35,148,14,13
	.byte	'reserved_718',0
	.word	42965
	.byte	216,2,3,35,152,14,13
	.byte	'OTSS',0
	.word	40830
	.byte	4,3,35,240,16,13
	.byte	'reserved_874',0
	.word	42976
	.byte	116,3,35,244,16,13
	.byte	'OCS',0
	.word	40643
	.byte	4,3,35,232,17,13
	.byte	'KRSTCLR',0
	.word	38800
	.byte	4,3,35,236,17,13
	.byte	'KRST1',0
	.word	38736
	.byte	4,3,35,240,17,13
	.byte	'KRST0',0
	.word	38672
	.byte	4,3,35,244,17,13
	.byte	'ACCEN1',0
	.word	36576
	.byte	4,3,35,248,17,13
	.byte	'ACCEN0',0
	.word	36511
	.byte	4,3,35,252,17,13
	.byte	'reserved_900',0
	.word	42985
	.byte	128,14,3,35,128,18,0,16
	.word	42996
	.byte	21
	.byte	'Ifx_ERAY',0,6,195,21,3
	.word	44949
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	493
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	493
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	510
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	45017
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	352
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8546
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	45083
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	45111
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	298
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	384
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	45111
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	45196
	.byte	17,9,90,9,1,18
	.byte	'IfxEray_NodeId_none',0,127,18
	.byte	'IfxEray_NodeId_a',0,0,18
	.byte	'IfxEray_NodeId_b',0,1,0,21
	.byte	'IfxEray_NodeId',0,9,95,3
	.word	45334
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7110
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7023
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3366
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1419
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2414
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1547
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2194
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1762
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1977
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6382
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6506
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6590
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6770
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5021
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5545
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5195
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5369
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6034
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	848
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4358
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4846
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4505
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4674
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5701
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	532
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4072
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3706
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2737
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3041
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7637
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7070
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3657
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1498
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2688
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1722
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2374
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1937
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2154
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6466
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6715
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6974
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6342
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5155
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5661
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5329
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5505
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1379
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5994
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4465
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4981
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4634
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4806
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	808
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4318
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4032
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3001
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3317
	.byte	16
	.word	7677
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	46741
	.byte	17,10,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,10,255,10,3
	.word	46761
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,11,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,11,79,3
	.word	46883
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,11,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,11,85,3
	.word	47440
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,11,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,11,94,3
	.word	47517
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,11,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,11,111,3
	.word	47653
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,11,114,16,4,11
	.byte	'CANDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,11,126,3
	.word	47933
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,11,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,11,135,1,3
	.word	48171
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,11,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,11,150,1,3
	.word	48299
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,11,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,11,165,1,3
	.word	48542
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,11,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,11,174,1,3
	.word	48777
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,11,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,11,181,1,3
	.word	48905
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,11,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,11,188,1,3
	.word	49005
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,11,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,11,202,1,3
	.word	49105
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,11,205,1,16,4,11
	.byte	'PWD',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,11,213,1,3
	.word	49313
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,11,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,11,225,1,3
	.word	49478
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,11,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,11,235,1,3
	.word	49661
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,11,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,11,129,2,3
	.word	49815
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,11,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,11,143,2,3
	.word	50179
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,11,146,2,16,4,11
	.byte	'POL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	510
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,11,159,2,3
	.word	50390
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,11,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,11,167,2,3
	.word	50642
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,11,170,2,16,4,11
	.byte	'ARI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,11,175,2,3
	.word	50760
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,11,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,11,185,2,3
	.word	50871
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,11,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,11,195,2,3
	.word	51034
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,11,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,11,205,2,3
	.word	51197
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,11,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,11,215,2,3
	.word	51355
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,11,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	510
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,11,232,2,3
	.word	51520
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,11,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	510
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,11,245,2,3
	.word	51849
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,11,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,11,255,2,3
	.word	52070
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,11,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,11,142,3,3
	.word	52233
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,11,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,11,152,3,3
	.word	52505
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,11,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,11,162,3,3
	.word	52658
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,11,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,11,172,3,3
	.word	52814
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,11,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,11,181,3,3
	.word	52976
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,11,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,11,191,3,3
	.word	53119
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,11,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,11,200,3,3
	.word	53284
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,11,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,11,211,3,3
	.word	53429
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,11,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,11,222,3,3
	.word	53610
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,11,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,11,232,3,3
	.word	53784
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,11,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,11,241,3,3
	.word	53944
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,11,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,11,130,4,3
	.word	54088
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,11,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,11,139,4,3
	.word	54362
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,11,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,11,149,4,3
	.word	54501
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,11,152,4,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	493
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	510
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	493
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,11,163,4,3
	.word	54664
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,11,166,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,11,174,4,3
	.word	54882
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,11,177,4,16,4,11
	.byte	'FS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,11,197,4,3
	.word	55045
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,11,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,11,205,4,3
	.word	55381
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,11,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,11,232,4,3
	.word	55488
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,11,235,4,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,11,240,4,3
	.word	55940
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,11,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,11,250,4,3
	.word	56039
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,11,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,11,131,5,3
	.word	56189
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,11,134,5,16,4,11
	.byte	'SEED',0,4
	.word	470
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,11,141,5,3
	.word	56338
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,11,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,11,149,5,3
	.word	56499
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,11,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,11,158,5,3
	.word	56629
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,11,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,11,166,5,3
	.word	56761
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,11,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	510
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,11,174,5,3
	.word	56876
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,11,177,5,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,11,185,5,3
	.word	56987
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,11,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,11,209,5,3
	.word	57145
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,11,212,5,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,11,217,5,3
	.word	57557
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,11,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	510
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,11,233,5,3
	.word	57658
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,11,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,11,242,5,3
	.word	57925
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,11,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,11,250,5,3
	.word	58061
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,11,253,5,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,11,132,6,3
	.word	58172
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,11,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,11,146,6,3
	.word	58305
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,11,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,11,166,6,3
	.word	58508
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,11,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,11,177,6,3
	.word	58864
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,11,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,11,184,6,3
	.word	59042
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,11,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,11,204,6,3
	.word	59142
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,11,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,11,215,6,3
	.word	59512
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,11,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,11,227,6,3
	.word	59698
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,11,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,11,241,6,3
	.word	59896
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,11,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,11,251,6,3
	.word	60129
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,11,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	493
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,11,153,7,3
	.word	60281
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,11,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,11,170,7,3
	.word	60848
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,11,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,11,187,7,3
	.word	61142
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,11,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,11,214,7,3
	.word	61420
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,11,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,11,230,7,3
	.word	61916
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,11,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,11,243,7,3
	.word	62229
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,11,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,11,129,8,3
	.word	62438
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,11,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,11,155,8,3
	.word	62649
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,11,158,8,16,4,11
	.byte	'HBT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,11,162,8,3
	.word	63081
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,11,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,11,178,8,3
	.word	63177
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,11,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,11,186,8,3
	.word	63437
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,11,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,11,198,8,3
	.word	63562
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,11,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,11,208,8,3
	.word	63759
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,11,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,11,218,8,3
	.word	63912
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,11,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,11,228,8,3
	.word	64065
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,11,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,11,238,8,3
	.word	64218
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,11,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	64373
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	64373
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	64373
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	64373
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,11,247,8,3
	.word	64389
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,11,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,11,134,9,3
	.word	64519
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,11,137,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,11,150,9,3
	.word	64757
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,11,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	64373
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	64373
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	64373
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	64373
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,11,159,9,3
	.word	64980
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,11,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,11,175,9,3
	.word	65106
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,11,178,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,11,191,9,3
	.word	65358
	.byte	12,11,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46883
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,11,204,9,3
	.word	65577
	.byte	12,11,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47440
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,11,212,9,3
	.word	65641
	.byte	12,11,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47517
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,11,220,9,3
	.word	65705
	.byte	12,11,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47653
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,11,228,9,3
	.word	65770
	.byte	12,11,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47933
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,11,236,9,3
	.word	65835
	.byte	12,11,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48171
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,11,244,9,3
	.word	65900
	.byte	12,11,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48299
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,11,252,9,3
	.word	65965
	.byte	12,11,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48542
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,11,132,10,3
	.word	66030
	.byte	12,11,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48777
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,11,140,10,3
	.word	66095
	.byte	12,11,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48905
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,11,148,10,3
	.word	66160
	.byte	12,11,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49005
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,11,156,10,3
	.word	66225
	.byte	12,11,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49105
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,11,164,10,3
	.word	66290
	.byte	12,11,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49313
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,11,172,10,3
	.word	66354
	.byte	12,11,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49478
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,11,180,10,3
	.word	66418
	.byte	12,11,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49661
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,11,188,10,3
	.word	66482
	.byte	12,11,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49815
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,11,196,10,3
	.word	66547
	.byte	12,11,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50179
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,11,204,10,3
	.word	66609
	.byte	12,11,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50390
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,11,212,10,3
	.word	66671
	.byte	12,11,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50642
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,11,220,10,3
	.word	66733
	.byte	12,11,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50760
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,11,228,10,3
	.word	66797
	.byte	12,11,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50871
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,11,236,10,3
	.word	66862
	.byte	12,11,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51034
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,11,244,10,3
	.word	66928
	.byte	12,11,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51197
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,11,252,10,3
	.word	66994
	.byte	12,11,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51355
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,11,132,11,3
	.word	67062
	.byte	12,11,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51520
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,11,140,11,3
	.word	67129
	.byte	12,11,143,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51849
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,11,148,11,3
	.word	67197
	.byte	12,11,151,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52070
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,11,156,11,3
	.word	67265
	.byte	12,11,159,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52233
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,11,164,11,3
	.word	67331
	.byte	12,11,167,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52505
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,11,172,11,3
	.word	67398
	.byte	12,11,175,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52658
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,11,180,11,3
	.word	67467
	.byte	12,11,183,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52814
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,11,188,11,3
	.word	67536
	.byte	12,11,191,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52976
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,11,196,11,3
	.word	67605
	.byte	12,11,199,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53119
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,11,204,11,3
	.word	67674
	.byte	12,11,207,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53284
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,11,212,11,3
	.word	67743
	.byte	12,11,215,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53429
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,11,220,11,3
	.word	67812
	.byte	12,11,223,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53610
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,11,228,11,3
	.word	67880
	.byte	12,11,231,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53784
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,11,236,11,3
	.word	67948
	.byte	12,11,239,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53944
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,11,244,11,3
	.word	68016
	.byte	12,11,247,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54088
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,11,252,11,3
	.word	68084
	.byte	12,11,255,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54362
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,11,132,12,3
	.word	68149
	.byte	12,11,135,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54501
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,11,140,12,3
	.word	68214
	.byte	12,11,143,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54664
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,11,148,12,3
	.word	68280
	.byte	12,11,151,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54882
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,11,156,12,3
	.word	68344
	.byte	12,11,159,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55045
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,11,164,12,3
	.word	68405
	.byte	12,11,167,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55381
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,11,172,12,3
	.word	68466
	.byte	12,11,175,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55488
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,11,180,12,3
	.word	68526
	.byte	12,11,183,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55940
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,11,188,12,3
	.word	68588
	.byte	12,11,191,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56039
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,11,196,12,3
	.word	68648
	.byte	12,11,199,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56189
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,11,204,12,3
	.word	68710
	.byte	12,11,207,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56338
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,11,212,12,3
	.word	68778
	.byte	12,11,215,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56499
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,11,220,12,3
	.word	68846
	.byte	12,11,223,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56629
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,11,228,12,3
	.word	68914
	.byte	12,11,231,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56761
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,11,236,12,3
	.word	68978
	.byte	12,11,239,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56876
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,11,244,12,3
	.word	69043
	.byte	12,11,247,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56987
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,11,252,12,3
	.word	69106
	.byte	12,11,255,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57145
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,11,132,13,3
	.word	69167
	.byte	12,11,135,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57557
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,11,140,13,3
	.word	69231
	.byte	12,11,143,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57658
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,11,148,13,3
	.word	69292
	.byte	12,11,151,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57925
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,11,156,13,3
	.word	69356
	.byte	12,11,159,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58061
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,11,164,13,3
	.word	69423
	.byte	12,11,167,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58172
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,11,172,13,3
	.word	69486
	.byte	12,11,175,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58305
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,11,180,13,3
	.word	69547
	.byte	12,11,183,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58508
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,11,188,13,3
	.word	69609
	.byte	12,11,191,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58864
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,11,196,13,3
	.word	69674
	.byte	12,11,199,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59042
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,11,204,13,3
	.word	69739
	.byte	12,11,207,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59142
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,11,212,13,3
	.word	69804
	.byte	12,11,215,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59512
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,11,220,13,3
	.word	69873
	.byte	12,11,223,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59698
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,11,228,13,3
	.word	69942
	.byte	12,11,231,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59896
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,11,236,13,3
	.word	70011
	.byte	12,11,239,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60129
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,11,244,13,3
	.word	70076
	.byte	12,11,247,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60281
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,11,252,13,3
	.word	70139
	.byte	12,11,255,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60848
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,11,132,14,3
	.word	70204
	.byte	12,11,135,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61142
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,11,140,14,3
	.word	70269
	.byte	12,11,143,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61420
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,11,148,14,3
	.word	70334
	.byte	12,11,151,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61916
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,11,156,14,3
	.word	70400
	.byte	12,11,159,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62438
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,11,164,14,3
	.word	70469
	.byte	12,11,167,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62229
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,11,172,14,3
	.word	70533
	.byte	12,11,175,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62649
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,11,180,14,3
	.word	70598
	.byte	12,11,183,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63081
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,11,188,14,3
	.word	70663
	.byte	12,11,191,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63177
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,11,196,14,3
	.word	70728
	.byte	12,11,199,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63437
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,11,204,14,3
	.word	70792
	.byte	12,11,207,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63562
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,11,212,14,3
	.word	70858
	.byte	12,11,215,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63759
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,11,220,14,3
	.word	70922
	.byte	12,11,223,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63912
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,11,228,14,3
	.word	70987
	.byte	12,11,231,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64065
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,11,236,14,3
	.word	71052
	.byte	12,11,239,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64218
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,11,244,14,3
	.word	71117
	.byte	12,11,247,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64389
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,11,252,14,3
	.word	71183
	.byte	12,11,255,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64519
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,11,132,15,3
	.word	71252
	.byte	12,11,135,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64757
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,11,140,15,3
	.word	71321
	.byte	12,11,143,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64980
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,11,148,15,3
	.word	71388
	.byte	12,11,151,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65106
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,11,156,15,3
	.word	71455
	.byte	12,11,159,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65358
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,11,164,15,3
	.word	71522
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,11,175,15,25,12,13
	.byte	'CON0',0
	.word	71183
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	71252
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	71321
	.byte	4,2,35,8,0,16
	.word	71587
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,11,180,15,3
	.word	71650
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,11,183,15,25,12,13
	.byte	'CON0',0
	.word	71388
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	71455
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	71522
	.byte	4,2,35,8,0,16
	.word	71679
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,11,188,15,3
	.word	71740
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	71767
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	71918
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	72162
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	72260
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8290
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8285
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	493
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	72725
	.byte	16
	.word	42996
	.byte	3
	.word	72785
	.byte	23,12,59,15,20,13
	.byte	'module',0
	.word	72790
	.byte	4,2,35,0,13
	.byte	'nodeId',0
	.word	45334
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	72725
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	45196
	.byte	1,2,35,16,0,24
	.word	72795
	.byte	21
	.byte	'IfxEray_Rxd_In',0,12,65,3
	.word	72862
	.byte	23,12,68,15,20,13
	.byte	'module',0
	.word	72790
	.byte	4,2,35,0,13
	.byte	'nodeId',0
	.word	45334
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	72725
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	71918
	.byte	1,2,35,16,0,24
	.word	72890
	.byte	21
	.byte	'IfxEray_Txd_Out',0,12,74,3
	.word	72957
	.byte	23,12,77,15,20,13
	.byte	'module',0
	.word	72790
	.byte	4,2,35,0,13
	.byte	'nodeId',0
	.word	45334
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	72725
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	71918
	.byte	1,2,35,16,0,24
	.word	72986
	.byte	21
	.byte	'IfxEray_Txen_Out',0,12,83,3
	.word	73053
.L60:
	.byte	24
	.word	72795
.L61:
	.byte	24
	.word	72795
.L62:
	.byte	24
	.word	72795
.L63:
	.byte	24
	.word	72795
.L64:
	.byte	24
	.word	72795
.L65:
	.byte	24
	.word	72795
.L66:
	.byte	24
	.word	72795
.L67:
	.byte	24
	.word	72795
.L68:
	.byte	24
	.word	72890
.L69:
	.byte	24
	.word	72890
.L70:
	.byte	24
	.word	72890
.L71:
	.byte	24
	.word	72890
.L72:
	.byte	24
	.word	72890
.L73:
	.byte	24
	.word	72890
.L74:
	.byte	24
	.word	72890
.L75:
	.byte	24
	.word	72890
.L76:
	.byte	24
	.word	72986
.L77:
	.byte	24
	.word	72986
.L78:
	.byte	24
	.word	72986
.L79:
	.byte	24
	.word	72986
.L80:
	.byte	24
	.word	72986
.L81:
	.byte	24
	.word	72986
.L82:
	.byte	24
	.word	72986
.L83:
	.byte	24
	.word	72986
	.byte	24
	.word	72795
	.byte	3
	.word	73203
	.byte	14,16
	.word	73208
	.byte	15,3,0,14,32
	.word	73213
	.byte	15,1,0
.L84:
	.byte	14,32
	.word	73222
	.byte	15,0,0,24
	.word	72890
	.byte	3
	.word	73240
	.byte	14,16
	.word	73245
	.byte	15,3,0,14,32
	.word	73250
	.byte	15,1,0
.L85:
	.byte	14,32
	.word	73259
	.byte	15,0,0,24
	.word	72986
	.byte	3
	.word	73277
	.byte	14,20
	.word	73282
	.byte	15,4,0,14,40
	.word	73287
	.byte	15,1,0
.L86:
	.byte	14,40
	.word	73296
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L88-.L87
.L87:
	.half	3
	.word	.L90-.L89
.L89:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0,0,0,0
	.byte	'IfxEray_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxEray_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxEray_PinMap.h',0,0,0,0,0
.L90:
.L88:
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDA0_P14_8_IN')
	.sect	'.debug_info'
.L6:
	.word	275
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDA0_P14_8_IN',0,5,48,16
	.word	.L60
	.byte	1,5,3
	.word	IfxEray0_RXDA0_P14_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDA0_P14_8_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDA1_P11_9_IN')
	.sect	'.debug_info'
.L8:
	.word	275
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDA1_P11_9_IN',0,5,49,16
	.word	.L61
	.byte	1,5,3
	.word	IfxEray0_RXDA1_P11_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDA1_P11_9_IN')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDA2_P02_1_IN')
	.sect	'.debug_info'
.L10:
	.word	275
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDA2_P02_1_IN',0,5,50,16
	.word	.L62
	.byte	1,5,3
	.word	IfxEray0_RXDA2_P02_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDA2_P02_1_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDA3_P14_1_IN')
	.sect	'.debug_info'
.L12:
	.word	275
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDA3_P14_1_IN',0,5,51,16
	.word	.L63
	.byte	1,5,3
	.word	IfxEray0_RXDA3_P14_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDA3_P14_1_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDB0_P14_7_IN')
	.sect	'.debug_info'
.L14:
	.word	275
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDB0_P14_7_IN',0,5,52,16
	.word	.L64
	.byte	1,5,3
	.word	IfxEray0_RXDB0_P14_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDB0_P14_7_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDB1_P11_10_IN')
	.sect	'.debug_info'
.L16:
	.word	276
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDB1_P11_10_IN',0,5,53,16
	.word	.L65
	.byte	1,5,3
	.word	IfxEray0_RXDB1_P11_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDB1_P11_10_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDB2_P02_3_IN')
	.sect	'.debug_info'
.L18:
	.word	275
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDB2_P02_3_IN',0,5,54,16
	.word	.L66
	.byte	1,5,3
	.word	IfxEray0_RXDB2_P02_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDB2_P02_3_IN')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_RXDB3_P14_1_IN')
	.sect	'.debug_info'
.L20:
	.word	275
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_RXDB3_P14_1_IN',0,5,55,16
	.word	.L67
	.byte	1,5,3
	.word	IfxEray0_RXDB3_P14_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_RXDB3_P14_1_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDA_P02_0_OUT')
	.sect	'.debug_info'
.L22:
	.word	275
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDA_P02_0_OUT',0,5,56,17
	.word	.L68
	.byte	1,5,3
	.word	IfxEray0_TXDA_P02_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDA_P02_0_OUT')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDA_P11_3_OUT')
	.sect	'.debug_info'
.L24:
	.word	275
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDA_P11_3_OUT',0,5,57,17
	.word	.L69
	.byte	1,5,3
	.word	IfxEray0_TXDA_P11_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDA_P11_3_OUT')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDA_P14_0_OUT')
	.sect	'.debug_info'
.L26:
	.word	275
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDA_P14_0_OUT',0,5,58,17
	.word	.L70
	.byte	1,5,3
	.word	IfxEray0_TXDA_P14_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDA_P14_0_OUT')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDA_P14_10_OUT')
	.sect	'.debug_info'
.L28:
	.word	276
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDA_P14_10_OUT',0,5,59,17
	.word	.L71
	.byte	1,5,3
	.word	IfxEray0_TXDA_P14_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDA_P14_10_OUT')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDB_P02_2_OUT')
	.sect	'.debug_info'
.L30:
	.word	275
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDB_P02_2_OUT',0,5,60,17
	.word	.L72
	.byte	1,5,3
	.word	IfxEray0_TXDB_P02_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDB_P02_2_OUT')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDB_P11_12_OUT')
	.sect	'.debug_info'
.L32:
	.word	276
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDB_P11_12_OUT',0,5,61,17
	.word	.L73
	.byte	1,5,3
	.word	IfxEray0_TXDB_P11_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDB_P11_12_OUT')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDB_P14_0_OUT')
	.sect	'.debug_info'
.L34:
	.word	275
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDB_P14_0_OUT',0,5,62,17
	.word	.L74
	.byte	1,5,3
	.word	IfxEray0_TXDB_P14_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDB_P14_0_OUT')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXDB_P14_5_OUT')
	.sect	'.debug_info'
.L36:
	.word	275
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXDB_P14_5_OUT',0,5,63,17
	.word	.L75
	.byte	1,5,3
	.word	IfxEray0_TXDB_P14_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXDB_P14_5_OUT')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENA_P02_4_OUT')
	.sect	'.debug_info'
.L38:
	.word	276
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENA_P02_4_OUT',0,5,64,18
	.word	.L76
	.byte	1,5,3
	.word	IfxEray0_TXENA_P02_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENA_P02_4_OUT')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENA_P11_6_OUT')
	.sect	'.debug_info'
.L40:
	.word	276
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENA_P11_6_OUT',0,5,65,18
	.word	.L77
	.byte	1,5,3
	.word	IfxEray0_TXENA_P11_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENA_P11_6_OUT')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENA_P14_9_OUT')
	.sect	'.debug_info'
.L42:
	.word	276
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENA_P14_9_OUT',0,5,66,18
	.word	.L78
	.byte	1,5,3
	.word	IfxEray0_TXENA_P14_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENA_P14_9_OUT')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENB_P02_5_OUT')
	.sect	'.debug_info'
.L44:
	.word	276
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENB_P02_5_OUT',0,5,67,18
	.word	.L79
	.byte	1,5,3
	.word	IfxEray0_TXENB_P02_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENB_P02_5_OUT')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENB_P11_11_OUT')
	.sect	'.debug_info'
.L46:
	.word	277
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENB_P11_11_OUT',0,5,68,18
	.word	.L80
	.byte	1,5,3
	.word	IfxEray0_TXENB_P11_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENB_P11_11_OUT')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENB_P11_6_OUT')
	.sect	'.debug_info'
.L48:
	.word	276
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENB_P11_6_OUT',0,5,69,18
	.word	.L81
	.byte	1,5,3
	.word	IfxEray0_TXENB_P11_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENB_P11_6_OUT')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENB_P14_6_OUT')
	.sect	'.debug_info'
.L50:
	.word	276
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENB_P14_6_OUT',0,5,70,18
	.word	.L82
	.byte	1,5,3
	.word	IfxEray0_TXENB_P14_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENB_P14_6_OUT')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray0_TXENB_P14_9_OUT')
	.sect	'.debug_info'
.L52:
	.word	276
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray0_TXENB_P14_9_OUT',0,5,71,18
	.word	.L83
	.byte	1,5,3
	.word	IfxEray0_TXENB_P14_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray0_TXENB_P14_9_OUT')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray_Rxd_In_pinTable')
	.sect	'.debug_info'
.L54:
	.word	275
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray_Rxd_In_pinTable',0,5,74,23
	.word	.L84
	.byte	1,5,3
	.word	IfxEray_Rxd_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray_Rxd_In_pinTable')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray_Txd_Out_pinTable')
	.sect	'.debug_info'
.L56:
	.word	276
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray_Txd_Out_pinTable',0,5,91,24
	.word	.L85
	.byte	1,5,3
	.word	IfxEray_Txd_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray_Txd_Out_pinTable')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEray_Txen_Out_pinTable')
	.sect	'.debug_info'
.L58:
	.word	277
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEray_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEray_Txen_Out_pinTable',0,5,108,25
	.word	.L86
	.byte	1,5,3
	.word	IfxEray_Txen_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEray_Txen_Out_pinTable')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
