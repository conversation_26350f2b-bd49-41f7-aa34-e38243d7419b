	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc32992a --dep-file=zf_device_mpu6050.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_mpu6050.src ../libraries/zf_device/zf_device_mpu6050.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_mpu6050.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_mpu6050.mpu6050_self1_check',code,cluster('mpu6050_self1_check')
	.sect	'.text.zf_device_mpu6050.mpu6050_self1_check'
	.align	2
	
; Function mpu6050_self1_check
.L34:
mpu6050_self1_check:	.type	func
	mov	d15,#0
.L115:
	mov	d9,#0
.L117:
	mov	d8,#0
.L118:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#107
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L243:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#25
	mov	d5,#7
	call	soft_iic_write_8bit_register
.L244:
	j	.L2
.L3:
	mov	d0,d8
	add	d8,#1
.L119:
	extr.u	d8,d8,#0,#16
.L120:
	mov	d15,#255
.L116:
	jge.u	d15,d0,.L4
.L245:
	mov	d9,#1
.L246:
	j	.L5
.L4:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#25
	call	soft_iic_read_8bit_register
.L121:
	mov	d15,d2
.L122:
	mov	d4,#10
	call	system_delay_ms
.L2:
	jne	d15,#7,.L3
.L5:
	mov	d2,d9
.L123:
	j	.L6
.L6:
	ret
.L109:
	
__mpu6050_self1_check_function_end:
	.size	mpu6050_self1_check,__mpu6050_self1_check_function_end-mpu6050_self1_check
.L78:
	; End of function
	
	.sdecl	'.text.zf_device_mpu6050.mpu6050_get_acc',code,cluster('mpu6050_get_acc')
	.sect	'.text.zf_device_mpu6050.mpu6050_get_acc'
	.align	2
	
	.global	mpu6050_get_acc
; Function mpu6050_get_acc
.L36:
mpu6050_get_acc:	.type	func
	sub.a	a10,#8
.L124:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#59
	lea	a5,[a10]0
	mov	d5,#6
	call	soft_iic_read_8bit_registers
.L138:
	movh.a	a15,#@his(mpu6050_acc_x)
	lea	a15,[a15]@los(mpu6050_acc_x)
.L139:
	ld.bu	d15,[a10]
.L140:
	sha	d0,d15,#8
.L141:
	ld.bu	d15,[a10]1
.L142:
	or	d0,d15
.L143:
	st.h	[a15],d0
.L144:
	movh.a	a15,#@his(mpu6050_acc_y)
	lea	a15,[a15]@los(mpu6050_acc_y)
.L145:
	ld.bu	d15,[a10]2
.L146:
	sha	d0,d15,#8
.L147:
	ld.bu	d15,[a10]3
.L148:
	or	d0,d15
.L149:
	st.h	[a15],d0
.L150:
	movh.a	a15,#@his(mpu6050_acc_z)
	lea	a15,[a15]@los(mpu6050_acc_z)
.L151:
	ld.bu	d15,[a10]4
.L152:
	sha	d0,d15,#8
.L153:
	ld.bu	d15,[a10]5
.L154:
	or	d0,d15
.L155:
	st.h	[a15],d0
.L156:
	ret
.L93:
	
__mpu6050_get_acc_function_end:
	.size	mpu6050_get_acc,__mpu6050_get_acc_function_end-mpu6050_get_acc
.L53:
	; End of function
	
	.sdecl	'.text.zf_device_mpu6050.mpu6050_get_gyro',code,cluster('mpu6050_get_gyro')
	.sect	'.text.zf_device_mpu6050.mpu6050_get_gyro'
	.align	2
	
	.global	mpu6050_get_gyro
; Function mpu6050_get_gyro
.L38:
mpu6050_get_gyro:	.type	func
	sub.a	a10,#8
.L125:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#67
	lea	a5,[a10]0
	mov	d5,#6
	call	soft_iic_read_8bit_registers
.L161:
	movh.a	a15,#@his(mpu6050_gyro_x)
	lea	a15,[a15]@los(mpu6050_gyro_x)
.L162:
	ld.bu	d15,[a10]
.L163:
	sha	d0,d15,#8
.L164:
	ld.bu	d15,[a10]1
.L165:
	or	d0,d15
.L166:
	st.h	[a15],d0
.L167:
	movh.a	a15,#@his(mpu6050_gyro_y)
	lea	a15,[a15]@los(mpu6050_gyro_y)
.L168:
	ld.bu	d15,[a10]2
.L169:
	sha	d0,d15,#8
.L170:
	ld.bu	d15,[a10]3
.L171:
	or	d0,d15
.L172:
	st.h	[a15],d0
.L173:
	movh.a	a15,#@his(mpu6050_gyro_z)
	lea	a15,[a15]@los(mpu6050_gyro_z)
.L174:
	ld.bu	d15,[a10]4
.L175:
	sha	d0,d15,#8
.L176:
	ld.bu	d15,[a10]5
.L177:
	or	d0,d15
.L178:
	st.h	[a15],d0
.L179:
	ret
.L96:
	
__mpu6050_get_gyro_function_end:
	.size	mpu6050_get_gyro,__mpu6050_get_gyro_function_end-mpu6050_get_gyro
.L58:
	; End of function
	
	.sdecl	'.text.zf_device_mpu6050.mpu6050_acc_transition',code,cluster('mpu6050_acc_transition')
	.sect	'.text.zf_device_mpu6050.mpu6050_acc_transition'
	.align	2
	
	.global	mpu6050_acc_transition
; Function mpu6050_acc_transition
.L40:
mpu6050_acc_transition:	.type	func
	mov	d2,#0
.L126:
	mov	d15,#16
.L184:
	mov	d0,#0
	jeq	d15,d0,.L7
.L185:
	mov	d0,#8
	jeq	d15,d0,.L8
.L186:
	mov	d0,#16
	jeq	d15,d0,.L9
.L187:
	mov	d0,#24
	jeq	d15,d0,.L10
	j	.L11
.L7:
	itof	d15,d4
.L188:
	movh	d0,#18048
.L189:
	div.f	d2,d15,d0
.L190:
	j	.L12
.L8:
	itof	d15,d4
.L191:
	movh	d0,#17920
.L192:
	div.f	d2,d15,d0
.L193:
	j	.L13
.L9:
	itof	d15,d4
.L194:
	movh	d0,#17792
.L195:
	div.f	d2,d15,d0
.L196:
	j	.L14
.L10:
	itof	d15,d4
.L197:
	movh	d0,#17664
.L198:
	div.f	d2,d15,d0
.L199:
	j	.L15
.L11:
	j	.L16
.L16:
.L15:
.L14:
.L13:
.L12:
	j	.L17
.L17:
	ret
.L99:
	
__mpu6050_acc_transition_function_end:
	.size	mpu6050_acc_transition,__mpu6050_acc_transition_function_end-mpu6050_acc_transition
.L63:
	; End of function
	
	.sdecl	'.text.zf_device_mpu6050.mpu6050_gyro_transition',code,cluster('mpu6050_gyro_transition')
	.sect	'.text.zf_device_mpu6050.mpu6050_gyro_transition'
	.align	2
	
	.global	mpu6050_gyro_transition
; Function mpu6050_gyro_transition
.L42:
mpu6050_gyro_transition:	.type	func
	mov	d2,#0
.L127:
	mov	d15,#24
.L204:
	mov	d0,#0
	jeq	d15,d0,.L18
.L205:
	mov	d0,#8
	jeq	d15,d0,.L19
.L206:
	mov	d0,#16
	jeq	d15,d0,.L20
.L207:
	mov	d0,#24
	jeq	d15,d0,.L21
	j	.L22
.L18:
	itof	d15,d4
.L208:
	movh	d0,#17155
.L209:
	div.f	d2,d15,d0
.L210:
	j	.L23
.L19:
	itof	d15,d4
.L211:
	movh	d0,#17027
.L212:
	div.f	d2,d15,d0
.L213:
	j	.L24
.L20:
	itof	d15,d4
.L214:
	mov	d0,#13107
	addih	d0,d0,#16899
.L215:
	div.f	d2,d15,d0
.L216:
	j	.L25
.L21:
	itof	d15,d4
.L217:
	mov	d0,#13107
	addih	d0,d0,#16771
.L218:
	div.f	d2,d15,d0
.L219:
	j	.L26
.L22:
	j	.L27
.L27:
.L26:
.L25:
.L24:
.L23:
	j	.L28
.L28:
	ret
.L103:
	
__mpu6050_gyro_transition_function_end:
	.size	mpu6050_gyro_transition,__mpu6050_gyro_transition_function_end-mpu6050_gyro_transition
.L68:
	; End of function
	
	.sdecl	'.text.zf_device_mpu6050.mpu6050_init',code,cluster('mpu6050_init')
	.sect	'.text.zf_device_mpu6050.mpu6050_init'
	.align	2
	
	.global	mpu6050_init
; Function mpu6050_init
.L44:
mpu6050_init:	.type	func
	mov	d15,#0
.L128:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
.L224:
	mov	d4,#104
.L225:
	mov	d5,#59
.L226:
	mov	d6,#651
.L227:
	mov	d7,#654
	call	soft_iic_init
.L228:
	mov	d4,#100
	call	system_delay_ms
.L29:
	call	mpu6050_self1_check
.L229:
	jeq	d2,#0,.L30
.L230:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#201
	call	debug_log_handler
.L231:
	mov	d15,#1
.L232:
	j	.L31
.L30:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#107
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L233:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#25
	mov	d5,#7
	call	soft_iic_write_8bit_register
.L234:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#26
	mov	d5,#4
	call	soft_iic_write_8bit_register
.L235:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#27
	mov	d5,#24
	call	soft_iic_write_8bit_register
.L236:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#28
	mov	d5,#16
	call	soft_iic_write_8bit_register
.L237:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#106
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L238:
	movh.a	a4,#@his(mpu6050_iic_struct)
	lea	a4,[a4]@los(mpu6050_iic_struct)
	mov	d4,#55
	mov	d5,#2
	call	soft_iic_write_8bit_register
.L31:
	mov	d2,d15
.L129:
	j	.L32
.L32:
	ret
.L107:
	
__mpu6050_init_function_end:
	.size	mpu6050_init,__mpu6050_init_function_end-mpu6050_init
.L73:
	; End of function
	
	.sdecl	'.data.zf_device_mpu6050.mpu6050_gyro_x',data,cluster('mpu6050_gyro_x')
	.sect	'.data.zf_device_mpu6050.mpu6050_gyro_x'
	.global	mpu6050_gyro_x
	.align	2
mpu6050_gyro_x:	.type	object
	.size	mpu6050_gyro_x,2
	.space	2
	.sdecl	'.data.zf_device_mpu6050.mpu6050_gyro_y',data,cluster('mpu6050_gyro_y')
	.sect	'.data.zf_device_mpu6050.mpu6050_gyro_y'
	.global	mpu6050_gyro_y
	.align	2
mpu6050_gyro_y:	.type	object
	.size	mpu6050_gyro_y,2
	.space	2
	.sdecl	'.data.zf_device_mpu6050.mpu6050_gyro_z',data,cluster('mpu6050_gyro_z')
	.sect	'.data.zf_device_mpu6050.mpu6050_gyro_z'
	.global	mpu6050_gyro_z
	.align	2
mpu6050_gyro_z:	.type	object
	.size	mpu6050_gyro_z,2
	.space	2
	.sdecl	'.data.zf_device_mpu6050.mpu6050_acc_x',data,cluster('mpu6050_acc_x')
	.sect	'.data.zf_device_mpu6050.mpu6050_acc_x'
	.global	mpu6050_acc_x
	.align	2
mpu6050_acc_x:	.type	object
	.size	mpu6050_acc_x,2
	.space	2
	.sdecl	'.data.zf_device_mpu6050.mpu6050_acc_y',data,cluster('mpu6050_acc_y')
	.sect	'.data.zf_device_mpu6050.mpu6050_acc_y'
	.global	mpu6050_acc_y
	.align	2
mpu6050_acc_y:	.type	object
	.size	mpu6050_acc_y,2
	.space	2
	.sdecl	'.data.zf_device_mpu6050.mpu6050_acc_z',data,cluster('mpu6050_acc_z')
	.sect	'.data.zf_device_mpu6050.mpu6050_acc_z'
	.global	mpu6050_acc_z
	.align	2
mpu6050_acc_z:	.type	object
	.size	mpu6050_acc_z,2
	.space	2
	.sdecl	'.bss.zf_device_mpu6050.mpu6050_iic_struct',data,cluster('mpu6050_iic_struct')
	.sect	'.bss.zf_device_mpu6050.mpu6050_iic_struct'
	.align	4
mpu6050_iic_struct:	.type	object
	.size	mpu6050_iic_struct,24
	.space	24
	.sdecl	'.rodata.zf_device_mpu6050..1.str',data,rom
	.sect	'.rodata.zf_device_mpu6050..1.str'
.1.str:	.type	object
	.size	.1.str,26
	.byte	77,80,85,54,48,53,48,32
	.byte	115,101,108,102,32,99,104,101
	.byte	99,107,32,101,114,114,111,114
	.byte	46
	.space	1
	.sdecl	'.rodata.zf_device_mpu6050..2.str',data,rom
	.sect	'.rodata.zf_device_mpu6050..2.str'
.2.str:	.type	object
	.size	.2.str,43
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,109,112,117,54,48,53,48
	.byte	46,99
	.space	1
	.calls	'mpu6050_self1_check','soft_iic_write_8bit_register'
	.calls	'mpu6050_self1_check','soft_iic_read_8bit_register'
	.calls	'mpu6050_self1_check','system_delay_ms'
	.calls	'mpu6050_get_acc','soft_iic_read_8bit_registers'
	.calls	'mpu6050_get_gyro','soft_iic_read_8bit_registers'
	.calls	'mpu6050_init','soft_iic_init'
	.calls	'mpu6050_init','system_delay_ms'
	.calls	'mpu6050_init','mpu6050_self1_check'
	.calls	'mpu6050_init','debug_log_handler'
	.calls	'mpu6050_init','soft_iic_write_8bit_register'
	.calls	'mpu6050_self1_check','',0
	.calls	'mpu6050_get_acc','',8
	.calls	'mpu6050_get_gyro','',8
	.calls	'mpu6050_acc_transition','',0
	.calls	'mpu6050_gyro_transition','',0
	.extern	debug_log_handler
	.extern	system_delay_ms
	.extern	soft_iic_write_8bit_register
	.extern	soft_iic_read_8bit_register
	.extern	soft_iic_read_8bit_registers
	.extern	soft_iic_init
	.calls	'mpu6050_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L46:
	.word	38902
	.half	3
	.word	.L47
	.byte	4
.L45:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L48
	.byte	2,1,1,3
	.word	205
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	208
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L98:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	253
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	265
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	345
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	319
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	351
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	351
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	319
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L106:
	.byte	7
	.byte	'unsigned char',0,1,8
.L112:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	499
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	460
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	815
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1386
	.byte	4,2,35,0,0,14,4
	.word	460
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	460
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	460
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	460
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	460
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	460
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1514
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	460
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	460
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	460
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	460
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	460
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1729
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	460
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	460
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	460
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	460
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	460
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1944
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	460
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	460
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	460
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	460
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	460
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2161
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2381
	.byte	4,2,35,0,0,14,24
	.word	460
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	460
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	460
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	460
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	460
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	460
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	460
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	460
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2704
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	460
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	460
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	460
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	460
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	460
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	460
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	460
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3008
	.byte	4,2,35,0,0,14,8
	.word	460
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3333
	.byte	4,2,35,0,0,14,12
	.word	460
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3673
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	437
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4039
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	437
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4325
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	437
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4472
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	437
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4641
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	477
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4813
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	477
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	477
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4988
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	437
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	437
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	460
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5336
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	437
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	460
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5512
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5668
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	477
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	460
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6001
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6349
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	460
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	437
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6473
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6557
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	460
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6737
	.byte	4,2,35,0,0,14,76
	.word	460
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	437
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6990
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	460
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7077
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	775
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1346
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1465
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1505
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1689
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1904
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2121
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2341
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1505
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2655
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2695
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2968
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3284
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3324
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3624
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3664
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	3999
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4285
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3324
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4432
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4601
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4773
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4948
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5122
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5296
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5472
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5628
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5961
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6309
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3324
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6433
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6682
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6941
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6981
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7037
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7604
	.byte	4,3,35,252,1,0,16
	.word	7644
	.byte	3
	.word	8247
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8252
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	460
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8257
	.byte	6,0,7
	.byte	'char',0,1,6,3
	.word	8438
	.byte	19
	.byte	'debug_log_handler',0,5,113,9,1,1,1,1,5
	.byte	'pass',0,5,113,47
	.word	460
	.byte	5
	.byte	'str',0,5,113,59
	.word	8446
	.byte	5
	.byte	'file',0,5,113,70
	.word	8446
	.byte	5
	.byte	'line',0,5,113,80
	.word	453
	.byte	0,7
	.byte	'unsigned long int',0,4,7,19
	.byte	'system_delay_ms',0,6,46,9,1,1,1,1,5
	.byte	'time',0,6,46,45
	.word	8529
	.byte	0,20
	.word	213
	.byte	21
	.word	239
	.byte	6,0,20
	.word	274
	.byte	21
	.word	306
	.byte	6,0,20
	.word	356
	.byte	21
	.word	375
	.byte	6,0,20
	.word	391
	.byte	21
	.word	406
	.byte	21
	.word	420
	.byte	6,0,20
	.word	8360
	.byte	21
	.word	8388
	.byte	21
	.word	8402
	.byte	21
	.word	8420
	.byte	6,0
.L114:
	.byte	22,7,42,9,24,13
	.byte	'scl_pin',0
	.word	8529
	.byte	4,2,35,0,13
	.byte	'sda_pin',0
	.word	8529
	.byte	4,2,35,4,13
	.byte	'addr',0
	.word	460
	.byte	1,2,35,8,13
	.byte	'delay',0
	.word	8529
	.byte	4,2,35,10,13
	.byte	'iic_scl',0
	.word	351
	.byte	4,2,35,16,13
	.byte	'iic_sda',0
	.word	351
	.byte	4,2,35,20,0,3
	.word	8663
	.byte	23
	.word	460
	.byte	23
	.word	460
	.byte	19
	.byte	'soft_iic_write_8bit_register',0,7,59,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,7,59,68
	.word	8766
	.byte	5
	.byte	'register_name',0,7,59,94
	.word	8771
	.byte	5
	.byte	'data',0,7,59,121
	.word	8776
	.byte	0,23
	.word	460
	.byte	24
	.byte	'soft_iic_read_8bit_register',0,7,71,13
	.word	460
	.byte	1,1,1,1,5
	.byte	'soft_iic_obj',0,7,71,68
	.word	8766
	.byte	5
	.byte	'register_name',0,7,71,94
	.word	8875
	.byte	0,23
	.word	460
	.byte	3
	.word	460
	.byte	19
	.byte	'soft_iic_read_8bit_registers',0,7,72,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,7,72,68
	.word	8766
	.byte	5
	.byte	'register_name',0,7,72,94
	.word	8964
	.byte	5
	.byte	'data',0,7,72,116
	.word	8969
	.byte	5
	.byte	'len',0,7,72,129,1
	.word	8529
	.byte	0,17,8,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,19
	.byte	'soft_iic_init',0,7,83,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,7,83,68
	.word	8766
	.byte	5
	.byte	'addr',0,7,83,88
	.word	460
	.byte	5
	.byte	'delay',0,7,83,101
	.word	8529
	.byte	5
	.byte	'scl_pin',0,7,83,122
	.word	9081
	.byte	5
	.byte	'sda_pin',0,7,83,145,1
	.word	9081
	.byte	0
.L94:
	.byte	14,6
	.word	460
	.byte	15,5,0
.L100:
	.byte	7
	.byte	'short int',0,2,5,25
	.byte	'__wchar_t',0,9,1,1
	.word	11134
	.byte	25
	.byte	'__size_t',0,9,1,1
	.word	437
	.byte	25
	.byte	'__ptrdiff_t',0,9,1,1
	.word	453
	.byte	26,1,3
	.word	11202
	.byte	25
	.byte	'__codeptr',0,9,1,1
	.word	11204
	.byte	25
	.byte	'__intptr_t',0,9,1,1
	.word	453
	.byte	25
	.byte	'__uintptr_t',0,9,1,1
	.word	437
	.byte	25
	.byte	'_iob_flag_t',0,10,82,25
	.word	477
	.byte	25
	.byte	'boolean',0,11,101,29
	.word	460
	.byte	25
	.byte	'uint8',0,11,105,29
	.word	460
	.byte	25
	.byte	'uint16',0,11,109,29
	.word	477
	.byte	25
	.byte	'uint32',0,11,113,29
	.word	8529
	.byte	25
	.byte	'uint64',0,11,118,29
	.word	319
	.byte	25
	.byte	'sint16',0,11,126,29
	.word	11134
	.byte	7
	.byte	'long int',0,4,5,25
	.byte	'sint32',0,11,131,1,29
	.word	11376
	.byte	7
	.byte	'long long int',0,8,5,25
	.byte	'sint64',0,11,138,1,29
	.word	11404
	.byte	25
	.byte	'float32',0,11,167,1,29
	.word	265
	.byte	25
	.byte	'pvoid',0,12,57,28
	.word	351
	.byte	25
	.byte	'Ifx_TickTime',0,12,79,28
	.word	11404
	.byte	7
	.byte	'char',0,1,6,25
	.byte	'int8',0,13,54,29
	.word	11489
	.byte	25
	.byte	'int16',0,13,55,29
	.word	11134
	.byte	25
	.byte	'int32',0,13,56,29
	.word	453
	.byte	25
	.byte	'int64',0,13,57,29
	.word	11404
	.byte	25
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7077
	.byte	25
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6990
	.byte	25
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3333
	.byte	25
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1386
	.byte	25
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2381
	.byte	25
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1514
	.byte	25
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2161
	.byte	25
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1729
	.byte	25
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1944
	.byte	25
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6349
	.byte	25
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6473
	.byte	25
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6557
	.byte	25
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6737
	.byte	25
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4988
	.byte	25
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5512
	.byte	25
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5162
	.byte	25
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5336
	.byte	25
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6001
	.byte	25
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	815
	.byte	25
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4325
	.byte	25
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4813
	.byte	25
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4472
	.byte	25
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4641
	.byte	25
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5668
	.byte	25
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	499
	.byte	25
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4039
	.byte	25
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3673
	.byte	25
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2704
	.byte	25
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3008
	.byte	25
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7604
	.byte	25
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7037
	.byte	25
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3624
	.byte	25
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1465
	.byte	25
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2655
	.byte	25
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1689
	.byte	25
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2341
	.byte	25
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1904
	.byte	25
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2121
	.byte	25
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6433
	.byte	25
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6682
	.byte	25
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6941
	.byte	25
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6309
	.byte	25
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5122
	.byte	25
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5628
	.byte	25
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5296
	.byte	25
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5472
	.byte	25
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1346
	.byte	25
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5961
	.byte	25
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4432
	.byte	25
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4948
	.byte	25
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4601
	.byte	25
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4773
	.byte	25
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	775
	.byte	25
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4285
	.byte	25
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	3999
	.byte	25
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2968
	.byte	25
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3284
	.byte	16
	.word	7644
	.byte	25
	.byte	'Ifx_P',0,4,139,6,3
	.word	12870
	.byte	17,14,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,25
	.byte	'IfxScu_WDTCON1_IR',0,14,255,10,3
	.word	12890
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_ACCEN0_Bits',0,15,79,3
	.word	13012
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	437
	.byte	32,0,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1_Bits',0,15,85,3
	.word	13569
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,15,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	437
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,15,94,3
	.word	13646
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,15,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	460
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	460
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	460
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	460
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	460
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	460
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	460
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	460
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	460
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	460
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON0_Bits',0,15,111,3
	.word	13782
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,15,114,16,4,11
	.byte	'CANDIV',0,1
	.word	460
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	460
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	460
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	460
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	460
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	460
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	460
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	460
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON1_Bits',0,15,126,3
	.word	14062
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,15,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	460
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	437
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON2_Bits',0,15,135,1,3
	.word	14300
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,15,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	460
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	460
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	460
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	460
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	460
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON3_Bits',0,15,150,1,3
	.word	14428
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,15,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	460
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	460
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	460
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	460
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	460
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON4_Bits',0,15,165,1,3
	.word	14671
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,15,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	460
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	437
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON5_Bits',0,15,174,1,3
	.word	14906
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,15,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	460
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	437
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6_Bits',0,15,181,1,3
	.word	15034
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,15,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	460
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	437
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7_Bits',0,15,188,1,3
	.word	15134
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,15,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	460
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	460
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	460
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	460
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	460
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CHIPID_Bits',0,15,202,1,3
	.word	15234
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,15,205,1,16,4,11
	.byte	'PWD',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	437
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSCON_Bits',0,15,213,1,3
	.word	15442
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,15,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	477
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	460
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	477
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	460
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSLIM_Bits',0,15,225,1,3
	.word	15607
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,15,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	477
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	460
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,15,235,1,3
	.word	15790
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,15,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	460
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	460
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	437
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	460
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	460
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EICR_Bits',0,15,129,2,3
	.word	15944
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,15,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	437
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR_Bits',0,15,143,2,3
	.word	16308
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,15,146,2,16,4,11
	.byte	'POL',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	477
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	460
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	460
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	460
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	460
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_EMSR_Bits',0,15,159,2,3
	.word	16519
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,15,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	477
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	437
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG_Bits',0,15,167,2,3
	.word	16771
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,15,170,2,16,4,11
	.byte	'ARI',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG_Bits',0,15,175,2,3
	.word	16889
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,15,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	437
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR13CON_Bits',0,15,185,2,3
	.word	17000
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,15,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	437
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR33CON_Bits',0,15,195,2,3
	.word	17163
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,15,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,15,205,2,3
	.word	17326
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,15,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,15,215,2,3
	.word	17484
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,15,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	460
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	460
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	460
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	460
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	460
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	460
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	460
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	460
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	460
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	477
	.byte	10,0,2,35,2,0,25
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,15,232,2,3
	.word	17649
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,15,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	477
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	460
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	460
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	477
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	460
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,15,245,2,3
	.word	17978
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,15,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROVMON_Bits',0,15,255,2,3
	.word	18199
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,15,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	437
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,15,142,3,3
	.word	18362
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,15,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,15,152,3,3
	.word	18634
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,15,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,15,162,3,3
	.word	18787
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,15,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,15,172,3,3
	.word	18943
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,15,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,15,181,3,3
	.word	19105
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,15,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,15,191,3,3
	.word	19248
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,15,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,15,200,3,3
	.word	19413
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,15,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	477
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	460
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	460
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,15,211,3,3
	.word	19558
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,15,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	460
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,15,222,3,3
	.word	19739
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,15,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,15,232,3,3
	.word	19913
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,15,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	437
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,15,241,3,3
	.word	20073
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,15,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	437
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,15,130,4,3
	.word	20217
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,15,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,15,139,4,3
	.word	20491
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,15,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,15,149,4,3
	.word	20630
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,15,152,4,16,4,11
	.byte	'EN0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	460
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	477
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	460
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	460
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	460
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_EXTCON_Bits',0,15,163,4,3
	.word	20793
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,15,166,4,16,4,11
	.byte	'STEP',0,2
	.word	477
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	460
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	477
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	460
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_FDR_Bits',0,15,174,4,3
	.word	21011
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,15,177,4,16,4,11
	.byte	'FS0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_FMR_Bits',0,15,197,4,3
	.word	21174
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,15,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_ID_Bits',0,15,205,4,3
	.word	21510
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,15,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	460
	.byte	2,0,2,35,3,0,25
	.byte	'Ifx_SCU_IGCR_Bits',0,15,232,4,3
	.word	21617
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,15,235,4,16,4,11
	.byte	'P0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_IN_Bits',0,15,240,4,3
	.word	22069
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,15,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	460
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	460
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_IOCR_Bits',0,15,250,4,3
	.word	22168
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,15,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	477
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,15,131,5,3
	.word	22318
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,15,134,5,16,4,11
	.byte	'SEED',0,4
	.word	437
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	460
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,15,141,5,3
	.word	22467
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,15,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	437
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,15,149,5,3
	.word	22628
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,15,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	477
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	477
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LCLCON_Bits',0,15,158,5,3
	.word	22758
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,15,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST_Bits',0,15,166,5,3
	.word	22890
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,15,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	460
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	477
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_MANID_Bits',0,15,174,5,3
	.word	23005
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,15,177,5,16,4,11
	.byte	'PS0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	477
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	477
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_OMR_Bits',0,15,185,5,3
	.word	23116
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,15,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	460
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	460
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	460
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	460
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	460
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	460
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_OSCCON_Bits',0,15,209,5,3
	.word	23274
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,15,212,5,16,4,11
	.byte	'P0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_OUT_Bits',0,15,217,5,3
	.word	23686
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,15,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	477
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	460
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	460
	.byte	6,0,2,35,3,0,25
	.byte	'Ifx_SCU_OVCCON_Bits',0,15,233,5,3
	.word	23787
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,15,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	437
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,15,242,5,3
	.word	24054
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,15,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC_Bits',0,15,250,5,3
	.word	24190
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,15,253,5,16,4,11
	.byte	'PD0',0,1
	.word	460
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	460
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	437
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDR_Bits',0,15,132,6,3
	.word	24301
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,15,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	437
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR_Bits',0,15,146,6,3
	.word	24434
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,15,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	477
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	460
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	460
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	460
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	460
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLCON0_Bits',0,15,166,6,3
	.word	24637
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,15,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	460
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	460
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	460
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	477
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON1_Bits',0,15,177,6,3
	.word	24993
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,15,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	477
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON2_Bits',0,15,184,6,3
	.word	25171
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,15,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	477
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	460
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	460
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	460
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	460
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,15,204,6,3
	.word	25271
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,15,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	460
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	460
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	460
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	460
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	477
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,15,215,6,3
	.word	25641
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,15,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	437
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,15,227,6,3
	.word	25827
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,15,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	437
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,15,241,6,3
	.word	26025
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,15,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	460
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	460
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	437
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR_Bits',0,15,251,6,3
	.word	26258
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,15,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	460
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	460
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	460
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	460
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	460
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	460
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	460
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,15,153,7,3
	.word	26410
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,15,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	460
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	460
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	460
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	460
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,15,170,7,3
	.word	26977
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,15,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	460
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	460
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	460
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	460
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	460
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	460
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	460
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,15,187,7,3
	.word	27271
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,15,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	460
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	460
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	460
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	460
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	460
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	477
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	460
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	460
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,15,214,7,3
	.word	27549
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,15,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	477
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,15,230,7,3
	.word	28045
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,15,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	477
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	460
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	460
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	460
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON2_Bits',0,15,243,7,3
	.word	28358
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,15,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	460
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	460
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	460
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	460
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	460
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	460
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	460
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	460
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON_Bits',0,15,129,8,3
	.word	28567
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,15,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	460
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	460
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	460
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	460
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	460
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	460
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	460
	.byte	3,0,2,35,3,0,25
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,15,155,8,3
	.word	28778
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,15,158,8,16,4,11
	.byte	'HBT',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	437
	.byte	31,0,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON_Bits',0,15,162,8,3
	.word	29210
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,15,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	460
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	460
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	460
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	460
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	460
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	460
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	460
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	460
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	460
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	460
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	460
	.byte	7,0,2,35,3,0,25
	.byte	'Ifx_SCU_STSTAT_Bits',0,15,178,8,3
	.word	29306
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,15,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	437
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,15,186,8,3
	.word	29566
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,15,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	460
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	460
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	437
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON_Bits',0,15,198,8,3
	.word	29691
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,15,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	437
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,15,208,8,3
	.word	29888
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,15,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	437
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,15,218,8,3
	.word	30041
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,15,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	437
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET_Bits',0,15,228,8,3
	.word	30194
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,15,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	437
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,15,238,8,3
	.word	30347
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,15,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	30502
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30502
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30502
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30502
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,15,247,8,3
	.word	30518
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,15,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	460
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	460
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,15,134,9,3
	.word	30648
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,15,137,9,16,4,11
	.byte	'AE',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	460
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,15,150,9,3
	.word	30886
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,15,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	30502
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30502
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30502
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30502
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,15,159,9,3
	.word	31109
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,15,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	460
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,15,175,9,3
	.word	31235
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,15,178,9,16,4,11
	.byte	'AE',0,1
	.word	460
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	460
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	460
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	460
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	460
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	460
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	460
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	460
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	460
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	460
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	477
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,15,191,9,3
	.word	31487
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13012
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN0',0,15,204,9,3
	.word	31706
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13569
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1',0,15,212,9,3
	.word	31770
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13646
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS',0,15,220,9,3
	.word	31834
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13782
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON0',0,15,228,9,3
	.word	31899
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14062
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON1',0,15,236,9,3
	.word	31964
	.byte	12,15,239,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14300
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON2',0,15,244,9,3
	.word	32029
	.byte	12,15,247,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14428
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON3',0,15,252,9,3
	.word	32094
	.byte	12,15,255,9,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14671
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON4',0,15,132,10,3
	.word	32159
	.byte	12,15,135,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14906
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON5',0,15,140,10,3
	.word	32224
	.byte	12,15,143,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15034
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6',0,15,148,10,3
	.word	32289
	.byte	12,15,151,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15134
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7',0,15,156,10,3
	.word	32354
	.byte	12,15,159,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15234
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CHIPID',0,15,164,10,3
	.word	32419
	.byte	12,15,167,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15442
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSCON',0,15,172,10,3
	.word	32483
	.byte	12,15,175,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15607
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSLIM',0,15,180,10,3
	.word	32547
	.byte	12,15,183,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15790
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSSTAT',0,15,188,10,3
	.word	32611
	.byte	12,15,191,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15944
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EICR',0,15,196,10,3
	.word	32676
	.byte	12,15,199,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16308
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR',0,15,204,10,3
	.word	32738
	.byte	12,15,207,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16519
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EMSR',0,15,212,10,3
	.word	32800
	.byte	12,15,215,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16771
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG',0,15,220,10,3
	.word	32862
	.byte	12,15,223,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16889
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG',0,15,228,10,3
	.word	32926
	.byte	12,15,231,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17000
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR13CON',0,15,236,10,3
	.word	32991
	.byte	12,15,239,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17163
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR33CON',0,15,244,10,3
	.word	33057
	.byte	12,15,247,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17326
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRADCSTAT',0,15,252,10,3
	.word	33123
	.byte	12,15,255,10,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17484
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRDVSTAT',0,15,132,11,3
	.word	33191
	.byte	12,15,135,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17649
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRMONCTRL',0,15,140,11,3
	.word	33258
	.byte	12,15,143,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17978
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROSCCTRL',0,15,148,11,3
	.word	33326
	.byte	12,15,151,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18199
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROVMON',0,15,156,11,3
	.word	33394
	.byte	12,15,159,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18362
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRRSTCON',0,15,164,11,3
	.word	33460
	.byte	12,15,167,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18634
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,15,172,11,3
	.word	33527
	.byte	12,15,175,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18787
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,15,180,11,3
	.word	33596
	.byte	12,15,183,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18943
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,15,188,11,3
	.word	33665
	.byte	12,15,191,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19105
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,15,196,11,3
	.word	33734
	.byte	12,15,199,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19248
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,15,204,11,3
	.word	33803
	.byte	12,15,207,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19413
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,15,212,11,3
	.word	33872
	.byte	12,15,215,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19558
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1',0,15,220,11,3
	.word	33941
	.byte	12,15,223,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19739
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2',0,15,228,11,3
	.word	34009
	.byte	12,15,231,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19913
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3',0,15,236,11,3
	.word	34077
	.byte	12,15,239,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20073
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4',0,15,244,11,3
	.word	34145
	.byte	12,15,247,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20217
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT',0,15,252,11,3
	.word	34213
	.byte	12,15,255,11,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20491
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRTRIM',0,15,132,12,3
	.word	34278
	.byte	12,15,135,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20630
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRUVMON',0,15,140,12,3
	.word	34343
	.byte	12,15,143,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20793
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EXTCON',0,15,148,12,3
	.word	34409
	.byte	12,15,151,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21011
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FDR',0,15,156,12,3
	.word	34473
	.byte	12,15,159,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21174
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FMR',0,15,164,12,3
	.word	34534
	.byte	12,15,167,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21510
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ID',0,15,172,12,3
	.word	34595
	.byte	12,15,175,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21617
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IGCR',0,15,180,12,3
	.word	34655
	.byte	12,15,183,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22069
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IN',0,15,188,12,3
	.word	34717
	.byte	12,15,191,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22168
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IOCR',0,15,196,12,3
	.word	34777
	.byte	12,15,199,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22318
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL0',0,15,204,12,3
	.word	34839
	.byte	12,15,207,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22467
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL1',0,15,212,12,3
	.word	34907
	.byte	12,15,215,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22628
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL2',0,15,220,12,3
	.word	34975
	.byte	12,15,223,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22758
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLCON',0,15,228,12,3
	.word	35043
	.byte	12,15,231,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22890
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST',0,15,236,12,3
	.word	35107
	.byte	12,15,239,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23005
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_MANID',0,15,244,12,3
	.word	35172
	.byte	12,15,247,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23116
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OMR',0,15,252,12,3
	.word	35235
	.byte	12,15,255,12,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23274
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OSCCON',0,15,132,13,3
	.word	35296
	.byte	12,15,135,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23686
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OUT',0,15,140,13,3
	.word	35360
	.byte	12,15,143,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23787
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCCON',0,15,148,13,3
	.word	35421
	.byte	12,15,151,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24054
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE',0,15,156,13,3
	.word	35485
	.byte	12,15,159,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24190
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC',0,15,164,13,3
	.word	35552
	.byte	12,15,167,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24301
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDR',0,15,172,13,3
	.word	35615
	.byte	12,15,175,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24434
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR',0,15,180,13,3
	.word	35676
	.byte	12,15,183,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24637
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON0',0,15,188,13,3
	.word	35738
	.byte	12,15,191,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24993
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON1',0,15,196,13,3
	.word	35803
	.byte	12,15,199,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25171
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON2',0,15,204,13,3
	.word	35868
	.byte	12,15,207,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25271
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON0',0,15,212,13,3
	.word	35933
	.byte	12,15,215,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25641
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON1',0,15,220,13,3
	.word	36002
	.byte	12,15,223,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25827
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT',0,15,228,13,3
	.word	36071
	.byte	12,15,231,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26025
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT',0,15,236,13,3
	.word	36140
	.byte	12,15,239,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26258
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR',0,15,244,13,3
	.word	36205
	.byte	12,15,247,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26410
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR0',0,15,252,13,3
	.word	36268
	.byte	12,15,255,13,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26977
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR1',0,15,132,14,3
	.word	36333
	.byte	12,15,135,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27271
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR2',0,15,140,14,3
	.word	36398
	.byte	12,15,143,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27549
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTAT',0,15,148,14,3
	.word	36463
	.byte	12,15,151,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28045
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR',0,15,156,14,3
	.word	36529
	.byte	12,15,159,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28567
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON',0,15,164,14,3
	.word	36598
	.byte	12,15,167,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28358
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON2',0,15,172,14,3
	.word	36662
	.byte	12,15,175,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28778
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTSTAT',0,15,180,14,3
	.word	36727
	.byte	12,15,183,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29210
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON',0,15,188,14,3
	.word	36792
	.byte	12,15,191,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29306
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_STSTAT',0,15,196,14,3
	.word	36857
	.byte	12,15,199,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29566
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON',0,15,204,14,3
	.word	36921
	.byte	12,15,207,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29691
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON',0,15,212,14,3
	.word	36987
	.byte	12,15,215,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29888
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR',0,15,220,14,3
	.word	37051
	.byte	12,15,223,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30041
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS',0,15,228,14,3
	.word	37116
	.byte	12,15,231,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30194
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET',0,15,236,14,3
	.word	37181
	.byte	12,15,239,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30347
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT',0,15,244,14,3
	.word	37246
	.byte	12,15,247,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30518
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0',0,15,252,14,3
	.word	37312
	.byte	12,15,255,14,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30648
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1',0,15,132,15,3
	.word	37381
	.byte	12,15,135,15,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30886
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_SR',0,15,140,15,3
	.word	37450
	.byte	12,15,143,15,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31109
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0',0,15,148,15,3
	.word	37517
	.byte	12,15,151,15,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31235
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON1',0,15,156,15,3
	.word	37584
	.byte	12,15,159,15,9,4,13
	.byte	'U',0
	.word	437
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31487
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_SR',0,15,164,15,3
	.word	37651
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,15,175,15,25,12,13
	.byte	'CON0',0
	.word	37312
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37381
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37450
	.byte	4,2,35,8,0,16
	.word	37716
	.byte	25
	.byte	'Ifx_SCU_WDTCPU',0,15,180,15,3
	.word	37779
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,15,183,15,25,12,13
	.byte	'CON0',0
	.word	37517
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37584
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37651
	.byte	4,2,35,8,0,16
	.word	37808
	.byte	25
	.byte	'Ifx_SCU_WDTS',0,15,188,15,3
	.word	37869
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,25
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	37896
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,25
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	38047
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,25
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	38291
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,25
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	38389
	.byte	25
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8257
	.byte	25
	.byte	'gpio_pin_enum',0,8,89,2
	.word	9081
	.byte	25
	.byte	'soft_iic_info_struct',0,7,50,2
	.word	8663
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,20
	.byte	46,1,49,19,0,0,21,5,0,49,19,0,0,22,19,1,58,15,59,15,57,15,11,15,0,0,23,38,0,73,19,0,0,24,46,1,3,8,58,15
	.byte	59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,25,22,0,3,8,58,15,59,15,57,15,73,19,0,0,26,21,0,54,15,0
	.byte	0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L48:
	.word	.L131-.L130
.L130:
	.half	3
	.word	.L133-.L132
.L132:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'zf_driver_delay.h',0,4,0,0
	.byte	'zf_driver_soft_iic.h',0,4,0,0
	.byte	'zf_driver_gpio.h',0,4,0,0
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0,0,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0,0
.L133:
.L131:
	.sdecl	'.debug_info',debug,cluster('mpu6050_get_acc')
	.sect	'.debug_info'
.L49:
	.word	273
	.half	3
	.word	.L50
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L52,.L51
	.byte	2
	.word	.L45
	.byte	3
	.byte	'mpu6050_get_acc',0,1,108,6,1,1,1
	.word	.L36,.L93,.L35
	.byte	4
	.word	.L36,.L93
	.byte	5
	.byte	'dat',0,1,110,11
	.word	.L94,.L95
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_get_acc')
	.sect	'.debug_abbrev'
.L50:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mpu6050_get_acc')
	.sect	'.debug_line'
.L51:
	.word	.L135-.L134
.L134:
	.half	3
	.word	.L137-.L136
.L136:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0,0,0,0,0
.L137:
	.byte	5,6,7,0,5,2
	.word	.L36
	.byte	3,235,0,1,5,5,9
	.half	.L124-.L36
	.byte	3,4,1,9
	.half	.L138-.L124
	.byte	3,1,1,5,41,9
	.half	.L139-.L138
	.byte	1,5,45,9
	.half	.L140-.L139
	.byte	1,5,55,9
	.half	.L141-.L140
	.byte	1,5,50,9
	.half	.L142-.L141
	.byte	1,5,19,9
	.half	.L143-.L142
	.byte	1,5,5,9
	.half	.L144-.L143
	.byte	3,1,1,5,41,9
	.half	.L145-.L144
	.byte	1,5,45,9
	.half	.L146-.L145
	.byte	1,5,55,9
	.half	.L147-.L146
	.byte	1,5,50,9
	.half	.L148-.L147
	.byte	1,5,19,9
	.half	.L149-.L148
	.byte	1,5,5,9
	.half	.L150-.L149
	.byte	3,1,1,5,41,9
	.half	.L151-.L150
	.byte	1,5,45,9
	.half	.L152-.L151
	.byte	1,5,55,9
	.half	.L153-.L152
	.byte	1,5,50,9
	.half	.L154-.L153
	.byte	1,5,19,9
	.half	.L155-.L154
	.byte	1,5,1,9
	.half	.L156-.L155
	.byte	3,1,1,7,9
	.half	.L53-.L156
	.byte	0,1,1
.L135:
	.sdecl	'.debug_ranges',debug,cluster('mpu6050_get_acc')
	.sect	'.debug_ranges'
.L52:
	.word	-1,.L36,0,.L53-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_get_gyro')
	.sect	'.debug_info'
.L54:
	.word	274
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L57,.L56
	.byte	2
	.word	.L45
	.byte	3
	.byte	'mpu6050_get_gyro',0,1,125,6,1,1,1
	.word	.L38,.L96,.L37
	.byte	4
	.word	.L38,.L96
	.byte	5
	.byte	'dat',0,1,127,11
	.word	.L94,.L97
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_get_gyro')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mpu6050_get_gyro')
	.sect	'.debug_line'
.L56:
	.word	.L158-.L157
.L157:
	.half	3
	.word	.L160-.L159
.L159:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0,0,0,0,0
.L160:
	.byte	5,6,7,0,5,2
	.word	.L38
	.byte	3,252,0,1,5,5,9
	.half	.L125-.L38
	.byte	3,4,1,9
	.half	.L161-.L125
	.byte	3,1,1,5,42,9
	.half	.L162-.L161
	.byte	1,5,46,9
	.half	.L163-.L162
	.byte	1,5,56,9
	.half	.L164-.L163
	.byte	1,5,51,9
	.half	.L165-.L164
	.byte	1,5,20,9
	.half	.L166-.L165
	.byte	1,5,5,9
	.half	.L167-.L166
	.byte	3,1,1,5,42,9
	.half	.L168-.L167
	.byte	1,5,46,9
	.half	.L169-.L168
	.byte	1,5,56,9
	.half	.L170-.L169
	.byte	1,5,51,9
	.half	.L171-.L170
	.byte	1,5,20,9
	.half	.L172-.L171
	.byte	1,5,5,9
	.half	.L173-.L172
	.byte	3,1,1,5,42,9
	.half	.L174-.L173
	.byte	1,5,46,9
	.half	.L175-.L174
	.byte	1,5,56,9
	.half	.L176-.L175
	.byte	1,5,51,9
	.half	.L177-.L176
	.byte	1,5,20,9
	.half	.L178-.L177
	.byte	1,5,1,9
	.half	.L179-.L178
	.byte	3,1,1,7,9
	.half	.L58-.L179
	.byte	0,1,1
.L158:
	.sdecl	'.debug_ranges',debug,cluster('mpu6050_get_gyro')
	.sect	'.debug_ranges'
.L57:
	.word	-1,.L38,0,.L58-.L38,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_acc_transition')
	.sect	'.debug_info'
.L59:
	.word	314
	.half	3
	.word	.L60
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L62,.L61
	.byte	2
	.word	.L45
	.byte	3
	.byte	'mpu6050_acc_transition',0,1,142,1,7
	.word	.L98
	.byte	1,1,1
	.word	.L40,.L99,.L39
	.byte	4
	.byte	'acc_value',0,1,142,1,37
	.word	.L100,.L101
	.byte	5
	.word	.L40,.L99
	.byte	6
	.byte	'acc_data',0,1,144,1,11
	.word	.L98,.L102
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_acc_transition')
	.sect	'.debug_abbrev'
.L60:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mpu6050_acc_transition')
	.sect	'.debug_line'
.L61:
	.word	.L181-.L180
.L180:
	.half	3
	.word	.L183-.L182
.L182:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0,0,0,0,0
.L183:
	.byte	5,20,7,0,5,2
	.word	.L40
	.byte	3,143,1,1,5,12,9
	.half	.L126-.L40
	.byte	3,1,1,5,14,9
	.half	.L184-.L126
	.byte	3,2,1,9
	.half	.L185-.L184
	.byte	3,1,1,9
	.half	.L186-.L185
	.byte	3,1,1,9
	.half	.L187-.L186
	.byte	3,1,1,5,31,9
	.half	.L7-.L187
	.byte	3,125,1,5,50,9
	.half	.L188-.L7
	.byte	1,5,48,9
	.half	.L189-.L188
	.byte	1,5,57,9
	.half	.L190-.L189
	.byte	1,5,31,9
	.half	.L8-.L190
	.byte	3,1,1,5,50,9
	.half	.L191-.L8
	.byte	1,5,48,9
	.half	.L192-.L191
	.byte	1,5,57,9
	.half	.L193-.L192
	.byte	1,5,31,9
	.half	.L9-.L193
	.byte	3,1,1,5,50,9
	.half	.L194-.L9
	.byte	1,5,48,9
	.half	.L195-.L194
	.byte	1,5,57,9
	.half	.L196-.L195
	.byte	1,5,31,9
	.half	.L10-.L196
	.byte	3,1,1,5,50,9
	.half	.L197-.L10
	.byte	1,5,48,9
	.half	.L198-.L197
	.byte	1,5,57,9
	.half	.L199-.L198
	.byte	1,5,18,9
	.half	.L11-.L199
	.byte	3,1,1,5,5,9
	.half	.L12-.L11
	.byte	3,2,1,5,1,9
	.half	.L17-.L12
	.byte	3,1,1,7,9
	.half	.L63-.L17
	.byte	0,1,1
.L181:
	.sdecl	'.debug_ranges',debug,cluster('mpu6050_acc_transition')
	.sect	'.debug_ranges'
.L62:
	.word	-1,.L40,0,.L63-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_gyro_transition')
	.sect	'.debug_info'
.L64:
	.word	317
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L67,.L66
	.byte	2
	.word	.L45
	.byte	3
	.byte	'mpu6050_gyro_transition',0,1,163,1,7
	.word	.L98
	.byte	1,1,1
	.word	.L42,.L103,.L41
	.byte	4
	.byte	'gyro_value',0,1,163,1,38
	.word	.L100,.L104
	.byte	5
	.word	.L42,.L103
	.byte	6
	.byte	'gyro_data',0,1,165,1,11
	.word	.L98,.L105
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_gyro_transition')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mpu6050_gyro_transition')
	.sect	'.debug_line'
.L66:
	.word	.L201-.L200
.L200:
	.half	3
	.word	.L203-.L202
.L202:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0,0,0,0,0
.L203:
	.byte	5,21,7,0,5,2
	.word	.L42
	.byte	3,164,1,1,5,12,9
	.half	.L127-.L42
	.byte	3,1,1,5,14,9
	.half	.L204-.L127
	.byte	3,2,1,9
	.half	.L205-.L204
	.byte	3,1,1,9
	.half	.L206-.L205
	.byte	3,1,1,9
	.half	.L207-.L206
	.byte	3,1,1,5,32,9
	.half	.L18-.L207
	.byte	3,125,1,5,52,9
	.half	.L208-.L18
	.byte	1,5,50,9
	.half	.L209-.L208
	.byte	1,5,61,9
	.half	.L210-.L209
	.byte	1,5,32,9
	.half	.L19-.L210
	.byte	3,1,1,5,52,9
	.half	.L211-.L19
	.byte	1,5,50,9
	.half	.L212-.L211
	.byte	1,5,61,9
	.half	.L213-.L212
	.byte	1,5,32,9
	.half	.L20-.L213
	.byte	3,1,1,5,52,9
	.half	.L214-.L20
	.byte	1,5,50,9
	.half	.L215-.L214
	.byte	1,5,61,9
	.half	.L216-.L215
	.byte	1,5,32,9
	.half	.L21-.L216
	.byte	3,1,1,5,52,9
	.half	.L217-.L21
	.byte	1,5,50,9
	.half	.L218-.L217
	.byte	1,5,61,9
	.half	.L219-.L218
	.byte	1,5,18,9
	.half	.L22-.L219
	.byte	3,1,1,5,5,9
	.half	.L23-.L22
	.byte	3,2,1,5,1,9
	.half	.L28-.L23
	.byte	3,1,1,7,9
	.half	.L68-.L28
	.byte	0,1,1
.L201:
	.sdecl	'.debug_ranges',debug,cluster('mpu6050_gyro_transition')
	.sect	'.debug_ranges'
.L67:
	.word	-1,.L42,0,.L68-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_init')
	.sect	'.debug_info'
.L69:
	.word	285
	.half	3
	.word	.L70
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L72,.L71
	.byte	2
	.word	.L45
	.byte	3
	.byte	'mpu6050_init',0,1,184,1,7
	.word	.L106
	.byte	1,1,1
	.word	.L44,.L107,.L43
	.byte	4
	.word	.L44,.L107
	.byte	5
	.byte	'return_state',0,1,186,1,11
	.word	.L106,.L108
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_init')
	.sect	'.debug_abbrev'
.L70:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mpu6050_init')
	.sect	'.debug_line'
.L71:
	.word	.L221-.L220
.L220:
	.half	3
	.word	.L223-.L222
.L222:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0,0,0,0,0
.L223:
	.byte	5,24,7,0,5,2
	.word	.L44
	.byte	3,185,1,1,5,20,9
	.half	.L128-.L44
	.byte	3,2,1,5,40,9
	.half	.L224-.L128
	.byte	1,5,58,9
	.half	.L225-.L224
	.byte	1,5,82,9
	.half	.L226-.L225
	.byte	1,5,99,9
	.half	.L227-.L226
	.byte	1,5,21,9
	.half	.L228-.L227
	.byte	3,4,1,5,31,9
	.half	.L29-.L228
	.byte	3,4,1,5,9,9
	.half	.L229-.L29
	.byte	1,5,13,7,9
	.half	.L230-.L229
	.byte	3,5,1,5,26,9
	.half	.L231-.L230
	.byte	3,1,1,5,13,9
	.half	.L232-.L231
	.byte	3,1,1,5,9,9
	.half	.L30-.L232
	.byte	3,2,1,9
	.half	.L233-.L30
	.byte	3,1,1,9
	.half	.L234-.L233
	.byte	3,1,1,9
	.half	.L235-.L234
	.byte	3,2,1,9
	.half	.L236-.L235
	.byte	3,7,1,9
	.half	.L237-.L236
	.byte	3,7,1,9
	.half	.L238-.L237
	.byte	3,1,1,5,5,9
	.half	.L31-.L238
	.byte	3,2,1,5,1,9
	.half	.L32-.L31
	.byte	3,1,1,7,9
	.half	.L73-.L32
	.byte	0,1,1
.L221:
	.sdecl	'.debug_ranges',debug,cluster('mpu6050_init')
	.sect	'.debug_ranges'
.L72:
	.word	-1,.L44,0,.L73-.L44,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_self1_check')
	.sect	'.debug_info'
.L74:
	.word	331
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L77,.L76
	.byte	2
	.word	.L45
	.byte	3
	.byte	'mpu6050_self1_check',0,1,81,14
	.word	.L106
	.byte	1,1
	.word	.L34,.L109,.L33
	.byte	4
	.word	.L34,.L109
	.byte	5
	.byte	'dat',0,1,83,11
	.word	.L106,.L110
	.byte	5
	.byte	'return_state',0,1,83,20
	.word	.L106,.L111
	.byte	5
	.byte	'timeout_count',0,1,84,12
	.word	.L112,.L113
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_self1_check')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mpu6050_self1_check')
	.sect	'.debug_line'
.L76:
	.word	.L240-.L239
.L239:
	.half	3
	.word	.L242-.L241
.L241:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0,0,0,0,0
.L242:
	.byte	5,15,7,0,5,2
	.word	.L34
	.byte	3,210,0,1,5,33,9
	.half	.L115-.L34
	.byte	1,5,26,9
	.half	.L117-.L115
	.byte	3,1,1,5,5,9
	.half	.L118-.L117
	.byte	3,2,1,9
	.half	.L243-.L118
	.byte	3,1,1,5,22,9
	.half	.L244-.L243
	.byte	3,1,1,5,50,9
	.half	.L3-.L244
	.byte	3,2,1,5,12,9
	.half	.L120-.L3
	.byte	1,5,9,9
	.half	.L116-.L120
	.byte	1,5,26,7,9
	.half	.L245-.L116
	.byte	3,2,1,5,13,9
	.half	.L246-.L245
	.byte	3,1,1,5,15,9
	.half	.L4-.L246
	.byte	3,2,1,5,13,9
	.half	.L121-.L4
	.byte	1,5,25,9
	.half	.L122-.L121
	.byte	3,1,1,5,22,9
	.half	.L2-.L122
	.byte	3,120,1,5,5,7,9
	.half	.L5-.L2
	.byte	3,10,1,5,1,9
	.half	.L6-.L5
	.byte	3,1,1,7,9
	.half	.L78-.L6
	.byte	0,1,1
.L240:
	.sdecl	'.debug_ranges',debug,cluster('mpu6050_self1_check')
	.sect	'.debug_ranges'
.L77:
	.word	-1,.L34,0,.L78-.L34,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_gyro_x')
	.sect	'.debug_info'
.L79:
	.word	233
	.half	3
	.word	.L80
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L45
	.byte	3
	.byte	'mpu6050_gyro_x',0,9,61,7
	.word	.L100
	.byte	1,5,3
	.word	mpu6050_gyro_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_gyro_x')
	.sect	'.debug_abbrev'
.L80:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_gyro_y')
	.sect	'.debug_info'
.L81:
	.word	233
	.half	3
	.word	.L82
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L45
	.byte	3
	.byte	'mpu6050_gyro_y',0,9,61,27
	.word	.L100
	.byte	1,5,3
	.word	mpu6050_gyro_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_gyro_y')
	.sect	'.debug_abbrev'
.L82:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_gyro_z')
	.sect	'.debug_info'
.L83:
	.word	233
	.half	3
	.word	.L84
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L45
	.byte	3
	.byte	'mpu6050_gyro_z',0,9,61,47
	.word	.L100
	.byte	1,5,3
	.word	mpu6050_gyro_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_gyro_z')
	.sect	'.debug_abbrev'
.L84:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_acc_x')
	.sect	'.debug_info'
.L85:
	.word	232
	.half	3
	.word	.L86
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L45
	.byte	3
	.byte	'mpu6050_acc_x',0,9,62,7
	.word	.L100
	.byte	1,5,3
	.word	mpu6050_acc_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_acc_x')
	.sect	'.debug_abbrev'
.L86:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_acc_y')
	.sect	'.debug_info'
.L87:
	.word	232
	.half	3
	.word	.L88
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L45
	.byte	3
	.byte	'mpu6050_acc_y',0,9,62,27
	.word	.L100
	.byte	1,5,3
	.word	mpu6050_acc_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_acc_y')
	.sect	'.debug_abbrev'
.L88:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_acc_z')
	.sect	'.debug_info'
.L89:
	.word	232
	.half	3
	.word	.L90
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L45
	.byte	3
	.byte	'mpu6050_acc_z',0,9,62,47
	.word	.L100
	.byte	1,5,3
	.word	mpu6050_acc_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_acc_z')
	.sect	'.debug_abbrev'
.L90:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mpu6050_iic_struct')
	.sect	'.debug_info'
.L91:
	.word	236
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mpu6050.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L45
	.byte	3
	.byte	'mpu6050_iic_struct',0,9,65,29
	.word	.L114
	.byte	5,3
	.word	mpu6050_iic_struct
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mpu6050_iic_struct')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('mpu6050_acc_transition')
	.sect	'.debug_loc'
.L102:
	.word	-1,.L40,.L126-.L40,.L99-.L40
	.half	1
	.byte	82
	.word	0,0
.L101:
	.word	-1,.L40,0,.L99-.L40
	.half	1
	.byte	84
	.word	0,0
.L39:
	.word	-1,.L40,0,.L99-.L40
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mpu6050_get_acc')
	.sect	'.debug_loc'
.L95:
	.word	-1,.L36,0,.L93-.L36
	.half	2
	.byte	145,120
	.word	0,0
.L35:
	.word	-1,.L36,0,.L124-.L36
	.half	2
	.byte	138,0
	.word	.L124-.L36,.L93-.L36
	.half	2
	.byte	138,8
	.word	.L93-.L36,.L93-.L36
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mpu6050_get_gyro')
	.sect	'.debug_loc'
.L97:
	.word	-1,.L38,0,.L96-.L38
	.half	2
	.byte	145,120
	.word	0,0
.L37:
	.word	-1,.L38,0,.L125-.L38
	.half	2
	.byte	138,0
	.word	.L125-.L38,.L96-.L38
	.half	2
	.byte	138,8
	.word	.L96-.L38,.L96-.L38
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mpu6050_gyro_transition')
	.sect	'.debug_loc'
.L105:
	.word	-1,.L42,.L127-.L42,.L103-.L42
	.half	1
	.byte	82
	.word	0,0
.L104:
	.word	-1,.L42,0,.L103-.L42
	.half	1
	.byte	84
	.word	0,0
.L41:
	.word	-1,.L42,0,.L103-.L42
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mpu6050_init')
	.sect	'.debug_loc'
.L43:
	.word	-1,.L44,0,.L107-.L44
	.half	2
	.byte	138,0
	.word	0,0
.L108:
	.word	-1,.L44,.L128-.L44,.L107-.L44
	.half	1
	.byte	95
	.word	.L129-.L44,.L107-.L44
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mpu6050_self1_check')
	.sect	'.debug_loc'
.L110:
	.word	-1,.L34,.L115-.L34,.L116-.L34
	.half	1
	.byte	95
	.word	.L121-.L34,.L2-.L34
	.half	1
	.byte	82
	.word	.L122-.L34,.L5-.L34
	.half	1
	.byte	95
	.word	0,0
.L33:
	.word	-1,.L34,0,.L109-.L34
	.half	2
	.byte	138,0
	.word	0,0
.L111:
	.word	-1,.L34,.L117-.L34,.L109-.L34
	.half	1
	.byte	89
	.word	.L123-.L34,.L109-.L34
	.half	1
	.byte	82
	.word	0,0
.L113:
	.word	-1,.L34,.L118-.L34,.L119-.L34
	.half	1
	.byte	88
	.word	.L120-.L34,.L109-.L34
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L247:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('mpu6050_self1_check')
	.sect	'.debug_frame'
	.word	12
	.word	.L247,.L34,.L109-.L34
	.sdecl	'.debug_frame',debug,cluster('mpu6050_get_acc')
	.sect	'.debug_frame'
	.word	36
	.word	.L247,.L36,.L93-.L36
	.byte	4
	.word	(.L124-.L36)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L93-.L124)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('mpu6050_get_gyro')
	.sect	'.debug_frame'
	.word	36
	.word	.L247,.L38,.L96-.L38
	.byte	4
	.word	(.L125-.L38)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L96-.L125)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('mpu6050_acc_transition')
	.sect	'.debug_frame'
	.word	24
	.word	.L247,.L40,.L99-.L40
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('mpu6050_gyro_transition')
	.sect	'.debug_frame'
	.word	24
	.word	.L247,.L42,.L103-.L42
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('mpu6050_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L247,.L44,.L107-.L44
	; Module end
