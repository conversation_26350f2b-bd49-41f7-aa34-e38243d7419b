/**
 * \file IfxQspi_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Qspi_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Qspi
 * 
 */
#ifndef IFXQSPI_BF_H
#define IFXQSPI_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Qspi_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN0 */
#define IFX_QSPI_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN0 */
#define IFX_QSPI_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN0 */
#define IFX_QSPI_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN10 */
#define IFX_QSPI_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN10 */
#define IFX_QSPI_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN10 */
#define IFX_QSPI_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN11 */
#define IFX_QSPI_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN11 */
#define IFX_QSPI_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN11 */
#define IFX_QSPI_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN12 */
#define IFX_QSPI_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN12 */
#define IFX_QSPI_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN12 */
#define IFX_QSPI_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN13 */
#define IFX_QSPI_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN13 */
#define IFX_QSPI_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN13 */
#define IFX_QSPI_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN14 */
#define IFX_QSPI_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN14 */
#define IFX_QSPI_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN14 */
#define IFX_QSPI_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN15 */
#define IFX_QSPI_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN15 */
#define IFX_QSPI_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN15 */
#define IFX_QSPI_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN16 */
#define IFX_QSPI_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN16 */
#define IFX_QSPI_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN16 */
#define IFX_QSPI_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN17 */
#define IFX_QSPI_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN17 */
#define IFX_QSPI_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN17 */
#define IFX_QSPI_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN18 */
#define IFX_QSPI_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN18 */
#define IFX_QSPI_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN18 */
#define IFX_QSPI_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN19 */
#define IFX_QSPI_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN19 */
#define IFX_QSPI_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN19 */
#define IFX_QSPI_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN1 */
#define IFX_QSPI_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN1 */
#define IFX_QSPI_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN1 */
#define IFX_QSPI_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN20 */
#define IFX_QSPI_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN20 */
#define IFX_QSPI_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN20 */
#define IFX_QSPI_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN21 */
#define IFX_QSPI_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN21 */
#define IFX_QSPI_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN21 */
#define IFX_QSPI_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN22 */
#define IFX_QSPI_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN22 */
#define IFX_QSPI_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN22 */
#define IFX_QSPI_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN23 */
#define IFX_QSPI_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN23 */
#define IFX_QSPI_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN23 */
#define IFX_QSPI_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN24 */
#define IFX_QSPI_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN24 */
#define IFX_QSPI_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN24 */
#define IFX_QSPI_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN25 */
#define IFX_QSPI_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN25 */
#define IFX_QSPI_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN25 */
#define IFX_QSPI_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN26 */
#define IFX_QSPI_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN26 */
#define IFX_QSPI_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN26 */
#define IFX_QSPI_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN27 */
#define IFX_QSPI_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN27 */
#define IFX_QSPI_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN27 */
#define IFX_QSPI_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN28 */
#define IFX_QSPI_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN28 */
#define IFX_QSPI_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN28 */
#define IFX_QSPI_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN29 */
#define IFX_QSPI_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN29 */
#define IFX_QSPI_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN29 */
#define IFX_QSPI_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN2 */
#define IFX_QSPI_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN2 */
#define IFX_QSPI_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN2 */
#define IFX_QSPI_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN30 */
#define IFX_QSPI_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN30 */
#define IFX_QSPI_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN30 */
#define IFX_QSPI_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN31 */
#define IFX_QSPI_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN31 */
#define IFX_QSPI_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN31 */
#define IFX_QSPI_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN3 */
#define IFX_QSPI_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN3 */
#define IFX_QSPI_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN3 */
#define IFX_QSPI_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN4 */
#define IFX_QSPI_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN4 */
#define IFX_QSPI_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN4 */
#define IFX_QSPI_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN5 */
#define IFX_QSPI_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN5 */
#define IFX_QSPI_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN5 */
#define IFX_QSPI_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN6 */
#define IFX_QSPI_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN6 */
#define IFX_QSPI_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN6 */
#define IFX_QSPI_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN7 */
#define IFX_QSPI_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN7 */
#define IFX_QSPI_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN7 */
#define IFX_QSPI_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN8 */
#define IFX_QSPI_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN8 */
#define IFX_QSPI_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN8 */
#define IFX_QSPI_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_QSPI_ACCEN0_Bits.EN9 */
#define IFX_QSPI_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ACCEN0_Bits.EN9 */
#define IFX_QSPI_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ACCEN0_Bits.EN9 */
#define IFX_QSPI_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.BYTE */
#define IFX_QSPI_BACON_BYTE_LEN (1u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.BYTE */
#define IFX_QSPI_BACON_BYTE_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.BYTE */
#define IFX_QSPI_BACON_BYTE_OFF (22u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.CS */
#define IFX_QSPI_BACON_CS_LEN (4u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.CS */
#define IFX_QSPI_BACON_CS_MSK (0xfu)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.CS */
#define IFX_QSPI_BACON_CS_OFF (28u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.DL */
#define IFX_QSPI_BACON_DL_LEN (5u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.DL */
#define IFX_QSPI_BACON_DL_MSK (0x1fu)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.DL */
#define IFX_QSPI_BACON_DL_OFF (23u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.IDLE */
#define IFX_QSPI_BACON_IDLE_LEN (3u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.IDLE */
#define IFX_QSPI_BACON_IDLE_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.IDLE */
#define IFX_QSPI_BACON_IDLE_OFF (4u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.IPRE */
#define IFX_QSPI_BACON_IPRE_LEN (3u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.IPRE */
#define IFX_QSPI_BACON_IPRE_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.IPRE */
#define IFX_QSPI_BACON_IPRE_OFF (1u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.LAST */
#define IFX_QSPI_BACON_LAST_LEN (1u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.LAST */
#define IFX_QSPI_BACON_LAST_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.LAST */
#define IFX_QSPI_BACON_LAST_OFF (0u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.LEAD */
#define IFX_QSPI_BACON_LEAD_LEN (3u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.LEAD */
#define IFX_QSPI_BACON_LEAD_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.LEAD */
#define IFX_QSPI_BACON_LEAD_OFF (10u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.LPRE */
#define IFX_QSPI_BACON_LPRE_LEN (3u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.LPRE */
#define IFX_QSPI_BACON_LPRE_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.LPRE */
#define IFX_QSPI_BACON_LPRE_OFF (7u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.MSB */
#define IFX_QSPI_BACON_MSB_LEN (1u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.MSB */
#define IFX_QSPI_BACON_MSB_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.MSB */
#define IFX_QSPI_BACON_MSB_OFF (21u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.PARTYP */
#define IFX_QSPI_BACON_PARTYP_LEN (1u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.PARTYP */
#define IFX_QSPI_BACON_PARTYP_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.PARTYP */
#define IFX_QSPI_BACON_PARTYP_OFF (19u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.TPRE */
#define IFX_QSPI_BACON_TPRE_LEN (3u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.TPRE */
#define IFX_QSPI_BACON_TPRE_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.TPRE */
#define IFX_QSPI_BACON_TPRE_OFF (13u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.TRAIL */
#define IFX_QSPI_BACON_TRAIL_LEN (3u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.TRAIL */
#define IFX_QSPI_BACON_TRAIL_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.TRAIL */
#define IFX_QSPI_BACON_TRAIL_OFF (16u)

/** \brief  Length for Ifx_QSPI_BACON_Bits.UINT */
#define IFX_QSPI_BACON_UINT_LEN (1u)

/** \brief  Mask for Ifx_QSPI_BACON_Bits.UINT */
#define IFX_QSPI_BACON_UINT_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_BACON_Bits.UINT */
#define IFX_QSPI_BACON_UINT_OFF (20u)

/** \brief  Length for Ifx_QSPI_BACONENTRY_Bits.E */
#define IFX_QSPI_BACONENTRY_E_LEN (32u)

/** \brief  Mask for Ifx_QSPI_BACONENTRY_Bits.E */
#define IFX_QSPI_BACONENTRY_E_MSK (0xffffffffu)

/** \brief  Offset for Ifx_QSPI_BACONENTRY_Bits.E */
#define IFX_QSPI_BACONENTRY_E_OFF (0u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.CAP */
#define IFX_QSPI_CAPCON_CAP_LEN (15u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.CAP */
#define IFX_QSPI_CAPCON_CAP_MSK (0x7fffu)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.CAP */
#define IFX_QSPI_CAPCON_CAP_OFF (0u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.CAPC */
#define IFX_QSPI_CAPCON_CAPC_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.CAPC */
#define IFX_QSPI_CAPCON_CAPC_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.CAPC */
#define IFX_QSPI_CAPCON_CAPC_OFF (28u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.CAPF */
#define IFX_QSPI_CAPCON_CAPF_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.CAPF */
#define IFX_QSPI_CAPCON_CAPF_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.CAPF */
#define IFX_QSPI_CAPCON_CAPF_OFF (30u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.CAPS */
#define IFX_QSPI_CAPCON_CAPS_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.CAPS */
#define IFX_QSPI_CAPCON_CAPS_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.CAPS */
#define IFX_QSPI_CAPCON_CAPS_OFF (29u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.CAPSEL */
#define IFX_QSPI_CAPCON_CAPSEL_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.CAPSEL */
#define IFX_QSPI_CAPCON_CAPSEL_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.CAPSEL */
#define IFX_QSPI_CAPCON_CAPSEL_OFF (31u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.EDGECON */
#define IFX_QSPI_CAPCON_EDGECON_LEN (2u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.EDGECON */
#define IFX_QSPI_CAPCON_EDGECON_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.EDGECON */
#define IFX_QSPI_CAPCON_EDGECON_OFF (16u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.EN */
#define IFX_QSPI_CAPCON_EN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.EN */
#define IFX_QSPI_CAPCON_EN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.EN */
#define IFX_QSPI_CAPCON_EN_OFF (20u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.INS */
#define IFX_QSPI_CAPCON_INS_LEN (2u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.INS */
#define IFX_QSPI_CAPCON_INS_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.INS */
#define IFX_QSPI_CAPCON_INS_OFF (18u)

/** \brief  Length for Ifx_QSPI_CAPCON_Bits.OVF */
#define IFX_QSPI_CAPCON_OVF_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CAPCON_Bits.OVF */
#define IFX_QSPI_CAPCON_OVF_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CAPCON_Bits.OVF */
#define IFX_QSPI_CAPCON_OVF_OFF (15u)

/** \brief  Length for Ifx_QSPI_CLC_Bits.DISR */
#define IFX_QSPI_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CLC_Bits.DISR */
#define IFX_QSPI_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CLC_Bits.DISR */
#define IFX_QSPI_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_QSPI_CLC_Bits.DISS */
#define IFX_QSPI_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CLC_Bits.DISS */
#define IFX_QSPI_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CLC_Bits.DISS */
#define IFX_QSPI_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_QSPI_CLC_Bits.EDIS */
#define IFX_QSPI_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_QSPI_CLC_Bits.EDIS */
#define IFX_QSPI_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_CLC_Bits.EDIS */
#define IFX_QSPI_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_QSPI_DATAENTRY_Bits.E */
#define IFX_QSPI_DATAENTRY_E_LEN (32u)

/** \brief  Mask for Ifx_QSPI_DATAENTRY_Bits.E */
#define IFX_QSPI_DATAENTRY_E_MSK (0xffffffffu)

/** \brief  Offset for Ifx_QSPI_DATAENTRY_Bits.E */
#define IFX_QSPI_DATAENTRY_E_OFF (0u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.A */
#define IFX_QSPI_ECON_A_LEN (2u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.A */
#define IFX_QSPI_ECON_A_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.A */
#define IFX_QSPI_ECON_A_OFF (6u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.B */
#define IFX_QSPI_ECON_B_LEN (2u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.B */
#define IFX_QSPI_ECON_B_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.B */
#define IFX_QSPI_ECON_B_OFF (8u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.BE */
#define IFX_QSPI_ECON_BE_LEN (2u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.BE */
#define IFX_QSPI_ECON_BE_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.BE */
#define IFX_QSPI_ECON_BE_OFF (30u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.C */
#define IFX_QSPI_ECON_C_LEN (2u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.C */
#define IFX_QSPI_ECON_C_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.C */
#define IFX_QSPI_ECON_C_OFF (10u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.CPH */
#define IFX_QSPI_ECON_CPH_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.CPH */
#define IFX_QSPI_ECON_CPH_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.CPH */
#define IFX_QSPI_ECON_CPH_OFF (12u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.CPOL */
#define IFX_QSPI_ECON_CPOL_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.CPOL */
#define IFX_QSPI_ECON_CPOL_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.CPOL */
#define IFX_QSPI_ECON_CPOL_OFF (13u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.PAREN */
#define IFX_QSPI_ECON_PAREN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.PAREN */
#define IFX_QSPI_ECON_PAREN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.PAREN */
#define IFX_QSPI_ECON_PAREN_OFF (14u)

/** \brief  Length for Ifx_QSPI_ECON_Bits.Q */
#define IFX_QSPI_ECON_Q_LEN (6u)

/** \brief  Mask for Ifx_QSPI_ECON_Bits.Q */
#define IFX_QSPI_ECON_Q_MSK (0x3fu)

/** \brief  Offset for Ifx_QSPI_ECON_Bits.Q */
#define IFX_QSPI_ECON_Q_OFF (0u)

/** \brief  Length for Ifx_QSPI_FLAGSCLEAR_Bits.ERRORCLEARS */
#define IFX_QSPI_FLAGSCLEAR_ERRORCLEARS_LEN (9u)

/** \brief  Mask for Ifx_QSPI_FLAGSCLEAR_Bits.ERRORCLEARS */
#define IFX_QSPI_FLAGSCLEAR_ERRORCLEARS_MSK (0x1ffu)

/** \brief  Offset for Ifx_QSPI_FLAGSCLEAR_Bits.ERRORCLEARS */
#define IFX_QSPI_FLAGSCLEAR_ERRORCLEARS_OFF (0u)

/** \brief  Length for Ifx_QSPI_FLAGSCLEAR_Bits.PT1C */
#define IFX_QSPI_FLAGSCLEAR_PT1C_LEN (1u)

/** \brief  Mask for Ifx_QSPI_FLAGSCLEAR_Bits.PT1C */
#define IFX_QSPI_FLAGSCLEAR_PT1C_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_FLAGSCLEAR_Bits.PT1C */
#define IFX_QSPI_FLAGSCLEAR_PT1C_OFF (11u)

/** \brief  Length for Ifx_QSPI_FLAGSCLEAR_Bits.PT2C */
#define IFX_QSPI_FLAGSCLEAR_PT2C_LEN (1u)

/** \brief  Mask for Ifx_QSPI_FLAGSCLEAR_Bits.PT2C */
#define IFX_QSPI_FLAGSCLEAR_PT2C_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_FLAGSCLEAR_Bits.PT2C */
#define IFX_QSPI_FLAGSCLEAR_PT2C_OFF (12u)

/** \brief  Length for Ifx_QSPI_FLAGSCLEAR_Bits.RXC */
#define IFX_QSPI_FLAGSCLEAR_RXC_LEN (1u)

/** \brief  Mask for Ifx_QSPI_FLAGSCLEAR_Bits.RXC */
#define IFX_QSPI_FLAGSCLEAR_RXC_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_FLAGSCLEAR_Bits.RXC */
#define IFX_QSPI_FLAGSCLEAR_RXC_OFF (10u)

/** \brief  Length for Ifx_QSPI_FLAGSCLEAR_Bits.TXC */
#define IFX_QSPI_FLAGSCLEAR_TXC_LEN (1u)

/** \brief  Mask for Ifx_QSPI_FLAGSCLEAR_Bits.TXC */
#define IFX_QSPI_FLAGSCLEAR_TXC_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_FLAGSCLEAR_Bits.TXC */
#define IFX_QSPI_FLAGSCLEAR_TXC_OFF (9u)

/** \brief  Length for Ifx_QSPI_FLAGSCLEAR_Bits.USRC */
#define IFX_QSPI_FLAGSCLEAR_USRC_LEN (1u)

/** \brief  Mask for Ifx_QSPI_FLAGSCLEAR_Bits.USRC */
#define IFX_QSPI_FLAGSCLEAR_USRC_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_FLAGSCLEAR_Bits.USRC */
#define IFX_QSPI_FLAGSCLEAR_USRC_OFF (15u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.ERRORENS */
#define IFX_QSPI_GLOBALCON1_ERRORENS_LEN (9u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.ERRORENS */
#define IFX_QSPI_GLOBALCON1_ERRORENS_MSK (0x1ffu)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.ERRORENS */
#define IFX_QSPI_GLOBALCON1_ERRORENS_OFF (0u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.PT1 */
#define IFX_QSPI_GLOBALCON1_PT1_LEN (3u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.PT1 */
#define IFX_QSPI_GLOBALCON1_PT1_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.PT1 */
#define IFX_QSPI_GLOBALCON1_PT1_OFF (20u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.PT1EN */
#define IFX_QSPI_GLOBALCON1_PT1EN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.PT1EN */
#define IFX_QSPI_GLOBALCON1_PT1EN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.PT1EN */
#define IFX_QSPI_GLOBALCON1_PT1EN_OFF (11u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.PT2 */
#define IFX_QSPI_GLOBALCON1_PT2_LEN (3u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.PT2 */
#define IFX_QSPI_GLOBALCON1_PT2_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.PT2 */
#define IFX_QSPI_GLOBALCON1_PT2_OFF (23u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.PT2EN */
#define IFX_QSPI_GLOBALCON1_PT2EN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.PT2EN */
#define IFX_QSPI_GLOBALCON1_PT2EN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.PT2EN */
#define IFX_QSPI_GLOBALCON1_PT2EN_OFF (12u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.RXEN */
#define IFX_QSPI_GLOBALCON1_RXEN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.RXEN */
#define IFX_QSPI_GLOBALCON1_RXEN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.RXEN */
#define IFX_QSPI_GLOBALCON1_RXEN_OFF (10u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.RXFIFOINT */
#define IFX_QSPI_GLOBALCON1_RXFIFOINT_LEN (2u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.RXFIFOINT */
#define IFX_QSPI_GLOBALCON1_RXFIFOINT_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.RXFIFOINT */
#define IFX_QSPI_GLOBALCON1_RXFIFOINT_OFF (18u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.RXFM */
#define IFX_QSPI_GLOBALCON1_RXFM_LEN (2u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.RXFM */
#define IFX_QSPI_GLOBALCON1_RXFM_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.RXFM */
#define IFX_QSPI_GLOBALCON1_RXFM_OFF (28u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.TXEN */
#define IFX_QSPI_GLOBALCON1_TXEN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.TXEN */
#define IFX_QSPI_GLOBALCON1_TXEN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.TXEN */
#define IFX_QSPI_GLOBALCON1_TXEN_OFF (9u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.TXFIFOINT */
#define IFX_QSPI_GLOBALCON1_TXFIFOINT_LEN (2u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.TXFIFOINT */
#define IFX_QSPI_GLOBALCON1_TXFIFOINT_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.TXFIFOINT */
#define IFX_QSPI_GLOBALCON1_TXFIFOINT_OFF (16u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.TXFM */
#define IFX_QSPI_GLOBALCON1_TXFM_LEN (2u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.TXFM */
#define IFX_QSPI_GLOBALCON1_TXFM_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.TXFM */
#define IFX_QSPI_GLOBALCON1_TXFM_OFF (26u)

/** \brief  Length for Ifx_QSPI_GLOBALCON1_Bits.USREN */
#define IFX_QSPI_GLOBALCON1_USREN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON1_Bits.USREN */
#define IFX_QSPI_GLOBALCON1_USREN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON1_Bits.USREN */
#define IFX_QSPI_GLOBALCON1_USREN_OFF (15u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.AREN */
#define IFX_QSPI_GLOBALCON_AREN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.AREN */
#define IFX_QSPI_GLOBALCON_AREN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.AREN */
#define IFX_QSPI_GLOBALCON_AREN_OFF (27u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.DEL0 */
#define IFX_QSPI_GLOBALCON_DEL0_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.DEL0 */
#define IFX_QSPI_GLOBALCON_DEL0_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.DEL0 */
#define IFX_QSPI_GLOBALCON_DEL0_OFF (15u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.EN */
#define IFX_QSPI_GLOBALCON_EN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.EN */
#define IFX_QSPI_GLOBALCON_EN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.EN */
#define IFX_QSPI_GLOBALCON_EN_OFF (24u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.EXPECT */
#define IFX_QSPI_GLOBALCON_EXPECT_LEN (4u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.EXPECT */
#define IFX_QSPI_GLOBALCON_EXPECT_MSK (0xfu)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.EXPECT */
#define IFX_QSPI_GLOBALCON_EXPECT_OFF (10u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.LB */
#define IFX_QSPI_GLOBALCON_LB_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.LB */
#define IFX_QSPI_GLOBALCON_LB_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.LB */
#define IFX_QSPI_GLOBALCON_LB_OFF (14u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.MS */
#define IFX_QSPI_GLOBALCON_MS_LEN (2u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.MS */
#define IFX_QSPI_GLOBALCON_MS_MSK (0x3u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.MS */
#define IFX_QSPI_GLOBALCON_MS_OFF (25u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.RESETS */
#define IFX_QSPI_GLOBALCON_RESETS_LEN (4u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.RESETS */
#define IFX_QSPI_GLOBALCON_RESETS_MSK (0xfu)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.RESETS */
#define IFX_QSPI_GLOBALCON_RESETS_OFF (28u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.SI */
#define IFX_QSPI_GLOBALCON_SI_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.SI */
#define IFX_QSPI_GLOBALCON_SI_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.SI */
#define IFX_QSPI_GLOBALCON_SI_OFF (9u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.SRF */
#define IFX_QSPI_GLOBALCON_SRF_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.SRF */
#define IFX_QSPI_GLOBALCON_SRF_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.SRF */
#define IFX_QSPI_GLOBALCON_SRF_OFF (21u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.STIP */
#define IFX_QSPI_GLOBALCON_STIP_LEN (1u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.STIP */
#define IFX_QSPI_GLOBALCON_STIP_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.STIP */
#define IFX_QSPI_GLOBALCON_STIP_OFF (22u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.STROBE */
#define IFX_QSPI_GLOBALCON_STROBE_LEN (5u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.STROBE */
#define IFX_QSPI_GLOBALCON_STROBE_MSK (0x1fu)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.STROBE */
#define IFX_QSPI_GLOBALCON_STROBE_OFF (16u)

/** \brief  Length for Ifx_QSPI_GLOBALCON_Bits.TQ */
#define IFX_QSPI_GLOBALCON_TQ_LEN (8u)

/** \brief  Mask for Ifx_QSPI_GLOBALCON_Bits.TQ */
#define IFX_QSPI_GLOBALCON_TQ_MSK (0xffu)

/** \brief  Offset for Ifx_QSPI_GLOBALCON_Bits.TQ */
#define IFX_QSPI_GLOBALCON_TQ_OFF (0u)

/** \brief  Length for Ifx_QSPI_ID_Bits.MODNUMBER */
#define IFX_QSPI_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_QSPI_ID_Bits.MODNUMBER */
#define IFX_QSPI_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_QSPI_ID_Bits.MODNUMBER */
#define IFX_QSPI_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_QSPI_ID_Bits.MODREV */
#define IFX_QSPI_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_QSPI_ID_Bits.MODREV */
#define IFX_QSPI_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_QSPI_ID_Bits.MODREV */
#define IFX_QSPI_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_QSPI_ID_Bits.MODTYPE */
#define IFX_QSPI_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_QSPI_ID_Bits.MODTYPE */
#define IFX_QSPI_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_QSPI_ID_Bits.MODTYPE */
#define IFX_QSPI_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_QSPI_KRST0_Bits.RST */
#define IFX_QSPI_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_QSPI_KRST0_Bits.RST */
#define IFX_QSPI_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_KRST0_Bits.RST */
#define IFX_QSPI_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_QSPI_KRST0_Bits.RSTSTAT */
#define IFX_QSPI_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_QSPI_KRST0_Bits.RSTSTAT */
#define IFX_QSPI_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_KRST0_Bits.RSTSTAT */
#define IFX_QSPI_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_QSPI_KRST1_Bits.RST */
#define IFX_QSPI_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_QSPI_KRST1_Bits.RST */
#define IFX_QSPI_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_KRST1_Bits.RST */
#define IFX_QSPI_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_QSPI_KRSTCLR_Bits.CLR */
#define IFX_QSPI_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_QSPI_KRSTCLR_Bits.CLR */
#define IFX_QSPI_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_KRSTCLR_Bits.CLR */
#define IFX_QSPI_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_QSPI_MIXENTRY_Bits.E */
#define IFX_QSPI_MIXENTRY_E_LEN (32u)

/** \brief  Mask for Ifx_QSPI_MIXENTRY_Bits.E */
#define IFX_QSPI_MIXENTRY_E_MSK (0xffffffffu)

/** \brief  Offset for Ifx_QSPI_MIXENTRY_Bits.E */
#define IFX_QSPI_MIXENTRY_E_OFF (0u)

/** \brief  Length for Ifx_QSPI_OCS_Bits.SUS */
#define IFX_QSPI_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_QSPI_OCS_Bits.SUS */
#define IFX_QSPI_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_QSPI_OCS_Bits.SUS */
#define IFX_QSPI_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_QSPI_OCS_Bits.SUS_P */
#define IFX_QSPI_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_QSPI_OCS_Bits.SUS_P */
#define IFX_QSPI_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_OCS_Bits.SUS_P */
#define IFX_QSPI_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_QSPI_OCS_Bits.SUSSTA */
#define IFX_QSPI_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_QSPI_OCS_Bits.SUSSTA */
#define IFX_QSPI_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_OCS_Bits.SUSSTA */
#define IFX_QSPI_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_QSPI_PISEL_Bits.MRIS */
#define IFX_QSPI_PISEL_MRIS_LEN (3u)

/** \brief  Mask for Ifx_QSPI_PISEL_Bits.MRIS */
#define IFX_QSPI_PISEL_MRIS_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_PISEL_Bits.MRIS */
#define IFX_QSPI_PISEL_MRIS_OFF (0u)

/** \brief  Length for Ifx_QSPI_PISEL_Bits.SCIS */
#define IFX_QSPI_PISEL_SCIS_LEN (3u)

/** \brief  Mask for Ifx_QSPI_PISEL_Bits.SCIS */
#define IFX_QSPI_PISEL_SCIS_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_PISEL_Bits.SCIS */
#define IFX_QSPI_PISEL_SCIS_OFF (8u)

/** \brief  Length for Ifx_QSPI_PISEL_Bits.SLSIS */
#define IFX_QSPI_PISEL_SLSIS_LEN (3u)

/** \brief  Mask for Ifx_QSPI_PISEL_Bits.SLSIS */
#define IFX_QSPI_PISEL_SLSIS_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_PISEL_Bits.SLSIS */
#define IFX_QSPI_PISEL_SLSIS_OFF (12u)

/** \brief  Length for Ifx_QSPI_PISEL_Bits.SRIS */
#define IFX_QSPI_PISEL_SRIS_LEN (3u)

/** \brief  Mask for Ifx_QSPI_PISEL_Bits.SRIS */
#define IFX_QSPI_PISEL_SRIS_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_PISEL_Bits.SRIS */
#define IFX_QSPI_PISEL_SRIS_OFF (4u)

/** \brief  Length for Ifx_QSPI_RXEXIT_Bits.E */
#define IFX_QSPI_RXEXIT_E_LEN (32u)

/** \brief  Mask for Ifx_QSPI_RXEXIT_Bits.E */
#define IFX_QSPI_RXEXIT_E_MSK (0xffffffffu)

/** \brief  Offset for Ifx_QSPI_RXEXIT_Bits.E */
#define IFX_QSPI_RXEXIT_E_OFF (0u)

/** \brief  Length for Ifx_QSPI_RXEXITD_Bits.E */
#define IFX_QSPI_RXEXITD_E_LEN (32u)

/** \brief  Mask for Ifx_QSPI_RXEXITD_Bits.E */
#define IFX_QSPI_RXEXITD_E_MSK (0xffffffffu)

/** \brief  Offset for Ifx_QSPI_RXEXITD_Bits.E */
#define IFX_QSPI_RXEXITD_E_OFF (0u)

/** \brief  Length for Ifx_QSPI_SSOC_Bits.AOL */
#define IFX_QSPI_SSOC_AOL_LEN (16u)

/** \brief  Mask for Ifx_QSPI_SSOC_Bits.AOL */
#define IFX_QSPI_SSOC_AOL_MSK (0xffffu)

/** \brief  Offset for Ifx_QSPI_SSOC_Bits.AOL */
#define IFX_QSPI_SSOC_AOL_OFF (0u)

/** \brief  Length for Ifx_QSPI_SSOC_Bits.OEN */
#define IFX_QSPI_SSOC_OEN_LEN (16u)

/** \brief  Mask for Ifx_QSPI_SSOC_Bits.OEN */
#define IFX_QSPI_SSOC_OEN_MSK (0xffffu)

/** \brief  Offset for Ifx_QSPI_SSOC_Bits.OEN */
#define IFX_QSPI_SSOC_OEN_OFF (16u)

/** \brief  Length for Ifx_QSPI_STATUS1_Bits.BITCOUNT */
#define IFX_QSPI_STATUS1_BITCOUNT_LEN (8u)

/** \brief  Mask for Ifx_QSPI_STATUS1_Bits.BITCOUNT */
#define IFX_QSPI_STATUS1_BITCOUNT_MSK (0xffu)

/** \brief  Offset for Ifx_QSPI_STATUS1_Bits.BITCOUNT */
#define IFX_QSPI_STATUS1_BITCOUNT_OFF (0u)

/** \brief  Length for Ifx_QSPI_STATUS1_Bits.BRD */
#define IFX_QSPI_STATUS1_BRD_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS1_Bits.BRD */
#define IFX_QSPI_STATUS1_BRD_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS1_Bits.BRD */
#define IFX_QSPI_STATUS1_BRD_OFF (29u)

/** \brief  Length for Ifx_QSPI_STATUS1_Bits.BRDEN */
#define IFX_QSPI_STATUS1_BRDEN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS1_Bits.BRDEN */
#define IFX_QSPI_STATUS1_BRDEN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS1_Bits.BRDEN */
#define IFX_QSPI_STATUS1_BRDEN_OFF (28u)

/** \brief  Length for Ifx_QSPI_STATUS1_Bits.SPD */
#define IFX_QSPI_STATUS1_SPD_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS1_Bits.SPD */
#define IFX_QSPI_STATUS1_SPD_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS1_Bits.SPD */
#define IFX_QSPI_STATUS1_SPD_OFF (31u)

/** \brief  Length for Ifx_QSPI_STATUS1_Bits.SPDEN */
#define IFX_QSPI_STATUS1_SPDEN_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS1_Bits.SPDEN */
#define IFX_QSPI_STATUS1_SPDEN_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS1_Bits.SPDEN */
#define IFX_QSPI_STATUS1_SPDEN_OFF (30u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.ERRORFLAGS */
#define IFX_QSPI_STATUS_ERRORFLAGS_LEN (9u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.ERRORFLAGS */
#define IFX_QSPI_STATUS_ERRORFLAGS_MSK (0x1ffu)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.ERRORFLAGS */
#define IFX_QSPI_STATUS_ERRORFLAGS_OFF (0u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.PHASE */
#define IFX_QSPI_STATUS_PHASE_LEN (4u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.PHASE */
#define IFX_QSPI_STATUS_PHASE_MSK (0xfu)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.PHASE */
#define IFX_QSPI_STATUS_PHASE_OFF (28u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.PT1F */
#define IFX_QSPI_STATUS_PT1F_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.PT1F */
#define IFX_QSPI_STATUS_PT1F_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.PT1F */
#define IFX_QSPI_STATUS_PT1F_OFF (11u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.PT2F */
#define IFX_QSPI_STATUS_PT2F_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.PT2F */
#define IFX_QSPI_STATUS_PT2F_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.PT2F */
#define IFX_QSPI_STATUS_PT2F_OFF (12u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.RPV */
#define IFX_QSPI_STATUS_RPV_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.RPV */
#define IFX_QSPI_STATUS_RPV_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.RPV */
#define IFX_QSPI_STATUS_RPV_OFF (26u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.RXF */
#define IFX_QSPI_STATUS_RXF_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.RXF */
#define IFX_QSPI_STATUS_RXF_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.RXF */
#define IFX_QSPI_STATUS_RXF_OFF (10u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.RXFIFOLEVEL */
#define IFX_QSPI_STATUS_RXFIFOLEVEL_LEN (3u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.RXFIFOLEVEL */
#define IFX_QSPI_STATUS_RXFIFOLEVEL_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.RXFIFOLEVEL */
#define IFX_QSPI_STATUS_RXFIFOLEVEL_OFF (19u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.SLAVESEL */
#define IFX_QSPI_STATUS_SLAVESEL_LEN (4u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.SLAVESEL */
#define IFX_QSPI_STATUS_SLAVESEL_MSK (0xfu)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.SLAVESEL */
#define IFX_QSPI_STATUS_SLAVESEL_OFF (22u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.TPV */
#define IFX_QSPI_STATUS_TPV_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.TPV */
#define IFX_QSPI_STATUS_TPV_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.TPV */
#define IFX_QSPI_STATUS_TPV_OFF (27u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.TXF */
#define IFX_QSPI_STATUS_TXF_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.TXF */
#define IFX_QSPI_STATUS_TXF_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.TXF */
#define IFX_QSPI_STATUS_TXF_OFF (9u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.TXFIFOLEVEL */
#define IFX_QSPI_STATUS_TXFIFOLEVEL_LEN (3u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.TXFIFOLEVEL */
#define IFX_QSPI_STATUS_TXFIFOLEVEL_MSK (0x7u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.TXFIFOLEVEL */
#define IFX_QSPI_STATUS_TXFIFOLEVEL_OFF (16u)

/** \brief  Length for Ifx_QSPI_STATUS_Bits.USRF */
#define IFX_QSPI_STATUS_USRF_LEN (1u)

/** \brief  Mask for Ifx_QSPI_STATUS_Bits.USRF */
#define IFX_QSPI_STATUS_USRF_MSK (0x1u)

/** \brief  Offset for Ifx_QSPI_STATUS_Bits.USRF */
#define IFX_QSPI_STATUS_USRF_OFF (15u)

/** \brief  Length for Ifx_QSPI_XXLCON_Bits.BYTECOUNT */
#define IFX_QSPI_XXLCON_BYTECOUNT_LEN (16u)

/** \brief  Mask for Ifx_QSPI_XXLCON_Bits.BYTECOUNT */
#define IFX_QSPI_XXLCON_BYTECOUNT_MSK (0xffffu)

/** \brief  Offset for Ifx_QSPI_XXLCON_Bits.BYTECOUNT */
#define IFX_QSPI_XXLCON_BYTECOUNT_OFF (16u)

/** \brief  Length for Ifx_QSPI_XXLCON_Bits.XDL */
#define IFX_QSPI_XXLCON_XDL_LEN (16u)

/** \brief  Mask for Ifx_QSPI_XXLCON_Bits.XDL */
#define IFX_QSPI_XXLCON_XDL_MSK (0xffffu)

/** \brief  Offset for Ifx_QSPI_XXLCON_Bits.XDL */
#define IFX_QSPI_XXLCON_XDL_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXQSPI_BF_H */
