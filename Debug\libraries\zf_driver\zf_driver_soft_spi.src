	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc41296a --dep-file=zf_driver_soft_spi.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_soft_spi.src ../libraries/zf_driver/zf_driver_soft_spi.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_soft_spi.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_8bit_data_handler',code,cluster('soft_spi_8bit_data_handler')
	.sect	'.text.zf_driver_soft_spi.soft_spi_8bit_data_handler'
	.align	2
	
; Function soft_spi_8bit_data_handler
.L88:
soft_spi_8bit_data_handler:	.type	func
	mov.aa	a15,a4
.L367:
	mov	d8,d4
.L368:
	mov	d9,#0
.L370:
	ld.bu	d15,[a15]
.L648:
	jz.t	d15:7,.L2
.L649:
	ld.h	d4,[a15]8
.L365:
	call	get_port
.L366:
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]8
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L2:
	ld.bu	d15,[a15]
	and	d15,#63
.L650:
	jeq	d15,#0,.L3
.L651:
	ld.bu	d15,[a15]
	and	d15,#63
.L652:
	jne	d15,#1,.L4
.L3:
	ld.h	d4,[a15]2
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]2
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L5
.L4:
	ld.h	d4,[a15]2
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]2
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L5:
	ld.bu	d15,[a15]
	and	d15,#63
.L653:
	mov	d0,#2
.L654:
	div	e0,d15,d0
.L655:
	jne	d1,#0,.L6
.L656:
	mov	d10,#8
.L372:
	j	.L7
.L8:
	jz.t	d8:7,.L9
.L657:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L10
.L9:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L10:
	ld.w	d0,[a15]10
.L373:
	j	.L11
.L12:
.L11:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L12
.L332:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L374:
	sha	d8,#1
.L369:
	extr.u	d8,d8,#0,#8
.L375:
	sha	d9,#1
.L371:
	extr.u	d9,d9,#0,#8
.L377:
	ld.bu	d15,[a15]
.L658:
	jz.t	d15:6,.L13
.L659:
	ld.h	d4,[a15]6
	call	gpio_get_level
.L660:
	or	d9,d2
.L13:
	ld.w	d0,[a15]10
.L379:
	j	.L14
.L15:
.L14:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L15
.L334:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L380:
	add	d10,#-1
.L7:
	jge.u	d10,#1,.L8
.L661:
	j	.L16
.L6:
	mov	d10,#8
.L381:
	j	.L17
.L18:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L662:
	jz.t	d8:7,.L19
.L663:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L20
.L19:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L20:
	ld.w	d0,[a15]10
.L382:
	j	.L21
.L22:
.L21:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L22
.L336:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L383:
	sha	d8,#1
.L376:
	extr.u	d8,d8,#0,#8
.L384:
	sha	d9,#1
.L378:
	extr.u	d9,d9,#0,#8
.L385:
	ld.bu	d15,[a15]
.L664:
	jz.t	d15:6,.L23
.L665:
	ld.h	d4,[a15]6
	call	gpio_get_level
.L666:
	or	d9,d2
.L23:
	ld.w	d0,[a15]10
.L386:
	j	.L24
.L25:
.L24:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L25
.L338:
	add	d10,#-1
.L17:
	jge.u	d10,#1,.L18
.L16:
	ld.bu	d15,[a15]
.L667:
	jz.t	d15:7,.L26
.L668:
	ld.h	d4,[a15]8
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]8
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L26:
	mov	d2,d9
.L387:
	j	.L27
.L27:
	ret
.L325:
	
__soft_spi_8bit_data_handler_function_end:
	.size	soft_spi_8bit_data_handler,__soft_spi_8bit_data_handler_function_end-soft_spi_8bit_data_handler
.L222:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_16bit_data_handler',code,cluster('soft_spi_16bit_data_handler')
	.sect	'.text.zf_driver_soft_spi.soft_spi_16bit_data_handler'
	.align	2
	
; Function soft_spi_16bit_data_handler
.L90:
soft_spi_16bit_data_handler:	.type	func
	mov.aa	a15,a4
.L390:
	mov	d8,d4
.L391:
	mov	d9,#0
.L393:
	ld.bu	d15,[a15]
.L673:
	jz.t	d15:7,.L28
.L674:
	ld.h	d4,[a15]8
.L388:
	call	get_port
.L389:
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]8
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L28:
	ld.bu	d15,[a15]
	and	d15,#63
.L675:
	jeq	d15,#0,.L29
.L676:
	ld.bu	d15,[a15]
	and	d15,#63
.L677:
	jne	d15,#1,.L30
.L29:
	ld.h	d4,[a15]2
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]2
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L31
.L30:
	ld.h	d4,[a15]2
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]2
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L31:
	ld.bu	d15,[a15]
	and	d15,#63
.L678:
	mov	d0,#2
.L679:
	div	e0,d15,d0
.L680:
	jne	d1,#0,.L32
.L681:
	mov	d10,#16
.L395:
	j	.L33
.L34:
	jz.t	d8:15,.L35
.L682:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L36
.L35:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L36:
	ld.w	d0,[a15]10
.L396:
	j	.L37
.L38:
.L37:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L38
.L347:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L397:
	sha	d8,#1
.L392:
	extr.u	d8,d8,#0,#16
.L398:
	sha	d9,#1
.L394:
	extr.u	d9,d9,#0,#16
.L400:
	ld.bu	d15,[a15]
.L683:
	jz.t	d15:6,.L39
.L684:
	ld.h	d4,[a15]6
	call	gpio_get_level
.L685:
	or	d9,d2
.L39:
	ld.w	d0,[a15]10
.L402:
	j	.L40
.L41:
.L40:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L41
.L349:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L403:
	add	d10,#-1
.L33:
	jge.u	d10,#1,.L34
.L686:
	j	.L42
.L32:
	mov	d10,#16
.L404:
	j	.L43
.L44:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L687:
	jz.t	d8:15,.L45
.L688:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L46
.L45:
	ld.h	d4,[a15]4
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	ld.h	d15,[a15]4
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L46:
	ld.w	d0,[a15]10
.L405:
	j	.L47
.L48:
.L47:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L48
.L351:
	ld.h	d4,[a15]2
	call	gpio_toggle_level
.L406:
	sha	d8,#1
.L399:
	extr.u	d8,d8,#0,#16
.L407:
	sha	d9,#1
.L401:
	extr.u	d9,d9,#0,#16
.L408:
	ld.bu	d15,[a15]
.L689:
	jz.t	d15:6,.L49
.L690:
	ld.h	d4,[a15]6
	call	gpio_get_level
.L691:
	or	d9,d2
.L49:
	ld.w	d0,[a15]10
.L409:
	j	.L50
.L51:
.L50:
	mov	d15,d0
	add	d0,#-1
	jne	d15,#0,.L51
.L353:
	add	d10,#-1
.L43:
	jge.u	d10,#1,.L44
.L42:
	ld.bu	d15,[a15]
.L692:
	jz.t	d15:7,.L52
.L693:
	ld.h	d4,[a15]8
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	ld.h	d15,[a15]8
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L52:
	mov	d2,d9
.L410:
	j	.L53
.L53:
	ret
.L340:
	
__soft_spi_16bit_data_handler_function_end:
	.size	soft_spi_16bit_data_handler,__soft_spi_16bit_data_handler_function_end-soft_spi_16bit_data_handler
.L227:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_8bit',code,cluster('soft_spi_write_8bit')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_8bit'
	.align	2
	
	.global	soft_spi_write_8bit
; Function soft_spi_write_8bit
.L92:
soft_spi_write_8bit:	.type	func
	call	soft_spi_8bit_data_handler
.L411:
	ret
.L238:
	
__soft_spi_write_8bit_function_end:
	.size	soft_spi_write_8bit,__soft_spi_write_8bit_function_end-soft_spi_write_8bit
.L137:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_8bit_array',code,cluster('soft_spi_write_8bit_array')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_8bit_array'
	.align	2
	
	.global	soft_spi_write_8bit_array
; Function soft_spi_write_8bit_array
.L94:
soft_spi_write_8bit_array:	.type	func
	mov.aa	a15,a4
.L412:
	mov.aa	a12,a5
.L413:
	mov	d8,d4
.L414:
	j	.L54
.L55:
	ld.bu	d4,[a12]
.L529:
	add.a	a12,#1
	mov.aa	a4,a15
.L415:
	call	soft_spi_8bit_data_handler
.L54:
	mov	d15,d8
	add	d8,#-1
.L530:
	jne	d15,#0,.L55
.L531:
	ret
.L243:
	
__soft_spi_write_8bit_array_function_end:
	.size	soft_spi_write_8bit_array,__soft_spi_write_8bit_array_function_end-soft_spi_write_8bit_array
.L142:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_16bit',code,cluster('soft_spi_write_16bit')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_16bit'
	.align	2
	
	.global	soft_spi_write_16bit
; Function soft_spi_write_16bit
.L96:
soft_spi_write_16bit:	.type	func
	call	soft_spi_16bit_data_handler
.L416:
	ret
.L249:
	
__soft_spi_write_16bit_function_end:
	.size	soft_spi_write_16bit,__soft_spi_write_16bit_function_end-soft_spi_write_16bit
.L147:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_16bit_array',code,cluster('soft_spi_write_16bit_array')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_16bit_array'
	.align	2
	
	.global	soft_spi_write_16bit_array
; Function soft_spi_write_16bit_array
.L98:
soft_spi_write_16bit_array:	.type	func
	mov.aa	a15,a4
.L417:
	mov.aa	a12,a5
.L418:
	mov	d8,d4
.L419:
	j	.L56
.L57:
	ld.hu	d4,[a12]0
.L540:
	add.a	a12,#2
	mov.aa	a4,a15
.L420:
	call	soft_spi_16bit_data_handler
.L56:
	mov	d15,d8
	add	d8,#-1
.L541:
	jne	d15,#0,.L57
.L542:
	ret
.L253:
	
__soft_spi_write_16bit_array_function_end:
	.size	soft_spi_write_16bit_array,__soft_spi_write_16bit_array_function_end-soft_spi_write_16bit_array
.L152:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_8bit_register',code,cluster('soft_spi_write_8bit_register')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_8bit_register'
	.align	2
	
	.global	soft_spi_write_8bit_register
; Function soft_spi_write_8bit_register
.L100:
soft_spi_write_8bit_register:	.type	func
	mov.aa	a15,a4
.L422:
	mov	d15,d5
.L423:
	mov.aa	a4,a15
	call	soft_spi_8bit_data_handler
.L421:
	mov.aa	a4,a15
.L424:
	mov	d4,d15
.L426:
	call	soft_spi_8bit_data_handler
.L425:
	ret
.L258:
	
__soft_spi_write_8bit_register_function_end:
	.size	soft_spi_write_8bit_register,__soft_spi_write_8bit_register_function_end-soft_spi_write_8bit_register
.L157:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_8bit_registers',code,cluster('soft_spi_write_8bit_registers')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_8bit_registers'
	.align	2
	
	.global	soft_spi_write_8bit_registers
; Function soft_spi_write_8bit_registers
.L102:
soft_spi_write_8bit_registers:	.type	func
	mov.aa	a15,a4
.L428:
	mov.aa	a12,a5
.L429:
	mov	d8,d5
.L430:
	mov.aa	a4,a15
	call	soft_spi_8bit_data_handler
.L427:
	j	.L58
.L59:
	ld.bu	d4,[a12]
.L551:
	add.a	a12,#1
	mov.aa	a4,a15
.L431:
	call	soft_spi_8bit_data_handler
.L58:
	mov	d15,d8
	add	d8,#-1
.L552:
	jne	d15,#0,.L59
.L553:
	ret
.L264:
	
__soft_spi_write_8bit_registers_function_end:
	.size	soft_spi_write_8bit_registers,__soft_spi_write_8bit_registers_function_end-soft_spi_write_8bit_registers
.L162:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_16bit_register',code,cluster('soft_spi_write_16bit_register')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_16bit_register'
	.align	2
	
	.global	soft_spi_write_16bit_register
; Function soft_spi_write_16bit_register
.L104:
soft_spi_write_16bit_register:	.type	func
	mov.aa	a15,a4
.L433:
	mov	d15,d5
.L434:
	mov.aa	a4,a15
	call	soft_spi_16bit_data_handler
.L432:
	mov.aa	a4,a15
.L435:
	mov	d4,d15
.L437:
	call	soft_spi_16bit_data_handler
.L436:
	ret
.L270:
	
__soft_spi_write_16bit_register_function_end:
	.size	soft_spi_write_16bit_register,__soft_spi_write_16bit_register_function_end-soft_spi_write_16bit_register
.L167:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_write_16bit_registers',code,cluster('soft_spi_write_16bit_registers')
	.sect	'.text.zf_driver_soft_spi.soft_spi_write_16bit_registers'
	.align	2
	
	.global	soft_spi_write_16bit_registers
; Function soft_spi_write_16bit_registers
.L106:
soft_spi_write_16bit_registers:	.type	func
	mov.aa	a15,a4
.L439:
	mov.aa	a12,a5
.L440:
	mov	d8,d5
.L441:
	mov.aa	a4,a15
	call	soft_spi_16bit_data_handler
.L438:
	j	.L60
.L61:
	ld.hu	d4,[a12]0
.L562:
	add.a	a12,#2
	mov.aa	a4,a15
.L442:
	call	soft_spi_16bit_data_handler
.L60:
	mov	d15,d8
	add	d8,#-1
.L563:
	jne	d15,#0,.L61
.L564:
	ret
.L275:
	
__soft_spi_write_16bit_registers_function_end:
	.size	soft_spi_write_16bit_registers,__soft_spi_write_16bit_registers_function_end-soft_spi_write_16bit_registers
.L172:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_8bit',code,cluster('soft_spi_read_8bit')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_8bit'
	.align	2
	
	.global	soft_spi_read_8bit
; Function soft_spi_read_8bit
.L108:
soft_spi_read_8bit:	.type	func
	mov	d4,#0
	call	soft_spi_8bit_data_handler
.L443:
	j	.L62
.L62:
	ret
.L282:
	
__soft_spi_read_8bit_function_end:
	.size	soft_spi_read_8bit,__soft_spi_read_8bit_function_end-soft_spi_read_8bit
.L177:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_8bit_array',code,cluster('soft_spi_read_8bit_array')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_8bit_array'
	.align	2
	
	.global	soft_spi_read_8bit_array
; Function soft_spi_read_8bit_array
.L110:
soft_spi_read_8bit_array:	.type	func
	mov.aa	a15,a4
.L444:
	mov.aa	a12,a5
.L445:
	mov	d8,d4
.L446:
	j	.L63
.L64:
	mov	d4,#0
	mov.aa	a4,a15
.L447:
	call	soft_spi_8bit_data_handler
.L448:
	st.b	[a12],d2
.L573:
	add.a	a12,#1
.L63:
	mov	d15,d8
	add	d8,#-1
.L574:
	jne	d15,#0,.L64
.L575:
	ret
.L284:
	
__soft_spi_read_8bit_array_function_end:
	.size	soft_spi_read_8bit_array,__soft_spi_read_8bit_array_function_end-soft_spi_read_8bit_array
.L182:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_16bit',code,cluster('soft_spi_read_16bit')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_16bit'
	.align	2
	
	.global	soft_spi_read_16bit
; Function soft_spi_read_16bit
.L112:
soft_spi_read_16bit:	.type	func
	mov	d4,#0
	call	soft_spi_16bit_data_handler
.L449:
	j	.L65
.L65:
	ret
.L289:
	
__soft_spi_read_16bit_function_end:
	.size	soft_spi_read_16bit,__soft_spi_read_16bit_function_end-soft_spi_read_16bit
.L187:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_16bit_array',code,cluster('soft_spi_read_16bit_array')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_16bit_array'
	.align	2
	
	.global	soft_spi_read_16bit_array
; Function soft_spi_read_16bit_array
.L114:
soft_spi_read_16bit_array:	.type	func
	mov.aa	a15,a4
.L450:
	mov.aa	a12,a5
.L451:
	mov	d8,d4
.L452:
	j	.L66
.L67:
	mov	d4,#0
	mov.aa	a4,a15
.L453:
	call	soft_spi_16bit_data_handler
.L454:
	st.h	[a12],d2
.L584:
	add.a	a12,#2
.L66:
	mov	d15,d8
	add	d8,#-1
.L585:
	jne	d15,#0,.L67
.L586:
	ret
.L291:
	
__soft_spi_read_16bit_array_function_end:
	.size	soft_spi_read_16bit_array,__soft_spi_read_16bit_array_function_end-soft_spi_read_16bit_array
.L192:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_8bit_register',code,cluster('soft_spi_read_8bit_register')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_8bit_register'
	.align	2
	
	.global	soft_spi_read_8bit_register
; Function soft_spi_read_8bit_register
.L116:
soft_spi_read_8bit_register:	.type	func
	mov.aa	a15,a4
.L456:
	mov.aa	a4,a15
	call	soft_spi_8bit_data_handler
.L455:
	mov	d4,#0
	mov.aa	a4,a15
.L457:
	call	soft_spi_8bit_data_handler
.L458:
	j	.L68
.L68:
	ret
.L296:
	
__soft_spi_read_8bit_register_function_end:
	.size	soft_spi_read_8bit_register,__soft_spi_read_8bit_register_function_end-soft_spi_read_8bit_register
.L197:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_8bit_registers',code,cluster('soft_spi_read_8bit_registers')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_8bit_registers'
	.align	2
	
	.global	soft_spi_read_8bit_registers
; Function soft_spi_read_8bit_registers
.L118:
soft_spi_read_8bit_registers:	.type	func
	mov.aa	a15,a4
.L460:
	mov.aa	a12,a5
.L461:
	mov	d8,d5
.L462:
	mov.aa	a4,a15
	call	soft_spi_8bit_data_handler
.L459:
	j	.L69
.L70:
	mov	d4,#0
	mov.aa	a4,a15
.L463:
	call	soft_spi_8bit_data_handler
.L464:
	st.b	[a12],d2
.L595:
	add.a	a12,#1
.L69:
	mov	d15,d8
	add	d8,#-1
.L596:
	jne	d15,#0,.L70
.L597:
	ret
.L300:
	
__soft_spi_read_8bit_registers_function_end:
	.size	soft_spi_read_8bit_registers,__soft_spi_read_8bit_registers_function_end-soft_spi_read_8bit_registers
.L202:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_16bit_register',code,cluster('soft_spi_read_16bit_register')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_16bit_register'
	.align	2
	
	.global	soft_spi_read_16bit_register
; Function soft_spi_read_16bit_register
.L120:
soft_spi_read_16bit_register:	.type	func
	mov.aa	a15,a4
.L466:
	mov.aa	a4,a15
	call	soft_spi_16bit_data_handler
.L465:
	mov	d4,#0
	mov.aa	a4,a15
.L467:
	call	soft_spi_16bit_data_handler
.L468:
	j	.L71
.L71:
	ret
.L306:
	
__soft_spi_read_16bit_register_function_end:
	.size	soft_spi_read_16bit_register,__soft_spi_read_16bit_register_function_end-soft_spi_read_16bit_register
.L207:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_read_16bit_registers',code,cluster('soft_spi_read_16bit_registers')
	.sect	'.text.zf_driver_soft_spi.soft_spi_read_16bit_registers'
	.align	2
	
	.global	soft_spi_read_16bit_registers
; Function soft_spi_read_16bit_registers
.L122:
soft_spi_read_16bit_registers:	.type	func
	mov.aa	a15,a4
.L470:
	mov.aa	a12,a5
.L471:
	mov	d8,d5
.L472:
	mov.aa	a4,a15
	call	soft_spi_16bit_data_handler
.L469:
	j	.L72
.L73:
	mov	d4,#0
	mov.aa	a4,a15
.L473:
	call	soft_spi_16bit_data_handler
.L474:
	st.h	[a12],d2
.L606:
	add.a	a12,#2
.L72:
	mov	d15,d8
	add	d8,#-1
.L607:
	jne	d15,#0,.L73
.L608:
	ret
.L310:
	
__soft_spi_read_16bit_registers_function_end:
	.size	soft_spi_read_16bit_registers,__soft_spi_read_16bit_registers_function_end-soft_spi_read_16bit_registers
.L212:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_8bit_transfer',code,cluster('soft_spi_8bit_transfer')
	.sect	'.text.zf_driver_soft_spi.soft_spi_8bit_transfer'
	.align	2
	
	.global	soft_spi_8bit_transfer
; Function soft_spi_8bit_transfer
.L124:
soft_spi_8bit_transfer:	.type	func
	mov.aa	a15,a4
.L475:
	mov.aa	a12,a5
.L476:
	mov.aa	a13,a6
.L477:
	mov	d8,d4
.L478:
	j	.L74
.L75:
	ld.bu	d4,[a12]
	mov.aa	a4,a15
.L479:
	call	soft_spi_8bit_data_handler
.L480:
	st.b	[a13],d2
.L698:
	add.a	a12,#1
.L699:
	add.a	a13,#1
.L74:
	mov	d15,d8
	add	d8,#-1
.L700:
	jne	d15,#0,.L75
.L701:
	ret
.L355:
	
__soft_spi_8bit_transfer_function_end:
	.size	soft_spi_8bit_transfer,__soft_spi_8bit_transfer_function_end-soft_spi_8bit_transfer
.L232:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_16bit_transfer',code,cluster('soft_spi_16bit_transfer')
	.sect	'.text.zf_driver_soft_spi.soft_spi_16bit_transfer'
	.align	2
	
	.global	soft_spi_16bit_transfer
; Function soft_spi_16bit_transfer
.L126:
soft_spi_16bit_transfer:	.type	func
	mov.aa	a15,a4
.L481:
	mov.aa	a12,a5
.L482:
	mov.aa	a13,a6
.L483:
	mov	d8,d4
.L484:
	j	.L76
.L77:
	ld.hu	d4,[a12]0
	mov.aa	a4,a15
.L485:
	call	soft_spi_16bit_data_handler
.L486:
	st.h	[a13],d2
.L706:
	add.a	a12,#2
.L707:
	add.a	a13,#2
.L76:
	mov	d15,d8
	add	d8,#-1
.L708:
	jne	d15,#0,.L77
.L709:
	ret
.L360:
	
__soft_spi_16bit_transfer_function_end:
	.size	soft_spi_16bit_transfer,__soft_spi_16bit_transfer_function_end-soft_spi_16bit_transfer
.L237:
	; End of function
	
	.sdecl	'.text.zf_driver_soft_spi.soft_spi_init',code,cluster('soft_spi_init')
	.sect	'.text.zf_driver_soft_spi.soft_spi_init'
	.align	2
	
	.global	soft_spi_init
; Function soft_spi_init
.L128:
soft_spi_init:	.type	func
	mov.aa	a15,a4
.L491:
	mov	e8,d5,d4
	mov	e10,d7,d6
	ld.w	d12,[a10]
.L492:
	ld.w	d13,[a10]4
.L493:
	ne	d4,d10,d11
.L488:
	movh.a	a4,#@his(.1.str)
.L490:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#526
.L487:
	call	debug_assert_handler
.L489:
	ne	d4,d10,d12
.L494:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#527
	call	debug_assert_handler
.L495:
	ne	d4,d10,d13
.L496:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#528
	call	debug_assert_handler
.L497:
	ne	d4,d11,d12
.L498:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#529
	call	debug_assert_handler
.L499:
	ne	d4,d11,d13
.L500:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#530
	call	debug_assert_handler
.L613:
	mov	d4,#0
	jne	d12,d13,.L78
	mov.u	d15,#65535
	jne	d15,d13,.L79
.L78:
	mov	d4,#1
.L79:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#531
	call	debug_assert_handler
.L501:
	lt.u	d4,d8,#4
.L502:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#533
	call	debug_assert_handler
.L614:
	ld.bu	d0,[a15]
.L503:
	insert	d15,d0,d8,#0,#6
.L504:
	st.b	[a15],d15
.L505:
	st.w	[a15]10,d9
.L506:
	st.h	[a15]2,d10
.L507:
	st.h	[a15]4,d11
.L508:
	jeq	d8,#0,.L80
.L615:
	jne	d8,#1,.L81
.L80:
	mov	d5,#1
.L616:
	mov	d6,#0
.L617:
	mov	d7,#3
.L509:
	mov	d4,d10
.L510:
	call	gpio_init
.L511:
	j	.L82
.L81:
	mov	d5,#1
.L618:
	mov	d6,#1
.L619:
	mov	d7,#3
.L512:
	mov	d4,d10
.L513:
	call	gpio_init
.L82:
	mov	d5,#1
.L620:
	mov	d6,#1
.L621:
	mov	d7,#3
.L514:
	mov	d4,d11
.L515:
	call	gpio_init
.L516:
	mov.u	d15,#65535
.L622:
	jne	d15,d12,.L83
.L623:
	ld.bu	d15,[a15]
.L624:
	insert	d15,d15,#0,#6,#1
	st.b	[a15],d15
.L625:
	j	.L84
.L83:
	ld.bu	d15,[a15]
.L626:
	or	d15,#64
	st.b	[a15],d15
.L627:
	extr	d15,d12,#0,#16
.L628:
	st.h	[a15]6,d15
.L629:
	extr	d4,d12,#0,#16
.L630:
	mov	d5,#0
.L631:
	mov	d6,#1
.L632:
	mov	d7,#0
	call	gpio_init
.L84:
	mov.u	d15,#65535
.L633:
	jne	d15,d13,.L85
.L634:
	ld.bu	d15,[a15]
.L635:
	insert	d15,d15,#0,#7,#1
	st.b	[a15],d15
.L636:
	j	.L86
.L85:
	ld.bu	d15,[a15]
.L637:
	or	d15,#128
	st.b	[a15],d15
.L638:
	extr	d15,d13,#0,#16
.L639:
	st.h	[a15]8,d15
.L640:
	extr	d4,d13,#0,#16
.L641:
	mov	d5,#1
.L642:
	mov	d6,#1
.L643:
	mov	d7,#3
	call	gpio_init
.L86:
	ret
.L316:
	
__soft_spi_init_function_end:
	.size	soft_spi_init,__soft_spi_init_function_end-soft_spi_init
.L217:
	; End of function
	
	.sdecl	'.rodata.zf_driver_soft_spi..1.str',data,rom
	.sect	'.rodata.zf_driver_soft_spi..1.str'
.1.str:	.type	object
	.size	.1.str,44
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,115,111,102,116,95,115,112
	.byte	105,46,99
	.space	1
	.calls	'soft_spi_8bit_data_handler','get_port'
	.calls	'soft_spi_8bit_data_handler','gpio_toggle_level'
	.calls	'soft_spi_8bit_data_handler','gpio_get_level'
	.calls	'soft_spi_16bit_data_handler','get_port'
	.calls	'soft_spi_16bit_data_handler','gpio_toggle_level'
	.calls	'soft_spi_16bit_data_handler','gpio_get_level'
	.calls	'soft_spi_write_8bit','soft_spi_8bit_data_handler'
	.calls	'soft_spi_write_8bit_array','soft_spi_8bit_data_handler'
	.calls	'soft_spi_write_16bit','soft_spi_16bit_data_handler'
	.calls	'soft_spi_write_16bit_array','soft_spi_16bit_data_handler'
	.calls	'soft_spi_write_8bit_register','soft_spi_8bit_data_handler'
	.calls	'soft_spi_write_8bit_registers','soft_spi_8bit_data_handler'
	.calls	'soft_spi_write_16bit_register','soft_spi_16bit_data_handler'
	.calls	'soft_spi_write_16bit_registers','soft_spi_16bit_data_handler'
	.calls	'soft_spi_read_8bit','soft_spi_8bit_data_handler'
	.calls	'soft_spi_read_8bit_array','soft_spi_8bit_data_handler'
	.calls	'soft_spi_read_16bit','soft_spi_16bit_data_handler'
	.calls	'soft_spi_read_16bit_array','soft_spi_16bit_data_handler'
	.calls	'soft_spi_read_8bit_register','soft_spi_8bit_data_handler'
	.calls	'soft_spi_read_8bit_registers','soft_spi_8bit_data_handler'
	.calls	'soft_spi_read_16bit_register','soft_spi_16bit_data_handler'
	.calls	'soft_spi_read_16bit_registers','soft_spi_16bit_data_handler'
	.calls	'soft_spi_8bit_transfer','soft_spi_8bit_data_handler'
	.calls	'soft_spi_16bit_transfer','soft_spi_16bit_data_handler'
	.calls	'soft_spi_init','debug_assert_handler'
	.calls	'soft_spi_init','gpio_init'
	.calls	'soft_spi_8bit_data_handler','',0
	.calls	'soft_spi_16bit_data_handler','',0
	.calls	'soft_spi_write_8bit','',0
	.calls	'soft_spi_write_8bit_array','',0
	.calls	'soft_spi_write_16bit','',0
	.calls	'soft_spi_write_16bit_array','',0
	.calls	'soft_spi_write_8bit_register','',0
	.calls	'soft_spi_write_8bit_registers','',0
	.calls	'soft_spi_write_16bit_register','',0
	.calls	'soft_spi_write_16bit_registers','',0
	.calls	'soft_spi_read_8bit','',0
	.calls	'soft_spi_read_8bit_array','',0
	.calls	'soft_spi_read_16bit','',0
	.calls	'soft_spi_read_16bit_array','',0
	.calls	'soft_spi_read_8bit_register','',0
	.calls	'soft_spi_read_8bit_registers','',0
	.calls	'soft_spi_read_16bit_register','',0
	.calls	'soft_spi_read_16bit_registers','',0
	.calls	'soft_spi_8bit_transfer','',0
	.calls	'soft_spi_16bit_transfer','',0
	.extern	debug_assert_handler
	.extern	get_port
	.extern	gpio_get_level
	.extern	gpio_toggle_level
	.extern	gpio_init
	.calls	'soft_spi_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L130:
	.word	38960
	.half	3
	.word	.L131
	.byte	4
.L129:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L132
	.byte	2,1,1,3
	.word	206
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	209
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	254
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	266
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	346
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	320
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	352
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	352
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	320
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L281:
	.byte	7
	.byte	'unsigned char',0,1,8
.L251:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	816
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1387
	.byte	4,2,35,0,0,14,4
	.word	461
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1515
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1730
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1945
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	461
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	461
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2382
	.byte	4,2,35,0,0,14,24
	.word	461
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	461
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3009
	.byte	4,2,35,0,0,14,8
	.word	461
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3334
	.byte	4,2,35,0,0,14,12
	.word	461
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4326
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	438
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4642
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	478
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5163
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5337
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6002
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6350
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6474
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6738
	.byte	4,2,35,0,0,14,76
	.word	461
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6991
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7078
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	776
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1347
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1466
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1506
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1690
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1905
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2122
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2342
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1506
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2656
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2696
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2969
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3285
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3325
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3625
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3665
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4000
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4286
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3325
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4433
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4602
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4774
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4949
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5123
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5297
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5473
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5629
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5962
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6310
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3325
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6434
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6683
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6942
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6982
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7038
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7605
	.byte	4,3,35,252,1,0,16
	.word	7645
	.byte	3
	.word	8248
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8253
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	461
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8258
	.byte	6,0,7
	.byte	'char',0,1,6,3
	.word	8439
	.byte	19
	.byte	'debug_assert_handler',0,5,112,9,1,1,1,1,5
	.byte	'pass',0,5,112,47
	.word	461
	.byte	5
	.byte	'file',0,5,112,59
	.word	8447
	.byte	5
	.byte	'line',0,5,112,69
	.word	454
	.byte	0,20
	.word	214
	.byte	21
	.word	240
	.byte	6,0,20
	.word	275
	.byte	21
	.word	307
	.byte	6,0,20
	.word	357
	.byte	21
	.word	376
	.byte	6,0,20
	.word	392
	.byte	21
	.word	407
	.byte	21
	.word	421
	.byte	6,0,20
	.word	8361
	.byte	21
	.word	8389
	.byte	21
	.word	8403
	.byte	21
	.word	8421
	.byte	6,0
.L320:
	.byte	17,6,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'get_port',0,6,114,13
	.word	8253
	.byte	1,1,1,1,5
	.byte	'pin',0,6,114,56
	.word	8596
	.byte	0,22
	.byte	'gpio_get_level',0,6,140,1,7
	.word	461
	.byte	1,1,1,1,5
	.byte	'pin',0,6,140,1,40
	.word	8596
	.byte	0,19
	.byte	'gpio_toggle_level',0,6,141,1,7,1,1,1,1,5
	.byte	'pin',0,6,141,1,40
	.word	8596
	.byte	0,17,6,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,6,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,19
	.byte	'gpio_init',0,6,143,1,7,1,1,1,1,5
	.byte	'pin',0,6,143,1,40
	.word	8596
	.byte	5
	.byte	'dir',0,6,143,1,59
	.word	10653
	.byte	5
	.byte	'dat',0,6,143,1,70
	.word	461
	.byte	5
	.byte	'pinconf',0,6,143,1,90
	.word	10671
	.byte	0,23,7,45,9,1,11
	.byte	'mode',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'use_miso',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'use_cs',0,1
	.word	461
	.byte	1,0,2,35,0,0
.L247:
	.byte	7
	.byte	'unsigned long int',0,4,7,23,7,52,9,16,13
	.byte	'config',0
	.word	10834
	.byte	1,2,35,0,13
	.byte	'sck_pin',0
	.word	8596
	.byte	2,2,35,2,13
	.byte	'mosi_pin',0
	.word	8596
	.byte	2,2,35,4,13
	.byte	'miso_pin',0
	.word	8596
	.byte	2,2,35,6,13
	.byte	'cs_pin',0
	.word	8596
	.byte	2,2,35,8,13
	.byte	'delay',0
	.word	10894
	.byte	4,2,35,10,0
.L239:
	.byte	3
	.word	10915
.L241:
	.byte	24
	.word	461
	.byte	24
	.word	461
.L245:
	.byte	3
	.word	11031
	.byte	24
	.word	478
.L255:
	.byte	3
	.word	11041
.L260:
	.byte	24
	.word	461
.L262:
	.byte	24
	.word	461
.L266:
	.byte	24
	.word	461
.L272:
	.byte	24
	.word	478
.L277:
	.byte	24
	.word	478
.L286:
	.byte	3
	.word	461
.L293:
	.byte	3
	.word	478
.L298:
	.byte	24
	.word	461
.L302:
	.byte	24
	.word	461
.L308:
	.byte	24
	.word	478
.L312:
	.byte	24
	.word	478
.L327:
	.byte	24
	.word	461
.L342:
	.byte	24
	.word	478
	.byte	7
	.byte	'short int',0,2,5,25
	.byte	'__wchar_t',0,8,1,1
	.word	11116
	.byte	25
	.byte	'__size_t',0,8,1,1
	.word	438
	.byte	25
	.byte	'__ptrdiff_t',0,8,1,1
	.word	454
	.byte	26,1,3
	.word	11184
	.byte	25
	.byte	'__codeptr',0,8,1,1
	.word	11186
	.byte	25
	.byte	'__intptr_t',0,8,1,1
	.word	454
	.byte	25
	.byte	'__uintptr_t',0,8,1,1
	.word	438
	.byte	25
	.byte	'_iob_flag_t',0,9,82,25
	.word	478
	.byte	25
	.byte	'boolean',0,10,101,29
	.word	461
	.byte	25
	.byte	'uint8',0,10,105,29
	.word	461
	.byte	25
	.byte	'uint16',0,10,109,29
	.word	478
	.byte	25
	.byte	'uint32',0,10,113,29
	.word	10894
	.byte	25
	.byte	'uint64',0,10,118,29
	.word	320
	.byte	25
	.byte	'sint16',0,10,126,29
	.word	11116
	.byte	7
	.byte	'long int',0,4,5,25
	.byte	'sint32',0,10,131,1,29
	.word	11358
	.byte	7
	.byte	'long long int',0,8,5,25
	.byte	'sint64',0,10,138,1,29
	.word	11386
	.byte	25
	.byte	'float32',0,10,167,1,29
	.word	266
	.byte	25
	.byte	'pvoid',0,11,57,28
	.word	352
	.byte	25
	.byte	'Ifx_TickTime',0,11,79,28
	.word	11386
	.byte	7
	.byte	'char',0,1,6,25
	.byte	'int8',0,12,54,29
	.word	11471
	.byte	25
	.byte	'int16',0,12,55,29
	.word	11116
	.byte	25
	.byte	'int32',0,12,56,29
	.word	454
	.byte	25
	.byte	'int64',0,12,57,29
	.word	11386
	.byte	25
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7078
	.byte	25
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6991
	.byte	25
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3334
	.byte	25
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1387
	.byte	25
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2382
	.byte	25
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1515
	.byte	25
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2162
	.byte	25
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1730
	.byte	25
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1945
	.byte	25
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6350
	.byte	25
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6474
	.byte	25
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6558
	.byte	25
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6738
	.byte	25
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4989
	.byte	25
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5513
	.byte	25
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5163
	.byte	25
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5337
	.byte	25
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6002
	.byte	25
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	816
	.byte	25
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4326
	.byte	25
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4814
	.byte	25
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4473
	.byte	25
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4642
	.byte	25
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5669
	.byte	25
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	500
	.byte	25
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4040
	.byte	25
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3674
	.byte	25
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2705
	.byte	25
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3009
	.byte	25
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7605
	.byte	25
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7038
	.byte	25
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3625
	.byte	25
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1466
	.byte	25
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2656
	.byte	25
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1690
	.byte	25
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2342
	.byte	25
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1905
	.byte	25
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2122
	.byte	25
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6434
	.byte	25
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6683
	.byte	25
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6942
	.byte	25
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6310
	.byte	25
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5123
	.byte	25
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5629
	.byte	25
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5297
	.byte	25
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5473
	.byte	25
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1347
	.byte	25
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5962
	.byte	25
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4433
	.byte	25
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4949
	.byte	25
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4602
	.byte	25
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4774
	.byte	25
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	776
	.byte	25
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4286
	.byte	25
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4000
	.byte	25
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2969
	.byte	25
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3285
	.byte	16
	.word	7645
	.byte	25
	.byte	'Ifx_P',0,4,139,6,3
	.word	12852
	.byte	17,13,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,25
	.byte	'IfxScu_WDTCON1_IR',0,13,255,10,3
	.word	12872
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_ACCEN0_Bits',0,14,79,3
	.word	12994
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	32,0,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1_Bits',0,14,85,3
	.word	13551
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,14,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,14,94,3
	.word	13628
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,14,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON0_Bits',0,14,111,3
	.word	13764
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,14,114,16,4,11
	.byte	'CANDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	461
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON1_Bits',0,14,126,3
	.word	14044
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,14,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON2_Bits',0,14,135,1,3
	.word	14282
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,14,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON3_Bits',0,14,150,1,3
	.word	14410
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,14,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	461
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	461
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON4_Bits',0,14,165,1,3
	.word	14653
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,14,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CCUCON5_Bits',0,14,174,1,3
	.word	14888
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,14,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6_Bits',0,14,181,1,3
	.word	15016
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,14,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7_Bits',0,14,188,1,3
	.word	15116
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,14,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	461
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	461
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_CHIPID_Bits',0,14,202,1,3
	.word	15216
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,14,205,1,16,4,11
	.byte	'PWD',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	438
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSCON_Bits',0,14,213,1,3
	.word	15424
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,14,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_DTSLIM_Bits',0,14,225,1,3
	.word	15589
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,14,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,14,235,1,3
	.word	15772
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,14,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	461
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	438
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	461
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EICR_Bits',0,14,129,2,3
	.word	15926
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,14,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR_Bits',0,14,143,2,3
	.word	16290
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,14,146,2,16,4,11
	.byte	'POL',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	478
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	461
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_EMSR_Bits',0,14,159,2,3
	.word	16501
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,14,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG_Bits',0,14,167,2,3
	.word	16753
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,14,170,2,16,4,11
	.byte	'ARI',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG_Bits',0,14,175,2,3
	.word	16871
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,14,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR13CON_Bits',0,14,185,2,3
	.word	16982
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,14,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	438
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVR33CON_Bits',0,14,195,2,3
	.word	17145
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,14,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,14,205,2,3
	.word	17308
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,14,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,14,215,2,3
	.word	17466
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,14,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	461
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	461
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	478
	.byte	10,0,2,35,2,0,25
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,14,232,2,3
	.word	17631
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,14,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	461
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	461
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	478
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,14,245,2,3
	.word	17960
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,14,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVROVMON_Bits',0,14,255,2,3
	.word	18181
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,14,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,14,142,3,3
	.word	18344
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,14,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,14,152,3,3
	.word	18616
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,14,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,14,162,3,3
	.word	18769
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,14,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,14,172,3,3
	.word	18925
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,14,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,14,181,3,3
	.word	19087
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,14,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,14,191,3,3
	.word	19230
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,14,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,14,200,3,3
	.word	19395
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,14,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,14,211,3,3
	.word	19540
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,14,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	461
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,14,222,3,3
	.word	19721
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,14,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,14,232,3,3
	.word	19895
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,14,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,14,241,3,3
	.word	20055
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,14,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,14,130,4,3
	.word	20199
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,14,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,14,139,4,3
	.word	20473
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,14,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,14,149,4,3
	.word	20612
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,14,152,4,16,4,11
	.byte	'EN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	461
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	478
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	461
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	461
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	461
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_EXTCON_Bits',0,14,163,4,3
	.word	20775
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,14,166,4,16,4,11
	.byte	'STEP',0,2
	.word	478
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	461
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	478
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_FDR_Bits',0,14,174,4,3
	.word	20993
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,14,177,4,16,4,11
	.byte	'FS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	8,0,2,35,3,0,25
	.byte	'Ifx_SCU_FMR_Bits',0,14,197,4,3
	.word	21156
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,14,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_ID_Bits',0,14,205,4,3
	.word	21492
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,14,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	461
	.byte	2,0,2,35,3,0,25
	.byte	'Ifx_SCU_IGCR_Bits',0,14,232,4,3
	.word	21599
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,14,235,4,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_IN_Bits',0,14,240,4,3
	.word	22051
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,14,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	461
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_IOCR_Bits',0,14,250,4,3
	.word	22150
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,14,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,14,131,5,3
	.word	22300
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,14,134,5,16,4,11
	.byte	'SEED',0,4
	.word	438
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,14,141,5,3
	.word	22449
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,14,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	438
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,14,149,5,3
	.word	22610
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,14,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	478
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_LCLCON_Bits',0,14,158,5,3
	.word	22740
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,14,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST_Bits',0,14,166,5,3
	.word	22872
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,14,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	461
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	478
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_MANID_Bits',0,14,174,5,3
	.word	22987
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,14,177,5,16,4,11
	.byte	'PS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_OMR_Bits',0,14,185,5,3
	.word	23098
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,14,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	461
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	461
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_OSCCON_Bits',0,14,209,5,3
	.word	23256
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,14,212,5,16,4,11
	.byte	'P0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_OUT_Bits',0,14,217,5,3
	.word	23668
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,14,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	478
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	6,0,2,35,3,0,25
	.byte	'Ifx_SCU_OVCCON_Bits',0,14,233,5,3
	.word	23769
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,14,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	438
	.byte	29,0,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,14,242,5,3
	.word	24036
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,14,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC_Bits',0,14,250,5,3
	.word	24172
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,14,253,5,16,4,11
	.byte	'PD0',0,1
	.word	461
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	461
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDR_Bits',0,14,132,6,3
	.word	24283
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,14,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR_Bits',0,14,146,6,3
	.word	24416
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,14,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLCON0_Bits',0,14,166,6,3
	.word	24619
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,14,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON1_Bits',0,14,177,6,3
	.word	24975
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,14,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	478
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLCON2_Bits',0,14,184,6,3
	.word	25153
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,14,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	478
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	461
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	461
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	461
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,14,204,6,3
	.word	25253
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,14,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	461
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	461
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	461
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	9,0,2,35,2,0,25
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,14,215,6,3
	.word	25623
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,14,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	438
	.byte	26,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,14,227,6,3
	.word	25809
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,14,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	438
	.byte	24,0,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,14,241,6,3
	.word	26007
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,14,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	461
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	438
	.byte	21,0,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR_Bits',0,14,251,6,3
	.word	26240
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,14,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	461
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	461
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	461
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	461
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,14,153,7,3
	.word	26392
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,14,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	461
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	461
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	461
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	461
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,14,170,7,3
	.word	26959
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,14,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	461
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	461
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	461
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	461
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	461
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	461
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	461
	.byte	1,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,14,187,7,3
	.word	27253
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,14,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	461
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	461
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	461
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	461
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	461
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	478
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	461
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	461
	.byte	4,0,2,35,3,0,25
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,14,214,7,3
	.word	27531
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,14,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	478
	.byte	14,0,2,35,2,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,14,230,7,3
	.word	28027
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,14,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	478
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	461
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	461
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	461
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON2_Bits',0,14,243,7,3
	.word	28340
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,14,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	461
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	461
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	461
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_RSTCON_Bits',0,14,129,8,3
	.word	28549
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,14,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	461
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	461
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	461
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	461
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	461
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	461
	.byte	3,0,2,35,3,0,25
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,14,155,8,3
	.word	28760
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,14,158,8,16,4,11
	.byte	'HBT',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	438
	.byte	31,0,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON_Bits',0,14,162,8,3
	.word	29192
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,14,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	461
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	461
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	461
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	461
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	461
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	461
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	461
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	461
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	461
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	461
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	461
	.byte	7,0,2,35,3,0,25
	.byte	'Ifx_SCU_STSTAT_Bits',0,14,178,8,3
	.word	29288
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,14,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	438
	.byte	30,0,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,14,186,8,3
	.word	29548
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,14,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	461
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	461
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	438
	.byte	23,0,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON_Bits',0,14,198,8,3
	.word	29673
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,14,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,14,208,8,3
	.word	29870
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,14,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,14,218,8,3
	.word	30023
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,14,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET_Bits',0,14,228,8,3
	.word	30176
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,14,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	438
	.byte	28,0,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,14,238,8,3
	.word	30329
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,14,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	30484
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30484
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30484
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30484
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,14,247,8,3
	.word	30500
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,14,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	461
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,14,134,9,3
	.word	30630
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,14,137,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,14,150,9,3
	.word	30868
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,14,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	30484
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	30484
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	30484
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	30484
	.byte	16,0,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,14,159,9,3
	.word	31091
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,14,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,14,175,9,3
	.word	31217
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,14,178,9,16,4,11
	.byte	'AE',0,1
	.word	461
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	461
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	461
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	461
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	461
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	461
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	461
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	461
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	461
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	461
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	478
	.byte	16,0,2,35,2,0,25
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,14,191,9,3
	.word	31469
	.byte	12,14,199,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12994
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN0',0,14,204,9,3
	.word	31688
	.byte	12,14,207,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13551
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ACCEN1',0,14,212,9,3
	.word	31752
	.byte	12,14,215,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13628
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ARSTDIS',0,14,220,9,3
	.word	31816
	.byte	12,14,223,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13764
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON0',0,14,228,9,3
	.word	31881
	.byte	12,14,231,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14044
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON1',0,14,236,9,3
	.word	31946
	.byte	12,14,239,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14282
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON2',0,14,244,9,3
	.word	32011
	.byte	12,14,247,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14410
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON3',0,14,252,9,3
	.word	32076
	.byte	12,14,255,9,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14653
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON4',0,14,132,10,3
	.word	32141
	.byte	12,14,135,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14888
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON5',0,14,140,10,3
	.word	32206
	.byte	12,14,143,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15016
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON6',0,14,148,10,3
	.word	32271
	.byte	12,14,151,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15116
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CCUCON7',0,14,156,10,3
	.word	32336
	.byte	12,14,159,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15216
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_CHIPID',0,14,164,10,3
	.word	32401
	.byte	12,14,167,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15424
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSCON',0,14,172,10,3
	.word	32465
	.byte	12,14,175,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15589
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSLIM',0,14,180,10,3
	.word	32529
	.byte	12,14,183,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15772
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_DTSSTAT',0,14,188,10,3
	.word	32593
	.byte	12,14,191,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15926
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EICR',0,14,196,10,3
	.word	32658
	.byte	12,14,199,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16290
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EIFR',0,14,204,10,3
	.word	32720
	.byte	12,14,207,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16501
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EMSR',0,14,212,10,3
	.word	32782
	.byte	12,14,215,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16753
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESRCFG',0,14,220,10,3
	.word	32844
	.byte	12,14,223,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16871
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ESROCFG',0,14,228,10,3
	.word	32908
	.byte	12,14,231,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16982
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR13CON',0,14,236,10,3
	.word	32973
	.byte	12,14,239,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17145
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVR33CON',0,14,244,10,3
	.word	33039
	.byte	12,14,247,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17308
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRADCSTAT',0,14,252,10,3
	.word	33105
	.byte	12,14,255,10,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17466
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRDVSTAT',0,14,132,11,3
	.word	33173
	.byte	12,14,135,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17631
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRMONCTRL',0,14,140,11,3
	.word	33240
	.byte	12,14,143,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17960
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROSCCTRL',0,14,148,11,3
	.word	33308
	.byte	12,14,151,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18181
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVROVMON',0,14,156,11,3
	.word	33376
	.byte	12,14,159,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18344
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRRSTCON',0,14,164,11,3
	.word	33442
	.byte	12,14,167,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18616
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,14,172,11,3
	.word	33509
	.byte	12,14,175,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18769
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,14,180,11,3
	.word	33578
	.byte	12,14,183,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18925
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,14,188,11,3
	.word	33647
	.byte	12,14,191,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19087
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,14,196,11,3
	.word	33716
	.byte	12,14,199,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19230
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,14,204,11,3
	.word	33785
	.byte	12,14,207,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19395
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,14,212,11,3
	.word	33854
	.byte	12,14,215,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19540
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL1',0,14,220,11,3
	.word	33923
	.byte	12,14,223,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19721
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL2',0,14,228,11,3
	.word	33991
	.byte	12,14,231,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19895
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL3',0,14,236,11,3
	.word	34059
	.byte	12,14,239,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20055
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSDCTRL4',0,14,244,11,3
	.word	34127
	.byte	12,14,247,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20199
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRSTAT',0,14,252,11,3
	.word	34195
	.byte	12,14,255,11,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20473
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRTRIM',0,14,132,12,3
	.word	34260
	.byte	12,14,135,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20612
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EVRUVMON',0,14,140,12,3
	.word	34325
	.byte	12,14,143,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20775
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_EXTCON',0,14,148,12,3
	.word	34391
	.byte	12,14,151,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20993
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FDR',0,14,156,12,3
	.word	34455
	.byte	12,14,159,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21156
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_FMR',0,14,164,12,3
	.word	34516
	.byte	12,14,167,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21492
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_ID',0,14,172,12,3
	.word	34577
	.byte	12,14,175,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21599
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IGCR',0,14,180,12,3
	.word	34637
	.byte	12,14,183,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22051
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IN',0,14,188,12,3
	.word	34699
	.byte	12,14,191,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22150
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_IOCR',0,14,196,12,3
	.word	34759
	.byte	12,14,199,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22300
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL0',0,14,204,12,3
	.word	34821
	.byte	12,14,207,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22449
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL1',0,14,212,12,3
	.word	34889
	.byte	12,14,215,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22610
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LBISTCTRL2',0,14,220,12,3
	.word	34957
	.byte	12,14,223,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22740
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLCON',0,14,228,12,3
	.word	35025
	.byte	12,14,231,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22872
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_LCLTEST',0,14,236,12,3
	.word	35089
	.byte	12,14,239,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22987
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_MANID',0,14,244,12,3
	.word	35154
	.byte	12,14,247,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23098
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OMR',0,14,252,12,3
	.word	35217
	.byte	12,14,255,12,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23256
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OSCCON',0,14,132,13,3
	.word	35278
	.byte	12,14,135,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23668
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OUT',0,14,140,13,3
	.word	35342
	.byte	12,14,143,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23769
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCCON',0,14,148,13,3
	.word	35403
	.byte	12,14,151,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24036
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_OVCENABLE',0,14,156,13,3
	.word	35467
	.byte	12,14,159,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24172
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDISC',0,14,164,13,3
	.word	35534
	.byte	12,14,167,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24283
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDR',0,14,172,13,3
	.word	35597
	.byte	12,14,175,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24416
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PDRR',0,14,180,13,3
	.word	35658
	.byte	12,14,183,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24619
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON0',0,14,188,13,3
	.word	35720
	.byte	12,14,191,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24975
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON1',0,14,196,13,3
	.word	35785
	.byte	12,14,199,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25153
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLCON2',0,14,204,13,3
	.word	35850
	.byte	12,14,207,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25253
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON0',0,14,212,13,3
	.word	35915
	.byte	12,14,215,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25623
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYCON1',0,14,220,13,3
	.word	35984
	.byte	12,14,223,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25809
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLERAYSTAT',0,14,228,13,3
	.word	36053
	.byte	12,14,231,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26007
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PLLSTAT',0,14,236,13,3
	.word	36122
	.byte	12,14,239,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26240
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMCSR',0,14,244,13,3
	.word	36187
	.byte	12,14,247,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26392
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR0',0,14,252,13,3
	.word	36250
	.byte	12,14,255,13,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26959
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR1',0,14,132,14,3
	.word	36315
	.byte	12,14,135,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27253
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWCR2',0,14,140,14,3
	.word	36380
	.byte	12,14,143,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27531
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTAT',0,14,148,14,3
	.word	36445
	.byte	12,14,151,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28027
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_PMSWSTATCLR',0,14,156,14,3
	.word	36511
	.byte	12,14,159,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28549
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON',0,14,164,14,3
	.word	36580
	.byte	12,14,167,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28340
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTCON2',0,14,172,14,3
	.word	36644
	.byte	12,14,175,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28760
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_RSTSTAT',0,14,180,14,3
	.word	36709
	.byte	12,14,183,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29192
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SAFECON',0,14,188,14,3
	.word	36774
	.byte	12,14,191,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29288
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_STSTAT',0,14,196,14,3
	.word	36839
	.byte	12,14,199,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29548
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SWRSTCON',0,14,204,14,3
	.word	36903
	.byte	12,14,207,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29673
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_SYSCON',0,14,212,14,3
	.word	36969
	.byte	12,14,215,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29870
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPCLR',0,14,220,14,3
	.word	37033
	.byte	12,14,223,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30023
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPDIS',0,14,228,14,3
	.word	37098
	.byte	12,14,231,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30176
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSET',0,14,236,14,3
	.word	37163
	.byte	12,14,239,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30329
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_TRAPSTAT',0,14,244,14,3
	.word	37228
	.byte	12,14,247,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30500
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON0',0,14,252,14,3
	.word	37294
	.byte	12,14,255,14,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30630
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_CON1',0,14,132,15,3
	.word	37363
	.byte	12,14,135,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30868
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTCPU_SR',0,14,140,15,3
	.word	37432
	.byte	12,14,143,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31091
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON0',0,14,148,15,3
	.word	37499
	.byte	12,14,151,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31217
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_CON1',0,14,156,15,3
	.word	37566
	.byte	12,14,159,15,9,4,13
	.byte	'U',0
	.word	438
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	454
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31469
	.byte	4,2,35,0,0,25
	.byte	'Ifx_SCU_WDTS_SR',0,14,164,15,3
	.word	37633
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,14,175,15,25,12,13
	.byte	'CON0',0
	.word	37294
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37363
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37432
	.byte	4,2,35,8,0,16
	.word	37698
	.byte	25
	.byte	'Ifx_SCU_WDTCPU',0,14,180,15,3
	.word	37761
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,14,183,15,25,12,13
	.byte	'CON0',0
	.word	37499
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	37566
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	37633
	.byte	4,2,35,8,0,16
	.word	37790
	.byte	25
	.byte	'Ifx_SCU_WDTS',0,14,188,15,3
	.word	37851
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,25
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	37878
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,25
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	38029
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,25
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	38273
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,25
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	38371
	.byte	25
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8258
	.byte	25
	.byte	'gpio_pin_enum',0,6,89,2
	.word	8596
	.byte	25
	.byte	'gpio_dir_enum',0,6,95,2
	.word	10653
	.byte	25
	.byte	'gpio_mode_enum',0,6,111,2
	.word	10671
	.byte	25
	.byte	'spi_config_info_struct',0,7,50,2
	.word	10834
	.byte	25
	.byte	'soft_spi_info_struct',0,7,60,2
	.word	10915
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,20
	.byte	46,1,49,19,0,0,21,5,0,49,19,0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,23,19
	.byte	1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,25,22,0,3,8,58,15,59,15,57,15,73,19,0,0,26,21,0,54,15
	.byte	0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L132:
	.word	.L518-.L517
.L517:
	.half	3
	.word	.L520-.L519
.L519:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_gpio.h',0,0,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_soft_spi.h',0,0,0,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'Platform_Types.h',0,5,0,0
	.byte	'ifx_types.h',0,5,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0,0
.L520:
.L518:
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_8bit')
	.sect	'.debug_info'
.L133:
	.word	306
	.half	3
	.word	.L134
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L136,.L135
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_8bit',0,1,224,1,6,1,1,1
	.word	.L92,.L238,.L91
	.byte	4
	.byte	'soft_spi_obj',0,1,224,1,49
	.word	.L239,.L240
	.byte	4
	.byte	'data',0,1,224,1,75
	.word	.L241,.L242
	.byte	5
	.word	.L92,.L238
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_8bit')
	.sect	'.debug_abbrev'
.L134:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_8bit')
	.sect	'.debug_line'
.L135:
	.word	.L522-.L521
.L521:
	.half	3
	.word	.L524-.L523
.L523:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L524:
	.byte	5,46,7,0,5,2
	.word	.L92
	.byte	3,225,1,1,5,1,9
	.half	.L411-.L92
	.byte	3,1,1,7,9
	.half	.L137-.L411
	.byte	0,1,1
.L522:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_8bit')
	.sect	'.debug_ranges'
.L136:
	.word	-1,.L92,0,.L137-.L92,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_8bit_array')
	.sect	'.debug_info'
.L138:
	.word	329
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L141,.L140
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_8bit_array',0,1,238,1,6,1,1,1
	.word	.L94,.L243,.L93
	.byte	4
	.byte	'soft_spi_obj',0,1,238,1,55
	.word	.L239,.L244
	.byte	4
	.byte	'data',0,1,238,1,82
	.word	.L245,.L246
	.byte	4
	.byte	'len',0,1,238,1,95
	.word	.L247,.L248
	.byte	5
	.word	.L94,.L243
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_8bit_array')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_8bit_array')
	.sect	'.debug_line'
.L140:
	.word	.L526-.L525
.L525:
	.half	3
	.word	.L528-.L527
.L527:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L528:
	.byte	5,6,7,0,5,2
	.word	.L94
	.byte	3,237,1,1,5,17,9
	.half	.L414-.L94
	.byte	3,2,1,5,50,9
	.half	.L55-.L414
	.byte	3,2,1,5,56,9
	.half	.L529-.L55
	.byte	1,5,15,9
	.half	.L54-.L529
	.byte	3,126,1,5,17,9
	.half	.L530-.L54
	.byte	1,5,1,7,9
	.half	.L531-.L530
	.byte	3,4,1,7,9
	.half	.L142-.L531
	.byte	0,1,1
.L526:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_8bit_array')
	.sect	'.debug_ranges'
.L141:
	.word	-1,.L94,0,.L142-.L94,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_16bit')
	.sect	'.debug_info'
.L143:
	.word	307
	.half	3
	.word	.L144
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L146,.L145
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_16bit',0,1,254,1,6,1,1,1
	.word	.L96,.L249,.L95
	.byte	4
	.byte	'soft_spi_obj',0,1,254,1,50
	.word	.L239,.L250
	.byte	4
	.byte	'data',0,1,254,1,71
	.word	.L251,.L252
	.byte	5
	.word	.L96,.L249
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_16bit')
	.sect	'.debug_abbrev'
.L144:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_16bit')
	.sect	'.debug_line'
.L145:
	.word	.L533-.L532
.L532:
	.half	3
	.word	.L535-.L534
.L534:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L535:
	.byte	5,47,7,0,5,2
	.word	.L96
	.byte	3,255,1,1,5,1,9
	.half	.L416-.L96
	.byte	3,1,1,7,9
	.half	.L147-.L416
	.byte	0,1,1
.L533:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_16bit')
	.sect	'.debug_ranges'
.L146:
	.word	-1,.L96,0,.L147-.L96,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_16bit_array')
	.sect	'.debug_info'
.L148:
	.word	330
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L151,.L150
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_16bit_array',0,1,140,2,6,1,1,1
	.word	.L98,.L253,.L97
	.byte	4
	.byte	'soft_spi_obj',0,1,140,2,56
	.word	.L239,.L254
	.byte	4
	.byte	'data',0,1,140,2,84
	.word	.L255,.L256
	.byte	4
	.byte	'len',0,1,140,2,97
	.word	.L247,.L257
	.byte	5
	.word	.L98,.L253
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_16bit_array')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_16bit_array')
	.sect	'.debug_line'
.L150:
	.word	.L537-.L536
.L536:
	.half	3
	.word	.L539-.L538
.L538:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L539:
	.byte	5,6,7,0,5,2
	.word	.L98
	.byte	3,139,2,1,5,17,9
	.half	.L419-.L98
	.byte	3,2,1,5,51,9
	.half	.L57-.L419
	.byte	3,2,1,5,57,9
	.half	.L540-.L57
	.byte	1,5,15,9
	.half	.L56-.L540
	.byte	3,126,1,5,17,9
	.half	.L541-.L56
	.byte	1,5,1,7,9
	.half	.L542-.L541
	.byte	3,4,1,7,9
	.half	.L152-.L542
	.byte	0,1,1
.L537:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_16bit_array')
	.sect	'.debug_ranges'
.L151:
	.word	-1,.L98,0,.L152-.L98,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_8bit_register')
	.sect	'.debug_info'
.L153:
	.word	342
	.half	3
	.word	.L154
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L156,.L155
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_8bit_register',0,1,157,2,6,1,1,1
	.word	.L100,.L258,.L99
	.byte	4
	.byte	'soft_spi_obj',0,1,157,2,58
	.word	.L239,.L259
	.byte	4
	.byte	'register_name',0,1,157,2,84
	.word	.L260,.L261
	.byte	4
	.byte	'data',0,1,157,2,111
	.word	.L262,.L263
	.byte	5
	.word	.L100,.L258
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_8bit_register')
	.sect	'.debug_abbrev'
.L154:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_8bit_register')
	.sect	'.debug_line'
.L155:
	.word	.L544-.L543
.L543:
	.half	3
	.word	.L546-.L545
.L545:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L546:
	.byte	5,6,7,0,5,2
	.word	.L100
	.byte	3,156,2,1,5,46,9
	.half	.L423-.L100
	.byte	3,2,1,9
	.half	.L421-.L423
	.byte	3,1,1,5,1,9
	.half	.L425-.L421
	.byte	3,1,1,7,9
	.half	.L157-.L425
	.byte	0,1,1
.L544:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_8bit_register')
	.sect	'.debug_ranges'
.L156:
	.word	-1,.L100,0,.L157-.L100,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_8bit_registers')
	.sect	'.debug_info'
.L158:
	.word	360
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L161,.L160
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_8bit_registers',0,1,172,2,6,1,1,1
	.word	.L102,.L264,.L101
	.byte	4
	.byte	'soft_spi_obj',0,1,172,2,59
	.word	.L239,.L265
	.byte	4
	.byte	'register_name',0,1,172,2,85
	.word	.L266,.L267
	.byte	4
	.byte	'data',0,1,172,2,113
	.word	.L245,.L268
	.byte	4
	.byte	'len',0,1,172,2,126
	.word	.L247,.L269
	.byte	5
	.word	.L102,.L264
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_8bit_registers')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_8bit_registers')
	.sect	'.debug_line'
.L160:
	.word	.L548-.L547
.L547:
	.half	3
	.word	.L550-.L549
.L549:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L550:
	.byte	5,6,7,0,5,2
	.word	.L102
	.byte	3,171,2,1,5,46,9
	.half	.L430-.L102
	.byte	3,2,1,5,17,9
	.half	.L427-.L430
	.byte	3,1,1,5,50,9
	.half	.L59-.L427
	.byte	3,2,1,5,56,9
	.half	.L551-.L59
	.byte	1,5,15,9
	.half	.L58-.L551
	.byte	3,126,1,5,17,9
	.half	.L552-.L58
	.byte	1,5,1,7,9
	.half	.L553-.L552
	.byte	3,4,1,7,9
	.half	.L162-.L553
	.byte	0,1,1
.L548:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_8bit_registers')
	.sect	'.debug_ranges'
.L161:
	.word	-1,.L102,0,.L162-.L102,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_16bit_register')
	.sect	'.debug_info'
.L163:
	.word	343
	.half	3
	.word	.L164
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L166,.L165
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_16bit_register',0,1,190,2,6,1,1,1
	.word	.L104,.L270,.L103
	.byte	4
	.byte	'soft_spi_obj',0,1,190,2,59
	.word	.L239,.L271
	.byte	4
	.byte	'register_name',0,1,190,2,86
	.word	.L272,.L273
	.byte	4
	.byte	'data',0,1,190,2,108
	.word	.L251,.L274
	.byte	5
	.word	.L104,.L270
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_16bit_register')
	.sect	'.debug_abbrev'
.L164:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_16bit_register')
	.sect	'.debug_line'
.L165:
	.word	.L555-.L554
.L554:
	.half	3
	.word	.L557-.L556
.L556:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L557:
	.byte	5,6,7,0,5,2
	.word	.L104
	.byte	3,189,2,1,5,47,9
	.half	.L434-.L104
	.byte	3,2,1,9
	.half	.L432-.L434
	.byte	3,1,1,5,1,9
	.half	.L436-.L432
	.byte	3,1,1,7,9
	.half	.L167-.L436
	.byte	0,1,1
.L555:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_16bit_register')
	.sect	'.debug_ranges'
.L166:
	.word	-1,.L104,0,.L167-.L104,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_write_16bit_registers')
	.sect	'.debug_info'
.L168:
	.word	362
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L171,.L170
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_write_16bit_registers',0,1,206,2,6,1,1,1
	.word	.L106,.L275,.L105
	.byte	4
	.byte	'soft_spi_obj',0,1,206,2,60
	.word	.L239,.L276
	.byte	4
	.byte	'register_name',0,1,206,2,87
	.word	.L277,.L278
	.byte	4
	.byte	'data',0,1,206,2,116
	.word	.L255,.L279
	.byte	4
	.byte	'len',0,1,206,2,129,1
	.word	.L247,.L280
	.byte	5
	.word	.L106,.L275
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_write_16bit_registers')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_write_16bit_registers')
	.sect	'.debug_line'
.L170:
	.word	.L559-.L558
.L558:
	.half	3
	.word	.L561-.L560
.L560:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L561:
	.byte	5,6,7,0,5,2
	.word	.L106
	.byte	3,205,2,1,5,47,9
	.half	.L441-.L106
	.byte	3,2,1,5,17,9
	.half	.L438-.L441
	.byte	3,1,1,5,51,9
	.half	.L61-.L438
	.byte	3,2,1,5,57,9
	.half	.L562-.L61
	.byte	1,5,15,9
	.half	.L60-.L562
	.byte	3,126,1,5,17,9
	.half	.L563-.L60
	.byte	1,5,1,7,9
	.half	.L564-.L563
	.byte	3,4,1,7,9
	.half	.L172-.L564
	.byte	0,1,1
.L559:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_write_16bit_registers')
	.sect	'.debug_ranges'
.L171:
	.word	-1,.L106,0,.L172-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_8bit')
	.sect	'.debug_info'
.L173:
	.word	291
	.half	3
	.word	.L174
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L176,.L175
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_8bit',0,1,223,2,7
	.word	.L281
	.byte	1,1,1
	.word	.L108,.L282,.L107
	.byte	4
	.byte	'soft_spi_obj',0,1,223,2,49
	.word	.L239,.L283
	.byte	5
	.word	.L108,.L282
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_8bit')
	.sect	'.debug_abbrev'
.L174:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_8bit')
	.sect	'.debug_line'
.L175:
	.word	.L566-.L565
.L565:
	.half	3
	.word	.L568-.L567
.L567:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L568:
	.byte	5,53,7,0,5,2
	.word	.L108
	.byte	3,224,2,1,5,5,9
	.half	.L443-.L108
	.byte	1,5,1,9
	.half	.L62-.L443
	.byte	3,1,1,7,9
	.half	.L177-.L62
	.byte	0,1,1
.L566:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_8bit')
	.sect	'.debug_ranges'
.L176:
	.word	-1,.L108,0,.L177-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_8bit_array')
	.sect	'.debug_info'
.L178:
	.word	328
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L181,.L180
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_8bit_array',0,1,237,2,6,1,1,1
	.word	.L110,.L284,.L109
	.byte	4
	.byte	'soft_spi_obj',0,1,237,2,54
	.word	.L239,.L285
	.byte	4
	.byte	'data',0,1,237,2,75
	.word	.L286,.L287
	.byte	4
	.byte	'len',0,1,237,2,88
	.word	.L247,.L288
	.byte	5
	.word	.L110,.L284
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_8bit_array')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_8bit_array')
	.sect	'.debug_line'
.L180:
	.word	.L570-.L569
.L569:
	.half	3
	.word	.L572-.L571
.L571:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L572:
	.byte	5,6,7,0,5,2
	.word	.L110
	.byte	3,236,2,1,5,17,9
	.half	.L446-.L110
	.byte	3,2,1,5,61,9
	.half	.L64-.L446
	.byte	3,2,1,5,18,9
	.half	.L448-.L64
	.byte	1,5,15,9
	.half	.L573-.L448
	.byte	1,9
	.half	.L63-.L573
	.byte	3,126,1,5,17,9
	.half	.L574-.L63
	.byte	1,5,1,7,9
	.half	.L575-.L574
	.byte	3,4,1,7,9
	.half	.L182-.L575
	.byte	0,1,1
.L570:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_8bit_array')
	.sect	'.debug_ranges'
.L181:
	.word	-1,.L110,0,.L182-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_16bit')
	.sect	'.debug_info'
.L183:
	.word	292
	.half	3
	.word	.L184
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L186,.L185
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_16bit',0,1,253,2,8
	.word	.L251
	.byte	1,1,1
	.word	.L112,.L289,.L111
	.byte	4
	.byte	'soft_spi_obj',0,1,253,2,51
	.word	.L239,.L290
	.byte	5
	.word	.L112,.L289
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_16bit')
	.sect	'.debug_abbrev'
.L184:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_16bit')
	.sect	'.debug_line'
.L185:
	.word	.L577-.L576
.L576:
	.half	3
	.word	.L579-.L578
.L578:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L579:
	.byte	5,54,7,0,5,2
	.word	.L112
	.byte	3,254,2,1,5,5,9
	.half	.L449-.L112
	.byte	1,5,1,9
	.half	.L65-.L449
	.byte	3,1,1,7,9
	.half	.L187-.L65
	.byte	0,1,1
.L577:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_16bit')
	.sect	'.debug_ranges'
.L186:
	.word	-1,.L112,0,.L187-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_16bit_array')
	.sect	'.debug_info'
.L188:
	.word	329
	.half	3
	.word	.L189
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L191,.L190
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_16bit_array',0,1,139,3,6,1,1,1
	.word	.L114,.L291,.L113
	.byte	4
	.byte	'soft_spi_obj',0,1,139,3,55
	.word	.L239,.L292
	.byte	4
	.byte	'data',0,1,139,3,77
	.word	.L293,.L294
	.byte	4
	.byte	'len',0,1,139,3,90
	.word	.L247,.L295
	.byte	5
	.word	.L114,.L291
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_16bit_array')
	.sect	'.debug_abbrev'
.L189:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_16bit_array')
	.sect	'.debug_line'
.L190:
	.word	.L581-.L580
.L580:
	.half	3
	.word	.L583-.L582
.L582:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L583:
	.byte	5,6,7,0,5,2
	.word	.L114
	.byte	3,138,3,1,5,17,9
	.half	.L452-.L114
	.byte	3,2,1,5,62,9
	.half	.L67-.L452
	.byte	3,2,1,5,18,9
	.half	.L454-.L67
	.byte	1,5,15,9
	.half	.L584-.L454
	.byte	1,9
	.half	.L66-.L584
	.byte	3,126,1,5,17,9
	.half	.L585-.L66
	.byte	1,5,1,7,9
	.half	.L586-.L585
	.byte	3,4,1,7,9
	.half	.L192-.L586
	.byte	0,1,1
.L581:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_16bit_array')
	.sect	'.debug_ranges'
.L191:
	.word	-1,.L114,0,.L192-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_8bit_register')
	.sect	'.debug_info'
.L193:
	.word	327
	.half	3
	.word	.L194
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L196,.L195
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_8bit_register',0,1,156,3,7
	.word	.L281
	.byte	1,1,1
	.word	.L116,.L296,.L115
	.byte	4
	.byte	'soft_spi_obj',0,1,156,3,58
	.word	.L239,.L297
	.byte	4
	.byte	'register_name',0,1,156,3,84
	.word	.L298,.L299
	.byte	5
	.word	.L116,.L296
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_8bit_register')
	.sect	'.debug_abbrev'
.L194:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_8bit_register')
	.sect	'.debug_line'
.L195:
	.word	.L588-.L587
.L587:
	.half	3
	.word	.L590-.L589
.L589:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L590:
	.byte	5,7,7,0,5,2
	.word	.L116
	.byte	3,155,3,1,5,46,9
	.half	.L456-.L116
	.byte	3,2,1,5,53,9
	.half	.L455-.L456
	.byte	3,1,1,5,5,9
	.half	.L458-.L455
	.byte	1,5,1,9
	.half	.L68-.L458
	.byte	3,1,1,7,9
	.half	.L197-.L68
	.byte	0,1,1
.L588:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_8bit_register')
	.sect	'.debug_ranges'
.L196:
	.word	-1,.L116,0,.L197-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_8bit_registers')
	.sect	'.debug_info'
.L198:
	.word	359
	.half	3
	.word	.L199
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L201,.L200
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_8bit_registers',0,1,172,3,6,1,1,1
	.word	.L118,.L300,.L117
	.byte	4
	.byte	'soft_spi_obj',0,1,172,3,58
	.word	.L239,.L301
	.byte	4
	.byte	'register_name',0,1,172,3,84
	.word	.L302,.L303
	.byte	4
	.byte	'data',0,1,172,3,106
	.word	.L286,.L304
	.byte	4
	.byte	'len',0,1,172,3,119
	.word	.L247,.L305
	.byte	5
	.word	.L118,.L300
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_8bit_registers')
	.sect	'.debug_abbrev'
.L199:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_8bit_registers')
	.sect	'.debug_line'
.L200:
	.word	.L592-.L591
.L591:
	.half	3
	.word	.L594-.L593
.L593:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L594:
	.byte	5,6,7,0,5,2
	.word	.L118
	.byte	3,171,3,1,5,46,9
	.half	.L462-.L118
	.byte	3,2,1,5,17,9
	.half	.L459-.L462
	.byte	3,1,1,5,61,9
	.half	.L70-.L459
	.byte	3,2,1,5,18,9
	.half	.L464-.L70
	.byte	1,5,15,9
	.half	.L595-.L464
	.byte	1,9
	.half	.L69-.L595
	.byte	3,126,1,5,17,9
	.half	.L596-.L69
	.byte	1,5,1,7,9
	.half	.L597-.L596
	.byte	3,4,1,7,9
	.half	.L202-.L597
	.byte	0,1,1
.L592:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_8bit_registers')
	.sect	'.debug_ranges'
.L201:
	.word	-1,.L118,0,.L202-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_16bit_register')
	.sect	'.debug_info'
.L203:
	.word	328
	.half	3
	.word	.L204
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L206,.L205
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_16bit_register',0,1,190,3,8
	.word	.L251
	.byte	1,1,1
	.word	.L120,.L306,.L119
	.byte	4
	.byte	'soft_spi_obj',0,1,190,3,60
	.word	.L239,.L307
	.byte	4
	.byte	'register_name',0,1,190,3,87
	.word	.L308,.L309
	.byte	5
	.word	.L120,.L306
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_16bit_register')
	.sect	'.debug_abbrev'
.L204:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_16bit_register')
	.sect	'.debug_line'
.L205:
	.word	.L599-.L598
.L598:
	.half	3
	.word	.L601-.L600
.L600:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L601:
	.byte	5,8,7,0,5,2
	.word	.L120
	.byte	3,189,3,1,5,47,9
	.half	.L466-.L120
	.byte	3,2,1,5,54,9
	.half	.L465-.L466
	.byte	3,1,1,5,5,9
	.half	.L468-.L465
	.byte	1,5,1,9
	.half	.L71-.L468
	.byte	3,1,1,7,9
	.half	.L207-.L71
	.byte	0,1,1
.L599:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_16bit_register')
	.sect	'.debug_ranges'
.L206:
	.word	-1,.L120,0,.L207-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_read_16bit_registers')
	.sect	'.debug_info'
.L208:
	.word	360
	.half	3
	.word	.L209
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L211,.L210
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_read_16bit_registers',0,1,206,3,6,1,1,1
	.word	.L122,.L310,.L121
	.byte	4
	.byte	'soft_spi_obj',0,1,206,3,59
	.word	.L239,.L311
	.byte	4
	.byte	'register_name',0,1,206,3,86
	.word	.L312,.L313
	.byte	4
	.byte	'data',0,1,206,3,109
	.word	.L293,.L314
	.byte	4
	.byte	'len',0,1,206,3,122
	.word	.L247,.L315
	.byte	5
	.word	.L122,.L310
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_read_16bit_registers')
	.sect	'.debug_abbrev'
.L209:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_read_16bit_registers')
	.sect	'.debug_line'
.L210:
	.word	.L603-.L602
.L602:
	.half	3
	.word	.L605-.L604
.L604:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L605:
	.byte	5,6,7,0,5,2
	.word	.L122
	.byte	3,205,3,1,5,47,9
	.half	.L472-.L122
	.byte	3,2,1,5,17,9
	.half	.L469-.L472
	.byte	3,1,1,5,62,9
	.half	.L73-.L469
	.byte	3,2,1,5,18,9
	.half	.L474-.L73
	.byte	1,5,15,9
	.half	.L606-.L474
	.byte	1,9
	.half	.L72-.L606
	.byte	3,126,1,5,17,9
	.half	.L607-.L72
	.byte	1,5,1,7,9
	.half	.L608-.L607
	.byte	3,4,1,7,9
	.half	.L212-.L608
	.byte	0,1,1
.L603:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_read_16bit_registers')
	.sect	'.debug_ranges'
.L211:
	.word	-1,.L122,0,.L212-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_init')
	.sect	'.debug_info'
.L213:
	.word	406
	.half	3
	.word	.L214
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L216,.L215
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_init',0,1,140,4,6,1,1,1
	.word	.L128,.L316,.L127
	.byte	4
	.byte	'soft_spi_obj',0,1,140,4,43
	.word	.L239,.L317
	.byte	4
	.byte	'mode',0,1,140,4,63
	.word	.L281,.L318
	.byte	4
	.byte	'delay',0,1,140,4,76
	.word	.L247,.L319
	.byte	4
	.byte	'sck_pin',0,1,140,4,97
	.word	.L320,.L321
	.byte	4
	.byte	'mosi_pin',0,1,140,4,120
	.word	.L320,.L322
	.byte	4
	.byte	'miso_pin',0,1,140,4,137,1
	.word	.L247,.L323
	.byte	4
	.byte	'cs_pin',0,1,140,4,154,1
	.word	.L247,.L324
	.byte	5
	.word	.L128,.L316
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_init')
	.sect	'.debug_abbrev'
.L214:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_init')
	.sect	'.debug_line'
.L215:
	.word	.L610-.L609
.L609:
	.half	3
	.word	.L612-.L611
.L611:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L612:
	.byte	5,6,7,0,5,2
	.word	.L128
	.byte	3,139,4,1,5,5,9
	.half	.L493-.L128
	.byte	3,2,1,9
	.half	.L489-.L493
	.byte	3,1,1,9
	.half	.L495-.L489
	.byte	3,1,1,9
	.half	.L497-.L495
	.byte	3,1,1,9
	.half	.L499-.L497
	.byte	3,1,1,9
	.half	.L613-.L499
	.byte	3,1,1,9
	.half	.L501-.L613
	.byte	3,2,1,5,25,9
	.half	.L614-.L501
	.byte	3,2,1,5,31,9
	.half	.L503-.L614
	.byte	1,5,25,9
	.half	.L505-.L503
	.byte	3,1,1,5,27,9
	.half	.L506-.L505
	.byte	3,2,1,5,28,9
	.half	.L507-.L506
	.byte	3,1,1,5,8,9
	.half	.L508-.L507
	.byte	3,1,1,5,23,7,9
	.half	.L615-.L508
	.byte	1,5,28,7,9
	.half	.L80-.L615
	.byte	3,2,1,5,33,9
	.half	.L616-.L80
	.byte	1,5,43,9
	.half	.L617-.L616
	.byte	1,5,57,9
	.half	.L511-.L617
	.byte	1,5,28,9
	.half	.L81-.L511
	.byte	3,4,1,5,33,9
	.half	.L618-.L81
	.byte	1,5,44,9
	.half	.L619-.L618
	.byte	1,5,25,9
	.half	.L82-.L619
	.byte	3,2,1,5,30,9
	.half	.L620-.L82
	.byte	1,5,41,9
	.half	.L621-.L620
	.byte	1,5,8,9
	.half	.L516-.L621
	.byte	3,2,1,5,5,9
	.half	.L622-.L516
	.byte	1,5,29,7,9
	.half	.L623-.L622
	.byte	3,2,1,5,39,9
	.half	.L624-.L623
	.byte	1,5,42,9
	.half	.L625-.L624
	.byte	1,5,29,9
	.half	.L83-.L625
	.byte	3,4,1,5,39,9
	.half	.L626-.L83
	.byte	1,5,34,9
	.half	.L627-.L626
	.byte	3,1,1,5,32,9
	.half	.L628-.L627
	.byte	1,5,31,9
	.half	.L629-.L628
	.byte	3,1,1,5,43,9
	.half	.L630-.L629
	.byte	1,5,48,9
	.half	.L631-.L630
	.byte	1,5,59,9
	.half	.L632-.L631
	.byte	1,5,8,9
	.half	.L84-.L632
	.byte	3,2,1,5,5,9
	.half	.L633-.L84
	.byte	1,5,29,7,9
	.half	.L634-.L633
	.byte	3,2,1,5,37,9
	.half	.L635-.L634
	.byte	1,5,40,9
	.half	.L636-.L635
	.byte	1,5,29,9
	.half	.L85-.L636
	.byte	3,4,1,5,37,9
	.half	.L637-.L85
	.byte	1,5,32,9
	.half	.L638-.L637
	.byte	3,1,1,5,30,9
	.half	.L639-.L638
	.byte	1,5,31,9
	.half	.L640-.L639
	.byte	3,1,1,5,41,9
	.half	.L641-.L640
	.byte	1,5,46,9
	.half	.L642-.L641
	.byte	1,5,57,9
	.half	.L643-.L642
	.byte	1,5,1,9
	.half	.L86-.L643
	.byte	3,2,1,7,9
	.half	.L217-.L86
	.byte	0,1,1
.L610:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_init')
	.sect	'.debug_ranges'
.L216:
	.word	-1,.L128,0,.L217-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_8bit_data_handler')
	.sect	'.debug_info'
.L218:
	.word	472
	.half	3
	.word	.L219
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L221,.L220
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_8bit_data_handler',0,1,56,14
	.word	.L281
	.byte	1,1
	.word	.L88,.L325,.L87
	.byte	4
	.byte	'soft_spi_obj',0,1,56,64
	.word	.L239,.L326
	.byte	4
	.byte	'data',0,1,56,90
	.word	.L327,.L328
	.byte	5
	.word	.L88,.L325
	.byte	6
	.byte	'temp',0,1,58,11
	.word	.L281,.L329
	.byte	6
	.byte	'write_data',0,1,59,11
	.word	.L281,.L330
	.byte	6
	.byte	'read_data',0,1,60,11
	.word	.L281,.L331
	.byte	5
	.word	.L10,.L332
	.byte	6
	.byte	'i',0,1,88,13
	.word	.L247,.L333
	.byte	0,5
	.word	.L13,.L334
	.byte	6
	.byte	'i',0,1,96,13
	.word	.L247,.L335
	.byte	0,5
	.word	.L20,.L336
	.byte	6
	.byte	'i',0,1,113,13
	.word	.L247,.L337
	.byte	0,5
	.word	.L23,.L338
	.byte	6
	.byte	'i',0,1,121,13
	.word	.L247,.L339
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_8bit_data_handler')
	.sect	'.debug_abbrev'
.L219:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_8bit_data_handler')
	.sect	'.debug_line'
.L220:
	.word	.L645-.L644
.L644:
	.half	3
	.word	.L647-.L646
.L646:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L647:
	.byte	5,14,7,0,5,2
	.word	.L88
	.byte	3,55,1,5,22,9
	.half	.L367-.L88
	.byte	3,3,1,5,21,9
	.half	.L368-.L367
	.byte	3,1,1,5,28,9
	.half	.L370-.L368
	.byte	3,2,1,5,5,9
	.half	.L648-.L370
	.byte	1,5,9,7,9
	.half	.L649-.L648
	.byte	3,2,1,5,33,9
	.half	.L2-.L649
	.byte	3,3,1,5,8,9
	.half	.L650-.L2
	.byte	1,5,67,7,9
	.half	.L651-.L650
	.byte	1,5,44,9
	.half	.L652-.L651
	.byte	1,5,9,7,9
	.half	.L3-.L652
	.byte	3,2,1,9
	.half	.L4-.L3
	.byte	3,4,1,5,33,9
	.half	.L5-.L4
	.byte	3,3,1,5,41,9
	.half	.L653-.L5
	.byte	1,5,39,9
	.half	.L654-.L653
	.byte	1,5,5,9
	.half	.L655-.L654
	.byte	1,5,18,7,9
	.half	.L656-.L655
	.byte	3,2,1,5,31,9
	.half	.L372-.L656
	.byte	1,5,13,9
	.half	.L8-.L372
	.byte	3,2,1,5,17,7,9
	.half	.L657-.L8
	.byte	3,2,1,9
	.half	.L9-.L657
	.byte	3,4,1,5,13,9
	.half	.L10-.L9
	.byte	3,2,1,5,43,9
	.half	.L332-.L10
	.byte	3,1,1,5,37,9
	.half	.L374-.L332
	.byte	3,1,1,5,35,9
	.half	.L375-.L374
	.byte	3,1,1,5,36,9
	.half	.L377-.L375
	.byte	3,1,1,5,13,9
	.half	.L658-.L377
	.byte	1,5,57,7,9
	.half	.L659-.L658
	.byte	3,2,1,5,27,9
	.half	.L660-.L659
	.byte	1,5,13,9
	.half	.L13-.L660
	.byte	3,2,1,5,43,9
	.half	.L334-.L13
	.byte	3,1,1,5,38,9
	.half	.L380-.L334
	.byte	3,109,1,5,31,9
	.half	.L7-.L380
	.byte	1,7,9
	.half	.L661-.L7
	.byte	1,5,18,9
	.half	.L6-.L661
	.byte	3,24,1,5,31,9
	.half	.L381-.L6
	.byte	1,5,43,9
	.half	.L18-.L381
	.byte	3,2,1,5,13,9
	.half	.L662-.L18
	.byte	3,1,1,5,17,7,9
	.half	.L663-.L662
	.byte	3,2,1,9
	.half	.L19-.L663
	.byte	3,4,1,5,13,9
	.half	.L20-.L19
	.byte	3,2,1,5,43,9
	.half	.L336-.L20
	.byte	3,1,1,5,37,9
	.half	.L383-.L336
	.byte	3,1,1,5,35,9
	.half	.L384-.L383
	.byte	3,1,1,5,36,9
	.half	.L385-.L384
	.byte	3,1,1,5,13,9
	.half	.L664-.L385
	.byte	1,5,57,7,9
	.half	.L665-.L664
	.byte	3,2,1,5,27,9
	.half	.L666-.L665
	.byte	1,5,13,9
	.half	.L23-.L666
	.byte	3,2,1,5,38,9
	.half	.L338-.L23
	.byte	3,109,1,5,31,9
	.half	.L17-.L338
	.byte	1,5,28,7,9
	.half	.L16-.L17
	.byte	3,23,1,5,5,9
	.half	.L667-.L16
	.byte	1,5,9,7,9
	.half	.L668-.L667
	.byte	3,2,1,5,5,9
	.half	.L26-.L668
	.byte	3,2,1,5,1,9
	.half	.L27-.L26
	.byte	3,1,1,7,9
	.half	.L222-.L27
	.byte	0,1,1
.L645:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_8bit_data_handler')
	.sect	'.debug_ranges'
.L221:
	.word	-1,.L88,0,.L222-.L88,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_16bit_data_handler')
	.sect	'.debug_info'
.L223:
	.word	483
	.half	3
	.word	.L224
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L226,.L225
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_16bit_data_handler',0,1,140,1,15
	.word	.L251
	.byte	1,1
	.word	.L90,.L340,.L89
	.byte	4
	.byte	'soft_spi_obj',0,1,140,1,66
	.word	.L239,.L341
	.byte	4
	.byte	'data',0,1,140,1,93
	.word	.L342,.L343
	.byte	5
	.word	.L90,.L340
	.byte	6
	.byte	'temp',0,1,142,1,11
	.word	.L281,.L344
	.byte	6
	.byte	'write_data',0,1,143,1,12
	.word	.L251,.L345
	.byte	6
	.byte	'read_data',0,1,144,1,12
	.word	.L251,.L346
	.byte	5
	.word	.L36,.L347
	.byte	6
	.byte	'i',0,1,172,1,13
	.word	.L247,.L348
	.byte	0,5
	.word	.L39,.L349
	.byte	6
	.byte	'i',0,1,180,1,13
	.word	.L247,.L350
	.byte	0,5
	.word	.L46,.L351
	.byte	6
	.byte	'i',0,1,197,1,13
	.word	.L247,.L352
	.byte	0,5
	.word	.L49,.L353
	.byte	6
	.byte	'i',0,1,205,1,13
	.word	.L247,.L354
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_16bit_data_handler')
	.sect	'.debug_abbrev'
.L224:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_16bit_data_handler')
	.sect	'.debug_line'
.L225:
	.word	.L670-.L669
.L669:
	.half	3
	.word	.L672-.L671
.L671:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L672:
	.byte	5,15,7,0,5,2
	.word	.L90
	.byte	3,139,1,1,5,23,9
	.half	.L390-.L90
	.byte	3,3,1,5,22,9
	.half	.L391-.L390
	.byte	3,1,1,5,28,9
	.half	.L393-.L391
	.byte	3,2,1,5,5,9
	.half	.L673-.L393
	.byte	1,5,9,7,9
	.half	.L674-.L673
	.byte	3,2,1,5,33,9
	.half	.L28-.L674
	.byte	3,3,1,5,8,9
	.half	.L675-.L28
	.byte	1,5,67,7,9
	.half	.L676-.L675
	.byte	1,5,44,9
	.half	.L677-.L676
	.byte	1,5,9,7,9
	.half	.L29-.L677
	.byte	3,2,1,9
	.half	.L30-.L29
	.byte	3,4,1,5,33,9
	.half	.L31-.L30
	.byte	3,3,1,5,41,9
	.half	.L678-.L31
	.byte	1,5,39,9
	.half	.L679-.L678
	.byte	1,5,5,9
	.half	.L680-.L679
	.byte	1,5,18,7,9
	.half	.L681-.L680
	.byte	3,2,1,5,32,9
	.half	.L395-.L681
	.byte	1,5,13,9
	.half	.L34-.L395
	.byte	3,2,1,5,17,7,9
	.half	.L682-.L34
	.byte	3,2,1,9
	.half	.L35-.L682
	.byte	3,4,1,5,13,9
	.half	.L36-.L35
	.byte	3,2,1,5,43,9
	.half	.L347-.L36
	.byte	3,1,1,5,37,9
	.half	.L397-.L347
	.byte	3,1,1,5,35,9
	.half	.L398-.L397
	.byte	3,1,1,5,36,9
	.half	.L400-.L398
	.byte	3,1,1,5,13,9
	.half	.L683-.L400
	.byte	1,5,57,7,9
	.half	.L684-.L683
	.byte	3,2,1,5,27,9
	.half	.L685-.L684
	.byte	1,5,13,9
	.half	.L39-.L685
	.byte	3,2,1,5,43,9
	.half	.L349-.L39
	.byte	3,1,1,5,39,9
	.half	.L403-.L349
	.byte	3,109,1,5,32,9
	.half	.L33-.L403
	.byte	1,7,9
	.half	.L686-.L33
	.byte	1,5,18,9
	.half	.L32-.L686
	.byte	3,24,1,5,32,9
	.half	.L404-.L32
	.byte	1,5,43,9
	.half	.L44-.L404
	.byte	3,2,1,5,13,9
	.half	.L687-.L44
	.byte	3,1,1,5,17,7,9
	.half	.L688-.L687
	.byte	3,2,1,9
	.half	.L45-.L688
	.byte	3,4,1,5,13,9
	.half	.L46-.L45
	.byte	3,2,1,5,43,9
	.half	.L351-.L46
	.byte	3,1,1,5,37,9
	.half	.L406-.L351
	.byte	3,1,1,5,35,9
	.half	.L407-.L406
	.byte	3,1,1,5,36,9
	.half	.L408-.L407
	.byte	3,1,1,5,13,9
	.half	.L689-.L408
	.byte	1,5,57,7,9
	.half	.L690-.L689
	.byte	3,2,1,5,27,9
	.half	.L691-.L690
	.byte	1,5,13,9
	.half	.L49-.L691
	.byte	3,2,1,5,39,9
	.half	.L353-.L49
	.byte	3,109,1,5,32,9
	.half	.L43-.L353
	.byte	1,5,28,7,9
	.half	.L42-.L43
	.byte	3,23,1,5,5,9
	.half	.L692-.L42
	.byte	1,5,9,7,9
	.half	.L693-.L692
	.byte	3,2,1,5,5,9
	.half	.L52-.L693
	.byte	3,2,1,5,1,9
	.half	.L53-.L52
	.byte	3,1,1,7,9
	.half	.L227-.L53
	.byte	0,1,1
.L670:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_16bit_data_handler')
	.sect	'.debug_ranges'
.L226:
	.word	-1,.L90,0,.L227-.L90,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_8bit_transfer')
	.sect	'.debug_info'
.L228:
	.word	359
	.half	3
	.word	.L229
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L231,.L230
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_8bit_transfer',0,1,225,3,6,1,1,1
	.word	.L124,.L355,.L123
	.byte	4
	.byte	'soft_spi_obj',0,1,225,3,52
	.word	.L239,.L356
	.byte	4
	.byte	'write_buffer',0,1,225,3,79
	.word	.L245,.L357
	.byte	4
	.byte	'read_buffer',0,1,225,3,100
	.word	.L286,.L358
	.byte	4
	.byte	'len',0,1,225,3,120
	.word	.L247,.L359
	.byte	5
	.word	.L124,.L355
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_8bit_transfer')
	.sect	'.debug_abbrev'
.L229:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_8bit_transfer')
	.sect	'.debug_line'
.L230:
	.word	.L695-.L694
.L694:
	.half	3
	.word	.L697-.L696
.L696:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L697:
	.byte	5,6,7,0,5,2
	.word	.L124
	.byte	3,224,3,1,5,17,9
	.half	.L478-.L124
	.byte	3,2,1,5,65,9
	.half	.L75-.L478
	.byte	3,2,1,5,22,9
	.half	.L480-.L75
	.byte	1,9
	.half	.L698-.L480
	.byte	3,1,1,5,21,9
	.half	.L699-.L698
	.byte	3,1,1,5,15,9
	.half	.L74-.L699
	.byte	3,124,1,5,17,9
	.half	.L700-.L74
	.byte	1,5,1,7,9
	.half	.L701-.L700
	.byte	3,6,1,7,9
	.half	.L232-.L701
	.byte	0,1,1
.L695:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_8bit_transfer')
	.sect	'.debug_ranges'
.L231:
	.word	-1,.L124,0,.L232-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('soft_spi_16bit_transfer')
	.sect	'.debug_info'
.L233:
	.word	360
	.half	3
	.word	.L234
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L236,.L235
	.byte	2
	.word	.L129
	.byte	3
	.byte	'soft_spi_16bit_transfer',0,1,245,3,6,1,1,1
	.word	.L126,.L360,.L125
	.byte	4
	.byte	'soft_spi_obj',0,1,245,3,53
	.word	.L239,.L361
	.byte	4
	.byte	'write_buffer',0,1,245,3,81
	.word	.L255,.L362
	.byte	4
	.byte	'read_buffer',0,1,245,3,103
	.word	.L293,.L363
	.byte	4
	.byte	'len',0,1,245,3,123
	.word	.L247,.L364
	.byte	5
	.word	.L126,.L360
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('soft_spi_16bit_transfer')
	.sect	'.debug_abbrev'
.L234:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('soft_spi_16bit_transfer')
	.sect	'.debug_line'
.L235:
	.word	.L703-.L702
.L702:
	.half	3
	.word	.L705-.L704
.L704:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_soft_spi.c',0,0,0,0,0
.L705:
	.byte	5,6,7,0,5,2
	.word	.L126
	.byte	3,244,3,1,5,17,9
	.half	.L484-.L126
	.byte	3,2,1,5,66,9
	.half	.L77-.L484
	.byte	3,2,1,5,22,9
	.half	.L486-.L77
	.byte	1,9
	.half	.L706-.L486
	.byte	3,1,1,5,21,9
	.half	.L707-.L706
	.byte	3,1,1,5,15,9
	.half	.L76-.L707
	.byte	3,124,1,5,17,9
	.half	.L708-.L76
	.byte	1,5,1,7,9
	.half	.L709-.L708
	.byte	3,6,1,7,9
	.half	.L237-.L709
	.byte	0,1,1
.L703:
	.sdecl	'.debug_ranges',debug,cluster('soft_spi_16bit_transfer')
	.sect	'.debug_ranges'
.L236:
	.word	-1,.L126,0,.L237-.L126,0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_16bit_data_handler')
	.sect	'.debug_loc'
.L343:
	.word	-1,.L90,0,.L388-.L90
	.half	1
	.byte	84
	.word	0,0
.L348:
	.word	-1,.L90,.L396-.L90,.L397-.L90
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L350:
	.word	-1,.L90,.L402-.L90,.L403-.L90
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L352:
	.word	-1,.L90,.L405-.L90,.L406-.L90
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L354:
	.word	-1,.L90,.L409-.L90,.L43-.L90
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L346:
	.word	-1,.L90,.L393-.L90,.L394-.L90
	.half	1
	.byte	89
	.word	.L400-.L90,.L401-.L90
	.half	1
	.byte	89
	.word	.L408-.L90,.L340-.L90
	.half	1
	.byte	89
	.word	.L410-.L90,.L340-.L90
	.half	1
	.byte	82
	.word	0,0
.L89:
	.word	-1,.L90,0,.L340-.L90
	.half	2
	.byte	138,0
	.word	0,0
.L341:
	.word	-1,.L90,0,.L389-.L90
	.half	1
	.byte	100
	.word	.L390-.L90,.L340-.L90
	.half	1
	.byte	111
	.word	0,0
.L344:
	.word	-1,.L90,.L395-.L90,.L32-.L90
	.half	1
	.byte	90
	.word	.L404-.L90,.L42-.L90
	.half	1
	.byte	90
	.word	0,0
.L345:
	.word	-1,.L90,.L391-.L90,.L392-.L90
	.half	1
	.byte	88
	.word	.L398-.L90,.L399-.L90
	.half	1
	.byte	88
	.word	.L407-.L90,.L340-.L90
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_16bit_transfer')
	.sect	'.debug_loc'
.L364:
	.word	-1,.L126,0,.L77-.L126
	.half	1
	.byte	84
	.word	.L484-.L126,.L360-.L126
	.half	1
	.byte	88
	.word	0,0
.L363:
	.word	-1,.L126,0,.L77-.L126
	.half	1
	.byte	102
	.word	.L483-.L126,.L360-.L126
	.half	1
	.byte	109
	.word	0,0
.L125:
	.word	-1,.L126,0,.L360-.L126
	.half	2
	.byte	138,0
	.word	0,0
.L361:
	.word	-1,.L126,0,.L77-.L126
	.half	1
	.byte	100
	.word	.L481-.L126,.L360-.L126
	.half	1
	.byte	111
	.word	.L485-.L126,.L486-.L126
	.half	1
	.byte	100
	.word	0,0
.L362:
	.word	-1,.L126,0,.L77-.L126
	.half	1
	.byte	101
	.word	.L482-.L126,.L360-.L126
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_8bit_data_handler')
	.sect	'.debug_loc'
.L328:
	.word	-1,.L88,0,.L365-.L88
	.half	1
	.byte	84
	.word	0,0
.L333:
	.word	-1,.L88,.L373-.L88,.L374-.L88
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L335:
	.word	-1,.L88,.L379-.L88,.L380-.L88
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L337:
	.word	-1,.L88,.L382-.L88,.L383-.L88
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L339:
	.word	-1,.L88,.L386-.L88,.L17-.L88
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L331:
	.word	-1,.L88,.L370-.L88,.L371-.L88
	.half	1
	.byte	89
	.word	.L377-.L88,.L378-.L88
	.half	1
	.byte	89
	.word	.L385-.L88,.L325-.L88
	.half	1
	.byte	89
	.word	.L387-.L88,.L325-.L88
	.half	1
	.byte	82
	.word	0,0
.L87:
	.word	-1,.L88,0,.L325-.L88
	.half	2
	.byte	138,0
	.word	0,0
.L326:
	.word	-1,.L88,0,.L366-.L88
	.half	1
	.byte	100
	.word	.L367-.L88,.L325-.L88
	.half	1
	.byte	111
	.word	0,0
.L329:
	.word	-1,.L88,.L372-.L88,.L6-.L88
	.half	1
	.byte	90
	.word	.L381-.L88,.L16-.L88
	.half	1
	.byte	90
	.word	0,0
.L330:
	.word	-1,.L88,.L368-.L88,.L369-.L88
	.half	1
	.byte	88
	.word	.L375-.L88,.L376-.L88
	.half	1
	.byte	88
	.word	.L384-.L88,.L325-.L88
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_8bit_transfer')
	.sect	'.debug_loc'
.L359:
	.word	-1,.L124,0,.L75-.L124
	.half	1
	.byte	84
	.word	.L478-.L124,.L355-.L124
	.half	1
	.byte	88
	.word	0,0
.L358:
	.word	-1,.L124,0,.L75-.L124
	.half	1
	.byte	102
	.word	.L477-.L124,.L355-.L124
	.half	1
	.byte	109
	.word	0,0
.L123:
	.word	-1,.L124,0,.L355-.L124
	.half	2
	.byte	138,0
	.word	0,0
.L356:
	.word	-1,.L124,0,.L75-.L124
	.half	1
	.byte	100
	.word	.L475-.L124,.L355-.L124
	.half	1
	.byte	111
	.word	.L479-.L124,.L480-.L124
	.half	1
	.byte	100
	.word	0,0
.L357:
	.word	-1,.L124,0,.L75-.L124
	.half	1
	.byte	101
	.word	.L476-.L124,.L355-.L124
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_init')
	.sect	'.debug_loc'
.L324:
	.word	-1,.L128,0,.L316-.L128
	.half	2
	.byte	145,4
	.word	0,.L316-.L128
	.half	2
	.byte	145,4
	.word	.L493-.L128,.L316-.L128
	.half	1
	.byte	93
	.word	0,0
.L319:
	.word	-1,.L128,0,.L487-.L128
	.half	1
	.byte	85
	.word	.L505-.L128,.L506-.L128
	.half	1
	.byte	89
	.word	0,0
.L323:
	.word	-1,.L128,0,.L316-.L128
	.half	2
	.byte	145,0
	.word	0,.L316-.L128
	.half	2
	.byte	145,0
	.word	.L492-.L128,.L316-.L128
	.half	1
	.byte	92
	.word	0,0
.L318:
	.word	-1,.L128,0,.L488-.L128
	.half	1
	.byte	84
	.word	.L501-.L128,.L502-.L128
	.half	1
	.byte	88
	.word	.L503-.L128,.L504-.L128
	.half	1
	.byte	88
	.word	.L508-.L128,.L80-.L128
	.half	1
	.byte	88
	.word	0,0
.L322:
	.word	-1,.L128,0,.L489-.L128
	.half	1
	.byte	87
	.word	.L493-.L128,.L488-.L128
	.half	1
	.byte	91
	.word	.L497-.L128,.L498-.L128
	.half	1
	.byte	91
	.word	.L499-.L128,.L500-.L128
	.half	1
	.byte	91
	.word	.L507-.L128,.L508-.L128
	.half	1
	.byte	91
	.word	.L514-.L128,.L515-.L128
	.half	1
	.byte	91
	.word	.L515-.L128,.L516-.L128
	.half	1
	.byte	84
	.word	0,0
.L321:
	.word	-1,.L128,0,.L489-.L128
	.half	1
	.byte	86
	.word	.L493-.L128,.L488-.L128
	.half	1
	.byte	90
	.word	.L489-.L128,.L494-.L128
	.half	1
	.byte	90
	.word	.L495-.L128,.L496-.L128
	.half	1
	.byte	90
	.word	.L506-.L128,.L507-.L128
	.half	1
	.byte	90
	.word	.L509-.L128,.L510-.L128
	.half	1
	.byte	90
	.word	.L510-.L128,.L511-.L128
	.half	1
	.byte	84
	.word	.L512-.L128,.L513-.L128
	.half	1
	.byte	90
	.word	.L513-.L128,.L82-.L128
	.half	1
	.byte	84
	.word	0,0
.L127:
	.word	-1,.L128,0,.L316-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L317:
	.word	-1,.L128,0,.L490-.L128
	.half	1
	.byte	100
	.word	.L491-.L128,.L316-.L128
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_16bit')
	.sect	'.debug_loc'
.L290:
	.word	-1,.L112,0,.L449-.L112
	.half	1
	.byte	100
	.word	0,0
.L111:
	.word	-1,.L112,0,.L289-.L112
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_16bit_array')
	.sect	'.debug_loc'
.L294:
	.word	-1,.L114,0,.L67-.L114
	.half	1
	.byte	101
	.word	.L451-.L114,.L291-.L114
	.half	1
	.byte	108
	.word	0,0
.L295:
	.word	-1,.L114,0,.L67-.L114
	.half	1
	.byte	84
	.word	.L452-.L114,.L291-.L114
	.half	1
	.byte	88
	.word	0,0
.L292:
	.word	-1,.L114,0,.L67-.L114
	.half	1
	.byte	100
	.word	.L450-.L114,.L291-.L114
	.half	1
	.byte	111
	.word	.L453-.L114,.L454-.L114
	.half	1
	.byte	100
	.word	0,0
.L113:
	.word	-1,.L114,0,.L291-.L114
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_16bit_register')
	.sect	'.debug_loc'
.L309:
	.word	-1,.L120,0,.L465-.L120
	.half	1
	.byte	84
	.word	0,0
.L307:
	.word	-1,.L120,0,.L465-.L120
	.half	1
	.byte	100
	.word	.L466-.L120,.L306-.L120
	.half	1
	.byte	111
	.word	.L467-.L120,.L468-.L120
	.half	1
	.byte	100
	.word	0,0
.L119:
	.word	-1,.L120,0,.L306-.L120
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_16bit_registers')
	.sect	'.debug_loc'
.L314:
	.word	-1,.L122,0,.L469-.L122
	.half	1
	.byte	101
	.word	.L471-.L122,.L310-.L122
	.half	1
	.byte	108
	.word	0,0
.L315:
	.word	-1,.L122,0,.L469-.L122
	.half	1
	.byte	85
	.word	.L472-.L122,.L310-.L122
	.half	1
	.byte	88
	.word	0,0
.L313:
	.word	-1,.L122,0,.L469-.L122
	.half	1
	.byte	84
	.word	0,0
.L311:
	.word	-1,.L122,0,.L469-.L122
	.half	1
	.byte	100
	.word	.L470-.L122,.L310-.L122
	.half	1
	.byte	111
	.word	.L473-.L122,.L474-.L122
	.half	1
	.byte	100
	.word	0,0
.L121:
	.word	-1,.L122,0,.L310-.L122
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_8bit')
	.sect	'.debug_loc'
.L283:
	.word	-1,.L108,0,.L443-.L108
	.half	1
	.byte	100
	.word	0,0
.L107:
	.word	-1,.L108,0,.L282-.L108
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_8bit_array')
	.sect	'.debug_loc'
.L287:
	.word	-1,.L110,0,.L64-.L110
	.half	1
	.byte	101
	.word	.L445-.L110,.L284-.L110
	.half	1
	.byte	108
	.word	0,0
.L288:
	.word	-1,.L110,0,.L64-.L110
	.half	1
	.byte	84
	.word	.L446-.L110,.L284-.L110
	.half	1
	.byte	88
	.word	0,0
.L285:
	.word	-1,.L110,0,.L64-.L110
	.half	1
	.byte	100
	.word	.L444-.L110,.L284-.L110
	.half	1
	.byte	111
	.word	.L447-.L110,.L448-.L110
	.half	1
	.byte	100
	.word	0,0
.L109:
	.word	-1,.L110,0,.L284-.L110
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_8bit_register')
	.sect	'.debug_loc'
.L299:
	.word	-1,.L116,0,.L455-.L116
	.half	1
	.byte	84
	.word	0,0
.L297:
	.word	-1,.L116,0,.L455-.L116
	.half	1
	.byte	100
	.word	.L456-.L116,.L296-.L116
	.half	1
	.byte	111
	.word	.L457-.L116,.L458-.L116
	.half	1
	.byte	100
	.word	0,0
.L115:
	.word	-1,.L116,0,.L296-.L116
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_read_8bit_registers')
	.sect	'.debug_loc'
.L304:
	.word	-1,.L118,0,.L459-.L118
	.half	1
	.byte	101
	.word	.L461-.L118,.L300-.L118
	.half	1
	.byte	108
	.word	0,0
.L305:
	.word	-1,.L118,0,.L459-.L118
	.half	1
	.byte	85
	.word	.L462-.L118,.L300-.L118
	.half	1
	.byte	88
	.word	0,0
.L303:
	.word	-1,.L118,0,.L459-.L118
	.half	1
	.byte	84
	.word	0,0
.L301:
	.word	-1,.L118,0,.L459-.L118
	.half	1
	.byte	100
	.word	.L460-.L118,.L300-.L118
	.half	1
	.byte	111
	.word	.L463-.L118,.L464-.L118
	.half	1
	.byte	100
	.word	0,0
.L117:
	.word	-1,.L118,0,.L300-.L118
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_16bit')
	.sect	'.debug_loc'
.L252:
	.word	-1,.L96,0,.L416-.L96
	.half	1
	.byte	84
	.word	0,0
.L250:
	.word	-1,.L96,0,.L416-.L96
	.half	1
	.byte	100
	.word	0,0
.L95:
	.word	-1,.L96,0,.L249-.L96
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_16bit_array')
	.sect	'.debug_loc'
.L256:
	.word	-1,.L98,0,.L57-.L98
	.half	1
	.byte	101
	.word	.L418-.L98,.L253-.L98
	.half	1
	.byte	108
	.word	0,0
.L257:
	.word	-1,.L98,0,.L57-.L98
	.half	1
	.byte	84
	.word	.L419-.L98,.L253-.L98
	.half	1
	.byte	88
	.word	0,0
.L254:
	.word	-1,.L98,0,.L57-.L98
	.half	1
	.byte	100
	.word	.L417-.L98,.L253-.L98
	.half	1
	.byte	111
	.word	.L420-.L98,.L56-.L98
	.half	1
	.byte	100
	.word	0,0
.L97:
	.word	-1,.L98,0,.L253-.L98
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_16bit_register')
	.sect	'.debug_loc'
.L274:
	.word	-1,.L104,0,.L432-.L104
	.half	1
	.byte	85
	.word	.L434-.L104,.L270-.L104
	.half	1
	.byte	95
	.word	.L437-.L104,.L436-.L104
	.half	1
	.byte	84
	.word	0,0
.L273:
	.word	-1,.L104,0,.L432-.L104
	.half	1
	.byte	84
	.word	0,0
.L271:
	.word	-1,.L104,0,.L432-.L104
	.half	1
	.byte	100
	.word	.L433-.L104,.L270-.L104
	.half	1
	.byte	111
	.word	.L435-.L104,.L436-.L104
	.half	1
	.byte	100
	.word	0,0
.L103:
	.word	-1,.L104,0,.L270-.L104
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_16bit_registers')
	.sect	'.debug_loc'
.L279:
	.word	-1,.L106,0,.L438-.L106
	.half	1
	.byte	101
	.word	.L440-.L106,.L275-.L106
	.half	1
	.byte	108
	.word	0,0
.L280:
	.word	-1,.L106,0,.L438-.L106
	.half	1
	.byte	85
	.word	.L441-.L106,.L275-.L106
	.half	1
	.byte	88
	.word	0,0
.L278:
	.word	-1,.L106,0,.L438-.L106
	.half	1
	.byte	84
	.word	0,0
.L276:
	.word	-1,.L106,0,.L438-.L106
	.half	1
	.byte	100
	.word	.L439-.L106,.L275-.L106
	.half	1
	.byte	111
	.word	.L442-.L106,.L60-.L106
	.half	1
	.byte	100
	.word	0,0
.L105:
	.word	-1,.L106,0,.L275-.L106
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_8bit')
	.sect	'.debug_loc'
.L242:
	.word	-1,.L92,0,.L411-.L92
	.half	1
	.byte	84
	.word	0,0
.L240:
	.word	-1,.L92,0,.L411-.L92
	.half	1
	.byte	100
	.word	0,0
.L91:
	.word	-1,.L92,0,.L238-.L92
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_8bit_array')
	.sect	'.debug_loc'
.L246:
	.word	-1,.L94,0,.L55-.L94
	.half	1
	.byte	101
	.word	.L413-.L94,.L243-.L94
	.half	1
	.byte	108
	.word	0,0
.L248:
	.word	-1,.L94,0,.L55-.L94
	.half	1
	.byte	84
	.word	.L414-.L94,.L243-.L94
	.half	1
	.byte	88
	.word	0,0
.L244:
	.word	-1,.L94,0,.L55-.L94
	.half	1
	.byte	100
	.word	.L412-.L94,.L243-.L94
	.half	1
	.byte	111
	.word	.L415-.L94,.L54-.L94
	.half	1
	.byte	100
	.word	0,0
.L93:
	.word	-1,.L94,0,.L243-.L94
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_8bit_register')
	.sect	'.debug_loc'
.L263:
	.word	-1,.L100,0,.L421-.L100
	.half	1
	.byte	85
	.word	.L423-.L100,.L258-.L100
	.half	1
	.byte	95
	.word	.L426-.L100,.L425-.L100
	.half	1
	.byte	84
	.word	0,0
.L261:
	.word	-1,.L100,0,.L421-.L100
	.half	1
	.byte	84
	.word	0,0
.L259:
	.word	-1,.L100,0,.L421-.L100
	.half	1
	.byte	100
	.word	.L422-.L100,.L258-.L100
	.half	1
	.byte	111
	.word	.L424-.L100,.L425-.L100
	.half	1
	.byte	100
	.word	0,0
.L99:
	.word	-1,.L100,0,.L258-.L100
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('soft_spi_write_8bit_registers')
	.sect	'.debug_loc'
.L268:
	.word	-1,.L102,0,.L427-.L102
	.half	1
	.byte	101
	.word	.L429-.L102,.L264-.L102
	.half	1
	.byte	108
	.word	0,0
.L269:
	.word	-1,.L102,0,.L427-.L102
	.half	1
	.byte	85
	.word	.L430-.L102,.L264-.L102
	.half	1
	.byte	88
	.word	0,0
.L267:
	.word	-1,.L102,0,.L427-.L102
	.half	1
	.byte	84
	.word	0,0
.L265:
	.word	-1,.L102,0,.L427-.L102
	.half	1
	.byte	100
	.word	.L428-.L102,.L264-.L102
	.half	1
	.byte	111
	.word	.L431-.L102,.L58-.L102
	.half	1
	.byte	100
	.word	0,0
.L101:
	.word	-1,.L102,0,.L264-.L102
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L710:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('soft_spi_8bit_data_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L88,.L325-.L88
	.sdecl	'.debug_frame',debug,cluster('soft_spi_16bit_data_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L90,.L340-.L90
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_8bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L92,.L238-.L92
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_8bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L94,.L243-.L94
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_16bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L96,.L249-.L96
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_16bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L98,.L253-.L98
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_8bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L100,.L258-.L100
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_8bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L102,.L264-.L102
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_16bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L104,.L270-.L104
	.sdecl	'.debug_frame',debug,cluster('soft_spi_write_16bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L106,.L275-.L106
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_8bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L108,.L282-.L108
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_8bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L110,.L284-.L110
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_16bit')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L112,.L289-.L112
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_16bit_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L114,.L291-.L114
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_8bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L116,.L296-.L116
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_8bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L118,.L300-.L118
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_16bit_register')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L120,.L306-.L120
	.sdecl	'.debug_frame',debug,cluster('soft_spi_read_16bit_registers')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L122,.L310-.L122
	.sdecl	'.debug_frame',debug,cluster('soft_spi_8bit_transfer')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L124,.L355-.L124
	.sdecl	'.debug_frame',debug,cluster('soft_spi_16bit_transfer')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L126,.L360-.L126
	.sdecl	'.debug_frame',debug,cluster('soft_spi_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L710,.L128,.L316-.L128
	; Module end
