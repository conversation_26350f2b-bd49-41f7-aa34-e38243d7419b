/**
 * \file Compilers.h
 *
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 * $Date: 2014-04-07 12:13:19 GMT$
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */

#ifndef COMPILERS_H
#define COMPILERS_H 1

/******************************************************************************/

#include "Ifx_Cfg.h"

/*this file shall not be modified by the user, IFX_XXXX defines shall be defined in Ifx_Cfg.h */
#ifndef IFX_STATIC 
#define IFX_STATIC static
#endif

#ifndef IFX_CONST
#define IFX_CONST const
#endif
#ifndef CONST_CFG
#define CONST_CFG       const           /* configuration constants are stored in ROM */
#endif

#ifdef __cplusplus
#define IFX_EXTERN      extern "C"
#else
#define IFX_EXTERN      extern
#endif

#ifndef NULL_PTR
#ifdef __cplusplus
#define NULL_PTR        (0)
#else /*#ifdef __cplusplus */
#define NULL_PTR        ((void *)0)
#endif /*#ifdef __cplusplus */
#endif /*#ifndef NULL_PTR */

#ifndef CFG_LONG_SIZE_T
#define CFG_LONG_SIZE_T (0)
#endif

#if defined(__DCC__)
#include "CompilerDcc.h"

#elif defined(__HIGHTEC__)
#include "CompilerGnuc.h"

#elif defined(__TASKING__)
#include "CompilerTasking.h"

#elif defined(__ghs__)
#include "CompilerGhs.h"

#elif defined(__MSVC__)
#include "CompilerMsvc.h"

#else

/** \addtogroup IfxLld_Cpu_Std_Interrupt
 * \{  */
/** \brief Macro to define Interrupt Service Routine.
 * This macro makes following definitions:\n
 * 1) Define linker section as .intvec_tc<vector number>_<interrupt priority>.\n
 * 2) define compiler specific attribute for the interrupt functions.\n
 * 3) define the Interrupt service routine as Isr function.\n
 * To get details about usage of this macro, refer \ref IfxLld_Cpu_Irq_Usage
 *
 * \param isr Name of the Isr function.
 * \param vectabNum Vector table number. 
 * \param prio Interrupt priority. Refer Usage of Interrupt Macro for more details.
 */
#define IFX_INTERRUPT(isr, vectabNum, prio)

/** \}  */
#error "Compiler unsupported"
#endif

#if defined(__HIGHTEC__)
#define BEGIN_DATA_SECTION(sec) DATA_SECTION(section #sec aw 4)
#define DATA_SECTION(sec) _Pragma(#sec)
#define END_DATA_SECTION DATA_SECTION(section)
#elif defined(__TASKING__)
#define BEGIN_DATA_SECTION(sec) DATA_SECTION(section farbss #sec)
#define DATA_SECTION(sec) _Pragma(#sec)
#define END_DATA_SECTION DATA_SECTION(section farbss align restore) \
		DATA_SECTION(section farbss)
#elif defined(__DCC__)
#define BEGIN_DATA_SECTION(sec) DATA_SECTION(section #sec WX)
#define DATA_SECTION(sec) _Pragma(#sec)
#define END_DATA_SECTION DATA_SECTION(section DATA X)
#elif defined(__ghs__)
#define BEGIN_DATA_SECTION(sec) DATA_SECTION(section #sec WX)
#define DATA_SECTION(sec) _Pragma(#sec)
#define END_DATA_SECTION DATA_SECTION(section DATA X)
#else
#error "Please specify compiler."
#endif

/* Functions prototypes                                                       */
/******************************************************************************/
void Ifx_C_Init(void);
/******************************************************************************/


#endif /* COMPILERS_H */
