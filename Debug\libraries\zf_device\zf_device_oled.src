	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc404a --dep-file=zf_device_oled.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_oled.src ../libraries/zf_device/zf_device_oled.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_oled.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_oled.oled_write_data',code,cluster('oled_write_data')
	.sect	'.text.zf_device_oled.oled_write_data'
	.align	2
	
; Function oled_write_data
.L127:
oled_write_data:	.type	func
	mov	d8,d4
.L397:
	mov	d15,#1
	jeq	d15,#0,.L2
	mov	d4,#480
.L396:
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L3
.L2:
	mov	d4,#480
.L398:
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L3:
	mov	d4,#2
	mov	d5,d8
.L399:
	call	spi_write_8bit
.L400:
	ret
.L384:
	
__oled_write_data_function_end:
	.size	oled_write_data,__oled_write_data_function_end-oled_write_data
.L240:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_write_command',code,cluster('oled_write_command')
	.sect	'.text.zf_device_oled.oled_write_command'
	.align	2
	
; Function oled_write_command
.L129:
oled_write_command:	.type	func
	mov	d8,d4
.L402:
	mov	d15,#0
	jeq	d15,#0,.L4
	mov	d4,#480
.L401:
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L5
.L4:
	mov	d4,#480
.L403:
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L5:
	mov	d4,#2
	mov	d5,d8
.L404:
	call	spi_write_8bit
.L405:
	ret
.L387:
	
__oled_write_command_function_end:
	.size	oled_write_command,__oled_write_command_function_end-oled_write_command
.L245:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_set_coordinate',code,cluster('oled_set_coordinate')
	.sect	'.text.zf_device_oled.oled_set_coordinate'
	.align	2
	
; Function oled_set_coordinate
.L131:
oled_set_coordinate:	.type	func
	mov	d15,d4
.L408:
	mov	d8,d5
.L409:
	lt.u	d4,d15,#128
.L406:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#107
.L407:
	call	debug_assert_handler
.L1075:
	lt.u	d4,d8,#8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#108
	call	debug_assert_handler
.L1076:
	add	d0,d8,#176
	extr.u	d4,d0,#0,#8
	call	oled_write_command
.L1077:
	and	d0,d15,#240
.L1078:
	sha	d0,#-4
.L1079:
	or	d4,d0,#16
	call	oled_write_command
.L1080:
	and	d4,d15,#15
	call	oled_write_command
.L1081:
	ret
.L390:
	
__oled_set_coordinate_function_end:
	.size	oled_set_coordinate,__oled_set_coordinate_function_end-oled_set_coordinate
.L250:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_debug_init',code,cluster('oled_debug_init')
	.sect	'.text.zf_device_oled.oled_debug_init'
	.align	2
	
; Function oled_debug_init
.L133:
oled_debug_init:	.type	func
	sub.a	a10,#24
.L410:
	lea	a4,[a10]0
	call	debug_output_struct_init
.L1086:
	mov	d15,#1
.L1087:
	st.h	[a10],d15
.L1088:
	mov	d15,#128
.L1089:
	st.h	[a10]2,d15
.L1090:
	mov	d15,#64
.L1091:
	st.h	[a10]4,d15
.L1092:
	movh.a	a15,#@his(oled_display_font)
	lea	a15,[a15]@los(oled_display_font)
	ld.bu	d15,[a15]
.L1093:
	mov	d0,#0
	jeq	d15,d0,.L6
.L1094:
	mov	d0,#1
	jeq	d15,d0,.L7
.L1095:
	mov	d0,#2
	jeq	d15,d0,.L8
	j	.L9
.L6:
	mov	d15,#6
.L1096:
	st.b	[a10]6,d15
.L1097:
	mov	d15,#1
.L1098:
	st.b	[a10]7,d15
.L1099:
	j	.L10
.L7:
	mov	d15,#8
.L1100:
	st.b	[a10]6,d15
.L1101:
	mov	d15,#2
.L1102:
	st.b	[a10]7,d15
.L1103:
	j	.L11
.L8:
	j	.L12
.L9:
.L12:
.L11:
.L10:
	movh.a	a15,#@his(oled_show_string)
	lea	a15,[a15]@los(oled_show_string)
.L1104:
	st.a	[a10]12,a15
.L1105:
	movh.a	a15,#@his(oled_clear)
	lea	a15,[a15]@los(oled_clear)
.L1106:
	st.a	[a10]16,a15
.L1107:
	lea	a4,[a10]0
	call	debug_output_init
.L1108:
	ret
.L393:
	
__oled_debug_init_function_end:
	.size	oled_debug_init,__oled_debug_init_function_end-oled_debug_init
.L255:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_clear',code,cluster('oled_clear')
	.sect	'.text.zf_device_oled.oled_clear'
	.align	2
	
	.global	oled_clear
; Function oled_clear
.L135:
oled_clear:	.type	func
	mov	d15,#0
	jeq	d15,#0,.L13
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L14
.L13:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L14:
	mov	d8,#0
.L411:
	j	.L15
.L16:
	add	d4,d8,#176
	call	oled_write_command
.L677:
	mov	d4,#1
	call	oled_write_command
.L678:
	mov	d4,#16
	call	oled_write_command
.L679:
	mov	d9,#0
.L412:
	j	.L17
.L18:
	mov	d4,#0
	call	oled_write_data
.L680:
	add	d9,#1
.L17:
	mov	d15,#128
.L681:
	jlt.u	d9,d15,.L18
.L682:
	add	d8,#1
.L15:
	jlt.u	d8,#8,.L16
.L683:
	mov	d15,#1
	jeq	d15,#0,.L19
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L20
.L19:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L20:
	ret
.L260:
	
__oled_clear_function_end:
	.size	oled_clear,__oled_clear_function_end-oled_clear
.L170:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_full',code,cluster('oled_full')
	.sect	'.text.zf_device_oled.oled_full'
	.align	2
	
	.global	oled_full
; Function oled_full
.L137:
oled_full:	.type	func
	mov	d8,d4
.L414:
	mov	d15,#0
	jeq	d15,#0,.L21
	mov	d4,#482
.L413:
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L22
.L21:
	mov	d4,#482
.L415:
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L22:
	mov	d9,#0
.L416:
	j	.L23
.L24:
	add	d4,d9,#176
	call	oled_write_command
.L688:
	mov	d4,#1
	call	oled_write_command
.L689:
	mov	d4,#16
	call	oled_write_command
.L690:
	mov	d10,#0
.L417:
	j	.L25
.L26:
	mov	d4,d8
.L418:
	call	oled_write_data
.L419:
	add	d10,#1
.L25:
	mov	d15,#128
.L691:
	jlt.u	d10,d15,.L26
.L692:
	add	d9,#1
.L23:
	jlt.u	d9,#8,.L24
.L693:
	mov	d15,#1
	jeq	d15,#0,.L27
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L28
.L27:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L28:
	ret
.L264:
	
__oled_full_function_end:
	.size	oled_full,__oled_full_function_end-oled_full
.L175:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_set_dir',code,cluster('oled_set_dir')
	.sect	'.text.zf_device_oled.oled_set_dir'
	.align	2
	
	.global	oled_set_dir
; Function oled_set_dir
.L139:
oled_set_dir:	.type	func
	movh.a	a15,#@his(oled_display_dir)
	lea	a15,[a15]@los(oled_display_dir)
.L698:
	st.b	[a15],d4
.L699:
	ret
.L269:
	
__oled_set_dir_function_end:
	.size	oled_set_dir,__oled_set_dir_function_end-oled_set_dir
.L180:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_set_font',code,cluster('oled_set_font')
	.sect	'.text.zf_device_oled.oled_set_font'
	.align	2
	
	.global	oled_set_font
; Function oled_set_font
.L141:
oled_set_font:	.type	func
	movh.a	a15,#@his(oled_display_font)
	lea	a15,[a15]@los(oled_display_font)
.L704:
	st.b	[a15],d4
.L705:
	ret
.L272:
	
__oled_set_font_function_end:
	.size	oled_set_font,__oled_set_font_function_end-oled_set_font
.L185:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_draw_point',code,cluster('oled_draw_point')
	.sect	'.text.zf_device_oled.oled_draw_point'
	.align	2
	
	.global	oled_draw_point
; Function oled_draw_point
.L143:
oled_draw_point:	.type	func
	mov	e8,d4,d5
	mov	d10,d6
.L423:
	lt.u	d4,d9,#128
.L421:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#242
.L422:
	call	debug_assert_handler
.L420:
	lt.u	d4,d8,#8
.L424:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#243
	call	debug_assert_handler
.L710:
	mov	d15,#0
	jeq	d15,#0,.L29
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L30
.L29:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L30:
	extr.u	d4,d9,#0,#8
.L425:
	extr.u	d5,d8,#0,#8
.L426:
	call	oled_set_coordinate
.L427:
	add	d0,d8,#176
.L428:
	extr.u	d4,d0,#0,#8
	call	oled_write_command
.L429:
	and	d15,d9,#240
.L430:
	sha	d15,#-4
.L711:
	or	d4,d15,#16
	call	oled_write_command
.L431:
	and	d4,d9,#15
.L432:
	call	oled_write_command
.L712:
	mov	d4,d10
.L433:
	call	oled_write_data
.L434:
	mov	d15,#1
	jeq	d15,#0,.L31
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L32
.L31:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L32:
	ret
.L275:
	
__oled_draw_point_function_end:
	.size	oled_draw_point,__oled_draw_point_function_end-oled_draw_point
.L190:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_string',code,cluster('oled_show_string')
	.sect	'.text.zf_device_oled.oled_show_string'
	.align	2
	
	.global	oled_show_string
; Function oled_show_string
.L145:
oled_show_string:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L438:
	lt.u	d4,d8,#128
.L436:
	movh.a	a4,#@his(.1.str)
.L435:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#268
.L437:
	call	debug_assert_handler
.L439:
	lt.u	d4,d9,#8
.L440:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#269
	call	debug_assert_handler
.L717:
	mov	d15,#0
	jeq	d15,#0,.L33
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L34
.L33:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L34:
	mov	d10,#0
.L441:
	j	.L35
.L36:
	movh.a	a15,#@his(oled_display_font)
	lea	a15,[a15]@los(oled_display_font)
	ld.bu	d0,[a15]
.L718:
	mov	d15,#0
	jeq	d15,d0,.L37
.L719:
	mov	d1,#1
	jeq	d1,d0,.L38
.L720:
	mov	d1,#2
	jeq	d1,d0,.L39
	j	.L40
.L37:
	addsc.a	a15,a12,d10,#0
	ld.b	d0,[a15]0
.L721:
	add	d15,d0,#-32
	extr.u	d11,d15,#0,#8
.L443:
	mov	d15,#126
.L444:
	jge.u	d15,d8,.L41
.L445:
	mov	d8,#0
.L446:
	add	d9,#1
	extr.u	d9,d9,#0,#16
.L41:
	extr.u	d5,d9,#0,#8
.L447:
	mov	d4,d8
.L448:
	call	oled_set_coordinate
.L449:
	mov	d15,#0
.L450:
	j	.L42
.L43:
	mul	d0,d11,#6
.L722:
	movh.a	a15,#@his(ascii_font_6x8)
	lea	a15,[a15]@los(ascii_font_6x8)
.L723:
	addsc.a	a15,a15,d0,#0
.L724:
	addsc.a	a15,a15,d15,#0
	ld.bu	d4,[a15]
	call	oled_write_data
.L725:
	add	d15,#1
.L42:
	jlt.u	d15,#6,.L43
.L451:
	add	d8,#6
.L726:
	add	d10,#1
.L442:
	extr.u	d10,d10,#0,#8
.L452:
	j	.L44
.L38:
	addsc.a	a15,a12,d10,#0
	ld.b	d15,[a15]0
.L727:
	add	d15,d15,#-32
	extr.u	d11,d15,#0,#8
.L454:
	mov	d15,#120
.L455:
	jge.u	d15,d8,.L45
.L456:
	mov	d8,#0
.L457:
	add	d9,#1
	extr.u	d9,d9,#0,#16
.L45:
	extr.u	d5,d9,#0,#8
.L458:
	mov	d4,d8
.L459:
	call	oled_set_coordinate
.L460:
	mov	d15,#0
.L461:
	j	.L46
.L47:
	mul	d0,d11,#16
.L728:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L729:
	addsc.a	a15,a15,d0,#0
.L730:
	addsc.a	a15,a15,d15,#0
	ld.bu	d4,[a15]
	call	oled_write_data
.L731:
	add	d15,#1
.L46:
	jlt.u	d15,#8,.L47
.L463:
	add	d15,d9,#1
.L462:
	extr.u	d5,d15,#0,#8
.L464:
	mov	d4,d8
.L465:
	call	oled_set_coordinate
.L466:
	mov	d15,#0
.L467:
	j	.L48
.L49:
	mul	d0,d11,#16
.L732:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L733:
	addsc.a	a15,a15,d0,#0
.L734:
	addsc.a	a15,a15,d15,#0
	ld.bu	d4,[a15]8
	call	oled_write_data
.L735:
	add	d15,#1
.L48:
	jlt.u	d15,#8,.L49
.L468:
	add	d8,d8,#8
.L736:
	add	d10,#1
.L453:
	extr.u	d10,d10,#0,#8
.L469:
	j	.L50
.L39:
	j	.L51
.L40:
.L51:
.L50:
.L44:
.L35:
	addsc.a	a15,a12,d10,#0
	ld.b	d15,[a15]0
.L737:
	jne	d15,#0,.L36
.L738:
	mov	d15,#1
	jeq	d15,#0,.L52
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L53
.L52:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L53:
	ret
.L281:
	
__oled_show_string_function_end:
	.size	oled_show_string,__oled_show_string_function_end-oled_show_string
.L195:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_int',code,cluster('oled_show_int')
	.sect	'.text.zf_device_oled.oled_show_int'
	.align	2
	
	.global	oled_show_int
; Function oled_show_int
.L147:
oled_show_int:	.type	func
	sub.a	a10,#16
.L470:
	mov	e8,d5,d4
	mov	d13,d6
.L474:
	mov	d10,d7
.L475:
	lt.u	d4,d8,#128
.L472:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#339
.L473:
	call	debug_assert_handler
.L471:
	lt.u	d4,d9,#8
.L476:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#340
	call	debug_assert_handler
.L743:
	mov	d15,#0
	lt.u	d4,d15,d10
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#342
	call	debug_assert_handler
.L744:
	mov	d15,#10
	ge.u	d4,d15,d10
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#343
	call	debug_assert_handler
.L295:
	mov	d15,#1
.L477:
	lea	a4,[a10]0
.L745:
	mov	d4,#0
.L746:
	mov	d5,#12
	call	memset
.L747:
	lea	a4,[a10]0
.L748:
	mov	d4,#32
.L749:
	add	d5,d10,#1
	call	memset
.L750:
	jge.u	d10,#10,.L54
.L751:
	j	.L55
.L56:
	mul	d15,d15,#10
.L752:
	add	d10,#-1
.L55:
	jge.u	d10,#1,.L56
.L753:
	div	e12,d13,d15
.L54:
	lea	a4,[a10]0
.L754:
	mov	d4,d13
.L478:
	call	func_int_to_str
.L479:
	lea	a4,[a10]0
.L480:
	mov	e4,d9,d8
.L481:
	call	oled_show_string
.L755:
	ret
.L289:
	
__oled_show_int_function_end:
	.size	oled_show_int,__oled_show_int_function_end-oled_show_int
.L200:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_uint',code,cluster('oled_show_uint')
	.sect	'.text.zf_device_oled.oled_show_uint'
	.align	2
	
	.global	oled_show_uint
; Function oled_show_uint
.L149:
oled_show_uint:	.type	func
	sub.a	a10,#16
.L482:
	mov	e8,d5,d4
	mov	d13,d6
.L486:
	mov	d10,d7
.L487:
	lt.u	d4,d8,#128
.L484:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#380
.L485:
	call	debug_assert_handler
.L483:
	lt.u	d4,d9,#8
.L488:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#381
	call	debug_assert_handler
.L760:
	mov	d15,#0
	lt.u	d4,d15,d10
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#383
	call	debug_assert_handler
.L761:
	mov	d15,#10
	ge.u	d4,d15,d10
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#384
	call	debug_assert_handler
.L307:
	mov	d15,#1
.L489:
	lea	a4,[a10]0
.L762:
	mov	d4,#0
.L763:
	mov	d5,#12
	call	memset
.L764:
	lea	a4,[a10]0
.L765:
	mov	d4,#32
.L766:
	mov	d5,d10
.L490:
	call	memset
.L491:
	jge.u	d10,#10,.L57
.L767:
	j	.L58
.L59:
	mul	d15,d15,#10
.L768:
	add	d10,#-1
.L58:
	jge.u	d10,#1,.L59
.L769:
	div.u	e12,d13,d15
.L57:
	lea	a4,[a10]0
.L770:
	mov	d4,d13
.L492:
	call	func_uint_to_str
.L493:
	lea	a4,[a10]0
.L494:
	mov	e4,d9,d8
.L495:
	call	oled_show_string
.L771:
	ret
.L301:
	
__oled_show_uint_function_end:
	.size	oled_show_uint,__oled_show_uint_function_end-oled_show_uint
.L205:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_float',code,cluster('oled_show_float')
	.sect	'.text.zf_device_oled.oled_show_float'
	.align	2
	
	.global	oled_show_float
; Function oled_show_float
.L151:
oled_show_float:	.type	func
	sub.a	a10,#24
.L496:
	st.w	[a10]20,d4
.L500:
	mov	d14,d5
.L501:
	mov	e10,d7,d6
	ld.bu	d9,[a10]24
.L502:
	ld.bu	d8,[a10]28
.L503:
	ld.w	d15,[a10]20
.L498:
	lt.u	d4,d15,#128
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#424
.L499:
	call	debug_assert_handler
.L497:
	lt.u	d4,d14,#8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#425
	call	debug_assert_handler
.L776:
	mov	d15,#0
.L504:
	lt.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#427
	call	debug_assert_handler
.L777:
	mov	d15,#8
	ge.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#428
	call	debug_assert_handler
.L778:
	mov	d15,#0
	lt.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#429
	call	debug_assert_handler
.L779:
	mov	d15,#6
	ge.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#430
	call	debug_assert_handler
.L319:
	mov	d12,#0
	mov	d13,#0
.L505:
	addih	d13,d13,#16368
.L780:
	lea	a4,[a10]0
.L781:
	mov	d4,#0
.L782:
	mov	d5,#17
	call	memset
.L783:
	lea	a4,[a10]0
.L784:
	mov	d4,#32
.L785:
	add	d15,d9,d8
.L786:
	add	d5,d15,#2
	call	memset
.L787:
	j	.L60
.L61:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L506:
	mov	e4,d13,d12
.L507:
	call	__d_mul
	mov	e12,d3,d2
.L788:
	add	d9,#-1
.L60:
	jge.u	d9,#1,.L61
.L508:
	mov	e4,d11,d10
.L509:
	call	__d_dtoi
	mov	d15,d2
.L510:
	mov	e4,d13,d12
.L511:
	call	__d_dtoi
.L789:
	div	e4,d15,d2
	call	__d_itod
	mov	e4,d3,d2
.L512:
	mov	e6,d13,d12
.L513:
	call	__d_mul
	mov	e6,d3,d2
.L514:
	mov	e4,d11,d10
.L515:
	call	__d_sub
	mov	e4,d3,d2
.L790:
	lea	a4,[a10]0
.L791:
	mov	d6,d8
.L516:
	call	func_double_to_str
.L517:
	lea	a4,[a10]0
	ld.w	d4,[a10]20
.L518:
	mov	d5,d14
.L520:
	call	oled_show_string
.L519:
	ret
.L312:
	
__oled_show_float_function_end:
	.size	oled_show_float,__oled_show_float_function_end-oled_show_float
.L210:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_binary_image',code,cluster('oled_show_binary_image')
	.sect	'.text.zf_device_oled.oled_show_binary_image'
	.align	2
	
	.global	oled_show_binary_image
; Function oled_show_binary_image
.L153:
oled_show_binary_image:	.type	func
	sub.a	a10,#24
.L521:
	st.w	[a10]16,d4
.L528:
	st.w	[a10]12,d5
.L529:
	mov.aa	a12,a4
.L530:
	mov	d9,d6
.L531:
	st.w	[a10]20,d7
.L532:
	ld.hu	d8,[a10]24
.L533:
	ld.hu	d10,[a10]28
.L535:
	ld.w	d15,[a10]16
.L526:
	lt.u	d4,d15,#128
	movh.a	a4,#@his(.1.str)
.L525:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#469
.L527:
	call	debug_assert_handler
.L524:
	ld.w	d15,[a10]12
.L537:
	lt.u	d4,d15,#8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#470
	call	debug_assert_handler
.L796:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#471
	call	debug_assert_handler
.L334:
	mov	d15,#0
.L538:
	jeq	d15,#0,.L62
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L63
.L62:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L63:
	mov	d15,#8
.L797:
	div	e0,d10,d15
.L798:
	sub	d10,d1
.L536:
	extr.u	d15,d10,#0,#16
.L539:
	st.w	[a10]8,d15
.L522:
	mov	d15,#8
.L540:
	div	e0,d8,d15
.L799:
	sub	d8,d1
.L534:
	extr.u	d15,d8,#0,#16
.L541:
	st.w	[a10],d15
.L523:
	mov	d15,#0
.L542:
	st.w	[a10]4,d15
.L543:
	j	.L64
.L65:
	ld.bu	d4,[a10]16
.L800:
	mov	d15,#8
.L544:
	div.u	e0,d0,d15
.L545:
	ld.w	d15,[a10]12
.L546:
	add	d15,d0
.L547:
	extr.u	d5,d15,#0,#8
	call	oled_set_coordinate
.L801:
	ld.w	d15,[a10]20
.L548:
	ld.w	d0,[a10]4
.L550:
	mul	d0,d15
.L551:
	ld.w	d15,[a10]8
.L549:
	div.u	e10,d0,d15
.L552:
	mov	d8,#0
.L553:
	j	.L66
.L67:
	mul	d15,d8,d9
.L554:
	ld.w	d0,[a10]
.L555:
	div.u	e0,d15,d0
.L556:
	mov	d15,#8
.L802:
	div.u	e12,d0,d15
.L557:
	mov	d14,#0
.L558:
	j	.L68
.L69:
	mov	d4,#0
.L560:
	mul	d0,d10,d9
.L803:
	mov	d15,#8
.L804:
	div.u	e0,d0,d15
.L805:
	addsc.a	a15,a12,d0,#0
.L806:
	addsc.a	a15,a15,d12,#0
.L807:
	ld.bu	d0,[a15]
.L808:
	mov	d15,#128
.L809:
	mov	d1,d14
.L562:
	rsub	d1,#0
.L559:
	sha	d15,d15,d1
.L810:
	and	d0,d15
.L811:
	jeq	d0,#0,.L70
.L812:
	or	d4,d4,#1
.L70:
	mov	d15,#8
.L813:
	div	e0,d9,d15
.L563:
	mul	d0,d0,#1
.L814:
	mul	d1,d10,d9
.L815:
	mov	d15,#8
.L816:
	div.u	e2,d1,d15
.L817:
	addsc.a	a15,a12,d2,#0
.L818:
	addsc.a	a15,a15,d12,#0
.L819:
	addsc.a	a15,a15,d0,#0
.L820:
	ld.bu	d0,[a15]
.L821:
	mov	d15,#128
.L564:
	mov	d1,d14
.L565:
	rsub	d1,#0
	sha	d15,d15,d1
.L822:
	and	d0,d15
.L823:
	jeq	d0,#0,.L71
.L824:
	or	d4,d4,#2
.L71:
	mov	d15,#8
.L825:
	div	e0,d9,d15
.L566:
	mul	d0,d0,#2
.L826:
	mul	d1,d10,d9
.L827:
	mov	d15,#8
.L828:
	div.u	e2,d1,d15
.L829:
	addsc.a	a15,a12,d2,#0
.L830:
	addsc.a	a15,a15,d12,#0
.L831:
	addsc.a	a15,a15,d0,#0
.L832:
	ld.bu	d0,[a15]
.L833:
	mov	d15,#128
.L567:
	mov	d1,d14
.L568:
	rsub	d1,#0
	sha	d15,d15,d1
.L834:
	and	d0,d15
.L835:
	jeq	d0,#0,.L72
.L836:
	or	d4,d4,#4
.L72:
	mov	d15,#8
.L837:
	div	e0,d9,d15
.L569:
	mul	d0,d0,#3
.L838:
	mul	d1,d10,d9
.L839:
	mov	d15,#8
.L840:
	div.u	e2,d1,d15
.L841:
	addsc.a	a15,a12,d2,#0
.L842:
	addsc.a	a15,a15,d12,#0
.L843:
	addsc.a	a15,a15,d0,#0
.L844:
	ld.bu	d0,[a15]
.L845:
	mov	d15,#128
.L570:
	mov	d1,d14
.L571:
	rsub	d1,#0
	sha	d15,d15,d1
.L846:
	and	d0,d15
.L847:
	jeq	d0,#0,.L73
.L848:
	or	d4,d4,#8
.L73:
	mov	d15,#8
.L849:
	div	e0,d9,d15
.L572:
	mul	d0,d0,#4
.L850:
	mul	d1,d10,d9
.L851:
	mov	d15,#8
.L852:
	div.u	e2,d1,d15
.L853:
	addsc.a	a15,a12,d2,#0
.L854:
	addsc.a	a15,a15,d12,#0
.L855:
	addsc.a	a15,a15,d0,#0
.L856:
	ld.bu	d0,[a15]
.L857:
	mov	d15,#128
.L573:
	mov	d1,d14
.L574:
	rsub	d1,#0
	sha	d15,d15,d1
.L858:
	and	d0,d15
.L859:
	jeq	d0,#0,.L74
.L860:
	or	d4,d4,#16
.L74:
	mov	d15,#8
.L861:
	div	e0,d9,d15
.L575:
	mul	d0,d0,#5
.L862:
	mul	d1,d10,d9
.L863:
	mov	d15,#8
.L864:
	div.u	e2,d1,d15
.L865:
	addsc.a	a15,a12,d2,#0
.L866:
	addsc.a	a15,a15,d12,#0
.L867:
	addsc.a	a15,a15,d0,#0
.L868:
	ld.bu	d0,[a15]
.L869:
	mov	d15,#128
.L576:
	mov	d1,d14
.L577:
	rsub	d1,#0
	sha	d15,d15,d1
.L870:
	and	d0,d15
.L871:
	jeq	d0,#0,.L75
.L872:
	or	d4,d4,#32
.L75:
	mov	d15,#8
.L873:
	div	e0,d9,d15
.L578:
	mul	d0,d0,#6
.L874:
	mul	d1,d10,d9
.L875:
	mov	d15,#8
.L876:
	div.u	e2,d1,d15
.L877:
	addsc.a	a15,a12,d2,#0
.L878:
	addsc.a	a15,a15,d12,#0
.L879:
	addsc.a	a15,a15,d0,#0
.L880:
	ld.bu	d0,[a15]
.L881:
	mov	d15,#128
.L579:
	mov	d1,d14
.L580:
	rsub	d1,#0
	sha	d15,d15,d1
.L882:
	and	d0,d15
.L883:
	jeq	d0,#0,.L76
.L884:
	or	d4,d4,#64
.L76:
	mov	d15,#8
.L885:
	div	e0,d9,d15
.L581:
	mul	d0,d0,#7
.L886:
	mul	d1,d10,d9
.L887:
	mov	d15,#8
.L888:
	div.u	e2,d1,d15
.L889:
	addsc.a	a15,a12,d2,#0
.L890:
	addsc.a	a15,a15,d12,#0
.L891:
	addsc.a	a15,a15,d0,#0
.L892:
	ld.bu	d0,[a15]
.L893:
	mov	d15,#128
.L582:
	mov	d1,d14
.L583:
	rsub	d1,#0
	sha	d15,d15,d1
.L894:
	and	d0,d15
.L895:
	jeq	d0,#0,.L77
.L896:
	or	d4,d4,#128
.L77:
	call	oled_write_data
.L561:
	add	d14,#1
.L68:
	jlt.u	d14,#8,.L69
.L897:
	add	d8,d8,#8
.L66:
	ld.w	d15,[a10]
.L584:
	jlt.u	d8,d15,.L67
.L898:
	ld.w	d15,[a10]4
.L585:
	add	d15,d15,#8
	st.w	[a10]4,d15
.L64:
	ld.w	d15,[a10]8
.L586:
	ld.w	d0,[a10]4
.L588:
	jlt.u	d0,d15,.L65
.L899:
	mov	d15,#1
.L587:
	jeq	d15,#0,.L78
	mov	d4,#482
	call	get_port
.L589:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L79
.L78:
	mov	d4,#482
	call	get_port
.L590:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L79:
	ret
.L325:
	
__oled_show_binary_image_function_end:
	.size	oled_show_binary_image,__oled_show_binary_image_function_end-oled_show_binary_image
.L215:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_gray_image',code,cluster('oled_show_gray_image')
	.sect	'.text.zf_device_oled.oled_show_gray_image'
	.align	2
	
	.global	oled_show_gray_image
; Function oled_show_gray_image
.L155:
oled_show_gray_image:	.type	func
	sub.a	a10,#16
.L591:
	st.w	[a10]8,d4
.L597:
	st.w	[a10]4,d5
.L598:
	mov.aa	a12,a4
.L599:
	mov	d8,d6
.L600:
	st.w	[a10]12,d7
.L601:
	ld.hu	d10,[a10]16
.L602:
	ld.hu	d9,[a10]20
.L603:
	ld.bu	d11,[a10]24
.L605:
	ld.w	d15,[a10]8
.L595:
	lt.u	d4,d15,#128
	movh.a	a4,#@his(.1.str)
.L594:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#551
.L596:
	call	debug_assert_handler
.L593:
	ld.w	d15,[a10]4
.L606:
	lt.u	d4,d15,#8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#552
	call	debug_assert_handler
.L904:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#553
	call	debug_assert_handler
.L350:
	mov	d15,#0
.L607:
	jeq	d15,#0,.L80
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L81
.L80:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L81:
	mov	d15,#8
.L905:
	div	e0,d9,d15
.L906:
	sub	d9,d1
.L604:
	extr.u	d15,d9,#0,#16
.L608:
	st.w	[a10],d15
.L592:
	mov	d9,#0
.L610:
	j	.L82
.L83:
	ld.bu	d4,[a10]8
.L907:
	mov	d15,#8
.L609:
	div	e0,d9,d15
.L908:
	ld.w	d15,[a10]4
.L611:
	add	d15,d0
.L612:
	extr.u	d5,d15,#0,#8
	call	oled_set_coordinate
.L909:
	ld.w	d15,[a10]12
.L613:
	mul	d15,d9
.L614:
	ld.w	d0,[a10]
.L615:
	div	e12,d15,d0
.L616:
	mov	d14,#0
.L617:
	j	.L84
.L85:
	mul	d15,d14,d8
.L910:
	div	e0,d15,d10
.L619:
	mov	d4,#0
.L621:
	mul	d15,d12,d8
.L911:
	addsc.a	a15,a12,d15,#0
.L912:
	addsc.a	a15,a15,d0,#0
.L913:
	ld.bu	d15,[a15]
.L914:
	jge.u	d11,d15,.L86
.L915:
	or	d4,d4,#1
.L86:
	mul	d2,d8,#1
.L916:
	mul	d15,d12,d8
.L917:
	addsc.a	a15,a12,d15,#0
.L918:
	addsc.a	a15,a15,d0,#0
.L919:
	addsc.a	a15,a15,d2,#0
.L920:
	ld.bu	d15,[a15]
.L921:
	jge.u	d11,d15,.L87
.L922:
	or	d4,d4,#2
.L87:
	mul	d2,d8,#2
.L923:
	mul	d15,d12,d8
.L924:
	addsc.a	a15,a12,d15,#0
.L925:
	addsc.a	a15,a15,d0,#0
.L926:
	addsc.a	a15,a15,d2,#0
.L927:
	ld.bu	d15,[a15]
.L928:
	jge.u	d11,d15,.L88
.L929:
	or	d4,d4,#4
.L88:
	mul	d2,d8,#3
.L930:
	mul	d15,d12,d8
.L931:
	addsc.a	a15,a12,d15,#0
.L932:
	addsc.a	a15,a15,d0,#0
.L933:
	addsc.a	a15,a15,d2,#0
.L934:
	ld.bu	d15,[a15]
.L935:
	jge.u	d11,d15,.L89
.L936:
	or	d4,d4,#8
.L89:
	mul	d2,d8,#4
.L937:
	mul	d15,d12,d8
.L938:
	addsc.a	a15,a12,d15,#0
.L939:
	addsc.a	a15,a15,d0,#0
.L940:
	addsc.a	a15,a15,d2,#0
.L941:
	ld.bu	d15,[a15]
.L942:
	jge.u	d11,d15,.L90
.L943:
	or	d4,d4,#16
.L90:
	mul	d2,d8,#5
.L944:
	mul	d15,d12,d8
.L945:
	addsc.a	a15,a12,d15,#0
.L946:
	addsc.a	a15,a15,d0,#0
.L947:
	addsc.a	a15,a15,d2,#0
.L948:
	ld.bu	d15,[a15]
.L949:
	jge.u	d11,d15,.L91
.L950:
	or	d4,d4,#32
.L91:
	mul	d2,d8,#6
.L951:
	mul	d15,d12,d8
.L952:
	addsc.a	a15,a12,d15,#0
.L953:
	addsc.a	a15,a15,d0,#0
.L954:
	addsc.a	a15,a15,d2,#0
.L955:
	ld.bu	d15,[a15]
.L956:
	jge.u	d11,d15,.L92
.L957:
	or	d4,d4,#64
.L92:
	mul	d2,d8,#7
.L958:
	mul	d15,d12,d8
.L959:
	addsc.a	a15,a12,d15,#0
.L960:
	addsc.a	a15,a15,d0,#0
.L961:
	addsc.a	a15,a15,d2,#0
.L962:
	ld.bu	d15,[a15]
.L963:
	jge.u	d11,d15,.L93
.L964:
	or	d4,d4,#128
.L93:
	call	oled_write_data
.L620:
	add	d14,#1
.L618:
	extr	d14,d14,#0,#16
.L84:
	jlt	d14,d10,.L85
.L965:
	add	d15,d9,#8
	extr	d9,d15,#0,#16
.L82:
	ld.w	d15,[a10]
.L622:
	jlt	d9,d15,.L83
.L966:
	mov	d15,#1
.L623:
	jeq	d15,#0,.L94
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L95
.L94:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L95:
	ret
.L341:
	
__oled_show_gray_image_function_end:
	.size	oled_show_gray_image,__oled_show_gray_image_function_end-oled_show_gray_image
.L220:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_wave',code,cluster('oled_show_wave')
	.sect	'.text.zf_device_oled.oled_show_wave'
	.align	2
	
	.global	oled_show_wave
; Function oled_show_wave
.L157:
oled_show_wave:	.type	func
	sub.a	a10,#8
.L624:
	mov	e8,d5,d4
	mov.aa	a15,a4
.L629:
	st.w	[a10],d6
.L630:
	mov	d10,d7
.L631:
	ld.hu	d11,[a10]8
.L632:
	ld.hu	d12,[a10]12
.L633:
	lt.u	d4,d8,#128
.L627:
	movh.a	a4,#@his(.1.str)
.L626:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#625
.L628:
	call	debug_assert_handler
.L625:
	lt.u	d4,d9,#8
.L634:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#626
	call	debug_assert_handler
.L971:
	mov.a	a2,#0
	ne.a	d4,a2,a15
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#627
	call	debug_assert_handler
.L366:
	mov	d15,#0
	jeq	d15,#0,.L96
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L97
.L96:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L97:
	mov	d13,#0
.L635:
	j	.L98
.L99:
	extr.u	d4,d8,#0,#8
.L637:
	mov	d15,#8
.L972:
	div.u	e0,d13,d15
.L638:
	add	d15,d9,d0
.L639:
	extr.u	d5,d15,#0,#8
	call	oled_set_coordinate
.L973:
	mov	d15,#0
.L640:
	j	.L100
.L101:
	mov	d4,#0
	call	oled_write_data
.L974:
	add	d15,#1
.L100:
	jlt.u	d15,d11,.L101
.L975:
	add	d13,d13,#8
.L98:
	jlt.u	d13,d12,.L99
.L976:
	mov	d13,#0
.L636:
	j	.L102
.L103:
	ld.w	d0,[a10]
.L641:
	mul	d0,d13
.L642:
	div.u	e0,d0,d11
.L643:
	mul	d15,d0,#2
	addsc.a	a2,a15,d15,#0
.L977:
	ld.hu	d0,[a2]0
.L644:
	add	d15,d12,#-1
.L978:
	mul	d0,d15
.L979:
	div	e0,d0,d10
.L645:
	add	d15,d12,#-1
.L980:
	sub	d15,d0
.L981:
	extr.u	d14,d15,#0,#8
.L647:
	add	d15,d13,d8
.L649:
	extr.u	d4,d15,#0,#8
.L982:
	mov	d15,#8
.L983:
	div	e0,d14,d15
.L646:
	add	d0,d9
.L650:
	extr.u	d5,d0,#0,#8
	call	oled_set_coordinate
.L984:
	mov	d0,#1
.L985:
	mov	d15,#8
.L986:
	div	e2,d14,d15
.L987:
	sha	d0,d0,d3
	extr.u	d4,d0,#0,#8
.L648:
	call	oled_write_data
.L651:
	add	d13,#1
.L102:
	jlt.u	d13,d11,.L103
.L988:
	mov	d15,#1
	jeq	d15,#0,.L104
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L105
.L104:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L105:
	ret
.L357:
	
__oled_show_wave_function_end:
	.size	oled_show_wave,__oled_show_wave_function_end-oled_show_wave
.L225:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_show_chinese',code,cluster('oled_show_chinese')
	.sect	'.text.zf_device_oled.oled_show_chinese'
	.align	2
	
	.global	oled_show_chinese
; Function oled_show_chinese
.L159:
oled_show_chinese:	.type	func
	mov	e8,d5,d4
	mov	d10,d6
.L656:
	mov.aa	a15,a4
.L657:
	mov	d12,d7
.L658:
	lt.u	d4,d8,#128
.L654:
	movh.a	a4,#@his(.1.str)
.L652:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#674
.L655:
	call	debug_assert_handler
.L653:
	lt.u	d4,d9,#8
.L659:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#675
	call	debug_assert_handler
.L993:
	mov.a	a2,#0
	ne.a	d4,a2,a15
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#676
	call	debug_assert_handler
.L379:
	mov	d15,#0
	jeq	d15,#0,.L106
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L107
.L106:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L107:
	mov	d11,#0
.L660:
	j	.L108
.L109:
	mov	d13,#0
.L662:
	j	.L110
.L111:
	madd	d0,d8,d11,d10
.L664:
	extr.u	d4,d0,#0,#8
.L665:
	add	d15,d9,d13
.L666:
	extr.u	d5,d15,#0,#8
	call	oled_set_coordinate
.L994:
	mov	d14,#0
.L667:
	j	.L112
.L113:
	ld.bu	d4,[a15]
	call	oled_write_data
.L995:
	add.a	a15,#1
.L996:
	add	d14,#1
.L112:
	mov	d15,#16
.L997:
	jlt	d14,d15,.L113
.L998:
	add	d13,#1
.L663:
	extr	d13,d13,#0,#16
.L110:
	mov	d15,#8
.L999:
	div	e0,d10,d15
.L1000:
	jlt	d13,d0,.L111
.L1001:
	add	d11,#1
.L661:
	extr	d11,d11,#0,#16
.L108:
	jlt	d11,d12,.L109
.L1002:
	mov	d15,#1
	jeq	d15,#0,.L114
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L115
.L114:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L115:
	ret
.L373:
	
__oled_show_chinese_function_end:
	.size	oled_show_chinese,__oled_show_chinese_function_end-oled_show_chinese
.L230:
	; End of function
	
	.sdecl	'.text.zf_device_oled.oled_init',code,cluster('oled_init')
	.sect	'.text.zf_device_oled.oled_init'
	.align	2
	
	.global	oled_init
; Function oled_init
.L161:
oled_init:	.type	func
	sub.a	a10,#16
.L668:
	mov	d15,#211
	st.h	[a10],d15
.L1007:
	mov	d15,#217
	st.h	[a10]4,d15
.L1008:
	mov	d15,#403
	st.h	[a10]8,d15
.L1009:
	mov	d4,#2
.L1010:
	mov	d5,#0
.L1011:
	mov.u	d6,#50048
	addih	d6,d6,#457
.L1012:
	mov	d7,#206
	call	spi_init
.L1013:
	mov	d4,#481
.L1014:
	mov	d5,#1
.L1015:
	mov	d6,#1
.L1016:
	mov	d7,#3
	call	gpio_init
.L1017:
	mov	d4,#480
.L1018:
	mov	d5,#1
.L1019:
	mov	d6,#1
.L1020:
	mov	d7,#3
	call	gpio_init
.L1021:
	mov	d4,#482
.L1022:
	mov	d5,#1
.L1023:
	mov	d6,#1
.L1024:
	mov	d7,#3
	call	gpio_init
.L1025:
	movh.a	a15,#@his(oled_display_dir)
	lea	a15,[a15]@los(oled_display_dir)
	ld.bu	d4,[a15]
	call	oled_set_dir
.L1026:
	mov	d15,#0
	jeq	d15,#0,.L116
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L117
.L116:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L117:
	mov	d15,#0
	jeq	d15,#0,.L118
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L119
.L118:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L119:
	mov	d4,#50
	call	system_delay_ms
.L1027:
	mov	d15,#1
	jeq	d15,#0,.L120
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L121
.L120:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L121:
	mov	d4,#174
	call	oled_write_command
.L1028:
	mov	d4,#0
	call	oled_write_command
.L1029:
	mov	d4,#16
	call	oled_write_command
.L1030:
	mov	d4,#64
	call	oled_write_command
.L1031:
	mov	d4,#129
	call	oled_write_command
.L1032:
	mov	d4,#127
	call	oled_write_command
.L1033:
	movh.a	a15,#@his(oled_display_dir)
	lea	a15,[a15]@los(oled_display_dir)
	ld.bu	d15,[a15]
.L1034:
	jne	d15,#0,.L122
.L1035:
	mov	d4,#161
	call	oled_write_command
.L1036:
	mov	d4,#200
	call	oled_write_command
.L1037:
	j	.L123
.L122:
	mov	d4,#160
	call	oled_write_command
.L1038:
	mov	d4,#192
	call	oled_write_command
.L123:
	mov	d4,#166
	call	oled_write_command
.L1039:
	mov	d4,#168
	call	oled_write_command
.L1040:
	mov	d4,#63
	call	oled_write_command
.L1041:
	mov	d4,#211
	call	oled_write_command
.L1042:
	mov	d4,#0
	call	oled_write_command
.L1043:
	mov	d4,#213
	call	oled_write_command
.L1044:
	mov	d4,#128
	call	oled_write_command
.L1045:
	mov	d4,#217
	call	oled_write_command
.L1046:
	mov	d4,#241
	call	oled_write_command
.L1047:
	mov	d4,#218
	call	oled_write_command
.L1048:
	mov	d4,#18
	call	oled_write_command
.L1049:
	mov	d4,#219
	call	oled_write_command
.L1050:
	mov	d4,#64
	call	oled_write_command
.L1051:
	mov	d4,#32
	call	oled_write_command
.L1052:
	mov	d4,#2
	call	oled_write_command
.L1053:
	mov	d4,#141
	call	oled_write_command
.L1054:
	mov	d4,#20
	call	oled_write_command
.L1055:
	mov	d4,#164
	call	oled_write_command
.L1056:
	mov	d4,#166
	call	oled_write_command
.L1057:
	mov	d4,#175
	call	oled_write_command
.L1058:
	mov	d15,#1
	jeq	d15,#0,.L124
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L125
.L124:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L125:
	call	oled_clear
.L1059:
	mov	d4,#0
.L1060:
	mov	d5,#0
	call	oled_set_coordinate
.L1061:
	call	oled_debug_init
.L1062:
	ret
.L383:
	
__oled_init_function_end:
	.size	oled_init,__oled_init_function_end-oled_init
.L235:
	; End of function
	
	.sdecl	'.data.zf_device_oled.oled_display_dir',data,cluster('oled_display_dir')
	.sect	'.data.zf_device_oled.oled_display_dir'
oled_display_dir:	.type	object
	.size	oled_display_dir,1
	.space	1
	.sdecl	'.data.zf_device_oled.oled_display_font',data,cluster('oled_display_font')
	.sect	'.data.zf_device_oled.oled_display_font'
oled_display_font:	.type	object
	.size	oled_display_font,1
	.space	1
	.sdecl	'.rodata.zf_device_oled..1.str',data,rom
	.sect	'.rodata.zf_device_oled..1.str'
.1.str:	.type	object
	.size	.1.str,40
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,111,108,101
	.byte	100,46,99
	.space	1
	.calls	'oled_show_float','__d_mul'
	.calls	'oled_show_float','__d_dtoi'
	.calls	'oled_show_float','__d_itod'
	.calls	'oled_show_float','__d_sub'
	.calls	'__INDIRECT__','oled_clear'
	.calls	'__INDIRECT__','oled_show_string'
	.calls	'oled_write_data','get_port'
	.calls	'oled_write_data','spi_write_8bit'
	.calls	'oled_write_command','get_port'
	.calls	'oled_write_command','spi_write_8bit'
	.calls	'oled_set_coordinate','debug_assert_handler'
	.calls	'oled_set_coordinate','oled_write_command'
	.calls	'oled_debug_init','debug_output_struct_init'
	.calls	'oled_debug_init','debug_output_init'
	.calls	'oled_clear','get_port'
	.calls	'oled_clear','oled_write_command'
	.calls	'oled_clear','oled_write_data'
	.calls	'oled_full','get_port'
	.calls	'oled_full','oled_write_command'
	.calls	'oled_full','oled_write_data'
	.calls	'oled_draw_point','debug_assert_handler'
	.calls	'oled_draw_point','get_port'
	.calls	'oled_draw_point','oled_set_coordinate'
	.calls	'oled_draw_point','oled_write_command'
	.calls	'oled_draw_point','oled_write_data'
	.calls	'oled_show_string','debug_assert_handler'
	.calls	'oled_show_string','get_port'
	.calls	'oled_show_string','oled_set_coordinate'
	.calls	'oled_show_string','oled_write_data'
	.calls	'oled_show_int','debug_assert_handler'
	.calls	'oled_show_int','memset'
	.calls	'oled_show_int','func_int_to_str'
	.calls	'oled_show_int','oled_show_string'
	.calls	'oled_show_uint','debug_assert_handler'
	.calls	'oled_show_uint','memset'
	.calls	'oled_show_uint','func_uint_to_str'
	.calls	'oled_show_uint','oled_show_string'
	.calls	'oled_show_float','debug_assert_handler'
	.calls	'oled_show_float','memset'
	.calls	'oled_show_float','func_double_to_str'
	.calls	'oled_show_float','oled_show_string'
	.calls	'oled_show_binary_image','debug_assert_handler'
	.calls	'oled_show_binary_image','get_port'
	.calls	'oled_show_binary_image','oled_set_coordinate'
	.calls	'oled_show_binary_image','oled_write_data'
	.calls	'oled_show_gray_image','debug_assert_handler'
	.calls	'oled_show_gray_image','get_port'
	.calls	'oled_show_gray_image','oled_set_coordinate'
	.calls	'oled_show_gray_image','oled_write_data'
	.calls	'oled_show_wave','debug_assert_handler'
	.calls	'oled_show_wave','get_port'
	.calls	'oled_show_wave','oled_set_coordinate'
	.calls	'oled_show_wave','oled_write_data'
	.calls	'oled_show_chinese','debug_assert_handler'
	.calls	'oled_show_chinese','get_port'
	.calls	'oled_show_chinese','oled_set_coordinate'
	.calls	'oled_show_chinese','oled_write_data'
	.calls	'oled_init','spi_init'
	.calls	'oled_init','gpio_init'
	.calls	'oled_init','oled_set_dir'
	.calls	'oled_init','get_port'
	.calls	'oled_init','system_delay_ms'
	.calls	'oled_init','oled_write_command'
	.calls	'oled_init','oled_clear'
	.calls	'oled_init','oled_set_coordinate'
	.calls	'oled_init','oled_debug_init'
	.calls	'oled_write_data','',0
	.calls	'oled_write_command','',0
	.calls	'oled_set_coordinate','',0
	.calls	'oled_debug_init','',24
	.calls	'oled_clear','',0
	.calls	'oled_full','',0
	.calls	'oled_set_dir','',0
	.calls	'oled_set_font','',0
	.calls	'oled_draw_point','',0
	.calls	'oled_show_string','',0
	.calls	'oled_show_int','',16
	.calls	'oled_show_uint','',16
	.calls	'oled_show_float','',24
	.calls	'oled_show_binary_image','',24
	.calls	'oled_show_gray_image','',16
	.calls	'oled_show_wave','',8
	.calls	'oled_show_chinese','',0
	.extern	memset
	.extern	debug_assert_handler
	.extern	debug_output_struct_init
	.extern	debug_output_init
	.extern	ascii_font_8x16
	.extern	ascii_font_6x8
	.extern	func_int_to_str
	.extern	func_uint_to_str
	.extern	func_double_to_str
	.extern	system_delay_ms
	.extern	get_port
	.extern	gpio_init
	.extern	spi_write_8bit
	.extern	spi_init
	.extern	__d_mul
	.extern	__d_dtoi
	.extern	__d_itod
	.extern	__d_sub
	.extern	__INDIRECT__
	.calls	'oled_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L163:
	.word	41667
	.half	3
	.word	.L164
	.byte	4
.L162:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L165
	.byte	2,1,1,3
	.word	202
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	205
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	250
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	262
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	342
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	316
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	348
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	348
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	316
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L296:
	.byte	7
	.byte	'int',0,4,5
.L261:
	.byte	7
	.byte	'unsigned char',0,1,8
.L276:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	457
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	812
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1383
	.byte	4,2,35,0,0,14,4
	.word	457
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	457
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	457
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	457
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	457
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	457
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	457
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	457
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	457
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	457
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	457
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1726
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	457
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	457
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	457
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	457
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	457
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1941
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	457
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	457
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	457
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	457
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	457
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2158
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2378
	.byte	4,2,35,0,0,14,24
	.word	457
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	457
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	457
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	457
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	457
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	457
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	457
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	457
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	457
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	457
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	457
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	457
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	457
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	457
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	457
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3005
	.byte	4,2,35,0,0,14,8
	.word	457
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3330
	.byte	4,2,35,0,0,14,12
	.word	457
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3670
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	434
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4036
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	434
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4322
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	434
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4469
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	434
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4638
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4810
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	474
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4985
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	434
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5159
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	434
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	457
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5333
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	434
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	457
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5509
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5665
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	457
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5998
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6346
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	457
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	434
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6470
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6554
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	457
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6734
	.byte	4,2,35,0,0,14,76
	.word	457
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	434
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	457
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7074
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	772
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1343
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1462
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1502
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1686
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1901
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2118
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2338
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1502
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2652
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2692
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2965
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3281
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3321
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3621
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3661
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	3996
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4282
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3321
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4429
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4598
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4770
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4945
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5119
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5293
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5469
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5625
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5958
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6306
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3321
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6430
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6679
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6938
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6978
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7034
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7601
	.byte	4,3,35,252,1,0,16
	.word	7641
	.byte	3
	.word	8244
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8249
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	457
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8254
	.byte	6,0,19
	.byte	'memset',0,5,56,17
	.word	348
	.byte	1,1,1,1,20,5,56,33
	.word	348
	.byte	20,5,56,36
	.word	450
	.byte	20,5,56,41
	.word	434
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	8479
	.byte	21
	.byte	'debug_assert_handler',0,6,112,9,1,1,1,1,5
	.byte	'pass',0,6,112,47
	.word	457
	.byte	5
	.byte	'file',0,6,112,59
	.word	8487
	.byte	5
	.byte	'line',0,6,112,69
	.word	450
	.byte	0,22
	.word	8479
.L284:
	.byte	3
	.word	8561
	.byte	23,1,1,24
	.word	8566
	.byte	0,3
	.word	8571
	.byte	23,1,1,24
	.word	474
	.byte	24
	.word	474
	.byte	24
	.word	8566
	.byte	0,3
	.word	8585
.L394:
	.byte	25,6,86,9,20,13
	.byte	'type_index',0
	.word	474
	.byte	2,2,35,0,13
	.byte	'display_x_max',0
	.word	474
	.byte	2,2,35,2,13
	.byte	'display_y_max',0
	.word	474
	.byte	2,2,35,4,13
	.byte	'font_x_size',0
	.word	457
	.byte	1,2,35,6,13
	.byte	'font_y_size',0
	.word	457
	.byte	1,2,35,7,13
	.byte	'output_uart',0
	.word	8580
	.byte	4,2,35,8,13
	.byte	'output_screen',0
	.word	8604
	.byte	4,2,35,12,13
	.byte	'output_screen_clear',0
	.word	205
	.byte	4,2,35,16,0,3
	.word	8609
	.byte	21
	.byte	'debug_output_struct_init',0,6,114,9,1,1,1,1,5
	.byte	'info',0,6,114,62
	.word	8796
	.byte	0,21
	.byte	'debug_output_init',0,6,115,9,1,1,1,1,5
	.byte	'info',0,6,115,62
	.word	8796
	.byte	0,21
	.byte	'func_int_to_str',0,7,80,13,1,1,1,1,5
	.byte	'str',0,7,80,56
	.word	8487
	.byte	5
	.byte	'number',0,7,80,67
	.word	450
	.byte	0
.L308:
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'func_uint_to_str',0,7,82,13,1,1,1,1,5
	.byte	'str',0,7,82,56
	.word	8487
	.byte	5
	.byte	'number',0,7,82,68
	.word	8940
	.byte	0
.L320:
	.byte	7
	.byte	'double',0,8,4,21
	.byte	'func_double_to_str',0,7,86,13,1,1,1,1,5
	.byte	'str',0,7,86,56
	.word	8487
	.byte	5
	.byte	'number',0,7,86,68
	.word	9014
	.byte	5
	.byte	'point_bit',0,7,86,82
	.word	457
	.byte	0,21
	.byte	'system_delay_ms',0,8,46,9,1,1,1,1,5
	.byte	'time',0,8,46,45
	.word	8940
	.byte	0,26
	.word	210
	.byte	27
	.word	236
	.byte	6,0,26
	.word	271
	.byte	27
	.word	303
	.byte	6,0,26
	.word	353
	.byte	27
	.word	372
	.byte	6,0,26
	.word	388
	.byte	27
	.word	403
	.byte	27
	.word	417
	.byte	6,0,26
	.word	8357
	.byte	27
	.word	8385
	.byte	27
	.word	8399
	.byte	27
	.word	8417
	.byte	6,0,17,9,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,19
	.byte	'get_port',0,9,114,13
	.word	8249
	.byte	1,1,1,1,5
	.byte	'pin',0,9,114,56
	.word	9210
	.byte	0,17,9,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,9,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,21
	.byte	'gpio_init',0,9,143,1,7,1,1,1,1,5
	.byte	'pin',0,9,143,1,40
	.word	9210
	.byte	5
	.byte	'dir',0,9,143,1,59
	.word	11184
	.byte	5
	.byte	'dat',0,9,143,1,70
	.word	457
	.byte	5
	.byte	'pinconf',0,9,143,1,90
	.word	11202
	.byte	0,17,10,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,22
	.word	457
	.byte	21
	.byte	'spi_write_8bit',0,10,143,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,143,1,61
	.word	11365
	.byte	5
	.byte	'data',0,10,143,1,80
	.word	11403
	.byte	0,17,10,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,10,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,10,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,10,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,10,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,21
	.byte	'spi_init',0,10,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,170,1,61
	.word	11365
	.byte	5
	.byte	'mode',0,10,170,1,82
	.word	11462
	.byte	5
	.byte	'baud',0,10,170,1,95
	.word	8940
	.byte	5
	.byte	'sck_pin',0,10,170,1,118
	.word	11516
	.byte	5
	.byte	'mosi_pin',0,10,170,1,145,1
	.word	11789
	.byte	5
	.byte	'miso_pin',0,10,170,1,173,1
	.word	12043
	.byte	5
	.byte	'cs_pin',0,10,170,1,199,1
	.word	12297
	.byte	0
.L265:
	.byte	22
	.word	457
.L270:
	.byte	17,11,90,9,1,18
	.byte	'OLED_PORTAIT',0,0,18
	.byte	'OLED_PORTAIT_180',0,1,0
.L273:
	.byte	17,11,96,9,1,18
	.byte	'OLED_6X8_FONT',0,0,18
	.byte	'OLED_8X16_FONT',0,1,18
	.byte	'OLED_16X16_FONT',0,2,0
.L279:
	.byte	22
	.word	457
.L292:
	.byte	22
	.word	450
.L299:
	.byte	14,12
	.word	8479
	.byte	15,11,0
.L304:
	.byte	22
	.word	8940
.L315:
	.byte	22
	.word	9014
.L323:
	.byte	14,17
	.word	8479
	.byte	15,16,0,22
	.word	457
.L328:
	.byte	3
	.word	13409
.L351:
	.byte	7
	.byte	'short int',0,2,5,22
	.word	474
.L360:
	.byte	3
	.word	13432
.L385:
	.byte	22
	.word	457
.L388:
	.byte	22
	.word	457
	.byte	28
	.byte	'__INDIRECT__',0,12,1,1,1,1,1,29
	.byte	'__wchar_t',0,12,1,1
	.word	13419
	.byte	29
	.byte	'__size_t',0,12,1,1
	.word	434
	.byte	29
	.byte	'__ptrdiff_t',0,12,1,1
	.word	450
	.byte	30,1,3
	.word	13527
	.byte	29
	.byte	'__codeptr',0,12,1,1
	.word	13529
	.byte	29
	.byte	'__intptr_t',0,12,1,1
	.word	450
	.byte	29
	.byte	'__uintptr_t',0,12,1,1
	.word	434
	.byte	29
	.byte	'size_t',0,13,31,25
	.word	434
	.byte	29
	.byte	'_iob_flag_t',0,13,82,25
	.word	474
	.byte	29
	.byte	'boolean',0,14,101,29
	.word	457
	.byte	29
	.byte	'uint8',0,14,105,29
	.word	457
	.byte	29
	.byte	'uint16',0,14,109,29
	.word	474
	.byte	29
	.byte	'uint32',0,14,113,29
	.word	8940
	.byte	29
	.byte	'uint64',0,14,118,29
	.word	316
	.byte	29
	.byte	'sint16',0,14,126,29
	.word	13419
	.byte	7
	.byte	'long int',0,4,5,29
	.byte	'sint32',0,14,131,1,29
	.word	13716
	.byte	7
	.byte	'long long int',0,8,5,29
	.byte	'sint64',0,14,138,1,29
	.word	13744
	.byte	29
	.byte	'float32',0,14,167,1,29
	.word	262
	.byte	29
	.byte	'pvoid',0,15,57,28
	.word	348
	.byte	29
	.byte	'Ifx_TickTime',0,15,79,28
	.word	13744
	.byte	7
	.byte	'char',0,1,6,29
	.byte	'int8',0,16,54,29
	.word	13829
	.byte	29
	.byte	'int16',0,16,55,29
	.word	13419
	.byte	29
	.byte	'int32',0,16,56,29
	.word	450
	.byte	29
	.byte	'int64',0,16,57,29
	.word	13744
	.byte	29
	.byte	'debug_output_struct',0,6,99,2
	.word	8609
	.byte	14,16
	.word	457
	.byte	15,15,0,31
	.word	13920
	.byte	32,0,22
	.word	13929
	.byte	33
	.byte	'ascii_font_8x16',0,17,61,25
	.word	13936
	.byte	1,1,14,6
	.word	457
	.byte	15,5,0,31
	.word	13967
	.byte	32,0,22
	.word	13976
	.byte	33
	.byte	'ascii_font_6x8',0,17,62,25
	.word	13983
	.byte	1,1,29
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7074
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6987
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3330
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1383
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2378
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1511
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2158
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1726
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1941
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6346
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6470
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6554
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6734
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4985
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5509
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5159
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5333
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	5998
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	812
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4322
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4810
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4469
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4638
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5665
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	496
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4036
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3670
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2701
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3005
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7601
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7034
	.byte	29
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3621
	.byte	29
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1462
	.byte	29
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2652
	.byte	29
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1686
	.byte	29
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2338
	.byte	29
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1901
	.byte	29
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2118
	.byte	29
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6430
	.byte	29
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6679
	.byte	29
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6938
	.byte	29
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6306
	.byte	29
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5119
	.byte	29
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5625
	.byte	29
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5293
	.byte	29
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5469
	.byte	29
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1343
	.byte	29
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5958
	.byte	29
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4429
	.byte	29
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4945
	.byte	29
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4598
	.byte	29
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4770
	.byte	29
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	772
	.byte	29
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4282
	.byte	29
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	3996
	.byte	29
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2965
	.byte	29
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3281
	.byte	16
	.word	7641
	.byte	29
	.byte	'Ifx_P',0,4,139,6,3
	.word	15331
	.byte	17,18,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,18,255,10,3
	.word	15351
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,19,45,16,4,11
	.byte	'EN0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,19,79,3
	.word	15473
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,19,82,16,4,11
	.byte	'reserved_0',0,4
	.word	434
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,19,85,3
	.word	16030
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,19,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	434
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,19,94,3
	.word	16107
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,19,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	457
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	457
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	457
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	457
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	457
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	457
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	457
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	457
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	457
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	457
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,19,111,3
	.word	16243
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,19,114,16,4,11
	.byte	'CANDIV',0,1
	.word	457
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	457
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	457
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	457
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	457
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	457
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	457
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	457
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,19,126,3
	.word	16523
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,19,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	457
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	434
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,19,135,1,3
	.word	16761
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,19,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	457
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	457
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	457
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	457
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	457
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,19,150,1,3
	.word	16889
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,19,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	457
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	457
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	457
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	457
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	457
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,19,165,1,3
	.word	17132
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,19,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	457
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	434
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,19,174,1,3
	.word	17367
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,19,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	457
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	434
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,19,181,1,3
	.word	17495
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,19,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	457
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	434
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,19,188,1,3
	.word	17595
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,19,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	457
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	457
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	457
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	457
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	457
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,19,202,1,3
	.word	17695
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,19,205,1,16,4,11
	.byte	'PWD',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	434
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,19,213,1,3
	.word	17903
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,19,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	457
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	474
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	457
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,19,225,1,3
	.word	18068
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,19,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	457
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,19,235,1,3
	.word	18251
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,19,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	457
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	457
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	434
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	457
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	457
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,19,129,2,3
	.word	18405
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,19,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	434
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,19,143,2,3
	.word	18769
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,19,146,2,16,4,11
	.byte	'POL',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	474
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	457
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	457
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	457
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	457
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,19,159,2,3
	.word	18980
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,19,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	474
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	434
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,19,167,2,3
	.word	19232
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,19,170,2,16,4,11
	.byte	'ARI',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,19,175,2,3
	.word	19350
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,19,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	434
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,19,185,2,3
	.word	19461
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,19,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	434
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,19,195,2,3
	.word	19624
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,19,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,19,205,2,3
	.word	19787
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,19,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,19,215,2,3
	.word	19945
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,19,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	457
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	457
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	457
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	457
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	457
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	457
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	457
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	457
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	457
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	474
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,19,232,2,3
	.word	20110
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,19,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	457
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	457
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	474
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	457
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,19,245,2,3
	.word	20439
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,19,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,19,255,2,3
	.word	20660
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,19,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	434
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,19,142,3,3
	.word	20823
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,19,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,19,152,3,3
	.word	21095
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,19,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,19,162,3,3
	.word	21248
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,19,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,19,172,3,3
	.word	21404
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,19,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,19,181,3,3
	.word	21566
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,19,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,19,191,3,3
	.word	21709
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,19,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,19,200,3,3
	.word	21874
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,19,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	457
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	457
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,19,211,3,3
	.word	22019
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,19,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	457
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,19,222,3,3
	.word	22200
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,19,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,19,232,3,3
	.word	22374
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,19,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	434
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,19,241,3,3
	.word	22534
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,19,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	434
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,19,130,4,3
	.word	22678
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,19,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,19,139,4,3
	.word	22952
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,19,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,19,149,4,3
	.word	23091
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,19,152,4,16,4,11
	.byte	'EN0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	457
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	474
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	457
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	457
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	457
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,19,163,4,3
	.word	23254
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,19,166,4,16,4,11
	.byte	'STEP',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	457
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	474
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	457
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,19,174,4,3
	.word	23472
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,19,177,4,16,4,11
	.byte	'FS0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,19,197,4,3
	.word	23635
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,19,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,19,205,4,3
	.word	23971
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,19,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	457
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,19,232,4,3
	.word	24078
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,19,235,4,16,4,11
	.byte	'P0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,19,240,4,3
	.word	24530
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,19,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	457
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	457
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,19,250,4,3
	.word	24629
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,19,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	474
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,19,131,5,3
	.word	24779
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,19,134,5,16,4,11
	.byte	'SEED',0,4
	.word	434
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	457
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,19,141,5,3
	.word	24928
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,19,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	434
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,19,149,5,3
	.word	25089
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,19,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	474
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,19,158,5,3
	.word	25219
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,19,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,19,166,5,3
	.word	25351
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,19,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	457
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	474
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,19,174,5,3
	.word	25466
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,19,177,5,16,4,11
	.byte	'PS0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	474
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	474
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,19,185,5,3
	.word	25577
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,19,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	457
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	457
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	457
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	457
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	457
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	457
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,19,209,5,3
	.word	25735
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,19,212,5,16,4,11
	.byte	'P0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,19,217,5,3
	.word	26147
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,19,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	474
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	457
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	457
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,19,233,5,3
	.word	26248
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,19,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	434
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,19,242,5,3
	.word	26515
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,19,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,19,250,5,3
	.word	26651
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,19,253,5,16,4,11
	.byte	'PD0',0,1
	.word	457
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	457
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	434
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,19,132,6,3
	.word	26762
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,19,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	434
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,19,146,6,3
	.word	26895
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,19,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	474
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	457
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	457
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	457
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	457
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,19,166,6,3
	.word	27098
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,19,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	457
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	457
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	457
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	474
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,19,177,6,3
	.word	27454
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,19,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,19,184,6,3
	.word	27632
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,19,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	474
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	457
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	457
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	457
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	457
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,19,204,6,3
	.word	27732
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,19,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	457
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	457
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	457
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	457
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	474
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,19,215,6,3
	.word	28102
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,19,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	434
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,19,227,6,3
	.word	28288
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,19,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	434
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,19,241,6,3
	.word	28486
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,19,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	457
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	457
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	434
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR_Bits',0,19,251,6,3
	.word	28719
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,19,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	457
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	457
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	457
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	457
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	457
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	457
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	457
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,19,153,7,3
	.word	28871
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,19,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	457
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	457
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	457
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	457
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,19,170,7,3
	.word	29438
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,19,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	457
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	457
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	457
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	457
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	457
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	457
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	457
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,19,187,7,3
	.word	29732
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,19,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	457
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	457
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	457
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	457
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	457
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	474
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	457
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	457
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,19,214,7,3
	.word	30010
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,19,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	474
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,19,230,7,3
	.word	30506
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,19,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	474
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	457
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	457
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	457
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,19,243,7,3
	.word	30819
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,19,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	457
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	457
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	457
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	457
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	457
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	457
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	457
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	457
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,19,129,8,3
	.word	31028
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,19,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	457
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	457
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	457
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	457
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	457
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	457
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	457
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,19,155,8,3
	.word	31239
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,19,158,8,16,4,11
	.byte	'HBT',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	434
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,19,162,8,3
	.word	31671
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,19,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	457
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	457
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	457
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	457
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	457
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	457
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	457
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	457
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	457
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	457
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	457
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,19,178,8,3
	.word	31767
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,19,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	434
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,19,186,8,3
	.word	32027
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,19,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	457
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	457
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	434
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,19,198,8,3
	.word	32152
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,19,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	434
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,19,208,8,3
	.word	32349
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,19,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	434
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,19,218,8,3
	.word	32502
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,19,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	434
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,19,228,8,3
	.word	32655
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,19,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	434
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,19,238,8,3
	.word	32808
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,19,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	32963
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32963
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32963
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32963
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,19,247,8,3
	.word	32979
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,19,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	457
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	457
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,19,134,9,3
	.word	33109
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,19,137,9,16,4,11
	.byte	'AE',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	457
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,19,150,9,3
	.word	33347
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,19,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	32963
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	32963
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	32963
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	32963
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,19,159,9,3
	.word	33570
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,19,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	457
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,19,175,9,3
	.word	33696
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,19,178,9,16,4,11
	.byte	'AE',0,1
	.word	457
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	457
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	457
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	457
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	457
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	457
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	457
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	457
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	457
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	474
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,19,191,9,3
	.word	33948
	.byte	12,19,199,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15473
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,19,204,9,3
	.word	34167
	.byte	12,19,207,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16030
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,19,212,9,3
	.word	34231
	.byte	12,19,215,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16107
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,19,220,9,3
	.word	34295
	.byte	12,19,223,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16243
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,19,228,9,3
	.word	34360
	.byte	12,19,231,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16523
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,19,236,9,3
	.word	34425
	.byte	12,19,239,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16761
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,19,244,9,3
	.word	34490
	.byte	12,19,247,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16889
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,19,252,9,3
	.word	34555
	.byte	12,19,255,9,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17132
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,19,132,10,3
	.word	34620
	.byte	12,19,135,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17367
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,19,140,10,3
	.word	34685
	.byte	12,19,143,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17495
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,19,148,10,3
	.word	34750
	.byte	12,19,151,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17595
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,19,156,10,3
	.word	34815
	.byte	12,19,159,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17695
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,19,164,10,3
	.word	34880
	.byte	12,19,167,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17903
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,19,172,10,3
	.word	34944
	.byte	12,19,175,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18068
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,19,180,10,3
	.word	35008
	.byte	12,19,183,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18251
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,19,188,10,3
	.word	35072
	.byte	12,19,191,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18405
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,19,196,10,3
	.word	35137
	.byte	12,19,199,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18769
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,19,204,10,3
	.word	35199
	.byte	12,19,207,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18980
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,19,212,10,3
	.word	35261
	.byte	12,19,215,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19232
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,19,220,10,3
	.word	35323
	.byte	12,19,223,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19350
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,19,228,10,3
	.word	35387
	.byte	12,19,231,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19461
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,19,236,10,3
	.word	35452
	.byte	12,19,239,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19624
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,19,244,10,3
	.word	35518
	.byte	12,19,247,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19787
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,19,252,10,3
	.word	35584
	.byte	12,19,255,10,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19945
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,19,132,11,3
	.word	35652
	.byte	12,19,135,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20110
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,19,140,11,3
	.word	35719
	.byte	12,19,143,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20439
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,19,148,11,3
	.word	35787
	.byte	12,19,151,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20660
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,19,156,11,3
	.word	35855
	.byte	12,19,159,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20823
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,19,164,11,3
	.word	35921
	.byte	12,19,167,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21095
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,19,172,11,3
	.word	35988
	.byte	12,19,175,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21248
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,19,180,11,3
	.word	36057
	.byte	12,19,183,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21404
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,19,188,11,3
	.word	36126
	.byte	12,19,191,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21566
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,19,196,11,3
	.word	36195
	.byte	12,19,199,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21709
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,19,204,11,3
	.word	36264
	.byte	12,19,207,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21874
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,19,212,11,3
	.word	36333
	.byte	12,19,215,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22019
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,19,220,11,3
	.word	36402
	.byte	12,19,223,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22200
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,19,228,11,3
	.word	36470
	.byte	12,19,231,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22374
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,19,236,11,3
	.word	36538
	.byte	12,19,239,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22534
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,19,244,11,3
	.word	36606
	.byte	12,19,247,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22678
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,19,252,11,3
	.word	36674
	.byte	12,19,255,11,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22952
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,19,132,12,3
	.word	36739
	.byte	12,19,135,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23091
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,19,140,12,3
	.word	36804
	.byte	12,19,143,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23254
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,19,148,12,3
	.word	36870
	.byte	12,19,151,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23472
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,19,156,12,3
	.word	36934
	.byte	12,19,159,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23635
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,19,164,12,3
	.word	36995
	.byte	12,19,167,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23971
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,19,172,12,3
	.word	37056
	.byte	12,19,175,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24078
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,19,180,12,3
	.word	37116
	.byte	12,19,183,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24530
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,19,188,12,3
	.word	37178
	.byte	12,19,191,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24629
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,19,196,12,3
	.word	37238
	.byte	12,19,199,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24779
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,19,204,12,3
	.word	37300
	.byte	12,19,207,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24928
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,19,212,12,3
	.word	37368
	.byte	12,19,215,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25089
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,19,220,12,3
	.word	37436
	.byte	12,19,223,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25219
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,19,228,12,3
	.word	37504
	.byte	12,19,231,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25351
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,19,236,12,3
	.word	37568
	.byte	12,19,239,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25466
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,19,244,12,3
	.word	37633
	.byte	12,19,247,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25577
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,19,252,12,3
	.word	37696
	.byte	12,19,255,12,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25735
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,19,132,13,3
	.word	37757
	.byte	12,19,135,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26147
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,19,140,13,3
	.word	37821
	.byte	12,19,143,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26248
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,19,148,13,3
	.word	37882
	.byte	12,19,151,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26515
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,19,156,13,3
	.word	37946
	.byte	12,19,159,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26651
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,19,164,13,3
	.word	38013
	.byte	12,19,167,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26762
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,19,172,13,3
	.word	38076
	.byte	12,19,175,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26895
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,19,180,13,3
	.word	38137
	.byte	12,19,183,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27098
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,19,188,13,3
	.word	38199
	.byte	12,19,191,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27454
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,19,196,13,3
	.word	38264
	.byte	12,19,199,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27632
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,19,204,13,3
	.word	38329
	.byte	12,19,207,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27732
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,19,212,13,3
	.word	38394
	.byte	12,19,215,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28102
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,19,220,13,3
	.word	38463
	.byte	12,19,223,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28288
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,19,228,13,3
	.word	38532
	.byte	12,19,231,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28486
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,19,236,13,3
	.word	38601
	.byte	12,19,239,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28719
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR',0,19,244,13,3
	.word	38666
	.byte	12,19,247,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28871
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,19,252,13,3
	.word	38729
	.byte	12,19,255,13,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29438
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,19,132,14,3
	.word	38794
	.byte	12,19,135,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29732
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,19,140,14,3
	.word	38859
	.byte	12,19,143,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30010
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,19,148,14,3
	.word	38924
	.byte	12,19,151,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30506
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,19,156,14,3
	.word	38990
	.byte	12,19,159,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31028
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,19,164,14,3
	.word	39059
	.byte	12,19,167,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30819
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,19,172,14,3
	.word	39123
	.byte	12,19,175,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31239
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,19,180,14,3
	.word	39188
	.byte	12,19,183,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31671
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,19,188,14,3
	.word	39253
	.byte	12,19,191,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31767
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,19,196,14,3
	.word	39318
	.byte	12,19,199,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32027
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,19,204,14,3
	.word	39382
	.byte	12,19,207,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32152
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,19,212,14,3
	.word	39448
	.byte	12,19,215,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32349
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,19,220,14,3
	.word	39512
	.byte	12,19,223,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32502
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,19,228,14,3
	.word	39577
	.byte	12,19,231,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32655
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,19,236,14,3
	.word	39642
	.byte	12,19,239,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32808
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,19,244,14,3
	.word	39707
	.byte	12,19,247,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32979
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,19,252,14,3
	.word	39773
	.byte	12,19,255,14,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33109
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,19,132,15,3
	.word	39842
	.byte	12,19,135,15,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33347
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTCPU_SR',0,19,140,15,3
	.word	39911
	.byte	12,19,143,15,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33570
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,19,148,15,3
	.word	39978
	.byte	12,19,151,15,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33696
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,19,156,15,3
	.word	40045
	.byte	12,19,159,15,9,4,13
	.byte	'U',0
	.word	434
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	450
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33948
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,19,164,15,3
	.word	40112
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,19,175,15,25,12,13
	.byte	'CON0',0
	.word	39773
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	39842
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	39911
	.byte	4,2,35,8,0,16
	.word	40177
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,19,180,15,3
	.word	40240
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,19,183,15,25,12,13
	.byte	'CON0',0
	.word	39978
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40045
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40112
	.byte	4,2,35,8,0,16
	.word	40269
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,19,188,15,3
	.word	40330
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,29
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	40357
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,29
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	40508
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,29
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	40752
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	40850
	.byte	29
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8254
	.byte	29
	.byte	'gpio_pin_enum',0,9,89,2
	.word	9210
	.byte	29
	.byte	'gpio_dir_enum',0,9,95,2
	.word	11184
	.byte	29
	.byte	'gpio_mode_enum',0,9,111,2
	.word	11202
	.byte	25,20,45,9,1,11
	.byte	'mode',0,1
	.word	457
	.byte	6,2,2,35,0,11
	.byte	'use_miso',0,1
	.word	457
	.byte	1,1,2,35,0,11
	.byte	'use_cs',0,1
	.word	457
	.byte	1,0,2,35,0,0,29
	.byte	'spi_config_info_struct',0,20,50,2
	.word	41382
	.byte	29
	.byte	'spi_index_enum',0,10,48,2
	.word	11365
	.byte	29
	.byte	'spi_mode_enum',0,10,56,2
	.word	11462
	.byte	29
	.byte	'spi_sck_pin_enum',0,10,67,2
	.word	11516
	.byte	29
	.byte	'spi_mosi_pin_enum',0,10,78,2
	.word	11789
	.byte	29
	.byte	'spi_miso_pin_enum',0,10,89,2
	.word	12043
	.byte	29
	.byte	'spi_cs_pin_enum',0,10,140,1,2
	.word	12297
	.byte	29
	.byte	'oled_dir_enum',0,11,94,2
	.word	13274
	.byte	29
	.byte	'oled_font_size_enum',0,11,101,2
	.word	13314
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L164:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0
	.byte	0,20,5,0,58,15,59,15,57,15,73,19,0,0,21,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,22,38,0
	.byte	73,19,0,0,23,21,1,54,15,39,12,0,0,24,5,0,73,19,0,0,25,19,1,58,15,59,15,57,15,11,15,0,0,26,46,1,49,19,0
	.byte	0,27,5,0,49,19,0,0,28,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,29,22,0,3,8,58,15,59,15,57,15,73
	.byte	19,0,0,30,21,0,54,15,0,0,31,1,1,73,19,0,0,32,33,0,0,0,33,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12
	.byte	0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L165:
	.word	.L670-.L669
.L669:
	.half	3
	.word	.L672-.L671
.L671:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'string.h',0,3,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'zf_common_function.h',0,4,0,0
	.byte	'zf_driver_delay.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,5,0,0
	.byte	'zf_driver_spi.h',0,5,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_oled.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'zf_common_font.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0
	.byte	'zf_driver_soft_spi.h',0,5,0,0,0
.L672:
.L670:
	.sdecl	'.debug_info',debug,cluster('oled_clear')
	.sect	'.debug_info'
.L166:
	.word	280
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L169,.L168
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_clear',0,1,161,1,6,1,1,1
	.word	.L135,.L260,.L134
	.byte	4
	.word	.L135,.L260
	.byte	5
	.byte	'y',0,1,163,1,11
	.word	.L261,.L262
	.byte	5
	.byte	'x',0,1,163,1,18
	.word	.L261,.L263
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_clear')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_clear')
	.sect	'.debug_line'
.L168:
	.word	.L674-.L673
.L673:
	.half	3
	.word	.L676-.L675
.L675:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L676:
	.byte	5,5,7,0,5,2
	.word	.L135
	.byte	3,164,1,1,5,11,9
	.half	.L14-.L135
	.byte	3,1,1,5,21,9
	.half	.L411-.L14
	.byte	1,5,33,9
	.half	.L16-.L411
	.byte	3,2,1,5,28,9
	.half	.L677-.L16
	.byte	3,1,1,9
	.half	.L678-.L677
	.byte	3,1,1,5,15,9
	.half	.L679-.L678
	.byte	3,1,1,5,34,9
	.half	.L412-.L679
	.byte	1,5,29,9
	.half	.L18-.L412
	.byte	3,2,1,5,38,9
	.half	.L680-.L18
	.byte	3,126,1,5,20,9
	.half	.L17-.L680
	.byte	1,5,34,9
	.half	.L681-.L17
	.byte	1,5,25,7,9
	.half	.L682-.L681
	.byte	3,123,1,5,21,9
	.half	.L15-.L682
	.byte	1,5,5,7,9
	.half	.L683-.L15
	.byte	3,10,1,5,1,9
	.half	.L20-.L683
	.byte	3,1,1,7,9
	.half	.L170-.L20
	.byte	0,1,1
.L674:
	.sdecl	'.debug_ranges',debug,cluster('oled_clear')
	.sect	'.debug_ranges'
.L169:
	.word	-1,.L135,0,.L170-.L135,0,0
	.sdecl	'.debug_info',debug,cluster('oled_full')
	.sect	'.debug_info'
.L171:
	.word	298
	.half	3
	.word	.L172
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L174,.L173
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_full',0,1,186,1,6,1,1,1
	.word	.L137,.L264,.L136
	.byte	4
	.byte	'color',0,1,186,1,29
	.word	.L265,.L266
	.byte	5
	.word	.L137,.L264
	.byte	6
	.byte	'y',0,1,188,1,11
	.word	.L261,.L267
	.byte	6
	.byte	'x',0,1,188,1,18
	.word	.L261,.L268
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_full')
	.sect	'.debug_abbrev'
.L172:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_full')
	.sect	'.debug_line'
.L173:
	.word	.L685-.L684
.L684:
	.half	3
	.word	.L687-.L686
.L686:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L687:
	.byte	5,6,7,0,5,2
	.word	.L137
	.byte	3,185,1,1,5,5,9
	.half	.L414-.L137
	.byte	3,4,1,5,11,9
	.half	.L22-.L414
	.byte	3,1,1,5,21,9
	.half	.L416-.L22
	.byte	1,5,33,9
	.half	.L24-.L416
	.byte	3,2,1,5,28,9
	.half	.L688-.L24
	.byte	3,1,1,9
	.half	.L689-.L688
	.byte	3,1,1,5,15,9
	.half	.L690-.L689
	.byte	3,1,1,5,34,9
	.half	.L417-.L690
	.byte	1,5,29,9
	.half	.L26-.L417
	.byte	3,2,1,5,38,9
	.half	.L419-.L26
	.byte	3,126,1,5,20,9
	.half	.L25-.L419
	.byte	1,5,34,9
	.half	.L691-.L25
	.byte	1,5,25,7,9
	.half	.L692-.L691
	.byte	3,123,1,5,21,9
	.half	.L23-.L692
	.byte	1,5,5,7,9
	.half	.L693-.L23
	.byte	3,10,1,5,1,9
	.half	.L28-.L693
	.byte	3,1,1,7,9
	.half	.L175-.L28
	.byte	0,1,1
.L685:
	.sdecl	'.debug_ranges',debug,cluster('oled_full')
	.sect	'.debug_ranges'
.L174:
	.word	-1,.L137,0,.L175-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('oled_set_dir')
	.sect	'.debug_info'
.L176:
	.word	268
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L179,.L178
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_set_dir',0,1,211,1,6,1,1,1
	.word	.L139,.L269,.L138
	.byte	4
	.byte	'dir',0,1,211,1,34
	.word	.L270,.L271
	.byte	5
	.word	.L139,.L269
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_set_dir')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_set_dir')
	.sect	'.debug_line'
.L178:
	.word	.L695-.L694
.L694:
	.half	3
	.word	.L697-.L696
.L696:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L697:
	.byte	5,5,7,0,5,2
	.word	.L139
	.byte	3,212,1,1,5,22,9
	.half	.L698-.L139
	.byte	1,5,1,9
	.half	.L699-.L698
	.byte	3,1,1,7,9
	.half	.L180-.L699
	.byte	0,1,1
.L695:
	.sdecl	'.debug_ranges',debug,cluster('oled_set_dir')
	.sect	'.debug_ranges'
.L179:
	.word	-1,.L139,0,.L180-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('oled_set_font')
	.sect	'.debug_info'
.L181:
	.word	270
	.half	3
	.word	.L182
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L184,.L183
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_set_font',0,1,223,1,6,1,1,1
	.word	.L141,.L272,.L140
	.byte	4
	.byte	'font',0,1,223,1,41
	.word	.L273,.L274
	.byte	5
	.word	.L141,.L272
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_set_font')
	.sect	'.debug_abbrev'
.L182:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_set_font')
	.sect	'.debug_line'
.L183:
	.word	.L701-.L700
.L700:
	.half	3
	.word	.L703-.L702
.L702:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L703:
	.byte	5,5,7,0,5,2
	.word	.L141
	.byte	3,224,1,1,5,23,9
	.half	.L704-.L141
	.byte	1,5,1,9
	.half	.L705-.L704
	.byte	3,1,1,7,9
	.half	.L185-.L705
	.byte	0,1,1
.L701:
	.sdecl	'.debug_ranges',debug,cluster('oled_set_font')
	.sect	'.debug_ranges'
.L184:
	.word	-1,.L141,0,.L185-.L141,0,0
	.sdecl	'.debug_info',debug,cluster('oled_draw_point')
	.sect	'.debug_info'
.L186:
	.word	303
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L189,.L188
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_draw_point',0,1,237,1,6,1,1,1
	.word	.L143,.L275,.L142
	.byte	4
	.byte	'x',0,1,237,1,30
	.word	.L276,.L277
	.byte	4
	.byte	'y',0,1,237,1,40
	.word	.L276,.L278
	.byte	4
	.byte	'color',0,1,237,1,55
	.word	.L279,.L280
	.byte	5
	.word	.L143,.L275
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_draw_point')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_draw_point')
	.sect	'.debug_line'
.L188:
	.word	.L707-.L706
.L706:
	.half	3
	.word	.L709-.L708
.L708:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L709:
	.byte	5,6,7,0,5,2
	.word	.L143
	.byte	3,236,1,1,5,5,9
	.half	.L423-.L143
	.byte	3,5,1,9
	.half	.L420-.L423
	.byte	3,1,1,9
	.half	.L710-.L420
	.byte	3,2,1,5,25,9
	.half	.L30-.L710
	.byte	3,1,1,5,35,9
	.half	.L425-.L30
	.byte	1,5,29,9
	.half	.L427-.L425
	.byte	3,1,1,5,28,9
	.half	.L429-.L427
	.byte	3,1,1,5,36,9
	.half	.L430-.L429
	.byte	1,5,42,9
	.half	.L711-.L430
	.byte	1,5,27,9
	.half	.L431-.L711
	.byte	3,1,1,5,35,9
	.half	.L432-.L431
	.byte	1,5,21,9
	.half	.L712-.L432
	.byte	3,1,1,5,5,9
	.half	.L434-.L712
	.byte	3,1,1,5,1,9
	.half	.L32-.L434
	.byte	3,1,1,7,9
	.half	.L190-.L32
	.byte	0,1,1
.L707:
	.sdecl	'.debug_ranges',debug,cluster('oled_draw_point')
	.sect	'.debug_ranges'
.L189:
	.word	-1,.L143,0,.L190-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_string')
	.sect	'.debug_info'
.L191:
	.word	357
	.half	3
	.word	.L192
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L194,.L193
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_string',0,1,135,2,6,1,1,1
	.word	.L145,.L281,.L144
	.byte	4
	.byte	'x',0,1,135,2,31
	.word	.L276,.L282
	.byte	4
	.byte	'y',0,1,135,2,41
	.word	.L276,.L283
	.byte	4
	.byte	'ch',0,1,135,2,55
	.word	.L284,.L285
	.byte	5
	.word	.L145,.L281
	.byte	5
	.word	.L34,.L281
	.byte	6
	.byte	'c',0,1,144,2,11
	.word	.L261,.L286
	.byte	6
	.byte	'i',0,1,144,2,18
	.word	.L261,.L287
	.byte	6
	.byte	'j',0,1,144,2,25
	.word	.L261,.L288
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_string')
	.sect	'.debug_abbrev'
.L192:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_string')
	.sect	'.debug_line'
.L193:
	.word	.L714-.L713
.L713:
	.half	3
	.word	.L716-.L715
.L715:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L716:
	.byte	5,6,7,0,5,2
	.word	.L145
	.byte	3,134,2,1,5,5,9
	.half	.L438-.L145
	.byte	3,5,1,9
	.half	.L439-.L438
	.byte	3,1,1,9
	.half	.L717-.L439
	.byte	3,2,1,5,27,9
	.half	.L34-.L717
	.byte	3,1,1,5,25,9
	.half	.L441-.L34
	.byte	3,1,1,5,16,9
	.half	.L36-.L441
	.byte	3,2,1,5,18,9
	.half	.L718-.L36
	.byte	3,2,1,9
	.half	.L719-.L718
	.byte	3,16,1,9
	.half	.L720-.L719
	.byte	3,22,1,5,23,9
	.half	.L37-.L720
	.byte	3,92,1,5,27,9
	.half	.L721-.L37
	.byte	1,5,24,9
	.half	.L443-.L721
	.byte	3,1,1,5,17,9
	.half	.L444-.L443
	.byte	1,5,23,7,9
	.half	.L445-.L444
	.byte	3,2,1,9
	.half	.L446-.L445
	.byte	3,1,1,5,47,9
	.half	.L41-.L446
	.byte	3,2,1,5,23,9
	.half	.L449-.L41
	.byte	3,1,1,5,33,9
	.half	.L450-.L449
	.byte	1,5,51,9
	.half	.L43-.L450
	.byte	3,2,1,5,37,9
	.half	.L722-.L43
	.byte	1,5,51,9
	.half	.L723-.L722
	.byte	1,5,54,9
	.half	.L724-.L723
	.byte	1,5,37,9
	.half	.L725-.L724
	.byte	3,126,1,5,33,9
	.half	.L42-.L725
	.byte	1,5,19,7,9
	.half	.L451-.L42
	.byte	3,4,1,9
	.half	.L726-.L451
	.byte	3,1,1,5,14,9
	.half	.L452-.L726
	.byte	3,1,1,5,23,9
	.half	.L38-.L452
	.byte	3,3,1,5,27,9
	.half	.L727-.L38
	.byte	1,5,24,9
	.half	.L454-.L727
	.byte	3,1,1,5,17,9
	.half	.L455-.L454
	.byte	1,5,23,7,9
	.half	.L456-.L455
	.byte	3,2,1,9
	.half	.L457-.L456
	.byte	3,1,1,5,47,9
	.half	.L45-.L457
	.byte	3,2,1,5,23,9
	.half	.L460-.L45
	.byte	3,1,1,5,33,9
	.half	.L461-.L460
	.byte	1,5,52,9
	.half	.L47-.L461
	.byte	3,2,1,5,37,9
	.half	.L728-.L47
	.byte	1,5,52,9
	.half	.L729-.L728
	.byte	1,5,55,9
	.half	.L730-.L729
	.byte	1,5,37,9
	.half	.L731-.L730
	.byte	3,126,1,5,33,9
	.half	.L46-.L731
	.byte	1,5,57,7,9
	.half	.L463-.L46
	.byte	3,5,1,5,47,9
	.half	.L462-.L463
	.byte	1,5,23,9
	.half	.L466-.L462
	.byte	3,1,1,5,33,9
	.half	.L467-.L466
	.byte	1,5,52,9
	.half	.L49-.L467
	.byte	3,2,1,5,37,9
	.half	.L732-.L49
	.byte	1,5,52,9
	.half	.L733-.L732
	.byte	1,5,55,9
	.half	.L734-.L733
	.byte	1,5,37,9
	.half	.L735-.L734
	.byte	3,126,1,5,33,9
	.half	.L48-.L735
	.byte	1,5,19,7,9
	.half	.L468-.L48
	.byte	3,4,1,9
	.half	.L736-.L468
	.byte	3,1,1,5,14,9
	.half	.L469-.L736
	.byte	3,1,1,9
	.half	.L39-.L469
	.byte	3,4,1,5,22,9
	.half	.L35-.L39
	.byte	3,83,1,5,25,9
	.half	.L737-.L35
	.byte	1,5,5,7,9
	.half	.L738-.L737
	.byte	3,48,1,5,1,9
	.half	.L53-.L738
	.byte	3,1,1,7,9
	.half	.L195-.L53
	.byte	0,1,1
.L714:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_string')
	.sect	'.debug_ranges'
.L194:
	.word	-1,.L145,0,.L195-.L145,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_int')
	.sect	'.debug_info'
.L196:
	.word	394
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L199,.L198
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_int',0,1,206,2,6,1,1,1
	.word	.L147,.L289,.L146
	.byte	4
	.byte	'x',0,1,206,2,28
	.word	.L276,.L290
	.byte	4
	.byte	'y',0,1,206,2,38
	.word	.L276,.L291
	.byte	4
	.byte	'dat',0,1,206,2,53
	.word	.L292,.L293
	.byte	4
	.byte	'num',0,1,206,2,64
	.word	.L261,.L294
	.byte	5
	.word	.L147,.L289
	.byte	5
	.word	.L295,.L289
	.byte	6
	.byte	'dat_temp',0,1,217,2,11
	.word	.L296,.L297
	.byte	6
	.byte	'offset',0,1,218,2,11
	.word	.L296,.L298
	.byte	6
	.byte	'data_buffer',0,1,219,2,10
	.word	.L299,.L300
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_int')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_int')
	.sect	'.debug_line'
.L198:
	.word	.L740-.L739
.L739:
	.half	3
	.word	.L742-.L741
.L741:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L742:
	.byte	5,6,7,0,5,2
	.word	.L147
	.byte	3,205,2,1,5,5,9
	.half	.L475-.L147
	.byte	3,5,1,9
	.half	.L471-.L475
	.byte	3,1,1,9
	.half	.L743-.L471
	.byte	3,2,1,9
	.half	.L744-.L743
	.byte	3,1,1,5,18,9
	.half	.L295-.L744
	.byte	3,3,1,5,12,9
	.half	.L477-.L295
	.byte	3,3,1,5,25,9
	.half	.L745-.L477
	.byte	1,5,28,9
	.half	.L746-.L745
	.byte	1,5,12,9
	.half	.L747-.L746
	.byte	3,1,1,5,25,9
	.half	.L748-.L747
	.byte	1,5,34,9
	.half	.L749-.L748
	.byte	1,5,5,9
	.half	.L750-.L749
	.byte	3,3,1,5,22,7,9
	.half	.L751-.L750
	.byte	3,2,1,5,20,9
	.half	.L56-.L751
	.byte	3,2,1,5,28,9
	.half	.L752-.L56
	.byte	3,126,1,5,22,9
	.half	.L55-.L752
	.byte	1,5,18,7,9
	.half	.L753-.L55
	.byte	3,4,1,5,21,9
	.half	.L54-.L753
	.byte	3,2,1,5,34,9
	.half	.L754-.L54
	.byte	1,5,43,9
	.half	.L479-.L754
	.byte	3,1,1,5,28,9
	.half	.L480-.L479
	.byte	1,5,1,9
	.half	.L755-.L480
	.byte	3,1,1,7,9
	.half	.L200-.L755
	.byte	0,1,1
.L740:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_int')
	.sect	'.debug_ranges'
.L199:
	.word	-1,.L147,0,.L200-.L147,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_uint')
	.sect	'.debug_info'
.L201:
	.word	395
	.half	3
	.word	.L202
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L204,.L203
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_uint',0,1,247,2,6,1,1,1
	.word	.L149,.L301,.L148
	.byte	4
	.byte	'x',0,1,247,2,29
	.word	.L276,.L302
	.byte	4
	.byte	'y',0,1,247,2,38
	.word	.L276,.L303
	.byte	4
	.byte	'dat',0,1,247,2,53
	.word	.L304,.L305
	.byte	4
	.byte	'num',0,1,247,2,63
	.word	.L261,.L306
	.byte	5
	.word	.L149,.L301
	.byte	5
	.word	.L307,.L301
	.byte	6
	.byte	'dat_temp',0,1,130,3,12
	.word	.L308,.L309
	.byte	6
	.byte	'offset',0,1,131,3,11
	.word	.L296,.L310
	.byte	6
	.byte	'data_buffer',0,1,132,3,10
	.word	.L299,.L311
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_uint')
	.sect	'.debug_abbrev'
.L202:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_uint')
	.sect	'.debug_line'
.L203:
	.word	.L757-.L756
.L756:
	.half	3
	.word	.L759-.L758
.L758:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L759:
	.byte	5,6,7,0,5,2
	.word	.L149
	.byte	3,246,2,1,5,5,9
	.half	.L487-.L149
	.byte	3,5,1,9
	.half	.L483-.L487
	.byte	3,1,1,9
	.half	.L760-.L483
	.byte	3,2,1,9
	.half	.L761-.L760
	.byte	3,1,1,5,18,9
	.half	.L307-.L761
	.byte	3,3,1,5,12,9
	.half	.L489-.L307
	.byte	3,2,1,5,25,9
	.half	.L762-.L489
	.byte	1,5,28,9
	.half	.L763-.L762
	.byte	1,5,12,9
	.half	.L764-.L763
	.byte	3,1,1,5,25,9
	.half	.L765-.L764
	.byte	1,5,30,9
	.half	.L766-.L765
	.byte	1,5,5,9
	.half	.L491-.L766
	.byte	3,3,1,5,22,7,9
	.half	.L767-.L491
	.byte	3,2,1,5,20,9
	.half	.L59-.L767
	.byte	3,2,1,5,28,9
	.half	.L768-.L59
	.byte	3,126,1,5,22,9
	.half	.L58-.L768
	.byte	1,5,18,7,9
	.half	.L769-.L58
	.byte	3,4,1,5,22,9
	.half	.L57-.L769
	.byte	3,2,1,5,35,9
	.half	.L770-.L57
	.byte	1,5,43,9
	.half	.L493-.L770
	.byte	3,1,1,5,28,9
	.half	.L494-.L493
	.byte	1,5,1,9
	.half	.L771-.L494
	.byte	3,1,1,7,9
	.half	.L205-.L771
	.byte	0,1,1
.L757:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_uint')
	.sect	'.debug_ranges'
.L204:
	.word	-1,.L149,0,.L205-.L149,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_float')
	.sect	'.debug_info'
.L206:
	.word	418
	.half	3
	.word	.L207
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L209,.L208
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_float',0,1,163,3,6,1,1,1
	.word	.L151,.L312,.L150
	.byte	4
	.byte	'x',0,1,163,3,30
	.word	.L276,.L313
	.byte	4
	.byte	'y',0,1,163,3,39
	.word	.L276,.L314
	.byte	4
	.byte	'dat',0,1,163,3,54
	.word	.L315,.L316
	.byte	4
	.byte	'num',0,1,163,3,64
	.word	.L261,.L317
	.byte	4
	.byte	'pointnum',0,1,163,3,74
	.word	.L261,.L318
	.byte	5
	.word	.L151,.L312
	.byte	5
	.word	.L319,.L312
	.byte	6
	.byte	'dat_temp',0,1,176,3,12
	.word	.L320,.L321
	.byte	6
	.byte	'offset',0,1,177,3,12
	.word	.L320,.L322
	.byte	6
	.byte	'data_buffer',0,1,178,3,10
	.word	.L323,.L324
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_float')
	.sect	'.debug_abbrev'
.L207:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_float')
	.sect	'.debug_line'
.L208:
	.word	.L773-.L772
.L772:
	.half	3
	.word	.L775-.L774
.L774:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L775:
	.byte	5,6,7,0,5,2
	.word	.L151
	.byte	3,162,3,1,5,5,9
	.half	.L503-.L151
	.byte	3,5,1,9
	.half	.L497-.L503
	.byte	3,1,1,9
	.half	.L776-.L497
	.byte	3,2,1,9
	.half	.L777-.L776
	.byte	3,1,1,9
	.half	.L778-.L777
	.byte	3,1,1,9
	.half	.L779-.L778
	.byte	3,1,1,5,21,9
	.half	.L319-.L779
	.byte	3,3,1,5,12,9
	.half	.L780-.L319
	.byte	3,2,1,5,25,9
	.half	.L781-.L780
	.byte	1,5,28,9
	.half	.L782-.L781
	.byte	1,5,12,9
	.half	.L783-.L782
	.byte	3,1,1,5,25,9
	.half	.L784-.L783
	.byte	1,5,34,9
	.half	.L785-.L784
	.byte	1,5,45,9
	.half	.L786-.L785
	.byte	1,5,18,9
	.half	.L787-.L786
	.byte	3,3,1,5,19,9
	.half	.L61-.L787
	.byte	3,2,1,5,16,9
	.half	.L506-.L61
	.byte	1,5,24,9
	.half	.L788-.L506
	.byte	3,126,1,5,18,9
	.half	.L60-.L788
	.byte	1,5,28,7,9
	.half	.L508-.L60
	.byte	3,4,1,5,44,9
	.half	.L510-.L508
	.byte	1,5,42,9
	.half	.L789-.L510
	.byte	1,5,57,9
	.half	.L512-.L789
	.byte	1,5,25,9
	.half	.L514-.L512
	.byte	1,5,24,9
	.half	.L790-.L514
	.byte	3,1,1,5,47,9
	.half	.L791-.L790
	.byte	1,5,28,9
	.half	.L517-.L791
	.byte	3,1,1,5,1,9
	.half	.L519-.L517
	.byte	3,1,1,7,9
	.half	.L210-.L519
	.byte	0,1,1
.L773:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_float')
	.sect	'.debug_ranges'
.L209:
	.word	-1,.L151,0,.L210-.L151,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_binary_image')
	.sect	'.debug_info'
.L211:
	.word	520
	.half	3
	.word	.L212
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L214,.L213
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_binary_image',0,1,208,3,6,1,1,1
	.word	.L153,.L325,.L152
	.byte	4
	.byte	'x',0,1,208,3,37
	.word	.L276,.L326
	.byte	4
	.byte	'y',0,1,208,3,47
	.word	.L276,.L327
	.byte	4
	.byte	'image',0,1,208,3,63
	.word	.L328,.L329
	.byte	4
	.byte	'width',0,1,208,3,77
	.word	.L276,.L330
	.byte	4
	.byte	'height',0,1,208,3,91
	.word	.L276,.L331
	.byte	4
	.byte	'dis_width',0,1,208,3,106
	.word	.L276,.L332
	.byte	4
	.byte	'dis_height',0,1,208,3,124
	.word	.L276,.L333
	.byte	5
	.word	.L153,.L325
	.byte	5
	.word	.L334,.L325
	.byte	6
	.byte	'i',0,1,217,3,12
	.word	.L308,.L335
	.byte	6
	.byte	'j',0,1,217,3,19
	.word	.L308,.L336
	.byte	6
	.byte	'z',0,1,217,3,26
	.word	.L308,.L337
	.byte	6
	.byte	'dat',0,1,218,3,11
	.word	.L261,.L338
	.byte	6
	.byte	'width_index',0,1,219,3,12
	.word	.L308,.L339
	.byte	6
	.byte	'height_index',0,1,219,3,29
	.word	.L308,.L340
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_binary_image')
	.sect	'.debug_abbrev'
.L212:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_binary_image')
	.sect	'.debug_line'
.L213:
	.word	.L793-.L792
.L792:
	.half	3
	.word	.L795-.L794
.L794:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L795:
	.byte	5,6,7,0,5,2
	.word	.L153
	.byte	3,207,3,1,5,5,9
	.half	.L535-.L153
	.byte	3,5,1,9
	.half	.L524-.L535
	.byte	3,1,1,9
	.half	.L796-.L524
	.byte	3,1,1,9
	.half	.L334-.L796
	.byte	3,6,1,5,44,9
	.half	.L63-.L334
	.byte	3,1,1,5,42,9
	.half	.L797-.L63
	.byte	1,5,29,9
	.half	.L798-.L797
	.byte	1,5,41,9
	.half	.L522-.L798
	.byte	3,1,1,5,39,9
	.half	.L540-.L522
	.byte	1,5,27,9
	.half	.L799-.L540
	.byte	1,5,11,9
	.half	.L523-.L799
	.byte	3,1,1,5,30,9
	.half	.L543-.L523
	.byte	1,5,31,9
	.half	.L65-.L543
	.byte	3,2,1,5,53,9
	.half	.L800-.L65
	.byte	1,5,51,9
	.half	.L544-.L800
	.byte	1,5,47,9
	.half	.L545-.L544
	.byte	1,5,36,9
	.half	.L547-.L545
	.byte	1,5,26,9
	.half	.L801-.L547
	.byte	3,1,1,5,35,9
	.half	.L551-.L801
	.byte	1,5,15,9
	.half	.L552-.L551
	.byte	3,1,1,5,33,9
	.half	.L553-.L552
	.byte	1,5,29,9
	.half	.L67-.L553
	.byte	3,2,1,5,37,9
	.half	.L554-.L67
	.byte	1,5,51,9
	.half	.L556-.L554
	.byte	1,5,49,9
	.half	.L802-.L556
	.byte	1,5,19,9
	.half	.L557-.L802
	.byte	3,1,1,5,29,9
	.half	.L558-.L557
	.byte	1,5,21,9
	.half	.L69-.L558
	.byte	3,2,1,5,43,9
	.half	.L560-.L69
	.byte	3,1,1,5,53,9
	.half	.L803-.L560
	.byte	1,5,51,9
	.half	.L804-.L803
	.byte	1,5,28,9
	.half	.L805-.L804
	.byte	1,5,55,9
	.half	.L806-.L805
	.byte	1,5,20,9
	.half	.L807-.L806
	.byte	1,5,89,9
	.half	.L808-.L807
	.byte	1,5,94,9
	.half	.L809-.L808
	.byte	1,5,86,9
	.half	.L810-.L809
	.byte	1,5,17,9
	.half	.L811-.L810
	.byte	1,5,25,7,9
	.half	.L812-.L811
	.byte	3,2,1,5,79,9
	.half	.L70-.L812
	.byte	3,2,1,5,77,9
	.half	.L813-.L70
	.byte	1,5,81,9
	.half	.L563-.L813
	.byte	1,5,43,9
	.half	.L814-.L563
	.byte	1,5,53,9
	.half	.L815-.L814
	.byte	1,5,51,9
	.half	.L816-.L815
	.byte	1,5,28,9
	.half	.L817-.L816
	.byte	1,5,55,9
	.half	.L818-.L817
	.byte	1,5,69,9
	.half	.L819-.L818
	.byte	1,5,20,9
	.half	.L820-.L819
	.byte	1,5,89,9
	.half	.L821-.L820
	.byte	1,5,94,9
	.half	.L564-.L821
	.byte	1,5,86,9
	.half	.L822-.L564
	.byte	1,5,17,9
	.half	.L823-.L822
	.byte	1,5,25,7,9
	.half	.L824-.L823
	.byte	3,2,1,5,79,9
	.half	.L71-.L824
	.byte	3,2,1,5,77,9
	.half	.L825-.L71
	.byte	1,5,81,9
	.half	.L566-.L825
	.byte	1,5,43,9
	.half	.L826-.L566
	.byte	1,5,53,9
	.half	.L827-.L826
	.byte	1,5,51,9
	.half	.L828-.L827
	.byte	1,5,28,9
	.half	.L829-.L828
	.byte	1,5,55,9
	.half	.L830-.L829
	.byte	1,5,69,9
	.half	.L831-.L830
	.byte	1,5,20,9
	.half	.L832-.L831
	.byte	1,5,89,9
	.half	.L833-.L832
	.byte	1,5,94,9
	.half	.L567-.L833
	.byte	1,5,86,9
	.half	.L834-.L567
	.byte	1,5,17,9
	.half	.L835-.L834
	.byte	1,5,25,7,9
	.half	.L836-.L835
	.byte	3,2,1,5,79,9
	.half	.L72-.L836
	.byte	3,2,1,5,77,9
	.half	.L837-.L72
	.byte	1,5,81,9
	.half	.L569-.L837
	.byte	1,5,43,9
	.half	.L838-.L569
	.byte	1,5,53,9
	.half	.L839-.L838
	.byte	1,5,51,9
	.half	.L840-.L839
	.byte	1,5,28,9
	.half	.L841-.L840
	.byte	1,5,55,9
	.half	.L842-.L841
	.byte	1,5,69,9
	.half	.L843-.L842
	.byte	1,5,20,9
	.half	.L844-.L843
	.byte	1,5,89,9
	.half	.L845-.L844
	.byte	1,5,94,9
	.half	.L570-.L845
	.byte	1,5,86,9
	.half	.L846-.L570
	.byte	1,5,17,9
	.half	.L847-.L846
	.byte	1,5,25,7,9
	.half	.L848-.L847
	.byte	3,2,1,5,79,9
	.half	.L73-.L848
	.byte	3,2,1,5,77,9
	.half	.L849-.L73
	.byte	1,5,81,9
	.half	.L572-.L849
	.byte	1,5,43,9
	.half	.L850-.L572
	.byte	1,5,53,9
	.half	.L851-.L850
	.byte	1,5,51,9
	.half	.L852-.L851
	.byte	1,5,28,9
	.half	.L853-.L852
	.byte	1,5,55,9
	.half	.L854-.L853
	.byte	1,5,69,9
	.half	.L855-.L854
	.byte	1,5,20,9
	.half	.L856-.L855
	.byte	1,5,89,9
	.half	.L857-.L856
	.byte	1,5,94,9
	.half	.L573-.L857
	.byte	1,5,86,9
	.half	.L858-.L573
	.byte	1,5,17,9
	.half	.L859-.L858
	.byte	1,5,25,7,9
	.half	.L860-.L859
	.byte	3,2,1,5,79,9
	.half	.L74-.L860
	.byte	3,2,1,5,77,9
	.half	.L861-.L74
	.byte	1,5,81,9
	.half	.L575-.L861
	.byte	1,5,43,9
	.half	.L862-.L575
	.byte	1,5,53,9
	.half	.L863-.L862
	.byte	1,5,51,9
	.half	.L864-.L863
	.byte	1,5,28,9
	.half	.L865-.L864
	.byte	1,5,55,9
	.half	.L866-.L865
	.byte	1,5,69,9
	.half	.L867-.L866
	.byte	1,5,20,9
	.half	.L868-.L867
	.byte	1,5,89,9
	.half	.L869-.L868
	.byte	1,5,94,9
	.half	.L576-.L869
	.byte	1,5,86,9
	.half	.L870-.L576
	.byte	1,5,17,9
	.half	.L871-.L870
	.byte	1,5,25,7,9
	.half	.L872-.L871
	.byte	3,2,1,5,79,9
	.half	.L75-.L872
	.byte	3,2,1,5,77,9
	.half	.L873-.L75
	.byte	1,5,81,9
	.half	.L578-.L873
	.byte	1,5,43,9
	.half	.L874-.L578
	.byte	1,5,53,9
	.half	.L875-.L874
	.byte	1,5,51,9
	.half	.L876-.L875
	.byte	1,5,28,9
	.half	.L877-.L876
	.byte	1,5,55,9
	.half	.L878-.L877
	.byte	1,5,69,9
	.half	.L879-.L878
	.byte	1,5,20,9
	.half	.L880-.L879
	.byte	1,5,89,9
	.half	.L881-.L880
	.byte	1,5,94,9
	.half	.L579-.L881
	.byte	1,5,86,9
	.half	.L882-.L579
	.byte	1,5,17,9
	.half	.L883-.L882
	.byte	1,5,25,7,9
	.half	.L884-.L883
	.byte	3,2,1,5,79,9
	.half	.L76-.L884
	.byte	3,2,1,5,77,9
	.half	.L885-.L76
	.byte	1,5,81,9
	.half	.L581-.L885
	.byte	1,5,43,9
	.half	.L886-.L581
	.byte	1,5,53,9
	.half	.L887-.L886
	.byte	1,5,51,9
	.half	.L888-.L887
	.byte	1,5,28,9
	.half	.L889-.L888
	.byte	1,5,55,9
	.half	.L890-.L889
	.byte	1,5,69,9
	.half	.L891-.L890
	.byte	1,5,20,9
	.half	.L892-.L891
	.byte	1,5,89,9
	.half	.L893-.L892
	.byte	1,5,94,9
	.half	.L582-.L893
	.byte	1,5,86,9
	.half	.L894-.L582
	.byte	1,5,17,9
	.half	.L895-.L894
	.byte	1,5,25,7,9
	.half	.L896-.L895
	.byte	3,2,1,5,33,9
	.half	.L77-.L896
	.byte	3,2,1,9
	.half	.L561-.L77
	.byte	3,93,1,5,29,9
	.half	.L68-.L561
	.byte	1,5,37,7,9
	.half	.L897-.L68
	.byte	3,125,1,5,33,9
	.half	.L66-.L897
	.byte	1,5,34,9
	.half	.L898-.L66
	.byte	3,124,1,5,30,9
	.half	.L64-.L898
	.byte	1,5,5,9
	.half	.L899-.L64
	.byte	3,46,1,5,1,9
	.half	.L79-.L899
	.byte	3,1,1,7,9
	.half	.L215-.L79
	.byte	0,1,1
.L793:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_binary_image')
	.sect	'.debug_ranges'
.L214:
	.word	-1,.L153,0,.L215-.L153,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_gray_image')
	.sect	'.debug_info'
.L216:
	.word	527
	.half	3
	.word	.L217
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L219,.L218
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_gray_image',0,1,162,4,6,1,1,1
	.word	.L155,.L341,.L154
	.byte	4
	.byte	'x',0,1,162,4,35
	.word	.L276,.L342
	.byte	4
	.byte	'y',0,1,162,4,45
	.word	.L276,.L343
	.byte	4
	.byte	'image',0,1,162,4,61
	.word	.L328,.L344
	.byte	4
	.byte	'width',0,1,162,4,75
	.word	.L276,.L345
	.byte	4
	.byte	'height',0,1,162,4,89
	.word	.L276,.L346
	.byte	4
	.byte	'dis_width',0,1,162,4,104
	.word	.L276,.L347
	.byte	4
	.byte	'dis_height',0,1,162,4,122
	.word	.L276,.L348
	.byte	4
	.byte	'threshold',0,1,162,4,140,1
	.word	.L261,.L349
	.byte	5
	.word	.L155,.L341
	.byte	5
	.word	.L350,.L341
	.byte	6
	.byte	'i',0,1,171,4,11
	.word	.L351,.L352
	.byte	6
	.byte	'j',0,1,171,4,18
	.word	.L351,.L353
	.byte	6
	.byte	'dat',0,1,172,4,11
	.word	.L261,.L354
	.byte	6
	.byte	'width_index',0,1,173,4,12
	.word	.L308,.L355
	.byte	6
	.byte	'height_index',0,1,173,4,29
	.word	.L308,.L356
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_gray_image')
	.sect	'.debug_abbrev'
.L217:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_gray_image')
	.sect	'.debug_line'
.L218:
	.word	.L901-.L900
.L900:
	.half	3
	.word	.L903-.L902
.L902:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L903:
	.byte	5,6,7,0,5,2
	.word	.L155
	.byte	3,161,4,1,5,5,9
	.half	.L605-.L155
	.byte	3,5,1,9
	.half	.L593-.L605
	.byte	3,1,1,9
	.half	.L904-.L593
	.byte	3,1,1,9
	.half	.L350-.L904
	.byte	3,6,1,5,44,9
	.half	.L81-.L350
	.byte	3,1,1,5,42,9
	.half	.L905-.L81
	.byte	1,5,29,9
	.half	.L906-.L905
	.byte	1,5,11,9
	.half	.L592-.L906
	.byte	3,1,1,5,30,9
	.half	.L610-.L592
	.byte	1,5,31,9
	.half	.L83-.L610
	.byte	3,2,1,5,44,9
	.half	.L907-.L83
	.byte	1,5,42,9
	.half	.L609-.L907
	.byte	1,5,38,9
	.half	.L908-.L609
	.byte	1,5,26,9
	.half	.L909-.L908
	.byte	3,1,1,5,35,9
	.half	.L614-.L909
	.byte	1,5,15,9
	.half	.L616-.L614
	.byte	3,1,1,5,33,9
	.half	.L617-.L616
	.byte	1,5,29,9
	.half	.L85-.L617
	.byte	3,2,1,5,37,9
	.half	.L910-.L85
	.byte	1,5,17,9
	.half	.L619-.L910
	.byte	3,1,1,5,39,9
	.half	.L621-.L619
	.byte	3,1,1,5,24,9
	.half	.L911-.L621
	.byte	1,5,47,9
	.half	.L912-.L911
	.byte	1,5,16,9
	.half	.L913-.L912
	.byte	1,5,13,9
	.half	.L914-.L913
	.byte	1,5,21,7,9
	.half	.L915-.L914
	.byte	3,2,1,5,69,9
	.half	.L86-.L915
	.byte	3,2,1,5,39,9
	.half	.L916-.L86
	.byte	1,5,24,9
	.half	.L917-.L916
	.byte	1,5,47,9
	.half	.L918-.L917
	.byte	1,5,61,9
	.half	.L919-.L918
	.byte	1,5,16,9
	.half	.L920-.L919
	.byte	1,5,13,9
	.half	.L921-.L920
	.byte	1,5,21,7,9
	.half	.L922-.L921
	.byte	3,2,1,5,69,9
	.half	.L87-.L922
	.byte	3,2,1,5,39,9
	.half	.L923-.L87
	.byte	1,5,24,9
	.half	.L924-.L923
	.byte	1,5,47,9
	.half	.L925-.L924
	.byte	1,5,61,9
	.half	.L926-.L925
	.byte	1,5,16,9
	.half	.L927-.L926
	.byte	1,5,13,9
	.half	.L928-.L927
	.byte	1,5,21,7,9
	.half	.L929-.L928
	.byte	3,2,1,5,69,9
	.half	.L88-.L929
	.byte	3,2,1,5,39,9
	.half	.L930-.L88
	.byte	1,5,24,9
	.half	.L931-.L930
	.byte	1,5,47,9
	.half	.L932-.L931
	.byte	1,5,61,9
	.half	.L933-.L932
	.byte	1,5,16,9
	.half	.L934-.L933
	.byte	1,5,13,9
	.half	.L935-.L934
	.byte	1,5,21,7,9
	.half	.L936-.L935
	.byte	3,2,1,5,69,9
	.half	.L89-.L936
	.byte	3,2,1,5,39,9
	.half	.L937-.L89
	.byte	1,5,24,9
	.half	.L938-.L937
	.byte	1,5,47,9
	.half	.L939-.L938
	.byte	1,5,61,9
	.half	.L940-.L939
	.byte	1,5,16,9
	.half	.L941-.L940
	.byte	1,5,13,9
	.half	.L942-.L941
	.byte	1,5,21,7,9
	.half	.L943-.L942
	.byte	3,2,1,5,69,9
	.half	.L90-.L943
	.byte	3,2,1,5,39,9
	.half	.L944-.L90
	.byte	1,5,24,9
	.half	.L945-.L944
	.byte	1,5,47,9
	.half	.L946-.L945
	.byte	1,5,61,9
	.half	.L947-.L946
	.byte	1,5,16,9
	.half	.L948-.L947
	.byte	1,5,13,9
	.half	.L949-.L948
	.byte	1,5,21,7,9
	.half	.L950-.L949
	.byte	3,2,1,5,69,9
	.half	.L91-.L950
	.byte	3,2,1,5,39,9
	.half	.L951-.L91
	.byte	1,5,24,9
	.half	.L952-.L951
	.byte	1,5,47,9
	.half	.L953-.L952
	.byte	1,5,61,9
	.half	.L954-.L953
	.byte	1,5,16,9
	.half	.L955-.L954
	.byte	1,5,13,9
	.half	.L956-.L955
	.byte	1,5,21,7,9
	.half	.L957-.L956
	.byte	3,2,1,5,69,9
	.half	.L92-.L957
	.byte	3,2,1,5,39,9
	.half	.L958-.L92
	.byte	1,5,24,9
	.half	.L959-.L958
	.byte	1,5,47,9
	.half	.L960-.L959
	.byte	1,5,61,9
	.half	.L961-.L960
	.byte	1,5,16,9
	.half	.L962-.L961
	.byte	1,5,13,9
	.half	.L963-.L962
	.byte	1,5,21,7,9
	.half	.L964-.L963
	.byte	3,2,1,5,29,9
	.half	.L93-.L964
	.byte	3,2,1,5,37,9
	.half	.L620-.L93
	.byte	3,92,1,5,33,9
	.half	.L84-.L620
	.byte	1,5,34,7,9
	.half	.L965-.L84
	.byte	3,124,1,5,30,9
	.half	.L82-.L965
	.byte	1,5,5,9
	.half	.L966-.L82
	.byte	3,43,1,5,1,9
	.half	.L95-.L966
	.byte	3,1,1,7,9
	.half	.L220-.L95
	.byte	0,1,1
.L901:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_gray_image')
	.sect	'.debug_ranges'
.L219:
	.word	-1,.L155,0,.L220-.L155,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_wave')
	.sect	'.debug_info'
.L221:
	.word	532
	.half	3
	.word	.L222
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L224,.L223
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_wave',0,1,236,4,6,1,1,1
	.word	.L157,.L357,.L156
	.byte	4
	.byte	'x',0,1,236,4,29
	.word	.L276,.L358
	.byte	4
	.byte	'y',0,1,236,4,39
	.word	.L276,.L359
	.byte	4
	.byte	'wave',0,1,236,4,56
	.word	.L360,.L361
	.byte	4
	.byte	'width',0,1,236,4,69
	.word	.L276,.L362
	.byte	4
	.byte	'value_max',0,1,236,4,83
	.word	.L276,.L363
	.byte	4
	.byte	'dis_width',0,1,236,4,101
	.word	.L276,.L364
	.byte	4
	.byte	'dis_value_max',0,1,236,4,119
	.word	.L276,.L365
	.byte	5
	.word	.L157,.L357
	.byte	5
	.word	.L366,.L357
	.byte	6
	.byte	'i',0,1,245,4,12
	.word	.L308,.L367
	.byte	6
	.byte	'width_index',0,1,246,4,12
	.word	.L308,.L368
	.byte	6
	.byte	'value_max_index',0,1,246,4,29
	.word	.L308,.L369
	.byte	6
	.byte	'dis_h',0,1,247,4,11
	.word	.L261,.L370
	.byte	6
	.byte	'x_temp',0,1,249,4,12
	.word	.L308,.L371
	.byte	6
	.byte	'y_temp',0,1,250,4,12
	.word	.L308,.L372
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_wave')
	.sect	'.debug_abbrev'
.L222:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_wave')
	.sect	'.debug_line'
.L223:
	.word	.L968-.L967
.L967:
	.half	3
	.word	.L970-.L969
.L969:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L970:
	.byte	5,6,7,0,5,2
	.word	.L157
	.byte	3,235,4,1,5,5,9
	.half	.L633-.L157
	.byte	3,5,1,9
	.half	.L625-.L633
	.byte	3,1,1,9
	.half	.L971-.L625
	.byte	3,1,1,9
	.half	.L366-.L971
	.byte	3,9,1,5,16,9
	.half	.L97-.L366
	.byte	3,1,1,5,43,9
	.half	.L635-.L97
	.byte	1,5,31,9
	.half	.L99-.L635
	.byte	3,2,1,5,58,9
	.half	.L637-.L99
	.byte	1,5,56,9
	.half	.L972-.L637
	.byte	1,5,47,9
	.half	.L638-.L972
	.byte	1,5,36,9
	.half	.L639-.L638
	.byte	1,5,20,9
	.half	.L973-.L639
	.byte	3,1,1,5,43,9
	.half	.L640-.L973
	.byte	1,5,29,9
	.half	.L101-.L640
	.byte	3,2,1,5,52,9
	.half	.L974-.L101
	.byte	3,126,1,5,43,9
	.half	.L100-.L974
	.byte	1,5,52,7,9
	.half	.L975-.L100
	.byte	3,125,1,5,43,9
	.half	.L98-.L975
	.byte	1,5,11,7,9
	.half	.L976-.L98
	.byte	3,8,1,5,29,9
	.half	.L636-.L976
	.byte	1,5,25,9
	.half	.L103-.L636
	.byte	3,2,1,5,33,9
	.half	.L642-.L103
	.byte	1,5,34,9
	.half	.L643-.L642
	.byte	3,1,1,5,27,9
	.half	.L977-.L643
	.byte	1,5,66,9
	.half	.L644-.L977
	.byte	1,5,49,9
	.half	.L978-.L644
	.byte	1,5,71,9
	.half	.L979-.L978
	.byte	1,5,40,9
	.half	.L645-.L979
	.byte	3,2,1,5,45,9
	.half	.L980-.L645
	.byte	1,5,17,9
	.half	.L981-.L980
	.byte	1,5,40,9
	.half	.L647-.L981
	.byte	3,1,1,5,29,9
	.half	.L649-.L647
	.byte	1,5,54,9
	.half	.L982-.L649
	.byte	1,5,52,9
	.half	.L983-.L982
	.byte	1,5,56,9
	.half	.L646-.L983
	.byte	1,5,18,9
	.half	.L984-.L646
	.byte	3,1,1,5,34,9
	.half	.L985-.L984
	.byte	1,5,32,9
	.half	.L986-.L985
	.byte	1,5,23,9
	.half	.L987-.L986
	.byte	1,5,25,9
	.half	.L648-.L987
	.byte	3,1,1,5,33,9
	.half	.L651-.L648
	.byte	3,120,1,5,29,9
	.half	.L102-.L651
	.byte	1,5,5,7,9
	.half	.L988-.L102
	.byte	3,10,1,5,1,9
	.half	.L105-.L988
	.byte	3,1,1,7,9
	.half	.L225-.L105
	.byte	0,1,1
.L968:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_wave')
	.sect	'.debug_ranges'
.L224:
	.word	-1,.L157,0,.L225-.L157,0,0
	.sdecl	'.debug_info',debug,cluster('oled_show_chinese')
	.sect	'.debug_info'
.L226:
	.word	408
	.half	3
	.word	.L227
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L229,.L228
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_show_chinese',0,1,157,5,6,1,1,1
	.word	.L159,.L373,.L158
	.byte	4
	.byte	'x',0,1,157,5,32
	.word	.L276,.L374
	.byte	4
	.byte	'y',0,1,157,5,42
	.word	.L276,.L375
	.byte	4
	.byte	'size',0,1,157,5,51
	.word	.L261,.L376
	.byte	4
	.byte	'chinese_buffer',0,1,157,5,70
	.word	.L328,.L377
	.byte	4
	.byte	'number',0,1,157,5,92
	.word	.L261,.L378
	.byte	5
	.word	.L159,.L373
	.byte	5
	.word	.L379,.L373
	.byte	6
	.byte	'i',0,1,166,5,11
	.word	.L351,.L380
	.byte	6
	.byte	'j',0,1,166,5,18
	.word	.L351,.L381
	.byte	6
	.byte	'k',0,1,166,5,25
	.word	.L351,.L382
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_show_chinese')
	.sect	'.debug_abbrev'
.L227:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_show_chinese')
	.sect	'.debug_line'
.L228:
	.word	.L990-.L989
.L989:
	.half	3
	.word	.L992-.L991
.L991:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L992:
	.byte	5,6,7,0,5,2
	.word	.L159
	.byte	3,156,5,1,5,5,9
	.half	.L658-.L159
	.byte	3,5,1,9
	.half	.L653-.L658
	.byte	3,1,1,9
	.half	.L993-.L653
	.byte	3,1,1,9
	.half	.L379-.L993
	.byte	3,4,1,5,11,9
	.half	.L107-.L379
	.byte	3,1,1,5,26,9
	.half	.L660-.L107
	.byte	1,5,15,9
	.half	.L109-.L660
	.byte	3,2,1,5,34,9
	.half	.L662-.L109
	.byte	1,5,35,9
	.half	.L111-.L662
	.byte	3,2,1,5,49,9
	.half	.L665-.L111
	.byte	1,5,19,9
	.half	.L994-.L665
	.byte	3,1,1,5,30,9
	.half	.L667-.L994
	.byte	1,5,33,9
	.half	.L113-.L667
	.byte	3,2,1,5,32,9
	.half	.L995-.L113
	.byte	3,1,1,5,34,9
	.half	.L996-.L995
	.byte	3,125,1,5,24,9
	.half	.L112-.L996
	.byte	1,5,30,9
	.half	.L997-.L112
	.byte	1,5,38,7,9
	.half	.L998-.L997
	.byte	3,125,1,5,32,9
	.half	.L110-.L998
	.byte	1,5,30,9
	.half	.L999-.L110
	.byte	1,5,34,9
	.half	.L1000-.L999
	.byte	1,5,30,7,9
	.half	.L1001-.L1000
	.byte	3,126,1,5,26,9
	.half	.L108-.L1001
	.byte	1,5,5,7,9
	.half	.L1002-.L108
	.byte	3,12,1,5,1,9
	.half	.L115-.L1002
	.byte	3,1,1,7,9
	.half	.L230-.L115
	.byte	0,1,1
.L990:
	.sdecl	'.debug_ranges',debug,cluster('oled_show_chinese')
	.sect	'.debug_ranges'
.L229:
	.word	-1,.L159,0,.L230-.L159,0,0
	.sdecl	'.debug_info',debug,cluster('oled_init')
	.sect	'.debug_info'
.L231:
	.word	248
	.half	3
	.word	.L232
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L234,.L233
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_init',0,1,191,5,6,1,1,1
	.word	.L161,.L383,.L160
	.byte	4
	.word	.L161,.L383
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_init')
	.sect	'.debug_abbrev'
.L232:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_init')
	.sect	'.debug_line'
.L233:
	.word	.L1004-.L1003
.L1003:
	.half	3
	.word	.L1006-.L1005
.L1005:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L1006:
	.byte	5,6,7,0,5,2
	.word	.L161
	.byte	3,190,5,1,5,64,9
	.half	.L668-.L161
	.byte	3,5,1,5,77,9
	.half	.L1007-.L668
	.byte	1,5,93,9
	.half	.L1008-.L1007
	.byte	1,5,14,9
	.half	.L1009-.L1008
	.byte	1,5,24,9
	.half	.L1010-.L1009
	.byte	1,5,35,9
	.half	.L1011-.L1010
	.byte	1,5,51,9
	.half	.L1012-.L1011
	.byte	1,5,15,9
	.half	.L1013-.L1012
	.byte	3,2,1,5,29,9
	.half	.L1014-.L1013
	.byte	1,5,34,9
	.half	.L1015-.L1014
	.byte	1,5,45,9
	.half	.L1016-.L1015
	.byte	1,5,15,9
	.half	.L1017-.L1016
	.byte	3,1,1,5,29,9
	.half	.L1018-.L1017
	.byte	1,5,34,9
	.half	.L1019-.L1018
	.byte	1,5,45,9
	.half	.L1020-.L1019
	.byte	1,5,15,9
	.half	.L1021-.L1020
	.byte	3,1,1,5,29,9
	.half	.L1022-.L1021
	.byte	1,5,34,9
	.half	.L1023-.L1022
	.byte	1,5,45,9
	.half	.L1024-.L1023
	.byte	1,5,18,9
	.half	.L1025-.L1024
	.byte	3,2,1,5,5,9
	.half	.L1026-.L1025
	.byte	3,2,1,9
	.half	.L117-.L1026
	.byte	3,1,1,5,21,9
	.half	.L119-.L117
	.byte	3,1,1,5,5,9
	.half	.L1027-.L119
	.byte	3,1,1,5,24,9
	.half	.L121-.L1027
	.byte	3,2,1,9
	.half	.L1028-.L121
	.byte	3,1,1,9
	.half	.L1029-.L1028
	.byte	3,1,1,9
	.half	.L1030-.L1029
	.byte	3,1,1,9
	.half	.L1031-.L1030
	.byte	3,1,1,9
	.half	.L1032-.L1031
	.byte	3,1,1,9
	.half	.L1033-.L1032
	.byte	3,2,1,5,5,9
	.half	.L1034-.L1033
	.byte	1,5,28,7,9
	.half	.L1035-.L1034
	.byte	3,2,1,9
	.half	.L1036-.L1035
	.byte	3,1,1,5,33,9
	.half	.L1037-.L1036
	.byte	3,127,1,5,28,9
	.half	.L122-.L1037
	.byte	3,5,1,9
	.half	.L1038-.L122
	.byte	3,1,1,5,24,9
	.half	.L123-.L1038
	.byte	3,3,1,9
	.half	.L1039-.L123
	.byte	3,1,1,9
	.half	.L1040-.L1039
	.byte	3,1,1,9
	.half	.L1041-.L1040
	.byte	3,1,1,9
	.half	.L1042-.L1041
	.byte	3,1,1,9
	.half	.L1043-.L1042
	.byte	3,1,1,9
	.half	.L1044-.L1043
	.byte	3,1,1,9
	.half	.L1045-.L1044
	.byte	3,1,1,9
	.half	.L1046-.L1045
	.byte	3,1,1,9
	.half	.L1047-.L1046
	.byte	3,1,1,9
	.half	.L1048-.L1047
	.byte	3,1,1,9
	.half	.L1049-.L1048
	.byte	3,1,1,9
	.half	.L1050-.L1049
	.byte	3,1,1,9
	.half	.L1051-.L1050
	.byte	3,1,1,9
	.half	.L1052-.L1051
	.byte	3,1,1,9
	.half	.L1053-.L1052
	.byte	3,1,1,9
	.half	.L1054-.L1053
	.byte	3,1,1,9
	.half	.L1055-.L1054
	.byte	3,1,1,9
	.half	.L1056-.L1055
	.byte	3,1,1,9
	.half	.L1057-.L1056
	.byte	3,1,1,5,5,9
	.half	.L1058-.L1057
	.byte	3,1,1,5,15,9
	.half	.L125-.L1058
	.byte	3,2,1,5,25,9
	.half	.L1059-.L125
	.byte	3,1,1,5,28,9
	.half	.L1060-.L1059
	.byte	1,5,20,9
	.half	.L1061-.L1060
	.byte	3,1,1,5,1,9
	.half	.L1062-.L1061
	.byte	3,1,1,7,9
	.half	.L235-.L1062
	.byte	0,1,1
.L1004:
	.sdecl	'.debug_ranges',debug,cluster('oled_init')
	.sect	'.debug_ranges'
.L234:
	.word	-1,.L161,0,.L235-.L161,0,0
	.sdecl	'.debug_info',debug,cluster('oled_write_data')
	.sect	'.debug_info'
.L236:
	.word	269
	.half	3
	.word	.L237
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L239,.L238
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_write_data',0,1,75,13,1,1
	.word	.L127,.L384,.L126
	.byte	4
	.byte	'data',0,1,75,42
	.word	.L385,.L386
	.byte	5
	.word	.L127,.L384
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_write_data')
	.sect	'.debug_abbrev'
.L237:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_write_data')
	.sect	'.debug_line'
.L238:
	.word	.L1064-.L1063
.L1063:
	.half	3
	.word	.L1066-.L1065
.L1065:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L1066:
	.byte	5,13,7,0,5,2
	.word	.L127
	.byte	3,202,0,1,5,5,9
	.half	.L397-.L127
	.byte	3,2,1,9
	.half	.L3-.L397
	.byte	3,1,1,5,1,9
	.half	.L400-.L3
	.byte	3,1,1,7,9
	.half	.L240-.L400
	.byte	0,1,1
.L1064:
	.sdecl	'.debug_ranges',debug,cluster('oled_write_data')
	.sect	'.debug_ranges'
.L239:
	.word	-1,.L127,0,.L240-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('oled_write_command')
	.sect	'.debug_info'
.L241:
	.word	275
	.half	3
	.word	.L242
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L244,.L243
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_write_command',0,1,88,13,1,1
	.word	.L129,.L387,.L128
	.byte	4
	.byte	'command',0,1,88,45
	.word	.L388,.L389
	.byte	5
	.word	.L129,.L387
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_write_command')
	.sect	'.debug_abbrev'
.L242:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_write_command')
	.sect	'.debug_line'
.L243:
	.word	.L1068-.L1067
.L1067:
	.half	3
	.word	.L1070-.L1069
.L1069:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L1070:
	.byte	5,13,7,0,5,2
	.word	.L129
	.byte	3,215,0,1,5,5,9
	.half	.L402-.L129
	.byte	3,2,1,9
	.half	.L5-.L402
	.byte	3,1,1,5,1,9
	.half	.L405-.L5
	.byte	3,1,1,7,9
	.half	.L245-.L405
	.byte	0,1,1
.L1068:
	.sdecl	'.debug_ranges',debug,cluster('oled_write_command')
	.sect	'.debug_ranges'
.L244:
	.word	-1,.L129,0,.L245-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('oled_set_coordinate')
	.sect	'.debug_info'
.L246:
	.word	284
	.half	3
	.word	.L247
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L249,.L248
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_set_coordinate',0,1,102,13,1,1
	.word	.L131,.L390,.L130
	.byte	4
	.byte	'x',0,1,102,40
	.word	.L261,.L391
	.byte	4
	.byte	'y',0,1,102,49
	.word	.L261,.L392
	.byte	5
	.word	.L131,.L390
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_set_coordinate')
	.sect	'.debug_abbrev'
.L247:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_set_coordinate')
	.sect	'.debug_line'
.L248:
	.word	.L1072-.L1071
.L1071:
	.half	3
	.word	.L1074-.L1073
.L1073:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L1074:
	.byte	5,13,7,0,5,2
	.word	.L131
	.byte	3,229,0,1,5,5,9
	.half	.L409-.L131
	.byte	3,5,1,9
	.half	.L1075-.L409
	.byte	3,1,1,5,29,9
	.half	.L1076-.L1075
	.byte	3,2,1,5,28,9
	.half	.L1077-.L1076
	.byte	3,1,1,5,36,9
	.half	.L1078-.L1077
	.byte	1,5,42,9
	.half	.L1079-.L1078
	.byte	1,5,27,9
	.half	.L1080-.L1079
	.byte	3,1,1,5,1,9
	.half	.L1081-.L1080
	.byte	3,1,1,7,9
	.half	.L250-.L1081
	.byte	0,1,1
.L1072:
	.sdecl	'.debug_ranges',debug,cluster('oled_set_coordinate')
	.sect	'.debug_ranges'
.L249:
	.word	-1,.L131,0,.L250-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('oled_debug_init')
	.sect	'.debug_info'
.L251:
	.word	270
	.half	3
	.word	.L252
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L254,.L253
	.byte	2
	.word	.L162
	.byte	3
	.byte	'oled_debug_init',0,1,122,13,1,1
	.word	.L133,.L393,.L132
	.byte	4
	.word	.L133,.L393
	.byte	5
	.byte	'info',0,1,124,25
	.word	.L394,.L395
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('oled_debug_init')
	.sect	'.debug_abbrev'
.L252:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('oled_debug_init')
	.sect	'.debug_line'
.L253:
	.word	.L1083-.L1082
.L1082:
	.half	3
	.word	.L1085-.L1084
.L1084:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_oled.c',0,0,0,0,0
.L1085:
	.byte	5,13,7,0,5,2
	.word	.L133
	.byte	3,249,0,1,5,31,9
	.half	.L410-.L133
	.byte	3,3,1,5,23,9
	.half	.L1086-.L410
	.byte	3,2,1,5,21,9
	.half	.L1087-.L1086
	.byte	1,5,26,9
	.half	.L1088-.L1087
	.byte	3,2,1,5,24,9
	.half	.L1089-.L1088
	.byte	1,5,26,9
	.half	.L1090-.L1089
	.byte	3,1,1,5,24,9
	.half	.L1091-.L1090
	.byte	1,5,12,9
	.half	.L1092-.L1091
	.byte	3,1,1,5,14,9
	.half	.L1093-.L1092
	.byte	3,2,1,9
	.half	.L1094-.L1093
	.byte	3,5,1,9
	.half	.L1095-.L1094
	.byte	3,5,1,5,32,9
	.half	.L6-.L1095
	.byte	3,120,1,5,30,9
	.half	.L1096-.L6
	.byte	1,5,32,9
	.half	.L1097-.L1096
	.byte	3,1,1,5,30,9
	.half	.L1098-.L1097
	.byte	1,5,10,9
	.half	.L1099-.L1098
	.byte	3,1,1,5,32,9
	.half	.L7-.L1099
	.byte	3,3,1,5,30,9
	.half	.L1100-.L7
	.byte	1,5,32,9
	.half	.L1101-.L1100
	.byte	3,1,1,5,30,9
	.half	.L1102-.L1101
	.byte	1,5,10,9
	.half	.L1103-.L1102
	.byte	3,1,1,9
	.half	.L8-.L1103
	.byte	3,4,1,5,26,9
	.half	.L10-.L8
	.byte	3,2,1,5,24,9
	.half	.L1104-.L10
	.byte	1,5,32,9
	.half	.L1105-.L1104
	.byte	3,1,1,5,30,9
	.half	.L1106-.L1105
	.byte	1,5,24,9
	.half	.L1107-.L1106
	.byte	3,2,1,5,1,9
	.half	.L1108-.L1107
	.byte	3,1,1,7,9
	.half	.L255-.L1108
	.byte	0,1,1
.L1083:
	.sdecl	'.debug_ranges',debug,cluster('oled_debug_init')
	.sect	'.debug_ranges'
.L254:
	.word	-1,.L133,0,.L255-.L133,0,0
	.sdecl	'.debug_info',debug,cluster('oled_display_dir')
	.sect	'.debug_info'
.L256:
	.word	231
	.half	3
	.word	.L257
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L162
	.byte	3
	.byte	'oled_display_dir',0,12,65,29
	.word	.L270
	.byte	5,3
	.word	oled_display_dir
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('oled_display_dir')
	.sect	'.debug_abbrev'
.L257:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('oled_display_font')
	.sect	'.debug_info'
.L258:
	.word	232
	.half	3
	.word	.L259
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_oled.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L162
	.byte	3
	.byte	'oled_display_font',0,12,66,29
	.word	.L273
	.byte	5,3
	.word	oled_display_font
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('oled_display_font')
	.sect	'.debug_abbrev'
.L259:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('oled_clear')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L135,0,.L260-.L135
	.half	2
	.byte	138,0
	.word	0,0
.L263:
	.word	-1,.L135,.L412-.L135,.L15-.L135
	.half	1
	.byte	89
	.word	0,0
.L262:
	.word	-1,.L135,.L411-.L135,.L260-.L135
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_debug_init')
	.sect	'.debug_loc'
.L395:
	.word	-1,.L133,0,.L393-.L133
	.half	2
	.byte	145,104
	.word	0,0
.L132:
	.word	-1,.L133,0,.L410-.L133
	.half	2
	.byte	138,0
	.word	.L410-.L133,.L393-.L133
	.half	2
	.byte	138,24
	.word	.L393-.L133,.L393-.L133
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_draw_point')
	.sect	'.debug_loc'
.L280:
	.word	-1,.L143,0,.L420-.L143
	.half	1
	.byte	86
	.word	.L423-.L143,.L275-.L143
	.half	1
	.byte	90
	.word	.L433-.L143,.L434-.L143
	.half	1
	.byte	84
	.word	0,0
.L142:
	.word	-1,.L143,0,.L275-.L143
	.half	2
	.byte	138,0
	.word	0,0
.L277:
	.word	-1,.L143,0,.L421-.L143
	.half	1
	.byte	84
	.word	.L423-.L143,.L421-.L143
	.half	1
	.byte	89
	.word	.L30-.L143,.L425-.L143
	.half	1
	.byte	89
	.word	.L429-.L143,.L430-.L143
	.half	1
	.byte	89
	.word	.L431-.L143,.L432-.L143
	.half	1
	.byte	89
	.word	0,0
.L278:
	.word	-1,.L143,0,.L422-.L143
	.half	1
	.byte	85
	.word	.L420-.L143,.L424-.L143
	.half	1
	.byte	88
	.word	.L425-.L143,.L426-.L143
	.half	1
	.byte	88
	.word	.L427-.L143,.L428-.L143
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_full')
	.sect	'.debug_loc'
.L266:
	.word	-1,.L137,0,.L413-.L137
	.half	1
	.byte	84
	.word	.L414-.L137,.L264-.L137
	.half	1
	.byte	88
	.word	.L21-.L137,.L415-.L137
	.half	1
	.byte	84
	.word	.L418-.L137,.L419-.L137
	.half	1
	.byte	84
	.word	0,0
.L136:
	.word	-1,.L137,0,.L264-.L137
	.half	2
	.byte	138,0
	.word	0,0
.L268:
	.word	-1,.L137,.L417-.L137,.L23-.L137
	.half	1
	.byte	90
	.word	0,0
.L267:
	.word	-1,.L137,.L416-.L137,.L264-.L137
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_init')
	.sect	'.debug_loc'
.L160:
	.word	-1,.L161,0,.L668-.L161
	.half	2
	.byte	138,0
	.word	.L668-.L161,.L383-.L161
	.half	2
	.byte	138,16
	.word	.L383-.L161,.L383-.L161
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_set_coordinate')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L131,0,.L390-.L131
	.half	2
	.byte	138,0
	.word	0,0
.L391:
	.word	-1,.L131,0,.L406-.L131
	.half	1
	.byte	84
	.word	.L408-.L131,.L390-.L131
	.half	1
	.byte	95
	.word	0,0
.L392:
	.word	-1,.L131,0,.L407-.L131
	.half	1
	.byte	85
	.word	.L409-.L131,.L390-.L131
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_set_dir')
	.sect	'.debug_loc'
.L271:
	.word	-1,.L139,0,.L269-.L139
	.half	1
	.byte	84
	.word	0,0
.L138:
	.word	-1,.L139,0,.L269-.L139
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_set_font')
	.sect	'.debug_loc'
.L274:
	.word	-1,.L141,0,.L272-.L141
	.half	1
	.byte	84
	.word	0,0
.L140:
	.word	-1,.L141,0,.L272-.L141
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_binary_image')
	.sect	'.debug_loc'
.L338:
	.word	-1,.L153,.L560-.L153,.L561-.L153
	.half	1
	.byte	84
	.word	0,0
.L333:
	.word	-1,.L153,0,.L522-.L153
	.half	2
	.byte	145,4
	.word	.L535-.L153,.L536-.L153
	.half	1
	.byte	90
	.word	.L539-.L153,.L540-.L153
	.half	1
	.byte	95
	.word	.L522-.L153,.L325-.L153
	.half	2
	.byte	145,112
	.word	.L65-.L153,.L544-.L153
	.half	1
	.byte	95
	.word	.L549-.L153,.L67-.L153
	.half	1
	.byte	95
	.word	.L586-.L153,.L587-.L153
	.half	1
	.byte	95
	.word	0,0
.L332:
	.word	-1,.L153,0,.L523-.L153
	.half	2
	.byte	145,0
	.word	.L533-.L153,.L534-.L153
	.half	1
	.byte	88
	.word	.L541-.L153,.L542-.L153
	.half	1
	.byte	95
	.word	.L523-.L153,.L325-.L153
	.half	2
	.byte	145,104
	.word	.L67-.L153,.L554-.L153
	.half	1
	.byte	95
	.word	.L555-.L153,.L556-.L153
	.half	5
	.byte	144,32,157,32,0
	.word	.L584-.L153,.L585-.L153
	.half	1
	.byte	95
	.word	0,0
.L331:
	.word	-1,.L153,0,.L524-.L153
	.half	1
	.byte	87
	.word	.L532-.L153,.L325-.L153
	.half	2
	.byte	145,124
	.word	.L548-.L153,.L549-.L153
	.half	1
	.byte	95
	.word	0,0
.L340:
	.word	-1,.L153,.L552-.L153,.L64-.L153
	.half	1
	.byte	90
	.word	0,0
.L335:
	.word	-1,.L153,.L553-.L153,.L64-.L153
	.half	1
	.byte	88
	.word	0,0
.L329:
	.word	-1,.L153,0,.L525-.L153
	.half	1
	.byte	100
	.word	.L530-.L153,.L325-.L153
	.half	1
	.byte	108
	.word	0,0
.L336:
	.word	-1,.L153,.L542-.L153,.L65-.L153
	.half	1
	.byte	95
	.word	.L543-.L153,.L325-.L153
	.half	2
	.byte	145,108
	.word	.L65-.L153,.L545-.L153
	.half	5
	.byte	144,32,157,32,0
	.word	.L550-.L153,.L551-.L153
	.half	5
	.byte	144,32,157,32,0
	.word	.L585-.L153,.L64-.L153
	.half	1
	.byte	95
	.word	.L588-.L153,.L589-.L153
	.half	5
	.byte	144,32,157,32,0
	.word	.L78-.L153,.L590-.L153
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L152:
	.word	-1,.L153,0,.L521-.L153
	.half	2
	.byte	138,0
	.word	.L521-.L153,.L325-.L153
	.half	2
	.byte	138,24
	.word	.L325-.L153,.L325-.L153
	.half	2
	.byte	138,0
	.word	0,0
.L330:
	.word	-1,.L153,0,.L524-.L153
	.half	1
	.byte	86
	.word	.L531-.L153,.L325-.L153
	.half	1
	.byte	89
	.word	0,0
.L339:
	.word	-1,.L153,.L557-.L153,.L66-.L153
	.half	1
	.byte	92
	.word	0,0
.L326:
	.word	-1,.L153,0,.L526-.L153
	.half	1
	.byte	84
	.word	.L528-.L153,.L325-.L153
	.half	2
	.byte	145,120
	.word	.L526-.L153,.L537-.L153
	.half	1
	.byte	95
	.word	0,0
.L327:
	.word	-1,.L153,0,.L527-.L153
	.half	1
	.byte	85
	.word	.L529-.L153,.L325-.L153
	.half	2
	.byte	145,116
	.word	.L537-.L153,.L538-.L153
	.half	1
	.byte	95
	.word	.L546-.L153,.L547-.L153
	.half	1
	.byte	95
	.word	0,0
.L337:
	.word	-1,.L153,.L558-.L153,.L559-.L153
	.half	1
	.byte	94
	.word	.L562-.L153,.L563-.L153
	.half	1
	.byte	81
	.word	.L564-.L153,.L565-.L153
	.half	1
	.byte	94
	.word	.L565-.L153,.L566-.L153
	.half	1
	.byte	81
	.word	.L567-.L153,.L568-.L153
	.half	1
	.byte	94
	.word	.L568-.L153,.L569-.L153
	.half	1
	.byte	81
	.word	.L570-.L153,.L571-.L153
	.half	1
	.byte	94
	.word	.L571-.L153,.L572-.L153
	.half	1
	.byte	81
	.word	.L573-.L153,.L574-.L153
	.half	1
	.byte	94
	.word	.L574-.L153,.L575-.L153
	.half	1
	.byte	81
	.word	.L576-.L153,.L577-.L153
	.half	1
	.byte	94
	.word	.L577-.L153,.L578-.L153
	.half	1
	.byte	81
	.word	.L579-.L153,.L580-.L153
	.half	1
	.byte	94
	.word	.L580-.L153,.L581-.L153
	.half	1
	.byte	81
	.word	.L582-.L153,.L583-.L153
	.half	1
	.byte	94
	.word	.L583-.L153,.L561-.L153
	.half	1
	.byte	81
	.word	.L561-.L153,.L66-.L153
	.half	1
	.byte	94
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_chinese')
	.sect	'.debug_loc'
.L377:
	.word	-1,.L159,0,.L652-.L159
	.half	1
	.byte	100
	.word	.L657-.L159,.L373-.L159
	.half	1
	.byte	111
	.word	0,0
.L380:
	.word	-1,.L159,.L660-.L159,.L661-.L159
	.half	1
	.byte	91
	.word	.L108-.L159,.L373-.L159
	.half	1
	.byte	91
	.word	0,0
.L381:
	.word	-1,.L159,.L662-.L159,.L663-.L159
	.half	1
	.byte	93
	.word	.L110-.L159,.L108-.L159
	.half	1
	.byte	93
	.word	0,0
.L382:
	.word	-1,.L159,.L667-.L159,.L110-.L159
	.half	1
	.byte	94
	.word	0,0
.L378:
	.word	-1,.L159,0,.L653-.L159
	.half	1
	.byte	87
	.word	.L658-.L159,.L373-.L159
	.half	1
	.byte	92
	.word	0,0
.L158:
	.word	-1,.L159,0,.L373-.L159
	.half	2
	.byte	138,0
	.word	0,0
.L376:
	.word	-1,.L159,0,.L653-.L159
	.half	1
	.byte	86
	.word	.L656-.L159,.L373-.L159
	.half	1
	.byte	90
	.word	0,0
.L374:
	.word	-1,.L159,0,.L654-.L159
	.half	1
	.byte	84
	.word	.L658-.L159,.L654-.L159
	.half	1
	.byte	88
	.word	.L111-.L159,.L664-.L159
	.half	1
	.byte	88
	.word	0,0
.L375:
	.word	-1,.L159,0,.L655-.L159
	.half	1
	.byte	85
	.word	.L653-.L159,.L659-.L159
	.half	1
	.byte	89
	.word	.L665-.L159,.L666-.L159
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_float')
	.sect	'.debug_loc'
.L316:
	.word	-1,.L151,0,.L497-.L151
	.half	2
	.byte	144,35
	.word	.L508-.L151,.L509-.L151
	.half	2
	.byte	144,37
	.word	.L514-.L151,.L515-.L151
	.half	2
	.byte	144,37
	.word	0,0
.L321:
	.word	-1,.L151,.L516-.L151,.L517-.L151
	.half	2
	.byte	144,34
	.word	0,0
.L324:
	.word	-1,.L151,0,.L312-.L151
	.half	2
	.byte	145,104
	.word	0,0
.L317:
	.word	-1,.L151,0,.L312-.L151
	.half	2
	.byte	145,0
	.word	.L502-.L151,.L312-.L151
	.half	1
	.byte	89
	.word	0,0
.L322:
	.word	-1,.L151,.L505-.L151,.L61-.L151
	.half	2
	.byte	144,38
	.word	.L506-.L151,.L507-.L151
	.half	2
	.byte	144,38
	.word	.L510-.L151,.L511-.L151
	.half	2
	.byte	144,38
	.word	.L512-.L151,.L513-.L151
	.half	2
	.byte	144,38
	.word	0,0
.L150:
	.word	-1,.L151,0,.L496-.L151
	.half	2
	.byte	138,0
	.word	.L496-.L151,.L312-.L151
	.half	2
	.byte	138,24
	.word	.L312-.L151,.L312-.L151
	.half	2
	.byte	138,0
	.word	0,0
.L318:
	.word	-1,.L151,0,.L312-.L151
	.half	2
	.byte	145,4
	.word	.L503-.L151,.L312-.L151
	.half	1
	.byte	88
	.word	.L516-.L151,.L517-.L151
	.half	1
	.byte	86
	.word	0,0
.L313:
	.word	-1,.L151,0,.L498-.L151
	.half	1
	.byte	84
	.word	.L500-.L151,.L312-.L151
	.half	2
	.byte	145,124
	.word	.L498-.L151,.L504-.L151
	.half	1
	.byte	95
	.word	.L518-.L151,.L519-.L151
	.half	1
	.byte	84
	.word	0,0
.L314:
	.word	-1,.L151,0,.L499-.L151
	.half	1
	.byte	85
	.word	.L501-.L151,.L312-.L151
	.half	1
	.byte	94
	.word	.L520-.L151,.L519-.L151
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_gray_image')
	.sect	'.debug_loc'
.L354:
	.word	-1,.L155,.L621-.L155,.L620-.L155
	.half	1
	.byte	84
	.word	0,0
.L348:
	.word	-1,.L155,0,.L592-.L155
	.half	2
	.byte	145,4
	.word	.L603-.L155,.L604-.L155
	.half	1
	.byte	89
	.word	.L608-.L155,.L609-.L155
	.half	1
	.byte	95
	.word	.L592-.L155,.L341-.L155
	.half	2
	.byte	145,112
	.word	.L615-.L155,.L85-.L155
	.half	5
	.byte	144,32,157,32,0
	.word	.L622-.L155,.L623-.L155
	.half	1
	.byte	95
	.word	0,0
.L347:
	.word	-1,.L155,0,.L341-.L155
	.half	2
	.byte	145,0
	.word	.L602-.L155,.L341-.L155
	.half	1
	.byte	90
	.word	0,0
.L346:
	.word	-1,.L155,0,.L593-.L155
	.half	1
	.byte	87
	.word	.L601-.L155,.L341-.L155
	.half	2
	.byte	145,124
	.word	.L613-.L155,.L614-.L155
	.half	1
	.byte	95
	.word	0,0
.L356:
	.word	-1,.L155,.L616-.L155,.L82-.L155
	.half	1
	.byte	92
	.word	0,0
.L352:
	.word	-1,.L155,.L617-.L155,.L618-.L155
	.half	1
	.byte	94
	.word	.L84-.L155,.L82-.L155
	.half	1
	.byte	94
	.word	0,0
.L344:
	.word	-1,.L155,0,.L594-.L155
	.half	1
	.byte	100
	.word	.L599-.L155,.L341-.L155
	.half	1
	.byte	108
	.word	0,0
.L353:
	.word	-1,.L155,.L610-.L155,.L341-.L155
	.half	1
	.byte	89
	.word	0,0
.L154:
	.word	-1,.L155,0,.L591-.L155
	.half	2
	.byte	138,0
	.word	.L591-.L155,.L341-.L155
	.half	2
	.byte	138,16
	.word	.L341-.L155,.L341-.L155
	.half	2
	.byte	138,0
	.word	0,0
.L349:
	.word	-1,.L155,0,.L341-.L155
	.half	2
	.byte	145,8
	.word	.L605-.L155,.L341-.L155
	.half	1
	.byte	91
	.word	0,0
.L345:
	.word	-1,.L155,0,.L593-.L155
	.half	1
	.byte	86
	.word	.L600-.L155,.L341-.L155
	.half	1
	.byte	88
	.word	0,0
.L355:
	.word	-1,.L155,.L619-.L155,.L620-.L155
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L342:
	.word	-1,.L155,0,.L595-.L155
	.half	1
	.byte	84
	.word	.L597-.L155,.L341-.L155
	.half	2
	.byte	145,120
	.word	.L595-.L155,.L606-.L155
	.half	1
	.byte	95
	.word	0,0
.L343:
	.word	-1,.L155,0,.L596-.L155
	.half	1
	.byte	85
	.word	.L598-.L155,.L341-.L155
	.half	2
	.byte	145,116
	.word	.L606-.L155,.L607-.L155
	.half	1
	.byte	95
	.word	.L611-.L155,.L612-.L155
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_int')
	.sect	'.debug_loc'
.L293:
	.word	-1,.L147,0,.L471-.L147
	.half	1
	.byte	86
	.word	.L474-.L147,.L289-.L147
	.half	1
	.byte	93
	.word	.L478-.L147,.L479-.L147
	.half	1
	.byte	84
	.word	0,0
.L297:
	.word	0,0
.L300:
	.word	-1,.L147,0,.L289-.L147
	.half	2
	.byte	145,112
	.word	0,0
.L294:
	.word	-1,.L147,0,.L471-.L147
	.half	1
	.byte	87
	.word	.L475-.L147,.L289-.L147
	.half	1
	.byte	90
	.word	0,0
.L298:
	.word	-1,.L147,.L477-.L147,.L289-.L147
	.half	1
	.byte	95
	.word	0,0
.L146:
	.word	-1,.L147,0,.L470-.L147
	.half	2
	.byte	138,0
	.word	.L470-.L147,.L289-.L147
	.half	2
	.byte	138,16
	.word	.L289-.L147,.L289-.L147
	.half	2
	.byte	138,0
	.word	0,0
.L290:
	.word	-1,.L147,0,.L472-.L147
	.half	1
	.byte	84
	.word	.L475-.L147,.L472-.L147
	.half	1
	.byte	88
	.word	.L480-.L147,.L481-.L147
	.half	1
	.byte	88
	.word	0,0
.L291:
	.word	-1,.L147,0,.L473-.L147
	.half	1
	.byte	85
	.word	.L471-.L147,.L476-.L147
	.half	1
	.byte	89
	.word	.L480-.L147,.L481-.L147
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_string')
	.sect	'.debug_loc'
.L286:
	.word	-1,.L145,.L443-.L145,.L38-.L145
	.half	1
	.byte	91
	.word	.L454-.L145,.L39-.L145
	.half	1
	.byte	91
	.word	0,0
.L285:
	.word	-1,.L145,0,.L435-.L145
	.half	1
	.byte	100
	.word	.L438-.L145,.L281-.L145
	.half	1
	.byte	108
	.word	0,0
.L287:
	.word	-1,.L145,.L450-.L145,.L38-.L145
	.half	1
	.byte	95
	.word	.L461-.L145,.L462-.L145
	.half	1
	.byte	95
	.word	.L467-.L145,.L39-.L145
	.half	1
	.byte	95
	.word	0,0
.L288:
	.word	-1,.L145,.L441-.L145,.L442-.L145
	.half	1
	.byte	90
	.word	.L452-.L145,.L453-.L145
	.half	1
	.byte	90
	.word	.L469-.L145,.L281-.L145
	.half	1
	.byte	90
	.word	0,0
.L144:
	.word	-1,.L145,0,.L281-.L145
	.half	2
	.byte	138,0
	.word	0,0
.L282:
	.word	-1,.L145,0,.L436-.L145
	.half	1
	.byte	84
	.word	.L438-.L145,.L436-.L145
	.half	1
	.byte	88
	.word	.L444-.L145,.L445-.L145
	.half	1
	.byte	88
	.word	.L446-.L145,.L41-.L145
	.half	1
	.byte	88
	.word	.L447-.L145,.L448-.L145
	.half	1
	.byte	88
	.word	.L448-.L145,.L449-.L145
	.half	1
	.byte	84
	.word	.L451-.L145,.L38-.L145
	.half	1
	.byte	88
	.word	.L455-.L145,.L456-.L145
	.half	1
	.byte	88
	.word	.L457-.L145,.L45-.L145
	.half	1
	.byte	88
	.word	.L458-.L145,.L459-.L145
	.half	1
	.byte	88
	.word	.L459-.L145,.L460-.L145
	.half	1
	.byte	84
	.word	.L464-.L145,.L465-.L145
	.half	1
	.byte	88
	.word	.L465-.L145,.L466-.L145
	.half	1
	.byte	84
	.word	.L468-.L145,.L39-.L145
	.half	1
	.byte	88
	.word	0,0
.L283:
	.word	-1,.L145,0,.L437-.L145
	.half	1
	.byte	85
	.word	.L439-.L145,.L440-.L145
	.half	1
	.byte	89
	.word	.L41-.L145,.L447-.L145
	.half	1
	.byte	89
	.word	.L45-.L145,.L458-.L145
	.half	1
	.byte	89
	.word	.L463-.L145,.L462-.L145
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_uint')
	.sect	'.debug_loc'
.L305:
	.word	-1,.L149,0,.L483-.L149
	.half	1
	.byte	86
	.word	.L486-.L149,.L301-.L149
	.half	1
	.byte	93
	.word	.L492-.L149,.L493-.L149
	.half	1
	.byte	84
	.word	0,0
.L309:
	.word	0,0
.L311:
	.word	-1,.L149,0,.L301-.L149
	.half	2
	.byte	145,112
	.word	0,0
.L306:
	.word	-1,.L149,0,.L483-.L149
	.half	1
	.byte	87
	.word	.L487-.L149,.L301-.L149
	.half	1
	.byte	90
	.word	.L490-.L149,.L491-.L149
	.half	1
	.byte	85
	.word	0,0
.L310:
	.word	-1,.L149,.L489-.L149,.L301-.L149
	.half	1
	.byte	95
	.word	0,0
.L148:
	.word	-1,.L149,0,.L482-.L149
	.half	2
	.byte	138,0
	.word	.L482-.L149,.L301-.L149
	.half	2
	.byte	138,16
	.word	.L301-.L149,.L301-.L149
	.half	2
	.byte	138,0
	.word	0,0
.L302:
	.word	-1,.L149,0,.L484-.L149
	.half	1
	.byte	84
	.word	.L487-.L149,.L484-.L149
	.half	1
	.byte	88
	.word	.L494-.L149,.L495-.L149
	.half	1
	.byte	88
	.word	0,0
.L303:
	.word	-1,.L149,0,.L485-.L149
	.half	1
	.byte	85
	.word	.L483-.L149,.L488-.L149
	.half	1
	.byte	89
	.word	.L494-.L149,.L495-.L149
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_show_wave')
	.sect	'.debug_loc'
.L370:
	.word	-1,.L157,.L647-.L157,.L648-.L157
	.half	1
	.byte	94
	.word	.L648-.L157,.L651-.L157
	.half	1
	.byte	84
	.word	0,0
.L365:
	.word	-1,.L157,0,.L357-.L157
	.half	2
	.byte	145,4
	.word	.L633-.L157,.L357-.L157
	.half	1
	.byte	92
	.word	0,0
.L364:
	.word	-1,.L157,0,.L357-.L157
	.half	2
	.byte	145,0
	.word	.L632-.L157,.L357-.L157
	.half	1
	.byte	91
	.word	0,0
.L367:
	.word	-1,.L157,.L636-.L157,.L357-.L157
	.half	1
	.byte	93
	.word	0,0
.L156:
	.word	-1,.L157,0,.L624-.L157
	.half	2
	.byte	138,0
	.word	.L624-.L157,.L357-.L157
	.half	2
	.byte	138,8
	.word	.L357-.L157,.L357-.L157
	.half	2
	.byte	138,0
	.word	0,0
.L363:
	.word	-1,.L157,0,.L625-.L157
	.half	1
	.byte	87
	.word	.L631-.L157,.L357-.L157
	.half	1
	.byte	90
	.word	0,0
.L369:
	.word	-1,.L157,.L645-.L157,.L646-.L157
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L361:
	.word	-1,.L157,0,.L626-.L157
	.half	1
	.byte	100
	.word	.L629-.L157,.L357-.L157
	.half	1
	.byte	111
	.word	0,0
.L362:
	.word	-1,.L157,0,.L625-.L157
	.half	1
	.byte	86
	.word	.L630-.L157,.L357-.L157
	.half	2
	.byte	145,120
	.word	.L641-.L157,.L642-.L157
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L368:
	.word	-1,.L157,.L643-.L157,.L644-.L157
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L358:
	.word	-1,.L157,0,.L627-.L157
	.half	1
	.byte	84
	.word	.L633-.L157,.L627-.L157
	.half	1
	.byte	88
	.word	.L99-.L157,.L637-.L157
	.half	1
	.byte	88
	.word	.L647-.L157,.L649-.L157
	.half	1
	.byte	88
	.word	0,0
.L371:
	.word	-1,.L157,.L640-.L157,.L98-.L157
	.half	1
	.byte	95
	.word	0,0
.L359:
	.word	-1,.L157,0,.L628-.L157
	.half	1
	.byte	85
	.word	.L625-.L157,.L634-.L157
	.half	1
	.byte	89
	.word	.L638-.L157,.L639-.L157
	.half	1
	.byte	89
	.word	.L646-.L157,.L650-.L157
	.half	1
	.byte	89
	.word	0,0
.L372:
	.word	-1,.L157,.L635-.L157,.L636-.L157
	.half	1
	.byte	93
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_write_command')
	.sect	'.debug_loc'
.L389:
	.word	-1,.L129,0,.L401-.L129
	.half	1
	.byte	84
	.word	.L402-.L129,.L387-.L129
	.half	1
	.byte	88
	.word	.L4-.L129,.L403-.L129
	.half	1
	.byte	84
	.word	.L404-.L129,.L405-.L129
	.half	1
	.byte	85
	.word	0,0
.L128:
	.word	-1,.L129,0,.L387-.L129
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('oled_write_data')
	.sect	'.debug_loc'
.L386:
	.word	-1,.L127,0,.L396-.L127
	.half	1
	.byte	84
	.word	.L397-.L127,.L384-.L127
	.half	1
	.byte	88
	.word	.L2-.L127,.L398-.L127
	.half	1
	.byte	84
	.word	.L399-.L127,.L400-.L127
	.half	1
	.byte	85
	.word	0,0
.L126:
	.word	-1,.L127,0,.L384-.L127
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1109:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('oled_write_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L127,.L384-.L127
	.sdecl	'.debug_frame',debug,cluster('oled_write_command')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L129,.L387-.L129
	.sdecl	'.debug_frame',debug,cluster('oled_set_coordinate')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L131,.L390-.L131
	.sdecl	'.debug_frame',debug,cluster('oled_debug_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L133,.L393-.L133
	.byte	4
	.word	(.L410-.L133)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L393-.L410)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('oled_clear')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L135,.L260-.L135
	.sdecl	'.debug_frame',debug,cluster('oled_full')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L137,.L264-.L137
	.sdecl	'.debug_frame',debug,cluster('oled_set_dir')
	.sect	'.debug_frame'
	.word	24
	.word	.L1109,.L139,.L269-.L139
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('oled_set_font')
	.sect	'.debug_frame'
	.word	24
	.word	.L1109,.L141,.L272-.L141
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('oled_draw_point')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L143,.L275-.L143
	.sdecl	'.debug_frame',debug,cluster('oled_show_string')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L145,.L281-.L145
	.sdecl	'.debug_frame',debug,cluster('oled_show_int')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L147,.L289-.L147
	.byte	4
	.word	(.L470-.L147)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L289-.L470)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('oled_show_uint')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L149,.L301-.L149
	.byte	4
	.word	(.L482-.L149)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L301-.L482)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('oled_show_float')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L151,.L312-.L151
	.byte	4
	.word	(.L496-.L151)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L312-.L496)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('oled_show_binary_image')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L153,.L325-.L153
	.byte	4
	.word	(.L521-.L153)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L325-.L521)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('oled_show_gray_image')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L155,.L341-.L155
	.byte	4
	.word	(.L591-.L155)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L341-.L591)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('oled_show_wave')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L157,.L357-.L157
	.byte	4
	.word	(.L624-.L157)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L357-.L624)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('oled_show_chinese')
	.sect	'.debug_frame'
	.word	12
	.word	.L1109,.L159,.L373-.L159
	.sdecl	'.debug_frame',debug,cluster('oled_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1109,.L161,.L383-.L161
	.byte	4
	.word	(.L668-.L161)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L383-.L668)/2
	.byte	19,0,8,26,0,0
	; Module end
