/**
 * \file Ifx_LutAtan2F32.h
 * \brief Floating point signal, vector, and matrix library
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup library_srvsw_sysse_math_lut_atan2 ATAN lookup table
 * \ingroup library_srvsw_sysse_math_f32_lut
 */

#ifndef IFX_LUTATAN2F32_H
#define IFX_LUTATAN2F32_H
//----------------------------------------------------------------------------------------
#include "Ifx_Lut.h"

//----------------------------------------------------------------------------------------

#define IFX_LUTATAN2F32_SIZE (1024)           /**< \brief size of Ifx_LutAtan2F32 table */

//----------------------------------------------------------------------------------------

/** \brief Table I for Ifx_LutAtan2F32_fxpAngle() */
IFX_EXTERN IFX_LUT_TABLE Ifx_Lut_FxpAngle Ifx_g_LutAtan2F32_FxpAngle_table[IFX_LUTATAN2F32_SIZE + 1];
IFX_EXTERN IFX_LUT_TABLE float32          Ifx_g_LutAtan2F32_table[IFX_LUTATAN2F32_SIZE + 1];

/** \brief Initialise the lookup tables
 * \ingroup library_srvsw_sysse_math_lut_atan2
 */
IFX_EXTERN void Ifx_LutAtan2F32_init(void);

/**
 * \brief Look-up arcus tangent value of y/x.
 * \return fxpAngle 0 .. (IFX_LUT_ANGLE_RESOLUTION - 1), which represents 0 .. 2*IFX_PI
 * \ingroup library_srvsw_sysse_math_lut_atan2
 */
IFX_EXTERN Ifx_Lut_FxpAngle Ifx_LutAtan2F32_fxpAngle(float32 x, float32 y);
IFX_EXTERN float32          Ifx_LutAtan2F32_float32(float32 y, float32 x);

#endif
