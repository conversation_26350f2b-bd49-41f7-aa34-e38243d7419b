	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc24216a --dep-file=IfxAsclin_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxAsclin_cfg.IfxAsclin_cfg_indexMap',data,rom,cluster('IfxAsclin_cfg_indexMap')
	.sect	'.rodata.IfxAsclin_cfg.IfxAsclin_cfg_indexMap'
	.global	IfxAsclin_cfg_indexMap
	.align	4
IfxAsclin_cfg_indexMap:	.type	object
	.size	IfxAsclin_cfg_indexMap,32
	.word	-268433920
	.space	4
	.word	-268433664,1,-268433408,2
	.word	-268433152,3
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	8387
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	235
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	266
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	299
	.byte	4,1,5
	.word	326
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	328
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	351
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	382
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	419
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	235
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	470
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	498
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	531
	.byte	6
	.byte	'void',0,5
	.word	557
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	563
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	498
	.byte	7
	.word	557
	.byte	5
	.word	603
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	608
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	470
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	613
	.byte	10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	351
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	351
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	351
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	351
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	351
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	351
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	351
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	351
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	351
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	351
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	351
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	351
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	351
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	351
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	351
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	351
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	351
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	351
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	351
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	351
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	351
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	351
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	351
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	351
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	351
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	351
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	351
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,4,79,3
	.word	679
	.byte	10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	266
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,4,85,3
	.word	1242
	.byte	10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,4,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	382
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	351
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	351
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	351
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	351
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	351
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,4,97,3
	.word	1325
	.byte	10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,4,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	351
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	351
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	382
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	351
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_BRD_Bits',0,4,106,3
	.word	1537
	.byte	10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,4,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	382
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	351
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	382
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	351
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_BRG_Bits',0,4,115,3
	.word	1679
	.byte	10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,4,118,16,4,11
	.byte	'DISR',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	351
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	351
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	266
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_CLC_Bits',0,4,125,3
	.word	1824
	.byte	10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,4,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	351
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	266
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_CSR_Bits',0,4,133,1,3
	.word	1971
	.byte	10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,4,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	351
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	382
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	351
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	351
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	351
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	351
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	351
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,4,145,1,3
	.word	2083
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,4,148,1,16,4,11
	.byte	'TH',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	351
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	351
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	351
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	351
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	351
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	382
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	351
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	351
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	351
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	351
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	351
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	351
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	351
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	351
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	351
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	351
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	351
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	351
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	351
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	351
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	351
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	351
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,4,177,1,3
	.word	2272
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,4,180,1,16,4,11
	.byte	'THC',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	351
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	351
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	351
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	351
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	351
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	382
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	351
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	351
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	351
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	351
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	351
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	351
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	351
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	351
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	351
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	351
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	351
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	351
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	351
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	351
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	351
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	351
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,4,209,1,3
	.word	2749
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,4,212,1,16,4,11
	.byte	'THE',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	351
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	351
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	351
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	351
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	351
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	382
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	351
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	351
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	351
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	351
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	351
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	351
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	351
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	351
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	351
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	351
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	351
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	351
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	351
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,4,238,1,3
	.word	3260
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,4,241,1,16,4,11
	.byte	'THS',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	351
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	351
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	351
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	351
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	351
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	382
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	351
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	351
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	351
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	351
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	351
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	351
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	351
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	351
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	351
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	351
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	351
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	351
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	351
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	351
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	351
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	351
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,4,142,2,3
	.word	3722
	.byte	10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,4,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	351
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	382
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	351
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	351
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	351
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	351
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	382
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	351
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,4,158,2,3
	.word	4229
	.byte	10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,4,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	351
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	351
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	382
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_ASCLIN_ID_Bits',0,4,166,2,3
	.word	4488
	.byte	10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,4,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	351
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	351
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	382
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	351
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	351
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	382
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	351
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	351
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	351
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	351
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,4,184,2,3
	.word	4601
	.byte	10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,4,187,2,16,4,11
	.byte	'RST',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	266
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,4,192,2,3
	.word	4886
	.byte	10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,4,195,2,16,4,11
	.byte	'RST',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	266
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,4,199,2,3
	.word	5003
	.byte	10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,4,202,2,16,4,11
	.byte	'CLR',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	266
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,4,206,2,3
	.word	5101
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,4,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	351
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	266
	.byte	26,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,4,213,2,3
	.word	5203
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	266
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	351
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	351
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	351
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	351
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	351
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	351
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,4,225,2,3
	.word	5313
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,4,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	351
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	266
	.byte	24,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,4,232,2,3
	.word	5506
	.byte	10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,4,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	266
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	351
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	351
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	351
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	351
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_OCS_Bits',0,4,242,2,3
	.word	5617
	.byte	10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,4,245,2,16,4,11
	.byte	'DATA',0,4
	.word	266
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,4,248,2,3
	.word	5769
	.byte	10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,4,251,2,16,4,11
	.byte	'DATA',0,4
	.word	266
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,4,254,2,3
	.word	5848
	.byte	10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,4,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	351
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	351
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	351
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	351
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	351
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	382
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	351
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,4,140,3,3
	.word	5929
	.byte	10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,4,143,3,16,4,11
	.byte	'DATA',0,4
	.word	266
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,4,146,3,3
	.word	6165
	.byte	10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,4,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	351
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	351
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	351
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	351
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	351
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	351
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	351
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	382
	.byte	11,0,2,35,2,0,3
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,4,159,3,3
	.word	6244
	.byte	12,4,167,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	679
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_ACCEN0',0,4,172,3,3
	.word	6464
	.byte	12,4,175,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1242
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_ACCEN1',0,4,180,3,3
	.word	6531
	.byte	12,4,183,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1325
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_BITCON',0,4,188,3,3
	.word	6598
	.byte	12,4,191,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1537
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_BRD',0,4,196,3,3
	.word	6665
	.byte	12,4,199,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1679
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_BRG',0,4,204,3,3
	.word	6729
	.byte	12,4,207,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1824
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_CLC',0,4,212,3,3
	.word	6793
	.byte	12,4,215,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1971
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_CSR',0,4,220,3,3
	.word	6857
	.byte	12,4,223,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2083
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_DATCON',0,4,228,3,3
	.word	6921
	.byte	12,4,231,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2272
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_FLAGS',0,4,236,3,3
	.word	6988
	.byte	12,4,239,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2749
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,4,244,3,3
	.word	7054
	.byte	12,4,247,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3260
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,4,252,3,3
	.word	7125
	.byte	12,4,255,3,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3722
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_FLAGSSET',0,4,132,4,3
	.word	7197
	.byte	12,4,135,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4229
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_FRAMECON',0,4,140,4,3
	.word	7266
	.byte	12,4,143,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4488
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_ID',0,4,148,4,3
	.word	7335
	.byte	12,4,151,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4601
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_IOCR',0,4,156,4,3
	.word	7398
	.byte	12,4,159,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4886
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_KRST0',0,4,164,4,3
	.word	7463
	.byte	12,4,167,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5003
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_KRST1',0,4,172,4,3
	.word	7529
	.byte	12,4,175,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5101
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_KRSTCLR',0,4,180,4,3
	.word	7595
	.byte	12,4,183,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5203
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,4,188,4,3
	.word	7663
	.byte	12,4,191,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5313
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_LIN_CON',0,4,196,4,3
	.word	7734
	.byte	12,4,199,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5506
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,4,204,4,3
	.word	7802
	.byte	12,4,207,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5617
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_OCS',0,4,212,4,3
	.word	7873
	.byte	12,4,215,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5769
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_RXDATA',0,4,220,4,3
	.word	7937
	.byte	12,4,223,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5848
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_RXDATAD',0,4,228,4,3
	.word	8004
	.byte	12,4,231,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5929
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,4,236,4,3
	.word	8072
	.byte	12,4,239,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6165
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_TXDATA',0,4,244,4,3
	.word	8142
	.byte	12,4,247,4,9,4,9
	.byte	'U',0
	.word	266
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	299
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6244
	.byte	4,2,35,0,0,3
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,4,252,4,3
	.word	8209
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN',0,4,135,5,25,12,9
	.byte	'CON',0
	.word	7734
	.byte	4,2,35,0,9
	.byte	'BTIMER',0
	.word	7663
	.byte	4,2,35,4,9
	.byte	'HTIMER',0
	.word	7802
	.byte	4,2,35,8,0,7
	.word	8279
	.byte	3
	.byte	'Ifx_ASCLIN_LIN',0,4,140,5,3
	.word	8347
	.byte	13,32
	.word	613
	.byte	14,3,0
.L8:
	.byte	15
	.word	8376
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	271
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxAsclin_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin_cfg_indexMap',0,1,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxAsclin_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
