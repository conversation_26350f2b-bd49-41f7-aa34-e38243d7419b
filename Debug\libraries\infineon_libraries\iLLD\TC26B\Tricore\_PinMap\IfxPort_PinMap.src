	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc33552a --dep-file=IfxPort_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c'

	
$TC16X
	
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_0',data,cluster('IfxPort_P00_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_0'
	.global	IfxPort_P00_0
	.align	4
IfxPort_P00_0:	.type	object
	.size	IfxPort_P00_0,8
	.word	-268197888
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_1',data,cluster('IfxPort_P00_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_1'
	.global	IfxPort_P00_1
	.align	4
IfxPort_P00_1:	.type	object
	.size	IfxPort_P00_1,8
	.word	-268197888
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_10',data,cluster('IfxPort_P00_10')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_10'
	.global	IfxPort_P00_10
	.align	4
IfxPort_P00_10:	.type	object
	.size	IfxPort_P00_10,8
	.word	-268197888
	.byte	10
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_11',data,cluster('IfxPort_P00_11')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_11'
	.global	IfxPort_P00_11
	.align	4
IfxPort_P00_11:	.type	object
	.size	IfxPort_P00_11,8
	.word	-268197888
	.byte	11
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_12',data,cluster('IfxPort_P00_12')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_12'
	.global	IfxPort_P00_12
	.align	4
IfxPort_P00_12:	.type	object
	.size	IfxPort_P00_12,8
	.word	-268197888
	.byte	12
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_2',data,cluster('IfxPort_P00_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_2'
	.global	IfxPort_P00_2
	.align	4
IfxPort_P00_2:	.type	object
	.size	IfxPort_P00_2,8
	.word	-268197888
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_3',data,cluster('IfxPort_P00_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_3'
	.global	IfxPort_P00_3
	.align	4
IfxPort_P00_3:	.type	object
	.size	IfxPort_P00_3,8
	.word	-268197888
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_4',data,cluster('IfxPort_P00_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_4'
	.global	IfxPort_P00_4
	.align	4
IfxPort_P00_4:	.type	object
	.size	IfxPort_P00_4,8
	.word	-268197888
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_5',data,cluster('IfxPort_P00_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_5'
	.global	IfxPort_P00_5
	.align	4
IfxPort_P00_5:	.type	object
	.size	IfxPort_P00_5,8
	.word	-268197888
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_6',data,cluster('IfxPort_P00_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_6'
	.global	IfxPort_P00_6
	.align	4
IfxPort_P00_6:	.type	object
	.size	IfxPort_P00_6,8
	.word	-268197888
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_7',data,cluster('IfxPort_P00_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_7'
	.global	IfxPort_P00_7
	.align	4
IfxPort_P00_7:	.type	object
	.size	IfxPort_P00_7,8
	.word	-268197888
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_8',data,cluster('IfxPort_P00_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_8'
	.global	IfxPort_P00_8
	.align	4
IfxPort_P00_8:	.type	object
	.size	IfxPort_P00_8,8
	.word	-268197888
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P00_9',data,cluster('IfxPort_P00_9')
	.sect	'.data.IfxPort_PinMap.IfxPort_P00_9'
	.global	IfxPort_P00_9
	.align	4
IfxPort_P00_9:	.type	object
	.size	IfxPort_P00_9,8
	.word	-268197888
	.byte	9
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_0',data,cluster('IfxPort_P02_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_0'
	.global	IfxPort_P02_0
	.align	4
IfxPort_P02_0:	.type	object
	.size	IfxPort_P02_0,8
	.word	-268197376
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_1',data,cluster('IfxPort_P02_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_1'
	.global	IfxPort_P02_1
	.align	4
IfxPort_P02_1:	.type	object
	.size	IfxPort_P02_1,8
	.word	-268197376
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_2',data,cluster('IfxPort_P02_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_2'
	.global	IfxPort_P02_2
	.align	4
IfxPort_P02_2:	.type	object
	.size	IfxPort_P02_2,8
	.word	-268197376
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_3',data,cluster('IfxPort_P02_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_3'
	.global	IfxPort_P02_3
	.align	4
IfxPort_P02_3:	.type	object
	.size	IfxPort_P02_3,8
	.word	-268197376
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_4',data,cluster('IfxPort_P02_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_4'
	.global	IfxPort_P02_4
	.align	4
IfxPort_P02_4:	.type	object
	.size	IfxPort_P02_4,8
	.word	-268197376
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_5',data,cluster('IfxPort_P02_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_5'
	.global	IfxPort_P02_5
	.align	4
IfxPort_P02_5:	.type	object
	.size	IfxPort_P02_5,8
	.word	-268197376
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_6',data,cluster('IfxPort_P02_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_6'
	.global	IfxPort_P02_6
	.align	4
IfxPort_P02_6:	.type	object
	.size	IfxPort_P02_6,8
	.word	-268197376
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_7',data,cluster('IfxPort_P02_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_7'
	.global	IfxPort_P02_7
	.align	4
IfxPort_P02_7:	.type	object
	.size	IfxPort_P02_7,8
	.word	-268197376
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P02_8',data,cluster('IfxPort_P02_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P02_8'
	.global	IfxPort_P02_8
	.align	4
IfxPort_P02_8:	.type	object
	.size	IfxPort_P02_8,8
	.word	-268197376
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_0',data,cluster('IfxPort_P10_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_0'
	.global	IfxPort_P10_0
	.align	4
IfxPort_P10_0:	.type	object
	.size	IfxPort_P10_0,8
	.word	-268193792
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_1',data,cluster('IfxPort_P10_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_1'
	.global	IfxPort_P10_1
	.align	4
IfxPort_P10_1:	.type	object
	.size	IfxPort_P10_1,8
	.word	-268193792
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_2',data,cluster('IfxPort_P10_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_2'
	.global	IfxPort_P10_2
	.align	4
IfxPort_P10_2:	.type	object
	.size	IfxPort_P10_2,8
	.word	-268193792
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_3',data,cluster('IfxPort_P10_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_3'
	.global	IfxPort_P10_3
	.align	4
IfxPort_P10_3:	.type	object
	.size	IfxPort_P10_3,8
	.word	-268193792
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_4',data,cluster('IfxPort_P10_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_4'
	.global	IfxPort_P10_4
	.align	4
IfxPort_P10_4:	.type	object
	.size	IfxPort_P10_4,8
	.word	-268193792
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_5',data,cluster('IfxPort_P10_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_5'
	.global	IfxPort_P10_5
	.align	4
IfxPort_P10_5:	.type	object
	.size	IfxPort_P10_5,8
	.word	-268193792
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_6',data,cluster('IfxPort_P10_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_6'
	.global	IfxPort_P10_6
	.align	4
IfxPort_P10_6:	.type	object
	.size	IfxPort_P10_6,8
	.word	-268193792
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_7',data,cluster('IfxPort_P10_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_7'
	.global	IfxPort_P10_7
	.align	4
IfxPort_P10_7:	.type	object
	.size	IfxPort_P10_7,8
	.word	-268193792
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P10_8',data,cluster('IfxPort_P10_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P10_8'
	.global	IfxPort_P10_8
	.align	4
IfxPort_P10_8:	.type	object
	.size	IfxPort_P10_8,8
	.word	-268193792
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P11_10',data,cluster('IfxPort_P11_10')
	.sect	'.data.IfxPort_PinMap.IfxPort_P11_10'
	.global	IfxPort_P11_10
	.align	4
IfxPort_P11_10:	.type	object
	.size	IfxPort_P11_10,8
	.word	-268193536
	.byte	10
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P11_11',data,cluster('IfxPort_P11_11')
	.sect	'.data.IfxPort_PinMap.IfxPort_P11_11'
	.global	IfxPort_P11_11
	.align	4
IfxPort_P11_11:	.type	object
	.size	IfxPort_P11_11,8
	.word	-268193536
	.byte	11
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P11_12',data,cluster('IfxPort_P11_12')
	.sect	'.data.IfxPort_PinMap.IfxPort_P11_12'
	.global	IfxPort_P11_12
	.align	4
IfxPort_P11_12:	.type	object
	.size	IfxPort_P11_12,8
	.word	-268193536
	.byte	12
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P11_2',data,cluster('IfxPort_P11_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P11_2'
	.global	IfxPort_P11_2
	.align	4
IfxPort_P11_2:	.type	object
	.size	IfxPort_P11_2,8
	.word	-268193536
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P11_3',data,cluster('IfxPort_P11_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P11_3'
	.global	IfxPort_P11_3
	.align	4
IfxPort_P11_3:	.type	object
	.size	IfxPort_P11_3,8
	.word	-268193536
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P11_6',data,cluster('IfxPort_P11_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P11_6'
	.global	IfxPort_P11_6
	.align	4
IfxPort_P11_6:	.type	object
	.size	IfxPort_P11_6,8
	.word	-268193536
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P11_9',data,cluster('IfxPort_P11_9')
	.sect	'.data.IfxPort_PinMap.IfxPort_P11_9'
	.global	IfxPort_P11_9
	.align	4
IfxPort_P11_9:	.type	object
	.size	IfxPort_P11_9,8
	.word	-268193536
	.byte	9
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P13_0',data,cluster('IfxPort_P13_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P13_0'
	.global	IfxPort_P13_0
	.align	4
IfxPort_P13_0:	.type	object
	.size	IfxPort_P13_0,8
	.word	-268193024
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P13_1',data,cluster('IfxPort_P13_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P13_1'
	.global	IfxPort_P13_1
	.align	4
IfxPort_P13_1:	.type	object
	.size	IfxPort_P13_1,8
	.word	-268193024
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P13_2',data,cluster('IfxPort_P13_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P13_2'
	.global	IfxPort_P13_2
	.align	4
IfxPort_P13_2:	.type	object
	.size	IfxPort_P13_2,8
	.word	-268193024
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P13_3',data,cluster('IfxPort_P13_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P13_3'
	.global	IfxPort_P13_3
	.align	4
IfxPort_P13_3:	.type	object
	.size	IfxPort_P13_3,8
	.word	-268193024
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_0',data,cluster('IfxPort_P14_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_0'
	.global	IfxPort_P14_0
	.align	4
IfxPort_P14_0:	.type	object
	.size	IfxPort_P14_0,8
	.word	-268192768
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_1',data,cluster('IfxPort_P14_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_1'
	.global	IfxPort_P14_1
	.align	4
IfxPort_P14_1:	.type	object
	.size	IfxPort_P14_1,8
	.word	-268192768
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_10',data,cluster('IfxPort_P14_10')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_10'
	.global	IfxPort_P14_10
	.align	4
IfxPort_P14_10:	.type	object
	.size	IfxPort_P14_10,8
	.word	-268192768
	.byte	10
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_2',data,cluster('IfxPort_P14_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_2'
	.global	IfxPort_P14_2
	.align	4
IfxPort_P14_2:	.type	object
	.size	IfxPort_P14_2,8
	.word	-268192768
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_3',data,cluster('IfxPort_P14_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_3'
	.global	IfxPort_P14_3
	.align	4
IfxPort_P14_3:	.type	object
	.size	IfxPort_P14_3,8
	.word	-268192768
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_4',data,cluster('IfxPort_P14_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_4'
	.global	IfxPort_P14_4
	.align	4
IfxPort_P14_4:	.type	object
	.size	IfxPort_P14_4,8
	.word	-268192768
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_5',data,cluster('IfxPort_P14_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_5'
	.global	IfxPort_P14_5
	.align	4
IfxPort_P14_5:	.type	object
	.size	IfxPort_P14_5,8
	.word	-268192768
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_6',data,cluster('IfxPort_P14_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_6'
	.global	IfxPort_P14_6
	.align	4
IfxPort_P14_6:	.type	object
	.size	IfxPort_P14_6,8
	.word	-268192768
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_7',data,cluster('IfxPort_P14_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_7'
	.global	IfxPort_P14_7
	.align	4
IfxPort_P14_7:	.type	object
	.size	IfxPort_P14_7,8
	.word	-268192768
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_8',data,cluster('IfxPort_P14_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_8'
	.global	IfxPort_P14_8
	.align	4
IfxPort_P14_8:	.type	object
	.size	IfxPort_P14_8,8
	.word	-268192768
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P14_9',data,cluster('IfxPort_P14_9')
	.sect	'.data.IfxPort_PinMap.IfxPort_P14_9'
	.global	IfxPort_P14_9
	.align	4
IfxPort_P14_9:	.type	object
	.size	IfxPort_P14_9,8
	.word	-268192768
	.byte	9
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_0',data,cluster('IfxPort_P15_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_0'
	.global	IfxPort_P15_0
	.align	4
IfxPort_P15_0:	.type	object
	.size	IfxPort_P15_0,8
	.word	-268192512
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_1',data,cluster('IfxPort_P15_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_1'
	.global	IfxPort_P15_1
	.align	4
IfxPort_P15_1:	.type	object
	.size	IfxPort_P15_1,8
	.word	-268192512
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_2',data,cluster('IfxPort_P15_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_2'
	.global	IfxPort_P15_2
	.align	4
IfxPort_P15_2:	.type	object
	.size	IfxPort_P15_2,8
	.word	-268192512
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_3',data,cluster('IfxPort_P15_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_3'
	.global	IfxPort_P15_3
	.align	4
IfxPort_P15_3:	.type	object
	.size	IfxPort_P15_3,8
	.word	-268192512
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_4',data,cluster('IfxPort_P15_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_4'
	.global	IfxPort_P15_4
	.align	4
IfxPort_P15_4:	.type	object
	.size	IfxPort_P15_4,8
	.word	-268192512
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_5',data,cluster('IfxPort_P15_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_5'
	.global	IfxPort_P15_5
	.align	4
IfxPort_P15_5:	.type	object
	.size	IfxPort_P15_5,8
	.word	-268192512
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_6',data,cluster('IfxPort_P15_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_6'
	.global	IfxPort_P15_6
	.align	4
IfxPort_P15_6:	.type	object
	.size	IfxPort_P15_6,8
	.word	-268192512
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_7',data,cluster('IfxPort_P15_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_7'
	.global	IfxPort_P15_7
	.align	4
IfxPort_P15_7:	.type	object
	.size	IfxPort_P15_7,8
	.word	-268192512
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P15_8',data,cluster('IfxPort_P15_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P15_8'
	.global	IfxPort_P15_8
	.align	4
IfxPort_P15_8:	.type	object
	.size	IfxPort_P15_8,8
	.word	-268192512
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_0',data,cluster('IfxPort_P20_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_0'
	.global	IfxPort_P20_0
	.align	4
IfxPort_P20_0:	.type	object
	.size	IfxPort_P20_0,8
	.word	-268189696
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_1',data,cluster('IfxPort_P20_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_1'
	.global	IfxPort_P20_1
	.align	4
IfxPort_P20_1:	.type	object
	.size	IfxPort_P20_1,8
	.word	-268189696
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_10',data,cluster('IfxPort_P20_10')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_10'
	.global	IfxPort_P20_10
	.align	4
IfxPort_P20_10:	.type	object
	.size	IfxPort_P20_10,8
	.word	-268189696
	.byte	10
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_11',data,cluster('IfxPort_P20_11')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_11'
	.global	IfxPort_P20_11
	.align	4
IfxPort_P20_11:	.type	object
	.size	IfxPort_P20_11,8
	.word	-268189696
	.byte	11
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_12',data,cluster('IfxPort_P20_12')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_12'
	.global	IfxPort_P20_12
	.align	4
IfxPort_P20_12:	.type	object
	.size	IfxPort_P20_12,8
	.word	-268189696
	.byte	12
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_13',data,cluster('IfxPort_P20_13')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_13'
	.global	IfxPort_P20_13
	.align	4
IfxPort_P20_13:	.type	object
	.size	IfxPort_P20_13,8
	.word	-268189696
	.byte	13
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_14',data,cluster('IfxPort_P20_14')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_14'
	.global	IfxPort_P20_14
	.align	4
IfxPort_P20_14:	.type	object
	.size	IfxPort_P20_14,8
	.word	-268189696
	.byte	14
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_3',data,cluster('IfxPort_P20_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_3'
	.global	IfxPort_P20_3
	.align	4
IfxPort_P20_3:	.type	object
	.size	IfxPort_P20_3,8
	.word	-268189696
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_6',data,cluster('IfxPort_P20_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_6'
	.global	IfxPort_P20_6
	.align	4
IfxPort_P20_6:	.type	object
	.size	IfxPort_P20_6,8
	.word	-268189696
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_7',data,cluster('IfxPort_P20_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_7'
	.global	IfxPort_P20_7
	.align	4
IfxPort_P20_7:	.type	object
	.size	IfxPort_P20_7,8
	.word	-268189696
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_8',data,cluster('IfxPort_P20_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_8'
	.global	IfxPort_P20_8
	.align	4
IfxPort_P20_8:	.type	object
	.size	IfxPort_P20_8,8
	.word	-268189696
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P20_9',data,cluster('IfxPort_P20_9')
	.sect	'.data.IfxPort_PinMap.IfxPort_P20_9'
	.global	IfxPort_P20_9
	.align	4
IfxPort_P20_9:	.type	object
	.size	IfxPort_P20_9,8
	.word	-268189696
	.byte	9
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_0',data,cluster('IfxPort_P21_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_0'
	.global	IfxPort_P21_0
	.align	4
IfxPort_P21_0:	.type	object
	.size	IfxPort_P21_0,8
	.word	-268189440
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_1',data,cluster('IfxPort_P21_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_1'
	.global	IfxPort_P21_1
	.align	4
IfxPort_P21_1:	.type	object
	.size	IfxPort_P21_1,8
	.word	-268189440
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_2',data,cluster('IfxPort_P21_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_2'
	.global	IfxPort_P21_2
	.align	4
IfxPort_P21_2:	.type	object
	.size	IfxPort_P21_2,8
	.word	-268189440
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_3',data,cluster('IfxPort_P21_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_3'
	.global	IfxPort_P21_3
	.align	4
IfxPort_P21_3:	.type	object
	.size	IfxPort_P21_3,8
	.word	-268189440
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_4',data,cluster('IfxPort_P21_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_4'
	.global	IfxPort_P21_4
	.align	4
IfxPort_P21_4:	.type	object
	.size	IfxPort_P21_4,8
	.word	-268189440
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_5',data,cluster('IfxPort_P21_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_5'
	.global	IfxPort_P21_5
	.align	4
IfxPort_P21_5:	.type	object
	.size	IfxPort_P21_5,8
	.word	-268189440
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_6',data,cluster('IfxPort_P21_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_6'
	.global	IfxPort_P21_6
	.align	4
IfxPort_P21_6:	.type	object
	.size	IfxPort_P21_6,8
	.word	-268189440
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P21_7',data,cluster('IfxPort_P21_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P21_7'
	.global	IfxPort_P21_7
	.align	4
IfxPort_P21_7:	.type	object
	.size	IfxPort_P21_7,8
	.word	-268189440
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P22_0',data,cluster('IfxPort_P22_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P22_0'
	.global	IfxPort_P22_0
	.align	4
IfxPort_P22_0:	.type	object
	.size	IfxPort_P22_0,8
	.word	-268189184
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P22_1',data,cluster('IfxPort_P22_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P22_1'
	.global	IfxPort_P22_1
	.align	4
IfxPort_P22_1:	.type	object
	.size	IfxPort_P22_1,8
	.word	-268189184
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P22_2',data,cluster('IfxPort_P22_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P22_2'
	.global	IfxPort_P22_2
	.align	4
IfxPort_P22_2:	.type	object
	.size	IfxPort_P22_2,8
	.word	-268189184
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P22_3',data,cluster('IfxPort_P22_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P22_3'
	.global	IfxPort_P22_3
	.align	4
IfxPort_P22_3:	.type	object
	.size	IfxPort_P22_3,8
	.word	-268189184
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P23_0',data,cluster('IfxPort_P23_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P23_0'
	.global	IfxPort_P23_0
	.align	4
IfxPort_P23_0:	.type	object
	.size	IfxPort_P23_0,8
	.word	-268188928
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P23_1',data,cluster('IfxPort_P23_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P23_1'
	.global	IfxPort_P23_1
	.align	4
IfxPort_P23_1:	.type	object
	.size	IfxPort_P23_1,8
	.word	-268188928
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P23_2',data,cluster('IfxPort_P23_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P23_2'
	.global	IfxPort_P23_2
	.align	4
IfxPort_P23_2:	.type	object
	.size	IfxPort_P23_2,8
	.word	-268188928
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P23_3',data,cluster('IfxPort_P23_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P23_3'
	.global	IfxPort_P23_3
	.align	4
IfxPort_P23_3:	.type	object
	.size	IfxPort_P23_3,8
	.word	-268188928
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P23_4',data,cluster('IfxPort_P23_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P23_4'
	.global	IfxPort_P23_4
	.align	4
IfxPort_P23_4:	.type	object
	.size	IfxPort_P23_4,8
	.word	-268188928
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P23_5',data,cluster('IfxPort_P23_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P23_5'
	.global	IfxPort_P23_5
	.align	4
IfxPort_P23_5:	.type	object
	.size	IfxPort_P23_5,8
	.word	-268188928
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P32_0',data,cluster('IfxPort_P32_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P32_0'
	.global	IfxPort_P32_0
	.align	4
IfxPort_P32_0:	.type	object
	.size	IfxPort_P32_0,8
	.word	-268185088
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P32_2',data,cluster('IfxPort_P32_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P32_2'
	.global	IfxPort_P32_2
	.align	4
IfxPort_P32_2:	.type	object
	.size	IfxPort_P32_2,8
	.word	-268185088
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P32_3',data,cluster('IfxPort_P32_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P32_3'
	.global	IfxPort_P32_3
	.align	4
IfxPort_P32_3:	.type	object
	.size	IfxPort_P32_3,8
	.word	-268185088
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P32_4',data,cluster('IfxPort_P32_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P32_4'
	.global	IfxPort_P32_4
	.align	4
IfxPort_P32_4:	.type	object
	.size	IfxPort_P32_4,8
	.word	-268185088
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_0',data,cluster('IfxPort_P33_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_0'
	.global	IfxPort_P33_0
	.align	4
IfxPort_P33_0:	.type	object
	.size	IfxPort_P33_0,8
	.word	-268184832
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_1',data,cluster('IfxPort_P33_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_1'
	.global	IfxPort_P33_1
	.align	4
IfxPort_P33_1:	.type	object
	.size	IfxPort_P33_1,8
	.word	-268184832
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_10',data,cluster('IfxPort_P33_10')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_10'
	.global	IfxPort_P33_10
	.align	4
IfxPort_P33_10:	.type	object
	.size	IfxPort_P33_10,8
	.word	-268184832
	.byte	10
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_11',data,cluster('IfxPort_P33_11')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_11'
	.global	IfxPort_P33_11
	.align	4
IfxPort_P33_11:	.type	object
	.size	IfxPort_P33_11,8
	.word	-268184832
	.byte	11
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_12',data,cluster('IfxPort_P33_12')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_12'
	.global	IfxPort_P33_12
	.align	4
IfxPort_P33_12:	.type	object
	.size	IfxPort_P33_12,8
	.word	-268184832
	.byte	12
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_13',data,cluster('IfxPort_P33_13')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_13'
	.global	IfxPort_P33_13
	.align	4
IfxPort_P33_13:	.type	object
	.size	IfxPort_P33_13,8
	.word	-268184832
	.byte	13
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_2',data,cluster('IfxPort_P33_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_2'
	.global	IfxPort_P33_2
	.align	4
IfxPort_P33_2:	.type	object
	.size	IfxPort_P33_2,8
	.word	-268184832
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_3',data,cluster('IfxPort_P33_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_3'
	.global	IfxPort_P33_3
	.align	4
IfxPort_P33_3:	.type	object
	.size	IfxPort_P33_3,8
	.word	-268184832
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_4',data,cluster('IfxPort_P33_4')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_4'
	.global	IfxPort_P33_4
	.align	4
IfxPort_P33_4:	.type	object
	.size	IfxPort_P33_4,8
	.word	-268184832
	.byte	4
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_5',data,cluster('IfxPort_P33_5')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_5'
	.global	IfxPort_P33_5
	.align	4
IfxPort_P33_5:	.type	object
	.size	IfxPort_P33_5,8
	.word	-268184832
	.byte	5
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_6',data,cluster('IfxPort_P33_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_6'
	.global	IfxPort_P33_6
	.align	4
IfxPort_P33_6:	.type	object
	.size	IfxPort_P33_6,8
	.word	-268184832
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_7',data,cluster('IfxPort_P33_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_7'
	.global	IfxPort_P33_7
	.align	4
IfxPort_P33_7:	.type	object
	.size	IfxPort_P33_7,8
	.word	-268184832
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_8',data,cluster('IfxPort_P33_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_8'
	.global	IfxPort_P33_8
	.align	4
IfxPort_P33_8:	.type	object
	.size	IfxPort_P33_8,8
	.word	-268184832
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P33_9',data,cluster('IfxPort_P33_9')
	.sect	'.data.IfxPort_PinMap.IfxPort_P33_9'
	.global	IfxPort_P33_9
	.align	4
IfxPort_P33_9:	.type	object
	.size	IfxPort_P33_9,8
	.word	-268184832
	.byte	9
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_0',data,cluster('IfxPort_P40_0')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_0'
	.global	IfxPort_P40_0
	.align	4
IfxPort_P40_0:	.type	object
	.size	IfxPort_P40_0,8
	.word	-268181504
	.space	4
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_1',data,cluster('IfxPort_P40_1')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_1'
	.global	IfxPort_P40_1
	.align	4
IfxPort_P40_1:	.type	object
	.size	IfxPort_P40_1,8
	.word	-268181504
	.byte	1
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_2',data,cluster('IfxPort_P40_2')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_2'
	.global	IfxPort_P40_2
	.align	4
IfxPort_P40_2:	.type	object
	.size	IfxPort_P40_2,8
	.word	-268181504
	.byte	2
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_3',data,cluster('IfxPort_P40_3')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_3'
	.global	IfxPort_P40_3
	.align	4
IfxPort_P40_3:	.type	object
	.size	IfxPort_P40_3,8
	.word	-268181504
	.byte	3
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_6',data,cluster('IfxPort_P40_6')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_6'
	.global	IfxPort_P40_6
	.align	4
IfxPort_P40_6:	.type	object
	.size	IfxPort_P40_6,8
	.word	-268181504
	.byte	6
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_7',data,cluster('IfxPort_P40_7')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_7'
	.global	IfxPort_P40_7
	.align	4
IfxPort_P40_7:	.type	object
	.size	IfxPort_P40_7,8
	.word	-268181504
	.byte	7
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_8',data,cluster('IfxPort_P40_8')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_8'
	.global	IfxPort_P40_8
	.align	4
IfxPort_P40_8:	.type	object
	.size	IfxPort_P40_8,8
	.word	-268181504
	.byte	8
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_P40_9',data,cluster('IfxPort_P40_9')
	.sect	'.data.IfxPort_PinMap.IfxPort_P40_9'
	.global	IfxPort_P40_9
	.align	4
IfxPort_P40_9:	.type	object
	.size	IfxPort_P40_9,8
	.word	-268181504
	.byte	9
	.space	3
	.sdecl	'.data.IfxPort_PinMap.IfxPort_Pin_pinTable',data,cluster('IfxPort_Pin_pinTable')
	.sect	'.data.IfxPort_PinMap.IfxPort_Pin_pinTable'
	.global	IfxPort_Pin_pinTable
	.align	4
IfxPort_Pin_pinTable:	.type	object
	.size	IfxPort_Pin_pinTable,2460
	.word	IfxPort_P00_0,IfxPort_P00_1,IfxPort_P00_2,IfxPort_P00_3,IfxPort_P00_4,IfxPort_P00_5,IfxPort_P00_6,IfxPort_P00_7
	.word	IfxPort_P00_8,IfxPort_P00_9,IfxPort_P00_10,IfxPort_P00_11
	.word	IfxPort_P00_12
	.space	68
	.word	IfxPort_P02_0,IfxPort_P02_1,IfxPort_P02_2,IfxPort_P02_3,IfxPort_P02_4,IfxPort_P02_5,IfxPort_P02_6,IfxPort_P02_7
	.word	IfxPort_P02_8
	.space	444
	.word	IfxPort_P10_0,IfxPort_P10_1,IfxPort_P10_2,IfxPort_P10_3,IfxPort_P10_4,IfxPort_P10_5,IfxPort_P10_6,IfxPort_P10_7
	.word	IfxPort_P10_8
	.space	32
	.word	IfxPort_P11_2,IfxPort_P11_3
	.space	8
	.word	IfxPort_P11_6
	.space	8
	.word	IfxPort_P11_9,IfxPort_P11_10,IfxPort_P11_11,IfxPort_P11_12
	.space	68
	.word	IfxPort_P13_0,IfxPort_P13_1,IfxPort_P13_2,IfxPort_P13_3
	.space	44
	.word	IfxPort_P14_0,IfxPort_P14_1,IfxPort_P14_2,IfxPort_P14_3,IfxPort_P14_4,IfxPort_P14_5,IfxPort_P14_6,IfxPort_P14_7
	.word	IfxPort_P14_8,IfxPort_P14_9,IfxPort_P14_10
	.space	16
	.word	IfxPort_P15_0,IfxPort_P15_1,IfxPort_P15_2,IfxPort_P15_3,IfxPort_P15_4,IfxPort_P15_5,IfxPort_P15_6,IfxPort_P15_7
	.word	IfxPort_P15_8
	.space	264
	.word	IfxPort_P20_0,IfxPort_P20_1
	.space	4
	.word	IfxPort_P20_3
	.space	8
	.word	IfxPort_P20_6,IfxPort_P20_7,IfxPort_P20_8,IfxPort_P20_9,IfxPort_P20_10,IfxPort_P20_11,IfxPort_P20_12,IfxPort_P20_13
	.word	IfxPort_P20_14,IfxPort_P21_0,IfxPort_P21_1,IfxPort_P21_2,IfxPort_P21_3,IfxPort_P21_4,IfxPort_P21_5,IfxPort_P21_6
	.word	IfxPort_P21_7
	.space	28
	.word	IfxPort_P22_0,IfxPort_P22_1,IfxPort_P22_2,IfxPort_P22_3
	.space	44
	.word	IfxPort_P23_0,IfxPort_P23_1,IfxPort_P23_2,IfxPort_P23_3
	.word	IfxPort_P23_4,IfxPort_P23_5
	.space	516
	.word	IfxPort_P32_0
	.space	4
	.word	IfxPort_P32_2,IfxPort_P32_3,IfxPort_P32_4
	.space	40
	.word	IfxPort_P33_0,IfxPort_P33_1,IfxPort_P33_2,IfxPort_P33_3,IfxPort_P33_4,IfxPort_P33_5,IfxPort_P33_6,IfxPort_P33_7
	.word	IfxPort_P33_8,IfxPort_P33_9,IfxPort_P33_10,IfxPort_P33_11
	.word	IfxPort_P33_12,IfxPort_P33_13
	.space	364
	.word	IfxPort_P40_0,IfxPort_P40_1,IfxPort_P40_2,IfxPort_P40_3
	.space	8
	.word	IfxPort_P40_6,IfxPort_P40_7,IfxPort_P40_8,IfxPort_P40_9
	.space	20
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	36251
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	238
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	241
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	286
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	298
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	352
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	384
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	384
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	352
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1419
	.byte	4,2,35,0,0,14,4
	.word	493
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1977
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2414
	.byte	4,2,35,0,0,14,24
	.word	493
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3041
	.byte	4,2,35,0,0,14,8
	.word	493
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3366
	.byte	4,2,35,0,0,14,12
	.word	493
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3706
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5369
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6382
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6506
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6770
	.byte	4,2,35,0,0,14,76
	.word	493
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	808
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1379
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1498
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1538
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1722
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1937
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2154
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2374
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1538
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2688
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2728
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3001
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3317
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3357
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3657
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3697
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4032
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4318
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3357
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4465
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4634
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4806
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4981
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5155
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5329
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5505
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5661
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5994
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6342
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3357
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6466
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6715
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6974
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7014
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7070
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7637
	.byte	4,3,35,252,1,0,16
	.word	7677
	.byte	3
	.word	8280
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8285
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	493
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8290
	.byte	6,0,19
	.word	246
	.byte	20
	.word	272
	.byte	6,0,19
	.word	307
	.byte	20
	.word	339
	.byte	6,0,19
	.word	389
	.byte	20
	.word	408
	.byte	6,0,19
	.word	424
	.byte	20
	.word	439
	.byte	20
	.word	453
	.byte	6,0,19
	.word	8393
	.byte	20
	.word	8421
	.byte	20
	.word	8435
	.byte	20
	.word	8453
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8546
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	470
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	486
	.byte	22,1,3
	.word	8614
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8616
	.byte	21
	.byte	'boolean',0,6,101,29
	.word	493
	.byte	21
	.byte	'uint8',0,6,105,29
	.word	493
	.byte	21
	.byte	'uint16',0,6,109,29
	.word	510
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,6,113,29
	.word	8684
	.byte	21
	.byte	'uint64',0,6,118,29
	.word	352
	.byte	21
	.byte	'sint16',0,6,126,29
	.word	8546
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,6,131,1,29
	.word	8750
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,6,138,1,29
	.word	8778
	.byte	21
	.byte	'float32',0,6,167,1,29
	.word	298
	.byte	21
	.byte	'pvoid',0,7,57,28
	.word	384
	.byte	21
	.byte	'Ifx_TickTime',0,7,79,28
	.word	8778
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7110
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7023
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3366
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1419
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2414
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1547
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2194
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1762
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1977
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6382
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6506
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6590
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6770
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5021
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5545
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5195
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5369
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6034
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	848
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4358
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4846
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4505
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4674
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5701
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	532
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4072
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3706
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2737
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3041
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7637
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7070
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3657
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1498
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2688
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1722
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2374
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1937
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2154
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6466
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6715
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6974
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6342
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5155
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5661
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5329
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5505
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1379
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5994
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4465
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4981
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4634
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4806
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	808
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4318
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4032
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3001
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3317
	.byte	16
	.word	7677
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	10181
	.byte	17,8,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,8,255,10,3
	.word	10201
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,9,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,9,79,3
	.word	10323
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,9,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,9,85,3
	.word	10880
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,9,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,9,94,3
	.word	10957
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,9,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,9,111,3
	.word	11093
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,9,114,16,4,11
	.byte	'CANDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,9,126,3
	.word	11373
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,9,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,9,135,1,3
	.word	11611
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,9,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,9,150,1,3
	.word	11739
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,9,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,9,165,1,3
	.word	11982
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,9,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,9,174,1,3
	.word	12217
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,9,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,9,181,1,3
	.word	12345
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,9,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,9,188,1,3
	.word	12445
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,9,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,9,202,1,3
	.word	12545
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,9,205,1,16,4,11
	.byte	'PWD',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,9,213,1,3
	.word	12753
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,9,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,9,225,1,3
	.word	12918
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,9,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,9,235,1,3
	.word	13101
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,9,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,9,129,2,3
	.word	13255
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,9,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,9,143,2,3
	.word	13619
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,9,146,2,16,4,11
	.byte	'POL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	510
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,9,159,2,3
	.word	13830
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,9,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,9,167,2,3
	.word	14082
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,9,170,2,16,4,11
	.byte	'ARI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,9,175,2,3
	.word	14200
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,9,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,9,185,2,3
	.word	14311
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,9,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,9,195,2,3
	.word	14474
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,9,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,9,205,2,3
	.word	14637
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,9,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,9,215,2,3
	.word	14795
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,9,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	510
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,9,232,2,3
	.word	14960
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,9,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	510
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,9,245,2,3
	.word	15289
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,9,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,9,255,2,3
	.word	15510
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,9,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,9,142,3,3
	.word	15673
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,9,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,9,152,3,3
	.word	15945
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,9,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,9,162,3,3
	.word	16098
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,9,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,9,172,3,3
	.word	16254
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,9,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,9,181,3,3
	.word	16416
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,9,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,9,191,3,3
	.word	16559
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,9,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,9,200,3,3
	.word	16724
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,9,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,9,211,3,3
	.word	16869
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,9,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,9,222,3,3
	.word	17050
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,9,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,9,232,3,3
	.word	17224
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,9,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,9,241,3,3
	.word	17384
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,9,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,9,130,4,3
	.word	17528
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,9,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,9,139,4,3
	.word	17802
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,9,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,9,149,4,3
	.word	17941
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,9,152,4,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	493
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	510
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	493
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,9,163,4,3
	.word	18104
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,9,166,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,9,174,4,3
	.word	18322
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,9,177,4,16,4,11
	.byte	'FS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,9,197,4,3
	.word	18485
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,9,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,9,205,4,3
	.word	18821
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,9,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,9,232,4,3
	.word	18928
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,9,235,4,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,9,240,4,3
	.word	19380
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,9,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,9,250,4,3
	.word	19479
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,9,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,9,131,5,3
	.word	19629
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,9,134,5,16,4,11
	.byte	'SEED',0,4
	.word	470
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,9,141,5,3
	.word	19778
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,9,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,9,149,5,3
	.word	19939
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,9,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,9,158,5,3
	.word	20069
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,9,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,9,166,5,3
	.word	20201
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,9,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	510
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,9,174,5,3
	.word	20316
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,9,177,5,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,9,185,5,3
	.word	20427
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,9,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,9,209,5,3
	.word	20585
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,9,212,5,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,9,217,5,3
	.word	20997
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,9,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	510
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,9,233,5,3
	.word	21098
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,9,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,9,242,5,3
	.word	21365
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,9,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,9,250,5,3
	.word	21501
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,9,253,5,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,9,132,6,3
	.word	21612
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,9,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,9,146,6,3
	.word	21745
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,9,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,9,166,6,3
	.word	21948
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,9,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,9,177,6,3
	.word	22304
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,9,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,9,184,6,3
	.word	22482
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,9,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,9,204,6,3
	.word	22582
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,9,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,9,215,6,3
	.word	22952
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,9,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,9,227,6,3
	.word	23138
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,9,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,9,241,6,3
	.word	23336
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,9,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,9,251,6,3
	.word	23569
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,9,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	493
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,9,153,7,3
	.word	23721
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,9,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,9,170,7,3
	.word	24288
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,9,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,9,187,7,3
	.word	24582
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,9,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,9,214,7,3
	.word	24860
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,9,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,9,230,7,3
	.word	25356
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,9,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,9,243,7,3
	.word	25669
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,9,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,9,129,8,3
	.word	25878
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,9,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,9,155,8,3
	.word	26089
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,9,158,8,16,4,11
	.byte	'HBT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,9,162,8,3
	.word	26521
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,9,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,9,178,8,3
	.word	26617
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,9,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,9,186,8,3
	.word	26877
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,9,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,9,198,8,3
	.word	27002
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,9,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,9,208,8,3
	.word	27199
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,9,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,9,218,8,3
	.word	27352
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,9,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,9,228,8,3
	.word	27505
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,9,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,9,238,8,3
	.word	27658
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,9,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	27813
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	27813
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	27813
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	27813
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,9,247,8,3
	.word	27829
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,9,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,9,134,9,3
	.word	27959
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,9,137,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,9,150,9,3
	.word	28197
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,9,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	27813
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	27813
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	27813
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	27813
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,9,159,9,3
	.word	28420
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,9,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,9,175,9,3
	.word	28546
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,9,178,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,9,191,9,3
	.word	28798
	.byte	12,9,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10323
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,9,204,9,3
	.word	29017
	.byte	12,9,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10880
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,9,212,9,3
	.word	29081
	.byte	12,9,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10957
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,9,220,9,3
	.word	29145
	.byte	12,9,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11093
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,9,228,9,3
	.word	29210
	.byte	12,9,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11373
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,9,236,9,3
	.word	29275
	.byte	12,9,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11611
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,9,244,9,3
	.word	29340
	.byte	12,9,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11739
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,9,252,9,3
	.word	29405
	.byte	12,9,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11982
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,9,132,10,3
	.word	29470
	.byte	12,9,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12217
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,9,140,10,3
	.word	29535
	.byte	12,9,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12345
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,9,148,10,3
	.word	29600
	.byte	12,9,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12445
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,9,156,10,3
	.word	29665
	.byte	12,9,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12545
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,9,164,10,3
	.word	29730
	.byte	12,9,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12753
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,9,172,10,3
	.word	29794
	.byte	12,9,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12918
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,9,180,10,3
	.word	29858
	.byte	12,9,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13101
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,9,188,10,3
	.word	29922
	.byte	12,9,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13255
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,9,196,10,3
	.word	29987
	.byte	12,9,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13619
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,9,204,10,3
	.word	30049
	.byte	12,9,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13830
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,9,212,10,3
	.word	30111
	.byte	12,9,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14082
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,9,220,10,3
	.word	30173
	.byte	12,9,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14200
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,9,228,10,3
	.word	30237
	.byte	12,9,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14311
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,9,236,10,3
	.word	30302
	.byte	12,9,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14474
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,9,244,10,3
	.word	30368
	.byte	12,9,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14637
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,9,252,10,3
	.word	30434
	.byte	12,9,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14795
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,9,132,11,3
	.word	30502
	.byte	12,9,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14960
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,9,140,11,3
	.word	30569
	.byte	12,9,143,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15289
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,9,148,11,3
	.word	30637
	.byte	12,9,151,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15510
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,9,156,11,3
	.word	30705
	.byte	12,9,159,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15673
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,9,164,11,3
	.word	30771
	.byte	12,9,167,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15945
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,9,172,11,3
	.word	30838
	.byte	12,9,175,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16098
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,9,180,11,3
	.word	30907
	.byte	12,9,183,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16254
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,9,188,11,3
	.word	30976
	.byte	12,9,191,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16416
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,9,196,11,3
	.word	31045
	.byte	12,9,199,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16559
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,9,204,11,3
	.word	31114
	.byte	12,9,207,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16724
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,9,212,11,3
	.word	31183
	.byte	12,9,215,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16869
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,9,220,11,3
	.word	31252
	.byte	12,9,223,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17050
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,9,228,11,3
	.word	31320
	.byte	12,9,231,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17224
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,9,236,11,3
	.word	31388
	.byte	12,9,239,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17384
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,9,244,11,3
	.word	31456
	.byte	12,9,247,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17528
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,9,252,11,3
	.word	31524
	.byte	12,9,255,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17802
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,9,132,12,3
	.word	31589
	.byte	12,9,135,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17941
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,9,140,12,3
	.word	31654
	.byte	12,9,143,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18104
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,9,148,12,3
	.word	31720
	.byte	12,9,151,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18322
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,9,156,12,3
	.word	31784
	.byte	12,9,159,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18485
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,9,164,12,3
	.word	31845
	.byte	12,9,167,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18821
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,9,172,12,3
	.word	31906
	.byte	12,9,175,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18928
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,9,180,12,3
	.word	31966
	.byte	12,9,183,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19380
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,9,188,12,3
	.word	32028
	.byte	12,9,191,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19479
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,9,196,12,3
	.word	32088
	.byte	12,9,199,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19629
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,9,204,12,3
	.word	32150
	.byte	12,9,207,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19778
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,9,212,12,3
	.word	32218
	.byte	12,9,215,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19939
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,9,220,12,3
	.word	32286
	.byte	12,9,223,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20069
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,9,228,12,3
	.word	32354
	.byte	12,9,231,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20201
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,9,236,12,3
	.word	32418
	.byte	12,9,239,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20316
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,9,244,12,3
	.word	32483
	.byte	12,9,247,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20427
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,9,252,12,3
	.word	32546
	.byte	12,9,255,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20585
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,9,132,13,3
	.word	32607
	.byte	12,9,135,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20997
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,9,140,13,3
	.word	32671
	.byte	12,9,143,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21098
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,9,148,13,3
	.word	32732
	.byte	12,9,151,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21365
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,9,156,13,3
	.word	32796
	.byte	12,9,159,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21501
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,9,164,13,3
	.word	32863
	.byte	12,9,167,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21612
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,9,172,13,3
	.word	32926
	.byte	12,9,175,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21745
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,9,180,13,3
	.word	32987
	.byte	12,9,183,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21948
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,9,188,13,3
	.word	33049
	.byte	12,9,191,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22304
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,9,196,13,3
	.word	33114
	.byte	12,9,199,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22482
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,9,204,13,3
	.word	33179
	.byte	12,9,207,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22582
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,9,212,13,3
	.word	33244
	.byte	12,9,215,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22952
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,9,220,13,3
	.word	33313
	.byte	12,9,223,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23138
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,9,228,13,3
	.word	33382
	.byte	12,9,231,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23336
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,9,236,13,3
	.word	33451
	.byte	12,9,239,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23569
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,9,244,13,3
	.word	33516
	.byte	12,9,247,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23721
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,9,252,13,3
	.word	33579
	.byte	12,9,255,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24288
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,9,132,14,3
	.word	33644
	.byte	12,9,135,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24582
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,9,140,14,3
	.word	33709
	.byte	12,9,143,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24860
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,9,148,14,3
	.word	33774
	.byte	12,9,151,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25356
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,9,156,14,3
	.word	33840
	.byte	12,9,159,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25878
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,9,164,14,3
	.word	33909
	.byte	12,9,167,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25669
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,9,172,14,3
	.word	33973
	.byte	12,9,175,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26089
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,9,180,14,3
	.word	34038
	.byte	12,9,183,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26521
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,9,188,14,3
	.word	34103
	.byte	12,9,191,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26617
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,9,196,14,3
	.word	34168
	.byte	12,9,199,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26877
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,9,204,14,3
	.word	34232
	.byte	12,9,207,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27002
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,9,212,14,3
	.word	34298
	.byte	12,9,215,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27199
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,9,220,14,3
	.word	34362
	.byte	12,9,223,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27352
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,9,228,14,3
	.word	34427
	.byte	12,9,231,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27505
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,9,236,14,3
	.word	34492
	.byte	12,9,239,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27658
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,9,244,14,3
	.word	34557
	.byte	12,9,247,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27829
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,9,252,14,3
	.word	34623
	.byte	12,9,255,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27959
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,9,132,15,3
	.word	34692
	.byte	12,9,135,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28197
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,9,140,15,3
	.word	34761
	.byte	12,9,143,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28420
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,9,148,15,3
	.word	34828
	.byte	12,9,151,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28546
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,9,156,15,3
	.word	34895
	.byte	12,9,159,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28798
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,9,164,15,3
	.word	34962
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,9,175,15,25,12,13
	.byte	'CON0',0
	.word	34623
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	34692
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	34761
	.byte	4,2,35,8,0,16
	.word	35027
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,9,180,15,3
	.word	35090
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,9,183,15,25,12,13
	.byte	'CON0',0
	.word	34828
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	34895
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	34962
	.byte	4,2,35,8,0,16
	.word	35119
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,9,188,15,3
	.word	35180
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	35207
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	35358
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	35602
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	35700
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8290
.L244:
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8285
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	493
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	36165
	.byte	24
	.word	36165
	.byte	3
	.word	36225
	.byte	14,60
	.word	36230
	.byte	15,14,0
.L245:
	.byte	14,156,19
	.word	36235
	.byte	15,40,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L247-.L246
.L246:
	.half	3
	.word	.L249-.L248
.L248:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0,0
.L249:
.L247:
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_0')
	.sect	'.debug_info'
.L6:
	.word	265
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_0',0,5,48,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_0')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_1')
	.sect	'.debug_info'
.L8:
	.word	265
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_1',0,5,49,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_1')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_10')
	.sect	'.debug_info'
.L10:
	.word	266
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_10',0,5,50,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_10
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_10')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_11')
	.sect	'.debug_info'
.L12:
	.word	266
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_11',0,5,51,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_11
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_11')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_12')
	.sect	'.debug_info'
.L14:
	.word	266
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_12',0,5,52,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_12
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_12')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_2')
	.sect	'.debug_info'
.L16:
	.word	265
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_2',0,5,53,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_2')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_3')
	.sect	'.debug_info'
.L18:
	.word	265
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_3',0,5,54,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_3')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_4')
	.sect	'.debug_info'
.L20:
	.word	265
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_4',0,5,55,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_4')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_5')
	.sect	'.debug_info'
.L22:
	.word	265
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_5',0,5,56,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_5')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_6')
	.sect	'.debug_info'
.L24:
	.word	265
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_6',0,5,57,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_6')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_7')
	.sect	'.debug_info'
.L26:
	.word	265
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_7',0,5,58,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_7')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_8')
	.sect	'.debug_info'
.L28:
	.word	265
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_8',0,5,59,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_8')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P00_9')
	.sect	'.debug_info'
.L30:
	.word	265
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P00_9',0,5,60,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P00_9
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P00_9')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_0')
	.sect	'.debug_info'
.L32:
	.word	265
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_0',0,5,61,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_0')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_1')
	.sect	'.debug_info'
.L34:
	.word	265
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_1',0,5,62,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_1')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_2')
	.sect	'.debug_info'
.L36:
	.word	265
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_2',0,5,63,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_2')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_3')
	.sect	'.debug_info'
.L38:
	.word	265
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_3',0,5,64,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_3')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_4')
	.sect	'.debug_info'
.L40:
	.word	265
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_4',0,5,65,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_4')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_5')
	.sect	'.debug_info'
.L42:
	.word	265
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_5',0,5,66,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_5')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_6')
	.sect	'.debug_info'
.L44:
	.word	265
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_6',0,5,67,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_6')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_7')
	.sect	'.debug_info'
.L46:
	.word	265
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_7',0,5,68,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_7')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P02_8')
	.sect	'.debug_info'
.L48:
	.word	265
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P02_8',0,5,69,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P02_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P02_8')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_0')
	.sect	'.debug_info'
.L50:
	.word	265
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_0',0,5,70,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_0')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_1')
	.sect	'.debug_info'
.L52:
	.word	265
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_1',0,5,71,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_1')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_2')
	.sect	'.debug_info'
.L54:
	.word	265
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_2',0,5,72,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_2')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_3')
	.sect	'.debug_info'
.L56:
	.word	265
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_3',0,5,73,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_3')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_4')
	.sect	'.debug_info'
.L58:
	.word	265
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_4',0,5,74,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_4')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_5')
	.sect	'.debug_info'
.L60:
	.word	265
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_5',0,5,75,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_5')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_6')
	.sect	'.debug_info'
.L62:
	.word	265
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_6',0,5,76,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_6')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_7')
	.sect	'.debug_info'
.L64:
	.word	265
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_7',0,5,77,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_7')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P10_8')
	.sect	'.debug_info'
.L66:
	.word	265
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P10_8',0,5,78,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P10_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P10_8')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P11_10')
	.sect	'.debug_info'
.L68:
	.word	266
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P11_10',0,5,79,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P11_10
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P11_10')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P11_11')
	.sect	'.debug_info'
.L70:
	.word	266
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P11_11',0,5,80,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P11_11
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P11_11')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P11_12')
	.sect	'.debug_info'
.L72:
	.word	266
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P11_12',0,5,81,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P11_12
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P11_12')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P11_2')
	.sect	'.debug_info'
.L74:
	.word	265
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P11_2',0,5,82,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P11_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P11_2')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P11_3')
	.sect	'.debug_info'
.L76:
	.word	265
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P11_3',0,5,83,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P11_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P11_3')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P11_6')
	.sect	'.debug_info'
.L78:
	.word	265
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P11_6',0,5,84,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P11_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P11_6')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P11_9')
	.sect	'.debug_info'
.L80:
	.word	265
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P11_9',0,5,85,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P11_9
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P11_9')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P13_0')
	.sect	'.debug_info'
.L82:
	.word	265
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P13_0',0,5,86,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P13_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P13_0')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P13_1')
	.sect	'.debug_info'
.L84:
	.word	265
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P13_1',0,5,87,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P13_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P13_1')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P13_2')
	.sect	'.debug_info'
.L86:
	.word	265
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P13_2',0,5,88,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P13_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P13_2')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P13_3')
	.sect	'.debug_info'
.L88:
	.word	265
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P13_3',0,5,89,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P13_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P13_3')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_0')
	.sect	'.debug_info'
.L90:
	.word	265
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_0',0,5,90,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_0')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_1')
	.sect	'.debug_info'
.L92:
	.word	265
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_1',0,5,91,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_1')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_10')
	.sect	'.debug_info'
.L94:
	.word	266
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_10',0,5,92,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_10
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_10')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_2')
	.sect	'.debug_info'
.L96:
	.word	265
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_2',0,5,93,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_2')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_3')
	.sect	'.debug_info'
.L98:
	.word	265
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_3',0,5,94,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_3')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_4')
	.sect	'.debug_info'
.L100:
	.word	265
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_4',0,5,95,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_4')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_5')
	.sect	'.debug_info'
.L102:
	.word	265
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_5',0,5,96,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_5')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_6')
	.sect	'.debug_info'
.L104:
	.word	265
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_6',0,5,97,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_6')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_7')
	.sect	'.debug_info'
.L106:
	.word	265
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_7',0,5,98,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_7')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_8')
	.sect	'.debug_info'
.L108:
	.word	265
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_8',0,5,99,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_8')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P14_9')
	.sect	'.debug_info'
.L110:
	.word	265
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P14_9',0,5,100,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P14_9
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P14_9')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_0')
	.sect	'.debug_info'
.L112:
	.word	265
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_0',0,5,101,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_0')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_1')
	.sect	'.debug_info'
.L114:
	.word	265
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_1',0,5,102,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_1')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_2')
	.sect	'.debug_info'
.L116:
	.word	265
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_2',0,5,103,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_2')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_3')
	.sect	'.debug_info'
.L118:
	.word	265
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_3',0,5,104,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_3')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_4')
	.sect	'.debug_info'
.L120:
	.word	265
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_4',0,5,105,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_4')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_5')
	.sect	'.debug_info'
.L122:
	.word	265
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_5',0,5,106,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_5')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_6')
	.sect	'.debug_info'
.L124:
	.word	265
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_6',0,5,107,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_6')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_7')
	.sect	'.debug_info'
.L126:
	.word	265
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_7',0,5,108,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_7')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P15_8')
	.sect	'.debug_info'
.L128:
	.word	265
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P15_8',0,5,109,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P15_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P15_8')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_0')
	.sect	'.debug_info'
.L130:
	.word	265
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_0',0,5,110,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_0')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_1')
	.sect	'.debug_info'
.L132:
	.word	265
	.half	3
	.word	.L133
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_1',0,5,111,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_1')
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_10')
	.sect	'.debug_info'
.L134:
	.word	266
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_10',0,5,112,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_10
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_10')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_11')
	.sect	'.debug_info'
.L136:
	.word	266
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_11',0,5,113,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_11
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_11')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_12')
	.sect	'.debug_info'
.L138:
	.word	266
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_12',0,5,114,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_12
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_12')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_13')
	.sect	'.debug_info'
.L140:
	.word	266
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_13',0,5,115,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_13
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_13')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_14')
	.sect	'.debug_info'
.L142:
	.word	266
	.half	3
	.word	.L143
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_14',0,5,116,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_14
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_14')
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_3')
	.sect	'.debug_info'
.L144:
	.word	265
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_3',0,5,117,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_3')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_6')
	.sect	'.debug_info'
.L146:
	.word	265
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_6',0,5,118,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_6')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_7')
	.sect	'.debug_info'
.L148:
	.word	265
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_7',0,5,119,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_7')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_8')
	.sect	'.debug_info'
.L150:
	.word	265
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_8',0,5,120,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_8')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P20_9')
	.sect	'.debug_info'
.L152:
	.word	265
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P20_9',0,5,121,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P20_9
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P20_9')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_0')
	.sect	'.debug_info'
.L154:
	.word	265
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_0',0,5,122,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_0')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_1')
	.sect	'.debug_info'
.L156:
	.word	265
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_1',0,5,123,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_1')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_2')
	.sect	'.debug_info'
.L158:
	.word	265
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_2',0,5,124,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_2')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_3')
	.sect	'.debug_info'
.L160:
	.word	265
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_3',0,5,125,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_3')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_4')
	.sect	'.debug_info'
.L162:
	.word	265
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_4',0,5,126,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_4')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_5')
	.sect	'.debug_info'
.L164:
	.word	265
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_5',0,5,127,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_5')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_6')
	.sect	'.debug_info'
.L166:
	.word	266
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_6',0,5,128,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_6')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P21_7')
	.sect	'.debug_info'
.L168:
	.word	266
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P21_7',0,5,129,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P21_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P21_7')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P22_0')
	.sect	'.debug_info'
.L170:
	.word	266
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P22_0',0,5,130,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P22_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P22_0')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P22_1')
	.sect	'.debug_info'
.L172:
	.word	266
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P22_1',0,5,131,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P22_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P22_1')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P22_2')
	.sect	'.debug_info'
.L174:
	.word	266
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P22_2',0,5,132,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P22_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P22_2')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P22_3')
	.sect	'.debug_info'
.L176:
	.word	266
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P22_3',0,5,133,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P22_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P22_3')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P23_0')
	.sect	'.debug_info'
.L178:
	.word	266
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P23_0',0,5,134,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P23_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P23_0')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P23_1')
	.sect	'.debug_info'
.L180:
	.word	266
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P23_1',0,5,135,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P23_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P23_1')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P23_2')
	.sect	'.debug_info'
.L182:
	.word	266
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P23_2',0,5,136,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P23_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P23_2')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P23_3')
	.sect	'.debug_info'
.L184:
	.word	266
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P23_3',0,5,137,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P23_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P23_3')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P23_4')
	.sect	'.debug_info'
.L186:
	.word	266
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P23_4',0,5,138,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P23_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P23_4')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P23_5')
	.sect	'.debug_info'
.L188:
	.word	266
	.half	3
	.word	.L189
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P23_5',0,5,139,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P23_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P23_5')
	.sect	'.debug_abbrev'
.L189:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P32_0')
	.sect	'.debug_info'
.L190:
	.word	266
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P32_0',0,5,140,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P32_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P32_0')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P32_2')
	.sect	'.debug_info'
.L192:
	.word	266
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P32_2',0,5,141,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P32_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P32_2')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P32_3')
	.sect	'.debug_info'
.L194:
	.word	266
	.half	3
	.word	.L195
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P32_3',0,5,142,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P32_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P32_3')
	.sect	'.debug_abbrev'
.L195:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P32_4')
	.sect	'.debug_info'
.L196:
	.word	266
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P32_4',0,5,143,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P32_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P32_4')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_0')
	.sect	'.debug_info'
.L198:
	.word	266
	.half	3
	.word	.L199
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_0',0,5,144,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_0')
	.sect	'.debug_abbrev'
.L199:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_1')
	.sect	'.debug_info'
.L200:
	.word	266
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_1',0,5,145,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_1')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_10')
	.sect	'.debug_info'
.L202:
	.word	267
	.half	3
	.word	.L203
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_10',0,5,146,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_10
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_10')
	.sect	'.debug_abbrev'
.L203:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_11')
	.sect	'.debug_info'
.L204:
	.word	267
	.half	3
	.word	.L205
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_11',0,5,147,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_11
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_11')
	.sect	'.debug_abbrev'
.L205:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_12')
	.sect	'.debug_info'
.L206:
	.word	267
	.half	3
	.word	.L207
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_12',0,5,148,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_12
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_12')
	.sect	'.debug_abbrev'
.L207:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_13')
	.sect	'.debug_info'
.L208:
	.word	267
	.half	3
	.word	.L209
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_13',0,5,149,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_13
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_13')
	.sect	'.debug_abbrev'
.L209:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_2')
	.sect	'.debug_info'
.L210:
	.word	266
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_2',0,5,150,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_2')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_3')
	.sect	'.debug_info'
.L212:
	.word	266
	.half	3
	.word	.L213
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_3',0,5,151,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_3')
	.sect	'.debug_abbrev'
.L213:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_4')
	.sect	'.debug_info'
.L214:
	.word	266
	.half	3
	.word	.L215
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_4',0,5,152,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_4')
	.sect	'.debug_abbrev'
.L215:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_5')
	.sect	'.debug_info'
.L216:
	.word	266
	.half	3
	.word	.L217
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_5',0,5,153,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_5')
	.sect	'.debug_abbrev'
.L217:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_6')
	.sect	'.debug_info'
.L218:
	.word	266
	.half	3
	.word	.L219
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_6',0,5,154,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_6')
	.sect	'.debug_abbrev'
.L219:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_7')
	.sect	'.debug_info'
.L220:
	.word	266
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_7',0,5,155,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_7')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_8')
	.sect	'.debug_info'
.L222:
	.word	266
	.half	3
	.word	.L223
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_8',0,5,156,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_8')
	.sect	'.debug_abbrev'
.L223:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P33_9')
	.sect	'.debug_info'
.L224:
	.word	266
	.half	3
	.word	.L225
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P33_9',0,5,157,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P33_9
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P33_9')
	.sect	'.debug_abbrev'
.L225:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_0')
	.sect	'.debug_info'
.L226:
	.word	266
	.half	3
	.word	.L227
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_0',0,5,158,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_0')
	.sect	'.debug_abbrev'
.L227:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_1')
	.sect	'.debug_info'
.L228:
	.word	266
	.half	3
	.word	.L229
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_1',0,5,159,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_1')
	.sect	'.debug_abbrev'
.L229:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_2')
	.sect	'.debug_info'
.L230:
	.word	266
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_2',0,5,160,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_2')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_3')
	.sect	'.debug_info'
.L232:
	.word	266
	.half	3
	.word	.L233
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_3',0,5,161,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_3')
	.sect	'.debug_abbrev'
.L233:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_6')
	.sect	'.debug_info'
.L234:
	.word	266
	.half	3
	.word	.L235
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_6',0,5,162,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_6')
	.sect	'.debug_abbrev'
.L235:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_7')
	.sect	'.debug_info'
.L236:
	.word	266
	.half	3
	.word	.L237
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_7',0,5,163,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_7')
	.sect	'.debug_abbrev'
.L237:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_8')
	.sect	'.debug_info'
.L238:
	.word	266
	.half	3
	.word	.L239
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_8',0,5,164,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_8')
	.sect	'.debug_abbrev'
.L239:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_P40_9')
	.sect	'.debug_info'
.L240:
	.word	266
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_P40_9',0,5,165,1,13
	.word	.L244
	.byte	1,5,3
	.word	IfxPort_P40_9
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_P40_9')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxPort_Pin_pinTable')
	.sect	'.debug_info'
.L242:
	.word	273
	.half	3
	.word	.L243
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxPort_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxPort_Pin_pinTable',0,5,168,1,20
	.word	.L245
	.byte	1,5,3
	.word	IfxPort_Pin_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxPort_Pin_pinTable')
	.sect	'.debug_abbrev'
.L243:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
