/**
 * \file IfxCif_PinMap.c
 * \brief CIF I/O map
 * \ingroup IfxLld_Cif
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */

#include "IfxCif_PinMap.h"

IfxCif_Clk_In IfxCif_CLK_P00_7_IN = {&MODULE_CIF, {&MODULE_P00, 7}};
IfxCif_D_In IfxCif_D0_P02_0_IN = {&MODULE_CIF, {&MODULE_P02, 0}};
IfxCif_D_In IfxCif_D10_P00_1_IN = {&MODULE_CIF, {&MODULE_P00, 1}};
IfxCif_D_In IfxCif_D11_P00_2_IN = {&MODULE_CIF, {&MODULE_P00, 2}};
IfxCif_D_In IfxCif_D12_P00_3_IN = {&MODULE_CIF, {&MODULE_P00, 3}};
IfxCif_D_In IfxCif_D13_P00_4_IN = {&MODULE_CIF, {&MODULE_P00, 4}};
IfxCif_D_In IfxCif_D14_P00_5_IN = {&MODULE_CIF, {&MODULE_P00, 5}};
IfxCif_D_In IfxCif_D15_P00_6_IN = {&MODULE_CIF, {&MODULE_P00, 6}};
IfxCif_D_In IfxCif_D1_P02_1_IN = {&MODULE_CIF, {&MODULE_P02, 1}};
IfxCif_D_In IfxCif_D2_P02_2_IN = {&MODULE_CIF, {&MODULE_P02, 2}};
IfxCif_D_In IfxCif_D3_P02_3_IN = {&MODULE_CIF, {&MODULE_P02, 3}};
IfxCif_D_In IfxCif_D4_P02_4_IN = {&MODULE_CIF, {&MODULE_P02, 4}};
IfxCif_D_In IfxCif_D5_P02_5_IN = {&MODULE_CIF, {&MODULE_P02, 5}};
IfxCif_D_In IfxCif_D6_P02_6_IN = {&MODULE_CIF, {&MODULE_P02, 6}};
IfxCif_D_In IfxCif_D7_P02_7_IN = {&MODULE_CIF, {&MODULE_P02, 7}};
IfxCif_D_In IfxCif_D8_P02_8_IN = {&MODULE_CIF, {&MODULE_P02, 8}};
IfxCif_D_In IfxCif_D9_P00_0_IN = {&MODULE_CIF, {&MODULE_P00, 0}};
IfxCif_Hsnc_In IfxCif_HSNC_P00_9_IN = {&MODULE_CIF, {&MODULE_P00, 9}};
IfxCif_Vsnc_In IfxCif_VSNC_P00_8_IN = {&MODULE_CIF, {&MODULE_P00, 8}};
