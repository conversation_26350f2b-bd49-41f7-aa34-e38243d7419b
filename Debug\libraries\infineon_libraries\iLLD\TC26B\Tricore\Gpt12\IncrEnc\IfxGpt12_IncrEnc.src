	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc25208a --dep-file=IfxGpt12_IncrEnc.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c'

	
$TC16X
	.sdecl	'.rodata.IfxGpt12_IncrEnc..1.cnt',data,rom
	.sect	'.rodata.IfxGpt12_IncrEnc..1.cnt'
	.align	4
.1.cnt:	.type	object
	.size	.1.cnt,8
	.word	1610612736,1074340347
	.sdecl	'.rodata.IfxGpt12_IncrEnc..2.cnt',data,rom
	.sect	'.rodata.IfxGpt12_IncrEnc..2.cnt'
	.align	4
.2.cnt:	.type	object
	.size	.2.cnt,8
	.word	1610612736,1075388923
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getAbsolutePosition',code,cluster('IfxGpt12_IncrEnc_getAbsolutePosition')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getAbsolutePosition'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getAbsolutePosition
; Function IfxGpt12_IncrEnc_getAbsolutePosition
.L99:
IfxGpt12_IncrEnc_getAbsolutePosition:	.type	func
	ld.w	d15,[a4]8
.L889:
	itof	d15,d15
.L890:
	ld.w	d0,[a4]
.L891:
	itof	d0,d0
.L892:
	ld.w	d1,[a4]22
.L893:
	itof	d1,d1
.L894:
	div.f	d0,d0,d1
.L895:
	add.f	d4,d15,d0
	call	__f_ftod
.L637:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
.L896:
	mov	e4,d3,d2
	call	__d_mul
.L897:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e6,[a15]0
.L898:
	mov	e4,d3,d2
	call	__d_mul
	mov	e4,d3,d2
	call	__d_dtof
.L899:
	j	.L2
.L2:
	ret
.L558:
	
__IfxGpt12_IncrEnc_getAbsolutePosition_function_end:
	.size	IfxGpt12_IncrEnc_getAbsolutePosition,__IfxGpt12_IncrEnc_getAbsolutePosition_function_end-IfxGpt12_IncrEnc_getAbsolutePosition
.L166:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getDirection',code,cluster('IfxGpt12_IncrEnc_getDirection')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getDirection'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getDirection
; Function IfxGpt12_IncrEnc_getDirection
.L101:
IfxGpt12_IncrEnc_getDirection:	.type	func
	ld.bu	d2,[a4]12
.L904:
	j	.L3
.L3:
	ret
.L561:
	
__IfxGpt12_IncrEnc_getDirection_function_end:
	.size	IfxGpt12_IncrEnc_getDirection,__IfxGpt12_IncrEnc_getDirection_function_end-IfxGpt12_IncrEnc_getDirection
.L171:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getFault',code,cluster('IfxGpt12_IncrEnc_getFault')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getFault'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getFault
; Function IfxGpt12_IncrEnc_getFault
.L103:
IfxGpt12_IncrEnc_getFault:	.type	func
	ld.w	d2,[a4]14
.L909:
	j	.L4
.L4:
	ret
.L564:
	
__IfxGpt12_IncrEnc_getFault_function_end:
	.size	IfxGpt12_IncrEnc_getFault,__IfxGpt12_IncrEnc_getFault_function_end-IfxGpt12_IncrEnc_getFault
.L176:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getOffset',code,cluster('IfxGpt12_IncrEnc_getOffset')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getOffset'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getOffset
; Function IfxGpt12_IncrEnc_getOffset
.L105:
IfxGpt12_IncrEnc_getOffset:	.type	func
	ld.w	d2,[a4]18
.L914:
	j	.L5
.L5:
	ret
.L567:
	
__IfxGpt12_IncrEnc_getOffset_function_end:
	.size	IfxGpt12_IncrEnc_getOffset,__IfxGpt12_IncrEnc_getOffset_function_end-IfxGpt12_IncrEnc_getOffset
.L181:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getPeriodPerRotation',code,cluster('IfxGpt12_IncrEnc_getPeriodPerRotation')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getPeriodPerRotation'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getPeriodPerRotation
; Function IfxGpt12_IncrEnc_getPeriodPerRotation
.L107:
IfxGpt12_IncrEnc_getPeriodPerRotation:	.type	func
	jz.a	a4,.L6
.L6:
	mov	d2,#1
.L919:
	j	.L7
.L7:
	ret
.L570:
	
__IfxGpt12_IncrEnc_getPeriodPerRotation_function_end:
	.size	IfxGpt12_IncrEnc_getPeriodPerRotation,__IfxGpt12_IncrEnc_getPeriodPerRotation_function_end-IfxGpt12_IncrEnc_getPeriodPerRotation
.L186:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getPosition',code,cluster('IfxGpt12_IncrEnc_getPosition')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getPosition'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getPosition
; Function IfxGpt12_IncrEnc_getPosition
.L109:
IfxGpt12_IncrEnc_getPosition:	.type	func
	ld.w	d15,[a4]
.L924:
	itof	d15,d15
.L925:
	ld.w	d0,[a4]38
.L926:
	mul.f	d2,d15,d0
.L927:
	j	.L8
.L8:
	ret
.L572:
	
__IfxGpt12_IncrEnc_getPosition_function_end:
	.size	IfxGpt12_IncrEnc_getPosition,__IfxGpt12_IncrEnc_getPosition_function_end-IfxGpt12_IncrEnc_getPosition
.L191:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getRawPosition',code,cluster('IfxGpt12_IncrEnc_getRawPosition')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getRawPosition'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getRawPosition
; Function IfxGpt12_IncrEnc_getRawPosition
.L111:
IfxGpt12_IncrEnc_getRawPosition:	.type	func
	ld.w	d2,[a4]
.L932:
	j	.L9
.L9:
	ret
.L574:
	
__IfxGpt12_IncrEnc_getRawPosition_function_end:
	.size	IfxGpt12_IncrEnc_getRawPosition,__IfxGpt12_IncrEnc_getRawPosition_function_end-IfxGpt12_IncrEnc_getRawPosition
.L196:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getRefreshPeriod',code,cluster('IfxGpt12_IncrEnc_getRefreshPeriod')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getRefreshPeriod'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getRefreshPeriod
; Function IfxGpt12_IncrEnc_getRefreshPeriod
.L113:
IfxGpt12_IncrEnc_getRefreshPeriod:	.type	func
	ld.w	d2,[a4]26
.L937:
	j	.L10
.L10:
	ret
.L576:
	
__IfxGpt12_IncrEnc_getRefreshPeriod_function_end:
	.size	IfxGpt12_IncrEnc_getRefreshPeriod,__IfxGpt12_IncrEnc_getRefreshPeriod_function_end-IfxGpt12_IncrEnc_getRefreshPeriod
.L201:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getResolution',code,cluster('IfxGpt12_IncrEnc_getResolution')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getResolution'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getResolution
; Function IfxGpt12_IncrEnc_getResolution
.L115:
IfxGpt12_IncrEnc_getResolution:	.type	func
	ld.w	d2,[a4]22
.L942:
	j	.L11
.L11:
	ret
.L578:
	
__IfxGpt12_IncrEnc_getResolution_function_end:
	.size	IfxGpt12_IncrEnc_getResolution,__IfxGpt12_IncrEnc_getResolution_function_end-IfxGpt12_IncrEnc_getResolution
.L206:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getSensorType',code,cluster('IfxGpt12_IncrEnc_getSensorType')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getSensorType'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getSensorType
; Function IfxGpt12_IncrEnc_getSensorType
.L117:
IfxGpt12_IncrEnc_getSensorType:	.type	func
	jz.a	a4,.L12
.L12:
	mov	d2,#0
.L947:
	j	.L13
.L13:
	ret
.L581:
	
__IfxGpt12_IncrEnc_getSensorType_function_end:
	.size	IfxGpt12_IncrEnc_getSensorType,__IfxGpt12_IncrEnc_getSensorType_function_end-IfxGpt12_IncrEnc_getSensorType
.L211:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getSpeed',code,cluster('IfxGpt12_IncrEnc_getSpeed')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getSpeed'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getSpeed
; Function IfxGpt12_IncrEnc_getSpeed
.L119:
IfxGpt12_IncrEnc_getSpeed:	.type	func
	ld.w	d2,[a4]4
.L952:
	j	.L14
.L14:
	ret
.L583:
	
__IfxGpt12_IncrEnc_getSpeed_function_end:
	.size	IfxGpt12_IncrEnc_getSpeed,__IfxGpt12_IncrEnc_getSpeed_function_end-IfxGpt12_IncrEnc_getSpeed
.L216:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getTurn',code,cluster('IfxGpt12_IncrEnc_getTurn')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_getTurn'
	.align	2
	
	.global	IfxGpt12_IncrEnc_getTurn
; Function IfxGpt12_IncrEnc_getTurn
.L121:
IfxGpt12_IncrEnc_getTurn:	.type	func
	ld.w	d2,[a4]8
.L957:
	j	.L15
.L15:
	ret
.L585:
	
__IfxGpt12_IncrEnc_getTurn_function_end:
	.size	IfxGpt12_IncrEnc_getTurn,__IfxGpt12_IncrEnc_getTurn_function_end-IfxGpt12_IncrEnc_getTurn
.L221:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_init',code,cluster('IfxGpt12_IncrEnc_init')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_init'
	.align	2
	
	.global	IfxGpt12_IncrEnc_init
; Function IfxGpt12_IncrEnc_init
.L123:
IfxGpt12_IncrEnc_init:	.type	func
	sub.a	a10,#16
.L638:
	mov.aa	a12,a4
.L640:
	mov.aa	a15,a5
.L641:
	mov	d8,#1
.L642:
	ld.a	a13,[a15]36
.L643:
	st.a	[a12]52,a13
.L697:
	ld.w	d15,[a15]
.L698:
	st.w	[a12]18,d15
.L699:
	ld.w	d0,[a15]6
.L700:
	ld.bu	d15,[a15]12
.L701:
	mul	d0,d15
.L702:
	st.w	[a12]22,d0
.L703:
	mov	d10,#0
	mov	d11,#0
	addih	d11,d11,#16368
.L704:
	ld.w	d15,[a12]22
.L705:
	itof	d4,d15
	call	__f_ftod
.L639:
	mov	e6,d3,d2
.L706:
	mov	e4,d11,d10
	call	__d_div
	mov	e4,d3,d2
.L707:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16384
.L708:
	call	__d_mul
	mov	e4,d3,d2
.L709:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e6,[a2]0
.L710:
	call	__d_mul
	mov	e4,d3,d2
	call	__d_dtof
.L711:
	st.w	[a12]38,d2
.L712:
	ld.w	d15,[a15]18
.L713:
	st.w	[a12]42,d15
.L714:
	ld.w	d4,[a15]14
	mov.aa	a4,a12
.L644:
	call	IfxGpt12_IncrEnc_setRefreshPeriod
.L645:
	mov	d15,#0
.L715:
	st.w	[a12]14,d15
.L716:
	ld.bu	d15,[a12]14
.L717:
	or	d15,#1
	st.b	[a12]14,d15
.L718:
	ld.w	d15,[a15]22
.L719:
	st.w	[a12]56,d15
.L720:
	ld.w	d15,[a15]26
.L721:
	st.w	[a12]60,d15
.L722:
	mov	d15,#0
.L723:
	st.w	[a12],d15
.L724:
	mov	d15,#0
.L725:
	st.w	[a12]4,d15
.L726:
	mov	d15,#2
.L727:
	st.b	[a12]12,d15
.L728:
	mov	d15,#0
.L729:
	st.w	[a12]8,d15
.L730:
	ld.a	a2,[a15]40
.L731:
	ld.bu	d15,[a2]4
.L732:
	jne	d15,#3,.L16
.L733:
	mov	d15,#7
.L287:
	ld.bu	d0,[a13]20
.L734:
	insert	d15,d0,d15,#3,#3
	st.b	[a13]20,d15
.L288:
	ld.bu	d15,[a15]12
.L735:
	mov	d0,#2
	jeq	d15,d0,.L17
.L736:
	mov	d0,#4
	jeq	d15,d0,.L18
	j	.L19
.L17:
	mov	d15,#1
.L295:
	ld.bu	d0,[a13]20
.L737:
	insert	d15,d0,d15,#0,#3
	st.b	[a13]20,d15
.L296:
	j	.L20
.L18:
	mov	d15,#3
.L302:
	ld.bu	d0,[a13]20
.L738:
	insert	d15,d0,d15,#0,#3
	st.b	[a13]20,d15
.L303:
	j	.L21
.L19:
	mov	d8,#0
.L739:
	j	.L22
.L22:
.L21:
.L20:
	mov	d15,#1
.L305:
	ld.bu	d0,[a13]21
.L740:
	insert	d15,d0,d15,#0,#1
	st.b	[a13]21,d15
.L306:
	ld.bu	d15,[a15]4
.L741:
	jeq	d15,#0,.L23
.L742:
	mov	d15,#0
.L743:
	j	.L24
.L23:
	mov	d15,#1
.L24:
	ld.bu	d0,[a13]20
.L744:
	insert	d15,d0,d15,#7,#1
	st.b	[a13]20,d15
.L313:
	mov	d15,#0
.L320:
	jeq	d15,#0,.L25
.L745:
	mov	d15,#1
.L746:
	j	.L26
.L25:
	mov	d15,#0
.L26:
	ld.bu	d0,[a13]21
.L747:
	insert	d15,d0,d15,#1,#1
	st.b	[a13]21,d15
.L321:
	mov	d15,#1
.L328:
	ld.bu	d0,[a13]20
.L748:
	insert	d15,d0,d15,#6,#1
	st.b	[a13]20,d15
.L329:
	ld.w	d15,[a15]48
.L749:
	jeq	d15,#0,.L27
.L750:
	mov	d15,#5
.L336:
	ld.bu	d0,[a13]24
.L751:
	insert	d15,d0,d15,#3,#3
	st.b	[a13]24,d15
.L337:
	mov	d15,#1
.L344:
	ld.bu	d0,[a13]24
.L752:
	insert	d15,d0,d15,#0,#3
	st.b	[a13]24,d15
.L345:
	mov	d15,#0
.L352:
	jeq	d15,#0,.L28
.L753:
	mov	d15,#1
.L754:
	j	.L29
.L28:
	mov	d15,#0
.L29:
	ld.bu	d0,[a13]25
.L755:
	insert	d15,d0,d15,#2,#1
	st.b	[a13]25,d15
.L353:
	mov	d15,#1
.L360:
	jeq	d15,#0,.L30
.L756:
	mov	d15,#1
.L757:
	j	.L31
.L30:
	mov	d15,#0
.L31:
	ld.bu	d0,[a13]25
.L758:
	insert	d15,d0,d15,#3,#1
	st.b	[a13]25,d15
.L361:
	ld.hu	d15,[a15]54
.L759:
	ne	d15,d15,#0
.L368:
	jeq	d15,#0,.L32
.L760:
	mov	d15,#0
.L761:
	j	.L33
.L32:
	mov	d15,#1
.L33:
	ld.bu	d0,[a13]25
.L762:
	insert	d15,d0,d15,#4,#1
	st.b	[a13]25,d15
.L369:
	mov	d15,#0
.L376:
	ld.bu	d0,[a13]25
.L763:
	insert	d15,d0,d15,#1,#1
	st.b	[a13]25,d15
.L377:
	mov	d15,#0
.L384:
	ld.bu	d0,[a13]24
.L764:
	insert	d15,d0,d15,#6,#1
	st.b	[a13]24,d15
.L385:
	ld.hu	d15,[a15]54
.L765:
	jeq	d15,#0,.L34
.L391:
	jz.a	a13,.L35
.L35:
	movh.a	a2,#61444
.L646:
	lea	a2,[a2]@los(0xf003846c)
.L766:
	j	.L36
.L36:
	ld.bu	d0,[a15]56
.L767:
	ld.hu	d15,[a15]54
.L399:
	ld.bu	d1,[a2]
.L768:
	extr.u	d15,d15,#0,#8
.L769:
	insert	d15,d1,d15,#0,#8
	st.b	[a2],d15
.L770:
	ld.bu	d15,[a2]1
.L771:
	insert	d15,d15,d0,#3,#2
	st.b	[a2]1,d15
.L409:
	ld.bu	d15,[a2]3
.L772:
	or	d15,#2
	st.b	[a2]3,d15
.L400:
	ld.bu	d15,[a2]1
.L773:
	or	d15,#4
	st.b	[a2]1,d15
.L34:
.L27:
	mov	d15,#0
.L418:
	ld.bu	d0,[a13]28
.L774:
	insert	d15,d0,d15,#3,#3
	st.b	[a13]28,d15
.L419:
	mov	d15,#2
.L426:
	ld.bu	d0,[a13]28
.L775:
	insert	d15,d0,d15,#0,#3
	st.b	[a13]28,d15
.L427:
	mov	d15,#1
.L434:
	ld.bu	d0,[a13]29
.L776:
	insert	d15,d0,d15,#2,#1
	st.b	[a13]29,d15
.L435:
	mov	d15,#1
.L442:
	ld.bu	d0,[a13]29
.L777:
	insert	d15,d0,d15,#4,#2
	st.b	[a13]29,d15
.L443:
	mov	d15,#1
.L450:
	jeq	d15,#0,.L37
.L778:
	mov	d15,#1
.L779:
	j	.L38
.L37:
	mov	d15,#0
.L38:
	ld.bu	d0,[a13]29
.L780:
	insert	d15,d0,d15,#6,#1
	st.b	[a13]29,d15
.L451:
	ld.bu	d15,[a13]29
.L781:
	or	d15,#128
	st.b	[a13]29,d15
.L458:
	mov	d15,#0
.L465:
	ld.bu	d0,[a13]29
.L782:
	insert	d15,d0,d15,#1,#1
	st.b	[a13]29,d15
.L466:
	mov	d15,#0
.L473:
	ld.bu	d0,[a13]29
.L783:
	insert	d15,d0,d15,#0,#1
	st.b	[a13]29,d15
.L474:
	mov	d15,#0
.L481:
	ld.bu	d0,[a13]28
.L784:
	insert	d15,d0,d15,#7,#1
	st.b	[a13]28,d15
.L482:
	mov	d15,#1
.L489:
	ld.bu	d0,[a13]28
.L785:
	insert	d15,d0,d15,#6,#1
	st.b	[a13]28,d15
.L490:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_updateFromT3)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_updateFromT3)
.L786:
	st.a	[a12]76,a2
.L787:
	j	.L39
.L16:
	ld.a	a2,[a15]40
.L788:
	ld.bu	d15,[a2]4
.L789:
	jne	d15,#2,.L40
.L790:
	mov	d15,#7
.L497:
	ld.bu	d0,[a13]16
.L791:
	insert	d15,d0,d15,#3,#3
	st.b	[a13]16,d15
.L498:
	ld.bu	d15,[a15]12
.L792:
	mov	d0,#2
	jeq	d15,d0,.L41
.L793:
	mov	d0,#4
	jeq	d15,d0,.L42
	j	.L43
.L41:
	mov	d15,#1
.L505:
	ld.bu	d0,[a13]16
.L794:
	insert	d15,d0,d15,#0,#3
	st.b	[a13]16,d15
.L506:
	j	.L44
.L42:
	mov	d15,#3
.L512:
	ld.bu	d0,[a13]16
.L795:
	insert	d15,d0,d15,#0,#3
	st.b	[a13]16,d15
.L513:
	j	.L45
.L43:
	mov	d8,#0
.L796:
	j	.L46
.L46:
.L45:
.L44:
	mov	d15,#1
.L515:
	ld.bu	d0,[a13]17
.L797:
	insert	d15,d0,d15,#0,#1
	st.b	[a13]17,d15
.L516:
	ld.bu	d15,[a15]4
.L798:
	jeq	d15,#0,.L47
.L799:
	mov	d15,#0
.L800:
	j	.L48
.L47:
	mov	d15,#1
.L48:
	ld.bu	d0,[a13]16
.L801:
	insert	d15,d0,d15,#7,#1
	st.b	[a13]16,d15
.L523:
	mov	d15,#1
.L530:
	ld.bu	d0,[a13]16
.L802:
	insert	d15,d0,d15,#6,#1
	st.b	[a13]16,d15
.L531:
	ld.w	d15,[a15]48
.L803:
	jeq	d15,#0,.L49
.L804:
	mov	d15,#5
.L537:
	ld.bu	d0,[a13]24
.L805:
	insert	d15,d0,d15,#3,#3
	st.b	[a13]24,d15
.L538:
	mov	d15,#0
.L539:
	ld.bu	d0,[a13]24
.L806:
	insert	d15,d0,d15,#0,#3
	st.b	[a13]24,d15
.L540:
	mov	d15,#1
.L541:
	jeq	d15,#0,.L50
.L807:
	mov	d15,#1
.L808:
	j	.L51
.L50:
	mov	d15,#0
.L51:
	ld.bu	d0,[a13]25
.L809:
	insert	d15,d0,d15,#2,#1
	st.b	[a13]25,d15
.L542:
	mov	d15,#0
.L543:
	jeq	d15,#0,.L52
.L810:
	mov	d15,#1
.L811:
	j	.L53
.L52:
	mov	d15,#0
.L53:
	ld.bu	d0,[a13]25
.L812:
	insert	d15,d0,d15,#3,#1
	st.b	[a13]25,d15
.L544:
	mov	d15,#0
.L545:
	jeq	d15,#0,.L54
.L813:
	mov	d15,#0
.L814:
	j	.L55
.L54:
	mov	d15,#1
.L55:
	ld.bu	d0,[a13]25
.L815:
	insert	d15,d0,d15,#4,#1
	st.b	[a13]25,d15
.L546:
	mov	d15,#0
.L547:
	ld.bu	d0,[a13]25
.L816:
	insert	d15,d0,d15,#1,#1
	st.b	[a13]25,d15
.L548:
	mov	d15,#0
.L549:
	ld.bu	d0,[a13]24
.L817:
	insert	d15,d0,d15,#6,#1
	st.b	[a13]24,d15
.L49:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_updateFromT2)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_updateFromT2)
.L818:
	st.a	[a12]76,a2
.L40:
.L39:
	ld.bu	d15,[a15]58
.L819:
	jne	d15,#1,.L56
.L820:
	ld.a	a4,[a15]40
.L821:
	ld.b	d4,[a15]52
.L822:
	ld.bu	d5,[a15]57
	call	IfxGpt12_initTxInPinWithPadLevel
.L823:
	ld.a	a4,[a15]44
.L824:
	ld.b	d4,[a15]52
.L825:
	ld.bu	d5,[a15]57
	call	IfxGpt12_initTxEudInPinWithPadLevel
.L826:
	ld.w	d15,[a15]48
.L827:
	jeq	d15,#0,.L57
.L828:
	ld.a	a4,[a15]48
.L829:
	ld.b	d4,[a15]52
.L830:
	ld.bu	d5,[a15]57
	call	IfxGpt12_initTxInPinWithPadLevel
.L57:
.L56:
	movh.a	a2,#@his(.2.cnt)
	lea	a2,[a2]@los(.2.cnt)
	ld.d	e10,[a2]0
.L831:
	ld.w	d15,[a15]6
.L832:
	mul	d4,d15,#2
	call	__d_itod
	mov	e6,d3,d2
.L833:
	mov	e4,d11,d10
	call	__d_div
	mov	e10,d3,d2
.L834:
	mov.aa	a4,a13
.L647:
	call	IfxGpt12_T5_getFrequency
.L648:
	mov	d4,d2
	call	__f_ftod
	mov	e6,d3,d2
.L835:
	mov	e4,d11,d10
	call	__d_mul
	mov	e4,d3,d2
	call	__d_dtof
.L836:
	st.w	[a12]34,d2
.L837:
	ld.bu	d15,[a15]30
.L838:
	st.b	[a12]80,d15
.L839:
	ld.bu	d15,[a15]30
.L840:
	jeq	d15,#0,.L58
.L550:
	movh	d15,#16256
.L841:
	st.w	[a10]4,d15
.L842:
	ld.w	d15,[a15]32
.L843:
	st.w	[a10],d15
.L844:
	ld.w	d15,[a15]14
.L845:
	st.w	[a10]8,d15
.L846:
	lea	a4,[a12]64
.L847:
	lea	a5,[a10]0
	call	Ifx_LowPassPt1F32_init
.L58:
	mov	d2,d8
.L649:
	j	.L59
.L59:
	ret
.L278:
	
__IfxGpt12_IncrEnc_init_function_end:
	.size	IfxGpt12_IncrEnc_init,__IfxGpt12_IncrEnc_init_function_end-IfxGpt12_IncrEnc_init
.L156:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_initConfig',code,cluster('IfxGpt12_IncrEnc_initConfig')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_initConfig'
	.align	2
	
	.global	IfxGpt12_IncrEnc_initConfig
; Function IfxGpt12_IncrEnc_initConfig
.L125:
IfxGpt12_IncrEnc_initConfig:	.type	func
	mov.aa	a15,a4
.L651:
	mov.aa	a12,a5
.L652:
	mov.aa	a4,a15
	call	IfxStdIf_Pos_initConfig
.L650:
	mov	d15,#2
.L852:
	st.b	[a15]12,d15
.L853:
	mov	d15,#30544
	addih	d15,d15,#15830
.L854:
	st.w	[a15]22,d15
.L855:
	mov.u	d15,#58963
	addih	d15,d15,#17666
.L856:
	st.w	[a15]26,d15
.L857:
	mov	d15,#1
.L858:
	st.b	[a15]30,d15
.L859:
	ld.w	d15,[a15]26
.L860:
	movh	d0,#16384
.L861:
	div.f	d15,d15,d0
.L862:
	mov	d0,#4059
	addih	d0,d0,#16457
.L863:
	mul.f	d15,d15,d0
.L864:
	movh	d0,#16672
.L865:
	mul.f	d15,d15,d0
.L866:
	st.w	[a15]32,d15
.L867:
	mov.a	a2,#0
.L868:
	st.a	[a15]40,a2
.L869:
	mov.a	a2,#0
.L870:
	st.a	[a15]44,a2
.L871:
	mov.a	a2,#0
.L872:
	st.a	[a15]48,a2
.L873:
	mov	d15,#0
.L874:
	st.b	[a15]52,d15
.L875:
	st.a	[a15]36,a12
.L876:
	mov	d15,#0
.L877:
	st.h	[a15]54,d15
.L878:
	mov	d15,#0
.L879:
	st.b	[a15]56,d15
.L880:
	mov	d15,#0
.L881:
	st.b	[a15]57,d15
.L882:
	mov	d15,#1
.L883:
	st.b	[a15]58,d15
.L884:
	ret
.L553:
	
__IfxGpt12_IncrEnc_initConfig_function_end:
	.size	IfxGpt12_IncrEnc_initConfig,__IfxGpt12_IncrEnc_initConfig_function_end-IfxGpt12_IncrEnc_initConfig
.L161:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_onZeroIrq',code,cluster('IfxGpt12_IncrEnc_onZeroIrq')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_onZeroIrq'
	.align	2
	
	.global	IfxGpt12_IncrEnc_onZeroIrq
; Function IfxGpt12_IncrEnc_onZeroIrq
.L127:
IfxGpt12_IncrEnc_onZeroIrq:	.type	func
	ld.bu	d15,[a4]14
.L962:
	jz.t	d15:0,.L60
.L963:
	ld.bu	d15,[a4]14
.L964:
	insert	d15,d15,#0,#0,#1
	st.b	[a4]14,d15
.L60:
	ld.bu	d15,[a4]12
.L965:
	jne	d15,#0,.L61
.L966:
	ld.w	d15,[a4]8
.L967:
	add	d15,#1
	st.w	[a4]8,d15
.L968:
	j	.L62
.L61:
	ld.w	d15,[a4]8
.L969:
	add	d15,#-1
	st.w	[a4]8,d15
.L62:
	ret
.L587:
	
__IfxGpt12_IncrEnc_onZeroIrq_function_end:
	.size	IfxGpt12_IncrEnc_onZeroIrq,__IfxGpt12_IncrEnc_onZeroIrq_function_end-IfxGpt12_IncrEnc_onZeroIrq
.L226:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_reset',code,cluster('IfxGpt12_IncrEnc_reset')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_reset'
	.align	2
	
	.global	IfxGpt12_IncrEnc_reset
; Function IfxGpt12_IncrEnc_reset
.L129:
IfxGpt12_IncrEnc_reset:	.type	func
	mov	d15,#0
.L974:
	st.w	[a4],d15
.L975:
	mov	d15,#0
.L976:
	st.w	[a4]8,d15
.L977:
	mov	d15,#0
.L978:
	st.w	[a4]4,d15
.L979:
	mov	d15,#0
.L980:
	st.w	[a4]14,d15
.L981:
	ld.bu	d15,[a4]14
.L982:
	or	d15,#1
	st.b	[a4]14,d15
.L983:
	ret
.L589:
	
__IfxGpt12_IncrEnc_reset_function_end:
	.size	IfxGpt12_IncrEnc_reset,__IfxGpt12_IncrEnc_reset_function_end-IfxGpt12_IncrEnc_reset
.L231:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_resetFaults',code,cluster('IfxGpt12_IncrEnc_resetFaults')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_resetFaults'
	.align	2
	
	.global	IfxGpt12_IncrEnc_resetFaults
; Function IfxGpt12_IncrEnc_resetFaults
.L131:
IfxGpt12_IncrEnc_resetFaults:	.type	func
	mov	d0,#0
.L653:
	ld.bu	d15,[a4]14
	and	d15,#1
.L988:
	insert	d0,d0,d15,#0,#1
.L989:
	st.w	[a4]14,d0
.L990:
	ret
.L591:
	
__IfxGpt12_IncrEnc_resetFaults_function_end:
	.size	IfxGpt12_IncrEnc_resetFaults,__IfxGpt12_IncrEnc_resetFaults_function_end-IfxGpt12_IncrEnc_resetFaults
.L236:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_setOffset',code,cluster('IfxGpt12_IncrEnc_setOffset')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_setOffset'
	.align	2
	
	.global	IfxGpt12_IncrEnc_setOffset
; Function IfxGpt12_IncrEnc_setOffset
.L133:
IfxGpt12_IncrEnc_setOffset:	.type	func
	st.w	[a4]18,d4
.L995:
	ld.bu	d15,[a4]14
.L996:
	insert	d15,d15,#0,#0,#1
	st.b	[a4]14,d15
.L997:
	ret
.L594:
	
__IfxGpt12_IncrEnc_setOffset_function_end:
	.size	IfxGpt12_IncrEnc_setOffset,__IfxGpt12_IncrEnc_setOffset_function_end-IfxGpt12_IncrEnc_setOffset
.L241:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_setRefreshPeriod',code,cluster('IfxGpt12_IncrEnc_setRefreshPeriod')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_setRefreshPeriod'
	.align	2
	
	.global	IfxGpt12_IncrEnc_setRefreshPeriod
; Function IfxGpt12_IncrEnc_setRefreshPeriod
.L135:
IfxGpt12_IncrEnc_setRefreshPeriod:	.type	func
	mov.aa	a15,a4
.L656:
	mov	d15,d4
.L657:
	st.w	[a15]26,d15
.L1002:
	movh.a	a2,#@his(.2.cnt)
	lea	a2,[a2]@los(.2.cnt)
	ld.d	e8,[a2]0
.L1003:
	ld.w	d4,[a15]22
.L655:
	call	__d_itod
.L654:
	mov	e6,d3,d2
.L1004:
	mov	e4,d9,d8
	call	__d_div
	mov	e8,d3,d2
.L1005:
	mov	d4,d15
.L658:
	call	__f_ftod
.L659:
	mov	e6,d3,d2
.L1006:
	mov	e4,d9,d8
	call	__d_div
	mov	e4,d3,d2
	call	__d_dtof
.L1007:
	st.w	[a15]30,d2
.L1008:
	ld.w	d0,[a15]42
.L1009:
	ld.w	d1,[a15]22
	itof	d1,d1
.L1010:
	mul.f	d0,d0,d1
.L1011:
	mul.f	d4,d0,d15
	call	__f_ftod
.L1012:
	movh.a	a2,#@his(.2.cnt)
	lea	a2,[a2]@los(.2.cnt)
	ld.d	e6,[a2]0
.L1013:
	mov	e4,d3,d2
	call	__d_div
	mov	e4,d3,d2
	call	__d_dtoi
.L1014:
	st.w	[a15]46,d2
.L1015:
	ret
.L597:
	
__IfxGpt12_IncrEnc_setRefreshPeriod_function_end:
	.size	IfxGpt12_IncrEnc_setRefreshPeriod,__IfxGpt12_IncrEnc_setRefreshPeriod_function_end-IfxGpt12_IncrEnc_setRefreshPeriod
.L246:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_stdIfPosInit',code,cluster('IfxGpt12_IncrEnc_stdIfPosInit')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_stdIfPosInit'
	.align	2
	
	.global	IfxGpt12_IncrEnc_stdIfPosInit
; Function IfxGpt12_IncrEnc_stdIfPosInit
.L137:
IfxGpt12_IncrEnc_stdIfPosInit:	.type	func
	mov.aa	a15,a4
.L661:
	mov.aa	a12,a5
.L662:
	mov	d4,#0
.L1025:
	mov	d5,#92
	mov.aa	a4,a15
	call	memset
.L660:
	st.a	[a15],a12
.L1026:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_onZeroIrq)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_onZeroIrq)
.L1027:
	st.a	[a15]4,a2
.L1028:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getAbsolutePosition)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getAbsolutePosition)
.L1029:
	st.a	[a15]8,a2
.L1030:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getDirection)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getDirection)
.L1031:
	st.a	[a15]20,a2
.L1032:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getFault)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getFault)
.L1033:
	st.a	[a15]24,a2
.L1034:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getOffset)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getOffset)
.L1035:
	st.a	[a15]12,a2
.L1036:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getPeriodPerRotation)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getPeriodPerRotation)
.L1037:
	st.a	[a15]32,a2
.L1038:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getPosition)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getPosition)
.L1039:
	st.a	[a15]16,a2
.L1040:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getRawPosition)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getRawPosition)
.L1041:
	st.a	[a15]28,a2
.L1042:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getRefreshPeriod)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getRefreshPeriod)
.L1043:
	st.a	[a15]36,a2
.L1044:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getResolution)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getResolution)
.L1045:
	st.a	[a15]40,a2
.L1046:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getSensorType)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getSensorType)
.L1047:
	st.a	[a15]44,a2
.L1048:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_reset)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_reset)
.L1049:
	st.a	[a15]56,a2
.L1050:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_resetFaults)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_resetFaults)
.L1051:
	st.a	[a15]60,a2
.L1052:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getSpeed)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getSpeed)
.L1053:
	st.a	[a15]64,a2
.L1054:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_update)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_update)
.L1055:
	st.a	[a15]68,a2
.L1056:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_setOffset)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_setOffset)
.L1057:
	st.a	[a15]72,a2
.L1058:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_setRefreshPeriod)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_setRefreshPeriod)
.L1059:
	st.a	[a15]88,a2
.L1060:
	movh.a	a2,#@his(IfxGpt12_IncrEnc_getTurn)
	lea	a2,[a2]@los(IfxGpt12_IncrEnc_getTurn)
.L1061:
	st.a	[a15]48,a2
.L1062:
	mov	d2,#1
.L1063:
	j	.L63
.L63:
	ret
.L602:
	
__IfxGpt12_IncrEnc_stdIfPosInit_function_end:
	.size	IfxGpt12_IncrEnc_stdIfPosInit,__IfxGpt12_IncrEnc_stdIfPosInit_function_end-IfxGpt12_IncrEnc_stdIfPosInit
.L256:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_update',code,cluster('IfxGpt12_IncrEnc_update')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_update'
	.align	2
	
	.global	IfxGpt12_IncrEnc_update
; Function IfxGpt12_IncrEnc_update
.L139:
IfxGpt12_IncrEnc_update:	.type	func
	ld.a	a15,[a4]76
.L1020:
	calli	a15
.L663:
	ret
.L600:
	
__IfxGpt12_IncrEnc_update_function_end:
	.size	IfxGpt12_IncrEnc_update,__IfxGpt12_IncrEnc_update_function_end-IfxGpt12_IncrEnc_update
.L251:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateFromT2',code,cluster('IfxGpt12_IncrEnc_updateFromT2')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateFromT2'
	.align	2
	
; Function IfxGpt12_IncrEnc_updateFromT2
.L141:
IfxGpt12_IncrEnc_updateFromT2:	.type	func
	mov.aa	a15,a4
.L665:
	ld.a	a2,[a15]52
.L666:
	ld.bu	d15,[a2]17
.L1068:
	jz.t	d15:7,.L64
.L1069:
	mov	d15,#1
.L1070:
	j	.L65
.L64:
	mov	d15,#0
.L65:
	st.b	[a15]12,d15
.L1071:
	ld.w	d15,[a2]52
.L667:
	ld.w	d0,[a15]18
.L1072:
	add	d9,d15,d0
.L668:
	ld.w	d15,[a15]22
.L1073:
	jlt	d9,d15,.L66
.L1074:
	ld.w	d15,[a15]22
.L1075:
	div	e8,d9,d15
.L1076:
	j	.L67
.L66:
	jge	d9,#0,.L68
.L1077:
	ld.w	d15,[a15]22
.L1078:
	add	d9,d15
.L68:
.L67:
	mov.aa	a4,a15
	mov	d4,d9
.L669:
	call	IfxGpt12_IncrEnc_updateSpeedFromT2
.L664:
	st.w	[a15],d9
.L1079:
	ret
.L606:
	
__IfxGpt12_IncrEnc_updateFromT2_function_end:
	.size	IfxGpt12_IncrEnc_updateFromT2,__IfxGpt12_IncrEnc_updateFromT2_function_end-IfxGpt12_IncrEnc_updateFromT2
.L261:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateFromT3',code,cluster('IfxGpt12_IncrEnc_updateFromT3')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateFromT3'
	.align	2
	
; Function IfxGpt12_IncrEnc_updateFromT3
.L143:
IfxGpt12_IncrEnc_updateFromT3:	.type	func
	mov.aa	a15,a4
.L671:
	ld.a	a2,[a15]52
.L672:
	ld.bu	d15,[a2]21
.L1084:
	jz.t	d15:7,.L69
.L1085:
	mov	d15,#1
.L1086:
	j	.L70
.L69:
	mov	d15,#0
.L70:
	st.b	[a15]12,d15
.L1087:
	ld.w	d15,[a2]56
.L673:
	ld.w	d0,[a15]18
.L1088:
	add	d9,d15,d0
.L674:
	ld.w	d15,[a15]22
.L1089:
	jlt	d9,d15,.L71
.L1090:
	ld.w	d15,[a15]22
.L1091:
	div	e8,d9,d15
.L1092:
	j	.L72
.L71:
	jge	d9,#0,.L73
.L1093:
	ld.w	d15,[a15]22
.L1094:
	add	d9,d15
.L73:
.L72:
	mov.aa	a4,a15
	mov	d4,d9
.L675:
	call	IfxGpt12_IncrEnc_updateSpeedFromT3
.L670:
	st.w	[a15],d9
.L1095:
	ret
.L610:
	
__IfxGpt12_IncrEnc_updateFromT3_function_end:
	.size	IfxGpt12_IncrEnc_updateFromT3,__IfxGpt12_IncrEnc_updateFromT3_function_end-IfxGpt12_IncrEnc_updateFromT3
.L266:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateSpeedFromT2',code,cluster('IfxGpt12_IncrEnc_updateSpeedFromT2')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateSpeedFromT2'
	.align	2
	
; Function IfxGpt12_IncrEnc_updateSpeedFromT2
.L145:
IfxGpt12_IncrEnc_updateSpeedFromT2:	.type	func
	mov.aa	a15,a4
.L678:
	ld.bu	d15,[a15]12
.L1100:
	jne	d15,#0,.L74
.L1101:
	ld.w	d15,[a15]
.L679:
	sub	d4,d15
.L677:
	j	.L75
.L74:
	ld.w	d15,[a15]
.L1102:
	sub	d4,d15,d4
.L75:
	jge	d4,#0,.L76
.L1103:
	ld.w	d15,[a15]22
.L1104:
	add	d4,d15
.L76:
	itof	d15,d4
.L1105:
	ld.w	d0,[a15]30
.L1106:
	mul.f	d4,d15,d0
.L680:
	ld.bu	d15,[a15]12
.L1107:
	jne	d15,#0,.L77
.L1108:
	j	.L78
.L77:
	insn.t	d4,d4:31,d4:31
.L78:
	ld.bu	d15,[a15]80
.L1109:
	jeq	d15,#0,.L79
.L1110:
	lea	a4,[a15]64
.L676:
	call	Ifx_LowPassPt1F32_do
.L681:
	st.w	[a15]4,d2
.L1111:
	j	.L80
.L79:
	st.w	[a15]4,d4
.L80:
	ret
.L614:
	
__IfxGpt12_IncrEnc_updateSpeedFromT2_function_end:
	.size	IfxGpt12_IncrEnc_updateSpeedFromT2,__IfxGpt12_IncrEnc_updateSpeedFromT2_function_end-IfxGpt12_IncrEnc_updateSpeedFromT2
.L271:
	; End of function
	
	.sdecl	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateSpeedFromT3',code,cluster('IfxGpt12_IncrEnc_updateSpeedFromT3')
	.sect	'.text.IfxGpt12_IncrEnc.IfxGpt12_IncrEnc_updateSpeedFromT3'
	.align	2
	
; Function IfxGpt12_IncrEnc_updateSpeedFromT3
.L147:
IfxGpt12_IncrEnc_updateSpeedFromT3:	.type	func
	mov.aa	a15,a4
.L684:
	ld.bu	d15,[a15]12
.L1116:
	jne	d15,#0,.L81
.L1117:
	ld.w	d15,[a15]
.L685:
	sub	d4,d15
.L683:
	j	.L82
.L81:
	ld.w	d15,[a15]
.L1118:
	sub	d4,d15,d4
.L82:
	jge	d4,#0,.L83
.L1119:
	ld.w	d15,[a15]22
.L1120:
	add	d4,d15
.L83:
	ld.w	d15,[a15]46
.L1121:
	jge	d15,d4,.L84
.L1122:
	itof	d15,d4
.L1123:
	ld.w	d0,[a15]30
.L1124:
	mul.f	d4,d15,d0
.L686:
	j	.L85
.L84:
	ld.a	a2,[a15]52
.L627:
	jz.a	a2,.L86
.L86:
	movh.a	a4,#61444
.L682:
	lea	a4,[a4]@los(0xf0038470)
.L1125:
	j	.L87
.L87:
	ld.bu	d15,[a4]3
	and	d15,#1
.L1126:
	jeq	d15,#1,.L88
.L631:
	jz.a	a2,.L89
.L89:
	movh.a	a4,#61444
	lea	a4,[a4]@los(0xf0038460)
.L1127:
	j	.L90
.L90:
	ld.bu	d15,[a4]3
.L1128:
	jz.t	d15:0,.L91
.L1129:
	ld.bu	d15,[a4]3
.L1130:
	or	d15,#2
	st.b	[a4]3,d15
.L1131:
	ld.w	d15,[a15]34
.L1132:
	ld.hu	d0,[a2]48
	extr.u	d0,d0,#0,#16
	utof	d0,d0
.L1133:
	div.f	d4,d15,d0
.L687:
	j	.L92
.L91:
	ld.w	d4,[a15]4
.L92:
	j	.L93
.L88:
	ld.bu	d15,[a4]3
.L1134:
	or	d15,#2
	st.b	[a4]3,d15
.L1135:
	mov	d4,#0
.L93:
.L85:
	ld.bu	d15,[a15]12
.L1136:
	jne	d15,#0,.L94
.L1137:
	j	.L95
.L94:
	insn.t	d4,d4:31,d4:31
.L95:
	ld.bu	d15,[a15]80
.L1138:
	jeq	d15,#0,.L96
.L1139:
	lea	a4,[a15]64
.L1140:
	call	Ifx_LowPassPt1F32_do
.L688:
	st.w	[a15]4,d2
.L1141:
	j	.L97
.L96:
	st.w	[a15]4,d4
.L97:
	ret
.L619:
	
__IfxGpt12_IncrEnc_updateSpeedFromT3_function_end:
	.size	IfxGpt12_IncrEnc_updateSpeedFromT3,__IfxGpt12_IncrEnc_updateSpeedFromT3_function_end-IfxGpt12_IncrEnc_updateSpeedFromT3
.L276:
	; End of function
	
	.calls	'IfxGpt12_IncrEnc_getAbsolutePosition','__f_ftod'
	.calls	'IfxGpt12_IncrEnc_getAbsolutePosition','__d_mul'
	.calls	'IfxGpt12_IncrEnc_getAbsolutePosition','__d_dtof'
	.calls	'IfxGpt12_IncrEnc_init','__f_ftod'
	.calls	'IfxGpt12_IncrEnc_init','__d_div'
	.calls	'IfxGpt12_IncrEnc_init','__d_mul'
	.calls	'IfxGpt12_IncrEnc_init','__d_dtof'
	.calls	'IfxGpt12_IncrEnc_init','__d_itod'
	.calls	'IfxGpt12_IncrEnc_setRefreshPeriod','__d_itod'
	.calls	'IfxGpt12_IncrEnc_setRefreshPeriod','__d_div'
	.calls	'IfxGpt12_IncrEnc_setRefreshPeriod','__f_ftod'
	.calls	'IfxGpt12_IncrEnc_setRefreshPeriod','__d_dtof'
	.calls	'IfxGpt12_IncrEnc_setRefreshPeriod','__d_dtoi'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getAbsolutePosition'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getDirection'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getFault'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getOffset'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getPeriodPerRotation'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getPosition'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getRawPosition'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getRefreshPeriod'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getResolution'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getSensorType'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getSpeed'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_getTurn'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_onZeroIrq'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_reset'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_resetFaults'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_setOffset'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_setRefreshPeriod'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_update'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_updateFromT2'
	.calls	'__INDIRECT__','IfxGpt12_IncrEnc_updateFromT3'
	.calls	'IfxGpt12_IncrEnc_init','IfxGpt12_IncrEnc_setRefreshPeriod'
	.calls	'IfxGpt12_IncrEnc_init','IfxGpt12_initTxInPinWithPadLevel'
	.calls	'IfxGpt12_IncrEnc_init','IfxGpt12_initTxEudInPinWithPadLevel'
	.calls	'IfxGpt12_IncrEnc_init','IfxGpt12_T5_getFrequency'
	.calls	'IfxGpt12_IncrEnc_init','Ifx_LowPassPt1F32_init'
	.calls	'IfxGpt12_IncrEnc_initConfig','IfxStdIf_Pos_initConfig'
	.calls	'IfxGpt12_IncrEnc_stdIfPosInit','memset'
	.calls	'IfxGpt12_IncrEnc_update','__INDIRECT__'
	.calls	'IfxGpt12_IncrEnc_updateFromT2','IfxGpt12_IncrEnc_updateSpeedFromT2'
	.calls	'IfxGpt12_IncrEnc_updateFromT3','IfxGpt12_IncrEnc_updateSpeedFromT3'
	.calls	'IfxGpt12_IncrEnc_updateSpeedFromT2','Ifx_LowPassPt1F32_do'
	.calls	'IfxGpt12_IncrEnc_updateSpeedFromT3','Ifx_LowPassPt1F32_do'
	.calls	'IfxGpt12_IncrEnc_getAbsolutePosition','',0
	.calls	'IfxGpt12_IncrEnc_getDirection','',0
	.calls	'IfxGpt12_IncrEnc_getFault','',0
	.calls	'IfxGpt12_IncrEnc_getOffset','',0
	.calls	'IfxGpt12_IncrEnc_getPeriodPerRotation','',0
	.calls	'IfxGpt12_IncrEnc_getPosition','',0
	.calls	'IfxGpt12_IncrEnc_getRawPosition','',0
	.calls	'IfxGpt12_IncrEnc_getRefreshPeriod','',0
	.calls	'IfxGpt12_IncrEnc_getResolution','',0
	.calls	'IfxGpt12_IncrEnc_getSensorType','',0
	.calls	'IfxGpt12_IncrEnc_getSpeed','',0
	.calls	'IfxGpt12_IncrEnc_getTurn','',0
	.calls	'IfxGpt12_IncrEnc_init','',16
	.calls	'IfxGpt12_IncrEnc_initConfig','',0
	.calls	'IfxGpt12_IncrEnc_onZeroIrq','',0
	.calls	'IfxGpt12_IncrEnc_reset','',0
	.calls	'IfxGpt12_IncrEnc_resetFaults','',0
	.calls	'IfxGpt12_IncrEnc_setOffset','',0
	.calls	'IfxGpt12_IncrEnc_setRefreshPeriod','',0
	.calls	'IfxGpt12_IncrEnc_stdIfPosInit','',0
	.calls	'IfxGpt12_IncrEnc_update','',0
	.calls	'IfxGpt12_IncrEnc_updateFromT2','',0
	.calls	'IfxGpt12_IncrEnc_updateFromT3','',0
	.calls	'IfxGpt12_IncrEnc_updateSpeedFromT2','',0
	.extern	IfxStdIf_Pos_initConfig
	.extern	Ifx_LowPassPt1F32_init
	.extern	Ifx_LowPassPt1F32_do
	.extern	IfxGpt12_T5_getFrequency
	.extern	IfxGpt12_initTxEudInPinWithPadLevel
	.extern	IfxGpt12_initTxInPinWithPadLevel
	.extern	memset
	.extern	__f_ftod
	.extern	__d_mul
	.extern	__d_dtof
	.extern	__d_div
	.extern	__d_itod
	.extern	__d_dtoi
	.extern	__INDIRECT__
	.calls	'IfxGpt12_IncrEnc_updateSpeedFromT3','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L149:
	.word	95129
	.half	3
	.word	.L150
	.byte	4
.L148:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L151
	.byte	2
	.byte	'unsigned long int',0,4,7
.L277:
	.byte	2
	.byte	'unsigned char',0,1,8,3,1,106,5,1,4
	.byte	'notSynchronised',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'signalLoss',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'signalDegradation',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'trackingLoss',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'commError',0,1
	.word	267
	.byte	1,3,2,35,0,0
.L563:
	.byte	5,1,103,9,4,6
	.byte	'status',0
	.word	246
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	284
	.byte	1,2,35,0,0,7
	.byte	'void',0,8
	.word	446
	.byte	9
	.byte	'IfxStdIf_InterfaceDriver',0,2,118,15
	.word	452
	.byte	10,1,1,11
	.word	452
	.byte	0,8
	.word	490
	.byte	9
	.byte	'IfxStdIf_Pos_OnZeroIrq',0,1,135,1,16
	.word	499
.L557:
	.byte	2
	.byte	'float',0,4,4,12
	.word	536
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	545
	.byte	9
	.byte	'IfxStdIf_Pos_GetAbsolutePosition',0,1,129,1,19
	.word	558
.L566:
	.byte	2
	.byte	'long int',0,4,5,12
	.word	605
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	617
	.byte	9
	.byte	'IfxStdIf_Pos_GetOffset',0,1,142,1,18
	.word	630
	.byte	9
	.byte	'IfxStdIf_Pos_GetPosition',0,1,152,1,19
	.word	558
.L560:
	.byte	13,1,95,9,1,14
	.byte	'IfxStdIf_Pos_Dir_forward',0,0,14
	.byte	'IfxStdIf_Pos_Dir_backward',0,1,14
	.byte	'IfxStdIf_Pos_Dir_unknown',0,2,0,12
	.word	701
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	789
	.byte	9
	.byte	'IfxStdIf_Pos_GetDirection',0,1,161,1,28
	.word	802
	.byte	12
	.word	413
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	842
	.byte	9
	.byte	'IfxStdIf_Pos_GetFault',0,1,168,1,31
	.word	855
	.byte	9
	.byte	'IfxStdIf_Pos_GetRawPosition',0,1,184,1,18
	.word	630
.L569:
	.byte	2
	.byte	'unsigned short int',0,2,7,12
	.word	928
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	950
	.byte	9
	.byte	'IfxStdIf_Pos_GetPeriodPerRotation',0,1,175,1,18
	.word	963
	.byte	9
	.byte	'IfxStdIf_Pos_GetRefreshPeriod',0,1,190,1,19
	.word	558
	.byte	9
	.byte	'IfxStdIf_Pos_GetResolution',0,1,196,1,18
	.word	630
.L580:
	.byte	13,1,84,9,1,14
	.byte	'IfxStdIf_Pos_SensorType_encoder',0,0,14
	.byte	'IfxStdIf_Pos_SensorType_hall',0,1,14
	.byte	'IfxStdIf_Pos_SensorType_resolver',0,2,14
	.byte	'IfxStdIf_Pos_SensorType_angletrk',0,3,14
	.byte	'IfxStdIf_Pos_SensorType_igmr',0,4,14
	.byte	'IfxStdIf_Pos_SensorType_virtual',0,5,0,12
	.word	1086
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	1292
	.byte	9
	.byte	'IfxStdIf_Pos_GetSensorType',0,1,202,1,35
	.word	1305
	.byte	9
	.byte	'IfxStdIf_Pos_GetTurn',0,1,214,1,18
	.word	630
	.byte	9
	.byte	'IfxStdIf_Pos_OnEventA',0,1,221,1,16
	.word	499
	.byte	9
	.byte	'IfxStdIf_Pos_Reset',0,1,239,1,16
	.word	499
	.byte	9
	.byte	'IfxStdIf_Pos_ResetFaults',0,1,248,1,16
	.word	499
	.byte	9
	.byte	'IfxStdIf_Pos_GetSpeed',0,1,208,1,19
	.word	558
	.byte	9
	.byte	'IfxStdIf_Pos_Update',0,1,230,1,16
	.word	499
	.byte	10,1,1,11
	.word	452
	.byte	11
	.word	605
	.byte	0,8
	.word	1529
	.byte	9
	.byte	'IfxStdIf_Pos_SetOffset',0,1,255,1,16
	.word	1543
	.byte	10,1,1,11
	.word	452
	.byte	11
	.word	536
	.byte	0,8
	.word	1580
	.byte	9
	.byte	'IfxStdIf_Pos_SetPosition',0,1,134,2,16
	.word	1594
	.byte	9
	.byte	'IfxStdIf_Pos_SetRawPosition',0,1,140,2,16
	.word	1543
	.byte	9
	.byte	'IfxStdIf_Pos_SetSpeed',0,1,147,2,16
	.word	1594
	.byte	9
	.byte	'IfxStdIf_Pos_SetRefreshPeriod',0,1,154,2,16
	.word	1594
	.byte	15
	.byte	'IfxStdIf_Pos_',0,1,158,2,8,92,6
	.byte	'driver',0
	.word	457
	.byte	4,2,35,0,6
	.byte	'onZeroIrq',0
	.word	504
	.byte	4,2,35,4,6
	.byte	'getAbsolutePosition',0
	.word	563
	.byte	4,2,35,8,6
	.byte	'getOffset',0
	.word	635
	.byte	4,2,35,12,6
	.byte	'getPosition',0
	.word	667
	.byte	4,2,35,16,6
	.byte	'getDirection',0
	.word	807
	.byte	4,2,35,20,6
	.byte	'getFault',0
	.word	860
	.byte	4,2,35,24,6
	.byte	'getRawPosition',0
	.word	891
	.byte	4,2,35,28,6
	.byte	'getPeriodPerRotation',0
	.word	968
	.byte	4,2,35,32,6
	.byte	'getRefreshPeriod',0
	.word	1011
	.byte	4,2,35,36,6
	.byte	'getResolution',0
	.word	1050
	.byte	4,2,35,40,6
	.byte	'getSensorType',0
	.word	1310
	.byte	4,2,35,44,6
	.byte	'getTurn',0
	.word	1346
	.byte	4,2,35,48,6
	.byte	'onEventA',0
	.word	1376
	.byte	4,2,35,52,6
	.byte	'reset',0
	.word	1407
	.byte	4,2,35,56,6
	.byte	'resetFaults',0
	.word	1435
	.byte	4,2,35,60,6
	.byte	'getSpeed',0
	.word	1469
	.byte	4,2,35,64,6
	.byte	'update',0
	.word	1500
	.byte	4,2,35,68,6
	.byte	'setOffset',0
	.word	1548
	.byte	4,2,35,72,6
	.byte	'setPosition',0
	.word	1599
	.byte	4,2,35,76,6
	.byte	'setRawPosition',0
	.word	1633
	.byte	4,2,35,80,6
	.byte	'setSpeed',0
	.word	1670
	.byte	4,2,35,84,6
	.byte	'setRefreshPeriod',0
	.word	1701
	.byte	4,2,35,88,0
.L603:
	.byte	8
	.word	1740
	.byte	16
	.byte	'IfxStdIf_Pos_getFault',0,3,1,225,2,32
	.word	413
	.byte	1,1,17
	.byte	'stdIf',0,1,225,2,68
	.word	2244
	.byte	18,0,8
	.word	446
	.byte	8
	.word	490
	.byte	8
	.word	545
	.byte	8
	.word	617
	.byte	8
	.word	545
	.byte	8
	.word	789
	.byte	8
	.word	842
	.byte	8
	.word	617
	.byte	8
	.word	950
	.byte	8
	.word	545
	.byte	8
	.word	617
	.byte	8
	.word	1292
	.byte	8
	.word	617
	.byte	8
	.word	490
	.byte	8
	.word	490
	.byte	8
	.word	490
	.byte	8
	.word	545
	.byte	8
	.word	490
	.byte	8
	.word	1529
	.byte	8
	.word	1580
	.byte	8
	.word	1529
	.byte	8
	.word	1580
	.byte	8
	.word	1580
	.byte	19,1,1,8
	.word	2415
	.byte	20
	.byte	'Ifx__jump_and_link',0,3,3,61,17,1,1,17
	.byte	'fun',0,3,61,43
	.word	2418
	.byte	18,0,2
	.byte	'__fract',0,4,128,1,16
	.byte	'Ifx__float_to_fract',0,3,3,152,2,18
	.word	2463
	.byte	1,1,17
	.byte	'a',0,3,152,2,44
	.word	536
	.byte	18,0,20
	.byte	'Ifx__stopPerfCounters',0,3,3,172,2,17,1,1,18,0,2
	.byte	'unsigned long long int',0,8,7,16
	.byte	'__ld64',0,3,4,135,1,19
	.word	2552
	.byte	1,1,17
	.byte	'addr',0,4,135,1,32
	.word	452
	.byte	18,0,20
	.byte	'__st64',0,3,4,143,1,17,1,1,17
	.byte	'addr',0,4,143,1,30
	.word	452
	.byte	17
	.byte	'value',0,4,143,1,43
	.word	2552
	.byte	18,0,2
	.byte	'unsigned int',0,4,7,2
	.byte	'int',0,4,5,15
	.byte	'_Ifx_SRC_SRCR_Bits',0,6,45,16,4,4
	.byte	'SRPN',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	2,6,2,35,1,4
	.byte	'SRE',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'TOS',0,1
	.word	267
	.byte	2,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	267
	.byte	3,0,2,35,1,4
	.byte	'ECC',0,1
	.word	267
	.byte	6,2,2,35,2,4
	.byte	'reserved_22',0,1
	.word	267
	.byte	2,0,2,35,2,4
	.byte	'SRR',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'CLRR',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'SETR',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'IOV',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'IOVCLR',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'SWS',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'SWSCLR',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,6,70,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	2682
	.byte	4,2,35,0,0,21
	.word	2972
.L392:
	.byte	8
	.word	3011
.L408:
	.byte	20
	.byte	'IfxSrc_clearRequest',0,3,5,250,1,17,1,1
.L410:
	.byte	17
	.byte	'src',0,5,250,1,60
	.word	3016
.L412:
	.byte	18,0
.L413:
	.byte	20
	.byte	'IfxSrc_enable',0,3,5,140,2,17,1,1
.L414:
	.byte	17
	.byte	'src',0,5,140,2,54
	.word	3016
.L416:
	.byte	18,0,13,7,69,9,1,14
	.byte	'IfxSrc_Tos_cpu0',0,0,14
	.byte	'IfxSrc_Tos_cpu1',0,1,14
	.byte	'IfxSrc_Tos_dma',0,3,0
.L398:
	.byte	20
	.byte	'IfxSrc_init',0,3,5,146,2,17,1,1
.L401:
	.byte	17
	.byte	'src',0,5,146,2,52
	.word	3016
.L403:
	.byte	17
	.byte	'typOfService',0,5,146,2,68
	.word	3101
.L405:
	.byte	17
	.byte	'priority',0,5,146,2,95
	.word	928
.L407:
	.byte	22,18,0,0,2
	.byte	'unsigned int',0,4,7,15
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,9,241,8,16,4,4
	.byte	'ENDINIT',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'LCK',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'PW',0,4
	.word	3237
	.byte	14,16,2,35,0,4
	.byte	'REL',0,4
	.word	3237
	.byte	16,0,2,35,0,0,5,9,247,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	3253
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,9,250,8,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	2,6,2,35,0,4
	.byte	'IR0',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'DR',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'IR1',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'UR',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PAR',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'TCR',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'TCTR',0,1
	.word	267
	.byte	7,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,9,255,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	3389
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,9,137,9,16,4,4
	.byte	'AE',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'OE',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'IS0',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'DS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'TO',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'IS1',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'US',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PAS',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'TCS',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'TCT',0,1
	.word	267
	.byte	7,0,2,35,1,4
	.byte	'TIM',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,9,135,15,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	3633
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_SCU_WDTCPU',0,9,175,15,25,12,6
	.byte	'CON0',0
	.word	3349
	.byte	4,2,35,0,6
	.byte	'CON1',0
	.word	3593
	.byte	4,2,35,4,6
	.byte	'SR',0
	.word	3824
	.byte	4,2,35,8,0,21
	.word	3864
	.byte	8
	.word	3927
	.byte	20
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,8,181,3,17,1,1,17
	.byte	'watchdog',0,8,181,3,65
	.word	3932
	.byte	17
	.byte	'password',0,8,181,3,82
	.word	928
	.byte	18,0,20
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,8,140,4,17,1,1,17
	.byte	'watchdog',0,8,140,4,63
	.word	3932
	.byte	17
	.byte	'password',0,8,140,4,80
	.word	928
	.byte	18,0,16
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,8,227,3,19
	.word	928
	.byte	1,1,17
	.byte	'watchdog',0,8,227,3,74
	.word	3932
	.byte	18,0,13,11,156,1,9,1,14
	.byte	'IfxCpu_ResourceCpu_0',0,0,14
	.byte	'IfxCpu_ResourceCpu_1',0,1,14
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,16
	.byte	'IfxCpu_getCoreIndex',0,3,10,141,6,31
	.word	4162
	.byte	1,1,18,0,16
	.byte	'IfxCpu_areInterruptsEnabled',0,3,10,139,5,20
	.word	267
	.byte	1,1,18,0,16
	.byte	'IfxCpu_getPerformanceCounter',0,3,10,161,6,19
	.word	246
	.byte	1,1,17
	.byte	'address',0,10,161,6,55
	.word	928
	.byte	18,0,16
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,10,190,6,20
	.word	267
	.byte	1,1,17
	.byte	'address',0,10,190,6,70
	.word	928
	.byte	18,0,20
	.byte	'IfxCpu_updatePerformanceCounter',0,3,10,172,8,17,1,1,17
	.byte	'address',0,10,172,8,56
	.word	246
	.byte	17
	.byte	'count',0,10,172,8,72
	.word	246
	.byte	22,18,0,0,15
	.byte	'_Ifx_P_OUT_Bits',0,13,143,3,16,4,4
	.byte	'P0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'P2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'P3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'P4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'P5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'P6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'P7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'P8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'P9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'P10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'P11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'P12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'P13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'P14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'P15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,181,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	4527
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMR_Bits',0,13,169,2,16,4,4
	.byte	'PS0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PS2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PS3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PS4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'PS5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PS6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PS7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'PS8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'PS9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'PS10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'PS11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'PS12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'PS13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'PS14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'PS15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'PCL0',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'PCL2',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'PCL3',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'PCL4',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'PCL5',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'PCL6',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'PCL7',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'PCL8',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'PCL9',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'PCL10',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'PCL11',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'PCL12',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'PCL13',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'PCL14',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'PCL15',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,13,133,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	4843
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_ID_Bits',0,13,110,16,4,4
	.byte	'MODREV',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,148,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5414
	.byte	4,2,35,0,0,23,4
	.word	267
	.byte	24,3,0,15
	.byte	'_Ifx_P_IOCR0_Bits',0,13,140,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'PC0',0,1
	.word	267
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'PC1',0,1
	.word	267
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	267
	.byte	3,5,2,35,2,4
	.byte	'PC2',0,1
	.word	267
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'PC3',0,1
	.word	267
	.byte	5,0,2,35,3,0,5,13,164,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5542
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IOCR4_Bits',0,13,166,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'PC4',0,1
	.word	267
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'PC5',0,1
	.word	267
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	267
	.byte	3,5,2,35,2,4
	.byte	'PC6',0,1
	.word	267
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'PC7',0,1
	.word	267
	.byte	5,0,2,35,3,0,5,13,180,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5757
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IOCR8_Bits',0,13,179,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'PC8',0,1
	.word	267
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'PC9',0,1
	.word	267
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	267
	.byte	3,5,2,35,2,4
	.byte	'PC10',0,1
	.word	267
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'PC11',0,1
	.word	267
	.byte	5,0,2,35,3,0,5,13,188,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5972
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IOCR12_Bits',0,13,153,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'PC12',0,1
	.word	267
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'PC13',0,1
	.word	267
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	267
	.byte	3,5,2,35,2,4
	.byte	'PC14',0,1
	.word	267
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'PC15',0,1
	.word	267
	.byte	5,0,2,35,3,0,5,13,172,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	6189
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IN_Bits',0,13,118,16,4,4
	.byte	'P0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'P2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'P3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'P4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'P5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'P6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'P7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'P8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'P9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'P10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'P11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'P12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'P13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'P14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'P15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,156,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	6409
	.byte	4,2,35,0,0,23,24
	.word	267
	.byte	24,23,0,15
	.byte	'_Ifx_P_PDR0_Bits',0,13,205,3,16,4,4
	.byte	'PD0',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'PL0',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PD1',0,1
	.word	267
	.byte	3,1,2,35,0,4
	.byte	'PL1',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'PD2',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'PL2',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'PD3',0,1
	.word	267
	.byte	3,1,2,35,1,4
	.byte	'PL3',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'PD4',0,1
	.word	267
	.byte	3,5,2,35,2,4
	.byte	'PL4',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'PD5',0,1
	.word	267
	.byte	3,1,2,35,2,4
	.byte	'PL5',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'PD6',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'PL6',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'PD7',0,1
	.word	267
	.byte	3,1,2,35,3,4
	.byte	'PL7',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,13,205,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	6732
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_PDR1_Bits',0,13,226,3,16,4,4
	.byte	'PD8',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'PL8',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PD9',0,1
	.word	267
	.byte	3,1,2,35,0,4
	.byte	'PL9',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'PD10',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'PL10',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'PD11',0,1
	.word	267
	.byte	3,1,2,35,1,4
	.byte	'PL11',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'PD12',0,1
	.word	267
	.byte	3,5,2,35,2,4
	.byte	'PL12',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'PD13',0,1
	.word	267
	.byte	3,1,2,35,2,4
	.byte	'PL13',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'PD14',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'PL14',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'PD15',0,1
	.word	267
	.byte	3,1,2,35,3,4
	.byte	'PL15',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,13,213,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	7036
	.byte	4,2,35,0,0,23,8
	.word	267
	.byte	24,7,0,15
	.byte	'_Ifx_P_ESR_Bits',0,13,88,16,4,4
	.byte	'EN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,140,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	7361
	.byte	4,2,35,0,0,23,12
	.word	267
	.byte	24,11,0,15
	.byte	'_Ifx_P_PDISC_Bits',0,13,183,3,16,4,4
	.byte	'PDIS0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PDIS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PDIS2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PDIS3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PDIS4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'PDIS5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PDIS6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PDIS7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'PDIS8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'PDIS9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'PDIS10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'PDIS11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'PDIS12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'PDIS13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'PDIS14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'PDIS15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,197,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	7701
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_PCSR_Bits',0,13,165,3,16,4,4
	.byte	'SEL0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'SEL1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'SEL2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'SEL3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'SEL4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'SEL5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'SEL6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'SEL7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	2,6,2,35,1,4
	.byte	'SEL10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'SEL11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,4
	.word	2659
	.byte	19,1,2,35,0,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,13,189,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8067
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR0_Bits',0,13,206,2,16,4,4
	.byte	'PS0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PS2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PS3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,5,13,149,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8353
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR4_Bits',0,13,227,2,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'PS4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'PS5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PS6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PS7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	24,0,2,35,0,0,5,13,165,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8500
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR8_Bits',0,13,238,2,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'PS8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'PS9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'PS10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'PS11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,4
	.word	2659
	.byte	20,0,2,35,0,0,5,13,173,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8669
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR12_Bits',0,13,216,2,16,4,4
	.byte	'reserved_0',0,2
	.word	928
	.byte	12,4,2,35,0,4
	.byte	'PS12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'PS13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'PS14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'PS15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,157,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8841
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR0_Bits',0,13,232,1,16,4,4
	.byte	'reserved_0',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'PCL0',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'PCL2',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'PCL3',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'reserved_20',0,2
	.word	928
	.byte	12,0,2,35,2,0,5,13,229,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9016
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR4_Bits',0,13,253,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	20,12,2,35,0,4
	.byte	'PCL4',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'PCL5',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'PCL6',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'PCL7',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	8,0,2,35,3,0,5,13,245,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9190
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR8_Bits',0,13,136,2,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	24,8,2,35,0,4
	.byte	'PCL8',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'PCL9',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'PCL10',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'PCL11',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	267
	.byte	4,0,2,35,3,0,5,13,253,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9364
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR12_Bits',0,13,243,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	28,4,2,35,0,4
	.byte	'PCL12',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'PCL13',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'PCL14',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'PCL15',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,13,237,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9540
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR_Bits',0,13,249,2,16,4,4
	.byte	'PS0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PS2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PS3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PS4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'PS5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PS6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PS7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'PS8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'PS9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'PS10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'PS11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'PS12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'PS13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'PS14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'PS15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,141,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9696
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR_Bits',0,13,147,2,16,4,4
	.byte	'reserved_0',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'PCL0',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'PCL2',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'PCL3',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'PCL4',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'PCL5',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'PCL6',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'PCL7',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'PCL8',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'PCL9',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'PCL10',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'PCL11',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'PCL12',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'PCL13',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'PCL14',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'PCL15',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,13,221,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10029
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_LPCR0_Bits',0,13,192,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,5,13,196,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10377
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_LPCR1_Bits',0,13,200,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,15
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,13,208,1,16,4,4
	.byte	'RDIS_CTRL',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'RX_DIS',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'TERM',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'LRXTERM',0,1
	.word	267
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	24,0,2,35,0,0,5,13,204,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10501
	.byte	4,2,35,0,6
	.byte	'B_P21',0
	.word	10585
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_LPCR2_Bits',0,13,218,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'LVDSR',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'LVDSRL',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,1
	.word	267
	.byte	2,4,2,35,1,4
	.byte	'TDIS_CTRL',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'TX_DIS',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'TX_PD',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'TX_PWDPD',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,13,213,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10765
	.byte	4,2,35,0,0,23,76
	.word	267
	.byte	24,75,0,15
	.byte	'_Ifx_P_ACCEN1_Bits',0,13,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,5,13,132,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	11018
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_ACCEN0_Bits',0,13,45,16,4,4
	.byte	'EN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,13,252,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	11105
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P',0,13,229,5,25,128,2,6
	.byte	'OUT',0
	.word	4803
	.byte	4,2,35,0,6
	.byte	'OMR',0
	.word	5374
	.byte	4,2,35,4,6
	.byte	'ID',0
	.word	5493
	.byte	4,2,35,8,6
	.byte	'reserved_C',0
	.word	5533
	.byte	4,2,35,12,6
	.byte	'IOCR0',0
	.word	5717
	.byte	4,2,35,16,6
	.byte	'IOCR4',0
	.word	5932
	.byte	4,2,35,20,6
	.byte	'IOCR8',0
	.word	6149
	.byte	4,2,35,24,6
	.byte	'IOCR12',0
	.word	6369
	.byte	4,2,35,28,6
	.byte	'reserved_20',0
	.word	5533
	.byte	4,2,35,32,6
	.byte	'IN',0
	.word	6683
	.byte	4,2,35,36,6
	.byte	'reserved_28',0
	.word	6723
	.byte	24,2,35,40,6
	.byte	'PDR0',0
	.word	6996
	.byte	4,2,35,64,6
	.byte	'PDR1',0
	.word	7312
	.byte	4,2,35,68,6
	.byte	'reserved_48',0
	.word	7352
	.byte	8,2,35,72,6
	.byte	'ESR',0
	.word	7652
	.byte	4,2,35,80,6
	.byte	'reserved_54',0
	.word	7692
	.byte	12,2,35,84,6
	.byte	'PDISC',0
	.word	8027
	.byte	4,2,35,96,6
	.byte	'PCSR',0
	.word	8313
	.byte	4,2,35,100,6
	.byte	'reserved_68',0
	.word	7352
	.byte	8,2,35,104,6
	.byte	'OMSR0',0
	.word	8460
	.byte	4,2,35,112,6
	.byte	'OMSR4',0
	.word	8629
	.byte	4,2,35,116,6
	.byte	'OMSR8',0
	.word	8801
	.byte	4,2,35,120,6
	.byte	'OMSR12',0
	.word	8976
	.byte	4,2,35,124,6
	.byte	'OMCR0',0
	.word	9150
	.byte	4,3,35,128,1,6
	.byte	'OMCR4',0
	.word	9324
	.byte	4,3,35,132,1,6
	.byte	'OMCR8',0
	.word	9500
	.byte	4,3,35,136,1,6
	.byte	'OMCR12',0
	.word	9656
	.byte	4,3,35,140,1,6
	.byte	'OMSR',0
	.word	9989
	.byte	4,3,35,144,1,6
	.byte	'OMCR',0
	.word	10337
	.byte	4,3,35,148,1,6
	.byte	'reserved_98',0
	.word	7352
	.byte	8,3,35,152,1,6
	.byte	'LPCR0',0
	.word	10461
	.byte	4,3,35,160,1,6
	.byte	'LPCR1',0
	.word	10710
	.byte	4,3,35,164,1,6
	.byte	'LPCR2',0
	.word	10969
	.byte	4,3,35,168,1,6
	.byte	'reserved_A4',0
	.word	11009
	.byte	76,3,35,172,1,6
	.byte	'ACCEN1',0
	.word	11065
	.byte	4,3,35,248,1,6
	.byte	'ACCEN0',0
	.word	11632
	.byte	4,3,35,252,1,0,21
	.word	11672
	.byte	8
	.word	12275
	.byte	13,12,83,9,1,14
	.byte	'IfxPort_InputMode_undefined',0,127,14
	.byte	'IfxPort_InputMode_noPullDevice',0,0,14
	.byte	'IfxPort_InputMode_pullDown',0,8,14
	.byte	'IfxPort_InputMode_pullUp',0,16,0,20
	.byte	'IfxPort_setPinModeInput',0,3,12,196,4,17,1,1,17
	.byte	'port',0,12,196,4,48
	.word	12280
	.byte	17
	.byte	'pinIndex',0,12,196,4,60
	.word	267
	.byte	17
	.byte	'mode',0,12,196,4,88
	.word	12285
	.byte	18,0,13,12,134,1,9,1,14
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,14
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,13,12,120,9,1,14
	.byte	'IfxPort_OutputIdx_general',0,128,1,14
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,14
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,14
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,14
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,14
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,14
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,14
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,20
	.byte	'IfxPort_setPinModeOutput',0,3,12,202,4,17,1,1,17
	.byte	'port',0,12,202,4,49
	.word	12280
	.byte	17
	.byte	'pinIndex',0,12,202,4,61
	.word	267
	.byte	17
	.byte	'mode',0,12,202,4,90
	.word	12490
	.byte	17
	.byte	'index',0,12,202,4,114
	.word	12560
	.byte	18,0,13,12,172,1,9,4,14
	.byte	'IfxPort_State_notChanged',0,0,14
	.byte	'IfxPort_State_high',0,1,14
	.byte	'IfxPort_State_low',0,128,128,4,14
	.byte	'IfxPort_State_toggled',0,129,128,4,0,20
	.byte	'IfxPort_setPinState',0,3,12,208,4,17,1,1,17
	.byte	'port',0,12,208,4,44
	.word	12280
	.byte	17
	.byte	'pinIndex',0,12,208,4,56
	.word	267
	.byte	17
	.byte	'action',0,12,208,4,80
	.word	12873
	.byte	18,0,15
	.byte	'_Ifx_GPT12_CLC_Bits',0,15,95,16,4,4
	.byte	'DISR',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'DISS',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EDIS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,5,15,184,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13054
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_PISEL_Bits',0,15,145,1,16,4,4
	.byte	'IST2IN',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'IST2EUD',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'IST3IN',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'IST3EUD',0,1
	.word	267
	.byte	2,2,2,35,0,4
	.byte	'IST4IN',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'IST4EUD',0,1
	.word	267
	.byte	2,6,2,35,1,4
	.byte	'IST5IN',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'IST5EUD',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'IST6IN',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'IST6EUD',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'ISCAPIN',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,232,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13212
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_ID_Bits',0,15,105,16,4,4
	.byte	'MODREV',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,192,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13508
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T2CON_Bits',0,15,169,1,16,4,4
	.byte	'T2I',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'T2M',0,1
	.word	267
	.byte	3,2,2,35,0,4
	.byte	'T2R',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'T2UD',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'T2UDE',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'T2RC',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,1
	.word	267
	.byte	2,4,2,35,1,4
	.byte	'T2IRDIS',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'T2EDGE',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'T2CHDIR',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'T2RDIR',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,248,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13631
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T3CON_Bits',0,15,193,1,16,4,4
	.byte	'T3I',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'T3M',0,1
	.word	267
	.byte	3,2,2,35,0,4
	.byte	'T3R',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'T3UD',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'T3UDE',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'T3OE',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'T3OTL',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'BPS1',0,1
	.word	267
	.byte	2,3,2,35,1,4
	.byte	'T3EDGE',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'T3CHDIR',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'T3RDIR',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,136,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13914
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T4CON_Bits',0,15,217,1,16,4,4
	.byte	'T4I',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'T4M',0,1
	.word	267
	.byte	3,2,2,35,0,4
	.byte	'T4R',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'T4UD',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'T4UDE',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'T4RC',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'CLRT2EN',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'CLRT3EN',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'T4IRDIS',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'T4EDGE',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'T4CHDIR',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'T4RDIR',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,152,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	14188
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T5CON_Bits',0,15,242,1,16,4,4
	.byte	'T5I',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'T5M',0,1
	.word	267
	.byte	3,2,2,35,0,4
	.byte	'T5R',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'T5UD',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'T5UDE',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'T5RC',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'CT3',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'reserved_11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'CI',0,1
	.word	267
	.byte	2,2,2,35,1,4
	.byte	'T5CLR',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'T5SC',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,168,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	14486
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T6CON_Bits',0,15,138,2,16,4,4
	.byte	'T6I',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'T6M',0,1
	.word	267
	.byte	3,2,2,35,0,4
	.byte	'T6R',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'T6UD',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'T6UDE',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'T6OE',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'T6OTL',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'BPS2',0,1
	.word	267
	.byte	2,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'T6CLR',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'T6SR',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,184,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	14757
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_CAPREL_Bits',0,15,88,16,4,4
	.byte	'CAPREL',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,176,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15032
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T2_Bits',0,15,162,1,16,4,4
	.byte	'T2',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,240,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15142
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T3_Bits',0,15,186,1,16,4,4
	.byte	'T3',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,128,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15245
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T4_Bits',0,15,210,1,16,4,4
	.byte	'T4',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,144,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15348
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T5_Bits',0,15,235,1,16,4,4
	.byte	'T5',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,160,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15451
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T6_Bits',0,15,131,2,16,4,4
	.byte	'T6',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,5,15,176,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15554
	.byte	4,2,35,0,0,23,160,1
	.word	267
	.byte	24,159,1,0,15
	.byte	'_Ifx_GPT12_OCS_Bits',0,15,135,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	24,8,2,35,0,4
	.byte	'SUS',0,1
	.word	267
	.byte	4,4,2,35,3,4
	.byte	'SUS_P',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'SUSSTA',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	267
	.byte	2,0,2,35,3,0,5,15,224,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15668
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_KRSTCLR_Bits',0,15,128,1,16,4,4
	.byte	'CLR',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2659
	.byte	31,0,2,35,0,0,5,15,216,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15830
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_KRST1_Bits',0,15,121,16,4,4
	.byte	'RST',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2659
	.byte	31,0,2,35,0,0,5,15,208,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15938
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_KRST0_Bits',0,15,113,16,4,4
	.byte	'RST',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'RSTSTAT',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,5,15,200,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	16043
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_ACCEN1_Bits',0,15,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,5,15,168,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	16167
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_ACCEN0_Bits',0,15,45,16,4,4
	.byte	'EN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	267
	.byte	1,0,2,35,3,0,5,15,160,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	16258
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12',0,15,200,3,25,128,2,6
	.byte	'CLC',0
	.word	13172
	.byte	4,2,35,0,6
	.byte	'PISEL',0
	.word	13468
	.byte	4,2,35,4,6
	.byte	'ID',0
	.word	13591
	.byte	4,2,35,8,6
	.byte	'reserved_C',0
	.word	5533
	.byte	4,2,35,12,6
	.byte	'T2CON',0
	.word	13874
	.byte	4,2,35,16,6
	.byte	'T3CON',0
	.word	14148
	.byte	4,2,35,20,6
	.byte	'T4CON',0
	.word	14446
	.byte	4,2,35,24,6
	.byte	'T5CON',0
	.word	14717
	.byte	4,2,35,28,6
	.byte	'T6CON',0
	.word	14992
	.byte	4,2,35,32,6
	.byte	'reserved_24',0
	.word	7692
	.byte	12,2,35,36,6
	.byte	'CAPREL',0
	.word	15102
	.byte	4,2,35,48,6
	.byte	'T2',0
	.word	15205
	.byte	4,2,35,52,6
	.byte	'T3',0
	.word	15308
	.byte	4,2,35,56,6
	.byte	'T4',0
	.word	15411
	.byte	4,2,35,60,6
	.byte	'T5',0
	.word	15514
	.byte	4,2,35,64,6
	.byte	'T6',0
	.word	15617
	.byte	4,2,35,68,6
	.byte	'reserved_48',0
	.word	15657
	.byte	160,1,2,35,72,6
	.byte	'OCS',0
	.word	15790
	.byte	4,3,35,232,1,6
	.byte	'KRSTCLR',0
	.word	15898
	.byte	4,3,35,236,1,6
	.byte	'KRST1',0
	.word	16003
	.byte	4,3,35,240,1,6
	.byte	'KRST0',0
	.word	16127
	.byte	4,3,35,244,1,6
	.byte	'ACCEN1',0
	.word	16218
	.byte	4,3,35,248,1,6
	.byte	'ACCEN0',0
	.word	16789
	.byte	4,3,35,252,1,0,21
	.word	16829
.L284:
	.byte	8
	.word	17200
.L394:
	.byte	16
	.byte	'IfxGpt12_T4_getSrc',0,3,14,129,10,35
	.word	3016
	.byte	1,1
.L395:
	.byte	17
	.byte	'gpt12',0,14,129,10,65
	.word	17205
.L397:
	.byte	18,0
.L626:
	.byte	16
	.byte	'IfxGpt12_T5_getSrc',0,3,14,238,10,35
	.word	3016
	.byte	1,1
.L628:
	.byte	17
	.byte	'gpt12',0,14,238,10,65
	.word	17205
.L630:
	.byte	18,0
.L633:
	.byte	16
	.byte	'IfxGpt12_getCaptureSrc',0,3,14,165,12,35
	.word	3016
	.byte	1,1
.L634:
	.byte	17
	.byte	'gpt12',0,14,165,12,69
	.word	17205
.L636:
	.byte	18,0,13,14,245,1,9,1,14
	.byte	'IfxGpt12_TimerDirectionSource_internal',0,0,14
	.byte	'IfxGpt12_TimerDirectionSource_external',0,1,0
.L514:
	.byte	20
	.byte	'IfxGpt12_T2_setDirectionSource',0,3,14,212,8,17,1,1
.L517:
	.byte	17
	.byte	'gpt12',0,14,212,8,59
	.word	17205
.L519:
	.byte	17
	.byte	'source',0,14,212,8,96
	.word	17358
.L521:
	.byte	18,0,13,14,167,1,9,1,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_stopCounterTx',0,0,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxIN',0,1,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxEUD',0,2,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxINOrTxEUD',0,3,0
.L504:
	.byte	20
	.byte	'IfxGpt12_T2_setIncrementalInterfaceInputMode',0,3,14,225,8,17,1,1
.L507:
	.byte	17
	.byte	'gpt12',0,14,225,8,73
	.word	17205
.L509:
	.byte	17
	.byte	'inputMode',0,14,225,8,119
	.word	17519
.L511:
	.byte	18,0,13,14,189,1,9,1,14
	.byte	'IfxGpt12_Mode_timer',0,0,14
	.byte	'IfxGpt12_Mode_counter',0,1,14
	.byte	'IfxGpt12_Mode_lowGatedTimer',0,2,14
	.byte	'IfxGpt12_Mode_highGatedTimer',0,3,14
	.byte	'IfxGpt12_Mode_reload',0,4,14
	.byte	'IfxGpt12_Mode_capture',0,5,14
	.byte	'IfxGpt12_Mode_incrementalInterfaceRotationDetection',0,6,14
	.byte	'IfxGpt12_Mode_incrementalInterfaceEdgeDetection',0,7,0
.L496:
	.byte	20
	.byte	'IfxGpt12_T2_setMode',0,3,14,244,8,17,1,1
.L499:
	.byte	17
	.byte	'gpt12',0,14,244,8,48
	.word	17205
.L501:
	.byte	17
	.byte	'mode',0,14,244,8,69
	.word	17843
.L503:
	.byte	18,0,13,14,236,1,9,1,14
	.byte	'IfxGpt12_TimerDirection_up',0,0,14
	.byte	'IfxGpt12_TimerDirection_down',0,1,0
.L522:
	.byte	20
	.byte	'IfxGpt12_T2_setTimerDirection',0,3,14,134,9,17,1,1
.L524:
	.byte	17
	.byte	'gpt12',0,14,134,9,58
	.word	17205
.L526:
	.byte	17
	.byte	'direction',0,14,134,9,89
	.word	18167
.L528:
	.byte	18,0
.L319:
	.byte	20
	.byte	'IfxGpt12_T3_enableOutput',0,3,14,152,9,17,1,1
.L322:
	.byte	17
	.byte	'gpt12',0,14,152,9,53
	.word	17205
.L324:
	.byte	17
	.byte	'enable',0,14,152,9,68
	.word	267
.L326:
	.byte	18,0
.L304:
	.byte	20
	.byte	'IfxGpt12_T3_setDirectionSource',0,3,14,190,9,17,1,1
.L307:
	.byte	17
	.byte	'gpt12',0,14,190,9,59
	.word	17205
.L309:
	.byte	17
	.byte	'source',0,14,190,9,96
	.word	17358
.L311:
	.byte	18,0
.L294:
	.byte	20
	.byte	'IfxGpt12_T3_setIncrementalInterfaceInputMode',0,3,14,202,9,17,1,1
.L297:
	.byte	17
	.byte	'gpt12',0,14,202,9,73
	.word	17205
.L299:
	.byte	17
	.byte	'inputMode',0,14,202,9,119
	.word	17519
.L301:
	.byte	18,0
.L286:
	.byte	20
	.byte	'IfxGpt12_T3_setMode',0,3,14,214,9,17,1,1
.L289:
	.byte	17
	.byte	'gpt12',0,14,214,9,48
	.word	17205
.L291:
	.byte	17
	.byte	'mode',0,14,214,9,69
	.word	17843
.L293:
	.byte	18,0
.L312:
	.byte	20
	.byte	'IfxGpt12_T3_setTimerDirection',0,3,14,221,9,17,1,1
.L314:
	.byte	17
	.byte	'gpt12',0,14,221,9,58
	.word	17205
.L316:
	.byte	17
	.byte	'direction',0,14,221,9,89
	.word	18167
.L318:
	.byte	18,0
.L351:
	.byte	20
	.byte	'IfxGpt12_T4_enableClearTimerT2',0,3,14,239,9,17,1,1
.L354:
	.byte	17
	.byte	'gpt12',0,14,239,9,59
	.word	17205
.L356:
	.byte	17
	.byte	'enabled',0,14,239,9,74
	.word	267
.L358:
	.byte	18,0
.L359:
	.byte	20
	.byte	'IfxGpt12_T4_enableClearTimerT3',0,3,14,245,9,17,1,1
.L362:
	.byte	17
	.byte	'gpt12',0,14,245,9,59
	.word	17205
.L364:
	.byte	17
	.byte	'enabled',0,14,245,9,74
	.word	267
.L366:
	.byte	18,0,13,14,89,9,1,14
	.byte	'IfxGpt12_CaptureInputMode_none',0,0,14
	.byte	'IfxGpt12_CaptureInputMode_risingEdgeTxIN',0,1,14
	.byte	'IfxGpt12_CaptureInputMode_fallingEdgeTxIN',0,2,14
	.byte	'IfxGpt12_CaptureInputMode_bothEdgesTxIN',0,3,0
.L343:
	.byte	20
	.byte	'IfxGpt12_T4_setCaptureInputMode',0,3,14,148,10,17,1,1
.L346:
	.byte	17
	.byte	'gpt12',0,14,148,10,60
	.word	17205
.L348:
	.byte	17
	.byte	'inputMode',0,14,148,10,93
	.word	18814
.L350:
	.byte	18,0
.L367:
	.byte	20
	.byte	'IfxGpt12_T4_setInterruptEnable',0,3,14,184,10,17,1,1
.L370:
	.byte	17
	.byte	'gpt12',0,14,184,10,59
	.word	17205
.L372:
	.byte	17
	.byte	'enabled',0,14,184,10,74
	.word	267
.L374:
	.byte	18,0
.L335:
	.byte	20
	.byte	'IfxGpt12_T4_setMode',0,3,14,190,10,17,1,1
.L338:
	.byte	17
	.byte	'gpt12',0,14,190,10,48
	.word	17205
.L340:
	.byte	17
	.byte	'mode',0,14,190,10,69
	.word	17843
.L342:
	.byte	18,0,13,14,150,2,9,1,14
	.byte	'IfxGpt12_TimerRemoteControl_off',0,0,14
	.byte	'IfxGpt12_TimerRemoteControl_on',0,1,0
.L375:
	.byte	20
	.byte	'IfxGpt12_T4_setRemoteControl',0,3,14,202,10,17,1,1
.L378:
	.byte	17
	.byte	'gpt12',0,14,202,10,57
	.word	17205
.L380:
	.byte	17
	.byte	'control',0,14,202,10,92
	.word	19190
.L382:
	.byte	18,0
.L449:
	.byte	20
	.byte	'IfxGpt12_T5_enableClearTimer',0,3,14,226,10,17,1,1
.L452:
	.byte	17
	.byte	'gpt12',0,14,226,10,57
	.word	17205
.L454:
	.byte	17
	.byte	'enabled',0,14,226,10,72
	.word	267
.L456:
	.byte	18,0,13,14,100,9,1,14
	.byte	'IfxGpt12_CaptureTrigger_capin',0,0,14
	.byte	'IfxGpt12_CaptureTrigger_t3inOrT3EUD',0,1,0
.L433:
	.byte	20
	.byte	'IfxGpt12_T5_setCaptureTrigger',0,3,14,129,11,17,1,1
.L436:
	.byte	17
	.byte	'gpt12',0,14,129,11,58
	.word	17205
.L438:
	.byte	17
	.byte	'trigger',0,14,129,11,89
	.word	19406
.L440:
	.byte	18,0
.L457:
	.byte	20
	.byte	'IfxGpt12_T5_setCaptureTriggerEnable',0,3,14,135,11,17,1,1
.L459:
	.byte	17
	.byte	'gpt12',0,14,135,11,64
	.word	17205
.L461:
	.byte	17
	.byte	'enabled',0,14,135,11,79
	.word	267
.L463:
	.byte	18,0,13,14,109,9,1,14
	.byte	'IfxGpt12_CaptureTriggerMode_disabled',0,0,14
	.byte	'IfxGpt12_CaptureTriggerMode_risingEdge',0,1,14
	.byte	'IfxGpt12_CaptureTriggerMode_fallingEdge',0,2,14
	.byte	'IfxGpt12_CaptureTriggerMode_randomEdge',0,3,0
.L441:
	.byte	20
	.byte	'IfxGpt12_T5_setCaptureTriggerMode',0,3,14,141,11,17,1,1
.L444:
	.byte	17
	.byte	'gpt12',0,14,141,11,62
	.word	17205
.L446:
	.byte	17
	.byte	'mode',0,14,141,11,97
	.word	19632
.L448:
	.byte	18,0
.L472:
	.byte	20
	.byte	'IfxGpt12_T5_setDirectionSource',0,3,14,153,11,17,1,1
.L475:
	.byte	17
	.byte	'gpt12',0,14,153,11,59
	.word	17205
.L477:
	.byte	17
	.byte	'source',0,14,153,11,96
	.word	17358
.L479:
	.byte	18,0
.L417:
	.byte	20
	.byte	'IfxGpt12_T5_setMode',0,3,14,173,11,17,1,1
.L420:
	.byte	17
	.byte	'gpt12',0,14,173,11,48
	.word	17205
.L422:
	.byte	17
	.byte	'mode',0,14,173,11,69
	.word	17843
.L424:
	.byte	18,0
.L464:
	.byte	20
	.byte	'IfxGpt12_T5_setRemoteControl',0,3,14,180,11,17,1,1
.L467:
	.byte	17
	.byte	'gpt12',0,14,180,11,57
	.word	17205
.L469:
	.byte	17
	.byte	'control',0,14,180,11,92
	.word	19190
.L471:
	.byte	18,0
.L480:
	.byte	20
	.byte	'IfxGpt12_T5_setTimerDirection',0,3,14,186,11,17,1,1
.L483:
	.byte	17
	.byte	'gpt12',0,14,186,11,58
	.word	17205
.L485:
	.byte	17
	.byte	'direction',0,14,186,11,89
	.word	18167
.L487:
	.byte	18,0,13,14,254,1,9,1,14
	.byte	'IfxGpt12_TimerInputPrescaler_1',0,0,14
	.byte	'IfxGpt12_TimerInputPrescaler_2',0,1,14
	.byte	'IfxGpt12_TimerInputPrescaler_4',0,2,14
	.byte	'IfxGpt12_TimerInputPrescaler_8',0,3,14
	.byte	'IfxGpt12_TimerInputPrescaler_16',0,4,14
	.byte	'IfxGpt12_TimerInputPrescaler_32',0,5,14
	.byte	'IfxGpt12_TimerInputPrescaler_64',0,6,14
	.byte	'IfxGpt12_TimerInputPrescaler_128',0,7,0
.L425:
	.byte	20
	.byte	'IfxGpt12_T5_setTimerPrescaler',0,3,14,192,11,17,1,1
.L428:
	.byte	17
	.byte	'gpt12',0,14,192,11,58
	.word	17205
.L430:
	.byte	17
	.byte	'inputPrescaler',0,14,192,11,94
	.word	20150
.L432:
	.byte	18,0,13,14,79,9,1,14
	.byte	'IfxGpt12_CaptureInput_A',0,0,14
	.byte	'IfxGpt12_CaptureInput_B',0,1,14
	.byte	'IfxGpt12_CaptureInput_C',0,2,14
	.byte	'IfxGpt12_CaptureInput_D',0,3,0,20
	.byte	'IfxGpt12_setCaptureInput',0,3,14,213,12,17,1,1,17
	.byte	'gpt12',0,14,213,12,53
	.word	17205
	.byte	17
	.byte	'input',0,14,213,12,82
	.word	20505
	.byte	18,0,13,14,159,2,9,1,14
	.byte	'IfxGpt12_TimerRun_stop',0,0,14
	.byte	'IfxGpt12_TimerRun_start',0,1,0
.L529:
	.byte	20
	.byte	'IfxGpt12_T2_run',0,3,14,194,8,17,1,1
.L532:
	.byte	17
	.byte	'gpt12',0,14,194,8,44
	.word	17205
.L534:
	.byte	17
	.byte	'runTimer',0,14,194,8,69
	.word	20680
.L536:
	.byte	18,0
.L327:
	.byte	20
	.byte	'IfxGpt12_T3_run',0,3,14,177,9,17,1,1
.L330:
	.byte	17
	.byte	'gpt12',0,14,177,9,44
	.word	17205
.L332:
	.byte	17
	.byte	'runTimer',0,14,177,9,69
	.word	20680
.L334:
	.byte	18,0
.L383:
	.byte	20
	.byte	'IfxGpt12_T4_run',0,3,14,142,10,17,1,1
.L386:
	.byte	17
	.byte	'gpt12',0,14,142,10,44
	.word	17205
.L388:
	.byte	17
	.byte	'runTimer',0,14,142,10,69
	.word	20680
.L390:
	.byte	18,0
.L488:
	.byte	20
	.byte	'IfxGpt12_T5_run',0,3,14,251,10,17,1,1
.L491:
	.byte	17
	.byte	'gpt12',0,14,251,10,44
	.word	17205
.L493:
	.byte	17
	.byte	'runTimer',0,14,251,10,69
	.word	20680
.L495:
	.byte	18,0,25
	.word	2249
	.byte	26
	.word	2283
	.byte	18,0,13,1,76,9,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_oneFold',0,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_twoFold',0,2,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_fourFold',0,4,0,3,1,186,2,9,36,6
	.byte	'offset',0
	.word	605
	.byte	4,2,35,0,6
	.byte	'reversed',0
	.word	267
	.byte	1,2,35,4,6
	.byte	'resolution',0
	.word	605
	.byte	4,2,35,6,6
	.byte	'periodPerRotation',0
	.word	928
	.byte	2,2,35,10,6
	.byte	'resolutionFactor',0
	.word	20986
	.byte	1,2,35,12,6
	.byte	'updatePeriod',0
	.word	536
	.byte	4,2,35,14,6
	.byte	'speedModeThreshold',0
	.word	536
	.byte	4,2,35,18,6
	.byte	'minSpeed',0
	.word	536
	.byte	4,2,35,22,6
	.byte	'maxSpeed',0
	.word	536
	.byte	4,2,35,26,6
	.byte	'speedFilterEnabled',0
	.word	267
	.byte	1,2,35,30,6
	.byte	'speedFilerCutOffFrequency',0
	.word	536
	.byte	4,2,35,32,0,8
	.word	21113
	.byte	27
	.byte	'IfxStdIf_Pos_initConfig',0,1,188,4,17,1,1,1,1,17
	.byte	'config',0,1,188,4,62
	.word	21376
	.byte	0,3,16,64,9,12,6
	.byte	'a',0
	.word	536
	.byte	4,2,35,0,6
	.byte	'b',0
	.word	536
	.byte	4,2,35,4,6
	.byte	'out',0
	.word	536
	.byte	4,2,35,8,0,8
	.word	21431
.L551:
	.byte	3,16,72,9,12,6
	.byte	'cutOffFrequency',0
	.word	536
	.byte	4,2,35,0,6
	.byte	'gain',0
	.word	536
	.byte	4,2,35,4,6
	.byte	'samplingTime',0
	.word	536
	.byte	4,2,35,8,0,28
	.word	21477
	.byte	8
	.word	21544
	.byte	27
	.byte	'Ifx_LowPassPt1F32_init',0,16,83,20,1,1,1,1,17
	.byte	'filter',0,16,83,62
	.word	21472
	.byte	17
	.byte	'config',0,16,83,102
	.word	21549
	.byte	0,29
	.byte	'Ifx_LowPassPt1F32_do',0,16,85,20
	.word	536
	.byte	1,1,1,1,17
	.byte	'filter',0,16,85,60
	.word	21472
	.byte	17
	.byte	'input',0,16,85,76
	.word	536
	.byte	0,25
	.word	2423
	.byte	26
	.word	2449
	.byte	18,0,25
	.word	2475
	.byte	26
	.word	2507
	.byte	18,0,25
	.word	2520
	.byte	18,0,25
	.word	2578
	.byte	26
	.word	2597
	.byte	18,0,25
	.word	2613
	.byte	26
	.word	2628
	.byte	26
	.word	2642
	.byte	18,0,25
	.word	3021
	.byte	26
	.word	3049
	.byte	18,0,25
	.word	3064
	.byte	26
	.word	3086
	.byte	18,0,25
	.word	3160
	.byte	26
	.word	3180
	.byte	26
	.word	3193
	.byte	26
	.word	3215
	.byte	22,30
	.word	3021
	.byte	26
	.word	3049
	.byte	31
	.word	3062
	.byte	0,18,0,0,25
	.word	3937
	.byte	26
	.word	3977
	.byte	26
	.word	3995
	.byte	18,0,25
	.word	4015
	.byte	26
	.word	4053
	.byte	26
	.word	4071
	.byte	18,0,25
	.word	4091
	.byte	26
	.word	4142
	.byte	18,0,25
	.word	4241
	.byte	18,0,25
	.word	4275
	.byte	18,0,25
	.word	4317
	.byte	26
	.word	4358
	.byte	18,0,25
	.word	4377
	.byte	26
	.word	4432
	.byte	18,0,25
	.word	4451
	.byte	26
	.word	4491
	.byte	26
	.word	4508
	.byte	22,18,0,0,25
	.word	12410
	.byte	26
	.word	12442
	.byte	26
	.word	12456
	.byte	26
	.word	12474
	.byte	18,0,25
	.word	12777
	.byte	26
	.word	12810
	.byte	26
	.word	12824
	.byte	26
	.word	12842
	.byte	26
	.word	12856
	.byte	18,0,25
	.word	12976
	.byte	26
	.word	13004
	.byte	26
	.word	13018
	.byte	26
	.word	13036
	.byte	18,0,25
	.word	17210
	.byte	26
	.word	17241
	.byte	18,0,25
	.word	17258
	.byte	26
	.word	17289
	.byte	18,0,25
	.word	17306
	.byte	26
	.word	17341
	.byte	18,0,25
	.word	17447
	.byte	26
	.word	17486
	.byte	26
	.word	17501
	.byte	18,0,25
	.word	17754
	.byte	26
	.word	17807
	.byte	26
	.word	17822
	.byte	18,0,25
	.word	18108
	.byte	26
	.word	18136
	.byte	26
	.word	18151
	.byte	18,0,25
	.word	18234
	.byte	26
	.word	18272
	.byte	26
	.word	18287
	.byte	18,0,25
	.word	18308
	.byte	26
	.word	18341
	.byte	26
	.word	18356
	.byte	18,0,25
	.word	18374
	.byte	26
	.word	18413
	.byte	26
	.word	18428
	.byte	18,0,25
	.word	18446
	.byte	26
	.word	18499
	.byte	26
	.word	18514
	.byte	18,0,25
	.word	18535
	.byte	26
	.word	18563
	.byte	26
	.word	18578
	.byte	18,0,25
	.word	18594
	.byte	26
	.word	18632
	.byte	26
	.word	18647
	.byte	18,0,25
	.word	18668
	.byte	26
	.word	18707
	.byte	26
	.word	18722
	.byte	18,0,25
	.word	18741
	.byte	26
	.word	18780
	.byte	26
	.word	18795
	.byte	18,0,25
	.word	18982
	.byte	26
	.word	19022
	.byte	26
	.word	19037
	.byte	18,0,25
	.word	19058
	.byte	26
	.word	19097
	.byte	26
	.word	19112
	.byte	18,0,25
	.word	19131
	.byte	26
	.word	19159
	.byte	26
	.word	19174
	.byte	18,0,25
	.word	19264
	.byte	26
	.word	19301
	.byte	26
	.word	19316
	.byte	18,0,25
	.word	19335
	.byte	26
	.word	19372
	.byte	26
	.word	19387
	.byte	18,0,25
	.word	19482
	.byte	26
	.word	19520
	.byte	26
	.word	19535
	.byte	18,0,25
	.word	19554
	.byte	26
	.word	19598
	.byte	26
	.word	19613
	.byte	18,0,25
	.word	19801
	.byte	26
	.word	19843
	.byte	26
	.word	19858
	.byte	18,0,25
	.word	19874
	.byte	26
	.word	19913
	.byte	26
	.word	19928
	.byte	18,0,25
	.word	19946
	.byte	26
	.word	19974
	.byte	26
	.word	19989
	.byte	18,0,25
	.word	20005
	.byte	26
	.word	20042
	.byte	26
	.word	20057
	.byte	18,0,25
	.word	20076
	.byte	26
	.word	20114
	.byte	26
	.word	20129
	.byte	18,0,25
	.word	20426
	.byte	26
	.word	20464
	.byte	26
	.word	20479
	.byte	18,0,25
	.word	20615
	.byte	26
	.word	20648
	.byte	26
	.word	20663
	.byte	18,0,29
	.byte	'IfxGpt12_T5_getFrequency',0,14,138,7,20
	.word	536
	.byte	1,1,1,1,17
	.byte	'gpt12',0,14,138,7,56
	.word	17205
	.byte	0,25
	.word	20738
	.byte	26
	.word	20762
	.byte	26
	.word	20777
	.byte	18,0,25
	.word	20797
	.byte	26
	.word	20821
	.byte	26
	.word	20836
	.byte	18,0,25
	.word	20856
	.byte	26
	.word	20880
	.byte	26
	.word	20895
	.byte	18,0,25
	.word	20915
	.byte	26
	.word	20939
	.byte	26
	.word	20954
	.byte	18,0,3,12,190,1,9,8,6
	.byte	'port',0
	.word	12280
	.byte	4,2,35,0,6
	.byte	'pinIndex',0
	.word	267
	.byte	1,2,35,4,0,13,18,130,1,9,1,14
	.byte	'Ifx_RxSel_a',0,0,14
	.byte	'Ifx_RxSel_b',0,1,14
	.byte	'Ifx_RxSel_c',0,2,14
	.byte	'Ifx_RxSel_d',0,3,14
	.byte	'Ifx_RxSel_e',0,4,14
	.byte	'Ifx_RxSel_f',0,5,14
	.byte	'Ifx_RxSel_g',0,6,14
	.byte	'Ifx_RxSel_h',0,7,0,3,17,67,15,20,6
	.byte	'module',0
	.word	17205
	.byte	4,2,35,0,6
	.byte	'timer',0
	.word	267
	.byte	1,2,35,4,6
	.byte	'pin',0
	.word	22560
	.byte	8,2,35,8,6
	.byte	'select',0
	.word	22599
	.byte	1,2,35,16,0,28
	.word	22718
	.byte	8
	.word	22784
	.byte	13,12,144,1,9,1,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,14
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,14
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,14
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,14
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,14
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,14
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,14
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,14
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,27
	.byte	'IfxGpt12_initTxEudInPinWithPadLevel',0,14,161,8,17,1,1,1,1,17
	.byte	'txEudIn',0,14,161,8,78
	.word	22789
	.byte	17
	.byte	'inputMode',0,14,161,8,105
	.word	12285
	.byte	17
	.byte	'padDriver',0,14,161,8,134,1
	.word	22794
	.byte	0,3,17,76,15,20,6
	.byte	'module',0
	.word	17205
	.byte	4,2,35,0,6
	.byte	'timer',0
	.word	267
	.byte	1,2,35,4,6
	.byte	'pin',0
	.word	22560
	.byte	8,2,35,8,6
	.byte	'select',0
	.word	22599
	.byte	1,2,35,16,0,28
	.word	23311
	.byte	8
	.word	23377
	.byte	27
	.byte	'IfxGpt12_initTxInPinWithPadLevel',0,14,169,8,17,1,1,1,1,17
	.byte	'txIn',0,14,169,8,74
	.word	23382
	.byte	17
	.byte	'inputMode',0,14,169,8,98
	.word	12285
	.byte	17
	.byte	'padDriver',0,14,169,8,127
	.word	22794
	.byte	0,29
	.byte	'memset',0,19,56,17
	.word	452
	.byte	1,1,1,1,32,19,56,33
	.word	452
	.byte	32,19,56,36
	.word	2675
	.byte	32,19,56,41
	.word	2659
	.byte	0,9
	.byte	'IfxGpt12_IncrEnc_Update',0,20,203,1,29
	.word	1500
	.byte	3,20,213,1,9,84,6
	.byte	'rawPosition',0
	.word	605
	.byte	4,2,35,0,6
	.byte	'speed',0
	.word	536
	.byte	4,2,35,4,6
	.byte	'turn',0
	.word	605
	.byte	4,2,35,8,6
	.byte	'direction',0
	.word	701
	.byte	1,2,35,12,6
	.byte	'status',0
	.word	413
	.byte	4,2,35,14,6
	.byte	'offset',0
	.word	605
	.byte	4,2,35,18,6
	.byte	'resolution',0
	.word	605
	.byte	4,2,35,22,6
	.byte	'updatePeriod',0
	.word	536
	.byte	4,2,35,26,6
	.byte	'speedConstPulseCount',0
	.word	536
	.byte	4,2,35,30,6
	.byte	'speedConstTimeDiff',0
	.word	536
	.byte	4,2,35,34,6
	.byte	'positionConst',0
	.word	536
	.byte	4,2,35,38,6
	.byte	'speedModeThreshold',0
	.word	536
	.byte	4,2,35,42,6
	.byte	'speedModeThresholdTick',0
	.word	605
	.byte	4,2,35,46,6
	.byte	'module',0
	.word	17205
	.byte	4,2,35,52,6
	.byte	'minSpeed',0
	.word	536
	.byte	4,2,35,56,6
	.byte	'maxSpeed',0
	.word	536
	.byte	4,2,35,60,6
	.byte	'speedLpf',0
	.word	21431
	.byte	12,2,35,64,6
	.byte	'update',0
	.word	23526
	.byte	4,2,35,76,6
	.byte	'speedFilterEnabled',0
	.word	267
	.byte	1,2,35,80,0
.L279:
	.byte	8
	.word	23559
	.byte	8
	.word	490
	.byte	3,20,238,1,9,60,6
	.byte	'base',0
	.word	21113
	.byte	36,2,35,0,6
	.byte	'module',0
	.word	17205
	.byte	4,2,35,36,6
	.byte	'pinA',0
	.word	23382
	.byte	4,2,35,40,6
	.byte	'pinB',0
	.word	22789
	.byte	4,2,35,44,6
	.byte	'pinZ',0
	.word	23382
	.byte	4,2,35,48,6
	.byte	'pinMode',0
	.word	12285
	.byte	1,2,35,52,6
	.byte	'zeroIsrPriority',0
	.word	928
	.byte	2,2,35,54,6
	.byte	'zeroIsrProvider',0
	.word	3101
	.byte	1,2,35,56,6
	.byte	'pinDriver',0
	.word	22794
	.byte	1,2,35,57,6
	.byte	'initPins',0
	.word	267
	.byte	1,2,35,58,0,28
	.word	23974
.L281:
	.byte	8
	.word	24157
.L554:
	.byte	8
	.word	23974
	.byte	33
	.byte	'__INDIRECT__',0,21,1,1,1,1,1,2
	.byte	'short int',0,2,5,9
	.byte	'__wchar_t',0,21,1,1
	.word	24192
	.byte	9
	.byte	'__size_t',0,21,1,1
	.word	2659
	.byte	9
	.byte	'__ptrdiff_t',0,21,1,1
	.word	2675
	.byte	34,1,8
	.word	24260
	.byte	9
	.byte	'__codeptr',0,21,1,1
	.word	24262
	.byte	9
	.byte	'size_t',0,22,24,25
	.word	2659
	.byte	9
	.byte	'boolean',0,23,101,29
	.word	267
	.byte	9
	.byte	'uint8',0,23,105,29
	.word	267
	.byte	9
	.byte	'uint16',0,23,109,29
	.word	928
	.byte	9
	.byte	'uint32',0,23,113,29
	.word	246
	.byte	9
	.byte	'uint64',0,23,118,29
	.word	2552
	.byte	9
	.byte	'sint16',0,23,126,29
	.word	24192
	.byte	9
	.byte	'sint32',0,23,131,1,29
	.word	605
	.byte	2
	.byte	'long long int',0,8,5,9
	.byte	'sint64',0,23,138,1,29
	.word	24406
	.byte	9
	.byte	'float32',0,23,167,1,29
	.word	536
	.byte	9
	.byte	'pvoid',0,18,57,28
	.word	452
	.byte	9
	.byte	'Ifx_TickTime',0,18,79,28
	.word	24406
	.byte	9
	.byte	'Ifx_SizeT',0,18,92,16
	.word	24192
	.byte	9
	.byte	'Ifx_Priority',0,18,103,16
	.word	928
	.byte	9
	.byte	'Ifx_RxSel',0,18,140,1,3
	.word	22599
	.byte	8
	.word	24192
	.byte	12
	.word	267
	.byte	1,1,11
	.word	452
	.byte	11
	.word	452
	.byte	11
	.word	24549
	.byte	11
	.word	24406
	.byte	0,8
	.word	24554
	.byte	9
	.byte	'IfxStdIf_DPipe_Write',0,24,92,19
	.word	24582
	.byte	9
	.byte	'IfxStdIf_DPipe_Read',0,24,107,19
	.word	24582
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadCount',0,24,115,18
	.word	630
	.byte	21
	.word	267
	.byte	8
	.word	24680
	.byte	12
	.word	24685
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	24690
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,24,123,36
	.word	24703
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,24,147,1,18
	.word	630
	.byte	8
	.word	24690
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,24,155,1,37
	.word	24782
	.byte	12
	.word	267
	.byte	1,1,11
	.word	452
	.byte	11
	.word	24192
	.byte	11
	.word	24406
	.byte	0,8
	.word	24825
	.byte	9
	.byte	'IfxStdIf_DPipe_CanReadCount',0,24,166,1,19
	.word	24848
	.byte	9
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,24,177,1,19
	.word	24848
	.byte	12
	.word	267
	.byte	1,1,11
	.word	452
	.byte	11
	.word	24406
	.byte	0,8
	.word	24928
	.byte	9
	.byte	'IfxStdIf_DPipe_FlushTx',0,24,186,1,19
	.word	24946
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearTx',0,24,200,1,16
	.word	499
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearRx',0,24,193,1,16
	.word	499
	.byte	9
	.byte	'IfxStdIf_DPipe_OnReceive',0,24,208,1,16
	.word	499
	.byte	9
	.byte	'IfxStdIf_DPipe_OnTransmit',0,24,215,1,16
	.word	499
	.byte	9
	.byte	'IfxStdIf_DPipe_OnError',0,24,222,1,16
	.word	499
	.byte	12
	.word	246
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	25148
	.byte	9
	.byte	'IfxStdIf_DPipe_GetSendCount',0,24,131,1,18
	.word	25161
	.byte	12
	.word	24406
	.byte	1,1,11
	.word	452
	.byte	0,8
	.word	25203
	.byte	9
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,24,139,1,24
	.word	25216
	.byte	9
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,24,229,1,16
	.word	499
	.byte	15
	.byte	'IfxStdIf_DPipe_',0,24,233,1,8,76,6
	.byte	'driver',0
	.word	457
	.byte	4,2,35,0,6
	.byte	'txDisabled',0
	.word	267
	.byte	1,2,35,4,6
	.byte	'write',0
	.word	24587
	.byte	4,2,35,8,6
	.byte	'read',0
	.word	24616
	.byte	4,2,35,12,6
	.byte	'getReadCount',0
	.word	24644
	.byte	4,2,35,16,6
	.byte	'getReadEvent',0
	.word	24708
	.byte	4,2,35,20,6
	.byte	'getWriteCount',0
	.word	24744
	.byte	4,2,35,24,6
	.byte	'getWriteEvent',0
	.word	24787
	.byte	4,2,35,28,6
	.byte	'canReadCount',0
	.word	24853
	.byte	4,2,35,32,6
	.byte	'canWriteCount',0
	.word	24890
	.byte	4,2,35,36,6
	.byte	'flushTx',0
	.word	24951
	.byte	4,2,35,40,6
	.byte	'clearTx',0
	.word	24983
	.byte	4,2,35,44,6
	.byte	'clearRx',0
	.word	25015
	.byte	4,2,35,48,6
	.byte	'onReceive',0
	.word	25047
	.byte	4,2,35,52,6
	.byte	'onTransmit',0
	.word	25081
	.byte	4,2,35,56,6
	.byte	'onError',0
	.word	25116
	.byte	4,2,35,60,6
	.byte	'getSendCount',0
	.word	25166
	.byte	4,2,35,64,6
	.byte	'getTxTimeStamp',0
	.word	25221
	.byte	4,2,35,68,6
	.byte	'resetSendCount',0
	.word	25260
	.byte	4,2,35,72,0,9
	.byte	'IfxStdIf_DPipe',0,24,71,32
	.word	25299
	.byte	8
	.word	24554
	.byte	8
	.word	24554
	.byte	8
	.word	617
	.byte	8
	.word	24690
	.byte	8
	.word	617
	.byte	8
	.word	24690
	.byte	8
	.word	24825
	.byte	8
	.word	24825
	.byte	8
	.word	24928
	.byte	8
	.word	490
	.byte	8
	.word	490
	.byte	8
	.word	490
	.byte	8
	.word	490
	.byte	8
	.word	490
	.byte	8
	.word	25148
	.byte	8
	.word	25203
	.byte	8
	.word	490
	.byte	21
	.word	267
	.byte	8
	.word	25807
	.byte	9
	.byte	'IfxStdIf_DPipe_WriteEvent',0,24,73,32
	.word	25812
	.byte	9
	.byte	'IfxStdIf_DPipe_ReadEvent',0,24,74,32
	.word	25812
	.byte	9
	.byte	'IfxStdIf_Pos_ResolutionFactor',0,1,81,3
	.word	20986
	.byte	9
	.byte	'IfxStdIf_Pos_SensorType',0,1,92,3
	.word	1086
	.byte	9
	.byte	'IfxStdIf_Pos_Dir',0,1,100,3
	.word	701
	.byte	9
	.byte	'IfxStdIf_Pos_Status',0,1,114,3
	.word	413
	.byte	9
	.byte	'IfxStdIf_Pos',0,1,119,30
	.word	1740
	.byte	9
	.byte	'IfxStdIf_Pos_Config',0,1,199,2,3
	.word	21113
	.byte	9
	.byte	'Ifx_LowPassPt1F32',0,16,69,3
	.word	21431
	.byte	9
	.byte	'Ifx_LowPassPt1F32_Config',0,16,77,3
	.word	21477
	.byte	9
	.byte	'IfxSrc_Tos',0,7,74,3
	.word	3101
	.byte	9
	.byte	'Ifx_SRC_SRCR_Bits',0,6,62,3
	.word	2682
	.byte	9
	.byte	'Ifx_SRC_SRCR',0,6,75,3
	.word	2972
	.byte	15
	.byte	'_Ifx_SRC_AGBT',0,6,86,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	26182
	.byte	9
	.byte	'Ifx_SRC_AGBT',0,6,89,3
	.word	26214
	.byte	15
	.byte	'_Ifx_SRC_ASCLIN',0,6,92,25,12,6
	.byte	'TX',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'RX',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'ERR',0
	.word	2972
	.byte	4,2,35,8,0,21
	.word	26240
	.byte	9
	.byte	'Ifx_SRC_ASCLIN',0,6,97,3
	.word	26299
	.byte	15
	.byte	'_Ifx_SRC_BCUSPB',0,6,100,25,4,6
	.byte	'SBSRC',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	26327
	.byte	9
	.byte	'Ifx_SRC_BCUSPB',0,6,103,3
	.word	26364
	.byte	23,64
	.word	2972
	.byte	24,15,0,15
	.byte	'_Ifx_SRC_CAN',0,6,106,25,64,6
	.byte	'INT',0
	.word	26392
	.byte	64,2,35,0,0,21
	.word	26401
	.byte	9
	.byte	'Ifx_SRC_CAN',0,6,109,3
	.word	26433
	.byte	15
	.byte	'_Ifx_SRC_CCU6',0,6,112,25,16,6
	.byte	'SR0',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2972
	.byte	4,2,35,12,0,21
	.word	26458
	.byte	9
	.byte	'Ifx_SRC_CCU6',0,6,118,3
	.word	26530
	.byte	23,8
	.word	2972
	.byte	24,1,0,15
	.byte	'_Ifx_SRC_CERBERUS',0,6,121,25,8,6
	.byte	'SR',0
	.word	26556
	.byte	8,2,35,0,0,21
	.word	26565
	.byte	9
	.byte	'Ifx_SRC_CERBERUS',0,6,124,3
	.word	26601
	.byte	15
	.byte	'_Ifx_SRC_CIF',0,6,127,25,16,6
	.byte	'MI',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'MIEP',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'ISP',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'MJPEG',0
	.word	2972
	.byte	4,2,35,12,0,21
	.word	26631
	.byte	9
	.byte	'Ifx_SRC_CIF',0,6,133,1,3
	.word	26704
	.byte	15
	.byte	'_Ifx_SRC_CPU',0,6,136,1,25,4,6
	.byte	'SBSRC',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	26730
	.byte	9
	.byte	'Ifx_SRC_CPU',0,6,139,1,3
	.word	26765
	.byte	23,192,1
	.word	2972
	.byte	24,47,0,15
	.byte	'_Ifx_SRC_DMA',0,6,142,1,25,208,1,6
	.byte	'ERR',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'reserved_4',0
	.word	7692
	.byte	12,2,35,4,6
	.byte	'CH',0
	.word	26791
	.byte	192,1,2,35,16,0,21
	.word	26801
	.byte	9
	.byte	'Ifx_SRC_DMA',0,6,147,1,3
	.word	26868
	.byte	15
	.byte	'_Ifx_SRC_DSADC',0,6,150,1,25,8,6
	.byte	'SRM',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'SRA',0
	.word	2972
	.byte	4,2,35,4,0,21
	.word	26894
	.byte	9
	.byte	'Ifx_SRC_DSADC',0,6,154,1,3
	.word	26942
	.byte	15
	.byte	'_Ifx_SRC_EMEM',0,6,157,1,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	26970
	.byte	9
	.byte	'Ifx_SRC_EMEM',0,6,160,1,3
	.word	27003
	.byte	23,40
	.word	267
	.byte	24,39,0,15
	.byte	'_Ifx_SRC_ERAY',0,6,163,1,25,80,6
	.byte	'INT',0
	.word	26556
	.byte	8,2,35,0,6
	.byte	'TINT',0
	.word	26556
	.byte	8,2,35,8,6
	.byte	'NDAT',0
	.word	26556
	.byte	8,2,35,16,6
	.byte	'MBSC',0
	.word	26556
	.byte	8,2,35,24,6
	.byte	'OBUSY',0
	.word	2972
	.byte	4,2,35,32,6
	.byte	'IBUSY',0
	.word	2972
	.byte	4,2,35,36,6
	.byte	'reserved_28',0
	.word	27030
	.byte	40,2,35,40,0,21
	.word	27039
	.byte	9
	.byte	'Ifx_SRC_ERAY',0,6,172,1,3
	.word	27166
	.byte	15
	.byte	'_Ifx_SRC_ETH',0,6,175,1,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	27193
	.byte	9
	.byte	'Ifx_SRC_ETH',0,6,178,1,3
	.word	27225
	.byte	15
	.byte	'_Ifx_SRC_FCE',0,6,181,1,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	27251
	.byte	9
	.byte	'Ifx_SRC_FCE',0,6,184,1,3
	.word	27283
	.byte	15
	.byte	'_Ifx_SRC_FFT',0,6,187,1,25,12,6
	.byte	'DONE',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'ERR',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'RFS',0
	.word	2972
	.byte	4,2,35,8,0,21
	.word	27309
	.byte	9
	.byte	'Ifx_SRC_FFT',0,6,192,1,3
	.word	27369
	.byte	23,16
	.word	267
	.byte	24,15,0,15
	.byte	'_Ifx_SRC_GPSR',0,6,195,1,25,32,6
	.byte	'SR0',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2972
	.byte	4,2,35,12,6
	.byte	'reserved_10',0
	.word	27395
	.byte	16,2,35,16,0,21
	.word	27404
	.byte	9
	.byte	'Ifx_SRC_GPSR',0,6,202,1,3
	.word	27498
	.byte	15
	.byte	'_Ifx_SRC_GPT12',0,6,205,1,25,48,6
	.byte	'CIRQ',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'T2',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'T3',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'T4',0
	.word	2972
	.byte	4,2,35,12,6
	.byte	'T5',0
	.word	2972
	.byte	4,2,35,16,6
	.byte	'T6',0
	.word	2972
	.byte	4,2,35,20,6
	.byte	'reserved_18',0
	.word	6723
	.byte	24,2,35,24,0,21
	.word	27525
	.byte	9
	.byte	'Ifx_SRC_GPT12',0,6,214,1,3
	.word	27642
	.byte	23,12
	.word	2972
	.byte	24,2,0,23,32
	.word	2972
	.byte	24,7,0,23,32
	.word	27679
	.byte	24,0,0,23,88
	.word	267
	.byte	24,87,0,23,108
	.word	2972
	.byte	24,26,0,23,96
	.word	267
	.byte	24,95,0,23,96
	.word	27679
	.byte	24,2,0,23,160,3
	.word	267
	.byte	24,159,3,0,23,64
	.word	27679
	.byte	24,1,0,23,192,3
	.word	267
	.byte	24,191,3,0,23,16
	.word	2972
	.byte	24,3,0,23,64
	.word	27764
	.byte	24,3,0,23,192,2
	.word	267
	.byte	24,191,2,0,23,52
	.word	267
	.byte	24,51,0,15
	.byte	'_Ifx_SRC_GTM',0,6,217,1,25,204,18,6
	.byte	'AEIIRQ',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'ARUIRQ',0
	.word	27670
	.byte	12,2,35,4,6
	.byte	'reserved_10',0
	.word	5533
	.byte	4,2,35,16,6
	.byte	'BRCIRQ',0
	.word	2972
	.byte	4,2,35,20,6
	.byte	'CMPIRQ',0
	.word	2972
	.byte	4,2,35,24,6
	.byte	'SPEIRQ',0
	.word	26556
	.byte	8,2,35,28,6
	.byte	'reserved_24',0
	.word	7352
	.byte	8,2,35,36,6
	.byte	'PSM',0
	.word	27688
	.byte	32,2,35,44,6
	.byte	'reserved_4C',0
	.word	27697
	.byte	88,2,35,76,6
	.byte	'DPLL',0
	.word	27706
	.byte	108,3,35,164,1,6
	.byte	'reserved_110',0
	.word	27715
	.byte	96,3,35,144,2,6
	.byte	'ERR',0
	.word	2972
	.byte	4,3,35,240,2,6
	.byte	'reserved_174',0
	.word	7692
	.byte	12,3,35,244,2,6
	.byte	'TIM',0
	.word	27724
	.byte	96,3,35,128,3,6
	.byte	'reserved_1E0',0
	.word	27733
	.byte	160,3,3,35,224,3,6
	.byte	'MCS',0
	.word	27724
	.byte	96,3,35,128,7,6
	.byte	'reserved_3E0',0
	.word	27733
	.byte	160,3,3,35,224,7,6
	.byte	'TOM',0
	.word	27744
	.byte	64,3,35,128,11,6
	.byte	'reserved_5C0',0
	.word	27753
	.byte	192,3,3,35,192,11,6
	.byte	'ATOM',0
	.word	27773
	.byte	64,3,35,128,15,6
	.byte	'reserved_7C0',0
	.word	27782
	.byte	192,2,3,35,192,15,6
	.byte	'MCSW0',0
	.word	27670
	.byte	12,3,35,128,18,6
	.byte	'reserved_90C',0
	.word	27793
	.byte	52,3,35,140,18,6
	.byte	'MCSW1',0
	.word	27670
	.byte	12,3,35,192,18,0,21
	.word	27802
	.byte	9
	.byte	'Ifx_SRC_GTM',0,6,243,1,3
	.word	28262
	.byte	15
	.byte	'_Ifx_SRC_HSCT',0,6,246,1,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	28288
	.byte	9
	.byte	'Ifx_SRC_HSCT',0,6,249,1,3
	.word	28321
	.byte	15
	.byte	'_Ifx_SRC_HSSL',0,6,252,1,25,16,6
	.byte	'COK',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'RDI',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'ERR',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'TRG',0
	.word	2972
	.byte	4,2,35,12,0,21
	.word	28348
	.byte	9
	.byte	'Ifx_SRC_HSSL',0,6,130,2,3
	.word	28421
	.byte	23,56
	.word	267
	.byte	24,55,0,15
	.byte	'_Ifx_SRC_I2C',0,6,133,2,25,80,6
	.byte	'BREQ',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'LBREQ',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'SREQ',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'LSREQ',0
	.word	2972
	.byte	4,2,35,12,6
	.byte	'ERR',0
	.word	2972
	.byte	4,2,35,16,6
	.byte	'P',0
	.word	2972
	.byte	4,2,35,20,6
	.byte	'reserved_18',0
	.word	28448
	.byte	56,2,35,24,0,21
	.word	28457
	.byte	9
	.byte	'Ifx_SRC_I2C',0,6,142,2,3
	.word	28580
	.byte	15
	.byte	'_Ifx_SRC_LMU',0,6,145,2,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	28606
	.byte	9
	.byte	'Ifx_SRC_LMU',0,6,148,2,3
	.word	28638
	.byte	15
	.byte	'_Ifx_SRC_MSC',0,6,151,2,25,20,6
	.byte	'SR0',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2972
	.byte	4,2,35,12,6
	.byte	'SR4',0
	.word	2972
	.byte	4,2,35,16,0,21
	.word	28664
	.byte	9
	.byte	'Ifx_SRC_MSC',0,6,158,2,3
	.word	28749
	.byte	15
	.byte	'_Ifx_SRC_PMU',0,6,161,2,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	28775
	.byte	9
	.byte	'Ifx_SRC_PMU',0,6,164,2,3
	.word	28807
	.byte	15
	.byte	'_Ifx_SRC_PSI5',0,6,167,2,25,32,6
	.byte	'SR',0
	.word	27679
	.byte	32,2,35,0,0,21
	.word	28833
	.byte	9
	.byte	'Ifx_SRC_PSI5',0,6,170,2,3
	.word	28866
	.byte	15
	.byte	'_Ifx_SRC_PSI5S',0,6,173,2,25,32,6
	.byte	'SR',0
	.word	27679
	.byte	32,2,35,0,0,21
	.word	28893
	.byte	9
	.byte	'Ifx_SRC_PSI5S',0,6,176,2,3
	.word	28927
	.byte	15
	.byte	'_Ifx_SRC_QSPI',0,6,179,2,25,24,6
	.byte	'TX',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'RX',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'ERR',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'PT',0
	.word	2972
	.byte	4,2,35,12,6
	.byte	'HC',0
	.word	2972
	.byte	4,2,35,16,6
	.byte	'U',0
	.word	2972
	.byte	4,2,35,20,0,21
	.word	28955
	.byte	9
	.byte	'Ifx_SRC_QSPI',0,6,187,2,3
	.word	29048
	.byte	15
	.byte	'_Ifx_SRC_SCR',0,6,190,2,25,4,6
	.byte	'SR',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	29075
	.byte	9
	.byte	'Ifx_SRC_SCR',0,6,193,2,3
	.word	29107
	.byte	15
	.byte	'_Ifx_SRC_SCU',0,6,196,2,25,20,6
	.byte	'DTS',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'ERU',0
	.word	27764
	.byte	16,2,35,4,0,21
	.word	29133
	.byte	9
	.byte	'Ifx_SRC_SCU',0,6,200,2,3
	.word	29179
	.byte	23,24
	.word	2972
	.byte	24,5,0,15
	.byte	'_Ifx_SRC_SENT',0,6,203,2,25,24,6
	.byte	'SR',0
	.word	29205
	.byte	24,2,35,0,0,21
	.word	29214
	.byte	9
	.byte	'Ifx_SRC_SENT',0,6,206,2,3
	.word	29247
	.byte	15
	.byte	'_Ifx_SRC_SMU',0,6,209,2,25,12,6
	.byte	'SR',0
	.word	27670
	.byte	12,2,35,0,0,21
	.word	29274
	.byte	9
	.byte	'Ifx_SRC_SMU',0,6,212,2,3
	.word	29306
	.byte	15
	.byte	'_Ifx_SRC_STM',0,6,215,2,25,8,6
	.byte	'SR0',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2972
	.byte	4,2,35,4,0,21
	.word	29332
	.byte	9
	.byte	'Ifx_SRC_STM',0,6,219,2,3
	.word	29378
	.byte	15
	.byte	'_Ifx_SRC_VADCCG',0,6,222,2,25,16,6
	.byte	'SR0',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2972
	.byte	4,2,35,12,0,21
	.word	29404
	.byte	9
	.byte	'Ifx_SRC_VADCCG',0,6,228,2,3
	.word	29479
	.byte	15
	.byte	'_Ifx_SRC_VADCG',0,6,231,2,25,16,6
	.byte	'SR0',0
	.word	2972
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2972
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2972
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2972
	.byte	4,2,35,12,0,21
	.word	29508
	.byte	9
	.byte	'Ifx_SRC_VADCG',0,6,237,2,3
	.word	29582
	.byte	15
	.byte	'_Ifx_SRC_XBAR',0,6,240,2,25,4,6
	.byte	'SRC',0
	.word	2972
	.byte	4,2,35,0,0,21
	.word	29610
	.byte	9
	.byte	'Ifx_SRC_XBAR',0,6,243,2,3
	.word	29644
	.byte	23,4
	.word	26182
	.byte	24,0,0,21
	.word	29671
	.byte	15
	.byte	'_Ifx_SRC_GAGBT',0,6,128,3,25,4,6
	.byte	'AGBT',0
	.word	29680
	.byte	4,2,35,0,0,21
	.word	29685
	.byte	9
	.byte	'Ifx_SRC_GAGBT',0,6,131,3,3
	.word	29721
	.byte	23,48
	.word	26240
	.byte	24,3,0,21
	.word	29749
	.byte	15
	.byte	'_Ifx_SRC_GASCLIN',0,6,134,3,25,48,6
	.byte	'ASCLIN',0
	.word	29758
	.byte	48,2,35,0,0,21
	.word	29763
	.byte	9
	.byte	'Ifx_SRC_GASCLIN',0,6,137,3,3
	.word	29803
	.byte	21
	.word	26327
	.byte	15
	.byte	'_Ifx_SRC_GBCU',0,6,140,3,25,4,6
	.byte	'SPB',0
	.word	29833
	.byte	4,2,35,0,0,21
	.word	29838
	.byte	9
	.byte	'Ifx_SRC_GBCU',0,6,143,3,3
	.word	29872
	.byte	23,64
	.word	26401
	.byte	24,0,0,21
	.word	29899
	.byte	15
	.byte	'_Ifx_SRC_GCAN',0,6,146,3,25,64,6
	.byte	'CAN',0
	.word	29908
	.byte	64,2,35,0,0,21
	.word	29913
	.byte	9
	.byte	'Ifx_SRC_GCAN',0,6,149,3,3
	.word	29947
	.byte	23,32
	.word	26458
	.byte	24,1,0,21
	.word	29974
	.byte	15
	.byte	'_Ifx_SRC_GCCU6',0,6,152,3,25,32,6
	.byte	'CCU6',0
	.word	29983
	.byte	32,2,35,0,0,21
	.word	29988
	.byte	9
	.byte	'Ifx_SRC_GCCU6',0,6,155,3,3
	.word	30024
	.byte	21
	.word	26565
	.byte	15
	.byte	'_Ifx_SRC_GCERBERUS',0,6,158,3,25,8,6
	.byte	'CERBERUS',0
	.word	30052
	.byte	8,2,35,0,0,21
	.word	30057
	.byte	9
	.byte	'Ifx_SRC_GCERBERUS',0,6,161,3,3
	.word	30101
	.byte	23,16
	.word	26631
	.byte	24,0,0,21
	.word	30133
	.byte	15
	.byte	'_Ifx_SRC_GCIF',0,6,164,3,25,16,6
	.byte	'CIF',0
	.word	30142
	.byte	16,2,35,0,0,21
	.word	30147
	.byte	9
	.byte	'Ifx_SRC_GCIF',0,6,167,3,3
	.word	30181
	.byte	23,8
	.word	26730
	.byte	24,1,0,21
	.word	30208
	.byte	15
	.byte	'_Ifx_SRC_GCPU',0,6,170,3,25,8,6
	.byte	'CPU',0
	.word	30217
	.byte	8,2,35,0,0,21
	.word	30222
	.byte	9
	.byte	'Ifx_SRC_GCPU',0,6,173,3,3
	.word	30256
	.byte	23,208,1
	.word	26801
	.byte	24,0,0,21
	.word	30283
	.byte	15
	.byte	'_Ifx_SRC_GDMA',0,6,176,3,25,208,1,6
	.byte	'DMA',0
	.word	30293
	.byte	208,1,2,35,0,0,21
	.word	30298
	.byte	9
	.byte	'Ifx_SRC_GDMA',0,6,179,3,3
	.word	30334
	.byte	21
	.word	26894
	.byte	21
	.word	26894
	.byte	21
	.word	26894
	.byte	15
	.byte	'_Ifx_SRC_GDSADC',0,6,182,3,25,32,6
	.byte	'DSADC0',0
	.word	30361
	.byte	8,2,35,0,6
	.byte	'reserved_8',0
	.word	7352
	.byte	8,2,35,8,6
	.byte	'DSADC2',0
	.word	30366
	.byte	8,2,35,16,6
	.byte	'DSADC3',0
	.word	30371
	.byte	8,2,35,24,0,21
	.word	30376
	.byte	9
	.byte	'Ifx_SRC_GDSADC',0,6,188,3,3
	.word	30467
	.byte	23,4
	.word	26970
	.byte	24,0,0,21
	.word	30496
	.byte	15
	.byte	'_Ifx_SRC_GEMEM',0,6,191,3,25,4,6
	.byte	'EMEM',0
	.word	30505
	.byte	4,2,35,0,0,21
	.word	30510
	.byte	9
	.byte	'Ifx_SRC_GEMEM',0,6,194,3,3
	.word	30546
	.byte	23,80
	.word	27039
	.byte	24,0,0,21
	.word	30574
	.byte	15
	.byte	'_Ifx_SRC_GERAY',0,6,197,3,25,80,6
	.byte	'ERAY',0
	.word	30583
	.byte	80,2,35,0,0,21
	.word	30588
	.byte	9
	.byte	'Ifx_SRC_GERAY',0,6,200,3,3
	.word	30624
	.byte	23,4
	.word	27193
	.byte	24,0,0,21
	.word	30652
	.byte	15
	.byte	'_Ifx_SRC_GETH',0,6,203,3,25,4,6
	.byte	'ETH',0
	.word	30661
	.byte	4,2,35,0,0,21
	.word	30666
	.byte	9
	.byte	'Ifx_SRC_GETH',0,6,206,3,3
	.word	30700
	.byte	23,4
	.word	27251
	.byte	24,0,0,21
	.word	30727
	.byte	15
	.byte	'_Ifx_SRC_GFCE',0,6,209,3,25,4,6
	.byte	'FCE',0
	.word	30736
	.byte	4,2,35,0,0,21
	.word	30741
	.byte	9
	.byte	'Ifx_SRC_GFCE',0,6,212,3,3
	.word	30775
	.byte	23,12
	.word	27309
	.byte	24,0,0,21
	.word	30802
	.byte	15
	.byte	'_Ifx_SRC_GFFT',0,6,215,3,25,12,6
	.byte	'FFT',0
	.word	30811
	.byte	12,2,35,0,0,21
	.word	30816
	.byte	9
	.byte	'Ifx_SRC_GFFT',0,6,218,3,3
	.word	30850
	.byte	23,64
	.word	27404
	.byte	24,1,0,21
	.word	30877
	.byte	15
	.byte	'_Ifx_SRC_GGPSR',0,6,221,3,25,64,6
	.byte	'GPSR',0
	.word	30886
	.byte	64,2,35,0,0,21
	.word	30891
	.byte	9
	.byte	'Ifx_SRC_GGPSR',0,6,224,3,3
	.word	30927
	.byte	23,48
	.word	27525
	.byte	24,0,0,21
	.word	30955
	.byte	15
	.byte	'_Ifx_SRC_GGPT12',0,6,227,3,25,48,6
	.byte	'GPT12',0
	.word	30964
	.byte	48,2,35,0,0,21
	.word	30969
	.byte	9
	.byte	'Ifx_SRC_GGPT12',0,6,230,3,3
	.word	31007
	.byte	23,204,18
	.word	27802
	.byte	24,0,0,21
	.word	31036
	.byte	15
	.byte	'_Ifx_SRC_GGTM',0,6,233,3,25,204,18,6
	.byte	'GTM',0
	.word	31046
	.byte	204,18,2,35,0,0,21
	.word	31051
	.byte	9
	.byte	'Ifx_SRC_GGTM',0,6,236,3,3
	.word	31087
	.byte	23,4
	.word	28288
	.byte	24,0,0,21
	.word	31114
	.byte	15
	.byte	'_Ifx_SRC_GHSCT',0,6,239,3,25,4,6
	.byte	'HSCT',0
	.word	31123
	.byte	4,2,35,0,0,21
	.word	31128
	.byte	9
	.byte	'Ifx_SRC_GHSCT',0,6,242,3,3
	.word	31164
	.byte	23,64
	.word	28348
	.byte	24,3,0,21
	.word	31192
	.byte	15
	.byte	'_Ifx_SRC_GHSSL',0,6,245,3,25,68,6
	.byte	'HSSL',0
	.word	31201
	.byte	64,2,35,0,6
	.byte	'EXI',0
	.word	2972
	.byte	4,2,35,64,0,21
	.word	31206
	.byte	9
	.byte	'Ifx_SRC_GHSSL',0,6,249,3,3
	.word	31255
	.byte	23,80
	.word	28457
	.byte	24,0,0,21
	.word	31283
	.byte	15
	.byte	'_Ifx_SRC_GI2C',0,6,252,3,25,80,6
	.byte	'I2C',0
	.word	31292
	.byte	80,2,35,0,0,21
	.word	31297
	.byte	9
	.byte	'Ifx_SRC_GI2C',0,6,255,3,3
	.word	31331
	.byte	23,4
	.word	28606
	.byte	24,0,0,21
	.word	31358
	.byte	15
	.byte	'_Ifx_SRC_GLMU',0,6,130,4,25,4,6
	.byte	'LMU',0
	.word	31367
	.byte	4,2,35,0,0,21
	.word	31372
	.byte	9
	.byte	'Ifx_SRC_GLMU',0,6,133,4,3
	.word	31406
	.byte	23,40
	.word	28664
	.byte	24,1,0,21
	.word	31433
	.byte	15
	.byte	'_Ifx_SRC_GMSC',0,6,136,4,25,40,6
	.byte	'MSC',0
	.word	31442
	.byte	40,2,35,0,0,21
	.word	31447
	.byte	9
	.byte	'Ifx_SRC_GMSC',0,6,139,4,3
	.word	31481
	.byte	23,8
	.word	28775
	.byte	24,1,0,21
	.word	31508
	.byte	15
	.byte	'_Ifx_SRC_GPMU',0,6,142,4,25,8,6
	.byte	'PMU',0
	.word	31517
	.byte	8,2,35,0,0,21
	.word	31522
	.byte	9
	.byte	'Ifx_SRC_GPMU',0,6,145,4,3
	.word	31556
	.byte	23,32
	.word	28833
	.byte	24,0,0,21
	.word	31583
	.byte	15
	.byte	'_Ifx_SRC_GPSI5',0,6,148,4,25,32,6
	.byte	'PSI5',0
	.word	31592
	.byte	32,2,35,0,0,21
	.word	31597
	.byte	9
	.byte	'Ifx_SRC_GPSI5',0,6,151,4,3
	.word	31633
	.byte	23,32
	.word	28893
	.byte	24,0,0,21
	.word	31661
	.byte	15
	.byte	'_Ifx_SRC_GPSI5S',0,6,154,4,25,32,6
	.byte	'PSI5S',0
	.word	31670
	.byte	32,2,35,0,0,21
	.word	31675
	.byte	9
	.byte	'Ifx_SRC_GPSI5S',0,6,157,4,3
	.word	31713
	.byte	23,96
	.word	28955
	.byte	24,3,0,21
	.word	31742
	.byte	15
	.byte	'_Ifx_SRC_GQSPI',0,6,160,4,25,96,6
	.byte	'QSPI',0
	.word	31751
	.byte	96,2,35,0,0,21
	.word	31756
	.byte	9
	.byte	'Ifx_SRC_GQSPI',0,6,163,4,3
	.word	31792
	.byte	23,4
	.word	29075
	.byte	24,0,0,21
	.word	31820
	.byte	15
	.byte	'_Ifx_SRC_GSCR',0,6,166,4,25,4,6
	.byte	'SCR',0
	.word	31829
	.byte	4,2,35,0,0,21
	.word	31834
	.byte	9
	.byte	'Ifx_SRC_GSCR',0,6,169,4,3
	.word	31868
	.byte	21
	.word	29133
	.byte	15
	.byte	'_Ifx_SRC_GSCU',0,6,172,4,25,20,6
	.byte	'SCU',0
	.word	31895
	.byte	20,2,35,0,0,21
	.word	31900
	.byte	9
	.byte	'Ifx_SRC_GSCU',0,6,175,4,3
	.word	31934
	.byte	23,24
	.word	29214
	.byte	24,0,0,21
	.word	31961
	.byte	15
	.byte	'_Ifx_SRC_GSENT',0,6,178,4,25,24,6
	.byte	'SENT',0
	.word	31970
	.byte	24,2,35,0,0,21
	.word	31975
	.byte	9
	.byte	'Ifx_SRC_GSENT',0,6,181,4,3
	.word	32011
	.byte	23,12
	.word	29274
	.byte	24,0,0,21
	.word	32039
	.byte	15
	.byte	'_Ifx_SRC_GSMU',0,6,184,4,25,12,6
	.byte	'SMU',0
	.word	32048
	.byte	12,2,35,0,0,21
	.word	32053
	.byte	9
	.byte	'Ifx_SRC_GSMU',0,6,187,4,3
	.word	32087
	.byte	23,16
	.word	29332
	.byte	24,1,0,21
	.word	32114
	.byte	15
	.byte	'_Ifx_SRC_GSTM',0,6,190,4,25,16,6
	.byte	'STM',0
	.word	32123
	.byte	16,2,35,0,0,21
	.word	32128
	.byte	9
	.byte	'Ifx_SRC_GSTM',0,6,193,4,3
	.word	32162
	.byte	23,64
	.word	29508
	.byte	24,3,0,21
	.word	32189
	.byte	23,224,1
	.word	267
	.byte	24,223,1,0,23,32
	.word	29404
	.byte	24,1,0,21
	.word	32214
	.byte	15
	.byte	'_Ifx_SRC_GVADC',0,6,196,4,25,192,2,6
	.byte	'G',0
	.word	32198
	.byte	64,2,35,0,6
	.byte	'reserved_40',0
	.word	32203
	.byte	224,1,2,35,64,6
	.byte	'CG',0
	.word	32223
	.byte	32,3,35,160,2,0,21
	.word	32228
	.byte	9
	.byte	'Ifx_SRC_GVADC',0,6,201,4,3
	.word	32297
	.byte	21
	.word	29610
	.byte	15
	.byte	'_Ifx_SRC_GXBAR',0,6,204,4,25,4,6
	.byte	'XBAR',0
	.word	32325
	.byte	4,2,35,0,0,21
	.word	32330
	.byte	9
	.byte	'Ifx_SRC_GXBAR',0,6,207,4,3
	.word	32366
	.byte	13,25,236,10,9,1,14
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,14
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,9
	.byte	'IfxScu_CCUCON0_CLKSEL',0,25,240,10,3
	.word	32394
	.byte	13,25,250,10,9,1,14
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,14
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,14
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,9
	.byte	'IfxScu_WDTCON1_IR',0,25,255,10,3
	.word	32491
	.byte	15
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,9,45,16,4,4
	.byte	'EN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_ACCEN0_Bits',0,9,79,3
	.word	32613
	.byte	15
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,9,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_SCU_ACCEN1_Bits',0,9,85,3
	.word	33170
	.byte	15
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,9,88,16,4,4
	.byte	'STM0DIS',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'STM1DIS',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'STM2DIS',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,4
	.word	2659
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,9,94,3
	.word	33247
	.byte	15
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,9,97,16,4,4
	.byte	'BAUD1DIV',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'BAUD2DIV',0,1
	.word	267
	.byte	4,0,2,35,0,4
	.byte	'SRIDIV',0,1
	.word	267
	.byte	4,4,2,35,1,4
	.byte	'LPDIV',0,1
	.word	267
	.byte	4,0,2,35,1,4
	.byte	'SPBDIV',0,1
	.word	267
	.byte	4,4,2,35,2,4
	.byte	'FSI2DIV',0,1
	.word	267
	.byte	2,2,2,35,2,4
	.byte	'reserved_22',0,1
	.word	267
	.byte	2,0,2,35,2,4
	.byte	'FSIDIV',0,1
	.word	267
	.byte	2,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	267
	.byte	2,4,2,35,3,4
	.byte	'CLKSEL',0,1
	.word	267
	.byte	2,2,2,35,3,4
	.byte	'UP',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON0_Bits',0,9,111,3
	.word	33383
	.byte	15
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,9,114,16,4,4
	.byte	'CANDIV',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'ERAYDIV',0,1
	.word	267
	.byte	4,0,2,35,0,4
	.byte	'STMDIV',0,1
	.word	267
	.byte	4,4,2,35,1,4
	.byte	'GTMDIV',0,1
	.word	267
	.byte	4,0,2,35,1,4
	.byte	'ETHDIV',0,1
	.word	267
	.byte	4,4,2,35,2,4
	.byte	'ASCLINFDIV',0,1
	.word	267
	.byte	4,0,2,35,2,4
	.byte	'ASCLINSDIV',0,1
	.word	267
	.byte	4,4,2,35,3,4
	.byte	'INSEL',0,1
	.word	267
	.byte	2,2,2,35,3,4
	.byte	'UP',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON1_Bits',0,9,126,3
	.word	33663
	.byte	15
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,9,129,1,16,4,4
	.byte	'BBBDIV',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	26,2,2,35,0,4
	.byte	'UP',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON2_Bits',0,9,135,1,3
	.word	33901
	.byte	15
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,9,138,1,16,4,4
	.byte	'PLLDIV',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'PLLSEL',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'PLLERAYDIV',0,1
	.word	267
	.byte	6,2,2,35,1,4
	.byte	'PLLERAYSEL',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'SRIDIV',0,1
	.word	267
	.byte	6,2,2,35,2,4
	.byte	'SRISEL',0,1
	.word	267
	.byte	2,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	5,3,2,35,3,4
	.byte	'SLCK',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'UP',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON3_Bits',0,9,150,1,3
	.word	34029
	.byte	15
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,9,153,1,16,4,4
	.byte	'SPBDIV',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'SPBSEL',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'GTMDIV',0,1
	.word	267
	.byte	6,2,2,35,1,4
	.byte	'GTMSEL',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'STMDIV',0,1
	.word	267
	.byte	6,2,2,35,2,4
	.byte	'STMSEL',0,1
	.word	267
	.byte	2,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	5,3,2,35,3,4
	.byte	'SLCK',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'UP',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON4_Bits',0,9,165,1,3
	.word	34272
	.byte	15
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,9,168,1,16,4,4
	.byte	'MAXDIV',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	26,2,2,35,0,4
	.byte	'UP',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON5_Bits',0,9,174,1,3
	.word	34507
	.byte	15
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,9,177,1,16,4,4
	.byte	'CPU0DIV',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2659
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON6_Bits',0,9,181,1,3
	.word	34635
	.byte	15
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,9,184,1,16,4,4
	.byte	'CPU1DIV',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2659
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON7_Bits',0,9,188,1,3
	.word	34735
	.byte	15
	.byte	'_Ifx_SCU_CHIPID_Bits',0,9,191,1,16,4,4
	.byte	'CHREV',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'CHTEC',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'CHID',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'EEA',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'UCODE',0,1
	.word	267
	.byte	7,0,2,35,2,4
	.byte	'FSIZE',0,1
	.word	267
	.byte	4,4,2,35,3,4
	.byte	'SP',0,1
	.word	267
	.byte	2,2,2,35,3,4
	.byte	'SEC',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CHIPID_Bits',0,9,202,1,3
	.word	34835
	.byte	15
	.byte	'_Ifx_SCU_DTSCON_Bits',0,9,205,1,16,4,4
	.byte	'PWD',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'START',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'CAL',0,4
	.word	2659
	.byte	20,8,2,35,0,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'SLCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_DTSCON_Bits',0,9,213,1,3
	.word	35043
	.byte	15
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,9,216,1,16,4,4
	.byte	'LOWER',0,2
	.word	928
	.byte	10,6,2,35,0,4
	.byte	'reserved_10',0,1
	.word	267
	.byte	5,1,2,35,1,4
	.byte	'LLU',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'UPPER',0,2
	.word	928
	.byte	10,6,2,35,2,4
	.byte	'reserved_26',0,1
	.word	267
	.byte	4,2,2,35,3,4
	.byte	'SLCK',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'UOF',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_DTSLIM_Bits',0,9,225,1,3
	.word	35208
	.byte	15
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,9,228,1,16,4,4
	.byte	'RESULT',0,2
	.word	928
	.byte	10,6,2,35,0,4
	.byte	'reserved_10',0,1
	.word	267
	.byte	4,2,2,35,1,4
	.byte	'RDY',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'BUSY',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,9,235,1,3
	.word	35391
	.byte	15
	.byte	'_Ifx_SCU_EICR_Bits',0,9,238,1,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'EXIS0',0,1
	.word	267
	.byte	3,1,2,35,0,4
	.byte	'reserved_7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'FEN0',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'REN0',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'LDEN0',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'EIEN0',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'INP0',0,1
	.word	267
	.byte	3,1,2,35,1,4
	.byte	'reserved_15',0,4
	.word	2659
	.byte	5,12,2,35,0,4
	.byte	'EXIS1',0,1
	.word	267
	.byte	3,1,2,35,2,4
	.byte	'reserved_23',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'FEN1',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'REN1',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'LDEN1',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'EIEN1',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'INP1',0,1
	.word	267
	.byte	3,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EICR_Bits',0,9,129,2,3
	.word	35545
	.byte	15
	.byte	'_Ifx_SCU_EIFR_Bits',0,9,132,2,16,4,4
	.byte	'INTF0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'INTF1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'INTF2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'INTF3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'INTF4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'INTF5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'INTF6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'INTF7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_EIFR_Bits',0,9,143,2,3
	.word	35909
	.byte	15
	.byte	'_Ifx_SCU_EMSR_Bits',0,9,146,2,16,4,4
	.byte	'POL',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'MODE',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'ENON',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PSEL',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,2
	.word	928
	.byte	12,0,2,35,0,4
	.byte	'EMSF',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'SEMSF',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,1
	.word	267
	.byte	6,0,2,35,2,4
	.byte	'EMSFM',0,1
	.word	267
	.byte	2,6,2,35,3,4
	.byte	'SEMSFM',0,1
	.word	267
	.byte	2,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	267
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_EMSR_Bits',0,9,159,2,3
	.word	36120
	.byte	15
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,9,162,2,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	7,1,2,35,0,4
	.byte	'EDCON',0,2
	.word	928
	.byte	2,7,2,35,0,4
	.byte	'reserved_9',0,4
	.word	2659
	.byte	23,0,2,35,0,0,9
	.byte	'Ifx_SCU_ESRCFG_Bits',0,9,167,2,3
	.word	36372
	.byte	15
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,9,170,2,16,4,4
	.byte	'ARI',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'ARC',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_ESROCFG_Bits',0,9,175,2,3
	.word	36490
	.byte	15
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,9,178,2,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	28,4,2,35,0,4
	.byte	'EVR13OFF',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'BPEVR13OFF',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVR13CON_Bits',0,9,185,2,3
	.word	36601
	.byte	15
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,9,188,2,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	28,4,2,35,0,4
	.byte	'EVR33OFF',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'BPEVR33OFF',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVR33CON_Bits',0,9,195,2,3
	.word	36764
	.byte	15
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,9,198,2,16,4,4
	.byte	'ADC13V',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'ADC33V',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'ADCSWDV',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'VAL',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,9,205,2,3
	.word	36927
	.byte	15
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,9,208,2,16,4,4
	.byte	'DVS13TRIM',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'DVS33TRIM',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'VAL',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,9,215,2,3
	.word	37085
	.byte	15
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,9,218,2,16,4,4
	.byte	'EVR13OVMOD',0,1
	.word	267
	.byte	2,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'EVR13UVMOD',0,1
	.word	267
	.byte	2,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'EVR33OVMOD',0,1
	.word	267
	.byte	2,6,2,35,1,4
	.byte	'reserved_10',0,1
	.word	267
	.byte	2,4,2,35,1,4
	.byte	'EVR33UVMOD',0,1
	.word	267
	.byte	2,2,2,35,1,4
	.byte	'reserved_14',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'SWDOVMOD',0,1
	.word	267
	.byte	2,6,2,35,2,4
	.byte	'reserved_18',0,1
	.word	267
	.byte	2,4,2,35,2,4
	.byte	'SWDUVMOD',0,1
	.word	267
	.byte	2,2,2,35,2,4
	.byte	'reserved_22',0,2
	.word	928
	.byte	10,0,2,35,2,0,9
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,9,232,2,3
	.word	37250
	.byte	15
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,9,235,2,16,4,4
	.byte	'OSCTRIM',0,2
	.word	928
	.byte	10,6,2,35,0,4
	.byte	'OSCPTAT',0,1
	.word	267
	.byte	6,0,2,35,1,4
	.byte	'OSCANASEL',0,1
	.word	267
	.byte	4,4,2,35,2,4
	.byte	'HPBGTRIM',0,2
	.word	928
	.byte	7,5,2,35,2,4
	.byte	'HPBGCLKEN',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'OSC3V3',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	267
	.byte	2,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,9,245,2,3
	.word	37579
	.byte	15
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,9,248,2,16,4,4
	.byte	'EVR13OVVAL',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'EVR33OVVAL',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'SWDOVVAL',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVROVMON_Bits',0,9,255,2,3
	.word	37800
	.byte	15
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,9,130,3,16,4,4
	.byte	'RST13TRIM',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	16,8,2,35,0,4
	.byte	'RST13OFF',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'BPRST13OFF',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'RST33OFF',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'BPRST33OFF',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'RSTSWDOFF',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'BPRSTSWDOFF',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,9,142,3,3
	.word	37963
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,9,145,3,16,4,4
	.byte	'SD5P',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'SD5I',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'SD5D',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,9,152,3,3
	.word	38235
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,9,155,3,16,4,4
	.byte	'SD33P',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'SD33I',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'SD33D',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,9,162,3,3
	.word	38388
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,9,165,3,16,4,4
	.byte	'CT5REG0',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'CT5REG1',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'CT5REG2',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,9,172,3,3
	.word	38544
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,9,175,3,16,4,4
	.byte	'CT5REG3',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'CT5REG4',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	15,1,2,35,2,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,9,181,3,3
	.word	38706
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,9,184,3,16,4,4
	.byte	'CT33REG0',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'CT33REG1',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'CT33REG2',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,9,191,3,3
	.word	38849
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,9,194,3,16,4,4
	.byte	'CT33REG3',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'CT33REG4',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	15,1,2,35,2,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,9,200,3,3
	.word	39014
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,9,203,3,16,4,4
	.byte	'SDFREQSPRD',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'SDFREQ',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'SDSTEP',0,1
	.word	267
	.byte	4,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	267
	.byte	2,2,2,35,3,4
	.byte	'SDSAMPLE',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,9,211,3,3
	.word	39159
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,9,214,3,16,4,4
	.byte	'DRVP',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'SDMINMAXDC',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'DRVN',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'SDLUT',0,1
	.word	267
	.byte	6,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,9,222,3,3
	.word	39340
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,9,225,3,16,4,4
	.byte	'SDPWMPRE',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'SDPID',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'SDVOKLVL',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,9,232,3,3
	.word	39514
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,9,235,3,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'SYNCDIV',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'reserved_11',0,4
	.word	2659
	.byte	20,1,2,35,0,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,9,241,3,3
	.word	39674
	.byte	15
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,9,244,3,16,4,4
	.byte	'EVR13',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'OV13',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EVR33',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'OV33',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'OVSWD',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'UV13',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'UV33',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'UVSWD',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'EXTPASS13',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'EXTPASS33',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'BGPROK',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'reserved_11',0,4
	.word	2659
	.byte	21,0,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,9,130,4,3
	.word	39818
	.byte	15
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,9,133,4,16,4,4
	.byte	'EVR13TRIM',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'SDVOUTSEL',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	15,1,2,35,2,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,9,139,4,3
	.word	40092
	.byte	15
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,9,142,4,16,4,4
	.byte	'EVR13UVVAL',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'EVR33UVVAL',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'SWDUVVAL',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,9,149,4,3
	.word	40231
	.byte	15
	.byte	'_Ifx_SCU_EXTCON_Bits',0,9,152,4,16,4,4
	.byte	'EN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'SEL0',0,1
	.word	267
	.byte	4,2,2,35,0,4
	.byte	'reserved_6',0,2
	.word	928
	.byte	10,0,2,35,0,4
	.byte	'EN1',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'NSEL',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'SEL1',0,1
	.word	267
	.byte	4,2,2,35,2,4
	.byte	'reserved_22',0,1
	.word	267
	.byte	2,0,2,35,2,4
	.byte	'DIV1',0,1
	.word	267
	.byte	8,0,2,35,3,0,9
	.byte	'Ifx_SCU_EXTCON_Bits',0,9,163,4,3
	.word	40394
	.byte	15
	.byte	'_Ifx_SCU_FDR_Bits',0,9,166,4,16,4,4
	.byte	'STEP',0,2
	.word	928
	.byte	10,6,2,35,0,4
	.byte	'reserved_10',0,1
	.word	267
	.byte	4,2,2,35,1,4
	.byte	'DM',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'RESULT',0,2
	.word	928
	.byte	10,6,2,35,2,4
	.byte	'reserved_26',0,1
	.word	267
	.byte	5,1,2,35,3,4
	.byte	'DISCLK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_FDR_Bits',0,9,174,4,3
	.word	40612
	.byte	15
	.byte	'_Ifx_SCU_FMR_Bits',0,9,177,4,16,4,4
	.byte	'FS0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'FS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'FS2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'FS3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'FS4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'FS5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'FS6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'FS7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'FC0',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'FC1',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'FC2',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'FC3',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'FC4',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'FC5',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'FC6',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'FC7',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	8,0,2,35,3,0,9
	.byte	'Ifx_SCU_FMR_Bits',0,9,197,4,3
	.word	40775
	.byte	15
	.byte	'_Ifx_SCU_ID_Bits',0,9,200,4,16,4,4
	.byte	'MODREV',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_ID_Bits',0,9,205,4,3
	.word	41111
	.byte	15
	.byte	'_Ifx_SCU_IGCR_Bits',0,9,208,4,16,4,4
	.byte	'IPEN00',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'IPEN01',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'IPEN02',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'IPEN03',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'IPEN04',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'IPEN05',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'IPEN06',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'IPEN07',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	5,3,2,35,1,4
	.byte	'GEEN0',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'IGP0',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'IPEN10',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'IPEN11',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'IPEN12',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'IPEN13',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'IPEN14',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'IPEN15',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'IPEN16',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'IPEN17',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	5,3,2,35,3,4
	.byte	'GEEN1',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'IGP1',0,1
	.word	267
	.byte	2,0,2,35,3,0,9
	.byte	'Ifx_SCU_IGCR_Bits',0,9,232,4,3
	.word	41218
	.byte	15
	.byte	'_Ifx_SCU_IN_Bits',0,9,235,4,16,4,4
	.byte	'P0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_IN_Bits',0,9,240,4,3
	.word	41670
	.byte	15
	.byte	'_Ifx_SCU_IOCR_Bits',0,9,243,4,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'PC0',0,1
	.word	267
	.byte	4,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	4,4,2,35,1,4
	.byte	'PC1',0,1
	.word	267
	.byte	4,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_IOCR_Bits',0,9,250,4,3
	.word	41769
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,9,253,4,16,4,4
	.byte	'LBISTREQ',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'LBISTREQP',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PATTERNS',0,2
	.word	928
	.byte	14,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,9,131,5,3
	.word	41919
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,9,134,5,16,4,4
	.byte	'SEED',0,4
	.word	2659
	.byte	23,9,2,35,0,4
	.byte	'reserved_23',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'SPLITSH',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'BODY',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'LBISTFREQU',0,1
	.word	267
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,9,141,5,3
	.word	42068
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,9,144,5,16,4,4
	.byte	'SIGNATURE',0,4
	.word	2659
	.byte	24,8,2,35,0,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	7,1,2,35,3,4
	.byte	'LBISTDONE',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,9,149,5,3
	.word	42229
	.byte	15
	.byte	'_Ifx_SCU_LCLCON_Bits',0,9,152,5,16,4,4
	.byte	'reserved_0',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'LS',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,2
	.word	928
	.byte	14,1,2,35,2,4
	.byte	'LSEN',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_LCLCON_Bits',0,9,158,5,3
	.word	42359
	.byte	15
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,9,161,5,16,4,4
	.byte	'LCLT0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'LCLT1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_LCLTEST_Bits',0,9,166,5,3
	.word	42491
	.byte	15
	.byte	'_Ifx_SCU_MANID_Bits',0,9,169,5,16,4,4
	.byte	'DEPT',0,1
	.word	267
	.byte	5,3,2,35,0,4
	.byte	'MANUF',0,2
	.word	928
	.byte	11,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_MANID_Bits',0,9,174,5,3
	.word	42606
	.byte	15
	.byte	'_Ifx_SCU_OMR_Bits',0,9,177,5,16,4,4
	.byte	'PS0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,2
	.word	928
	.byte	14,0,2,35,0,4
	.byte	'PCL0',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,2
	.word	928
	.byte	14,0,2,35,2,0,9
	.byte	'Ifx_SCU_OMR_Bits',0,9,185,5,3
	.word	42717
	.byte	15
	.byte	'_Ifx_SCU_OSCCON_Bits',0,9,188,5,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PLLLV',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'OSCRES',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'GAINSEL',0,1
	.word	267
	.byte	2,3,2,35,0,4
	.byte	'MODE',0,1
	.word	267
	.byte	2,1,2,35,0,4
	.byte	'SHBY',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'PLLHV',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'reserved_9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'X1D',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'X1DEN',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,1
	.word	267
	.byte	4,0,2,35,1,4
	.byte	'OSCVAL',0,1
	.word	267
	.byte	5,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	267
	.byte	2,1,2,35,2,4
	.byte	'APREN',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'CAP0EN',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'CAP1EN',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'CAP2EN',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'CAP3EN',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	267
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_OSCCON_Bits',0,9,209,5,3
	.word	42875
	.byte	15
	.byte	'_Ifx_SCU_OUT_Bits',0,9,212,5,16,4,4
	.byte	'P0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_OUT_Bits',0,9,217,5,3
	.word	43287
	.byte	15
	.byte	'_Ifx_SCU_OVCCON_Bits',0,9,220,5,16,4,4
	.byte	'CSEL0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'CSEL1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'CSEL2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,2
	.word	928
	.byte	13,0,2,35,0,4
	.byte	'OVSTRT',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'OVSTP',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'DCINVAL',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	267
	.byte	5,0,2,35,2,4
	.byte	'OVCONF',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'POVCONF',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	267
	.byte	6,0,2,35,3,0,9
	.byte	'Ifx_SCU_OVCCON_Bits',0,9,233,5,3
	.word	43388
	.byte	15
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,9,236,5,16,4,4
	.byte	'OVEN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'OVEN1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'OVEN2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,4
	.word	2659
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,9,242,5,3
	.word	43655
	.byte	15
	.byte	'_Ifx_SCU_PDISC_Bits',0,9,245,5,16,4,4
	.byte	'PDIS0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PDIS1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_PDISC_Bits',0,9,250,5,3
	.word	43791
	.byte	15
	.byte	'_Ifx_SCU_PDR_Bits',0,9,253,5,16,4,4
	.byte	'PD0',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'PL0',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PD1',0,1
	.word	267
	.byte	3,1,2,35,0,4
	.byte	'PL1',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_PDR_Bits',0,9,132,6,3
	.word	43902
	.byte	15
	.byte	'_Ifx_SCU_PDRR_Bits',0,9,135,6,16,4,4
	.byte	'PDR0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PDR1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PDR2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PDR3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PDR4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'PDR5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PDR6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PDR7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_PDRR_Bits',0,9,146,6,3
	.word	44035
	.byte	15
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,9,149,6,16,4,4
	.byte	'VCOBYP',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'VCOPWD',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'MODEN',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'SETFINDIS',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'CLRFINDIS',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'OSCDISCDIS',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'reserved_7',0,2
	.word	928
	.byte	2,7,2,35,0,4
	.byte	'NDIV',0,1
	.word	267
	.byte	7,0,2,35,1,4
	.byte	'PLLPWD',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'RESLD',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	267
	.byte	5,0,2,35,2,4
	.byte	'PDIV',0,1
	.word	267
	.byte	4,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	267
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_PLLCON0_Bits',0,9,166,6,3
	.word	44238
	.byte	15
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,9,169,6,16,4,4
	.byte	'K2DIV',0,1
	.word	267
	.byte	7,1,2,35,0,4
	.byte	'reserved_7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'K3DIV',0,1
	.word	267
	.byte	7,1,2,35,1,4
	.byte	'reserved_15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'K1DIV',0,1
	.word	267
	.byte	7,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	928
	.byte	9,0,2,35,2,0,9
	.byte	'Ifx_SCU_PLLCON1_Bits',0,9,177,6,3
	.word	44594
	.byte	15
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,9,180,6,16,4,4
	.byte	'MODCFG',0,2
	.word	928
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_PLLCON2_Bits',0,9,184,6,3
	.word	44772
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,9,187,6,16,4,4
	.byte	'VCOBYP',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'VCOPWD',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'SETFINDIS',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'CLRFINDIS',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'OSCDISCDIS',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'reserved_7',0,2
	.word	928
	.byte	2,7,2,35,0,4
	.byte	'NDIV',0,1
	.word	267
	.byte	5,2,2,35,1,4
	.byte	'reserved_14',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'PLLPWD',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'RESLD',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	267
	.byte	5,0,2,35,2,4
	.byte	'PDIV',0,1
	.word	267
	.byte	4,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	267
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,9,204,6,3
	.word	44872
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,9,207,6,16,4,4
	.byte	'K2DIV',0,1
	.word	267
	.byte	7,1,2,35,0,4
	.byte	'reserved_7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'K3DIV',0,1
	.word	267
	.byte	4,4,2,35,1,4
	.byte	'reserved_12',0,1
	.word	267
	.byte	4,0,2,35,1,4
	.byte	'K1DIV',0,1
	.word	267
	.byte	7,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	928
	.byte	9,0,2,35,2,0,9
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,9,215,6,3
	.word	45242
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,9,218,6,16,4,4
	.byte	'VCOBYST',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PWDSTAT',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'VCOLOCK',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'FINDIS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'K1RDY',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'K2RDY',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2659
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,9,227,6,3
	.word	45428
	.byte	15
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,9,230,6,16,4,4
	.byte	'VCOBYST',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'VCOLOCK',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'FINDIS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'K1RDY',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'K2RDY',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'MODRUN',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,9,241,6,3
	.word	45626
	.byte	15
	.byte	'_Ifx_SCU_PMCSR_Bits',0,9,244,6,16,4,4
	.byte	'REQSLP',0,1
	.word	267
	.byte	2,6,2,35,0,4
	.byte	'SMUSLP',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,1
	.word	267
	.byte	5,0,2,35,0,4
	.byte	'PMST',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'reserved_11',0,4
	.word	2659
	.byte	21,0,2,35,0,0,9
	.byte	'Ifx_SCU_PMCSR_Bits',0,9,251,6,3
	.word	45859
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,9,254,6,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'ESR1WKEN',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PINAWKEN',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PINBWKEN',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'ESR0DFEN',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'ESR0EDCON',0,1
	.word	267
	.byte	2,1,2,35,0,4
	.byte	'ESR1DFEN',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'ESR1EDCON',0,1
	.word	267
	.byte	2,6,2,35,1,4
	.byte	'PINADFEN',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'PINAEDCON',0,1
	.word	267
	.byte	2,3,2,35,1,4
	.byte	'PINBDFEN',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'PINBEDCON',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'SCREN',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'STBYRAMSEL',0,1
	.word	267
	.byte	2,5,2,35,2,4
	.byte	'SCRCLKSEL',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'SCRWKEN',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'TRISTEN',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'TRISTREQ',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'PORSTDF',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'DCDCSYNC',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	267
	.byte	3,3,2,35,3,4
	.byte	'ESR0TRIST',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,9,153,7,3
	.word	46011
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,9,156,7,16,4,4
	.byte	'SCRSTEN',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'SCRSTREQ',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	6,0,2,35,0,4
	.byte	'CPUIDLSEL',0,1
	.word	267
	.byte	3,5,2,35,1,4
	.byte	'reserved_11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'IRADIS',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	267
	.byte	3,0,2,35,1,4
	.byte	'SCRCFG',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'CPUSEL',0,1
	.word	267
	.byte	3,5,2,35,3,4
	.byte	'STBYEVEN',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'STBYEV',0,1
	.word	267
	.byte	3,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,9,170,7,3
	.word	46578
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,9,173,7,16,4,4
	.byte	'SCRINT',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'BUSY',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'SCRECC',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'SCRWDT',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'SCRRST',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,1
	.word	267
	.byte	4,0,2,35,1,4
	.byte	'TCINT',0,1
	.word	267
	.byte	8,0,2,35,2,4
	.byte	'TCINTREQ',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'SMURST',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'RST',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	267
	.byte	4,1,2,35,3,4
	.byte	'LCK',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,9,187,7,3
	.word	46872
	.byte	15
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,9,190,7,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	2,6,2,35,0,4
	.byte	'ESR1WKP',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'ESR1OVRUN',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PINAWKP',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'PINAOVRUN',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PINBWKP',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PINBOVRUN',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'PORSTDF',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'HWCFGEVR',0,1
	.word	267
	.byte	3,3,2,35,1,4
	.byte	'STBYRAM',0,1
	.word	267
	.byte	2,1,2,35,1,4
	.byte	'TRIST',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'SCRST',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'SCRWKP',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'SCR',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'SCRWKEN',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'ESR1WKEN',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'PINAWKEN',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'PINBWKEN',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	928
	.byte	4,5,2,35,2,4
	.byte	'ESR0TRIST',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	267
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,9,214,7,3
	.word	47150
	.byte	15
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,9,217,7,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	2,6,2,35,0,4
	.byte	'ESR1WKPCLR',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'ESR1OVRUNCLR',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PINAWKPCLR',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'PINAOVRUNCLR',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PINBWKPCLR',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PINBOVRUNCLR',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'SCRSTCLR',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'SCRWKPCLR',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,2
	.word	928
	.byte	14,0,2,35,2,0,9
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,9,230,7,3
	.word	47646
	.byte	15
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,9,233,7,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'CLRC',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,2
	.word	928
	.byte	10,4,2,35,0,4
	.byte	'CSS0',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'CSS1',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'CSS2',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'reserved_15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'USRINFO',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_RSTCON2_Bits',0,9,243,7,3
	.word	47959
	.byte	15
	.byte	'_Ifx_SCU_RSTCON_Bits',0,9,246,7,16,4,4
	.byte	'ESR0',0,1
	.word	267
	.byte	2,6,2,35,0,4
	.byte	'ESR1',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'reserved_4',0,1
	.word	267
	.byte	2,2,2,35,0,4
	.byte	'SMU',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'SW',0,1
	.word	267
	.byte	2,6,2,35,1,4
	.byte	'STM0',0,1
	.word	267
	.byte	2,4,2,35,1,4
	.byte	'STM1',0,1
	.word	267
	.byte	2,2,2,35,1,4
	.byte	'STM2',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_RSTCON_Bits',0,9,129,8,3
	.word	48168
	.byte	15
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,9,132,8,16,4,4
	.byte	'ESR0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'ESR1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'SMU',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'SW',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'STM0',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'STM1',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'STM2',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'PORST',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'CB0',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'CB1',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'CB3',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	267
	.byte	2,1,2,35,2,4
	.byte	'EVR13',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'EVR33',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'SWD',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	267
	.byte	2,4,2,35,3,4
	.byte	'STBYR',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	267
	.byte	3,0,2,35,3,0,9
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,9,155,8,3
	.word	48379
	.byte	15
	.byte	'_Ifx_SCU_SAFECON_Bits',0,9,158,8,16,4,4
	.byte	'HBT',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2659
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_SCU_SAFECON_Bits',0,9,162,8,3
	.word	48811
	.byte	15
	.byte	'_Ifx_SCU_STSTAT_Bits',0,9,165,8,16,4,4
	.byte	'HWCFG',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'FTM',0,1
	.word	267
	.byte	7,1,2,35,1,4
	.byte	'MODE',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'FCBAE',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'LUDIS',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'TRSTL',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'SPDEN',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	267
	.byte	3,0,2,35,2,4
	.byte	'RAMINT',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'reserved_25',0,1
	.word	267
	.byte	7,0,2,35,3,0,9
	.byte	'Ifx_SCU_STSTAT_Bits',0,9,178,8,3
	.word	48907
	.byte	15
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,9,181,8,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'SWRSTREQ',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,9,186,8,3
	.word	49167
	.byte	15
	.byte	'_Ifx_SCU_SYSCON_Bits',0,9,189,8,16,4,4
	.byte	'CCTRIG0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'RAMINTM',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'SETLUDIS',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'reserved_5',0,1
	.word	267
	.byte	3,0,2,35,0,4
	.byte	'DATM',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'reserved_9',0,4
	.word	2659
	.byte	23,0,2,35,0,0,9
	.byte	'Ifx_SCU_SYSCON_Bits',0,9,198,8,3
	.word	49292
	.byte	15
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,9,201,8,16,4,4
	.byte	'ESR0T',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,9,208,8,3
	.word	49489
	.byte	15
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,9,211,8,16,4,4
	.byte	'ESR0T',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,9,218,8,3
	.word	49642
	.byte	15
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,9,221,8,16,4,4
	.byte	'ESR0T',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSET_Bits',0,9,228,8,3
	.word	49795
	.byte	15
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,9,231,8,16,4,4
	.byte	'ESR0T',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,9,238,8,3
	.word	49948
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,9,247,8,3
	.word	3253
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,9,134,9,3
	.word	3389
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,9,150,9,3
	.word	3633
	.byte	15
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,9,153,9,16,4,4
	.byte	'ENDINIT',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'LCK',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'PW',0,4
	.word	3237
	.byte	14,16,2,35,0,4
	.byte	'REL',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,9,159,9,3
	.word	50203
	.byte	15
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,9,162,9,16,4,4
	.byte	'CLRIRF',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'IR0',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'DR',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'IR1',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'UR',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PAR',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'TCR',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'TCTR',0,1
	.word	267
	.byte	7,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,9,175,9,3
	.word	50329
	.byte	15
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,9,178,9,16,4,4
	.byte	'AE',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'OE',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'IS0',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'DS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'TO',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'IS1',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'US',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PAS',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'TCS',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'TCT',0,1
	.word	267
	.byte	7,0,2,35,1,4
	.byte	'TIM',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,9,191,9,3
	.word	50581
	.byte	5,9,199,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	32613
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ACCEN0',0,9,204,9,3
	.word	50800
	.byte	5,9,207,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33170
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ACCEN1',0,9,212,9,3
	.word	50864
	.byte	5,9,215,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33247
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ARSTDIS',0,9,220,9,3
	.word	50928
	.byte	5,9,223,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33383
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON0',0,9,228,9,3
	.word	50993
	.byte	5,9,231,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33663
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON1',0,9,236,9,3
	.word	51058
	.byte	5,9,239,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33901
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON2',0,9,244,9,3
	.word	51123
	.byte	5,9,247,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34029
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON3',0,9,252,9,3
	.word	51188
	.byte	5,9,255,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34272
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON4',0,9,132,10,3
	.word	51253
	.byte	5,9,135,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34507
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON5',0,9,140,10,3
	.word	51318
	.byte	5,9,143,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34635
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON6',0,9,148,10,3
	.word	51383
	.byte	5,9,151,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34735
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON7',0,9,156,10,3
	.word	51448
	.byte	5,9,159,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34835
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CHIPID',0,9,164,10,3
	.word	51513
	.byte	5,9,167,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35043
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_DTSCON',0,9,172,10,3
	.word	51577
	.byte	5,9,175,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35208
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_DTSLIM',0,9,180,10,3
	.word	51641
	.byte	5,9,183,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35391
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_DTSSTAT',0,9,188,10,3
	.word	51705
	.byte	5,9,191,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35545
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EICR',0,9,196,10,3
	.word	51770
	.byte	5,9,199,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35909
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EIFR',0,9,204,10,3
	.word	51832
	.byte	5,9,207,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36120
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EMSR',0,9,212,10,3
	.word	51894
	.byte	5,9,215,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36372
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ESRCFG',0,9,220,10,3
	.word	51956
	.byte	5,9,223,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36490
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ESROCFG',0,9,228,10,3
	.word	52020
	.byte	5,9,231,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36601
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVR13CON',0,9,236,10,3
	.word	52085
	.byte	5,9,239,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36764
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVR33CON',0,9,244,10,3
	.word	52151
	.byte	5,9,247,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36927
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRADCSTAT',0,9,252,10,3
	.word	52217
	.byte	5,9,255,10,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37085
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRDVSTAT',0,9,132,11,3
	.word	52285
	.byte	5,9,135,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37250
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRMONCTRL',0,9,140,11,3
	.word	52352
	.byte	5,9,143,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37579
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVROSCCTRL',0,9,148,11,3
	.word	52420
	.byte	5,9,151,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37800
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVROVMON',0,9,156,11,3
	.word	52488
	.byte	5,9,159,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37963
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRRSTCON',0,9,164,11,3
	.word	52554
	.byte	5,9,167,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38235
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,9,172,11,3
	.word	52621
	.byte	5,9,175,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38388
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,9,180,11,3
	.word	52690
	.byte	5,9,183,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38544
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,9,188,11,3
	.word	52759
	.byte	5,9,191,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38706
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,9,196,11,3
	.word	52828
	.byte	5,9,199,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38849
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,9,204,11,3
	.word	52897
	.byte	5,9,207,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39014
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,9,212,11,3
	.word	52966
	.byte	5,9,215,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39159
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL1',0,9,220,11,3
	.word	53035
	.byte	5,9,223,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39340
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL2',0,9,228,11,3
	.word	53103
	.byte	5,9,231,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39514
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL3',0,9,236,11,3
	.word	53171
	.byte	5,9,239,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39674
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL4',0,9,244,11,3
	.word	53239
	.byte	5,9,247,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39818
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSTAT',0,9,252,11,3
	.word	53307
	.byte	5,9,255,11,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40092
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRTRIM',0,9,132,12,3
	.word	53372
	.byte	5,9,135,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40231
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRUVMON',0,9,140,12,3
	.word	53437
	.byte	5,9,143,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40394
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EXTCON',0,9,148,12,3
	.word	53503
	.byte	5,9,151,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40612
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_FDR',0,9,156,12,3
	.word	53567
	.byte	5,9,159,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40775
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_FMR',0,9,164,12,3
	.word	53628
	.byte	5,9,167,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41111
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ID',0,9,172,12,3
	.word	53689
	.byte	5,9,175,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41218
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_IGCR',0,9,180,12,3
	.word	53749
	.byte	5,9,183,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41670
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_IN',0,9,188,12,3
	.word	53811
	.byte	5,9,191,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41769
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_IOCR',0,9,196,12,3
	.word	53871
	.byte	5,9,199,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41919
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LBISTCTRL0',0,9,204,12,3
	.word	53933
	.byte	5,9,207,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42068
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LBISTCTRL1',0,9,212,12,3
	.word	54001
	.byte	5,9,215,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42229
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LBISTCTRL2',0,9,220,12,3
	.word	54069
	.byte	5,9,223,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42359
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LCLCON',0,9,228,12,3
	.word	54137
	.byte	5,9,231,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42491
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LCLTEST',0,9,236,12,3
	.word	54201
	.byte	5,9,239,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42606
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_MANID',0,9,244,12,3
	.word	54266
	.byte	5,9,247,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42717
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OMR',0,9,252,12,3
	.word	54329
	.byte	5,9,255,12,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42875
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OSCCON',0,9,132,13,3
	.word	54390
	.byte	5,9,135,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43287
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OUT',0,9,140,13,3
	.word	54454
	.byte	5,9,143,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43388
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OVCCON',0,9,148,13,3
	.word	54515
	.byte	5,9,151,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43655
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OVCENABLE',0,9,156,13,3
	.word	54579
	.byte	5,9,159,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43791
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PDISC',0,9,164,13,3
	.word	54646
	.byte	5,9,167,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43902
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PDR',0,9,172,13,3
	.word	54709
	.byte	5,9,175,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44035
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PDRR',0,9,180,13,3
	.word	54770
	.byte	5,9,183,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44238
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLCON0',0,9,188,13,3
	.word	54832
	.byte	5,9,191,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44594
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLCON1',0,9,196,13,3
	.word	54897
	.byte	5,9,199,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44772
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLCON2',0,9,204,13,3
	.word	54962
	.byte	5,9,207,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44872
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYCON0',0,9,212,13,3
	.word	55027
	.byte	5,9,215,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	45242
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYCON1',0,9,220,13,3
	.word	55096
	.byte	5,9,223,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	45428
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYSTAT',0,9,228,13,3
	.word	55165
	.byte	5,9,231,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	45626
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLSTAT',0,9,236,13,3
	.word	55234
	.byte	5,9,239,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	45859
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMCSR',0,9,244,13,3
	.word	55299
	.byte	5,9,247,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	46011
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWCR0',0,9,252,13,3
	.word	55362
	.byte	5,9,255,13,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	46578
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWCR1',0,9,132,14,3
	.word	55427
	.byte	5,9,135,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	46872
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWCR2',0,9,140,14,3
	.word	55492
	.byte	5,9,143,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	47150
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWSTAT',0,9,148,14,3
	.word	55557
	.byte	5,9,151,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	47646
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWSTATCLR',0,9,156,14,3
	.word	55623
	.byte	5,9,159,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48168
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_RSTCON',0,9,164,14,3
	.word	55692
	.byte	5,9,167,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	47959
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_RSTCON2',0,9,172,14,3
	.word	55756
	.byte	5,9,175,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48379
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_RSTSTAT',0,9,180,14,3
	.word	55821
	.byte	5,9,183,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48811
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_SAFECON',0,9,188,14,3
	.word	55886
	.byte	5,9,191,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48907
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_STSTAT',0,9,196,14,3
	.word	55951
	.byte	5,9,199,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49167
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_SWRSTCON',0,9,204,14,3
	.word	56015
	.byte	5,9,207,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49292
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_SYSCON',0,9,212,14,3
	.word	56081
	.byte	5,9,215,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49489
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPCLR',0,9,220,14,3
	.word	56145
	.byte	5,9,223,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49642
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPDIS',0,9,228,14,3
	.word	56210
	.byte	5,9,231,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49795
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSET',0,9,236,14,3
	.word	56275
	.byte	5,9,239,14,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49948
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSTAT',0,9,244,14,3
	.word	56340
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON0',0,9,252,14,3
	.word	3349
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON1',0,9,132,15,3
	.word	3593
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_SR',0,9,140,15,3
	.word	3824
	.byte	5,9,143,15,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	50203
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_CON0',0,9,148,15,3
	.word	56491
	.byte	5,9,151,15,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	50329
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_CON1',0,9,156,15,3
	.word	56558
	.byte	5,9,159,15,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	50581
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_SR',0,9,164,15,3
	.word	56625
	.byte	21
	.word	3864
	.byte	9
	.byte	'Ifx_SCU_WDTCPU',0,9,180,15,3
	.word	56690
	.byte	15
	.byte	'_Ifx_SCU_WDTS',0,9,183,15,25,12,6
	.byte	'CON0',0
	.word	56491
	.byte	4,2,35,0,6
	.byte	'CON1',0
	.word	56558
	.byte	4,2,35,4,6
	.byte	'SR',0
	.word	56625
	.byte	4,2,35,8,0,21
	.word	56719
	.byte	9
	.byte	'Ifx_SCU_WDTS',0,9,188,15,3
	.word	56780
	.byte	23,8
	.word	51956
	.byte	24,1,0,23,20
	.word	267
	.byte	24,19,0,23,8
	.word	55299
	.byte	24,1,0,21
	.word	56719
	.byte	23,24
	.word	3864
	.byte	24,1,0,21
	.word	56839
	.byte	23,28
	.word	267
	.byte	24,27,0,23,16
	.word	51770
	.byte	24,3,0,23,16
	.word	53749
	.byte	24,3,0,23,180,3
	.word	267
	.byte	24,179,3,0,15
	.byte	'_Ifx_SCU',0,9,201,15,25,128,8,6
	.byte	'reserved_0',0
	.word	7352
	.byte	8,2,35,0,6
	.byte	'ID',0
	.word	53689
	.byte	4,2,35,8,6
	.byte	'reserved_C',0
	.word	5533
	.byte	4,2,35,12,6
	.byte	'OSCCON',0
	.word	54390
	.byte	4,2,35,16,6
	.byte	'PLLSTAT',0
	.word	55234
	.byte	4,2,35,20,6
	.byte	'PLLCON0',0
	.word	54832
	.byte	4,2,35,24,6
	.byte	'PLLCON1',0
	.word	54897
	.byte	4,2,35,28,6
	.byte	'PLLCON2',0
	.word	54962
	.byte	4,2,35,32,6
	.byte	'PLLERAYSTAT',0
	.word	55165
	.byte	4,2,35,36,6
	.byte	'PLLERAYCON0',0
	.word	55027
	.byte	4,2,35,40,6
	.byte	'PLLERAYCON1',0
	.word	55096
	.byte	4,2,35,44,6
	.byte	'CCUCON0',0
	.word	50993
	.byte	4,2,35,48,6
	.byte	'CCUCON1',0
	.word	51058
	.byte	4,2,35,52,6
	.byte	'FDR',0
	.word	53567
	.byte	4,2,35,56,6
	.byte	'EXTCON',0
	.word	53503
	.byte	4,2,35,60,6
	.byte	'CCUCON2',0
	.word	51123
	.byte	4,2,35,64,6
	.byte	'CCUCON3',0
	.word	51188
	.byte	4,2,35,68,6
	.byte	'CCUCON4',0
	.word	51253
	.byte	4,2,35,72,6
	.byte	'CCUCON5',0
	.word	51318
	.byte	4,2,35,76,6
	.byte	'RSTSTAT',0
	.word	55821
	.byte	4,2,35,80,6
	.byte	'reserved_54',0
	.word	5533
	.byte	4,2,35,84,6
	.byte	'RSTCON',0
	.word	55692
	.byte	4,2,35,88,6
	.byte	'ARSTDIS',0
	.word	50928
	.byte	4,2,35,92,6
	.byte	'SWRSTCON',0
	.word	56015
	.byte	4,2,35,96,6
	.byte	'RSTCON2',0
	.word	55756
	.byte	4,2,35,100,6
	.byte	'reserved_68',0
	.word	5533
	.byte	4,2,35,104,6
	.byte	'EVRRSTCON',0
	.word	52554
	.byte	4,2,35,108,6
	.byte	'ESRCFG',0
	.word	56807
	.byte	8,2,35,112,6
	.byte	'ESROCFG',0
	.word	52020
	.byte	4,2,35,120,6
	.byte	'SYSCON',0
	.word	56081
	.byte	4,2,35,124,6
	.byte	'CCUCON6',0
	.word	51383
	.byte	4,3,35,128,1,6
	.byte	'CCUCON7',0
	.word	51448
	.byte	4,3,35,132,1,6
	.byte	'reserved_88',0
	.word	56816
	.byte	20,3,35,136,1,6
	.byte	'PDR',0
	.word	54709
	.byte	4,3,35,156,1,6
	.byte	'IOCR',0
	.word	53871
	.byte	4,3,35,160,1,6
	.byte	'OUT',0
	.word	54454
	.byte	4,3,35,164,1,6
	.byte	'OMR',0
	.word	54329
	.byte	4,3,35,168,1,6
	.byte	'IN',0
	.word	53811
	.byte	4,3,35,172,1,6
	.byte	'EVRSTAT',0
	.word	53307
	.byte	4,3,35,176,1,6
	.byte	'EVRDVSTAT',0
	.word	52285
	.byte	4,3,35,180,1,6
	.byte	'EVR13CON',0
	.word	52085
	.byte	4,3,35,184,1,6
	.byte	'EVR33CON',0
	.word	52151
	.byte	4,3,35,188,1,6
	.byte	'STSTAT',0
	.word	55951
	.byte	4,3,35,192,1,6
	.byte	'reserved_C4',0
	.word	5533
	.byte	4,3,35,196,1,6
	.byte	'PMSWCR0',0
	.word	55362
	.byte	4,3,35,200,1,6
	.byte	'PMSWSTAT',0
	.word	55557
	.byte	4,3,35,204,1,6
	.byte	'PMSWSTATCLR',0
	.word	55623
	.byte	4,3,35,208,1,6
	.byte	'PMCSR',0
	.word	56825
	.byte	8,3,35,212,1,6
	.byte	'reserved_DC',0
	.word	5533
	.byte	4,3,35,220,1,6
	.byte	'DTSSTAT',0
	.word	51705
	.byte	4,3,35,224,1,6
	.byte	'DTSCON',0
	.word	51577
	.byte	4,3,35,228,1,6
	.byte	'PMSWCR1',0
	.word	55427
	.byte	4,3,35,232,1,6
	.byte	'PMSWCR2',0
	.word	55492
	.byte	4,3,35,236,1,6
	.byte	'WDTS',0
	.word	56834
	.byte	12,3,35,240,1,6
	.byte	'EMSR',0
	.word	51894
	.byte	4,3,35,252,1,6
	.byte	'WDTCPU',0
	.word	56848
	.byte	24,3,35,128,2,6
	.byte	'reserved_118',0
	.word	7692
	.byte	12,3,35,152,2,6
	.byte	'TRAPSTAT',0
	.word	56340
	.byte	4,3,35,164,2,6
	.byte	'TRAPSET',0
	.word	56275
	.byte	4,3,35,168,2,6
	.byte	'TRAPCLR',0
	.word	56145
	.byte	4,3,35,172,2,6
	.byte	'TRAPDIS',0
	.word	56210
	.byte	4,3,35,176,2,6
	.byte	'reserved_134',0
	.word	5533
	.byte	4,3,35,180,2,6
	.byte	'LCLCON1',0
	.word	54137
	.byte	4,3,35,184,2,6
	.byte	'LCLTEST',0
	.word	54201
	.byte	4,3,35,188,2,6
	.byte	'CHIPID',0
	.word	51513
	.byte	4,3,35,192,2,6
	.byte	'MANID',0
	.word	54266
	.byte	4,3,35,196,2,6
	.byte	'reserved_148',0
	.word	7352
	.byte	8,3,35,200,2,6
	.byte	'SAFECON',0
	.word	55886
	.byte	4,3,35,208,2,6
	.byte	'reserved_154',0
	.word	27395
	.byte	16,3,35,212,2,6
	.byte	'LBISTCTRL0',0
	.word	53933
	.byte	4,3,35,228,2,6
	.byte	'LBISTCTRL1',0
	.word	54001
	.byte	4,3,35,232,2,6
	.byte	'LBISTCTRL2',0
	.word	54069
	.byte	4,3,35,236,2,6
	.byte	'reserved_170',0
	.word	56853
	.byte	28,3,35,240,2,6
	.byte	'PDISC',0
	.word	54646
	.byte	4,3,35,140,3,6
	.byte	'reserved_190',0
	.word	7352
	.byte	8,3,35,144,3,6
	.byte	'EVRTRIM',0
	.word	53372
	.byte	4,3,35,152,3,6
	.byte	'EVRADCSTAT',0
	.word	52217
	.byte	4,3,35,156,3,6
	.byte	'EVRUVMON',0
	.word	53437
	.byte	4,3,35,160,3,6
	.byte	'EVROVMON',0
	.word	52488
	.byte	4,3,35,164,3,6
	.byte	'EVRMONCTRL',0
	.word	52352
	.byte	4,3,35,168,3,6
	.byte	'reserved_1AC',0
	.word	5533
	.byte	4,3,35,172,3,6
	.byte	'EVRSDCTRL1',0
	.word	53035
	.byte	4,3,35,176,3,6
	.byte	'EVRSDCTRL2',0
	.word	53103
	.byte	4,3,35,180,3,6
	.byte	'EVRSDCTRL3',0
	.word	53171
	.byte	4,3,35,184,3,6
	.byte	'EVRSDCTRL4',0
	.word	53239
	.byte	4,3,35,188,3,6
	.byte	'EVRSDCOEFF1',0
	.word	52621
	.byte	4,3,35,192,3,6
	.byte	'EVRSDCOEFF2',0
	.word	52690
	.byte	4,3,35,196,3,6
	.byte	'EVRSDCOEFF3',0
	.word	52759
	.byte	4,3,35,200,3,6
	.byte	'EVRSDCOEFF4',0
	.word	52828
	.byte	4,3,35,204,3,6
	.byte	'EVRSDCOEFF5',0
	.word	52897
	.byte	4,3,35,208,3,6
	.byte	'EVRSDCOEFF6',0
	.word	52966
	.byte	4,3,35,212,3,6
	.byte	'EVROSCCTRL',0
	.word	52420
	.byte	4,3,35,216,3,6
	.byte	'reserved_1DC',0
	.word	5533
	.byte	4,3,35,220,3,6
	.byte	'OVCENABLE',0
	.word	54579
	.byte	4,3,35,224,3,6
	.byte	'OVCCON',0
	.word	54515
	.byte	4,3,35,228,3,6
	.byte	'reserved_1E8',0
	.word	27030
	.byte	40,3,35,232,3,6
	.byte	'EICR',0
	.word	56862
	.byte	16,3,35,144,4,6
	.byte	'EIFR',0
	.word	51832
	.byte	4,3,35,160,4,6
	.byte	'FMR',0
	.word	53628
	.byte	4,3,35,164,4,6
	.byte	'PDRR',0
	.word	54770
	.byte	4,3,35,168,4,6
	.byte	'IGCR',0
	.word	56871
	.byte	16,3,35,172,4,6
	.byte	'reserved_23C',0
	.word	5533
	.byte	4,3,35,188,4,6
	.byte	'DTSLIM',0
	.word	51641
	.byte	4,3,35,192,4,6
	.byte	'reserved_244',0
	.word	56880
	.byte	180,3,3,35,196,4,6
	.byte	'ACCEN1',0
	.word	50864
	.byte	4,3,35,248,7,6
	.byte	'ACCEN0',0
	.word	50800
	.byte	4,3,35,252,7,0,21
	.word	56891
	.byte	9
	.byte	'Ifx_SCU',0,9,181,16,3
	.word	58881
	.byte	15
	.byte	'_Ifx_CPU_A_Bits',0,26,45,16,4,4
	.byte	'ADDR',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_A_Bits',0,26,48,3
	.word	58903
	.byte	15
	.byte	'_Ifx_CPU_BIV_Bits',0,26,51,16,4,4
	.byte	'VSS',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'BIV',0,4
	.word	3237
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_BIV_Bits',0,26,55,3
	.word	58964
	.byte	15
	.byte	'_Ifx_CPU_BTV_Bits',0,26,58,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'BTV',0,4
	.word	3237
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_BTV_Bits',0,26,62,3
	.word	59043
	.byte	15
	.byte	'_Ifx_CPU_CCNT_Bits',0,26,65,16,4,4
	.byte	'CountValue',0,4
	.word	3237
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_CCNT_Bits',0,26,69,3
	.word	59129
	.byte	15
	.byte	'_Ifx_CPU_CCTRL_Bits',0,26,72,16,4,4
	.byte	'CM',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'CE',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'M1',0,4
	.word	3237
	.byte	3,27,2,35,0,4
	.byte	'M2',0,4
	.word	3237
	.byte	3,24,2,35,0,4
	.byte	'M3',0,4
	.word	3237
	.byte	3,21,2,35,0,4
	.byte	'reserved_11',0,4
	.word	3237
	.byte	21,0,2,35,0,0,9
	.byte	'Ifx_CPU_CCTRL_Bits',0,26,80,3
	.word	59218
	.byte	15
	.byte	'_Ifx_CPU_COMPAT_Bits',0,26,83,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'RM',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'SP',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3237
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_COMPAT_Bits',0,26,89,3
	.word	59364
	.byte	15
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,26,92,16,4,4
	.byte	'CORE_ID',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3237
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CORE_ID_Bits',0,26,96,3
	.word	59491
	.byte	15
	.byte	'_Ifx_CPU_CPR_L_Bits',0,26,99,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'LOWBND',0,4
	.word	3237
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_L_Bits',0,26,103,3
	.word	59589
	.byte	15
	.byte	'_Ifx_CPU_CPR_U_Bits',0,26,106,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'UPPBND',0,4
	.word	3237
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_U_Bits',0,26,110,3
	.word	59682
	.byte	15
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,26,113,16,4,4
	.byte	'MODREV',0,4
	.word	3237
	.byte	8,24,2,35,0,4
	.byte	'MOD_32B',0,4
	.word	3237
	.byte	8,16,2,35,0,4
	.byte	'MOD',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPU_ID_Bits',0,26,118,3
	.word	59775
	.byte	15
	.byte	'_Ifx_CPU_CPXE_Bits',0,26,121,16,4,4
	.byte	'XE',0,4
	.word	3237
	.byte	8,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3237
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPXE_Bits',0,26,125,3
	.word	59882
	.byte	15
	.byte	'_Ifx_CPU_CREVT_Bits',0,26,128,1,16,4,4
	.byte	'EVTA',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3237
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3237
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3237
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_CREVT_Bits',0,26,136,1,3
	.word	59969
	.byte	15
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,26,139,1,16,4,4
	.byte	'CID',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3237
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CUS_ID_Bits',0,26,143,1,3
	.word	60123
	.byte	15
	.byte	'_Ifx_CPU_D_Bits',0,26,146,1,16,4,4
	.byte	'DATA',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_D_Bits',0,26,149,1,3
	.word	60217
	.byte	15
	.byte	'_Ifx_CPU_DATR_Bits',0,26,152,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'SBE',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'reserved_4',0,4
	.word	3237
	.byte	5,23,2,35,0,4
	.byte	'CWE',0,4
	.word	3237
	.byte	1,22,2,35,0,4
	.byte	'CFE',0,4
	.word	3237
	.byte	1,21,2,35,0,4
	.byte	'reserved_11',0,4
	.word	3237
	.byte	3,18,2,35,0,4
	.byte	'SOE',0,4
	.word	3237
	.byte	1,17,2,35,0,4
	.byte	'SME',0,4
	.word	3237
	.byte	1,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DATR_Bits',0,26,163,1,3
	.word	60280
	.byte	15
	.byte	'_Ifx_CPU_DBGSR_Bits',0,26,166,1,16,4,4
	.byte	'DE',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'HALT',0,4
	.word	3237
	.byte	2,29,2,35,0,4
	.byte	'SIH',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'SUSP',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3237
	.byte	1,26,2,35,0,4
	.byte	'PREVSUSP',0,4
	.word	3237
	.byte	1,25,2,35,0,4
	.byte	'PEVT',0,4
	.word	3237
	.byte	1,24,2,35,0,4
	.byte	'EVTSRC',0,4
	.word	3237
	.byte	5,19,2,35,0,4
	.byte	'reserved_13',0,4
	.word	3237
	.byte	19,0,2,35,0,0,9
	.byte	'Ifx_CPU_DBGSR_Bits',0,26,177,1,3
	.word	60498
	.byte	15
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,26,180,1,16,4,4
	.byte	'DTA',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'reserved_1',0,4
	.word	3237
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_DBGTCR_Bits',0,26,184,1,3
	.word	60713
	.byte	15
	.byte	'_Ifx_CPU_DCON0_Bits',0,26,187,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'DCBYP',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3237
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_CPU_DCON0_Bits',0,26,192,1,3
	.word	60807
	.byte	15
	.byte	'_Ifx_CPU_DCON2_Bits',0,26,195,1,16,4,4
	.byte	'DCACHE_SZE',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'DSCRATCH_SZE',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DCON2_Bits',0,26,199,1,3
	.word	60923
	.byte	15
	.byte	'_Ifx_CPU_DCX_Bits',0,26,202,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	6,26,2,35,0,4
	.byte	'DCXValue',0,4
	.word	3237
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_CPU_DCX_Bits',0,26,206,1,3
	.word	61024
	.byte	15
	.byte	'_Ifx_CPU_DEADD_Bits',0,26,209,1,16,4,4
	.byte	'ERROR_ADDRESS',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_DEADD_Bits',0,26,212,1,3
	.word	61117
	.byte	15
	.byte	'_Ifx_CPU_DIEAR_Bits',0,26,215,1,16,4,4
	.byte	'TA',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_DIEAR_Bits',0,26,218,1,3
	.word	61197
	.byte	15
	.byte	'_Ifx_CPU_DIETR_Bits',0,26,221,1,16,4,4
	.byte	'IED',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'IE_T',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'IE_C',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'IE_S',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'IE_BI',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'E_INFO',0,4
	.word	3237
	.byte	6,21,2,35,0,4
	.byte	'IE_DUAL',0,4
	.word	3237
	.byte	1,20,2,35,0,4
	.byte	'IE_SP',0,4
	.word	3237
	.byte	1,19,2,35,0,4
	.byte	'IE_BS',0,4
	.word	3237
	.byte	1,18,2,35,0,4
	.byte	'reserved_14',0,4
	.word	3237
	.byte	18,0,2,35,0,0,9
	.byte	'Ifx_CPU_DIETR_Bits',0,26,233,1,3
	.word	61266
	.byte	15
	.byte	'_Ifx_CPU_DMS_Bits',0,26,236,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'DMSValue',0,4
	.word	3237
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_DMS_Bits',0,26,240,1,3
	.word	61495
	.byte	15
	.byte	'_Ifx_CPU_DPR_L_Bits',0,26,243,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'LOWBND',0,4
	.word	3237
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_L_Bits',0,26,247,1,3
	.word	61588
	.byte	15
	.byte	'_Ifx_CPU_DPR_U_Bits',0,26,250,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'UPPBND',0,4
	.word	3237
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_U_Bits',0,26,254,1,3
	.word	61683
	.byte	15
	.byte	'_Ifx_CPU_DPRE_Bits',0,26,129,2,16,4,4
	.byte	'RE',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPRE_Bits',0,26,133,2,3
	.word	61778
	.byte	15
	.byte	'_Ifx_CPU_DPWE_Bits',0,26,136,2,16,4,4
	.byte	'WE',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPWE_Bits',0,26,140,2,3
	.word	61868
	.byte	15
	.byte	'_Ifx_CPU_DSTR_Bits',0,26,143,2,16,4,4
	.byte	'SRE',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'GAE',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'LBE',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3237
	.byte	3,26,2,35,0,4
	.byte	'CRE',0,4
	.word	3237
	.byte	1,25,2,35,0,4
	.byte	'reserved_7',0,4
	.word	3237
	.byte	7,18,2,35,0,4
	.byte	'DTME',0,4
	.word	3237
	.byte	1,17,2,35,0,4
	.byte	'LOE',0,4
	.word	3237
	.byte	1,16,2,35,0,4
	.byte	'SDE',0,4
	.word	3237
	.byte	1,15,2,35,0,4
	.byte	'SCE',0,4
	.word	3237
	.byte	1,14,2,35,0,4
	.byte	'CAC',0,4
	.word	3237
	.byte	1,13,2,35,0,4
	.byte	'MPE',0,4
	.word	3237
	.byte	1,12,2,35,0,4
	.byte	'CLE',0,4
	.word	3237
	.byte	1,11,2,35,0,4
	.byte	'reserved_21',0,4
	.word	3237
	.byte	3,8,2,35,0,4
	.byte	'ALN',0,4
	.word	3237
	.byte	1,7,2,35,0,4
	.byte	'reserved_25',0,4
	.word	3237
	.byte	7,0,2,35,0,0,9
	.byte	'Ifx_CPU_DSTR_Bits',0,26,161,2,3
	.word	61958
	.byte	15
	.byte	'_Ifx_CPU_EXEVT_Bits',0,26,164,2,16,4,4
	.byte	'EVTA',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3237
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3237
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3237
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_EXEVT_Bits',0,26,172,2,3
	.word	62282
	.byte	15
	.byte	'_Ifx_CPU_FCX_Bits',0,26,175,2,16,4,4
	.byte	'FCXO',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'FCXS',0,4
	.word	3237
	.byte	4,12,2,35,0,4
	.byte	'reserved_20',0,4
	.word	3237
	.byte	12,0,2,35,0,0,9
	.byte	'Ifx_CPU_FCX_Bits',0,26,180,2,3
	.word	62436
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,26,183,2,16,4,4
	.byte	'TST',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'TCL',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3237
	.byte	6,24,2,35,0,4
	.byte	'RM',0,4
	.word	3237
	.byte	2,22,2,35,0,4
	.byte	'reserved_10',0,4
	.word	3237
	.byte	8,14,2,35,0,4
	.byte	'FXE',0,4
	.word	3237
	.byte	1,13,2,35,0,4
	.byte	'FUE',0,4
	.word	3237
	.byte	1,12,2,35,0,4
	.byte	'FZE',0,4
	.word	3237
	.byte	1,11,2,35,0,4
	.byte	'FVE',0,4
	.word	3237
	.byte	1,10,2,35,0,4
	.byte	'FIE',0,4
	.word	3237
	.byte	1,9,2,35,0,4
	.byte	'reserved_23',0,4
	.word	3237
	.byte	3,6,2,35,0,4
	.byte	'FX',0,4
	.word	3237
	.byte	1,5,2,35,0,4
	.byte	'FU',0,4
	.word	3237
	.byte	1,4,2,35,0,4
	.byte	'FZ',0,4
	.word	3237
	.byte	1,3,2,35,0,4
	.byte	'FV',0,4
	.word	3237
	.byte	1,2,2,35,0,4
	.byte	'FI',0,4
	.word	3237
	.byte	1,1,2,35,0,4
	.byte	'reserved_31',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,26,202,2,3
	.word	62542
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,26,205,2,16,4,4
	.byte	'OPC',0,4
	.word	3237
	.byte	8,24,2,35,0,4
	.byte	'FMT',0,4
	.word	3237
	.byte	1,23,2,35,0,4
	.byte	'reserved_9',0,4
	.word	3237
	.byte	7,16,2,35,0,4
	.byte	'DREG',0,4
	.word	3237
	.byte	4,12,2,35,0,4
	.byte	'reserved_20',0,4
	.word	3237
	.byte	12,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,26,212,2,3
	.word	62891
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,26,215,2,16,4,4
	.byte	'PC',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,26,218,2,3
	.word	63051
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,26,221,2,16,4,4
	.byte	'SRC1',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,26,224,2,3
	.word	63132
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,26,227,2,16,4,4
	.byte	'SRC2',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,26,230,2,3
	.word	63219
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,26,233,2,16,4,4
	.byte	'SRC3',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,26,236,2,3
	.word	63306
	.byte	15
	.byte	'_Ifx_CPU_ICNT_Bits',0,26,239,2,16,4,4
	.byte	'CountValue',0,4
	.word	3237
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_ICNT_Bits',0,26,243,2,3
	.word	63393
	.byte	15
	.byte	'_Ifx_CPU_ICR_Bits',0,26,246,2,16,4,4
	.byte	'CCPN',0,4
	.word	3237
	.byte	10,22,2,35,0,4
	.byte	'reserved_10',0,4
	.word	3237
	.byte	5,17,2,35,0,4
	.byte	'IE',0,4
	.word	3237
	.byte	1,16,2,35,0,4
	.byte	'PIPN',0,4
	.word	3237
	.byte	10,6,2,35,0,4
	.byte	'reserved_26',0,4
	.word	3237
	.byte	6,0,2,35,0,0,9
	.byte	'Ifx_CPU_ICR_Bits',0,26,253,2,3
	.word	63484
	.byte	15
	.byte	'_Ifx_CPU_ISP_Bits',0,26,128,3,16,4,4
	.byte	'ISP',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_ISP_Bits',0,26,131,3,3
	.word	63627
	.byte	15
	.byte	'_Ifx_CPU_LCX_Bits',0,26,134,3,16,4,4
	.byte	'LCXO',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'LCXS',0,4
	.word	3237
	.byte	4,12,2,35,0,4
	.byte	'reserved_20',0,4
	.word	3237
	.byte	12,0,2,35,0,0,9
	.byte	'Ifx_CPU_LCX_Bits',0,26,139,3,3
	.word	63693
	.byte	15
	.byte	'_Ifx_CPU_M1CNT_Bits',0,26,142,3,16,4,4
	.byte	'CountValue',0,4
	.word	3237
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_M1CNT_Bits',0,26,146,3,3
	.word	63799
	.byte	15
	.byte	'_Ifx_CPU_M2CNT_Bits',0,26,149,3,16,4,4
	.byte	'CountValue',0,4
	.word	3237
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_M2CNT_Bits',0,26,153,3,3
	.word	63892
	.byte	15
	.byte	'_Ifx_CPU_M3CNT_Bits',0,26,156,3,16,4,4
	.byte	'CountValue',0,4
	.word	3237
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_M3CNT_Bits',0,26,160,3,3
	.word	63985
	.byte	15
	.byte	'_Ifx_CPU_PC_Bits',0,26,163,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'PC',0,4
	.word	3237
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_PC_Bits',0,26,167,3,3
	.word	64078
	.byte	15
	.byte	'_Ifx_CPU_PCON0_Bits',0,26,170,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'PCBYP',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3237
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCON0_Bits',0,26,175,3,3
	.word	64163
	.byte	15
	.byte	'_Ifx_CPU_PCON1_Bits',0,26,178,3,16,4,4
	.byte	'PCINV',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'PBINV',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3237
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCON1_Bits',0,26,183,3,3
	.word	64279
	.byte	15
	.byte	'_Ifx_CPU_PCON2_Bits',0,26,186,3,16,4,4
	.byte	'PCACHE_SZE',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'PSCRATCH_SZE',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCON2_Bits',0,26,190,3,3
	.word	64390
	.byte	15
	.byte	'_Ifx_CPU_PCXI_Bits',0,26,193,3,16,4,4
	.byte	'PCXO',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'PCXS',0,4
	.word	3237
	.byte	4,12,2,35,0,4
	.byte	'UL',0,4
	.word	3237
	.byte	1,11,2,35,0,4
	.byte	'PIE',0,4
	.word	3237
	.byte	1,10,2,35,0,4
	.byte	'PCPN',0,4
	.word	3237
	.byte	10,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCXI_Bits',0,26,200,3,3
	.word	64491
	.byte	15
	.byte	'_Ifx_CPU_PIEAR_Bits',0,26,203,3,16,4,4
	.byte	'TA',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_PIEAR_Bits',0,26,206,3,3
	.word	64621
	.byte	15
	.byte	'_Ifx_CPU_PIETR_Bits',0,26,209,3,16,4,4
	.byte	'IED',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'IE_T',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'IE_C',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'IE_S',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'IE_BI',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'E_INFO',0,4
	.word	3237
	.byte	6,21,2,35,0,4
	.byte	'IE_DUAL',0,4
	.word	3237
	.byte	1,20,2,35,0,4
	.byte	'IE_SP',0,4
	.word	3237
	.byte	1,19,2,35,0,4
	.byte	'IE_BS',0,4
	.word	3237
	.byte	1,18,2,35,0,4
	.byte	'reserved_14',0,4
	.word	3237
	.byte	18,0,2,35,0,0,9
	.byte	'Ifx_CPU_PIETR_Bits',0,26,221,3,3
	.word	64690
	.byte	15
	.byte	'_Ifx_CPU_PMA0_Bits',0,26,224,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	13,19,2,35,0,4
	.byte	'DAC',0,4
	.word	3237
	.byte	3,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PMA0_Bits',0,26,229,3,3
	.word	64919
	.byte	15
	.byte	'_Ifx_CPU_PMA1_Bits',0,26,232,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3237
	.byte	14,18,2,35,0,4
	.byte	'CAC',0,4
	.word	3237
	.byte	2,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PMA1_Bits',0,26,237,3,3
	.word	65032
	.byte	15
	.byte	'_Ifx_CPU_PMA2_Bits',0,26,240,3,16,4,4
	.byte	'PSI',0,4
	.word	3237
	.byte	16,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3237
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PMA2_Bits',0,26,244,3,3
	.word	65145
	.byte	15
	.byte	'_Ifx_CPU_PSTR_Bits',0,26,247,3,16,4,4
	.byte	'FRE',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'reserved_1',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'FBE',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3237
	.byte	9,20,2,35,0,4
	.byte	'FPE',0,4
	.word	3237
	.byte	1,19,2,35,0,4
	.byte	'reserved_13',0,4
	.word	3237
	.byte	1,18,2,35,0,4
	.byte	'FME',0,4
	.word	3237
	.byte	1,17,2,35,0,4
	.byte	'reserved_15',0,4
	.word	3237
	.byte	17,0,2,35,0,0,9
	.byte	'Ifx_CPU_PSTR_Bits',0,26,129,4,3
	.word	65236
	.byte	15
	.byte	'_Ifx_CPU_PSW_Bits',0,26,132,4,16,4,4
	.byte	'CDC',0,4
	.word	3237
	.byte	7,25,2,35,0,4
	.byte	'CDE',0,4
	.word	3237
	.byte	1,24,2,35,0,4
	.byte	'GW',0,4
	.word	3237
	.byte	1,23,2,35,0,4
	.byte	'IS',0,4
	.word	3237
	.byte	1,22,2,35,0,4
	.byte	'IO',0,4
	.word	3237
	.byte	2,20,2,35,0,4
	.byte	'PRS',0,4
	.word	3237
	.byte	2,18,2,35,0,4
	.byte	'S',0,4
	.word	3237
	.byte	1,17,2,35,0,4
	.byte	'reserved_15',0,4
	.word	3237
	.byte	12,5,2,35,0,4
	.byte	'SAV',0,4
	.word	3237
	.byte	1,4,2,35,0,4
	.byte	'AV',0,4
	.word	3237
	.byte	1,3,2,35,0,4
	.byte	'SV',0,4
	.word	3237
	.byte	1,2,2,35,0,4
	.byte	'V',0,4
	.word	3237
	.byte	1,1,2,35,0,4
	.byte	'C',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_PSW_Bits',0,26,147,4,3
	.word	65439
	.byte	15
	.byte	'_Ifx_CPU_SEGEN_Bits',0,26,150,4,16,4,4
	.byte	'ADFLIP',0,4
	.word	3237
	.byte	8,24,2,35,0,4
	.byte	'ADTYPE',0,4
	.word	3237
	.byte	2,22,2,35,0,4
	.byte	'reserved_10',0,4
	.word	3237
	.byte	21,1,2,35,0,4
	.byte	'AE',0,4
	.word	3237
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_SEGEN_Bits',0,26,156,4,3
	.word	65682
	.byte	15
	.byte	'_Ifx_CPU_SMACON_Bits',0,26,159,4,16,4,4
	.byte	'PC',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'reserved_1',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'PT',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3237
	.byte	5,24,2,35,0,4
	.byte	'DC',0,4
	.word	3237
	.byte	1,23,2,35,0,4
	.byte	'reserved_9',0,4
	.word	3237
	.byte	1,22,2,35,0,4
	.byte	'DT',0,4
	.word	3237
	.byte	1,21,2,35,0,4
	.byte	'reserved_11',0,4
	.word	3237
	.byte	13,8,2,35,0,4
	.byte	'IODT',0,4
	.word	3237
	.byte	1,7,2,35,0,4
	.byte	'reserved_25',0,4
	.word	3237
	.byte	7,0,2,35,0,0,9
	.byte	'Ifx_CPU_SMACON_Bits',0,26,171,4,3
	.word	65810
	.byte	15
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,26,174,4,16,4,4
	.byte	'EN',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,26,177,4,3
	.word	66051
	.byte	15
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,26,180,4,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,26,183,4,3
	.word	66134
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,26,186,4,16,4,4
	.byte	'EN',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,26,189,4,3
	.word	66225
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,26,192,4,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,26,195,4,3
	.word	66316
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,26,198,4,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2659
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,26,202,4,3
	.word	66415
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,26,205,4,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2659
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,26,209,4,3
	.word	66522
	.byte	15
	.byte	'_Ifx_CPU_SWEVT_Bits',0,26,212,4,16,4,4
	.byte	'EVTA',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3237
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3237
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3237
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_SWEVT_Bits',0,26,220,4,3
	.word	66629
	.byte	15
	.byte	'_Ifx_CPU_SYSCON_Bits',0,26,223,4,16,4,4
	.byte	'FCDSF',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'PROTEN',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'TPROTEN',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'IS',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'IT',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3237
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_SYSCON_Bits',0,26,231,4,3
	.word	66783
	.byte	15
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,26,234,4,16,4,4
	.byte	'ASI',0,4
	.word	3237
	.byte	5,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3237
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,26,238,4,3
	.word	66944
	.byte	15
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,26,241,4,16,4,4
	.byte	'TEXP0',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'TEXP1',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'TEXP2',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3237
	.byte	13,16,2,35,0,4
	.byte	'TTRAP',0,4
	.word	3237
	.byte	1,15,2,35,0,4
	.byte	'reserved_17',0,4
	.word	3237
	.byte	15,0,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_CON_Bits',0,26,249,4,3
	.word	67042
	.byte	15
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,26,252,4,16,4,4
	.byte	'Timer',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,26,255,4,3
	.word	67214
	.byte	15
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,26,130,5,16,4,4
	.byte	'ADDR',0,4
	.word	3237
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_TR_ADR_Bits',0,26,133,5,3
	.word	67294
	.byte	15
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,26,136,5,16,4,4
	.byte	'EVTA',0,4
	.word	3237
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3237
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3237
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3237
	.byte	4,20,2,35,0,4
	.byte	'TYP',0,4
	.word	3237
	.byte	1,19,2,35,0,4
	.byte	'RNG',0,4
	.word	3237
	.byte	1,18,2,35,0,4
	.byte	'reserved_14',0,4
	.word	3237
	.byte	1,17,2,35,0,4
	.byte	'ASI_EN',0,4
	.word	3237
	.byte	1,16,2,35,0,4
	.byte	'ASI',0,4
	.word	3237
	.byte	5,11,2,35,0,4
	.byte	'reserved_21',0,4
	.word	3237
	.byte	6,5,2,35,0,4
	.byte	'AST',0,4
	.word	3237
	.byte	1,4,2,35,0,4
	.byte	'ALD',0,4
	.word	3237
	.byte	1,3,2,35,0,4
	.byte	'reserved_29',0,4
	.word	3237
	.byte	3,0,2,35,0,0,9
	.byte	'Ifx_CPU_TR_EVT_Bits',0,26,153,5,3
	.word	67367
	.byte	15
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,26,156,5,16,4,4
	.byte	'T0',0,4
	.word	3237
	.byte	1,31,2,35,0,4
	.byte	'T1',0,4
	.word	3237
	.byte	1,30,2,35,0,4
	.byte	'T2',0,4
	.word	3237
	.byte	1,29,2,35,0,4
	.byte	'T3',0,4
	.word	3237
	.byte	1,28,2,35,0,4
	.byte	'T4',0,4
	.word	3237
	.byte	1,27,2,35,0,4
	.byte	'T5',0,4
	.word	3237
	.byte	1,26,2,35,0,4
	.byte	'T6',0,4
	.word	3237
	.byte	1,25,2,35,0,4
	.byte	'T7',0,4
	.word	3237
	.byte	1,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3237
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,26,167,5,3
	.word	67685
	.byte	5,26,175,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58903
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_A',0,26,180,5,3
	.word	67880
	.byte	5,26,183,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58964
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_BIV',0,26,188,5,3
	.word	67939
	.byte	5,26,191,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59043
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_BTV',0,26,196,5,3
	.word	68000
	.byte	5,26,199,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59129
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CCNT',0,26,204,5,3
	.word	68061
	.byte	5,26,207,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59218
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CCTRL',0,26,212,5,3
	.word	68123
	.byte	5,26,215,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59364
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_COMPAT',0,26,220,5,3
	.word	68186
	.byte	5,26,223,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59491
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CORE_ID',0,26,228,5,3
	.word	68250
	.byte	5,26,231,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59589
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_L',0,26,236,5,3
	.word	68315
	.byte	5,26,239,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59682
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_U',0,26,244,5,3
	.word	68378
	.byte	5,26,247,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59775
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPU_ID',0,26,252,5,3
	.word	68441
	.byte	5,26,255,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59882
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPXE',0,26,132,6,3
	.word	68505
	.byte	5,26,135,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59969
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CREVT',0,26,140,6,3
	.word	68567
	.byte	5,26,143,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60123
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CUS_ID',0,26,148,6,3
	.word	68630
	.byte	5,26,151,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60217
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_D',0,26,156,6,3
	.word	68694
	.byte	5,26,159,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60280
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DATR',0,26,164,6,3
	.word	68753
	.byte	5,26,167,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60498
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DBGSR',0,26,172,6,3
	.word	68815
	.byte	5,26,175,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60713
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DBGTCR',0,26,180,6,3
	.word	68878
	.byte	5,26,183,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60807
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DCON0',0,26,188,6,3
	.word	68942
	.byte	5,26,191,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60923
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DCON2',0,26,196,6,3
	.word	69005
	.byte	5,26,199,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61024
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DCX',0,26,204,6,3
	.word	69068
	.byte	5,26,207,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61117
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DEADD',0,26,212,6,3
	.word	69129
	.byte	5,26,215,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61197
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DIEAR',0,26,220,6,3
	.word	69192
	.byte	5,26,223,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61266
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DIETR',0,26,228,6,3
	.word	69255
	.byte	5,26,231,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61495
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DMS',0,26,236,6,3
	.word	69318
	.byte	5,26,239,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61588
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_L',0,26,244,6,3
	.word	69379
	.byte	5,26,247,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61683
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_U',0,26,252,6,3
	.word	69442
	.byte	5,26,255,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61778
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPRE',0,26,132,7,3
	.word	69505
	.byte	5,26,135,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61868
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPWE',0,26,140,7,3
	.word	69567
	.byte	5,26,143,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61958
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DSTR',0,26,148,7,3
	.word	69629
	.byte	5,26,151,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62282
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_EXEVT',0,26,156,7,3
	.word	69691
	.byte	5,26,159,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62436
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FCX',0,26,164,7,3
	.word	69754
	.byte	5,26,167,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62542
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,26,172,7,3
	.word	69815
	.byte	5,26,175,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62891
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,26,180,7,3
	.word	69885
	.byte	5,26,183,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63051
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,26,188,7,3
	.word	69955
	.byte	5,26,191,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63132
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,26,196,7,3
	.word	70024
	.byte	5,26,199,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63219
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,26,204,7,3
	.word	70095
	.byte	5,26,207,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63306
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,26,212,7,3
	.word	70166
	.byte	5,26,215,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63393
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_ICNT',0,26,220,7,3
	.word	70237
	.byte	5,26,223,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63484
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_ICR',0,26,228,7,3
	.word	70299
	.byte	5,26,231,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63627
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_ISP',0,26,236,7,3
	.word	70360
	.byte	5,26,239,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63693
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_LCX',0,26,244,7,3
	.word	70421
	.byte	5,26,247,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63799
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_M1CNT',0,26,252,7,3
	.word	70482
	.byte	5,26,255,7,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63892
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_M2CNT',0,26,132,8,3
	.word	70545
	.byte	5,26,135,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63985
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_M3CNT',0,26,140,8,3
	.word	70608
	.byte	5,26,143,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64078
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PC',0,26,148,8,3
	.word	70671
	.byte	5,26,151,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64163
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCON0',0,26,156,8,3
	.word	70731
	.byte	5,26,159,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64279
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCON1',0,26,164,8,3
	.word	70794
	.byte	5,26,167,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64390
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCON2',0,26,172,8,3
	.word	70857
	.byte	5,26,175,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64491
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCXI',0,26,180,8,3
	.word	70920
	.byte	5,26,183,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64621
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PIEAR',0,26,188,8,3
	.word	70982
	.byte	5,26,191,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64690
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PIETR',0,26,196,8,3
	.word	71045
	.byte	5,26,199,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64919
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PMA0',0,26,204,8,3
	.word	71108
	.byte	5,26,207,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65032
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PMA1',0,26,212,8,3
	.word	71170
	.byte	5,26,215,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65145
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PMA2',0,26,220,8,3
	.word	71232
	.byte	5,26,223,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65236
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PSTR',0,26,228,8,3
	.word	71294
	.byte	5,26,231,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65439
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PSW',0,26,236,8,3
	.word	71356
	.byte	5,26,239,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65682
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SEGEN',0,26,244,8,3
	.word	71417
	.byte	5,26,247,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65810
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SMACON',0,26,252,8,3
	.word	71480
	.byte	5,26,255,8,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66051
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENA',0,26,132,9,3
	.word	71544
	.byte	5,26,135,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66134
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENB',0,26,140,9,3
	.word	71614
	.byte	5,26,143,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66225
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,26,148,9,3
	.word	71684
	.byte	5,26,151,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66316
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,26,156,9,3
	.word	71758
	.byte	5,26,159,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66415
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,26,164,9,3
	.word	71832
	.byte	5,26,167,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66522
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,26,172,9,3
	.word	71902
	.byte	5,26,175,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66629
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SWEVT',0,26,180,9,3
	.word	71972
	.byte	5,26,183,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66783
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SYSCON',0,26,188,9,3
	.word	72035
	.byte	5,26,191,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66944
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TASK_ASI',0,26,196,9,3
	.word	72099
	.byte	5,26,199,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	67042
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_CON',0,26,204,9,3
	.word	72165
	.byte	5,26,207,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	67214
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_TIMER',0,26,212,9,3
	.word	72230
	.byte	5,26,215,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	67294
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TR_ADR',0,26,220,9,3
	.word	72297
	.byte	5,26,223,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	67367
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TR_EVT',0,26,228,9,3
	.word	72361
	.byte	5,26,231,9,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	67685
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TRIG_ACC',0,26,236,9,3
	.word	72425
	.byte	15
	.byte	'_Ifx_CPU_CPR',0,26,247,9,25,8,6
	.byte	'L',0
	.word	68315
	.byte	4,2,35,0,6
	.byte	'U',0
	.word	68378
	.byte	4,2,35,4,0,21
	.word	72491
	.byte	9
	.byte	'Ifx_CPU_CPR',0,26,251,9,3
	.word	72533
	.byte	15
	.byte	'_Ifx_CPU_DPR',0,26,254,9,25,8,6
	.byte	'L',0
	.word	69379
	.byte	4,2,35,0,6
	.byte	'U',0
	.word	69442
	.byte	4,2,35,4,0,21
	.word	72559
	.byte	9
	.byte	'Ifx_CPU_DPR',0,26,130,10,3
	.word	72601
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN',0,26,133,10,25,16,6
	.byte	'LA',0
	.word	71832
	.byte	4,2,35,0,6
	.byte	'UA',0
	.word	71902
	.byte	4,2,35,4,6
	.byte	'ACCENA',0
	.word	71684
	.byte	4,2,35,8,6
	.byte	'ACCENB',0
	.word	71758
	.byte	4,2,35,12,0,21
	.word	72627
	.byte	9
	.byte	'Ifx_CPU_SPROT_RGN',0,26,139,10,3
	.word	72709
	.byte	23,12
	.word	72230
	.byte	24,2,0,15
	.byte	'_Ifx_CPU_TPS',0,26,142,10,25,16,6
	.byte	'CON',0
	.word	72165
	.byte	4,2,35,0,6
	.byte	'TIMER',0
	.word	72741
	.byte	12,2,35,4,0,21
	.word	72750
	.byte	9
	.byte	'Ifx_CPU_TPS',0,26,146,10,3
	.word	72798
	.byte	15
	.byte	'_Ifx_CPU_TR',0,26,149,10,25,8,6
	.byte	'EVT',0
	.word	72361
	.byte	4,2,35,0,6
	.byte	'ADR',0
	.word	72297
	.byte	4,2,35,4,0,21
	.word	72824
	.byte	9
	.byte	'Ifx_CPU_TR',0,26,153,10,3
	.word	72869
	.byte	23,176,32
	.word	267
	.byte	24,175,32,0,23,208,223,1
	.word	267
	.byte	24,207,223,1,0,23,248,1
	.word	267
	.byte	24,247,1,0,23,244,29
	.word	267
	.byte	24,243,29,0,23,188,3
	.word	267
	.byte	24,187,3,0,23,232,3
	.word	267
	.byte	24,231,3,0,23,252,23
	.word	267
	.byte	24,251,23,0,23,228,63
	.word	267
	.byte	24,227,63,0,23,128,1
	.word	72559
	.byte	24,15,0,21
	.word	72984
	.byte	23,128,31
	.word	267
	.byte	24,255,30,0,23,64
	.word	72491
	.byte	24,7,0,21
	.word	73010
	.byte	23,192,31
	.word	267
	.byte	24,191,31,0,23,16
	.word	68505
	.byte	24,3,0,23,16
	.word	69505
	.byte	24,3,0,23,16
	.word	69567
	.byte	24,3,0,23,208,7
	.word	267
	.byte	24,207,7,0,21
	.word	72750
	.byte	23,240,23
	.word	267
	.byte	24,239,23,0,23,64
	.word	72824
	.byte	24,7,0,21
	.word	73089
	.byte	23,192,23
	.word	267
	.byte	24,191,23,0,23,232,1
	.word	267
	.byte	24,231,1,0,23,180,1
	.word	267
	.byte	24,179,1,0,23,172,1
	.word	267
	.byte	24,171,1,0,23,64
	.word	68694
	.byte	24,15,0,23,64
	.word	267
	.byte	24,63,0,23,64
	.word	67880
	.byte	24,15,0,15
	.byte	'_Ifx_CPU',0,26,166,10,25,128,128,4,6
	.byte	'reserved_0',0
	.word	72894
	.byte	176,32,2,35,0,6
	.byte	'SEGEN',0
	.word	71417
	.byte	4,3,35,176,32,6
	.byte	'reserved_1034',0
	.word	72905
	.byte	208,223,1,3,35,180,32,6
	.byte	'TASK_ASI',0
	.word	72099
	.byte	4,4,35,132,128,2,6
	.byte	'reserved_8008',0
	.word	72918
	.byte	248,1,4,35,136,128,2,6
	.byte	'PMA0',0
	.word	71108
	.byte	4,4,35,128,130,2,6
	.byte	'PMA1',0
	.word	71170
	.byte	4,4,35,132,130,2,6
	.byte	'PMA2',0
	.word	71232
	.byte	4,4,35,136,130,2,6
	.byte	'reserved_810C',0
	.word	72929
	.byte	244,29,4,35,140,130,2,6
	.byte	'DCON2',0
	.word	69005
	.byte	4,4,35,128,160,2,6
	.byte	'reserved_9004',0
	.word	7352
	.byte	8,4,35,132,160,2,6
	.byte	'SMACON',0
	.word	71480
	.byte	4,4,35,140,160,2,6
	.byte	'DSTR',0
	.word	69629
	.byte	4,4,35,144,160,2,6
	.byte	'reserved_9014',0
	.word	5533
	.byte	4,4,35,148,160,2,6
	.byte	'DATR',0
	.word	68753
	.byte	4,4,35,152,160,2,6
	.byte	'DEADD',0
	.word	69129
	.byte	4,4,35,156,160,2,6
	.byte	'DIEAR',0
	.word	69192
	.byte	4,4,35,160,160,2,6
	.byte	'DIETR',0
	.word	69255
	.byte	4,4,35,164,160,2,6
	.byte	'reserved_9028',0
	.word	6723
	.byte	24,4,35,168,160,2,6
	.byte	'DCON0',0
	.word	68942
	.byte	4,4,35,192,160,2,6
	.byte	'reserved_9044',0
	.word	72940
	.byte	188,3,4,35,196,160,2,6
	.byte	'PSTR',0
	.word	71294
	.byte	4,4,35,128,164,2,6
	.byte	'PCON1',0
	.word	70794
	.byte	4,4,35,132,164,2,6
	.byte	'PCON2',0
	.word	70857
	.byte	4,4,35,136,164,2,6
	.byte	'PCON0',0
	.word	70731
	.byte	4,4,35,140,164,2,6
	.byte	'PIEAR',0
	.word	70982
	.byte	4,4,35,144,164,2,6
	.byte	'PIETR',0
	.word	71045
	.byte	4,4,35,148,164,2,6
	.byte	'reserved_9218',0
	.word	72951
	.byte	232,3,4,35,152,164,2,6
	.byte	'COMPAT',0
	.word	68186
	.byte	4,4,35,128,168,2,6
	.byte	'reserved_9404',0
	.word	72962
	.byte	252,23,4,35,132,168,2,6
	.byte	'FPU_TRAP_CON',0
	.word	69815
	.byte	4,4,35,128,192,2,6
	.byte	'FPU_TRAP_PC',0
	.word	69955
	.byte	4,4,35,132,192,2,6
	.byte	'FPU_TRAP_OPC',0
	.word	69885
	.byte	4,4,35,136,192,2,6
	.byte	'reserved_A00C',0
	.word	5533
	.byte	4,4,35,140,192,2,6
	.byte	'FPU_TRAP_SRC1',0
	.word	70024
	.byte	4,4,35,144,192,2,6
	.byte	'FPU_TRAP_SRC2',0
	.word	70095
	.byte	4,4,35,148,192,2,6
	.byte	'FPU_TRAP_SRC3',0
	.word	70166
	.byte	4,4,35,152,192,2,6
	.byte	'reserved_A01C',0
	.word	72973
	.byte	228,63,4,35,156,192,2,6
	.byte	'DPR',0
	.word	72994
	.byte	128,1,4,35,128,128,3,6
	.byte	'reserved_C080',0
	.word	72999
	.byte	128,31,4,35,128,129,3,6
	.byte	'CPR',0
	.word	73019
	.byte	64,4,35,128,160,3,6
	.byte	'reserved_D040',0
	.word	73024
	.byte	192,31,4,35,192,160,3,6
	.byte	'CPXE',0
	.word	73035
	.byte	16,4,35,128,192,3,6
	.byte	'DPRE',0
	.word	73044
	.byte	16,4,35,144,192,3,6
	.byte	'DPWE',0
	.word	73053
	.byte	16,4,35,160,192,3,6
	.byte	'reserved_E030',0
	.word	73062
	.byte	208,7,4,35,176,192,3,6
	.byte	'TPS',0
	.word	73073
	.byte	16,4,35,128,200,3,6
	.byte	'reserved_E410',0
	.word	73078
	.byte	240,23,4,35,144,200,3,6
	.byte	'TR',0
	.word	73098
	.byte	64,4,35,128,224,3,6
	.byte	'reserved_F040',0
	.word	73103
	.byte	192,23,4,35,192,224,3,6
	.byte	'CCTRL',0
	.word	68123
	.byte	4,4,35,128,248,3,6
	.byte	'CCNT',0
	.word	68061
	.byte	4,4,35,132,248,3,6
	.byte	'ICNT',0
	.word	70237
	.byte	4,4,35,136,248,3,6
	.byte	'M1CNT',0
	.word	70482
	.byte	4,4,35,140,248,3,6
	.byte	'M2CNT',0
	.word	70545
	.byte	4,4,35,144,248,3,6
	.byte	'M3CNT',0
	.word	70608
	.byte	4,4,35,148,248,3,6
	.byte	'reserved_FC18',0
	.word	73114
	.byte	232,1,4,35,152,248,3,6
	.byte	'DBGSR',0
	.word	68815
	.byte	4,4,35,128,250,3,6
	.byte	'reserved_FD04',0
	.word	5533
	.byte	4,4,35,132,250,3,6
	.byte	'EXEVT',0
	.word	69691
	.byte	4,4,35,136,250,3,6
	.byte	'CREVT',0
	.word	68567
	.byte	4,4,35,140,250,3,6
	.byte	'SWEVT',0
	.word	71972
	.byte	4,4,35,144,250,3,6
	.byte	'reserved_FD14',0
	.word	56853
	.byte	28,4,35,148,250,3,6
	.byte	'TRIG_ACC',0
	.word	72425
	.byte	4,4,35,176,250,3,6
	.byte	'reserved_FD34',0
	.word	7692
	.byte	12,4,35,180,250,3,6
	.byte	'DMS',0
	.word	69318
	.byte	4,4,35,192,250,3,6
	.byte	'DCX',0
	.word	69068
	.byte	4,4,35,196,250,3,6
	.byte	'DBGTCR',0
	.word	68878
	.byte	4,4,35,200,250,3,6
	.byte	'reserved_FD4C',0
	.word	73125
	.byte	180,1,4,35,204,250,3,6
	.byte	'PCXI',0
	.word	70920
	.byte	4,4,35,128,252,3,6
	.byte	'PSW',0
	.word	71356
	.byte	4,4,35,132,252,3,6
	.byte	'PC',0
	.word	70671
	.byte	4,4,35,136,252,3,6
	.byte	'reserved_FE0C',0
	.word	7352
	.byte	8,4,35,140,252,3,6
	.byte	'SYSCON',0
	.word	72035
	.byte	4,4,35,148,252,3,6
	.byte	'CPU_ID',0
	.word	68441
	.byte	4,4,35,152,252,3,6
	.byte	'CORE_ID',0
	.word	68250
	.byte	4,4,35,156,252,3,6
	.byte	'BIV',0
	.word	67939
	.byte	4,4,35,160,252,3,6
	.byte	'BTV',0
	.word	68000
	.byte	4,4,35,164,252,3,6
	.byte	'ISP',0
	.word	70360
	.byte	4,4,35,168,252,3,6
	.byte	'ICR',0
	.word	70299
	.byte	4,4,35,172,252,3,6
	.byte	'reserved_FE30',0
	.word	7352
	.byte	8,4,35,176,252,3,6
	.byte	'FCX',0
	.word	69754
	.byte	4,4,35,184,252,3,6
	.byte	'LCX',0
	.word	70421
	.byte	4,4,35,188,252,3,6
	.byte	'reserved_FE40',0
	.word	27395
	.byte	16,4,35,192,252,3,6
	.byte	'CUS_ID',0
	.word	68630
	.byte	4,4,35,208,252,3,6
	.byte	'reserved_FE54',0
	.word	73136
	.byte	172,1,4,35,212,252,3,6
	.byte	'D',0
	.word	73147
	.byte	64,4,35,128,254,3,6
	.byte	'reserved_FF40',0
	.word	73156
	.byte	64,4,35,192,254,3,6
	.byte	'A',0
	.word	73165
	.byte	64,4,35,128,255,3,6
	.byte	'reserved_FFC0',0
	.word	73156
	.byte	64,4,35,192,255,3,0,21
	.word	73174
	.byte	9
	.byte	'Ifx_CPU',0,26,130,11,3
	.word	74965
	.byte	13,11,127,9,1,14
	.byte	'IfxCpu_Id_0',0,0,14
	.byte	'IfxCpu_Id_1',0,1,14
	.byte	'IfxCpu_Id_none',0,2,0,9
	.byte	'IfxCpu_Id',0,11,132,1,3
	.word	74987
	.byte	9
	.byte	'IfxCpu_ResourceCpu',0,11,161,1,3
	.word	4162
	.byte	15
	.byte	'_Ifx_STM_ACCEN0_Bits',0,27,45,16,4,4
	.byte	'EN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_STM_ACCEN0_Bits',0,27,79,3
	.word	75085
	.byte	15
	.byte	'_Ifx_STM_ACCEN1_Bits',0,27,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_ACCEN1_Bits',0,27,85,3
	.word	75642
	.byte	15
	.byte	'_Ifx_STM_CAP_Bits',0,27,88,16,4,4
	.byte	'STMCAP63_32',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_CAP_Bits',0,27,91,3
	.word	75719
	.byte	15
	.byte	'_Ifx_STM_CAPSV_Bits',0,27,94,16,4,4
	.byte	'STMCAP63_32',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_CAPSV_Bits',0,27,97,3
	.word	75791
	.byte	15
	.byte	'_Ifx_STM_CLC_Bits',0,27,100,16,4,4
	.byte	'DISR',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'DISS',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EDIS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_STM_CLC_Bits',0,27,107,3
	.word	75867
	.byte	15
	.byte	'_Ifx_STM_CMCON_Bits',0,27,110,16,4,4
	.byte	'MSIZE0',0,1
	.word	267
	.byte	5,3,2,35,0,4
	.byte	'reserved_5',0,1
	.word	267
	.byte	3,0,2,35,0,4
	.byte	'MSTART0',0,1
	.word	267
	.byte	5,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	267
	.byte	3,0,2,35,1,4
	.byte	'MSIZE1',0,1
	.word	267
	.byte	5,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	267
	.byte	3,0,2,35,2,4
	.byte	'MSTART1',0,1
	.word	267
	.byte	5,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	267
	.byte	3,0,2,35,3,0,9
	.byte	'Ifx_STM_CMCON_Bits',0,27,120,3
	.word	76008
	.byte	15
	.byte	'_Ifx_STM_CMP_Bits',0,27,123,16,4,4
	.byte	'CMPVAL',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_CMP_Bits',0,27,126,3
	.word	76226
	.byte	15
	.byte	'_Ifx_STM_ICR_Bits',0,27,129,1,16,4,4
	.byte	'CMP0EN',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'CMP0IR',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'CMP0OS',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'CMP1EN',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'CMP1IR',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'CMP1OS',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'reserved_7',0,4
	.word	2659
	.byte	25,0,2,35,0,0,9
	.byte	'Ifx_STM_ICR_Bits',0,27,139,1,3
	.word	76293
	.byte	15
	.byte	'_Ifx_STM_ID_Bits',0,27,142,1,16,4,4
	.byte	'MODREV',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_STM_ID_Bits',0,27,147,1,3
	.word	76496
	.byte	15
	.byte	'_Ifx_STM_ISCR_Bits',0,27,150,1,16,4,4
	.byte	'CMP0IRR',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'CMP0IRS',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'CMP1IRR',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'CMP1IRS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_STM_ISCR_Bits',0,27,157,1,3
	.word	76603
	.byte	15
	.byte	'_Ifx_STM_KRST0_Bits',0,27,160,1,16,4,4
	.byte	'RST',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'RSTSTAT',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2659
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_STM_KRST0_Bits',0,27,165,1,3
	.word	76754
	.byte	15
	.byte	'_Ifx_STM_KRST1_Bits',0,27,168,1,16,4,4
	.byte	'RST',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2659
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_STM_KRST1_Bits',0,27,172,1,3
	.word	76865
	.byte	15
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,27,175,1,16,4,4
	.byte	'CLR',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2659
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_STM_KRSTCLR_Bits',0,27,179,1,3
	.word	76957
	.byte	15
	.byte	'_Ifx_STM_OCS_Bits',0,27,182,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	24,8,2,35,0,4
	.byte	'SUS',0,1
	.word	267
	.byte	4,4,2,35,3,4
	.byte	'SUS_P',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'SUSSTA',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	267
	.byte	2,0,2,35,3,0,9
	.byte	'Ifx_STM_OCS_Bits',0,27,189,1,3
	.word	77053
	.byte	15
	.byte	'_Ifx_STM_TIM0_Bits',0,27,192,1,16,4,4
	.byte	'STM31_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM0_Bits',0,27,195,1,3
	.word	77199
	.byte	15
	.byte	'_Ifx_STM_TIM0SV_Bits',0,27,198,1,16,4,4
	.byte	'STM31_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM0SV_Bits',0,27,201,1,3
	.word	77271
	.byte	15
	.byte	'_Ifx_STM_TIM1_Bits',0,27,204,1,16,4,4
	.byte	'STM35_4',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM1_Bits',0,27,207,1,3
	.word	77347
	.byte	15
	.byte	'_Ifx_STM_TIM2_Bits',0,27,210,1,16,4,4
	.byte	'STM39_8',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM2_Bits',0,27,213,1,3
	.word	77419
	.byte	15
	.byte	'_Ifx_STM_TIM3_Bits',0,27,216,1,16,4,4
	.byte	'STM43_12',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM3_Bits',0,27,219,1,3
	.word	77491
	.byte	15
	.byte	'_Ifx_STM_TIM4_Bits',0,27,222,1,16,4,4
	.byte	'STM47_16',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM4_Bits',0,27,225,1,3
	.word	77564
	.byte	15
	.byte	'_Ifx_STM_TIM5_Bits',0,27,228,1,16,4,4
	.byte	'STM51_20',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM5_Bits',0,27,231,1,3
	.word	77637
	.byte	15
	.byte	'_Ifx_STM_TIM6_Bits',0,27,234,1,16,4,4
	.byte	'STM63_32',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM6_Bits',0,27,237,1,3
	.word	77710
	.byte	5,27,245,1,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75085
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ACCEN0',0,27,250,1,3
	.word	77783
	.byte	5,27,253,1,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75642
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ACCEN1',0,27,130,2,3
	.word	77847
	.byte	5,27,133,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75719
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CAP',0,27,138,2,3
	.word	77911
	.byte	5,27,141,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75791
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CAPSV',0,27,146,2,3
	.word	77972
	.byte	5,27,149,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75867
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CLC',0,27,154,2,3
	.word	78035
	.byte	5,27,157,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76008
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CMCON',0,27,162,2,3
	.word	78096
	.byte	5,27,165,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76226
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CMP',0,27,170,2,3
	.word	78159
	.byte	5,27,173,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76293
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ICR',0,27,178,2,3
	.word	78220
	.byte	5,27,181,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76496
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ID',0,27,186,2,3
	.word	78281
	.byte	5,27,189,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76603
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ISCR',0,27,194,2,3
	.word	78341
	.byte	5,27,197,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76754
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_KRST0',0,27,202,2,3
	.word	78403
	.byte	5,27,205,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76865
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_KRST1',0,27,210,2,3
	.word	78466
	.byte	5,27,213,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76957
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_KRSTCLR',0,27,218,2,3
	.word	78529
	.byte	5,27,221,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77053
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_OCS',0,27,226,2,3
	.word	78594
	.byte	5,27,229,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77199
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM0',0,27,234,2,3
	.word	78655
	.byte	5,27,237,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77271
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM0SV',0,27,242,2,3
	.word	78717
	.byte	5,27,245,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77347
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM1',0,27,250,2,3
	.word	78781
	.byte	5,27,253,2,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77419
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM2',0,27,130,3,3
	.word	78843
	.byte	5,27,133,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77491
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM3',0,27,138,3,3
	.word	78905
	.byte	5,27,141,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77564
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM4',0,27,146,3,3
	.word	78967
	.byte	5,27,149,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77637
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM5',0,27,154,3,3
	.word	79029
	.byte	5,27,157,3,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	77710
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM6',0,27,162,3,3
	.word	79091
	.byte	13,10,144,1,9,1,14
	.byte	'IfxCpu_CounterMode_normal',0,0,14
	.byte	'IfxCpu_CounterMode_task',0,1,0,9
	.byte	'IfxCpu_CounterMode',0,10,148,1,3
	.word	79153
	.byte	3,10,160,1,9,6,6
	.byte	'counter',0
	.word	246
	.byte	4,2,35,0,6
	.byte	'overlfow',0
	.word	267
	.byte	1,2,35,4,0,9
	.byte	'IfxCpu_Counter',0,10,164,1,3
	.word	79242
	.byte	3,10,172,1,9,32,6
	.byte	'instruction',0
	.word	79242
	.byte	6,2,35,0,6
	.byte	'clock',0
	.word	79242
	.byte	6,2,35,6,6
	.byte	'counter1',0
	.word	79242
	.byte	6,2,35,12,6
	.byte	'counter2',0
	.word	79242
	.byte	6,2,35,18,6
	.byte	'counter3',0
	.word	79242
	.byte	6,2,35,24,0,9
	.byte	'IfxCpu_Perf',0,10,179,1,3
	.word	79308
	.byte	15
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,28,45,16,4,4
	.byte	'EN0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,28,79,3
	.word	79426
	.byte	15
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,28,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,28,85,3
	.word	79987
	.byte	15
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,28,88,16,4,4
	.byte	'SEL',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'CLR',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'DIS',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,4
	.word	2659
	.byte	22,0,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,28,95,3
	.word	80068
	.byte	15
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,28,98,16,4,4
	.byte	'VLD0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'VLD1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'VLD2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'VLD3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'VLD4',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'VLD5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'VLD6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'VLD7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'VLD8',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'VLD9',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,4
	.word	2659
	.byte	22,0,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,28,111,3
	.word	80221
	.byte	15
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,28,114,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2659
	.byte	19,8,2,35,0,4
	.byte	'ERR',0,1
	.word	267
	.byte	6,2,2,35,3,4
	.byte	'VLD',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'CLR',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,28,121,3
	.word	80469
	.byte	15
	.byte	'_Ifx_FLASH_COMM0_Bits',0,28,124,16,4,4
	.byte	'STATUS',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2659
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM0_Bits',0,28,128,1,3
	.word	80615
	.byte	15
	.byte	'_Ifx_FLASH_COMM1_Bits',0,28,131,1,16,4,4
	.byte	'STATUS',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'DATA',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_COMM1_Bits',0,28,136,1,3
	.word	80713
	.byte	15
	.byte	'_Ifx_FLASH_COMM2_Bits',0,28,139,1,16,4,4
	.byte	'STATUS',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'DATA',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_COMM2_Bits',0,28,144,1,3
	.word	80829
	.byte	15
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,28,147,1,16,4,4
	.byte	'RCODE',0,4
	.word	2659
	.byte	22,10,2,35,0,4
	.byte	'reserved_22',0,2
	.word	928
	.byte	8,2,2,35,2,4
	.byte	'EDCERRINJ',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'ECCORDIS',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ECCRD_Bits',0,28,153,1,3
	.word	80945
	.byte	15
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,28,156,1,16,4,4
	.byte	'RCODE',0,4
	.word	2659
	.byte	22,10,2,35,0,4
	.byte	'reserved_22',0,2
	.word	928
	.byte	8,2,2,35,2,4
	.byte	'EDCERRINJ',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'ECCORDIS',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ECCRP_Bits',0,28,162,1,3
	.word	81085
	.byte	15
	.byte	'_Ifx_FLASH_ECCW_Bits',0,28,165,1,16,4,4
	.byte	'WCODE',0,4
	.word	2659
	.byte	22,10,2,35,0,4
	.byte	'reserved_22',0,2
	.word	928
	.byte	8,2,2,35,2,4
	.byte	'DECENCDIS',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'PECENCDIS',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ECCW_Bits',0,28,171,1,3
	.word	81225
	.byte	15
	.byte	'_Ifx_FLASH_FCON_Bits',0,28,174,1,16,4,4
	.byte	'WSPFLASH',0,1
	.word	267
	.byte	4,4,2,35,0,4
	.byte	'WSECPF',0,1
	.word	267
	.byte	2,2,2,35,0,4
	.byte	'WSDFLASH',0,2
	.word	928
	.byte	6,4,2,35,0,4
	.byte	'WSECDF',0,1
	.word	267
	.byte	3,1,2,35,1,4
	.byte	'IDLE',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'ESLDIS',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'SLEEP',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'NSAFECC',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'STALL',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'RES21',0,1
	.word	267
	.byte	2,2,2,35,2,4
	.byte	'RES23',0,1
	.word	267
	.byte	2,0,2,35,2,4
	.byte	'VOPERM',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'SQERM',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'PROERM',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	267
	.byte	3,2,2,35,3,4
	.byte	'PR5V',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'EOBM',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_FCON_Bits',0,28,193,1,3
	.word	81364
	.byte	15
	.byte	'_Ifx_FLASH_FPRO_Bits',0,28,196,1,16,4,4
	.byte	'PROINP',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'PRODISP',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'PROIND',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'PRODISD',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'PROINHSMCOTP',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'RES5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'PROINOTP',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'RES7',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'PROINDBG',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'PRODISDBG',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'PROINHSM',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'reserved_11',0,1
	.word	267
	.byte	5,0,2,35,1,4
	.byte	'DCFP',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'DDFP',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'DDFPX',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'DDFD',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'ENPE',0,1
	.word	267
	.byte	2,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	267
	.byte	8,0,2,35,3,0,9
	.byte	'Ifx_FLASH_FPRO_Bits',0,28,218,1,3
	.word	81726
	.byte	15
	.byte	'_Ifx_FLASH_FSR_Bits',0,28,221,1,16,4,4
	.byte	'FABUSY',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'D0BUSY',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'RES1',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'P0BUSY',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'P1BUSY',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'RES5',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'RES6',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'PROG',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'ERASE',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'PFPAGE',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'DFPAGE',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'OPER',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'SQER',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'PROER',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'PFSBER',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'PFDBER',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'PFMBER',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'RES17',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'DFSBER',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'DFDBER',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'DFTBER',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'DFMBER',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'SRIADDERR',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	928
	.byte	2,7,2,35,2,4
	.byte	'PVER',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'EVER',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'SPND',0,1
	.word	267
	.byte	1,4,2,35,3,4
	.byte	'SLM',0,1
	.word	267
	.byte	1,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	267
	.byte	1,2,2,35,3,4
	.byte	'ORIER',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_FSR_Bits',0,28,254,1,3
	.word	82167
	.byte	15
	.byte	'_Ifx_FLASH_ID_Bits',0,28,129,2,16,4,4
	.byte	'MODREV',0,1
	.word	267
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	267
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_ID_Bits',0,28,134,2,3
	.word	82773
	.byte	15
	.byte	'_Ifx_FLASH_MARD_Bits',0,28,137,2,16,4,4
	.byte	'HMARGIN',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'SELD0',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'SPND',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'SPNDERR',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'reserved_5',0,2
	.word	928
	.byte	10,1,2,35,0,4
	.byte	'TRAPDIS',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_MARD_Bits',0,28,147,2,3
	.word	82884
	.byte	15
	.byte	'_Ifx_FLASH_MARP_Bits',0,28,150,2,16,4,4
	.byte	'SELP0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'SELP1',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'RES2',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'RES3',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,2
	.word	928
	.byte	11,1,2,35,0,4
	.byte	'TRAPDIS',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_MARP_Bits',0,28,159,2,3
	.word	83098
	.byte	15
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,28,162,2,16,4,4
	.byte	'L',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'NSAFECC',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'RAMIN',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'RAMINSEL',0,1
	.word	267
	.byte	4,0,2,35,0,4
	.byte	'OSCCFG',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'MODE',0,1
	.word	267
	.byte	2,5,2,35,1,4
	.byte	'APREN',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'CAP0EN',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'CAP1EN',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'CAP2EN',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'CAP3EN',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'ESR0CNT',0,2
	.word	928
	.byte	12,4,2,35,2,4
	.byte	'RES29',0,1
	.word	267
	.byte	2,2,2,35,3,4
	.byte	'RES30',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'RPRO',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCOND_Bits',0,28,179,2,3
	.word	83285
	.byte	15
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,28,182,2,16,4,4
	.byte	'OCDSDIS',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'DBGIFLCK',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'EDM',0,1
	.word	267
	.byte	2,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2659
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,28,188,2,3
	.word	83609
	.byte	15
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,28,191,2,16,4,4
	.byte	'HSMDBGDIS',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'DBGIFLCK',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'TSTIFLCK',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'HSMTSTDIS',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'RES15',0,2
	.word	928
	.byte	12,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,28,199,2,3
	.word	83752
	.byte	15
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,28,202,2,16,4,4
	.byte	'HSMBOOTEN',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'SSWWAIT',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'HSMDX',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'HSM6X',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'HSM16X',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'HSM17X',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'S6ROM',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'HSMENPINS',0,2
	.word	928
	.byte	2,7,2,35,0,4
	.byte	'HSMENRES',0,1
	.word	267
	.byte	2,5,2,35,1,4
	.byte	'DESTDBG',0,1
	.word	267
	.byte	2,3,2,35,1,4
	.byte	'BLKFLAN',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'reserved_14',0,1
	.word	267
	.byte	2,0,2,35,1,4
	.byte	'S16ROM',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'S17ROM',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,2
	.word	928
	.byte	14,0,2,35,2,0,9
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,28,219,2,3
	.word	83941
	.byte	15
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,28,222,2,16,4,4
	.byte	'S0ROM',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'S1ROM',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'S2ROM',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'S3ROM',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'S4ROM',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'S5ROM',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'S6ROM',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'S7ROM',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'S8ROM',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'S9ROM',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'S10ROM',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'S11ROM',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'S12ROM',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'S13ROM',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'S14ROM',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'S15ROM',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'S16ROM',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'S17ROM',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'S18ROM',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'S19ROM',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'S20ROM',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'S21ROM',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'S22ROM',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'S23ROM',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'S24ROM',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'S25ROM',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'S26ROM',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	267
	.byte	2,3,2,35,3,4
	.byte	'BML',0,1
	.word	267
	.byte	2,1,2,35,3,4
	.byte	'TP',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,28,254,2,3
	.word	84304
	.byte	15
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,28,129,3,16,4,4
	.byte	'S0L',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'S1L',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'S2L',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'S3L',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'S4L',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'S5L',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'S6L',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'S7L',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'S8L',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'S9L',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'S10L',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'S11L',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'S12L',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'S13L',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'S14L',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'S15L',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'S16L',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'S17L',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'S18L',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'S19L',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'S20L',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'S21L',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'S22L',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'S23L',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'S24L',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'S25L',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'S26L',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	267
	.byte	4,1,2,35,3,4
	.byte	'RPRO',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCONP_Bits',0,28,160,3,3
	.word	84899
	.byte	15
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,28,163,3,16,4,4
	.byte	'S0WOP',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'S1WOP',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'S2WOP',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'S3WOP',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'S4WOP',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'S5WOP',0,1
	.word	267
	.byte	1,2,2,35,0,4
	.byte	'S6WOP',0,1
	.word	267
	.byte	1,1,2,35,0,4
	.byte	'S7WOP',0,1
	.word	267
	.byte	1,0,2,35,0,4
	.byte	'S8WOP',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'S9WOP',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'S10WOP',0,1
	.word	267
	.byte	1,5,2,35,1,4
	.byte	'S11WOP',0,1
	.word	267
	.byte	1,4,2,35,1,4
	.byte	'S12WOP',0,1
	.word	267
	.byte	1,3,2,35,1,4
	.byte	'S13WOP',0,1
	.word	267
	.byte	1,2,2,35,1,4
	.byte	'S14WOP',0,1
	.word	267
	.byte	1,1,2,35,1,4
	.byte	'S15WOP',0,1
	.word	267
	.byte	1,0,2,35,1,4
	.byte	'S16WOP',0,1
	.word	267
	.byte	1,7,2,35,2,4
	.byte	'S17WOP',0,1
	.word	267
	.byte	1,6,2,35,2,4
	.byte	'S18WOP',0,1
	.word	267
	.byte	1,5,2,35,2,4
	.byte	'S19WOP',0,1
	.word	267
	.byte	1,4,2,35,2,4
	.byte	'S20WOP',0,1
	.word	267
	.byte	1,3,2,35,2,4
	.byte	'S21WOP',0,1
	.word	267
	.byte	1,2,2,35,2,4
	.byte	'S22WOP',0,1
	.word	267
	.byte	1,1,2,35,2,4
	.byte	'S23WOP',0,1
	.word	267
	.byte	1,0,2,35,2,4
	.byte	'S24WOP',0,1
	.word	267
	.byte	1,7,2,35,3,4
	.byte	'S25WOP',0,1
	.word	267
	.byte	1,6,2,35,3,4
	.byte	'S26WOP',0,1
	.word	267
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	267
	.byte	4,1,2,35,3,4
	.byte	'DATM',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,28,194,3,3
	.word	85423
	.byte	15
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,28,197,3,16,4,4
	.byte	'TAG',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2659
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,28,201,3,3
	.word	86005
	.byte	15
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,28,204,3,16,4,4
	.byte	'TAG',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2659
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,28,208,3,3
	.word	86107
	.byte	15
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,28,211,3,16,4,4
	.byte	'TAG',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2659
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,28,215,3,3
	.word	86209
	.byte	15
	.byte	'_Ifx_FLASH_RRAD_Bits',0,28,218,3,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	3,5,2,35,0,4
	.byte	'ADD',0,4
	.word	2659
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RRAD_Bits',0,28,222,3,3
	.word	86311
	.byte	15
	.byte	'_Ifx_FLASH_RRCT_Bits',0,28,225,3,16,4,4
	.byte	'STRT',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'STP',0,1
	.word	267
	.byte	1,6,2,35,0,4
	.byte	'BUSY',0,1
	.word	267
	.byte	1,5,2,35,0,4
	.byte	'DONE',0,1
	.word	267
	.byte	1,4,2,35,0,4
	.byte	'ERR',0,1
	.word	267
	.byte	1,3,2,35,0,4
	.byte	'reserved_5',0,1
	.word	267
	.byte	3,0,2,35,0,4
	.byte	'EOBM',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'reserved_9',0,1
	.word	267
	.byte	7,0,2,35,1,4
	.byte	'CNT',0,2
	.word	928
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_RRCT_Bits',0,28,236,3,3
	.word	86405
	.byte	15
	.byte	'_Ifx_FLASH_RRD0_Bits',0,28,239,3,16,4,4
	.byte	'DATA',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD0_Bits',0,28,242,3,3
	.word	86615
	.byte	15
	.byte	'_Ifx_FLASH_RRD1_Bits',0,28,245,3,16,4,4
	.byte	'DATA',0,4
	.word	2659
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD1_Bits',0,28,248,3,3
	.word	86688
	.byte	15
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,28,251,3,16,4,4
	.byte	'SEL',0,1
	.word	267
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	267
	.byte	2,0,2,35,0,4
	.byte	'CLR',0,1
	.word	267
	.byte	1,7,2,35,1,4
	.byte	'DIS',0,1
	.word	267
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,4
	.word	2659
	.byte	22,0,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,28,130,4,3
	.word	86761
	.byte	15
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,28,133,4,16,4,4
	.byte	'VLD0',0,1
	.word	267
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2659
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,28,137,4,3
	.word	86916
	.byte	15
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,28,140,4,16,4,4
	.byte	'reserved_0',0,1
	.word	267
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2659
	.byte	19,8,2,35,0,4
	.byte	'ERR',0,1
	.word	267
	.byte	6,2,2,35,3,4
	.byte	'VLD',0,1
	.word	267
	.byte	1,1,2,35,3,4
	.byte	'CLR',0,1
	.word	267
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,28,147,4,3
	.word	87021
	.byte	5,28,155,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79426
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ACCEN0',0,28,160,4,3
	.word	87169
	.byte	5,28,163,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79987
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ACCEN1',0,28,168,4,3
	.word	87235
	.byte	5,28,171,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80068
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_CFG',0,28,176,4,3
	.word	87301
	.byte	5,28,179,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80221
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_STAT',0,28,184,4,3
	.word	87369
	.byte	5,28,187,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80469
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_TOP',0,28,192,4,3
	.word	87438
	.byte	5,28,195,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80615
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM0',0,28,200,4,3
	.word	87506
	.byte	5,28,203,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80713
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM1',0,28,208,4,3
	.word	87571
	.byte	5,28,211,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80829
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM2',0,28,216,4,3
	.word	87636
	.byte	5,28,219,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80945
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ECCRD',0,28,224,4,3
	.word	87701
	.byte	5,28,227,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81085
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ECCRP',0,28,232,4,3
	.word	87766
	.byte	5,28,235,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81225
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ECCW',0,28,240,4,3
	.word	87831
	.byte	5,28,243,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81364
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_FCON',0,28,248,4,3
	.word	87895
	.byte	5,28,251,4,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81726
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_FPRO',0,28,128,5,3
	.word	87959
	.byte	5,28,131,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	82167
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_FSR',0,28,136,5,3
	.word	88023
	.byte	5,28,139,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	82773
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ID',0,28,144,5,3
	.word	88086
	.byte	5,28,147,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	82884
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_MARD',0,28,152,5,3
	.word	88148
	.byte	5,28,155,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	83098
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_MARP',0,28,160,5,3
	.word	88212
	.byte	5,28,163,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	83285
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCOND',0,28,168,5,3
	.word	88276
	.byte	5,28,171,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	83609
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONDBG',0,28,176,5,3
	.word	88343
	.byte	5,28,179,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	83752
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONHSM',0,28,184,5,3
	.word	88412
	.byte	5,28,187,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	83941
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,28,192,5,3
	.word	88481
	.byte	5,28,195,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	84304
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONOTP',0,28,200,5,3
	.word	88554
	.byte	5,28,203,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	84899
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONP',0,28,208,5,3
	.word	88623
	.byte	5,28,211,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85423
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONWOP',0,28,216,5,3
	.word	88690
	.byte	5,28,219,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86005
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG0',0,28,224,5,3
	.word	88759
	.byte	5,28,227,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86107
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG1',0,28,232,5,3
	.word	88827
	.byte	5,28,235,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86209
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG2',0,28,240,5,3
	.word	88895
	.byte	5,28,243,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86311
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRAD',0,28,248,5,3
	.word	88963
	.byte	5,28,251,5,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86405
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRCT',0,28,128,6,3
	.word	89027
	.byte	5,28,131,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86615
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD0',0,28,136,6,3
	.word	89091
	.byte	5,28,139,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86688
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD1',0,28,144,6,3
	.word	89155
	.byte	5,28,147,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86761
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_CFG',0,28,152,6,3
	.word	89219
	.byte	5,28,155,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	86916
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_STAT',0,28,160,6,3
	.word	89287
	.byte	5,28,163,6,9,4,6
	.byte	'U',0
	.word	2659
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2675
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	87021
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_TOP',0,28,168,6,3
	.word	89356
	.byte	15
	.byte	'_Ifx_FLASH_CBAB',0,28,179,6,25,12,6
	.byte	'CFG',0
	.word	87301
	.byte	4,2,35,0,6
	.byte	'STAT',0
	.word	87369
	.byte	4,2,35,4,6
	.byte	'TOP',0
	.word	87438
	.byte	4,2,35,8,0,21
	.word	89424
	.byte	9
	.byte	'Ifx_FLASH_CBAB',0,28,184,6,3
	.word	89487
	.byte	15
	.byte	'_Ifx_FLASH_RDB',0,28,187,6,25,12,6
	.byte	'CFG0',0
	.word	88759
	.byte	4,2,35,0,6
	.byte	'CFG1',0
	.word	88827
	.byte	4,2,35,4,6
	.byte	'CFG2',0
	.word	88895
	.byte	4,2,35,8,0,21
	.word	89516
	.byte	9
	.byte	'Ifx_FLASH_RDB',0,28,192,6,3
	.word	89580
	.byte	15
	.byte	'_Ifx_FLASH_UBAB',0,28,195,6,25,12,6
	.byte	'CFG',0
	.word	89219
	.byte	4,2,35,0,6
	.byte	'STAT',0
	.word	89287
	.byte	4,2,35,4,6
	.byte	'TOP',0
	.word	89356
	.byte	4,2,35,8,0,21
	.word	89608
	.byte	9
	.byte	'Ifx_FLASH_UBAB',0,28,200,6,3
	.word	89671
	.byte	9
	.byte	'Ifx_P_ACCEN0_Bits',0,13,79,3
	.word	11105
	.byte	9
	.byte	'Ifx_P_ACCEN1_Bits',0,13,85,3
	.word	11018
	.byte	9
	.byte	'Ifx_P_ESR_Bits',0,13,107,3
	.word	7361
	.byte	9
	.byte	'Ifx_P_ID_Bits',0,13,115,3
	.word	5414
	.byte	9
	.byte	'Ifx_P_IN_Bits',0,13,137,1,3
	.word	6409
	.byte	9
	.byte	'Ifx_P_IOCR0_Bits',0,13,150,1,3
	.word	5542
	.byte	9
	.byte	'Ifx_P_IOCR12_Bits',0,13,163,1,3
	.word	6189
	.byte	9
	.byte	'Ifx_P_IOCR4_Bits',0,13,176,1,3
	.word	5757
	.byte	9
	.byte	'Ifx_P_IOCR8_Bits',0,13,189,1,3
	.word	5972
	.byte	9
	.byte	'Ifx_P_LPCR0_Bits',0,13,197,1,3
	.word	10377
	.byte	9
	.byte	'Ifx_P_LPCR1_Bits',0,13,205,1,3
	.word	10501
	.byte	9
	.byte	'Ifx_P_LPCR1_P21_Bits',0,13,215,1,3
	.word	10585
	.byte	9
	.byte	'Ifx_P_LPCR2_Bits',0,13,229,1,3
	.word	10765
	.byte	9
	.byte	'Ifx_P_OMCR0_Bits',0,13,240,1,3
	.word	9016
	.byte	9
	.byte	'Ifx_P_OMCR12_Bits',0,13,250,1,3
	.word	9540
	.byte	9
	.byte	'Ifx_P_OMCR4_Bits',0,13,133,2,3
	.word	9190
	.byte	9
	.byte	'Ifx_P_OMCR8_Bits',0,13,144,2,3
	.word	9364
	.byte	9
	.byte	'Ifx_P_OMCR_Bits',0,13,166,2,3
	.word	10029
	.byte	9
	.byte	'Ifx_P_OMR_Bits',0,13,203,2,3
	.word	4843
	.byte	9
	.byte	'Ifx_P_OMSR0_Bits',0,13,213,2,3
	.word	8353
	.byte	9
	.byte	'Ifx_P_OMSR12_Bits',0,13,224,2,3
	.word	8841
	.byte	9
	.byte	'Ifx_P_OMSR4_Bits',0,13,235,2,3
	.word	8500
	.byte	9
	.byte	'Ifx_P_OMSR8_Bits',0,13,246,2,3
	.word	8669
	.byte	9
	.byte	'Ifx_P_OMSR_Bits',0,13,140,3,3
	.word	9696
	.byte	9
	.byte	'Ifx_P_OUT_Bits',0,13,162,3,3
	.word	4527
	.byte	9
	.byte	'Ifx_P_PCSR_Bits',0,13,180,3,3
	.word	8067
	.byte	9
	.byte	'Ifx_P_PDISC_Bits',0,13,202,3,3
	.word	7701
	.byte	9
	.byte	'Ifx_P_PDR0_Bits',0,13,223,3,3
	.word	6732
	.byte	9
	.byte	'Ifx_P_PDR1_Bits',0,13,244,3,3
	.word	7036
	.byte	9
	.byte	'Ifx_P_ACCEN0',0,13,129,4,3
	.word	11632
	.byte	9
	.byte	'Ifx_P_ACCEN1',0,13,137,4,3
	.word	11065
	.byte	9
	.byte	'Ifx_P_ESR',0,13,145,4,3
	.word	7652
	.byte	9
	.byte	'Ifx_P_ID',0,13,153,4,3
	.word	5493
	.byte	9
	.byte	'Ifx_P_IN',0,13,161,4,3
	.word	6683
	.byte	9
	.byte	'Ifx_P_IOCR0',0,13,169,4,3
	.word	5717
	.byte	9
	.byte	'Ifx_P_IOCR12',0,13,177,4,3
	.word	6369
	.byte	9
	.byte	'Ifx_P_IOCR4',0,13,185,4,3
	.word	5932
	.byte	9
	.byte	'Ifx_P_IOCR8',0,13,193,4,3
	.word	6149
	.byte	9
	.byte	'Ifx_P_LPCR0',0,13,201,4,3
	.word	10461
	.byte	9
	.byte	'Ifx_P_LPCR1',0,13,210,4,3
	.word	10710
	.byte	9
	.byte	'Ifx_P_LPCR2',0,13,218,4,3
	.word	10969
	.byte	9
	.byte	'Ifx_P_OMCR',0,13,226,4,3
	.word	10337
	.byte	9
	.byte	'Ifx_P_OMCR0',0,13,234,4,3
	.word	9150
	.byte	9
	.byte	'Ifx_P_OMCR12',0,13,242,4,3
	.word	9656
	.byte	9
	.byte	'Ifx_P_OMCR4',0,13,250,4,3
	.word	9324
	.byte	9
	.byte	'Ifx_P_OMCR8',0,13,130,5,3
	.word	9500
	.byte	9
	.byte	'Ifx_P_OMR',0,13,138,5,3
	.word	5374
	.byte	9
	.byte	'Ifx_P_OMSR',0,13,146,5,3
	.word	9989
	.byte	9
	.byte	'Ifx_P_OMSR0',0,13,154,5,3
	.word	8460
	.byte	9
	.byte	'Ifx_P_OMSR12',0,13,162,5,3
	.word	8976
	.byte	9
	.byte	'Ifx_P_OMSR4',0,13,170,5,3
	.word	8629
	.byte	9
	.byte	'Ifx_P_OMSR8',0,13,178,5,3
	.word	8801
	.byte	9
	.byte	'Ifx_P_OUT',0,13,186,5,3
	.word	4803
	.byte	9
	.byte	'Ifx_P_PCSR',0,13,194,5,3
	.word	8313
	.byte	9
	.byte	'Ifx_P_PDISC',0,13,202,5,3
	.word	8027
	.byte	9
	.byte	'Ifx_P_PDR0',0,13,210,5,3
	.word	6996
	.byte	9
	.byte	'Ifx_P_PDR1',0,13,218,5,3
	.word	7312
	.byte	21
	.word	11672
	.byte	9
	.byte	'Ifx_P',0,13,139,6,3
	.word	91018
	.byte	9
	.byte	'IfxPort_InputMode',0,12,89,3
	.word	12285
	.byte	9
	.byte	'IfxPort_OutputIdx',0,12,130,1,3
	.word	12560
	.byte	9
	.byte	'IfxPort_OutputMode',0,12,138,1,3
	.word	12490
	.byte	9
	.byte	'IfxPort_PadDriver',0,12,158,1,3
	.word	22794
	.byte	9
	.byte	'IfxPort_State',0,12,178,1,3
	.word	12873
	.byte	9
	.byte	'IfxPort_Pin',0,12,194,1,3
	.word	22560
	.byte	9
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,29,148,1,16
	.word	2418
	.byte	3,29,212,5,9,8,6
	.byte	'value',0
	.word	246
	.byte	4,2,35,0,6
	.byte	'mask',0
	.word	246
	.byte	4,2,35,4,0,9
	.byte	'IfxScuCcu_CcuconRegConfig',0,29,216,5,3
	.word	91230
	.byte	3,29,221,5,9,8,6
	.byte	'pDivider',0
	.word	267
	.byte	1,2,35,0,6
	.byte	'nDivider',0
	.word	267
	.byte	1,2,35,1,6
	.byte	'k2Initial',0
	.word	267
	.byte	1,2,35,2,6
	.byte	'waitTime',0
	.word	536
	.byte	4,2,35,4,0,9
	.byte	'IfxScuCcu_InitialStepConfig',0,29,227,5,3
	.word	91301
	.byte	3,29,231,5,9,12,6
	.byte	'k2Step',0
	.word	267
	.byte	1,2,35,0,6
	.byte	'waitTime',0
	.word	536
	.byte	4,2,35,2,6
	.byte	'hookFunction',0
	.word	91190
	.byte	4,2,35,8,0,9
	.byte	'IfxScuCcu_PllStepsConfig',0,29,236,5,3
	.word	91418
	.byte	8
	.word	2415
	.byte	3,29,244,5,9,48,6
	.byte	'ccucon0',0
	.word	91230
	.byte	8,2,35,0,6
	.byte	'ccucon1',0
	.word	91230
	.byte	8,2,35,8,6
	.byte	'ccucon2',0
	.word	91230
	.byte	8,2,35,16,6
	.byte	'ccucon5',0
	.word	91230
	.byte	8,2,35,24,6
	.byte	'ccucon6',0
	.word	91230
	.byte	8,2,35,32,6
	.byte	'ccucon7',0
	.word	91230
	.byte	8,2,35,40,0,9
	.byte	'IfxScuCcu_ClockDistributionConfig',0,29,252,5,3
	.word	91520
	.byte	3,29,128,6,9,8,6
	.byte	'value',0
	.word	246
	.byte	4,2,35,0,6
	.byte	'mask',0
	.word	246
	.byte	4,2,35,4,0,9
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,29,132,6,3
	.word	91672
	.byte	8
	.word	91418
	.byte	3,29,137,6,9,16,6
	.byte	'numOfPllDividerSteps',0
	.word	267
	.byte	1,2,35,0,6
	.byte	'pllDividerStep',0
	.word	91748
	.byte	4,2,35,4,6
	.byte	'pllInitialStep',0
	.word	91301
	.byte	8,2,35,8,0,9
	.byte	'IfxScuCcu_SysPllConfig',0,29,142,6,3
	.word	91753
	.byte	9
	.byte	'Ifx_GPT12_ACCEN0_Bits',0,15,79,3
	.word	16258
	.byte	9
	.byte	'Ifx_GPT12_ACCEN1_Bits',0,15,85,3
	.word	16167
	.byte	9
	.byte	'Ifx_GPT12_CAPREL_Bits',0,15,92,3
	.word	15032
	.byte	9
	.byte	'Ifx_GPT12_CLC_Bits',0,15,102,3
	.word	13054
	.byte	9
	.byte	'Ifx_GPT12_ID_Bits',0,15,110,3
	.word	13508
	.byte	9
	.byte	'Ifx_GPT12_KRST0_Bits',0,15,118,3
	.word	16043
	.byte	9
	.byte	'Ifx_GPT12_KRST1_Bits',0,15,125,3
	.word	15938
	.byte	9
	.byte	'Ifx_GPT12_KRSTCLR_Bits',0,15,132,1,3
	.word	15830
	.byte	9
	.byte	'Ifx_GPT12_OCS_Bits',0,15,142,1,3
	.word	15668
	.byte	9
	.byte	'Ifx_GPT12_PISEL_Bits',0,15,159,1,3
	.word	13212
	.byte	9
	.byte	'Ifx_GPT12_T2_Bits',0,15,166,1,3
	.word	15142
	.byte	9
	.byte	'Ifx_GPT12_T2CON_Bits',0,15,183,1,3
	.word	13631
	.byte	9
	.byte	'Ifx_GPT12_T3_Bits',0,15,190,1,3
	.word	15245
	.byte	9
	.byte	'Ifx_GPT12_T3CON_Bits',0,15,207,1,3
	.word	13914
	.byte	9
	.byte	'Ifx_GPT12_T4_Bits',0,15,214,1,3
	.word	15348
	.byte	9
	.byte	'Ifx_GPT12_T4CON_Bits',0,15,232,1,3
	.word	14188
	.byte	9
	.byte	'Ifx_GPT12_T5_Bits',0,15,239,1,3
	.word	15451
	.byte	9
	.byte	'Ifx_GPT12_T5CON_Bits',0,15,128,2,3
	.word	14486
	.byte	9
	.byte	'Ifx_GPT12_T6_Bits',0,15,135,2,3
	.word	15554
	.byte	9
	.byte	'Ifx_GPT12_T6CON_Bits',0,15,152,2,3
	.word	14757
	.byte	9
	.byte	'Ifx_GPT12_ACCEN0',0,15,165,2,3
	.word	16789
	.byte	9
	.byte	'Ifx_GPT12_ACCEN1',0,15,173,2,3
	.word	16218
	.byte	9
	.byte	'Ifx_GPT12_CAPREL',0,15,181,2,3
	.word	15102
	.byte	9
	.byte	'Ifx_GPT12_CLC',0,15,189,2,3
	.word	13172
	.byte	9
	.byte	'Ifx_GPT12_ID',0,15,197,2,3
	.word	13591
	.byte	9
	.byte	'Ifx_GPT12_KRST0',0,15,205,2,3
	.word	16127
	.byte	9
	.byte	'Ifx_GPT12_KRST1',0,15,213,2,3
	.word	16003
	.byte	9
	.byte	'Ifx_GPT12_KRSTCLR',0,15,221,2,3
	.word	15898
	.byte	9
	.byte	'Ifx_GPT12_OCS',0,15,229,2,3
	.word	15790
	.byte	9
	.byte	'Ifx_GPT12_PISEL',0,15,237,2,3
	.word	13468
	.byte	9
	.byte	'Ifx_GPT12_T2',0,15,245,2,3
	.word	15205
	.byte	9
	.byte	'Ifx_GPT12_T2CON',0,15,253,2,3
	.word	13874
	.byte	9
	.byte	'Ifx_GPT12_T3',0,15,133,3,3
	.word	15308
	.byte	9
	.byte	'Ifx_GPT12_T3CON',0,15,141,3,3
	.word	14148
	.byte	9
	.byte	'Ifx_GPT12_T4',0,15,149,3,3
	.word	15411
	.byte	9
	.byte	'Ifx_GPT12_T4CON',0,15,157,3,3
	.word	14446
	.byte	9
	.byte	'Ifx_GPT12_T5',0,15,165,3,3
	.word	15514
	.byte	9
	.byte	'Ifx_GPT12_T5CON',0,15,173,3,3
	.word	14717
	.byte	9
	.byte	'Ifx_GPT12_T6',0,15,181,3,3
	.word	15617
	.byte	9
	.byte	'Ifx_GPT12_T6CON',0,15,189,3,3
	.word	14992
	.byte	21
	.word	16829
	.byte	9
	.byte	'Ifx_GPT12',0,15,225,3,3
	.word	92929
	.byte	28
	.word	22718
	.byte	9
	.byte	'IfxGpt12_TxEud_In',0,17,73,3
	.word	92953
	.byte	28
	.word	23311
	.byte	9
	.byte	'IfxGpt12_TxIn_In',0,17,82,3
	.word	92984
	.byte	3,17,85,15,20,6
	.byte	'module',0
	.word	17205
	.byte	4,2,35,0,6
	.byte	'timer',0
	.word	267
	.byte	1,2,35,4,6
	.byte	'pin',0
	.word	22560
	.byte	8,2,35,8,6
	.byte	'select',0
	.word	12560
	.byte	1,2,35,16,0,28
	.word	93014
	.byte	9
	.byte	'IfxGpt12_TxOut_Out',0,17,91,3
	.word	93080
	.byte	9
	.byte	'IfxGpt12_CaptureInput',0,14,85,3
	.word	20505
	.byte	9
	.byte	'IfxGpt12_CaptureInputMode',0,14,95,3
	.word	18814
	.byte	9
	.byte	'IfxGpt12_CaptureTrigger',0,14,104,3
	.word	19406
	.byte	9
	.byte	'IfxGpt12_CaptureTriggerMode',0,14,115,3
	.word	19632
	.byte	13,14,120,9,1,14
	.byte	'IfxGpt12_CounterInputMode_counterDisabled',0,0,14
	.byte	'IfxGpt12_CounterInputMode_risingEdgeTxIN',0,1,14
	.byte	'IfxGpt12_CounterInputMode_fallingEdgeTxIN',0,2,14
	.byte	'IfxGpt12_CounterInputMode_bothEdgesTxIN',0,3,14
	.byte	'IfxGpt12_CounterInputMode_risingEdgeTxOTL',0,5,14
	.byte	'IfxGpt12_CounterInputMode_fallingEdgeTxOTL',0,6,14
	.byte	'IfxGpt12_CounterInputMode_bothEdgesTxOTL',0,7,0,9
	.byte	'IfxGpt12_CounterInputMode',0,14,129,1,3
	.word	93244
	.byte	13,14,134,1,9,1,14
	.byte	'IfxGpt12_EudInput_A',0,0,14
	.byte	'IfxGpt12_EudInput_B',0,1,14
	.byte	'IfxGpt12_EudInput_C',0,2,14
	.byte	'IfxGpt12_EudInput_D',0,3,0,9
	.byte	'IfxGpt12_EudInput',0,14,140,1,3
	.word	93590
	.byte	13,14,145,1,9,1,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_8',0,0,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_4',0,1,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_32',0,2,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_16',0,3,0,9
	.byte	'IfxGpt12_Gpt1BlockPrescaler',0,14,151,1,3
	.word	93712
	.byte	13,14,156,1,9,1,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_4',0,0,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_2',0,1,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_16',0,2,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_8',0,3,0,9
	.byte	'IfxGpt12_Gpt2BlockPrescaler',0,14,162,1,3
	.word	93886
	.byte	9
	.byte	'IfxGpt12_IncrementalInterfaceInputMode',0,14,173,1,3
	.word	17519
	.byte	13,14,178,1,9,1,14
	.byte	'IfxGpt12_Input_A',0,0,14
	.byte	'IfxGpt12_Input_B',0,1,14
	.byte	'IfxGpt12_Input_C',0,2,14
	.byte	'IfxGpt12_Input_D',0,3,0,9
	.byte	'IfxGpt12_Input',0,14,184,1,3
	.word	94107
	.byte	9
	.byte	'IfxGpt12_Mode',0,14,199,1,3
	.word	17843
	.byte	13,14,204,1,9,1,14
	.byte	'IfxGpt12_ReloadInputMode_counterDisabled',0,0,14
	.byte	'IfxGpt12_ReloadInputMode_risingEdgeTxIN',0,1,14
	.byte	'IfxGpt12_ReloadInputMode_fallingEdgeTxIN',0,2,14
	.byte	'IfxGpt12_ReloadInputMode_bothEdgesTxIN',0,3,14
	.byte	'IfxGpt12_ReloadInputMode_risingEdgeTxOTL',0,5,14
	.byte	'IfxGpt12_ReloadInputMode_fallingEdgeTxOTL',0,6,14
	.byte	'IfxGpt12_ReloadInputMode_bothEdgesTxOTL',0,7,0,9
	.byte	'IfxGpt12_ReloadInputMode',0,14,213,1,3
	.word	94237
	.byte	13,14,218,1,9,1,14
	.byte	'IfxGpt12_SleepMode_enable',0,0,14
	.byte	'IfxGpt12_SleepMode_disable',0,1,0,9
	.byte	'IfxGpt12_SleepMode',0,14,222,1,3
	.word	94576
	.byte	13,14,226,1,9,1,14
	.byte	'IfxGpt12_SuspendMode_none',0,0,14
	.byte	'IfxGpt12_SuspendMode_hard',0,1,14
	.byte	'IfxGpt12_SuspendMode_soft',0,2,0,9
	.byte	'IfxGpt12_SuspendMode',0,14,231,1,3
	.word	94668
	.byte	9
	.byte	'IfxGpt12_TimerDirection',0,14,240,1,3
	.word	18167
	.byte	9
	.byte	'IfxGpt12_TimerDirectionSource',0,14,249,1,3
	.word	17358
	.byte	9
	.byte	'IfxGpt12_TimerInputPrescaler',0,14,136,2,3
	.word	20150
	.byte	13,14,141,2,9,1,14
	.byte	'IfxGpt12_TimerReloadMode_disable',0,0,14
	.byte	'IfxGpt12_TimerReloadMode_enable',0,1,0,9
	.byte	'IfxGpt12_TimerReloadMode',0,14,145,2,3
	.word	94899
	.byte	9
	.byte	'IfxGpt12_TimerRemoteControl',0,14,154,2,3
	.word	19190
	.byte	9
	.byte	'IfxGpt12_TimerRun',0,14,163,2,3
	.word	20680
	.byte	9
	.byte	'IfxGpt12_IncrEnc',0,20,234,1,3
	.word	23559
	.byte	9
	.byte	'IfxGpt12_IncrEnc_Config',0,20,251,1,3
	.word	23974
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L150:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,5,23,1,58,15,59,15,57,15,11,15,0,0,6,13,0,3,8,73,19,11
	.byte	15,56,9,0,0,7,59,0,3,8,0,0,8,15,0,73,19,0,0,9,22,0,3,8,58,15,59,15,57,15,73,19,0,0,10,21,1,54,15,39,12
	.byte	0,0,11,5,0,73,19,0,0,12,21,1,73,19,54,15,39,12,0,0,13,4,1,58,15,59,15,57,15,11,15,0,0,14,40,0,3,8,28,13
	.byte	0,0,15,19,1,3,8,58,15,59,15,57,15,11,15,0,0,16,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0
	.byte	17,5,0,3,8,58,15,59,15,57,15,73,19,0,0,18,11,0,0,0,19,21,0,54,15,39,12,0,0,20,46,1,3,8,32,13,58,15,59
	.byte	15,57,15,54,15,39,12,0,0,21,53,0,73,19,0,0,22,11,1,0,0,23,1,1,11,15,73,19,0,0,24,33,0,47,15,0,0,25,46
	.byte	1,49,19,0,0,26,5,0,49,19,0,0,27,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,28,38,0,73,19,0
	.byte	0,29,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,30,29,1,49,19,0,0,31,11,0,49,19,0,0
	.byte	32,5,0,58,15,59,15,57,15,73,19,0,0,33,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,34,21,0,54,15,0
	.byte	0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L151:
	.word	.L690-.L689
.L689:
	.half	3
	.word	.L692-.L691
.L691:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_Pos.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0
	.byte	'IfxGpt12_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Math\\Ifx_LowPassPt1F32.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxGpt12_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'string.h',0,2,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Gpt12\\IncrEnc\\IfxGpt12_IncrEnc.h',0,0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0
	.byte	'stddef.h',0,2,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L692:
.L690:
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_init')
	.sect	'.debug_info'
.L152:
	.word	2276
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L155,.L154
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_init',0,1,161,1,9
	.word	.L277
	.byte	1,1,1
	.word	.L123,.L278,.L122
	.byte	4
	.byte	'driver',0,1,161,1,49
	.word	.L279,.L280
	.byte	4
	.byte	'config',0,1,161,1,88
	.word	.L281,.L282
	.byte	5
	.word	.L123,.L278
	.byte	6
	.byte	'status',0,1,163,1,16
	.word	.L277,.L283
	.byte	6
	.byte	'gpt12',0,1,164,1,16
	.word	.L284,.L285
	.byte	7
	.word	.L286,.L287,.L288
	.byte	8
	.word	.L289,.L290
	.byte	8
	.word	.L291,.L292
	.byte	9
	.word	.L293,.L287,.L288
	.byte	0,7
	.word	.L294,.L295,.L296
	.byte	8
	.word	.L297,.L298
	.byte	8
	.word	.L299,.L300
	.byte	9
	.word	.L301,.L295,.L296
	.byte	0,7
	.word	.L294,.L302,.L303
	.byte	8
	.word	.L297,.L298
	.byte	8
	.word	.L299,.L300
	.byte	9
	.word	.L301,.L302,.L303
	.byte	0,7
	.word	.L304,.L305,.L306
	.byte	8
	.word	.L307,.L308
	.byte	8
	.word	.L309,.L310
	.byte	9
	.word	.L311,.L305,.L306
	.byte	0,7
	.word	.L312,.L24,.L313
	.byte	8
	.word	.L314,.L315
	.byte	8
	.word	.L316,.L317
	.byte	9
	.word	.L318,.L24,.L313
	.byte	0,7
	.word	.L319,.L320,.L321
	.byte	8
	.word	.L322,.L323
	.byte	8
	.word	.L324,.L325
	.byte	9
	.word	.L326,.L320,.L321
	.byte	0,7
	.word	.L327,.L328,.L329
	.byte	8
	.word	.L330,.L331
	.byte	8
	.word	.L332,.L333
	.byte	9
	.word	.L334,.L328,.L329
	.byte	0,7
	.word	.L335,.L336,.L337
	.byte	8
	.word	.L338,.L339
	.byte	8
	.word	.L340,.L341
	.byte	9
	.word	.L342,.L336,.L337
	.byte	0,7
	.word	.L343,.L344,.L345
	.byte	8
	.word	.L346,.L347
	.byte	8
	.word	.L348,.L349
	.byte	9
	.word	.L350,.L344,.L345
	.byte	0,7
	.word	.L351,.L352,.L353
	.byte	8
	.word	.L354,.L355
	.byte	8
	.word	.L356,.L357
	.byte	9
	.word	.L358,.L352,.L353
	.byte	0,7
	.word	.L359,.L360,.L361
	.byte	8
	.word	.L362,.L363
	.byte	8
	.word	.L364,.L365
	.byte	9
	.word	.L366,.L360,.L361
	.byte	0,7
	.word	.L367,.L368,.L369
	.byte	8
	.word	.L370,.L371
	.byte	8
	.word	.L372,.L373
	.byte	9
	.word	.L374,.L368,.L369
	.byte	0,7
	.word	.L375,.L376,.L377
	.byte	8
	.word	.L378,.L379
	.byte	8
	.word	.L380,.L381
	.byte	9
	.word	.L382,.L376,.L377
	.byte	0,7
	.word	.L383,.L384,.L385
	.byte	8
	.word	.L386,.L387
	.byte	8
	.word	.L388,.L389
	.byte	9
	.word	.L390,.L384,.L385
	.byte	0,5
	.word	.L391,.L27
	.byte	6
	.byte	'src',0,1,221,1,40
	.word	.L392,.L393
	.byte	7
	.word	.L394,.L391,.L36
	.byte	8
	.word	.L395,.L396
	.byte	9
	.word	.L397,.L391,.L36
	.byte	0,7
	.word	.L398,.L399,.L400
	.byte	8
	.word	.L401,.L402
	.byte	8
	.word	.L403,.L404
	.byte	8
	.word	.L405,.L406
	.byte	10
	.word	.L407,.L399,.L400
	.byte	7
	.word	.L408,.L409,.L400
	.byte	8
	.word	.L410,.L411
	.byte	9
	.word	.L412,.L409,.L400
	.byte	0,0,0,7
	.word	.L413,.L400,.L27
	.byte	8
	.word	.L414,.L415
	.byte	9
	.word	.L416,.L400,.L27
	.byte	0,0,7
	.word	.L417,.L418,.L419
	.byte	8
	.word	.L420,.L421
	.byte	8
	.word	.L422,.L423
	.byte	9
	.word	.L424,.L418,.L419
	.byte	0,7
	.word	.L425,.L426,.L427
	.byte	8
	.word	.L428,.L429
	.byte	8
	.word	.L430,.L431
	.byte	9
	.word	.L432,.L426,.L427
	.byte	0,7
	.word	.L433,.L434,.L435
	.byte	8
	.word	.L436,.L437
	.byte	8
	.word	.L438,.L439
	.byte	9
	.word	.L440,.L434,.L435
	.byte	0,7
	.word	.L441,.L442,.L443
	.byte	8
	.word	.L444,.L445
	.byte	8
	.word	.L446,.L447
	.byte	9
	.word	.L448,.L442,.L443
	.byte	0,7
	.word	.L449,.L450,.L451
	.byte	8
	.word	.L452,.L453
	.byte	8
	.word	.L454,.L455
	.byte	9
	.word	.L456,.L450,.L451
	.byte	0,7
	.word	.L457,.L451,.L458
	.byte	8
	.word	.L459,.L460
	.byte	8
	.word	.L461,.L462
	.byte	9
	.word	.L463,.L451,.L458
	.byte	0,7
	.word	.L464,.L465,.L466
	.byte	8
	.word	.L467,.L468
	.byte	8
	.word	.L469,.L470
	.byte	9
	.word	.L471,.L465,.L466
	.byte	0,7
	.word	.L472,.L473,.L474
	.byte	8
	.word	.L475,.L476
	.byte	8
	.word	.L477,.L478
	.byte	9
	.word	.L479,.L473,.L474
	.byte	0,7
	.word	.L480,.L481,.L482
	.byte	8
	.word	.L483,.L484
	.byte	8
	.word	.L485,.L486
	.byte	9
	.word	.L487,.L481,.L482
	.byte	0,7
	.word	.L488,.L489,.L490
	.byte	8
	.word	.L491,.L492
	.byte	8
	.word	.L493,.L494
	.byte	9
	.word	.L495,.L489,.L490
	.byte	0,7
	.word	.L496,.L497,.L498
	.byte	8
	.word	.L499,.L500
	.byte	8
	.word	.L501,.L502
	.byte	9
	.word	.L503,.L497,.L498
	.byte	0,7
	.word	.L504,.L505,.L506
	.byte	8
	.word	.L507,.L508
	.byte	8
	.word	.L509,.L510
	.byte	9
	.word	.L511,.L505,.L506
	.byte	0,7
	.word	.L504,.L512,.L513
	.byte	8
	.word	.L507,.L508
	.byte	8
	.word	.L509,.L510
	.byte	9
	.word	.L511,.L512,.L513
	.byte	0,7
	.word	.L514,.L515,.L516
	.byte	8
	.word	.L517,.L518
	.byte	8
	.word	.L519,.L520
	.byte	9
	.word	.L521,.L515,.L516
	.byte	0,7
	.word	.L522,.L48,.L523
	.byte	8
	.word	.L524,.L525
	.byte	8
	.word	.L526,.L527
	.byte	9
	.word	.L528,.L48,.L523
	.byte	0,7
	.word	.L529,.L530,.L531
	.byte	8
	.word	.L532,.L533
	.byte	8
	.word	.L534,.L535
	.byte	9
	.word	.L536,.L530,.L531
	.byte	0,7
	.word	.L335,.L537,.L538
	.byte	8
	.word	.L338,.L339
	.byte	8
	.word	.L340,.L341
	.byte	9
	.word	.L342,.L537,.L538
	.byte	0,7
	.word	.L343,.L539,.L540
	.byte	8
	.word	.L346,.L347
	.byte	8
	.word	.L348,.L349
	.byte	9
	.word	.L350,.L539,.L540
	.byte	0,7
	.word	.L351,.L541,.L542
	.byte	8
	.word	.L354,.L355
	.byte	8
	.word	.L356,.L357
	.byte	9
	.word	.L358,.L541,.L542
	.byte	0,7
	.word	.L359,.L543,.L544
	.byte	8
	.word	.L362,.L363
	.byte	8
	.word	.L364,.L365
	.byte	9
	.word	.L366,.L543,.L544
	.byte	0,7
	.word	.L367,.L545,.L546
	.byte	8
	.word	.L370,.L371
	.byte	8
	.word	.L372,.L373
	.byte	9
	.word	.L374,.L545,.L546
	.byte	0,7
	.word	.L375,.L547,.L548
	.byte	8
	.word	.L378,.L379
	.byte	8
	.word	.L380,.L381
	.byte	9
	.word	.L382,.L547,.L548
	.byte	0,7
	.word	.L383,.L549,.L49
	.byte	8
	.word	.L386,.L387
	.byte	8
	.word	.L388,.L389
	.byte	9
	.word	.L390,.L549,.L49
	.byte	0,5
	.word	.L550,.L58
	.byte	6
	.byte	'lpfConfig',0,1,168,2,34
	.word	.L551,.L552
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_init')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_init')
	.sect	'.debug_line'
.L154:
	.word	.L694-.L693
.L693:
	.half	3
	.word	.L696-.L695
.L695:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L696:
	.byte	5,9,7,0,5,2
	.word	.L123
	.byte	3,160,1,1,5,23,9
	.half	.L641-.L123
	.byte	3,2,1,5,31,9
	.half	.L642-.L641
	.byte	3,1,1,5,32,9
	.half	.L643-.L642
	.byte	3,2,1,5,46,9
	.half	.L697-.L643
	.byte	3,2,1,5,32,9
	.half	.L698-.L697
	.byte	1,5,46,9
	.half	.L699-.L698
	.byte	3,1,1,5,72,9
	.half	.L700-.L699
	.byte	1,5,58,9
	.half	.L701-.L700
	.byte	1,5,32,9
	.half	.L702-.L701
	.byte	1,5,34,9
	.half	.L703-.L702
	.byte	3,1,1,5,55,9
	.half	.L704-.L703
	.byte	1,5,40,9
	.half	.L705-.L704
	.byte	1,5,38,9
	.half	.L706-.L705
	.byte	1,5,70,9
	.half	.L707-.L706
	.byte	1,5,68,9
	.half	.L708-.L707
	.byte	1,5,76,9
	.half	.L709-.L708
	.byte	1,5,74,9
	.half	.L710-.L709
	.byte	1,5,32,9
	.half	.L711-.L710
	.byte	1,5,46,9
	.half	.L712-.L711
	.byte	3,1,1,5,32,9
	.half	.L713-.L712
	.byte	1,5,59,9
	.half	.L714-.L713
	.byte	3,1,1,5,40,9
	.half	.L645-.L714
	.byte	3,2,1,5,38,9
	.half	.L715-.L645
	.byte	1,5,21,9
	.half	.L716-.L715
	.byte	3,1,1,5,38,9
	.half	.L717-.L716
	.byte	1,5,52,9
	.half	.L718-.L717
	.byte	3,1,1,5,38,9
	.half	.L719-.L718
	.byte	1,5,52,9
	.half	.L720-.L719
	.byte	3,1,1,5,38,9
	.half	.L721-.L720
	.byte	1,5,40,9
	.half	.L722-.L721
	.byte	3,2,1,5,38,9
	.half	.L723-.L722
	.byte	1,5,40,9
	.half	.L724-.L723
	.byte	3,1,1,5,38,9
	.half	.L725-.L724
	.byte	1,5,40,9
	.half	.L726-.L725
	.byte	3,1,1,5,38,9
	.half	.L727-.L726
	.byte	1,5,40,9
	.half	.L728-.L727
	.byte	3,1,1,5,38,9
	.half	.L729-.L728
	.byte	1,5,15,9
	.half	.L730-.L729
	.byte	3,2,1,5,21,9
	.half	.L731-.L730
	.byte	1,5,5,9
	.half	.L732-.L731
	.byte	1,5,36,7,9
	.half	.L733-.L732
	.byte	3,3,1,4,2,5,19,9
	.half	.L287-.L733
	.byte	3,158,8,1,5,24,9
	.half	.L734-.L287
	.byte	1,4,1,5,29,9
	.half	.L288-.L734
	.byte	3,228,119,1,5,14,9
	.half	.L735-.L288
	.byte	3,2,1,9
	.half	.L736-.L735
	.byte	3,3,1,5,65,9
	.half	.L17-.L736
	.byte	3,126,1,4,2,5,19,9
	.half	.L295-.L17
	.byte	3,140,8,1,5,24,9
	.half	.L737-.L295
	.byte	1,4,1,5,13,9
	.half	.L296-.L737
	.byte	3,245,119,1,5,65,9
	.half	.L18-.L296
	.byte	3,2,1,4,2,5,19,9
	.half	.L302-.L18
	.byte	3,137,8,1,5,24,9
	.half	.L738-.L302
	.byte	1,4,1,5,13,9
	.half	.L303-.L738
	.byte	3,248,119,1,5,20,9
	.half	.L19-.L303
	.byte	3,2,1,5,13,9
	.half	.L739-.L19
	.byte	3,1,1,5,47,9
	.half	.L20-.L739
	.byte	3,3,1,4,2,5,19,9
	.half	.L305-.L20
	.byte	3,246,7,1,5,26,9
	.half	.L740-.L305
	.byte	1,4,1,5,58,9
	.half	.L306-.L740
	.byte	3,139,120,1,5,46,9
	.half	.L741-.L306
	.byte	1,5,68,7,9
	.half	.L742-.L741
	.byte	1,5,97,9
	.half	.L743-.L742
	.byte	1,5,68,9
	.half	.L23-.L743
	.byte	1,4,2,5,19,9
	.half	.L24-.L23
	.byte	3,148,8,1,5,25,9
	.half	.L744-.L24
	.byte	1,4,1,5,41,9
	.half	.L313-.L744
	.byte	3,237,119,1,4,2,5,27,9
	.half	.L320-.L313
	.byte	3,206,7,1,5,34,7,9
	.half	.L745-.L320
	.byte	1,5,38,9
	.half	.L746-.L745
	.byte	1,5,34,9
	.half	.L25-.L746
	.byte	1,5,19,9
	.half	.L26-.L25
	.byte	1,5,25,9
	.half	.L747-.L26
	.byte	1,4,1,5,32,9
	.half	.L321-.L747
	.byte	3,179,120,1,4,2,5,19,9
	.half	.L328-.L321
	.byte	3,230,7,1,5,24,9
	.half	.L748-.L328
	.byte	1,4,1,5,19,9
	.half	.L329-.L748
	.byte	3,156,120,1,5,9,9
	.half	.L749-.L329
	.byte	1,5,40,7,9
	.half	.L750-.L749
	.byte	3,3,1,4,2,5,19,9
	.half	.L336-.L750
	.byte	3,238,8,1,5,24,9
	.half	.L751-.L336
	.byte	1,4,1,5,52,9
	.half	.L337-.L751
	.byte	3,147,119,1,4,2,5,19,9
	.half	.L344-.L337
	.byte	3,195,8,1,5,24,9
	.half	.L752-.L344
	.byte	1,4,1,5,51,9
	.half	.L345-.L752
	.byte	3,190,119,1,4,2,5,30,9
	.half	.L352-.L345
	.byte	3,157,8,1,5,38,7,9
	.half	.L753-.L352
	.byte	1,5,42,9
	.half	.L754-.L753
	.byte	1,5,38,9
	.half	.L28-.L754
	.byte	1,5,19,9
	.half	.L29-.L28
	.byte	1,5,28,9
	.half	.L755-.L29
	.byte	1,4,1,5,51,9
	.half	.L353-.L755
	.byte	3,228,119,1,4,2,5,30,9
	.half	.L360-.L353
	.byte	3,162,8,1,5,38,7,9
	.half	.L756-.L360
	.byte	1,5,42,9
	.half	.L757-.L756
	.byte	1,5,38,9
	.half	.L30-.L757
	.byte	1,5,19,9
	.half	.L31-.L30
	.byte	1,5,28,9
	.half	.L758-.L31
	.byte	1,4,1,5,57,9
	.half	.L361-.L758
	.byte	3,223,119,1,5,75,9
	.half	.L759-.L361
	.byte	1,4,2,5,30,9
	.half	.L368-.L759
	.byte	3,228,8,1,5,38,7,9
	.half	.L760-.L368
	.byte	1,5,42,9
	.half	.L761-.L760
	.byte	1,5,38,9
	.half	.L32-.L761
	.byte	1,5,19,9
	.half	.L33-.L32
	.byte	1,5,28,9
	.half	.L762-.L33
	.byte	1,4,1,5,49,9
	.half	.L369-.L762
	.byte	3,157,119,1,4,2,5,19,9
	.half	.L376-.L369
	.byte	3,245,8,1,5,25,9
	.half	.L763-.L376
	.byte	1,4,1,5,36,9
	.half	.L377-.L763
	.byte	3,140,119,1,4,2,5,19,9
	.half	.L384-.L377
	.byte	3,184,8,1,5,24,9
	.half	.L764-.L384
	.byte	1,4,1,5,23,9
	.half	.L385-.L764
	.byte	3,202,119,1,5,13,9
	.half	.L765-.L385
	.byte	1,4,2,5,5,7,9
	.half	.L391-.L765
	.byte	3,169,8,1,5,38,7,9
	.half	.L35-.L391
	.byte	3,1,1,5,5,9
	.half	.L766-.L35
	.byte	1,4,1,5,40,9
	.half	.L36-.L766
	.byte	3,218,119,1,5,65,9
	.half	.L767-.L36
	.byte	1,4,3,5,11,9
	.half	.L399-.L767
	.byte	3,54,1,5,19,9
	.half	.L768-.L399
	.byte	1,5,17,9
	.half	.L769-.L768
	.byte	1,5,11,9
	.half	.L770-.L769
	.byte	3,1,1,5,17,9
	.half	.L771-.L770
	.byte	1,5,11,9
	.half	.L409-.L771
	.byte	3,103,1,5,17,9
	.half	.L772-.L409
	.byte	1,5,11,9
	.half	.L400-.L772
	.byte	3,18,1,5,16,9
	.half	.L773-.L400
	.byte	1,4,1,5,36,9
	.half	.L27-.L773
	.byte	3,86,1,4,2,5,19,9
	.half	.L418-.L27
	.byte	3,204,9,1,5,24,9
	.half	.L774-.L418
	.byte	1,4,1,5,46,9
	.half	.L419-.L774
	.byte	3,181,118,1,4,2,5,19,9
	.half	.L426-.L419
	.byte	3,221,9,1,5,24,9
	.half	.L775-.L426
	.byte	1,4,1,5,46,9
	.half	.L427-.L775
	.byte	3,164,118,1,4,2,5,19,9
	.half	.L434-.L427
	.byte	3,157,9,1,5,24,9
	.half	.L776-.L434
	.byte	1,4,1,5,50,9
	.half	.L435-.L776
	.byte	3,228,118,1,4,2,5,19,9
	.half	.L442-.L435
	.byte	3,168,9,1,5,23,9
	.half	.L777-.L442
	.byte	1,4,1,5,45,9
	.half	.L443-.L777
	.byte	3,217,118,1,4,2,5,28,9
	.half	.L450-.L443
	.byte	3,252,8,1,5,36,7,9
	.half	.L778-.L450
	.byte	1,5,40,9
	.half	.L779-.L778
	.byte	1,5,36,9
	.half	.L37-.L779
	.byte	1,5,19,9
	.half	.L38-.L37
	.byte	1,5,26,9
	.half	.L780-.L38
	.byte	1,5,19,9
	.half	.L451-.L780
	.byte	3,37,1,5,25,9
	.half	.L781-.L451
	.byte	1,4,1,5,45,9
	.half	.L458-.L781
	.byte	3,225,118,1,4,2,5,19,9
	.half	.L465-.L458
	.byte	3,204,9,1,5,25,9
	.half	.L782-.L465
	.byte	1,4,1,5,47,9
	.half	.L466-.L782
	.byte	3,181,118,1,4,2,5,19,9
	.half	.L473-.L466
	.byte	3,176,9,1,5,26,9
	.half	.L783-.L473
	.byte	1,4,1,5,46,9
	.half	.L474-.L783
	.byte	3,209,118,1,4,2,5,19,9
	.half	.L481-.L474
	.byte	3,208,9,1,5,25,9
	.half	.L784-.L481
	.byte	1,4,1,5,32,9
	.half	.L482-.L784
	.byte	3,177,118,1,4,2,5,19,9
	.half	.L489-.L482
	.byte	3,144,9,1,5,24,9
	.half	.L785-.L489
	.byte	1,4,1,5,26,9
	.half	.L490-.L785
	.byte	3,242,118,1,5,24,9
	.half	.L786-.L490
	.byte	1,5,56,9
	.half	.L787-.L786
	.byte	3,117,1,5,20,9
	.half	.L16-.L787
	.byte	3,13,1,5,26,9
	.half	.L788-.L16
	.byte	1,5,10,9
	.half	.L789-.L788
	.byte	1,5,36,7,9
	.half	.L790-.L789
	.byte	3,3,1,4,2,5,19,9
	.half	.L497-.L790
	.byte	3,130,7,1,5,24,9
	.half	.L791-.L497
	.byte	1,4,1,5,29,9
	.half	.L498-.L791
	.byte	3,128,121,1,5,14,9
	.half	.L792-.L498
	.byte	3,2,1,9
	.half	.L793-.L792
	.byte	3,3,1,5,65,9
	.half	.L41-.L793
	.byte	3,126,1,4,2,5,19,9
	.half	.L505-.L41
	.byte	3,234,6,1,5,24,9
	.half	.L794-.L505
	.byte	1,4,1,5,13,9
	.half	.L506-.L794
	.byte	3,151,121,1,5,65,9
	.half	.L42-.L506
	.byte	3,2,1,4,2,5,19,9
	.half	.L512-.L42
	.byte	3,231,6,1,5,24,9
	.half	.L795-.L512
	.byte	1,4,1,5,13,9
	.half	.L513-.L795
	.byte	3,154,121,1,5,20,9
	.half	.L43-.L513
	.byte	3,2,1,5,13,9
	.half	.L796-.L43
	.byte	3,1,1,5,47,9
	.half	.L44-.L796
	.byte	3,3,1,4,2,5,19,9
	.half	.L515-.L44
	.byte	3,211,6,1,5,26,9
	.half	.L797-.L515
	.byte	1,4,1,5,58,9
	.half	.L516-.L797
	.byte	3,174,121,1,5,46,9
	.half	.L798-.L516
	.byte	1,5,68,7,9
	.half	.L799-.L798
	.byte	1,5,97,9
	.half	.L800-.L799
	.byte	1,5,68,9
	.half	.L47-.L800
	.byte	1,4,2,5,19,9
	.half	.L48-.L47
	.byte	3,132,7,1,5,25,9
	.half	.L801-.L48
	.byte	1,4,1,5,32,9
	.half	.L523-.L801
	.byte	3,253,120,1,4,2,5,19,9
	.half	.L530-.L523
	.byte	3,191,6,1,5,24,9
	.half	.L802-.L530
	.byte	1,4,1,5,19,9
	.half	.L531-.L802
	.byte	3,195,121,1,5,9,9
	.half	.L803-.L531
	.byte	1,5,40,7,9
	.half	.L804-.L803
	.byte	3,3,1,4,2,5,19,9
	.half	.L537-.L804
	.byte	3,182,8,1,5,24,9
	.half	.L805-.L537
	.byte	1,4,1,5,52,9
	.half	.L538-.L805
	.byte	3,203,119,1,4,2,5,19,9
	.half	.L539-.L538
	.byte	3,139,8,1,5,24,9
	.half	.L806-.L539
	.byte	1,4,1,5,51,9
	.half	.L540-.L806
	.byte	3,246,119,1,4,2,5,30,9
	.half	.L541-.L540
	.byte	3,229,7,1,5,38,7,9
	.half	.L807-.L541
	.byte	1,5,42,9
	.half	.L808-.L807
	.byte	1,5,38,9
	.half	.L50-.L808
	.byte	1,5,19,9
	.half	.L51-.L50
	.byte	1,5,28,9
	.half	.L809-.L51
	.byte	1,4,1,5,51,9
	.half	.L542-.L809
	.byte	3,156,120,1,4,2,5,30,9
	.half	.L543-.L542
	.byte	3,234,7,1,5,38,7,9
	.half	.L810-.L543
	.byte	1,5,42,9
	.half	.L811-.L810
	.byte	1,5,38,9
	.half	.L52-.L811
	.byte	1,5,19,9
	.half	.L53-.L52
	.byte	1,5,28,9
	.half	.L812-.L53
	.byte	1,4,1,5,51,9
	.half	.L544-.L812
	.byte	3,151,120,1,4,2,5,30,9
	.half	.L545-.L544
	.byte	3,172,8,1,5,38,7,9
	.half	.L813-.L545
	.byte	1,5,42,9
	.half	.L814-.L813
	.byte	1,5,38,9
	.half	.L54-.L814
	.byte	1,5,19,9
	.half	.L55-.L54
	.byte	1,5,28,9
	.half	.L815-.L55
	.byte	1,4,1,5,49,9
	.half	.L546-.L815
	.byte	3,213,119,1,4,2,5,19,9
	.half	.L547-.L546
	.byte	3,189,8,1,5,25,9
	.half	.L816-.L547
	.byte	1,4,1,5,36,9
	.half	.L548-.L816
	.byte	3,196,119,1,4,2,5,19,9
	.half	.L549-.L548
	.byte	3,128,8,1,5,24,9
	.half	.L817-.L549
	.byte	1,4,1,5,26,9
	.half	.L49-.L817
	.byte	3,131,120,1,5,24,9
	.half	.L818-.L49
	.byte	1,5,15,9
	.half	.L39-.L818
	.byte	3,3,1,5,5,9
	.half	.L819-.L39
	.byte	1,5,48,7,9
	.half	.L820-.L819
	.byte	3,2,1,5,62,9
	.half	.L821-.L820
	.byte	1,5,79,9
	.half	.L822-.L821
	.byte	1,5,51,9
	.half	.L823-.L822
	.byte	3,1,1,5,65,9
	.half	.L824-.L823
	.byte	1,5,82,9
	.half	.L825-.L824
	.byte	1,5,19,9
	.half	.L826-.L825
	.byte	3,2,1,5,9,9
	.half	.L827-.L826
	.byte	1,5,52,7,9
	.half	.L828-.L827
	.byte	3,2,1,5,66,9
	.half	.L829-.L828
	.byte	1,5,83,9
	.half	.L830-.L829
	.byte	1,5,14,9
	.half	.L56-.L830
	.byte	3,5,1,5,39,9
	.half	.L831-.L56
	.byte	1,5,51,9
	.half	.L832-.L831
	.byte	1,5,24,9
	.half	.L833-.L832
	.byte	1,5,83,9
	.half	.L834-.L833
	.byte	1,5,82,9
	.half	.L648-.L834
	.byte	1,5,56,9
	.half	.L835-.L648
	.byte	1,5,32,9
	.half	.L836-.L835
	.byte	3,127,1,5,46,9
	.half	.L837-.L836
	.byte	3,3,1,5,32,9
	.half	.L838-.L837
	.byte	1,5,21,9
	.half	.L839-.L838
	.byte	3,2,1,5,5,9
	.half	.L840-.L839
	.byte	1,5,37,7,9
	.half	.L550-.L840
	.byte	3,3,1,5,35,9
	.half	.L841-.L550
	.byte	1,5,49,9
	.half	.L842-.L841
	.byte	3,1,1,5,35,9
	.half	.L843-.L842
	.byte	1,5,49,9
	.half	.L844-.L843
	.byte	3,1,1,5,35,9
	.half	.L845-.L844
	.byte	1,5,39,9
	.half	.L846-.L845
	.byte	3,1,1,5,52,9
	.half	.L847-.L846
	.byte	1,5,5,9
	.half	.L58-.L847
	.byte	3,3,1,5,1,9
	.half	.L59-.L58
	.byte	3,1,1,7,9
	.half	.L156-.L59
	.byte	0,1,1
.L694:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_init')
	.sect	'.debug_ranges'
.L155:
	.word	-1,.L123,0,.L156-.L123,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_initConfig')
	.sect	'.debug_info'
.L157:
	.word	349
	.half	3
	.word	.L158
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L160,.L159
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_initConfig',0,1,179,2,6,1,1,1
	.word	.L125,.L553,.L124
	.byte	4
	.byte	'config',0,1,179,2,59
	.word	.L554,.L555
	.byte	4
	.byte	'gpt12',0,1,179,2,78
	.word	.L284,.L556
	.byte	5
	.word	.L125,.L553
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_initConfig')
	.sect	'.debug_abbrev'
.L158:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_initConfig')
	.sect	'.debug_line'
.L159:
	.word	.L849-.L848
.L848:
	.half	3
	.word	.L851-.L850
.L850:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L851:
	.byte	5,6,7,0,5,2
	.word	.L125
	.byte	3,178,2,1,5,36,9
	.half	.L652-.L125
	.byte	3,2,1,5,46,9
	.half	.L650-.L652
	.byte	3,1,1,5,44,9
	.half	.L852-.L650
	.byte	1,5,57,9
	.half	.L853-.L852
	.byte	3,1,1,5,44,9
	.half	.L854-.L853
	.byte	1,5,61,9
	.half	.L855-.L854
	.byte	3,1,1,5,44,9
	.half	.L856-.L855
	.byte	1,5,46,9
	.half	.L857-.L856
	.byte	3,1,1,5,44,9
	.half	.L858-.L857
	.byte	1,5,58,9
	.half	.L859-.L858
	.byte	3,1,1,5,70,9
	.half	.L860-.L859
	.byte	1,5,68,9
	.half	.L861-.L860
	.byte	1,5,74,9
	.half	.L862-.L861
	.byte	1,5,72,9
	.half	.L863-.L862
	.byte	1,5,83,9
	.half	.L864-.L863
	.byte	1,5,81,9
	.half	.L865-.L864
	.byte	1,5,44,9
	.half	.L866-.L865
	.byte	1,5,46,9
	.half	.L867-.L866
	.byte	3,2,1,5,44,9
	.half	.L868-.L867
	.byte	1,5,46,9
	.half	.L869-.L868
	.byte	3,1,1,5,44,9
	.half	.L870-.L869
	.byte	1,5,46,9
	.half	.L871-.L870
	.byte	3,1,1,5,44,9
	.half	.L872-.L871
	.byte	1,5,46,9
	.half	.L873-.L872
	.byte	3,1,1,5,44,9
	.half	.L874-.L873
	.byte	1,9
	.half	.L875-.L874
	.byte	3,1,1,5,46,9
	.half	.L876-.L875
	.byte	3,1,1,5,44,9
	.half	.L877-.L876
	.byte	1,5,46,9
	.half	.L878-.L877
	.byte	3,1,1,5,44,9
	.half	.L879-.L878
	.byte	1,5,46,9
	.half	.L880-.L879
	.byte	3,1,1,5,44,9
	.half	.L881-.L880
	.byte	1,5,46,9
	.half	.L882-.L881
	.byte	3,2,1,5,44,9
	.half	.L883-.L882
	.byte	1,5,1,9
	.half	.L884-.L883
	.byte	3,1,1,7,9
	.half	.L161-.L884
	.byte	0,1,1
.L849:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_initConfig')
	.sect	'.debug_ranges'
.L160:
	.word	-1,.L125,0,.L161-.L125,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getAbsolutePosition')
	.sect	'.debug_info'
.L162:
	.word	341
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L165,.L164
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getAbsolutePosition',0,1,87,9
	.word	.L557
	.byte	1,1,1
	.word	.L99,.L558,.L98
	.byte	4
	.byte	'driver',0,1,87,64
	.word	.L279,.L559
	.byte	5
	.word	.L99,.L558
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getAbsolutePosition')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getAbsolutePosition')
	.sect	'.debug_line'
.L164:
	.word	.L886-.L885
.L885:
	.half	3
	.word	.L888-.L887
.L887:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L888:
	.byte	5,28,7,0,5,2
	.word	.L99
	.byte	3,216,0,1,5,13,9
	.half	.L889-.L99
	.byte	1,5,52,9
	.half	.L890-.L889
	.byte	1,5,37,9
	.half	.L891-.L890
	.byte	1,5,83,9
	.half	.L892-.L891
	.byte	1,5,68,9
	.half	.L893-.L892
	.byte	1,5,66,9
	.half	.L894-.L893
	.byte	1,5,35,9
	.half	.L895-.L894
	.byte	1,5,99,9
	.half	.L637-.L895
	.byte	1,5,97,9
	.half	.L896-.L637
	.byte	1,5,105,9
	.half	.L897-.L896
	.byte	1,5,103,9
	.half	.L898-.L897
	.byte	1,5,5,9
	.half	.L899-.L898
	.byte	1,5,1,9
	.half	.L2-.L899
	.byte	3,1,1,7,9
	.half	.L166-.L2
	.byte	0,1,1
.L886:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getAbsolutePosition')
	.sect	'.debug_ranges'
.L165:
	.word	-1,.L99,0,.L166-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getDirection')
	.sect	'.debug_info'
.L167:
	.word	334
	.half	3
	.word	.L168
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L170,.L169
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getDirection',0,1,93,18
	.word	.L560
	.byte	1,1,1
	.word	.L101,.L561,.L100
	.byte	4
	.byte	'driver',0,1,93,66
	.word	.L279,.L562
	.byte	5
	.word	.L101,.L561
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getDirection')
	.sect	'.debug_abbrev'
.L168:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getDirection')
	.sect	'.debug_line'
.L169:
	.word	.L901-.L900
.L900:
	.half	3
	.word	.L903-.L902
.L902:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L903:
	.byte	5,18,7,0,5,2
	.word	.L101
	.byte	3,222,0,1,5,5,9
	.half	.L904-.L101
	.byte	1,5,1,9
	.half	.L3-.L904
	.byte	3,1,1,7,9
	.half	.L171-.L3
	.byte	0,1,1
.L901:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getDirection')
	.sect	'.debug_ranges'
.L170:
	.word	-1,.L101,0,.L171-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getFault')
	.sect	'.debug_info'
.L172:
	.word	330
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L175,.L174
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getFault',0,1,99,21
	.word	.L563
	.byte	1,1,1
	.word	.L103,.L564,.L102
	.byte	4
	.byte	'driver',0,1,99,65
	.word	.L279,.L565
	.byte	5
	.word	.L103,.L564
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getFault')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getFault')
	.sect	'.debug_line'
.L174:
	.word	.L906-.L905
.L905:
	.half	3
	.word	.L908-.L907
.L907:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L908:
	.byte	5,18,7,0,5,2
	.word	.L103
	.byte	3,228,0,1,5,5,9
	.half	.L909-.L103
	.byte	1,5,1,9
	.half	.L4-.L909
	.byte	3,1,1,7,9
	.half	.L176-.L4
	.byte	0,1,1
.L906:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getFault')
	.sect	'.debug_ranges'
.L175:
	.word	-1,.L103,0,.L176-.L103,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getOffset')
	.sect	'.debug_info'
.L177:
	.word	331
	.half	3
	.word	.L178
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L180,.L179
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getOffset',0,1,105,8
	.word	.L566
	.byte	1,1,1
	.word	.L105,.L567,.L104
	.byte	4
	.byte	'driver',0,1,105,53
	.word	.L279,.L568
	.byte	5
	.word	.L105,.L567
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getOffset')
	.sect	'.debug_abbrev'
.L178:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getOffset')
	.sect	'.debug_line'
.L179:
	.word	.L911-.L910
.L910:
	.half	3
	.word	.L913-.L912
.L912:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L913:
	.byte	5,18,7,0,5,2
	.word	.L105
	.byte	3,234,0,1,5,5,9
	.half	.L914-.L105
	.byte	1,5,1,9
	.half	.L5-.L914
	.byte	3,1,1,7,9
	.half	.L181-.L5
	.byte	0,1,1
.L911:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getOffset')
	.sect	'.debug_ranges'
.L180:
	.word	-1,.L105,0,.L181-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getPeriodPerRotation')
	.sect	'.debug_info'
.L182:
	.word	342
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L185,.L184
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getPeriodPerRotation',0,1,111,8
	.word	.L569
	.byte	1,1,1
	.word	.L107,.L570,.L106
	.byte	4
	.byte	'driver',0,1,111,64
	.word	.L279,.L571
	.byte	5
	.word	.L107,.L570
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getPeriodPerRotation')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getPeriodPerRotation')
	.sect	'.debug_line'
.L184:
	.word	.L916-.L915
.L915:
	.half	3
	.word	.L918-.L917
.L917:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L918:
	.byte	5,5,7,0,5,2
	.word	.L107
	.byte	3,240,0,1,5,12,7,9
	.half	.L6-.L107
	.byte	3,1,1,5,5,9
	.half	.L919-.L6
	.byte	1,5,1,9
	.half	.L7-.L919
	.byte	3,1,1,7,9
	.half	.L186-.L7
	.byte	0,1,1
.L916:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getPeriodPerRotation')
	.sect	'.debug_ranges'
.L185:
	.word	-1,.L107,0,.L186-.L107,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getPosition')
	.sect	'.debug_info'
.L187:
	.word	333
	.half	3
	.word	.L188
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L190,.L189
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getPosition',0,1,118,9
	.word	.L557
	.byte	1,1,1
	.word	.L109,.L572,.L108
	.byte	4
	.byte	'driver',0,1,118,56
	.word	.L279,.L573
	.byte	5
	.word	.L109,.L572
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getPosition')
	.sect	'.debug_abbrev'
.L188:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getPosition')
	.sect	'.debug_line'
.L189:
	.word	.L921-.L920
.L920:
	.half	3
	.word	.L923-.L922
.L922:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L923:
	.byte	5,27,7,0,5,2
	.word	.L109
	.byte	3,247,0,1,5,12,9
	.half	.L924-.L109
	.byte	1,5,49,9
	.half	.L925-.L924
	.byte	1,5,41,9
	.half	.L926-.L925
	.byte	1,5,5,9
	.half	.L927-.L926
	.byte	1,5,1,9
	.half	.L8-.L927
	.byte	3,1,1,7,9
	.half	.L191-.L8
	.byte	0,1,1
.L921:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getPosition')
	.sect	'.debug_ranges'
.L190:
	.word	-1,.L109,0,.L191-.L109,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getRawPosition')
	.sect	'.debug_info'
.L192:
	.word	336
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L195,.L194
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getRawPosition',0,1,124,8
	.word	.L566
	.byte	1,1,1
	.word	.L111,.L574,.L110
	.byte	4
	.byte	'driver',0,1,124,58
	.word	.L279,.L575
	.byte	5
	.word	.L111,.L574
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getRawPosition')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getRawPosition')
	.sect	'.debug_line'
.L194:
	.word	.L929-.L928
.L928:
	.half	3
	.word	.L931-.L930
.L930:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L931:
	.byte	5,18,7,0,5,2
	.word	.L111
	.byte	3,253,0,1,5,5,9
	.half	.L932-.L111
	.byte	1,5,1,9
	.half	.L9-.L932
	.byte	3,1,1,7,9
	.half	.L196-.L9
	.byte	0,1,1
.L929:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getRawPosition')
	.sect	'.debug_ranges'
.L195:
	.word	-1,.L111,0,.L196-.L111,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getRefreshPeriod')
	.sect	'.debug_info'
.L197:
	.word	340
	.half	3
	.word	.L198
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L200,.L199
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getRefreshPeriod',0,1,130,1,9
	.word	.L557
	.byte	1,1,1
	.word	.L113,.L576,.L112
	.byte	4
	.byte	'driver',0,1,130,1,61
	.word	.L279,.L577
	.byte	5
	.word	.L113,.L576
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getRefreshPeriod')
	.sect	'.debug_abbrev'
.L198:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getRefreshPeriod')
	.sect	'.debug_line'
.L199:
	.word	.L934-.L933
.L933:
	.half	3
	.word	.L936-.L935
.L935:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L936:
	.byte	5,18,7,0,5,2
	.word	.L113
	.byte	3,131,1,1,5,5,9
	.half	.L937-.L113
	.byte	1,5,1,9
	.half	.L10-.L937
	.byte	3,1,1,7,9
	.half	.L201-.L10
	.byte	0,1,1
.L934:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getRefreshPeriod')
	.sect	'.debug_ranges'
.L200:
	.word	-1,.L113,0,.L201-.L113,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getResolution')
	.sect	'.debug_info'
.L202:
	.word	337
	.half	3
	.word	.L203
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L205,.L204
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getResolution',0,1,136,1,8
	.word	.L566
	.byte	1,1,1
	.word	.L115,.L578,.L114
	.byte	4
	.byte	'driver',0,1,136,1,57
	.word	.L279,.L579
	.byte	5
	.word	.L115,.L578
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getResolution')
	.sect	'.debug_abbrev'
.L203:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getResolution')
	.sect	'.debug_line'
.L204:
	.word	.L939-.L938
.L938:
	.half	3
	.word	.L941-.L940
.L940:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L941:
	.byte	5,18,7,0,5,2
	.word	.L115
	.byte	3,137,1,1,5,5,9
	.half	.L942-.L115
	.byte	1,5,1,9
	.half	.L11-.L942
	.byte	3,1,1,7,9
	.half	.L206-.L11
	.byte	0,1,1
.L939:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getResolution')
	.sect	'.debug_ranges'
.L205:
	.word	-1,.L115,0,.L206-.L115,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getSensorType')
	.sect	'.debug_info'
.L207:
	.word	337
	.half	3
	.word	.L208
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L210,.L209
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getSensorType',0,1,142,1,25
	.word	.L580
	.byte	1,1,1
	.word	.L117,.L581,.L116
	.byte	4
	.byte	'driver',0,1,142,1,74
	.word	.L279,.L582
	.byte	5
	.word	.L117,.L581
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getSensorType')
	.sect	'.debug_abbrev'
.L208:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getSensorType')
	.sect	'.debug_line'
.L209:
	.word	.L944-.L943
.L943:
	.half	3
	.word	.L946-.L945
.L945:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L946:
	.byte	5,5,7,0,5,2
	.word	.L117
	.byte	3,143,1,1,5,12,7,9
	.half	.L12-.L117
	.byte	3,1,1,5,5,9
	.half	.L947-.L12
	.byte	1,5,1,9
	.half	.L13-.L947
	.byte	3,1,1,7,9
	.half	.L211-.L13
	.byte	0,1,1
.L944:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getSensorType')
	.sect	'.debug_ranges'
.L210:
	.word	-1,.L117,0,.L211-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getSpeed')
	.sect	'.debug_info'
.L212:
	.word	332
	.half	3
	.word	.L213
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L215,.L214
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getSpeed',0,1,149,1,9
	.word	.L557
	.byte	1,1,1
	.word	.L119,.L583,.L118
	.byte	4
	.byte	'driver',0,1,149,1,53
	.word	.L279,.L584
	.byte	5
	.word	.L119,.L583
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getSpeed')
	.sect	'.debug_abbrev'
.L213:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getSpeed')
	.sect	'.debug_line'
.L214:
	.word	.L949-.L948
.L948:
	.half	3
	.word	.L951-.L950
.L950:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L951:
	.byte	5,18,7,0,5,2
	.word	.L119
	.byte	3,150,1,1,5,5,9
	.half	.L952-.L119
	.byte	1,5,1,9
	.half	.L14-.L952
	.byte	3,1,1,7,9
	.half	.L216-.L14
	.byte	0,1,1
.L949:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getSpeed')
	.sect	'.debug_ranges'
.L215:
	.word	-1,.L119,0,.L216-.L119,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_getTurn')
	.sect	'.debug_info'
.L217:
	.word	331
	.half	3
	.word	.L218
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L220,.L219
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_getTurn',0,1,155,1,8
	.word	.L566
	.byte	1,1,1
	.word	.L121,.L585,.L120
	.byte	4
	.byte	'driver',0,1,155,1,51
	.word	.L279,.L586
	.byte	5
	.word	.L121,.L585
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_getTurn')
	.sect	'.debug_abbrev'
.L218:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_getTurn')
	.sect	'.debug_line'
.L219:
	.word	.L954-.L953
.L953:
	.half	3
	.word	.L956-.L955
.L955:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L956:
	.byte	5,18,7,0,5,2
	.word	.L121
	.byte	3,156,1,1,5,5,9
	.half	.L957-.L121
	.byte	1,5,1,9
	.half	.L15-.L957
	.byte	3,1,1,7,9
	.half	.L221-.L15
	.byte	0,1,1
.L954:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_getTurn')
	.sect	'.debug_ranges'
.L220:
	.word	-1,.L121,0,.L221-.L121,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_onZeroIrq')
	.sect	'.debug_info'
.L222:
	.word	329
	.half	3
	.word	.L223
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L225,.L224
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_onZeroIrq',0,1,201,2,6,1,1,1
	.word	.L127,.L587,.L126
	.byte	4
	.byte	'driver',0,1,201,2,51
	.word	.L279,.L588
	.byte	5
	.word	.L127,.L587
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_onZeroIrq')
	.sect	'.debug_abbrev'
.L223:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_onZeroIrq')
	.sect	'.debug_line'
.L224:
	.word	.L959-.L958
.L958:
	.half	3
	.word	.L961-.L960
.L960:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L961:
	.byte	5,25,7,0,5,2
	.word	.L127
	.byte	3,202,2,1,5,5,9
	.half	.L962-.L127
	.byte	1,5,25,7,9
	.half	.L963-.L962
	.byte	3,2,1,5,42,9
	.half	.L964-.L963
	.byte	1,5,15,9
	.half	.L60-.L964
	.byte	3,3,1,5,5,9
	.half	.L965-.L60
	.byte	1,5,15,7,9
	.half	.L966-.L965
	.byte	3,2,1,5,21,9
	.half	.L967-.L966
	.byte	1,5,23,9
	.half	.L968-.L967
	.byte	1,5,15,9
	.half	.L61-.L968
	.byte	3,4,1,5,21,9
	.half	.L969-.L61
	.byte	1,5,1,9
	.half	.L62-.L969
	.byte	3,2,1,7,9
	.half	.L226-.L62
	.byte	0,1,1
.L959:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_onZeroIrq')
	.sect	'.debug_ranges'
.L225:
	.word	-1,.L127,0,.L226-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_reset')
	.sect	'.debug_info'
.L227:
	.word	325
	.half	3
	.word	.L228
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L230,.L229
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_reset',0,1,219,2,6,1,1,1
	.word	.L129,.L589,.L128
	.byte	4
	.byte	'driver',0,1,219,2,47
	.word	.L279,.L590
	.byte	5
	.word	.L129,.L589
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_reset')
	.sect	'.debug_abbrev'
.L228:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_reset')
	.sect	'.debug_line'
.L229:
	.word	.L971-.L970
.L970:
	.half	3
	.word	.L973-.L972
.L972:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L973:
	.byte	5,40,7,0,5,2
	.word	.L129
	.byte	3,220,2,1,5,38,9
	.half	.L974-.L129
	.byte	1,5,40,9
	.half	.L975-.L974
	.byte	3,1,1,5,38,9
	.half	.L976-.L975
	.byte	1,5,40,9
	.half	.L977-.L976
	.byte	3,1,1,5,38,9
	.half	.L978-.L977
	.byte	1,5,40,9
	.half	.L979-.L978
	.byte	3,1,1,5,38,9
	.half	.L980-.L979
	.byte	1,5,21,9
	.half	.L981-.L980
	.byte	3,1,1,5,38,9
	.half	.L982-.L981
	.byte	1,5,1,9
	.half	.L983-.L982
	.byte	3,1,1,7,9
	.half	.L231-.L983
	.byte	0,1,1
.L971:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_reset')
	.sect	'.debug_ranges'
.L230:
	.word	-1,.L129,0,.L231-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_resetFaults')
	.sect	'.debug_info'
.L232:
	.word	352
	.half	3
	.word	.L233
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L235,.L234
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_resetFaults',0,1,229,2,6,1,1,1
	.word	.L131,.L591,.L130
	.byte	4
	.byte	'driver',0,1,229,2,53
	.word	.L279,.L592
	.byte	5
	.word	.L131,.L591
	.byte	6
	.byte	'status',0,1,231,2,25
	.word	.L563,.L593
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_resetFaults')
	.sect	'.debug_abbrev'
.L233:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_resetFaults')
	.sect	'.debug_line'
.L234:
	.word	.L985-.L984
.L984:
	.half	3
	.word	.L987-.L986
.L986:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L987:
	.byte	5,30,7,0,5,2
	.word	.L131
	.byte	3,231,2,1,5,48,9
	.half	.L653-.L131
	.byte	3,1,1,5,30,9
	.half	.L988-.L653
	.byte	1,9
	.half	.L989-.L988
	.byte	3,1,1,5,1,9
	.half	.L990-.L989
	.byte	3,1,1,7,9
	.half	.L236-.L990
	.byte	0,1,1
.L985:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_resetFaults')
	.sect	'.debug_ranges'
.L235:
	.word	-1,.L131,0,.L236-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_setOffset')
	.sect	'.debug_info'
.L237:
	.word	349
	.half	3
	.word	.L238
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L240,.L239
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_setOffset',0,1,238,2,6,1,1,1
	.word	.L133,.L594,.L132
	.byte	4
	.byte	'driver',0,1,238,2,51
	.word	.L279,.L595
	.byte	4
	.byte	'offset',0,1,238,2,66
	.word	.L566,.L596
	.byte	5
	.word	.L133,.L594
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_setOffset')
	.sect	'.debug_abbrev'
.L238:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_setOffset')
	.sect	'.debug_line'
.L239:
	.word	.L992-.L991
.L991:
	.half	3
	.word	.L994-.L993
.L993:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L994:
	.byte	5,38,7,0,5,2
	.word	.L133
	.byte	3,239,2,1,5,21,9
	.half	.L995-.L133
	.byte	3,1,1,5,38,9
	.half	.L996-.L995
	.byte	1,5,1,9
	.half	.L997-.L996
	.byte	3,1,1,7,9
	.half	.L241-.L997
	.byte	0,1,1
.L992:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_setOffset')
	.sect	'.debug_ranges'
.L240:
	.word	-1,.L133,0,.L241-.L133,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_setRefreshPeriod')
	.sect	'.debug_info'
.L242:
	.word	362
	.half	3
	.word	.L243
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L245,.L244
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_setRefreshPeriod',0,1,245,2,6,1,1,1
	.word	.L135,.L597,.L134
	.byte	4
	.byte	'driver',0,1,245,2,58
	.word	.L279,.L598
	.byte	4
	.byte	'updatePeriod',0,1,245,2,74
	.word	.L557,.L599
	.byte	5
	.word	.L135,.L597
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_setRefreshPeriod')
	.sect	'.debug_abbrev'
.L243:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_setRefreshPeriod')
	.sect	'.debug_line'
.L244:
	.word	.L999-.L998
.L998:
	.half	3
	.word	.L1001-.L1000
.L1000:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L1001:
	.byte	5,6,7,0,5,2
	.word	.L135
	.byte	3,244,2,1,5,36,9
	.half	.L657-.L135
	.byte	3,2,1,5,43,9
	.half	.L1002-.L657
	.byte	3,1,1,5,61,9
	.half	.L1003-.L1002
	.byte	1,5,53,9
	.half	.L1004-.L1003
	.byte	1,5,76,9
	.half	.L1005-.L1004
	.byte	1,5,74,9
	.half	.L1006-.L1005
	.byte	1,5,36,9
	.half	.L1007-.L1006
	.byte	1,5,44,9
	.half	.L1008-.L1007
	.byte	3,1,1,5,73,9
	.half	.L1009-.L1008
	.byte	1,5,65,9
	.half	.L1010-.L1009
	.byte	1,5,86,9
	.half	.L1011-.L1010
	.byte	1,5,108,9
	.half	.L1012-.L1011
	.byte	1,5,101,9
	.half	.L1013-.L1012
	.byte	1,5,36,9
	.half	.L1014-.L1013
	.byte	1,5,1,9
	.half	.L1015-.L1014
	.byte	3,1,1,7,9
	.half	.L246-.L1015
	.byte	0,1,1
.L999:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_setRefreshPeriod')
	.sect	'.debug_ranges'
.L245:
	.word	-1,.L135,0,.L246-.L135,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_update')
	.sect	'.debug_info'
.L247:
	.word	326
	.half	3
	.word	.L248
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L250,.L249
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_update',0,1,159,3,6,1,1,1
	.word	.L139,.L600,.L138
	.byte	4
	.byte	'driver',0,1,159,3,48
	.word	.L279,.L601
	.byte	5
	.word	.L139,.L600
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_update')
	.sect	'.debug_abbrev'
.L248:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_update')
	.sect	'.debug_line'
.L249:
	.word	.L1017-.L1016
.L1016:
	.half	3
	.word	.L1019-.L1018
.L1018:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L1019:
	.byte	5,11,7,0,5,2
	.word	.L139
	.byte	3,160,3,1,5,20,9
	.half	.L1020-.L139
	.byte	1,5,1,9
	.half	.L663-.L1020
	.byte	3,1,1,7,9
	.half	.L251-.L663
	.byte	0,1,1
.L1017:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_update')
	.sect	'.debug_ranges'
.L250:
	.word	-1,.L139,0,.L251-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_stdIfPosInit')
	.sect	'.debug_info'
.L252:
	.word	355
	.half	3
	.word	.L253
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L255,.L254
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_stdIfPosInit',0,1,253,2,9
	.word	.L277
	.byte	1,1,1
	.word	.L137,.L602,.L136
	.byte	4
	.byte	'stdif',0,1,253,2,53
	.word	.L603,.L604
	.byte	4
	.byte	'driver',0,1,253,2,78
	.word	.L279,.L605
	.byte	5
	.word	.L137,.L602
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_stdIfPosInit')
	.sect	'.debug_abbrev'
.L253:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_stdIfPosInit')
	.sect	'.debug_line'
.L254:
	.word	.L1022-.L1021
.L1021:
	.half	3
	.word	.L1024-.L1023
.L1023:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L1024:
	.byte	5,9,7,0,5,2
	.word	.L137
	.byte	3,252,2,1,5,19,9
	.half	.L662-.L137
	.byte	3,3,1,5,22,9
	.half	.L1025-.L662
	.byte	1,5,19,9
	.half	.L660-.L1025
	.byte	3,3,1,5,29,9
	.half	.L1026-.L660
	.byte	3,4,1,5,28,9
	.half	.L1027-.L1026
	.byte	1,5,29,9
	.half	.L1028-.L1027
	.byte	3,1,1,5,28,9
	.half	.L1029-.L1028
	.byte	1,5,26,9
	.half	.L1030-.L1029
	.byte	3,1,1,5,25,9
	.half	.L1031-.L1030
	.byte	1,5,29,9
	.half	.L1032-.L1031
	.byte	3,1,1,5,28,9
	.half	.L1033-.L1032
	.byte	1,5,23,9
	.half	.L1034-.L1033
	.byte	3,1,1,5,22,9
	.half	.L1035-.L1034
	.byte	1,5,32,9
	.half	.L1036-.L1035
	.byte	3,1,1,5,31,9
	.half	.L1037-.L1036
	.byte	1,5,25,9
	.half	.L1038-.L1037
	.byte	3,1,1,5,24,9
	.half	.L1039-.L1038
	.byte	1,5,27,9
	.half	.L1040-.L1039
	.byte	3,1,1,5,26,9
	.half	.L1041-.L1040
	.byte	1,5,29,9
	.half	.L1042-.L1041
	.byte	3,1,1,5,28,9
	.half	.L1043-.L1042
	.byte	1,5,29,9
	.half	.L1044-.L1043
	.byte	3,1,1,5,28,9
	.half	.L1045-.L1044
	.byte	1,5,32,9
	.half	.L1046-.L1045
	.byte	3,1,1,5,31,9
	.half	.L1047-.L1046
	.byte	1,5,20,9
	.half	.L1048-.L1047
	.byte	3,1,1,5,19,9
	.half	.L1049-.L1048
	.byte	1,5,25,9
	.half	.L1050-.L1049
	.byte	3,1,1,5,24,9
	.half	.L1051-.L1050
	.byte	1,5,29,9
	.half	.L1052-.L1051
	.byte	3,1,1,5,28,9
	.half	.L1053-.L1052
	.byte	1,5,21,9
	.half	.L1054-.L1053
	.byte	3,1,1,5,20,9
	.half	.L1055-.L1054
	.byte	1,5,23,9
	.half	.L1056-.L1055
	.byte	3,1,1,5,22,9
	.half	.L1057-.L1056
	.byte	1,5,29,9
	.half	.L1058-.L1057
	.byte	3,1,1,5,28,9
	.half	.L1059-.L1058
	.byte	1,5,29,9
	.half	.L1060-.L1059
	.byte	3,1,1,5,28,9
	.half	.L1061-.L1060
	.byte	1,5,12,9
	.half	.L1062-.L1061
	.byte	3,3,1,5,5,9
	.half	.L1063-.L1062
	.byte	1,5,1,9
	.half	.L63-.L1063
	.byte	3,1,1,7,9
	.half	.L256-.L63
	.byte	0,1,1
.L1022:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_stdIfPosInit')
	.sect	'.debug_ranges'
.L255:
	.word	-1,.L137,0,.L256-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_updateFromT2')
	.sect	'.debug_info'
.L257:
	.word	376
	.half	3
	.word	.L258
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L260,.L259
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_updateFromT2',0,1,165,3,17,1,1
	.word	.L141,.L606,.L140
	.byte	4
	.byte	'driver',0,1,165,3,65
	.word	.L279,.L607
	.byte	5
	.word	.L141,.L606
	.byte	6
	.byte	'gpt12',0,1,167,3,16
	.word	.L284,.L608
	.byte	6
	.byte	'newPosition',0,1,168,3,16
	.word	.L566,.L609
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_updateFromT2')
	.sect	'.debug_abbrev'
.L258:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_updateFromT2')
	.sect	'.debug_line'
.L259:
	.word	.L1065-.L1064
.L1064:
	.half	3
	.word	.L1067-.L1066
.L1066:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L1067:
	.byte	5,17,7,0,5,2
	.word	.L141
	.byte	3,164,3,1,5,30,9
	.half	.L665-.L141
	.byte	3,2,1,5,39,9
	.half	.L666-.L665
	.byte	3,2,1,5,25,9
	.half	.L1068-.L666
	.byte	1,5,47,7,9
	.half	.L1069-.L1068
	.byte	1,5,75,9
	.half	.L1070-.L1069
	.byte	1,5,47,9
	.half	.L64-.L1070
	.byte	1,5,23,9
	.half	.L65-.L64
	.byte	1,5,34,9
	.half	.L1071-.L65
	.byte	3,2,1,5,46,9
	.half	.L667-.L1071
	.byte	3,2,1,5,38,9
	.half	.L1072-.L667
	.byte	1,5,30,9
	.half	.L668-.L1072
	.byte	3,2,1,5,5,9
	.half	.L1073-.L668
	.byte	1,5,30,7,9
	.half	.L1074-.L1073
	.byte	3,2,1,5,21,9
	.half	.L1075-.L1074
	.byte	1,5,42,9
	.half	.L1076-.L1075
	.byte	1,5,10,9
	.half	.L66-.L1076
	.byte	3,2,1,5,44,7,9
	.half	.L1077-.L66
	.byte	3,2,1,5,36,9
	.half	.L1078-.L1077
	.byte	1,5,48,9
	.half	.L67-.L1078
	.byte	3,3,1,5,25,9
	.half	.L664-.L67
	.byte	3,1,1,5,1,9
	.half	.L1079-.L664
	.byte	3,1,1,7,9
	.half	.L261-.L1079
	.byte	0,1,1
.L1065:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_updateFromT2')
	.sect	'.debug_ranges'
.L260:
	.word	-1,.L141,0,.L261-.L141,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_updateFromT3')
	.sect	'.debug_info'
.L262:
	.word	376
	.half	3
	.word	.L263
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L265,.L264
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_updateFromT3',0,1,189,3,17,1,1
	.word	.L143,.L610,.L142
	.byte	4
	.byte	'driver',0,1,189,3,65
	.word	.L279,.L611
	.byte	5
	.word	.L143,.L610
	.byte	6
	.byte	'gpt12',0,1,191,3,16
	.word	.L284,.L612
	.byte	6
	.byte	'newPosition',0,1,192,3,16
	.word	.L566,.L613
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_updateFromT3')
	.sect	'.debug_abbrev'
.L263:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_updateFromT3')
	.sect	'.debug_line'
.L264:
	.word	.L1081-.L1080
.L1080:
	.half	3
	.word	.L1083-.L1082
.L1082:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L1083:
	.byte	5,17,7,0,5,2
	.word	.L143
	.byte	3,188,3,1,5,30,9
	.half	.L671-.L143
	.byte	3,2,1,5,39,9
	.half	.L672-.L671
	.byte	3,2,1,5,25,9
	.half	.L1084-.L672
	.byte	1,5,47,7,9
	.half	.L1085-.L1084
	.byte	1,5,75,9
	.half	.L1086-.L1085
	.byte	1,5,47,9
	.half	.L69-.L1086
	.byte	1,5,23,9
	.half	.L70-.L69
	.byte	1,5,34,9
	.half	.L1087-.L70
	.byte	3,2,1,5,46,9
	.half	.L673-.L1087
	.byte	3,2,1,5,38,9
	.half	.L1088-.L673
	.byte	1,5,30,9
	.half	.L674-.L1088
	.byte	3,2,1,5,5,9
	.half	.L1089-.L674
	.byte	1,5,30,7,9
	.half	.L1090-.L1089
	.byte	3,2,1,5,21,9
	.half	.L1091-.L1090
	.byte	1,5,42,9
	.half	.L1092-.L1091
	.byte	1,5,10,9
	.half	.L71-.L1092
	.byte	3,2,1,5,44,7,9
	.half	.L1093-.L71
	.byte	3,2,1,5,36,9
	.half	.L1094-.L1093
	.byte	1,5,48,9
	.half	.L72-.L1094
	.byte	3,3,1,5,25,9
	.half	.L670-.L72
	.byte	3,1,1,5,1,9
	.half	.L1095-.L670
	.byte	3,1,1,7,9
	.half	.L266-.L1095
	.byte	0,1,1
.L1081:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_updateFromT3')
	.sect	'.debug_ranges'
.L265:
	.word	-1,.L143,0,.L266-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT2')
	.sect	'.debug_info'
.L267:
	.word	399
	.half	3
	.word	.L268
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L270,.L269
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_updateSpeedFromT2',0,1,213,3,17,1,1
	.word	.L145,.L614,.L144
	.byte	4
	.byte	'driver',0,1,213,3,70
	.word	.L279,.L615
	.byte	4
	.byte	'newPosition',0,1,213,3,85
	.word	.L566,.L616
	.byte	5
	.word	.L145,.L614
	.byte	6
	.byte	'speed',0,1,215,3,13
	.word	.L557,.L617
	.byte	6
	.byte	'diff',0,1,216,3,13
	.word	.L566,.L618
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT2')
	.sect	'.debug_abbrev'
.L268:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT2')
	.sect	'.debug_line'
.L269:
	.word	.L1097-.L1096
.L1096:
	.half	3
	.word	.L1099-.L1098
.L1098:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0,0
.L1099:
	.byte	5,17,7,0,5,2
	.word	.L145
	.byte	3,212,3,1,5,15,9
	.half	.L678-.L145
	.byte	3,5,1,5,5,9
	.half	.L1100-.L678
	.byte	1,5,36,7,9
	.half	.L1101-.L1100
	.byte	3,2,1,5,28,9
	.half	.L679-.L1101
	.byte	1,5,49,9
	.half	.L677-.L679
	.byte	1,5,22,9
	.half	.L74-.L677
	.byte	3,4,1,5,36,9
	.half	.L1102-.L74
	.byte	1,5,5,9
	.half	.L75-.L1102
	.byte	3,3,1,5,23,7,9
	.half	.L1103-.L75
	.byte	3,2,1,5,14,9
	.half	.L1104-.L1103
	.byte	1,5,13,9
	.half	.L76-.L1104
	.byte	3,3,1,5,26,9
	.half	.L1105-.L76
	.byte	1,5,18,9
	.half	.L1106-.L1105
	.byte	1,5,19,9
	.half	.L680-.L1106
	.byte	3,2,1,5,13,9
	.half	.L1107-.L680
	.byte	1,5,67,7,9
	.half	.L1108-.L1107
	.byte	1,5,69,9
	.half	.L77-.L1108
	.byte	1,5,15,9
	.half	.L78-.L77
	.byte	3,2,1,5,5,9
	.half	.L1109-.L78
	.byte	1,5,53,7,9
	.half	.L1110-.L1109
	.byte	3,2,1,5,65,9
	.half	.L676-.L1110
	.byte	1,5,23,9
	.half	.L681-.L676
	.byte	1,5,71,9
	.half	.L1111-.L681
	.byte	1,5,23,9
	.half	.L79-.L1111
	.byte	3,4,1,5,1,9
	.half	.L80-.L79
	.byte	3,2,1,7,9
	.half	.L271-.L80
	.byte	0,1,1
.L1097:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT2')
	.sect	'.debug_ranges'
.L270:
	.word	-1,.L145,0,.L271-.L145,0,0
	.sdecl	'.debug_info',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT3')
	.sect	'.debug_info'
.L272:
	.word	549
	.half	3
	.word	.L273
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L275,.L274
	.byte	2
	.word	.L148
	.byte	3
	.byte	'IfxGpt12_IncrEnc_updateSpeedFromT3',0,1,247,3,17,1,1
	.word	.L147,.L619,.L146
	.byte	4
	.byte	'driver',0,1,247,3,70
	.word	.L279,.L620
	.byte	4
	.byte	'newPosition',0,1,247,3,85
	.word	.L566,.L621
	.byte	5
	.word	.L147,.L619
	.byte	6
	.byte	'speed',0,1,249,3,13
	.word	.L557,.L622
	.byte	6
	.byte	'diff',0,1,250,3,13
	.word	.L566,.L623
	.byte	5
	.word	.L84,.L85
	.byte	6
	.byte	'gpt12',0,1,144,4,32
	.word	.L284,.L624
	.byte	6
	.byte	'srcT5',0,1,145,4,32
	.word	.L392,.L625
	.byte	7
	.word	.L626,.L627,.L87
	.byte	8
	.word	.L628,.L629
	.byte	9
	.word	.L630,.L627,.L87
	.byte	0,5
	.word	.L631,.L92
	.byte	6
	.byte	'srcCap',0,1,149,4,36
	.word	.L392,.L632
	.byte	7
	.word	.L633,.L631,.L90
	.byte	8
	.word	.L634,.L635
	.byte	9
	.word	.L636,.L631,.L90
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT3')
	.sect	'.debug_abbrev'
.L273:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18,1,0,0
	.byte	0
	.sdecl	'.debug_line',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT3')
	.sect	'.debug_line'
.L274:
	.word	.L1113-.L1112
.L1112:
	.half	3
	.word	.L1115-.L1114
.L1114:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc/IfxGpt12_IncrEnc.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0,0
.L1115:
	.byte	5,17,7,0,5,2
	.word	.L147
	.byte	3,246,3,1,5,15,9
	.half	.L684-.L147
	.byte	3,5,1,5,5,9
	.half	.L1116-.L684
	.byte	1,5,36,7,9
	.half	.L1117-.L1116
	.byte	3,2,1,5,28,9
	.half	.L685-.L1117
	.byte	1,5,49,9
	.half	.L683-.L685
	.byte	1,5,22,9
	.half	.L81-.L683
	.byte	3,4,1,5,36,9
	.half	.L1118-.L81
	.byte	1,5,5,9
	.half	.L82-.L1118
	.byte	3,3,1,5,23,7,9
	.half	.L1119-.L82
	.byte	3,2,1,5,14,9
	.half	.L1120-.L1119
	.byte	1,5,22,9
	.half	.L83-.L1120
	.byte	3,3,1,5,5,9
	.half	.L1121-.L83
	.byte	1,5,17,7,9
	.half	.L1122-.L1121
	.byte	3,2,1,5,30,9
	.half	.L1123-.L1122
	.byte	1,5,22,9
	.half	.L1124-.L1123
	.byte	1,5,52,9
	.half	.L686-.L1124
	.byte	1,5,46,9
	.half	.L84-.L686
	.byte	3,4,1,4,2,5,5,9
	.half	.L627-.L84
	.byte	3,224,6,1,5,38,7,9
	.half	.L86-.L627
	.byte	3,1,1,5,5,9
	.half	.L1125-.L86
	.byte	1,4,1,5,21,9
	.half	.L87-.L1125
	.byte	3,162,121,1,5,9,9
	.half	.L1126-.L87
	.byte	1,4,2,5,5,7,9
	.half	.L631-.L1126
	.byte	3,148,8,1,5,38,7,9
	.half	.L89-.L631
	.byte	3,1,1,5,5,9
	.half	.L1127-.L89
	.byte	1,4,1,5,26,9
	.half	.L90-.L1127
	.byte	3,240,119,1,5,13,9
	.half	.L1128-.L90
	.byte	1,5,26,7,9
	.half	.L1129-.L1128
	.byte	3,3,1,5,32,9
	.half	.L1130-.L1129
	.byte	1,5,40,9
	.half	.L1131-.L1130
	.byte	3,1,1,5,78,9
	.half	.L1132-.L1131
	.byte	1,5,61,9
	.half	.L1133-.L1132
	.byte	1,5,35,9
	.half	.L687-.L1133
	.byte	3,127,1,5,31,9
	.half	.L91-.L687
	.byte	3,5,1,5,13,9
	.half	.L92-.L91
	.byte	3,126,1,5,21,9
	.half	.L88-.L92
	.byte	3,8,1,5,27,9
	.half	.L1134-.L88
	.byte	1,9
	.half	.L1135-.L1134
	.byte	3,1,1,5,19,9
	.half	.L85-.L1135
	.byte	3,4,1,5,13,9
	.half	.L1136-.L85
	.byte	1,5,67,7,9
	.half	.L1137-.L1136
	.byte	1,5,69,9
	.half	.L94-.L1137
	.byte	1,5,15,9
	.half	.L95-.L94
	.byte	3,2,1,5,5,9
	.half	.L1138-.L95
	.byte	1,5,53,7,9
	.half	.L1139-.L1138
	.byte	3,2,1,5,65,9
	.half	.L1140-.L1139
	.byte	1,5,23,9
	.half	.L688-.L1140
	.byte	1,5,71,9
	.half	.L1141-.L688
	.byte	1,5,23,9
	.half	.L96-.L1141
	.byte	3,4,1,5,1,9
	.half	.L97-.L96
	.byte	3,2,1,7,9
	.half	.L276-.L97
	.byte	0,1,1
.L1113:
	.sdecl	'.debug_ranges',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT3')
	.sect	'.debug_ranges'
.L275:
	.word	-1,.L147,0,.L276-.L147,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getAbsolutePosition')
	.sect	'.debug_loc'
.L98:
	.word	-1,.L99,0,.L558-.L99
	.half	2
	.byte	138,0
	.word	0,0
.L559:
	.word	-1,.L99,0,.L637-.L99
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getDirection')
	.sect	'.debug_loc'
.L100:
	.word	-1,.L101,0,.L561-.L101
	.half	2
	.byte	138,0
	.word	0,0
.L562:
	.word	-1,.L101,0,.L561-.L101
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getFault')
	.sect	'.debug_loc'
.L102:
	.word	-1,.L103,0,.L564-.L103
	.half	2
	.byte	138,0
	.word	0,0
.L565:
	.word	-1,.L103,0,.L564-.L103
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getOffset')
	.sect	'.debug_loc'
.L104:
	.word	-1,.L105,0,.L567-.L105
	.half	2
	.byte	138,0
	.word	0,0
.L568:
	.word	-1,.L105,0,.L567-.L105
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getPeriodPerRotation')
	.sect	'.debug_loc'
.L106:
	.word	-1,.L107,0,.L570-.L107
	.half	2
	.byte	138,0
	.word	0,0
.L571:
	.word	-1,.L107,0,.L570-.L107
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getPosition')
	.sect	'.debug_loc'
.L108:
	.word	-1,.L109,0,.L572-.L109
	.half	2
	.byte	138,0
	.word	0,0
.L573:
	.word	-1,.L109,0,.L572-.L109
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getRawPosition')
	.sect	'.debug_loc'
.L110:
	.word	-1,.L111,0,.L574-.L111
	.half	2
	.byte	138,0
	.word	0,0
.L575:
	.word	-1,.L111,0,.L574-.L111
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getRefreshPeriod')
	.sect	'.debug_loc'
.L112:
	.word	-1,.L113,0,.L576-.L113
	.half	2
	.byte	138,0
	.word	0,0
.L577:
	.word	-1,.L113,0,.L576-.L113
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getResolution')
	.sect	'.debug_loc'
.L114:
	.word	-1,.L115,0,.L578-.L115
	.half	2
	.byte	138,0
	.word	0,0
.L579:
	.word	-1,.L115,0,.L578-.L115
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getSensorType')
	.sect	'.debug_loc'
.L116:
	.word	-1,.L117,0,.L581-.L117
	.half	2
	.byte	138,0
	.word	0,0
.L582:
	.word	-1,.L117,0,.L581-.L117
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getSpeed')
	.sect	'.debug_loc'
.L118:
	.word	-1,.L119,0,.L583-.L119
	.half	2
	.byte	138,0
	.word	0,0
.L584:
	.word	-1,.L119,0,.L583-.L119
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_getTurn')
	.sect	'.debug_loc'
.L120:
	.word	-1,.L121,0,.L585-.L121
	.half	2
	.byte	138,0
	.word	0,0
.L586:
	.word	-1,.L121,0,.L585-.L121
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_init')
	.sect	'.debug_loc'
.L122:
	.word	-1,.L123,0,.L638-.L123
	.half	2
	.byte	138,0
	.word	.L638-.L123,.L278-.L123
	.half	2
	.byte	138,16
	.word	.L278-.L123,.L278-.L123
	.half	2
	.byte	138,0
	.word	0,0
.L282:
	.word	-1,.L123,0,.L639-.L123
	.half	1
	.byte	101
	.word	.L641-.L123,.L278-.L123
	.half	1
	.byte	111
	.word	0,0
.L381:
	.word	0,0
.L470:
	.word	0,0
.L527:
	.word	0,0
.L317:
	.word	0,0
.L486:
	.word	0,0
.L280:
	.word	-1,.L123,0,.L639-.L123
	.half	1
	.byte	100
	.word	.L640-.L123,.L278-.L123
	.half	1
	.byte	108
	.word	.L644-.L123,.L645-.L123
	.half	1
	.byte	100
	.word	0,0
.L325:
	.word	0,0
.L357:
	.word	0,0
.L365:
	.word	0,0
.L373:
	.word	0,0
.L455:
	.word	0,0
.L462:
	.word	0,0
.L285:
	.word	-1,.L123,.L643-.L123,.L278-.L123
	.half	1
	.byte	109
	.word	.L647-.L123,.L648-.L123
	.half	1
	.byte	100
	.word	0,0
.L533:
	.word	0,0
.L518:
	.word	0,0
.L508:
	.word	0,0
.L500:
	.word	0,0
.L525:
	.word	0,0
.L323:
	.word	0,0
.L331:
	.word	0,0
.L308:
	.word	0,0
.L298:
	.word	0,0
.L290:
	.word	0,0
.L315:
	.word	0,0
.L355:
	.word	0,0
.L363:
	.word	0,0
.L396:
	.word	0,0
.L387:
	.word	0,0
.L347:
	.word	0,0
.L371:
	.word	0,0
.L339:
	.word	0,0
.L379:
	.word	0,0
.L453:
	.word	0,0
.L492:
	.word	0,0
.L437:
	.word	0,0
.L460:
	.word	0,0
.L445:
	.word	0,0
.L476:
	.word	0,0
.L421:
	.word	0,0
.L468:
	.word	0,0
.L484:
	.word	0,0
.L429:
	.word	0,0
.L510:
	.word	0,0
.L300:
	.word	0,0
.L349:
	.word	0,0
.L431:
	.word	0,0
.L552:
	.word	-1,.L123,0,.L278-.L123
	.half	2
	.byte	145,112
	.word	0,0
.L502:
	.word	0,0
.L292:
	.word	0,0
.L341:
	.word	0,0
.L447:
	.word	0,0
.L423:
	.word	0,0
.L406:
	.word	0,0
.L535:
	.word	0,0
.L333:
	.word	0,0
.L389:
	.word	0,0
.L494:
	.word	0,0
.L520:
	.word	0,0
.L310:
	.word	0,0
.L478:
	.word	0,0
.L393:
	.word	-1,.L123,.L646-.L123,.L27-.L123
	.half	1
	.byte	98
	.word	0,0
.L411:
	.word	0,0
.L415:
	.word	0,0
.L402:
	.word	0,0
.L283:
	.word	-1,.L123,.L642-.L123,.L278-.L123
	.half	1
	.byte	88
	.word	.L649-.L123,.L278-.L123
	.half	1
	.byte	82
	.word	0,0
.L439:
	.word	0,0
.L404:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_initConfig')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L125,0,.L553-.L125
	.half	2
	.byte	138,0
	.word	0,0
.L555:
	.word	-1,.L125,0,.L650-.L125
	.half	1
	.byte	100
	.word	.L651-.L125,.L553-.L125
	.half	1
	.byte	111
	.word	0,0
.L556:
	.word	-1,.L125,0,.L650-.L125
	.half	1
	.byte	101
	.word	.L652-.L125,.L553-.L125
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_onZeroIrq')
	.sect	'.debug_loc'
.L126:
	.word	-1,.L127,0,.L587-.L127
	.half	2
	.byte	138,0
	.word	0,0
.L588:
	.word	-1,.L127,0,.L587-.L127
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_reset')
	.sect	'.debug_loc'
.L128:
	.word	-1,.L129,0,.L589-.L129
	.half	2
	.byte	138,0
	.word	0,0
.L590:
	.word	-1,.L129,0,.L589-.L129
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_resetFaults')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L131,0,.L591-.L131
	.half	2
	.byte	138,0
	.word	0,0
.L592:
	.word	-1,.L131,0,.L591-.L131
	.half	1
	.byte	100
	.word	0,0
.L593:
	.word	-1,.L131,.L653-.L131,.L591-.L131
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_setOffset')
	.sect	'.debug_loc'
.L132:
	.word	-1,.L133,0,.L594-.L133
	.half	2
	.byte	138,0
	.word	0,0
.L595:
	.word	-1,.L133,0,.L594-.L133
	.half	1
	.byte	100
	.word	0,0
.L596:
	.word	-1,.L133,0,.L594-.L133
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_setRefreshPeriod')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L135,0,.L597-.L135
	.half	2
	.byte	138,0
	.word	0,0
.L598:
	.word	-1,.L135,0,.L654-.L135
	.half	1
	.byte	100
	.word	.L656-.L135,.L597-.L135
	.half	1
	.byte	111
	.word	0,0
.L599:
	.word	-1,.L135,0,.L655-.L135
	.half	1
	.byte	84
	.word	.L657-.L135,.L597-.L135
	.half	1
	.byte	95
	.word	.L658-.L135,.L659-.L135
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_stdIfPosInit')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L137,0,.L602-.L137
	.half	2
	.byte	138,0
	.word	0,0
.L605:
	.word	-1,.L137,0,.L660-.L137
	.half	1
	.byte	101
	.word	.L662-.L137,.L602-.L137
	.half	1
	.byte	108
	.word	0,0
.L604:
	.word	-1,.L137,0,.L660-.L137
	.half	1
	.byte	100
	.word	.L661-.L137,.L602-.L137
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_update')
	.sect	'.debug_loc'
.L138:
	.word	-1,.L139,0,.L600-.L139
	.half	2
	.byte	138,0
	.word	0,0
.L601:
	.word	-1,.L139,0,.L663-.L139
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_updateFromT2')
	.sect	'.debug_loc'
.L140:
	.word	-1,.L141,0,.L606-.L141
	.half	2
	.byte	138,0
	.word	0,0
.L607:
	.word	-1,.L141,0,.L664-.L141
	.half	1
	.byte	100
	.word	.L665-.L141,.L606-.L141
	.half	1
	.byte	111
	.word	0,0
.L608:
	.word	-1,.L141,.L666-.L141,.L664-.L141
	.half	1
	.byte	98
	.word	0,0
.L609:
	.word	-1,.L141,.L667-.L141,.L668-.L141
	.half	1
	.byte	95
	.word	.L668-.L141,.L606-.L141
	.half	1
	.byte	89
	.word	.L669-.L141,.L664-.L141
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_updateFromT3')
	.sect	'.debug_loc'
.L142:
	.word	-1,.L143,0,.L610-.L143
	.half	2
	.byte	138,0
	.word	0,0
.L611:
	.word	-1,.L143,0,.L670-.L143
	.half	1
	.byte	100
	.word	.L671-.L143,.L610-.L143
	.half	1
	.byte	111
	.word	0,0
.L612:
	.word	-1,.L143,.L672-.L143,.L670-.L143
	.half	1
	.byte	98
	.word	0,0
.L613:
	.word	-1,.L143,.L673-.L143,.L674-.L143
	.half	1
	.byte	95
	.word	.L674-.L143,.L610-.L143
	.half	1
	.byte	89
	.word	.L675-.L143,.L670-.L143
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT2')
	.sect	'.debug_loc'
.L144:
	.word	-1,.L145,0,.L614-.L145
	.half	2
	.byte	138,0
	.word	0,0
.L618:
	.word	-1,.L145,.L679-.L145,.L74-.L145
	.half	1
	.byte	84
	.word	.L75-.L145,.L680-.L145
	.half	1
	.byte	84
	.word	0,0
.L615:
	.word	-1,.L145,0,.L676-.L145
	.half	1
	.byte	100
	.word	.L678-.L145,.L614-.L145
	.half	1
	.byte	111
	.word	.L79-.L145,.L80-.L145
	.half	1
	.byte	100
	.word	0,0
.L616:
	.word	-1,.L145,0,.L677-.L145
	.half	1
	.byte	84
	.word	.L74-.L145,.L75-.L145
	.half	1
	.byte	84
	.word	0,0
.L617:
	.word	-1,.L145,.L680-.L145,.L681-.L145
	.half	1
	.byte	84
	.word	.L79-.L145,.L80-.L145
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT3')
	.sect	'.debug_loc'
.L146:
	.word	-1,.L147,0,.L619-.L147
	.half	2
	.byte	138,0
	.word	0,0
.L623:
	.word	-1,.L147,.L685-.L147,.L81-.L147
	.half	1
	.byte	84
	.word	.L82-.L147,.L686-.L147
	.half	1
	.byte	84
	.word	.L84-.L147,.L687-.L147
	.half	1
	.byte	84
	.word	.L91-.L147,.L92-.L147
	.half	1
	.byte	84
	.word	.L88-.L147,.L85-.L147
	.half	1
	.byte	84
	.word	0,0
.L620:
	.word	-1,.L147,0,.L682-.L147
	.half	1
	.byte	100
	.word	.L684-.L147,.L619-.L147
	.half	1
	.byte	111
	.word	0,0
.L624:
	.word	-1,.L147,.L627-.L147,.L85-.L147
	.half	1
	.byte	98
	.word	0,0
.L629:
	.word	0,0
.L635:
	.word	0,0
.L621:
	.word	-1,.L147,0,.L683-.L147
	.half	1
	.byte	84
	.word	.L81-.L147,.L82-.L147
	.half	1
	.byte	84
	.word	0,0
.L622:
	.word	-1,.L147,.L686-.L147,.L84-.L147
	.half	1
	.byte	84
	.word	.L687-.L147,.L91-.L147
	.half	1
	.byte	84
	.word	.L92-.L147,.L88-.L147
	.half	1
	.byte	84
	.word	.L85-.L147,.L688-.L147
	.half	1
	.byte	84
	.word	.L96-.L147,.L97-.L147
	.half	1
	.byte	84
	.word	0,0
.L632:
	.word	0,0
.L625:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1142:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getAbsolutePosition')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L99,.L558-.L99
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getDirection')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L101,.L561-.L101
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getFault')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L103,.L564-.L103
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getOffset')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L105,.L567-.L105
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getPeriodPerRotation')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L107,.L570-.L107
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getPosition')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L109,.L572-.L109
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getRawPosition')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L111,.L574-.L111
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getRefreshPeriod')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L113,.L576-.L113
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getResolution')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L115,.L578-.L115
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getSensorType')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L117,.L581-.L117
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getSpeed')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L119,.L583-.L119
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_getTurn')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L121,.L585-.L121
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1142,.L123,.L278-.L123
	.byte	4
	.word	(.L638-.L123)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L278-.L638)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_initConfig')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L125,.L553-.L125
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_onZeroIrq')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L127,.L587-.L127
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_reset')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L129,.L589-.L129
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_resetFaults')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L131,.L591-.L131
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_setOffset')
	.sect	'.debug_frame'
	.word	24
	.word	.L1142,.L133,.L594-.L133
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_setRefreshPeriod')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L135,.L597-.L135
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_stdIfPosInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L137,.L602-.L137
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_update')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L139,.L600-.L139
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_updateFromT2')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L141,.L606-.L141
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_updateFromT3')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L143,.L610-.L143
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT2')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L145,.L614-.L145
	.sdecl	'.debug_frame',debug,cluster('IfxGpt12_IncrEnc_updateSpeedFromT3')
	.sect	'.debug_frame'
	.word	12
	.word	.L1142,.L147,.L619-.L147
	; Module end
