/**
 * \file IfxSent_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Sent_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Sent
 * 
 */
#ifndef IFXSENT_BF_H
#define IFXSENT_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Sent_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN0 */
#define IFX_SENT_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN0 */
#define IFX_SENT_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN0 */
#define IFX_SENT_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN10 */
#define IFX_SENT_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN10 */
#define IFX_SENT_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN10 */
#define IFX_SENT_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN11 */
#define IFX_SENT_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN11 */
#define IFX_SENT_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN11 */
#define IFX_SENT_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN12 */
#define IFX_SENT_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN12 */
#define IFX_SENT_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN12 */
#define IFX_SENT_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN13 */
#define IFX_SENT_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN13 */
#define IFX_SENT_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN13 */
#define IFX_SENT_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN14 */
#define IFX_SENT_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN14 */
#define IFX_SENT_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN14 */
#define IFX_SENT_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN15 */
#define IFX_SENT_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN15 */
#define IFX_SENT_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN15 */
#define IFX_SENT_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN16 */
#define IFX_SENT_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN16 */
#define IFX_SENT_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN16 */
#define IFX_SENT_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN17 */
#define IFX_SENT_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN17 */
#define IFX_SENT_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN17 */
#define IFX_SENT_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN18 */
#define IFX_SENT_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN18 */
#define IFX_SENT_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN18 */
#define IFX_SENT_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN19 */
#define IFX_SENT_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN19 */
#define IFX_SENT_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN19 */
#define IFX_SENT_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN1 */
#define IFX_SENT_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN1 */
#define IFX_SENT_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN1 */
#define IFX_SENT_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN20 */
#define IFX_SENT_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN20 */
#define IFX_SENT_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN20 */
#define IFX_SENT_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN21 */
#define IFX_SENT_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN21 */
#define IFX_SENT_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN21 */
#define IFX_SENT_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN22 */
#define IFX_SENT_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN22 */
#define IFX_SENT_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN22 */
#define IFX_SENT_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN23 */
#define IFX_SENT_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN23 */
#define IFX_SENT_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN23 */
#define IFX_SENT_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN24 */
#define IFX_SENT_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN24 */
#define IFX_SENT_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN24 */
#define IFX_SENT_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN25 */
#define IFX_SENT_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN25 */
#define IFX_SENT_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN25 */
#define IFX_SENT_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN26 */
#define IFX_SENT_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN26 */
#define IFX_SENT_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN26 */
#define IFX_SENT_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN27 */
#define IFX_SENT_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN27 */
#define IFX_SENT_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN27 */
#define IFX_SENT_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN28 */
#define IFX_SENT_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN28 */
#define IFX_SENT_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN28 */
#define IFX_SENT_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN29 */
#define IFX_SENT_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN29 */
#define IFX_SENT_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN29 */
#define IFX_SENT_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN2 */
#define IFX_SENT_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN2 */
#define IFX_SENT_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN2 */
#define IFX_SENT_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN30 */
#define IFX_SENT_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN30 */
#define IFX_SENT_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN30 */
#define IFX_SENT_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN31 */
#define IFX_SENT_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN31 */
#define IFX_SENT_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN31 */
#define IFX_SENT_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN3 */
#define IFX_SENT_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN3 */
#define IFX_SENT_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN3 */
#define IFX_SENT_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN4 */
#define IFX_SENT_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN4 */
#define IFX_SENT_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN4 */
#define IFX_SENT_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN5 */
#define IFX_SENT_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN5 */
#define IFX_SENT_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN5 */
#define IFX_SENT_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN6 */
#define IFX_SENT_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN6 */
#define IFX_SENT_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN6 */
#define IFX_SENT_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN7 */
#define IFX_SENT_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN7 */
#define IFX_SENT_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN7 */
#define IFX_SENT_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN8 */
#define IFX_SENT_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN8 */
#define IFX_SENT_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN8 */
#define IFX_SENT_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_SENT_ACCEN0_Bits.EN9 */
#define IFX_SENT_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_SENT_ACCEN0_Bits.EN9 */
#define IFX_SENT_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_ACCEN0_Bits.EN9 */
#define IFX_SENT_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_SENT_CH_CFDR_Bits.DIV */
#define IFX_SENT_CH_CFDR_DIV_LEN (12u)

/** \brief  Mask for Ifx_SENT_CH_CFDR_Bits.DIV */
#define IFX_SENT_CH_CFDR_DIV_MSK (0xfffu)

/** \brief  Offset for Ifx_SENT_CH_CFDR_Bits.DIV */
#define IFX_SENT_CH_CFDR_DIV_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_CFDR_Bits.DIVM */
#define IFX_SENT_CH_CFDR_DIVM_LEN (12u)

/** \brief  Mask for Ifx_SENT_CH_CFDR_Bits.DIVM */
#define IFX_SENT_CH_CFDR_DIVM_MSK (0xfffu)

/** \brief  Offset for Ifx_SENT_CH_CFDR_Bits.DIVM */
#define IFX_SENT_CH_CFDR_DIVM_OFF (16u)

/** \brief  Length for Ifx_SENT_CH_CPDR_Bits.PDIV */
#define IFX_SENT_CH_CPDR_PDIV_LEN (12u)

/** \brief  Mask for Ifx_SENT_CH_CPDR_Bits.PDIV */
#define IFX_SENT_CH_CPDR_PDIV_MSK (0xfffu)

/** \brief  Offset for Ifx_SENT_CH_CPDR_Bits.PDIV */
#define IFX_SENT_CH_CPDR_PDIV_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.ERRI */
#define IFX_SENT_CH_INP_ERRI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.ERRI */
#define IFX_SENT_CH_INP_ERRI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.ERRI */
#define IFX_SENT_CH_INP_ERRI_OFF (20u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.RBI */
#define IFX_SENT_CH_INP_RBI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.RBI */
#define IFX_SENT_CH_INP_RBI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.RBI */
#define IFX_SENT_CH_INP_RBI_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.RDI */
#define IFX_SENT_CH_INP_RDI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.RDI */
#define IFX_SENT_CH_INP_RDI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.RDI */
#define IFX_SENT_CH_INP_RDI_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.RSI */
#define IFX_SENT_CH_INP_RSI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.RSI */
#define IFX_SENT_CH_INP_RSI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.RSI */
#define IFX_SENT_CH_INP_RSI_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.SDI */
#define IFX_SENT_CH_INP_SDI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.SDI */
#define IFX_SENT_CH_INP_SDI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.SDI */
#define IFX_SENT_CH_INP_SDI_OFF (24u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.TBI */
#define IFX_SENT_CH_INP_TBI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.TBI */
#define IFX_SENT_CH_INP_TBI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.TBI */
#define IFX_SENT_CH_INP_TBI_OFF (16u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.TDI */
#define IFX_SENT_CH_INP_TDI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.TDI */
#define IFX_SENT_CH_INP_TDI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.TDI */
#define IFX_SENT_CH_INP_TDI_OFF (12u)

/** \brief  Length for Ifx_SENT_CH_INP_Bits.WDI */
#define IFX_SENT_CH_INP_WDI_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_INP_Bits.WDI */
#define IFX_SENT_CH_INP_WDI_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_INP_Bits.WDI */
#define IFX_SENT_CH_INP_WDI_OFF (28u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.CRCI */
#define IFX_SENT_CH_INTCLR_CRCI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.CRCI */
#define IFX_SENT_CH_INTCLR_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.CRCI */
#define IFX_SENT_CH_INTCLR_CRCI_OFF (9u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.FDI */
#define IFX_SENT_CH_INTCLR_FDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.FDI */
#define IFX_SENT_CH_INTCLR_FDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.FDI */
#define IFX_SENT_CH_INTCLR_FDI_OFF (6u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.FRI */
#define IFX_SENT_CH_INTCLR_FRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.FRI */
#define IFX_SENT_CH_INTCLR_FRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.FRI */
#define IFX_SENT_CH_INTCLR_FRI_OFF (5u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.NNI */
#define IFX_SENT_CH_INTCLR_NNI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.NNI */
#define IFX_SENT_CH_INTCLR_NNI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.NNI */
#define IFX_SENT_CH_INTCLR_NNI_OFF (7u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.NVI */
#define IFX_SENT_CH_INTCLR_NVI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.NVI */
#define IFX_SENT_CH_INTCLR_NVI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.NVI */
#define IFX_SENT_CH_INTCLR_NVI_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.RBI */
#define IFX_SENT_CH_INTCLR_RBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.RBI */
#define IFX_SENT_CH_INTCLR_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.RBI */
#define IFX_SENT_CH_INTCLR_RBI_OFF (2u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.RDI */
#define IFX_SENT_CH_INTCLR_RDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.RDI */
#define IFX_SENT_CH_INTCLR_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.RDI */
#define IFX_SENT_CH_INTCLR_RDI_OFF (1u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.RSI */
#define IFX_SENT_CH_INTCLR_RSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.RSI */
#define IFX_SENT_CH_INTCLR_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.RSI */
#define IFX_SENT_CH_INTCLR_RSI_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.SCRI */
#define IFX_SENT_CH_INTCLR_SCRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.SCRI */
#define IFX_SENT_CH_INTCLR_SCRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.SCRI */
#define IFX_SENT_CH_INTCLR_SCRI_OFF (12u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.SDI */
#define IFX_SENT_CH_INTCLR_SDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.SDI */
#define IFX_SENT_CH_INTCLR_SDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.SDI */
#define IFX_SENT_CH_INTCLR_SDI_OFF (11u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.TBI */
#define IFX_SENT_CH_INTCLR_TBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.TBI */
#define IFX_SENT_CH_INTCLR_TBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.TBI */
#define IFX_SENT_CH_INTCLR_TBI_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.TDI */
#define IFX_SENT_CH_INTCLR_TDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.TDI */
#define IFX_SENT_CH_INTCLR_TDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.TDI */
#define IFX_SENT_CH_INTCLR_TDI_OFF (3u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.WDI */
#define IFX_SENT_CH_INTCLR_WDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.WDI */
#define IFX_SENT_CH_INTCLR_WDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.WDI */
#define IFX_SENT_CH_INTCLR_WDI_OFF (13u)

/** \brief  Length for Ifx_SENT_CH_INTCLR_Bits.WSI */
#define IFX_SENT_CH_INTCLR_WSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTCLR_Bits.WSI */
#define IFX_SENT_CH_INTCLR_WSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTCLR_Bits.WSI */
#define IFX_SENT_CH_INTCLR_WSI_OFF (10u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.CRCI */
#define IFX_SENT_CH_INTEN_CRCI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.CRCI */
#define IFX_SENT_CH_INTEN_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.CRCI */
#define IFX_SENT_CH_INTEN_CRCI_OFF (9u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.FDI */
#define IFX_SENT_CH_INTEN_FDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.FDI */
#define IFX_SENT_CH_INTEN_FDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.FDI */
#define IFX_SENT_CH_INTEN_FDI_OFF (6u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.FRI */
#define IFX_SENT_CH_INTEN_FRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.FRI */
#define IFX_SENT_CH_INTEN_FRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.FRI */
#define IFX_SENT_CH_INTEN_FRI_OFF (5u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.NNI */
#define IFX_SENT_CH_INTEN_NNI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.NNI */
#define IFX_SENT_CH_INTEN_NNI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.NNI */
#define IFX_SENT_CH_INTEN_NNI_OFF (7u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.NVI */
#define IFX_SENT_CH_INTEN_NVI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.NVI */
#define IFX_SENT_CH_INTEN_NVI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.NVI */
#define IFX_SENT_CH_INTEN_NVI_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.RBI */
#define IFX_SENT_CH_INTEN_RBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.RBI */
#define IFX_SENT_CH_INTEN_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.RBI */
#define IFX_SENT_CH_INTEN_RBI_OFF (2u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.RDI */
#define IFX_SENT_CH_INTEN_RDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.RDI */
#define IFX_SENT_CH_INTEN_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.RDI */
#define IFX_SENT_CH_INTEN_RDI_OFF (1u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.RSI */
#define IFX_SENT_CH_INTEN_RSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.RSI */
#define IFX_SENT_CH_INTEN_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.RSI */
#define IFX_SENT_CH_INTEN_RSI_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.SCRI */
#define IFX_SENT_CH_INTEN_SCRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.SCRI */
#define IFX_SENT_CH_INTEN_SCRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.SCRI */
#define IFX_SENT_CH_INTEN_SCRI_OFF (12u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.SDI */
#define IFX_SENT_CH_INTEN_SDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.SDI */
#define IFX_SENT_CH_INTEN_SDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.SDI */
#define IFX_SENT_CH_INTEN_SDI_OFF (11u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.TBI */
#define IFX_SENT_CH_INTEN_TBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.TBI */
#define IFX_SENT_CH_INTEN_TBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.TBI */
#define IFX_SENT_CH_INTEN_TBI_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.TDI */
#define IFX_SENT_CH_INTEN_TDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.TDI */
#define IFX_SENT_CH_INTEN_TDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.TDI */
#define IFX_SENT_CH_INTEN_TDI_OFF (3u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.WDI */
#define IFX_SENT_CH_INTEN_WDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.WDI */
#define IFX_SENT_CH_INTEN_WDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.WDI */
#define IFX_SENT_CH_INTEN_WDI_OFF (13u)

/** \brief  Length for Ifx_SENT_CH_INTEN_Bits.WSI */
#define IFX_SENT_CH_INTEN_WSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTEN_Bits.WSI */
#define IFX_SENT_CH_INTEN_WSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTEN_Bits.WSI */
#define IFX_SENT_CH_INTEN_WSI_OFF (10u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.CRCI */
#define IFX_SENT_CH_INTSET_CRCI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.CRCI */
#define IFX_SENT_CH_INTSET_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.CRCI */
#define IFX_SENT_CH_INTSET_CRCI_OFF (9u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.FDI */
#define IFX_SENT_CH_INTSET_FDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.FDI */
#define IFX_SENT_CH_INTSET_FDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.FDI */
#define IFX_SENT_CH_INTSET_FDI_OFF (6u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.FRI */
#define IFX_SENT_CH_INTSET_FRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.FRI */
#define IFX_SENT_CH_INTSET_FRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.FRI */
#define IFX_SENT_CH_INTSET_FRI_OFF (5u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.NNI */
#define IFX_SENT_CH_INTSET_NNI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.NNI */
#define IFX_SENT_CH_INTSET_NNI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.NNI */
#define IFX_SENT_CH_INTSET_NNI_OFF (7u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.NVI */
#define IFX_SENT_CH_INTSET_NVI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.NVI */
#define IFX_SENT_CH_INTSET_NVI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.NVI */
#define IFX_SENT_CH_INTSET_NVI_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.RBI */
#define IFX_SENT_CH_INTSET_RBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.RBI */
#define IFX_SENT_CH_INTSET_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.RBI */
#define IFX_SENT_CH_INTSET_RBI_OFF (2u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.RDI */
#define IFX_SENT_CH_INTSET_RDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.RDI */
#define IFX_SENT_CH_INTSET_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.RDI */
#define IFX_SENT_CH_INTSET_RDI_OFF (1u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.RSI */
#define IFX_SENT_CH_INTSET_RSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.RSI */
#define IFX_SENT_CH_INTSET_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.RSI */
#define IFX_SENT_CH_INTSET_RSI_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.SCRI */
#define IFX_SENT_CH_INTSET_SCRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.SCRI */
#define IFX_SENT_CH_INTSET_SCRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.SCRI */
#define IFX_SENT_CH_INTSET_SCRI_OFF (12u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.SDI */
#define IFX_SENT_CH_INTSET_SDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.SDI */
#define IFX_SENT_CH_INTSET_SDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.SDI */
#define IFX_SENT_CH_INTSET_SDI_OFF (11u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.TBI */
#define IFX_SENT_CH_INTSET_TBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.TBI */
#define IFX_SENT_CH_INTSET_TBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.TBI */
#define IFX_SENT_CH_INTSET_TBI_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.TDI */
#define IFX_SENT_CH_INTSET_TDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.TDI */
#define IFX_SENT_CH_INTSET_TDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.TDI */
#define IFX_SENT_CH_INTSET_TDI_OFF (3u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.WDI */
#define IFX_SENT_CH_INTSET_WDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.WDI */
#define IFX_SENT_CH_INTSET_WDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.WDI */
#define IFX_SENT_CH_INTSET_WDI_OFF (13u)

/** \brief  Length for Ifx_SENT_CH_INTSET_Bits.WSI */
#define IFX_SENT_CH_INTSET_WSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSET_Bits.WSI */
#define IFX_SENT_CH_INTSET_WSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSET_Bits.WSI */
#define IFX_SENT_CH_INTSET_WSI_OFF (10u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.CRCI */
#define IFX_SENT_CH_INTSTAT_CRCI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.CRCI */
#define IFX_SENT_CH_INTSTAT_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.CRCI */
#define IFX_SENT_CH_INTSTAT_CRCI_OFF (9u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.FDI */
#define IFX_SENT_CH_INTSTAT_FDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.FDI */
#define IFX_SENT_CH_INTSTAT_FDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.FDI */
#define IFX_SENT_CH_INTSTAT_FDI_OFF (6u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.FRI */
#define IFX_SENT_CH_INTSTAT_FRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.FRI */
#define IFX_SENT_CH_INTSTAT_FRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.FRI */
#define IFX_SENT_CH_INTSTAT_FRI_OFF (5u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.NNI */
#define IFX_SENT_CH_INTSTAT_NNI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.NNI */
#define IFX_SENT_CH_INTSTAT_NNI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.NNI */
#define IFX_SENT_CH_INTSTAT_NNI_OFF (7u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.NVI */
#define IFX_SENT_CH_INTSTAT_NVI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.NVI */
#define IFX_SENT_CH_INTSTAT_NVI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.NVI */
#define IFX_SENT_CH_INTSTAT_NVI_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.RBI */
#define IFX_SENT_CH_INTSTAT_RBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.RBI */
#define IFX_SENT_CH_INTSTAT_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.RBI */
#define IFX_SENT_CH_INTSTAT_RBI_OFF (2u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.RDI */
#define IFX_SENT_CH_INTSTAT_RDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.RDI */
#define IFX_SENT_CH_INTSTAT_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.RDI */
#define IFX_SENT_CH_INTSTAT_RDI_OFF (1u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.RSI */
#define IFX_SENT_CH_INTSTAT_RSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.RSI */
#define IFX_SENT_CH_INTSTAT_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.RSI */
#define IFX_SENT_CH_INTSTAT_RSI_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.SCRI */
#define IFX_SENT_CH_INTSTAT_SCRI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.SCRI */
#define IFX_SENT_CH_INTSTAT_SCRI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.SCRI */
#define IFX_SENT_CH_INTSTAT_SCRI_OFF (12u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.SDI */
#define IFX_SENT_CH_INTSTAT_SDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.SDI */
#define IFX_SENT_CH_INTSTAT_SDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.SDI */
#define IFX_SENT_CH_INTSTAT_SDI_OFF (11u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.TBI */
#define IFX_SENT_CH_INTSTAT_TBI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.TBI */
#define IFX_SENT_CH_INTSTAT_TBI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.TBI */
#define IFX_SENT_CH_INTSTAT_TBI_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.TDI */
#define IFX_SENT_CH_INTSTAT_TDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.TDI */
#define IFX_SENT_CH_INTSTAT_TDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.TDI */
#define IFX_SENT_CH_INTSTAT_TDI_OFF (3u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.WDI */
#define IFX_SENT_CH_INTSTAT_WDI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.WDI */
#define IFX_SENT_CH_INTSTAT_WDI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.WDI */
#define IFX_SENT_CH_INTSTAT_WDI_OFF (13u)

/** \brief  Length for Ifx_SENT_CH_INTSTAT_Bits.WSI */
#define IFX_SENT_CH_INTSTAT_WSI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_INTSTAT_Bits.WSI */
#define IFX_SENT_CH_INTSTAT_WSI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_INTSTAT_Bits.WSI */
#define IFX_SENT_CH_INTSTAT_WSI_OFF (10u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.ALTI */
#define IFX_SENT_CH_IOCR_ALTI_LEN (2u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.ALTI */
#define IFX_SENT_CH_IOCR_ALTI_MSK (0x3u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.ALTI */
#define IFX_SENT_CH_IOCR_ALTI_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.CEC */
#define IFX_SENT_CH_IOCR_CEC_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.CEC */
#define IFX_SENT_CH_IOCR_CEC_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.CEC */
#define IFX_SENT_CH_IOCR_CEC_OFF (10u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.CFEG */
#define IFX_SENT_CH_IOCR_CFEG_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.CFEG */
#define IFX_SENT_CH_IOCR_CFEG_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.CFEG */
#define IFX_SENT_CH_IOCR_CFEG_OFF (15u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.CREG */
#define IFX_SENT_CH_IOCR_CREG_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.CREG */
#define IFX_SENT_CH_IOCR_CREG_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.CREG */
#define IFX_SENT_CH_IOCR_CREG_OFF (14u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.CTR */
#define IFX_SENT_CH_IOCR_CTR_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.CTR */
#define IFX_SENT_CH_IOCR_CTR_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.CTR */
#define IFX_SENT_CH_IOCR_CTR_OFF (28u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.DEPTH */
#define IFX_SENT_CH_IOCR_DEPTH_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.DEPTH */
#define IFX_SENT_CH_IOCR_DEPTH_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.DEPTH */
#define IFX_SENT_CH_IOCR_DEPTH_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.EC */
#define IFX_SENT_CH_IOCR_EC_LEN (8u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.EC */
#define IFX_SENT_CH_IOCR_EC_MSK (0xffu)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.EC */
#define IFX_SENT_CH_IOCR_EC_OFF (20u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.ETS */
#define IFX_SENT_CH_IOCR_ETS_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.ETS */
#define IFX_SENT_CH_IOCR_ETS_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.ETS */
#define IFX_SENT_CH_IOCR_ETS_OFF (16u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.FEG */
#define IFX_SENT_CH_IOCR_FEG_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.FEG */
#define IFX_SENT_CH_IOCR_FEG_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.FEG */
#define IFX_SENT_CH_IOCR_FEG_OFF (13u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.IIE */
#define IFX_SENT_CH_IOCR_IIE_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.IIE */
#define IFX_SENT_CH_IOCR_IIE_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.IIE */
#define IFX_SENT_CH_IOCR_IIE_OFF (9u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.OIE */
#define IFX_SENT_CH_IOCR_OIE_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.OIE */
#define IFX_SENT_CH_IOCR_OIE_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.OIE */
#define IFX_SENT_CH_IOCR_OIE_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.REG */
#define IFX_SENT_CH_IOCR_REG_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.REG */
#define IFX_SENT_CH_IOCR_REG_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.REG */
#define IFX_SENT_CH_IOCR_REG_OFF (12u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.RXM */
#define IFX_SENT_CH_IOCR_RXM_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.RXM */
#define IFX_SENT_CH_IOCR_RXM_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.RXM */
#define IFX_SENT_CH_IOCR_RXM_OFF (30u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.TRM */
#define IFX_SENT_CH_IOCR_TRM_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.TRM */
#define IFX_SENT_CH_IOCR_TRM_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.TRM */
#define IFX_SENT_CH_IOCR_TRM_OFF (29u)

/** \brief  Length for Ifx_SENT_CH_IOCR_Bits.TXM */
#define IFX_SENT_CH_IOCR_TXM_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_IOCR_Bits.TXM */
#define IFX_SENT_CH_IOCR_TXM_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_IOCR_Bits.TXM */
#define IFX_SENT_CH_IOCR_TXM_OFF (31u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.ACE */
#define IFX_SENT_CH_RCR_ACE_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.ACE */
#define IFX_SENT_CH_RCR_ACE_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.ACE */
#define IFX_SENT_CH_RCR_ACE_OFF (2u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.CDIS */
#define IFX_SENT_CH_RCR_CDIS_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.CDIS */
#define IFX_SENT_CH_RCR_CDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.CDIS */
#define IFX_SENT_CH_RCR_CDIS_OFF (6u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.CEN */
#define IFX_SENT_CH_RCR_CEN_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.CEN */
#define IFX_SENT_CH_RCR_CEN_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.CEN */
#define IFX_SENT_CH_RCR_CEN_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.CFC */
#define IFX_SENT_CH_RCR_CFC_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.CFC */
#define IFX_SENT_CH_RCR_CFC_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.CFC */
#define IFX_SENT_CH_RCR_CFC_OFF (7u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.CRZ */
#define IFX_SENT_CH_RCR_CRZ_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.CRZ */
#define IFX_SENT_CH_RCR_CRZ_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.CRZ */
#define IFX_SENT_CH_RCR_CRZ_OFF (16u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.ESF */
#define IFX_SENT_CH_RCR_ESF_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.ESF */
#define IFX_SENT_CH_RCR_ESF_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.ESF */
#define IFX_SENT_CH_RCR_ESF_OFF (17u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.FRL */
#define IFX_SENT_CH_RCR_FRL_LEN (8u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.FRL */
#define IFX_SENT_CH_RCR_FRL_MSK (0xffu)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.FRL */
#define IFX_SENT_CH_RCR_FRL_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.IDE */
#define IFX_SENT_CH_RCR_IDE_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.IDE */
#define IFX_SENT_CH_RCR_IDE_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.IDE */
#define IFX_SENT_CH_RCR_IDE_OFF (18u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.IEP */
#define IFX_SENT_CH_RCR_IEP_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.IEP */
#define IFX_SENT_CH_RCR_IEP_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.IEP */
#define IFX_SENT_CH_RCR_IEP_OFF (1u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.SCDIS */
#define IFX_SENT_CH_RCR_SCDIS_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.SCDIS */
#define IFX_SENT_CH_RCR_SCDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.SCDIS */
#define IFX_SENT_CH_RCR_SCDIS_OFF (5u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.SDP */
#define IFX_SENT_CH_RCR_SDP_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.SDP */
#define IFX_SENT_CH_RCR_SDP_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.SDP */
#define IFX_SENT_CH_RCR_SDP_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.SNI */
#define IFX_SENT_CH_RCR_SNI_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.SNI */
#define IFX_SENT_CH_RCR_SNI_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.SNI */
#define IFX_SENT_CH_RCR_SNI_OFF (3u)

/** \brief  Length for Ifx_SENT_CH_RCR_Bits.SUSEN */
#define IFX_SENT_CH_RCR_SUSEN_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_RCR_Bits.SUSEN */
#define IFX_SENT_CH_RCR_SUSEN_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_RCR_Bits.SUSEN */
#define IFX_SENT_CH_RCR_SUSEN_OFF (19u)

/** \brief  Length for Ifx_SENT_CH_RSR_Bits.CRC */
#define IFX_SENT_CH_RSR_CRC_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_RSR_Bits.CRC */
#define IFX_SENT_CH_RSR_CRC_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_RSR_Bits.CRC */
#define IFX_SENT_CH_RSR_CRC_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_RSR_Bits.CST */
#define IFX_SENT_CH_RSR_CST_LEN (2u)

/** \brief  Mask for Ifx_SENT_CH_RSR_Bits.CST */
#define IFX_SENT_CH_RSR_CST_MSK (0x3u)

/** \brief  Offset for Ifx_SENT_CH_RSR_Bits.CST */
#define IFX_SENT_CH_RSR_CST_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_RSR_Bits.SCN */
#define IFX_SENT_CH_RSR_SCN_LEN (4u)

/** \brief  Mask for Ifx_SENT_CH_RSR_Bits.SCN */
#define IFX_SENT_CH_RSR_SCN_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_CH_RSR_Bits.SCN */
#define IFX_SENT_CH_RSR_SCN_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_SCR_Bits.BASE */
#define IFX_SENT_CH_SCR_BASE_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_SCR_Bits.BASE */
#define IFX_SENT_CH_SCR_BASE_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_SCR_Bits.BASE */
#define IFX_SENT_CH_SCR_BASE_OFF (14u)

/** \brief  Length for Ifx_SENT_CH_SCR_Bits.DEL */
#define IFX_SENT_CH_SCR_DEL_LEN (6u)

/** \brief  Mask for Ifx_SENT_CH_SCR_Bits.DEL */
#define IFX_SENT_CH_SCR_DEL_MSK (0x3fu)

/** \brief  Offset for Ifx_SENT_CH_SCR_Bits.DEL */
#define IFX_SENT_CH_SCR_DEL_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_SCR_Bits.PLEN */
#define IFX_SENT_CH_SCR_PLEN_LEN (6u)

/** \brief  Mask for Ifx_SENT_CH_SCR_Bits.PLEN */
#define IFX_SENT_CH_SCR_PLEN_MSK (0x3fu)

/** \brief  Offset for Ifx_SENT_CH_SCR_Bits.PLEN */
#define IFX_SENT_CH_SCR_PLEN_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_SCR_Bits.TRIG */
#define IFX_SENT_CH_SCR_TRIG_LEN (2u)

/** \brief  Mask for Ifx_SENT_CH_SCR_Bits.TRIG */
#define IFX_SENT_CH_SCR_TRIG_MSK (0x3u)

/** \brief  Offset for Ifx_SENT_CH_SCR_Bits.TRIG */
#define IFX_SENT_CH_SCR_TRIG_OFF (6u)

/** \brief  Length for Ifx_SENT_CH_SCR_Bits.TRQ */
#define IFX_SENT_CH_SCR_TRQ_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_SCR_Bits.TRQ */
#define IFX_SENT_CH_SCR_TRQ_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_SCR_Bits.TRQ */
#define IFX_SENT_CH_SCR_TRQ_OFF (15u)

/** \brief  Length for Ifx_SENT_CH_SDS_Bits.CON */
#define IFX_SENT_CH_SDS_CON_LEN (1u)

/** \brief  Mask for Ifx_SENT_CH_SDS_Bits.CON */
#define IFX_SENT_CH_SDS_CON_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CH_SDS_Bits.CON */
#define IFX_SENT_CH_SDS_CON_OFF (31u)

/** \brief  Length for Ifx_SENT_CH_SDS_Bits.MID */
#define IFX_SENT_CH_SDS_MID_LEN (8u)

/** \brief  Mask for Ifx_SENT_CH_SDS_Bits.MID */
#define IFX_SENT_CH_SDS_MID_MSK (0xffu)

/** \brief  Offset for Ifx_SENT_CH_SDS_Bits.MID */
#define IFX_SENT_CH_SDS_MID_OFF (16u)

/** \brief  Length for Ifx_SENT_CH_SDS_Bits.SCRC */
#define IFX_SENT_CH_SDS_SCRC_LEN (6u)

/** \brief  Mask for Ifx_SENT_CH_SDS_Bits.SCRC */
#define IFX_SENT_CH_SDS_SCRC_MSK (0x3fu)

/** \brief  Offset for Ifx_SENT_CH_SDS_Bits.SCRC */
#define IFX_SENT_CH_SDS_SCRC_OFF (24u)

/** \brief  Length for Ifx_SENT_CH_SDS_Bits.SD */
#define IFX_SENT_CH_SDS_SD_LEN (16u)

/** \brief  Mask for Ifx_SENT_CH_SDS_Bits.SD */
#define IFX_SENT_CH_SDS_SD_MSK (0xffffu)

/** \brief  Offset for Ifx_SENT_CH_SDS_Bits.SD */
#define IFX_SENT_CH_SDS_SD_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP0 */
#define IFX_SENT_CH_VIEW_RDNP0_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP0 */
#define IFX_SENT_CH_VIEW_RDNP0_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP0 */
#define IFX_SENT_CH_VIEW_RDNP0_OFF (0u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP1 */
#define IFX_SENT_CH_VIEW_RDNP1_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP1 */
#define IFX_SENT_CH_VIEW_RDNP1_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP1 */
#define IFX_SENT_CH_VIEW_RDNP1_OFF (4u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP2 */
#define IFX_SENT_CH_VIEW_RDNP2_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP2 */
#define IFX_SENT_CH_VIEW_RDNP2_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP2 */
#define IFX_SENT_CH_VIEW_RDNP2_OFF (8u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP3 */
#define IFX_SENT_CH_VIEW_RDNP3_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP3 */
#define IFX_SENT_CH_VIEW_RDNP3_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP3 */
#define IFX_SENT_CH_VIEW_RDNP3_OFF (12u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP4 */
#define IFX_SENT_CH_VIEW_RDNP4_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP4 */
#define IFX_SENT_CH_VIEW_RDNP4_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP4 */
#define IFX_SENT_CH_VIEW_RDNP4_OFF (16u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP5 */
#define IFX_SENT_CH_VIEW_RDNP5_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP5 */
#define IFX_SENT_CH_VIEW_RDNP5_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP5 */
#define IFX_SENT_CH_VIEW_RDNP5_OFF (20u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP6 */
#define IFX_SENT_CH_VIEW_RDNP6_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP6 */
#define IFX_SENT_CH_VIEW_RDNP6_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP6 */
#define IFX_SENT_CH_VIEW_RDNP6_OFF (24u)

/** \brief  Length for Ifx_SENT_CH_VIEW_Bits.RDNP7 */
#define IFX_SENT_CH_VIEW_RDNP7_LEN (3u)

/** \brief  Mask for Ifx_SENT_CH_VIEW_Bits.RDNP7 */
#define IFX_SENT_CH_VIEW_RDNP7_MSK (0x7u)

/** \brief  Offset for Ifx_SENT_CH_VIEW_Bits.RDNP7 */
#define IFX_SENT_CH_VIEW_RDNP7_OFF (28u)

/** \brief  Length for Ifx_SENT_CH_WDT_Bits.WDLx */
#define IFX_SENT_CH_WDT_WDLX_LEN (16u)

/** \brief  Mask for Ifx_SENT_CH_WDT_Bits.WDLx */
#define IFX_SENT_CH_WDT_WDLX_MSK (0xffffu)

/** \brief  Offset for Ifx_SENT_CH_WDT_Bits.WDLx */
#define IFX_SENT_CH_WDT_WDLX_OFF (0u)

/** \brief  Length for Ifx_SENT_CLC_Bits.DISR */
#define IFX_SENT_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_SENT_CLC_Bits.DISR */
#define IFX_SENT_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CLC_Bits.DISR */
#define IFX_SENT_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_SENT_CLC_Bits.DISS */
#define IFX_SENT_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_SENT_CLC_Bits.DISS */
#define IFX_SENT_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CLC_Bits.DISS */
#define IFX_SENT_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_SENT_CLC_Bits.EDIS */
#define IFX_SENT_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_SENT_CLC_Bits.EDIS */
#define IFX_SENT_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_CLC_Bits.EDIS */
#define IFX_SENT_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_SENT_CLC_Bits.RMC */
#define IFX_SENT_CLC_RMC_LEN (8u)

/** \brief  Mask for Ifx_SENT_CLC_Bits.RMC */
#define IFX_SENT_CLC_RMC_MSK (0xffu)

/** \brief  Offset for Ifx_SENT_CLC_Bits.RMC */
#define IFX_SENT_CLC_RMC_OFF (8u)

/** \brief  Length for Ifx_SENT_FDR_Bits.DM */
#define IFX_SENT_FDR_DM_LEN (2u)

/** \brief  Mask for Ifx_SENT_FDR_Bits.DM */
#define IFX_SENT_FDR_DM_MSK (0x3u)

/** \brief  Offset for Ifx_SENT_FDR_Bits.DM */
#define IFX_SENT_FDR_DM_OFF (14u)

/** \brief  Length for Ifx_SENT_FDR_Bits.RESULT */
#define IFX_SENT_FDR_RESULT_LEN (10u)

/** \brief  Mask for Ifx_SENT_FDR_Bits.RESULT */
#define IFX_SENT_FDR_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_SENT_FDR_Bits.RESULT */
#define IFX_SENT_FDR_RESULT_OFF (16u)

/** \brief  Length for Ifx_SENT_FDR_Bits.STEP */
#define IFX_SENT_FDR_STEP_LEN (10u)

/** \brief  Mask for Ifx_SENT_FDR_Bits.STEP */
#define IFX_SENT_FDR_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_SENT_FDR_Bits.STEP */
#define IFX_SENT_FDR_STEP_OFF (0u)

/** \brief  Length for Ifx_SENT_ID_Bits.MODNUMBER */
#define IFX_SENT_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_SENT_ID_Bits.MODNUMBER */
#define IFX_SENT_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_SENT_ID_Bits.MODNUMBER */
#define IFX_SENT_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_SENT_ID_Bits.MODREV */
#define IFX_SENT_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_SENT_ID_Bits.MODREV */
#define IFX_SENT_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_SENT_ID_Bits.MODREV */
#define IFX_SENT_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_SENT_ID_Bits.MODTYPE */
#define IFX_SENT_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_SENT_ID_Bits.MODTYPE */
#define IFX_SENT_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_SENT_ID_Bits.MODTYPE */
#define IFX_SENT_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_SENT_INTOV_Bits.IPC0 */
#define IFX_SENT_INTOV_IPC0_LEN (1u)

/** \brief  Mask for Ifx_SENT_INTOV_Bits.IPC0 */
#define IFX_SENT_INTOV_IPC0_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_INTOV_Bits.IPC0 */
#define IFX_SENT_INTOV_IPC0_OFF (0u)

/** \brief  Length for Ifx_SENT_INTOV_Bits.IPC1 */
#define IFX_SENT_INTOV_IPC1_LEN (1u)

/** \brief  Mask for Ifx_SENT_INTOV_Bits.IPC1 */
#define IFX_SENT_INTOV_IPC1_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_INTOV_Bits.IPC1 */
#define IFX_SENT_INTOV_IPC1_OFF (1u)

/** \brief  Length for Ifx_SENT_INTOV_Bits.IPC2 */
#define IFX_SENT_INTOV_IPC2_LEN (1u)

/** \brief  Mask for Ifx_SENT_INTOV_Bits.IPC2 */
#define IFX_SENT_INTOV_IPC2_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_INTOV_Bits.IPC2 */
#define IFX_SENT_INTOV_IPC2_OFF (2u)

/** \brief  Length for Ifx_SENT_INTOV_Bits.IPC3 */
#define IFX_SENT_INTOV_IPC3_LEN (1u)

/** \brief  Mask for Ifx_SENT_INTOV_Bits.IPC3 */
#define IFX_SENT_INTOV_IPC3_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_INTOV_Bits.IPC3 */
#define IFX_SENT_INTOV_IPC3_OFF (3u)

/** \brief  Length for Ifx_SENT_INTOV_Bits.IPC4 */
#define IFX_SENT_INTOV_IPC4_LEN (1u)

/** \brief  Mask for Ifx_SENT_INTOV_Bits.IPC4 */
#define IFX_SENT_INTOV_IPC4_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_INTOV_Bits.IPC4 */
#define IFX_SENT_INTOV_IPC4_OFF (4u)

/** \brief  Length for Ifx_SENT_INTOV_Bits.IPC5 */
#define IFX_SENT_INTOV_IPC5_LEN (1u)

/** \brief  Mask for Ifx_SENT_INTOV_Bits.IPC5 */
#define IFX_SENT_INTOV_IPC5_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_INTOV_Bits.IPC5 */
#define IFX_SENT_INTOV_IPC5_OFF (5u)

/** \brief  Length for Ifx_SENT_KRST0_Bits.RST */
#define IFX_SENT_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_SENT_KRST0_Bits.RST */
#define IFX_SENT_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_KRST0_Bits.RST */
#define IFX_SENT_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_SENT_KRST0_Bits.RSTSTAT */
#define IFX_SENT_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_SENT_KRST0_Bits.RSTSTAT */
#define IFX_SENT_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_KRST0_Bits.RSTSTAT */
#define IFX_SENT_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_SENT_KRST1_Bits.RST */
#define IFX_SENT_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_SENT_KRST1_Bits.RST */
#define IFX_SENT_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_KRST1_Bits.RST */
#define IFX_SENT_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_SENT_KRSTCLR_Bits.CLR */
#define IFX_SENT_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_SENT_KRSTCLR_Bits.CLR */
#define IFX_SENT_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_KRSTCLR_Bits.CLR */
#define IFX_SENT_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_SENT_OCS_Bits.SUS */
#define IFX_SENT_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_SENT_OCS_Bits.SUS */
#define IFX_SENT_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_OCS_Bits.SUS */
#define IFX_SENT_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_SENT_OCS_Bits.SUS_P */
#define IFX_SENT_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_SENT_OCS_Bits.SUS_P */
#define IFX_SENT_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_OCS_Bits.SUS_P */
#define IFX_SENT_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_SENT_OCS_Bits.SUSSTA */
#define IFX_SENT_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_SENT_OCS_Bits.SUSSTA */
#define IFX_SENT_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_SENT_OCS_Bits.SUSSTA */
#define IFX_SENT_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD0 */
#define IFX_SENT_RDR_RD0_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD0 */
#define IFX_SENT_RDR_RD0_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD0 */
#define IFX_SENT_RDR_RD0_OFF (0u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD1 */
#define IFX_SENT_RDR_RD1_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD1 */
#define IFX_SENT_RDR_RD1_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD1 */
#define IFX_SENT_RDR_RD1_OFF (4u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD2 */
#define IFX_SENT_RDR_RD2_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD2 */
#define IFX_SENT_RDR_RD2_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD2 */
#define IFX_SENT_RDR_RD2_OFF (8u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD3 */
#define IFX_SENT_RDR_RD3_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD3 */
#define IFX_SENT_RDR_RD3_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD3 */
#define IFX_SENT_RDR_RD3_OFF (12u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD4 */
#define IFX_SENT_RDR_RD4_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD4 */
#define IFX_SENT_RDR_RD4_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD4 */
#define IFX_SENT_RDR_RD4_OFF (16u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD5 */
#define IFX_SENT_RDR_RD5_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD5 */
#define IFX_SENT_RDR_RD5_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD5 */
#define IFX_SENT_RDR_RD5_OFF (20u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD6 */
#define IFX_SENT_RDR_RD6_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD6 */
#define IFX_SENT_RDR_RD6_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD6 */
#define IFX_SENT_RDR_RD6_OFF (24u)

/** \brief  Length for Ifx_SENT_RDR_Bits.RD7 */
#define IFX_SENT_RDR_RD7_LEN (4u)

/** \brief  Mask for Ifx_SENT_RDR_Bits.RD7 */
#define IFX_SENT_RDR_RD7_MSK (0xfu)

/** \brief  Offset for Ifx_SENT_RDR_Bits.RD7 */
#define IFX_SENT_RDR_RD7_OFF (28u)

/** \brief  Length for Ifx_SENT_RTS_Bits.LTS */
#define IFX_SENT_RTS_LTS_LEN (32u)

/** \brief  Mask for Ifx_SENT_RTS_Bits.LTS */
#define IFX_SENT_RTS_LTS_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SENT_RTS_Bits.LTS */
#define IFX_SENT_RTS_LTS_OFF (0u)

/** \brief  Length for Ifx_SENT_TPD_Bits.TDIV */
#define IFX_SENT_TPD_TDIV_LEN (20u)

/** \brief  Mask for Ifx_SENT_TPD_Bits.TDIV */
#define IFX_SENT_TPD_TDIV_MSK (0xfffffu)

/** \brief  Offset for Ifx_SENT_TPD_Bits.TDIV */
#define IFX_SENT_TPD_TDIV_OFF (0u)

/** \brief  Length for Ifx_SENT_TSR_Bits.CTS */
#define IFX_SENT_TSR_CTS_LEN (32u)

/** \brief  Mask for Ifx_SENT_TSR_Bits.CTS */
#define IFX_SENT_TSR_CTS_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SENT_TSR_Bits.CTS */
#define IFX_SENT_TSR_CTS_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXSENT_BF_H */
