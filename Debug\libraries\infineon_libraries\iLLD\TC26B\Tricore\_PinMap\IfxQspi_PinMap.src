	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc26204a --dep-file=IfxQspi_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_HSICINA_P15_2_IN',data,rom,cluster('IfxQspi2_HSICINA_P15_2_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_HSICINA_P15_2_IN'
	.global	IfxQspi2_HSICINA_P15_2_IN
	.align	4
IfxQspi2_HSICINA_P15_2_IN:	.type	object
	.size	IfxQspi2_HSICINA_P15_2_IN,16
	.word	-268427776,-268192512
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_HSICINB_P15_3_IN',data,rom,cluster('IfxQspi2_HSICINB_P15_3_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_HSICINB_P15_3_IN'
	.global	IfxQspi2_HSICINB_P15_3_IN
	.align	4
IfxQspi2_HSICINB_P15_3_IN:	.type	object
	.size	IfxQspi2_HSICINB_P15_3_IN,16
	.word	-268427776,-268192512
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_HSICINA_P33_9_IN',data,rom,cluster('IfxQspi3_HSICINA_P33_9_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_HSICINA_P33_9_IN'
	.global	IfxQspi3_HSICINA_P33_9_IN
	.align	4
IfxQspi3_HSICINA_P33_9_IN:	.type	object
	.size	IfxQspi3_HSICINA_P33_9_IN,16
	.word	-*********,-*********
	.byte	9
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_HSICINB_P33_10_IN',data,rom,cluster('IfxQspi3_HSICINB_P33_10_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_HSICINB_P33_10_IN'
	.global	IfxQspi3_HSICINB_P33_10_IN
	.align	4
IfxQspi3_HSICINB_P33_10_IN:	.type	object
	.size	IfxQspi3_HSICINB_P33_10_IN,16
	.word	-*********,-*********
	.byte	10
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_MRSTA_P20_12_IN',data,rom,cluster('IfxQspi0_MRSTA_P20_12_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_MRSTA_P20_12_IN'
	.global	IfxQspi0_MRSTA_P20_12_IN
	.align	4
IfxQspi0_MRSTA_P20_12_IN:	.type	object
	.size	IfxQspi0_MRSTA_P20_12_IN,16
	.word	-*********,-*********
	.byte	12
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MRSTA_P10_1_IN',data,rom,cluster('IfxQspi1_MRSTA_P10_1_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MRSTA_P10_1_IN'
	.global	IfxQspi1_MRSTA_P10_1_IN
	.align	4
IfxQspi1_MRSTA_P10_1_IN:	.type	object
	.size	IfxQspi1_MRSTA_P10_1_IN,16
	.word	-268428032,-268193792
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MRSTB_P11_3_IN',data,rom,cluster('IfxQspi1_MRSTB_P11_3_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MRSTB_P11_3_IN'
	.global	IfxQspi1_MRSTB_P11_3_IN
	.align	4
IfxQspi1_MRSTB_P11_3_IN:	.type	object
	.size	IfxQspi1_MRSTB_P11_3_IN,16
	.word	-268428032,-268193536
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTA_P15_4_IN',data,rom,cluster('IfxQspi2_MRSTA_P15_4_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTA_P15_4_IN'
	.global	IfxQspi2_MRSTA_P15_4_IN
	.align	4
IfxQspi2_MRSTA_P15_4_IN:	.type	object
	.size	IfxQspi2_MRSTA_P15_4_IN,16
	.word	-268427776,-268192512
	.byte	4
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTB_P15_7_IN',data,rom,cluster('IfxQspi2_MRSTB_P15_7_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTB_P15_7_IN'
	.global	IfxQspi2_MRSTB_P15_7_IN
	.align	4
IfxQspi2_MRSTB_P15_7_IN:	.type	object
	.size	IfxQspi2_MRSTB_P15_7_IN,16
	.word	-268427776,-268192512
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTCN_P21_2_IN',data,rom,cluster('IfxQspi2_MRSTCN_P21_2_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTCN_P21_2_IN'
	.global	IfxQspi2_MRSTCN_P21_2_IN
	.align	4
IfxQspi2_MRSTCN_P21_2_IN:	.type	object
	.size	IfxQspi2_MRSTCN_P21_2_IN,16
	.word	-268427776,-*********
	.byte	2
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTCP_P21_3_IN',data,rom,cluster('IfxQspi2_MRSTCP_P21_3_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTCP_P21_3_IN'
	.global	IfxQspi2_MRSTCP_P21_3_IN
	.align	4
IfxQspi2_MRSTCP_P21_3_IN:	.type	object
	.size	IfxQspi2_MRSTCP_P21_3_IN,16
	.word	-268427776,-*********
	.byte	3
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTE_P15_2_IN',data,rom,cluster('IfxQspi2_MRSTE_P15_2_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MRSTE_P15_2_IN'
	.global	IfxQspi2_MRSTE_P15_2_IN
	.align	4
IfxQspi2_MRSTE_P15_2_IN:	.type	object
	.size	IfxQspi2_MRSTE_P15_2_IN,16
	.word	-268427776,-268192512
	.byte	2
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTA_P02_5_IN',data,rom,cluster('IfxQspi3_MRSTA_P02_5_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTA_P02_5_IN'
	.global	IfxQspi3_MRSTA_P02_5_IN
	.align	4
IfxQspi3_MRSTA_P02_5_IN:	.type	object
	.size	IfxQspi3_MRSTA_P02_5_IN,16
	.word	-*********,-268197376
	.byte	5
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTB_P10_7_IN',data,rom,cluster('IfxQspi3_MRSTB_P10_7_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTB_P10_7_IN'
	.global	IfxQspi3_MRSTB_P10_7_IN
	.align	4
IfxQspi3_MRSTB_P10_7_IN:	.type	object
	.size	IfxQspi3_MRSTB_P10_7_IN,16
	.word	-*********,-268193792
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTD_P33_13_IN',data,rom,cluster('IfxQspi3_MRSTD_P33_13_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTD_P33_13_IN'
	.global	IfxQspi3_MRSTD_P33_13_IN
	.align	4
IfxQspi3_MRSTD_P33_13_IN:	.type	object
	.size	IfxQspi3_MRSTD_P33_13_IN,16
	.word	-*********,-*********
	.byte	13
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTE_P22_1_IN',data,rom,cluster('IfxQspi3_MRSTE_P22_1_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTE_P22_1_IN'
	.global	IfxQspi3_MRSTE_P22_1_IN
	.align	4
IfxQspi3_MRSTE_P22_1_IN:	.type	object
	.size	IfxQspi3_MRSTE_P22_1_IN,16
	.word	-*********,-*********
	.byte	1
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTFN_P21_2_IN',data,rom,cluster('IfxQspi3_MRSTFN_P21_2_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTFN_P21_2_IN'
	.global	IfxQspi3_MRSTFN_P21_2_IN
	.align	4
IfxQspi3_MRSTFN_P21_2_IN:	.type	object
	.size	IfxQspi3_MRSTFN_P21_2_IN,16
	.word	-*********,-*********
	.byte	2
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTFP_P21_3_IN',data,rom,cluster('IfxQspi3_MRSTFP_P21_3_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRSTFP_P21_3_IN'
	.global	IfxQspi3_MRSTFP_P21_3_IN
	.align	4
IfxQspi3_MRSTFP_P21_3_IN:	.type	object
	.size	IfxQspi3_MRSTFP_P21_3_IN,16
	.word	-*********,-*********
	.byte	3
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_MRST_P20_12_OUT',data,rom,cluster('IfxQspi0_MRST_P20_12_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_MRST_P20_12_OUT'
	.global	IfxQspi0_MRST_P20_12_OUT
	.align	4
IfxQspi0_MRST_P20_12_OUT:	.type	object
	.size	IfxQspi0_MRST_P20_12_OUT,16
	.word	-*********,-*********
	.byte	12
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MRST_P10_1_OUT',data,rom,cluster('IfxQspi1_MRST_P10_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MRST_P10_1_OUT'
	.global	IfxQspi1_MRST_P10_1_OUT
	.align	4
IfxQspi1_MRST_P10_1_OUT:	.type	object
	.size	IfxQspi1_MRST_P10_1_OUT,16
	.word	-268428032,-268193792
	.byte	1
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MRST_P10_6_OUT',data,rom,cluster('IfxQspi1_MRST_P10_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MRST_P10_6_OUT'
	.global	IfxQspi1_MRST_P10_6_OUT
	.align	4
IfxQspi1_MRST_P10_6_OUT:	.type	object
	.size	IfxQspi1_MRST_P10_6_OUT,16
	.word	-268428032,-268193792
	.byte	6
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MRST_P11_3_OUT',data,rom,cluster('IfxQspi1_MRST_P11_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MRST_P11_3_OUT'
	.global	IfxQspi1_MRST_P11_3_OUT
	.align	4
IfxQspi1_MRST_P11_3_OUT:	.type	object
	.size	IfxQspi1_MRST_P11_3_OUT,16
	.word	-268428032,-268193536
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MRST_P15_4_OUT',data,rom,cluster('IfxQspi2_MRST_P15_4_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MRST_P15_4_OUT'
	.global	IfxQspi2_MRST_P15_4_OUT
	.align	4
IfxQspi2_MRST_P15_4_OUT:	.type	object
	.size	IfxQspi2_MRST_P15_4_OUT,16
	.word	-268427776,-268192512
	.byte	4
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MRST_P15_7_OUT',data,rom,cluster('IfxQspi2_MRST_P15_7_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MRST_P15_7_OUT'
	.global	IfxQspi2_MRST_P15_7_OUT
	.align	4
IfxQspi2_MRST_P15_7_OUT:	.type	object
	.size	IfxQspi2_MRST_P15_7_OUT,16
	.word	-268427776,-268192512
	.byte	7
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P02_5_OUT',data,rom,cluster('IfxQspi3_MRST_P02_5_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P02_5_OUT'
	.global	IfxQspi3_MRST_P02_5_OUT
	.align	4
IfxQspi3_MRST_P02_5_OUT:	.type	object
	.size	IfxQspi3_MRST_P02_5_OUT,16
	.word	-*********,-268197376
	.byte	5
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P10_7_OUT',data,rom,cluster('IfxQspi3_MRST_P10_7_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P10_7_OUT'
	.global	IfxQspi3_MRST_P10_7_OUT
	.align	4
IfxQspi3_MRST_P10_7_OUT:	.type	object
	.size	IfxQspi3_MRST_P10_7_OUT,16
	.word	-*********,-268193792
	.byte	7
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P22_1_OUT',data,rom,cluster('IfxQspi3_MRST_P22_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P22_1_OUT'
	.global	IfxQspi3_MRST_P22_1_OUT
	.align	4
IfxQspi3_MRST_P22_1_OUT:	.type	object
	.size	IfxQspi3_MRST_P22_1_OUT,16
	.word	-*********,-*********
	.byte	1
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P33_13_OUT',data,rom,cluster('IfxQspi3_MRST_P33_13_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MRST_P33_13_OUT'
	.global	IfxQspi3_MRST_P33_13_OUT
	.align	4
IfxQspi3_MRST_P33_13_OUT:	.type	object
	.size	IfxQspi3_MRST_P33_13_OUT,16
	.word	-*********,-*********
	.byte	13
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_MTSRA_P20_14_IN',data,rom,cluster('IfxQspi0_MTSRA_P20_14_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_MTSRA_P20_14_IN'
	.global	IfxQspi0_MTSRA_P20_14_IN
	.align	4
IfxQspi0_MTSRA_P20_14_IN:	.type	object
	.size	IfxQspi0_MTSRA_P20_14_IN,16
	.word	-*********,-*********
	.byte	14
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSRA_P10_3_IN',data,rom,cluster('IfxQspi1_MTSRA_P10_3_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSRA_P10_3_IN'
	.global	IfxQspi1_MTSRA_P10_3_IN
	.align	4
IfxQspi1_MTSRA_P10_3_IN:	.type	object
	.size	IfxQspi1_MTSRA_P10_3_IN,16
	.word	-268428032,-268193792
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSRB_P11_9_IN',data,rom,cluster('IfxQspi1_MTSRB_P11_9_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSRB_P11_9_IN'
	.global	IfxQspi1_MTSRB_P11_9_IN
	.align	4
IfxQspi1_MTSRB_P11_9_IN:	.type	object
	.size	IfxQspi1_MTSRB_P11_9_IN,16
	.word	-268428032,-268193536
	.byte	9
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSRC_P10_4_IN',data,rom,cluster('IfxQspi1_MTSRC_P10_4_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSRC_P10_4_IN'
	.global	IfxQspi1_MTSRC_P10_4_IN
	.align	4
IfxQspi1_MTSRC_P10_4_IN:	.type	object
	.size	IfxQspi1_MTSRC_P10_4_IN,16
	.word	-268428032,-268193792
	.byte	4
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRA_P15_5_IN',data,rom,cluster('IfxQspi2_MTSRA_P15_5_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRA_P15_5_IN'
	.global	IfxQspi2_MTSRA_P15_5_IN
	.align	4
IfxQspi2_MTSRA_P15_5_IN:	.type	object
	.size	IfxQspi2_MTSRA_P15_5_IN,16
	.word	-268427776,-268192512
	.byte	5
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRB_P15_6_IN',data,rom,cluster('IfxQspi2_MTSRB_P15_6_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRB_P15_6_IN'
	.global	IfxQspi2_MTSRB_P15_6_IN
	.align	4
IfxQspi2_MTSRB_P15_6_IN:	.type	object
	.size	IfxQspi2_MTSRB_P15_6_IN,16
	.word	-268427776,-268192512
	.byte	6
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRA_P02_6_IN',data,rom,cluster('IfxQspi3_MTSRA_P02_6_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRA_P02_6_IN'
	.global	IfxQspi3_MTSRA_P02_6_IN
	.align	4
IfxQspi3_MTSRA_P02_6_IN:	.type	object
	.size	IfxQspi3_MTSRA_P02_6_IN,16
	.word	-*********,-268197376
	.byte	6
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRB_P10_6_IN',data,rom,cluster('IfxQspi3_MTSRB_P10_6_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRB_P10_6_IN'
	.global	IfxQspi3_MTSRB_P10_6_IN
	.align	4
IfxQspi3_MTSRB_P10_6_IN:	.type	object
	.size	IfxQspi3_MTSRB_P10_6_IN,16
	.word	-*********,-268193792
	.byte	6
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRD_P33_12_IN',data,rom,cluster('IfxQspi3_MTSRD_P33_12_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRD_P33_12_IN'
	.global	IfxQspi3_MTSRD_P33_12_IN
	.align	4
IfxQspi3_MTSRD_P33_12_IN:	.type	object
	.size	IfxQspi3_MTSRD_P33_12_IN,16
	.word	-*********,-*********
	.byte	12
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRE_P22_0_IN',data,rom,cluster('IfxQspi3_MTSRE_P22_0_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRE_P22_0_IN'
	.global	IfxQspi3_MTSRE_P22_0_IN
	.align	4
IfxQspi3_MTSRE_P22_0_IN:	.type	object
	.size	IfxQspi3_MTSRE_P22_0_IN,16
	.word	-*********,-*********
	.space	4
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_MTSR_P20_12_OUT',data,rom,cluster('IfxQspi0_MTSR_P20_12_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_MTSR_P20_12_OUT'
	.global	IfxQspi0_MTSR_P20_12_OUT
	.align	4
IfxQspi0_MTSR_P20_12_OUT:	.type	object
	.size	IfxQspi0_MTSR_P20_12_OUT,16
	.word	-*********,-*********
	.byte	12
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_MTSR_P20_14_OUT',data,rom,cluster('IfxQspi0_MTSR_P20_14_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_MTSR_P20_14_OUT'
	.global	IfxQspi0_MTSR_P20_14_OUT
	.align	4
IfxQspi0_MTSR_P20_14_OUT:	.type	object
	.size	IfxQspi0_MTSR_P20_14_OUT,16
	.word	-*********,-*********
	.byte	14
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P10_1_OUT',data,rom,cluster('IfxQspi1_MTSR_P10_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P10_1_OUT'
	.global	IfxQspi1_MTSR_P10_1_OUT
	.align	4
IfxQspi1_MTSR_P10_1_OUT:	.type	object
	.size	IfxQspi1_MTSR_P10_1_OUT,16
	.word	-268428032,-268193792
	.byte	1
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P10_3_OUT',data,rom,cluster('IfxQspi1_MTSR_P10_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P10_3_OUT'
	.global	IfxQspi1_MTSR_P10_3_OUT
	.align	4
IfxQspi1_MTSR_P10_3_OUT:	.type	object
	.size	IfxQspi1_MTSR_P10_3_OUT,16
	.word	-268428032,-268193792
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P10_4_OUT',data,rom,cluster('IfxQspi1_MTSR_P10_4_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P10_4_OUT'
	.global	IfxQspi1_MTSR_P10_4_OUT
	.align	4
IfxQspi1_MTSR_P10_4_OUT:	.type	object
	.size	IfxQspi1_MTSR_P10_4_OUT,16
	.word	-268428032,-268193792
	.byte	4
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P11_9_OUT',data,rom,cluster('IfxQspi1_MTSR_P11_9_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_MTSR_P11_9_OUT'
	.global	IfxQspi1_MTSR_P11_9_OUT
	.align	4
IfxQspi1_MTSR_P11_9_OUT:	.type	object
	.size	IfxQspi1_MTSR_P11_9_OUT,16
	.word	-268428032,-268193536
	.byte	9
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRN_P13_2_OUT',data,rom,cluster('IfxQspi2_MTSRN_P13_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRN_P13_2_OUT'
	.global	IfxQspi2_MTSRN_P13_2_OUT
	.align	4
IfxQspi2_MTSRN_P13_2_OUT:	.type	object
	.size	IfxQspi2_MTSRN_P13_2_OUT,16
	.word	-268427776,-268193024
	.byte	2
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRP_P13_3_OUT',data,rom,cluster('IfxQspi2_MTSRP_P13_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSRP_P13_3_OUT'
	.global	IfxQspi2_MTSRP_P13_3_OUT
	.align	4
IfxQspi2_MTSRP_P13_3_OUT:	.type	object
	.size	IfxQspi2_MTSRP_P13_3_OUT,16
	.word	-268427776,-268193024
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSR_P15_5_OUT',data,rom,cluster('IfxQspi2_MTSR_P15_5_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSR_P15_5_OUT'
	.global	IfxQspi2_MTSR_P15_5_OUT
	.align	4
IfxQspi2_MTSR_P15_5_OUT:	.type	object
	.size	IfxQspi2_MTSR_P15_5_OUT,16
	.word	-268427776,-268192512
	.byte	5
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSR_P15_6_OUT',data,rom,cluster('IfxQspi2_MTSR_P15_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_MTSR_P15_6_OUT'
	.global	IfxQspi2_MTSR_P15_6_OUT
	.align	4
IfxQspi2_MTSR_P15_6_OUT:	.type	object
	.size	IfxQspi2_MTSR_P15_6_OUT,16
	.word	-268427776,-268192512
	.byte	6
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRN_P22_2_OUT',data,rom,cluster('IfxQspi3_MTSRN_P22_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRN_P22_2_OUT'
	.global	IfxQspi3_MTSRN_P22_2_OUT
	.align	4
IfxQspi3_MTSRN_P22_2_OUT:	.type	object
	.size	IfxQspi3_MTSRN_P22_2_OUT,16
	.word	-*********,-*********
	.byte	2
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRP_P22_3_OUT',data,rom,cluster('IfxQspi3_MTSRP_P22_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSRP_P22_3_OUT'
	.global	IfxQspi3_MTSRP_P22_3_OUT
	.align	4
IfxQspi3_MTSRP_P22_3_OUT:	.type	object
	.size	IfxQspi3_MTSRP_P22_3_OUT,16
	.word	-*********,-*********
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P02_6_OUT',data,rom,cluster('IfxQspi3_MTSR_P02_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P02_6_OUT'
	.global	IfxQspi3_MTSR_P02_6_OUT
	.align	4
IfxQspi3_MTSR_P02_6_OUT:	.type	object
	.size	IfxQspi3_MTSR_P02_6_OUT,16
	.word	-*********,-268197376
	.byte	6
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P10_6_OUT',data,rom,cluster('IfxQspi3_MTSR_P10_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P10_6_OUT'
	.global	IfxQspi3_MTSR_P10_6_OUT
	.align	4
IfxQspi3_MTSR_P10_6_OUT:	.type	object
	.size	IfxQspi3_MTSR_P10_6_OUT,16
	.word	-*********,-268193792
	.byte	6
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P22_0_OUT',data,rom,cluster('IfxQspi3_MTSR_P22_0_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P22_0_OUT'
	.global	IfxQspi3_MTSR_P22_0_OUT
	.align	4
IfxQspi3_MTSR_P22_0_OUT:	.type	object
	.size	IfxQspi3_MTSR_P22_0_OUT,16
	.word	-*********,-*********
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P33_12_OUT',data,rom,cluster('IfxQspi3_MTSR_P33_12_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_MTSR_P33_12_OUT'
	.global	IfxQspi3_MTSR_P33_12_OUT
	.align	4
IfxQspi3_MTSR_P33_12_OUT:	.type	object
	.size	IfxQspi3_MTSR_P33_12_OUT,16
	.word	-*********,-*********
	.byte	12
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SCLKA_P20_11_IN',data,rom,cluster('IfxQspi0_SCLKA_P20_11_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SCLKA_P20_11_IN'
	.global	IfxQspi0_SCLKA_P20_11_IN
	.align	4
IfxQspi0_SCLKA_P20_11_IN:	.type	object
	.size	IfxQspi0_SCLKA_P20_11_IN,16
	.word	-*********,-*********
	.byte	11
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLKA_P10_2_IN',data,rom,cluster('IfxQspi1_SCLKA_P10_2_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLKA_P10_2_IN'
	.global	IfxQspi1_SCLKA_P10_2_IN
	.align	4
IfxQspi1_SCLKA_P10_2_IN:	.type	object
	.size	IfxQspi1_SCLKA_P10_2_IN,16
	.word	-268428032,-268193792
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLKB_P11_6_IN',data,rom,cluster('IfxQspi1_SCLKB_P11_6_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLKB_P11_6_IN'
	.global	IfxQspi1_SCLKB_P11_6_IN
	.align	4
IfxQspi1_SCLKB_P11_6_IN:	.type	object
	.size	IfxQspi1_SCLKB_P11_6_IN,16
	.word	-268428032,-268193536
	.byte	6
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKA_P15_3_IN',data,rom,cluster('IfxQspi2_SCLKA_P15_3_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKA_P15_3_IN'
	.global	IfxQspi2_SCLKA_P15_3_IN
	.align	4
IfxQspi2_SCLKA_P15_3_IN:	.type	object
	.size	IfxQspi2_SCLKA_P15_3_IN,16
	.word	-268427776,-268192512
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKB_P15_8_IN',data,rom,cluster('IfxQspi2_SCLKB_P15_8_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKB_P15_8_IN'
	.global	IfxQspi2_SCLKB_P15_8_IN
	.align	4
IfxQspi2_SCLKB_P15_8_IN:	.type	object
	.size	IfxQspi2_SCLKB_P15_8_IN,16
	.word	-268427776,-268192512
	.byte	8
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKA_P02_7_IN',data,rom,cluster('IfxQspi3_SCLKA_P02_7_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKA_P02_7_IN'
	.global	IfxQspi3_SCLKA_P02_7_IN
	.align	4
IfxQspi3_SCLKA_P02_7_IN:	.type	object
	.size	IfxQspi3_SCLKA_P02_7_IN,16
	.word	-*********,-268197376
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKB_P10_8_IN',data,rom,cluster('IfxQspi3_SCLKB_P10_8_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKB_P10_8_IN'
	.global	IfxQspi3_SCLKB_P10_8_IN
	.align	4
IfxQspi3_SCLKB_P10_8_IN:	.type	object
	.size	IfxQspi3_SCLKB_P10_8_IN,16
	.word	-*********,-268193792
	.byte	8
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKD_P33_11_IN',data,rom,cluster('IfxQspi3_SCLKD_P33_11_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKD_P33_11_IN'
	.global	IfxQspi3_SCLKD_P33_11_IN
	.align	4
IfxQspi3_SCLKD_P33_11_IN:	.type	object
	.size	IfxQspi3_SCLKD_P33_11_IN,16
	.word	-*********,-*********
	.byte	11
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKE_P22_3_IN',data,rom,cluster('IfxQspi3_SCLKE_P22_3_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKE_P22_3_IN'
	.global	IfxQspi3_SCLKE_P22_3_IN
	.align	4
IfxQspi3_SCLKE_P22_3_IN:	.type	object
	.size	IfxQspi3_SCLKE_P22_3_IN,16
	.word	-*********,-*********
	.byte	3
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SCLK_P20_11_OUT',data,rom,cluster('IfxQspi0_SCLK_P20_11_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SCLK_P20_11_OUT'
	.global	IfxQspi0_SCLK_P20_11_OUT
	.align	4
IfxQspi0_SCLK_P20_11_OUT:	.type	object
	.size	IfxQspi0_SCLK_P20_11_OUT,16
	.word	-*********,-*********
	.byte	11
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SCLK_P20_13_OUT',data,rom,cluster('IfxQspi0_SCLK_P20_13_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SCLK_P20_13_OUT'
	.global	IfxQspi0_SCLK_P20_13_OUT
	.align	4
IfxQspi0_SCLK_P20_13_OUT:	.type	object
	.size	IfxQspi0_SCLK_P20_13_OUT,16
	.word	-*********,-*********
	.byte	13
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLK_P10_2_OUT',data,rom,cluster('IfxQspi1_SCLK_P10_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLK_P10_2_OUT'
	.global	IfxQspi1_SCLK_P10_2_OUT
	.align	4
IfxQspi1_SCLK_P10_2_OUT:	.type	object
	.size	IfxQspi1_SCLK_P10_2_OUT,16
	.word	-268428032,-268193792
	.byte	2
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLK_P11_6_OUT',data,rom,cluster('IfxQspi1_SCLK_P11_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SCLK_P11_6_OUT'
	.global	IfxQspi1_SCLK_P11_6_OUT
	.align	4
IfxQspi1_SCLK_P11_6_OUT:	.type	object
	.size	IfxQspi1_SCLK_P11_6_OUT,16
	.word	-268428032,-268193536
	.byte	6
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKN_P13_0_OUT',data,rom,cluster('IfxQspi2_SCLKN_P13_0_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKN_P13_0_OUT'
	.global	IfxQspi2_SCLKN_P13_0_OUT
	.align	4
IfxQspi2_SCLKN_P13_0_OUT:	.type	object
	.size	IfxQspi2_SCLKN_P13_0_OUT,16
	.word	-268427776,-268193024
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKP_P13_1_OUT',data,rom,cluster('IfxQspi2_SCLKP_P13_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLKP_P13_1_OUT'
	.global	IfxQspi2_SCLKP_P13_1_OUT
	.align	4
IfxQspi2_SCLKP_P13_1_OUT:	.type	object
	.size	IfxQspi2_SCLKP_P13_1_OUT,16
	.word	-268427776,-268193024
	.byte	1
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLK_P15_3_OUT',data,rom,cluster('IfxQspi2_SCLK_P15_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLK_P15_3_OUT'
	.global	IfxQspi2_SCLK_P15_3_OUT
	.align	4
IfxQspi2_SCLK_P15_3_OUT:	.type	object
	.size	IfxQspi2_SCLK_P15_3_OUT,16
	.word	-268427776,-268192512
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLK_P15_6_OUT',data,rom,cluster('IfxQspi2_SCLK_P15_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLK_P15_6_OUT'
	.global	IfxQspi2_SCLK_P15_6_OUT
	.align	4
IfxQspi2_SCLK_P15_6_OUT:	.type	object
	.size	IfxQspi2_SCLK_P15_6_OUT,16
	.word	-268427776,-268192512
	.byte	6
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLK_P15_8_OUT',data,rom,cluster('IfxQspi2_SCLK_P15_8_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SCLK_P15_8_OUT'
	.global	IfxQspi2_SCLK_P15_8_OUT
	.align	4
IfxQspi2_SCLK_P15_8_OUT:	.type	object
	.size	IfxQspi2_SCLK_P15_8_OUT,16
	.word	-268427776,-268192512
	.byte	8
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKN_P22_0_OUT',data,rom,cluster('IfxQspi3_SCLKN_P22_0_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKN_P22_0_OUT'
	.global	IfxQspi3_SCLKN_P22_0_OUT
	.align	4
IfxQspi3_SCLKN_P22_0_OUT:	.type	object
	.size	IfxQspi3_SCLKN_P22_0_OUT,16
	.word	-*********,-*********
	.space	4
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKP_P22_1_OUT',data,rom,cluster('IfxQspi3_SCLKP_P22_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLKP_P22_1_OUT'
	.global	IfxQspi3_SCLKP_P22_1_OUT
	.align	4
IfxQspi3_SCLKP_P22_1_OUT:	.type	object
	.size	IfxQspi3_SCLKP_P22_1_OUT,16
	.word	-*********,-*********
	.byte	1
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P02_7_OUT',data,rom,cluster('IfxQspi3_SCLK_P02_7_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P02_7_OUT'
	.global	IfxQspi3_SCLK_P02_7_OUT
	.align	4
IfxQspi3_SCLK_P02_7_OUT:	.type	object
	.size	IfxQspi3_SCLK_P02_7_OUT,16
	.word	-*********,-268197376
	.byte	7
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P10_8_OUT',data,rom,cluster('IfxQspi3_SCLK_P10_8_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P10_8_OUT'
	.global	IfxQspi3_SCLK_P10_8_OUT
	.align	4
IfxQspi3_SCLK_P10_8_OUT:	.type	object
	.size	IfxQspi3_SCLK_P10_8_OUT,16
	.word	-*********,-268193792
	.byte	8
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P22_3_OUT',data,rom,cluster('IfxQspi3_SCLK_P22_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P22_3_OUT'
	.global	IfxQspi3_SCLK_P22_3_OUT
	.align	4
IfxQspi3_SCLK_P22_3_OUT:	.type	object
	.size	IfxQspi3_SCLK_P22_3_OUT,16
	.word	-*********,-*********
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P33_11_OUT',data,rom,cluster('IfxQspi3_SCLK_P33_11_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SCLK_P33_11_OUT'
	.global	IfxQspi3_SCLK_P33_11_OUT
	.align	4
IfxQspi3_SCLK_P33_11_OUT:	.type	object
	.size	IfxQspi3_SCLK_P33_11_OUT,16
	.word	-*********,-*********
	.byte	11
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSIA_P20_13_IN',data,rom,cluster('IfxQspi0_SLSIA_P20_13_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSIA_P20_13_IN'
	.global	IfxQspi0_SLSIA_P20_13_IN
	.align	4
IfxQspi0_SLSIA_P20_13_IN:	.type	object
	.size	IfxQspi0_SLSIA_P20_13_IN,16
	.word	-*********,-*********
	.byte	13
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSIB_P20_9_IN',data,rom,cluster('IfxQspi0_SLSIB_P20_9_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSIB_P20_9_IN'
	.global	IfxQspi0_SLSIB_P20_9_IN
	.align	4
IfxQspi0_SLSIB_P20_9_IN:	.type	object
	.size	IfxQspi0_SLSIB_P20_9_IN,16
	.word	-*********,-*********
	.byte	9
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSIA_P11_10_IN',data,rom,cluster('IfxQspi1_SLSIA_P11_10_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSIA_P11_10_IN'
	.global	IfxQspi1_SLSIA_P11_10_IN
	.align	4
IfxQspi1_SLSIA_P11_10_IN:	.type	object
	.size	IfxQspi1_SLSIA_P11_10_IN,16
	.word	-268428032,-268193536
	.byte	10
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSIA_P15_2_IN',data,rom,cluster('IfxQspi2_SLSIA_P15_2_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSIA_P15_2_IN'
	.global	IfxQspi2_SLSIA_P15_2_IN
	.align	4
IfxQspi2_SLSIA_P15_2_IN:	.type	object
	.size	IfxQspi2_SLSIA_P15_2_IN,16
	.word	-268427776,-268192512
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSIB_P15_1_IN',data,rom,cluster('IfxQspi2_SLSIB_P15_1_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSIB_P15_1_IN'
	.global	IfxQspi2_SLSIB_P15_1_IN
	.align	4
IfxQspi2_SLSIB_P15_1_IN:	.type	object
	.size	IfxQspi2_SLSIB_P15_1_IN,16
	.word	-268427776,-268192512
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSIA_P02_4_IN',data,rom,cluster('IfxQspi3_SLSIA_P02_4_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSIA_P02_4_IN'
	.global	IfxQspi3_SLSIA_P02_4_IN
	.align	4
IfxQspi3_SLSIA_P02_4_IN:	.type	object
	.size	IfxQspi3_SLSIA_P02_4_IN,16
	.word	-*********,-268197376
	.byte	4
	.space	7
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSIC_P33_10_IN',data,rom,cluster('IfxQspi3_SLSIC_P33_10_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSIC_P33_10_IN'
	.global	IfxQspi3_SLSIC_P33_10_IN
	.align	4
IfxQspi3_SLSIC_P33_10_IN:	.type	object
	.size	IfxQspi3_SLSIC_P33_10_IN,16
	.word	-*********,-*********
	.byte	10
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSID_P22_2_IN',data,rom,cluster('IfxQspi3_SLSID_P22_2_IN')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSID_P22_2_IN'
	.global	IfxQspi3_SLSID_P22_2_IN
	.align	4
IfxQspi3_SLSID_P22_2_IN:	.type	object
	.size	IfxQspi3_SLSID_P22_2_IN,16
	.word	-*********,-*********
	.byte	2
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO0_P20_8_OUT',data,rom,cluster('IfxQspi0_SLSO0_P20_8_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO0_P20_8_OUT'
	.global	IfxQspi0_SLSO0_P20_8_OUT
	.align	4
IfxQspi0_SLSO0_P20_8_OUT:	.type	object
	.size	IfxQspi0_SLSO0_P20_8_OUT,20
	.word	-*********
	.space	4
	.word	-*********
	.byte	8
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO13_P15_0_OUT',data,rom,cluster('IfxQspi0_SLSO13_P15_0_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO13_P15_0_OUT'
	.global	IfxQspi0_SLSO13_P15_0_OUT
	.align	4
IfxQspi0_SLSO13_P15_0_OUT:	.type	object
	.size	IfxQspi0_SLSO13_P15_0_OUT,20
	.word	-*********,13,-268192512
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO1_P20_9_OUT',data,rom,cluster('IfxQspi0_SLSO1_P20_9_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO1_P20_9_OUT'
	.global	IfxQspi0_SLSO1_P20_9_OUT
	.align	4
IfxQspi0_SLSO1_P20_9_OUT:	.type	object
	.size	IfxQspi0_SLSO1_P20_9_OUT,20
	.word	-*********,1,-*********
	.byte	9
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO2_P20_13_OUT',data,rom,cluster('IfxQspi0_SLSO2_P20_13_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO2_P20_13_OUT'
	.global	IfxQspi0_SLSO2_P20_13_OUT
	.align	4
IfxQspi0_SLSO2_P20_13_OUT:	.type	object
	.size	IfxQspi0_SLSO2_P20_13_OUT,20
	.word	-*********,2,-*********
	.byte	13
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO3_P11_10_OUT',data,rom,cluster('IfxQspi0_SLSO3_P11_10_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO3_P11_10_OUT'
	.global	IfxQspi0_SLSO3_P11_10_OUT
	.align	4
IfxQspi0_SLSO3_P11_10_OUT:	.type	object
	.size	IfxQspi0_SLSO3_P11_10_OUT,20
	.word	-*********,3,-268193536
	.byte	10
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO4_P11_11_OUT',data,rom,cluster('IfxQspi0_SLSO4_P11_11_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO4_P11_11_OUT'
	.global	IfxQspi0_SLSO4_P11_11_OUT
	.align	4
IfxQspi0_SLSO4_P11_11_OUT:	.type	object
	.size	IfxQspi0_SLSO4_P11_11_OUT,20
	.word	-*********,4,-268193536
	.byte	11
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO5_P11_2_OUT',data,rom,cluster('IfxQspi0_SLSO5_P11_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO5_P11_2_OUT'
	.global	IfxQspi0_SLSO5_P11_2_OUT
	.align	4
IfxQspi0_SLSO5_P11_2_OUT:	.type	object
	.size	IfxQspi0_SLSO5_P11_2_OUT,20
	.word	-*********,5,-268193536
	.byte	2
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO6_P20_10_OUT',data,rom,cluster('IfxQspi0_SLSO6_P20_10_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO6_P20_10_OUT'
	.global	IfxQspi0_SLSO6_P20_10_OUT
	.align	4
IfxQspi0_SLSO6_P20_10_OUT:	.type	object
	.size	IfxQspi0_SLSO6_P20_10_OUT,20
	.word	-*********,6,-*********
	.byte	10
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO7_P33_5_OUT',data,rom,cluster('IfxQspi0_SLSO7_P33_5_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO7_P33_5_OUT'
	.global	IfxQspi0_SLSO7_P33_5_OUT
	.align	4
IfxQspi0_SLSO7_P33_5_OUT:	.type	object
	.size	IfxQspi0_SLSO7_P33_5_OUT,20
	.word	-*********,7,-*********
	.byte	5
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO8_P20_6_OUT',data,rom,cluster('IfxQspi0_SLSO8_P20_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO8_P20_6_OUT'
	.global	IfxQspi0_SLSO8_P20_6_OUT
	.align	4
IfxQspi0_SLSO8_P20_6_OUT:	.type	object
	.size	IfxQspi0_SLSO8_P20_6_OUT,20
	.word	-*********,8,-*********
	.byte	6
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO9_P20_3_OUT',data,rom,cluster('IfxQspi0_SLSO9_P20_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi0_SLSO9_P20_3_OUT'
	.global	IfxQspi0_SLSO9_P20_3_OUT
	.align	4
IfxQspi0_SLSO9_P20_3_OUT:	.type	object
	.size	IfxQspi0_SLSO9_P20_3_OUT,20
	.word	-*********,9,-*********
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO0_P20_8_OUT',data,rom,cluster('IfxQspi1_SLSO0_P20_8_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO0_P20_8_OUT'
	.global	IfxQspi1_SLSO0_P20_8_OUT
	.align	4
IfxQspi1_SLSO0_P20_8_OUT:	.type	object
	.size	IfxQspi1_SLSO0_P20_8_OUT,20
	.word	-268428032
	.space	4
	.word	-*********
	.byte	8
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO10_P10_0_OUT',data,rom,cluster('IfxQspi1_SLSO10_P10_0_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO10_P10_0_OUT'
	.global	IfxQspi1_SLSO10_P10_0_OUT
	.align	4
IfxQspi1_SLSO10_P10_0_OUT:	.type	object
	.size	IfxQspi1_SLSO10_P10_0_OUT,20
	.word	-268428032,10,-268193792
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO1_P20_9_OUT',data,rom,cluster('IfxQspi1_SLSO1_P20_9_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO1_P20_9_OUT'
	.global	IfxQspi1_SLSO1_P20_9_OUT
	.align	4
IfxQspi1_SLSO1_P20_9_OUT:	.type	object
	.size	IfxQspi1_SLSO1_P20_9_OUT,20
	.word	-268428032,1,-*********
	.byte	9
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO2_P20_13_OUT',data,rom,cluster('IfxQspi1_SLSO2_P20_13_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO2_P20_13_OUT'
	.global	IfxQspi1_SLSO2_P20_13_OUT
	.align	4
IfxQspi1_SLSO2_P20_13_OUT:	.type	object
	.size	IfxQspi1_SLSO2_P20_13_OUT,20
	.word	-268428032,2,-*********
	.byte	13
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO3_P11_10_OUT',data,rom,cluster('IfxQspi1_SLSO3_P11_10_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO3_P11_10_OUT'
	.global	IfxQspi1_SLSO3_P11_10_OUT
	.align	4
IfxQspi1_SLSO3_P11_10_OUT:	.type	object
	.size	IfxQspi1_SLSO3_P11_10_OUT,20
	.word	-268428032,3,-268193536
	.byte	10
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO4_P11_11_OUT',data,rom,cluster('IfxQspi1_SLSO4_P11_11_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO4_P11_11_OUT'
	.global	IfxQspi1_SLSO4_P11_11_OUT
	.align	4
IfxQspi1_SLSO4_P11_11_OUT:	.type	object
	.size	IfxQspi1_SLSO4_P11_11_OUT,20
	.word	-268428032,4,-268193536
	.byte	11
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO5_P11_2_OUT',data,rom,cluster('IfxQspi1_SLSO5_P11_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO5_P11_2_OUT'
	.global	IfxQspi1_SLSO5_P11_2_OUT
	.align	4
IfxQspi1_SLSO5_P11_2_OUT:	.type	object
	.size	IfxQspi1_SLSO5_P11_2_OUT,20
	.word	-268428032,5,-268193536
	.byte	2
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO6_P33_10_OUT',data,rom,cluster('IfxQspi1_SLSO6_P33_10_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO6_P33_10_OUT'
	.global	IfxQspi1_SLSO6_P33_10_OUT
	.align	4
IfxQspi1_SLSO6_P33_10_OUT:	.type	object
	.size	IfxQspi1_SLSO6_P33_10_OUT,20
	.word	-268428032,6,-*********
	.byte	10
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO7_P33_5_OUT',data,rom,cluster('IfxQspi1_SLSO7_P33_5_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO7_P33_5_OUT'
	.global	IfxQspi1_SLSO7_P33_5_OUT
	.align	4
IfxQspi1_SLSO7_P33_5_OUT:	.type	object
	.size	IfxQspi1_SLSO7_P33_5_OUT,20
	.word	-268428032,7,-*********
	.byte	5
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO8_P10_4_OUT',data,rom,cluster('IfxQspi1_SLSO8_P10_4_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO8_P10_4_OUT'
	.global	IfxQspi1_SLSO8_P10_4_OUT
	.align	4
IfxQspi1_SLSO8_P10_4_OUT:	.type	object
	.size	IfxQspi1_SLSO8_P10_4_OUT,20
	.word	-268428032,8,-268193792
	.byte	4
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO9_P10_5_OUT',data,rom,cluster('IfxQspi1_SLSO9_P10_5_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi1_SLSO9_P10_5_OUT'
	.global	IfxQspi1_SLSO9_P10_5_OUT
	.align	4
IfxQspi1_SLSO9_P10_5_OUT:	.type	object
	.size	IfxQspi1_SLSO9_P10_5_OUT,20
	.word	-268428032,9,-268193792
	.byte	5
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO0_P15_2_OUT',data,rom,cluster('IfxQspi2_SLSO0_P15_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO0_P15_2_OUT'
	.global	IfxQspi2_SLSO0_P15_2_OUT
	.align	4
IfxQspi2_SLSO0_P15_2_OUT:	.type	object
	.size	IfxQspi2_SLSO0_P15_2_OUT,20
	.word	-268427776
	.space	4
	.word	-268192512
	.byte	2
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO1_P14_2_OUT',data,rom,cluster('IfxQspi2_SLSO1_P14_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO1_P14_2_OUT'
	.global	IfxQspi2_SLSO1_P14_2_OUT
	.align	4
IfxQspi2_SLSO1_P14_2_OUT:	.type	object
	.size	IfxQspi2_SLSO1_P14_2_OUT,20
	.word	-268427776,1,-268192768
	.byte	2
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO2_P14_6_OUT',data,rom,cluster('IfxQspi2_SLSO2_P14_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO2_P14_6_OUT'
	.global	IfxQspi2_SLSO2_P14_6_OUT
	.align	4
IfxQspi2_SLSO2_P14_6_OUT:	.type	object
	.size	IfxQspi2_SLSO2_P14_6_OUT,20
	.word	-268427776,2,-268192768
	.byte	6
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO3_P14_3_OUT',data,rom,cluster('IfxQspi2_SLSO3_P14_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO3_P14_3_OUT'
	.global	IfxQspi2_SLSO3_P14_3_OUT
	.align	4
IfxQspi2_SLSO3_P14_3_OUT:	.type	object
	.size	IfxQspi2_SLSO3_P14_3_OUT,20
	.word	-268427776,3,-268192768
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO4_P14_7_OUT',data,rom,cluster('IfxQspi2_SLSO4_P14_7_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO4_P14_7_OUT'
	.global	IfxQspi2_SLSO4_P14_7_OUT
	.align	4
IfxQspi2_SLSO4_P14_7_OUT:	.type	object
	.size	IfxQspi2_SLSO4_P14_7_OUT,20
	.word	-268427776,4,-268192768
	.byte	7
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO5_P15_1_OUT',data,rom,cluster('IfxQspi2_SLSO5_P15_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO5_P15_1_OUT'
	.global	IfxQspi2_SLSO5_P15_1_OUT
	.align	4
IfxQspi2_SLSO5_P15_1_OUT:	.type	object
	.size	IfxQspi2_SLSO5_P15_1_OUT,20
	.word	-268427776,5,-268192512
	.byte	1
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO6_P33_13_OUT',data,rom,cluster('IfxQspi2_SLSO6_P33_13_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO6_P33_13_OUT'
	.global	IfxQspi2_SLSO6_P33_13_OUT
	.align	4
IfxQspi2_SLSO6_P33_13_OUT:	.type	object
	.size	IfxQspi2_SLSO6_P33_13_OUT,20
	.word	-268427776,6,-*********
	.byte	13
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO7_P20_10_OUT',data,rom,cluster('IfxQspi2_SLSO7_P20_10_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO7_P20_10_OUT'
	.global	IfxQspi2_SLSO7_P20_10_OUT
	.align	4
IfxQspi2_SLSO7_P20_10_OUT:	.type	object
	.size	IfxQspi2_SLSO7_P20_10_OUT,20
	.word	-268427776,7,-*********
	.byte	10
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO8_P20_6_OUT',data,rom,cluster('IfxQspi2_SLSO8_P20_6_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO8_P20_6_OUT'
	.global	IfxQspi2_SLSO8_P20_6_OUT
	.align	4
IfxQspi2_SLSO8_P20_6_OUT:	.type	object
	.size	IfxQspi2_SLSO8_P20_6_OUT,20
	.word	-268427776,8,-*********
	.byte	6
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO9_P20_3_OUT',data,rom,cluster('IfxQspi2_SLSO9_P20_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi2_SLSO9_P20_3_OUT'
	.global	IfxQspi2_SLSO9_P20_3_OUT
	.align	4
IfxQspi2_SLSO9_P20_3_OUT:	.type	object
	.size	IfxQspi2_SLSO9_P20_3_OUT,20
	.word	-268427776,9,-*********
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO0_P02_4_OUT',data,rom,cluster('IfxQspi3_SLSO0_P02_4_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO0_P02_4_OUT'
	.global	IfxQspi3_SLSO0_P02_4_OUT
	.align	4
IfxQspi3_SLSO0_P02_4_OUT:	.type	object
	.size	IfxQspi3_SLSO0_P02_4_OUT,20
	.word	-*********
	.space	4
	.word	-268197376
	.byte	4
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO11_P33_10_OUT',data,rom,cluster('IfxQspi3_SLSO11_P33_10_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO11_P33_10_OUT'
	.global	IfxQspi3_SLSO11_P33_10_OUT
	.align	4
IfxQspi3_SLSO11_P33_10_OUT:	.type	object
	.size	IfxQspi3_SLSO11_P33_10_OUT,20
	.word	-*********,11,-*********
	.byte	10
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO12_P22_2_OUT',data,rom,cluster('IfxQspi3_SLSO12_P22_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO12_P22_2_OUT'
	.global	IfxQspi3_SLSO12_P22_2_OUT
	.align	4
IfxQspi3_SLSO12_P22_2_OUT:	.type	object
	.size	IfxQspi3_SLSO12_P22_2_OUT,20
	.word	-*********,12,-*********
	.byte	2
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO13_P23_1_OUT',data,rom,cluster('IfxQspi3_SLSO13_P23_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO13_P23_1_OUT'
	.global	IfxQspi3_SLSO13_P23_1_OUT
	.align	4
IfxQspi3_SLSO13_P23_1_OUT:	.type	object
	.size	IfxQspi3_SLSO13_P23_1_OUT,20
	.word	-*********,13,-268188928
	.byte	1
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO1_P02_0_OUT',data,rom,cluster('IfxQspi3_SLSO1_P02_0_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO1_P02_0_OUT'
	.global	IfxQspi3_SLSO1_P02_0_OUT
	.align	4
IfxQspi3_SLSO1_P02_0_OUT:	.type	object
	.size	IfxQspi3_SLSO1_P02_0_OUT,20
	.word	-*********,1,-268197376
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO1_P33_9_OUT',data,rom,cluster('IfxQspi3_SLSO1_P33_9_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO1_P33_9_OUT'
	.global	IfxQspi3_SLSO1_P33_9_OUT
	.align	4
IfxQspi3_SLSO1_P33_9_OUT:	.type	object
	.size	IfxQspi3_SLSO1_P33_9_OUT,20
	.word	-*********,1,-*********
	.byte	9
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO2_P02_1_OUT',data,rom,cluster('IfxQspi3_SLSO2_P02_1_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO2_P02_1_OUT'
	.global	IfxQspi3_SLSO2_P02_1_OUT
	.align	4
IfxQspi3_SLSO2_P02_1_OUT:	.type	object
	.size	IfxQspi3_SLSO2_P02_1_OUT,20
	.word	-*********,2,-268197376
	.byte	1
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO2_P33_8_OUT',data,rom,cluster('IfxQspi3_SLSO2_P33_8_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO2_P33_8_OUT'
	.global	IfxQspi3_SLSO2_P33_8_OUT
	.align	4
IfxQspi3_SLSO2_P33_8_OUT:	.type	object
	.size	IfxQspi3_SLSO2_P33_8_OUT,20
	.word	-*********,2,-*********
	.byte	8
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO3_P02_2_OUT',data,rom,cluster('IfxQspi3_SLSO3_P02_2_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO3_P02_2_OUT'
	.global	IfxQspi3_SLSO3_P02_2_OUT
	.align	4
IfxQspi3_SLSO3_P02_2_OUT:	.type	object
	.size	IfxQspi3_SLSO3_P02_2_OUT,20
	.word	-*********,3,-268197376
	.byte	2
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO4_P02_3_OUT',data,rom,cluster('IfxQspi3_SLSO4_P02_3_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO4_P02_3_OUT'
	.global	IfxQspi3_SLSO4_P02_3_OUT
	.align	4
IfxQspi3_SLSO4_P02_3_OUT:	.type	object
	.size	IfxQspi3_SLSO4_P02_3_OUT,20
	.word	-*********,4,-268197376
	.byte	3
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO4_P23_5_OUT',data,rom,cluster('IfxQspi3_SLSO4_P23_5_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO4_P23_5_OUT'
	.global	IfxQspi3_SLSO4_P23_5_OUT
	.align	4
IfxQspi3_SLSO4_P23_5_OUT:	.type	object
	.size	IfxQspi3_SLSO4_P23_5_OUT,20
	.word	-*********,4,-268188928
	.byte	5
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO5_P02_8_OUT',data,rom,cluster('IfxQspi3_SLSO5_P02_8_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO5_P02_8_OUT'
	.global	IfxQspi3_SLSO5_P02_8_OUT
	.align	4
IfxQspi3_SLSO5_P02_8_OUT:	.type	object
	.size	IfxQspi3_SLSO5_P02_8_OUT,20
	.word	-*********,5,-268197376
	.byte	8
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO5_P23_4_OUT',data,rom,cluster('IfxQspi3_SLSO5_P23_4_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO5_P23_4_OUT'
	.global	IfxQspi3_SLSO5_P23_4_OUT
	.align	4
IfxQspi3_SLSO5_P23_4_OUT:	.type	object
	.size	IfxQspi3_SLSO5_P23_4_OUT,20
	.word	-*********,5,-268188928
	.byte	4
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO6_P00_8_OUT',data,rom,cluster('IfxQspi3_SLSO6_P00_8_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO6_P00_8_OUT'
	.global	IfxQspi3_SLSO6_P00_8_OUT
	.align	4
IfxQspi3_SLSO6_P00_8_OUT:	.type	object
	.size	IfxQspi3_SLSO6_P00_8_OUT,20
	.word	-*********,6,-268197888
	.byte	8
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO7_P00_9_OUT',data,rom,cluster('IfxQspi3_SLSO7_P00_9_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO7_P00_9_OUT'
	.global	IfxQspi3_SLSO7_P00_9_OUT
	.align	4
IfxQspi3_SLSO7_P00_9_OUT:	.type	object
	.size	IfxQspi3_SLSO7_P00_9_OUT,20
	.word	-*********,7,-268197888
	.byte	9
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO7_P33_7_OUT',data,rom,cluster('IfxQspi3_SLSO7_P33_7_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO7_P33_7_OUT'
	.global	IfxQspi3_SLSO7_P33_7_OUT
	.align	4
IfxQspi3_SLSO7_P33_7_OUT:	.type	object
	.size	IfxQspi3_SLSO7_P33_7_OUT,20
	.word	-*********,7,-*********
	.byte	7
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO8_P10_5_OUT',data,rom,cluster('IfxQspi3_SLSO8_P10_5_OUT')
	.sect	'.rodata.IfxQspi_PinMap.IfxQspi3_SLSO8_P10_5_OUT'
	.global	IfxQspi3_SLSO8_P10_5_OUT
	.align	4
IfxQspi3_SLSO8_P10_5_OUT:	.type	object
	.size	IfxQspi3_SLSO8_P10_5_OUT,20
	.word	-*********,8,-268193792
	.byte	5
	.space	3
	.byte	152
	.space	3
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Hsicin_In_pinTable',data,cluster('IfxQspi_Hsicin_In_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Hsicin_In_pinTable'
	.global	IfxQspi_Hsicin_In_pinTable
	.align	4
IfxQspi_Hsicin_In_pinTable:	.type	object
	.size	IfxQspi_Hsicin_In_pinTable,32
	.space	16
	.word	IfxQspi2_HSICINA_P15_2_IN,IfxQspi2_HSICINB_P15_3_IN,IfxQspi3_HSICINA_P33_9_IN,IfxQspi3_HSICINB_P33_10_IN
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Mrst_In_pinTable',data,cluster('IfxQspi_Mrst_In_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Mrst_In_pinTable'
	.global	IfxQspi_Mrst_In_pinTable
	.align	4
IfxQspi_Mrst_In_pinTable:	.type	object
	.size	IfxQspi_Mrst_In_pinTable,96
	.word	IfxQspi0_MRSTA_P20_12_IN
	.space	20
	.word	IfxQspi1_MRSTA_P10_1_IN,IfxQspi1_MRSTB_P11_3_IN
	.space	16
	.word	IfxQspi2_MRSTA_P15_4_IN,IfxQspi2_MRSTB_P15_7_IN,IfxQspi2_MRSTCN_P21_2_IN
	.space	4
	.word	IfxQspi2_MRSTE_P15_2_IN
	.space	4
	.word	IfxQspi3_MRSTA_P02_5_IN,IfxQspi3_MRSTB_P10_7_IN
	.space	4
	.word	IfxQspi3_MRSTD_P33_13_IN,IfxQspi3_MRSTE_P22_1_IN,IfxQspi3_MRSTFN_P21_2_IN
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Mrst_Out_pinTable',data,cluster('IfxQspi_Mrst_Out_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Mrst_Out_pinTable'
	.global	IfxQspi_Mrst_Out_pinTable
	.align	4
IfxQspi_Mrst_Out_pinTable:	.type	object
	.size	IfxQspi_Mrst_Out_pinTable,64
	.word	IfxQspi0_MRST_P20_12_OUT
	.space	12
	.word	IfxQspi1_MRST_P10_1_OUT,IfxQspi1_MRST_P10_6_OUT,IfxQspi1_MRST_P11_3_OUT
	.space	4
	.word	IfxQspi2_MRST_P15_4_OUT,IfxQspi2_MRST_P15_7_OUT
	.space	8
	.word	IfxQspi3_MRST_P02_5_OUT,IfxQspi3_MRST_P10_7_OUT,IfxQspi3_MRST_P22_1_OUT,IfxQspi3_MRST_P33_13_OUT
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Mtsr_In_pinTable',data,cluster('IfxQspi_Mtsr_In_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Mtsr_In_pinTable'
	.global	IfxQspi_Mtsr_In_pinTable
	.align	4
IfxQspi_Mtsr_In_pinTable:	.type	object
	.size	IfxQspi_Mtsr_In_pinTable,80
	.word	IfxQspi0_MTSRA_P20_14_IN
	.space	16
	.word	IfxQspi1_MTSRA_P10_3_IN,IfxQspi1_MTSRB_P11_9_IN,IfxQspi1_MTSRC_P10_4_IN
	.space	8
	.word	IfxQspi2_MTSRA_P15_5_IN,IfxQspi2_MTSRB_P15_6_IN
	.space	12
	.word	IfxQspi3_MTSRA_P02_6_IN,IfxQspi3_MTSRB_P10_6_IN
	.space	4
	.word	IfxQspi3_MTSRD_P33_12_IN,IfxQspi3_MTSRE_P22_0_IN
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Mtsr_Out_pinTable',data,cluster('IfxQspi_Mtsr_Out_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Mtsr_Out_pinTable'
	.global	IfxQspi_Mtsr_Out_pinTable
	.align	4
IfxQspi_Mtsr_Out_pinTable:	.type	object
	.size	IfxQspi_Mtsr_Out_pinTable,96
	.word	IfxQspi0_MTSR_P20_12_OUT,IfxQspi0_MTSR_P20_14_OUT
	.space	16
	.word	IfxQspi1_MTSR_P10_1_OUT,IfxQspi1_MTSR_P10_3_OUT,IfxQspi1_MTSR_P10_4_OUT,IfxQspi1_MTSR_P11_9_OUT
	.space	8
	.word	IfxQspi2_MTSRN_P13_2_OUT,IfxQspi2_MTSRP_P13_3_OUT,IfxQspi2_MTSR_P15_5_OUT,IfxQspi2_MTSR_P15_6_OUT
	.space	8
	.word	IfxQspi3_MTSR_P02_6_OUT,IfxQspi3_MTSR_P10_6_OUT,IfxQspi3_MTSR_P22_0_OUT,IfxQspi3_MTSRN_P22_2_OUT
	.word	IfxQspi3_MTSRP_P22_3_OUT,IfxQspi3_MTSR_P33_12_OUT
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Sclk_In_pinTable',data,cluster('IfxQspi_Sclk_In_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Sclk_In_pinTable'
	.global	IfxQspi_Sclk_In_pinTable
	.align	4
IfxQspi_Sclk_In_pinTable:	.type	object
	.size	IfxQspi_Sclk_In_pinTable,80
	.word	IfxQspi0_SCLKA_P20_11_IN
	.space	16
	.word	IfxQspi1_SCLKA_P10_2_IN,IfxQspi1_SCLKB_P11_6_IN
	.space	12
	.word	IfxQspi2_SCLKA_P15_3_IN,IfxQspi2_SCLKB_P15_8_IN
	.space	12
	.word	IfxQspi3_SCLKA_P02_7_IN,IfxQspi3_SCLKB_P10_8_IN
	.space	4
	.word	IfxQspi3_SCLKD_P33_11_IN,IfxQspi3_SCLKE_P22_3_IN
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Sclk_Out_pinTable',data,cluster('IfxQspi_Sclk_Out_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Sclk_Out_pinTable'
	.global	IfxQspi_Sclk_Out_pinTable
	.align	4
IfxQspi_Sclk_Out_pinTable:	.type	object
	.size	IfxQspi_Sclk_Out_pinTable,96
	.word	IfxQspi0_SCLK_P20_11_OUT,IfxQspi0_SCLK_P20_13_OUT
	.space	16
	.word	IfxQspi1_SCLK_P10_2_OUT,IfxQspi1_SCLK_P11_6_OUT
	.space	16
	.word	IfxQspi2_SCLKN_P13_0_OUT,IfxQspi2_SCLKP_P13_1_OUT,IfxQspi2_SCLK_P15_3_OUT,IfxQspi2_SCLK_P15_6_OUT
	.word	IfxQspi2_SCLK_P15_8_OUT
	.space	4
	.word	IfxQspi3_SCLK_P02_7_OUT,IfxQspi3_SCLK_P10_8_OUT,IfxQspi3_SCLKN_P22_0_OUT,IfxQspi3_SCLKP_P22_1_OUT
	.word	IfxQspi3_SCLK_P22_3_OUT,IfxQspi3_SCLK_P33_11_OUT
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Slsi_In_pinTable',data,cluster('IfxQspi_Slsi_In_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Slsi_In_pinTable'
	.global	IfxQspi_Slsi_In_pinTable
	.align	4
IfxQspi_Slsi_In_pinTable:	.type	object
	.size	IfxQspi_Slsi_In_pinTable,64
	.word	IfxQspi0_SLSIA_P20_13_IN,IfxQspi0_SLSIB_P20_9_IN
	.space	8
	.word	IfxQspi1_SLSIA_P11_10_IN
	.space	12
	.word	IfxQspi2_SLSIA_P15_2_IN,IfxQspi2_SLSIB_P15_1_IN
	.space	8
	.word	IfxQspi3_SLSIA_P02_4_IN
	.space	4
	.word	IfxQspi3_SLSIC_P33_10_IN,IfxQspi3_SLSID_P22_2_IN
	.sdecl	'.data.IfxQspi_PinMap.IfxQspi_Slso_Out_pinTable',data,cluster('IfxQspi_Slso_Out_pinTable')
	.sect	'.data.IfxQspi_PinMap.IfxQspi_Slso_Out_pinTable'
	.global	IfxQspi_Slso_Out_pinTable
	.align	4
IfxQspi_Slso_Out_pinTable:	.type	object
	.size	IfxQspi_Slso_Out_pinTable,448
	.word	IfxQspi0_SLSO0_P20_8_OUT
	.space	4
	.word	IfxQspi0_SLSO1_P20_9_OUT
	.space	4
	.word	IfxQspi0_SLSO2_P20_13_OUT
	.space	4
	.word	IfxQspi0_SLSO3_P11_10_OUT
	.space	4
	.word	IfxQspi0_SLSO4_P11_11_OUT
	.space	4
	.word	IfxQspi0_SLSO5_P11_2_OUT
	.space	4
	.word	IfxQspi0_SLSO6_P20_10_OUT
	.space	4
	.word	IfxQspi0_SLSO7_P33_5_OUT
	.space	4
	.word	IfxQspi0_SLSO8_P20_6_OUT
	.space	4
	.word	IfxQspi0_SLSO9_P20_3_OUT
	.space	28
	.word	IfxQspi0_SLSO13_P15_0_OUT
	.space	4
	.word	IfxQspi1_SLSO0_P20_8_OUT
	.space	4
	.word	IfxQspi1_SLSO1_P20_9_OUT
	.space	4
	.word	IfxQspi1_SLSO2_P20_13_OUT
	.space	4
	.word	IfxQspi1_SLSO3_P11_10_OUT
	.space	4
	.word	IfxQspi1_SLSO4_P11_11_OUT
	.space	4
	.word	IfxQspi1_SLSO5_P11_2_OUT
	.space	4
	.word	IfxQspi1_SLSO6_P33_10_OUT
	.space	4
	.word	IfxQspi1_SLSO7_P33_5_OUT
	.space	4
	.word	IfxQspi1_SLSO8_P10_4_OUT
	.space	4
	.word	IfxQspi1_SLSO9_P10_5_OUT
	.space	4
	.word	IfxQspi1_SLSO10_P10_0_OUT
	.space	28
	.word	IfxQspi2_SLSO0_P15_2_OUT
	.space	4
	.word	IfxQspi2_SLSO1_P14_2_OUT
	.space	4
	.word	IfxQspi2_SLSO2_P14_6_OUT
	.space	4
	.word	IfxQspi2_SLSO3_P14_3_OUT
	.space	4
	.word	IfxQspi2_SLSO4_P14_7_OUT
	.space	4
	.word	IfxQspi2_SLSO5_P15_1_OUT
	.space	4
	.word	IfxQspi2_SLSO6_P33_13_OUT
	.space	4
	.word	IfxQspi2_SLSO7_P20_10_OUT
	.space	4
	.word	IfxQspi2_SLSO8_P20_6_OUT
	.space	4
	.word	IfxQspi2_SLSO9_P20_3_OUT
	.space	36
	.word	IfxQspi3_SLSO0_P02_4_OUT
	.space	4
	.word	IfxQspi3_SLSO1_P02_0_OUT,IfxQspi3_SLSO1_P33_9_OUT,IfxQspi3_SLSO2_P02_1_OUT,IfxQspi3_SLSO2_P33_8_OUT
	.word	IfxQspi3_SLSO3_P02_2_OUT
	.space	4
	.word	IfxQspi3_SLSO4_P02_3_OUT,IfxQspi3_SLSO4_P23_5_OUT,IfxQspi3_SLSO5_P02_8_OUT,IfxQspi3_SLSO5_P23_4_OUT
	.word	IfxQspi3_SLSO6_P00_8_OUT
	.space	4
	.word	IfxQspi3_SLSO7_P00_9_OUT,IfxQspi3_SLSO7_P33_7_OUT,IfxQspi3_SLSO8_P10_5_OUT
	.space	20
	.word	IfxQspi3_SLSO11_P33_10_OUT
	.space	4
	.word	IfxQspi3_SLSO12_P22_2_OUT
	.space	4
	.word	IfxQspi3_SLSO13_P23_1_OUT
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	44293
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	238
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	241
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	286
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	298
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	352
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	384
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	384
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	352
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1419
	.byte	4,2,35,0,0,14,4
	.word	493
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1977
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2414
	.byte	4,2,35,0,0,14,24
	.word	493
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3041
	.byte	4,2,35,0,0,14,8
	.word	493
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3366
	.byte	4,2,35,0,0,14,12
	.word	493
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3706
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5369
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6382
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6506
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6770
	.byte	4,2,35,0,0,14,76
	.word	493
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	808
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1379
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1498
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1538
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1722
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1937
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2154
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2374
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1538
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2688
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2728
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3001
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3317
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3357
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3657
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3697
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4032
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4318
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3357
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4465
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4634
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4806
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4981
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5155
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5329
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5505
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5661
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5994
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6342
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3357
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6466
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6715
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6974
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7014
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7070
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7637
	.byte	4,3,35,252,1,0,16
	.word	7677
	.byte	3
	.word	8280
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8285
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	493
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8290
	.byte	6,0,19
	.word	246
	.byte	20
	.word	272
	.byte	6,0,19
	.word	307
	.byte	20
	.word	339
	.byte	6,0,19
	.word	389
	.byte	20
	.word	408
	.byte	6,0,19
	.word	424
	.byte	20
	.word	439
	.byte	20
	.word	453
	.byte	6,0,19
	.word	8393
	.byte	20
	.word	8421
	.byte	20
	.word	8435
	.byte	20
	.word	8453
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8546
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	470
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	486
	.byte	22,1,3
	.word	8614
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8616
	.byte	10
	.byte	'_Ifx_QSPI_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_QSPI_ACCEN0_Bits',0,6,79,3
	.word	8639
	.byte	10
	.byte	'_Ifx_QSPI_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_QSPI_ACCEN1_Bits',0,6,85,3
	.word	9198
	.byte	10
	.byte	'_Ifx_QSPI_BACON_Bits',0,6,88,16,4,11
	.byte	'LAST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPRE',0,1
	.word	493
	.byte	3,4,2,35,0,11
	.byte	'IDLE',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'LPRE',0,2
	.word	510
	.byte	3,6,2,35,0,11
	.byte	'LEAD',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'TPRE',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'TRAIL',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PARTYP',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'UINT',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'MSB',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'BYTE',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'DL',0,2
	.word	510
	.byte	5,4,2,35,2,11
	.byte	'CS',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_QSPI_BACON_Bits',0,6,103,3
	.word	9277
	.byte	10
	.byte	'_Ifx_QSPI_BACONENTRY_Bits',0,6,106,16,4,11
	.byte	'E',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_QSPI_BACONENTRY_Bits',0,6,109,3
	.word	9538
	.byte	10
	.byte	'_Ifx_QSPI_CAPCON_Bits',0,6,112,16,4,11
	.byte	'CAP',0,2
	.word	510
	.byte	15,1,2,35,0,11
	.byte	'OVF',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EDGECON',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'INS',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'EN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	510
	.byte	7,4,2,35,2,11
	.byte	'CAPC',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'CAPS',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'CAPF',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'CAPSEL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_QSPI_CAPCON_Bits',0,6,124,3
	.word	9616
	.byte	10
	.byte	'_Ifx_QSPI_CLC_Bits',0,6,127,16,4,11
	.byte	'DISR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_QSPI_CLC_Bits',0,6,134,1,3
	.word	9840
	.byte	10
	.byte	'_Ifx_QSPI_DATAENTRY_Bits',0,6,137,1,16,4,11
	.byte	'E',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_QSPI_DATAENTRY_Bits',0,6,140,1,3
	.word	9984
	.byte	10
	.byte	'_Ifx_QSPI_ECON_Bits',0,6,143,1,16,4,11
	.byte	'Q',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'A',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'B',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'C',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'CPH',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CPOL',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PAREN',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	15,2,2,35,0,11
	.byte	'BE',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_QSPI_ECON_Bits',0,6,154,1,3
	.word	10062
	.byte	10
	.byte	'_Ifx_QSPI_FLAGSCLEAR_Bits',0,6,157,1,16,4,11
	.byte	'ERRORCLEARS',0,2
	.word	510
	.byte	9,7,2,35,0,11
	.byte	'TXC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RXC',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PT1C',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PT2C',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'USRC',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_QSPI_FLAGSCLEAR_Bits',0,6,167,1,3
	.word	10254
	.byte	10
	.byte	'_Ifx_QSPI_GLOBALCON1_Bits',0,6,170,1,16,4,11
	.byte	'ERRORENS',0,2
	.word	510
	.byte	9,7,2,35,0,11
	.byte	'TXEN',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RXEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PT1EN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PT2EN',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'USREN',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TXFIFOINT',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'RXFIFOINT',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'PT1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PT2',0,2
	.word	510
	.byte	3,6,2,35,2,11
	.byte	'TXFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'RXFM',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_QSPI_GLOBALCON1_Bits',0,6,186,1,3
	.word	10468
	.byte	10
	.byte	'_Ifx_QSPI_GLOBALCON_Bits',0,6,189,1,16,4,11
	.byte	'TQ',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EXPECT',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'LB',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'DEL0',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'STROBE',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'SRF',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'STIP',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'MS',0,1
	.word	493
	.byte	2,5,2,35,3,11
	.byte	'AREN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RESETS',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_QSPI_GLOBALCON_Bits',0,6,205,1,3
	.word	10788
	.byte	10
	.byte	'_Ifx_QSPI_ID_Bits',0,6,208,1,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_QSPI_ID_Bits',0,6,213,1,3
	.word	11085
	.byte	10
	.byte	'_Ifx_QSPI_KRST0_Bits',0,6,216,1,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_QSPI_KRST0_Bits',0,6,221,1,3
	.word	11194
	.byte	10
	.byte	'_Ifx_QSPI_KRST1_Bits',0,6,224,1,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_QSPI_KRST1_Bits',0,6,228,1,3
	.word	11307
	.byte	10
	.byte	'_Ifx_QSPI_KRSTCLR_Bits',0,6,231,1,16,4,11
	.byte	'CLR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_QSPI_KRSTCLR_Bits',0,6,235,1,3
	.word	11401
	.byte	10
	.byte	'_Ifx_QSPI_MIXENTRY_Bits',0,6,238,1,16,4,11
	.byte	'E',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_QSPI_MIXENTRY_Bits',0,6,241,1,3
	.word	11499
	.byte	10
	.byte	'_Ifx_QSPI_OCS_Bits',0,6,244,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_QSPI_OCS_Bits',0,6,251,1,3
	.word	11575
	.byte	10
	.byte	'_Ifx_QSPI_PISEL_Bits',0,6,254,1,16,4,11
	.byte	'MRIS',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SRIS',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'SCIS',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SLSIS',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_QSPI_PISEL_Bits',0,6,136,2,3
	.word	11723
	.byte	10
	.byte	'_Ifx_QSPI_RXEXIT_Bits',0,6,139,2,16,4,11
	.byte	'E',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_QSPI_RXEXIT_Bits',0,6,142,2,3
	.word	11935
	.byte	10
	.byte	'_Ifx_QSPI_RXEXITD_Bits',0,6,145,2,16,4,11
	.byte	'E',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_QSPI_RXEXITD_Bits',0,6,148,2,3
	.word	12007
	.byte	10
	.byte	'_Ifx_QSPI_SSOC_Bits',0,6,151,2,16,4,11
	.byte	'AOL',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'OEN',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_QSPI_SSOC_Bits',0,6,155,2,3
	.word	12081
	.byte	10
	.byte	'_Ifx_QSPI_STATUS1_Bits',0,6,158,2,16,4,11
	.byte	'BITCOUNT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	20,4,2,35,0,11
	.byte	'BRDEN',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BRD',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'SPD',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_QSPI_STATUS1_Bits',0,6,166,2,3
	.word	12166
	.byte	10
	.byte	'_Ifx_QSPI_STATUS_Bits',0,6,169,2,16,4,11
	.byte	'ERRORFLAGS',0,2
	.word	510
	.byte	9,7,2,35,0,11
	.byte	'TXF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'RXF',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PT1F',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PT2F',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'USRF',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'TXFIFOLEVEL',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'RXFIFOLEVEL',0,1
	.word	493
	.byte	3,2,2,35,2,11
	.byte	'SLAVESEL',0,2
	.word	510
	.byte	4,6,2,35,2,11
	.byte	'RPV',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'TPV',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PHASE',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_QSPI_STATUS_Bits',0,6,184,2,3
	.word	12333
	.byte	10
	.byte	'_Ifx_QSPI_XXLCON_Bits',0,6,187,2,16,4,11
	.byte	'XDL',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'BYTECOUNT',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_QSPI_XXLCON_Bits',0,6,191,2,3
	.word	12628
	.byte	12,6,199,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_ACCEN0',0,6,204,2,3
	.word	12723
	.byte	12,6,207,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_ACCEN1',0,6,212,2,3
	.word	12788
	.byte	12,6,215,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_BACON',0,6,220,2,3
	.word	12853
	.byte	12,6,223,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9538
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_BACONENTRY',0,6,228,2,3
	.word	12917
	.byte	12,6,231,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9616
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_CAPCON',0,6,236,2,3
	.word	12986
	.byte	12,6,239,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9840
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_CLC',0,6,244,2,3
	.word	13051
	.byte	12,6,247,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9984
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_DATAENTRY',0,6,252,2,3
	.word	13113
	.byte	12,6,255,2,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10062
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_ECON',0,6,132,3,3
	.word	13181
	.byte	12,6,135,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10254
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_FLAGSCLEAR',0,6,140,3,3
	.word	13244
	.byte	12,6,143,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10788
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_GLOBALCON',0,6,148,3,3
	.word	13313
	.byte	12,6,151,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10468
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_GLOBALCON1',0,6,156,3,3
	.word	13381
	.byte	12,6,159,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11085
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_ID',0,6,164,3,3
	.word	13450
	.byte	12,6,167,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11194
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_KRST0',0,6,172,3,3
	.word	13511
	.byte	12,6,175,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11307
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_KRST1',0,6,180,3,3
	.word	13575
	.byte	12,6,183,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11401
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_KRSTCLR',0,6,188,3,3
	.word	13639
	.byte	12,6,191,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11499
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_MIXENTRY',0,6,196,3,3
	.word	13705
	.byte	12,6,199,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11575
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_OCS',0,6,204,3,3
	.word	13772
	.byte	12,6,207,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11723
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_PISEL',0,6,212,3,3
	.word	13834
	.byte	12,6,215,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11935
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_RXEXIT',0,6,220,3,3
	.word	13898
	.byte	12,6,223,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12007
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_RXEXITD',0,6,228,3,3
	.word	13963
	.byte	12,6,231,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12081
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_SSOC',0,6,236,3,3
	.word	14029
	.byte	12,6,239,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12333
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_STATUS',0,6,244,3,3
	.word	14092
	.byte	12,6,247,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12166
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_STATUS1',0,6,252,3,3
	.word	14157
	.byte	12,6,255,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12628
	.byte	4,2,35,0,0,21
	.byte	'Ifx_QSPI_XXLCON',0,6,132,4,3
	.word	14223
	.byte	14,32
	.word	13181
	.byte	15,7,0,14,32
	.word	13113
	.byte	15,7,0,14,68
	.word	493
	.byte	15,67,0,10
	.byte	'_Ifx_QSPI',0,6,143,4,25,128,2,13
	.byte	'CLC',0
	.word	13051
	.byte	4,2,35,0,13
	.byte	'PISEL',0
	.word	13834
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	13450
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1538
	.byte	4,2,35,12,13
	.byte	'GLOBALCON',0
	.word	13313
	.byte	4,2,35,16,13
	.byte	'GLOBALCON1',0
	.word	13381
	.byte	4,2,35,20,13
	.byte	'BACON',0
	.word	12853
	.byte	4,2,35,24,13
	.byte	'reserved_1C',0
	.word	1538
	.byte	4,2,35,28,13
	.byte	'ECON',0
	.word	14288
	.byte	32,2,35,32,13
	.byte	'STATUS',0
	.word	14092
	.byte	4,2,35,64,13
	.byte	'STATUS1',0
	.word	14157
	.byte	4,2,35,68,13
	.byte	'SSOC',0
	.word	14029
	.byte	4,2,35,72,13
	.byte	'reserved_4C',0
	.word	3357
	.byte	8,2,35,76,13
	.byte	'FLAGSCLEAR',0
	.word	13244
	.byte	4,2,35,84,13
	.byte	'XXLCON',0
	.word	14223
	.byte	4,2,35,88,13
	.byte	'MIXENTRY',0
	.word	13705
	.byte	4,2,35,92,13
	.byte	'BACONENTRY',0
	.word	12917
	.byte	4,2,35,96,13
	.byte	'DATAENTRY',0
	.word	14297
	.byte	32,2,35,100,13
	.byte	'reserved_84',0
	.word	3697
	.byte	12,3,35,132,1,13
	.byte	'RXEXIT',0
	.word	13898
	.byte	4,3,35,144,1,13
	.byte	'RXEXITD',0
	.word	13963
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3357
	.byte	8,3,35,152,1,13
	.byte	'CAPCON',0
	.word	12986
	.byte	4,3,35,160,1,13
	.byte	'reserved_A4',0
	.word	14306
	.byte	68,3,35,164,1,13
	.byte	'OCS',0
	.word	13772
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	13639
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	13575
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	13511
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	12788
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	12723
	.byte	4,3,35,252,1,0,16
	.word	14315
	.byte	21
	.byte	'Ifx_QSPI',0,6,175,4,3
	.word	14859
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	493
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	493
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	510
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	14927
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	352
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8546
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	14993
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	15021
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	298
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	384
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	15021
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	15106
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7110
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7023
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3366
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1419
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2414
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1547
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2194
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1762
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1977
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6382
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6506
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6590
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6770
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5021
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5545
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5195
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5369
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6034
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	848
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4358
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4846
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4505
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4674
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5701
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	532
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4072
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3706
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2737
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3041
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7637
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7070
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3657
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1498
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2688
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1722
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2374
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1937
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2154
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6466
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6715
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6974
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6342
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5155
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5661
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5329
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5505
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1379
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5994
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4465
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4981
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4634
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4806
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	808
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4318
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4032
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3001
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3317
	.byte	16
	.word	7677
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	16562
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	16582
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	16704
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	17261
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	17338
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	17474
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	17754
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	17992
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	18120
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	18363
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	18598
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	18726
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	18826
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	18926
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	19134
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	19299
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	19482
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	19636
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	20000
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	510
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	20211
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	20463
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	20581
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	20692
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	20855
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	21018
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	21176
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	510
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	21341
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	510
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	21670
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	21891
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	22054
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	22326
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	22479
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	22635
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	22797
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	22940
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	23105
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	23250
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	23431
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	23605
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	23765
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	23909
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	24183
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	24322
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	493
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	510
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	493
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	24485
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	24703
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	24866
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	25202
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	25309
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	25761
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	25860
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	26010
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	470
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	26159
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	26320
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	26450
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	26582
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	510
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	26697
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	26808
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	26966
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	27378
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	510
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	27479
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	27746
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	27882
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	27993
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	28126
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	28329
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	28685
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	28863
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	28963
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	29333
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	29519
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	29717
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	29950
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	493
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	30102
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	30669
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	30963
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	31241
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	31737
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	32050
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	32259
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	32470
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	32902
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	32998
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	33258
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	33383
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	33580
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	33733
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	33886
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	34039
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	34194
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	34194
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	34194
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	34194
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	34210
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	34340
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	34578
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	34194
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	34194
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	34194
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	34194
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	34801
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	34927
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	35179
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16704
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	35398
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17261
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	35462
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17338
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	35526
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17474
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	35591
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17754
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	35656
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17992
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	35721
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18120
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	35786
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18363
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	35851
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18598
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	35916
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18726
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	35981
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18826
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	36046
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18926
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	36111
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19134
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	36175
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19299
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	36239
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19482
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	36303
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19636
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	36368
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20000
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	36430
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20211
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	36492
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20463
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	36554
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20581
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	36618
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20692
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	36683
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20855
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	36749
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21018
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	36815
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21176
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	36883
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21341
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	36950
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21670
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	37018
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21891
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	37086
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22054
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	37152
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22326
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	37219
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22479
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	37288
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22635
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	37357
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22797
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	37426
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22940
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	37495
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23105
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	37564
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23250
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	37633
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23431
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	37701
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23605
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	37769
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23765
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	37837
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23909
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	37905
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24183
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	37970
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24322
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	38035
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24485
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	38101
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24703
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	38165
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24866
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	38226
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25202
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	38287
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25309
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	38347
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25761
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	38409
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25860
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	38469
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26010
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	38531
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26159
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	38599
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26320
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	38667
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26450
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	38735
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26582
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	38799
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26697
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	38864
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26808
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	38927
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26966
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	38988
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27378
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	39052
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27479
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	39113
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27746
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	39177
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27882
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	39244
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27993
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	39307
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28126
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	39368
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28329
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	39430
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28685
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	39495
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28863
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	39560
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28963
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	39625
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29333
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	39694
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29519
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	39763
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29717
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	39832
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29950
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	39897
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30102
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	39960
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30669
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	40025
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30963
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	40090
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31241
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	40155
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31737
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	40221
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32259
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	40290
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32050
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	40354
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32470
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	40419
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32902
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	40484
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32998
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	40549
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33258
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	40613
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33383
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	40679
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33580
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	40743
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33733
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	40808
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33886
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	40873
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34039
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	40938
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34210
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	41004
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34340
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	41073
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34578
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	41142
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34801
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	41209
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34927
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	41276
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35179
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	41343
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	41004
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	41073
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	41142
	.byte	4,2,35,8,0,16
	.word	41408
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	41471
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	41209
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	41276
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	41343
	.byte	4,2,35,8,0,16
	.word	41500
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	41561
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	41588
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	41739
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	41983
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	42081
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8290
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8285
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	493
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	42546
	.byte	16
	.word	14315
	.byte	3
	.word	42606
	.byte	23,11,59,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	15106
	.byte	1,2,35,12,0,24
	.word	42616
	.byte	21
	.byte	'IfxQspi_Mrst_In',0,11,64,3
	.word	42667
	.byte	23,11,67,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	15106
	.byte	1,2,35,12,0,24
	.word	42696
	.byte	21
	.byte	'IfxQspi_Mtsr_In',0,11,72,3
	.word	42747
	.byte	23,11,75,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	15106
	.byte	1,2,35,12,0,24
	.word	42776
	.byte	21
	.byte	'IfxQspi_Sclk_In',0,11,80,3
	.word	42827
	.byte	23,11,83,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	15106
	.byte	1,2,35,12,0,24
	.word	42856
	.byte	21
	.byte	'IfxQspi_Slsi_In',0,11,88,3
	.word	42907
	.byte	23,11,91,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	15106
	.byte	1,2,35,12,0,24
	.word	42936
	.byte	21
	.byte	'IfxQspi_Hsicin_In',0,11,96,3
	.word	42987
	.byte	23,11,99,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	41739
	.byte	1,2,35,12,0,24
	.word	43018
	.byte	21
	.byte	'IfxQspi_Mrst_Out',0,11,104,3
	.word	43069
	.byte	23,11,107,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	41739
	.byte	1,2,35,12,0,24
	.word	43099
	.byte	21
	.byte	'IfxQspi_Mtsr_Out',0,11,112,3
	.word	43150
	.byte	23,11,115,15,16,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	41739
	.byte	1,2,35,12,0,24
	.word	43180
	.byte	21
	.byte	'IfxQspi_Sclk_Out',0,11,120,3
	.word	43231
	.byte	23,11,123,15,20,13
	.byte	'module',0
	.word	42611
	.byte	4,2,35,0,13
	.byte	'slsoNr',0
	.word	14993
	.byte	4,2,35,4,13
	.byte	'pin',0
	.word	42546
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	41739
	.byte	1,2,35,16,0,24
	.word	43261
	.byte	21
	.byte	'IfxQspi_Slso_Out',0,11,129,1,3
	.word	43328
.L294:
	.byte	24
	.word	42936
.L295:
	.byte	24
	.word	42936
.L296:
	.byte	24
	.word	42936
.L297:
	.byte	24
	.word	42936
.L298:
	.byte	24
	.word	42616
.L299:
	.byte	24
	.word	42616
.L300:
	.byte	24
	.word	42616
.L301:
	.byte	24
	.word	42616
.L302:
	.byte	24
	.word	42616
.L303:
	.byte	24
	.word	42616
.L304:
	.byte	24
	.word	42616
.L305:
	.byte	24
	.word	42616
.L306:
	.byte	24
	.word	42616
.L307:
	.byte	24
	.word	42616
.L308:
	.byte	24
	.word	42616
.L309:
	.byte	24
	.word	42616
.L310:
	.byte	24
	.word	42616
.L311:
	.byte	24
	.word	42616
.L312:
	.byte	24
	.word	43018
.L313:
	.byte	24
	.word	43018
.L314:
	.byte	24
	.word	43018
.L315:
	.byte	24
	.word	43018
.L316:
	.byte	24
	.word	43018
.L317:
	.byte	24
	.word	43018
.L318:
	.byte	24
	.word	43018
.L319:
	.byte	24
	.word	43018
.L320:
	.byte	24
	.word	43018
.L321:
	.byte	24
	.word	43018
.L322:
	.byte	24
	.word	42696
.L323:
	.byte	24
	.word	42696
.L324:
	.byte	24
	.word	42696
.L325:
	.byte	24
	.word	42696
.L326:
	.byte	24
	.word	42696
.L327:
	.byte	24
	.word	42696
.L328:
	.byte	24
	.word	42696
.L329:
	.byte	24
	.word	42696
.L330:
	.byte	24
	.word	42696
.L331:
	.byte	24
	.word	42696
.L332:
	.byte	24
	.word	43099
.L333:
	.byte	24
	.word	43099
.L334:
	.byte	24
	.word	43099
.L335:
	.byte	24
	.word	43099
.L336:
	.byte	24
	.word	43099
.L337:
	.byte	24
	.word	43099
.L338:
	.byte	24
	.word	43099
.L339:
	.byte	24
	.word	43099
.L340:
	.byte	24
	.word	43099
.L341:
	.byte	24
	.word	43099
.L342:
	.byte	24
	.word	43099
.L343:
	.byte	24
	.word	43099
.L344:
	.byte	24
	.word	43099
.L345:
	.byte	24
	.word	43099
.L346:
	.byte	24
	.word	43099
.L347:
	.byte	24
	.word	43099
.L348:
	.byte	24
	.word	42776
.L349:
	.byte	24
	.word	42776
.L350:
	.byte	24
	.word	42776
.L351:
	.byte	24
	.word	42776
.L352:
	.byte	24
	.word	42776
.L353:
	.byte	24
	.word	42776
.L354:
	.byte	24
	.word	42776
.L355:
	.byte	24
	.word	42776
.L356:
	.byte	24
	.word	42776
.L357:
	.byte	24
	.word	43180
.L358:
	.byte	24
	.word	43180
.L359:
	.byte	24
	.word	43180
.L360:
	.byte	24
	.word	43180
.L361:
	.byte	24
	.word	43180
.L362:
	.byte	24
	.word	43180
.L363:
	.byte	24
	.word	43180
.L364:
	.byte	24
	.word	43180
.L365:
	.byte	24
	.word	43180
.L366:
	.byte	24
	.word	43180
.L367:
	.byte	24
	.word	43180
.L368:
	.byte	24
	.word	43180
.L369:
	.byte	24
	.word	43180
.L370:
	.byte	24
	.word	43180
.L371:
	.byte	24
	.word	43180
.L372:
	.byte	24
	.word	42856
.L373:
	.byte	24
	.word	42856
.L374:
	.byte	24
	.word	42856
.L375:
	.byte	24
	.word	42856
.L376:
	.byte	24
	.word	42856
.L377:
	.byte	24
	.word	42856
.L378:
	.byte	24
	.word	42856
.L379:
	.byte	24
	.word	42856
.L380:
	.byte	24
	.word	43261
.L381:
	.byte	24
	.word	43261
.L382:
	.byte	24
	.word	43261
.L383:
	.byte	24
	.word	43261
.L384:
	.byte	24
	.word	43261
.L385:
	.byte	24
	.word	43261
.L386:
	.byte	24
	.word	43261
.L387:
	.byte	24
	.word	43261
.L388:
	.byte	24
	.word	43261
.L389:
	.byte	24
	.word	43261
.L390:
	.byte	24
	.word	43261
.L391:
	.byte	24
	.word	43261
.L392:
	.byte	24
	.word	43261
.L393:
	.byte	24
	.word	43261
.L394:
	.byte	24
	.word	43261
.L395:
	.byte	24
	.word	43261
.L396:
	.byte	24
	.word	43261
.L397:
	.byte	24
	.word	43261
.L398:
	.byte	24
	.word	43261
.L399:
	.byte	24
	.word	43261
.L400:
	.byte	24
	.word	43261
.L401:
	.byte	24
	.word	43261
.L402:
	.byte	24
	.word	43261
.L403:
	.byte	24
	.word	43261
.L404:
	.byte	24
	.word	43261
.L405:
	.byte	24
	.word	43261
.L406:
	.byte	24
	.word	43261
.L407:
	.byte	24
	.word	43261
.L408:
	.byte	24
	.word	43261
.L409:
	.byte	24
	.word	43261
.L410:
	.byte	24
	.word	43261
.L411:
	.byte	24
	.word	43261
.L412:
	.byte	24
	.word	43261
.L413:
	.byte	24
	.word	43261
.L414:
	.byte	24
	.word	43261
.L415:
	.byte	24
	.word	43261
.L416:
	.byte	24
	.word	43261
.L417:
	.byte	24
	.word	43261
.L418:
	.byte	24
	.word	43261
.L419:
	.byte	24
	.word	43261
.L420:
	.byte	24
	.word	43261
.L421:
	.byte	24
	.word	43261
.L422:
	.byte	24
	.word	43261
.L423:
	.byte	24
	.word	43261
.L424:
	.byte	24
	.word	43261
.L425:
	.byte	24
	.word	43261
.L426:
	.byte	24
	.word	43261
.L427:
	.byte	24
	.word	43261
.L428:
	.byte	24
	.word	43261
	.byte	24
	.word	42936
	.byte	3
	.word	44034
	.byte	14,8
	.word	44039
	.byte	15,1,0
.L429:
	.byte	14,32
	.word	44044
	.byte	15,3,0,24
	.word	42616
	.byte	3
	.word	44062
	.byte	14,24
	.word	44067
	.byte	15,5,0
.L430:
	.byte	14,96
	.word	44072
	.byte	15,3,0,24
	.word	43018
	.byte	3
	.word	44090
	.byte	14,16
	.word	44095
	.byte	15,3,0
.L431:
	.byte	14,64
	.word	44100
	.byte	15,3,0,24
	.word	42696
	.byte	3
	.word	44118
	.byte	14,20
	.word	44123
	.byte	15,4,0
.L432:
	.byte	14,80
	.word	44128
	.byte	15,3,0,24
	.word	43099
	.byte	3
	.word	44146
	.byte	14,24
	.word	44151
	.byte	15,5,0
.L433:
	.byte	14,96
	.word	44156
	.byte	15,3,0,24
	.word	42776
	.byte	3
	.word	44174
	.byte	14,20
	.word	44179
	.byte	15,4,0
.L434:
	.byte	14,80
	.word	44184
	.byte	15,3,0,24
	.word	43180
	.byte	3
	.word	44202
	.byte	14,24
	.word	44207
	.byte	15,5,0
.L435:
	.byte	14,96
	.word	44212
	.byte	15,3,0,24
	.word	42856
	.byte	3
	.word	44230
	.byte	14,16
	.word	44235
	.byte	15,3,0
.L436:
	.byte	14,64
	.word	44240
	.byte	15,3,0,24
	.word	43261
	.byte	3
	.word	44258
	.byte	14,8
	.word	44263
	.byte	15,1,0,14,112
	.word	44268
	.byte	15,13,0
.L437:
	.byte	14,192,3
	.word	44277
	.byte	15,3,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L439-.L438
.L438:
	.half	3
	.word	.L441-.L440
.L440:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0,0,0,0
	.byte	'IfxQspi_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxQspi_PinMap.h',0,0,0,0,0
.L441:
.L439:
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_HSICINA_P15_2_IN')
	.sect	'.debug_info'
.L6:
	.word	277
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_HSICINA_P15_2_IN',0,5,48,19
	.word	.L294
	.byte	1,5,3
	.word	IfxQspi2_HSICINA_P15_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_HSICINA_P15_2_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_HSICINB_P15_3_IN')
	.sect	'.debug_info'
.L8:
	.word	277
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_HSICINB_P15_3_IN',0,5,49,19
	.word	.L295
	.byte	1,5,3
	.word	IfxQspi2_HSICINB_P15_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_HSICINB_P15_3_IN')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_HSICINA_P33_9_IN')
	.sect	'.debug_info'
.L10:
	.word	277
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_HSICINA_P33_9_IN',0,5,50,19
	.word	.L296
	.byte	1,5,3
	.word	IfxQspi3_HSICINA_P33_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_HSICINA_P33_9_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_HSICINB_P33_10_IN')
	.sect	'.debug_info'
.L12:
	.word	278
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_HSICINB_P33_10_IN',0,5,51,19
	.word	.L297
	.byte	1,5,3
	.word	IfxQspi3_HSICINB_P33_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_HSICINB_P33_10_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_MRSTA_P20_12_IN')
	.sect	'.debug_info'
.L14:
	.word	276
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_MRSTA_P20_12_IN',0,5,52,17
	.word	.L298
	.byte	1,5,3
	.word	IfxQspi0_MRSTA_P20_12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_MRSTA_P20_12_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MRSTA_P10_1_IN')
	.sect	'.debug_info'
.L16:
	.word	275
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MRSTA_P10_1_IN',0,5,53,17
	.word	.L299
	.byte	1,5,3
	.word	IfxQspi1_MRSTA_P10_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MRSTA_P10_1_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MRSTB_P11_3_IN')
	.sect	'.debug_info'
.L18:
	.word	275
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MRSTB_P11_3_IN',0,5,54,17
	.word	.L300
	.byte	1,5,3
	.word	IfxQspi1_MRSTB_P11_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MRSTB_P11_3_IN')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MRSTA_P15_4_IN')
	.sect	'.debug_info'
.L20:
	.word	275
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MRSTA_P15_4_IN',0,5,55,17
	.word	.L301
	.byte	1,5,3
	.word	IfxQspi2_MRSTA_P15_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MRSTA_P15_4_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MRSTB_P15_7_IN')
	.sect	'.debug_info'
.L22:
	.word	275
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MRSTB_P15_7_IN',0,5,56,17
	.word	.L302
	.byte	1,5,3
	.word	IfxQspi2_MRSTB_P15_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MRSTB_P15_7_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MRSTCN_P21_2_IN')
	.sect	'.debug_info'
.L24:
	.word	276
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MRSTCN_P21_2_IN',0,5,57,17
	.word	.L303
	.byte	1,5,3
	.word	IfxQspi2_MRSTCN_P21_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MRSTCN_P21_2_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MRSTCP_P21_3_IN')
	.sect	'.debug_info'
.L26:
	.word	276
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MRSTCP_P21_3_IN',0,5,58,17
	.word	.L304
	.byte	1,5,3
	.word	IfxQspi2_MRSTCP_P21_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MRSTCP_P21_3_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MRSTE_P15_2_IN')
	.sect	'.debug_info'
.L28:
	.word	275
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MRSTE_P15_2_IN',0,5,59,17
	.word	.L305
	.byte	1,5,3
	.word	IfxQspi2_MRSTE_P15_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MRSTE_P15_2_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRSTA_P02_5_IN')
	.sect	'.debug_info'
.L30:
	.word	275
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRSTA_P02_5_IN',0,5,60,17
	.word	.L306
	.byte	1,5,3
	.word	IfxQspi3_MRSTA_P02_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRSTA_P02_5_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRSTB_P10_7_IN')
	.sect	'.debug_info'
.L32:
	.word	275
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRSTB_P10_7_IN',0,5,61,17
	.word	.L307
	.byte	1,5,3
	.word	IfxQspi3_MRSTB_P10_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRSTB_P10_7_IN')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRSTD_P33_13_IN')
	.sect	'.debug_info'
.L34:
	.word	276
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRSTD_P33_13_IN',0,5,62,17
	.word	.L308
	.byte	1,5,3
	.word	IfxQspi3_MRSTD_P33_13_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRSTD_P33_13_IN')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRSTE_P22_1_IN')
	.sect	'.debug_info'
.L36:
	.word	275
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRSTE_P22_1_IN',0,5,63,17
	.word	.L309
	.byte	1,5,3
	.word	IfxQspi3_MRSTE_P22_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRSTE_P22_1_IN')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRSTFN_P21_2_IN')
	.sect	'.debug_info'
.L38:
	.word	276
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRSTFN_P21_2_IN',0,5,64,17
	.word	.L310
	.byte	1,5,3
	.word	IfxQspi3_MRSTFN_P21_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRSTFN_P21_2_IN')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRSTFP_P21_3_IN')
	.sect	'.debug_info'
.L40:
	.word	276
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRSTFP_P21_3_IN',0,5,65,17
	.word	.L311
	.byte	1,5,3
	.word	IfxQspi3_MRSTFP_P21_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRSTFP_P21_3_IN')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_MRST_P20_12_OUT')
	.sect	'.debug_info'
.L42:
	.word	276
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_MRST_P20_12_OUT',0,5,66,18
	.word	.L312
	.byte	1,5,3
	.word	IfxQspi0_MRST_P20_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_MRST_P20_12_OUT')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MRST_P10_1_OUT')
	.sect	'.debug_info'
.L44:
	.word	275
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MRST_P10_1_OUT',0,5,67,18
	.word	.L313
	.byte	1,5,3
	.word	IfxQspi1_MRST_P10_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MRST_P10_1_OUT')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MRST_P10_6_OUT')
	.sect	'.debug_info'
.L46:
	.word	275
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MRST_P10_6_OUT',0,5,68,18
	.word	.L314
	.byte	1,5,3
	.word	IfxQspi1_MRST_P10_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MRST_P10_6_OUT')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MRST_P11_3_OUT')
	.sect	'.debug_info'
.L48:
	.word	275
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MRST_P11_3_OUT',0,5,69,18
	.word	.L315
	.byte	1,5,3
	.word	IfxQspi1_MRST_P11_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MRST_P11_3_OUT')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MRST_P15_4_OUT')
	.sect	'.debug_info'
.L50:
	.word	275
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MRST_P15_4_OUT',0,5,70,18
	.word	.L316
	.byte	1,5,3
	.word	IfxQspi2_MRST_P15_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MRST_P15_4_OUT')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MRST_P15_7_OUT')
	.sect	'.debug_info'
.L52:
	.word	275
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MRST_P15_7_OUT',0,5,71,18
	.word	.L317
	.byte	1,5,3
	.word	IfxQspi2_MRST_P15_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MRST_P15_7_OUT')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRST_P02_5_OUT')
	.sect	'.debug_info'
.L54:
	.word	275
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRST_P02_5_OUT',0,5,72,18
	.word	.L318
	.byte	1,5,3
	.word	IfxQspi3_MRST_P02_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRST_P02_5_OUT')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRST_P10_7_OUT')
	.sect	'.debug_info'
.L56:
	.word	275
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRST_P10_7_OUT',0,5,73,18
	.word	.L319
	.byte	1,5,3
	.word	IfxQspi3_MRST_P10_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRST_P10_7_OUT')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRST_P22_1_OUT')
	.sect	'.debug_info'
.L58:
	.word	275
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRST_P22_1_OUT',0,5,74,18
	.word	.L320
	.byte	1,5,3
	.word	IfxQspi3_MRST_P22_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRST_P22_1_OUT')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MRST_P33_13_OUT')
	.sect	'.debug_info'
.L60:
	.word	276
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MRST_P33_13_OUT',0,5,75,18
	.word	.L321
	.byte	1,5,3
	.word	IfxQspi3_MRST_P33_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MRST_P33_13_OUT')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_MTSRA_P20_14_IN')
	.sect	'.debug_info'
.L62:
	.word	276
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_MTSRA_P20_14_IN',0,5,76,17
	.word	.L322
	.byte	1,5,3
	.word	IfxQspi0_MTSRA_P20_14_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_MTSRA_P20_14_IN')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MTSRA_P10_3_IN')
	.sect	'.debug_info'
.L64:
	.word	275
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MTSRA_P10_3_IN',0,5,77,17
	.word	.L323
	.byte	1,5,3
	.word	IfxQspi1_MTSRA_P10_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MTSRA_P10_3_IN')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MTSRB_P11_9_IN')
	.sect	'.debug_info'
.L66:
	.word	275
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MTSRB_P11_9_IN',0,5,78,17
	.word	.L324
	.byte	1,5,3
	.word	IfxQspi1_MTSRB_P11_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MTSRB_P11_9_IN')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MTSRC_P10_4_IN')
	.sect	'.debug_info'
.L68:
	.word	275
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MTSRC_P10_4_IN',0,5,79,17
	.word	.L325
	.byte	1,5,3
	.word	IfxQspi1_MTSRC_P10_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MTSRC_P10_4_IN')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MTSRA_P15_5_IN')
	.sect	'.debug_info'
.L70:
	.word	275
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MTSRA_P15_5_IN',0,5,80,17
	.word	.L326
	.byte	1,5,3
	.word	IfxQspi2_MTSRA_P15_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MTSRA_P15_5_IN')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MTSRB_P15_6_IN')
	.sect	'.debug_info'
.L72:
	.word	275
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MTSRB_P15_6_IN',0,5,81,17
	.word	.L327
	.byte	1,5,3
	.word	IfxQspi2_MTSRB_P15_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MTSRB_P15_6_IN')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSRA_P02_6_IN')
	.sect	'.debug_info'
.L74:
	.word	275
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSRA_P02_6_IN',0,5,82,17
	.word	.L328
	.byte	1,5,3
	.word	IfxQspi3_MTSRA_P02_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSRA_P02_6_IN')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSRB_P10_6_IN')
	.sect	'.debug_info'
.L76:
	.word	275
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSRB_P10_6_IN',0,5,83,17
	.word	.L329
	.byte	1,5,3
	.word	IfxQspi3_MTSRB_P10_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSRB_P10_6_IN')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSRD_P33_12_IN')
	.sect	'.debug_info'
.L78:
	.word	276
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSRD_P33_12_IN',0,5,84,17
	.word	.L330
	.byte	1,5,3
	.word	IfxQspi3_MTSRD_P33_12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSRD_P33_12_IN')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSRE_P22_0_IN')
	.sect	'.debug_info'
.L80:
	.word	275
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSRE_P22_0_IN',0,5,85,17
	.word	.L331
	.byte	1,5,3
	.word	IfxQspi3_MTSRE_P22_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSRE_P22_0_IN')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_MTSR_P20_12_OUT')
	.sect	'.debug_info'
.L82:
	.word	276
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_MTSR_P20_12_OUT',0,5,86,18
	.word	.L332
	.byte	1,5,3
	.word	IfxQspi0_MTSR_P20_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_MTSR_P20_12_OUT')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_MTSR_P20_14_OUT')
	.sect	'.debug_info'
.L84:
	.word	276
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_MTSR_P20_14_OUT',0,5,87,18
	.word	.L333
	.byte	1,5,3
	.word	IfxQspi0_MTSR_P20_14_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_MTSR_P20_14_OUT')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MTSR_P10_1_OUT')
	.sect	'.debug_info'
.L86:
	.word	275
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MTSR_P10_1_OUT',0,5,88,18
	.word	.L334
	.byte	1,5,3
	.word	IfxQspi1_MTSR_P10_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MTSR_P10_1_OUT')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MTSR_P10_3_OUT')
	.sect	'.debug_info'
.L88:
	.word	275
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MTSR_P10_3_OUT',0,5,89,18
	.word	.L335
	.byte	1,5,3
	.word	IfxQspi1_MTSR_P10_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MTSR_P10_3_OUT')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MTSR_P10_4_OUT')
	.sect	'.debug_info'
.L90:
	.word	275
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MTSR_P10_4_OUT',0,5,90,18
	.word	.L336
	.byte	1,5,3
	.word	IfxQspi1_MTSR_P10_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MTSR_P10_4_OUT')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_MTSR_P11_9_OUT')
	.sect	'.debug_info'
.L92:
	.word	275
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_MTSR_P11_9_OUT',0,5,91,18
	.word	.L337
	.byte	1,5,3
	.word	IfxQspi1_MTSR_P11_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_MTSR_P11_9_OUT')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MTSRN_P13_2_OUT')
	.sect	'.debug_info'
.L94:
	.word	276
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MTSRN_P13_2_OUT',0,5,92,18
	.word	.L338
	.byte	1,5,3
	.word	IfxQspi2_MTSRN_P13_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MTSRN_P13_2_OUT')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MTSRP_P13_3_OUT')
	.sect	'.debug_info'
.L96:
	.word	276
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MTSRP_P13_3_OUT',0,5,93,18
	.word	.L339
	.byte	1,5,3
	.word	IfxQspi2_MTSRP_P13_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MTSRP_P13_3_OUT')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MTSR_P15_5_OUT')
	.sect	'.debug_info'
.L98:
	.word	275
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MTSR_P15_5_OUT',0,5,94,18
	.word	.L340
	.byte	1,5,3
	.word	IfxQspi2_MTSR_P15_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MTSR_P15_5_OUT')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_MTSR_P15_6_OUT')
	.sect	'.debug_info'
.L100:
	.word	275
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_MTSR_P15_6_OUT',0,5,95,18
	.word	.L341
	.byte	1,5,3
	.word	IfxQspi2_MTSR_P15_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_MTSR_P15_6_OUT')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSRN_P22_2_OUT')
	.sect	'.debug_info'
.L102:
	.word	276
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSRN_P22_2_OUT',0,5,96,18
	.word	.L342
	.byte	1,5,3
	.word	IfxQspi3_MTSRN_P22_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSRN_P22_2_OUT')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSRP_P22_3_OUT')
	.sect	'.debug_info'
.L104:
	.word	276
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSRP_P22_3_OUT',0,5,97,18
	.word	.L343
	.byte	1,5,3
	.word	IfxQspi3_MTSRP_P22_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSRP_P22_3_OUT')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSR_P02_6_OUT')
	.sect	'.debug_info'
.L106:
	.word	275
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSR_P02_6_OUT',0,5,98,18
	.word	.L344
	.byte	1,5,3
	.word	IfxQspi3_MTSR_P02_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSR_P02_6_OUT')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSR_P10_6_OUT')
	.sect	'.debug_info'
.L108:
	.word	275
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSR_P10_6_OUT',0,5,99,18
	.word	.L345
	.byte	1,5,3
	.word	IfxQspi3_MTSR_P10_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSR_P10_6_OUT')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSR_P22_0_OUT')
	.sect	'.debug_info'
.L110:
	.word	275
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSR_P22_0_OUT',0,5,100,18
	.word	.L346
	.byte	1,5,3
	.word	IfxQspi3_MTSR_P22_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSR_P22_0_OUT')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_MTSR_P33_12_OUT')
	.sect	'.debug_info'
.L112:
	.word	276
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_MTSR_P33_12_OUT',0,5,101,18
	.word	.L347
	.byte	1,5,3
	.word	IfxQspi3_MTSR_P33_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_MTSR_P33_12_OUT')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SCLKA_P20_11_IN')
	.sect	'.debug_info'
.L114:
	.word	276
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SCLKA_P20_11_IN',0,5,102,17
	.word	.L348
	.byte	1,5,3
	.word	IfxQspi0_SCLKA_P20_11_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SCLKA_P20_11_IN')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SCLKA_P10_2_IN')
	.sect	'.debug_info'
.L116:
	.word	275
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SCLKA_P10_2_IN',0,5,103,17
	.word	.L349
	.byte	1,5,3
	.word	IfxQspi1_SCLKA_P10_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SCLKA_P10_2_IN')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SCLKB_P11_6_IN')
	.sect	'.debug_info'
.L118:
	.word	275
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SCLKB_P11_6_IN',0,5,104,17
	.word	.L350
	.byte	1,5,3
	.word	IfxQspi1_SCLKB_P11_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SCLKB_P11_6_IN')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SCLKA_P15_3_IN')
	.sect	'.debug_info'
.L120:
	.word	275
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SCLKA_P15_3_IN',0,5,105,17
	.word	.L351
	.byte	1,5,3
	.word	IfxQspi2_SCLKA_P15_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SCLKA_P15_3_IN')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SCLKB_P15_8_IN')
	.sect	'.debug_info'
.L122:
	.word	275
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SCLKB_P15_8_IN',0,5,106,17
	.word	.L352
	.byte	1,5,3
	.word	IfxQspi2_SCLKB_P15_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SCLKB_P15_8_IN')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLKA_P02_7_IN')
	.sect	'.debug_info'
.L124:
	.word	275
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLKA_P02_7_IN',0,5,107,17
	.word	.L353
	.byte	1,5,3
	.word	IfxQspi3_SCLKA_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLKA_P02_7_IN')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLKB_P10_8_IN')
	.sect	'.debug_info'
.L126:
	.word	275
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLKB_P10_8_IN',0,5,108,17
	.word	.L354
	.byte	1,5,3
	.word	IfxQspi3_SCLKB_P10_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLKB_P10_8_IN')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLKD_P33_11_IN')
	.sect	'.debug_info'
.L128:
	.word	276
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLKD_P33_11_IN',0,5,109,17
	.word	.L355
	.byte	1,5,3
	.word	IfxQspi3_SCLKD_P33_11_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLKD_P33_11_IN')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLKE_P22_3_IN')
	.sect	'.debug_info'
.L130:
	.word	275
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLKE_P22_3_IN',0,5,110,17
	.word	.L356
	.byte	1,5,3
	.word	IfxQspi3_SCLKE_P22_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLKE_P22_3_IN')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SCLK_P20_11_OUT')
	.sect	'.debug_info'
.L132:
	.word	276
	.half	3
	.word	.L133
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SCLK_P20_11_OUT',0,5,111,18
	.word	.L357
	.byte	1,5,3
	.word	IfxQspi0_SCLK_P20_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SCLK_P20_11_OUT')
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SCLK_P20_13_OUT')
	.sect	'.debug_info'
.L134:
	.word	276
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SCLK_P20_13_OUT',0,5,112,18
	.word	.L358
	.byte	1,5,3
	.word	IfxQspi0_SCLK_P20_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SCLK_P20_13_OUT')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SCLK_P10_2_OUT')
	.sect	'.debug_info'
.L136:
	.word	275
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SCLK_P10_2_OUT',0,5,113,18
	.word	.L359
	.byte	1,5,3
	.word	IfxQspi1_SCLK_P10_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SCLK_P10_2_OUT')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SCLK_P11_6_OUT')
	.sect	'.debug_info'
.L138:
	.word	275
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SCLK_P11_6_OUT',0,5,114,18
	.word	.L360
	.byte	1,5,3
	.word	IfxQspi1_SCLK_P11_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SCLK_P11_6_OUT')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SCLKN_P13_0_OUT')
	.sect	'.debug_info'
.L140:
	.word	276
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SCLKN_P13_0_OUT',0,5,115,18
	.word	.L361
	.byte	1,5,3
	.word	IfxQspi2_SCLKN_P13_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SCLKN_P13_0_OUT')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SCLKP_P13_1_OUT')
	.sect	'.debug_info'
.L142:
	.word	276
	.half	3
	.word	.L143
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SCLKP_P13_1_OUT',0,5,116,18
	.word	.L362
	.byte	1,5,3
	.word	IfxQspi2_SCLKP_P13_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SCLKP_P13_1_OUT')
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SCLK_P15_3_OUT')
	.sect	'.debug_info'
.L144:
	.word	275
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SCLK_P15_3_OUT',0,5,117,18
	.word	.L363
	.byte	1,5,3
	.word	IfxQspi2_SCLK_P15_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SCLK_P15_3_OUT')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SCLK_P15_6_OUT')
	.sect	'.debug_info'
.L146:
	.word	275
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SCLK_P15_6_OUT',0,5,118,18
	.word	.L364
	.byte	1,5,3
	.word	IfxQspi2_SCLK_P15_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SCLK_P15_6_OUT')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SCLK_P15_8_OUT')
	.sect	'.debug_info'
.L148:
	.word	275
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SCLK_P15_8_OUT',0,5,119,18
	.word	.L365
	.byte	1,5,3
	.word	IfxQspi2_SCLK_P15_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SCLK_P15_8_OUT')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLKN_P22_0_OUT')
	.sect	'.debug_info'
.L150:
	.word	276
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLKN_P22_0_OUT',0,5,120,18
	.word	.L366
	.byte	1,5,3
	.word	IfxQspi3_SCLKN_P22_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLKN_P22_0_OUT')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLKP_P22_1_OUT')
	.sect	'.debug_info'
.L152:
	.word	276
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLKP_P22_1_OUT',0,5,121,18
	.word	.L367
	.byte	1,5,3
	.word	IfxQspi3_SCLKP_P22_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLKP_P22_1_OUT')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLK_P02_7_OUT')
	.sect	'.debug_info'
.L154:
	.word	275
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLK_P02_7_OUT',0,5,122,18
	.word	.L368
	.byte	1,5,3
	.word	IfxQspi3_SCLK_P02_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLK_P02_7_OUT')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLK_P10_8_OUT')
	.sect	'.debug_info'
.L156:
	.word	275
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLK_P10_8_OUT',0,5,123,18
	.word	.L369
	.byte	1,5,3
	.word	IfxQspi3_SCLK_P10_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLK_P10_8_OUT')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLK_P22_3_OUT')
	.sect	'.debug_info'
.L158:
	.word	275
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLK_P22_3_OUT',0,5,124,18
	.word	.L370
	.byte	1,5,3
	.word	IfxQspi3_SCLK_P22_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLK_P22_3_OUT')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SCLK_P33_11_OUT')
	.sect	'.debug_info'
.L160:
	.word	276
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SCLK_P33_11_OUT',0,5,125,18
	.word	.L371
	.byte	1,5,3
	.word	IfxQspi3_SCLK_P33_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SCLK_P33_11_OUT')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSIA_P20_13_IN')
	.sect	'.debug_info'
.L162:
	.word	276
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSIA_P20_13_IN',0,5,126,17
	.word	.L372
	.byte	1,5,3
	.word	IfxQspi0_SLSIA_P20_13_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSIA_P20_13_IN')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSIB_P20_9_IN')
	.sect	'.debug_info'
.L164:
	.word	275
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSIB_P20_9_IN',0,5,127,17
	.word	.L373
	.byte	1,5,3
	.word	IfxQspi0_SLSIB_P20_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSIB_P20_9_IN')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSIA_P11_10_IN')
	.sect	'.debug_info'
.L166:
	.word	277
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSIA_P11_10_IN',0,5,128,1,17
	.word	.L374
	.byte	1,5,3
	.word	IfxQspi1_SLSIA_P11_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSIA_P11_10_IN')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSIA_P15_2_IN')
	.sect	'.debug_info'
.L168:
	.word	276
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSIA_P15_2_IN',0,5,129,1,17
	.word	.L375
	.byte	1,5,3
	.word	IfxQspi2_SLSIA_P15_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSIA_P15_2_IN')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSIB_P15_1_IN')
	.sect	'.debug_info'
.L170:
	.word	276
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSIB_P15_1_IN',0,5,130,1,17
	.word	.L376
	.byte	1,5,3
	.word	IfxQspi2_SLSIB_P15_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSIB_P15_1_IN')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSIA_P02_4_IN')
	.sect	'.debug_info'
.L172:
	.word	276
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSIA_P02_4_IN',0,5,131,1,17
	.word	.L377
	.byte	1,5,3
	.word	IfxQspi3_SLSIA_P02_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSIA_P02_4_IN')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSIC_P33_10_IN')
	.sect	'.debug_info'
.L174:
	.word	277
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSIC_P33_10_IN',0,5,132,1,17
	.word	.L378
	.byte	1,5,3
	.word	IfxQspi3_SLSIC_P33_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSIC_P33_10_IN')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSID_P22_2_IN')
	.sect	'.debug_info'
.L176:
	.word	276
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSID_P22_2_IN',0,5,133,1,17
	.word	.L379
	.byte	1,5,3
	.word	IfxQspi3_SLSID_P22_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSID_P22_2_IN')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO0_P20_8_OUT')
	.sect	'.debug_info'
.L178:
	.word	277
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO0_P20_8_OUT',0,5,134,1,18
	.word	.L380
	.byte	1,5,3
	.word	IfxQspi0_SLSO0_P20_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO0_P20_8_OUT')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO13_P15_0_OUT')
	.sect	'.debug_info'
.L180:
	.word	278
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO13_P15_0_OUT',0,5,135,1,18
	.word	.L381
	.byte	1,5,3
	.word	IfxQspi0_SLSO13_P15_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO13_P15_0_OUT')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO1_P20_9_OUT')
	.sect	'.debug_info'
.L182:
	.word	277
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO1_P20_9_OUT',0,5,136,1,18
	.word	.L382
	.byte	1,5,3
	.word	IfxQspi0_SLSO1_P20_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO1_P20_9_OUT')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO2_P20_13_OUT')
	.sect	'.debug_info'
.L184:
	.word	278
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO2_P20_13_OUT',0,5,137,1,18
	.word	.L383
	.byte	1,5,3
	.word	IfxQspi0_SLSO2_P20_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO2_P20_13_OUT')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO3_P11_10_OUT')
	.sect	'.debug_info'
.L186:
	.word	278
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO3_P11_10_OUT',0,5,138,1,18
	.word	.L384
	.byte	1,5,3
	.word	IfxQspi0_SLSO3_P11_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO3_P11_10_OUT')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO4_P11_11_OUT')
	.sect	'.debug_info'
.L188:
	.word	278
	.half	3
	.word	.L189
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO4_P11_11_OUT',0,5,139,1,18
	.word	.L385
	.byte	1,5,3
	.word	IfxQspi0_SLSO4_P11_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO4_P11_11_OUT')
	.sect	'.debug_abbrev'
.L189:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO5_P11_2_OUT')
	.sect	'.debug_info'
.L190:
	.word	277
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO5_P11_2_OUT',0,5,140,1,18
	.word	.L386
	.byte	1,5,3
	.word	IfxQspi0_SLSO5_P11_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO5_P11_2_OUT')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO6_P20_10_OUT')
	.sect	'.debug_info'
.L192:
	.word	278
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO6_P20_10_OUT',0,5,141,1,18
	.word	.L387
	.byte	1,5,3
	.word	IfxQspi0_SLSO6_P20_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO6_P20_10_OUT')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO7_P33_5_OUT')
	.sect	'.debug_info'
.L194:
	.word	277
	.half	3
	.word	.L195
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO7_P33_5_OUT',0,5,142,1,18
	.word	.L388
	.byte	1,5,3
	.word	IfxQspi0_SLSO7_P33_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO7_P33_5_OUT')
	.sect	'.debug_abbrev'
.L195:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO8_P20_6_OUT')
	.sect	'.debug_info'
.L196:
	.word	277
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO8_P20_6_OUT',0,5,143,1,18
	.word	.L389
	.byte	1,5,3
	.word	IfxQspi0_SLSO8_P20_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO8_P20_6_OUT')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi0_SLSO9_P20_3_OUT')
	.sect	'.debug_info'
.L198:
	.word	277
	.half	3
	.word	.L199
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi0_SLSO9_P20_3_OUT',0,5,144,1,18
	.word	.L390
	.byte	1,5,3
	.word	IfxQspi0_SLSO9_P20_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi0_SLSO9_P20_3_OUT')
	.sect	'.debug_abbrev'
.L199:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO0_P20_8_OUT')
	.sect	'.debug_info'
.L200:
	.word	277
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO0_P20_8_OUT',0,5,145,1,18
	.word	.L391
	.byte	1,5,3
	.word	IfxQspi1_SLSO0_P20_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO0_P20_8_OUT')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO10_P10_0_OUT')
	.sect	'.debug_info'
.L202:
	.word	278
	.half	3
	.word	.L203
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO10_P10_0_OUT',0,5,146,1,18
	.word	.L392
	.byte	1,5,3
	.word	IfxQspi1_SLSO10_P10_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO10_P10_0_OUT')
	.sect	'.debug_abbrev'
.L203:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO1_P20_9_OUT')
	.sect	'.debug_info'
.L204:
	.word	277
	.half	3
	.word	.L205
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO1_P20_9_OUT',0,5,147,1,18
	.word	.L393
	.byte	1,5,3
	.word	IfxQspi1_SLSO1_P20_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO1_P20_9_OUT')
	.sect	'.debug_abbrev'
.L205:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO2_P20_13_OUT')
	.sect	'.debug_info'
.L206:
	.word	278
	.half	3
	.word	.L207
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO2_P20_13_OUT',0,5,148,1,18
	.word	.L394
	.byte	1,5,3
	.word	IfxQspi1_SLSO2_P20_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO2_P20_13_OUT')
	.sect	'.debug_abbrev'
.L207:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO3_P11_10_OUT')
	.sect	'.debug_info'
.L208:
	.word	278
	.half	3
	.word	.L209
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO3_P11_10_OUT',0,5,149,1,18
	.word	.L395
	.byte	1,5,3
	.word	IfxQspi1_SLSO3_P11_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO3_P11_10_OUT')
	.sect	'.debug_abbrev'
.L209:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO4_P11_11_OUT')
	.sect	'.debug_info'
.L210:
	.word	278
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO4_P11_11_OUT',0,5,150,1,18
	.word	.L396
	.byte	1,5,3
	.word	IfxQspi1_SLSO4_P11_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO4_P11_11_OUT')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO5_P11_2_OUT')
	.sect	'.debug_info'
.L212:
	.word	277
	.half	3
	.word	.L213
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO5_P11_2_OUT',0,5,151,1,18
	.word	.L397
	.byte	1,5,3
	.word	IfxQspi1_SLSO5_P11_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO5_P11_2_OUT')
	.sect	'.debug_abbrev'
.L213:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO6_P33_10_OUT')
	.sect	'.debug_info'
.L214:
	.word	278
	.half	3
	.word	.L215
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO6_P33_10_OUT',0,5,152,1,18
	.word	.L398
	.byte	1,5,3
	.word	IfxQspi1_SLSO6_P33_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO6_P33_10_OUT')
	.sect	'.debug_abbrev'
.L215:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO7_P33_5_OUT')
	.sect	'.debug_info'
.L216:
	.word	277
	.half	3
	.word	.L217
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO7_P33_5_OUT',0,5,153,1,18
	.word	.L399
	.byte	1,5,3
	.word	IfxQspi1_SLSO7_P33_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO7_P33_5_OUT')
	.sect	'.debug_abbrev'
.L217:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO8_P10_4_OUT')
	.sect	'.debug_info'
.L218:
	.word	277
	.half	3
	.word	.L219
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO8_P10_4_OUT',0,5,154,1,18
	.word	.L400
	.byte	1,5,3
	.word	IfxQspi1_SLSO8_P10_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO8_P10_4_OUT')
	.sect	'.debug_abbrev'
.L219:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi1_SLSO9_P10_5_OUT')
	.sect	'.debug_info'
.L220:
	.word	277
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi1_SLSO9_P10_5_OUT',0,5,155,1,18
	.word	.L401
	.byte	1,5,3
	.word	IfxQspi1_SLSO9_P10_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi1_SLSO9_P10_5_OUT')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO0_P15_2_OUT')
	.sect	'.debug_info'
.L222:
	.word	277
	.half	3
	.word	.L223
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO0_P15_2_OUT',0,5,156,1,18
	.word	.L402
	.byte	1,5,3
	.word	IfxQspi2_SLSO0_P15_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO0_P15_2_OUT')
	.sect	'.debug_abbrev'
.L223:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO1_P14_2_OUT')
	.sect	'.debug_info'
.L224:
	.word	277
	.half	3
	.word	.L225
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO1_P14_2_OUT',0,5,157,1,18
	.word	.L403
	.byte	1,5,3
	.word	IfxQspi2_SLSO1_P14_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO1_P14_2_OUT')
	.sect	'.debug_abbrev'
.L225:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO2_P14_6_OUT')
	.sect	'.debug_info'
.L226:
	.word	277
	.half	3
	.word	.L227
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO2_P14_6_OUT',0,5,158,1,18
	.word	.L404
	.byte	1,5,3
	.word	IfxQspi2_SLSO2_P14_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO2_P14_6_OUT')
	.sect	'.debug_abbrev'
.L227:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO3_P14_3_OUT')
	.sect	'.debug_info'
.L228:
	.word	277
	.half	3
	.word	.L229
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO3_P14_3_OUT',0,5,159,1,18
	.word	.L405
	.byte	1,5,3
	.word	IfxQspi2_SLSO3_P14_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO3_P14_3_OUT')
	.sect	'.debug_abbrev'
.L229:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO4_P14_7_OUT')
	.sect	'.debug_info'
.L230:
	.word	277
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO4_P14_7_OUT',0,5,160,1,18
	.word	.L406
	.byte	1,5,3
	.word	IfxQspi2_SLSO4_P14_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO4_P14_7_OUT')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO5_P15_1_OUT')
	.sect	'.debug_info'
.L232:
	.word	277
	.half	3
	.word	.L233
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO5_P15_1_OUT',0,5,161,1,18
	.word	.L407
	.byte	1,5,3
	.word	IfxQspi2_SLSO5_P15_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO5_P15_1_OUT')
	.sect	'.debug_abbrev'
.L233:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO6_P33_13_OUT')
	.sect	'.debug_info'
.L234:
	.word	278
	.half	3
	.word	.L235
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO6_P33_13_OUT',0,5,162,1,18
	.word	.L408
	.byte	1,5,3
	.word	IfxQspi2_SLSO6_P33_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO6_P33_13_OUT')
	.sect	'.debug_abbrev'
.L235:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO7_P20_10_OUT')
	.sect	'.debug_info'
.L236:
	.word	278
	.half	3
	.word	.L237
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO7_P20_10_OUT',0,5,163,1,18
	.word	.L409
	.byte	1,5,3
	.word	IfxQspi2_SLSO7_P20_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO7_P20_10_OUT')
	.sect	'.debug_abbrev'
.L237:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO8_P20_6_OUT')
	.sect	'.debug_info'
.L238:
	.word	277
	.half	3
	.word	.L239
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO8_P20_6_OUT',0,5,164,1,18
	.word	.L410
	.byte	1,5,3
	.word	IfxQspi2_SLSO8_P20_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO8_P20_6_OUT')
	.sect	'.debug_abbrev'
.L239:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi2_SLSO9_P20_3_OUT')
	.sect	'.debug_info'
.L240:
	.word	277
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi2_SLSO9_P20_3_OUT',0,5,165,1,18
	.word	.L411
	.byte	1,5,3
	.word	IfxQspi2_SLSO9_P20_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi2_SLSO9_P20_3_OUT')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO0_P02_4_OUT')
	.sect	'.debug_info'
.L242:
	.word	277
	.half	3
	.word	.L243
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO0_P02_4_OUT',0,5,166,1,18
	.word	.L412
	.byte	1,5,3
	.word	IfxQspi3_SLSO0_P02_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO0_P02_4_OUT')
	.sect	'.debug_abbrev'
.L243:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO11_P33_10_OUT')
	.sect	'.debug_info'
.L244:
	.word	279
	.half	3
	.word	.L245
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO11_P33_10_OUT',0,5,167,1,18
	.word	.L413
	.byte	1,5,3
	.word	IfxQspi3_SLSO11_P33_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO11_P33_10_OUT')
	.sect	'.debug_abbrev'
.L245:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO12_P22_2_OUT')
	.sect	'.debug_info'
.L246:
	.word	278
	.half	3
	.word	.L247
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO12_P22_2_OUT',0,5,168,1,18
	.word	.L414
	.byte	1,5,3
	.word	IfxQspi3_SLSO12_P22_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO12_P22_2_OUT')
	.sect	'.debug_abbrev'
.L247:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO13_P23_1_OUT')
	.sect	'.debug_info'
.L248:
	.word	278
	.half	3
	.word	.L249
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO13_P23_1_OUT',0,5,169,1,18
	.word	.L415
	.byte	1,5,3
	.word	IfxQspi3_SLSO13_P23_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO13_P23_1_OUT')
	.sect	'.debug_abbrev'
.L249:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO1_P02_0_OUT')
	.sect	'.debug_info'
.L250:
	.word	277
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO1_P02_0_OUT',0,5,170,1,18
	.word	.L416
	.byte	1,5,3
	.word	IfxQspi3_SLSO1_P02_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO1_P02_0_OUT')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO1_P33_9_OUT')
	.sect	'.debug_info'
.L252:
	.word	277
	.half	3
	.word	.L253
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO1_P33_9_OUT',0,5,171,1,18
	.word	.L417
	.byte	1,5,3
	.word	IfxQspi3_SLSO1_P33_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO1_P33_9_OUT')
	.sect	'.debug_abbrev'
.L253:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO2_P02_1_OUT')
	.sect	'.debug_info'
.L254:
	.word	277
	.half	3
	.word	.L255
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO2_P02_1_OUT',0,5,172,1,18
	.word	.L418
	.byte	1,5,3
	.word	IfxQspi3_SLSO2_P02_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO2_P02_1_OUT')
	.sect	'.debug_abbrev'
.L255:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO2_P33_8_OUT')
	.sect	'.debug_info'
.L256:
	.word	277
	.half	3
	.word	.L257
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO2_P33_8_OUT',0,5,173,1,18
	.word	.L419
	.byte	1,5,3
	.word	IfxQspi3_SLSO2_P33_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO2_P33_8_OUT')
	.sect	'.debug_abbrev'
.L257:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO3_P02_2_OUT')
	.sect	'.debug_info'
.L258:
	.word	277
	.half	3
	.word	.L259
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO3_P02_2_OUT',0,5,174,1,18
	.word	.L420
	.byte	1,5,3
	.word	IfxQspi3_SLSO3_P02_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO3_P02_2_OUT')
	.sect	'.debug_abbrev'
.L259:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO4_P02_3_OUT')
	.sect	'.debug_info'
.L260:
	.word	277
	.half	3
	.word	.L261
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO4_P02_3_OUT',0,5,175,1,18
	.word	.L421
	.byte	1,5,3
	.word	IfxQspi3_SLSO4_P02_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO4_P02_3_OUT')
	.sect	'.debug_abbrev'
.L261:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO4_P23_5_OUT')
	.sect	'.debug_info'
.L262:
	.word	277
	.half	3
	.word	.L263
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO4_P23_5_OUT',0,5,176,1,18
	.word	.L422
	.byte	1,5,3
	.word	IfxQspi3_SLSO4_P23_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO4_P23_5_OUT')
	.sect	'.debug_abbrev'
.L263:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO5_P02_8_OUT')
	.sect	'.debug_info'
.L264:
	.word	277
	.half	3
	.word	.L265
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO5_P02_8_OUT',0,5,177,1,18
	.word	.L423
	.byte	1,5,3
	.word	IfxQspi3_SLSO5_P02_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO5_P02_8_OUT')
	.sect	'.debug_abbrev'
.L265:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO5_P23_4_OUT')
	.sect	'.debug_info'
.L266:
	.word	277
	.half	3
	.word	.L267
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO5_P23_4_OUT',0,5,178,1,18
	.word	.L424
	.byte	1,5,3
	.word	IfxQspi3_SLSO5_P23_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO5_P23_4_OUT')
	.sect	'.debug_abbrev'
.L267:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO6_P00_8_OUT')
	.sect	'.debug_info'
.L268:
	.word	277
	.half	3
	.word	.L269
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO6_P00_8_OUT',0,5,179,1,18
	.word	.L425
	.byte	1,5,3
	.word	IfxQspi3_SLSO6_P00_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO6_P00_8_OUT')
	.sect	'.debug_abbrev'
.L269:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO7_P00_9_OUT')
	.sect	'.debug_info'
.L270:
	.word	277
	.half	3
	.word	.L271
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO7_P00_9_OUT',0,5,180,1,18
	.word	.L426
	.byte	1,5,3
	.word	IfxQspi3_SLSO7_P00_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO7_P00_9_OUT')
	.sect	'.debug_abbrev'
.L271:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO7_P33_7_OUT')
	.sect	'.debug_info'
.L272:
	.word	277
	.half	3
	.word	.L273
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO7_P33_7_OUT',0,5,181,1,18
	.word	.L427
	.byte	1,5,3
	.word	IfxQspi3_SLSO7_P33_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO7_P33_7_OUT')
	.sect	'.debug_abbrev'
.L273:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi3_SLSO8_P10_5_OUT')
	.sect	'.debug_info'
.L274:
	.word	277
	.half	3
	.word	.L275
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi3_SLSO8_P10_5_OUT',0,5,182,1,18
	.word	.L428
	.byte	1,5,3
	.word	IfxQspi3_SLSO8_P10_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi3_SLSO8_P10_5_OUT')
	.sect	'.debug_abbrev'
.L275:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Hsicin_In_pinTable')
	.sect	'.debug_info'
.L276:
	.word	279
	.half	3
	.word	.L277
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Hsicin_In_pinTable',0,5,185,1,26
	.word	.L429
	.byte	1,5,3
	.word	IfxQspi_Hsicin_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Hsicin_In_pinTable')
	.sect	'.debug_abbrev'
.L277:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Mrst_In_pinTable')
	.sect	'.debug_info'
.L278:
	.word	277
	.half	3
	.word	.L279
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Mrst_In_pinTable',0,5,204,1,24
	.word	.L430
	.byte	1,5,3
	.word	IfxQspi_Mrst_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Mrst_In_pinTable')
	.sect	'.debug_abbrev'
.L279:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Mrst_Out_pinTable')
	.sect	'.debug_info'
.L280:
	.word	278
	.half	3
	.word	.L281
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Mrst_Out_pinTable',0,5,239,1,25
	.word	.L431
	.byte	1,5,3
	.word	IfxQspi_Mrst_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Mrst_Out_pinTable')
	.sect	'.debug_abbrev'
.L281:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Mtsr_In_pinTable')
	.sect	'.debug_info'
.L282:
	.word	277
	.half	3
	.word	.L283
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Mtsr_In_pinTable',0,5,138,2,24
	.word	.L432
	.byte	1,5,3
	.word	IfxQspi_Mtsr_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Mtsr_In_pinTable')
	.sect	'.debug_abbrev'
.L283:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Mtsr_Out_pinTable')
	.sect	'.debug_info'
.L284:
	.word	278
	.half	3
	.word	.L285
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Mtsr_Out_pinTable',0,5,169,2,25
	.word	.L433
	.byte	1,5,3
	.word	IfxQspi_Mtsr_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Mtsr_Out_pinTable')
	.sect	'.debug_abbrev'
.L285:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Sclk_In_pinTable')
	.sect	'.debug_info'
.L286:
	.word	277
	.half	3
	.word	.L287
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Sclk_In_pinTable',0,5,204,2,24
	.word	.L434
	.byte	1,5,3
	.word	IfxQspi_Sclk_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Sclk_In_pinTable')
	.sect	'.debug_abbrev'
.L287:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Sclk_Out_pinTable')
	.sect	'.debug_info'
.L288:
	.word	278
	.half	3
	.word	.L289
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Sclk_Out_pinTable',0,5,235,2,25
	.word	.L435
	.byte	1,5,3
	.word	IfxQspi_Sclk_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Sclk_Out_pinTable')
	.sect	'.debug_abbrev'
.L289:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Slsi_In_pinTable')
	.sect	'.debug_info'
.L290:
	.word	277
	.half	3
	.word	.L291
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Slsi_In_pinTable',0,5,142,3,24
	.word	.L436
	.byte	1,5,3
	.word	IfxQspi_Slsi_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Slsi_In_pinTable')
	.sect	'.debug_abbrev'
.L291:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_Slso_Out_pinTable')
	.sect	'.debug_info'
.L292:
	.word	278
	.half	3
	.word	.L293
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxQspi_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxQspi_Slso_Out_pinTable',0,5,169,3,25
	.word	.L437
	.byte	1,5,3
	.word	IfxQspi_Slso_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_Slso_Out_pinTable')
	.sect	'.debug_abbrev'
.L293:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
