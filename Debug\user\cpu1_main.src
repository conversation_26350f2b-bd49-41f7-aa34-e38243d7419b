	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc2904a --dep-file=cpu1_main.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o user/cpu1_main.src ../user/cpu1_main.c"
	.compiler_name		"ctc"
	;source	'../user/cpu1_main.c'

	
$TC16X
	
	.sdecl	'.text.cpu1_dsram',code,cluster('core1_main')
	.sect	'.text.cpu1_dsram'
	.align	2
	
	.global	core1_main
; Function core1_main
.L5:
core1_main:	.type	func
	call	disable_Watchdog
.L24:
	mov	d4,#0
	call	interrupt_global_enable
.L25:
	call	cpu_wait_event_ready
.L26:
	j	.L2
.L3:
.L2:
	j	.L3
.L27:
	ret
.L15:
	
__core1_main_function_end:
	.size	core1_main,__core1_main_function_end-core1_main
.L14:
	; End of function
	
	.calls	'core1_main','disable_Watchdog'
	.calls	'core1_main','interrupt_global_enable'
	.calls	'core1_main','cpu_wait_event_ready'
	.extern	disable_Watchdog
	.extern	cpu_wait_event_ready
	.extern	interrupt_global_enable
	.calls	'core1_main','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L7:
	.word	140526
	.half	3
	.word	.L8
	.byte	4
.L6:
	.byte	1
	.byte	'../user/cpu1_main.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L9
	.byte	2,1,1,3
	.word	182
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	185
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	230
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	242
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	354
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	328
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	360
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	360
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	328
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	469
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	469
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	485
	.byte	4,2,35,0,0,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	621
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	660
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	621
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	904
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	581
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	864
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1095
	.byte	4,2,35,8,0,14
	.word	1135
	.byte	3
	.word	1198
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1203
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	638
	.byte	6,0,4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,3,204,3,17,1,1,5
	.byte	'password',0,3,204,3,59
	.word	638
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1203
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	638
	.byte	6,0,4
	.byte	'IfxScuWdt_setSafetyEndinitInline',0,3,3,163,4,17,1,1,5
	.byte	'password',0,3,163,4,57
	.word	638
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	638
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1203
	.byte	6,0,8
	.byte	'IfxScuWdt_getSafetyWatchdogPasswordInline',0,3,3,253,3,19
	.word	638
	.byte	1,1,6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1613
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1929
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2500
	.byte	4,2,35,0,0,15,4
	.word	621
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	621
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	621
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	621
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2628
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	621
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	621
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	621
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2843
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	621
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	621
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	621
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3058
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	621
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	621
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	621
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3275
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3495
	.byte	4,2,35,0,0,15,24
	.word	621
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	621
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	621
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	621
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	621
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3818
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	621
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	621
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	621
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	621
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4122
	.byte	4,2,35,0,0,15,8
	.word	621
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4447
	.byte	4,2,35,0,0,15,12
	.word	621
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4787
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	446
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5153
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5439
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	446
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5755
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	638
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	638
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6102
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6276
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6450
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6626
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6782
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7463
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	621
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7587
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7671
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7851
	.byte	4,2,35,0,0,15,76
	.word	621
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8104
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8191
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1889
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2460
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2579
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2619
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2803
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3018
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3235
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3455
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2619
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3769
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3809
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4082
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4398
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4438
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4738
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4778
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5113
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5399
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4438
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5546
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5715
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5887
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6062
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6236
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6410
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6586
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6742
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7075
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7423
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4438
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7547
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7796
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8055
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8095
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8151
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8718
	.byte	4,3,35,252,1,0,14
	.word	8758
	.byte	3
	.word	9361
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9366
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	621
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9371
	.byte	6,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,5,202,4,17,1,1,5
	.byte	'port',0,5,202,4,49
	.word	9366
	.byte	5
	.byte	'pinIndex',0,5,202,4,61
	.word	621
	.byte	5
	.byte	'mode',0,5,202,4,90
	.word	9576
	.byte	5
	.byte	'index',0,5,202,4,114
	.word	9646
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9366
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	621
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9959
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,7,226,8,20
	.word	242
	.byte	1,1,6,0,17,9,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	10180
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	621
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,8,147,5,20
	.word	621
	.byte	1,1,19,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,8,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,8,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,8,168,7,17,1,1,5
	.byte	'enabled',0,8,168,7,50
	.word	621
	.byte	6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	10502
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	638
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	621
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	638
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	10502
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	10502
	.byte	19,6,0,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	621
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	621
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	621
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10733
	.byte	4,2,35,0,0,14
	.word	11023
	.byte	3
	.word	11062
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	11067
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,13,100,16,4,11
	.byte	'DISR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,12,13,149,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,13,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,13,181,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11271
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,13,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,229,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11393
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,13,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,245,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11478
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,13,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,253,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11563
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,13,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,133,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11648
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,13,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,141,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11734
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,13,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,149,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11820
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,13,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,157,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11906
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,13,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,133,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11992
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,13,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,165,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12079
	.byte	4,2,35,0,0,15,8
	.word	12121
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,13,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	621
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	621
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	621
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	621
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	621
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	621
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	621
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	3,0,2,35,3,0,12,13,157,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12170
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,13,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	446
	.byte	25,0,2,35,0,0,12,13,173,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12401
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,13,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,12,13,189,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12618
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,13,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,237,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12782
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,13,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,141,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12869
	.byte	4,2,35,0,0,15,144,1
	.word	621
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,13,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	2,0,2,35,3,0,12,13,221,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12969
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,13,175,1,16,4,11
	.byte	'CLR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,12,13,213,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13129
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,13,168,1,16,4,11
	.byte	'RST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,12,13,205,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13235
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,13,160,1,16,4,11
	.byte	'RST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,12,13,197,2,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13339
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,13,253,1,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,13,245,1,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13551
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,13,173,3,25,128,2,13
	.byte	'CLC',0
	.word	11231
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2619
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11353
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2619
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	11438
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	11523
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	11608
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	11694
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	11780
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11866
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11952
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	12039
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	12161
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	12361
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	12578
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	12742
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4778
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	12829
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12918
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12958
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	13089
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	13195
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	13299
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	13422
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	13511
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	14080
	.byte	4,3,35,252,1,0,14
	.word	14120
	.byte	3
	.word	14540
	.byte	8
	.byte	'IfxStm_get',0,3,12,162,4,19
	.word	328
	.byte	1,1,5
	.byte	'stm',0,12,162,4,39
	.word	14545
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,12,179,4,20
	.word	242
	.byte	1,1,5
	.byte	'stm',0,12,179,4,49
	.word	14545
	.byte	19,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,12,190,4,19
	.word	10502
	.byte	1,1,5
	.byte	'stm',0,12,190,4,44
	.word	14545
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,14,108,20
	.word	621
	.byte	1,1,19,6,0,0,4
	.byte	'restoreInterrupts',0,3,14,142,1,17,1,1,5
	.byte	'enabled',0,14,142,1,43
	.word	621
	.byte	19,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,14,164,2,25
	.word	14761
	.byte	1,1,5
	.byte	'timeout',0,14,164,2,50
	.word	14761
	.byte	19,6,0,0,8
	.byte	'isDeadLine',0,3,14,211,2,20
	.word	621
	.byte	1,1,5
	.byte	'deadLine',0,14,211,2,44
	.word	14761
	.byte	19,6,0,0,8
	.byte	'now',0,3,14,221,1,25
	.word	14761
	.byte	1,1,19,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,14,240,1,25
	.word	14761
	.byte	1,1,19,6,0,0,10
	.byte	'_Ifx_CCU6_CLC_Bits',0,16,144,1,16,4,11
	.byte	'DISR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,12,16,172,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14932
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCFG_Bits',0,16,241,2,16,4,11
	.byte	'T12',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'T13',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'MCM',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	446
	.byte	29,0,2,35,0,0,12,16,164,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15090
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ID_Bits',0,16,193,1,16,4,11
	.byte	'MODREV',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'MODNUMBER',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,196,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15224
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MOSEL_Bits',0,16,171,3,16,4,11
	.byte	'TRIG0SEL',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'TRIG1SEL',0,1
	.word	621
	.byte	3,2,2,35,0,11
	.byte	'TRIG2SEL',0,2
	.word	638
	.byte	3,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	446
	.byte	23,0,2,35,0,0,12,16,204,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15351
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PISEL0_Bits',0,16,193,3,16,4,11
	.byte	'ISCC60',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'ISCC61',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'ISCC62',0,1
	.word	621
	.byte	2,2,2,35,0,11
	.byte	'ISTRP',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'ISPOS0',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'ISPOS1',0,1
	.word	621
	.byte	2,4,2,35,1,11
	.byte	'ISPOS2',0,1
	.word	621
	.byte	2,2,2,35,1,11
	.byte	'IST12HR',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,220,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15501
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PISEL2_Bits',0,16,207,3,16,4,11
	.byte	'IST13HR',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'ISCNT12',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'ISCNT13',0,1
	.word	621
	.byte	2,2,2,35,0,11
	.byte	'T12EXT',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'T13EXT',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,12,16,228,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KSCSR_Bits',0,16,212,2,16,4,11
	.byte	'SB0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SB1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'SB2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SB3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,12,16,148,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15921
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12_Bits',0,16,227,3,16,4,11
	.byte	'T12CV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,244,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12PR_Bits',0,16,131,4,16,4,11
	.byte	'T12PV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,140,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16177
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12DTC_Bits',0,16,234,3,16,4,11
	.byte	'DTM',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'DTE0',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'DTE1',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'DTE2',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'DTR0',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'DTR1',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'DTR2',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	446
	.byte	17,0,2,35,0,0,12,16,252,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16285
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC60R_Bits',0,16,88,16,4,11
	.byte	'CCV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,236,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC61R_Bits',0,16,102,16,4,11
	.byte	'CCV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,252,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16616
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC62R_Bits',0,16,116,16,4,11
	.byte	'CCV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,140,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16721
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC60SR_Bits',0,16,95,16,4,11
	.byte	'CCS',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,244,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16826
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC61SR_Bits',0,16,109,16,4,11
	.byte	'CCS',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,132,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16932
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC62SR_Bits',0,16,123,16,4,11
	.byte	'CCS',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,148,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17038
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T13_Bits',0,16,138,4,16,4,11
	.byte	'T13CV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,148,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17144
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T13PR_Bits',0,16,145,4,16,4,11
	.byte	'T13PV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,156,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17250
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC63R_Bits',0,16,130,1,16,4,11
	.byte	'CCV',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,156,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CC63SR_Bits',0,16,137,1,16,4,11
	.byte	'CCS',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,164,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17464
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CMPSTAT_Bits',0,16,171,1,16,4,11
	.byte	'CC60ST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'CC61ST',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'CC62ST',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'CCPOS60',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'CCPOS61',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'CCPOS62',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'CC63ST',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'CC60PS',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'COUT60PS',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'CC61PS',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'COUT61PS',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'CC62PS',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'COUT62PS',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'COUT63PS',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'T13IM',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,188,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_CMPMODIF_Bits',0,16,154,1,16,4,11
	.byte	'MCC60S',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'MCC61S',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'MCC62S',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	621
	.byte	3,2,2,35,0,11
	.byte	'MCC63S',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'MCC60R',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'MCC61R',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'MCC62R',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	621
	.byte	3,2,2,35,1,11
	.byte	'MCC63R',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	446
	.byte	17,0,2,35,0,0,12,16,180,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17966
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_T12MSEL_Bits',0,16,248,3,16,4,11
	.byte	'MSEL60',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'MSEL61',0,1
	.word	621
	.byte	4,0,2,35,0,11
	.byte	'MSEL62',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'HSYNC',0,1
	.word	621
	.byte	3,1,2,35,1,11
	.byte	'DBYP',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,132,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18271
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR0_Bits',0,16,152,4,16,4,11
	.byte	'T12CLK',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'T12PRE',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'T12R',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'STE12',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'CDIR',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'CTM',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'T13CLK',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'T13PRE',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'T13R',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'STE13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	446
	.byte	18,0,2,35,0,0,12,16,164,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18451
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR2_Bits',0,16,168,4,16,4,11
	.byte	'T12SSC',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'T13SSC',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'T13TEC',0,1
	.word	621
	.byte	3,3,2,35,0,11
	.byte	'T13TED',0,1
	.word	621
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'T12RSEL',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'T13RSEL',0,1
	.word	621
	.byte	2,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	446
	.byte	20,0,2,35,0,0,12,16,172,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18711
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TCTR4_Bits',0,16,181,4,16,4,11
	.byte	'T12RR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'T12RS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'T12RES',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'DTRES',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'T12CNT',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'T12STR',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'T12STD',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'T13RR',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'T13RS',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'T13RES',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	621
	.byte	2,3,2,35,1,11
	.byte	'T13CNT',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'T13STR',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'T13STD',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,180,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18934
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MODCTR_Bits',0,16,159,3,16,4,11
	.byte	'T12MODEN',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'MCMEN',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'T13MODEN',0,1
	.word	621
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'ECT13O',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,196,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19299
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_TRPCTR_Bits',0,16,202,4,16,4,11
	.byte	'TRPM0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'TRPM1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'TRPM2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	621
	.byte	5,0,2,35,0,11
	.byte	'TRPEN',0,1
	.word	621
	.byte	6,2,2,35,1,11
	.byte	'TRPEN13',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'TRPPEN',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,188,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_PSLR_Bits',0,16,218,3,16,4,11
	.byte	'PSL',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PSL63',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,12,16,236,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19730
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMOUTS_Bits',0,16,146,3,16,4,11
	.byte	'MCMPS',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'STRMCM',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EXPHS',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'CURHS',0,1
	.word	621
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'STRHP',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,188,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19873
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMOUT_Bits',0,16,135,3,16,4,11
	.byte	'MCMP',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'R',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EXPH',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'CURH',0,1
	.word	621
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	446
	.byte	18,0,2,35,0,0,12,16,180,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20097
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_MCMCTR_Bits',0,16,250,2,16,4,11
	.byte	'SWSEL',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'SWSYN',0,1
	.word	621
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'STE12U',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'STE12D',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'STE13U',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	446
	.byte	21,0,2,35,0,0,12,16,172,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20272
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IMON_Bits',0,16,223,1,16,4,11
	.byte	'LBE',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0I',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1I',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2I',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'CC60INI',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'CC61INI',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'CC62INI',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'CTRAPI',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'T12HRI',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'T13HRI',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	446
	.byte	22,0,2,35,0,0,12,16,212,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_LI_Bits',0,16,222,2,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0EN',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1EN',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2EN',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'CC60INEN',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'CC61INEN',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'CC62INEN',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'CTRAPEN',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'T12HREN',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'T13HREN',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	3,3,2,35,1,11
	.byte	'LBEEN',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'INPLBE',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,156,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20769
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IS_Bits',0,16,252,1,16,4,11
	.byte	'ICC60R',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ICC60F',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'ICC61R',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'ICC61F',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'ICC62R',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'ICC62F',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'T12OM',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'T12PM',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'T13CM',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'T13PM',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'TRPF',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'TRPS',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'CHE',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'WHE',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'IDLE',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'STR',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,228,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21114
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ISS_Bits',0,16,168,2,16,4,11
	.byte	'SCC60R',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SCC60F',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'SCC61R',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SCC61F',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'SCC62R',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'SCC62F',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'ST12OM',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'ST12PM',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'ST13CM',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'ST13PM',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'STRPF',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'SWHC',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'SCHE',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'SWHE',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'SIDLE',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'SSTR',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,244,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21471
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ISR_Bits',0,16,146,2,16,4,11
	.byte	'RCC60R',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'RCC60F',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RCC61R',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'RCC61F',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'RCC62R',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'RCC62F',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'RT12OM',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'RT12PM',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'RT13CM',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'RT13PM',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'RTRPF',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'RCHE',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'RWHE',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'RIDLE',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'RSTR',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,236,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21838
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_INP_Bits',0,16,239,1,16,4,11
	.byte	'INPCC60',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'INPCC61',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'INPCC62',0,1
	.word	621
	.byte	2,2,2,35,0,11
	.byte	'INPCHE',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'INPERR',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'INPT12',0,1
	.word	621
	.byte	2,4,2,35,1,11
	.byte	'INPT13',0,1
	.word	621
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	446
	.byte	18,0,2,35,0,0,12,16,220,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22212
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_IEN_Bits',0,16,201,1,16,4,11
	.byte	'ENCC60R',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ENCC60F',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'ENCC61R',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'ENCC61F',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'ENCC62R',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'ENCC62F',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'ENT12OM',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'ENT12PM',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'ENT13CM',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'ENT13PM',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'ENTRPF',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'ENCHE',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'ENWHE',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'ENIDLE',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'ENSTR',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,16,204,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22430
	.byte	4,2,35,0,0,15,52
	.word	621
	.byte	16,51,0,10
	.byte	'_Ifx_CCU6_OCS_Bits',0,16,180,3,16,4,11
	.byte	'TGS',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	2,0,2,35,3,0,12,16,212,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22828
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRSTCLR_Bits',0,16,205,2,16,4,11
	.byte	'CLR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,12,16,140,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23035
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRST1_Bits',0,16,198,2,16,4,11
	.byte	'RST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,12,16,132,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23142
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_KRST0_Bits',0,16,190,2,16,4,11
	.byte	'RST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,12,16,252,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23247
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,16,228,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23371
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,16,220,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23461
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CCU6',0,16,204,7,25,128,2,13
	.byte	'CLC',0
	.word	15050
	.byte	4,2,35,0,13
	.byte	'MCFG',0
	.word	15184
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	15311
	.byte	4,2,35,8,13
	.byte	'MOSEL',0
	.word	15461
	.byte	4,2,35,12,13
	.byte	'PISEL0',0
	.word	15697
	.byte	4,2,35,16,13
	.byte	'PISEL2',0
	.word	15881
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	2619
	.byte	4,2,35,24,13
	.byte	'KSCSR',0
	.word	16031
	.byte	4,2,35,28,13
	.byte	'T12',0
	.word	16137
	.byte	4,2,35,32,13
	.byte	'T12PR',0
	.word	16245
	.byte	4,2,35,36,13
	.byte	'T12DTC',0
	.word	16471
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	2619
	.byte	4,2,35,44,13
	.byte	'CC60R',0
	.word	16576
	.byte	4,2,35,48,13
	.byte	'CC61R',0
	.word	16681
	.byte	4,2,35,52,13
	.byte	'CC62R',0
	.word	16786
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	2619
	.byte	4,2,35,60,13
	.byte	'CC60SR',0
	.word	16892
	.byte	4,2,35,64,13
	.byte	'CC61SR',0
	.word	16998
	.byte	4,2,35,68,13
	.byte	'CC62SR',0
	.word	17104
	.byte	4,2,35,72,13
	.byte	'reserved_4C',0
	.word	2619
	.byte	4,2,35,76,13
	.byte	'T13',0
	.word	17210
	.byte	4,2,35,80,13
	.byte	'T13PR',0
	.word	17318
	.byte	4,2,35,84,13
	.byte	'CC63R',0
	.word	17424
	.byte	4,2,35,88,13
	.byte	'CC63SR',0
	.word	17531
	.byte	4,2,35,92,13
	.byte	'CMPSTAT',0
	.word	17926
	.byte	4,2,35,96,13
	.byte	'CMPMODIF',0
	.word	18231
	.byte	4,2,35,100,13
	.byte	'T12MSEL',0
	.word	18411
	.byte	4,2,35,104,13
	.byte	'reserved_6C',0
	.word	2619
	.byte	4,2,35,108,13
	.byte	'TCTR0',0
	.word	18671
	.byte	4,2,35,112,13
	.byte	'TCTR2',0
	.word	18894
	.byte	4,2,35,116,13
	.byte	'TCTR4',0
	.word	19259
	.byte	4,2,35,120,13
	.byte	'reserved_7C',0
	.word	2619
	.byte	4,2,35,124,13
	.byte	'MODCTR',0
	.word	19471
	.byte	4,3,35,128,1,13
	.byte	'TRPCTR',0
	.word	19690
	.byte	4,3,35,132,1,13
	.byte	'PSLR',0
	.word	19833
	.byte	4,3,35,136,1,13
	.byte	'MCMOUTS',0
	.word	20057
	.byte	4,3,35,140,1,13
	.byte	'MCMOUT',0
	.word	20232
	.byte	4,3,35,144,1,13
	.byte	'MCMCTR',0
	.word	20456
	.byte	4,3,35,148,1,13
	.byte	'IMON',0
	.word	20729
	.byte	4,3,35,152,1,13
	.byte	'LI',0
	.word	21074
	.byte	4,3,35,156,1,13
	.byte	'IS',0
	.word	21431
	.byte	4,3,35,160,1,13
	.byte	'ISS',0
	.word	21798
	.byte	4,3,35,164,1,13
	.byte	'ISR',0
	.word	22172
	.byte	4,3,35,168,1,13
	.byte	'INP',0
	.word	22390
	.byte	4,3,35,172,1,13
	.byte	'IEN',0
	.word	22779
	.byte	4,3,35,176,1,13
	.byte	'reserved_B4',0
	.word	22819
	.byte	52,3,35,180,1,13
	.byte	'OCS',0
	.word	22995
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	23102
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	23207
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	23331
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	23421
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	23991
	.byte	4,3,35,252,1,0,14
	.word	24031
	.byte	3
	.word	24887
	.byte	17,15,81,9,1,18
	.byte	'IfxCcu6_CaptureCompareInput_cC60',0,0,18
	.byte	'IfxCcu6_CaptureCompareInput_cC61',0,2,18
	.byte	'IfxCcu6_CaptureCompareInput_cC62',0,4,18
	.byte	'IfxCcu6_CaptureCompareInput_cTRAP',0,6,18
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS0',0,8,18
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS1',0,10,18
	.byte	'IfxCcu6_CaptureCompareInput_cCPOS2',0,12,0,17,15,94,9,1,18
	.byte	'IfxCcu6_CaptureCompareInputSignal_a',0,0,18
	.byte	'IfxCcu6_CaptureCompareInputSignal_b',0,1,18
	.byte	'IfxCcu6_CaptureCompareInputSignal_c',0,2,18
	.byte	'IfxCcu6_CaptureCompareInputSignal_d',0,3,0,4
	.byte	'IfxCcu6_setCaptureCompareInputSignal',0,3,15,161,15,17,1,1,5
	.byte	'ccu6',0,15,161,15,64
	.word	24892
	.byte	5
	.byte	'input',0,15,161,15,98
	.word	24897
	.byte	5
	.byte	'signal',0,15,161,15,139,1
	.word	25155
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,18,118,16,4,11
	.byte	'DISR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,12,18,207,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25406
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,18,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	638
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	621
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	638
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,151,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25565
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,18,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	638
	.byte	16,0,2,35,2,0,12,18,143,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25860
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,18,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	621
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	638
	.byte	11,0,2,35,2,0,12,18,247,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25985
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,18,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	621
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	638
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,231,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26210
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,18,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	638
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	621
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	621
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,183,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26451
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,18,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	638
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	621
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	621
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	621
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	638
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,135,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26672
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,18,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	638
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	8,0,2,35,3,0,12,18,223,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26937
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,18,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	638
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	638
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,12,18,199,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27134
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,18,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	638
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,12,18,191,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27291
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,18,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,12,18,191,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27445
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,18,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,12,18,183,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27645
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,18,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,12,18,199,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27759
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,18,135,5,25,12,13
	.byte	'CON',0
	.word	27605
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	27719
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	27834
	.byte	4,2,35,8,0,14
	.word	27874
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,18,148,1,16,4,11
	.byte	'TH',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	638
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,231,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27947
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,18,241,1,16,4,11
	.byte	'THS',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	638
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,255,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28433
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,18,180,1,16,4,11
	.byte	'THC',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	638
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,239,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28946
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,18,212,1,16,4,11
	.byte	'THE',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	638
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,247,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29461
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,18,143,3,16,4,11
	.byte	'DATA',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,18,239,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29926
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,18,245,2,16,4,11
	.byte	'DATA',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,18,215,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30013
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,18,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	621
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	446
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,215,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30100
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,18,251,2,16,4,11
	.byte	'DATA',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,18,223,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30223
	.byte	4,2,35,0,0,15,148,1
	.word	621
	.byte	16,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,18,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	2,0,2,35,3,0,12,18,207,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30322
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,18,202,2,16,4,11
	.byte	'CLR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,12,18,175,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30485
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,18,195,2,16,4,11
	.byte	'RST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,12,18,167,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30594
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,18,187,2,16,4,11
	.byte	'RST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,12,18,159,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,18,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,12,18,175,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30827
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,18,45,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,12,18,167,3,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30919
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,18,153,5,25,128,2,13
	.byte	'CLC',0
	.word	25525
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	25820
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	25945
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	26170
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	26411
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	26632
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	26897
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	27094
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	27251
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	27405
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	27942
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	28393
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	28906
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	29421
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	29886
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	29973
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	30060
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	30183
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	30271
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	30311
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	30445
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	30554
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	30661
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	30787
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	30879
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	31451
	.byte	4,3,35,252,1,0,14
	.word	31491
	.byte	3
	.word	31933
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,17,228,13,17,1,1,5
	.byte	'asclin',0,17,228,13,49
	.word	31938
	.byte	5
	.byte	'enable',0,17,228,13,65
	.word	621
	.byte	6,0,17,17,123,9,1,18
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,18
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,18
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,18
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,17,169,17,17,1,1,5
	.byte	'asclin',0,17,169,17,51
	.word	31938
	.byte	5
	.byte	'ctsi',0,17,169,17,84
	.word	32005
	.byte	6,0,17,17,181,2,9,1,18
	.byte	'IfxAsclin_RxInputSelect_0',0,0,18
	.byte	'IfxAsclin_RxInputSelect_1',0,1,18
	.byte	'IfxAsclin_RxInputSelect_2',0,2,18
	.byte	'IfxAsclin_RxInputSelect_3',0,3,18
	.byte	'IfxAsclin_RxInputSelect_4',0,4,18
	.byte	'IfxAsclin_RxInputSelect_5',0,5,18
	.byte	'IfxAsclin_RxInputSelect_6',0,6,18
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,17,191,18,17,1,1,5
	.byte	'asclin',0,17,191,18,50
	.word	31938
	.byte	5
	.byte	'alti',0,17,191,18,82
	.word	32189
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,19,60,9,12,13
	.byte	'count',0
	.word	32481
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	32494
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	32494
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	32481
	.byte	2,2,35,10,0,14
	.word	621
	.byte	14
	.word	621
	.byte	10
	.byte	'_Fifo',0,19,73,16,28,13
	.byte	'buffer',0
	.word	360
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	32506
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	32481
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	32481
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	32481
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	32481
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	32587
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	32592
	.byte	1,2,35,25,0,3
	.word	32597
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,19,206,1,22
	.word	32481
	.byte	1,1,5
	.byte	'fifo',0,19,206,1,51
	.word	32756
	.byte	6,0,21
	.word	190
	.byte	22
	.word	216
	.byte	6,0,21
	.word	251
	.byte	22
	.word	283
	.byte	6,0,21
	.word	296
	.byte	6,0,21
	.word	365
	.byte	22
	.word	384
	.byte	6,0,21
	.word	400
	.byte	22
	.word	415
	.byte	22
	.word	429
	.byte	6,0,21
	.word	1208
	.byte	22
	.word	1248
	.byte	22
	.word	1266
	.byte	6,0,21
	.word	1286
	.byte	22
	.word	1329
	.byte	6,0,21
	.word	1349
	.byte	22
	.word	1387
	.byte	22
	.word	1405
	.byte	6,0,21
	.word	1425
	.byte	22
	.word	1466
	.byte	6,0,21
	.word	1486
	.byte	22
	.word	1537
	.byte	6,0,21
	.word	1557
	.byte	6,0,21
	.word	9496
	.byte	22
	.word	9528
	.byte	22
	.word	9542
	.byte	22
	.word	9560
	.byte	6,0,21
	.word	9863
	.byte	22
	.word	9896
	.byte	22
	.word	9910
	.byte	22
	.word	9928
	.byte	22
	.word	9942
	.byte	6,0,21
	.word	10062
	.byte	22
	.word	10090
	.byte	22
	.word	10104
	.byte	22
	.word	10122
	.byte	6,0,21
	.word	10140
	.byte	6,0,21
	.word	10259
	.byte	6,0,21
	.word	10293
	.byte	6,0,21
	.word	10335
	.byte	19,23
	.word	10293
	.byte	24
	.word	10333
	.byte	0,6,0,0,21
	.word	10376
	.byte	6,0,21
	.word	10410
	.byte	6,0,21
	.word	10450
	.byte	22
	.word	10483
	.byte	6,0,21
	.word	10523
	.byte	22
	.word	10564
	.byte	6,0,21
	.word	10583
	.byte	22
	.word	10638
	.byte	6,0,21
	.word	10657
	.byte	22
	.word	10697
	.byte	22
	.word	10714
	.byte	19,6,0,0,21
	.word	11072
	.byte	22
	.word	11100
	.byte	6,0,21
	.word	14550
	.byte	22
	.word	14573
	.byte	6,0,21
	.word	14588
	.byte	22
	.word	14620
	.byte	19,19,23
	.word	10140
	.byte	24
	.word	10178
	.byte	0,0,6,0,0,21
	.word	14638
	.byte	22
	.word	14666
	.byte	6,0,21
	.word	14681
	.byte	19,23
	.word	10335
	.byte	25
	.word	10372
	.byte	23
	.word	10293
	.byte	24
	.word	10333
	.byte	0,24
	.word	10373
	.byte	0,0,6,0,0,21
	.word	14714
	.byte	22
	.word	14740
	.byte	19,23
	.word	10450
	.byte	22
	.word	10483
	.byte	24
	.word	10500
	.byte	0,6,0,0,21
	.word	14778
	.byte	22
	.word	14802
	.byte	19,23
	.word	14868
	.byte	25
	.word	14884
	.byte	23
	.word	14681
	.byte	25
	.word	14710
	.byte	23
	.word	10335
	.byte	25
	.word	10372
	.byte	23
	.word	10293
	.byte	24
	.word	10333
	.byte	0,24
	.word	10373
	.byte	0,0,24
	.word	14711
	.byte	0,0,24
	.word	14885
	.byte	23
	.word	14714
	.byte	22
	.word	14740
	.byte	25
	.word	14757
	.byte	23
	.word	10450
	.byte	22
	.word	10483
	.byte	24
	.word	10500
	.byte	0,24
	.word	14758
	.byte	0,0,24
	.word	14886
	.byte	23
	.word	14550
	.byte	22
	.word	14573
	.byte	24
	.word	14586
	.byte	0,24
	.word	14887
	.byte	0,0,6,0,0,21
	.word	14823
	.byte	22
	.word	14846
	.byte	19,23
	.word	14868
	.byte	25
	.word	14884
	.byte	23
	.word	14681
	.byte	25
	.word	14710
	.byte	23
	.word	10335
	.byte	25
	.word	10372
	.byte	23
	.word	10293
	.byte	24
	.word	10333
	.byte	0,24
	.word	10373
	.byte	0,0,24
	.word	14711
	.byte	0,0,24
	.word	14885
	.byte	23
	.word	14714
	.byte	22
	.word	14740
	.byte	25
	.word	14757
	.byte	23
	.word	10450
	.byte	22
	.word	10483
	.byte	24
	.word	10500
	.byte	0,24
	.word	14758
	.byte	0,0,24
	.word	14886
	.byte	23
	.word	14550
	.byte	22
	.word	14573
	.byte	24
	.word	14586
	.byte	0,24
	.word	14887
	.byte	0,0,6,0,0,21
	.word	14868
	.byte	19,23
	.word	14681
	.byte	25
	.word	14710
	.byte	23
	.word	10335
	.byte	25
	.word	10372
	.byte	23
	.word	10293
	.byte	24
	.word	10333
	.byte	0,24
	.word	10373
	.byte	0,0,24
	.word	14711
	.byte	0,0,6,23
	.word	14714
	.byte	22
	.word	14740
	.byte	25
	.word	14757
	.byte	23
	.word	10450
	.byte	22
	.word	10483
	.byte	24
	.word	10500
	.byte	0,24
	.word	14758
	.byte	0,0,6,23
	.word	14550
	.byte	22
	.word	14573
	.byte	24
	.word	14586
	.byte	0,6,0,0,21
	.word	14890
	.byte	19,23
	.word	14550
	.byte	22
	.word	14573
	.byte	24
	.word	14586
	.byte	0,6,0,0,21
	.word	25313
	.byte	22
	.word	25358
	.byte	22
	.word	25372
	.byte	22
	.word	25387
	.byte	6,0,26
	.byte	'disable_Watchdog',0,20,45,6,1,1,1,1,26
	.byte	'cpu_wait_event_ready',0,20,47,6,1,1,1,1,27
	.byte	'interrupt_global_enable',0,21,41,8,1,1,1,1,5
	.byte	'primask',0,21,41,40
	.word	10502
	.byte	0,21
	.word	31943
	.byte	22
	.word	31971
	.byte	22
	.word	31987
	.byte	6,0,21
	.word	32127
	.byte	22
	.word	32157
	.byte	22
	.word	32173
	.byte	6,0,21
	.word	32420
	.byte	22
	.word	32449
	.byte	22
	.word	32465
	.byte	6,0,21
	.word	32761
	.byte	22
	.word	32792
	.byte	6,0,28
	.byte	'__wchar_t',0,22,1,1
	.word	32481
	.byte	28
	.byte	'__size_t',0,22,1,1
	.word	446
	.byte	28
	.byte	'__ptrdiff_t',0,22,1,1
	.word	462
	.byte	29,1,3
	.word	33914
	.byte	28
	.byte	'__codeptr',0,22,1,1
	.word	33916
	.byte	28
	.byte	'__intptr_t',0,22,1,1
	.word	462
	.byte	28
	.byte	'__uintptr_t',0,22,1,1
	.word	446
	.byte	28
	.byte	'_iob_flag_t',0,23,82,25
	.word	638
	.byte	28
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,18,79,3
	.word	30919
	.byte	28
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,18,85,3
	.word	30827
	.byte	28
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,18,97,3
	.word	26451
	.byte	28
	.byte	'Ifx_ASCLIN_BRD_Bits',0,18,106,3
	.word	27291
	.byte	28
	.byte	'Ifx_ASCLIN_BRG_Bits',0,18,115,3
	.word	27134
	.byte	28
	.byte	'Ifx_ASCLIN_CLC_Bits',0,18,125,3
	.word	25406
	.byte	28
	.byte	'Ifx_ASCLIN_CSR_Bits',0,18,133,1,3
	.word	30100
	.byte	28
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,18,145,1,3
	.word	26937
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,18,177,1,3
	.word	27947
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,18,209,1,3
	.word	28946
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,18,238,1,3
	.word	29461
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,18,142,2,3
	.word	28433
	.byte	28
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,18,158,2,3
	.word	26672
	.byte	28
	.byte	'Ifx_ASCLIN_ID_Bits',0,18,166,2,3
	.word	25860
	.byte	28
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,18,184,2,3
	.word	25565
	.byte	28
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,18,192,2,3
	.word	30701
	.byte	28
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,18,199,2,3
	.word	30594
	.byte	28
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,18,206,2,3
	.word	30485
	.byte	28
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,18,213,2,3
	.word	27645
	.byte	28
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,18,225,2,3
	.word	27445
	.byte	28
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,18,232,2,3
	.word	27759
	.byte	28
	.byte	'Ifx_ASCLIN_OCS_Bits',0,18,242,2,3
	.word	30322
	.byte	28
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,18,248,2,3
	.word	30013
	.byte	28
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,18,254,2,3
	.word	30223
	.byte	28
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,18,140,3,3
	.word	26210
	.byte	28
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,18,146,3,3
	.word	29926
	.byte	28
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,18,159,3,3
	.word	25985
	.byte	28
	.byte	'Ifx_ASCLIN_ACCEN0',0,18,172,3,3
	.word	31451
	.byte	28
	.byte	'Ifx_ASCLIN_ACCEN1',0,18,180,3,3
	.word	30879
	.byte	28
	.byte	'Ifx_ASCLIN_BITCON',0,18,188,3,3
	.word	26632
	.byte	28
	.byte	'Ifx_ASCLIN_BRD',0,18,196,3,3
	.word	27405
	.byte	28
	.byte	'Ifx_ASCLIN_BRG',0,18,204,3,3
	.word	27251
	.byte	28
	.byte	'Ifx_ASCLIN_CLC',0,18,212,3,3
	.word	25525
	.byte	28
	.byte	'Ifx_ASCLIN_CSR',0,18,220,3,3
	.word	30183
	.byte	28
	.byte	'Ifx_ASCLIN_DATCON',0,18,228,3,3
	.word	27094
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGS',0,18,236,3,3
	.word	28393
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,18,244,3,3
	.word	29421
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,18,252,3,3
	.word	29886
	.byte	28
	.byte	'Ifx_ASCLIN_FLAGSSET',0,18,132,4,3
	.word	28906
	.byte	28
	.byte	'Ifx_ASCLIN_FRAMECON',0,18,140,4,3
	.word	26897
	.byte	28
	.byte	'Ifx_ASCLIN_ID',0,18,148,4,3
	.word	25945
	.byte	28
	.byte	'Ifx_ASCLIN_IOCR',0,18,156,4,3
	.word	25820
	.byte	28
	.byte	'Ifx_ASCLIN_KRST0',0,18,164,4,3
	.word	30787
	.byte	28
	.byte	'Ifx_ASCLIN_KRST1',0,18,172,4,3
	.word	30661
	.byte	28
	.byte	'Ifx_ASCLIN_KRSTCLR',0,18,180,4,3
	.word	30554
	.byte	28
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,18,188,4,3
	.word	27719
	.byte	28
	.byte	'Ifx_ASCLIN_LIN_CON',0,18,196,4,3
	.word	27605
	.byte	28
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,18,204,4,3
	.word	27834
	.byte	28
	.byte	'Ifx_ASCLIN_OCS',0,18,212,4,3
	.word	30445
	.byte	28
	.byte	'Ifx_ASCLIN_RXDATA',0,18,220,4,3
	.word	30060
	.byte	28
	.byte	'Ifx_ASCLIN_RXDATAD',0,18,228,4,3
	.word	30271
	.byte	28
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,18,236,4,3
	.word	26411
	.byte	28
	.byte	'Ifx_ASCLIN_TXDATA',0,18,244,4,3
	.word	29973
	.byte	28
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,18,252,4,3
	.word	26170
	.byte	14
	.word	27874
	.byte	28
	.byte	'Ifx_ASCLIN_LIN',0,18,140,5,3
	.word	35597
	.byte	14
	.word	31491
	.byte	28
	.byte	'Ifx_ASCLIN',0,18,181,5,3
	.word	35626
	.byte	28
	.byte	'boolean',0,24,101,29
	.word	621
	.byte	28
	.byte	'uint8',0,24,105,29
	.word	621
	.byte	28
	.byte	'uint16',0,24,109,29
	.word	638
	.byte	28
	.byte	'uint32',0,24,113,29
	.word	10502
	.byte	28
	.byte	'uint64',0,24,118,29
	.word	328
	.byte	28
	.byte	'sint16',0,24,126,29
	.word	32481
	.byte	28
	.byte	'sint32',0,24,131,1,29
	.word	32494
	.byte	28
	.byte	'sint64',0,24,138,1,29
	.word	14761
	.byte	28
	.byte	'float32',0,24,167,1,29
	.word	242
	.byte	28
	.byte	'pvoid',0,25,57,28
	.word	360
	.byte	28
	.byte	'Ifx_TickTime',0,25,79,28
	.word	14761
	.byte	28
	.byte	'Ifx_SizeT',0,25,92,16
	.word	32481
	.byte	28
	.byte	'Ifx_Priority',0,25,103,16
	.word	638
	.byte	28
	.byte	'Ifx_TimerValue',0,25,104,16
	.word	10502
	.byte	17,25,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,28
	.byte	'Ifx_RxSel',0,25,140,1,3
	.word	35887
	.byte	17,25,164,1,9,1,18
	.byte	'Ifx_DataBufferMode_normal',0,0,18
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,28
	.byte	'Ifx_DataBufferMode',0,25,169,1,2
	.word	36025
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,26,45,16,4,11
	.byte	'ADDR',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_A_Bits',0,26,48,3
	.word	36125
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,26,51,16,4,11
	.byte	'VSS',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	469
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BIV_Bits',0,26,55,3
	.word	36186
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,26,58,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	469
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_BTV_Bits',0,26,62,3
	.word	36265
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,26,65,16,4,11
	.byte	'CountValue',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT_Bits',0,26,69,3
	.word	36351
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,26,72,16,4,11
	.byte	'CM',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	469
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	469
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	469
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL_Bits',0,26,80,3
	.word	36440
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,26,83,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	469
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT_Bits',0,26,89,3
	.word	36586
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,26,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID_Bits',0,26,96,3
	.word	36713
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,26,99,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	469
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L_Bits',0,26,103,3
	.word	36811
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,26,106,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	469
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U_Bits',0,26,110,3
	.word	36904
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,26,113,16,4,11
	.byte	'MODREV',0,4
	.word	469
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	469
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID_Bits',0,26,118,3
	.word	36997
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,26,121,16,4,11
	.byte	'XE',0,4
	.word	469
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE_Bits',0,26,125,3
	.word	37104
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,26,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	469
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	469
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT_Bits',0,26,136,1,3
	.word	37191
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,26,139,1,16,4,11
	.byte	'CID',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID_Bits',0,26,143,1,3
	.word	37345
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,26,146,1,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_D_Bits',0,26,149,1,3
	.word	37439
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,26,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	469
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	469
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	469
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	469
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DATR_Bits',0,26,163,1,3
	.word	37502
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,26,166,1,16,4,11
	.byte	'DE',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	469
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	469
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	469
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	469
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	469
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	469
	.byte	19,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR_Bits',0,26,177,1,3
	.word	37720
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,26,180,1,16,4,11
	.byte	'DTA',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR_Bits',0,26,184,1,3
	.word	37935
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,26,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0_Bits',0,26,192,1,3
	.word	38029
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,26,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2_Bits',0,26,199,1,3
	.word	38145
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,26,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	469
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_CPU_DCX_Bits',0,26,206,1,3
	.word	38246
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,26,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD_Bits',0,26,212,1,3
	.word	38339
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,26,215,1,16,4,11
	.byte	'TA',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR_Bits',0,26,218,1,3
	.word	38419
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,26,221,1,16,4,11
	.byte	'IED',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	469
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	469
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	469
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	469
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	469
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR_Bits',0,26,233,1,3
	.word	38488
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,26,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	469
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_DMS_Bits',0,26,240,1,3
	.word	38717
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,26,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	469
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L_Bits',0,26,247,1,3
	.word	38810
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,26,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	469
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U_Bits',0,26,254,1,3
	.word	38905
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,26,129,2,16,4,11
	.byte	'RE',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE_Bits',0,26,133,2,3
	.word	39000
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,26,136,2,16,4,11
	.byte	'WE',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE_Bits',0,26,140,2,3
	.word	39090
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,26,143,2,16,4,11
	.byte	'SRE',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	469
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	469
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	469
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	469
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	469
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	469
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	469
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	469
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	469
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	469
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	469
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR_Bits',0,26,161,2,3
	.word	39180
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,26,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	469
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	469
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT_Bits',0,26,172,2,3
	.word	39504
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,26,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	469
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	469
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FCX_Bits',0,26,180,2,3
	.word	39658
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,26,183,2,16,4,11
	.byte	'TST',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	469
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	469
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	469
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	469
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	469
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	469
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	469
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	469
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,26,202,2,3
	.word	39764
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,26,205,2,16,4,11
	.byte	'OPC',0,4
	.word	469
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	469
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	469
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	469
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,26,212,2,3
	.word	40113
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,26,215,2,16,4,11
	.byte	'PC',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,26,218,2,3
	.word	40273
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,26,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,26,224,2,3
	.word	40354
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,26,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,26,230,2,3
	.word	40441
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,26,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,26,236,2,3
	.word	40528
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,26,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT_Bits',0,26,243,2,3
	.word	40615
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,26,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	469
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	469
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	469
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	469
	.byte	6,0,2,35,0,0,28
	.byte	'Ifx_CPU_ICR_Bits',0,26,253,2,3
	.word	40706
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,26,128,3,16,4,11
	.byte	'ISP',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_ISP_Bits',0,26,131,3,3
	.word	40849
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,26,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	469
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	469
	.byte	12,0,2,35,0,0,28
	.byte	'Ifx_CPU_LCX_Bits',0,26,139,3,3
	.word	40915
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,26,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT_Bits',0,26,146,3,3
	.word	41021
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,26,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT_Bits',0,26,153,3,3
	.word	41114
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,26,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT_Bits',0,26,160,3,3
	.word	41207
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,26,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	469
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_CPU_PC_Bits',0,26,167,3,3
	.word	41300
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,26,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0_Bits',0,26,175,3,3
	.word	41385
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,26,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1_Bits',0,26,183,3,3
	.word	41501
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,26,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2_Bits',0,26,190,3,3
	.word	41612
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,26,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	469
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	469
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	469
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	469
	.byte	10,0,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI_Bits',0,26,200,3,3
	.word	41713
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,26,203,3,16,4,11
	.byte	'TA',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR_Bits',0,26,206,3,3
	.word	41843
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,26,209,3,16,4,11
	.byte	'IED',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	469
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	469
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	469
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	469
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	469
	.byte	18,0,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR_Bits',0,26,221,3,3
	.word	41912
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,26,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	469
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0_Bits',0,26,229,3,3
	.word	42141
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,26,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	469
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1_Bits',0,26,237,3,3
	.word	42254
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,26,240,3,16,4,11
	.byte	'PSI',0,4
	.word	469
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2_Bits',0,26,244,3,3
	.word	42367
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,26,247,3,16,4,11
	.byte	'FRE',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	469
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	469
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	469
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	17,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR_Bits',0,26,129,4,3
	.word	42458
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,26,132,4,16,4,11
	.byte	'CDC',0,4
	.word	469
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	469
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	469
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	469
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	469
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	469
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	469
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	469
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	469
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_PSW_Bits',0,26,147,4,3
	.word	42661
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,26,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	469
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	469
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	469
	.byte	1,0,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN_Bits',0,26,156,4,3
	.word	42904
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,26,159,4,16,4,11
	.byte	'PC',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	469
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	469
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	469
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	469
	.byte	7,0,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON_Bits',0,26,171,4,3
	.word	43032
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,26,174,4,16,4,11
	.byte	'EN',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,26,177,4,3
	.word	43273
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,26,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,26,183,4,3
	.word	43356
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,26,186,4,16,4,11
	.byte	'EN',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,26,189,4,3
	.word	43447
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,26,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,26,195,4,3
	.word	43538
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,26,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	446
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,26,202,4,3
	.word	43637
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,26,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	446
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,26,209,4,3
	.word	43744
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,26,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	469
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	469
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT_Bits',0,26,220,4,3
	.word	43851
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,26,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	469
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON_Bits',0,26,231,4,3
	.word	44005
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,26,234,4,16,4,11
	.byte	'ASI',0,4
	.word	469
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	469
	.byte	27,0,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,26,238,4,3
	.word	44166
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,26,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	469
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	469
	.byte	15,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON_Bits',0,26,249,4,3
	.word	44264
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,26,252,4,16,4,11
	.byte	'Timer',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,26,255,4,3
	.word	44436
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,26,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	469
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR_Bits',0,26,133,5,3
	.word	44516
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,26,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	469
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	469
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	469
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	469
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	469
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	469
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	469
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	469
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	469
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	469
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	469
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	469
	.byte	3,0,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT_Bits',0,26,153,5,3
	.word	44589
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,26,156,5,16,4,11
	.byte	'T0',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	469
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	469
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	469
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,26,167,5,3
	.word	44907
	.byte	12,26,175,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36125
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_A',0,26,180,5,3
	.word	45102
	.byte	12,26,183,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36186
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BIV',0,26,188,5,3
	.word	45161
	.byte	12,26,191,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36265
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_BTV',0,26,196,5,3
	.word	45222
	.byte	12,26,199,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36351
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCNT',0,26,204,5,3
	.word	45283
	.byte	12,26,207,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36440
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CCTRL',0,26,212,5,3
	.word	45345
	.byte	12,26,215,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36586
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_COMPAT',0,26,220,5,3
	.word	45408
	.byte	12,26,223,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36713
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CORE_ID',0,26,228,5,3
	.word	45472
	.byte	12,26,231,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36811
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_L',0,26,236,5,3
	.word	45537
	.byte	12,26,239,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36904
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPR_U',0,26,244,5,3
	.word	45600
	.byte	12,26,247,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36997
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPU_ID',0,26,252,5,3
	.word	45663
	.byte	12,26,255,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37104
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CPXE',0,26,132,6,3
	.word	45727
	.byte	12,26,135,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37191
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CREVT',0,26,140,6,3
	.word	45789
	.byte	12,26,143,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37345
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_CUS_ID',0,26,148,6,3
	.word	45852
	.byte	12,26,151,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37439
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_D',0,26,156,6,3
	.word	45916
	.byte	12,26,159,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37502
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DATR',0,26,164,6,3
	.word	45975
	.byte	12,26,167,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37720
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGSR',0,26,172,6,3
	.word	46037
	.byte	12,26,175,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37935
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DBGTCR',0,26,180,6,3
	.word	46100
	.byte	12,26,183,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38029
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON0',0,26,188,6,3
	.word	46164
	.byte	12,26,191,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38145
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCON2',0,26,196,6,3
	.word	46227
	.byte	12,26,199,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38246
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DCX',0,26,204,6,3
	.word	46290
	.byte	12,26,207,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38339
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DEADD',0,26,212,6,3
	.word	46351
	.byte	12,26,215,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38419
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIEAR',0,26,220,6,3
	.word	46414
	.byte	12,26,223,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38488
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DIETR',0,26,228,6,3
	.word	46477
	.byte	12,26,231,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38717
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DMS',0,26,236,6,3
	.word	46540
	.byte	12,26,239,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38810
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_L',0,26,244,6,3
	.word	46601
	.byte	12,26,247,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38905
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPR_U',0,26,252,6,3
	.word	46664
	.byte	12,26,255,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39000
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPRE',0,26,132,7,3
	.word	46727
	.byte	12,26,135,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39090
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DPWE',0,26,140,7,3
	.word	46789
	.byte	12,26,143,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39180
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_DSTR',0,26,148,7,3
	.word	46851
	.byte	12,26,151,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39504
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_EXEVT',0,26,156,7,3
	.word	46913
	.byte	12,26,159,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39658
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FCX',0,26,164,7,3
	.word	46976
	.byte	12,26,167,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39764
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,26,172,7,3
	.word	47037
	.byte	12,26,175,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40113
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,26,180,7,3
	.word	47107
	.byte	12,26,183,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40273
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,26,188,7,3
	.word	47177
	.byte	12,26,191,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40354
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,26,196,7,3
	.word	47246
	.byte	12,26,199,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40441
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,26,204,7,3
	.word	47317
	.byte	12,26,207,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40528
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,26,212,7,3
	.word	47388
	.byte	12,26,215,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40615
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICNT',0,26,220,7,3
	.word	47459
	.byte	12,26,223,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40706
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ICR',0,26,228,7,3
	.word	47521
	.byte	12,26,231,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40849
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_ISP',0,26,236,7,3
	.word	47582
	.byte	12,26,239,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40915
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_LCX',0,26,244,7,3
	.word	47643
	.byte	12,26,247,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41021
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M1CNT',0,26,252,7,3
	.word	47704
	.byte	12,26,255,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41114
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M2CNT',0,26,132,8,3
	.word	47767
	.byte	12,26,135,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41207
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_M3CNT',0,26,140,8,3
	.word	47830
	.byte	12,26,143,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41300
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PC',0,26,148,8,3
	.word	47893
	.byte	12,26,151,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41385
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON0',0,26,156,8,3
	.word	47953
	.byte	12,26,159,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41501
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON1',0,26,164,8,3
	.word	48016
	.byte	12,26,167,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41612
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCON2',0,26,172,8,3
	.word	48079
	.byte	12,26,175,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41713
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PCXI',0,26,180,8,3
	.word	48142
	.byte	12,26,183,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41843
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIEAR',0,26,188,8,3
	.word	48204
	.byte	12,26,191,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41912
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PIETR',0,26,196,8,3
	.word	48267
	.byte	12,26,199,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42141
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA0',0,26,204,8,3
	.word	48330
	.byte	12,26,207,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42254
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA1',0,26,212,8,3
	.word	48392
	.byte	12,26,215,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42367
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PMA2',0,26,220,8,3
	.word	48454
	.byte	12,26,223,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42458
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSTR',0,26,228,8,3
	.word	48516
	.byte	12,26,231,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42661
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_PSW',0,26,236,8,3
	.word	48578
	.byte	12,26,239,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42904
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SEGEN',0,26,244,8,3
	.word	48639
	.byte	12,26,247,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43032
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SMACON',0,26,252,8,3
	.word	48702
	.byte	12,26,255,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43273
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENA',0,26,132,9,3
	.word	48766
	.byte	12,26,135,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43356
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_ACCENB',0,26,140,9,3
	.word	48836
	.byte	12,26,143,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43447
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,26,148,9,3
	.word	48906
	.byte	12,26,151,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43538
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,26,156,9,3
	.word	48980
	.byte	12,26,159,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43637
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,26,164,9,3
	.word	49054
	.byte	12,26,167,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43744
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,26,172,9,3
	.word	49124
	.byte	12,26,175,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43851
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SWEVT',0,26,180,9,3
	.word	49194
	.byte	12,26,183,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44005
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_SYSCON',0,26,188,9,3
	.word	49257
	.byte	12,26,191,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44166
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TASK_ASI',0,26,196,9,3
	.word	49321
	.byte	12,26,199,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44264
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_CON',0,26,204,9,3
	.word	49387
	.byte	12,26,207,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44436
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TPS_TIMER',0,26,212,9,3
	.word	49452
	.byte	12,26,215,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44516
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_ADR',0,26,220,9,3
	.word	49519
	.byte	12,26,223,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44589
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TR_EVT',0,26,228,9,3
	.word	49583
	.byte	12,26,231,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44907
	.byte	4,2,35,0,0,28
	.byte	'Ifx_CPU_TRIG_ACC',0,26,236,9,3
	.word	49647
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,26,247,9,25,8,13
	.byte	'L',0
	.word	45537
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	45600
	.byte	4,2,35,4,0,14
	.word	49713
	.byte	28
	.byte	'Ifx_CPU_CPR',0,26,251,9,3
	.word	49755
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,26,254,9,25,8,13
	.byte	'L',0
	.word	46601
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	46664
	.byte	4,2,35,4,0,14
	.word	49781
	.byte	28
	.byte	'Ifx_CPU_DPR',0,26,130,10,3
	.word	49823
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,26,133,10,25,16,13
	.byte	'LA',0
	.word	49054
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	49124
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	48906
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	48980
	.byte	4,2,35,12,0,14
	.word	49849
	.byte	28
	.byte	'Ifx_CPU_SPROT_RGN',0,26,139,10,3
	.word	49931
	.byte	15,12
	.word	49452
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,26,142,10,25,16,13
	.byte	'CON',0
	.word	49387
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	49963
	.byte	12,2,35,4,0,14
	.word	49972
	.byte	28
	.byte	'Ifx_CPU_TPS',0,26,146,10,3
	.word	50020
	.byte	10
	.byte	'_Ifx_CPU_TR',0,26,149,10,25,8,13
	.byte	'EVT',0
	.word	49583
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	49519
	.byte	4,2,35,4,0,14
	.word	50046
	.byte	28
	.byte	'Ifx_CPU_TR',0,26,153,10,3
	.word	50091
	.byte	15,176,32
	.word	621
	.byte	16,175,32,0,15,208,223,1
	.word	621
	.byte	16,207,223,1,0,15,248,1
	.word	621
	.byte	16,247,1,0,15,244,29
	.word	621
	.byte	16,243,29,0,15,188,3
	.word	621
	.byte	16,187,3,0,15,232,3
	.word	621
	.byte	16,231,3,0,15,252,23
	.word	621
	.byte	16,251,23,0,15,228,63
	.word	621
	.byte	16,227,63,0,15,128,1
	.word	49781
	.byte	16,15,0,14
	.word	50206
	.byte	15,128,31
	.word	621
	.byte	16,255,30,0,15,64
	.word	49713
	.byte	16,7,0,14
	.word	50232
	.byte	15,192,31
	.word	621
	.byte	16,191,31,0,15,16
	.word	45727
	.byte	16,3,0,15,16
	.word	46727
	.byte	16,3,0,15,16
	.word	46789
	.byte	16,3,0,15,208,7
	.word	621
	.byte	16,207,7,0,14
	.word	49972
	.byte	15,240,23
	.word	621
	.byte	16,239,23,0,15,64
	.word	50046
	.byte	16,7,0,14
	.word	50311
	.byte	15,192,23
	.word	621
	.byte	16,191,23,0,15,232,1
	.word	621
	.byte	16,231,1,0,15,28
	.word	621
	.byte	16,27,0,15,180,1
	.word	621
	.byte	16,179,1,0,15,16
	.word	621
	.byte	16,15,0,15,172,1
	.word	621
	.byte	16,171,1,0,15,64
	.word	45916
	.byte	16,15,0,15,64
	.word	621
	.byte	16,63,0,15,64
	.word	45102
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,26,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	50116
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	48639
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	50127
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	49321
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	50140
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	48330
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	48392
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	48454
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	50151
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	46227
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4438
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	48702
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	46851
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2619
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	45975
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	46351
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	46414
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	46477
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3809
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	46164
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	50162
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	48516
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	48016
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	48079
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	47953
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	48204
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	48267
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	50173
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	45408
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	50184
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	47037
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	47177
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	47107
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2619
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	47246
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	47317
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	47388
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	50195
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	50216
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	50221
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	50241
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	50246
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	50257
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	50266
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	50275
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	50284
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	50295
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	50300
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	50320
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	50325
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	45345
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	45283
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	47459
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	47704
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	47767
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	47830
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	50336
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	46037
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2619
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	46913
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	45789
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	49194
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	50347
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	49647
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4778
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	46540
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	46290
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	46100
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	50356
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	48142
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	48578
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	47893
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4438
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	49257
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	45663
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	45472
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	45161
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	45222
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	47582
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	47521
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4438
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	46976
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	47643
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	50367
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	45852
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	50376
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	50387
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	50396
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	50405
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	50396
	.byte	64,4,35,192,255,3,0,14
	.word	50414
	.byte	28
	.byte	'Ifx_CPU',0,26,130,11,3
	.word	52205
	.byte	17,9,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,28
	.byte	'IfxCpu_Id',0,9,132,1,3
	.word	52227
	.byte	28
	.byte	'IfxCpu_ResourceCpu',0,9,161,1,3
	.word	10180
	.byte	28
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10733
	.byte	28
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	11023
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	52372
	.byte	28
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	52404
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	11023
	.byte	4,2,35,8,0,14
	.word	52430
	.byte	28
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	52489
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	52517
	.byte	28
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	52554
	.byte	15,64
	.word	11023
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	52582
	.byte	64,2,35,0,0,14
	.word	52591
	.byte	28
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	52623
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	11023
	.byte	4,2,35,12,0,14
	.word	52648
	.byte	28
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	52720
	.byte	15,8
	.word	11023
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	52746
	.byte	8,2,35,0,0,14
	.word	52755
	.byte	28
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	52791
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	11023
	.byte	4,2,35,12,0,14
	.word	52821
	.byte	28
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	52894
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	52920
	.byte	28
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	52955
	.byte	15,192,1
	.word	11023
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4778
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	52981
	.byte	192,1,2,35,16,0,14
	.word	52991
	.byte	28
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	53058
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	11023
	.byte	4,2,35,4,0,14
	.word	53084
	.byte	28
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	53132
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	53160
	.byte	28
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	53193
	.byte	15,40
	.word	621
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	52746
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	52746
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	52746
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	52746
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	11023
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	11023
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	53220
	.byte	40,2,35,40,0,14
	.word	53229
	.byte	28
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	53356
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	53383
	.byte	28
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	53415
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	53441
	.byte	28
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	53473
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	11023
	.byte	4,2,35,8,0,14
	.word	53499
	.byte	28
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	53559
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	11023
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	50367
	.byte	16,2,35,16,0,14
	.word	53585
	.byte	28
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	53679
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	11023
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	11023
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	11023
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3809
	.byte	24,2,35,24,0,14
	.word	53706
	.byte	28
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	53823
	.byte	15,12
	.word	11023
	.byte	16,2,0,15,32
	.word	11023
	.byte	16,7,0,15,32
	.word	53860
	.byte	16,0,0,15,88
	.word	621
	.byte	16,87,0,15,108
	.word	11023
	.byte	16,26,0,15,96
	.word	621
	.byte	16,95,0,15,96
	.word	53860
	.byte	16,2,0,15,160,3
	.word	621
	.byte	16,159,3,0,15,64
	.word	53860
	.byte	16,1,0,15,192,3
	.word	621
	.byte	16,191,3,0,15,16
	.word	11023
	.byte	16,3,0,15,64
	.word	53945
	.byte	16,3,0,15,192,2
	.word	621
	.byte	16,191,2,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	53851
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2619
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	11023
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	11023
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	52746
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4438
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	53869
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	53878
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	53887
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	53896
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	11023
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4778
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	53905
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	53914
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	53905
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	53914
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	53925
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	53934
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	53954
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	53963
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	53851
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	22819
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	53851
	.byte	12,3,35,192,18,0,14
	.word	53974
	.byte	28
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	54434
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	54460
	.byte	28
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	54493
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	11023
	.byte	4,2,35,12,0,14
	.word	54520
	.byte	28
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	54593
	.byte	15,56
	.word	621
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	11023
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	11023
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	11023
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	54620
	.byte	56,2,35,24,0,14
	.word	54629
	.byte	28
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	54752
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	54778
	.byte	28
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	54810
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	11023
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	11023
	.byte	4,2,35,16,0,14
	.word	54836
	.byte	28
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	54921
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	54947
	.byte	28
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	54979
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	53860
	.byte	32,2,35,0,0,14
	.word	55005
	.byte	28
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	55038
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	53860
	.byte	32,2,35,0,0,14
	.word	55065
	.byte	28
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	55099
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	11023
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	11023
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	11023
	.byte	4,2,35,20,0,14
	.word	55127
	.byte	28
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	55220
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	55247
	.byte	28
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	55279
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	53945
	.byte	16,2,35,4,0,14
	.word	55305
	.byte	28
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	55351
	.byte	15,24
	.word	11023
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	55377
	.byte	24,2,35,0,0,14
	.word	55386
	.byte	28
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	55419
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	53851
	.byte	12,2,35,0,0,14
	.word	55446
	.byte	28
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	55478
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	11023
	.byte	4,2,35,4,0,14
	.word	55504
	.byte	28
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	55550
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	11023
	.byte	4,2,35,12,0,14
	.word	55576
	.byte	28
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	55651
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	11023
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	11023
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	11023
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	11023
	.byte	4,2,35,12,0,14
	.word	55680
	.byte	28
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	55754
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	11023
	.byte	4,2,35,0,0,14
	.word	55782
	.byte	28
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	55816
	.byte	15,4
	.word	52372
	.byte	16,0,0,14
	.word	55843
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	55852
	.byte	4,2,35,0,0,14
	.word	55857
	.byte	28
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	55893
	.byte	15,48
	.word	52430
	.byte	16,3,0,14
	.word	55921
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	55930
	.byte	48,2,35,0,0,14
	.word	55935
	.byte	28
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	55975
	.byte	14
	.word	52517
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	56005
	.byte	4,2,35,0,0,14
	.word	56010
	.byte	28
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	56044
	.byte	15,64
	.word	52591
	.byte	16,0,0,14
	.word	56071
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	56080
	.byte	64,2,35,0,0,14
	.word	56085
	.byte	28
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	56119
	.byte	15,32
	.word	52648
	.byte	16,1,0,14
	.word	56146
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	56155
	.byte	32,2,35,0,0,14
	.word	56160
	.byte	28
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	56196
	.byte	14
	.word	52755
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	56224
	.byte	8,2,35,0,0,14
	.word	56229
	.byte	28
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	56273
	.byte	15,16
	.word	52821
	.byte	16,0,0,14
	.word	56305
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	56314
	.byte	16,2,35,0,0,14
	.word	56319
	.byte	28
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	56353
	.byte	15,8
	.word	52920
	.byte	16,1,0,14
	.word	56380
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	56389
	.byte	8,2,35,0,0,14
	.word	56394
	.byte	28
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	56428
	.byte	15,208,1
	.word	52991
	.byte	16,0,0,14
	.word	56455
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	56465
	.byte	208,1,2,35,0,0,14
	.word	56470
	.byte	28
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	56506
	.byte	14
	.word	53084
	.byte	14
	.word	53084
	.byte	14
	.word	53084
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	56533
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4438
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	56538
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	56543
	.byte	8,2,35,24,0,14
	.word	56548
	.byte	28
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	56639
	.byte	15,4
	.word	53160
	.byte	16,0,0,14
	.word	56668
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	56677
	.byte	4,2,35,0,0,14
	.word	56682
	.byte	28
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	56718
	.byte	15,80
	.word	53229
	.byte	16,0,0,14
	.word	56746
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	56755
	.byte	80,2,35,0,0,14
	.word	56760
	.byte	28
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	56796
	.byte	15,4
	.word	53383
	.byte	16,0,0,14
	.word	56824
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	56833
	.byte	4,2,35,0,0,14
	.word	56838
	.byte	28
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	56872
	.byte	15,4
	.word	53441
	.byte	16,0,0,14
	.word	56899
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	56908
	.byte	4,2,35,0,0,14
	.word	56913
	.byte	28
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	56947
	.byte	15,12
	.word	53499
	.byte	16,0,0,14
	.word	56974
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	56983
	.byte	12,2,35,0,0,14
	.word	56988
	.byte	28
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	57022
	.byte	15,64
	.word	53585
	.byte	16,1,0,14
	.word	57049
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	57058
	.byte	64,2,35,0,0,14
	.word	57063
	.byte	28
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	57099
	.byte	15,48
	.word	53706
	.byte	16,0,0,14
	.word	57127
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	57136
	.byte	48,2,35,0,0,14
	.word	57141
	.byte	28
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	57179
	.byte	15,204,18
	.word	53974
	.byte	16,0,0,14
	.word	57208
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	57218
	.byte	204,18,2,35,0,0,14
	.word	57223
	.byte	28
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	57259
	.byte	15,4
	.word	54460
	.byte	16,0,0,14
	.word	57286
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	57295
	.byte	4,2,35,0,0,14
	.word	57300
	.byte	28
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	57336
	.byte	15,64
	.word	54520
	.byte	16,3,0,14
	.word	57364
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	57373
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	11023
	.byte	4,2,35,64,0,14
	.word	57378
	.byte	28
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	57427
	.byte	15,80
	.word	54629
	.byte	16,0,0,14
	.word	57455
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	57464
	.byte	80,2,35,0,0,14
	.word	57469
	.byte	28
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	57503
	.byte	15,4
	.word	54778
	.byte	16,0,0,14
	.word	57530
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	57539
	.byte	4,2,35,0,0,14
	.word	57544
	.byte	28
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	57578
	.byte	15,40
	.word	54836
	.byte	16,1,0,14
	.word	57605
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	57614
	.byte	40,2,35,0,0,14
	.word	57619
	.byte	28
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	57653
	.byte	15,8
	.word	54947
	.byte	16,1,0,14
	.word	57680
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	57689
	.byte	8,2,35,0,0,14
	.word	57694
	.byte	28
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	57728
	.byte	15,32
	.word	55005
	.byte	16,0,0,14
	.word	57755
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	57764
	.byte	32,2,35,0,0,14
	.word	57769
	.byte	28
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	57805
	.byte	15,32
	.word	55065
	.byte	16,0,0,14
	.word	57833
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	57842
	.byte	32,2,35,0,0,14
	.word	57847
	.byte	28
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	57885
	.byte	15,96
	.word	55127
	.byte	16,3,0,14
	.word	57914
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	57923
	.byte	96,2,35,0,0,14
	.word	57928
	.byte	28
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	57964
	.byte	15,4
	.word	55247
	.byte	16,0,0,14
	.word	57992
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	58001
	.byte	4,2,35,0,0,14
	.word	58006
	.byte	28
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	58040
	.byte	14
	.word	55305
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	58067
	.byte	20,2,35,0,0,14
	.word	58072
	.byte	28
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	58106
	.byte	15,24
	.word	55386
	.byte	16,0,0,14
	.word	58133
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	58142
	.byte	24,2,35,0,0,14
	.word	58147
	.byte	28
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	58183
	.byte	15,12
	.word	55446
	.byte	16,0,0,14
	.word	58211
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	58220
	.byte	12,2,35,0,0,14
	.word	58225
	.byte	28
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	58259
	.byte	15,16
	.word	55504
	.byte	16,1,0,14
	.word	58286
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	58295
	.byte	16,2,35,0,0,14
	.word	58300
	.byte	28
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	58334
	.byte	15,64
	.word	55680
	.byte	16,3,0,14
	.word	58361
	.byte	15,224,1
	.word	621
	.byte	16,223,1,0,15,32
	.word	55576
	.byte	16,1,0,14
	.word	58386
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	58370
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	58375
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	58395
	.byte	32,3,35,160,2,0,14
	.word	58400
	.byte	28
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	58469
	.byte	14
	.word	55782
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	58497
	.byte	4,2,35,0,0,14
	.word	58502
	.byte	28
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	58538
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	58566
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	59123
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	446
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	59200
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	621
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	621
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	621
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	621
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	621
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	621
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	59336
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	621
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	621
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	621
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	621
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	59616
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	59854
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	621
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	621
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	59982
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	621
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	621
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	60225
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	60460
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	60588
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	60688
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	621
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	621
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	60788
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	446
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	60996
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	638
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	638
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	621
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	61161
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	638
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	61344
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	621
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	621
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	446
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	621
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	621
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	61498
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	61862
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	638
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	621
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	621
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	621
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	62073
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	638
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	446
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	62325
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	62443
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	62554
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	62717
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	62880
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	63038
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	621
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	621
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	621
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	621
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	621
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	638
	.byte	10,0,2,35,2,0,28
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	63203
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	638
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	621
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	621
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	638
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	63532
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	63753
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	63916
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	64188
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	64341
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	64497
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	64659
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	64802
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	64967
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	65112
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	621
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	65293
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	65467
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	446
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	65627
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	446
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	65771
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	66045
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	66184
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	621
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	638
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	621
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	66347
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	638
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	638
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	621
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	66565
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	66728
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	67064
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	621
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	67171
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	67623
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	621
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	67722
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	638
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	67872
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	446
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	68021
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	446
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	68182
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	638
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	68312
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	68444
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	621
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	638
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	68559
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	638
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	638
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	68670
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	621
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	621
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	621
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	621
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	68828
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	69240
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	638
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	621
	.byte	6,0,2,35,3,0,28
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	69341
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	446
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	69608
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	69744
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	621
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	69855
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	69988
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	638
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	621
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	70191
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	621
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	621
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	621
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	638
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	70547
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	70725
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	638
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	621
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	621
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	70825
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	621
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	621
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	638
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	71195
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	71381
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	71579
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	621
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	446
	.byte	21,0,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	71812
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	621
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	621
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	621
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	621
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	71964
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	621
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	621
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	621
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	72531
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	72825
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	621
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	621
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	638
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	73103
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	638
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	73599
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	638
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	73912
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	621
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	621
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	621
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	74121
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	621
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	621
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	3,0,2,35,3,0,28
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	74332
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	74764
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	621
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	621
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	621
	.byte	7,0,2,35,3,0,28
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	74860
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	75120
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	621
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	446
	.byte	23,0,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	75245
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	75442
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	75595
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	75748
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	75901
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	485
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	660
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	904
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	469
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	469
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	469
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	469
	.byte	16,0,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	76156
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	621
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	76282
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	621
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	76534
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58566
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	76753
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59123
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	76817
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59200
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	76881
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59336
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	76946
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59616
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	77011
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59854
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	77076
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59982
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	77141
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60225
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	77206
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60460
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	77271
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60588
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	77336
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60688
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	77401
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60788
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	77466
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60996
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	77530
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61161
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	77594
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61344
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	77658
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61498
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	77723
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61862
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	77785
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62073
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	77847
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62325
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	77909
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62443
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	77973
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62554
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	78038
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62717
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	78104
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62880
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	78170
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63038
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	78238
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63203
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	78305
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63532
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	78373
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63753
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	78441
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63916
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	78507
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64188
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	78574
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64341
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	78643
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64497
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	78712
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64659
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	78781
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64802
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	78850
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64967
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	78919
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65112
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	78988
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65293
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	79056
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65467
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	79124
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65627
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	79192
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65771
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	79260
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66045
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	79325
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66184
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	79390
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66347
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	79456
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66565
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	79520
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66728
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	79581
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67064
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	79642
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67171
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	79702
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67623
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	79764
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67722
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	79824
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67872
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	79886
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68021
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	79954
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68182
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	80022
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68312
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	80090
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68444
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	80154
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68559
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	80219
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68670
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	80282
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68828
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	80343
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69240
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	80407
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69341
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	80468
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69608
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	80532
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69744
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	80599
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69855
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	80662
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69988
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	80723
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70191
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	80785
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70547
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	80850
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70725
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	80915
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70825
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	80980
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71195
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	81049
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71381
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	81118
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71579
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	81187
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71812
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	81252
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71964
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	81315
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72531
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	81380
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72825
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	81445
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73103
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	81510
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73599
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	81576
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74121
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	81645
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73912
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	81709
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74332
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	81774
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74764
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	81839
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74860
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	81904
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75120
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	81968
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75245
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	82034
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75442
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	82098
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75595
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	82163
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75748
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	82228
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75901
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	82293
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	581
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	864
	.byte	28
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1095
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76156
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	82444
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76282
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	82511
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76534
	.byte	4,2,35,0,0,28
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	82578
	.byte	14
	.word	1135
	.byte	28
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	82643
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	82444
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	82511
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	82578
	.byte	4,2,35,8,0,14
	.word	82672
	.byte	28
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	82733
	.byte	15,8
	.word	77909
	.byte	16,1,0,15,20
	.word	621
	.byte	16,19,0,15,8
	.word	81252
	.byte	16,1,0,14
	.word	82672
	.byte	15,24
	.word	1135
	.byte	16,1,0,14
	.word	82792
	.byte	15,16
	.word	77723
	.byte	16,3,0,15,16
	.word	79702
	.byte	16,3,0,15,180,3
	.word	621
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4438
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	79642
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2619
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	80343
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	81187
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	80785
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	80850
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	80915
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	81118
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	80980
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	81049
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	76946
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	77011
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	79520
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	79456
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	77076
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	77141
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	77206
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	77271
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	81774
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2619
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	81645
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	76881
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	81968
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	81709
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2619
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	78507
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	82760
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	77973
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	82034
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	77336
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	77401
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	82769
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	80662
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	79824
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	80407
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	80282
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	79764
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	79260
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	78238
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	78038
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	78104
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	81904
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2619
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	81315
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	81510
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	81576
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	82778
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2619
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	77658
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	77530
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	81380
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	81445
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	82787
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	77847
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	82801
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4778
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	82293
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	82228
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	82098
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	82163
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2619
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	80090
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	80154
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	77466
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	80219
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4438
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	81839
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	50367
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	79886
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	79954
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	80022
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	50347
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	80599
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4438
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	79325
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	78170
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	79390
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	78441
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	78305
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2619
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	78988
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	79056
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	79124
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	79192
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	78574
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	78643
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	78712
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	78781
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	78850
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	78919
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	78373
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2619
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	80532
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	80468
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	53220
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	82806
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	77785
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	79581
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	80723
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	82815
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2619
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	77594
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	82824
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	76817
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	76753
	.byte	4,3,35,252,7,0,14
	.word	82835
	.byte	28
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	84825
	.byte	28
	.byte	'Ifx_STM_ACCEN0_Bits',0,13,79,3
	.word	13551
	.byte	28
	.byte	'Ifx_STM_ACCEN1_Bits',0,13,85,3
	.word	13462
	.byte	28
	.byte	'Ifx_STM_CAP_Bits',0,13,91,3
	.word	11992
	.byte	28
	.byte	'Ifx_STM_CAPSV_Bits',0,13,97,3
	.word	12869
	.byte	28
	.byte	'Ifx_STM_CLC_Bits',0,13,107,3
	.word	11115
	.byte	28
	.byte	'Ifx_STM_CMCON_Bits',0,13,120,3
	.word	12170
	.byte	28
	.byte	'Ifx_STM_CMP_Bits',0,13,126,3
	.word	12079
	.byte	28
	.byte	'Ifx_STM_ICR_Bits',0,13,139,1,3
	.word	12401
	.byte	28
	.byte	'Ifx_STM_ID_Bits',0,13,147,1,3
	.word	11271
	.byte	28
	.byte	'Ifx_STM_ISCR_Bits',0,13,157,1,3
	.word	12618
	.byte	28
	.byte	'Ifx_STM_KRST0_Bits',0,13,165,1,3
	.word	13339
	.byte	28
	.byte	'Ifx_STM_KRST1_Bits',0,13,172,1,3
	.word	13235
	.byte	28
	.byte	'Ifx_STM_KRSTCLR_Bits',0,13,179,1,3
	.word	13129
	.byte	28
	.byte	'Ifx_STM_OCS_Bits',0,13,189,1,3
	.word	12969
	.byte	28
	.byte	'Ifx_STM_TIM0_Bits',0,13,195,1,3
	.word	11393
	.byte	28
	.byte	'Ifx_STM_TIM0SV_Bits',0,13,201,1,3
	.word	12782
	.byte	28
	.byte	'Ifx_STM_TIM1_Bits',0,13,207,1,3
	.word	11478
	.byte	28
	.byte	'Ifx_STM_TIM2_Bits',0,13,213,1,3
	.word	11563
	.byte	28
	.byte	'Ifx_STM_TIM3_Bits',0,13,219,1,3
	.word	11648
	.byte	28
	.byte	'Ifx_STM_TIM4_Bits',0,13,225,1,3
	.word	11734
	.byte	28
	.byte	'Ifx_STM_TIM5_Bits',0,13,231,1,3
	.word	11820
	.byte	28
	.byte	'Ifx_STM_TIM6_Bits',0,13,237,1,3
	.word	11906
	.byte	28
	.byte	'Ifx_STM_ACCEN0',0,13,250,1,3
	.word	14080
	.byte	28
	.byte	'Ifx_STM_ACCEN1',0,13,130,2,3
	.word	13511
	.byte	28
	.byte	'Ifx_STM_CAP',0,13,138,2,3
	.word	12039
	.byte	28
	.byte	'Ifx_STM_CAPSV',0,13,146,2,3
	.word	12918
	.byte	28
	.byte	'Ifx_STM_CLC',0,13,154,2,3
	.word	11231
	.byte	28
	.byte	'Ifx_STM_CMCON',0,13,162,2,3
	.word	12361
	.byte	28
	.byte	'Ifx_STM_CMP',0,13,170,2,3
	.word	12121
	.byte	28
	.byte	'Ifx_STM_ICR',0,13,178,2,3
	.word	12578
	.byte	28
	.byte	'Ifx_STM_ID',0,13,186,2,3
	.word	11353
	.byte	28
	.byte	'Ifx_STM_ISCR',0,13,194,2,3
	.word	12742
	.byte	28
	.byte	'Ifx_STM_KRST0',0,13,202,2,3
	.word	13422
	.byte	28
	.byte	'Ifx_STM_KRST1',0,13,210,2,3
	.word	13299
	.byte	28
	.byte	'Ifx_STM_KRSTCLR',0,13,218,2,3
	.word	13195
	.byte	28
	.byte	'Ifx_STM_OCS',0,13,226,2,3
	.word	13089
	.byte	28
	.byte	'Ifx_STM_TIM0',0,13,234,2,3
	.word	11438
	.byte	28
	.byte	'Ifx_STM_TIM0SV',0,13,242,2,3
	.word	12829
	.byte	28
	.byte	'Ifx_STM_TIM1',0,13,250,2,3
	.word	11523
	.byte	28
	.byte	'Ifx_STM_TIM2',0,13,130,3,3
	.word	11608
	.byte	28
	.byte	'Ifx_STM_TIM3',0,13,138,3,3
	.word	11694
	.byte	28
	.byte	'Ifx_STM_TIM4',0,13,146,3,3
	.word	11780
	.byte	28
	.byte	'Ifx_STM_TIM5',0,13,154,3,3
	.word	11866
	.byte	28
	.byte	'Ifx_STM_TIM6',0,13,162,3,3
	.word	11952
	.byte	14
	.word	14120
	.byte	28
	.byte	'Ifx_STM',0,13,201,3,3
	.word	85930
	.byte	17,27,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,28
	.byte	'IfxScu_CCUCON0_CLKSEL',0,27,240,10,3
	.word	85952
	.byte	17,27,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,28
	.byte	'IfxScu_WDTCON1_IR',0,27,255,10,3
	.word	86049
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,28,45,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,28,79,3
	.word	86171
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,28,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,28,85,3
	.word	86732
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,28,88,16,4,11
	.byte	'SEL',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	446
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,28,95,3
	.word	86813
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,28,98,16,4,11
	.byte	'VLD0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	446
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,28,111,3
	.word	86966
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,28,114,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	446
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	621
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,28,121,3
	.word	87214
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,28,124,16,4,11
	.byte	'STATUS',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0_Bits',0,28,128,1,3
	.word	87360
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,28,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM1_Bits',0,28,136,1,3
	.word	87458
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,28,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_COMM2_Bits',0,28,144,1,3
	.word	87574
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,28,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	446
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	638
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRD_Bits',0,28,153,1,3
	.word	87690
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,28,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	446
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	638
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCRP_Bits',0,28,162,1,3
	.word	87830
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,28,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	446
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	638
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_ECCW_Bits',0,28,171,1,3
	.word	87970
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,28,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	621
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	638
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	621
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	621
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FCON_Bits',0,28,193,1,3
	.word	88109
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,28,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	621
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FPRO_Bits',0,28,218,1,3
	.word	88471
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,28,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	638
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_FSR_Bits',0,28,254,1,3
	.word	88912
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,28,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_ID_Bits',0,28,134,2,3
	.word	89518
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,28,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	638
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARD_Bits',0,28,147,2,3
	.word	89629
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,28,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	638
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_MARP_Bits',0,28,159,2,3
	.word	89843
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,28,162,2,16,4,11
	.byte	'L',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	621
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	621
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	638
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	621
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCOND_Bits',0,28,179,2,3
	.word	90030
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,28,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	621
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,28,188,2,3
	.word	90354
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,28,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	638
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,28,199,2,3
	.word	90497
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,28,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	638
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	621
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	621
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	638
	.byte	14,0,2,35,2,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,28,219,2,3
	.word	90686
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,28,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	621
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,28,254,2,3
	.word	91049
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,28,129,3,16,4,11
	.byte	'S0L',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONP_Bits',0,28,160,3,3
	.word	91644
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,28,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,28,194,3,3
	.word	92168
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,28,197,3,16,4,11
	.byte	'TAG',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,28,201,3,3
	.word	92750
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,28,204,3,16,4,11
	.byte	'TAG',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,28,208,3,3
	.word	92852
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,28,211,3,16,4,11
	.byte	'TAG',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	446
	.byte	26,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,28,215,3,3
	.word	92954
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,28,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	446
	.byte	29,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD_Bits',0,28,222,3,3
	.word	93056
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,28,225,3,16,4,11
	.byte	'STRT',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	621
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	621
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_FLASH_RRCT_Bits',0,28,236,3,3
	.word	93150
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,28,239,3,16,4,11
	.byte	'DATA',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0_Bits',0,28,242,3,3
	.word	93360
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,28,245,3,16,4,11
	.byte	'DATA',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1_Bits',0,28,248,3,3
	.word	93433
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,28,251,3,16,4,11
	.byte	'SEL',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	621
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	446
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,28,130,4,3
	.word	93506
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,28,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,28,137,4,3
	.word	93661
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,28,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	621
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	446
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	621
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,28,147,4,3
	.word	93766
	.byte	12,28,155,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86171
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN0',0,28,160,4,3
	.word	93914
	.byte	12,28,163,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86732
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ACCEN1',0,28,168,4,3
	.word	93980
	.byte	12,28,171,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86813
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_CFG',0,28,176,4,3
	.word	94046
	.byte	12,28,179,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86966
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_STAT',0,28,184,4,3
	.word	94114
	.byte	12,28,187,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87214
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_CBAB_TOP',0,28,192,4,3
	.word	94183
	.byte	12,28,195,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87360
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM0',0,28,200,4,3
	.word	94251
	.byte	12,28,203,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87458
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM1',0,28,208,4,3
	.word	94316
	.byte	12,28,211,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87574
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_COMM2',0,28,216,4,3
	.word	94381
	.byte	12,28,219,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87690
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRD',0,28,224,4,3
	.word	94446
	.byte	12,28,227,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87830
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCRP',0,28,232,4,3
	.word	94511
	.byte	12,28,235,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87970
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ECCW',0,28,240,4,3
	.word	94576
	.byte	12,28,243,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88109
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FCON',0,28,248,4,3
	.word	94640
	.byte	12,28,251,4,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88471
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FPRO',0,28,128,5,3
	.word	94704
	.byte	12,28,131,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88912
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_FSR',0,28,136,5,3
	.word	94768
	.byte	12,28,139,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89518
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_ID',0,28,144,5,3
	.word	94831
	.byte	12,28,147,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89629
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARD',0,28,152,5,3
	.word	94893
	.byte	12,28,155,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89843
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_MARP',0,28,160,5,3
	.word	94957
	.byte	12,28,163,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90030
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCOND',0,28,168,5,3
	.word	95021
	.byte	12,28,171,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90354
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONDBG',0,28,176,5,3
	.word	95088
	.byte	12,28,179,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90497
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSM',0,28,184,5,3
	.word	95157
	.byte	12,28,187,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90686
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,28,192,5,3
	.word	95226
	.byte	12,28,195,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91049
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONOTP',0,28,200,5,3
	.word	95299
	.byte	12,28,203,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91644
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONP',0,28,208,5,3
	.word	95368
	.byte	12,28,211,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92168
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_PROCONWOP',0,28,216,5,3
	.word	95435
	.byte	12,28,219,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92750
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG0',0,28,224,5,3
	.word	95504
	.byte	12,28,227,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92852
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG1',0,28,232,5,3
	.word	95572
	.byte	12,28,235,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92954
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RDB_CFG2',0,28,240,5,3
	.word	95640
	.byte	12,28,243,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93056
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRAD',0,28,248,5,3
	.word	95708
	.byte	12,28,251,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93150
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRCT',0,28,128,6,3
	.word	95772
	.byte	12,28,131,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93360
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD0',0,28,136,6,3
	.word	95836
	.byte	12,28,139,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93433
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_RRD1',0,28,144,6,3
	.word	95900
	.byte	12,28,147,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93506
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_CFG',0,28,152,6,3
	.word	95964
	.byte	12,28,155,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93661
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_STAT',0,28,160,6,3
	.word	96032
	.byte	12,28,163,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93766
	.byte	4,2,35,0,0,28
	.byte	'Ifx_FLASH_UBAB_TOP',0,28,168,6,3
	.word	96101
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,28,179,6,25,12,13
	.byte	'CFG',0
	.word	94046
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	94114
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	94183
	.byte	4,2,35,8,0,14
	.word	96169
	.byte	28
	.byte	'Ifx_FLASH_CBAB',0,28,184,6,3
	.word	96232
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,28,187,6,25,12,13
	.byte	'CFG0',0
	.word	95504
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	95572
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	95640
	.byte	4,2,35,8,0,14
	.word	96261
	.byte	28
	.byte	'Ifx_FLASH_RDB',0,28,192,6,3
	.word	96325
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,28,195,6,25,12,13
	.byte	'CFG',0
	.word	95964
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	96032
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	96101
	.byte	4,2,35,8,0,14
	.word	96353
	.byte	28
	.byte	'Ifx_FLASH_UBAB',0,28,200,6,3
	.word	96416
	.byte	28
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8191
	.byte	28
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	8104
	.byte	28
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4447
	.byte	28
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2500
	.byte	28
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3495
	.byte	28
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2628
	.byte	28
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3275
	.byte	28
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2843
	.byte	28
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	3058
	.byte	28
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7463
	.byte	28
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7587
	.byte	28
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7671
	.byte	28
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7851
	.byte	28
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	6102
	.byte	28
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6626
	.byte	28
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6276
	.byte	28
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6450
	.byte	28
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	7115
	.byte	28
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1929
	.byte	28
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5439
	.byte	28
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5927
	.byte	28
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5586
	.byte	28
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5755
	.byte	28
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6782
	.byte	28
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1613
	.byte	28
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5153
	.byte	28
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4787
	.byte	28
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3818
	.byte	28
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	4122
	.byte	28
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8718
	.byte	28
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8151
	.byte	28
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4738
	.byte	28
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2579
	.byte	28
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3769
	.byte	28
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2803
	.byte	28
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3455
	.byte	28
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	3018
	.byte	28
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3235
	.byte	28
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7547
	.byte	28
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7796
	.byte	28
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	8055
	.byte	28
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7423
	.byte	28
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6236
	.byte	28
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6742
	.byte	28
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6410
	.byte	28
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6586
	.byte	28
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2460
	.byte	28
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	7075
	.byte	28
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5546
	.byte	28
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	6062
	.byte	28
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5715
	.byte	28
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5887
	.byte	28
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1889
	.byte	28
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5399
	.byte	28
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	5113
	.byte	28
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	4082
	.byte	28
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4398
	.byte	14
	.word	8758
	.byte	28
	.byte	'Ifx_P',0,6,139,6,3
	.word	97763
	.byte	28
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9371
	.byte	28
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	9646
	.byte	28
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	9576
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,28
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	97864
	.byte	28
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9959
	.byte	20,5,190,1,9,8,13
	.byte	'port',0
	.word	9366
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	621
	.byte	1,2,35,4,0,28
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	98329
	.byte	14
	.word	82835
	.byte	3
	.word	98389
	.byte	20,29,74,15,20,13
	.byte	'module',0
	.word	98394
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	621
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,16,0,30
	.word	98399
	.byte	28
	.byte	'IfxScu_Req_In',0,29,80,3
	.word	98469
	.byte	28
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,7,148,1,16
	.word	185
	.byte	20,7,212,5,9,8,13
	.byte	'value',0
	.word	10502
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10502
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_CcuconRegConfig',0,7,216,5,3
	.word	98536
	.byte	20,7,221,5,9,8,13
	.byte	'pDivider',0
	.word	621
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	621
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	621
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	242
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_InitialStepConfig',0,7,227,5,3
	.word	98607
	.byte	20,7,231,5,9,12,13
	.byte	'k2Step',0
	.word	621
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	242
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	98496
	.byte	4,2,35,8,0,28
	.byte	'IfxScuCcu_PllStepsConfig',0,7,236,5,3
	.word	98724
	.byte	3
	.word	182
	.byte	20,7,244,5,9,48,13
	.byte	'ccucon0',0
	.word	98536
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	98536
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	98536
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	98536
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	98536
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	98536
	.byte	8,2,35,40,0,28
	.byte	'IfxScuCcu_ClockDistributionConfig',0,7,252,5,3
	.word	98826
	.byte	20,7,128,6,9,8,13
	.byte	'value',0
	.word	10502
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10502
	.byte	4,2,35,4,0,28
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,7,132,6,3
	.word	98978
	.byte	3
	.word	98724
	.byte	20,7,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	621
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	99054
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	98607
	.byte	8,2,35,8,0,28
	.byte	'IfxScuCcu_SysPllConfig',0,7,142,6,3
	.word	99059
	.byte	17,8,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,28
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	99176
	.byte	20,8,160,1,9,6,13
	.byte	'counter',0
	.word	10502
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	621
	.byte	1,2,35,4,0,28
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	99265
	.byte	20,8,172,1,9,32,13
	.byte	'instruction',0
	.word	99265
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	99265
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	99265
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	99265
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	99265
	.byte	6,2,35,24,0,28
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	99331
	.byte	17,30,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,28
	.byte	'IfxSrc_Tos',0,30,74,3
	.word	99449
	.byte	17,12,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,28
	.byte	'IfxStm_Comparator',0,12,155,1,3
	.word	99527
	.byte	17,12,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,28
	.byte	'IfxStm_ComparatorInterrupt',0,12,163,1,3
	.word	99605
	.byte	17,12,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,28
	.byte	'IfxStm_ComparatorOffset',0,12,201,1,3
	.word	99714
	.byte	17,12,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,28
	.byte	'IfxStm_ComparatorSize',0,12,239,1,3
	.word	100672
	.byte	17,12,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,28
	.byte	'IfxStm_SleepMode',0,12,248,1,3
	.word	101692
	.byte	17,12,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,28
	.byte	'IfxStm_SuspendMode',0,12,129,2,3
	.word	101778
	.byte	28
	.byte	'Ifx_CCU6_ACCEN0_Bits',0,16,79,3
	.word	23461
	.byte	28
	.byte	'Ifx_CCU6_ACCEN1_Bits',0,16,85,3
	.word	23371
	.byte	28
	.byte	'Ifx_CCU6_CC60R_Bits',0,16,92,3
	.word	16511
	.byte	28
	.byte	'Ifx_CCU6_CC60SR_Bits',0,16,99,3
	.word	16826
	.byte	28
	.byte	'Ifx_CCU6_CC61R_Bits',0,16,106,3
	.word	16616
	.byte	28
	.byte	'Ifx_CCU6_CC61SR_Bits',0,16,113,3
	.word	16932
	.byte	28
	.byte	'Ifx_CCU6_CC62R_Bits',0,16,120,3
	.word	16721
	.byte	28
	.byte	'Ifx_CCU6_CC62SR_Bits',0,16,127,3
	.word	17038
	.byte	28
	.byte	'Ifx_CCU6_CC63R_Bits',0,16,134,1,3
	.word	17358
	.byte	28
	.byte	'Ifx_CCU6_CC63SR_Bits',0,16,141,1,3
	.word	17464
	.byte	28
	.byte	'Ifx_CCU6_CLC_Bits',0,16,151,1,3
	.word	14932
	.byte	28
	.byte	'Ifx_CCU6_CMPMODIF_Bits',0,16,168,1,3
	.word	17966
	.byte	28
	.byte	'Ifx_CCU6_CMPSTAT_Bits',0,16,190,1,3
	.word	17571
	.byte	28
	.byte	'Ifx_CCU6_ID_Bits',0,16,198,1,3
	.word	15224
	.byte	28
	.byte	'Ifx_CCU6_IEN_Bits',0,16,220,1,3
	.word	22430
	.byte	28
	.byte	'Ifx_CCU6_IMON_Bits',0,16,236,1,3
	.word	20496
	.byte	28
	.byte	'Ifx_CCU6_INP_Bits',0,16,249,1,3
	.word	22212
	.byte	28
	.byte	'Ifx_CCU6_IS_Bits',0,16,143,2,3
	.word	21114
	.byte	28
	.byte	'Ifx_CCU6_ISR_Bits',0,16,165,2,3
	.word	21838
	.byte	28
	.byte	'Ifx_CCU6_ISS_Bits',0,16,187,2,3
	.word	21471
	.byte	28
	.byte	'Ifx_CCU6_KRST0_Bits',0,16,195,2,3
	.word	23247
	.byte	28
	.byte	'Ifx_CCU6_KRST1_Bits',0,16,202,2,3
	.word	23142
	.byte	28
	.byte	'Ifx_CCU6_KRSTCLR_Bits',0,16,209,2,3
	.word	23035
	.byte	28
	.byte	'Ifx_CCU6_KSCSR_Bits',0,16,219,2,3
	.word	15921
	.byte	28
	.byte	'Ifx_CCU6_LI_Bits',0,16,238,2,3
	.word	20769
	.byte	28
	.byte	'Ifx_CCU6_MCFG_Bits',0,16,247,2,3
	.word	15090
	.byte	28
	.byte	'Ifx_CCU6_MCMCTR_Bits',0,16,132,3,3
	.word	20272
	.byte	28
	.byte	'Ifx_CCU6_MCMOUT_Bits',0,16,143,3,3
	.word	20097
	.byte	28
	.byte	'Ifx_CCU6_MCMOUTS_Bits',0,16,156,3,3
	.word	19873
	.byte	28
	.byte	'Ifx_CCU6_MODCTR_Bits',0,16,168,3,3
	.word	19299
	.byte	28
	.byte	'Ifx_CCU6_MOSEL_Bits',0,16,177,3,3
	.word	15351
	.byte	28
	.byte	'Ifx_CCU6_OCS_Bits',0,16,190,3,3
	.word	22828
	.byte	28
	.byte	'Ifx_CCU6_PISEL0_Bits',0,16,204,3,3
	.word	15501
	.byte	28
	.byte	'Ifx_CCU6_PISEL2_Bits',0,16,215,3,3
	.word	15737
	.byte	28
	.byte	'Ifx_CCU6_PSLR_Bits',0,16,224,3,3
	.word	19730
	.byte	28
	.byte	'Ifx_CCU6_T12_Bits',0,16,231,3,3
	.word	16071
	.byte	28
	.byte	'Ifx_CCU6_T12DTC_Bits',0,16,245,3,3
	.word	16285
	.byte	28
	.byte	'Ifx_CCU6_T12MSEL_Bits',0,16,128,4,3
	.word	18271
	.byte	28
	.byte	'Ifx_CCU6_T12PR_Bits',0,16,135,4,3
	.word	16177
	.byte	28
	.byte	'Ifx_CCU6_T13_Bits',0,16,142,4,3
	.word	17144
	.byte	28
	.byte	'Ifx_CCU6_T13PR_Bits',0,16,149,4,3
	.word	17250
	.byte	28
	.byte	'Ifx_CCU6_TCTR0_Bits',0,16,165,4,3
	.word	18451
	.byte	28
	.byte	'Ifx_CCU6_TCTR2_Bits',0,16,178,4,3
	.word	18711
	.byte	28
	.byte	'Ifx_CCU6_TCTR4_Bits',0,16,199,4,3
	.word	18934
	.byte	28
	.byte	'Ifx_CCU6_TRPCTR_Bits',0,16,212,4,3
	.word	19511
	.byte	28
	.byte	'Ifx_CCU6_ACCEN0',0,16,225,4,3
	.word	23991
	.byte	28
	.byte	'Ifx_CCU6_ACCEN1',0,16,233,4,3
	.word	23421
	.byte	28
	.byte	'Ifx_CCU6_CC60R',0,16,241,4,3
	.word	16576
	.byte	28
	.byte	'Ifx_CCU6_CC60SR',0,16,249,4,3
	.word	16892
	.byte	28
	.byte	'Ifx_CCU6_CC61R',0,16,129,5,3
	.word	16681
	.byte	28
	.byte	'Ifx_CCU6_CC61SR',0,16,137,5,3
	.word	16998
	.byte	28
	.byte	'Ifx_CCU6_CC62R',0,16,145,5,3
	.word	16786
	.byte	28
	.byte	'Ifx_CCU6_CC62SR',0,16,153,5,3
	.word	17104
	.byte	28
	.byte	'Ifx_CCU6_CC63R',0,16,161,5,3
	.word	17424
	.byte	28
	.byte	'Ifx_CCU6_CC63SR',0,16,169,5,3
	.word	17531
	.byte	28
	.byte	'Ifx_CCU6_CLC',0,16,177,5,3
	.word	15050
	.byte	28
	.byte	'Ifx_CCU6_CMPMODIF',0,16,185,5,3
	.word	18231
	.byte	28
	.byte	'Ifx_CCU6_CMPSTAT',0,16,193,5,3
	.word	17926
	.byte	28
	.byte	'Ifx_CCU6_ID',0,16,201,5,3
	.word	15311
	.byte	28
	.byte	'Ifx_CCU6_IEN',0,16,209,5,3
	.word	22779
	.byte	28
	.byte	'Ifx_CCU6_IMON',0,16,217,5,3
	.word	20729
	.byte	28
	.byte	'Ifx_CCU6_INP',0,16,225,5,3
	.word	22390
	.byte	28
	.byte	'Ifx_CCU6_IS',0,16,233,5,3
	.word	21431
	.byte	28
	.byte	'Ifx_CCU6_ISR',0,16,241,5,3
	.word	22172
	.byte	28
	.byte	'Ifx_CCU6_ISS',0,16,249,5,3
	.word	21798
	.byte	28
	.byte	'Ifx_CCU6_KRST0',0,16,129,6,3
	.word	23331
	.byte	28
	.byte	'Ifx_CCU6_KRST1',0,16,137,6,3
	.word	23207
	.byte	28
	.byte	'Ifx_CCU6_KRSTCLR',0,16,145,6,3
	.word	23102
	.byte	28
	.byte	'Ifx_CCU6_KSCSR',0,16,153,6,3
	.word	16031
	.byte	28
	.byte	'Ifx_CCU6_LI',0,16,161,6,3
	.word	21074
	.byte	28
	.byte	'Ifx_CCU6_MCFG',0,16,169,6,3
	.word	15184
	.byte	28
	.byte	'Ifx_CCU6_MCMCTR',0,16,177,6,3
	.word	20456
	.byte	28
	.byte	'Ifx_CCU6_MCMOUT',0,16,185,6,3
	.word	20232
	.byte	28
	.byte	'Ifx_CCU6_MCMOUTS',0,16,193,6,3
	.word	20057
	.byte	28
	.byte	'Ifx_CCU6_MODCTR',0,16,201,6,3
	.word	19471
	.byte	28
	.byte	'Ifx_CCU6_MOSEL',0,16,209,6,3
	.word	15461
	.byte	28
	.byte	'Ifx_CCU6_OCS',0,16,217,6,3
	.word	22995
	.byte	28
	.byte	'Ifx_CCU6_PISEL0',0,16,225,6,3
	.word	15697
	.byte	28
	.byte	'Ifx_CCU6_PISEL2',0,16,233,6,3
	.word	15881
	.byte	28
	.byte	'Ifx_CCU6_PSLR',0,16,241,6,3
	.word	19833
	.byte	28
	.byte	'Ifx_CCU6_T12',0,16,249,6,3
	.word	16137
	.byte	28
	.byte	'Ifx_CCU6_T12DTC',0,16,129,7,3
	.word	16471
	.byte	28
	.byte	'Ifx_CCU6_T12MSEL',0,16,137,7,3
	.word	18411
	.byte	28
	.byte	'Ifx_CCU6_T12PR',0,16,145,7,3
	.word	16245
	.byte	28
	.byte	'Ifx_CCU6_T13',0,16,153,7,3
	.word	17210
	.byte	28
	.byte	'Ifx_CCU6_T13PR',0,16,161,7,3
	.word	17318
	.byte	28
	.byte	'Ifx_CCU6_TCTR0',0,16,169,7,3
	.word	18671
	.byte	28
	.byte	'Ifx_CCU6_TCTR2',0,16,177,7,3
	.word	18894
	.byte	28
	.byte	'Ifx_CCU6_TCTR4',0,16,185,7,3
	.word	19259
	.byte	28
	.byte	'Ifx_CCU6_TRPCTR',0,16,193,7,3
	.word	19690
	.byte	14
	.word	24031
	.byte	28
	.byte	'Ifx_CCU6',0,16,130,8,3
	.word	104260
	.byte	20,31,59,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104283
	.byte	28
	.byte	'IfxCcu6_Cc60in_In',0,31,64,3
	.word	104334
	.byte	20,31,67,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104365
	.byte	28
	.byte	'IfxCcu6_Cc61in_In',0,31,72,3
	.word	104416
	.byte	20,31,75,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104447
	.byte	28
	.byte	'IfxCcu6_Cc62in_In',0,31,80,3
	.word	104498
	.byte	20,31,83,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104529
	.byte	28
	.byte	'IfxCcu6_Ccpos0_In',0,31,88,3
	.word	104580
	.byte	20,31,91,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104611
	.byte	28
	.byte	'IfxCcu6_Ccpos1_In',0,31,96,3
	.word	104662
	.byte	20,31,99,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104693
	.byte	28
	.byte	'IfxCcu6_Ccpos2_In',0,31,104,3
	.word	104744
	.byte	20,31,107,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104775
	.byte	28
	.byte	'IfxCcu6_Ctrap_In',0,31,112,3
	.word	104826
	.byte	20,31,115,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104856
	.byte	28
	.byte	'IfxCcu6_T12hr_In',0,31,120,3
	.word	104907
	.byte	20,31,123,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	104937
	.byte	28
	.byte	'IfxCcu6_T13hr_In',0,31,128,1,3
	.word	104988
	.byte	20,31,131,1,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	105019
	.byte	28
	.byte	'IfxCcu6_Cc60_Out',0,31,136,1,3
	.word	105071
	.byte	20,31,139,1,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	105102
	.byte	28
	.byte	'IfxCcu6_Cc61_Out',0,31,144,1,3
	.word	105154
	.byte	20,31,147,1,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	105185
	.byte	28
	.byte	'IfxCcu6_Cc62_Out',0,31,152,1,3
	.word	105237
	.byte	20,31,155,1,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	105268
	.byte	28
	.byte	'IfxCcu6_Cout60_Out',0,31,160,1,3
	.word	105320
	.byte	20,31,163,1,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	105353
	.byte	28
	.byte	'IfxCcu6_Cout61_Out',0,31,168,1,3
	.word	105405
	.byte	20,31,171,1,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	105438
	.byte	28
	.byte	'IfxCcu6_Cout62_Out',0,31,176,1,3
	.word	105490
	.byte	20,31,179,1,15,16,13
	.byte	'module',0
	.word	24892
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	105523
	.byte	28
	.byte	'IfxCcu6_Cout63_Out',0,31,184,1,3
	.word	105575
	.byte	28
	.byte	'IfxCcu6_CaptureCompareInput',0,15,90,3
	.word	24897
	.byte	28
	.byte	'IfxCcu6_CaptureCompareInputSignal',0,15,100,3
	.word	25155
	.byte	17,15,113,9,1,18
	.byte	'IfxCcu6_ChannelOut_cc0',0,0,18
	.byte	'IfxCcu6_ChannelOut_cout0',0,1,18
	.byte	'IfxCcu6_ChannelOut_cc1',0,2,18
	.byte	'IfxCcu6_ChannelOut_cout1',0,3,18
	.byte	'IfxCcu6_ChannelOut_cc2',0,4,18
	.byte	'IfxCcu6_ChannelOut_cout2',0,5,18
	.byte	'IfxCcu6_ChannelOut_cout3',0,6,0,28
	.byte	'IfxCcu6_ChannelOut',0,15,122,3
	.word	105686
	.byte	17,15,127,9,1,18
	.byte	'IfxCcu6_CountingInputMode_internal',0,0,18
	.byte	'IfxCcu6_CountingInputMode_manual',0,1,18
	.byte	'IfxCcu6_CountingInputMode_externalRising',0,2,18
	.byte	'IfxCcu6_CountingInputMode_externalFalling',0,3,0,28
	.byte	'IfxCcu6_CountingInputMode',0,15,137,1,3
	.word	105902
	.byte	17,15,152,1,9,1,18
	.byte	'IfxCcu6_ExternalTriggerMode_disable',0,0,18
	.byte	'IfxCcu6_ExternalTriggerMode_risingEdge',0,1,18
	.byte	'IfxCcu6_ExternalTriggerMode_fallingEdge',0,2,18
	.byte	'IfxCcu6_ExternalTriggerMode_anyEdge',0,3,0,28
	.byte	'IfxCcu6_ExternalTriggerMode',0,15,161,1,3
	.word	106102
	.byte	17,15,166,1,9,1,18
	.byte	'IfxCcu6_HallSensorTriggerMode_permanentCheck',0,0,18
	.byte	'IfxCcu6_HallSensorTriggerMode_cM63',0,1,18
	.byte	'IfxCcu6_HallSensorTriggerMode_t13PM',0,2,18
	.byte	'IfxCcu6_HallSensorTriggerMode_off',0,3,18
	.byte	'IfxCcu6_HallSensorTriggerMode_t12PMCountingUp',0,4,18
	.byte	'IfxCcu6_HallSensorTriggerMode_t12OMCountingDown',0,5,18
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingUp',0,6,18
	.byte	'IfxCcu6_HallSensorTriggerMode_cM61CountingDown',0,7,0,28
	.byte	'IfxCcu6_HallSensorTriggerMode',0,15,177,1,3
	.word	106305
	.byte	17,15,182,1,9,1,18
	.byte	'IfxCcu6_InterruptSource_cc60RisingEdge',0,0,18
	.byte	'IfxCcu6_InterruptSource_cc60FallingEdge',0,1,18
	.byte	'IfxCcu6_InterruptSource_cc61RisingEdge',0,2,18
	.byte	'IfxCcu6_InterruptSource_cc61FallingEdge',0,3,18
	.byte	'IfxCcu6_InterruptSource_cc62RisingEdge',0,4,18
	.byte	'IfxCcu6_InterruptSource_cc62FallingEdge',0,5,18
	.byte	'IfxCcu6_InterruptSource_t12OneMatch',0,6,18
	.byte	'IfxCcu6_InterruptSource_t12PeriodMatch',0,7,18
	.byte	'IfxCcu6_InterruptSource_t13CompareMatch',0,8,18
	.byte	'IfxCcu6_InterruptSource_t13PeriodMatch',0,9,18
	.byte	'IfxCcu6_InterruptSource_trap',0,10,18
	.byte	'IfxCcu6_InterruptSource_correctHallEvent',0,12,18
	.byte	'IfxCcu6_InterruptSource_wrongHallEvent',0,13,0,28
	.byte	'IfxCcu6_InterruptSource',0,15,203,1,3
	.word	106703
	.byte	17,15,208,1,9,1,18
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_noEvent',0,0,18
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_correctHallEvent',0,1,18
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t13PeriodMatch',0,2,18
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12OneMatch',0,3,18
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12Channel1CompareMatch',0,4,18
	.byte	'IfxCcu6_MultiChannelSwitchingSelect_t12PeriodMatch',0,5,0,28
	.byte	'IfxCcu6_MultiChannelSwitchingSelect',0,15,217,1,3
	.word	107269
	.byte	17,15,222,1,9,1,18
	.byte	'IfxCcu6_MultiChannelSwitchingSync_direct',0,0,18
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t13ZeroMatch',0,1,18
	.byte	'IfxCcu6_MultiChannelSwitchingSync_t12ZeroMatch',0,2,0,28
	.byte	'IfxCcu6_MultiChannelSwitchingSync',0,15,229,1,3
	.word	107640
	.byte	17,15,233,1,9,1,18
	.byte	'IfxCcu6_ServiceRequest_0',0,0,18
	.byte	'IfxCcu6_ServiceRequest_1',0,1,18
	.byte	'IfxCcu6_ServiceRequest_2',0,2,18
	.byte	'IfxCcu6_ServiceRequest_3',0,3,0,28
	.byte	'IfxCcu6_ServiceRequest',0,15,239,1,3
	.word	107831
	.byte	17,15,244,1,9,1,18
	.byte	'IfxCcu6_SleepMode_enable',0,0,18
	.byte	'IfxCcu6_SleepMode_disable',0,1,0,28
	.byte	'IfxCcu6_SleepMode',0,15,248,1,3
	.word	107978
	.byte	17,15,252,1,9,1,18
	.byte	'IfxCcu6_SuspendMode_none',0,0,18
	.byte	'IfxCcu6_SuspendMode_hard',0,1,18
	.byte	'IfxCcu6_SuspendMode_soft',0,2,0,28
	.byte	'IfxCcu6_SuspendMode',0,15,129,2,3
	.word	108067
	.byte	17,15,133,2,9,1,18
	.byte	'IfxCcu6_T12Channel_0',0,0,18
	.byte	'IfxCcu6_T12Channel_1',0,1,18
	.byte	'IfxCcu6_T12Channel_2',0,2,0,28
	.byte	'IfxCcu6_T12Channel',0,15,138,2,3
	.word	108184
	.byte	17,15,142,2,9,1,18
	.byte	'IfxCcu6_T12ChannelMode_off',0,0,18
	.byte	'IfxCcu6_T12ChannelMode_compareMode',0,1,18
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRisingAndFalling',0,4,18
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureRising',0,5,18
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureFalling',0,6,18
	.byte	'IfxCcu6_T12ChannelMode_doubleRegisterCaptureAny',0,7,18
	.byte	'IfxCcu6_T12ChannelMode_hallSensor',0,8,18
	.byte	'IfxCcu6_T12ChannelMode_hysteresisLikecompare',0,9,18
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureRisingAndFalling',0,10,18
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureFallingAndRising',0,11,18
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothRising',0,12,18
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureBothFalling',0,13,18
	.byte	'IfxCcu6_T12ChannelMode_multiInputCaptureAny',0,14,0,28
	.byte	'IfxCcu6_T12ChannelMode',0,15,158,2,3
	.word	108288
	.byte	17,15,163,2,9,1,18
	.byte	'IfxCcu6_T12CountDirection_up',0,0,18
	.byte	'IfxCcu6_T12CountDirection_down',0,1,0,28
	.byte	'IfxCcu6_T12CountDirection',0,15,167,2,3
	.word	108967
	.byte	17,15,172,2,9,1,18
	.byte	'IfxCcu6_T12CountMode_edgeAligned',0,0,18
	.byte	'IfxCcu6_T12CountMode_centerAligned',0,1,0,28
	.byte	'IfxCcu6_T12CountMode',0,15,178,2,3
	.word	109073
	.byte	17,15,183,2,9,1,18
	.byte	'IfxCcu6_T13TriggerDirection_noAction',0,0,18
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingUp',0,1,18
	.byte	'IfxCcu6_T13TriggerDirection_onT12CountingDown',0,2,18
	.byte	'IfxCcu6_T13TriggerDirection_anyT12',0,3,0,28
	.byte	'IfxCcu6_T13TriggerDirection',0,15,189,2,3
	.word	109182
	.byte	17,15,194,2,9,1,18
	.byte	'IfxCcu6_T13TriggerEvent_noAction',0,0,18
	.byte	'IfxCcu6_T13TriggerEvent_onCC60RCompare',0,1,18
	.byte	'IfxCcu6_T13TriggerEvent_onCC61RCompare',0,2,18
	.byte	'IfxCcu6_T13TriggerEvent_onCC62RCompare',0,3,18
	.byte	'IfxCcu6_T13TriggerEvent_onAnyT12Compare',0,4,18
	.byte	'IfxCcu6_T13TriggerEvent_onT12Period',0,5,18
	.byte	'IfxCcu6_T13TriggerEvent_onT12Zero',0,6,18
	.byte	'IfxCcu6_T13TriggerEvent_onCCPOSxEdge',0,7,0,28
	.byte	'IfxCcu6_T13TriggerEvent',0,15,205,2,3
	.word	109396
	.byte	17,15,209,2,9,1,18
	.byte	'IfxCcu6_TimerId_t12',0,0,18
	.byte	'IfxCcu6_TimerId_t13',0,1,0,28
	.byte	'IfxCcu6_TimerId',0,15,213,2,3
	.word	109749
	.byte	17,15,218,2,9,1,18
	.byte	'IfxCcu6_TimerInputClock_fcc6',0,0,18
	.byte	'IfxCcu6_TimerInputClock_fcc6By2',0,1,18
	.byte	'IfxCcu6_TimerInputClock_fcc6By4',0,2,18
	.byte	'IfxCcu6_TimerInputClock_fcc6By8',0,3,18
	.byte	'IfxCcu6_TimerInputClock_fcc6By16',0,4,18
	.byte	'IfxCcu6_TimerInputClock_fcc6By32',0,5,18
	.byte	'IfxCcu6_TimerInputClock_fcc6By64',0,6,18
	.byte	'IfxCcu6_TimerInputClock_fcc6By128',0,7,0,28
	.byte	'IfxCcu6_TimerInputClock',0,15,228,2,3
	.word	109825
	.byte	17,15,247,2,9,1,18
	.byte	'IfxCcu6_TimerRunStatus_stopped',0,0,18
	.byte	'IfxCcu6_TimerRunStatus_running',0,1,0,28
	.byte	'IfxCcu6_TimerRunStatus',0,15,251,2,3
	.word	110139
	.byte	17,15,128,3,9,1,18
	.byte	'IfxCcu6_TrapMode_automatic',0,0,18
	.byte	'IfxCcu6_TrapMode_manual',0,1,0,28
	.byte	'IfxCcu6_TrapMode',0,15,133,3,3
	.word	110244
	.byte	17,15,138,3,9,1,18
	.byte	'IfxCcu6_TrapState_t12Sync',0,0,18
	.byte	'IfxCcu6_TrapState_t13Sync',0,1,18
	.byte	'IfxCcu6_TrapState_immediate',0,3,0,28
	.byte	'IfxCcu6_TrapState',0,15,145,3,3
	.word	110332
	.byte	10
	.byte	'Timer_s',0,32,72,8,16,13
	.byte	't12Frequency',0
	.word	242
	.byte	4,2,35,0,13
	.byte	't12Period',0
	.word	10502
	.byte	4,2,35,4,13
	.byte	't13Frequency',0
	.word	242
	.byte	4,2,35,8,13
	.byte	't13Period',0
	.word	10502
	.byte	4,2,35,12,0,28
	.byte	'Timer',0,32,53,24
	.word	110452
	.byte	3
	.word	110452
	.byte	31,1,1,32
	.word	110562
	.byte	0,3
	.word	110567
	.byte	28
	.byte	'Timer_Start',0,32,54,24
	.word	110576
	.byte	28
	.byte	'Timer_Stop',0,32,55,24
	.word	110576
	.byte	28
	.byte	'Timer_SynchronousStart',0,32,56,24
	.word	110576
	.byte	28
	.byte	'Timer_SynchronousStop',0,32,57,24
	.word	110576
	.byte	28
	.byte	'Timer_CountOneStep',0,32,58,24
	.word	110576
	.byte	28
	.byte	'Timer_StartSingleShotMode',0,32,59,24
	.word	110576
	.byte	20,32,84,9,24,13
	.byte	't12Frequency',0
	.word	242
	.byte	4,2,35,0,13
	.byte	't12Period',0
	.word	10502
	.byte	4,2,35,4,13
	.byte	't13Frequency',0
	.word	242
	.byte	4,2,35,8,13
	.byte	't13Period',0
	.word	10502
	.byte	4,2,35,12,13
	.byte	'waitingTime',0
	.word	10502
	.byte	4,2,35,16,13
	.byte	'activeCount',0
	.word	10502
	.byte	4,2,35,20,0,28
	.byte	'Timer_Config',0,32,92,3
	.word	110742
	.byte	30
	.word	104856
	.byte	3
	.word	110893
	.byte	30
	.word	104937
	.byte	3
	.word	110903
	.byte	20,33,234,1,9,20,13
	.byte	't12ExtClockEnabled',0
	.word	621
	.byte	1,2,35,0,13
	.byte	't12ExtClockInput',0
	.word	110898
	.byte	4,2,35,4,13
	.byte	't12countingInputMode',0
	.word	105902
	.byte	1,2,35,8,13
	.byte	't13ExtClockEnabled',0
	.word	621
	.byte	1,2,35,9,13
	.byte	't13ExtClockInput',0
	.word	110908
	.byte	4,2,35,12,13
	.byte	't13countingInputMode',0
	.word	105902
	.byte	1,2,35,16,0,28
	.byte	'IfxCcu6_Timer_Clock',0,33,242,1,3
	.word	110913
	.byte	20,33,246,1,9,6,13
	.byte	'source',0
	.word	106703
	.byte	1,2,35,0,13
	.byte	'serviceRequest',0
	.word	107831
	.byte	1,2,35,1,13
	.byte	'priority',0
	.word	638
	.byte	2,2,35,2,13
	.byte	'typeOfService',0
	.word	99449
	.byte	1,2,35,4,0,28
	.byte	'IfxCcu6_Timer_InterruptConfig',0,33,252,1,3
	.word	111117
	.byte	20,33,128,2,9,4,13
	.byte	'countMode',0
	.word	109073
	.byte	1,2,35,0,13
	.byte	'counterValue',0
	.word	638
	.byte	2,2,35,2,0,28
	.byte	'IfxCcu6_Timer_Timer12',0,33,132,2,3
	.word	111244
	.byte	20,33,136,2,9,4,13
	.byte	'counterValue',0
	.word	638
	.byte	2,2,35,0,13
	.byte	't12SyncEvent',0
	.word	109396
	.byte	1,2,35,2,13
	.byte	't12SyncDirection',0
	.word	109182
	.byte	1,2,35,3,0,28
	.byte	'IfxCcu6_Timer_Timer13',0,33,141,2,3
	.word	111323
	.byte	20,33,145,2,9,12,13
	.byte	't12ExtInputTrigger',0
	.word	110898
	.byte	4,2,35,0,13
	.byte	't13ExtInputTrigger',0
	.word	110908
	.byte	4,2,35,4,13
	.byte	'extInputTriggerMode',0
	.word	106102
	.byte	1,2,35,8,13
	.byte	't13InSyncWithT12',0
	.word	621
	.byte	1,2,35,9,0,28
	.byte	'IfxCcu6_Timer_TriggerConfig',0,33,151,2,3
	.word	111431
	.byte	20,33,157,2,9,12,13
	.byte	't12hr',0
	.word	110898
	.byte	4,2,35,0,13
	.byte	't13hr',0
	.word	110908
	.byte	4,2,35,4,13
	.byte	't1xhrInputMode',0
	.word	9371
	.byte	1,2,35,8,0,28
	.byte	'IfxCcu6_Timer_Pins',0,33,162,2,3
	.word	111586
	.byte	7
	.byte	'char',0,1,6,28
	.byte	'int8',0,34,54,29
	.word	111675
	.byte	28
	.byte	'int16',0,34,55,29
	.word	32481
	.byte	28
	.byte	'int32',0,34,56,29
	.word	462
	.byte	28
	.byte	'int64',0,34,57,29
	.word	14761
	.byte	17,35,78,9,1,18
	.byte	'FIFO_DATA_8BIT',0,0,18
	.byte	'FIFO_DATA_16BIT',0,1,18
	.byte	'FIFO_DATA_32BIT',0,2,0,28
	.byte	'fifo_data_type_enum',0,35,83,2
	.word	111738
	.byte	17,36,105,9,1,18
	.byte	'IfxDma_ChannelId_none',0,127,18
	.byte	'IfxDma_ChannelId_0',0,0,18
	.byte	'IfxDma_ChannelId_1',0,1,18
	.byte	'IfxDma_ChannelId_2',0,2,18
	.byte	'IfxDma_ChannelId_3',0,3,18
	.byte	'IfxDma_ChannelId_4',0,4,18
	.byte	'IfxDma_ChannelId_5',0,5,18
	.byte	'IfxDma_ChannelId_6',0,6,18
	.byte	'IfxDma_ChannelId_7',0,7,18
	.byte	'IfxDma_ChannelId_8',0,8,18
	.byte	'IfxDma_ChannelId_9',0,9,18
	.byte	'IfxDma_ChannelId_10',0,10,18
	.byte	'IfxDma_ChannelId_11',0,11,18
	.byte	'IfxDma_ChannelId_12',0,12,18
	.byte	'IfxDma_ChannelId_13',0,13,18
	.byte	'IfxDma_ChannelId_14',0,14,18
	.byte	'IfxDma_ChannelId_15',0,15,18
	.byte	'IfxDma_ChannelId_16',0,16,18
	.byte	'IfxDma_ChannelId_17',0,17,18
	.byte	'IfxDma_ChannelId_18',0,18,18
	.byte	'IfxDma_ChannelId_19',0,19,18
	.byte	'IfxDma_ChannelId_20',0,20,18
	.byte	'IfxDma_ChannelId_21',0,21,18
	.byte	'IfxDma_ChannelId_22',0,22,18
	.byte	'IfxDma_ChannelId_23',0,23,18
	.byte	'IfxDma_ChannelId_24',0,24,18
	.byte	'IfxDma_ChannelId_25',0,25,18
	.byte	'IfxDma_ChannelId_26',0,26,18
	.byte	'IfxDma_ChannelId_27',0,27,18
	.byte	'IfxDma_ChannelId_28',0,28,18
	.byte	'IfxDma_ChannelId_29',0,29,18
	.byte	'IfxDma_ChannelId_30',0,30,18
	.byte	'IfxDma_ChannelId_31',0,31,18
	.byte	'IfxDma_ChannelId_32',0,32,18
	.byte	'IfxDma_ChannelId_33',0,33,18
	.byte	'IfxDma_ChannelId_34',0,34,18
	.byte	'IfxDma_ChannelId_35',0,35,18
	.byte	'IfxDma_ChannelId_36',0,36,18
	.byte	'IfxDma_ChannelId_37',0,37,18
	.byte	'IfxDma_ChannelId_38',0,38,18
	.byte	'IfxDma_ChannelId_39',0,39,18
	.byte	'IfxDma_ChannelId_40',0,40,18
	.byte	'IfxDma_ChannelId_41',0,41,18
	.byte	'IfxDma_ChannelId_42',0,42,18
	.byte	'IfxDma_ChannelId_43',0,43,18
	.byte	'IfxDma_ChannelId_44',0,44,18
	.byte	'IfxDma_ChannelId_45',0,45,18
	.byte	'IfxDma_ChannelId_46',0,46,18
	.byte	'IfxDma_ChannelId_47',0,47,0,28
	.byte	'IfxDma_ChannelId',0,36,156,1,3
	.word	111825
	.byte	10
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,37,45,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_DMA_ACCEN00_Bits',0,37,79,3
	.word	112927
	.byte	10
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,37,82,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN01_Bits',0,37,85,3
	.word	113486
	.byte	10
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,37,88,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_DMA_ACCEN10_Bits',0,37,122,3
	.word	113565
	.byte	10
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,37,125,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN11_Bits',0,37,128,1,3
	.word	114124
	.byte	10
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,37,131,1,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_DMA_ACCEN20_Bits',0,37,165,1,3
	.word	114204
	.byte	10
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,37,168,1,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN21_Bits',0,37,171,1,3
	.word	114765
	.byte	10
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,37,174,1,16,4,11
	.byte	'EN0',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	621
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	621
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	621
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	621
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	621
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	621
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_DMA_ACCEN30_Bits',0,37,208,1,3
	.word	114846
	.byte	10
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,37,211,1,16,4,11
	.byte	'reserved_0',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN31_Bits',0,37,214,1,3
	.word	115407
	.byte	10
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,37,217,1,16,4,11
	.byte	'reserved_0',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'CSER',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'CDER',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	621
	.byte	2,4,2,35,2,11
	.byte	'CSPBER',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'CSRIER',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'CRAMER',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CSLLER',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'CDLLER',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	5,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,37,230,1,3
	.word	115488
	.byte	10
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,37,233,1,16,4,11
	.byte	'reserved_0',0,2
	.word	638
	.byte	16,0,2,35,0,11
	.byte	'ESER',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'EDER',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	621
	.byte	6,0,2,35,2,11
	.byte	'ERER',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'ELER',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	5,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_EER_Bits',0,37,243,1,3
	.word	115762
	.byte	10
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,37,246,1,16,4,11
	.byte	'LEC',0,1
	.word	621
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	638
	.byte	9,0,2,35,0,11
	.byte	'SER',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'DER',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	621
	.byte	2,4,2,35,2,11
	.byte	'SPBER',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'SRIER',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	621
	.byte	2,0,2,35,2,11
	.byte	'RAMER',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'SLLER',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'DLLER',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	5,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,37,132,2,3
	.word	115976
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,37,135,2,16,4,11
	.byte	'SMF',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	621
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	621
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	621
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,37,152,2,3
	.word	116260
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,37,155,2,16,4,11
	.byte	'TREL',0,2
	.word	638
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	621
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	621
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,37,168,2,3
	.word	116571
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,37,171,2,16,4,11
	.byte	'TCOUNT',0,2
	.word	638
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	621
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,37,184,2,3
	.word	116844
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,37,187,2,16,4,11
	.byte	'DADR',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,37,190,2,3
	.word	117111
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,37,193,2,16,4,11
	.byte	'RD00',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD01',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD02',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD03',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,37,199,2,3
	.word	117194
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,37,202,2,16,4,11
	.byte	'RD10',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD11',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD12',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD13',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,37,208,2,3
	.word	117321
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,37,211,2,16,4,11
	.byte	'RD20',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD21',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD22',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD23',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,37,217,2,3
	.word	117448
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,37,220,2,16,4,11
	.byte	'RD30',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD31',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD32',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD33',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,37,226,2,3
	.word	117575
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,37,229,2,16,4,11
	.byte	'RD40',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD41',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD42',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD43',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,37,235,2,3
	.word	117702
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,37,238,2,16,4,11
	.byte	'RD50',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD51',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD52',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD53',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,37,244,2,3
	.word	117829
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,37,247,2,16,4,11
	.byte	'RD60',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD61',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD62',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD63',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,37,253,2,3
	.word	117956
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,37,128,3,16,4,11
	.byte	'RD70',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'RD71',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'RD72',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'RD73',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,37,134,3,3
	.word	118083
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,37,137,3,16,4,11
	.byte	'RDCRC',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,37,140,3,3
	.word	118210
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,37,143,3,16,4,11
	.byte	'SADR',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,37,146,3,3
	.word	118296
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,37,149,3,16,4,11
	.byte	'SDCRC',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,37,152,3,3
	.word	118379
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,37,155,3,16,4,11
	.byte	'SHADR',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,37,158,3,3
	.word	118465
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,37,161,3,16,4,11
	.byte	'RS',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	621
	.byte	3,4,2,35,0,11
	.byte	'WS',0,1
	.word	621
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	638
	.byte	11,0,2,35,0,11
	.byte	'CH',0,1
	.word	621
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	638
	.byte	9,0,2,35,2,0,28
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,37,169,3,3
	.word	118551
	.byte	10
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,37,172,3,16,4,11
	.byte	'SMF',0,1
	.word	621
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	621
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	621
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	621
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	621
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	621
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	621
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	621
	.byte	4,0,2,35,3,0,28
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,37,189,3,3
	.word	118723
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,37,192,3,16,4,11
	.byte	'TREL',0,2
	.word	638
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	621
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	621
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	621
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	621
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	621
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	621
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	621
	.byte	2,0,2,35,3,0,28
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,37,205,3,3
	.word	119026
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,37,208,3,16,4,11
	.byte	'TCOUNT',0,2
	.word	638
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	621
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	621
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	621
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	621
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	621
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	621
	.byte	1,0,2,35,2,11
	.byte	'SWB',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'CWRP',0,1
	.word	621
	.byte	1,6,2,35,3,11
	.byte	'CICH',0,1
	.word	621
	.byte	1,5,2,35,3,11
	.byte	'SIT',0,1
	.word	621
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	621
	.byte	3,1,2,35,3,11
	.byte	'SCH',0,1
	.word	621
	.byte	1,0,2,35,3,0,28
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,37,226,3,3
	.word	119295
	.byte	10
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,37,229,3,16,4,11
	.byte	'DADR',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_CH_DADR_Bits',0,37,232,3,3
	.word	119633
	.byte	10
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,37,235,3,16,4,11
	.byte	'RDCRC',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,37,238,3,3
	.word	119708
	.byte	10
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,37,241,3,16,4,11
	.byte	'SADR',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_CH_SADR_Bits',0,37,244,3,3
	.word	119788
	.byte	10
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,37,247,3,16,4,11
	.byte	'SDCRC',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,37,250,3,3
	.word	119863
	.byte	10
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,37,253,3,16,4,11
	.byte	'SHADR',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,37,128,4,3
	.word	119943
	.byte	10
	.byte	'_Ifx_DMA_CLC_Bits',0,37,131,4,16,4,11
	.byte	'DISR',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	446
	.byte	28,0,2,35,0,0,28
	.byte	'Ifx_DMA_CLC_Bits',0,37,138,4,3
	.word	120021
	.byte	10
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,37,141,4,16,4,11
	.byte	'SIT',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_DMA_ERRINTR_Bits',0,37,145,4,3
	.word	120164
	.byte	10
	.byte	'_Ifx_DMA_HRR_Bits',0,37,148,4,16,4,11
	.byte	'HRP',0,1
	.word	621
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	446
	.byte	30,0,2,35,0,0,28
	.byte	'Ifx_DMA_HRR_Bits',0,37,152,4,3
	.word	120260
	.byte	10
	.byte	'_Ifx_DMA_ID_Bits',0,37,155,4,16,4,11
	.byte	'MODREV',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	638
	.byte	16,0,2,35,2,0,28
	.byte	'Ifx_DMA_ID_Bits',0,37,160,4,3
	.word	120348
	.byte	10
	.byte	'_Ifx_DMA_MEMCON_Bits',0,37,163,4,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	2,30,2,35,0,11
	.byte	'INTERR',0,4
	.word	469
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	1,28,2,35,0,11
	.byte	'RMWERR',0,4
	.word	469
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	469
	.byte	1,26,2,35,0,11
	.byte	'DATAERR',0,4
	.word	469
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	469
	.byte	1,24,2,35,0,11
	.byte	'PMIC',0,4
	.word	469
	.byte	1,23,2,35,0,11
	.byte	'ERRDIS',0,4
	.word	469
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	22,0,2,35,0,0,28
	.byte	'Ifx_DMA_MEMCON_Bits',0,37,175,4,3
	.word	120455
	.byte	10
	.byte	'_Ifx_DMA_MODE_Bits',0,37,178,4,16,4,11
	.byte	'MODE',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_DMA_MODE_Bits',0,37,182,4,3
	.word	120712
	.byte	10
	.byte	'_Ifx_DMA_OTSS_Bits',0,37,185,4,16,4,11
	.byte	'TGS',0,1
	.word	621
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	3,1,2,35,0,11
	.byte	'BS',0,1
	.word	621
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	446
	.byte	24,0,2,35,0,0,28
	.byte	'Ifx_DMA_OTSS_Bits',0,37,191,4,3
	.word	120803
	.byte	10
	.byte	'_Ifx_DMA_PRR0_Bits',0,37,194,4,16,4,11
	.byte	'PAT00',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'PAT01',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'PAT02',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'PAT03',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_PRR0_Bits',0,37,200,4,3
	.word	120929
	.byte	10
	.byte	'_Ifx_DMA_PRR1_Bits',0,37,203,4,16,4,11
	.byte	'PAT10',0,1
	.word	621
	.byte	8,0,2,35,0,11
	.byte	'PAT11',0,1
	.word	621
	.byte	8,0,2,35,1,11
	.byte	'PAT12',0,1
	.word	621
	.byte	8,0,2,35,2,11
	.byte	'PAT13',0,1
	.word	621
	.byte	8,0,2,35,3,0,28
	.byte	'Ifx_DMA_PRR1_Bits',0,37,209,4,3
	.word	121050
	.byte	10
	.byte	'_Ifx_DMA_SUSACR_Bits',0,37,212,4,16,4,11
	.byte	'SUSAC',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_DMA_SUSACR_Bits',0,37,216,4,3
	.word	121171
	.byte	10
	.byte	'_Ifx_DMA_SUSENR_Bits',0,37,219,4,16,4,11
	.byte	'SUSEN',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	446
	.byte	31,0,2,35,0,0,28
	.byte	'Ifx_DMA_SUSENR_Bits',0,37,223,4,3
	.word	121267
	.byte	10
	.byte	'_Ifx_DMA_TIME_Bits',0,37,226,4,16,4,11
	.byte	'COUNT',0,4
	.word	446
	.byte	32,0,2,35,0,0,28
	.byte	'Ifx_DMA_TIME_Bits',0,37,229,4,3
	.word	121363
	.byte	10
	.byte	'_Ifx_DMA_TSR_Bits',0,37,232,4,16,4,11
	.byte	'RST',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'HTRE',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'TRL',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'CH',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	621
	.byte	4,0,2,35,0,11
	.byte	'HLTREQ',0,1
	.word	621
	.byte	1,7,2,35,1,11
	.byte	'HLTACK',0,1
	.word	621
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	621
	.byte	6,0,2,35,1,11
	.byte	'ECH',0,1
	.word	621
	.byte	1,7,2,35,2,11
	.byte	'DCH',0,1
	.word	621
	.byte	1,6,2,35,2,11
	.byte	'CTL',0,1
	.word	621
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	621
	.byte	5,0,2,35,2,11
	.byte	'HLTCLR',0,1
	.word	621
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	621
	.byte	7,0,2,35,3,0,28
	.byte	'Ifx_DMA_TSR_Bits',0,37,248,4,3
	.word	121433
	.byte	12,37,128,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	112927
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN00',0,37,133,5,3
	.word	121734
	.byte	12,37,136,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113486
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN01',0,37,141,5,3
	.word	121799
	.byte	12,37,144,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113565
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN10',0,37,149,5,3
	.word	121864
	.byte	12,37,152,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114124
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN11',0,37,157,5,3
	.word	121929
	.byte	12,37,160,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114204
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN20',0,37,165,5,3
	.word	121994
	.byte	12,37,168,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114765
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN21',0,37,173,5,3
	.word	122059
	.byte	12,37,176,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114846
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN30',0,37,181,5,3
	.word	122124
	.byte	12,37,184,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	115407
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ACCEN31',0,37,189,5,3
	.word	122189
	.byte	12,37,192,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	115488
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_CLRE',0,37,197,5,3
	.word	122254
	.byte	12,37,200,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	115762
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_EER',0,37,205,5,3
	.word	122320
	.byte	12,37,208,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	115976
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ERRSR',0,37,213,5,3
	.word	122385
	.byte	12,37,216,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	116260
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,37,221,5,3
	.word	122452
	.byte	12,37,224,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	116571
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,37,229,5,3
	.word	122522
	.byte	12,37,232,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	116844
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,37,237,5,3
	.word	122591
	.byte	12,37,240,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117111
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_DADR',0,37,245,5,3
	.word	122660
	.byte	12,37,248,5,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117194
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R0',0,37,253,5,3
	.word	122729
	.byte	12,37,128,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117321
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R1',0,37,133,6,3
	.word	122796
	.byte	12,37,136,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117448
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R2',0,37,141,6,3
	.word	122863
	.byte	12,37,144,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117575
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R3',0,37,149,6,3
	.word	122930
	.byte	12,37,152,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117702
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R4',0,37,157,6,3
	.word	122997
	.byte	12,37,160,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117829
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R5',0,37,165,6,3
	.word	123064
	.byte	12,37,168,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	117956
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R6',0,37,173,6,3
	.word	123131
	.byte	12,37,176,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	118083
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_R7',0,37,181,6,3
	.word	123198
	.byte	12,37,184,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	118210
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,37,189,6,3
	.word	123265
	.byte	12,37,192,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	118296
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_SADR',0,37,197,6,3
	.word	123335
	.byte	12,37,200,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	118379
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,37,205,6,3
	.word	123404
	.byte	12,37,208,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	118465
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,37,213,6,3
	.word	123474
	.byte	12,37,216,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	118551
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_BLK_ME_SR',0,37,221,6,3
	.word	123544
	.byte	12,37,224,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	118723
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_ADICR',0,37,229,6,3
	.word	123611
	.byte	12,37,232,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	119026
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_CHCFGR',0,37,237,6,3
	.word	123677
	.byte	12,37,240,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	119295
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_CHCSR',0,37,245,6,3
	.word	123744
	.byte	12,37,248,6,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	119633
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_DADR',0,37,253,6,3
	.word	123810
	.byte	12,37,128,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	119708
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_RDCRCR',0,37,133,7,3
	.word	123875
	.byte	12,37,136,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	119788
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_SADR',0,37,141,7,3
	.word	123942
	.byte	12,37,144,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	119863
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_SDCRCR',0,37,149,7,3
	.word	124007
	.byte	12,37,152,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	119943
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CH_SHADR',0,37,157,7,3
	.word	124074
	.byte	12,37,160,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120021
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_CLC',0,37,165,7,3
	.word	124140
	.byte	12,37,168,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120164
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ERRINTR',0,37,173,7,3
	.word	124201
	.byte	12,37,176,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120260
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_HRR',0,37,181,7,3
	.word	124266
	.byte	12,37,184,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120348
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_ID',0,37,189,7,3
	.word	124327
	.byte	12,37,192,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120455
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_MEMCON',0,37,197,7,3
	.word	124387
	.byte	12,37,200,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120712
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_MODE',0,37,205,7,3
	.word	124451
	.byte	12,37,208,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120803
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_OTSS',0,37,213,7,3
	.word	124513
	.byte	12,37,216,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120929
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_PRR0',0,37,221,7,3
	.word	124575
	.byte	12,37,224,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121050
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_PRR1',0,37,229,7,3
	.word	124637
	.byte	12,37,232,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121171
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_SUSACR',0,37,237,7,3
	.word	124699
	.byte	12,37,240,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121267
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_SUSENR',0,37,245,7,3
	.word	124763
	.byte	12,37,248,7,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121363
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_TIME',0,37,253,7,3
	.word	124827
	.byte	12,37,128,8,9,4,13
	.byte	'U',0
	.word	446
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121433
	.byte	4,2,35,0,0,28
	.byte	'Ifx_DMA_TSR',0,37,133,8,3
	.word	124889
	.byte	15,32
	.word	621
	.byte	16,31,0,10
	.byte	'_Ifx_DMA_BLK_ME',0,37,144,8,25,112,13
	.byte	'SR',0
	.word	123544
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4778
	.byte	12,2,35,4,13
	.byte	'R0',0
	.word	122729
	.byte	4,2,35,16,13
	.byte	'R1',0
	.word	122796
	.byte	4,2,35,20,13
	.byte	'R2',0
	.word	122863
	.byte	4,2,35,24,13
	.byte	'R3',0
	.word	122930
	.byte	4,2,35,28,13
	.byte	'R4',0
	.word	122997
	.byte	4,2,35,32,13
	.byte	'R5',0
	.word	123064
	.byte	4,2,35,36,13
	.byte	'R6',0
	.word	123131
	.byte	4,2,35,40,13
	.byte	'R7',0
	.word	123198
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	124950
	.byte	32,2,35,48,13
	.byte	'RDCRC',0
	.word	123265
	.byte	4,2,35,80,13
	.byte	'SDCRC',0
	.word	123404
	.byte	4,2,35,84,13
	.byte	'SADR',0
	.word	123335
	.byte	4,2,35,88,13
	.byte	'DADR',0
	.word	122660
	.byte	4,2,35,92,13
	.byte	'ADICR',0
	.word	122452
	.byte	4,2,35,96,13
	.byte	'CHCR',0
	.word	122522
	.byte	4,2,35,100,13
	.byte	'SHADR',0
	.word	123474
	.byte	4,2,35,104,13
	.byte	'CHSR',0
	.word	122591
	.byte	4,2,35,108,0,14
	.word	124959
	.byte	28
	.byte	'Ifx_DMA_BLK_ME',0,37,165,8,3
	.word	125247
	.byte	14
	.word	124959
	.byte	10
	.byte	'_Ifx_DMA_BLK',0,37,178,8,25,128,1,13
	.byte	'EER',0
	.word	122320
	.byte	4,2,35,0,13
	.byte	'ERRSR',0
	.word	122385
	.byte	4,2,35,4,13
	.byte	'CLRE',0
	.word	122254
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2619
	.byte	4,2,35,12,13
	.byte	'ME',0
	.word	125276
	.byte	112,2,35,16,0,14
	.word	125281
	.byte	28
	.byte	'Ifx_DMA_BLK',0,37,185,8,3
	.word	125376
	.byte	10
	.byte	'_Ifx_DMA_CH',0,37,188,8,25,32,13
	.byte	'RDCRCR',0
	.word	123875
	.byte	4,2,35,0,13
	.byte	'SDCRCR',0
	.word	124007
	.byte	4,2,35,4,13
	.byte	'SADR',0
	.word	123942
	.byte	4,2,35,8,13
	.byte	'DADR',0
	.word	123810
	.byte	4,2,35,12,13
	.byte	'ADICR',0
	.word	123611
	.byte	4,2,35,16,13
	.byte	'CHCFGR',0
	.word	123677
	.byte	4,2,35,20,13
	.byte	'SHADR',0
	.word	124074
	.byte	4,2,35,24,13
	.byte	'CHCSR',0
	.word	123744
	.byte	4,2,35,28,0,14
	.word	125402
	.byte	28
	.byte	'Ifx_DMA_CH',0,37,198,8,3
	.word	125542
	.byte	15,192,1
	.word	621
	.byte	16,191,1,0,14
	.word	125281
	.byte	14
	.word	125281
	.byte	15,236,1
	.word	621
	.byte	16,235,1,0,15,16
	.word	124451
	.byte	16,3,0,15,240,9
	.word	621
	.byte	16,239,9,0,15,192,1
	.word	124266
	.byte	16,47,0,15,192,1
	.word	124763
	.byte	16,47,0,15,192,1
	.word	124699
	.byte	16,47,0,15,192,1
	.word	124889
	.byte	16,47,0,15,128,12
	.word	125402
	.byte	16,47,0,14
	.word	125659
	.byte	15,128,52
	.word	621
	.byte	16,255,51,0,10
	.byte	'_Ifx_DMA',0,37,211,8,25,128,128,1,13
	.byte	'CLC',0
	.word	124140
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2619
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	124327
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	82769
	.byte	20,2,35,12,13
	.byte	'MEMCON',0
	.word	124387
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	50347
	.byte	28,2,35,36,13
	.byte	'ACCEN00',0
	.word	121734
	.byte	4,2,35,64,13
	.byte	'ACCEN01',0
	.word	121799
	.byte	4,2,35,68,13
	.byte	'ACCEN10',0
	.word	121864
	.byte	4,2,35,72,13
	.byte	'ACCEN11',0
	.word	121929
	.byte	4,2,35,76,13
	.byte	'ACCEN20',0
	.word	121994
	.byte	4,2,35,80,13
	.byte	'ACCEN21',0
	.word	122059
	.byte	4,2,35,84,13
	.byte	'ACCEN30',0
	.word	122124
	.byte	4,2,35,88,13
	.byte	'ACCEN31',0
	.word	122189
	.byte	4,2,35,92,13
	.byte	'reserved_60',0
	.word	125567
	.byte	192,1,2,35,96,13
	.byte	'BLK0',0
	.word	125578
	.byte	128,1,3,35,160,2,13
	.byte	'reserved_1A0',0
	.word	50221
	.byte	128,31,3,35,160,3,13
	.byte	'BLK1',0
	.word	125583
	.byte	128,1,3,35,160,34,13
	.byte	'reserved_11A0',0
	.word	53896
	.byte	96,3,35,160,35,13
	.byte	'OTSS',0
	.word	124513
	.byte	4,3,35,128,36,13
	.byte	'ERRINTR',0
	.word	124201
	.byte	4,3,35,132,36,13
	.byte	'PRR0',0
	.word	124575
	.byte	4,3,35,136,36,13
	.byte	'PRR1',0
	.word	124637
	.byte	4,3,35,140,36,13
	.byte	'TIME',0
	.word	124827
	.byte	4,3,35,144,36,13
	.byte	'reserved_1214',0
	.word	125588
	.byte	236,1,3,35,148,36,13
	.byte	'MODE',0
	.word	125599
	.byte	16,3,35,128,38,13
	.byte	'reserved_1310',0
	.word	125608
	.byte	240,9,3,35,144,38,13
	.byte	'HRR',0
	.word	125619
	.byte	192,1,3,35,128,48,13
	.byte	'reserved_18C0',0
	.word	53963
	.byte	192,2,3,35,192,49,13
	.byte	'SUSENR',0
	.word	125629
	.byte	192,1,3,35,128,52,13
	.byte	'reserved_1AC0',0
	.word	53963
	.byte	192,2,3,35,192,53,13
	.byte	'SUSACR',0
	.word	125639
	.byte	192,1,3,35,128,56,13
	.byte	'reserved_1CC0',0
	.word	53963
	.byte	192,2,3,35,192,57,13
	.byte	'TSR',0
	.word	125649
	.byte	192,1,3,35,128,60,13
	.byte	'reserved_1EC0',0
	.word	53963
	.byte	192,2,3,35,192,61,13
	.byte	'CH',0
	.word	125669
	.byte	128,12,3,35,128,64,13
	.byte	'reserved_2600',0
	.word	125674
	.byte	128,52,3,35,128,76,0,14
	.word	125685
	.byte	28
	.byte	'Ifx_DMA',0,37,250,8,3
	.word	126391
	.byte	17,38,104,9,1,18
	.byte	'IfxDma_ChannelIncrementCircular_none',0,0,18
	.byte	'IfxDma_ChannelIncrementCircular_2',0,1,18
	.byte	'IfxDma_ChannelIncrementCircular_4',0,2,18
	.byte	'IfxDma_ChannelIncrementCircular_8',0,3,18
	.byte	'IfxDma_ChannelIncrementCircular_16',0,4,18
	.byte	'IfxDma_ChannelIncrementCircular_32',0,5,18
	.byte	'IfxDma_ChannelIncrementCircular_64',0,6,18
	.byte	'IfxDma_ChannelIncrementCircular_128',0,7,18
	.byte	'IfxDma_ChannelIncrementCircular_256',0,8,18
	.byte	'IfxDma_ChannelIncrementCircular_512',0,9,18
	.byte	'IfxDma_ChannelIncrementCircular_1024',0,10,18
	.byte	'IfxDma_ChannelIncrementCircular_2048',0,11,18
	.byte	'IfxDma_ChannelIncrementCircular_4096',0,12,18
	.byte	'IfxDma_ChannelIncrementCircular_8192',0,13,18
	.byte	'IfxDma_ChannelIncrementCircular_16384',0,14,18
	.byte	'IfxDma_ChannelIncrementCircular_32768',0,15,0,28
	.byte	'IfxDma_ChannelIncrementCircular',0,38,122,3
	.word	126413
	.byte	17,38,127,9,1,18
	.byte	'IfxDma_ChannelIncrementDirection_negative',0,0,18
	.byte	'IfxDma_ChannelIncrementDirection_positive',0,1,0,28
	.byte	'IfxDma_ChannelIncrementDirection',0,38,131,1,3
	.word	127067
	.byte	17,38,136,1,9,1,18
	.byte	'IfxDma_ChannelIncrementStep_1',0,0,18
	.byte	'IfxDma_ChannelIncrementStep_2',0,1,18
	.byte	'IfxDma_ChannelIncrementStep_4',0,2,18
	.byte	'IfxDma_ChannelIncrementStep_8',0,3,18
	.byte	'IfxDma_ChannelIncrementStep_16',0,4,18
	.byte	'IfxDma_ChannelIncrementStep_32',0,5,18
	.byte	'IfxDma_ChannelIncrementStep_64',0,6,18
	.byte	'IfxDma_ChannelIncrementStep_128',0,7,0,28
	.byte	'IfxDma_ChannelIncrementStep',0,38,146,1,3
	.word	127203
	.byte	17,38,160,1,9,1,18
	.byte	'IfxDma_ChannelMove_1',0,0,18
	.byte	'IfxDma_ChannelMove_2',0,1,18
	.byte	'IfxDma_ChannelMove_4',0,2,18
	.byte	'IfxDma_ChannelMove_8',0,3,18
	.byte	'IfxDma_ChannelMove_16',0,4,18
	.byte	'IfxDma_ChannelMove_3',0,5,18
	.byte	'IfxDma_ChannelMove_5',0,6,18
	.byte	'IfxDma_ChannelMove_9',0,7,0,28
	.byte	'IfxDma_ChannelMove',0,38,170,1,3
	.word	127508
	.byte	17,38,175,1,9,1,18
	.byte	'IfxDma_ChannelMoveSize_8bit',0,0,18
	.byte	'IfxDma_ChannelMoveSize_16bit',0,1,18
	.byte	'IfxDma_ChannelMoveSize_32bit',0,2,18
	.byte	'IfxDma_ChannelMoveSize_64bit',0,3,18
	.byte	'IfxDma_ChannelMoveSize_128bit',0,4,18
	.byte	'IfxDma_ChannelMoveSize_256bit',0,5,0,28
	.byte	'IfxDma_ChannelMoveSize',0,38,183,1,3
	.word	127728
	.byte	17,38,239,1,9,1,18
	.byte	'IfxDma_ChannelShadow_none',0,0,18
	.byte	'IfxDma_ChannelShadow_src',0,1,18
	.byte	'IfxDma_ChannelShadow_dst',0,2,18
	.byte	'IfxDma_ChannelShadow_srcDirectWrite',0,5,18
	.byte	'IfxDma_ChannelShadow_dstDirectWrite',0,6,18
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingSwSwitch',0,8,18
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingHwSwSwitch',0,9,18
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingSwSwitch',0,10,18
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingHwSwSwitch',0,11,18
	.byte	'IfxDma_ChannelShadow_linkedList',0,12,18
	.byte	'IfxDma_ChannelShadow_accumulatedLinkedList',0,13,18
	.byte	'IfxDma_ChannelShadow_safeLinkedList',0,14,18
	.byte	'IfxDma_ChannelShadow_conditionalLinkedList',0,15,0,28
	.byte	'IfxDma_ChannelShadow',0,38,254,1,3
	.word	127954
	.byte	17,38,128,2,9,1,18
	.byte	'IfxDma_HardwareResourcePartition_0',0,0,18
	.byte	'IfxDma_HardwareResourcePartition_1',0,1,18
	.byte	'IfxDma_HardwareResourcePartition_2',0,2,18
	.byte	'IfxDma_HardwareResourcePartition_3',0,3,0,28
	.byte	'IfxDma_HardwareResourcePartition',0,38,134,2,3
	.word	128537
	.byte	17,38,138,2,9,1,18
	.byte	'IfxDma_MoveEngine_0',0,0,18
	.byte	'IfxDma_MoveEngine_1',0,1,0,28
	.byte	'IfxDma_MoveEngine',0,38,142,2,3
	.word	128734
	.byte	17,38,147,2,9,1,18
	.byte	'IfxDma_SleepMode_enable',0,0,18
	.byte	'IfxDma_SleepMode_disable',0,1,0,28
	.byte	'IfxDma_SleepMode',0,38,151,2,3
	.word	128812
	.byte	17,39,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,28
	.byte	'gpio_pin_enum',0,39,89,2
	.word	128898
	.byte	20,40,45,9,1,11
	.byte	'mode',0,1
	.word	621
	.byte	6,2,2,35,0,11
	.byte	'use_miso',0,1
	.word	621
	.byte	1,1,2,35,0,11
	.byte	'use_cs',0,1
	.word	621
	.byte	1,0,2,35,0,0,28
	.byte	'spi_config_info_struct',0,40,50,2
	.word	130860
	.byte	20,41,59,15,16,13
	.byte	'module',0
	.word	31938
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	130951
	.byte	28
	.byte	'IfxAsclin_Cts_In',0,41,64,3
	.word	131002
	.byte	20,41,67,15,16,13
	.byte	'module',0
	.word	31938
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	35887
	.byte	1,2,35,12,0,30
	.word	131032
	.byte	28
	.byte	'IfxAsclin_Rx_In',0,41,72,3
	.word	131083
	.byte	20,41,75,15,16,13
	.byte	'module',0
	.word	31938
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	131112
	.byte	28
	.byte	'IfxAsclin_Rts_Out',0,41,80,3
	.word	131163
	.byte	20,41,83,15,16,13
	.byte	'module',0
	.word	31938
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	131194
	.byte	28
	.byte	'IfxAsclin_Sclk_Out',0,41,88,3
	.word	131245
	.byte	20,41,91,15,16,13
	.byte	'module',0
	.word	31938
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	131277
	.byte	28
	.byte	'IfxAsclin_Slso_Out',0,41,96,3
	.word	131328
	.byte	20,41,99,15,16,13
	.byte	'module',0
	.word	31938
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	98329
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9646
	.byte	1,2,35,12,0,30
	.word	131360
	.byte	28
	.byte	'IfxAsclin_Tx_Out',0,41,104,3
	.word	131411
	.byte	17,17,82,9,1,18
	.byte	'IfxAsclin_Checksum_classic',0,0,18
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,28
	.byte	'IfxAsclin_Checksum',0,17,86,3
	.word	131441
	.byte	17,17,91,9,1,18
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,18
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,28
	.byte	'IfxAsclin_ChecksumInjection',0,17,95,3
	.word	131533
	.byte	17,17,101,9,1,18
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,18
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,28
	.byte	'IfxAsclin_ClockPolarity',0,17,105,3
	.word	131654
	.byte	17,17,110,9,1,18
	.byte	'IfxAsclin_ClockSource_noClock',0,0,18
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,18
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,18
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,18
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,18
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,28
	.byte	'IfxAsclin_ClockSource',0,17,118,3
	.word	131761
	.byte	28
	.byte	'IfxAsclin_CtsInputSelect',0,17,129,1,3
	.word	32005
	.byte	17,17,134,1,9,1,18
	.byte	'IfxAsclin_DataLength_1',0,0,18
	.byte	'IfxAsclin_DataLength_2',0,1,18
	.byte	'IfxAsclin_DataLength_3',0,2,18
	.byte	'IfxAsclin_DataLength_4',0,3,18
	.byte	'IfxAsclin_DataLength_5',0,4,18
	.byte	'IfxAsclin_DataLength_6',0,5,18
	.byte	'IfxAsclin_DataLength_7',0,6,18
	.byte	'IfxAsclin_DataLength_8',0,7,18
	.byte	'IfxAsclin_DataLength_9',0,8,18
	.byte	'IfxAsclin_DataLength_10',0,9,18
	.byte	'IfxAsclin_DataLength_11',0,10,18
	.byte	'IfxAsclin_DataLength_12',0,11,18
	.byte	'IfxAsclin_DataLength_13',0,12,18
	.byte	'IfxAsclin_DataLength_14',0,13,18
	.byte	'IfxAsclin_DataLength_15',0,14,18
	.byte	'IfxAsclin_DataLength_16',0,15,0,28
	.byte	'IfxAsclin_DataLength',0,17,152,1,3
	.word	132050
	.byte	17,17,157,1,9,1,18
	.byte	'IfxAsclin_FrameMode_initialise',0,0,18
	.byte	'IfxAsclin_FrameMode_asc',0,1,18
	.byte	'IfxAsclin_FrameMode_spi',0,2,18
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,28
	.byte	'IfxAsclin_FrameMode',0,17,163,1,3
	.word	132494
	.byte	17,17,168,1,9,1,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,28
	.byte	'IfxAsclin_HeaderResponseSelect',0,17,172,1,3
	.word	132641
	.byte	17,17,179,1,9,1,18
	.byte	'IfxAsclin_IdleDelay_0',0,0,18
	.byte	'IfxAsclin_IdleDelay_1',0,1,18
	.byte	'IfxAsclin_IdleDelay_2',0,2,18
	.byte	'IfxAsclin_IdleDelay_3',0,3,18
	.byte	'IfxAsclin_IdleDelay_4',0,4,18
	.byte	'IfxAsclin_IdleDelay_5',0,5,18
	.byte	'IfxAsclin_IdleDelay_6',0,6,18
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,28
	.byte	'IfxAsclin_IdleDelay',0,17,189,1,3
	.word	132783
	.byte	17,17,195,1,9,1,18
	.byte	'IfxAsclin_LeadDelay_0',0,0,18
	.byte	'IfxAsclin_LeadDelay_1',0,1,18
	.byte	'IfxAsclin_LeadDelay_2',0,2,18
	.byte	'IfxAsclin_LeadDelay_3',0,3,18
	.byte	'IfxAsclin_LeadDelay_4',0,4,18
	.byte	'IfxAsclin_LeadDelay_5',0,5,18
	.byte	'IfxAsclin_LeadDelay_6',0,6,18
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,28
	.byte	'IfxAsclin_LeadDelay',0,17,205,1,3
	.word	133011
	.byte	17,17,210,1,9,1,18
	.byte	'IfxAsclin_LinMode_slave',0,0,18
	.byte	'IfxAsclin_LinMode_master',0,1,0,28
	.byte	'IfxAsclin_LinMode',0,17,214,1,3
	.word	133239
	.byte	17,17,219,1,9,1,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,28
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,17,223,1,3
	.word	133326
	.byte	17,17,228,1,9,1,18
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,18
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,18
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,18
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,18
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,18
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,18
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,18
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,18
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,18
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,18
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,18
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,18
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,28
	.byte	'IfxAsclin_OversamplingFactor',0,17,243,1,3
	.word	133474
	.byte	17,17,248,1,9,1,18
	.byte	'IfxAsclin_ParityType_even',0,0,18
	.byte	'IfxAsclin_ParityType_odd',0,1,0,28
	.byte	'IfxAsclin_ParityType',0,17,252,1,3
	.word	133955
	.byte	17,17,129,2,9,1,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,28
	.byte	'IfxAsclin_ReceiveBufferMode',0,17,133,2,3
	.word	134047
	.byte	17,17,138,2,9,1,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,28
	.byte	'IfxAsclin_RtsCtsPolarity',0,17,142,2,3
	.word	134167
	.byte	17,17,147,2,9,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,28
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,17,165,2,3
	.word	134283
	.byte	17,17,170,2,9,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,18
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,18
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,28
	.byte	'IfxAsclin_RxFifoOutletWidth',0,17,176,2,3
	.word	134897
	.byte	28
	.byte	'IfxAsclin_RxInputSelect',0,17,191,2,3
	.word	32189
	.byte	17,17,196,2,9,1,18
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,18
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,18
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,18
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,18
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,18
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,18
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,18
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,18
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,18
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,18
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,18
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,18
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,18
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,18
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,28
	.byte	'IfxAsclin_SamplePointPosition',0,17,213,2,3
	.word	135102
	.byte	17,17,218,2,9,1,18
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,18
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,28
	.byte	'IfxAsclin_SamplesPerBit',0,17,222,2,3
	.word	135664
	.byte	17,17,228,2,9,1,18
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,18
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,28
	.byte	'IfxAsclin_ShiftDirection',0,17,232,2,3
	.word	135766
	.byte	17,17,238,2,9,1,18
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,18
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,28
	.byte	'IfxAsclin_SlavePolarity',0,17,242,2,3
	.word	135879
	.byte	17,17,247,2,9,1,18
	.byte	'IfxAsclin_SleepMode_enable',0,0,18
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,28
	.byte	'IfxAsclin_SleepMode',0,17,251,2,3
	.word	135988
	.byte	17,17,136,3,9,1,18
	.byte	'IfxAsclin_StopBit_0',0,0,18
	.byte	'IfxAsclin_StopBit_1',0,1,18
	.byte	'IfxAsclin_StopBit_2',0,2,18
	.byte	'IfxAsclin_StopBit_3',0,3,18
	.byte	'IfxAsclin_StopBit_4',0,4,18
	.byte	'IfxAsclin_StopBit_5',0,5,18
	.byte	'IfxAsclin_StopBit_6',0,6,18
	.byte	'IfxAsclin_StopBit_7',0,7,0,28
	.byte	'IfxAsclin_StopBit',0,17,146,3,3
	.word	136083
	.byte	17,17,150,3,9,1,18
	.byte	'IfxAsclin_SuspendMode_none',0,0,18
	.byte	'IfxAsclin_SuspendMode_hard',0,1,18
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,28
	.byte	'IfxAsclin_SuspendMode',0,17,155,3,3
	.word	136293
	.byte	17,17,160,3,9,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,18
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,18
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,28
	.byte	'IfxAsclin_TxFifoInletWidth',0,17,166,3,3
	.word	136418
	.byte	17,17,171,3,9,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,28
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,17,189,3,3
	.word	136585
	.byte	28
	.byte	'Ifx_Fifo_Shared',0,19,66,3
	.word	32506
	.byte	28
	.byte	'Ifx_Fifo',0,19,83,3
	.word	32597
	.byte	28
	.byte	'IfxStdIf_InterfaceDriver',0,42,118,15
	.word	360
	.byte	3
	.word	32481
	.byte	33
	.word	621
	.byte	1,1,32
	.word	360
	.byte	32
	.word	360
	.byte	32
	.word	137272
	.byte	32
	.word	14761
	.byte	0,3
	.word	137277
	.byte	28
	.byte	'IfxStdIf_DPipe_Write',0,43,92,19
	.word	137305
	.byte	28
	.byte	'IfxStdIf_DPipe_Read',0,43,107,19
	.word	137305
	.byte	33
	.word	32494
	.byte	1,1,32
	.word	360
	.byte	0,3
	.word	137367
	.byte	28
	.byte	'IfxStdIf_DPipe_GetReadCount',0,43,115,18
	.word	137380
	.byte	14
	.word	621
	.byte	3
	.word	137421
	.byte	33
	.word	137426
	.byte	1,1,32
	.word	360
	.byte	0,3
	.word	137431
	.byte	28
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,43,123,36
	.word	137444
	.byte	28
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,43,147,1,18
	.word	137380
	.byte	3
	.word	137431
	.byte	28
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,43,155,1,37
	.word	137523
	.byte	33
	.word	621
	.byte	1,1,32
	.word	360
	.byte	32
	.word	32481
	.byte	32
	.word	14761
	.byte	0,3
	.word	137566
	.byte	28
	.byte	'IfxStdIf_DPipe_CanReadCount',0,43,166,1,19
	.word	137589
	.byte	28
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,43,177,1,19
	.word	137589
	.byte	33
	.word	621
	.byte	1,1,32
	.word	360
	.byte	32
	.word	14761
	.byte	0,3
	.word	137669
	.byte	28
	.byte	'IfxStdIf_DPipe_FlushTx',0,43,186,1,19
	.word	137687
	.byte	31,1,1,32
	.word	360
	.byte	0,3
	.word	137724
	.byte	28
	.byte	'IfxStdIf_DPipe_ClearTx',0,43,200,1,16
	.word	137733
	.byte	28
	.byte	'IfxStdIf_DPipe_ClearRx',0,43,193,1,16
	.word	137733
	.byte	28
	.byte	'IfxStdIf_DPipe_OnReceive',0,43,208,1,16
	.word	137733
	.byte	28
	.byte	'IfxStdIf_DPipe_OnTransmit',0,43,215,1,16
	.word	137733
	.byte	28
	.byte	'IfxStdIf_DPipe_OnError',0,43,222,1,16
	.word	137733
	.byte	33
	.word	10502
	.byte	1,1,32
	.word	360
	.byte	0,3
	.word	137903
	.byte	28
	.byte	'IfxStdIf_DPipe_GetSendCount',0,43,131,1,18
	.word	137916
	.byte	33
	.word	14761
	.byte	1,1,32
	.word	360
	.byte	0,3
	.word	137958
	.byte	28
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,43,139,1,24
	.word	137971
	.byte	28
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,43,229,1,16
	.word	137733
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,43,233,1,8,76,13
	.byte	'driver',0
	.word	137239
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	621
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	137310
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	137339
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	137385
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	137449
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	137485
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	137528
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	137594
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	137631
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	137692
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	137738
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	137770
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	137802
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	137836
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	137871
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	137921
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	137976
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	138015
	.byte	4,2,35,72,0,28
	.byte	'IfxStdIf_DPipe',0,43,71,32
	.word	138054
	.byte	3
	.word	354
	.byte	3
	.word	137277
	.byte	3
	.word	137277
	.byte	3
	.word	137367
	.byte	3
	.word	137431
	.byte	3
	.word	137367
	.byte	3
	.word	137431
	.byte	3
	.word	137566
	.byte	3
	.word	137566
	.byte	3
	.word	137669
	.byte	3
	.word	137724
	.byte	3
	.word	137724
	.byte	3
	.word	137724
	.byte	3
	.word	137724
	.byte	3
	.word	137724
	.byte	3
	.word	137903
	.byte	3
	.word	137958
	.byte	3
	.word	137724
	.byte	14
	.word	621
	.byte	3
	.word	138567
	.byte	28
	.byte	'IfxStdIf_DPipe_WriteEvent',0,43,73,32
	.word	138572
	.byte	28
	.byte	'IfxStdIf_DPipe_ReadEvent',0,43,74,32
	.word	138572
	.byte	20,44,252,1,9,1,11
	.byte	'parityError',0,1
	.word	621
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	621
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	621
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	621
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	621
	.byte	1,3,2,35,0,0,28
	.byte	'IfxAsclin_Asc_ErrorFlags',0,44,131,2,3
	.word	138644
	.byte	20,44,137,2,9,8,13
	.byte	'baudrate',0
	.word	242
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	638
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	133474
	.byte	1,2,35,6,0,28
	.byte	'IfxAsclin_Asc_BaudRate',0,44,142,2,3
	.word	138809
	.byte	20,44,146,2,9,2,13
	.byte	'medianFilter',0
	.word	135664
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	135102
	.byte	1,2,35,1,0,28
	.byte	'IfxAsclin_Asc_BitTimingControl',0,44,150,2,3
	.word	138907
	.byte	20,44,154,2,9,6,13
	.byte	'inWidth',0
	.word	136418
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	134897
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	136585
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	134283
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	134047
	.byte	1,2,35,4,0,28
	.byte	'IfxAsclin_Asc_FifoControl',0,44,161,2,3
	.word	139005
	.byte	20,44,165,2,9,8,13
	.byte	'idleDelay',0
	.word	132783
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	136083
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	132494
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	135766
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	133955
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	132050
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	621
	.byte	1,2,35,6,0,28
	.byte	'IfxAsclin_Asc_FrameControl',0,44,174,2,3
	.word	139160
	.byte	20,44,178,2,9,8,13
	.byte	'txPriority',0
	.word	638
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	638
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	638
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	99449
	.byte	1,2,35,6,0,28
	.byte	'IfxAsclin_Asc_InterruptConfig',0,44,184,2,3
	.word	139335
	.byte	30
	.word	130951
	.byte	3
	.word	139464
	.byte	30
	.word	131032
	.byte	3
	.word	139474
	.byte	30
	.word	131112
	.byte	3
	.word	139484
	.byte	30
	.word	131360
	.byte	3
	.word	139494
	.byte	20,44,188,2,9,32,13
	.byte	'cts',0
	.word	139469
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9371
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	139479
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9371
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	139489
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9576
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	139499
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9576
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	97864
	.byte	1,2,35,29,0,28
	.byte	'IfxAsclin_Asc_Pins',0,44,199,2,3
	.word	139504
	.byte	12,44,205,2,9,1,13
	.byte	'ALL',0
	.word	621
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	138644
	.byte	1,2,35,0,0,28
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,44,209,2,3
	.word	139674
	.byte	20,45,69,9,8,13
	.byte	'year',0
	.word	638
	.byte	2,2,35,0,13
	.byte	'month',0
	.word	621
	.byte	1,2,35,2,13
	.byte	'day',0
	.word	621
	.byte	1,2,35,3,13
	.byte	'hour',0
	.word	621
	.byte	1,2,35,4,13
	.byte	'minute',0
	.word	621
	.byte	1,2,35,5,13
	.byte	'second',0
	.word	621
	.byte	1,2,35,6,0,28
	.byte	'gps_time_struct',0,45,77,2
	.word	139748
	.byte	17,46,83,9,1,18
	.byte	'WIFI_UART_STATION',0,0,18
	.byte	'WIFI_UART_SOFTAP',0,1,0,28
	.byte	'wifi_uart_mode_enum',0,46,87,2
	.word	139866
	.byte	17,46,89,9,1,18
	.byte	'WIFI_UART_COMMAND',0,0,18
	.byte	'WIFI_UART_SERIANET',0,1,0,28
	.byte	'wifi_uart_transfer_mode_enum',0,46,93,2
	.word	139939
	.byte	17,46,95,9,1,18
	.byte	'WIFI_UART_TCP_CLIENT',0,0,18
	.byte	'WIFI_UART_TCP_SERVER',0,1,18
	.byte	'WIFI_UART_UDP_CLIENT',0,2,0,28
	.byte	'wifi_uart_connect_mode_enum',0,46,100,2
	.word	140023
	.byte	17,46,102,9,1,18
	.byte	'WIFI_UART_SERVER_OFF',0,0,18
	.byte	'WIFI_UART_SERVER_ON',0,1,0,28
	.byte	'wifi_uart_connect_state_enum',0,46,106,2
	.word	140134
	.byte	20,47,123,9,4,13
	.byte	'command',0
	.word	621
	.byte	1,2,35,0,13
	.byte	'reserve',0
	.word	621
	.byte	1,2,35,1,13
	.byte	'length',0
	.word	638
	.byte	2,2,35,2,0,28
	.byte	'wifi_spi_head_struct',0,47,128,1,2
	.word	140222
	.byte	17,48,75,9,1,18
	.byte	'SEEKFREE_ASSISTANT_BINARY',0,1,18
	.byte	'SEEKFREE_ASSISTANT_OV7725_BIN',0,1,18
	.byte	'SEEKFREE_ASSISTANT_GRAY',0,2,18
	.byte	'SEEKFREE_ASSISTANT_MT9V03X',0,2,18
	.byte	'SEEKFREE_ASSISTANT_RGB565',0,3,18
	.byte	'SEEKFREE_ASSISTANT_SCC8660',0,3,0,28
	.byte	'seekfree_assistant_image_type_enum',0,48,86,2
	.word	140308
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L8:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,49,19
	.byte	0,0,22,5,0,49,19,0,0,23,29,1,49,19,0,0,24,11,0,49,19,0,0,25,11,1,49,19,0,0,26,46,0,3,8,58,15,59,15,57
	.byte	15,54,15,39,12,63,12,60,12,0,0,27,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,28,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,29,21,0,54,15,0,0,30,38,0,73,19,0,0,31,21,1,54,15,39,12,0,0,32,5,0,73,19,0,0
	.byte	33,21,1,73,19,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L9:
	.word	.L17-.L16
.L16:
	.half	3
	.word	.L19-.L18
.L18:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Ccu6\\Std\\IfxCcu6.h',0
	.byte	0,0,0
	.byte	'IfxCcu6_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'zf_common_clock.h',0,2,0,0
	.byte	'zf_common_interrupt.h',0,2,0,0
	.byte	'../user/cpu1_main.c',0,0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxCcu6_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\If\\Ccu6If\\Timer.h',0
	.byte	0,0,0
	.byte	'IfxCcu6_Timer.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,2,0,0
	.byte	'zf_common_fifo.h',0,2,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxDma_cfg.h',0
	.byte	0,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'IfxDma.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,6,0,0
	.byte	'zf_driver_soft_spi.h',0,6,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,7,0,0
	.byte	'zf_device_gnss.h',0,8,0,0
	.byte	'zf_device_wifi_uart.h',0,8,0,0
	.byte	'zf_device_wifi_spi.h',0,8,0,0
	.byte	'seekfree_assistant.h',0,9,0,0,0
.L19:
.L17:
	.sdecl	'.debug_info',debug,cluster('core1_main')
	.sect	'.debug_info'
.L10:
	.word	228
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../user/cpu1_main.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L13,.L12
	.byte	2
	.word	.L6
	.byte	3
	.byte	'core1_main',0,1,46,6,1,1,1
	.word	.L5,.L15,.L4
	.byte	4
	.word	.L5,.L15
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('core1_main')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('core1_main')
	.sect	'.debug_line'
.L12:
	.word	.L21-.L20
.L20:
	.half	3
	.word	.L23-.L22
.L22:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../user/cpu1_main.c',0,0,0,0,0
.L23:
	.byte	5,21,7,0,5,2
	.word	.L5
	.byte	3,47,1,5,29,9
	.half	.L24-.L5
	.byte	3,1,1,5,25,9
	.half	.L25-.L24
	.byte	3,6,1,5,16,9
	.half	.L26-.L25
	.byte	3,1,1,5,1,9
	.half	.L27-.L26
	.byte	3,8,1,7,9
	.half	.L14-.L27
	.byte	0,1,1
.L21:
	.sdecl	'.debug_ranges',debug,cluster('core1_main')
	.sect	'.debug_ranges'
.L13:
	.word	-1,.L5,0,.L14-.L5,0,0
	.sdecl	'.debug_loc',debug,cluster('core1_main')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L15-.L5
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L28:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('core1_main')
	.sect	'.debug_frame'
	.word	12
	.word	.L28,.L5,.L15-.L5
	; Module end
