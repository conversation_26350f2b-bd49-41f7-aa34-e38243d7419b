/**
 * \file IfxEth_Phy_Pef7071.h
 * \brief ETH PHY_PEF7071 details
 * \ingroup IfxLld_Eth
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2019 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup IfxLld_Eth_Phy_Pef7071 PHY_PEF7071
 * \ingroup IfxLld_Eth
 * \defgroup IfxLld_Eth_Phy_Pef7071_Functions Functions
 * \ingroup IfxLld_Eth_Phy_Pef7071
 */

#ifndef IFXETH_PHY_PEF7071_H
#define IFXETH_PHY_PEF7071_H 1

/******************************************************************************/
/*----------------------------------Includes----------------------------------*/
/******************************************************************************/

#include "Eth/Std/IfxEth.h"
/** \addtogroup IfxLld_Eth_Phy_Pef7071_Functions
 * \{ */

/******************************************************************************/
/*-------------------------Global Function Prototypes-------------------------*/
/******************************************************************************/

/**
 * \return Status
 */
IFX_EXTERN uint32 IfxEth_Phy_Pef7071_init(void);

/**
 * \return Link status
 */
IFX_EXTERN boolean IfxEth_Phy_Pef7071_link(void);

/**
 * \return None
 */
IFX_EXTERN void IfxEth_Phy_Pef7071_read_mdio_reg(uint32 layeraddr, uint32 regaddr, uint32 *pdata);

/**
 * \return None
 */
IFX_EXTERN void IfxEth_Phy_Pef7071_write_mdio_reg(uint32 layeraddr, uint32 regaddr, uint32 data);

/** \brief Write the data into the MMD register of PHY
 * \param layeraddr physical layer address
 * \param devaddr Device address in PHY
 * \param regaddr Register address in PHY
 * \param data Data to be written
 * \return None
 */
IFX_EXTERN void IfxEth_Phy_Pef7071_write_mmd_indirect(uint32 layeraddr, uint32 devaddr, uint32 regaddr, uint32 data);

/** \brief Read data from MMD register of PHY
 * \param layeraddr physical layer address
 * \param devaddr Device address in PHY
 * \param regaddr Register address in PHY
 * \param data Data from PHY register
 * \return None
 */
IFX_EXTERN void IfxEth_Phy_Pef7071_read_mmd_indirect(uint32 layeraddr, uint32 devaddr, uint32 regaddr, uint32 data);

/** \} */

/******************************************************************************/
/*-------------------Global Exported Variables/Constants----------------------*/
/******************************************************************************/

IFX_EXTERN uint32 IfxEth_Phy_Pef7071_iPhyInitDone;

#endif /* IFXETH_PHY_PEF7071_H */
