<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxCcu6" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Port/Std/IfxPort.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxPort_cfg.c</iLLD:file>
  <iLLD:file class="mchal">Ccu6/Icu/IfxCcu6_Icu.c</iLLD:file>
  <iLLD:file class="mchal">Ccu6/PwmBc/IfxCcu6_PwmBc.c</iLLD:file>
  <iLLD:file class="mchal">Ccu6/Std/IfxCcu6.c</iLLD:file>
  <iLLD:file class="mchal">Ccu6/TPwm/IfxCcu6_TPwm.c</iLLD:file>
  <iLLD:file class="mchal">Ccu6/Timer/IfxCcu6_Timer.c</iLLD:file>
  <iLLD:file class="mchal">_PinMap/IfxCcu6_PinMap.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxCcu6_cfg.c</iLLD:file>
</iLLD:filelist>
