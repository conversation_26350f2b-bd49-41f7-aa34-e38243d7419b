/**
 * \file IfxXbar_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Xbar_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Xbar
 * 
 */
#ifndef IFXXBAR_BF_H
#define IFXXBAR_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Xbar_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN0 */
#define IFX_XBAR_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN0 */
#define IFX_XBAR_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN0 */
#define IFX_XBAR_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN10 */
#define IFX_XBAR_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN10 */
#define IFX_XBAR_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN10 */
#define IFX_XBAR_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN11 */
#define IFX_XBAR_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN11 */
#define IFX_XBAR_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN11 */
#define IFX_XBAR_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN12 */
#define IFX_XBAR_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN12 */
#define IFX_XBAR_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN12 */
#define IFX_XBAR_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN13 */
#define IFX_XBAR_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN13 */
#define IFX_XBAR_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN13 */
#define IFX_XBAR_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN14 */
#define IFX_XBAR_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN14 */
#define IFX_XBAR_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN14 */
#define IFX_XBAR_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN15 */
#define IFX_XBAR_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN15 */
#define IFX_XBAR_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN15 */
#define IFX_XBAR_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN16 */
#define IFX_XBAR_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN16 */
#define IFX_XBAR_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN16 */
#define IFX_XBAR_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN17 */
#define IFX_XBAR_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN17 */
#define IFX_XBAR_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN17 */
#define IFX_XBAR_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN18 */
#define IFX_XBAR_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN18 */
#define IFX_XBAR_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN18 */
#define IFX_XBAR_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN19 */
#define IFX_XBAR_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN19 */
#define IFX_XBAR_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN19 */
#define IFX_XBAR_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN1 */
#define IFX_XBAR_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN1 */
#define IFX_XBAR_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN1 */
#define IFX_XBAR_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN20 */
#define IFX_XBAR_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN20 */
#define IFX_XBAR_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN20 */
#define IFX_XBAR_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN21 */
#define IFX_XBAR_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN21 */
#define IFX_XBAR_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN21 */
#define IFX_XBAR_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN22 */
#define IFX_XBAR_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN22 */
#define IFX_XBAR_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN22 */
#define IFX_XBAR_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN23 */
#define IFX_XBAR_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN23 */
#define IFX_XBAR_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN23 */
#define IFX_XBAR_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN24 */
#define IFX_XBAR_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN24 */
#define IFX_XBAR_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN24 */
#define IFX_XBAR_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN25 */
#define IFX_XBAR_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN25 */
#define IFX_XBAR_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN25 */
#define IFX_XBAR_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN26 */
#define IFX_XBAR_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN26 */
#define IFX_XBAR_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN26 */
#define IFX_XBAR_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN27 */
#define IFX_XBAR_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN27 */
#define IFX_XBAR_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN27 */
#define IFX_XBAR_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN28 */
#define IFX_XBAR_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN28 */
#define IFX_XBAR_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN28 */
#define IFX_XBAR_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN29 */
#define IFX_XBAR_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN29 */
#define IFX_XBAR_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN29 */
#define IFX_XBAR_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN2 */
#define IFX_XBAR_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN2 */
#define IFX_XBAR_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN2 */
#define IFX_XBAR_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN30 */
#define IFX_XBAR_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN30 */
#define IFX_XBAR_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN30 */
#define IFX_XBAR_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN31 */
#define IFX_XBAR_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN31 */
#define IFX_XBAR_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN31 */
#define IFX_XBAR_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN3 */
#define IFX_XBAR_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN3 */
#define IFX_XBAR_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN3 */
#define IFX_XBAR_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN4 */
#define IFX_XBAR_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN4 */
#define IFX_XBAR_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN4 */
#define IFX_XBAR_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN5 */
#define IFX_XBAR_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN5 */
#define IFX_XBAR_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN5 */
#define IFX_XBAR_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN6 */
#define IFX_XBAR_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN6 */
#define IFX_XBAR_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN6 */
#define IFX_XBAR_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN7 */
#define IFX_XBAR_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN7 */
#define IFX_XBAR_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN7 */
#define IFX_XBAR_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN8 */
#define IFX_XBAR_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN8 */
#define IFX_XBAR_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN8 */
#define IFX_XBAR_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_XBAR_ACCEN0_Bits.EN9 */
#define IFX_XBAR_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ACCEN0_Bits.EN9 */
#define IFX_XBAR_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ACCEN0_Bits.EN9 */
#define IFX_XBAR_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_XBAR_ARBCON_Bits.INTACK */
#define IFX_XBAR_ARBCON_INTACK_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBCON_Bits.INTACK */
#define IFX_XBAR_ARBCON_INTACK_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBCON_Bits.INTACK */
#define IFX_XBAR_ARBCON_INTACK_OFF (4u)

/** \brief  Length for Ifx_XBAR_ARBCON_Bits.PRERREN */
#define IFX_XBAR_ARBCON_PRERREN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBCON_Bits.PRERREN */
#define IFX_XBAR_ARBCON_PRERREN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBCON_Bits.PRERREN */
#define IFX_XBAR_ARBCON_PRERREN_OFF (0u)

/** \brief  Length for Ifx_XBAR_ARBCON_Bits.SCERREN */
#define IFX_XBAR_ARBCON_SCERREN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBCON_Bits.SCERREN */
#define IFX_XBAR_ARBCON_SCERREN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBCON_Bits.SCERREN */
#define IFX_XBAR_ARBCON_SCERREN_OFF (1u)

/** \brief  Length for Ifx_XBAR_ARBCON_Bits.SETPRINT */
#define IFX_XBAR_ARBCON_SETPRINT_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBCON_Bits.SETPRINT */
#define IFX_XBAR_ARBCON_SETPRINT_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBCON_Bits.SETPRINT */
#define IFX_XBAR_ARBCON_SETPRINT_OFF (2u)

/** \brief  Length for Ifx_XBAR_ARBCON_Bits.SETSCINT */
#define IFX_XBAR_ARBCON_SETSCINT_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBCON_Bits.SETSCINT */
#define IFX_XBAR_ARBCON_SETSCINT_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBCON_Bits.SETSCINT */
#define IFX_XBAR_ARBCON_SETSCINT_OFF (3u)

/** \brief  Length for Ifx_XBAR_ARBCON_Bits.SPC */
#define IFX_XBAR_ARBCON_SPC_LEN (12u)

/** \brief  Mask for Ifx_XBAR_ARBCON_Bits.SPC */
#define IFX_XBAR_ARBCON_SPC_MSK (0xfffu)

/** \brief  Offset for Ifx_XBAR_ARBCON_Bits.SPC */
#define IFX_XBAR_ARBCON_SPC_OFF (20u)

/** \brief  Length for Ifx_XBAR_ARBITER0_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER0_DBADD_ADDRESS_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBITER0_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER0_DBADD_ADDRESS_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBITER0_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER0_DBADD_ADDRESS_OFF (27u)

/** \brief  Length for Ifx_XBAR_ARBITER0_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER0_DBMADD_ADDRESS_LEN (19u)

/** \brief  Mask for Ifx_XBAR_ARBITER0_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER0_DBMADD_ADDRESS_MSK (0x7ffffu)

/** \brief  Offset for Ifx_XBAR_ARBITER0_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER0_DBMADD_ADDRESS_OFF (2u)

/** \brief  Length for Ifx_XBAR_ARBITER1_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER1_DBADD_ADDRESS_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBITER1_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER1_DBADD_ADDRESS_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBITER1_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER1_DBADD_ADDRESS_OFF (31u)

/** \brief  Length for Ifx_XBAR_ARBITER1_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER1_DBMADD_ADDRESS_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBITER1_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER1_DBMADD_ADDRESS_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBITER1_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER1_DBMADD_ADDRESS_OFF (31u)

/** \brief  Length for Ifx_XBAR_ARBITER4_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER4_DBADD_ADDRESS_LEN (29u)

/** \brief  Mask for Ifx_XBAR_ARBITER4_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER4_DBADD_ADDRESS_MSK (0x1fffffffu)

/** \brief  Offset for Ifx_XBAR_ARBITER4_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER4_DBADD_ADDRESS_OFF (2u)

/** \brief  Length for Ifx_XBAR_ARBITER4_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER4_DBMADD_ADDRESS_LEN (29u)

/** \brief  Mask for Ifx_XBAR_ARBITER4_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER4_DBMADD_ADDRESS_MSK (0x1fffffffu)

/** \brief  Offset for Ifx_XBAR_ARBITER4_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER4_DBMADD_ADDRESS_OFF (2u)

/** \brief  Length for Ifx_XBAR_ARBITER6_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER6_DBADD_ADDRESS_LEN (3u)

/** \brief  Mask for Ifx_XBAR_ARBITER6_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER6_DBADD_ADDRESS_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_ARBITER6_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER6_DBADD_ADDRESS_OFF (28u)

/** \brief  Length for Ifx_XBAR_ARBITER6_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER6_DBMADD_ADDRESS_LEN (3u)

/** \brief  Mask for Ifx_XBAR_ARBITER6_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER6_DBMADD_ADDRESS_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_ARBITER6_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER6_DBMADD_ADDRESS_OFF (28u)

/** \brief  Length for Ifx_XBAR_ARBITER7_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER7_DBADD_ADDRESS_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBITER7_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER7_DBADD_ADDRESS_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBITER7_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER7_DBADD_ADDRESS_OFF (29u)

/** \brief  Length for Ifx_XBAR_ARBITER7_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER7_DBMADD_ADDRESS_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBITER7_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER7_DBMADD_ADDRESS_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBITER7_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER7_DBMADD_ADDRESS_OFF (29u)

/** \brief  Length for Ifx_XBAR_ARBITER8_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER8_DBADD_ADDRESS_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBITER8_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER8_DBADD_ADDRESS_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBITER8_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER8_DBADD_ADDRESS_OFF (29u)

/** \brief  Length for Ifx_XBAR_ARBITER8_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER8_DBMADD_ADDRESS_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ARBITER8_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER8_DBMADD_ADDRESS_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ARBITER8_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITER8_DBMADD_ADDRESS_OFF (29u)

/** \brief  Length for Ifx_XBAR_ARBITERD_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITERD_DBADD_ADDRESS_LEN (30u)

/** \brief  Mask for Ifx_XBAR_ARBITERD_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITERD_DBADD_ADDRESS_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_XBAR_ARBITERD_DBADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITERD_DBADD_ADDRESS_OFF (2u)

/** \brief  Length for Ifx_XBAR_ARBITERD_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITERD_DBMADD_ADDRESS_LEN (32u)

/** \brief  Mask for Ifx_XBAR_ARBITERD_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITERD_DBMADD_ADDRESS_MSK (0xffffffffu)

/** \brief  Offset for Ifx_XBAR_ARBITERD_DBMADD_Bits.ADDRESS */
#define IFX_XBAR_ARBITERD_DBMADD_ADDRESS_OFF (0u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.ADDEN */
#define IFX_XBAR_DBCON_ADDEN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.ADDEN */
#define IFX_XBAR_DBCON_ADDEN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.ADDEN */
#define IFX_XBAR_DBCON_ADDEN_OFF (19u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.DBEN */
#define IFX_XBAR_DBCON_DBEN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.DBEN */
#define IFX_XBAR_DBCON_DBEN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.DBEN */
#define IFX_XBAR_DBCON_DBEN_OFF (0u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.DBSAT */
#define IFX_XBAR_DBCON_DBSAT_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.DBSAT */
#define IFX_XBAR_DBCON_DBSAT_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.DBSAT */
#define IFX_XBAR_DBCON_DBSAT_OFF (1u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.ERREN */
#define IFX_XBAR_DBCON_ERREN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.ERREN */
#define IFX_XBAR_DBCON_ERREN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.ERREN */
#define IFX_XBAR_DBCON_ERREN_OFF (20u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.MASEN */
#define IFX_XBAR_DBCON_MASEN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.MASEN */
#define IFX_XBAR_DBCON_MASEN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.MASEN */
#define IFX_XBAR_DBCON_MASEN_OFF (23u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.MASTER */
#define IFX_XBAR_DBCON_MASTER_LEN (6u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.MASTER */
#define IFX_XBAR_DBCON_MASTER_MSK (0x3fu)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.MASTER */
#define IFX_XBAR_DBCON_MASTER_OFF (24u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.RDEN */
#define IFX_XBAR_DBCON_RDEN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.RDEN */
#define IFX_XBAR_DBCON_RDEN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.RDEN */
#define IFX_XBAR_DBCON_RDEN_OFF (16u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.REARM */
#define IFX_XBAR_DBCON_REARM_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.REARM */
#define IFX_XBAR_DBCON_REARM_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.REARM */
#define IFX_XBAR_DBCON_REARM_OFF (2u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.SETDBEVT */
#define IFX_XBAR_DBCON_SETDBEVT_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.SETDBEVT */
#define IFX_XBAR_DBCON_SETDBEVT_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.SETDBEVT */
#define IFX_XBAR_DBCON_SETDBEVT_OFF (3u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.SVMEN */
#define IFX_XBAR_DBCON_SVMEN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.SVMEN */
#define IFX_XBAR_DBCON_SVMEN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.SVMEN */
#define IFX_XBAR_DBCON_SVMEN_OFF (18u)

/** \brief  Length for Ifx_XBAR_DBCON_Bits.WREN */
#define IFX_XBAR_DBCON_WREN_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBCON_Bits.WREN */
#define IFX_XBAR_DBCON_WREN_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBCON_Bits.WREN */
#define IFX_XBAR_DBCON_WREN_OFF (17u)

/** \brief  Length for Ifx_XBAR_DBSAT_Bits.SCI0 */
#define IFX_XBAR_DBSAT_SCI0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBSAT_Bits.SCI0 */
#define IFX_XBAR_DBSAT_SCI0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBSAT_Bits.SCI0 */
#define IFX_XBAR_DBSAT_SCI0_OFF (0u)

/** \brief  Length for Ifx_XBAR_DBSAT_Bits.SCI1 */
#define IFX_XBAR_DBSAT_SCI1_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBSAT_Bits.SCI1 */
#define IFX_XBAR_DBSAT_SCI1_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBSAT_Bits.SCI1 */
#define IFX_XBAR_DBSAT_SCI1_OFF (1u)

/** \brief  Length for Ifx_XBAR_DBSAT_Bits.SCI4 */
#define IFX_XBAR_DBSAT_SCI4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBSAT_Bits.SCI4 */
#define IFX_XBAR_DBSAT_SCI4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBSAT_Bits.SCI4 */
#define IFX_XBAR_DBSAT_SCI4_OFF (4u)

/** \brief  Length for Ifx_XBAR_DBSAT_Bits.SCI6 */
#define IFX_XBAR_DBSAT_SCI6_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBSAT_Bits.SCI6 */
#define IFX_XBAR_DBSAT_SCI6_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBSAT_Bits.SCI6 */
#define IFX_XBAR_DBSAT_SCI6_OFF (6u)

/** \brief  Length for Ifx_XBAR_DBSAT_Bits.SCI7 */
#define IFX_XBAR_DBSAT_SCI7_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBSAT_Bits.SCI7 */
#define IFX_XBAR_DBSAT_SCI7_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBSAT_Bits.SCI7 */
#define IFX_XBAR_DBSAT_SCI7_OFF (7u)

/** \brief  Length for Ifx_XBAR_DBSAT_Bits.SCI8 */
#define IFX_XBAR_DBSAT_SCI8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBSAT_Bits.SCI8 */
#define IFX_XBAR_DBSAT_SCI8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBSAT_Bits.SCI8 */
#define IFX_XBAR_DBSAT_SCI8_OFF (8u)

/** \brief  Length for Ifx_XBAR_DBSAT_Bits.SCID */
#define IFX_XBAR_DBSAT_SCID_LEN (1u)

/** \brief  Mask for Ifx_XBAR_DBSAT_Bits.SCID */
#define IFX_XBAR_DBSAT_SCID_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_DBSAT_Bits.SCID */
#define IFX_XBAR_DBSAT_SCID_OFF (15u)

/** \brief  Length for Ifx_XBAR_ERR_Bits.ADDR_ECC */
#define IFX_XBAR_ERR_ADDR_ECC_LEN (8u)

/** \brief  Mask for Ifx_XBAR_ERR_Bits.ADDR_ECC */
#define IFX_XBAR_ERR_ADDR_ECC_MSK (0xffu)

/** \brief  Offset for Ifx_XBAR_ERR_Bits.ADDR_ECC */
#define IFX_XBAR_ERR_ADDR_ECC_OFF (16u)

/** \brief  Length for Ifx_XBAR_ERR_Bits.MCI_SBS */
#define IFX_XBAR_ERR_MCI_SBS_LEN (8u)

/** \brief  Mask for Ifx_XBAR_ERR_Bits.MCI_SBS */
#define IFX_XBAR_ERR_MCI_SBS_MSK (0xffu)

/** \brief  Offset for Ifx_XBAR_ERR_Bits.MCI_SBS */
#define IFX_XBAR_ERR_MCI_SBS_OFF (24u)

/** \brief  Length for Ifx_XBAR_ERR_Bits.OPC */
#define IFX_XBAR_ERR_OPC_LEN (4u)

/** \brief  Mask for Ifx_XBAR_ERR_Bits.OPC */
#define IFX_XBAR_ERR_OPC_MSK (0xfu)

/** \brief  Offset for Ifx_XBAR_ERR_Bits.OPC */
#define IFX_XBAR_ERR_OPC_OFF (4u)

/** \brief  Length for Ifx_XBAR_ERR_Bits.RD */
#define IFX_XBAR_ERR_RD_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ERR_Bits.RD */
#define IFX_XBAR_ERR_RD_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ERR_Bits.RD */
#define IFX_XBAR_ERR_RD_OFF (0u)

/** \brief  Length for Ifx_XBAR_ERR_Bits.SVM */
#define IFX_XBAR_ERR_SVM_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ERR_Bits.SVM */
#define IFX_XBAR_ERR_SVM_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ERR_Bits.SVM */
#define IFX_XBAR_ERR_SVM_OFF (2u)

/** \brief  Length for Ifx_XBAR_ERR_Bits.TR_ID */
#define IFX_XBAR_ERR_TR_ID_LEN (8u)

/** \brief  Mask for Ifx_XBAR_ERR_Bits.TR_ID */
#define IFX_XBAR_ERR_TR_ID_MSK (0xffu)

/** \brief  Offset for Ifx_XBAR_ERR_Bits.TR_ID */
#define IFX_XBAR_ERR_TR_ID_OFF (8u)

/** \brief  Length for Ifx_XBAR_ERR_Bits.WR */
#define IFX_XBAR_ERR_WR_LEN (1u)

/** \brief  Mask for Ifx_XBAR_ERR_Bits.WR */
#define IFX_XBAR_ERR_WR_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_ERR_Bits.WR */
#define IFX_XBAR_ERR_WR_OFF (1u)

/** \brief  Length for Ifx_XBAR_ERRADDR_Bits.ADDR */
#define IFX_XBAR_ERRADDR_ADDR_LEN (32u)

/** \brief  Mask for Ifx_XBAR_ERRADDR_Bits.ADDR */
#define IFX_XBAR_ERRADDR_ADDR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_XBAR_ERRADDR_Bits.ADDR */
#define IFX_XBAR_ERRADDR_ADDR_OFF (0u)

/** \brief  Length for Ifx_XBAR_EXTCOND_Bits.FREQDISF */
#define IFX_XBAR_EXTCOND_FREQDISF_LEN (1u)

/** \brief  Mask for Ifx_XBAR_EXTCOND_Bits.FREQDISF */
#define IFX_XBAR_EXTCOND_FREQDISF_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_EXTCOND_Bits.FREQDISF */
#define IFX_XBAR_EXTCOND_FREQDISF_OFF (6u)

/** \brief  Length for Ifx_XBAR_EXTCOND_Bits.MAX_WS */
#define IFX_XBAR_EXTCOND_MAX_WS_LEN (7u)

/** \brief  Mask for Ifx_XBAR_EXTCOND_Bits.MAX_WS */
#define IFX_XBAR_EXTCOND_MAX_WS_MSK (0x7fu)

/** \brief  Offset for Ifx_XBAR_EXTCOND_Bits.MAX_WS */
#define IFX_XBAR_EXTCOND_MAX_WS_OFF (13u)

/** \brief  Length for Ifx_XBAR_EXTCOND_Bits.NODELTR */
#define IFX_XBAR_EXTCOND_NODELTR_LEN (1u)

/** \brief  Mask for Ifx_XBAR_EXTCOND_Bits.NODELTR */
#define IFX_XBAR_EXTCOND_NODELTR_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_EXTCOND_Bits.NODELTR */
#define IFX_XBAR_EXTCOND_NODELTR_OFF (9u)

/** \brief  Length for Ifx_XBAR_EXTCOND_Bits.NORMW */
#define IFX_XBAR_EXTCOND_NORMW_LEN (1u)

/** \brief  Mask for Ifx_XBAR_EXTCOND_Bits.NORMW */
#define IFX_XBAR_EXTCOND_NORMW_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_EXTCOND_Bits.NORMW */
#define IFX_XBAR_EXTCOND_NORMW_OFF (10u)

/** \brief  Length for Ifx_XBAR_EXTCOND_Bits.WFWD */
#define IFX_XBAR_EXTCOND_WFWD_LEN (1u)

/** \brief  Mask for Ifx_XBAR_EXTCOND_Bits.WFWD */
#define IFX_XBAR_EXTCOND_WFWD_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_EXTCOND_Bits.WFWD */
#define IFX_XBAR_EXTCOND_WFWD_OFF (3u)

/** \brief  Length for Ifx_XBAR_ID_Bits.MODNUMBER */
#define IFX_XBAR_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_XBAR_ID_Bits.MODNUMBER */
#define IFX_XBAR_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_XBAR_ID_Bits.MODNUMBER */
#define IFX_XBAR_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_XBAR_ID_Bits.MODREV */
#define IFX_XBAR_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_XBAR_ID_Bits.MODREV */
#define IFX_XBAR_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_XBAR_ID_Bits.MODREV */
#define IFX_XBAR_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_XBAR_ID_Bits.MODTYPE */
#define IFX_XBAR_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_XBAR_ID_Bits.MODTYPE */
#define IFX_XBAR_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_XBAR_ID_Bits.MODTYPE */
#define IFX_XBAR_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENMCI0 */
#define IFX_XBAR_IDINTEN_ENMCI0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENMCI0 */
#define IFX_XBAR_IDINTEN_ENMCI0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENMCI0 */
#define IFX_XBAR_IDINTEN_ENMCI0_OFF (16u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENMCI12 */
#define IFX_XBAR_IDINTEN_ENMCI12_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENMCI12 */
#define IFX_XBAR_IDINTEN_ENMCI12_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENMCI12 */
#define IFX_XBAR_IDINTEN_ENMCI12_OFF (28u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENMCI13 */
#define IFX_XBAR_IDINTEN_ENMCI13_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENMCI13 */
#define IFX_XBAR_IDINTEN_ENMCI13_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENMCI13 */
#define IFX_XBAR_IDINTEN_ENMCI13_OFF (29u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENMCI4 */
#define IFX_XBAR_IDINTEN_ENMCI4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENMCI4 */
#define IFX_XBAR_IDINTEN_ENMCI4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENMCI4 */
#define IFX_XBAR_IDINTEN_ENMCI4_OFF (20u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENMCI5 */
#define IFX_XBAR_IDINTEN_ENMCI5_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENMCI5 */
#define IFX_XBAR_IDINTEN_ENMCI5_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENMCI5 */
#define IFX_XBAR_IDINTEN_ENMCI5_OFF (21u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENMCI8 */
#define IFX_XBAR_IDINTEN_ENMCI8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENMCI8 */
#define IFX_XBAR_IDINTEN_ENMCI8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENMCI8 */
#define IFX_XBAR_IDINTEN_ENMCI8_OFF (24u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENMCI9 */
#define IFX_XBAR_IDINTEN_ENMCI9_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENMCI9 */
#define IFX_XBAR_IDINTEN_ENMCI9_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENMCI9 */
#define IFX_XBAR_IDINTEN_ENMCI9_OFF (25u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENSCI0 */
#define IFX_XBAR_IDINTEN_ENSCI0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENSCI0 */
#define IFX_XBAR_IDINTEN_ENSCI0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENSCI0 */
#define IFX_XBAR_IDINTEN_ENSCI0_OFF (0u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENSCI1 */
#define IFX_XBAR_IDINTEN_ENSCI1_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENSCI1 */
#define IFX_XBAR_IDINTEN_ENSCI1_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENSCI1 */
#define IFX_XBAR_IDINTEN_ENSCI1_OFF (1u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENSCI4 */
#define IFX_XBAR_IDINTEN_ENSCI4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENSCI4 */
#define IFX_XBAR_IDINTEN_ENSCI4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENSCI4 */
#define IFX_XBAR_IDINTEN_ENSCI4_OFF (4u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENSCI6 */
#define IFX_XBAR_IDINTEN_ENSCI6_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENSCI6 */
#define IFX_XBAR_IDINTEN_ENSCI6_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENSCI6 */
#define IFX_XBAR_IDINTEN_ENSCI6_OFF (6u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENSCI7 */
#define IFX_XBAR_IDINTEN_ENSCI7_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENSCI7 */
#define IFX_XBAR_IDINTEN_ENSCI7_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENSCI7 */
#define IFX_XBAR_IDINTEN_ENSCI7_OFF (7u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENSCI8 */
#define IFX_XBAR_IDINTEN_ENSCI8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENSCI8 */
#define IFX_XBAR_IDINTEN_ENSCI8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENSCI8 */
#define IFX_XBAR_IDINTEN_ENSCI8_OFF (8u)

/** \brief  Length for Ifx_XBAR_IDINTEN_Bits.ENSCID */
#define IFX_XBAR_IDINTEN_ENSCID_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTEN_Bits.ENSCID */
#define IFX_XBAR_IDINTEN_ENSCID_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTEN_Bits.ENSCID */
#define IFX_XBAR_IDINTEN_ENSCID_OFF (15u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDCSCI4 */
#define IFX_XBAR_IDINTSAT_IDCSCI4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDCSCI4 */
#define IFX_XBAR_IDINTSAT_IDCSCI4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDCSCI4 */
#define IFX_XBAR_IDINTSAT_IDCSCI4_OFF (4u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDMCI0 */
#define IFX_XBAR_IDINTSAT_IDMCI0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDMCI0 */
#define IFX_XBAR_IDINTSAT_IDMCI0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDMCI0 */
#define IFX_XBAR_IDINTSAT_IDMCI0_OFF (16u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDMCI12 */
#define IFX_XBAR_IDINTSAT_IDMCI12_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDMCI12 */
#define IFX_XBAR_IDINTSAT_IDMCI12_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDMCI12 */
#define IFX_XBAR_IDINTSAT_IDMCI12_OFF (28u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDMCI13 */
#define IFX_XBAR_IDINTSAT_IDMCI13_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDMCI13 */
#define IFX_XBAR_IDINTSAT_IDMCI13_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDMCI13 */
#define IFX_XBAR_IDINTSAT_IDMCI13_OFF (29u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDMCI4 */
#define IFX_XBAR_IDINTSAT_IDMCI4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDMCI4 */
#define IFX_XBAR_IDINTSAT_IDMCI4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDMCI4 */
#define IFX_XBAR_IDINTSAT_IDMCI4_OFF (20u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDMCI5 */
#define IFX_XBAR_IDINTSAT_IDMCI5_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDMCI5 */
#define IFX_XBAR_IDINTSAT_IDMCI5_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDMCI5 */
#define IFX_XBAR_IDINTSAT_IDMCI5_OFF (21u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDMCI8 */
#define IFX_XBAR_IDINTSAT_IDMCI8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDMCI8 */
#define IFX_XBAR_IDINTSAT_IDMCI8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDMCI8 */
#define IFX_XBAR_IDINTSAT_IDMCI8_OFF (24u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDMCI9 */
#define IFX_XBAR_IDINTSAT_IDMCI9_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDMCI9 */
#define IFX_XBAR_IDINTSAT_IDMCI9_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDMCI9 */
#define IFX_XBAR_IDINTSAT_IDMCI9_OFF (25u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDSCI0 */
#define IFX_XBAR_IDINTSAT_IDSCI0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDSCI0 */
#define IFX_XBAR_IDINTSAT_IDSCI0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDSCI0 */
#define IFX_XBAR_IDINTSAT_IDSCI0_OFF (0u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDSCI1 */
#define IFX_XBAR_IDINTSAT_IDSCI1_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDSCI1 */
#define IFX_XBAR_IDINTSAT_IDSCI1_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDSCI1 */
#define IFX_XBAR_IDINTSAT_IDSCI1_OFF (1u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDSCI6 */
#define IFX_XBAR_IDINTSAT_IDSCI6_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDSCI6 */
#define IFX_XBAR_IDINTSAT_IDSCI6_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDSCI6 */
#define IFX_XBAR_IDINTSAT_IDSCI6_OFF (6u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDSCI7 */
#define IFX_XBAR_IDINTSAT_IDSCI7_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDSCI7 */
#define IFX_XBAR_IDINTSAT_IDSCI7_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDSCI7 */
#define IFX_XBAR_IDINTSAT_IDSCI7_OFF (7u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDSCI8 */
#define IFX_XBAR_IDINTSAT_IDSCI8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDSCI8 */
#define IFX_XBAR_IDINTSAT_IDSCI8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDSCI8 */
#define IFX_XBAR_IDINTSAT_IDSCI8_OFF (8u)

/** \brief  Length for Ifx_XBAR_IDINTSAT_Bits.IDSCID */
#define IFX_XBAR_IDINTSAT_IDSCID_LEN (1u)

/** \brief  Mask for Ifx_XBAR_IDINTSAT_Bits.IDSCID */
#define IFX_XBAR_IDINTSAT_IDSCID_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_IDINTSAT_Bits.IDSCID */
#define IFX_XBAR_IDINTSAT_IDSCID_OFF (15u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.PRSCI0 */
#define IFX_XBAR_INTSAT_PRSCI0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.PRSCI0 */
#define IFX_XBAR_INTSAT_PRSCI0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.PRSCI0 */
#define IFX_XBAR_INTSAT_PRSCI0_OFF (16u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.PRSCI1 */
#define IFX_XBAR_INTSAT_PRSCI1_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.PRSCI1 */
#define IFX_XBAR_INTSAT_PRSCI1_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.PRSCI1 */
#define IFX_XBAR_INTSAT_PRSCI1_OFF (17u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.PRSCI4 */
#define IFX_XBAR_INTSAT_PRSCI4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.PRSCI4 */
#define IFX_XBAR_INTSAT_PRSCI4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.PRSCI4 */
#define IFX_XBAR_INTSAT_PRSCI4_OFF (20u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.PRSCI6 */
#define IFX_XBAR_INTSAT_PRSCI6_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.PRSCI6 */
#define IFX_XBAR_INTSAT_PRSCI6_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.PRSCI6 */
#define IFX_XBAR_INTSAT_PRSCI6_OFF (22u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.PRSCI7 */
#define IFX_XBAR_INTSAT_PRSCI7_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.PRSCI7 */
#define IFX_XBAR_INTSAT_PRSCI7_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.PRSCI7 */
#define IFX_XBAR_INTSAT_PRSCI7_OFF (23u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.PRSCI8 */
#define IFX_XBAR_INTSAT_PRSCI8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.PRSCI8 */
#define IFX_XBAR_INTSAT_PRSCI8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.PRSCI8 */
#define IFX_XBAR_INTSAT_PRSCI8_OFF (24u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.PRSCID */
#define IFX_XBAR_INTSAT_PRSCID_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.PRSCID */
#define IFX_XBAR_INTSAT_PRSCID_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.PRSCID */
#define IFX_XBAR_INTSAT_PRSCID_OFF (31u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.SCSCI0 */
#define IFX_XBAR_INTSAT_SCSCI0_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.SCSCI0 */
#define IFX_XBAR_INTSAT_SCSCI0_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.SCSCI0 */
#define IFX_XBAR_INTSAT_SCSCI0_OFF (0u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.SCSCI1 */
#define IFX_XBAR_INTSAT_SCSCI1_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.SCSCI1 */
#define IFX_XBAR_INTSAT_SCSCI1_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.SCSCI1 */
#define IFX_XBAR_INTSAT_SCSCI1_OFF (1u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.SCSCI4 */
#define IFX_XBAR_INTSAT_SCSCI4_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.SCSCI4 */
#define IFX_XBAR_INTSAT_SCSCI4_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.SCSCI4 */
#define IFX_XBAR_INTSAT_SCSCI4_OFF (4u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.SCSCI6 */
#define IFX_XBAR_INTSAT_SCSCI6_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.SCSCI6 */
#define IFX_XBAR_INTSAT_SCSCI6_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.SCSCI6 */
#define IFX_XBAR_INTSAT_SCSCI6_OFF (6u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.SCSCI7 */
#define IFX_XBAR_INTSAT_SCSCI7_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.SCSCI7 */
#define IFX_XBAR_INTSAT_SCSCI7_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.SCSCI7 */
#define IFX_XBAR_INTSAT_SCSCI7_OFF (7u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.SCSCI8 */
#define IFX_XBAR_INTSAT_SCSCI8_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.SCSCI8 */
#define IFX_XBAR_INTSAT_SCSCI8_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.SCSCI8 */
#define IFX_XBAR_INTSAT_SCSCI8_OFF (8u)

/** \brief  Length for Ifx_XBAR_INTSAT_Bits.SCSCID */
#define IFX_XBAR_INTSAT_SCSCID_LEN (1u)

/** \brief  Mask for Ifx_XBAR_INTSAT_Bits.SCSCID */
#define IFX_XBAR_INTSAT_SCSCID_MSK (0x1u)

/** \brief  Offset for Ifx_XBAR_INTSAT_Bits.SCSCID */
#define IFX_XBAR_INTSAT_SCSCID_OFF (15u)

/** \brief  Length for Ifx_XBAR_PRIOH_Bits.MASTER12 */
#define IFX_XBAR_PRIOH_MASTER12_LEN (3u)

/** \brief  Mask for Ifx_XBAR_PRIOH_Bits.MASTER12 */
#define IFX_XBAR_PRIOH_MASTER12_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_PRIOH_Bits.MASTER12 */
#define IFX_XBAR_PRIOH_MASTER12_OFF (16u)

/** \brief  Length for Ifx_XBAR_PRIOH_Bits.MASTER13 */
#define IFX_XBAR_PRIOH_MASTER13_LEN (3u)

/** \brief  Mask for Ifx_XBAR_PRIOH_Bits.MASTER13 */
#define IFX_XBAR_PRIOH_MASTER13_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_PRIOH_Bits.MASTER13 */
#define IFX_XBAR_PRIOH_MASTER13_OFF (20u)

/** \brief  Length for Ifx_XBAR_PRIOH_Bits.MASTER8 */
#define IFX_XBAR_PRIOH_MASTER8_LEN (3u)

/** \brief  Mask for Ifx_XBAR_PRIOH_Bits.MASTER8 */
#define IFX_XBAR_PRIOH_MASTER8_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_PRIOH_Bits.MASTER8 */
#define IFX_XBAR_PRIOH_MASTER8_OFF (0u)

/** \brief  Length for Ifx_XBAR_PRIOH_Bits.MASTER9 */
#define IFX_XBAR_PRIOH_MASTER9_LEN (3u)

/** \brief  Mask for Ifx_XBAR_PRIOH_Bits.MASTER9 */
#define IFX_XBAR_PRIOH_MASTER9_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_PRIOH_Bits.MASTER9 */
#define IFX_XBAR_PRIOH_MASTER9_OFF (4u)

/** \brief  Length for Ifx_XBAR_PRIOL_Bits.MASTER0 */
#define IFX_XBAR_PRIOL_MASTER0_LEN (3u)

/** \brief  Mask for Ifx_XBAR_PRIOL_Bits.MASTER0 */
#define IFX_XBAR_PRIOL_MASTER0_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_PRIOL_Bits.MASTER0 */
#define IFX_XBAR_PRIOL_MASTER0_OFF (0u)

/** \brief  Length for Ifx_XBAR_PRIOL_Bits.MASTER4 */
#define IFX_XBAR_PRIOL_MASTER4_LEN (3u)

/** \brief  Mask for Ifx_XBAR_PRIOL_Bits.MASTER4 */
#define IFX_XBAR_PRIOL_MASTER4_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_PRIOL_Bits.MASTER4 */
#define IFX_XBAR_PRIOL_MASTER4_OFF (16u)

/** \brief  Length for Ifx_XBAR_PRIOL_Bits.MASTER5 */
#define IFX_XBAR_PRIOL_MASTER5_LEN (3u)

/** \brief  Mask for Ifx_XBAR_PRIOL_Bits.MASTER5 */
#define IFX_XBAR_PRIOL_MASTER5_MSK (0x7u)

/** \brief  Offset for Ifx_XBAR_PRIOL_Bits.MASTER5 */
#define IFX_XBAR_PRIOL_MASTER5_OFF (20u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXXBAR_BF_H */
