	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc23540a --dep-file=IfxGtm_Dpll.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std/IfxGtm_Dpll.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std/IfxGtm_Dpll.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std/IfxGtm_Dpll.c'

	
$TC16X
	
	.sdecl	'.text.IfxGtm_Dpll.IfxGtm_Dpll_getSubIncFrequency',code,cluster('IfxGtm_Dpll_getSubIncFrequency')
	.sect	'.text.IfxGtm_Dpll.IfxGtm_Dpll_getSubIncFrequency'
	.align	2
	
	.global	IfxGtm_Dpll_getSubIncFrequency
; Function IfxGtm_Dpll_getSubIncFrequency
.L6:
IfxGtm_Dpll_getSubIncFrequency:	.type	func
	jz.a	a4,.L2
.L2:
	jeq	d4,#0,.L3
.L3:
	mov	d2,#0
.L30:
	j	.L4
.L4:
	ret
.L17:
	
__IfxGtm_Dpll_getSubIncFrequency_function_end:
	.size	IfxGtm_Dpll_getSubIncFrequency,__IfxGtm_Dpll_getSubIncFrequency_function_end-IfxGtm_Dpll_getSubIncFrequency
.L15:
	; End of function
	
	.calls	'IfxGtm_Dpll_getSubIncFrequency','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L8:
	.word	148383
	.half	3
	.word	.L9
	.byte	4
.L7:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std/IfxGtm_Dpll.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L10
	.byte	2,1,1,3
	.word	235
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	238
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L16:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	283
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	295
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	375
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	349
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	381
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	381
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	349
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	529
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	490
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	845
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1416
	.byte	4,2,35,0,0,14,4
	.word	490
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	490
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	490
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	490
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	490
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	490
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1544
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	490
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	490
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	490
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	490
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	490
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1759
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	490
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	490
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	490
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	490
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	490
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1974
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	490
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	490
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	490
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	490
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	490
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2191
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2411
	.byte	4,2,35,0,0,14,24
	.word	490
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	490
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	490
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	490
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	490
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	490
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	490
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	490
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2734
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	490
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	490
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	490
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	490
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	490
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	490
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	490
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3038
	.byte	4,2,35,0,0,14,8
	.word	490
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3363
	.byte	4,2,35,0,0,14,12
	.word	490
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3703
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4069
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4355
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4502
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	467
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4671
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	507
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4843
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	507
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	507
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5018
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5192
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	490
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5366
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	490
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5542
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5698
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	507
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	490
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6031
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6379
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	490
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6503
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6587
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	490
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6767
	.byte	4,2,35,0,0,14,76
	.word	490
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	490
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7107
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	805
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1376
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1495
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1535
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1719
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1934
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2151
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2371
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1535
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2685
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2725
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2998
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3314
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3354
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3654
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3694
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4029
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4315
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3354
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4462
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4631
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4803
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4978
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5152
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5326
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5502
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5658
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5991
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6339
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3354
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6463
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6712
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6971
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7011
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7067
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7634
	.byte	4,3,35,252,1,0,16
	.word	7674
	.byte	3
	.word	8277
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8282
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	490
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8287
	.byte	6,0,19
	.word	243
	.byte	20
	.word	269
	.byte	6,0,19
	.word	304
	.byte	20
	.word	336
	.byte	6,0,19
	.word	386
	.byte	20
	.word	405
	.byte	6,0,19
	.word	421
	.byte	20
	.word	436
	.byte	20
	.word	450
	.byte	6,0,19
	.word	8390
	.byte	20
	.word	8418
	.byte	20
	.word	8432
	.byte	20
	.word	8450
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_GTM_REV_Bits',0,5,213,27,16,4,11
	.byte	'STEP',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'NO',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'MINOR',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'MAJOR',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'DEV_CODE0',0,4
	.word	8543
	.byte	4,8,2,35,0,11
	.byte	'DEV_CODE1',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'DEV_CODE2',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,249,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8559
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_RST_Bits',0,5,225,27,16,4,11
	.byte	'RST',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	31,0,2,35,0,0,12,5,129,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8751
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CTRL_Bits',0,5,201,10,16,4,11
	.byte	'RF_PROT',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TO_MODE',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'TO_VAL',0,4
	.word	8543
	.byte	5,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	23,0,2,35,0,0,12,5,168,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8853
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,5,114,16,4,11
	.byte	'TO_ADDR',0,4
	.word	8543
	.byte	20,12,2,35,0,11
	.byte	'TO_W1R0',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	11,0,2,35,0,0,12,5,232,33,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9019
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,5,139,23,16,4,11
	.byte	'AEI_TO_XPT',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,137,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9153
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,5,240,22,16,4,11
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,241,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9338
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,5,250,22,16,4,11
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG_AEI_USP_BE',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,249,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,5,132,23,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,129,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9749
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,5,211,18,16,4,11
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,217,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9861
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,5,180,8,16,4,11
	.byte	'BRG_MODE',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'MSK_WR_RSP',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	6,24,2,35,0,11
	.byte	'MODE_UP_PGR',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'BUFF_OVL',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'SYNC_INPUT_REG',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'BRG_RST',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8543
	.byte	7,8,2,35,0,11
	.byte	'BUFF_DPT',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,144,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10075
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,5,196,8,16,4,11
	.byte	'NEW_TRAN_PTR',0,4
	.word	8543
	.byte	5,27,2,35,0,11
	.byte	'FIRST_RSP_PTR',0,4
	.word	8543
	.byte	5,22,2,35,0,11
	.byte	'TRAN_IN_PGR',0,4
	.word	8543
	.byte	5,17,2,35,0,11
	.byte	'ABT_TRAN_PGR',0,4
	.word	8543
	.byte	5,12,2,35,0,11
	.byte	'FBC',0,4
	.word	8543
	.byte	6,6,2,35,0,11
	.byte	'RSP_TRAN_RDY',0,4
	.word	8543
	.byte	6,0,2,35,0,0,12,5,152,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10389
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,5,207,8,16,4,11
	.byte	'TRAN_IN_PGR2',0,4
	.word	8543
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	27,0,2,35,0,0,12,5,160,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10597
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRIDGE',0,5,222,57,25,12,13
	.byte	'MODE',0
	.word	10349
	.byte	4,2,35,0,13
	.byte	'PTR1',0
	.word	10557
	.byte	4,2,35,4,13
	.byte	'PTR2',0
	.word	10676
	.byte	4,2,35,8,0,16
	.word	10716
	.byte	10
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,5,151,29,16,4,11
	.byte	'SRC_CH0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'SRC_CH1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'SRC_CH2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'SRC_CH3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'SRC_CH4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'SRC_CH5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'SRC_CH6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'SRC_CH7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,153,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10786
	.byte	4,2,35,0,0,14,12
	.word	10996
	.byte	15,2,0,14,180,1
	.word	490
	.byte	15,179,1,0,10
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,5,142,29,16,4,11
	.byte	'ENDIS_CH0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CH1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CH2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8543
	.byte	26,0,2,35,0,0,12,5,145,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11056
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,5,232,28,16,4,11
	.byte	'LOW_RES',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	8543
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,233,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11211
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,5,225,28,16,4,11
	.byte	'BASE',0,4
	.word	8543
	.byte	27,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,225,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11348
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,5,247,28,16,4,11
	.byte	'CH_MODE',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	8543
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,249,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11461
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,5,240,28,16,4,11
	.byte	'BASE',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,241,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11598
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,5,134,29,16,4,11
	.byte	'CH_MODE',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	8543
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,137,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11711
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,5,255,28,16,4,11
	.byte	'BASE',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,129,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TBU',0,5,225,59,25,28,13
	.byte	'CHEN',0
	.word	11171
	.byte	4,2,35,0,13
	.byte	'CH0_CTRL',0
	.word	11308
	.byte	4,2,35,4,13
	.byte	'CH0_BASE',0
	.word	11421
	.byte	4,2,35,8,13
	.byte	'CH1_CTRL',0
	.word	11558
	.byte	4,2,35,12,13
	.byte	'CH1_BASE',0
	.word	11671
	.byte	4,2,35,16,13
	.byte	'CH2_CTRL',0
	.word	11808
	.byte	4,2,35,20,13
	.byte	'CH2_BASE',0
	.word	11921
	.byte	4,2,35,24,0,16
	.word	11961
	.byte	14,100
	.word	490
	.byte	15,99,0,10
	.byte	'_Ifx_GTM_MON_STATUS_Bits',0,5,246,25,16,4,11
	.byte	'ACT_CMU0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ACT_CMU1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ACT_CMU2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ACT_CMU3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ACT_CMU4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ACT_CMU5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'ACT_CMU6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'ACT_CMU7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'ACT_CMUFX0',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'ACT_CMUFX1',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'ACT_CMUFX2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'ACT_CMUFX3',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'ACT_CMUFX4',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'CMP_ERR',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8543
	.byte	3,12,2,35,0,11
	.byte	'MCS0_ERR',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'MCS1_ERR',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'MCS2_ERR',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,249,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12117
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MON_ACTIVITY_0_Bits',0,5,216,25,16,4,11
	.byte	'MCA_0_0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'MCA_0_1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MCA_0_2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'MCA_0_3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'MCA_0_4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'MCA_0_5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MCA_0_6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MCA_0_7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'MCA_1_0',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'MCA_1_1',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'MCA_1_2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'MCA_1_3',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'MCA_1_4',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'MCA_1_5',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'MCA_1_6',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'MCA_1_7',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'MCA_2_0',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'MCA_2_1',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'MCA_2_2',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'MCA_2_3',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'MCA_2_4',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'MCA_2_5',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'MCA_2_6',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'MCA_2_7',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,241,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12607
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MON',0,5,185,59,25,8,13
	.byte	'STATUS',0
	.word	12567
	.byte	4,2,35,0,13
	.byte	'ACTIVITY_0',0
	.word	13122
	.byte	4,2,35,4,0,16
	.word	13162
	.byte	14,120
	.word	490
	.byte	15,119,0,10
	.byte	'_Ifx_GTM_CMP_EN_Bits',0,5,254,8,16,4,11
	.byte	'ABWC0_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ABWC1_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ABWC2_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ABWC3_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ABWC4_EN',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ABWC5_EN',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'ABWC6_EN',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'ABWC7_EN',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'ABWC8_EN',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'ABWC9_EN',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'ABWC10_EN',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'ABWC11_EN',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TBWC0_EN',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TBWC1_EN',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TBWC2_EN',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TBWC3_EN',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TBWC4_EN',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TBWC5_EN',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TBWC6_EN',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TBWC7_EN',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TBWC8_EN',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TBWC9_EN',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TBWC10_EN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TBWC11_EN',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,184,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13232
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMP_IRQ_NOTIFY_Bits',0,5,223,9,16,4,11
	.byte	'ABWC0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ABWC1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ABWC2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ABWC3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ABWC4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ABWC5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'ABWC6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'ABWC7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'ABWC8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'ABWC9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'ABWC10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'ABWC11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TBWC0',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TBWC1',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TBWC2',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TBWC3',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TBWC4',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TBWC5',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TBWC6',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TBWC7',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TBWC8',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TBWC9',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TBWC10',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TBWC11',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,216,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13807
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMP_IRQ_EN_Bits',0,5,156,9,16,4,11
	.byte	'ABWC0_EN_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ABWC1_EN_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ABWC2_EN_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ABWC3_EN_IRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ABWC4_EN_IRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ABWC5_EN_IRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'ABWC6_EN_IRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'ABWC7_EN_IRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'ABWC8_EN_IRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'ABWC9_EN_IRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'ABWC10_EN_IRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'ABWC11_EN_IRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TBWC0_EN_IRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TBWC1_EN_IRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TBWC2_EN_IRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TBWC3_EN_IRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TBWC4_EN_IRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TBWC5_EN_IRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TBWC6_EN_IRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TBWC7_EN_IRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TBWC8_EN_IRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TBWC9_EN_IRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TBWC10_EN_IRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TBWC11_EN_IRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,192,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14318
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMP_IRQ_FORCINT_Bits',0,5,186,9,16,4,11
	.byte	'TRG_ABWC0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_ABWC1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_ABWC2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG_ABWC3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRG_ABWC4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TRG_ABWC5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TRG_ABWC6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TRG_ABWC7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TRG_ABWC8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TRG_ABWC9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TRG_ABWC10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TRG_ABWC11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TRG_TBWC0',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TRG_TBWC1',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TRG_TBWC2',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TRG_TBWC3',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TRG_TBWC4',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TRG_TBWC5',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TRG_TBWC6',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TRG_TBWC7',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TRG_TBWC8',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TRG_TBWC9',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TRG_TBWC10',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TRG_TBWC11',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,200,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14993
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMP_IRQ_MODE_Bits',0,5,216,9,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,208,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15601
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMP_EIRQ_EN_Bits',0,5,224,8,16,4,11
	.byte	'ABWC0_EN_EIRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ABWC1_EN_EIRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ABWC2_EN_EIRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ABWC3_EN_EIRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ABWC4_EN_EIRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ABWC5_EN_EIRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'ABWC6_EN_EIRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'ABWC7_EN_EIRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'ABWC8_EN_EIRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'ABWC9_EN_EIRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'ABWC10_EN_EIRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'ABWC11_EN_EIRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TBWC0_EN_EIRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TBWC1_EN_EIRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TBWC2_EN_EIRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TBWC3_EN_EIRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TBWC4_EN_EIRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TBWC5_EN_EIRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TBWC6_EN_EIRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TBWC7_EN_EIRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TBWC8_EN_EIRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TBWC9_EN_EIRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TBWC10_EN_EIRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TBWC11_EN_EIRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,176,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15717
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMP',0,5,230,57,25,24,13
	.byte	'EN',0
	.word	13767
	.byte	4,2,35,0,13
	.byte	'IRQ_NOTIFY',0
	.word	14278
	.byte	4,2,35,4,13
	.byte	'IRQ_EN',0
	.word	14953
	.byte	4,2,35,8,13
	.byte	'IRQ_FORCINT',0
	.word	15561
	.byte	4,2,35,12,13
	.byte	'IRQ_MODE',0
	.word	15677
	.byte	4,2,35,16,13
	.byte	'EIRQ_EN',0
	.word	16377
	.byte	4,2,35,20,0,16
	.word	16417
	.byte	14,104
	.word	490
	.byte	15,103,0,10
	.byte	'_Ifx_GTM_ARU_ARU_ACCESS_Bits',0,5,129,1,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'RREQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'WREQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	8543
	.byte	18,0,2,35,0,0,12,5,248,33,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16555
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DATA_H_Bits',0,5,139,1,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,128,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16724
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DATA_L_Bits',0,5,146,1,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,136,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16835
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DBG_ACCESS0_Bits',0,5,153,1,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	23,0,2,35,0,0,12,5,144,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16946
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DBG_DATA0_H_Bits',0,5,167,1,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,160,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17061
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DBG_DATA0_L_Bits',0,5,174,1,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,168,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17177
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DBG_ACCESS1_Bits',0,5,160,1,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	23,0,2,35,0,0,12,5,152,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17293
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DBG_DATA1_H_Bits',0,5,181,1,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,176,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17408
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_DBG_DATA1_L_Bits',0,5,188,1,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,184,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17524
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_IRQ_NOTIFY_Bits',0,5,220,1,16,4,11
	.byte	'NEW_DATA0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'NEW_DATA1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ACC_ACK',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,216,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17640
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_IRQ_EN_Bits',0,5,195,1,16,4,11
	.byte	'NEW_DATA0_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'NEW_DATA1_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ACC_ACK_IRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,192,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17799
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_IRQ_FORCINT_Bits',0,5,204,1,16,4,11
	.byte	'TRG_NEW_DATA0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_NEW_DATA',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_ACC_ACK',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,200,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17975
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU_IRQ_MODE_Bits',0,5,213,1,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,208,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18146
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ARU',0,5,148,57,25,52,13
	.byte	'ARU_ACCESS',0
	.word	16684
	.byte	4,2,35,0,13
	.byte	'DATA_H',0
	.word	16795
	.byte	4,2,35,4,13
	.byte	'DATA_L',0
	.word	16906
	.byte	4,2,35,8,13
	.byte	'DBG_ACCESS0',0
	.word	17021
	.byte	4,2,35,12,13
	.byte	'DBG_DATA0_H',0
	.word	17137
	.byte	4,2,35,16,13
	.byte	'DBG_DATA0_L',0
	.word	17253
	.byte	4,2,35,20,13
	.byte	'DBG_ACCESS1',0
	.word	17368
	.byte	4,2,35,24,13
	.byte	'DBG_DATA1_H',0
	.word	17484
	.byte	4,2,35,28,13
	.byte	'DBG_DATA1_L',0
	.word	17600
	.byte	4,2,35,32,13
	.byte	'IRQ_NOTIFY',0
	.word	17759
	.byte	4,2,35,36,13
	.byte	'IRQ_EN',0
	.word	17935
	.byte	4,2,35,40,13
	.byte	'IRQ_FORCINT',0
	.word	18106
	.byte	4,2,35,44,13
	.byte	'IRQ_MODE',0
	.word	18222
	.byte	4,2,35,48,0,16
	.word	18262
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,5,148,10,16,4,11
	.byte	'EN_CLK0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'EN_CLK1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'EN_CLK2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'EN_CLK3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'EN_CLK4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'EN_CLK5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'EN_CLK6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'EN_CLK7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'EN_ECLK0',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'EN_ECLK1',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'EN_ECLK2',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'EN_FXCLK',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,248,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18540
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,5,194,10,16,4,11
	.byte	'GCLK_NUM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,160,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18867
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,5,187,10,16,4,11
	.byte	'GCLK_DEN',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,152,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18984
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,5,253,9,16,4,11
	.byte	'CLK_CNT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,224,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19101
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,5,200,55,25,4,13
	.byte	'CTRL',0
	.word	19180
	.byte	4,2,35,0,0,14,24
	.word	19220
	.byte	15,5,0,16
	.word	19261
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,5,132,10,16,4,11
	.byte	'CLK_CNT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'CLK6_SEL',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8543
	.byte	7,0,2,35,0,0,12,5,232,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19275
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_CLK_6',0,5,206,55,25,4,13
	.byte	'CTRL',0
	.word	19373
	.byte	4,2,35,0,0,16
	.word	19413
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,5,140,10,16,4,11
	.byte	'CLK_CNT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'CLK7_SEL',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8543
	.byte	7,0,2,35,0,0,12,5,240,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19458
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_CLK_7',0,5,212,55,25,4,13
	.byte	'CTRL',0
	.word	19556
	.byte	4,2,35,0,0,16
	.word	19596
	.byte	10
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,5,173,10,16,4,11
	.byte	'ECLK_NUM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,136,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19641
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,5,166,10,16,4,11
	.byte	'ECLK_DEN',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,128,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19758
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_ECLK',0,5,218,55,25,8,13
	.byte	'NUM',0
	.word	19718
	.byte	4,2,35,0,13
	.byte	'DEN',0
	.word	19835
	.byte	4,2,35,4,0,14,24
	.word	19875
	.byte	15,2,0,16
	.word	19926
	.byte	10
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,5,180,10,16,4,11
	.byte	'FXCLK_SEL',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,144,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19940
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_CMU_FXCLK',0,5,225,55,25,4,13
	.byte	'CTRL',0
	.word	20019
	.byte	4,2,35,0,0,16
	.word	20059
	.byte	10
	.byte	'_Ifx_GTM_CMU',0,5,241,57,25,72,13
	.byte	'CLK_EN',0
	.word	18827
	.byte	4,2,35,0,13
	.byte	'GCLK_NUM',0
	.word	18944
	.byte	4,2,35,4,13
	.byte	'GCLK_DEN',0
	.word	19061
	.byte	4,2,35,8,13
	.byte	'CLK0_5',0
	.word	19270
	.byte	24,2,35,12,13
	.byte	'CLK_6',0
	.word	19453
	.byte	4,2,35,36,13
	.byte	'CLK_7',0
	.word	19636
	.byte	4,2,35,40,13
	.byte	'ECLK',0
	.word	19935
	.byte	24,2,35,44,13
	.byte	'FXCLK',0
	.word	20099
	.byte	4,2,35,68,0,16
	.word	20104
	.byte	14,184,1
	.word	490
	.byte	15,183,1,0,10
	.byte	'_Ifx_GTM_BRC_SRC0_ADDR_Bits',0,5,236,4,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,208,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20267
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC0_DEST_Bits',0,5,245,4,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,216,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20423
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC1_ADDR_Bits',0,5,222,5,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,128,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20996
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC1_DEST_Bits',0,5,231,5,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,136,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21152
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC2_ADDR_Bits',0,5,132,6,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,144,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21725
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC2_DEST_Bits',0,5,141,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,152,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21881
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC3_ADDR_Bits',0,5,170,6,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,160,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22454
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC3_DEST_Bits',0,5,179,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,168,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22610
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC4_ADDR_Bits',0,5,208,6,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,176,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23183
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC4_DEST_Bits',0,5,217,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,184,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23339
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC5_ADDR_Bits',0,5,246,6,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,192,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23912
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC5_DEST_Bits',0,5,255,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,200,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24068
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC6_ADDR_Bits',0,5,156,7,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,208,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24641
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC6_DEST_Bits',0,5,165,7,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,216,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24797
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC7_ADDR_Bits',0,5,194,7,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,224,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25370
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC7_DEST_Bits',0,5,203,7,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,232,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25526
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC8_ADDR_Bits',0,5,232,7,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,240,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26099
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC8_DEST_Bits',0,5,241,7,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,248,37,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26255
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC9_ADDR_Bits',0,5,142,8,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,128,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26828
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC9_DEST_Bits',0,5,151,8,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,136,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26984
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC10_ADDR_Bits',0,5,146,5,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,224,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27557
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC10_DEST_Bits',0,5,155,5,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,232,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27714
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC11_ADDR_Bits',0,5,184,5,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,240,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28288
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_SRC11_DEST_Bits',0,5,193,5,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	9,0,2,35,0,0,12,5,248,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28445
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_IRQ_NOTIFY_Bits',0,5,210,4,16,4,11
	.byte	'DEST_ERR',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'DID0',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'DID1',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'DID2',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'DID3',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'DID4',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'DID5',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'DID6',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'DID7',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'DID8',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'DID9',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'DID10',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'DID11',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,192,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29019
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_IRQ_EN_Bits',0,5,165,4,16,4,11
	.byte	'DEST_ERR_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'DID_EN0',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'DID_EN1',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'DID_EN2',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'DID_EN3',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'DID_EN4',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'DID_EN5',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'DID_EN6',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'DID_EN7',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'DID_EN8',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'DID_EN9',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'DID_EN10',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'DID_EN11',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,168,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29332
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_IRQ_FORCINT_Bits',0,5,184,4,16,4,11
	.byte	'TRG_DEST_ERR',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_DID0',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_DID1',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG_DID2',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRG_DID3',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TRG_DID4',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TRG_DID5',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TRG_DID6',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TRG_DID7',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TRG_DID8',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TRG_DID9',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TRG_DID10',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TRG_DID11',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,176,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_IRQ_MODE_Bits',0,5,203,4,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,184,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30046
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_RST_Bits',0,5,229,4,16,4,11
	.byte	'RST',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	31,0,2,35,0,0,12,5,200,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC_EIRQ_EN_Bits',0,5,146,4,16,4,11
	.byte	'DEST_ERR_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'DID_EN0',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'DID_EN1',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'DID_EN2',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'DID_EN3',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'DID_EN4',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'DID_EN5',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'DID_EN6',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'DID_EN7',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'DID_EN8',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'DID_EN9',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'DID_EN10',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'DID_EN11',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	19,0,2,35,0,0,12,5,160,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_BRC',0,5,187,57,25,120,13
	.byte	'SRC0_ADDR',0
	.word	20383
	.byte	4,2,35,0,13
	.byte	'SRC0_DEST',0
	.word	20956
	.byte	4,2,35,4,13
	.byte	'SRC1_ADDR',0
	.word	21112
	.byte	4,2,35,8,13
	.byte	'SRC1_DEST',0
	.word	21685
	.byte	4,2,35,12,13
	.byte	'SRC2_ADDR',0
	.word	21841
	.byte	4,2,35,16,13
	.byte	'SRC2_DEST',0
	.word	22414
	.byte	4,2,35,20,13
	.byte	'SRC3_ADDR',0
	.word	22570
	.byte	4,2,35,24,13
	.byte	'SRC3_DEST',0
	.word	23143
	.byte	4,2,35,28,13
	.byte	'SRC4_ADDR',0
	.word	23299
	.byte	4,2,35,32,13
	.byte	'SRC4_DEST',0
	.word	23872
	.byte	4,2,35,36,13
	.byte	'SRC5_ADDR',0
	.word	24028
	.byte	4,2,35,40,13
	.byte	'SRC5_DEST',0
	.word	24601
	.byte	4,2,35,44,13
	.byte	'SRC6_ADDR',0
	.word	24757
	.byte	4,2,35,48,13
	.byte	'SRC6_DEST',0
	.word	25330
	.byte	4,2,35,52,13
	.byte	'SRC7_ADDR',0
	.word	25486
	.byte	4,2,35,56,13
	.byte	'SRC7_DEST',0
	.word	26059
	.byte	4,2,35,60,13
	.byte	'SRC8_ADDR',0
	.word	26215
	.byte	4,2,35,64,13
	.byte	'SRC8_DEST',0
	.word	26788
	.byte	4,2,35,68,13
	.byte	'SRC9_ADDR',0
	.word	26944
	.byte	4,2,35,72,13
	.byte	'SRC9_DEST',0
	.word	27517
	.byte	4,2,35,76,13
	.byte	'SRC10_ADDR',0
	.word	27674
	.byte	4,2,35,80,13
	.byte	'SRC10_DEST',0
	.word	28248
	.byte	4,2,35,84,13
	.byte	'SRC11_ADDR',0
	.word	28405
	.byte	4,2,35,88,13
	.byte	'SRC11_DEST',0
	.word	28979
	.byte	4,2,35,92,13
	.byte	'IRQ_NOTIFY',0
	.word	29292
	.byte	4,2,35,96,13
	.byte	'IRQ_EN',0
	.word	29640
	.byte	4,2,35,100,13
	.byte	'IRQ_FORCINT',0
	.word	30006
	.byte	4,2,35,104,13
	.byte	'IRQ_MODE',0
	.word	30122
	.byte	4,2,35,108,13
	.byte	'RST',0
	.word	30228
	.byte	4,2,35,112,13
	.byte	'EIRQ_EN',0
	.word	30577
	.byte	4,2,35,116,0,16
	.word	30617
	.byte	14,136,3
	.word	490
	.byte	15,135,3,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,5,241,19,16,4,11
	.byte	'ARU_NEW_DATA0_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ARU_NEW_DATA1_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ARU_ACC_ACK_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'BRC_IRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'AEI_IRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'CMP_IRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'SPE0_IRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'SPE1_IRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	8,16,2,35,0,11
	.byte	'PSM0_CH0_IRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'PSM0_CH1_IRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'PSM0_CH2_IRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'PSM0_CH3_IRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'PSM0_CH4_IRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'PSM0_CH5_IRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'PSM0_CH6_IRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'PSM0_CH7_IRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,233,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31218
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_1_Bits',0,5,136,20,16,4,11
	.byte	'DPLL_DCG_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'DPLL_EDI_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'DPLL_TIN_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'DPLL_TAX_IRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'DPLL_SIS_IRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'DPLL_TIS_IRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'DPLL_MSI_IRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'DPLL_MTI_IRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'DPLL_SAS_IRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'DPLL_TAS_IRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'DPLL_PWI_IRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'DPLL_W2I_IRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'DPLL_W1I_IRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'DPLL_GLI_IRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'DPLL_LLI_IRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'DPLL_EI_IRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'DPLL_GL2I_IRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'DPLL_LL2I_IRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'DPLL_TE0_IRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'DPLL_TE1_IRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'DPLL_TE2_IRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'DPLL_TE3_IRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'DPLL_TE4_IRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'DPLL_CDIT_IRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'DPLL_CDIS_IRQ',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'DPLL_TORI_IRQ',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'DPLL_SORI_IRQ',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,241,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31709
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,5,169,20,16,4,11
	.byte	'TIM0_CH0_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TIM0_CH1_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TIM0_CH2_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TIM0_CH3_IRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TIM0_CH4_IRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TIM0_CH5_IRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TIM0_CH6_IRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TIM0_CH7_IRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TIM1_CH0_IRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TIM1_CH1_IRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TIM1_CH2_IRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TIM1_CH3_IRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TIM1_CH4_IRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TIM1_CH5_IRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TIM1_CH6_IRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TIM1_CH7_IRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TIM2_CH0_IRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TIM2_CH1_IRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TIM2_CH2_IRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TIM2_CH3_IRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TIM2_CH4_IRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TIM2_CH5_IRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TIM2_CH6_IRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TIM2_CH7_IRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,249,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32457
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_4_Bits',0,5,199,20,16,4,11
	.byte	'MCS0_CH0_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'MCS0_CH1_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MCS0_CH2_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'MCS0_CH3_IRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'MCS0_CH4_IRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'MCS0_CH5_IRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MCS0_CH6_IRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MCS0_CH7_IRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'MCS1_CH0_IRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'MCS1_CH1_IRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'MCS1_CH2_IRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'MCS1_CH3_IRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'MCS1_CH4_IRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'MCS1_CH5_IRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'MCS1_CH6_IRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'MCS1_CH7_IRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'MCS2_CH0_IRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'MCS2_CH1_IRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'MCS2_CH2_IRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'MCS2_CH3_IRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'MCS2_CH4_IRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'MCS2_CH5_IRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'MCS2_CH6_IRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'MCS2_CH7_IRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,129,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33128
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,5,229,20,16,4,11
	.byte	'TOM0_CH0_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TOM0_CH1_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TOM0_CH2_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TOM0_CH3_IRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TOM0_CH4_IRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TOM0_CH5_IRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TOM0_CH6_IRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TOM0_CH7_IRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TOM0_CH8_IRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TOM0_CH9_IRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TOM0_CH10_IRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TOM0_CH11_IRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TOM0_CH12_IRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TOM0_CH13_IRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TOM0_CH14_IRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TOM0_CH15_IRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TOM1_CH0_IRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TOM1_CH1_IRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TOM1_CH2_IRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TOM1_CH3_IRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TOM1_CH4_IRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TOM1_CH5_IRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TOM1_CH6_IRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TOM1_CH7_IRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'TOM1_CH8_IRQ',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TOM1_CH9_IRQ',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'TOM1_CH10_IRQ',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'TOM1_CH11_IRQ',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'TOM1_CH12_IRQ',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'TOM1_CH13_IRQ',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'TOM1_CH14_IRQ',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'TOM1_CH15_IRQ',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,137,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33799
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_9_Bits',0,5,138,21,16,4,11
	.byte	'ATOM0_CH0_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ATOM0_CH1_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ATOM0_CH2_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ATOM0_CH3_IRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ATOM0_CH4_IRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ATOM0_CH5_IRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'ATOM0_CH6_IRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'ATOM0_CH7_IRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'ATOM1_CH0_IRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'ATOM1_CH1_IRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'ATOM1_CH2_IRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'ATOM1_CH3_IRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'ATOM1_CH4_IRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'ATOM1_CH5_IRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'ATOM1_CH6_IRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'ATOM1_CH7_IRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'ATOM2_CH0_IRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'ATOM2_CH1_IRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'ATOM2_CH2_IRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'ATOM2_CH3_IRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'ATOM2_CH4_IRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'ATOM2_CH5_IRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'ATOM2_CH6_IRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'ATOM2_CH7_IRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'ATOM3_CH0_IRQ',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'ATOM3_CH1_IRQ',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'ATOM3_CH2_IRQ',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'ATOM3_CH3_IRQ',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'ATOM3_CH4_IRQ',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'ATOM3_CH5_IRQ',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'ATOM3_CH6_IRQ',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'ATOM3_CH7_IRQ',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,145,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34651
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,5,249,21,16,4,11
	.byte	'GTM_EIRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'BRC_EIRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'FIFO0_EIRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TIM0_EIRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TIM1_EIRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TIM2_EIRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8543
	.byte	5,20,2,35,0,11
	.byte	'MCS0_EIRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'MCS1_EIRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'MCS2_EIRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8543
	.byte	5,12,2,35,0,11
	.byte	'SPE0_EIRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'SPE1_EIRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'reserved_22',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'CMP_EIRQ',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'DPLL_EIRQ',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8543
	.byte	6,0,2,35,0,0,12,5,177,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35523
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_CEI0_Bits',0,5,175,21,16,4,11
	.byte	'FIFO0_CH0_EIRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'FIFO0_CH1_EIRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'FIFO0_CH2_EIRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'FIFO0_CH3_EIRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'FIFO0_CH4_EIRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'FIFO0_CH5_EIRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'FIFO0_CH6_EIRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'FIFO0_CH7_EIRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,153,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35981
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,5,189,21,16,4,11
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TIM1_CH0_EIRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TIM1_CH1_EIRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TIM1_CH2_EIRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TIM1_CH3_EIRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TIM1_CH4_EIRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TIM1_CH5_EIRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TIM1_CH6_EIRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TIM1_CH7_EIRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TIM2_CH0_EIRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TIM2_CH1_EIRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TIM2_CH2_EIRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TIM2_CH3_EIRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TIM2_CH4_EIRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TIM2_CH5_EIRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TIM2_CH6_EIRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TIM2_CH7_EIRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,161,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36286
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM_IRQG_CEI3_Bits',0,5,219,21,16,4,11
	.byte	'MCS0_CH0_EIRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'MCS0_CH1_EIRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MCS0_CH2_EIRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'MCS0_CH3_EIRQ',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'MCS0_CH4_EIRQ',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'MCS0_CH5_EIRQ',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MCS0_CH6_EIRQ',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MCS0_CH7_EIRQ',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'MCS1_CH0_EIRQ',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'MCS1_CH1_EIRQ',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'MCS1_CH2_EIRQ',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'MCS1_CH3_EIRQ',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'MCS1_CH4_EIRQ',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'MCS1_CH5_EIRQ',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'MCS1_CH6_EIRQ',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'MCS1_CH7_EIRQ',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'MCS2_CH0_EIRQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'MCS2_CH1_EIRQ',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'MCS2_CH2_EIRQ',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'MCS2_CH3_EIRQ',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'MCS2_CH4_EIRQ',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'MCS2_CH5_EIRQ',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'MCS2_CH6_EIRQ',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'MCS2_CH7_EIRQ',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,169,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36984
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ICM',0,5,133,59,25,68,13
	.byte	'IRQG_0',0
	.word	31669
	.byte	4,2,35,0,13
	.byte	'IRQG_1',0
	.word	32417
	.byte	4,2,35,4,13
	.byte	'IRQG_2',0
	.word	33088
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1535
	.byte	4,2,35,12,13
	.byte	'IRQG_4',0
	.word	33759
	.byte	4,2,35,16,13
	.byte	'reserved_14',0
	.word	1535
	.byte	4,2,35,20,13
	.byte	'IRQG_6',0
	.word	34611
	.byte	4,2,35,24,13
	.byte	'reserved_1C',0
	.word	3354
	.byte	8,2,35,28,13
	.byte	'IRQG_9',0
	.word	35483
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3354
	.byte	8,2,35,40,13
	.byte	'IRQG_MEI',0
	.word	35941
	.byte	4,2,35,48,13
	.byte	'IRQG_CEI0',0
	.word	36246
	.byte	4,2,35,52,13
	.byte	'IRQG_CEI1',0
	.word	36944
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	1535
	.byte	4,2,35,60,13
	.byte	'IRQG_CEI3',0
	.word	37642
	.byte	4,2,35,64,0,16
	.word	37682
	.byte	14,188,3
	.word	490
	.byte	15,187,3,0,10
	.byte	'_Ifx_GTM_SPE_CTRL_STAT_Bits',0,5,246,27,16,4,11
	.byte	'SPE_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'SIE0',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'SIE1',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'SIE2',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRIG_SEL',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'TIM_SEL',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'FSOM',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'SPE_PAT_PTR',0,4
	.word	8543
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'AIP',0,4
	.word	8543
	.byte	3,17,2,35,0,11
	.byte	'ADIR',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'PIP',0,4
	.word	8543
	.byte	3,13,2,35,0,11
	.byte	'PDIR',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'NIP',0,4
	.word	8543
	.byte	3,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'FSOL',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,153,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37993
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_PAT_Bits',0,5,204,28,16,4,11
	.byte	'IP0_VAL',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'IP0_PAT',0,4
	.word	8543
	.byte	3,28,2,35,0,11
	.byte	'IP1_VAL',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'IP1_PAT',0,4
	.word	8543
	.byte	3,24,2,35,0,11
	.byte	'IP2_VAL',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'IP2_PAT',0,4
	.word	8543
	.byte	3,20,2,35,0,11
	.byte	'IP3_VAL',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'IP3_PAT',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'IP4_VAL',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'IP4_PAT',0,4
	.word	8543
	.byte	3,12,2,35,0,11
	.byte	'IP5_VAL',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'IP5_PAT',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'IP6_VAL',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'IP6_PAT',0,4
	.word	8543
	.byte	3,4,2,35,0,11
	.byte	'IP7_VAL',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'IP7_PAT',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,217,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38351
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_OUT_PAT_Bits',0,5,197,28,16,4,11
	.byte	'SPE_OUT_PAT',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,209,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38724
	.byte	4,2,35,0,0,14,32
	.word	38803
	.byte	15,7,0,10
	.byte	'_Ifx_GTM_SPE_OUT_CTRL_Bits',0,5,190,28,16,4,11
	.byte	'SPE_OUT_CTRL',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,201,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38852
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_IRQ_NOTIFY_Bits',0,5,179,28,16,4,11
	.byte	'SPE_NIPD',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'SPE_DCHG',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'SPE_PERR',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'SPE_BIS',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'SPE_RCMP',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	27,0,2,35,0,0,12,5,193,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38973
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_IRQ_EN_Bits',0,5,150,28,16,4,11
	.byte	'SPE_NIPD_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'SPE_DCHG_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'SPE_PERR_IRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'SPE_BIS_IRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'SPE_RCMP_IRQ_EN',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	27,0,2,35,0,0,12,5,169,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39170
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_IRQ_FORCINT_Bits',0,5,161,28,16,4,11
	.byte	'TRG_SPE_NIPD',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_SPE_DCHG',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_SPE_PERR',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG_SPE_BIS',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRG_SPE_RCMP',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	27,0,2,35,0,0,12,5,177,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39398
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_IRQ_MODE_Bits',0,5,172,28,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,185,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39616
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_EIRQ_EN_Bits',0,5,139,28,16,4,11
	.byte	'SPE_NIPD_EIRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'SPE_DCHG_EIRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'SPE_PERR_EIRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'SPE_BIS_EIRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'SPE_RCMP_EIRQ_EN',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	27,0,2,35,0,0,12,5,161,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39732
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_CNT_Bits',0,5,239,27,16,4,11
	.byte	'CNT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,145,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39966
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_SPE_CMP_Bits',0,5,232,27,16,4,11
	.byte	'CMP',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,137,51,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40073
	.byte	4,2,35,0,0,14,56
	.word	490
	.byte	15,55,0,10
	.byte	'_Ifx_GTM_SPE',0,5,208,59,25,128,1,13
	.byte	'CTRL_STAT',0
	.word	38311
	.byte	4,2,35,0,13
	.byte	'PAT',0
	.word	38684
	.byte	4,2,35,4,13
	.byte	'OUT_PAT',0
	.word	38843
	.byte	32,2,35,8,13
	.byte	'OUT_CTRL',0
	.word	38933
	.byte	4,2,35,40,13
	.byte	'IRQ_NOTIFY',0
	.word	39130
	.byte	4,2,35,44,13
	.byte	'IRQ_EN',0
	.word	39358
	.byte	4,2,35,48,13
	.byte	'IRQ_FORCINT',0
	.word	39576
	.byte	4,2,35,52,13
	.byte	'IRQ_MODE',0
	.word	39692
	.byte	4,2,35,56,13
	.byte	'EIRQ_EN',0
	.word	39926
	.byte	4,2,35,60,13
	.byte	'CNT',0
	.word	40033
	.byte	4,2,35,64,13
	.byte	'CMP',0
	.word	40140
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	40180
	.byte	56,2,35,72,0,14,128,2
	.word	40189
	.byte	15,1,0,16
	.word	40416
	.byte	14,128,12
	.word	490
	.byte	15,255,11,0,10
	.byte	'_Ifx_GTM_MAP_CTRL_Bits',0,5,171,23,16,4,11
	.byte	'TSEL',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'SSL',0,4
	.word	8543
	.byte	3,28,2,35,0,11
	.byte	'LSEL',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	11,16,2,35,0,11
	.byte	'TSPP0_EN',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TSPP0_DLD',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'reserved_18',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'TSPP0_I0V',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TSPP0_I1V',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TSPP0_I2V',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'TSPP1_EN',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TSPP1_DLD',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'TSPP1_I0V',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'TSPP1_I1V',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'TSPP1_I2V',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,169,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40442
	.byte	4,2,35,0,0,14,60
	.word	490
	.byte	15,59,0,10
	.byte	'_Ifx_GTM_MCFG_CTRL_Bits',0,5,194,23,16,4,11
	.byte	'MEM0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'MEM1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'MEM2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8543
	.byte	26,0,2,35,0,0,12,5,177,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40890
	.byte	4,2,35,0,0,14,188,1
	.word	490
	.byte	15,187,1,0,10
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,5,248,29,16,4,11
	.byte	'GPR0',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,225,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41042
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,5,255,29,16,4,11
	.byte	'GPR1',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,233,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41147
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,5,165,29,16,4,11
	.byte	'CNT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,161,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41252
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,5,208,29,16,4,11
	.byte	'ECNT',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,185,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41362
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,5,172,29,16,4,11
	.byte	'CNTS',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,169,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41474
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,5,177,30,16,4,11
	.byte	'TO_CNT',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,145,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41579
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,5,184,30,16,4,11
	.byte	'TOV',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	20,4,2,35,0,11
	.byte	'TCS',0,4
	.word	8543
	.byte	3,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,153,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41692
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,5,241,29,16,4,11
	.byte	'FLT_RE',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,217,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41840
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,5,234,29,16,4,11
	.byte	'FLT_FE',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,209,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41956
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,5,179,29,16,4,11
	.byte	'TIM_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TIM_MODE',0,4
	.word	8543
	.byte	3,28,2,35,0,11
	.byte	'OSM',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'CICTRL',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TBU0_SEL',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'GPR0_SEL',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'GPR1_SEL',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'CNTS_SEL',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'DSL',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'ISL',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'ECNT_RESET',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'FLT_EN',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'FLT_CNT_FRQ',0,4
	.word	8543
	.byte	2,13,2,35,0,11
	.byte	'EXT_CAP_EN',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'FLT_MODE_RE',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'FLT_CTR_RE',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'FLT_MODE_FE',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'FLT_CTR_FE',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'CLK_SEL',0,4
	.word	8543
	.byte	3,5,2,35,0,11
	.byte	'FR_ECNT_OFL',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'EGPR0_SEL',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'EGPR1_SEL',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'TOCTRL',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,177,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,5,215,29,16,4,11
	.byte	'EXT_CAP_SRC',0,4
	.word	8543
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,193,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42621
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,5,165,30,16,4,11
	.byte	'NEWVAL',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'GPROFL',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TODET',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8543
	.byte	26,0,2,35,0,0,12,5,137,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42740
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,5,134,30,16,4,11
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'GPROFL_IRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TODET_IRQ_EN',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8543
	.byte	26,0,2,35,0,0,12,5,241,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42952
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,5,146,30,16,4,11
	.byte	'TRG_NEWVAL',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_ECNTOFL',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_CNTOFL',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG_GPROFL',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRG_TODET',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TRG_GLITCHDET',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8543
	.byte	26,0,2,35,0,0,12,5,249,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43202
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,5,158,30,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,129,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43439
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,5,222,29,16,4,11
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'GPROFL_EIRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TODET_EIRQ_EN',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8543
	.byte	26,0,2,35,0,0,12,5,201,52,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_CH',0,5,221,56,25,120,13
	.byte	'GPR0',0
	.word	41107
	.byte	4,2,35,0,13
	.byte	'GPR1',0
	.word	41212
	.byte	4,2,35,4,13
	.byte	'CNT',0
	.word	41322
	.byte	4,2,35,8,13
	.byte	'ECNT',0
	.word	41434
	.byte	4,2,35,12,13
	.byte	'CNTS',0
	.word	41539
	.byte	4,2,35,16,13
	.byte	'TDUC',0
	.word	41652
	.byte	4,2,35,20,13
	.byte	'TDUV',0
	.word	41800
	.byte	4,2,35,24,13
	.byte	'FLT_RE',0
	.word	41916
	.byte	4,2,35,28,13
	.byte	'FLT_FE',0
	.word	42032
	.byte	4,2,35,32,13
	.byte	'CTRL',0
	.word	42581
	.byte	4,2,35,36,13
	.byte	'ECTRL',0
	.word	42700
	.byte	4,2,35,40,13
	.byte	'IRQ_NOTIFY',0
	.word	42912
	.byte	4,2,35,44,13
	.byte	'IRQ_EN',0
	.word	43162
	.byte	4,2,35,48,13
	.byte	'IRQ_FORCINT',0
	.word	43399
	.byte	4,2,35,52,13
	.byte	'IRQ_MODE',0
	.word	43518
	.byte	4,2,35,56,13
	.byte	'EIRQ_EN',0
	.word	43775
	.byte	4,2,35,60,13
	.byte	'reserved_40',0
	.word	40180
	.byte	56,2,35,64,0,16
	.word	43815
	.byte	10
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,5,193,30,16,4,11
	.byte	'VAL_0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'MODE_0',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'VAL_1',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'MODE_1',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'VAL_2',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'MODE_2',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'VAL_3',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'MODE_3',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'VAL_4',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'MODE_4',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'VAL_5',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'MODE_5',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'VAL_6',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'MODE_6',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'VAL_7',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'MODE_7',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,161,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44114
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,5,214,30,16,4,11
	.byte	'RST_CH0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,169,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44466
	.byte	4,2,35,0,0,16
	.word	43815
	.byte	16
	.word	43815
	.byte	16
	.word	43815
	.byte	16
	.word	43815
	.byte	16
	.word	43815
	.byte	16
	.word	43815
	.byte	16
	.word	43815
	.byte	14,136,8
	.word	490
	.byte	15,135,8,0,10
	.byte	'_Ifx_GTM_TIM',0,5,237,59,25,128,16,13
	.byte	'CH0',0
	.word	44109
	.byte	120,2,35,0,13
	.byte	'IN_SRC',0
	.word	44426
	.byte	4,2,35,120,13
	.byte	'RST',0
	.word	44669
	.byte	4,2,35,124,13
	.byte	'CH1',0
	.word	44709
	.byte	120,3,35,128,1,13
	.byte	'reserved_F8',0
	.word	3354
	.byte	8,3,35,248,1,13
	.byte	'CH2',0
	.word	44714
	.byte	120,3,35,128,2,13
	.byte	'reserved_178',0
	.word	3354
	.byte	8,3,35,248,2,13
	.byte	'CH3',0
	.word	44719
	.byte	120,3,35,128,3,13
	.byte	'reserved_1F8',0
	.word	3354
	.byte	8,3,35,248,3,13
	.byte	'CH4',0
	.word	44724
	.byte	120,3,35,128,4,13
	.byte	'reserved_278',0
	.word	3354
	.byte	8,3,35,248,4,13
	.byte	'CH5',0
	.word	44729
	.byte	120,3,35,128,5,13
	.byte	'reserved_2F8',0
	.word	3354
	.byte	8,3,35,248,5,13
	.byte	'CH6',0
	.word	44734
	.byte	120,3,35,128,6,13
	.byte	'reserved_378',0
	.word	3354
	.byte	8,3,35,248,6,13
	.byte	'CH7',0
	.word	44739
	.byte	120,3,35,128,7,13
	.byte	'reserved_3F8',0
	.word	44744
	.byte	136,8,3,35,248,7,0,14,128,48
	.word	44755
	.byte	15,2,0,16
	.word	45077
	.byte	14,128,176,1
	.word	490
	.byte	15,255,175,1,0,10
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,5,249,30,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	11,21,2,35,0,11
	.byte	'SL',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC_SR',0,4
	.word	8543
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8543
	.byte	5,12,2,35,0,11
	.byte	'RST_CCU0',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'BITREV',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'SPEM',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'GCM',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,201,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45105
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,5,170,31,16,4,11
	.byte	'SR0',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,241,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45431
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,5,177,31,16,4,11
	.byte	'SR1',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,249,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45541
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,5,228,30,16,4,11
	.byte	'CM0',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,177,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45651
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,5,235,30,16,4,11
	.byte	'CM1',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,185,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45761
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,5,242,30,16,4,11
	.byte	'CN0',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,193,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45871
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,5,184,31,16,4,11
	.byte	'OL',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	31,0,2,35,0,0,12,5,129,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45981
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,5,162,31,16,4,11
	.byte	'CCU0TC',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,233,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46090
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,5,139,31,16,4,11
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,209,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46227
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,5,147,31,16,4,11
	.byte	'TRG_CCU0TC0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_CCU1TC0',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,217,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46374
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,5,155,31,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,225,53,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46522
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_CH',0,5,243,56,25,48,13
	.byte	'CTRL',0
	.word	45391
	.byte	4,2,35,0,13
	.byte	'SR0',0
	.word	45501
	.byte	4,2,35,4,13
	.byte	'SR1',0
	.word	45611
	.byte	4,2,35,8,13
	.byte	'CM0',0
	.word	45721
	.byte	4,2,35,12,13
	.byte	'CM1',0
	.word	45831
	.byte	4,2,35,16,13
	.byte	'CN0',0
	.word	45941
	.byte	4,2,35,20,13
	.byte	'STAT',0
	.word	46050
	.byte	4,2,35,24,13
	.byte	'IRQ_NOTIFY',0
	.word	46187
	.byte	4,2,35,28,13
	.byte	'IRQ_EN',0
	.word	46334
	.byte	4,2,35,32,13
	.byte	'IRQ_FORCINT',0
	.word	46482
	.byte	4,2,35,36,13
	.byte	'IRQ_MODE',0
	.word	46601
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	1535
	.byte	4,2,35,44,0,16
	.word	46641
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,5,249,31,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,169,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46858
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,5,191,31,16,4,11
	.byte	'ACT_TB',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	8543
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,137,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47308
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,5,228,31,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,161,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47464
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,5,144,32,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,177,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47896
	.byte	4,2,35,0,0,16
	.word	46641
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,5,200,31,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,145,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48171
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,5,214,31,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,153,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48459
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,5,158,32,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,185,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48747
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,5,172,32,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,193,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49035
	.byte	4,2,35,0,0,16
	.word	46641
	.byte	14,16
	.word	490
	.byte	15,15,0,16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,5,244,32,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,233,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49367
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,5,186,32,16,4,11
	.byte	'ACT_TB',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	8543
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,201,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49817
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,5,223,32,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,225,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49973
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,5,139,33,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,241,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50405
	.byte	4,2,35,0,0,16
	.word	46641
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,5,195,32,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,209,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,5,209,32,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,217,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50968
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,5,153,33,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,249,54,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51256
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,5,167,33,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,129,55,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51544
	.byte	4,2,35,0,0,16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	16
	.word	46641
	.byte	14,144,8
	.word	490
	.byte	15,143,8,0,10
	.byte	'_Ifx_GTM_TOM',0,5,131,60,25,128,16,13
	.byte	'CH0',0
	.word	46853
	.byte	48,2,35,0,13
	.byte	'TGC0_GLB_CTRL',0
	.word	47268
	.byte	4,2,35,48,13
	.byte	'TGC0_ACT_TB',0
	.word	47424
	.byte	4,2,35,52,13
	.byte	'TGC0_FUPD_CTRL',0
	.word	47856
	.byte	4,2,35,56,13
	.byte	'TGC0_INT_TRIG',0
	.word	48126
	.byte	4,2,35,60,13
	.byte	'CH1',0
	.word	48166
	.byte	48,2,35,64,13
	.byte	'TGC0_ENDIS_CTRL',0
	.word	48419
	.byte	4,2,35,112,13
	.byte	'TGC0_ENDIS_STAT',0
	.word	48707
	.byte	4,2,35,116,13
	.byte	'TGC0_OUTEN_CTRL',0
	.word	48995
	.byte	4,2,35,120,13
	.byte	'TGC0_OUTEN_STAT',0
	.word	49283
	.byte	4,2,35,124,13
	.byte	'CH2',0
	.word	49323
	.byte	48,3,35,128,1,13
	.byte	'reserved_B0',0
	.word	49328
	.byte	16,3,35,176,1,13
	.byte	'CH3',0
	.word	49337
	.byte	48,3,35,192,1,13
	.byte	'reserved_F0',0
	.word	49328
	.byte	16,3,35,240,1,13
	.byte	'CH4',0
	.word	49342
	.byte	48,3,35,128,2,13
	.byte	'reserved_130',0
	.word	49328
	.byte	16,3,35,176,2,13
	.byte	'CH5',0
	.word	49347
	.byte	48,3,35,192,2,13
	.byte	'reserved_170',0
	.word	49328
	.byte	16,3,35,240,2,13
	.byte	'CH6',0
	.word	49352
	.byte	48,3,35,128,3,13
	.byte	'reserved_1B0',0
	.word	49328
	.byte	16,3,35,176,3,13
	.byte	'CH7',0
	.word	49357
	.byte	48,3,35,192,3,13
	.byte	'reserved_1F0',0
	.word	49328
	.byte	16,3,35,240,3,13
	.byte	'CH8',0
	.word	49362
	.byte	48,3,35,128,4,13
	.byte	'TGC1_GLB_CTRL',0
	.word	49777
	.byte	4,3,35,176,4,13
	.byte	'TGC1_ACT_TB',0
	.word	49933
	.byte	4,3,35,180,4,13
	.byte	'TGC1_FUPD_CTRL',0
	.word	50365
	.byte	4,3,35,184,4,13
	.byte	'TGC1_INT_TRIG',0
	.word	50635
	.byte	4,3,35,188,4,13
	.byte	'CH9',0
	.word	50675
	.byte	48,3,35,192,4,13
	.byte	'TGC1_ENDIS_CTRL',0
	.word	50928
	.byte	4,3,35,240,4,13
	.byte	'TGC1_ENDIS_STAT',0
	.word	51216
	.byte	4,3,35,244,4,13
	.byte	'TGC1_OUTEN_CTRL',0
	.word	51504
	.byte	4,3,35,248,4,13
	.byte	'TGC1_OUTEN_STAT',0
	.word	51792
	.byte	4,3,35,252,4,13
	.byte	'CH10',0
	.word	51832
	.byte	48,3,35,128,5,13
	.byte	'reserved_2B0',0
	.word	49328
	.byte	16,3,35,176,5,13
	.byte	'CH11',0
	.word	51837
	.byte	48,3,35,192,5,13
	.byte	'reserved_2F0',0
	.word	49328
	.byte	16,3,35,240,5,13
	.byte	'CH12',0
	.word	51842
	.byte	48,3,35,128,6,13
	.byte	'reserved_330',0
	.word	49328
	.byte	16,3,35,176,6,13
	.byte	'CH13',0
	.word	51847
	.byte	48,3,35,192,6,13
	.byte	'reserved_370',0
	.word	49328
	.byte	16,3,35,240,6,13
	.byte	'CH14',0
	.word	51852
	.byte	48,3,35,128,7,13
	.byte	'reserved_3B0',0
	.word	49328
	.byte	16,3,35,176,7,13
	.byte	'CH15',0
	.word	51857
	.byte	48,3,35,192,7,13
	.byte	'reserved_3F0',0
	.word	51862
	.byte	144,8,3,35,240,7,0,14,128,32
	.word	51873
	.byte	15,1,0,16
	.word	52787
	.byte	14,128,128,1
	.word	490
	.byte	15,255,127,0,10
	.byte	'_Ifx_GTM_ATOM_CH_RDADDR_Bits',0,5,171,3,16,4,11
	.byte	'RDADDR0',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	7,16,2,35,0,11
	.byte	'RDADDR1',0,4
	.word	8543
	.byte	9,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8543
	.byte	7,0,2,35,0,0,12,5,224,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_CTRL_Bits',0,5,245,2,16,4,11
	.byte	'MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'TB12_SEL',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ACB',0,4
	.word	8543
	.byte	5,23,2,35,0,11
	.byte	'CMP_CTRL',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'SL',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC',0,4
	.word	8543
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'WR_REQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8543
	.byte	3,12,2,35,0,11
	.byte	'RST_CCU0',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'SLA',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'ABM',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,184,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52973
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_SOMC_Bits',0,5,180,3,16,4,11
	.byte	'MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'TB12_SEL',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ACB10',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'ACB42',0,4
	.word	8543
	.byte	3,23,2,35,0,11
	.byte	'CMP_CTRL',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'SL',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'WR_REQ',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8543
	.byte	7,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'SLA',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'ABM',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,232,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53386
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_SOMI_Bits',0,5,201,3,16,4,11
	.byte	'MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ACB0',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	6,21,2,35,0,11
	.byte	'SL',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	20,0,2,35,0,0,12,5,240,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53764
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_SOMP_Bits',0,5,213,3,16,4,11
	.byte	'MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ADL',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8543
	.byte	5,21,2,35,0,11
	.byte	'SL',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC_SR',0,4
	.word	8543
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8543
	.byte	5,12,2,35,0,11
	.byte	'RST_CCU0',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,248,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53969
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_SOMS_Bits',0,5,232,3,16,4,11
	.byte	'MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ACB0',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	6,21,2,35,0,11
	.byte	'SL',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC',0,4
	.word	8543
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8543
	.byte	11,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,128,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54318
	.byte	4,2,35,0,0,12,5,177,55,5,4,13
	.byte	'CTRL',0
	.word	53346
	.byte	4,2,35,0,13
	.byte	'SOMC',0
	.word	53724
	.byte	4,2,35,0,13
	.byte	'SOMI',0
	.word	53929
	.byte	4,2,35,0,13
	.byte	'SOMP',0
	.word	54278
	.byte	4,2,35,0,13
	.byte	'SOMS',0
	.word	54540
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_SR0_Bits',0,5,247,3,16,4,11
	.byte	'SR0',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,136,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54657
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_SR1_Bits',0,5,254,3,16,4,11
	.byte	'SR1',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,144,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54768
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_CM0_Bits',0,5,224,2,16,4,11
	.byte	'CM0',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,160,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54879
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_CM1_Bits',0,5,231,2,16,4,11
	.byte	'CM1',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,168,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54990
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_CN0_Bits',0,5,238,2,16,4,11
	.byte	'CN0',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,176,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55101
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_STAT_Bits',0,5,133,4,16,4,11
	.byte	'OL',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	15,16,2,35,0,11
	.byte	'ACBI',0,4
	.word	8543
	.byte	5,11,2,35,0,11
	.byte	'DV',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'WRF',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'ACBO',0,4
	.word	8543
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,152,36,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55212
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_NOTIFY_Bits',0,5,163,3,16,4,11
	.byte	'CCU0TC',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,216,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55429
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_EN_Bits',0,5,140,3,16,4,11
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,192,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55567
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_FORCINT_Bits',0,5,148,3,16,4,11
	.byte	'TRG_CCU0TC',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_CCU1TC',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,200,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55715
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_MODE_Bits',0,5,156,3,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,208,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55862
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_CH',0,5,174,55,25,64,13
	.byte	'RDADDR',0
	.word	52933
	.byte	4,2,35,0,21
	.word	54580
	.byte	4,2,35,4,13
	.byte	'SR0',0
	.word	54728
	.byte	4,2,35,8,13
	.byte	'SR1',0
	.word	54839
	.byte	4,2,35,12,13
	.byte	'CM0',0
	.word	54950
	.byte	4,2,35,16,13
	.byte	'CM1',0
	.word	55061
	.byte	4,2,35,20,13
	.byte	'CN0',0
	.word	55172
	.byte	4,2,35,24,13
	.byte	'STAT',0
	.word	55389
	.byte	4,2,35,28,13
	.byte	'IRQ_NOTIFY',0
	.word	55527
	.byte	4,2,35,32,13
	.byte	'IRQ_EN',0
	.word	55675
	.byte	4,2,35,36,13
	.byte	'IRQ_FORCINT',0
	.word	55822
	.byte	4,2,35,40,13
	.byte	'IRQ_MODE',0
	.word	55942
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	49328
	.byte	16,2,35,48,0,16
	.word	55982
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_GLB_CTRL_Bits',0,5,159,2,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,128,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56211
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_AGC_ENDIS_CTRL_Bits',0,5,238,1,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,232,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56661
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_AGC_ENDIS_STAT_Bits',0,5,252,1,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,240,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56949
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_AGC_ACT_TB_Bits',0,5,229,1,16,4,11
	.byte	'ACT_TB',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	8543
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,224,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57237
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_AGC_OUTEN_CTRL_Bits',0,5,196,2,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,144,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57393
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_AGC_OUTEN_STAT_Bits',0,5,210,2,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,152,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57681
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_AGC_FUPD_CTRL_Bits',0,5,138,2,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,248,34,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57969
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ATOM_AGC_INT_TRIG_Bits',0,5,182,2,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,136,35,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58401
	.byte	4,2,35,0,0,14,32
	.word	490
	.byte	15,31,0,10
	.byte	'_Ifx_GTM_ATOM_AGC',0,5,160,55,25,64,13
	.byte	'GLB_CTRL',0
	.word	56621
	.byte	4,2,35,0,13
	.byte	'ENDIS_CTRL',0
	.word	56909
	.byte	4,2,35,4,13
	.byte	'ENDIS_STAT',0
	.word	57197
	.byte	4,2,35,8,13
	.byte	'ACT_TB',0
	.word	57353
	.byte	4,2,35,12,13
	.byte	'OUTEN_CTRL',0
	.word	57641
	.byte	4,2,35,16,13
	.byte	'OUTEN_STAT',0
	.word	57929
	.byte	4,2,35,20,13
	.byte	'FUPD_CTRL',0
	.word	58361
	.byte	4,2,35,24,13
	.byte	'INT_TRIG',0
	.word	58631
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	58671
	.byte	32,2,35,32,0,16
	.word	58680
	.byte	16
	.word	55982
	.byte	14,64
	.word	490
	.byte	15,63,0,16
	.word	55982
	.byte	16
	.word	55982
	.byte	16
	.word	55982
	.byte	16
	.word	55982
	.byte	16
	.word	55982
	.byte	16
	.word	55982
	.byte	14,192,8
	.word	490
	.byte	15,191,8,0,10
	.byte	'_Ifx_GTM_ATOM',0,5,166,57,25,128,16,13
	.byte	'CH0',0
	.word	56206
	.byte	64,2,35,0,13
	.byte	'AGC',0
	.word	58877
	.byte	64,2,35,64,13
	.byte	'CH1',0
	.word	58882
	.byte	64,3,35,128,1,13
	.byte	'reserved_C0',0
	.word	58887
	.byte	64,3,35,192,1,13
	.byte	'CH2',0
	.word	58896
	.byte	64,3,35,128,2,13
	.byte	'reserved_140',0
	.word	58887
	.byte	64,3,35,192,2,13
	.byte	'CH3',0
	.word	58901
	.byte	64,3,35,128,3,13
	.byte	'reserved_1C0',0
	.word	58887
	.byte	64,3,35,192,3,13
	.byte	'CH4',0
	.word	58906
	.byte	64,3,35,128,4,13
	.byte	'reserved_240',0
	.word	58887
	.byte	64,3,35,192,4,13
	.byte	'CH5',0
	.word	58911
	.byte	64,3,35,128,5,13
	.byte	'reserved_2C0',0
	.word	58887
	.byte	64,3,35,192,5,13
	.byte	'CH6',0
	.word	58916
	.byte	64,3,35,128,6,13
	.byte	'reserved_340',0
	.word	58887
	.byte	64,3,35,192,6,13
	.byte	'CH7',0
	.word	58921
	.byte	64,3,35,128,7,13
	.byte	'reserved_3C0',0
	.word	58926
	.byte	192,8,3,35,192,7,0,14,128,64
	.word	58937
	.byte	15,3,0,16
	.word	59244
	.byte	14,128,160,2
	.word	490
	.byte	15,255,159,2,0,10
	.byte	'_Ifx_GTM_F2A_RD_CH_ARU_RD_FIFO_Bits',0,5,235,18,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	23,0,2,35,0,0,12,5,233,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59272
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_F2A_RD_CH',0,5,231,55,25,4,13
	.byte	'ARU_RD_FIFO',0
	.word	59353
	.byte	4,2,35,0,0,14,32
	.word	59393
	.byte	15,7,0,16
	.word	59440
	.byte	10
	.byte	'_Ifx_GTM_F2A_STR_CH_STR_CFG_Bits',0,5,242,18,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'TMODE',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'DIR',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8543
	.byte	13,0,2,35,0,0,12,5,241,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59454
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_F2A_STR_CH',0,5,237,55,25,4,13
	.byte	'STR_CFG',0
	.word	59571
	.byte	4,2,35,0,0,14,32
	.word	59611
	.byte	15,7,0,16
	.word	59655
	.byte	10
	.byte	'_Ifx_GTM_F2A_ENABLE_Bits',0,5,221,18,16,4,11
	.byte	'STR0_EN',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'STR1_EN',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'STR2_EN',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'STR3_EN',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'STR4_EN',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'STR5_EN',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'STR6_EN',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'STR7_EN',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,225,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59669
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_F2A',0,5,247,58,25,68,13
	.byte	'RD_CH',0
	.word	59449
	.byte	32,2,35,0,13
	.byte	'STR_CH',0
	.word	59664
	.byte	32,2,35,32,13
	.byte	'ENABLE',0
	.word	59876
	.byte	4,2,35,64,0,16
	.word	59916
	.byte	10
	.byte	'_Ifx_GTM_AFD_CH_BUF_ACC_Bits',0,5,122,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,240,33,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59988
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_AFD_CH',0,5,153,55,25,16,13
	.byte	'BUF_ACC',0
	.word	60062
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3694
	.byte	12,2,35,4,0,14,128,1
	.word	60102
	.byte	15,7,0,16
	.word	60162
	.byte	10
	.byte	'_Ifx_GTM_AFD',0,5,142,57,25,128,1,13
	.byte	'CH',0
	.word	60172
	.byte	128,1,2,35,0,0,16
	.word	60177
	.byte	14,128,6
	.word	490
	.byte	15,255,5,0,10
	.byte	'_Ifx_GTM_FIFO_CH_CTRL_Bits',0,5,251,18,16,4,11
	.byte	'RBM',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'RAP',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'FLUSH',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'WULOCK',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,249,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60227
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_END_ADDR_Bits',0,5,143,19,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,137,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60388
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_START_ADDR_Bits',0,5,210,19,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,201,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_UPPER_WM_Bits',0,5,227,19,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,217,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60624
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_LOWER_WM_Bits',0,5,196,19,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,185,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60741
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_STATUS_Bits',0,5,217,19,16,4,11
	.byte	'EMPTY',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'FULL',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'LOW_WM',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'UP_WM',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,209,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60858
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_FILL_LEVEL_Bits',0,5,150,19,16,4,11
	.byte	'LEVEL',0,4
	.word	8543
	.byte	11,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	21,0,2,35,0,0,12,5,145,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61024
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_WR_PTR_Bits',0,5,234,19,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,225,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61144
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_RD_PTR_Bits',0,5,203,19,16,4,11
	.byte	'ADDR',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,193,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61259
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_NOTIFY_Bits',0,5,186,19,16,4,11
	.byte	'FIFO_EMPTY',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'FIFO_FULL',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'FIFO_LWM',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'FIFO_UWM',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,177,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61374
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_EN_Bits',0,5,157,19,16,4,11
	.byte	'FIFO_EMPTY_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'FIFO_FULL_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'FIFO_LWM_IRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'FIFO_UWM_IRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,153,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61559
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_FORCINT_Bits',0,5,167,19,16,4,11
	.byte	'TRG_FIFO_EMPTY',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_FIFO_FULL',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_FIFO_LWM',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG_FIFO_UWM',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,161,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61768
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_MODE_Bits',0,5,177,19,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'DMA_HYSTERESIS',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'DMA_HYST_DIR',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,169,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61970
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH_EIRQ_EN_Bits',0,5,133,19,16,4,11
	.byte	'FIFO_EMPTY_EIRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'FIFO_FULL_EIRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'FIFO_LWM_EIRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'FIFO_UWM_EIRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'Reserved',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,129,46,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62140
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_FIFO_CH',0,5,243,55,25,64,13
	.byte	'CTRL',0
	.word	60348
	.byte	4,2,35,0,13
	.byte	'END_ADDR',0
	.word	60465
	.byte	4,2,35,4,13
	.byte	'START_ADDR',0
	.word	60584
	.byte	4,2,35,8,13
	.byte	'UPPER_WM',0
	.word	60701
	.byte	4,2,35,12,13
	.byte	'LOWER_WM',0
	.word	60818
	.byte	4,2,35,16,13
	.byte	'STATUS',0
	.word	60984
	.byte	4,2,35,20,13
	.byte	'FILL_LEVEL',0
	.word	61104
	.byte	4,2,35,24,13
	.byte	'WR_PTR',0
	.word	61219
	.byte	4,2,35,28,13
	.byte	'RD_PTR',0
	.word	61334
	.byte	4,2,35,32,13
	.byte	'IRQ_NOTIFY',0
	.word	61519
	.byte	4,2,35,36,13
	.byte	'IRQ_EN',0
	.word	61728
	.byte	4,2,35,40,13
	.byte	'IRQ_FORCINT',0
	.word	61930
	.byte	4,2,35,44,13
	.byte	'IRQ_MODE',0
	.word	62100
	.byte	4,2,35,48,13
	.byte	'EIRQ_EN',0
	.word	62312
	.byte	4,2,35,52,13
	.byte	'reserved_38',0
	.word	3354
	.byte	8,2,35,56,0,14,128,4
	.word	62352
	.byte	15,7,0,16
	.word	62645
	.byte	10
	.byte	'_Ifx_GTM_FIFO',0,5,255,58,25,128,4,13
	.byte	'CH',0
	.word	62655
	.byte	128,4,2,35,0,0,16
	.word	62660
	.byte	14,128,244,3
	.word	490
	.byte	15,255,243,3,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_0_Bits',0,5,144,12,16,4,11
	.byte	'MLT',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'IFP',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'SNU',0,4
	.word	8543
	.byte	5,16,2,35,0,11
	.byte	'TNU',0,4
	.word	8543
	.byte	9,7,2,35,0,11
	.byte	'AMS',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'AMT',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'IDS',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'IDT',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'SEN',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'TEN',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'RMO',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,224,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62713
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_1_Bits',0,5,186,12,16,4,11
	.byte	'DMO',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'DEN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'IDDS',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'COA',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'PIT',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'SGE1',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'DLM1',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'PCM1',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'SGE2',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'DLM2',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'PCM2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'SYN_NS',0,4
	.word	8543
	.byte	5,16,2,35,0,11
	.byte	'SYN_NT',0,4
	.word	8543
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'LCD',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'SWR',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'SYSF',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TS0_HRS',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'TS0_HRT',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'SMC',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'SSL',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'TSL',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,248,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62951
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_2_Bits',0,5,226,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'AEN0',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'AEN1',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'AEN2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'AEN3',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'AEN4',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'AEN5',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'AEN6',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'AEN7',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'WAD0',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'WAD1',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'WAD2',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'WAD3',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'WAD4',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'WAD5',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'WAD6',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'WAD7',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,136,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63384
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_3_Bits',0,5,249,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'AEN8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'AEN9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'AEN10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'AEN11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'AEN12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'AEN13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'AEN14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'AEN15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'WAD8',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'WAD9',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'WAD10',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'WAD11',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'WAD12',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'WAD13',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'WAD14',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'WAD15',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,144,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63758
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_4_Bits',0,5,144,13,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'AEN16',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'AEN17',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'AEN18',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'AEN19',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'AEN20',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'AEN21',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'AEN22',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'AEN23',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'WAD16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'WAD17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'WAD18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'WAD19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'WAD20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'WAD21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'WAD22',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'WAD23',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,152,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64144
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_ACT_STA_Bits',0,5,230,10,16,4,11
	.byte	'ACT_Ni',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,192,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64534
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_OSW_Bits',0,5,147,16,16,4,11
	.byte	'SWON_S',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'SWON_T',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	6,24,2,35,0,11
	.byte	'OSS',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,175,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64649
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_AOSV_2_Bits',0,5,145,11,16,4,11
	.byte	'AOSV_2A',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'AOSV_2B',0,4
	.word	8543
	.byte	8,16,2,35,0,11
	.byte	'AOSV_2C',0,4
	.word	8543
	.byte	8,8,2,35,0,11
	.byte	'AOSV_2D',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,240,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64815
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_APT_Bits',0,5,192,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'WAPT',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'APT',0,4
	.word	8543
	.byte	10,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'WAPT_2B',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'APT_2B',0,4
	.word	8543
	.byte	10,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,144,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64964
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_APS_Bits',0,5,162,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'WAPS',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'APS',0,4
	.word	8543
	.byte	6,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	5,19,2,35,0,11
	.byte	'WAPS_1C2',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'APS_1C2',0,4
	.word	8543
	.byte	6,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,248,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65170
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_APT_2C_Bits',0,5,184,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'APT_2C',0,4
	.word	8543
	.byte	10,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	20,0,2,35,0,0,12,5,152,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65377
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_APS_1C3_Bits',0,5,154,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'APS_1C3',0,4
	.word	8543
	.byte	6,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,128,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NUTC_Bits',0,5,132,16,16,4,11
	.byte	'NUTE',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'FST',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	2,19,2,35,0,11
	.byte	'SYN_T',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'SYN_T_OLD',0,4
	.word	8543
	.byte	3,13,2,35,0,11
	.byte	'VTN',0,4
	.word	8543
	.byte	6,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8543
	.byte	4,3,2,35,0,11
	.byte	'WNUT',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'WSYN',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'WVTN',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,167,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65650
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NUSC_Bits',0,5,246,15,16,4,11
	.byte	'NUSE',0,4
	.word	8543
	.byte	6,26,2,35,0,11
	.byte	'FSS',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'SYN_S',0,4
	.word	8543
	.byte	6,19,2,35,0,11
	.byte	'SYN_S_OLD',0,4
	.word	8543
	.byte	6,13,2,35,0,11
	.byte	'VSN',0,4
	.word	8543
	.byte	6,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8543
	.byte	4,3,2,35,0,11
	.byte	'WNUS',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'WSYN',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'WVSN',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,159,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NTI_CNT_Bits',0,5,239,15,16,4,11
	.byte	'NTI_CNT',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,151,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66125
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_IRQ_NOTIFY_Bits',0,5,235,14,16,4,11
	.byte	'PDI',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'PEI',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TINI',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TAXI',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'SISI',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TISI',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MSI',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MTI',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'SASI',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TASI',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'PWI',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'W2I',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'W1I',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'GL1I',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'LL1I',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EI',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'GL2I',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'LL2I',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TE0I',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TE1I',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TE2I',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TE3I',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TE4I',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'CDTI',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'CDSI',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TORI',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'SORI',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'DCGI',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,163,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66241
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_IRQ_EN_Bits',0,5,160,14,16,4,11
	.byte	'PDI_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'PEI_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TINI_IRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TAXI_IRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'SISI_IRQ_EN',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TISI_IRQ_EN',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MSI_IRQ_EN',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MTI_IRQ_EN',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'SASI_IRQ_EN',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TASI_IRQ_EN',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'PWI_IRQ_EN',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'W2I_IRQ_EN',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'W1I_IRQ_EN',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'GL1I_IRQ_EN',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'LL1I_IRQ_EN',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EI_IRQ_EN',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'GL2I_IRQ_EN',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'LL2I_IRQ_EN',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TE0I_IRQ_EN',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TE1I_IRQ_EN',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TE2I_IRQ_EN',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TE3I_IRQ_EN',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TE4I_IRQ_EN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'CDTI_IRQ_EN',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'CDSI_IRQ_EN',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TORI',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'SORI',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'DCGI',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,139,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66780
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_IRQ_FORCINT_Bits',0,5,194,14,16,4,11
	.byte	'TRG_PDI',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_PEI',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_TINI',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG_TAXI',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRG_SISI',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TRG_TISI',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TRG_MSI',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TRG_MTI',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TRG_SASI',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TRG_TASI',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TRG_PWI',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TRG_W2I',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TRG_W1I',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TRG_GL1I',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TRG_LL1I',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TRG_EI',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'TRG_GL2I',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'TRG_LL2I',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TRG_TE0I',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TRG_TE1I',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TRG_TE2I',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TRG_TE3I',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TRG_TE4I',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'TRG_CDTI',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'TRG_CDSI',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TRG_TORI',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'TRG_SORI',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'TRG_DCGI',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,147,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67490
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_IRQ_MODE_Bits',0,5,228,14,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,155,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68142
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_EIRQ_EN_Bits',0,5,218,13,16,4,11
	.byte	'PDI_EIRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'PEI_EIRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TINI_EIRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TAXI_EIRQ_EN',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'SISI_EIRQ_EN',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TISI_EIRQ_EN',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MSI_EIRQ_EN',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MTI_EIRQ_EN',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'SASI_EIRQ_EN',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TASI_EIRQ_EN',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'PWI_EIRQ_EN',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'W2I_EIRQ_EN',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'W1I_EIRQ_EN',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'GL1I_EIRQ_EN',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'LL1I_EIRQ_EN',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EI_EIRQ_EN',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'GL2I_EIRQ_EN',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'LL2I_EIRQ_EN',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'TE0I_EIRQ_EN',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'TE1I_EIRQ_EN',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'TE2I_EIRQ_EN',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'TE3I_EIRQ_EN',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'TE4I_EIRQ_EN',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'CDTI_EIRQ_EN',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'CDSI_EIRQ_EN',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'TORI',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'SORI',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'DCGI',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,218,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68259
	.byte	4,2,35,0,0,14,92
	.word	490
	.byte	15,91,0,10
	.byte	'_Ifx_GTM_DPLL_INC_CNT1_Bits',0,5,145,14,16,4,11
	.byte	'INC_CNT1',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,250,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69004
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_INC_CNT2_Bits',0,5,153,14,16,4,11
	.byte	'INC_CNT2',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,131,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69122
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_APT_SYNC_Bits',0,5,204,11,16,4,11
	.byte	'APT_2B_EXT',0,4
	.word	8543
	.byte	6,26,2,35,0,11
	.byte	'APT_2B_STATUS',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8543
	.byte	7,18,2,35,0,11
	.byte	'APT_2B_OLD',0,4
	.word	8543
	.byte	10,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,160,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69240
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_APS_SYNC_Bits',0,5,174,11,16,4,11
	.byte	'APS_1C2_EXT',0,4
	.word	8543
	.byte	6,26,2,35,0,11
	.byte	'APS_1C2_STATUS',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8543
	.byte	7,18,2,35,0,11
	.byte	'APS_1C2_OLD',0,4
	.word	8543
	.byte	6,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,136,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69429
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TBU_TS0_T_Bits',0,5,212,17,16,4,11
	.byte	'TBU_TS0_T',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,225,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69621
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TBU_TS0_S_Bits',0,5,205,17,16,4,11
	.byte	'TBU_TS0_S',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,217,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69741
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_LD1_Bits',0,5,251,10,16,4,11
	.byte	'ADD_IN_LD_1',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,216,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69861
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_LD2_Bits',0,5,130,11,16,4,11
	.byte	'ADD_IN_LD_2',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,224,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69984
	.byte	4,2,35,0,0,14,44
	.word	490
	.byte	15,43,0,10
	.byte	'_Ifx_GTM_DPLL_STATUS_Bits',0,5,169,17,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'CSO',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'CTO',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'CRO',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'RCS',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'RCT',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'PSE',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'SOR',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'MS',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TOR',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'MT',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'RAM2_ERR',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	2,17,2,35,0,11
	.byte	'LOW_RES',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'CSVS',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'CSVT',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'CAIP2',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'CAIP1',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'ISN',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'ITN',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'BWD2',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'BWD1',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'LOCK2',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'SYS',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'SYT',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'FSD',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'FTD',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'LOCK1',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'ERR',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,209,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70116
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_ID_PMTR_Bits',0,5,138,14,16,4,11
	.byte	'ID_PMTR_x',0,4
	.word	8543
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8543
	.byte	23,0,2,35,0,0,12,5,242,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70703
	.byte	4,2,35,0,0,14,96
	.word	70780
	.byte	15,23,0,14,128,1
	.word	490
	.byte	15,127,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_0_SHADOW_TRIGGER_Bits',0,5,173,12,16,4,11
	.byte	'MLT',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'IFP',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	15,6,2,35,0,11
	.byte	'AMT',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'IDS',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'IDT',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	2,1,2,35,0,11
	.byte	'RMO',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,240,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70839
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_0_SHADOW_STATE_Bits',0,5,160,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'IFP',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	14,7,2,35,0,11
	.byte	'AMS',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'IDS',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	3,1,2,35,0,11
	.byte	'RMO',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,232,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71063
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CTRL_1_SHADOW_TRIGGER_Bits',0,5,213,12,16,4,11
	.byte	'DMO',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	2,29,2,35,0,11
	.byte	'COA',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'PIT',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'SGE1',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'DLM1',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'PCM1',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,128,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71300
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CRTL_1_SHADOW_STATE_Bits',0,5,128,12,16,4,11
	.byte	'DMO',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	2,29,2,35,0,11
	.byte	'COA',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'PIT',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'SGE1',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'DLM1',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'PCM1',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'SGE2',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'DLM2',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'PCM2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'SYN_NS',0,4
	.word	8543
	.byte	21,0,2,35,0,0,12,5,216,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71525
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RAM_INI_Bits',0,5,228,16,16,4,11
	.byte	'INIT_1A',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'INIT_1B',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'INIT_2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'INIT_RAM',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	27,0,2,35,0,0,12,5,135,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71792
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PSA_Bits',0,5,165,16,16,4,11
	.byte	'PSA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,191,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71986
	.byte	4,2,35,0,0,14,96
	.word	72054
	.byte	15,23,0,10
	.byte	'_Ifx_GTM_DPLL_DLA_Bits',0,5,167,13,16,4,11
	.byte	'DLA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,160,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72103
	.byte	4,2,35,0,0,14,96
	.word	72171
	.byte	15,23,0,10
	.byte	'_Ifx_GTM_DPLL_NA_Bits',0,5,189,15,16,4,11
	.byte	'DB',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'DW',0,4
	.word	8543
	.byte	10,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,223,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72220
	.byte	4,2,35,0,0,14,96
	.word	72300
	.byte	15,23,0,10
	.byte	'_Ifx_GTM_DPLL_DTA_Bits',0,5,195,13,16,4,11
	.byte	'DTA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,192,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72349
	.byte	4,2,35,0,0,14,96
	.word	72417
	.byte	15,23,0,10
	.byte	'_Ifx_GTM_DPLL_TS_T_0_Bits',0,5,149,18,16,4,11
	.byte	'TRIGGER_TS',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,169,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72466
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TS_T_1_Bits',0,5,156,18,16,4,11
	.byte	'TRIGGER_TS',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,177,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72584
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_FTV_T_Bits',0,5,131,14,16,4,11
	.byte	'TRIGGER_FT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,234,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72702
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TS_S_0_Bits',0,5,135,18,16,4,11
	.byte	'STATE_TS',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,153,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72819
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TS_S_1_Bits',0,5,142,18,16,4,11
	.byte	'STATE_TS',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,161,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72935
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_FTV_S_Bits',0,5,252,13,16,4,11
	.byte	'STATE_FT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,226,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73051
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_THMI_Bits',0,5,226,17,16,4,11
	.byte	'THMI',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,241,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73166
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_THMA_Bits',0,5,219,17,16,4,11
	.byte	'THMA',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,233,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73276
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_THVAL_Bits',0,5,233,17,16,4,11
	.byte	'THVAL',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,249,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73386
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TOV_Bits',0,5,247,17,16,4,11
	.byte	'DB',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'DW',0,4
	.word	8543
	.byte	6,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,137,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TOV_S_Bits',0,5,255,17,16,4,11
	.byte	'DB',0,4
	.word	8543
	.byte	10,22,2,35,0,11
	.byte	'DW',0,4
	.word	8543
	.byte	6,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,145,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73619
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_CAL1_Bits',0,5,237,10,16,4,11
	.byte	'ADD_IN_CAL_1',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,200,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73742
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_CAL2_Bits',0,5,244,10,16,4,11
	.byte	'ADD_IN_CAL_2',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,208,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73867
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_MPVAL1_Bits',0,5,172,15,16,4,11
	.byte	'MPVAL1',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'SIX1',0,4
	.word	8543
	.byte	8,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,206,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73992
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_MPVAL2_Bits',0,5,181,15,16,4,11
	.byte	'MPVAL2',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'SIX2',0,4
	.word	8543
	.byte	8,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,215,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74122
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NMB_T_TAR_Bits',0,5,225,15,16,4,11
	.byte	'NMB_T_TAR',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,135,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74252
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NMB_T_TAR_OLD_Bits',0,5,232,15,16,4,11
	.byte	'NMB_T_TAR_OLD',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,143,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74372
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NMB_S_TAR_Bits',0,5,204,15,16,4,11
	.byte	'NMB_S_TAR',0,4
	.word	8543
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,239,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NMB_S_TAR_OLD_Bits',0,5,211,15,16,4,11
	.byte	'NMB_S_TAR_OLD',0,4
	.word	8543
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,247,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74620
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RCDT_TX_Bits',0,5,254,16,16,4,11
	.byte	'RCDT_TX',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,160,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74748
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RCDT_SX_Bits',0,5,239,16,16,4,11
	.byte	'RCDT_SX',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,143,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74864
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RCDT_TX_NOM_Bits',0,5,134,17,16,4,11
	.byte	'RCDT_TX_NOM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,169,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74980
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RCDT_SX_NOM_Bits',0,5,247,16,16,4,11
	.byte	'RCDT_SX_NOM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,152,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75104
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RDT_T_ACT_Bits',0,5,155,17,16,4,11
	.byte	'RDT_T_ACT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,193,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75228
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RDT_S_ACT_Bits',0,5,141,17,16,4,11
	.byte	'RDT_S_ACT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,185,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75348
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_DT_T_ACT_Bits',0,5,188,13,16,4,11
	.byte	'DT_T_ACT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,184,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75468
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_DT_S_ACT_Bits',0,5,174,13,16,4,11
	.byte	'DT_S_ACT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,176,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_EDT_T_Bits',0,5,211,13,16,4,11
	.byte	'EDT_T',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,210,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75704
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_MEDT_T_Bits',0,5,150,15,16,4,11
	.byte	'MEDT_T',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,181,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75816
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_EDT_S_Bits',0,5,203,13,16,4,11
	.byte	'EDT_S',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,201,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75930
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_MEDT_S_Bits',0,5,142,15,16,4,11
	.byte	'MEDT_S',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,172,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76042
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CDT_TX_Bits',0,5,228,11,16,4,11
	.byte	'CDT_TX',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,184,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76156
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CDT_SX_Bits',0,5,214,11,16,4,11
	.byte	'CDT_SX',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,168,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76270
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CDT_TX_NOM_Bits',0,5,235,11,16,4,11
	.byte	'CDT_TX_NOM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,192,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76384
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CDT_SX_NOM_Bits',0,5,221,11,16,4,11
	.byte	'CDT_SX_NOM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,176,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76506
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_TLR_Bits',0,5,240,17,16,4,11
	.byte	'TLR',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,129,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76628
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_SLR_Bits',0,5,162,17,16,4,11
	.byte	'SLR',0,4
	.word	8543
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,201,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76735
	.byte	4,2,35,0,0,14,88
	.word	490
	.byte	15,87,0,10
	.byte	'_Ifx_GTM_DPLL_PDT_T_Bits',0,5,157,16,16,4,11
	.byte	'DB',0,4
	.word	8543
	.byte	14,18,2,35,0,11
	.byte	'DW',0,4
	.word	8543
	.byte	10,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,183,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76851
	.byte	4,2,35,0,0,14,96
	.word	76934
	.byte	15,23,0,14,96
	.word	490
	.byte	15,95,0,10
	.byte	'_Ifx_GTM_DPLL_MLS1_Bits',0,5,157,15,16,4,11
	.byte	'MLS1',0,4
	.word	8543
	.byte	18,14,2,35,0,11
	.byte	'reserved_18',0,4
	.word	8543
	.byte	14,0,2,35,0,0,12,5,189,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76992
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_MLS2_Bits',0,5,164,15,16,4,11
	.byte	'MLS2',0,4
	.word	8543
	.byte	18,14,2,35,0,11
	.byte	'reserved_18',0,4
	.word	8543
	.byte	14,0,2,35,0,0,12,5,197,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77102
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CNT_NUM1_Bits',0,5,242,11,16,4,11
	.byte	'CNT_NUM_1',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,200,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77212
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_CNT_NUM2_Bits',0,5,249,11,16,4,11
	.byte	'CNT_NUM_2',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,208,40,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77331
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PVT_Bits',0,5,221,16,16,4,11
	.byte	'PVT',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,255,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77450
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PSTC_Bits',0,5,200,16,16,4,11
	.byte	'PSTC',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,231,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77558
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PSSC_Bits',0,5,179,16,16,4,11
	.byte	'PSSC',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,207,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77668
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PSTM_0_Bits',0,5,207,16,16,4,11
	.byte	'PSTM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,239,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77778
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PSTM_1_Bits',0,5,214,16,16,4,11
	.byte	'PSTM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,247,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77890
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PSSM_0_Bits',0,5,186,16,16,4,11
	.byte	'PSSM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,215,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78002
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_PSSM_1_Bits',0,5,193,16,16,4,11
	.byte	'PSSM',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,223,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78114
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NMB_T_Bits',0,5,218,15,16,4,11
	.byte	'NMB_T',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,255,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78226
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_NMB_S_Bits',0,5,197,15,16,4,11
	.byte	'NMB_S',0,4
	.word	8543
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,231,42,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78338
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DPLL_RDT_S_Bits',0,5,148,17,16,4,11
	.byte	'RDT_S',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,177,44,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78450
	.byte	4,2,35,0,0,14,128,2
	.word	78522
	.byte	15,63,0,10
	.byte	'_Ifx_GTM_DPLL_TSF_S_Bits',0,5,170,18,16,4,11
	.byte	'TSF_S',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,193,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78572
	.byte	4,2,35,0,0,14,128,2
	.word	78644
	.byte	15,63,0,10
	.byte	'_Ifx_GTM_DPLL_ADT_S_Bits',0,5,137,11,16,4,11
	.byte	'PD_S',0,4
	.word	8543
	.byte	16,16,2,35,0,11
	.byte	'NS',0,4
	.word	8543
	.byte	6,10,2,35,0,11
	.byte	'reserved_22',0,4
	.word	8543
	.byte	10,0,2,35,0,0,12,5,232,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78694
	.byte	4,2,35,0,0,14,128,2
	.word	78779
	.byte	15,63,0,10
	.byte	'_Ifx_GTM_DPLL_DT_S_Bits',0,5,181,13,16,4,11
	.byte	'DT_S',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,168,41,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78829
	.byte	4,2,35,0,0,14,128,2
	.word	78899
	.byte	15,63,0,14,128,8
	.word	490
	.byte	15,255,7,0,10
	.byte	'_Ifx_GTM_DPLL_TSAC_Bits',0,5,163,18,16,4,11
	.byte	'TSAC',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,185,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78960
	.byte	4,2,35,0,0,14,96
	.word	79030
	.byte	15,23,0,10
	.byte	'_Ifx_GTM_DPLL_PSAC_Bits',0,5,172,16,16,4,11
	.byte	'PSAC',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,199,43,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79079
	.byte	4,2,35,0,0,14,96
	.word	79149
	.byte	15,23,0,10
	.byte	'_Ifx_GTM_DPLL_ACB_Bits',0,5,217,10,16,4,11
	.byte	'ACB_0',0,4
	.word	8543
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	3,24,2,35,0,11
	.byte	'ACB_1',0,4
	.word	8543
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'ACB_2',0,4
	.word	8543
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'ACB_3',0,4
	.word	8543
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,184,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79198
	.byte	4,2,35,0,0,14,24
	.word	79387
	.byte	15,5,0,10
	.byte	'_Ifx_GTM_DPLL',0,5,254,57,25,152,30,13
	.byte	'CTRL_0',0
	.word	62911
	.byte	4,2,35,0,13
	.byte	'CTRL_1',0
	.word	63344
	.byte	4,2,35,4,13
	.byte	'CTRL_2',0
	.word	63718
	.byte	4,2,35,8,13
	.byte	'CTRL_3',0
	.word	64104
	.byte	4,2,35,12,13
	.byte	'CTRL_4',0
	.word	64494
	.byte	4,2,35,16,13
	.byte	'reserved_14',0
	.word	1535
	.byte	4,2,35,20,13
	.byte	'ACT_STA',0
	.word	64609
	.byte	4,2,35,24,13
	.byte	'OSW',0
	.word	64775
	.byte	4,2,35,28,13
	.byte	'AOSV_2',0
	.word	64924
	.byte	4,2,35,32,13
	.byte	'APT',0
	.word	65130
	.byte	4,2,35,36,13
	.byte	'APS',0
	.word	65337
	.byte	4,2,35,40,13
	.byte	'APT_2C',0
	.word	65473
	.byte	4,2,35,44,13
	.byte	'APS_1C3',0
	.word	65610
	.byte	4,2,35,48,13
	.byte	'NUTC',0
	.word	65859
	.byte	4,2,35,52,13
	.byte	'NUSC',0
	.word	66085
	.byte	4,2,35,56,13
	.byte	'NTI_CNT',0
	.word	66201
	.byte	4,2,35,60,13
	.byte	'IRQ_NOTIFY',0
	.word	66740
	.byte	4,2,35,64,13
	.byte	'IRQ_EN',0
	.word	67450
	.byte	4,2,35,68,13
	.byte	'IRQ_FORCINT',0
	.word	68102
	.byte	4,2,35,72,13
	.byte	'IRQ_MODE',0
	.word	68219
	.byte	4,2,35,76,13
	.byte	'EIRQ_EN',0
	.word	68955
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	68995
	.byte	92,2,35,84,13
	.byte	'INC_CNT1',0
	.word	69082
	.byte	4,3,35,176,1,13
	.byte	'INC_CNT2',0
	.word	69200
	.byte	4,3,35,180,1,13
	.byte	'APT_SYNC',0
	.word	69389
	.byte	4,3,35,184,1,13
	.byte	'APS_SYNC',0
	.word	69581
	.byte	4,3,35,188,1,13
	.byte	'TBU_TS0_T',0
	.word	69701
	.byte	4,3,35,192,1,13
	.byte	'TBU_TS0_S',0
	.word	69821
	.byte	4,3,35,196,1,13
	.byte	'ADD_IN_LD1',0
	.word	69944
	.byte	4,3,35,200,1,13
	.byte	'ADD_IN_LD2',0
	.word	70067
	.byte	4,3,35,204,1,13
	.byte	'reserved_D0',0
	.word	70107
	.byte	44,3,35,208,1,13
	.byte	'STATUS',0
	.word	70663
	.byte	4,3,35,252,1,13
	.byte	'ID_PMTR',0
	.word	70820
	.byte	96,3,35,128,2,13
	.byte	'reserved_160',0
	.word	70829
	.byte	128,1,3,35,224,2,13
	.byte	'CTRL_0_SHADOW_TRIGGER',0
	.word	71023
	.byte	4,3,35,224,3,13
	.byte	'CTRL_0_SHADOW_STATE',0
	.word	71260
	.byte	4,3,35,228,3,13
	.byte	'CTRL_1_SHADOW_TRIGGER',0
	.word	71485
	.byte	4,3,35,232,3,13
	.byte	'CRTL_1_SHADOW_STATE',0
	.word	71752
	.byte	4,3,35,236,3,13
	.byte	'reserved_1F0',0
	.word	3694
	.byte	12,3,35,240,3,13
	.byte	'RAM_INI',0
	.word	71946
	.byte	4,3,35,252,3,13
	.byte	'PSA',0
	.word	72094
	.byte	96,3,35,128,4,13
	.byte	'reserved_260',0
	.word	58671
	.byte	32,3,35,224,4,13
	.byte	'DLA',0
	.word	72211
	.byte	96,3,35,128,5,13
	.byte	'reserved_2E0',0
	.word	58671
	.byte	32,3,35,224,5,13
	.byte	'NA',0
	.word	72340
	.byte	96,3,35,128,6,13
	.byte	'reserved_360',0
	.word	58671
	.byte	32,3,35,224,6,13
	.byte	'DTA',0
	.word	72457
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	58671
	.byte	32,3,35,224,7,13
	.byte	'TS_T_0',0
	.word	72544
	.byte	4,3,35,128,8,13
	.byte	'TS_T_1',0
	.word	72662
	.byte	4,3,35,132,8,13
	.byte	'FTV_T',0
	.word	72779
	.byte	4,3,35,136,8,13
	.byte	'reserved_40C',0
	.word	1535
	.byte	4,3,35,140,8,13
	.byte	'TS_S_0',0
	.word	72895
	.byte	4,3,35,144,8,13
	.byte	'TS_S_1',0
	.word	73011
	.byte	4,3,35,148,8,13
	.byte	'FTV_S',0
	.word	73126
	.byte	4,3,35,152,8,13
	.byte	'reserved_41C',0
	.word	1535
	.byte	4,3,35,156,8,13
	.byte	'THMI',0
	.word	73236
	.byte	4,3,35,160,8,13
	.byte	'THMA',0
	.word	73346
	.byte	4,3,35,164,8,13
	.byte	'THVAL',0
	.word	73458
	.byte	4,3,35,168,8,13
	.byte	'reserved_42C',0
	.word	1535
	.byte	4,3,35,172,8,13
	.byte	'TOV',0
	.word	73579
	.byte	4,3,35,176,8,13
	.byte	'TOV_S',0
	.word	73702
	.byte	4,3,35,180,8,13
	.byte	'ADD_IN_CAL1',0
	.word	73827
	.byte	4,3,35,184,8,13
	.byte	'ADD_IN_CAL2',0
	.word	73952
	.byte	4,3,35,188,8,13
	.byte	'MPVAL1',0
	.word	74082
	.byte	4,3,35,192,8,13
	.byte	'MPVAL2',0
	.word	74212
	.byte	4,3,35,196,8,13
	.byte	'NMB_T_TAR',0
	.word	74332
	.byte	4,3,35,200,8,13
	.byte	'NMB_T_TAR_OLD',0
	.word	74460
	.byte	4,3,35,204,8,13
	.byte	'NMB_S_TAR',0
	.word	74580
	.byte	4,3,35,208,8,13
	.byte	'NMB_S_TAR_OLD',0
	.word	74708
	.byte	4,3,35,212,8,13
	.byte	'reserved_458',0
	.word	3354
	.byte	8,3,35,216,8,13
	.byte	'RCDT_TX',0
	.word	74824
	.byte	4,3,35,224,8,13
	.byte	'RCDT_SX',0
	.word	74940
	.byte	4,3,35,228,8,13
	.byte	'RCDT_TX_NOM',0
	.word	75064
	.byte	4,3,35,232,8,13
	.byte	'RCDT_SX_NOM',0
	.word	75188
	.byte	4,3,35,236,8,13
	.byte	'RDT_T_ACT',0
	.word	75308
	.byte	4,3,35,240,8,13
	.byte	'RDT_S_ACT',0
	.word	75428
	.byte	4,3,35,244,8,13
	.byte	'DT_T_ACT',0
	.word	75546
	.byte	4,3,35,248,8,13
	.byte	'DT_S_ACT',0
	.word	75664
	.byte	4,3,35,252,8,13
	.byte	'EDT_T',0
	.word	75776
	.byte	4,3,35,128,9,13
	.byte	'MEDT_T',0
	.word	75890
	.byte	4,3,35,132,9,13
	.byte	'EDT_S',0
	.word	76002
	.byte	4,3,35,136,9,13
	.byte	'MEDT_S',0
	.word	76116
	.byte	4,3,35,140,9,13
	.byte	'CDT_TX',0
	.word	76230
	.byte	4,3,35,144,9,13
	.byte	'CDT_SX',0
	.word	76344
	.byte	4,3,35,148,9,13
	.byte	'CDT_TX_NOM',0
	.word	76466
	.byte	4,3,35,152,9,13
	.byte	'CDT_SX_NOM',0
	.word	76588
	.byte	4,3,35,156,9,13
	.byte	'TLR',0
	.word	76695
	.byte	4,3,35,160,9,13
	.byte	'SLR',0
	.word	76802
	.byte	4,3,35,164,9,13
	.byte	'reserved_4A8',0
	.word	76842
	.byte	88,3,35,168,9,13
	.byte	'PDT_T',0
	.word	76974
	.byte	96,3,35,128,10,13
	.byte	'reserved_560',0
	.word	76983
	.byte	96,3,35,224,10,13
	.byte	'MLS1',0
	.word	77062
	.byte	4,3,35,192,11,13
	.byte	'MLS2',0
	.word	77172
	.byte	4,3,35,196,11,13
	.byte	'CNT_NUM1',0
	.word	77291
	.byte	4,3,35,200,11,13
	.byte	'CNT_NUM2',0
	.word	77410
	.byte	4,3,35,204,11,13
	.byte	'PVT',0
	.word	77518
	.byte	4,3,35,208,11,13
	.byte	'reserved_5D4',0
	.word	3694
	.byte	12,3,35,212,11,13
	.byte	'PSTC',0
	.word	77628
	.byte	4,3,35,224,11,13
	.byte	'PSSC',0
	.word	77738
	.byte	4,3,35,228,11,13
	.byte	'PSTM_0',0
	.word	77850
	.byte	4,3,35,232,11,13
	.byte	'PSTM_1',0
	.word	77962
	.byte	4,3,35,236,11,13
	.byte	'PSSM_0',0
	.word	78074
	.byte	4,3,35,240,11,13
	.byte	'PSSM_1',0
	.word	78186
	.byte	4,3,35,244,11,13
	.byte	'NMB_T',0
	.word	78298
	.byte	4,3,35,248,11,13
	.byte	'NMB_S',0
	.word	78410
	.byte	4,3,35,252,11,13
	.byte	'RDT_S',0
	.word	78562
	.byte	128,2,3,35,128,12,13
	.byte	'TSF_S',0
	.word	78684
	.byte	128,2,3,35,128,14,13
	.byte	'ADT_S',0
	.word	78819
	.byte	128,2,3,35,128,16,13
	.byte	'DT_S',0
	.word	78939
	.byte	128,2,3,35,128,18,13
	.byte	'reserved_A00',0
	.word	78949
	.byte	128,8,3,35,128,20,13
	.byte	'TSAC',0
	.word	79070
	.byte	96,3,35,128,28,13
	.byte	'reserved_E60',0
	.word	58671
	.byte	32,3,35,224,28,13
	.byte	'PSAC',0
	.word	79189
	.byte	96,3,35,128,29,13
	.byte	'reserved_EE0',0
	.word	58671
	.byte	32,3,35,224,29,13
	.byte	'ACB',0
	.word	79427
	.byte	24,3,35,128,30,0,16
	.word	79436
	.byte	14,232,225,1
	.word	490
	.byte	15,231,225,1,0,10
	.byte	'_Ifx_GTM_MCS_CH_R0_Bits',0,5,198,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,137,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81631
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_R1_Bits',0,5,205,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,145,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81741
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_R2_Bits',0,5,212,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,153,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81851
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_R3_Bits',0,5,219,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,161,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81961
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_R4_Bits',0,5,226,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,169,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_R5_Bits',0,5,233,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,177,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82181
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_R6_Bits',0,5,240,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,185,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82291
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_R7_Bits',0,5,247,24,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,193,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82401
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_CTRL_Bits',0,5,130,24,16,4,11
	.byte	'EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ERR',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'CY',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'Z',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'V',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'N',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'CAT',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'CWT',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	6,16,2,35,0,11
	.byte	'SP_CNT',0,4
	.word	8543
	.byte	3,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8543
	.byte	13,0,2,35,0,0,12,5,209,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_ACB_Bits',0,5,247,23,16,4,11
	.byte	'ACB0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ACB1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ACB2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ACB3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ACB4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	27,0,2,35,0,0,12,5,201,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82797
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH0_CTRG_Bits',0,5,203,23,16,4,11
	.byte	'TRG0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRG4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TRG5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TRG6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TRG7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TRG8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TRG9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TRG10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TRG11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TRG12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TRG13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TRG14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TRG15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,185,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82971
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH0_STRG_Bits',0,5,225,23,16,4,11
	.byte	'TRG0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'TRG3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'TRG4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'TRG5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'TRG6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'TRG7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'TRG8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'TRG9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'TRG10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'TRG11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'TRG12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'TRG13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'TRG14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'TRG15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,193,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83330
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_PC_Bits',0,5,191,24,16,4,11
	.byte	'PC',0,4
	.word	8543
	.byte	14,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	8543
	.byte	18,0,2,35,0,0,12,5,129,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83689
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_NOTIFY_Bits',0,5,182,24,16,4,11
	.byte	'MCS_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'STK_ERR_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MEM_ERR_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,249,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83797
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_EN_Bits',0,5,157,24,16,4,11
	.byte	'MCS_IRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'STK_ERR_IRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MEM_ERR_IRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,225,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83963
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_FORCINT_Bits',0,5,166,24,16,4,11
	.byte	'TRG_MCS_IRQ',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'TRG_STK_ERR_IRQ',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'TRG_MEM_ERR_IRQ',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,233,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84134
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_MODE_Bits',0,5,175,24,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,241,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84313
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH_EIRQ_EN_Bits',0,5,148,24,16,4,11
	.byte	'MCS_EIRQ_EN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'STK_ERR_EIRQ_EN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MEM_ERR_EIRQ_EN',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	29,0,2,35,0,0,12,5,217,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84432
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_CH0',0,5,197,56,25,88,13
	.byte	'R0',0
	.word	81701
	.byte	4,2,35,0,13
	.byte	'R1',0
	.word	81811
	.byte	4,2,35,4,13
	.byte	'R2',0
	.word	81921
	.byte	4,2,35,8,13
	.byte	'R3',0
	.word	82031
	.byte	4,2,35,12,13
	.byte	'R4',0
	.word	82141
	.byte	4,2,35,16,13
	.byte	'R5',0
	.word	82251
	.byte	4,2,35,20,13
	.byte	'R6',0
	.word	82361
	.byte	4,2,35,24,13
	.byte	'R7',0
	.word	82471
	.byte	4,2,35,28,13
	.byte	'CTRL',0
	.word	82757
	.byte	4,2,35,32,13
	.byte	'ACB',0
	.word	82931
	.byte	4,2,35,36,13
	.byte	'CTRG',0
	.word	83290
	.byte	4,2,35,40,13
	.byte	'STRG',0
	.word	83649
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	49328
	.byte	16,2,35,48,13
	.byte	'PC',0
	.word	83757
	.byte	4,2,35,64,13
	.byte	'IRQ_NOTIFY',0
	.word	83923
	.byte	4,2,35,68,13
	.byte	'IRQ_EN',0
	.word	84094
	.byte	4,2,35,72,13
	.byte	'IRQ_FORCINT',0
	.word	84273
	.byte	4,2,35,76,13
	.byte	'IRQ_MODE',0
	.word	84392
	.byte	4,2,35,80,13
	.byte	'EIRQ_EN',0
	.word	84567
	.byte	4,2,35,84,0,16
	.word	84607
	.byte	14,28
	.word	490
	.byte	15,27,0,10
	.byte	'_Ifx_GTM_MCS_CTRL_Bits',0,5,254,24,16,4,11
	.byte	'SCHED',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'HLT_SP_OFL',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	14,16,2,35,0,11
	.byte	'RAM_RST',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8543
	.byte	15,0,2,35,0,0,12,5,201,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84921
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_RST_Bits',0,5,150,25,16,4,11
	.byte	'RST0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'RST1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'RST2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'RST3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'RST4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'RST5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'RST6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'RST7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'CAT0',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'CAT1',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'CAT2',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'CAT3',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'CAT4',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'CAT5',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'CAT6',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'CAT7',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'CWT0',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'CWT1',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'CWT2',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'CWT3',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'CWT4',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'CWT5',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'CWT6',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'CWT7',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,217,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85094
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCS_ERR_Bits',0,5,136,25,16,4,11
	.byte	'ERR0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'ERR1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'ERR2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'ERR3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'ERR4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'ERR5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'ERR6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'ERR7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	24,0,2,35,0,0,12,5,209,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85570
	.byte	4,2,35,0,0,14,40
	.word	490
	.byte	15,39,0,10
	.byte	'_Ifx_GTM_MCS_CH',0,5,174,56,25,128,1,13
	.byte	'R0',0
	.word	81701
	.byte	4,2,35,0,13
	.byte	'R1',0
	.word	81811
	.byte	4,2,35,4,13
	.byte	'R2',0
	.word	81921
	.byte	4,2,35,8,13
	.byte	'R3',0
	.word	82031
	.byte	4,2,35,12,13
	.byte	'R4',0
	.word	82141
	.byte	4,2,35,16,13
	.byte	'R5',0
	.word	82251
	.byte	4,2,35,20,13
	.byte	'R6',0
	.word	82361
	.byte	4,2,35,24,13
	.byte	'R7',0
	.word	82471
	.byte	4,2,35,28,13
	.byte	'CTRL',0
	.word	82757
	.byte	4,2,35,32,13
	.byte	'ACB',0
	.word	82931
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2725
	.byte	24,2,35,40,13
	.byte	'PC',0
	.word	83757
	.byte	4,2,35,64,13
	.byte	'IRQ_NOTIFY',0
	.word	83923
	.byte	4,2,35,68,13
	.byte	'IRQ_EN',0
	.word	84094
	.byte	4,2,35,72,13
	.byte	'IRQ_FORCINT',0
	.word	84273
	.byte	4,2,35,76,13
	.byte	'IRQ_MODE',0
	.word	84392
	.byte	4,2,35,80,13
	.byte	'EIRQ_EN',0
	.word	84567
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	85789
	.byte	40,2,35,88,0,16
	.word	85798
	.byte	16
	.word	85798
	.byte	16
	.word	85798
	.byte	16
	.word	85798
	.byte	16
	.word	85798
	.byte	16
	.word	85798
	.byte	16
	.word	85798
	.byte	14,128,24
	.word	490
	.byte	15,255,23,0,10
	.byte	'_Ifx_GTM_MCS',0,5,167,59,25,128,32,13
	.byte	'CH0',0
	.word	84907
	.byte	88,2,35,0,13
	.byte	'reserved_58',0
	.word	84912
	.byte	28,2,35,88,13
	.byte	'CTRL',0
	.word	85054
	.byte	4,2,35,116,13
	.byte	'RST',0
	.word	85530
	.byte	4,2,35,120,13
	.byte	'ERR',0
	.word	85749
	.byte	4,2,35,124,13
	.byte	'CH1',0
	.word	86091
	.byte	128,1,3,35,128,1,13
	.byte	'CH2',0
	.word	86096
	.byte	128,1,3,35,128,2,13
	.byte	'CH3',0
	.word	86101
	.byte	128,1,3,35,128,3,13
	.byte	'CH4',0
	.word	86106
	.byte	128,1,3,35,128,4,13
	.byte	'CH5',0
	.word	86111
	.byte	128,1,3,35,128,5,13
	.byte	'CH6',0
	.word	86116
	.byte	128,1,3,35,128,6,13
	.byte	'CH7',0
	.word	86121
	.byte	128,1,3,35,128,7,13
	.byte	'reserved_400',0
	.word	86126
	.byte	128,24,3,35,128,8,0,14,128,96
	.word	86137
	.byte	15,2,0,16
	.word	86361
	.byte	14,128,154,27
	.word	490
	.byte	15,255,153,27,0,10
	.byte	'_Ifx_GTM_CLC_Bits',0,5,214,8,16,4,11
	.byte	'DISR',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'DISS',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EDIS',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	28,0,2,35,0,0,12,5,168,38,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86389
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,5,227,22,16,4,11
	.byte	'CH0SEL',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'CH1SEL',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'CH2SEL',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'CH3SEL',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'CH4SEL',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'CH5SEL',0,4
	.word	8543
	.byte	4,8,2,35,0,11
	.byte	'CH6SEL',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'CH7SEL',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,233,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,5,168,56,25,4,13
	.byte	'INSEL',0
	.word	86730
	.byte	4,2,35,0,0,14,12
	.word	86770
	.byte	15,2,0,16
	.word	86814
	.byte	14,20
	.word	490
	.byte	15,19,0,10
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,5,206,22,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,225,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86837
	.byte	4,2,35,0,0,14,40
	.word	87138
	.byte	15,9,0,10
	.byte	'_Ifx_GTM_INOUTSEL_T',0,5,162,56,25,40,13
	.byte	'OUTSEL',0
	.word	87178
	.byte	40,2,35,0,0,16
	.word	87187
	.byte	14,36
	.word	490
	.byte	15,35,0,10
	.byte	'_Ifx_GTM_INOUTSEL_DSADC_INSEL_Bits',0,5,157,22,16,4,11
	.byte	'INSEL0',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'INSEL1',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'INSEL2',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'INSEL3',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'INSEL4',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'INSEL5',0,4
	.word	8543
	.byte	4,8,2,35,0,11
	.byte	'INSEL6',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'INSEL7',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,193,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87244
	.byte	4,2,35,0,0,14,12
	.word	87430
	.byte	15,2,0,10
	.byte	'_Ifx_GTM_INOUTSEL_DSADC_OUTSEL_Bits',0,5,170,22,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	5,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8543
	.byte	17,0,2,35,0,0,12,5,201,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87479
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_INOUTSEL_DSADC',0,5,141,56,25,24,13
	.byte	'INSEL',0
	.word	87470
	.byte	12,2,35,0,13
	.byte	'OUTSEL00',0
	.word	87638
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	1535
	.byte	4,2,35,16,13
	.byte	'OUTSEL10',0
	.word	87638
	.byte	4,2,35,20,0,16
	.word	87678
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,5,144,22,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	4,8,2,35,0,11
	.byte	'SEL6',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'SEL7',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,185,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87786
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,5,135,56,25,4,13
	.byte	'OUTSEL',0
	.word	87955
	.byte	4,2,35,0,0,16
	.word	87995
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5_OUTSEL0_Bits',0,5,181,22,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	4,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	8,0,2,35,0,0,12,5,209,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88045
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5',0,5,150,56,25,4,13
	.byte	'OUTSEL0',0
	.word	88207
	.byte	4,2,35,0,0,16
	.word	88247
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5S_OUTSEL_Bits',0,5,193,22,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	4,8,2,35,0,11
	.byte	'SEL6',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,217,47,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88299
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5S',0,5,156,56,25,4,13
	.byte	'OUTSEL',0
	.word	88484
	.byte	4,2,35,0,0,16
	.word	88524
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL',0,5,153,59,25,156,1,13
	.byte	'TIM',0
	.word	86823
	.byte	12,2,35,0,13
	.byte	'reserved_C',0
	.word	86828
	.byte	20,2,35,12,13
	.byte	'T',0
	.word	87230
	.byte	40,2,35,32,13
	.byte	'reserved_48',0
	.word	87235
	.byte	36,2,35,72,13
	.byte	'DSADC',0
	.word	87781
	.byte	24,2,35,108,13
	.byte	'reserved_84',0
	.word	3694
	.byte	12,3,35,132,1,13
	.byte	'CAN',0
	.word	88040
	.byte	4,3,35,144,1,13
	.byte	'PSI5',0
	.word	88294
	.byte	4,3,35,148,1,13
	.byte	'PSI5S',0
	.word	88571
	.byte	4,3,35,152,1,0,16
	.word	88576
	.byte	10
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,5,92,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,216,33,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88754
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,5,103,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,224,33,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88930
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,5,148,27,16,4,11
	.byte	'CV',0,4
	.word	8543
	.byte	27,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'CM',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,201,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89106
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,5,157,27,16,4,11
	.byte	'CV',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'EN',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,209,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89248
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,5,166,27,16,4,11
	.byte	'CV',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'EN',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,217,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89390
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_OTSS_Bits',0,5,202,27,16,4,11
	.byte	'OTGB0',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'OTGB1',0,4
	.word	8543
	.byte	4,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'OTGB2',0,4
	.word	8543
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8543
	.byte	12,0,2,35,0,0,12,5,241,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_OTSC0_Bits',0,5,175,27,16,4,11
	.byte	'B0LMT',0,4
	.word	8543
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'B0LMI',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'B0HMT',0,4
	.word	8543
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'B0HMI',0,4
	.word	8543
	.byte	4,16,2,35,0,11
	.byte	'B1LMT',0,4
	.word	8543
	.byte	3,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'B1LMI',0,4
	.word	8543
	.byte	4,8,2,35,0,11
	.byte	'B1HMT',0,4
	.word	8543
	.byte	3,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'B1HMI',0,4
	.word	8543
	.byte	4,0,2,35,0,0,12,5,225,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89717
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_OTSC1_Bits',0,5,192,27,16,4,11
	.byte	'MCS',0,4
	.word	8543
	.byte	4,28,2,35,0,11
	.byte	'MI',0,4
	.word	8543
	.byte	4,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'MOE',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8543
	.byte	22,0,2,35,0,0,12,5,233,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90011
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ODA_Bits',0,5,140,27,16,4,11
	.byte	'DDREN',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'DREN',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,193,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90167
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_OCS_Bits',0,5,130,27,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,4
	.word	8543
	.byte	4,4,2,35,0,11
	.byte	'SUS_P',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'SUSSTA',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,185,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90287
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,5,164,23,16,4,11
	.byte	'CLR',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	31,0,2,35,0,0,12,5,161,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90447
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_KRST1_Bits',0,5,157,23,16,4,11
	.byte	'RST',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8543
	.byte	31,0,2,35,0,0,12,5,153,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90553
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_KRST0_Bits',0,5,149,23,16,4,11
	.byte	'RST',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'RSTSTAT',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8543
	.byte	30,0,2,35,0,0,12,5,145,48,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90657
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,5,86,16,4,11
	.byte	'reserved_0',0,4
	.word	8543
	.byte	32,0,2,35,0,0,12,5,208,33,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90780
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,5,49,16,4,11
	.byte	'EN0',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'EN1',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'EN2',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'EN3',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'EN4',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'EN5',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'EN6',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'EN7',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'EN8',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'EN9',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'EN10',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'EN11',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'EN12',0,4
	.word	8543
	.byte	1,19,2,35,0,11
	.byte	'EN13',0,4
	.word	8543
	.byte	1,18,2,35,0,11
	.byte	'EN14',0,4
	.word	8543
	.byte	1,17,2,35,0,11
	.byte	'EN15',0,4
	.word	8543
	.byte	1,16,2,35,0,11
	.byte	'EN16',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'EN17',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'EN18',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'EN19',0,4
	.word	8543
	.byte	1,12,2,35,0,11
	.byte	'EN20',0,4
	.word	8543
	.byte	1,11,2,35,0,11
	.byte	'EN21',0,4
	.word	8543
	.byte	1,10,2,35,0,11
	.byte	'EN22',0,4
	.word	8543
	.byte	1,9,2,35,0,11
	.byte	'EN23',0,4
	.word	8543
	.byte	1,8,2,35,0,11
	.byte	'EN24',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'EN25',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'EN26',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'EN27',0,4
	.word	8543
	.byte	1,4,2,35,0,11
	.byte	'EN28',0,4
	.word	8543
	.byte	1,3,2,35,0,11
	.byte	'EN29',0,4
	.word	8543
	.byte	1,2,2,35,0,11
	.byte	'EN30',0,4
	.word	8543
	.byte	1,1,2,35,0,11
	.byte	'EN31',0,4
	.word	8543
	.byte	1,0,2,35,0,0,12,5,200,33,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90869
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DXOUTCON_Bits',0,5,198,18,16,4,11
	.byte	'OUT00',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'OUT01',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'OUT02',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	5,24,2,35,0,11
	.byte	'OUT10',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'OUT11',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'OUT12',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	21,0,2,35,0,0,12,5,209,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91438
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_TRIGOUT_Bits',0,5,181,33,16,4,11
	.byte	'INT0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'INT1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'TRIG0',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'TRIG1',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'TRIG2',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'TRIG3',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'TRIG4',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'TRIG5',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8543
	.byte	16,0,2,35,0,0,12,5,137,55,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91655
	.byte	4,2,35,0,0,14,12
	.word	91841
	.byte	15,2,0,14,52
	.word	490
	.byte	15,51,0,10
	.byte	'_Ifx_GTM_MCSINTSTAT_Bits',0,5,198,25,16,4,11
	.byte	'MCS000',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'MCS001',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MCS010',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'MCS011',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'MCS100',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'MCS101',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MCS110',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MCS111',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'MCS200',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'MCS201',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'MCS210',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'MCS211',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	20,0,2,35,0,0,12,5,233,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MCSINTCLR_Bits',0,5,180,25,16,4,11
	.byte	'MCS000',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'MCS001',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'MCS010',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'MCS011',0,4
	.word	8543
	.byte	1,28,2,35,0,11
	.byte	'MCS100',0,4
	.word	8543
	.byte	1,27,2,35,0,11
	.byte	'MCS101',0,4
	.word	8543
	.byte	1,26,2,35,0,11
	.byte	'MCS110',0,4
	.word	8543
	.byte	1,25,2,35,0,11
	.byte	'MCS111',0,4
	.word	8543
	.byte	1,24,2,35,0,11
	.byte	'MCS200',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'MCS201',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'MCS210',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'MCS211',0,4
	.word	8543
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8543
	.byte	20,0,2,35,0,0,12,5,225,49,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92210
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DXINCON_Bits',0,5,177,18,16,4,11
	.byte	'IN00',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'IN01',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'IN02',0,4
	.word	8543
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8543
	.byte	5,24,2,35,0,11
	.byte	'IN10',0,4
	.word	8543
	.byte	1,23,2,35,0,11
	.byte	'IN11',0,4
	.word	8543
	.byte	1,22,2,35,0,11
	.byte	'IN12',0,4
	.word	8543
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8543
	.byte	5,16,2,35,0,11
	.byte	'DSS00',0,4
	.word	8543
	.byte	1,15,2,35,0,11
	.byte	'DSS01',0,4
	.word	8543
	.byte	1,14,2,35,0,11
	.byte	'DSS02',0,4
	.word	8543
	.byte	1,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8543
	.byte	5,8,2,35,0,11
	.byte	'DSS10',0,4
	.word	8543
	.byte	1,7,2,35,0,11
	.byte	'DSS11',0,4
	.word	8543
	.byte	1,6,2,35,0,11
	.byte	'DSS12',0,4
	.word	8543
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8543
	.byte	5,0,2,35,0,0,12,5,201,45,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92520
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_DATAIN_Bits',0,5,211,10,16,4,11
	.byte	'DATA',0,4
	.word	8543
	.byte	32,0,2,35,0,0,12,5,176,39,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92878
	.byte	4,2,35,0,0,14,12
	.word	92922
	.byte	15,2,0,10
	.byte	'_Ifx_GTM_MSCSET_CON0_Bits',0,5,206,26,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	3,24,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,153,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92971
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MSCSET_CON1_Bits',0,5,219,26,16,4,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	3,24,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'SEL6',0,4
	.word	8543
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'SEL7',0,4
	.word	8543
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,161,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93199
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MSCSET_CON2_Bits',0,5,232,26,16,4,11
	.byte	'SEL8',0,4
	.word	8543
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	3,24,2,35,0,11
	.byte	'SEL9',0,4
	.word	8543
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'SEL10',0,4
	.word	8543
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'SEL11',0,4
	.word	8543
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,169,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93427
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MSCSET_CON3_Bits',0,5,245,26,16,4,11
	.byte	'SEL12',0,4
	.word	8543
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8543
	.byte	3,24,2,35,0,11
	.byte	'SEL13',0,4
	.word	8543
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8543
	.byte	3,16,2,35,0,11
	.byte	'SEL14',0,4
	.word	8543
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8543
	.byte	3,8,2,35,0,11
	.byte	'SEL15',0,4
	.word	8543
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8543
	.byte	3,0,2,35,0,0,12,5,177,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93657
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MSCSET',0,5,199,59,25,16,13
	.byte	'CON0',0
	.word	93159
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	93387
	.byte	4,2,35,4,13
	.byte	'CON2',0
	.word	93617
	.byte	4,2,35,8,13
	.byte	'CON3',0
	.word	93849
	.byte	4,2,35,12,0,14,64
	.word	93889
	.byte	15,3,0,16
	.word	93968
	.byte	10
	.byte	'_Ifx_GTM_MSCIN_INLCON_Bits',0,5,185,26,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,145,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93982
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MSCIN_INHCON_Bits',0,5,164,26,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,137,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	94318
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_GTM_MSCIN',0,5,192,59,25,8,13
	.byte	'INLCON',0
	.word	94278
	.byte	4,2,35,0,13
	.byte	'INHCON',0
	.word	94614
	.byte	4,2,35,4,0,14,16
	.word	94654
	.byte	15,1,0,16
	.word	94708
	.byte	10
	.byte	'_Ifx_GTM_MSC0INLEXTCON_Bits',0,5,143,26,16,4,11
	.byte	'SEL0',0,4
	.word	8543
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8543
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8543
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8543
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8543
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8543
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8543
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8543
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8543
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8543
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8543
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8543
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8543
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8543
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8543
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8543
	.byte	2,0,2,35,0,0,12,5,129,50,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	94722
	.byte	4,2,35,0,0,14,140,1
	.word	490
	.byte	15,139,1,0,10
	.byte	'_Ifx_GTM',0,5,190,60,25,128,128,40,13
	.byte	'REV',0
	.word	8711
	.byte	4,2,35,0,13
	.byte	'RST',0
	.word	8813
	.byte	4,2,35,4,13
	.byte	'CTRL',0
	.word	8979
	.byte	4,2,35,8,13
	.byte	'AEI_ADDR_XPT',0
	.word	9113
	.byte	4,2,35,12,13
	.byte	'IRQ_NOTIFY',0
	.word	9298
	.byte	4,2,35,16,13
	.byte	'IRQ_EN',0
	.word	9507
	.byte	4,2,35,20,13
	.byte	'IRQ_FORCINT',0
	.word	9709
	.byte	4,2,35,24,13
	.byte	'IRQ_MODE',0
	.word	9821
	.byte	4,2,35,28,13
	.byte	'EIRQ_EN',0
	.word	10035
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	3694
	.byte	12,2,35,36,13
	.byte	'BRIDGE',0
	.word	10781
	.byte	12,2,35,48,13
	.byte	'reserved_3C',0
	.word	1535
	.byte	4,2,35,60,13
	.byte	'TIM_AUX_IN_SRC',0
	.word	11036
	.byte	12,2,35,64,13
	.byte	'reserved_4C',0
	.word	11045
	.byte	180,1,2,35,76,13
	.byte	'TBU',0
	.word	12103
	.byte	28,3,35,128,2,13
	.byte	'reserved_11C',0
	.word	12108
	.byte	100,3,35,156,2,13
	.byte	'MON',0
	.word	13218
	.byte	8,3,35,128,3,13
	.byte	'reserved_188',0
	.word	13223
	.byte	120,3,35,136,3,13
	.byte	'CMP',0
	.word	16541
	.byte	24,3,35,128,4,13
	.byte	'reserved_218',0
	.word	16546
	.byte	104,3,35,152,4,13
	.byte	'ARU',0
	.word	18535
	.byte	52,3,35,128,5,13
	.byte	'reserved_2B4',0
	.word	7011
	.byte	76,3,35,180,5,13
	.byte	'CMU',0
	.word	20251
	.byte	72,3,35,128,6,13
	.byte	'reserved_348',0
	.word	20256
	.byte	184,1,3,35,200,6,13
	.byte	'BRC',0
	.word	31202
	.byte	120,3,35,128,8,13
	.byte	'reserved_478',0
	.word	31207
	.byte	136,3,3,35,248,8,13
	.byte	'ICM',0
	.word	37977
	.byte	68,3,35,128,12,13
	.byte	'reserved_644',0
	.word	37982
	.byte	188,3,3,35,196,12,13
	.byte	'SPE',0
	.word	40426
	.byte	128,2,3,35,128,16,13
	.byte	'reserved_900',0
	.word	40431
	.byte	128,12,3,35,128,18,13
	.byte	'MAP_CTRL',0
	.word	40841
	.byte	4,3,35,128,30,13
	.byte	'reserved_F04',0
	.word	40881
	.byte	60,3,35,132,30,13
	.byte	'MCFG_CTRL',0
	.word	40991
	.byte	4,3,35,192,30,13
	.byte	'reserved_F44',0
	.word	41031
	.byte	188,1,3,35,196,30,13
	.byte	'TIM',0
	.word	45087
	.byte	128,48,3,35,128,32,13
	.byte	'reserved_2800',0
	.word	45092
	.byte	128,176,1,3,35,128,80,13
	.byte	'TOM',0
	.word	52797
	.byte	128,32,4,35,128,128,2,13
	.byte	'reserved_9000',0
	.word	52802
	.byte	128,128,1,4,35,128,160,2,13
	.byte	'ATOM',0
	.word	59254
	.byte	128,64,4,35,128,160,3,13
	.byte	'reserved_F000',0
	.word	59259
	.byte	128,160,2,4,35,128,224,3,13
	.byte	'F2A0',0
	.word	59983
	.byte	68,4,35,128,128,6,13
	.byte	'reserved_18044',0
	.word	40881
	.byte	60,4,35,196,128,6,13
	.byte	'AFD0',0
	.word	60211
	.byte	128,1,4,35,128,129,6,13
	.byte	'reserved_18100',0
	.word	60216
	.byte	128,6,4,35,128,130,6,13
	.byte	'FIFO0',0
	.word	62695
	.byte	128,4,4,35,128,136,6,13
	.byte	'reserved_18600',0
	.word	62700
	.byte	128,244,3,4,35,128,140,6,13
	.byte	'DPLL',0
	.word	81613
	.byte	152,30,4,35,128,128,10,13
	.byte	'reserved_28F18',0
	.word	81618
	.byte	232,225,1,4,35,152,158,10,13
	.byte	'MCS',0
	.word	86371
	.byte	128,96,4,35,128,128,12,13
	.byte	'reserved_33000',0
	.word	86376
	.byte	128,154,27,4,35,128,224,12,13
	.byte	'CLC',0
	.word	86506
	.byte	4,4,35,128,250,39,13
	.byte	'reserved_9FD04',0
	.word	3694
	.byte	12,4,35,132,250,39,13
	.byte	'INOUTSEL',0
	.word	88749
	.byte	156,1,4,35,144,250,39,13
	.byte	'reserved_9FDAC',0
	.word	1535
	.byte	4,4,35,172,251,39,13
	.byte	'ADCTRIG0OUT0',0
	.word	88890
	.byte	4,4,35,176,251,39,13
	.byte	'reserved_9FDB4',0
	.word	1535
	.byte	4,4,35,180,251,39,13
	.byte	'ADCTRIG1OUT0',0
	.word	89066
	.byte	4,4,35,184,251,39,13
	.byte	'reserved_9FDBC',0
	.word	3354
	.byte	8,4,35,188,251,39,13
	.byte	'OTBU0T',0
	.word	89208
	.byte	4,4,35,196,251,39,13
	.byte	'OTBU1T',0
	.word	89350
	.byte	4,4,35,200,251,39,13
	.byte	'OTBU2T',0
	.word	89492
	.byte	4,4,35,204,251,39,13
	.byte	'OTSS',0
	.word	89677
	.byte	4,4,35,208,251,39,13
	.byte	'OTSC0',0
	.word	89971
	.byte	4,4,35,212,251,39,13
	.byte	'OTSC1',0
	.word	90127
	.byte	4,4,35,216,251,39,13
	.byte	'ODA',0
	.word	90247
	.byte	4,4,35,220,251,39,13
	.byte	'reserved_9FDE0',0
	.word	3354
	.byte	8,4,35,224,251,39,13
	.byte	'OCS',0
	.word	90407
	.byte	4,4,35,232,251,39,13
	.byte	'KRSTCLR',0
	.word	90513
	.byte	4,4,35,236,251,39,13
	.byte	'KRST1',0
	.word	90617
	.byte	4,4,35,240,251,39,13
	.byte	'KRST0',0
	.word	90740
	.byte	4,4,35,244,251,39,13
	.byte	'ACCEN1',0
	.word	90829
	.byte	4,4,35,248,251,39,13
	.byte	'ACCEN0',0
	.word	91398
	.byte	4,4,35,252,251,39,13
	.byte	'DXOUTCON',0
	.word	91615
	.byte	4,4,35,128,252,39,13
	.byte	'TRIGOUT0',0
	.word	91881
	.byte	12,4,35,132,252,39,13
	.byte	'reserved_9FE10',0
	.word	91890
	.byte	52,4,35,144,252,39,13
	.byte	'TRIGOUT1',0
	.word	91881
	.byte	12,4,35,196,252,39,13
	.byte	'reserved_9FE50',0
	.word	58671
	.byte	32,4,35,208,252,39,13
	.byte	'MCSINTSTAT',0
	.word	92170
	.byte	4,4,35,240,252,39,13
	.byte	'MCSINTCLR',0
	.word	92480
	.byte	4,4,35,244,252,39,13
	.byte	'reserved_9FE78',0
	.word	2725
	.byte	24,4,35,248,252,39,13
	.byte	'DXINCON',0
	.word	92838
	.byte	4,4,35,144,253,39,13
	.byte	'DATAIN0',0
	.word	92962
	.byte	12,4,35,148,253,39,13
	.byte	'reserved_9FEA0',0
	.word	91890
	.byte	52,4,35,160,253,39,13
	.byte	'DATAIN1',0
	.word	92962
	.byte	12,4,35,212,253,39,13
	.byte	'reserved_9FEE0',0
	.word	58671
	.byte	32,4,35,224,253,39,13
	.byte	'MSCSET_1S',0
	.word	93977
	.byte	64,4,35,128,254,39,13
	.byte	'reserved_9FF40',0
	.word	58671
	.byte	32,4,35,192,254,39,13
	.byte	'MSCIN',0
	.word	94717
	.byte	16,4,35,224,254,39,13
	.byte	'MSC0INLEXTCON',0
	.word	95019
	.byte	4,4,35,240,254,39,13
	.byte	'reserved_9FF74',0
	.word	95059
	.byte	140,1,4,35,244,254,39,0,16
	.word	95070
.L18:
	.byte	3
	.word	96925
.L20:
	.byte	17,6,237,1,9,1,18
	.byte	'IfxGtm_Dpll_SubInc_1',0,0,18
	.byte	'IfxGtm_Dpll_SubInc_2',0,1,0,7
	.byte	'short int',0,2,5,22
	.byte	'__wchar_t',0,7,1,1
	.word	96988
	.byte	22
	.byte	'__size_t',0,7,1,1
	.word	467
	.byte	22
	.byte	'__ptrdiff_t',0,7,1,1
	.word	483
	.byte	23,1,3
	.word	97056
	.byte	22
	.byte	'__codeptr',0,7,1,1
	.word	97058
	.byte	22
	.byte	'boolean',0,8,101,29
	.word	490
	.byte	22
	.byte	'uint8',0,8,105,29
	.word	490
	.byte	22
	.byte	'uint16',0,8,109,29
	.word	507
	.byte	7
	.byte	'unsigned long int',0,4,7,22
	.byte	'uint32',0,8,113,29
	.word	97126
	.byte	22
	.byte	'uint64',0,8,118,29
	.word	349
	.byte	22
	.byte	'sint16',0,8,126,29
	.word	96988
	.byte	7
	.byte	'long int',0,4,5,22
	.byte	'sint32',0,8,131,1,29
	.word	97192
	.byte	7
	.byte	'long long int',0,8,5,22
	.byte	'sint64',0,8,138,1,29
	.word	97220
	.byte	22
	.byte	'float32',0,8,167,1,29
	.word	295
	.byte	22
	.byte	'pvoid',0,9,57,28
	.word	381
	.byte	22
	.byte	'Ifx_TickTime',0,9,79,28
	.word	97220
	.byte	22
	.byte	'Ifx_GTM_ACCEN0_Bits',0,5,83,3
	.word	90869
	.byte	22
	.byte	'Ifx_GTM_ACCEN1_Bits',0,5,89,3
	.word	90780
	.byte	22
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,5,100,3
	.word	88754
	.byte	22
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,5,111,3
	.word	88930
	.byte	22
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,5,119,3
	.word	9019
	.byte	22
	.byte	'Ifx_GTM_AFD_CH_BUF_ACC_Bits',0,5,126,3
	.word	59988
	.byte	22
	.byte	'Ifx_GTM_ARU_ARU_ACCESS_Bits',0,5,136,1,3
	.word	16555
	.byte	22
	.byte	'Ifx_GTM_ARU_DATA_H_Bits',0,5,143,1,3
	.word	16724
	.byte	22
	.byte	'Ifx_GTM_ARU_DATA_L_Bits',0,5,150,1,3
	.word	16835
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_ACCESS0_Bits',0,5,157,1,3
	.word	16946
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_ACCESS1_Bits',0,5,164,1,3
	.word	17293
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA0_H_Bits',0,5,171,1,3
	.word	17061
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA0_L_Bits',0,5,178,1,3
	.word	17177
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA1_H_Bits',0,5,185,1,3
	.word	17408
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA1_L_Bits',0,5,192,1,3
	.word	17524
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_EN_Bits',0,5,201,1,3
	.word	17799
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_FORCINT_Bits',0,5,210,1,3
	.word	17975
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_MODE_Bits',0,5,217,1,3
	.word	18146
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_NOTIFY_Bits',0,5,226,1,3
	.word	17640
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_ACT_TB_Bits',0,5,235,1,3
	.word	57237
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_CTRL_Bits',0,5,249,1,3
	.word	56661
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_STAT_Bits',0,5,135,2,3
	.word	56949
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_FUPD_CTRL_Bits',0,5,156,2,3
	.word	57969
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_GLB_CTRL_Bits',0,5,179,2,3
	.word	56211
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_INT_TRIG_Bits',0,5,193,2,3
	.word	58401
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_CTRL_Bits',0,5,207,2,3
	.word	57393
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_STAT_Bits',0,5,221,2,3
	.word	57681
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CM0_Bits',0,5,228,2,3
	.word	54879
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CM1_Bits',0,5,235,2,3
	.word	54990
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CN0_Bits',0,5,242,2,3
	.word	55101
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CTRL_Bits',0,5,137,3,3
	.word	52973
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_EN_Bits',0,5,145,3,3
	.word	55567
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_FORCINT_Bits',0,5,153,3,3
	.word	55715
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_MODE_Bits',0,5,160,3,3
	.word	55862
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_NOTIFY_Bits',0,5,168,3,3
	.word	55429
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_RDADDR_Bits',0,5,177,3,3
	.word	52814
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMC_Bits',0,5,198,3,3
	.word	53386
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMI_Bits',0,5,210,3,3
	.word	53764
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMP_Bits',0,5,229,3,3
	.word	53969
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMS_Bits',0,5,244,3,3
	.word	54318
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SR0_Bits',0,5,251,3,3
	.word	54657
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SR1_Bits',0,5,130,4,3
	.word	54768
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_STAT_Bits',0,5,143,4,3
	.word	55212
	.byte	22
	.byte	'Ifx_GTM_BRC_EIRQ_EN_Bits',0,5,162,4,3
	.word	30268
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_EN_Bits',0,5,181,4,3
	.word	29332
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_FORCINT_Bits',0,5,200,4,3
	.word	29680
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_MODE_Bits',0,5,207,4,3
	.word	30046
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_NOTIFY_Bits',0,5,226,4,3
	.word	29019
	.byte	22
	.byte	'Ifx_GTM_BRC_RST_Bits',0,5,233,4,3
	.word	30162
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC0_ADDR_Bits',0,5,242,4,3
	.word	20267
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC0_DEST_Bits',0,5,143,5,3
	.word	20423
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC10_ADDR_Bits',0,5,152,5,3
	.word	27557
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC10_DEST_Bits',0,5,181,5,3
	.word	27714
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC11_ADDR_Bits',0,5,190,5,3
	.word	28288
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC11_DEST_Bits',0,5,219,5,3
	.word	28445
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC1_ADDR_Bits',0,5,228,5,3
	.word	20996
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC1_DEST_Bits',0,5,129,6,3
	.word	21152
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC2_ADDR_Bits',0,5,138,6,3
	.word	21725
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC2_DEST_Bits',0,5,167,6,3
	.word	21881
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC3_ADDR_Bits',0,5,176,6,3
	.word	22454
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC3_DEST_Bits',0,5,205,6,3
	.word	22610
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC4_ADDR_Bits',0,5,214,6,3
	.word	23183
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC4_DEST_Bits',0,5,243,6,3
	.word	23339
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC5_ADDR_Bits',0,5,252,6,3
	.word	23912
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC5_DEST_Bits',0,5,153,7,3
	.word	24068
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC6_ADDR_Bits',0,5,162,7,3
	.word	24641
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC6_DEST_Bits',0,5,191,7,3
	.word	24797
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC7_ADDR_Bits',0,5,200,7,3
	.word	25370
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC7_DEST_Bits',0,5,229,7,3
	.word	25526
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC8_ADDR_Bits',0,5,238,7,3
	.word	26099
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC8_DEST_Bits',0,5,139,8,3
	.word	26255
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC9_ADDR_Bits',0,5,148,8,3
	.word	26828
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC9_DEST_Bits',0,5,177,8,3
	.word	26984
	.byte	22
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,5,193,8,3
	.word	10075
	.byte	22
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,5,204,8,3
	.word	10389
	.byte	22
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,5,211,8,3
	.word	10597
	.byte	22
	.byte	'Ifx_GTM_CLC_Bits',0,5,221,8,3
	.word	86389
	.byte	22
	.byte	'Ifx_GTM_CMP_EIRQ_EN_Bits',0,5,251,8,3
	.word	15717
	.byte	22
	.byte	'Ifx_GTM_CMP_EN_Bits',0,5,153,9,3
	.word	13232
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_EN_Bits',0,5,183,9,3
	.word	14318
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_FORCINT_Bits',0,5,213,9,3
	.word	14993
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_MODE_Bits',0,5,220,9,3
	.word	15601
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_NOTIFY_Bits',0,5,250,9,3
	.word	13807
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,5,129,10,3
	.word	19101
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,5,137,10,3
	.word	19275
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,5,145,10,3
	.word	19458
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,5,163,10,3
	.word	18540
	.byte	22
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,5,170,10,3
	.word	19758
	.byte	22
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,5,177,10,3
	.word	19641
	.byte	22
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,5,184,10,3
	.word	19940
	.byte	22
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,5,191,10,3
	.word	18984
	.byte	22
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,5,198,10,3
	.word	18867
	.byte	22
	.byte	'Ifx_GTM_CTRL_Bits',0,5,208,10,3
	.word	8853
	.byte	22
	.byte	'Ifx_GTM_DATAIN_Bits',0,5,214,10,3
	.word	92878
	.byte	22
	.byte	'Ifx_GTM_DPLL_ACB_Bits',0,5,227,10,3
	.word	79198
	.byte	22
	.byte	'Ifx_GTM_DPLL_ACT_STA_Bits',0,5,234,10,3
	.word	64534
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL1_Bits',0,5,241,10,3
	.word	73742
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL2_Bits',0,5,248,10,3
	.word	73867
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD1_Bits',0,5,255,10,3
	.word	69861
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD2_Bits',0,5,134,11,3
	.word	69984
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADT_S_Bits',0,5,142,11,3
	.word	78694
	.byte	22
	.byte	'Ifx_GTM_DPLL_AOSV_2_Bits',0,5,151,11,3
	.word	64815
	.byte	22
	.byte	'Ifx_GTM_DPLL_APS_1C3_Bits',0,5,159,11,3
	.word	65513
	.byte	22
	.byte	'Ifx_GTM_DPLL_APS_Bits',0,5,171,11,3
	.word	65170
	.byte	22
	.byte	'Ifx_GTM_DPLL_APS_SYNC_Bits',0,5,181,11,3
	.word	69429
	.byte	22
	.byte	'Ifx_GTM_DPLL_APT_2C_Bits',0,5,189,11,3
	.word	65377
	.byte	22
	.byte	'Ifx_GTM_DPLL_APT_Bits',0,5,201,11,3
	.word	64964
	.byte	22
	.byte	'Ifx_GTM_DPLL_APT_SYNC_Bits',0,5,211,11,3
	.word	69240
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_SX_Bits',0,5,218,11,3
	.word	76270
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_SX_NOM_Bits',0,5,225,11,3
	.word	76506
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_TX_Bits',0,5,232,11,3
	.word	76156
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_TX_NOM_Bits',0,5,239,11,3
	.word	76384
	.byte	22
	.byte	'Ifx_GTM_DPLL_CNT_NUM1_Bits',0,5,246,11,3
	.word	77212
	.byte	22
	.byte	'Ifx_GTM_DPLL_CNT_NUM2_Bits',0,5,253,11,3
	.word	77331
	.byte	22
	.byte	'Ifx_GTM_DPLL_CRTL_1_SHADOW_STATE_Bits',0,5,141,12,3
	.word	71525
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_0_Bits',0,5,157,12,3
	.word	62713
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_STATE_Bits',0,5,170,12,3
	.word	71063
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_TRIGGER_Bits',0,5,183,12,3
	.word	70839
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_1_Bits',0,5,210,12,3
	.word	62951
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_1_SHADOW_TRIGGER_Bits',0,5,223,12,3
	.word	71300
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_2_Bits',0,5,246,12,3
	.word	63384
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_3_Bits',0,5,141,13,3
	.word	63758
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_4_Bits',0,5,164,13,3
	.word	64144
	.byte	22
	.byte	'Ifx_GTM_DPLL_DLA_Bits',0,5,171,13,3
	.word	72103
	.byte	22
	.byte	'Ifx_GTM_DPLL_DT_S_ACT_Bits',0,5,178,13,3
	.word	75586
	.byte	22
	.byte	'Ifx_GTM_DPLL_DT_S_Bits',0,5,185,13,3
	.word	78829
	.byte	22
	.byte	'Ifx_GTM_DPLL_DT_T_ACT_Bits',0,5,192,13,3
	.word	75468
	.byte	22
	.byte	'Ifx_GTM_DPLL_DTA_Bits',0,5,199,13,3
	.word	72349
	.byte	22
	.byte	'Ifx_GTM_DPLL_EDT_S_Bits',0,5,207,13,3
	.word	75930
	.byte	22
	.byte	'Ifx_GTM_DPLL_EDT_T_Bits',0,5,215,13,3
	.word	75704
	.byte	22
	.byte	'Ifx_GTM_DPLL_EIRQ_EN_Bits',0,5,249,13,3
	.word	68259
	.byte	22
	.byte	'Ifx_GTM_DPLL_FTV_S_Bits',0,5,128,14,3
	.word	73051
	.byte	22
	.byte	'Ifx_GTM_DPLL_FTV_T_Bits',0,5,135,14,3
	.word	72702
	.byte	22
	.byte	'Ifx_GTM_DPLL_ID_PMTR_Bits',0,5,142,14,3
	.word	70703
	.byte	22
	.byte	'Ifx_GTM_DPLL_INC_CNT1_Bits',0,5,149,14,3
	.word	69004
	.byte	22
	.byte	'Ifx_GTM_DPLL_INC_CNT2_Bits',0,5,157,14,3
	.word	69122
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_EN_Bits',0,5,191,14,3
	.word	66780
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_FORCINT_Bits',0,5,225,14,3
	.word	67490
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_MODE_Bits',0,5,232,14,3
	.word	68142
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_NOTIFY_Bits',0,5,138,15,3
	.word	66241
	.byte	22
	.byte	'Ifx_GTM_DPLL_MEDT_S_Bits',0,5,146,15,3
	.word	76042
	.byte	22
	.byte	'Ifx_GTM_DPLL_MEDT_T_Bits',0,5,154,15,3
	.word	75816
	.byte	22
	.byte	'Ifx_GTM_DPLL_MLS1_Bits',0,5,161,15,3
	.word	76992
	.byte	22
	.byte	'Ifx_GTM_DPLL_MLS2_Bits',0,5,168,15,3
	.word	77102
	.byte	22
	.byte	'Ifx_GTM_DPLL_MPVAL1_Bits',0,5,177,15,3
	.word	73992
	.byte	22
	.byte	'Ifx_GTM_DPLL_MPVAL2_Bits',0,5,186,15,3
	.word	74122
	.byte	22
	.byte	'Ifx_GTM_DPLL_NA_Bits',0,5,194,15,3
	.word	72220
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_S_Bits',0,5,201,15,3
	.word	78338
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR_Bits',0,5,208,15,3
	.word	74500
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR_OLD_Bits',0,5,215,15,3
	.word	74620
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_T_Bits',0,5,222,15,3
	.word	78226
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR_Bits',0,5,229,15,3
	.word	74252
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR_OLD_Bits',0,5,236,15,3
	.word	74372
	.byte	22
	.byte	'Ifx_GTM_DPLL_NTI_CNT_Bits',0,5,243,15,3
	.word	66125
	.byte	22
	.byte	'Ifx_GTM_DPLL_NUSC_Bits',0,5,129,16,3
	.word	65899
	.byte	22
	.byte	'Ifx_GTM_DPLL_NUTC_Bits',0,5,144,16,3
	.word	65650
	.byte	22
	.byte	'Ifx_GTM_DPLL_OSW_Bits',0,5,154,16,3
	.word	64649
	.byte	22
	.byte	'Ifx_GTM_DPLL_PDT_T_Bits',0,5,162,16,3
	.word	76851
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSA_Bits',0,5,169,16,3
	.word	71986
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSAC_Bits',0,5,176,16,3
	.word	79079
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSSC_Bits',0,5,183,16,3
	.word	77668
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSSM_0_Bits',0,5,190,16,3
	.word	78002
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSSM_1_Bits',0,5,197,16,3
	.word	78114
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSTC_Bits',0,5,204,16,3
	.word	77558
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSTM_0_Bits',0,5,211,16,3
	.word	77778
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSTM_1_Bits',0,5,218,16,3
	.word	77890
	.byte	22
	.byte	'Ifx_GTM_DPLL_PVT_Bits',0,5,225,16,3
	.word	77450
	.byte	22
	.byte	'Ifx_GTM_DPLL_RAM_INI_Bits',0,5,236,16,3
	.word	71792
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_SX_Bits',0,5,243,16,3
	.word	74864
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_SX_NOM_Bits',0,5,251,16,3
	.word	75104
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_TX_Bits',0,5,130,17,3
	.word	74748
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_TX_NOM_Bits',0,5,138,17,3
	.word	74980
	.byte	22
	.byte	'Ifx_GTM_DPLL_RDT_S_ACT_Bits',0,5,145,17,3
	.word	75348
	.byte	22
	.byte	'Ifx_GTM_DPLL_RDT_S_Bits',0,5,152,17,3
	.word	78450
	.byte	22
	.byte	'Ifx_GTM_DPLL_RDT_T_ACT_Bits',0,5,159,17,3
	.word	75228
	.byte	22
	.byte	'Ifx_GTM_DPLL_SLR_Bits',0,5,166,17,3
	.word	76735
	.byte	22
	.byte	'Ifx_GTM_DPLL_STATUS_Bits',0,5,202,17,3
	.word	70116
	.byte	22
	.byte	'Ifx_GTM_DPLL_TBU_TS0_S_Bits',0,5,209,17,3
	.word	69741
	.byte	22
	.byte	'Ifx_GTM_DPLL_TBU_TS0_T_Bits',0,5,216,17,3
	.word	69621
	.byte	22
	.byte	'Ifx_GTM_DPLL_THMA_Bits',0,5,223,17,3
	.word	73276
	.byte	22
	.byte	'Ifx_GTM_DPLL_THMI_Bits',0,5,230,17,3
	.word	73166
	.byte	22
	.byte	'Ifx_GTM_DPLL_THVAL_Bits',0,5,237,17,3
	.word	73386
	.byte	22
	.byte	'Ifx_GTM_DPLL_TLR_Bits',0,5,244,17,3
	.word	76628
	.byte	22
	.byte	'Ifx_GTM_DPLL_TOV_Bits',0,5,252,17,3
	.word	73498
	.byte	22
	.byte	'Ifx_GTM_DPLL_TOV_S_Bits',0,5,132,18,3
	.word	73619
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_S_0_Bits',0,5,139,18,3
	.word	72819
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_S_1_Bits',0,5,146,18,3
	.word	72935
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_T_0_Bits',0,5,153,18,3
	.word	72466
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_T_1_Bits',0,5,160,18,3
	.word	72584
	.byte	22
	.byte	'Ifx_GTM_DPLL_TSAC_Bits',0,5,167,18,3
	.word	78960
	.byte	22
	.byte	'Ifx_GTM_DPLL_TSF_S_Bits',0,5,174,18,3
	.word	78572
	.byte	22
	.byte	'Ifx_GTM_DXINCON_Bits',0,5,195,18,3
	.word	92520
	.byte	22
	.byte	'Ifx_GTM_DXOUTCON_Bits',0,5,208,18,3
	.word	91438
	.byte	22
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,5,218,18,3
	.word	9861
	.byte	22
	.byte	'Ifx_GTM_F2A_ENABLE_Bits',0,5,232,18,3
	.word	59669
	.byte	22
	.byte	'Ifx_GTM_F2A_RD_CH_ARU_RD_FIFO_Bits',0,5,239,18,3
	.word	59272
	.byte	22
	.byte	'Ifx_GTM_F2A_STR_CH_STR_CFG_Bits',0,5,248,18,3
	.word	59454
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_CTRL_Bits',0,5,130,19,3
	.word	60227
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_EIRQ_EN_Bits',0,5,140,19,3
	.word	62140
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_END_ADDR_Bits',0,5,147,19,3
	.word	60388
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_FILL_LEVEL_Bits',0,5,154,19,3
	.word	61024
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_EN_Bits',0,5,164,19,3
	.word	61559
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_FORCINT_Bits',0,5,174,19,3
	.word	61768
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_MODE_Bits',0,5,183,19,3
	.word	61970
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_NOTIFY_Bits',0,5,193,19,3
	.word	61374
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_LOWER_WM_Bits',0,5,200,19,3
	.word	60741
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_RD_PTR_Bits',0,5,207,19,3
	.word	61259
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_START_ADDR_Bits',0,5,214,19,3
	.word	60505
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_STATUS_Bits',0,5,224,19,3
	.word	60858
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_UPPER_WM_Bits',0,5,231,19,3
	.word	60624
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_WR_PTR_Bits',0,5,238,19,3
	.word	61144
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,5,133,20,3
	.word	31218
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_1_Bits',0,5,166,20,3
	.word	31709
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,5,196,20,3
	.word	32457
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_4_Bits',0,5,226,20,3
	.word	33128
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,5,135,21,3
	.word	33799
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_9_Bits',0,5,172,21,3
	.word	34651
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_CEI0_Bits',0,5,186,21,3
	.word	35981
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,5,216,21,3
	.word	36286
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_CEI3_Bits',0,5,246,21,3
	.word	36984
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,5,141,22,3
	.word	35523
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,5,154,22,3
	.word	87786
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_DSADC_INSEL_Bits',0,5,167,22,3
	.word	87244
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_DSADC_OUTSEL_Bits',0,5,178,22,3
	.word	87479
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_PSI5_OUTSEL0_Bits',0,5,190,22,3
	.word	88045
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_PSI5S_OUTSEL_Bits',0,5,203,22,3
	.word	88299
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,5,224,22,3
	.word	86837
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,5,237,22,3
	.word	86546
	.byte	22
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,5,247,22,3
	.word	9338
	.byte	22
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,5,129,23,3
	.word	9547
	.byte	22
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,5,136,23,3
	.word	9749
	.byte	22
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,5,146,23,3
	.word	9153
	.byte	22
	.byte	'Ifx_GTM_KRST0_Bits',0,5,154,23,3
	.word	90657
	.byte	22
	.byte	'Ifx_GTM_KRST1_Bits',0,5,161,23,3
	.word	90553
	.byte	22
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,5,168,23,3
	.word	90447
	.byte	22
	.byte	'Ifx_GTM_MAP_CTRL_Bits',0,5,191,23,3
	.word	40442
	.byte	22
	.byte	'Ifx_GTM_MCFG_CTRL_Bits',0,5,200,23,3
	.word	40890
	.byte	22
	.byte	'Ifx_GTM_MCS_CH0_CTRG_Bits',0,5,222,23,3
	.word	82971
	.byte	22
	.byte	'Ifx_GTM_MCS_CH0_STRG_Bits',0,5,244,23,3
	.word	83330
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_ACB_Bits',0,5,255,23,3
	.word	82797
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_CTRL_Bits',0,5,145,24,3
	.word	82511
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_EIRQ_EN_Bits',0,5,154,24,3
	.word	84432
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_EN_Bits',0,5,163,24,3
	.word	83963
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_FORCINT_Bits',0,5,172,24,3
	.word	84134
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_MODE_Bits',0,5,179,24,3
	.word	84313
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_NOTIFY_Bits',0,5,188,24,3
	.word	83797
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_PC_Bits',0,5,195,24,3
	.word	83689
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R0_Bits',0,5,202,24,3
	.word	81631
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R1_Bits',0,5,209,24,3
	.word	81741
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R2_Bits',0,5,216,24,3
	.word	81851
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R3_Bits',0,5,223,24,3
	.word	81961
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R4_Bits',0,5,230,24,3
	.word	82071
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R5_Bits',0,5,237,24,3
	.word	82181
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R6_Bits',0,5,244,24,3
	.word	82291
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R7_Bits',0,5,251,24,3
	.word	82401
	.byte	22
	.byte	'Ifx_GTM_MCS_CTRL_Bits',0,5,133,25,3
	.word	84921
	.byte	22
	.byte	'Ifx_GTM_MCS_ERR_Bits',0,5,147,25,3
	.word	85570
	.byte	22
	.byte	'Ifx_GTM_MCS_RST_Bits',0,5,177,25,3
	.word	85094
	.byte	22
	.byte	'Ifx_GTM_MCSINTCLR_Bits',0,5,195,25,3
	.word	92210
	.byte	22
	.byte	'Ifx_GTM_MCSINTSTAT_Bits',0,5,213,25,3
	.word	91899
	.byte	22
	.byte	'Ifx_GTM_MON_ACTIVITY_0_Bits',0,5,243,25,3
	.word	12607
	.byte	22
	.byte	'Ifx_GTM_MON_STATUS_Bits',0,5,140,26,3
	.word	12117
	.byte	22
	.byte	'Ifx_GTM_MSC0INLEXTCON_Bits',0,5,161,26,3
	.word	94722
	.byte	22
	.byte	'Ifx_GTM_MSCIN_INHCON_Bits',0,5,182,26,3
	.word	94318
	.byte	22
	.byte	'Ifx_GTM_MSCIN_INLCON_Bits',0,5,203,26,3
	.word	93982
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON0_Bits',0,5,216,26,3
	.word	92971
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON1_Bits',0,5,229,26,3
	.word	93199
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON2_Bits',0,5,242,26,3
	.word	93427
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON3_Bits',0,5,255,26,3
	.word	93657
	.byte	22
	.byte	'Ifx_GTM_OCS_Bits',0,5,137,27,3
	.word	90287
	.byte	22
	.byte	'Ifx_GTM_ODA_Bits',0,5,145,27,3
	.word	90167
	.byte	22
	.byte	'Ifx_GTM_OTBU0T_Bits',0,5,154,27,3
	.word	89106
	.byte	22
	.byte	'Ifx_GTM_OTBU1T_Bits',0,5,163,27,3
	.word	89248
	.byte	22
	.byte	'Ifx_GTM_OTBU2T_Bits',0,5,172,27,3
	.word	89390
	.byte	22
	.byte	'Ifx_GTM_OTSC0_Bits',0,5,189,27,3
	.word	89717
	.byte	22
	.byte	'Ifx_GTM_OTSC1_Bits',0,5,199,27,3
	.word	90011
	.byte	22
	.byte	'Ifx_GTM_OTSS_Bits',0,5,210,27,3
	.word	89532
	.byte	22
	.byte	'Ifx_GTM_REV_Bits',0,5,222,27,3
	.word	8559
	.byte	22
	.byte	'Ifx_GTM_RST_Bits',0,5,229,27,3
	.word	8751
	.byte	22
	.byte	'Ifx_GTM_SPE_CMP_Bits',0,5,236,27,3
	.word	40073
	.byte	22
	.byte	'Ifx_GTM_SPE_CNT_Bits',0,5,243,27,3
	.word	39966
	.byte	22
	.byte	'Ifx_GTM_SPE_CTRL_STAT_Bits',0,5,136,28,3
	.word	37993
	.byte	22
	.byte	'Ifx_GTM_SPE_EIRQ_EN_Bits',0,5,147,28,3
	.word	39732
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_EN_Bits',0,5,158,28,3
	.word	39170
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_FORCINT_Bits',0,5,169,28,3
	.word	39398
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_MODE_Bits',0,5,176,28,3
	.word	39616
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_NOTIFY_Bits',0,5,187,28,3
	.word	38973
	.byte	22
	.byte	'Ifx_GTM_SPE_OUT_CTRL_Bits',0,5,194,28,3
	.word	38852
	.byte	22
	.byte	'Ifx_GTM_SPE_OUT_PAT_Bits',0,5,201,28,3
	.word	38724
	.byte	22
	.byte	'Ifx_GTM_SPE_PAT_Bits',0,5,222,28,3
	.word	38351
	.byte	22
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,5,229,28,3
	.word	11348
	.byte	22
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,5,237,28,3
	.word	11211
	.byte	22
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,5,244,28,3
	.word	11598
	.byte	22
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,5,252,28,3
	.word	11461
	.byte	22
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,5,131,29,3
	.word	11848
	.byte	22
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,5,139,29,3
	.word	11711
	.byte	22
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,5,148,29,3
	.word	11056
	.byte	22
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,5,162,29,3
	.word	10786
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,5,169,29,3
	.word	41252
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,5,176,29,3
	.word	41474
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,5,205,29,3
	.word	42072
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,5,212,29,3
	.word	41362
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,5,219,29,3
	.word	42621
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,5,231,29,3
	.word	43558
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,5,238,29,3
	.word	41956
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,5,245,29,3
	.word	41840
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,5,252,29,3
	.word	41042
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,5,131,30,3
	.word	41147
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,5,143,30,3
	.word	42952
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,5,155,30,3
	.word	43202
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,5,162,30,3
	.word	43439
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,5,174,30,3
	.word	42740
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,5,181,30,3
	.word	41579
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,5,190,30,3
	.word	41692
	.byte	22
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,5,211,30,3
	.word	44114
	.byte	22
	.byte	'Ifx_GTM_TIM_RST_Bits',0,5,225,30,3
	.word	44466
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,5,232,30,3
	.word	45651
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,5,239,30,3
	.word	45761
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,5,246,30,3
	.word	45871
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,5,136,31,3
	.word	45105
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,5,144,31,3
	.word	46227
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,5,152,31,3
	.word	46374
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,5,159,31,3
	.word	46522
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,5,167,31,3
	.word	46090
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,5,174,31,3
	.word	45431
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,5,181,31,3
	.word	45541
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,5,188,31,3
	.word	45981
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,5,197,31,3
	.word	47308
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,5,211,31,3
	.word	48171
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,5,225,31,3
	.word	48459
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,5,246,31,3
	.word	47464
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,5,141,32,3
	.word	46858
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,5,155,32,3
	.word	47896
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,5,169,32,3
	.word	48747
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,5,183,32,3
	.word	49035
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,5,192,32,3
	.word	49817
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,5,206,32,3
	.word	50680
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,5,220,32,3
	.word	50968
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,5,241,32,3
	.word	49973
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,5,136,33,3
	.word	49367
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,5,150,33,3
	.word	50405
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,5,164,33,3
	.word	51256
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,5,178,33,3
	.word	51544
	.byte	22
	.byte	'Ifx_GTM_TRIGOUT_Bits',0,5,192,33,3
	.word	91655
	.byte	22
	.byte	'Ifx_GTM_ACCEN0',0,5,205,33,3
	.word	91398
	.byte	22
	.byte	'Ifx_GTM_ACCEN1',0,5,213,33,3
	.word	90829
	.byte	22
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,5,221,33,3
	.word	88890
	.byte	22
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,5,229,33,3
	.word	89066
	.byte	22
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,5,237,33,3
	.word	9113
	.byte	22
	.byte	'Ifx_GTM_AFD_CH_BUF_ACC',0,5,245,33,3
	.word	60062
	.byte	22
	.byte	'Ifx_GTM_ARU_ARU_ACCESS',0,5,253,33,3
	.word	16684
	.byte	22
	.byte	'Ifx_GTM_ARU_DATA_H',0,5,133,34,3
	.word	16795
	.byte	22
	.byte	'Ifx_GTM_ARU_DATA_L',0,5,141,34,3
	.word	16906
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_ACCESS0',0,5,149,34,3
	.word	17021
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_ACCESS1',0,5,157,34,3
	.word	17368
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA0_H',0,5,165,34,3
	.word	17137
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA0_L',0,5,173,34,3
	.word	17253
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA1_H',0,5,181,34,3
	.word	17484
	.byte	22
	.byte	'Ifx_GTM_ARU_DBG_DATA1_L',0,5,189,34,3
	.word	17600
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_EN',0,5,197,34,3
	.word	17935
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_FORCINT',0,5,205,34,3
	.word	18106
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_MODE',0,5,213,34,3
	.word	18222
	.byte	22
	.byte	'Ifx_GTM_ARU_IRQ_NOTIFY',0,5,221,34,3
	.word	17759
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_ACT_TB',0,5,229,34,3
	.word	57353
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_CTRL',0,5,237,34,3
	.word	56909
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_STAT',0,5,245,34,3
	.word	57197
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_FUPD_CTRL',0,5,253,34,3
	.word	58361
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_GLB_CTRL',0,5,133,35,3
	.word	56621
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_INT_TRIG',0,5,141,35,3
	.word	58631
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_CTRL',0,5,149,35,3
	.word	57641
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_STAT',0,5,157,35,3
	.word	57929
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CM0',0,5,165,35,3
	.word	54950
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CM1',0,5,173,35,3
	.word	55061
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CN0',0,5,181,35,3
	.word	55172
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_CTRL',0,5,189,35,3
	.word	53346
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_EN',0,5,197,35,3
	.word	55675
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_FORCINT',0,5,205,35,3
	.word	55822
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_MODE',0,5,213,35,3
	.word	55942
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_IRQ_NOTIFY',0,5,221,35,3
	.word	55527
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_RDADDR',0,5,229,35,3
	.word	52933
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMC',0,5,237,35,3
	.word	53724
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMI',0,5,245,35,3
	.word	53929
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMP',0,5,253,35,3
	.word	54278
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SOMS',0,5,133,36,3
	.word	54540
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SR0',0,5,141,36,3
	.word	54728
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_SR1',0,5,149,36,3
	.word	54839
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH_STAT',0,5,157,36,3
	.word	55389
	.byte	22
	.byte	'Ifx_GTM_BRC_EIRQ_EN',0,5,165,36,3
	.word	30577
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_EN',0,5,173,36,3
	.word	29640
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_FORCINT',0,5,181,36,3
	.word	30006
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_MODE',0,5,189,36,3
	.word	30122
	.byte	22
	.byte	'Ifx_GTM_BRC_IRQ_NOTIFY',0,5,197,36,3
	.word	29292
	.byte	22
	.byte	'Ifx_GTM_BRC_RST',0,5,205,36,3
	.word	30228
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC0_ADDR',0,5,213,36,3
	.word	20383
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC0_DEST',0,5,221,36,3
	.word	20956
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC10_ADDR',0,5,229,36,3
	.word	27674
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC10_DEST',0,5,237,36,3
	.word	28248
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC11_ADDR',0,5,245,36,3
	.word	28405
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC11_DEST',0,5,253,36,3
	.word	28979
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC1_ADDR',0,5,133,37,3
	.word	21112
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC1_DEST',0,5,141,37,3
	.word	21685
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC2_ADDR',0,5,149,37,3
	.word	21841
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC2_DEST',0,5,157,37,3
	.word	22414
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC3_ADDR',0,5,165,37,3
	.word	22570
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC3_DEST',0,5,173,37,3
	.word	23143
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC4_ADDR',0,5,181,37,3
	.word	23299
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC4_DEST',0,5,189,37,3
	.word	23872
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC5_ADDR',0,5,197,37,3
	.word	24028
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC5_DEST',0,5,205,37,3
	.word	24601
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC6_ADDR',0,5,213,37,3
	.word	24757
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC6_DEST',0,5,221,37,3
	.word	25330
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC7_ADDR',0,5,229,37,3
	.word	25486
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC7_DEST',0,5,237,37,3
	.word	26059
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC8_ADDR',0,5,245,37,3
	.word	26215
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC8_DEST',0,5,253,37,3
	.word	26788
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC9_ADDR',0,5,133,38,3
	.word	26944
	.byte	22
	.byte	'Ifx_GTM_BRC_SRC9_DEST',0,5,141,38,3
	.word	27517
	.byte	22
	.byte	'Ifx_GTM_BRIDGE_MODE',0,5,149,38,3
	.word	10349
	.byte	22
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,5,157,38,3
	.word	10557
	.byte	22
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,5,165,38,3
	.word	10676
	.byte	22
	.byte	'Ifx_GTM_CLC',0,5,173,38,3
	.word	86506
	.byte	22
	.byte	'Ifx_GTM_CMP_EIRQ_EN',0,5,181,38,3
	.word	16377
	.byte	22
	.byte	'Ifx_GTM_CMP_EN',0,5,189,38,3
	.word	13767
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_EN',0,5,197,38,3
	.word	14953
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_FORCINT',0,5,205,38,3
	.word	15561
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_MODE',0,5,213,38,3
	.word	15677
	.byte	22
	.byte	'Ifx_GTM_CMP_IRQ_NOTIFY',0,5,221,38,3
	.word	14278
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,5,229,38,3
	.word	19180
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,5,237,38,3
	.word	19373
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,5,245,38,3
	.word	19556
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_EN',0,5,253,38,3
	.word	18827
	.byte	22
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,5,133,39,3
	.word	19835
	.byte	22
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,5,141,39,3
	.word	19718
	.byte	22
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,5,149,39,3
	.word	20019
	.byte	22
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,5,157,39,3
	.word	19061
	.byte	22
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,5,165,39,3
	.word	18944
	.byte	22
	.byte	'Ifx_GTM_CTRL',0,5,173,39,3
	.word	8979
	.byte	22
	.byte	'Ifx_GTM_DATAIN',0,5,181,39,3
	.word	92922
	.byte	22
	.byte	'Ifx_GTM_DPLL_ACB',0,5,189,39,3
	.word	79387
	.byte	22
	.byte	'Ifx_GTM_DPLL_ACT_STA',0,5,197,39,3
	.word	64609
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL1',0,5,205,39,3
	.word	73827
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL2',0,5,213,39,3
	.word	73952
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD1',0,5,221,39,3
	.word	69944
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD2',0,5,229,39,3
	.word	70067
	.byte	22
	.byte	'Ifx_GTM_DPLL_ADT_S',0,5,237,39,3
	.word	78779
	.byte	22
	.byte	'Ifx_GTM_DPLL_AOSV_2',0,5,245,39,3
	.word	64924
	.byte	22
	.byte	'Ifx_GTM_DPLL_APS',0,5,253,39,3
	.word	65337
	.byte	22
	.byte	'Ifx_GTM_DPLL_APS_1C3',0,5,133,40,3
	.word	65610
	.byte	22
	.byte	'Ifx_GTM_DPLL_APS_SYNC',0,5,141,40,3
	.word	69581
	.byte	22
	.byte	'Ifx_GTM_DPLL_APT',0,5,149,40,3
	.word	65130
	.byte	22
	.byte	'Ifx_GTM_DPLL_APT_2C',0,5,157,40,3
	.word	65473
	.byte	22
	.byte	'Ifx_GTM_DPLL_APT_SYNC',0,5,165,40,3
	.word	69389
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_SX',0,5,173,40,3
	.word	76344
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_SX_NOM',0,5,181,40,3
	.word	76588
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_TX',0,5,189,40,3
	.word	76230
	.byte	22
	.byte	'Ifx_GTM_DPLL_CDT_TX_NOM',0,5,197,40,3
	.word	76466
	.byte	22
	.byte	'Ifx_GTM_DPLL_CNT_NUM1',0,5,205,40,3
	.word	77291
	.byte	22
	.byte	'Ifx_GTM_DPLL_CNT_NUM2',0,5,213,40,3
	.word	77410
	.byte	22
	.byte	'Ifx_GTM_DPLL_CRTL_1_SHADOW_STATE',0,5,221,40,3
	.word	71752
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_0',0,5,229,40,3
	.word	62911
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_STATE',0,5,237,40,3
	.word	71260
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_TRIGGER',0,5,245,40,3
	.word	71023
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_1',0,5,253,40,3
	.word	63344
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_1_SHADOW_TRIGGER',0,5,133,41,3
	.word	71485
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_2',0,5,141,41,3
	.word	63718
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_3',0,5,149,41,3
	.word	64104
	.byte	22
	.byte	'Ifx_GTM_DPLL_CTRL_4',0,5,157,41,3
	.word	64494
	.byte	22
	.byte	'Ifx_GTM_DPLL_DLA',0,5,165,41,3
	.word	72171
	.byte	22
	.byte	'Ifx_GTM_DPLL_DT_S',0,5,173,41,3
	.word	78899
	.byte	22
	.byte	'Ifx_GTM_DPLL_DT_S_ACT',0,5,181,41,3
	.word	75664
	.byte	22
	.byte	'Ifx_GTM_DPLL_DT_T_ACT',0,5,189,41,3
	.word	75546
	.byte	22
	.byte	'Ifx_GTM_DPLL_DTA',0,5,197,41,3
	.word	72417
	.byte	22
	.byte	'Ifx_GTM_DPLL_EDT_S',0,5,206,41,3
	.word	76002
	.byte	22
	.byte	'Ifx_GTM_DPLL_EDT_T',0,5,215,41,3
	.word	75776
	.byte	22
	.byte	'Ifx_GTM_DPLL_EIRQ_EN',0,5,223,41,3
	.word	68955
	.byte	22
	.byte	'Ifx_GTM_DPLL_FTV_S',0,5,231,41,3
	.word	73126
	.byte	22
	.byte	'Ifx_GTM_DPLL_FTV_T',0,5,239,41,3
	.word	72779
	.byte	22
	.byte	'Ifx_GTM_DPLL_ID_PMTR',0,5,247,41,3
	.word	70780
	.byte	22
	.byte	'Ifx_GTM_DPLL_INC_CNT1',0,5,255,41,3
	.word	69082
	.byte	22
	.byte	'Ifx_GTM_DPLL_INC_CNT2',0,5,136,42,3
	.word	69200
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_EN',0,5,144,42,3
	.word	67450
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_FORCINT',0,5,152,42,3
	.word	68102
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_MODE',0,5,160,42,3
	.word	68219
	.byte	22
	.byte	'Ifx_GTM_DPLL_IRQ_NOTIFY',0,5,168,42,3
	.word	66740
	.byte	22
	.byte	'Ifx_GTM_DPLL_MEDT_S',0,5,177,42,3
	.word	76116
	.byte	22
	.byte	'Ifx_GTM_DPLL_MEDT_T',0,5,186,42,3
	.word	75890
	.byte	22
	.byte	'Ifx_GTM_DPLL_MLS1',0,5,194,42,3
	.word	77062
	.byte	22
	.byte	'Ifx_GTM_DPLL_MLS2',0,5,202,42,3
	.word	77172
	.byte	22
	.byte	'Ifx_GTM_DPLL_MPVAL1',0,5,211,42,3
	.word	74082
	.byte	22
	.byte	'Ifx_GTM_DPLL_MPVAL2',0,5,220,42,3
	.word	74212
	.byte	22
	.byte	'Ifx_GTM_DPLL_NA',0,5,228,42,3
	.word	72300
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_S',0,5,236,42,3
	.word	78410
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR',0,5,244,42,3
	.word	74580
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR_OLD',0,5,252,42,3
	.word	74708
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_T',0,5,132,43,3
	.word	78298
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR',0,5,140,43,3
	.word	74332
	.byte	22
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR_OLD',0,5,148,43,3
	.word	74460
	.byte	22
	.byte	'Ifx_GTM_DPLL_NTI_CNT',0,5,156,43,3
	.word	66201
	.byte	22
	.byte	'Ifx_GTM_DPLL_NUSC',0,5,164,43,3
	.word	66085
	.byte	22
	.byte	'Ifx_GTM_DPLL_NUTC',0,5,172,43,3
	.word	65859
	.byte	22
	.byte	'Ifx_GTM_DPLL_OSW',0,5,180,43,3
	.word	64775
	.byte	22
	.byte	'Ifx_GTM_DPLL_PDT_T',0,5,188,43,3
	.word	76934
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSA',0,5,196,43,3
	.word	72054
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSAC',0,5,204,43,3
	.word	79149
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSSC',0,5,212,43,3
	.word	77738
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSSM_0',0,5,220,43,3
	.word	78074
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSSM_1',0,5,228,43,3
	.word	78186
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSTC',0,5,236,43,3
	.word	77628
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSTM_0',0,5,244,43,3
	.word	77850
	.byte	22
	.byte	'Ifx_GTM_DPLL_PSTM_1',0,5,252,43,3
	.word	77962
	.byte	22
	.byte	'Ifx_GTM_DPLL_PVT',0,5,132,44,3
	.word	77518
	.byte	22
	.byte	'Ifx_GTM_DPLL_RAM_INI',0,5,140,44,3
	.word	71946
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_SX',0,5,148,44,3
	.word	74940
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_SX_NOM',0,5,157,44,3
	.word	75188
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_TX',0,5,165,44,3
	.word	74824
	.byte	22
	.byte	'Ifx_GTM_DPLL_RCDT_TX_NOM',0,5,174,44,3
	.word	75064
	.byte	22
	.byte	'Ifx_GTM_DPLL_RDT_S',0,5,182,44,3
	.word	78522
	.byte	22
	.byte	'Ifx_GTM_DPLL_RDT_S_ACT',0,5,190,44,3
	.word	75428
	.byte	22
	.byte	'Ifx_GTM_DPLL_RDT_T_ACT',0,5,198,44,3
	.word	75308
	.byte	22
	.byte	'Ifx_GTM_DPLL_SLR',0,5,206,44,3
	.word	76802
	.byte	22
	.byte	'Ifx_GTM_DPLL_STATUS',0,5,214,44,3
	.word	70663
	.byte	22
	.byte	'Ifx_GTM_DPLL_TBU_TS0_S',0,5,222,44,3
	.word	69821
	.byte	22
	.byte	'Ifx_GTM_DPLL_TBU_TS0_T',0,5,230,44,3
	.word	69701
	.byte	22
	.byte	'Ifx_GTM_DPLL_THMA',0,5,238,44,3
	.word	73346
	.byte	22
	.byte	'Ifx_GTM_DPLL_THMI',0,5,246,44,3
	.word	73236
	.byte	22
	.byte	'Ifx_GTM_DPLL_THVAL',0,5,254,44,3
	.word	73458
	.byte	22
	.byte	'Ifx_GTM_DPLL_TLR',0,5,134,45,3
	.word	76695
	.byte	22
	.byte	'Ifx_GTM_DPLL_TOV',0,5,142,45,3
	.word	73579
	.byte	22
	.byte	'Ifx_GTM_DPLL_TOV_S',0,5,150,45,3
	.word	73702
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_S_0',0,5,158,45,3
	.word	72895
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_S_1',0,5,166,45,3
	.word	73011
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_T_0',0,5,174,45,3
	.word	72544
	.byte	22
	.byte	'Ifx_GTM_DPLL_TS_T_1',0,5,182,45,3
	.word	72662
	.byte	22
	.byte	'Ifx_GTM_DPLL_TSAC',0,5,190,45,3
	.word	79030
	.byte	22
	.byte	'Ifx_GTM_DPLL_TSF_S',0,5,198,45,3
	.word	78644
	.byte	22
	.byte	'Ifx_GTM_DXINCON',0,5,206,45,3
	.word	92838
	.byte	22
	.byte	'Ifx_GTM_DXOUTCON',0,5,214,45,3
	.word	91615
	.byte	22
	.byte	'Ifx_GTM_EIRQ_EN',0,5,222,45,3
	.word	10035
	.byte	22
	.byte	'Ifx_GTM_F2A_ENABLE',0,5,230,45,3
	.word	59876
	.byte	22
	.byte	'Ifx_GTM_F2A_RD_CH_ARU_RD_FIFO',0,5,238,45,3
	.word	59353
	.byte	22
	.byte	'Ifx_GTM_F2A_STR_CH_STR_CFG',0,5,246,45,3
	.word	59571
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_CTRL',0,5,254,45,3
	.word	60348
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_EIRQ_EN',0,5,134,46,3
	.word	62312
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_END_ADDR',0,5,142,46,3
	.word	60465
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_FILL_LEVEL',0,5,150,46,3
	.word	61104
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_EN',0,5,158,46,3
	.word	61728
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_FORCINT',0,5,166,46,3
	.word	61930
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_MODE',0,5,174,46,3
	.word	62100
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_IRQ_NOTIFY',0,5,182,46,3
	.word	61519
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_LOWER_WM',0,5,190,46,3
	.word	60818
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_RD_PTR',0,5,198,46,3
	.word	61334
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_START_ADDR',0,5,206,46,3
	.word	60584
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_STATUS',0,5,214,46,3
	.word	60984
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_UPPER_WM',0,5,222,46,3
	.word	60701
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH_WR_PTR',0,5,230,46,3
	.word	61219
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_0',0,5,238,46,3
	.word	31669
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_1',0,5,246,46,3
	.word	32417
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_2',0,5,254,46,3
	.word	33088
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_4',0,5,134,47,3
	.word	33759
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_6',0,5,142,47,3
	.word	34611
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_9',0,5,150,47,3
	.word	35483
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_CEI0',0,5,158,47,3
	.word	36246
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,5,166,47,3
	.word	36944
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_CEI3',0,5,174,47,3
	.word	37642
	.byte	22
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,5,182,47,3
	.word	35941
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,5,190,47,3
	.word	87955
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_DSADC_INSEL',0,5,198,47,3
	.word	87430
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_DSADC_OUTSEL',0,5,206,47,3
	.word	87638
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_PSI5_OUTSEL0',0,5,214,47,3
	.word	88207
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_PSI5S_OUTSEL',0,5,222,47,3
	.word	88484
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,5,230,47,3
	.word	87138
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,5,238,47,3
	.word	86730
	.byte	22
	.byte	'Ifx_GTM_IRQ_EN',0,5,246,47,3
	.word	9507
	.byte	22
	.byte	'Ifx_GTM_IRQ_FORCINT',0,5,254,47,3
	.word	9709
	.byte	22
	.byte	'Ifx_GTM_IRQ_MODE',0,5,134,48,3
	.word	9821
	.byte	22
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,5,142,48,3
	.word	9298
	.byte	22
	.byte	'Ifx_GTM_KRST0',0,5,150,48,3
	.word	90740
	.byte	22
	.byte	'Ifx_GTM_KRST1',0,5,158,48,3
	.word	90617
	.byte	22
	.byte	'Ifx_GTM_KRSTCLR',0,5,166,48,3
	.word	90513
	.byte	22
	.byte	'Ifx_GTM_MAP_CTRL',0,5,174,48,3
	.word	40841
	.byte	22
	.byte	'Ifx_GTM_MCFG_CTRL',0,5,182,48,3
	.word	40991
	.byte	22
	.byte	'Ifx_GTM_MCS_CH0_CTRG',0,5,190,48,3
	.word	83290
	.byte	22
	.byte	'Ifx_GTM_MCS_CH0_STRG',0,5,198,48,3
	.word	83649
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_ACB',0,5,206,48,3
	.word	82931
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_CTRL',0,5,214,48,3
	.word	82757
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_EIRQ_EN',0,5,222,48,3
	.word	84567
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_EN',0,5,230,48,3
	.word	84094
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_FORCINT',0,5,238,48,3
	.word	84273
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_MODE',0,5,246,48,3
	.word	84392
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_IRQ_NOTIFY',0,5,254,48,3
	.word	83923
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_PC',0,5,134,49,3
	.word	83757
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R0',0,5,142,49,3
	.word	81701
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R1',0,5,150,49,3
	.word	81811
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R2',0,5,158,49,3
	.word	81921
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R3',0,5,166,49,3
	.word	82031
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R4',0,5,174,49,3
	.word	82141
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R5',0,5,182,49,3
	.word	82251
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R6',0,5,190,49,3
	.word	82361
	.byte	22
	.byte	'Ifx_GTM_MCS_CH_R7',0,5,198,49,3
	.word	82471
	.byte	22
	.byte	'Ifx_GTM_MCS_CTRL',0,5,206,49,3
	.word	85054
	.byte	22
	.byte	'Ifx_GTM_MCS_ERR',0,5,214,49,3
	.word	85749
	.byte	22
	.byte	'Ifx_GTM_MCS_RST',0,5,222,49,3
	.word	85530
	.byte	22
	.byte	'Ifx_GTM_MCSINTCLR',0,5,230,49,3
	.word	92480
	.byte	22
	.byte	'Ifx_GTM_MCSINTSTAT',0,5,238,49,3
	.word	92170
	.byte	22
	.byte	'Ifx_GTM_MON_ACTIVITY_0',0,5,246,49,3
	.word	13122
	.byte	22
	.byte	'Ifx_GTM_MON_STATUS',0,5,254,49,3
	.word	12567
	.byte	22
	.byte	'Ifx_GTM_MSC0INLEXTCON',0,5,134,50,3
	.word	95019
	.byte	22
	.byte	'Ifx_GTM_MSCIN_INHCON',0,5,142,50,3
	.word	94614
	.byte	22
	.byte	'Ifx_GTM_MSCIN_INLCON',0,5,150,50,3
	.word	94278
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON0',0,5,158,50,3
	.word	93159
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON1',0,5,166,50,3
	.word	93387
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON2',0,5,174,50,3
	.word	93617
	.byte	22
	.byte	'Ifx_GTM_MSCSET_CON3',0,5,182,50,3
	.word	93849
	.byte	22
	.byte	'Ifx_GTM_OCS',0,5,190,50,3
	.word	90407
	.byte	22
	.byte	'Ifx_GTM_ODA',0,5,198,50,3
	.word	90247
	.byte	22
	.byte	'Ifx_GTM_OTBU0T',0,5,206,50,3
	.word	89208
	.byte	22
	.byte	'Ifx_GTM_OTBU1T',0,5,214,50,3
	.word	89350
	.byte	22
	.byte	'Ifx_GTM_OTBU2T',0,5,222,50,3
	.word	89492
	.byte	22
	.byte	'Ifx_GTM_OTSC0',0,5,230,50,3
	.word	89971
	.byte	22
	.byte	'Ifx_GTM_OTSC1',0,5,238,50,3
	.word	90127
	.byte	22
	.byte	'Ifx_GTM_OTSS',0,5,246,50,3
	.word	89677
	.byte	22
	.byte	'Ifx_GTM_REV',0,5,254,50,3
	.word	8711
	.byte	22
	.byte	'Ifx_GTM_RST',0,5,134,51,3
	.word	8813
	.byte	22
	.byte	'Ifx_GTM_SPE_CMP',0,5,142,51,3
	.word	40140
	.byte	22
	.byte	'Ifx_GTM_SPE_CNT',0,5,150,51,3
	.word	40033
	.byte	22
	.byte	'Ifx_GTM_SPE_CTRL_STAT',0,5,158,51,3
	.word	38311
	.byte	22
	.byte	'Ifx_GTM_SPE_EIRQ_EN',0,5,166,51,3
	.word	39926
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_EN',0,5,174,51,3
	.word	39358
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_FORCINT',0,5,182,51,3
	.word	39576
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_MODE',0,5,190,51,3
	.word	39692
	.byte	22
	.byte	'Ifx_GTM_SPE_IRQ_NOTIFY',0,5,198,51,3
	.word	39130
	.byte	22
	.byte	'Ifx_GTM_SPE_OUT_CTRL',0,5,206,51,3
	.word	38933
	.byte	22
	.byte	'Ifx_GTM_SPE_OUT_PAT',0,5,214,51,3
	.word	38803
	.byte	22
	.byte	'Ifx_GTM_SPE_PAT',0,5,222,51,3
	.word	38684
	.byte	22
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,5,230,51,3
	.word	11421
	.byte	22
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,5,238,51,3
	.word	11308
	.byte	22
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,5,246,51,3
	.word	11671
	.byte	22
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,5,254,51,3
	.word	11558
	.byte	22
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,5,134,52,3
	.word	11921
	.byte	22
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,5,142,52,3
	.word	11808
	.byte	22
	.byte	'Ifx_GTM_TBU_CHEN',0,5,150,52,3
	.word	11171
	.byte	22
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,5,158,52,3
	.word	10996
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_CNT',0,5,166,52,3
	.word	41322
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,5,174,52,3
	.word	41539
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,5,182,52,3
	.word	42581
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,5,190,52,3
	.word	41434
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,5,198,52,3
	.word	42700
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,5,206,52,3
	.word	43775
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,5,214,52,3
	.word	42032
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,5,222,52,3
	.word	41916
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,5,230,52,3
	.word	41107
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,5,238,52,3
	.word	41212
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,5,246,52,3
	.word	43162
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,5,254,52,3
	.word	43399
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,5,134,53,3
	.word	43518
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,5,142,53,3
	.word	42912
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,5,150,53,3
	.word	41652
	.byte	22
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,5,158,53,3
	.word	41800
	.byte	22
	.byte	'Ifx_GTM_TIM_IN_SRC',0,5,166,53,3
	.word	44426
	.byte	22
	.byte	'Ifx_GTM_TIM_RST',0,5,174,53,3
	.word	44669
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CM0',0,5,182,53,3
	.word	45721
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CM1',0,5,190,53,3
	.word	45831
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CN0',0,5,198,53,3
	.word	45941
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,5,206,53,3
	.word	45391
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,5,214,53,3
	.word	46334
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,5,222,53,3
	.word	46482
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,5,230,53,3
	.word	46601
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,5,238,53,3
	.word	46187
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_SR0',0,5,246,53,3
	.word	45501
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_SR1',0,5,254,53,3
	.word	45611
	.byte	22
	.byte	'Ifx_GTM_TOM_CH_STAT',0,5,134,54,3
	.word	46050
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,5,142,54,3
	.word	47424
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,5,150,54,3
	.word	48419
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,5,158,54,3
	.word	48707
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,5,166,54,3
	.word	47856
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,5,174,54,3
	.word	47268
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,5,182,54,3
	.word	48126
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,5,190,54,3
	.word	48995
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,5,198,54,3
	.word	49283
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,5,206,54,3
	.word	49933
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,5,214,54,3
	.word	50928
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,5,222,54,3
	.word	51216
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,5,230,54,3
	.word	50365
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,5,238,54,3
	.word	49777
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,5,246,54,3
	.word	50635
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,5,254,54,3
	.word	51504
	.byte	22
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,5,134,55,3
	.word	51792
	.byte	22
	.byte	'Ifx_GTM_TRIGOUT',0,5,142,55,3
	.word	91841
	.byte	16
	.word	60102
	.byte	22
	.byte	'Ifx_GTM_AFD_CH',0,5,157,55,3
	.word	119839
	.byte	16
	.word	58680
	.byte	22
	.byte	'Ifx_GTM_ATOM_AGC',0,5,171,55,3
	.word	119868
	.byte	16
	.word	55982
	.byte	22
	.byte	'Ifx_GTM_ATOM_CH',0,5,197,55,3
	.word	119899
	.byte	16
	.word	19220
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK0_5',0,5,203,55,3
	.word	119929
	.byte	16
	.word	19413
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_6',0,5,209,55,3
	.word	119962
	.byte	16
	.word	19596
	.byte	22
	.byte	'Ifx_GTM_CMU_CLK_7',0,5,215,55,3
	.word	119994
	.byte	16
	.word	19875
	.byte	22
	.byte	'Ifx_GTM_CMU_ECLK',0,5,222,55,3
	.word	120026
	.byte	16
	.word	20059
	.byte	22
	.byte	'Ifx_GTM_CMU_FXCLK',0,5,228,55,3
	.word	120057
	.byte	16
	.word	59393
	.byte	22
	.byte	'Ifx_GTM_F2A_RD_CH',0,5,234,55,3
	.word	120089
	.byte	16
	.word	59611
	.byte	22
	.byte	'Ifx_GTM_F2A_STR_CH',0,5,240,55,3
	.word	120121
	.byte	16
	.word	62352
	.byte	22
	.byte	'Ifx_GTM_FIFO_CH',0,5,132,56,3
	.word	120154
	.byte	16
	.word	87995
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,5,138,56,3
	.word	120184
	.byte	16
	.word	87678
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_DSADC',0,5,147,56,3
	.word	120219
	.byte	16
	.word	88247
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_PSI5',0,5,153,56,3
	.word	120256
	.byte	16
	.word	88524
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_PSI5S',0,5,159,56,3
	.word	120292
	.byte	16
	.word	87187
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_T',0,5,165,56,3
	.word	120329
	.byte	16
	.word	86770
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,5,171,56,3
	.word	120362
	.byte	16
	.word	85798
	.byte	22
	.byte	'Ifx_GTM_MCS_CH',0,5,194,56,3
	.word	120397
	.byte	16
	.word	84607
	.byte	22
	.byte	'Ifx_GTM_MCS_CH0',0,5,218,56,3
	.word	120426
	.byte	16
	.word	43815
	.byte	22
	.byte	'Ifx_GTM_TIM_CH',0,5,240,56,3
	.word	120456
	.byte	16
	.word	46641
	.byte	22
	.byte	'Ifx_GTM_TOM_CH',0,5,129,57,3
	.word	120485
	.byte	16
	.word	60177
	.byte	22
	.byte	'Ifx_GTM_AFD',0,5,145,57,3
	.word	120514
	.byte	16
	.word	18262
	.byte	22
	.byte	'Ifx_GTM_ARU',0,5,163,57,3
	.word	120540
	.byte	16
	.word	58937
	.byte	22
	.byte	'Ifx_GTM_ATOM',0,5,184,57,3
	.word	120566
	.byte	16
	.word	30617
	.byte	22
	.byte	'Ifx_GTM_BRC',0,5,219,57,3
	.word	120593
	.byte	16
	.word	10716
	.byte	22
	.byte	'Ifx_GTM_BRIDGE',0,5,227,57,3
	.word	120619
	.byte	16
	.word	16417
	.byte	22
	.byte	'Ifx_GTM_CMP',0,5,238,57,3
	.word	120648
	.byte	16
	.word	20104
	.byte	22
	.byte	'Ifx_GTM_CMU',0,5,251,57,3
	.word	120674
	.byte	16
	.word	79436
	.byte	22
	.byte	'Ifx_GTM_DPLL',0,5,244,58,3
	.word	120700
	.byte	16
	.word	59916
	.byte	22
	.byte	'Ifx_GTM_F2A',0,5,252,58,3
	.word	120727
	.byte	16
	.word	62660
	.byte	22
	.byte	'Ifx_GTM_FIFO',0,5,130,59,3
	.word	120753
	.byte	16
	.word	37682
	.byte	22
	.byte	'Ifx_GTM_ICM',0,5,150,59,3
	.word	120780
	.byte	16
	.word	88576
	.byte	22
	.byte	'Ifx_GTM_INOUTSEL',0,5,164,59,3
	.word	120806
	.byte	16
	.word	86137
	.byte	22
	.byte	'Ifx_GTM_MCS',0,5,182,59,3
	.word	120837
	.byte	16
	.word	13162
	.byte	22
	.byte	'Ifx_GTM_MON',0,5,189,59,3
	.word	120863
	.byte	16
	.word	94654
	.byte	22
	.byte	'Ifx_GTM_MSCIN',0,5,196,59,3
	.word	120889
	.byte	16
	.word	93889
	.byte	22
	.byte	'Ifx_GTM_MSCSET',0,5,205,59,3
	.word	120917
	.byte	16
	.word	40189
	.byte	22
	.byte	'Ifx_GTM_SPE',0,5,222,59,3
	.word	120946
	.byte	16
	.word	11961
	.byte	22
	.byte	'Ifx_GTM_TBU',0,5,234,59,3
	.word	120972
	.byte	16
	.word	44755
	.byte	22
	.byte	'Ifx_GTM_TIM',0,5,128,60,3
	.word	120998
	.byte	16
	.word	51873
	.byte	22
	.byte	'Ifx_GTM_TOM',0,5,177,60,3
	.word	121024
	.byte	16
	.word	95070
	.byte	22
	.byte	'Ifx_GTM',0,5,154,61,3
	.word	121050
	.byte	22
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7107
	.byte	22
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7020
	.byte	22
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3363
	.byte	22
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1416
	.byte	22
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2411
	.byte	22
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1544
	.byte	22
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2191
	.byte	22
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1759
	.byte	22
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1974
	.byte	22
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6379
	.byte	22
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6503
	.byte	22
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6587
	.byte	22
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6767
	.byte	22
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5018
	.byte	22
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5542
	.byte	22
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5192
	.byte	22
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5366
	.byte	22
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6031
	.byte	22
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	845
	.byte	22
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4355
	.byte	22
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4843
	.byte	22
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4502
	.byte	22
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4671
	.byte	22
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5698
	.byte	22
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	529
	.byte	22
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4069
	.byte	22
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3703
	.byte	22
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2734
	.byte	22
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3038
	.byte	22
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7634
	.byte	22
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7067
	.byte	22
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3654
	.byte	22
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1495
	.byte	22
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2685
	.byte	22
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1719
	.byte	22
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2371
	.byte	22
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1934
	.byte	22
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2151
	.byte	22
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6463
	.byte	22
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6712
	.byte	22
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6971
	.byte	22
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6339
	.byte	22
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5152
	.byte	22
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5658
	.byte	22
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5326
	.byte	22
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5502
	.byte	22
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1376
	.byte	22
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5991
	.byte	22
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4462
	.byte	22
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4978
	.byte	22
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4631
	.byte	22
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4803
	.byte	22
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	805
	.byte	22
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4315
	.byte	22
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4029
	.byte	22
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2998
	.byte	22
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3314
	.byte	16
	.word	7674
	.byte	22
	.byte	'Ifx_P',0,4,139,6,3
	.word	122390
	.byte	17,10,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,22
	.byte	'IfxScu_WDTCON1_IR',0,10,255,10,3
	.word	122410
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,11,45,16,4,11
	.byte	'EN0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_ACCEN0_Bits',0,11,79,3
	.word	122532
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,11,82,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1_Bits',0,11,85,3
	.word	123089
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,11,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,11,94,3
	.word	123166
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,11,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	490
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	490
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	490
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	490
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	490
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	490
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	490
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	490
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	490
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	490
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON0_Bits',0,11,111,3
	.word	123302
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,11,114,16,4,11
	.byte	'CANDIV',0,1
	.word	490
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	490
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	490
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	490
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	490
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	490
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	490
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	490
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON1_Bits',0,11,126,3
	.word	123582
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,11,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	490
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON2_Bits',0,11,135,1,3
	.word	123820
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,11,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	490
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	490
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	490
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	490
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	490
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON3_Bits',0,11,150,1,3
	.word	123948
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,11,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	490
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	490
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	490
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	490
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	490
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON4_Bits',0,11,165,1,3
	.word	124191
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,11,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	490
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON5_Bits',0,11,174,1,3
	.word	124426
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,11,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	490
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6_Bits',0,11,181,1,3
	.word	124554
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,11,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	490
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7_Bits',0,11,188,1,3
	.word	124654
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,11,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	490
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	490
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	490
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	490
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	490
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CHIPID_Bits',0,11,202,1,3
	.word	124754
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,11,205,1,16,4,11
	.byte	'PWD',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	467
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSCON_Bits',0,11,213,1,3
	.word	124962
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,11,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	507
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	490
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	507
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	490
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSLIM_Bits',0,11,225,1,3
	.word	125127
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,11,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	507
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	490
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,11,235,1,3
	.word	125310
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,11,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	490
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	490
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	467
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	490
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	490
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EICR_Bits',0,11,129,2,3
	.word	125464
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,11,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR_Bits',0,11,143,2,3
	.word	125828
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,11,146,2,16,4,11
	.byte	'POL',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	507
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	490
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	490
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	490
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	490
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_EMSR_Bits',0,11,159,2,3
	.word	126039
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,11,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	507
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG_Bits',0,11,167,2,3
	.word	126291
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,11,170,2,16,4,11
	.byte	'ARI',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG_Bits',0,11,175,2,3
	.word	126409
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,11,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR13CON_Bits',0,11,185,2,3
	.word	126520
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,11,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	467
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR33CON_Bits',0,11,195,2,3
	.word	126683
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,11,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,11,205,2,3
	.word	126846
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,11,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,11,215,2,3
	.word	127004
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,11,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	490
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	490
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	490
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	490
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	490
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	490
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	490
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	490
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	490
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	507
	.byte	10,0,2,35,2,0,22
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,11,232,2,3
	.word	127169
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,11,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	507
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	490
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	490
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	507
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	490
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,11,245,2,3
	.word	127498
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,11,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROVMON_Bits',0,11,255,2,3
	.word	127719
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,11,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,11,142,3,3
	.word	127882
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,11,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,11,152,3,3
	.word	128154
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,11,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,11,162,3,3
	.word	128307
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,11,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,11,172,3,3
	.word	128463
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,11,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,11,181,3,3
	.word	128625
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,11,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,11,191,3,3
	.word	128768
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,11,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,11,200,3,3
	.word	128933
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,11,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	507
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	490
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	490
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,11,211,3,3
	.word	129078
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,11,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	490
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,11,222,3,3
	.word	129259
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,11,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,11,232,3,3
	.word	129433
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,11,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,11,241,3,3
	.word	129593
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,11,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,11,130,4,3
	.word	129737
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,11,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,11,139,4,3
	.word	130011
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,11,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,11,149,4,3
	.word	130150
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,11,152,4,16,4,11
	.byte	'EN0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	490
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	507
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	490
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	490
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	490
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_EXTCON_Bits',0,11,163,4,3
	.word	130313
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,11,166,4,16,4,11
	.byte	'STEP',0,2
	.word	507
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	490
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	507
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	490
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_FDR_Bits',0,11,174,4,3
	.word	130531
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,11,177,4,16,4,11
	.byte	'FS0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_FMR_Bits',0,11,197,4,3
	.word	130694
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,11,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_ID_Bits',0,11,205,4,3
	.word	131030
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,11,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	490
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_SCU_IGCR_Bits',0,11,232,4,3
	.word	131137
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,11,235,4,16,4,11
	.byte	'P0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_IN_Bits',0,11,240,4,3
	.word	131589
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,11,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	490
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	490
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_IOCR_Bits',0,11,250,4,3
	.word	131688
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,11,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	507
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,11,131,5,3
	.word	131838
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,11,134,5,16,4,11
	.byte	'SEED',0,4
	.word	467
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	490
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,11,141,5,3
	.word	131987
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,11,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	467
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,11,149,5,3
	.word	132148
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,11,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	507
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	507
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LCLCON_Bits',0,11,158,5,3
	.word	132278
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,11,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST_Bits',0,11,166,5,3
	.word	132410
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,11,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	490
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	507
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_MANID_Bits',0,11,174,5,3
	.word	132525
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,11,177,5,16,4,11
	.byte	'PS0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	507
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	507
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_OMR_Bits',0,11,185,5,3
	.word	132636
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,11,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	490
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	490
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	490
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	490
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	490
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	490
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_OSCCON_Bits',0,11,209,5,3
	.word	132794
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,11,212,5,16,4,11
	.byte	'P0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_OUT_Bits',0,11,217,5,3
	.word	133206
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,11,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	507
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	490
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	490
	.byte	6,0,2,35,3,0,22
	.byte	'Ifx_SCU_OVCCON_Bits',0,11,233,5,3
	.word	133307
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,11,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	467
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,11,242,5,3
	.word	133574
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,11,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC_Bits',0,11,250,5,3
	.word	133710
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,11,253,5,16,4,11
	.byte	'PD0',0,1
	.word	490
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	490
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDR_Bits',0,11,132,6,3
	.word	133821
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,11,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR_Bits',0,11,146,6,3
	.word	133954
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,11,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	507
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	490
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	490
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	490
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	490
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLCON0_Bits',0,11,166,6,3
	.word	134157
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,11,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	490
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	490
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	490
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	507
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON1_Bits',0,11,177,6,3
	.word	134513
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,11,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	507
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON2_Bits',0,11,184,6,3
	.word	134691
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,11,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	507
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	490
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	490
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	490
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	490
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,11,204,6,3
	.word	134791
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,11,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	490
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	490
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	490
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	490
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	507
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,11,215,6,3
	.word	135161
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,11,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	467
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,11,227,6,3
	.word	135347
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,11,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	467
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,11,241,6,3
	.word	135545
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,11,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	490
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	490
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	467
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR_Bits',0,11,251,6,3
	.word	135778
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,11,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	490
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	490
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	490
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	490
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	490
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	490
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	490
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,11,153,7,3
	.word	135930
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,11,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	490
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	490
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	490
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	490
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,11,170,7,3
	.word	136497
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,11,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	490
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	490
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	490
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	490
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	490
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	490
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	490
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,11,187,7,3
	.word	136791
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,11,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	490
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	490
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	490
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	490
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	490
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	507
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	490
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	490
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,11,214,7,3
	.word	137069
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,11,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	507
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,11,230,7,3
	.word	137565
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,11,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	507
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	490
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	490
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	490
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON2_Bits',0,11,243,7,3
	.word	137878
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,11,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	490
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	490
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	490
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	490
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	490
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	490
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	490
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	490
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON_Bits',0,11,129,8,3
	.word	138087
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,11,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	490
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	490
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	490
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	490
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	490
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	490
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	490
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,11,155,8,3
	.word	138298
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,11,158,8,16,4,11
	.byte	'HBT',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	467
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON_Bits',0,11,162,8,3
	.word	138730
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,11,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	490
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	490
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	490
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	490
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	490
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	490
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	490
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	490
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	490
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	490
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	490
	.byte	7,0,2,35,3,0,22
	.byte	'Ifx_SCU_STSTAT_Bits',0,11,178,8,3
	.word	138826
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,11,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	467
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,11,186,8,3
	.word	139086
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,11,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	490
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	490
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	467
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON_Bits',0,11,198,8,3
	.word	139211
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,11,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,11,208,8,3
	.word	139408
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,11,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,11,218,8,3
	.word	139561
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,11,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET_Bits',0,11,228,8,3
	.word	139714
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,11,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	467
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,11,238,8,3
	.word	139867
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,11,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8543
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8543
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,11,247,8,3
	.word	140022
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,11,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	490
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	490
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,11,134,9,3
	.word	140152
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,11,137,9,16,4,11
	.byte	'AE',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	490
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,11,150,9,3
	.word	140390
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,11,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	8543
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8543
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8543
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8543
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,11,159,9,3
	.word	140613
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,11,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	490
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,11,175,9,3
	.word	140739
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,11,178,9,16,4,11
	.byte	'AE',0,1
	.word	490
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	490
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	490
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	490
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	490
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	490
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	490
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	490
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	490
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	490
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	507
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,11,191,9,3
	.word	140991
	.byte	12,11,199,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122532
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN0',0,11,204,9,3
	.word	141210
	.byte	12,11,207,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123089
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1',0,11,212,9,3
	.word	141274
	.byte	12,11,215,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123166
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS',0,11,220,9,3
	.word	141338
	.byte	12,11,223,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123302
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON0',0,11,228,9,3
	.word	141403
	.byte	12,11,231,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123582
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON1',0,11,236,9,3
	.word	141468
	.byte	12,11,239,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123820
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON2',0,11,244,9,3
	.word	141533
	.byte	12,11,247,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123948
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON3',0,11,252,9,3
	.word	141598
	.byte	12,11,255,9,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124191
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON4',0,11,132,10,3
	.word	141663
	.byte	12,11,135,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124426
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON5',0,11,140,10,3
	.word	141728
	.byte	12,11,143,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124554
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6',0,11,148,10,3
	.word	141793
	.byte	12,11,151,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124654
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7',0,11,156,10,3
	.word	141858
	.byte	12,11,159,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124754
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CHIPID',0,11,164,10,3
	.word	141923
	.byte	12,11,167,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124962
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSCON',0,11,172,10,3
	.word	141987
	.byte	12,11,175,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125127
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSLIM',0,11,180,10,3
	.word	142051
	.byte	12,11,183,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125310
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSSTAT',0,11,188,10,3
	.word	142115
	.byte	12,11,191,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125464
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EICR',0,11,196,10,3
	.word	142180
	.byte	12,11,199,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125828
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR',0,11,204,10,3
	.word	142242
	.byte	12,11,207,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126039
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EMSR',0,11,212,10,3
	.word	142304
	.byte	12,11,215,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126291
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG',0,11,220,10,3
	.word	142366
	.byte	12,11,223,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126409
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG',0,11,228,10,3
	.word	142430
	.byte	12,11,231,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126520
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR13CON',0,11,236,10,3
	.word	142495
	.byte	12,11,239,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126683
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR33CON',0,11,244,10,3
	.word	142561
	.byte	12,11,247,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126846
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRADCSTAT',0,11,252,10,3
	.word	142627
	.byte	12,11,255,10,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127004
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRDVSTAT',0,11,132,11,3
	.word	142695
	.byte	12,11,135,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127169
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRMONCTRL',0,11,140,11,3
	.word	142762
	.byte	12,11,143,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127498
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROSCCTRL',0,11,148,11,3
	.word	142830
	.byte	12,11,151,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127719
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROVMON',0,11,156,11,3
	.word	142898
	.byte	12,11,159,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127882
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRRSTCON',0,11,164,11,3
	.word	142964
	.byte	12,11,167,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128154
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,11,172,11,3
	.word	143031
	.byte	12,11,175,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128307
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,11,180,11,3
	.word	143100
	.byte	12,11,183,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128463
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,11,188,11,3
	.word	143169
	.byte	12,11,191,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128625
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,11,196,11,3
	.word	143238
	.byte	12,11,199,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128768
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,11,204,11,3
	.word	143307
	.byte	12,11,207,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128933
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,11,212,11,3
	.word	143376
	.byte	12,11,215,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129078
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1',0,11,220,11,3
	.word	143445
	.byte	12,11,223,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129259
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2',0,11,228,11,3
	.word	143513
	.byte	12,11,231,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129433
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3',0,11,236,11,3
	.word	143581
	.byte	12,11,239,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129593
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4',0,11,244,11,3
	.word	143649
	.byte	12,11,247,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129737
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT',0,11,252,11,3
	.word	143717
	.byte	12,11,255,11,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130011
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRTRIM',0,11,132,12,3
	.word	143782
	.byte	12,11,135,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130150
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRUVMON',0,11,140,12,3
	.word	143847
	.byte	12,11,143,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130313
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EXTCON',0,11,148,12,3
	.word	143913
	.byte	12,11,151,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130531
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FDR',0,11,156,12,3
	.word	143977
	.byte	12,11,159,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130694
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FMR',0,11,164,12,3
	.word	144038
	.byte	12,11,167,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131030
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ID',0,11,172,12,3
	.word	144099
	.byte	12,11,175,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131137
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IGCR',0,11,180,12,3
	.word	144159
	.byte	12,11,183,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131589
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IN',0,11,188,12,3
	.word	144221
	.byte	12,11,191,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131688
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IOCR',0,11,196,12,3
	.word	144281
	.byte	12,11,199,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131838
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL0',0,11,204,12,3
	.word	144343
	.byte	12,11,207,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131987
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL1',0,11,212,12,3
	.word	144411
	.byte	12,11,215,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132148
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL2',0,11,220,12,3
	.word	144479
	.byte	12,11,223,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132278
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLCON',0,11,228,12,3
	.word	144547
	.byte	12,11,231,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132410
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST',0,11,236,12,3
	.word	144611
	.byte	12,11,239,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132525
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_MANID',0,11,244,12,3
	.word	144676
	.byte	12,11,247,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132636
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OMR',0,11,252,12,3
	.word	144739
	.byte	12,11,255,12,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132794
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OSCCON',0,11,132,13,3
	.word	144800
	.byte	12,11,135,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133206
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OUT',0,11,140,13,3
	.word	144864
	.byte	12,11,143,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133307
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCCON',0,11,148,13,3
	.word	144925
	.byte	12,11,151,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133574
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE',0,11,156,13,3
	.word	144989
	.byte	12,11,159,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133710
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC',0,11,164,13,3
	.word	145056
	.byte	12,11,167,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133821
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDR',0,11,172,13,3
	.word	145119
	.byte	12,11,175,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133954
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR',0,11,180,13,3
	.word	145180
	.byte	12,11,183,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	134157
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON0',0,11,188,13,3
	.word	145242
	.byte	12,11,191,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	134513
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON1',0,11,196,13,3
	.word	145307
	.byte	12,11,199,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	134691
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON2',0,11,204,13,3
	.word	145372
	.byte	12,11,207,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	134791
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON0',0,11,212,13,3
	.word	145437
	.byte	12,11,215,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135161
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON1',0,11,220,13,3
	.word	145506
	.byte	12,11,223,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135347
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT',0,11,228,13,3
	.word	145575
	.byte	12,11,231,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135545
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT',0,11,236,13,3
	.word	145644
	.byte	12,11,239,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135778
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR',0,11,244,13,3
	.word	145709
	.byte	12,11,247,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135930
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR0',0,11,252,13,3
	.word	145772
	.byte	12,11,255,13,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	136497
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR1',0,11,132,14,3
	.word	145837
	.byte	12,11,135,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	136791
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR2',0,11,140,14,3
	.word	145902
	.byte	12,11,143,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137069
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTAT',0,11,148,14,3
	.word	145967
	.byte	12,11,151,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137565
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR',0,11,156,14,3
	.word	146033
	.byte	12,11,159,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138087
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON',0,11,164,14,3
	.word	146102
	.byte	12,11,167,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137878
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON2',0,11,172,14,3
	.word	146166
	.byte	12,11,175,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138298
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTSTAT',0,11,180,14,3
	.word	146231
	.byte	12,11,183,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138730
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON',0,11,188,14,3
	.word	146296
	.byte	12,11,191,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138826
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_STSTAT',0,11,196,14,3
	.word	146361
	.byte	12,11,199,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	139086
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON',0,11,204,14,3
	.word	146425
	.byte	12,11,207,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	139211
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON',0,11,212,14,3
	.word	146491
	.byte	12,11,215,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	139408
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR',0,11,220,14,3
	.word	146555
	.byte	12,11,223,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	139561
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS',0,11,228,14,3
	.word	146620
	.byte	12,11,231,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	139714
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET',0,11,236,14,3
	.word	146685
	.byte	12,11,239,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	139867
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT',0,11,244,14,3
	.word	146750
	.byte	12,11,247,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	140022
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTCPU_CON0',0,11,252,14,3
	.word	146816
	.byte	12,11,255,14,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	140152
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTCPU_CON1',0,11,132,15,3
	.word	146885
	.byte	12,11,135,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	140390
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTCPU_SR',0,11,140,15,3
	.word	146954
	.byte	12,11,143,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	140613
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0',0,11,148,15,3
	.word	147021
	.byte	12,11,151,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	140739
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON1',0,11,156,15,3
	.word	147088
	.byte	12,11,159,15,9,4,13
	.byte	'U',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	483
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	140991
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_SR',0,11,164,15,3
	.word	147155
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,11,175,15,25,12,13
	.byte	'CON0',0
	.word	146816
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	146885
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	146954
	.byte	4,2,35,8,0,16
	.word	147220
	.byte	22
	.byte	'Ifx_SCU_WDTCPU',0,11,180,15,3
	.word	147283
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,11,183,15,25,12,13
	.byte	'CON0',0
	.word	147021
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	147088
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	147155
	.byte	4,2,35,8,0,16
	.word	147312
	.byte	22
	.byte	'Ifx_SCU_WDTS',0,11,188,15,3
	.word	147373
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,22
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	147400
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,22
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	147551
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,22
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	147795
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,22
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	147893
	.byte	22
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8287
	.byte	22
	.byte	'IfxGtm_Dpll_SubInc',0,6,241,1,3
	.word	96935
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,13,0,73,19,11,15,56,9,0
	.byte	0,22,22,0,3,8,58,15,59,15,57,15,73,19,0,0,23,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L10:
	.word	.L23-.L22
.L22:
	.half	3
	.word	.L25-.L24
.L24:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'IfxGtm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxGtm_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std/IfxGtm_Dpll.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0,0
.L25:
.L23:
	.sdecl	'.debug_info',debug,cluster('IfxGtm_Dpll_getSubIncFrequency')
	.sect	'.debug_info'
.L11:
	.word	339
	.half	3
	.word	.L12
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std/IfxGtm_Dpll.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L14,.L13
	.byte	2
	.word	.L7
	.byte	3
	.byte	'IfxGtm_Dpll_getSubIncFrequency',0,1,55,9
	.word	.L16
	.byte	1,1,1
	.word	.L6,.L17,.L5
	.byte	4
	.byte	'gtm',0,1,55,49
	.word	.L18,.L19
	.byte	4
	.byte	'index',0,1,55,73
	.word	.L20,.L21
	.byte	5
	.word	.L6,.L17
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxGtm_Dpll_getSubIncFrequency')
	.sect	'.debug_abbrev'
.L12:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxGtm_Dpll_getSubIncFrequency')
	.sect	'.debug_line'
.L13:
	.word	.L27-.L26
.L26:
	.half	3
	.word	.L29-.L28
.L28:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std/IfxGtm_Dpll.c',0,0,0,0,0
.L29:
	.byte	5,5,7,0,5,2
	.word	.L6
	.byte	3,56,1,7,9
	.half	.L2-.L6
	.byte	3,1,1,5,12,7,9
	.half	.L3-.L2
	.byte	3,1,1,5,5,9
	.half	.L30-.L3
	.byte	1,5,1,9
	.half	.L4-.L30
	.byte	3,1,1,7,9
	.half	.L15-.L4
	.byte	0,1,1
.L27:
	.sdecl	'.debug_ranges',debug,cluster('IfxGtm_Dpll_getSubIncFrequency')
	.sect	'.debug_ranges'
.L14:
	.word	-1,.L6,0,.L15-.L6,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxGtm_Dpll_getSubIncFrequency')
	.sect	'.debug_loc'
.L5:
	.word	-1,.L6,0,.L17-.L6
	.half	2
	.byte	138,0
	.word	0,0
.L19:
	.word	-1,.L6,0,.L17-.L6
	.half	1
	.byte	100
	.word	0,0
.L21:
	.word	-1,.L6,0,.L17-.L6
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L31:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxGtm_Dpll_getSubIncFrequency')
	.sect	'.debug_frame'
	.word	24
	.word	.L31,.L6,.L17-.L6
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
