	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc3036a --dep-file=IfxCcu6_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxCcu6_cfg.IfxCcu6_cfg_indexMap',data,rom,cluster('IfxCcu6_cfg_indexMap')
	.sect	'.rodata.IfxCcu6_cfg.IfxCcu6_cfg_indexMap'
	.global	IfxCcu6_cfg_indexMap
	.align	4
IfxCcu6_cfg_indexMap:	.type	object
	.size	IfxCcu6_cfg_indexMap,16
	.word	-268424704
	.space	4
	.word	-268424448,1
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	12147
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	233
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	264
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	297
	.byte	4,1,5
	.word	324
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	326
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	349
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	380
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	417
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	233
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	468
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	496
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	529
	.byte	6
	.byte	'void',0,5
	.word	555
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	561
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	496
	.byte	7
	.word	555
	.byte	5
	.word	601
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	606
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	468
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	611
	.byte	10
	.byte	'_Ifx_CCU6_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	349
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	349
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	349
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	349
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	349
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	349
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	349
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	349
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	349
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	349
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	349
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	349
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	349
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	349
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_CCU6_ACCEN0_Bits',0,4,79,3
	.word	677
	.byte	10
	.byte	'_Ifx_CCU6_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	264
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_CCU6_ACCEN1_Bits',0,4,85,3
	.word	1236
	.byte	10
	.byte	'_Ifx_CCU6_CC60R_Bits',0,4,88,16,4,11
	.byte	'CCV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC60R_Bits',0,4,92,3
	.word	1315
	.byte	10
	.byte	'_Ifx_CCU6_CC60SR_Bits',0,4,95,16,4,11
	.byte	'CCS',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC60SR_Bits',0,4,99,3
	.word	1408
	.byte	10
	.byte	'_Ifx_CCU6_CC61R_Bits',0,4,102,16,4,11
	.byte	'CCV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC61R_Bits',0,4,106,3
	.word	1503
	.byte	10
	.byte	'_Ifx_CCU6_CC61SR_Bits',0,4,109,16,4,11
	.byte	'CCS',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC61SR_Bits',0,4,113,3
	.word	1596
	.byte	10
	.byte	'_Ifx_CCU6_CC62R_Bits',0,4,116,16,4,11
	.byte	'CCV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC62R_Bits',0,4,120,3
	.word	1691
	.byte	10
	.byte	'_Ifx_CCU6_CC62SR_Bits',0,4,123,16,4,11
	.byte	'CCS',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC62SR_Bits',0,4,127,3
	.word	1784
	.byte	10
	.byte	'_Ifx_CCU6_CC63R_Bits',0,4,130,1,16,4,11
	.byte	'CCV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC63R_Bits',0,4,134,1,3
	.word	1879
	.byte	10
	.byte	'_Ifx_CCU6_CC63SR_Bits',0,4,137,1,16,4,11
	.byte	'CCS',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CC63SR_Bits',0,4,141,1,3
	.word	1974
	.byte	10
	.byte	'_Ifx_CCU6_CLC_Bits',0,4,144,1,16,4,11
	.byte	'DISR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_CCU6_CLC_Bits',0,4,151,1,3
	.word	2071
	.byte	10
	.byte	'_Ifx_CCU6_CMPMODIF_Bits',0,4,154,1,16,4,11
	.byte	'MCC60S',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'MCC61S',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MCC62S',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	349
	.byte	3,2,2,35,0,11
	.byte	'MCC63S',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'MCC60R',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'MCC61R',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'MCC62R',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	3,2,2,35,1,11
	.byte	'MCC63R',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	264
	.byte	17,0,2,35,0,0,3
	.byte	'Ifx_CCU6_CMPMODIF_Bits',0,4,168,1,3
	.word	2216
	.byte	10
	.byte	'_Ifx_CCU6_CMPSTAT_Bits',0,4,171,1,16,4,11
	.byte	'CC60ST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CC61ST',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CC62ST',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'CCPOS60',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CCPOS61',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CCPOS62',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'CC63ST',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'CC60PS',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'COUT60PS',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'CC61PS',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'COUT61PS',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'CC62PS',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'COUT62PS',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'COUT63PS',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'T13IM',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_CMPSTAT_Bits',0,4,190,1,3
	.word	2513
	.byte	10
	.byte	'_Ifx_CCU6_ID_Bits',0,4,193,1,16,4,11
	.byte	'MODREV',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'MODNUMBER',0,1
	.word	349
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_ID_Bits',0,4,198,1,3
	.word	2899
	.byte	10
	.byte	'_Ifx_CCU6_IEN_Bits',0,4,201,1,16,4,11
	.byte	'ENCC60R',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'ENCC60F',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'ENCC61R',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'ENCC61F',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'ENCC62R',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'ENCC62F',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'ENT12OM',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'ENT12PM',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'ENT13CM',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'ENT13PM',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'ENTRPF',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'ENCHE',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'ENWHE',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'ENIDLE',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'ENSTR',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_IEN_Bits',0,4,220,1,3
	.word	3012
	.byte	10
	.byte	'_Ifx_CCU6_IMON_Bits',0,4,223,1,16,4,11
	.byte	'LBE',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0I',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1I',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2I',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CC60INI',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CC61INI',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'CC62INI',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'CTRAPI',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'T12HRI',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'T13HRI',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	264
	.byte	22,0,2,35,0,0,3
	.byte	'Ifx_CCU6_IMON_Bits',0,4,236,1,3
	.word	3388
	.byte	10
	.byte	'_Ifx_CCU6_INP_Bits',0,4,239,1,16,4,11
	.byte	'INPCC60',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'INPCC61',0,1
	.word	349
	.byte	2,4,2,35,0,11
	.byte	'INPCC62',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'INPCHE',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'INPERR',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'INPT12',0,1
	.word	349
	.byte	2,4,2,35,1,11
	.byte	'INPT13',0,1
	.word	349
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	264
	.byte	18,0,2,35,0,0,3
	.byte	'Ifx_CCU6_INP_Bits',0,4,249,1,3
	.word	3649
	.byte	10
	.byte	'_Ifx_CCU6_IS_Bits',0,4,252,1,16,4,11
	.byte	'ICC60R',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'ICC60F',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'ICC61R',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'ICC61F',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'ICC62R',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'ICC62F',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'T12OM',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'T12PM',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'T13CM',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'T13PM',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'TRPF',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'TRPS',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'CHE',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'WHE',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'IDLE',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'STR',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_IS_Bits',0,4,143,2,3
	.word	3854
	.byte	10
	.byte	'_Ifx_CCU6_ISR_Bits',0,4,146,2,16,4,11
	.byte	'RCC60R',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RCC60F',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'RCC61R',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'RCC61F',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'RCC62R',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'RCC62F',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'RT12OM',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'RT12PM',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'RT13CM',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'RT13PM',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'RTRPF',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'RCHE',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'RWHE',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'RIDLE',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'RSTR',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_ISR_Bits',0,4,165,2,3
	.word	4197
	.byte	10
	.byte	'_Ifx_CCU6_ISS_Bits',0,4,168,2,16,4,11
	.byte	'SCC60R',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SCC60F',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SCC61R',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SCC61F',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'SCC62R',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'SCC62F',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'ST12OM',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'ST12PM',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'ST13CM',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'ST13PM',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'STRPF',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'SWHC',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'SCHE',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'SWHE',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'SIDLE',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'SSTR',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_ISS_Bits',0,4,187,2,3
	.word	4558
	.byte	10
	.byte	'_Ifx_CCU6_KRST0_Bits',0,4,190,2,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	264
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_CCU6_KRST0_Bits',0,4,195,2,3
	.word	4912
	.byte	10
	.byte	'_Ifx_CCU6_KRST1_Bits',0,4,198,2,16,4,11
	.byte	'RST',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_CCU6_KRST1_Bits',0,4,202,2,3
	.word	5025
	.byte	10
	.byte	'_Ifx_CCU6_KRSTCLR_Bits',0,4,205,2,16,4,11
	.byte	'CLR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	264
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_CCU6_KRSTCLR_Bits',0,4,209,2,3
	.word	5119
	.byte	10
	.byte	'_Ifx_CCU6_KSCSR_Bits',0,4,212,2,16,4,11
	.byte	'SB0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'SB1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'SB2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'SB3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_CCU6_KSCSR_Bits',0,4,219,2,3
	.word	5217
	.byte	10
	.byte	'_Ifx_CCU6_LI_Bits',0,4,222,2,16,4,11
	.byte	'reserved_0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0EN',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1EN',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2EN',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'CC60INEN',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'CC61INEN',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'CC62INEN',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'CTRAPEN',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'T12HREN',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'T13HREN',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	349
	.byte	3,3,2,35,1,11
	.byte	'LBEEN',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'INPLBE',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_LI_Bits',0,4,238,2,3
	.word	5356
	.byte	10
	.byte	'_Ifx_CCU6_MCFG_Bits',0,4,241,2,16,4,11
	.byte	'T12',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'T13',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'MCM',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	264
	.byte	29,0,2,35,0,0,3
	.byte	'Ifx_CCU6_MCFG_Bits',0,4,247,2,3
	.word	5687
	.byte	10
	.byte	'_Ifx_CCU6_MCMCTR_Bits',0,4,250,2,16,4,11
	.byte	'SWSEL',0,1
	.word	349
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'SWSYN',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'STE12U',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'STE12D',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'STE13U',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	264
	.byte	21,0,2,35,0,0,3
	.byte	'Ifx_CCU6_MCMCTR_Bits',0,4,132,3,3
	.word	5809
	.byte	10
	.byte	'_Ifx_CCU6_MCMOUT_Bits',0,4,135,3,16,4,11
	.byte	'MCMP',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'R',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EXPH',0,1
	.word	349
	.byte	3,5,2,35,1,11
	.byte	'CURH',0,1
	.word	349
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	264
	.byte	18,0,2,35,0,0,3
	.byte	'Ifx_CCU6_MCMOUT_Bits',0,4,143,3,3
	.word	6023
	.byte	10
	.byte	'_Ifx_CCU6_MCMOUTS_Bits',0,4,146,3,16,4,11
	.byte	'MCMPS',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'STRMCM',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'EXPHS',0,1
	.word	349
	.byte	3,5,2,35,1,11
	.byte	'CURHS',0,1
	.word	349
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'STRHP',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_MCMOUTS_Bits',0,4,156,3,3
	.word	6188
	.byte	10
	.byte	'_Ifx_CCU6_MODCTR_Bits',0,4,159,3,16,4,11
	.byte	'T12MODEN',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'MCMEN',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'T13MODEN',0,1
	.word	349
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'ECT13O',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_MODCTR_Bits',0,4,168,3,3
	.word	6403
	.byte	10
	.byte	'_Ifx_CCU6_MOSEL_Bits',0,4,171,3,16,4,11
	.byte	'TRIG0SEL',0,1
	.word	349
	.byte	3,5,2,35,0,11
	.byte	'TRIG1SEL',0,1
	.word	349
	.byte	3,2,2,35,0,11
	.byte	'TRIG2SEL',0,2
	.word	380
	.byte	3,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	264
	.byte	23,0,2,35,0,0,3
	.byte	'Ifx_CCU6_MOSEL_Bits',0,4,177,3,3
	.word	6605
	.byte	10
	.byte	'_Ifx_CCU6_OCS_Bits',0,4,180,3,16,4,11
	.byte	'TGS',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	264
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	349
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	349
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	349
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	349
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_CCU6_OCS_Bits',0,4,190,3,3
	.word	6744
	.byte	10
	.byte	'_Ifx_CCU6_PISEL0_Bits',0,4,193,3,16,4,11
	.byte	'ISCC60',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'ISCC61',0,1
	.word	349
	.byte	2,4,2,35,0,11
	.byte	'ISCC62',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'ISTRP',0,1
	.word	349
	.byte	2,0,2,35,0,11
	.byte	'ISPOS0',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'ISPOS1',0,1
	.word	349
	.byte	2,4,2,35,1,11
	.byte	'ISPOS2',0,1
	.word	349
	.byte	2,2,2,35,1,11
	.byte	'IST12HR',0,1
	.word	349
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_PISEL0_Bits',0,4,204,3,3
	.word	6938
	.byte	10
	.byte	'_Ifx_CCU6_PISEL2_Bits',0,4,207,3,16,4,11
	.byte	'IST13HR',0,1
	.word	349
	.byte	2,6,2,35,0,11
	.byte	'ISCNT12',0,1
	.word	349
	.byte	2,4,2,35,0,11
	.byte	'ISCNT13',0,1
	.word	349
	.byte	2,2,2,35,0,11
	.byte	'T12EXT',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'T13EXT',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	264
	.byte	24,0,2,35,0,0,3
	.byte	'Ifx_CCU6_PISEL2_Bits',0,4,215,3,3
	.word	7164
	.byte	10
	.byte	'_Ifx_CCU6_PSLR_Bits',0,4,218,3,16,4,11
	.byte	'PSL',0,1
	.word	349
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'PSL63',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	264
	.byte	24,0,2,35,0,0,3
	.byte	'Ifx_CCU6_PSLR_Bits',0,4,224,3,3
	.word	7338
	.byte	10
	.byte	'_Ifx_CCU6_T12_Bits',0,4,227,3,16,4,11
	.byte	'T12CV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_T12_Bits',0,4,231,3,3
	.word	7469
	.byte	10
	.byte	'_Ifx_CCU6_T12DTC_Bits',0,4,234,3,16,4,11
	.byte	'DTM',0,1
	.word	349
	.byte	8,0,2,35,0,11
	.byte	'DTE0',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'DTE1',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'DTE2',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'DTR0',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'DTR1',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'DTR2',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	264
	.byte	17,0,2,35,0,0,3
	.byte	'Ifx_CCU6_T12DTC_Bits',0,4,245,3,3
	.word	7562
	.byte	10
	.byte	'_Ifx_CCU6_T12MSEL_Bits',0,4,248,3,16,4,11
	.byte	'MSEL60',0,1
	.word	349
	.byte	4,4,2,35,0,11
	.byte	'MSEL61',0,1
	.word	349
	.byte	4,0,2,35,0,11
	.byte	'MSEL62',0,1
	.word	349
	.byte	4,4,2,35,1,11
	.byte	'HSYNC',0,1
	.word	349
	.byte	3,1,2,35,1,11
	.byte	'DBYP',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_T12MSEL_Bits',0,4,128,4,3
	.word	7778
	.byte	10
	.byte	'_Ifx_CCU6_T12PR_Bits',0,4,131,4,16,4,11
	.byte	'T12PV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_T12PR_Bits',0,4,135,4,3
	.word	7949
	.byte	10
	.byte	'_Ifx_CCU6_T13_Bits',0,4,138,4,16,4,11
	.byte	'T13CV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_T13_Bits',0,4,142,4,3
	.word	8046
	.byte	10
	.byte	'_Ifx_CCU6_T13PR_Bits',0,4,145,4,16,4,11
	.byte	'T13PV',0,2
	.word	380
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_T13PR_Bits',0,4,149,4,3
	.word	8139
	.byte	10
	.byte	'_Ifx_CCU6_TCTR0_Bits',0,4,152,4,16,4,11
	.byte	'T12CLK',0,1
	.word	349
	.byte	3,5,2,35,0,11
	.byte	'T12PRE',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'T12R',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'STE12',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'CDIR',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'CTM',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'T13CLK',0,1
	.word	349
	.byte	3,5,2,35,1,11
	.byte	'T13PRE',0,1
	.word	349
	.byte	1,4,2,35,1,11
	.byte	'T13R',0,1
	.word	349
	.byte	1,3,2,35,1,11
	.byte	'STE13',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	264
	.byte	18,0,2,35,0,0,3
	.byte	'Ifx_CCU6_TCTR0_Bits',0,4,165,4,3
	.word	8236
	.byte	10
	.byte	'_Ifx_CCU6_TCTR2_Bits',0,4,168,4,16,4,11
	.byte	'T12SSC',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'T13SSC',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'T13TEC',0,1
	.word	349
	.byte	3,3,2,35,0,11
	.byte	'T13TED',0,1
	.word	349
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'T12RSEL',0,1
	.word	349
	.byte	2,6,2,35,1,11
	.byte	'T13RSEL',0,1
	.word	349
	.byte	2,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	264
	.byte	20,0,2,35,0,0,3
	.byte	'Ifx_CCU6_TCTR2_Bits',0,4,178,4,3
	.word	8485
	.byte	10
	.byte	'_Ifx_CCU6_TCTR4_Bits',0,4,181,4,16,4,11
	.byte	'T12RR',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'T12RS',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'T12RES',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'DTRES',0,1
	.word	349
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	349
	.byte	1,3,2,35,0,11
	.byte	'T12CNT',0,1
	.word	349
	.byte	1,2,2,35,0,11
	.byte	'T12STR',0,1
	.word	349
	.byte	1,1,2,35,0,11
	.byte	'T12STD',0,1
	.word	349
	.byte	1,0,2,35,0,11
	.byte	'T13RR',0,1
	.word	349
	.byte	1,7,2,35,1,11
	.byte	'T13RS',0,1
	.word	349
	.byte	1,6,2,35,1,11
	.byte	'T13RES',0,1
	.word	349
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	349
	.byte	2,3,2,35,1,11
	.byte	'T13CNT',0,1
	.word	349
	.byte	1,2,2,35,1,11
	.byte	'T13STR',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'T13STD',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_TCTR4_Bits',0,4,199,4,3
	.word	8697
	.byte	10
	.byte	'_Ifx_CCU6_TRPCTR_Bits',0,4,202,4,16,4,11
	.byte	'TRPM0',0,1
	.word	349
	.byte	1,7,2,35,0,11
	.byte	'TRPM1',0,1
	.word	349
	.byte	1,6,2,35,0,11
	.byte	'TRPM2',0,1
	.word	349
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	349
	.byte	5,0,2,35,0,11
	.byte	'TRPEN',0,1
	.word	349
	.byte	6,2,2,35,1,11
	.byte	'TRPEN13',0,1
	.word	349
	.byte	1,1,2,35,1,11
	.byte	'TRPPEN',0,1
	.word	349
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	380
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CCU6_TRPCTR_Bits',0,4,212,4,3
	.word	9051
	.byte	12,4,220,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	677
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_ACCEN0',0,4,225,4,3
	.word	9260
	.byte	12,4,228,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1236
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_ACCEN1',0,4,233,4,3
	.word	9325
	.byte	12,4,236,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1315
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC60R',0,4,241,4,3
	.word	9390
	.byte	12,4,244,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1408
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC60SR',0,4,249,4,3
	.word	9454
	.byte	12,4,252,4,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1503
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC61R',0,4,129,5,3
	.word	9519
	.byte	12,4,132,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1596
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC61SR',0,4,137,5,3
	.word	9583
	.byte	12,4,140,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1691
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC62R',0,4,145,5,3
	.word	9648
	.byte	12,4,148,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1784
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC62SR',0,4,153,5,3
	.word	9712
	.byte	12,4,156,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1879
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC63R',0,4,161,5,3
	.word	9777
	.byte	12,4,164,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1974
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CC63SR',0,4,169,5,3
	.word	9841
	.byte	12,4,172,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2071
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CLC',0,4,177,5,3
	.word	9906
	.byte	12,4,180,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2216
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CMPMODIF',0,4,185,5,3
	.word	9968
	.byte	12,4,188,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2513
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_CMPSTAT',0,4,193,5,3
	.word	10035
	.byte	12,4,196,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2899
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_ID',0,4,201,5,3
	.word	10101
	.byte	12,4,204,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3012
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_IEN',0,4,209,5,3
	.word	10162
	.byte	12,4,212,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3388
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_IMON',0,4,217,5,3
	.word	10224
	.byte	12,4,220,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3649
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_INP',0,4,225,5,3
	.word	10287
	.byte	12,4,228,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3854
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_IS',0,4,233,5,3
	.word	10349
	.byte	12,4,236,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4197
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_ISR',0,4,241,5,3
	.word	10410
	.byte	12,4,244,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4558
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_ISS',0,4,249,5,3
	.word	10472
	.byte	12,4,252,5,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4912
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_KRST0',0,4,129,6,3
	.word	10534
	.byte	12,4,132,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5025
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_KRST1',0,4,137,6,3
	.word	10598
	.byte	12,4,140,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5119
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_KRSTCLR',0,4,145,6,3
	.word	10662
	.byte	12,4,148,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5217
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_KSCSR',0,4,153,6,3
	.word	10728
	.byte	12,4,156,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5356
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_LI',0,4,161,6,3
	.word	10792
	.byte	12,4,164,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5687
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_MCFG',0,4,169,6,3
	.word	10853
	.byte	12,4,172,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5809
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_MCMCTR',0,4,177,6,3
	.word	10916
	.byte	12,4,180,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6023
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_MCMOUT',0,4,185,6,3
	.word	10981
	.byte	12,4,188,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6188
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_MCMOUTS',0,4,193,6,3
	.word	11046
	.byte	12,4,196,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6403
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_MODCTR',0,4,201,6,3
	.word	11112
	.byte	12,4,204,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6605
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_MOSEL',0,4,209,6,3
	.word	11177
	.byte	12,4,212,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6744
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_OCS',0,4,217,6,3
	.word	11241
	.byte	12,4,220,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6938
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_PISEL0',0,4,225,6,3
	.word	11303
	.byte	12,4,228,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7164
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_PISEL2',0,4,233,6,3
	.word	11368
	.byte	12,4,236,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7338
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_PSLR',0,4,241,6,3
	.word	11433
	.byte	12,4,244,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7469
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_T12',0,4,249,6,3
	.word	11496
	.byte	12,4,252,6,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7562
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_T12DTC',0,4,129,7,3
	.word	11558
	.byte	12,4,132,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7778
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_T12MSEL',0,4,137,7,3
	.word	11623
	.byte	12,4,140,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7949
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_T12PR',0,4,145,7,3
	.word	11689
	.byte	12,4,148,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8046
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_T13',0,4,153,7,3
	.word	11753
	.byte	12,4,156,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8139
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_T13PR',0,4,161,7,3
	.word	11815
	.byte	12,4,164,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8236
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_TCTR0',0,4,169,7,3
	.word	11879
	.byte	12,4,172,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8485
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_TCTR2',0,4,177,7,3
	.word	11943
	.byte	12,4,180,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8697
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_TCTR4',0,4,185,7,3
	.word	12007
	.byte	12,4,188,7,9,4,9
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	9051
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CCU6_TRPCTR',0,4,193,7,3
	.word	12071
	.byte	13,16
	.word	611
	.byte	14,1,0
.L8:
	.byte	15
	.word	12136
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38
	.byte	0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCcu6_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxCcu6_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	267
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCcu6_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCcu6_cfg_indexMap',0,1,61,30
	.word	.L8
	.byte	1,5,3
	.word	IfxCcu6_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCcu6_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
