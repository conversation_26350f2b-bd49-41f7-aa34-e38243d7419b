	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc37336a --dep-file=zf_driver_dma.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_dma.src ../libraries/zf_driver/zf_driver_dma.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_dma.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_dma.dma_init',code,cluster('dma_init')
	.sect	'.text.zf_driver_dma.dma_init'
	.align	2
	
	.global	dma_init
; Function dma_init
.L39:
dma_init:	.type	func
	sub.a	a10,#80
.L145:
	mov	d8,d4
.L148:
	mov.aa	a12,a4
.L150:
	mov.aa	a13,a5
.L151:
	mov	d9,d7
.L152:
	mov	d4,d5
.L146:
	mov	d5,d6
.L147:
	call	exti_init
.L80:
	lea	a4,[a10]12
.L187:
	movh.a	a5,#61441
	call	IfxDma_Dma_initModuleConfig
.L83:
	lea	a4,[a10]16
.L188:
	lea	a5,[a10]12
	call	IfxDma_Dma_initModule
.L86:
	lea	a4,[a10]20
.L189:
	lea	a5,[a10]16
	call	IfxDma_Dma_initChannelConfig
.L89:
	mov	d15,#8
	div.u	e0,d9,d15
	eq	d4,d1,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#88
	call	debug_assert_handler
.L190:
	mov	d12,#1
.L153:
	div.u	e10,d9,d12
.L155:
	mov	d0,#16384
.L191:
	jge.u	d0,d10,.L2
.L192:
	j	.L3
.L4:
	div.u	e0,d9,d12
.L193:
	mov	d10,d0
.L194:
	mov	d15,#16384
.L195:
	jlt.u	d15,d10,.L5
.L196:
	div.u	e0,d9,d12
.L197:
	jne	d1,#0,.L6
.L198:
	j	.L7
.L6:
.L5:
	add	d12,#1
.L154:
	extr.u	d12,d12,#0,#8
.L156:
	jlt.u	d12,#11,.L8
.L199:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#105
	call	debug_assert_handler
.L8:
.L3:
	j	.L4
.L7:
.L2:
	jne	d12,#1,.L9
.L200:
	mov	d15,#0
.L201:
	st.b	[a10]62,d15
.L202:
	mov	d15,#0
.L203:
	st.b	[a10]50,d15
.L204:
	mov	d15,#0
.L205:
	st.w	[a10]34,d15
.L206:
	j	.L10
.L9:
	mov	d15,#12
.L207:
	st.b	[a10]62,d15
.L208:
	mov	d15,#1
.L209:
	st.b	[a10]50,d15
.L210:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	lea	a15,[a15]32
	mov.d	d15,a15
	insert	d0,d15,#0,#0,#28
	movh	d15,#53248
	jne	d15,d0,.L11
.L94:
	mfcr	d15,#65052
.L157:
	and	d15,#7
.L158:
	j	.L12
.L12:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	lea	a15,[a15]32
	mov.d	d0,a15
	insert	d0,d0,#0,#20,#12
	insert	d0,d0,#7,#28,#3
	movh	d1,#4096
	mul	d15,d1
	sub	d0,d15
	j	.L13
.L11:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	lea	a15,[a15]32
	mov.d	d0,a15
.L13:
	st.w	[a10]34,d0
.L10:
	mov	d15,#0
.L211:
	st.b	[a10]49,d15
.L212:
	mov	d15,#0
.L213:
	st.b	[a10]51,d15
.L214:
	mov	d15,#2
.L215:
	st.b	[a10]54,d15
.L216:
	mov.d	d15,a12
.L159:
	insert	d0,d15,#0,#0,#28
	movh	d15,#53248
.L160:
	jne	d15,d0,.L14
.L98:
	mfcr	d15,#65052
.L161:
	and	d15,#7
.L162:
	j	.L15
.L15:
	mov.d	d0,a12
.L163:
	insert	d0,d0,#0,#20,#12
.L164:
	insert	d0,d0,#7,#28,#3
	movh	d1,#4096
	mul	d15,d1
	sub	d0,d15
	j	.L16
.L14:
	mov.d	d0,a12
.L16:
	st.w	[a10]26,d0
.L217:
	mov	d15,#0
.L218:
	st.b	[a10]58,d15
.L219:
	mov	d15,#1
.L220:
	st.b	[a10]63,d15
.L221:
	mov	d15,#0
.L222:
	st.b	[a10]59,d15
.L223:
	st.b	[a10]24,d8
.L224:
	mov	d15,#0
.L225:
	st.b	[a10]55,d15
.L226:
	mov	d15,#1
.L227:
	st.b	[a10]68,d15
.L228:
	mov	d15,#60
.L229:
	st.h	[a10]72,d15
.L230:
	mov	d15,#0
.L231:
	st.b	[a10]74,d15
.L232:
	mov.d	d15,a13
.L165:
	insert	d0,d15,#0,#0,#28
	movh	d15,#53248
.L166:
	jne	d15,d0,.L17
.L100:
	mfcr	d15,#65052
.L167:
	and	d15,#7
.L168:
	j	.L18
.L18:
	mov.d	d0,a13
.L169:
	insert	d0,d0,#0,#20,#12
.L170:
	insert	d0,d0,#7,#28,#3
	movh	d1,#4096
	mul	d15,d1
	sub	d0,d15
	j	.L19
.L17:
	mov.d	d0,a13
.L19:
	st.w	[a10]30,d0
.L233:
	st.h	[a10]46,d10
.L234:
	lea	a4,[a10]0
.L235:
	lea	a5,[a10]20
	call	IfxDma_Dma_initChannel
.L236:
	jlt.u	d12,#2,.L20
.L237:
	mov	d8,#0
.L149:
	j	.L21
.L22:
	mul	d15,d10,d8
	addsc.a	a15,a13,d15,#0
	mov.d	d0,a15
	insert	d0,d0,#0,#0,#28
	movh	d15,#53248
	jne	d15,d0,.L23
.L102:
	mfcr	d15,#65052
.L172:
	and	d0,d15,#7
.L238:
	j	.L24
.L24:
	mul	d15,d10,d8
.L173:
	addsc.a	a15,a13,d15,#0
	mov.d	d15,a15
	insert	d15,d15,#0,#20,#12
	insert	d15,d15,#7,#28,#3
	movh	d1,#4096
	mul	d0,d1
	sub	d15,d0
	j	.L25
.L23:
	mul	d15,d10,d8
	addsc.a	a15,a13,d15,#0
	mov.d	d15,a15
.L25:
	st.w	[a10]30,d15
.L239:
	add	d15,d12,#-1
.L240:
	jne	d15,d8,.L26
.L241:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	mov.d	d15,a15
	insert	d0,d15,#0,#0,#28
	movh	d15,#53248
	jne	d15,d0,.L27
.L104:
	mfcr	d15,#65052
.L174:
	and	d15,#7
.L175:
	j	.L28
.L28:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	mov.d	d0,a15
	insert	d0,d0,#0,#20,#12
	insert	d0,d0,#7,#28,#3
	movh	d1,#4096
	mul	d15,d1
	sub	d0,d15
	j	.L29
.L27:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	mov.d	d0,a15
.L29:
	st.w	[a10]34,d0
.L242:
	j	.L30
.L26:
	add	d15,d8,#1
	mul	d15,d15,#32
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	addsc.a	a15,a15,d15,#0
	mov.d	d15,a15
	insert	d0,d15,#0,#0,#28
	movh	d15,#53248
	jne	d15,d0,.L31
.L106:
	mfcr	d15,#65052
.L176:
	and	d0,d15,#7
.L243:
	j	.L32
.L32:
	add	d15,d8,#1
.L177:
	mul	d15,d15,#32
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	addsc.a	a15,a15,d15,#0
	mov.d	d15,a15
	insert	d15,d15,#0,#20,#12
	insert	d15,d15,#7,#28,#3
	movh	d1,#4096
	mul	d0,d1
	sub	d15,d0
	j	.L33
.L31:
	add	d15,d8,#1
	mul	d15,d15,#32
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
	addsc.a	a15,a15,d15,#0
	mov.d	d15,a15
.L33:
	st.w	[a10]34,d15
.L30:
	st.h	[a10]46,d10
.L244:
	mul	d15,d8,#32
.L245:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
.L246:
	addsc.a	a4,a15,d15,#0
.L247:
	lea	a5,[a10]20
	call	IfxDma_Dma_initLinkedListEntry
.L248:
	add	d8,#1
.L171:
	extr.u	d8,d8,#0,#8
.L21:
	jlt.u	d8,d12,.L22
.L20:
	movh.a	a15,#@his(dma_link_list)
	lea	a15,[a15]@los(dma_link_list)
.L249:
	lea	a15,[a15]320
.L109:
	ld.a	a2,[a15]
.L250:
	ld.b	d15,[a15]4
.L118:
	jz.a	a2,.L34
.L34:
	mul	d15,d15,#4
	mov.a	a15,d15
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a15,[a3]-31488
.L251:
	j	.L35
.L35:
	j	.L36
.L36:
	add.a	a15,#3
.L252:
	ld.bu	d15,[a15]
.L110:
	or	d15,#2
.L115:
	st.b	[a15],d15
.L116:
	mov	d2,d12
.L178:
	j	.L37
.L37:
	ret
.L66:
	
__dma_init_function_end:
	.size	dma_init,__dma_init_function_end-dma_init
.L52:
	; End of function
	
	.sdecl	'.text.zf_driver_dma.dma_disable',code,cluster('dma_disable')
	.sect	'.text.zf_driver_dma.dma_disable'
	.align	2
	
	.global	dma_disable
; Function dma_disable
.L41:
dma_disable:	.type	func
	movh.a	a15,#61441
.L127:
	mul	d15,d4,#4
	addsc.a	a2,a15,d15,#0
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L257:
	ld.bu	d15,[a15]7682
.L258:
	or	d15,#2
	st.b	[a2]7682,d15
.L128:
	ret
.L124:
	
__dma_disable_function_end:
	.size	dma_disable,__dma_disable_function_end-dma_disable
.L57:
	; End of function
	
	.sdecl	'.text.zf_driver_dma.dma_enable',code,cluster('dma_enable')
	.sect	'.text.zf_driver_dma.dma_enable'
	.align	2
	
	.global	dma_enable
; Function dma_enable
.L43:
dma_enable:	.type	func
	movh.a	a15,#61441
.L137:
	mul	d15,d4,#4
	addsc.a	a2,a15,d15,#0
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L263:
	ld.bu	d15,[a15]7682
.L264:
	or	d15,#1
	st.b	[a2]7682,d15
.L138:
	ret
.L134:
	
__dma_enable_function_end:
	.size	dma_enable,__dma_enable_function_end-dma_enable
.L62:
	; End of function
	
	.sdecl	'.bss.cpu0_dsram',data,cluster('dma_link_list')
	.sect	'.bss.cpu0_dsram'
	.global	dma_link_list
	.align	256
dma_link_list:	.type	object
	.size	dma_link_list,332
	.space	332
	.sdecl	'.rodata.zf_driver_dma..1.str',data,rom
	.sect	'.rodata.zf_driver_dma..1.str'
.1.str:	.type	object
	.size	.1.str,39
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,100,109,97
	.byte	46,99
	.space	1
	.calls	'dma_init','exti_init'
	.calls	'dma_init','IfxDma_Dma_initModuleConfig'
	.calls	'dma_init','IfxDma_Dma_initModule'
	.calls	'dma_init','IfxDma_Dma_initChannelConfig'
	.calls	'dma_init','debug_assert_handler'
	.calls	'dma_init','IfxDma_Dma_initChannel'
	.calls	'dma_init','IfxDma_Dma_initLinkedListEntry'
	.calls	'dma_init','',80
	.calls	'dma_disable','',0
	.extern	IfxDma_Dma_initModule
	.extern	IfxDma_Dma_initModuleConfig
	.extern	IfxDma_Dma_initChannel
	.extern	IfxDma_Dma_initChannelConfig
	.extern	IfxDma_Dma_initLinkedListEntry
	.extern	debug_assert_handler
	.extern	exti_init
	.calls	'dma_enable','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L45:
	.word	99650
	.half	3
	.word	.L46
	.byte	4
.L44:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L47
	.byte	2,1,1,3
	.word	201
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	204
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	249
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	261
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	373
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	347
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	379
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	379
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	347
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L65:
	.byte	7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	505
	.byte	4,2,35,0,0,14
	.word	795
	.byte	3
	.word	834
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	839
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	887
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	887
	.byte	16,0,2,35,0,0,12,6,247,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	903
	.byte	4,2,35,0,0,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,6,255,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1061
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,11
	.byte	'AE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,6,135,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1305
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,13
	.byte	'CON0',0
	.word	999
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1265
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1496
	.byte	4,2,35,8,0,14
	.word	1536
	.byte	3
	.word	1599
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,5,181,3,17,1,1,5
	.byte	'watchdog',0,5,181,3,65
	.word	1604
	.byte	5
	.byte	'password',0,5,181,3,82
	.word	1039
	.byte	6,0,4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,5,204,3,17,1,1,5
	.byte	'password',0,5,204,3,59
	.word	1039
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,5,140,4,17,1,1,5
	.byte	'watchdog',0,5,140,4,63
	.word	1604
	.byte	5
	.byte	'password',0,5,140,4,80
	.word	1039
	.byte	6,0,4
	.byte	'IfxScuWdt_setSafetyEndinitInline',0,3,5,163,4,17,1,1,5
	.byte	'password',0,5,163,4,57
	.word	1039
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,5,227,3,19
	.word	1039
	.byte	1,1,5
	.byte	'watchdog',0,5,227,3,74
	.word	1604
	.byte	6,0,8
	.byte	'IfxScuWdt_getSafetyWatchdogPasswordInline',0,3,5,253,3,19
	.word	1039
	.byte	1,1,6,0,15,8,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0
.L93:
	.byte	8
	.byte	'IfxCpu_getCoreId',0,3,7,133,6,22
	.word	2014
	.byte	1,1
.L95:
	.byte	6,0,15,8,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	2096
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	488
	.byte	1,1,6,0
.L76:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	2251
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	1039
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	488
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	1039
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	2251
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	2251
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,10,143,3,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,181,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2482
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,10,169,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,133,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2798
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,10,110,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,148,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3369
	.byte	4,2,35,0,0,18,4
	.word	488
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,10,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,164,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3497
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,10,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,180,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3712
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,10,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,188,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,10,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,10,172,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4144
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,10,118,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,156,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4364
	.byte	4,2,35,0,0,18,24
	.word	488
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,10,205,3,16,4,11
	.byte	'PD0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,205,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4687
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,10,226,3,16,4,11
	.byte	'PD8',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,213,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4991
	.byte	4,2,35,0,0,18,8
	.word	488
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,10,88,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,140,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5316
	.byte	4,2,35,0,0,18,12
	.word	488
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,10,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,197,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5656
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,10,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,189,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,10,206,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,10,149,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6308
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,10,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,10,165,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6455
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,10,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	20,0,2,35,0,0,12,10,173,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6624
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,10,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,157,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6796
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,10,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	12,0,2,35,2,0,12,10,229,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6971
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,10,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,10,245,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7145
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,10,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,10,253,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7319
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,10,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,237,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,10,249,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,141,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7651
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,10,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,221,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7984
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,10,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,10,196,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8332
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,10,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,10,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,10,204,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8456
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8540
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,10,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,10,213,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8720
	.byte	4,2,35,0,0,18,76
	.word	488
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,10,132,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8973
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,10,252,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9060
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,10,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2758
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	3329
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3448
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3488
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3672
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3887
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	4104
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	4324
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3488
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4638
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4678
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4951
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	5267
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	5307
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5607
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5647
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5982
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	6268
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	5307
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6415
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6584
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6756
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6931
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	7105
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	7279
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7455
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7611
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7944
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	8292
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5307
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8416
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8665
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8924
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8964
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	9020
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9587
	.byte	4,3,35,252,1,0,14
	.word	9627
	.byte	3
	.word	10230
	.byte	15,9,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,9,196,4,17,1,1,5
	.byte	'port',0,9,196,4,48
	.word	10235
	.byte	5
	.byte	'pinIndex',0,9,196,4,60
	.word	488
	.byte	5
	.byte	'mode',0,9,196,4,88
	.word	10240
	.byte	6,0,15,9,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,9,208,4,17,1,1,5
	.byte	'port',0,9,208,4,44
	.word	10235
	.byte	5
	.byte	'pinIndex',0,9,208,4,56
	.word	488
	.byte	5
	.byte	'action',0,9,208,4,80
	.word	10445
	.byte	6,0,10
	.byte	'_Ifx_DMA_CLC_Bits',0,12,131,4,16,4,11
	.byte	'DISR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,12,160,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10626
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ID_Bits',0,12,155,4,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,184,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10783
	.byte	4,2,35,0,0,18,20
	.word	488
	.byte	19,19,0,10
	.byte	'_Ifx_DMA_MEMCON_Bits',0,12,163,4,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	2,30,2,35,0,11
	.byte	'INTERR',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'RMWERR',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'DATAERR',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'PMIC',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'ERRDIS',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	22,0,2,35,0,0,12,12,192,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10914
	.byte	4,2,35,0,0,18,28
	.word	488
	.byte	19,27,0,10
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,12,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,128,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11191
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,12,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,136,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11761
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,12,88,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,144,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11851
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,12,125,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,152,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12421
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,12,131,1,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,160,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,12,168,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,168,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13082
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,12,174,1,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,176,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13173
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,12,211,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,184,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13744
	.byte	4,2,35,0,0,18,192,1
	.word	488
	.byte	19,191,1,0,10
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,12,233,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'ESER',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EDER',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	6,0,2,35,2,11
	.byte	'ERER',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'ELER',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,12,200,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,12,246,1,16,4,11
	.byte	'LEC',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1039
	.byte	9,0,2,35,0,11
	.byte	'SER',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'DER',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	2,4,2,35,2,11
	.byte	'SPBER',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'SRIER',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'RAMER',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SLLER',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'DLLER',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,12,208,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14070
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,12,217,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'CSER',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'CDER',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	2,4,2,35,2,11
	.byte	'CSPBER',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'CSRIER',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'CRAMER',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CSLLER',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'CDLLER',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,12,192,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14362
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,12,161,3,16,4,11
	.byte	'RS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	3,4,2,35,0,11
	.byte	'WS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1039
	.byte	11,0,2,35,0,11
	.byte	'CH',0,1
	.word	488
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	9,0,2,35,2,0,12,12,216,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14645
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,12,193,2,16,4,11
	.byte	'RD00',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD01',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD02',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD03',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,248,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14825
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,12,202,2,16,4,11
	.byte	'RD10',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD11',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD12',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD13',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,128,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14960
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,12,211,2,16,4,11
	.byte	'RD20',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD21',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD22',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD23',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,136,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15095
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,12,220,2,16,4,11
	.byte	'RD30',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD31',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD32',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD33',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,144,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15230
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,12,229,2,16,4,11
	.byte	'RD40',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD41',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD42',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD43',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,152,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15365
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,12,238,2,16,4,11
	.byte	'RD50',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD51',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD52',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD53',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,160,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,12,247,2,16,4,11
	.byte	'RD60',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD61',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD62',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD63',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,168,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15635
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,12,128,3,16,4,11
	.byte	'RD70',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'RD71',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'RD72',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'RD73',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,176,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15770
	.byte	4,2,35,0,0,18,32
	.word	488
	.byte	19,31,0,10
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,12,137,3,16,4,11
	.byte	'RDCRC',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,184,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15914
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,12,149,3,16,4,11
	.byte	'SDCRC',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,200,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16005
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,12,143,3,16,4,11
	.byte	'SADR',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,192,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16096
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,12,187,2,16,4,11
	.byte	'DADR',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,240,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16185
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,12,135,2,16,4,11
	.byte	'SMF',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,216,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16274
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,12,155,2,16,4,11
	.byte	'TREL',0,2
	.word	1039
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	488
	.byte	2,0,2,35,3,0,12,12,224,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,12,155,3,16,4,11
	.byte	'SHADR',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,208,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16869
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,12,171,2,16,4,11
	.byte	'TCOUNT',0,2
	.word	1039
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,232,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16960
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME',0,12,144,8,25,112,13
	.byte	'SR',0
	.word	14785
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5647
	.byte	12,2,35,4,13
	.byte	'R0',0
	.word	14920
	.byte	4,2,35,16,13
	.byte	'R1',0
	.word	15055
	.byte	4,2,35,20,13
	.byte	'R2',0
	.word	15190
	.byte	4,2,35,24,13
	.byte	'R3',0
	.word	15325
	.byte	4,2,35,28,13
	.byte	'R4',0
	.word	15460
	.byte	4,2,35,32,13
	.byte	'R5',0
	.word	15595
	.byte	4,2,35,36,13
	.byte	'R6',0
	.word	15730
	.byte	4,2,35,40,13
	.byte	'R7',0
	.word	15865
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	15905
	.byte	32,2,35,48,13
	.byte	'RDCRC',0
	.word	15965
	.byte	4,2,35,80,13
	.byte	'SDCRC',0
	.word	16056
	.byte	4,2,35,84,13
	.byte	'SADR',0
	.word	16145
	.byte	4,2,35,88,13
	.byte	'DADR',0
	.word	16234
	.byte	4,2,35,92,13
	.byte	'ADICR',0
	.word	16550
	.byte	4,2,35,96,13
	.byte	'CHCR',0
	.word	16829
	.byte	4,2,35,100,13
	.byte	'SHADR',0
	.word	16920
	.byte	4,2,35,104,13
	.byte	'CHSR',0
	.word	17193
	.byte	4,2,35,108,0,14
	.word	17233
	.byte	10
	.byte	'_Ifx_DMA_BLK',0,12,178,8,25,128,1,13
	.byte	'EER',0
	.word	14030
	.byte	4,2,35,0,13
	.byte	'ERRSR',0
	.word	14322
	.byte	4,2,35,4,13
	.byte	'CLRE',0
	.word	14605
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3488
	.byte	4,2,35,12,13
	.byte	'ME',0
	.word	17521
	.byte	112,2,35,16,0,14
	.word	17526
	.byte	18,128,31
	.word	488
	.byte	19,255,30,0,14
	.word	17526
	.byte	18,96
	.word	488
	.byte	19,95,0,10
	.byte	'_Ifx_DMA_OTSS_Bits',0,12,185,4,16,4,11
	.byte	'TGS',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'BS',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,12,208,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17651
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,12,141,4,16,4,11
	.byte	'SIT',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,12,168,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17790
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_PRR0_Bits',0,12,194,4,16,4,11
	.byte	'PAT00',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'PAT01',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'PAT02',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'PAT03',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,216,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17896
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_PRR1_Bits',0,12,203,4,16,4,11
	.byte	'PAT10',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'PAT11',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'PAT12',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'PAT13',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,224,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18030
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_TIME_Bits',0,12,226,4,16,4,11
	.byte	'COUNT',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,248,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18164
	.byte	4,2,35,0,0,18,236,1
	.word	488
	.byte	19,235,1,0,10
	.byte	'_Ifx_DMA_MODE_Bits',0,12,178,4,16,4,11
	.byte	'MODE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,12,200,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18258
	.byte	4,2,35,0,0,18,16
	.word	18322
	.byte	19,3,0,18,240,9
	.word	488
	.byte	19,239,9,0,10
	.byte	'_Ifx_DMA_HRR_Bits',0,12,148,4,16,4,11
	.byte	'HRP',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,12,176,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18382
	.byte	4,2,35,0,0,18,192,1
	.word	18444
	.byte	19,47,0,18,192,2
	.word	488
	.byte	19,191,2,0,10
	.byte	'_Ifx_DMA_SUSENR_Bits',0,12,219,4,16,4,11
	.byte	'SUSEN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,12,240,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18505
	.byte	4,2,35,0,0,18,192,1
	.word	18572
	.byte	19,47,0,10
	.byte	'_Ifx_DMA_SUSACR_Bits',0,12,212,4,16,4,11
	.byte	'SUSAC',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,12,232,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18622
	.byte	4,2,35,0,0,18,192,1
	.word	18689
	.byte	19,47,0,10
	.byte	'_Ifx_DMA_TSR_Bits',0,12,232,4,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'HTRE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TRL',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CH',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'HLTREQ',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'HLTACK',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	6,0,2,35,1,11
	.byte	'ECH',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'DCH',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'CTL',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'HLTCLR',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	488
	.byte	7,0,2,35,3,0,12,12,128,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18739
	.byte	4,2,35,0,0,18,192,1
	.word	19014
	.byte	19,47,0,10
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,12,235,3,16,4,11
	.byte	'RDCRC',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,128,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19064
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,12,247,3,16,4,11
	.byte	'SDCRC',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,144,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19152
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,12,241,3,16,4,11
	.byte	'SADR',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,136,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19240
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,12,229,3,16,4,11
	.byte	'DADR',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,248,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19325
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,12,172,3,16,4,11
	.byte	'SMF',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,224,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19410
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,12,192,3,16,4,11
	.byte	'TREL',0,2
	.word	1039
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	488
	.byte	2,0,2,35,3,0,12,12,232,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19722
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,12,253,3,16,4,11
	.byte	'SHADR',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,152,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19999
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,12,208,3,16,4,11
	.byte	'TCOUNT',0,2
	.word	1039
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'SWB',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CWRP',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'CICH',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'SIT',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'SCH',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,240,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20086
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH',0,12,188,8,25,32,13
	.byte	'RDCRCR',0
	.word	19112
	.byte	4,2,35,0,13
	.byte	'SDCRCR',0
	.word	19200
	.byte	4,2,35,4,13
	.byte	'SADR',0
	.word	19285
	.byte	4,2,35,8,13
	.byte	'DADR',0
	.word	19370
	.byte	4,2,35,12,13
	.byte	'ADICR',0
	.word	19682
	.byte	4,2,35,16,13
	.byte	'CHCFGR',0
	.word	19959
	.byte	4,2,35,20,13
	.byte	'SHADR',0
	.word	20046
	.byte	4,2,35,24,13
	.byte	'CHCSR',0
	.word	20393
	.byte	4,2,35,28,0,18,128,12
	.word	20433
	.byte	19,47,0,14
	.word	20573
	.byte	18,128,52
	.word	488
	.byte	19,255,51,0,10
	.byte	'_Ifx_DMA',0,12,211,8,25,128,128,1,13
	.byte	'CLC',0
	.word	10743
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3488
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	10865
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	10905
	.byte	20,2,35,12,13
	.byte	'MEMCON',0
	.word	11142
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	11182
	.byte	28,2,35,36,13
	.byte	'ACCEN00',0
	.word	11721
	.byte	4,2,35,64,13
	.byte	'ACCEN01',0
	.word	11811
	.byte	4,2,35,68,13
	.byte	'ACCEN10',0
	.word	12381
	.byte	4,2,35,72,13
	.byte	'ACCEN11',0
	.word	12471
	.byte	4,2,35,76,13
	.byte	'ACCEN20',0
	.word	13042
	.byte	4,2,35,80,13
	.byte	'ACCEN21',0
	.word	13133
	.byte	4,2,35,84,13
	.byte	'ACCEN30',0
	.word	13704
	.byte	4,2,35,88,13
	.byte	'ACCEN31',0
	.word	13795
	.byte	4,2,35,92,13
	.byte	'reserved_60',0
	.word	13835
	.byte	192,1,2,35,96,13
	.byte	'BLK0',0
	.word	17621
	.byte	128,1,3,35,160,2,13
	.byte	'reserved_1A0',0
	.word	17626
	.byte	128,31,3,35,160,3,13
	.byte	'BLK1',0
	.word	17637
	.byte	128,1,3,35,160,34,13
	.byte	'reserved_11A0',0
	.word	17642
	.byte	96,3,35,160,35,13
	.byte	'OTSS',0
	.word	17750
	.byte	4,3,35,128,36,13
	.byte	'ERRINTR',0
	.word	17856
	.byte	4,3,35,132,36,13
	.byte	'PRR0',0
	.word	17990
	.byte	4,3,35,136,36,13
	.byte	'PRR1',0
	.word	18124
	.byte	4,3,35,140,36,13
	.byte	'TIME',0
	.word	18207
	.byte	4,3,35,144,36,13
	.byte	'reserved_1214',0
	.word	18247
	.byte	236,1,3,35,148,36,13
	.byte	'MODE',0
	.word	18362
	.byte	16,3,35,128,38,13
	.byte	'reserved_1310',0
	.word	18371
	.byte	240,9,3,35,144,38,13
	.byte	'HRR',0
	.word	18484
	.byte	192,1,3,35,128,48,13
	.byte	'reserved_18C0',0
	.word	18494
	.byte	192,2,3,35,192,49,13
	.byte	'SUSENR',0
	.word	18612
	.byte	192,1,3,35,128,52,13
	.byte	'reserved_1AC0',0
	.word	18494
	.byte	192,2,3,35,192,53,13
	.byte	'SUSACR',0
	.word	18729
	.byte	192,1,3,35,128,56,13
	.byte	'reserved_1CC0',0
	.word	18494
	.byte	192,2,3,35,192,57,13
	.byte	'TSR',0
	.word	19054
	.byte	192,1,3,35,128,60,13
	.byte	'reserved_1EC0',0
	.word	18494
	.byte	192,2,3,35,192,61,13
	.byte	'CH',0
	.word	20583
	.byte	128,12,3,35,128,64,13
	.byte	'reserved_2600',0
	.word	20588
	.byte	128,52,3,35,128,76,0,14
	.word	20599
	.byte	3
	.word	21305
.L67:
	.byte	15,13,105,9,1,16
	.byte	'IfxDma_ChannelId_none',0,127,16
	.byte	'IfxDma_ChannelId_0',0,0,16
	.byte	'IfxDma_ChannelId_1',0,1,16
	.byte	'IfxDma_ChannelId_2',0,2,16
	.byte	'IfxDma_ChannelId_3',0,3,16
	.byte	'IfxDma_ChannelId_4',0,4,16
	.byte	'IfxDma_ChannelId_5',0,5,16
	.byte	'IfxDma_ChannelId_6',0,6,16
	.byte	'IfxDma_ChannelId_7',0,7,16
	.byte	'IfxDma_ChannelId_8',0,8,16
	.byte	'IfxDma_ChannelId_9',0,9,16
	.byte	'IfxDma_ChannelId_10',0,10,16
	.byte	'IfxDma_ChannelId_11',0,11,16
	.byte	'IfxDma_ChannelId_12',0,12,16
	.byte	'IfxDma_ChannelId_13',0,13,16
	.byte	'IfxDma_ChannelId_14',0,14,16
	.byte	'IfxDma_ChannelId_15',0,15,16
	.byte	'IfxDma_ChannelId_16',0,16,16
	.byte	'IfxDma_ChannelId_17',0,17,16
	.byte	'IfxDma_ChannelId_18',0,18,16
	.byte	'IfxDma_ChannelId_19',0,19,16
	.byte	'IfxDma_ChannelId_20',0,20,16
	.byte	'IfxDma_ChannelId_21',0,21,16
	.byte	'IfxDma_ChannelId_22',0,22,16
	.byte	'IfxDma_ChannelId_23',0,23,16
	.byte	'IfxDma_ChannelId_24',0,24,16
	.byte	'IfxDma_ChannelId_25',0,25,16
	.byte	'IfxDma_ChannelId_26',0,26,16
	.byte	'IfxDma_ChannelId_27',0,27,16
	.byte	'IfxDma_ChannelId_28',0,28,16
	.byte	'IfxDma_ChannelId_29',0,29,16
	.byte	'IfxDma_ChannelId_30',0,30,16
	.byte	'IfxDma_ChannelId_31',0,31,16
	.byte	'IfxDma_ChannelId_32',0,32,16
	.byte	'IfxDma_ChannelId_33',0,33,16
	.byte	'IfxDma_ChannelId_34',0,34,16
	.byte	'IfxDma_ChannelId_35',0,35,16
	.byte	'IfxDma_ChannelId_36',0,36,16
	.byte	'IfxDma_ChannelId_37',0,37,16
	.byte	'IfxDma_ChannelId_38',0,38,16
	.byte	'IfxDma_ChannelId_39',0,39,16
	.byte	'IfxDma_ChannelId_40',0,40,16
	.byte	'IfxDma_ChannelId_41',0,41,16
	.byte	'IfxDma_ChannelId_42',0,42,16
	.byte	'IfxDma_ChannelId_43',0,43,16
	.byte	'IfxDma_ChannelId_44',0,44,16
	.byte	'IfxDma_ChannelId_45',0,45,16
	.byte	'IfxDma_ChannelId_46',0,46,16
	.byte	'IfxDma_ChannelId_47',0,47,0
.L126:
	.byte	4
	.byte	'IfxDma_disableChannelTransaction',0,3,11,144,10,17,1,1
.L129:
	.byte	5
	.byte	'dma',0,11,144,10,59
	.word	21310
.L131:
	.byte	5
	.byte	'channelId',0,11,144,10,81
	.word	21315
.L133:
	.byte	6,0
.L136:
	.byte	4
	.byte	'IfxDma_enableChannelTransaction',0,3,11,188,10,17,1,1
.L139:
	.byte	5
	.byte	'dma',0,11,188,10,58
	.word	21310
.L141:
	.byte	5
	.byte	'channelId',0,11,188,10,80
	.word	21315
.L143:
	.byte	6,0,8
	.byte	'IfxDma_isChannelTransactionPending',0,3,11,148,12,20
	.word	488
	.byte	1,1,5
	.byte	'dma',0,11,148,12,64
	.word	21310
	.byte	5
	.byte	'channelId',0,11,148,12,86
	.word	21315
	.byte	6,0,4
	.byte	'IfxDma_startChannelTransaction',0,3,11,147,13,17,1,1,5
	.byte	'dma',0,11,147,13,57
	.word	21310
	.byte	5
	.byte	'channelId',0,11,147,13,79
	.word	21315
	.byte	6,0,4
	.byte	'IfxDma_setChannelDestinationAddress',0,3,11,178,12,17,1,1,5
	.byte	'dma',0,11,178,12,62
	.word	21310
	.byte	5
	.byte	'channelId',0,11,178,12,84
	.word	21315
	.byte	5
	.byte	'address',0,11,178,12,101
	.word	379
	.byte	6,0,20
	.word	373
	.byte	3
	.word	22789
	.byte	4
	.byte	'IfxDma_setChannelSourceAddress',0,3,11,237,12,17,1,1,5
	.byte	'dma',0,11,237,12,57
	.word	21310
	.byte	5
	.byte	'channelId',0,11,237,12,79
	.word	21315
	.byte	5
	.byte	'address',0,11,237,12,102
	.word	22794
	.byte	6,0,4
	.byte	'IfxDma_setChannelTransferCount',0,3,11,132,13,17,1,1,5
	.byte	'dma',0,11,132,13,57
	.word	21310
	.byte	5
	.byte	'channelId',0,11,132,13,79
	.word	21315
	.byte	5
	.byte	'transferCount',0,11,132,13,97
	.word	2251
	.byte	6,0,4
	.byte	'IfxDma_clearChannelInterrupt',0,3,11,241,9,17,1,1,5
	.byte	'dma',0,11,241,9,55
	.word	21310
	.byte	5
	.byte	'channelId',0,11,241,9,77
	.word	21315
	.byte	6,0,8
	.byte	'IfxDma_getAndClearChannelInterrupt',0,3,11,226,10,20
	.word	488
	.byte	1,1,5
	.byte	'dma',0,11,226,10,64
	.word	21310
	.byte	5
	.byte	'channelId',0,11,226,10,86
	.word	21315
	.byte	6,0,8
	.byte	'IfxDma_getChannelInterrupt',0,3,11,170,11,20
	.word	488
	.byte	1,1,5
	.byte	'dma',0,11,170,11,56
	.word	21310
	.byte	5
	.byte	'channelId',0,11,170,11,78
	.word	21315
	.byte	6,0
.L117:
	.byte	8
	.byte	'IfxDma_getSrcPointer',0,3,11,250,11,35
	.word	839
	.byte	1,1
.L119:
	.byte	5
	.byte	'dma',0,11,250,11,65
	.word	21310
.L121:
	.byte	5
	.byte	'channelId',0,11,250,11,87
	.word	21315
.L123:
	.byte	6,0,14
	.word	20433
	.byte	3
	.word	23277
.L78:
	.byte	21,14,218,3,9,12,13
	.byte	'dma',0
	.word	21310
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	21315
	.byte	1,2,35,4,13
	.byte	'channel',0
	.word	23282
	.byte	4,2,35,8,0,3
	.word	23287
.L108:
	.byte	8
	.byte	'IfxDma_Dma_getSrcPointer',0,3,14,226,5,35
	.word	839
	.byte	1,1
.L111:
	.byte	5
	.byte	'channel',0,14,226,5,80
	.word	23343
.L113:
	.byte	17,6,0,0,14
	.word	481
	.byte	22
	.byte	'__mfcr',0
	.word	23406
	.byte	1,1,1,1,23
	.word	481
	.byte	0,24
	.word	209
	.byte	25
	.word	235
	.byte	6,0,24
	.word	270
	.byte	25
	.word	302
	.byte	6,0,24
	.word	315
	.byte	6,0,24
	.word	384
	.byte	25
	.word	403
	.byte	6,0,24
	.word	419
	.byte	25
	.word	434
	.byte	25
	.word	448
	.byte	6,0,24
	.word	844
	.byte	25
	.word	872
	.byte	6,0,24
	.word	1609
	.byte	25
	.word	1649
	.byte	25
	.word	1667
	.byte	6,0,24
	.word	1687
	.byte	25
	.word	1730
	.byte	6,0,24
	.word	1750
	.byte	25
	.word	1788
	.byte	25
	.word	1806
	.byte	6,0,24
	.word	1826
	.byte	25
	.word	1867
	.byte	6,0,24
	.word	1887
	.byte	25
	.word	1938
	.byte	6,0,24
	.word	1958
	.byte	6,0,24
	.word	2065
	.byte	6,0,24
	.word	2175
	.byte	6,0,24
	.word	2209
	.byte	6,0,24
	.word	2272
	.byte	25
	.word	2313
	.byte	6,0,24
	.word	2332
	.byte	25
	.word	2387
	.byte	6,0,24
	.word	2406
	.byte	25
	.word	2446
	.byte	25
	.word	2463
	.byte	17,6,0,0,24
	.word	10365
	.byte	25
	.word	10397
	.byte	25
	.word	10411
	.byte	25
	.word	10429
	.byte	6,0,24
	.word	10548
	.byte	25
	.word	10576
	.byte	25
	.word	10590
	.byte	25
	.word	10608
	.byte	6,0,24
	.word	22391
	.byte	25
	.word	22432
	.byte	25
	.word	22445
	.byte	6,0,24
	.word	22466
	.byte	25
	.word	22506
	.byte	25
	.word	22519
	.byte	6,0,24
	.word	22540
	.byte	25
	.word	22587
	.byte	25
	.word	22600
	.byte	6,0,24
	.word	22621
	.byte	25
	.word	22660
	.byte	25
	.word	22673
	.byte	6,0,24
	.word	22694
	.byte	25
	.word	22738
	.byte	25
	.word	22751
	.byte	25
	.word	22770
	.byte	6,0,24
	.word	22799
	.byte	25
	.word	22838
	.byte	25
	.word	22851
	.byte	25
	.word	22870
	.byte	6,0,24
	.word	22889
	.byte	25
	.word	22928
	.byte	25
	.word	22941
	.byte	25
	.word	22960
	.byte	6,0,24
	.word	22985
	.byte	25
	.word	23022
	.byte	25
	.word	23035
	.byte	6,0,24
	.word	23056
	.byte	25
	.word	23103
	.byte	25
	.word	23116
	.byte	6,0,24
	.word	23137
	.byte	25
	.word	23176
	.byte	25
	.word	23189
	.byte	6,0,24
	.word	23210
	.byte	25
	.word	23243
	.byte	25
	.word	23256
	.byte	6,0
.L84:
	.byte	21,14,207,3,9,4,13
	.byte	'dma',0
	.word	21310
	.byte	4,2,35,0,0,3
	.word	23892
.L81:
	.byte	21,14,139,4,9,4,13
	.byte	'dma',0
	.word	21310
	.byte	4,2,35,0,0,20
	.word	23917
	.byte	3
	.word	23937
	.byte	26
	.byte	'IfxDma_Dma_initModule',0,14,182,4,17,1,1,1,1,5
	.byte	'dma',0,14,182,4,51
	.word	23912
	.byte	5
	.byte	'config',0,14,182,4,81
	.word	23942
	.byte	0,3
	.word	23917
	.byte	26
	.byte	'IfxDma_Dma_initModuleConfig',0,14,192,4,17,1,1,1,1,5
	.byte	'config',0,14,192,4,64
	.word	24008
	.byte	5
	.byte	'dma',0,14,192,4,81
	.word	21310
	.byte	0,15,11,160,1,9,1,16
	.byte	'IfxDma_ChannelMove_1',0,0,16
	.byte	'IfxDma_ChannelMove_2',0,1,16
	.byte	'IfxDma_ChannelMove_4',0,2,16
	.byte	'IfxDma_ChannelMove_8',0,3,16
	.byte	'IfxDma_ChannelMove_16',0,4,16
	.byte	'IfxDma_ChannelMove_3',0,5,16
	.byte	'IfxDma_ChannelMove_5',0,6,16
	.byte	'IfxDma_ChannelMove_9',0,7,0,15,11,221,1,9,1,16
	.byte	'IfxDma_ChannelRequestMode_oneTransferPerRequest',0,0,16
	.byte	'IfxDma_ChannelRequestMode_completeTransactionPerRequest',0,1,0,15,11,188,1,9,1,16
	.byte	'IfxDma_ChannelOperationMode_single',0,0,16
	.byte	'IfxDma_ChannelOperationMode_continuous',0,1,0,15,11,175,1,9,1,16
	.byte	'IfxDma_ChannelMoveSize_8bit',0,0,16
	.byte	'IfxDma_ChannelMoveSize_16bit',0,1,16
	.byte	'IfxDma_ChannelMoveSize_32bit',0,2,16
	.byte	'IfxDma_ChannelMoveSize_64bit',0,3,16
	.byte	'IfxDma_ChannelMoveSize_128bit',0,4,16
	.byte	'IfxDma_ChannelMoveSize_256bit',0,5,0,15,11,197,1,9,1,16
	.byte	'IfxDma_ChannelPattern_0_disable',0,0,16
	.byte	'IfxDma_ChannelPattern_0_mode1',0,1,16
	.byte	'IfxDma_ChannelPattern_0_mode2',0,2,16
	.byte	'IfxDma_ChannelPattern_0_mode3',0,3,16
	.byte	'IfxDma_ChannelPattern_1_disable',0,4,16
	.byte	'IfxDma_ChannelPattern_1_mode1',0,5,16
	.byte	'IfxDma_ChannelPattern_1_mode2',0,6,16
	.byte	'IfxDma_ChannelPattern_1_mode3',0,7,0,15,11,230,1,9,1,16
	.byte	'IfxDma_ChannelRequestSource_peripheral',0,0,16
	.byte	'IfxDma_ChannelRequestSource_daisyChain',0,1,0,15,11,94,9,1,16
	.byte	'IfxDma_ChannelBusPriority_low',0,0,16
	.byte	'IfxDma_ChannelBusPriority_medium',0,1,16
	.byte	'IfxDma_ChannelBusPriority_high',0,2,0,15,11,136,1,9,1,16
	.byte	'IfxDma_ChannelIncrementStep_1',0,0,16
	.byte	'IfxDma_ChannelIncrementStep_2',0,1,16
	.byte	'IfxDma_ChannelIncrementStep_4',0,2,16
	.byte	'IfxDma_ChannelIncrementStep_8',0,3,16
	.byte	'IfxDma_ChannelIncrementStep_16',0,4,16
	.byte	'IfxDma_ChannelIncrementStep_32',0,5,16
	.byte	'IfxDma_ChannelIncrementStep_64',0,6,16
	.byte	'IfxDma_ChannelIncrementStep_128',0,7,0,15,11,127,9,1,16
	.byte	'IfxDma_ChannelIncrementDirection_negative',0,0,16
	.byte	'IfxDma_ChannelIncrementDirection_positive',0,1,0,15,11,104,9,1,16
	.byte	'IfxDma_ChannelIncrementCircular_none',0,0,16
	.byte	'IfxDma_ChannelIncrementCircular_2',0,1,16
	.byte	'IfxDma_ChannelIncrementCircular_4',0,2,16
	.byte	'IfxDma_ChannelIncrementCircular_8',0,3,16
	.byte	'IfxDma_ChannelIncrementCircular_16',0,4,16
	.byte	'IfxDma_ChannelIncrementCircular_32',0,5,16
	.byte	'IfxDma_ChannelIncrementCircular_64',0,6,16
	.byte	'IfxDma_ChannelIncrementCircular_128',0,7,16
	.byte	'IfxDma_ChannelIncrementCircular_256',0,8,16
	.byte	'IfxDma_ChannelIncrementCircular_512',0,9,16
	.byte	'IfxDma_ChannelIncrementCircular_1024',0,10,16
	.byte	'IfxDma_ChannelIncrementCircular_2048',0,11,16
	.byte	'IfxDma_ChannelIncrementCircular_4096',0,12,16
	.byte	'IfxDma_ChannelIncrementCircular_8192',0,13,16
	.byte	'IfxDma_ChannelIncrementCircular_16384',0,14,16
	.byte	'IfxDma_ChannelIncrementCircular_32768',0,15,0,15,11,239,1,9,1,16
	.byte	'IfxDma_ChannelShadow_none',0,0,16
	.byte	'IfxDma_ChannelShadow_src',0,1,16
	.byte	'IfxDma_ChannelShadow_dst',0,2,16
	.byte	'IfxDma_ChannelShadow_srcDirectWrite',0,5,16
	.byte	'IfxDma_ChannelShadow_dstDirectWrite',0,6,16
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingSwSwitch',0,8,16
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingHwSwSwitch',0,9,16
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingSwSwitch',0,10,16
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingHwSwSwitch',0,11,16
	.byte	'IfxDma_ChannelShadow_linkedList',0,12,16
	.byte	'IfxDma_ChannelShadow_accumulatedLinkedList',0,13,16
	.byte	'IfxDma_ChannelShadow_safeLinkedList',0,14,16
	.byte	'IfxDma_ChannelShadow_conditionalLinkedList',0,15,0,15,11,151,1,9,1,16
	.byte	'IfxDma_ChannelInterruptControl_thresholdLimitMatch',0,0,16
	.byte	'IfxDma_ChannelInterruptControl_transferCountDecremented',0,1,0,15,15,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0
.L87:
	.byte	21,14,227,3,9,56,13
	.byte	'module',0
	.word	23912
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	21315
	.byte	1,2,35,4,13
	.byte	'sourceAddress',0
	.word	2251
	.byte	4,2,35,6,13
	.byte	'destinationAddress',0
	.word	2251
	.byte	4,2,35,10,13
	.byte	'shadowAddress',0
	.word	2251
	.byte	4,2,35,14,13
	.byte	'readDataCrc',0
	.word	2251
	.byte	4,2,35,18,13
	.byte	'sourceDestinationAddressCrc',0
	.word	2251
	.byte	4,2,35,22,13
	.byte	'transferCount',0
	.word	1039
	.byte	2,2,35,26,13
	.byte	'blockMode',0
	.word	24080
	.byte	1,2,35,28,13
	.byte	'requestMode',0
	.word	24272
	.byte	1,2,35,29,13
	.byte	'operationMode',0
	.word	24387
	.byte	1,2,35,30,13
	.byte	'moveSize',0
	.word	24472
	.byte	1,2,35,31,13
	.byte	'pattern',0
	.word	24666
	.byte	1,2,35,32,13
	.byte	'requestSource',0
	.word	24933
	.byte	1,2,35,33,13
	.byte	'busPriority',0
	.word	25022
	.byte	1,2,35,34,13
	.byte	'hardwareRequestEnabled',0
	.word	488
	.byte	1,2,35,35,13
	.byte	'sourceAddressIncrementStep',0
	.word	25128
	.byte	1,2,35,36,13
	.byte	'sourceAddressIncrementDirection',0
	.word	25396
	.byte	1,2,35,37,13
	.byte	'sourceAddressCircularRange',0
	.word	25490
	.byte	1,2,35,38,13
	.byte	'destinationAddressIncrementStep',0
	.word	25128
	.byte	1,2,35,39,13
	.byte	'destinationAddressIncrementDirection',0
	.word	25396
	.byte	1,2,35,40,13
	.byte	'destinationAddressCircularRange',0
	.word	25490
	.byte	1,2,35,41,13
	.byte	'shadowControl',0
	.word	26104
	.byte	1,2,35,42,13
	.byte	'sourceCircularBufferEnabled',0
	.word	488
	.byte	1,2,35,43,13
	.byte	'destinationCircularBufferEnabled',0
	.word	488
	.byte	1,2,35,44,13
	.byte	'timestampEnabled',0
	.word	488
	.byte	1,2,35,45,13
	.byte	'wrapSourceInterruptEnabled',0
	.word	488
	.byte	1,2,35,46,13
	.byte	'wrapDestinationInterruptEnabled',0
	.word	488
	.byte	1,2,35,47,13
	.byte	'channelInterruptEnabled',0
	.word	488
	.byte	1,2,35,48,13
	.byte	'channelInterruptControl',0
	.word	26657
	.byte	1,2,35,49,13
	.byte	'interruptRaiseThreshold',0
	.word	488
	.byte	1,2,35,50,13
	.byte	'transactionRequestLostInterruptEnabled',0
	.word	488
	.byte	1,2,35,51,13
	.byte	'channelInterruptPriority',0
	.word	1039
	.byte	2,2,35,52,13
	.byte	'channelInterruptTypeOfService',0
	.word	26775
	.byte	1,2,35,54,0,20
	.word	26834
	.byte	3
	.word	27871
	.byte	26
	.byte	'IfxDma_Dma_initChannel',0,14,211,4,17,1,1,1,1,5
	.byte	'channel',0,14,211,4,60
	.word	23343
	.byte	5
	.byte	'config',0,14,211,4,101
	.word	27876
	.byte	0,3
	.word	26834
	.byte	26
	.byte	'IfxDma_Dma_initChannelConfig',0,14,221,4,17,1,1,1,1,5
	.byte	'config',0,14,221,4,72
	.word	27947
	.byte	5
	.byte	'dma',0,14,221,4,92
	.word	23912
	.byte	0,24
	.word	23348
	.byte	25
	.word	23385
	.byte	17,27
	.word	23210
	.byte	25
	.word	23243
	.byte	25
	.word	23256
	.byte	28
	.word	23275
	.byte	0,6,0,0,26
	.byte	'IfxDma_Dma_initLinkedListEntry',0,14,250,4,17,1,1,1,1,5
	.byte	'ptrToAddress',0,14,250,4,54
	.word	379
	.byte	5
	.byte	'config',0,14,250,4,100
	.word	27876
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	28134
	.byte	26
	.byte	'debug_assert_handler',0,16,112,9,1,1,1,1,5
	.byte	'pass',0,16,112,47
	.word	488
	.byte	5
	.byte	'file',0,16,112,59
	.word	28142
	.byte	5
	.byte	'line',0,16,112,69
	.word	481
	.byte	0
.L72:
	.byte	15,17,42,9,1,16
	.byte	'ERU_CH0_REQ0_P15_4',0,1,16
	.byte	'ERU_CH1_REQ10_P14_3',0,4,16
	.byte	'ERU_CH2_REQ7_P00_4',0,6,16
	.byte	'ERU_CH2_REQ14_P02_1',0,7,16
	.byte	'ERU_CH2_REQ2_P10_2',0,8,16
	.byte	'ERU_CH3_REQ6_P02_0',0,9,16
	.byte	'ERU_CH3_REQ3_P10_3',0,10,16
	.byte	'ERU_CH3_REQ15_P14_1',0,11,16
	.byte	'ERU_CH4_REQ13_P15_5',0,12,16
	.byte	'ERU_CH4_REQ8_P33_7',0,13,16
	.byte	'ERU_CH5_REQ1_P15_8',0,15,16
	.byte	'ERU_CH6_REQ12_P11_10',0,18,16
	.byte	'ERU_CH6_REQ9_P20_0',0,19,16
	.byte	'ERU_CH7_REQ16_P15_1',0,21,16
	.byte	'ERU_CH7_REQ11_P20_9',0,22,0
.L74:
	.byte	15,17,65,9,1,16
	.byte	'EXTI_TRIGGER_RISING',0,0,16
	.byte	'EXTI_TRIGGER_FALLING',0,1,16
	.byte	'EXTI_TRIGGER_BOTH',0,2,0,26
	.byte	'exti_init',0,17,83,6,1,1,1,1,5
	.byte	'eru_pin',0,17,83,44
	.word	28216
	.byte	5
	.byte	'trigger',0,17,83,71
	.word	28545
	.byte	0
.L69:
	.byte	3
	.word	488
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,18,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	29,0,2,35,0,0
.L96:
	.byte	12,18,223,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28672
	.byte	4,2,35,0,0,7
	.byte	'short int',0,2,5,29
	.byte	'__wchar_t',0,19,1,1
	.word	28781
	.byte	29
	.byte	'__size_t',0,19,1,1
	.word	465
	.byte	29
	.byte	'__ptrdiff_t',0,19,1,1
	.word	481
	.byte	30,1,3
	.word	28849
	.byte	29
	.byte	'__codeptr',0,19,1,1
	.word	28851
	.byte	29
	.byte	'__intptr_t',0,19,1,1
	.word	481
	.byte	29
	.byte	'__uintptr_t',0,19,1,1
	.word	465
	.byte	29
	.byte	'boolean',0,20,101,29
	.word	488
	.byte	29
	.byte	'uint8',0,20,105,29
	.word	488
	.byte	29
	.byte	'uint16',0,20,109,29
	.word	1039
	.byte	29
	.byte	'uint32',0,20,113,29
	.word	2251
	.byte	29
	.byte	'uint64',0,20,118,29
	.word	347
	.byte	29
	.byte	'sint16',0,20,126,29
	.word	28781
	.byte	7
	.byte	'long int',0,4,5,29
	.byte	'sint32',0,20,131,1,29
	.word	29003
	.byte	7
	.byte	'long long int',0,8,5,29
	.byte	'sint64',0,20,138,1,29
	.word	29031
	.byte	29
	.byte	'float32',0,20,167,1,29
	.word	261
	.byte	29
	.byte	'pvoid',0,21,57,28
	.word	379
	.byte	29
	.byte	'Ifx_TickTime',0,21,79,28
	.word	29031
	.byte	29
	.byte	'Ifx_Priority',0,21,103,16
	.word	1039
	.byte	15,21,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,29
	.byte	'Ifx_RxSel',0,21,140,1,3
	.word	29137
	.byte	29
	.byte	'IfxDma_ChannelId',0,13,156,1,3
	.word	21315
	.byte	29
	.byte	'Ifx_DMA_ACCEN00_Bits',0,12,79,3
	.word	11191
	.byte	29
	.byte	'Ifx_DMA_ACCEN01_Bits',0,12,85,3
	.word	11761
	.byte	29
	.byte	'Ifx_DMA_ACCEN10_Bits',0,12,122,3
	.word	11851
	.byte	29
	.byte	'Ifx_DMA_ACCEN11_Bits',0,12,128,1,3
	.word	12421
	.byte	29
	.byte	'Ifx_DMA_ACCEN20_Bits',0,12,165,1,3
	.word	12511
	.byte	29
	.byte	'Ifx_DMA_ACCEN21_Bits',0,12,171,1,3
	.word	13082
	.byte	29
	.byte	'Ifx_DMA_ACCEN30_Bits',0,12,208,1,3
	.word	13173
	.byte	29
	.byte	'Ifx_DMA_ACCEN31_Bits',0,12,214,1,3
	.word	13744
	.byte	29
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,12,230,1,3
	.word	14362
	.byte	29
	.byte	'Ifx_DMA_BLK_EER_Bits',0,12,243,1,3
	.word	13846
	.byte	29
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,12,132,2,3
	.word	14070
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,12,152,2,3
	.word	16274
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,12,168,2,3
	.word	16590
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,12,184,2,3
	.word	16960
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,12,190,2,3
	.word	16185
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,12,199,2,3
	.word	14825
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,12,208,2,3
	.word	14960
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,12,217,2,3
	.word	15095
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,12,226,2,3
	.word	15230
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,12,235,2,3
	.word	15365
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,12,244,2,3
	.word	15500
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,12,253,2,3
	.word	15635
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,12,134,3,3
	.word	15770
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,12,140,3,3
	.word	15914
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,12,146,3,3
	.word	16096
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,12,152,3,3
	.word	16005
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,12,158,3,3
	.word	16869
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,12,169,3,3
	.word	14645
	.byte	29
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,12,189,3,3
	.word	19410
	.byte	29
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,12,205,3,3
	.word	19722
	.byte	29
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,12,226,3,3
	.word	20086
	.byte	29
	.byte	'Ifx_DMA_CH_DADR_Bits',0,12,232,3,3
	.word	19325
	.byte	29
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,12,238,3,3
	.word	19064
	.byte	29
	.byte	'Ifx_DMA_CH_SADR_Bits',0,12,244,3,3
	.word	19240
	.byte	29
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,12,250,3,3
	.word	19152
	.byte	29
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,12,128,4,3
	.word	19999
	.byte	29
	.byte	'Ifx_DMA_CLC_Bits',0,12,138,4,3
	.word	10626
	.byte	29
	.byte	'Ifx_DMA_ERRINTR_Bits',0,12,145,4,3
	.word	17790
	.byte	29
	.byte	'Ifx_DMA_HRR_Bits',0,12,152,4,3
	.word	18382
	.byte	29
	.byte	'Ifx_DMA_ID_Bits',0,12,160,4,3
	.word	10783
	.byte	29
	.byte	'Ifx_DMA_MEMCON_Bits',0,12,175,4,3
	.word	10914
	.byte	29
	.byte	'Ifx_DMA_MODE_Bits',0,12,182,4,3
	.word	18258
	.byte	29
	.byte	'Ifx_DMA_OTSS_Bits',0,12,191,4,3
	.word	17651
	.byte	29
	.byte	'Ifx_DMA_PRR0_Bits',0,12,200,4,3
	.word	17896
	.byte	29
	.byte	'Ifx_DMA_PRR1_Bits',0,12,209,4,3
	.word	18030
	.byte	29
	.byte	'Ifx_DMA_SUSACR_Bits',0,12,216,4,3
	.word	18622
	.byte	29
	.byte	'Ifx_DMA_SUSENR_Bits',0,12,223,4,3
	.word	18505
	.byte	29
	.byte	'Ifx_DMA_TIME_Bits',0,12,229,4,3
	.word	18164
	.byte	29
	.byte	'Ifx_DMA_TSR_Bits',0,12,248,4,3
	.word	18739
	.byte	29
	.byte	'Ifx_DMA_ACCEN00',0,12,133,5,3
	.word	11721
	.byte	29
	.byte	'Ifx_DMA_ACCEN01',0,12,141,5,3
	.word	11811
	.byte	29
	.byte	'Ifx_DMA_ACCEN10',0,12,149,5,3
	.word	12381
	.byte	29
	.byte	'Ifx_DMA_ACCEN11',0,12,157,5,3
	.word	12471
	.byte	29
	.byte	'Ifx_DMA_ACCEN20',0,12,165,5,3
	.word	13042
	.byte	29
	.byte	'Ifx_DMA_ACCEN21',0,12,173,5,3
	.word	13133
	.byte	29
	.byte	'Ifx_DMA_ACCEN30',0,12,181,5,3
	.word	13704
	.byte	29
	.byte	'Ifx_DMA_ACCEN31',0,12,189,5,3
	.word	13795
	.byte	29
	.byte	'Ifx_DMA_BLK_CLRE',0,12,197,5,3
	.word	14605
	.byte	29
	.byte	'Ifx_DMA_BLK_EER',0,12,205,5,3
	.word	14030
	.byte	29
	.byte	'Ifx_DMA_BLK_ERRSR',0,12,213,5,3
	.word	14322
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,12,221,5,3
	.word	16550
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,12,229,5,3
	.word	16829
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,12,237,5,3
	.word	17193
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_DADR',0,12,245,5,3
	.word	16234
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R0',0,12,253,5,3
	.word	14920
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R1',0,12,133,6,3
	.word	15055
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R2',0,12,141,6,3
	.word	15190
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R3',0,12,149,6,3
	.word	15325
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R4',0,12,157,6,3
	.word	15460
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R5',0,12,165,6,3
	.word	15595
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R6',0,12,173,6,3
	.word	15730
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_R7',0,12,181,6,3
	.word	15865
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,12,189,6,3
	.word	15965
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SADR',0,12,197,6,3
	.word	16145
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,12,205,6,3
	.word	16056
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,12,213,6,3
	.word	16920
	.byte	29
	.byte	'Ifx_DMA_BLK_ME_SR',0,12,221,6,3
	.word	14785
	.byte	29
	.byte	'Ifx_DMA_CH_ADICR',0,12,229,6,3
	.word	19682
	.byte	29
	.byte	'Ifx_DMA_CH_CHCFGR',0,12,237,6,3
	.word	19959
	.byte	29
	.byte	'Ifx_DMA_CH_CHCSR',0,12,245,6,3
	.word	20393
	.byte	29
	.byte	'Ifx_DMA_CH_DADR',0,12,253,6,3
	.word	19370
	.byte	29
	.byte	'Ifx_DMA_CH_RDCRCR',0,12,133,7,3
	.word	19112
	.byte	29
	.byte	'Ifx_DMA_CH_SADR',0,12,141,7,3
	.word	19285
	.byte	29
	.byte	'Ifx_DMA_CH_SDCRCR',0,12,149,7,3
	.word	19200
	.byte	29
	.byte	'Ifx_DMA_CH_SHADR',0,12,157,7,3
	.word	20046
	.byte	29
	.byte	'Ifx_DMA_CLC',0,12,165,7,3
	.word	10743
	.byte	29
	.byte	'Ifx_DMA_ERRINTR',0,12,173,7,3
	.word	17856
	.byte	29
	.byte	'Ifx_DMA_HRR',0,12,181,7,3
	.word	18444
	.byte	29
	.byte	'Ifx_DMA_ID',0,12,189,7,3
	.word	10865
	.byte	29
	.byte	'Ifx_DMA_MEMCON',0,12,197,7,3
	.word	11142
	.byte	29
	.byte	'Ifx_DMA_MODE',0,12,205,7,3
	.word	18322
	.byte	29
	.byte	'Ifx_DMA_OTSS',0,12,213,7,3
	.word	17750
	.byte	29
	.byte	'Ifx_DMA_PRR0',0,12,221,7,3
	.word	17990
	.byte	29
	.byte	'Ifx_DMA_PRR1',0,12,229,7,3
	.word	18124
	.byte	29
	.byte	'Ifx_DMA_SUSACR',0,12,237,7,3
	.word	18689
	.byte	29
	.byte	'Ifx_DMA_SUSENR',0,12,245,7,3
	.word	18572
	.byte	29
	.byte	'Ifx_DMA_TIME',0,12,253,7,3
	.word	18207
	.byte	29
	.byte	'Ifx_DMA_TSR',0,12,133,8,3
	.word	19014
	.byte	14
	.word	17233
	.byte	29
	.byte	'Ifx_DMA_BLK_ME',0,12,165,8,3
	.word	32055
	.byte	14
	.word	17526
	.byte	29
	.byte	'Ifx_DMA_BLK',0,12,185,8,3
	.word	32084
	.byte	14
	.word	20433
	.byte	29
	.byte	'Ifx_DMA_CH',0,12,198,8,3
	.word	32110
	.byte	14
	.word	20599
	.byte	29
	.byte	'Ifx_DMA',0,12,250,8,3
	.word	32135
	.byte	29
	.byte	'IfxSrc_Tos',0,15,74,3
	.word	26775
	.byte	29
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	505
	.byte	29
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	795
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	32223
	.byte	29
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	32255
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,0,14
	.word	32281
	.byte	29
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	32340
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	32368
	.byte	29
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	32405
	.byte	18,64
	.word	795
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	32433
	.byte	64,2,35,0,0,14
	.word	32442
	.byte	29
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	32474
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	32499
	.byte	29
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	32571
	.byte	18,8
	.word	795
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	32597
	.byte	8,2,35,0,0,14
	.word	32606
	.byte	29
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	32642
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	32672
	.byte	29
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	32745
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	32771
	.byte	29
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	32806
	.byte	18,192,1
	.word	795
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5647
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	32832
	.byte	192,1,2,35,16,0,14
	.word	32842
	.byte	29
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	32909
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	795
	.byte	4,2,35,4,0,14
	.word	32935
	.byte	29
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	32983
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	33011
	.byte	29
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	33044
	.byte	18,40
	.word	488
	.byte	19,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	32597
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	32597
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	32597
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	32597
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	795
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	795
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	33071
	.byte	40,2,35,40,0,14
	.word	33080
	.byte	29
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	33207
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	33234
	.byte	29
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	33266
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	33292
	.byte	29
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	33324
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	795
	.byte	4,2,35,8,0,14
	.word	33350
	.byte	29
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	33410
	.byte	18,16
	.word	488
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	33436
	.byte	16,2,35,16,0,14
	.word	33445
	.byte	29
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	33539
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4678
	.byte	24,2,35,24,0,14
	.word	33566
	.byte	29
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	33683
	.byte	18,12
	.word	795
	.byte	19,2,0,18,32
	.word	795
	.byte	19,7,0,18,32
	.word	33720
	.byte	19,0,0,18,88
	.word	488
	.byte	19,87,0,18,108
	.word	795
	.byte	19,26,0,18,96
	.word	33720
	.byte	19,2,0,18,160,3
	.word	488
	.byte	19,159,3,0,18,64
	.word	33720
	.byte	19,1,0,18,192,3
	.word	488
	.byte	19,191,3,0,18,16
	.word	795
	.byte	19,3,0,18,64
	.word	33796
	.byte	19,3,0,18,52
	.word	488
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	33711
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3488
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	795
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	32597
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	5307
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	33729
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	33738
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	33747
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	17642
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	795
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5647
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	33756
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	33765
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	33756
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	33765
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	33776
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	33785
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	33805
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	18494
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	33711
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	33814
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	33711
	.byte	12,3,35,192,18,0,14
	.word	33823
	.byte	29
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	34283
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	34309
	.byte	29
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	34342
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	34369
	.byte	29
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	34442
	.byte	18,56
	.word	488
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	34469
	.byte	56,2,35,24,0,14
	.word	34478
	.byte	29
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	34601
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	34627
	.byte	29
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	34659
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	795
	.byte	4,2,35,16,0,14
	.word	34685
	.byte	29
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	34770
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	34796
	.byte	29
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	34828
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	33720
	.byte	32,2,35,0,0,14
	.word	34854
	.byte	29
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	34887
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	33720
	.byte	32,2,35,0,0,14
	.word	34914
	.byte	29
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	34948
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	795
	.byte	4,2,35,20,0,14
	.word	34976
	.byte	29
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	35069
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	35096
	.byte	29
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	35128
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	33796
	.byte	16,2,35,4,0,14
	.word	35154
	.byte	29
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	35200
	.byte	18,24
	.word	795
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	35226
	.byte	24,2,35,0,0,14
	.word	35235
	.byte	29
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	35268
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	33711
	.byte	12,2,35,0,0,14
	.word	35295
	.byte	29
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	35327
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,0,14
	.word	35353
	.byte	29
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	35399
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	35425
	.byte	29
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	35500
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	35529
	.byte	29
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	35603
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	35631
	.byte	29
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	35665
	.byte	18,4
	.word	32223
	.byte	19,0,0,14
	.word	35692
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	35701
	.byte	4,2,35,0,0,14
	.word	35706
	.byte	29
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	35742
	.byte	18,48
	.word	32281
	.byte	19,3,0,14
	.word	35770
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	35779
	.byte	48,2,35,0,0,14
	.word	35784
	.byte	29
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	35824
	.byte	14
	.word	32368
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	35854
	.byte	4,2,35,0,0,14
	.word	35859
	.byte	29
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	35893
	.byte	18,64
	.word	32442
	.byte	19,0,0,14
	.word	35920
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	35929
	.byte	64,2,35,0,0,14
	.word	35934
	.byte	29
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	35968
	.byte	18,32
	.word	32499
	.byte	19,1,0,14
	.word	35995
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	36004
	.byte	32,2,35,0,0,14
	.word	36009
	.byte	29
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	36045
	.byte	14
	.word	32606
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	36073
	.byte	8,2,35,0,0,14
	.word	36078
	.byte	29
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	36122
	.byte	18,16
	.word	32672
	.byte	19,0,0,14
	.word	36154
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	36163
	.byte	16,2,35,0,0,14
	.word	36168
	.byte	29
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	36202
	.byte	18,8
	.word	32771
	.byte	19,1,0,14
	.word	36229
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	36238
	.byte	8,2,35,0,0,14
	.word	36243
	.byte	29
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	36277
	.byte	18,208,1
	.word	32842
	.byte	19,0,0,14
	.word	36304
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	36314
	.byte	208,1,2,35,0,0,14
	.word	36319
	.byte	29
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	36355
	.byte	14
	.word	32935
	.byte	14
	.word	32935
	.byte	14
	.word	32935
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	36382
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	5307
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	36387
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	36392
	.byte	8,2,35,24,0,14
	.word	36397
	.byte	29
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	36488
	.byte	18,4
	.word	33011
	.byte	19,0,0,14
	.word	36517
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	36526
	.byte	4,2,35,0,0,14
	.word	36531
	.byte	29
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	36567
	.byte	18,80
	.word	33080
	.byte	19,0,0,14
	.word	36595
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	36604
	.byte	80,2,35,0,0,14
	.word	36609
	.byte	29
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	36645
	.byte	18,4
	.word	33234
	.byte	19,0,0,14
	.word	36673
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	36682
	.byte	4,2,35,0,0,14
	.word	36687
	.byte	29
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	36721
	.byte	18,4
	.word	33292
	.byte	19,0,0,14
	.word	36748
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	36757
	.byte	4,2,35,0,0,14
	.word	36762
	.byte	29
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	36796
	.byte	18,12
	.word	33350
	.byte	19,0,0,14
	.word	36823
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	36832
	.byte	12,2,35,0,0,14
	.word	36837
	.byte	29
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	36871
	.byte	18,64
	.word	33445
	.byte	19,1,0,14
	.word	36898
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	36907
	.byte	64,2,35,0,0,14
	.word	36912
	.byte	29
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	36948
	.byte	18,48
	.word	33566
	.byte	19,0,0,14
	.word	36976
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	36985
	.byte	48,2,35,0,0,14
	.word	36990
	.byte	29
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	37028
	.byte	18,204,18
	.word	33823
	.byte	19,0,0,14
	.word	37057
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	37067
	.byte	204,18,2,35,0,0,14
	.word	37072
	.byte	29
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	37108
	.byte	18,4
	.word	34309
	.byte	19,0,0,14
	.word	37135
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	37144
	.byte	4,2,35,0,0,14
	.word	37149
	.byte	29
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	37185
	.byte	18,64
	.word	34369
	.byte	19,3,0,14
	.word	37213
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	37222
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	795
	.byte	4,2,35,64,0,14
	.word	37227
	.byte	29
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	37276
	.byte	18,80
	.word	34478
	.byte	19,0,0,14
	.word	37304
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	37313
	.byte	80,2,35,0,0,14
	.word	37318
	.byte	29
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	37352
	.byte	18,4
	.word	34627
	.byte	19,0,0,14
	.word	37379
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	37388
	.byte	4,2,35,0,0,14
	.word	37393
	.byte	29
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	37427
	.byte	18,40
	.word	34685
	.byte	19,1,0,14
	.word	37454
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	37463
	.byte	40,2,35,0,0,14
	.word	37468
	.byte	29
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	37502
	.byte	18,8
	.word	34796
	.byte	19,1,0,14
	.word	37529
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	37538
	.byte	8,2,35,0,0,14
	.word	37543
	.byte	29
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	37577
	.byte	18,32
	.word	34854
	.byte	19,0,0,14
	.word	37604
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	37613
	.byte	32,2,35,0,0,14
	.word	37618
	.byte	29
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	37654
	.byte	18,32
	.word	34914
	.byte	19,0,0,14
	.word	37682
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	37691
	.byte	32,2,35,0,0,14
	.word	37696
	.byte	29
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	37734
	.byte	18,96
	.word	34976
	.byte	19,3,0,14
	.word	37763
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	37772
	.byte	96,2,35,0,0,14
	.word	37777
	.byte	29
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	37813
	.byte	18,4
	.word	35096
	.byte	19,0,0,14
	.word	37841
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	37850
	.byte	4,2,35,0,0,14
	.word	37855
	.byte	29
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	37889
	.byte	14
	.word	35154
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	37916
	.byte	20,2,35,0,0,14
	.word	37921
	.byte	29
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	37955
	.byte	18,24
	.word	35235
	.byte	19,0,0,14
	.word	37982
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	37991
	.byte	24,2,35,0,0,14
	.word	37996
	.byte	29
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	38032
	.byte	18,12
	.word	35295
	.byte	19,0,0,14
	.word	38060
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	38069
	.byte	12,2,35,0,0,14
	.word	38074
	.byte	29
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	38108
	.byte	18,16
	.word	35353
	.byte	19,1,0,14
	.word	38135
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	38144
	.byte	16,2,35,0,0,14
	.word	38149
	.byte	29
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	38183
	.byte	18,64
	.word	35529
	.byte	19,3,0,14
	.word	38210
	.byte	18,224,1
	.word	488
	.byte	19,223,1,0,18,32
	.word	35425
	.byte	19,1,0,14
	.word	38235
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	38219
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	38224
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	38244
	.byte	32,3,35,160,2,0,14
	.word	38249
	.byte	29
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	38318
	.byte	14
	.word	35631
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	38346
	.byte	4,2,35,0,0,14
	.word	38351
	.byte	29
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	38387
	.byte	15,22,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,29
	.byte	'IfxScu_CCUCON0_CLKSEL',0,22,240,10,3
	.word	38415
	.byte	15,22,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,22,255,10,3
	.word	38512
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	38634
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	39191
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	39268
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	488
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	39404
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,11
	.byte	'CANDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	39684
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	39922
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	40050
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	40293
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	40528
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	40656
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	40756
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	488
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	40856
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,11
	.byte	'PWD',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	465
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	41064
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1039
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	41229
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	41412
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	465
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	41566
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	41930
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,11
	.byte	'POL',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	488
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	42141
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	42393
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,11
	.byte	'ARI',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	42511
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	42622
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	42785
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	42948
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	43106
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	488
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	43271
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	488
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1039
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	43600
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	43821
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	43984
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	44256
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	44409
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	44565
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	44727
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	44870
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	45035
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	45180
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	45361
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	45535
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	45695
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	45839
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	46113
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	46252
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	488
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1039
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	488
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	488
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	46415
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	46633
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,11
	.byte	'FS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	46796
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	47132
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	488
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	47239
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	47691
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	47790
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1039
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	47940
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,11
	.byte	'SEED',0,4
	.word	465
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	48089
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	48250
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1039
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	48380
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	48512
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1039
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	48627
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1039
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	48738
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	488
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	488
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	48896
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	49308
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1039
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	49409
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	49676
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	49812
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,11
	.byte	'PD0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	49923
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	50056
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	50259
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	488
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	488
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	50615
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	50793
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	488
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	50893
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	488
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	51263
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	51449
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	51647
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	51880
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	488
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	52032
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	52599
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	52893
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	488
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	488
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	53171
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	53667
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1039
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	53980
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	54189
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	54400
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,11
	.byte	'HBT',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	54832
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	488
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	488
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	54928
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	55188
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	55313
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	55510
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	55663
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	55816
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	55969
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	903
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	1061
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	1305
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	887
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	56224
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	56350
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,11
	.byte	'AE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	56602
	.byte	12,6,199,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38634
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	56821
	.byte	12,6,207,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39191
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	56885
	.byte	12,6,215,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39268
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	56949
	.byte	12,6,223,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39404
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	57014
	.byte	12,6,231,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39684
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	57079
	.byte	12,6,239,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39922
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	57144
	.byte	12,6,247,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40050
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	57209
	.byte	12,6,255,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40293
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	57274
	.byte	12,6,135,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40528
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	57339
	.byte	12,6,143,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40656
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	57404
	.byte	12,6,151,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40756
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	57469
	.byte	12,6,159,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40856
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	57534
	.byte	12,6,167,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41064
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	57598
	.byte	12,6,175,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41229
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	57662
	.byte	12,6,183,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41412
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	57726
	.byte	12,6,191,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41566
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	57791
	.byte	12,6,199,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41930
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	57853
	.byte	12,6,207,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42141
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	57915
	.byte	12,6,215,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42393
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	57977
	.byte	12,6,223,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42511
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	58041
	.byte	12,6,231,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42622
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	58106
	.byte	12,6,239,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42785
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	58172
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42948
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	58238
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43106
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	58306
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43271
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	58373
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43600
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	58441
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43821
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	58509
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43984
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	58575
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44256
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	58642
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44409
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	58711
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44565
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	58780
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44727
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	58849
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44870
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	58918
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45035
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	58987
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45180
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	59056
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45361
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	59124
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45535
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	59192
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45695
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	59260
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45839
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	59328
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46113
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	59393
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46252
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	59458
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46415
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	59524
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46633
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	59588
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46796
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	59649
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47132
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	59710
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47239
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	59770
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47691
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	59832
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47790
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	59892
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47940
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	59954
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48089
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	60022
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48250
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	60090
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48380
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	60158
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48512
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	60222
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48627
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	60287
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48738
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	60350
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48896
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	60411
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49308
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	60475
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49409
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	60536
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49676
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	60600
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49812
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	60667
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49923
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	60730
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50056
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	60791
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50259
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	60853
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50615
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	60918
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50793
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	60983
	.byte	12,6,207,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50893
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	61048
	.byte	12,6,215,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51263
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	61117
	.byte	12,6,223,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51449
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	61186
	.byte	12,6,231,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51647
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	61255
	.byte	12,6,239,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51880
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	61320
	.byte	12,6,247,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52032
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	61383
	.byte	12,6,255,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52599
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	61448
	.byte	12,6,135,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52893
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	61513
	.byte	12,6,143,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53171
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	61578
	.byte	12,6,151,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53667
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	61644
	.byte	12,6,159,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54189
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	61713
	.byte	12,6,167,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53980
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	61777
	.byte	12,6,175,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54400
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	61842
	.byte	12,6,183,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54832
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	61907
	.byte	12,6,191,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54928
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	61972
	.byte	12,6,199,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55188
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	62036
	.byte	12,6,207,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55313
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	62102
	.byte	12,6,215,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55510
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	62166
	.byte	12,6,223,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55663
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	62231
	.byte	12,6,231,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55816
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	62296
	.byte	12,6,239,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55969
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	62361
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	999
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	1265
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	1496
	.byte	12,6,143,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56224
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	62512
	.byte	12,6,151,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56350
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	62579
	.byte	12,6,159,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56602
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	62646
	.byte	14
	.word	1536
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	62711
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,13
	.byte	'CON0',0
	.word	62512
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	62579
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	62646
	.byte	4,2,35,8,0,14
	.word	62740
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	62801
	.byte	18,8
	.word	57977
	.byte	19,1,0,18,8
	.word	61320
	.byte	19,1,0,14
	.word	62740
	.byte	18,24
	.word	1536
	.byte	19,1,0,14
	.word	62851
	.byte	18,16
	.word	57791
	.byte	19,3,0,18,16
	.word	59770
	.byte	19,3,0,18,180,3
	.word	488
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	5307
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	59710
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3488
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	60411
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	61255
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	60853
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	60918
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	60983
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	61186
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	61048
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	61117
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	57014
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	57079
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	59588
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	59524
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	57144
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	57209
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	57274
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	57339
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	61842
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3488
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	61713
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	56949
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	62036
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	61777
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3488
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	58575
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	62828
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	58041
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	62102
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	57404
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	57469
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	10905
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	60730
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	59892
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	60475
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	60350
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	59832
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	59328
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	58306
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	58106
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	58172
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	61972
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3488
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	61383
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	61578
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	61644
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	62837
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3488
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	57726
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	57598
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	61448
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	61513
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	62846
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	57915
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	62860
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5647
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	62361
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	62296
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	62166
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	62231
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3488
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	60158
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	60222
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	57534
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	60287
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	5307
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	61907
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	33436
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	59954
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	60022
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	60090
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	11182
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	60667
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	5307
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	59393
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	58238
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	59458
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	58509
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	58373
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3488
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	59056
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	59124
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	59192
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	59260
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	58642
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	58711
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	58780
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	58849
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	58918
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	58987
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	58441
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3488
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	60600
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	60536
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	33071
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	62865
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	57853
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	59649
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	60791
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	62874
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3488
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	57662
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	62883
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	56885
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	56821
	.byte	4,3,35,252,7,0,14
	.word	62894
	.byte	29
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	64884
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,18,45,16,4,11
	.byte	'ADDR',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_A_Bits',0,18,48,3
	.word	64906
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,18,51,16,4,11
	.byte	'VSS',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BIV_Bits',0,18,55,3
	.word	64967
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,18,58,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BTV_Bits',0,18,62,3
	.word	65046
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,18,65,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT_Bits',0,18,69,3
	.word	65132
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,18,72,16,4,11
	.byte	'CM',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	887
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	887
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	887
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL_Bits',0,18,80,3
	.word	65221
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,18,83,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT_Bits',0,18,89,3
	.word	65367
	.byte	29
	.byte	'Ifx_CPU_CORE_ID_Bits',0,18,96,3
	.word	28672
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,18,99,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L_Bits',0,18,103,3
	.word	65523
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,18,106,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U_Bits',0,18,110,3
	.word	65616
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,18,113,16,4,11
	.byte	'MODREV',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	887
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID_Bits',0,18,118,3
	.word	65709
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,18,121,16,4,11
	.byte	'XE',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE_Bits',0,18,125,3
	.word	65816
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,18,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT_Bits',0,18,136,1,3
	.word	65903
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,18,139,1,16,4,11
	.byte	'CID',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID_Bits',0,18,143,1,3
	.word	66057
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,18,146,1,16,4,11
	.byte	'DATA',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_D_Bits',0,18,149,1,3
	.word	66151
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,18,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	887
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	887
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DATR_Bits',0,18,163,1,3
	.word	66214
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,18,166,1,16,4,11
	.byte	'DE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	887
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	887
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	887
	.byte	19,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR_Bits',0,18,177,1,3
	.word	66432
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,18,180,1,16,4,11
	.byte	'DTA',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR_Bits',0,18,184,1,3
	.word	66647
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,18,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0_Bits',0,18,192,1,3
	.word	66741
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,18,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2_Bits',0,18,199,1,3
	.word	66857
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,18,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	887
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCX_Bits',0,18,206,1,3
	.word	66958
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,18,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD_Bits',0,18,212,1,3
	.word	67051
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,18,215,1,16,4,11
	.byte	'TA',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR_Bits',0,18,218,1,3
	.word	67131
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,18,221,1,16,4,11
	.byte	'IED',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	887
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	887
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR_Bits',0,18,233,1,3
	.word	67200
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,18,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DMS_Bits',0,18,240,1,3
	.word	67429
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,18,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L_Bits',0,18,247,1,3
	.word	67522
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,18,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U_Bits',0,18,254,1,3
	.word	67617
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,18,129,2,16,4,11
	.byte	'RE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE_Bits',0,18,133,2,3
	.word	67712
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,18,136,2,16,4,11
	.byte	'WE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE_Bits',0,18,140,2,3
	.word	67802
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,18,143,2,16,4,11
	.byte	'SRE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	887
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	887
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	887
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	887
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	887
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	887
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	887
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	887
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR_Bits',0,18,161,2,3
	.word	67892
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,18,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT_Bits',0,18,172,2,3
	.word	68216
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,18,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FCX_Bits',0,18,180,2,3
	.word	68370
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,18,183,2,16,4,11
	.byte	'TST',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	887
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	887
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	887
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	887
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	887
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	887
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	887
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	887
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	887
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,18,202,2,3
	.word	68476
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,205,2,16,4,11
	.byte	'OPC',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	887
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,212,2,3
	.word	68825
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,18,215,2,16,4,11
	.byte	'PC',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,18,218,2,3
	.word	68985
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,224,2,3
	.word	69066
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,230,2,3
	.word	69153
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,236,2,3
	.word	69240
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,18,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT_Bits',0,18,243,2,3
	.word	69327
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,18,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	887
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	887
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	887
	.byte	6,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICR_Bits',0,18,253,2,3
	.word	69418
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,18,128,3,16,4,11
	.byte	'ISP',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_ISP_Bits',0,18,131,3,3
	.word	69561
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,18,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_LCX_Bits',0,18,139,3,3
	.word	69627
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,18,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT_Bits',0,18,146,3,3
	.word	69733
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,18,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT_Bits',0,18,153,3,3
	.word	69826
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,18,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT_Bits',0,18,160,3,3
	.word	69919
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,18,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_PC_Bits',0,18,167,3,3
	.word	70012
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,18,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0_Bits',0,18,175,3,3
	.word	70097
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,18,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1_Bits',0,18,183,3,3
	.word	70213
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,18,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2_Bits',0,18,190,3,3
	.word	70324
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,18,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	887
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	887
	.byte	10,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI_Bits',0,18,200,3,3
	.word	70425
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,18,203,3,16,4,11
	.byte	'TA',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR_Bits',0,18,206,3,3
	.word	70555
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,18,209,3,16,4,11
	.byte	'IED',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	887
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	887
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR_Bits',0,18,221,3,3
	.word	70624
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,18,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	887
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0_Bits',0,18,229,3,3
	.word	70853
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,18,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	887
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1_Bits',0,18,237,3,3
	.word	70966
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,18,240,3,16,4,11
	.byte	'PSI',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2_Bits',0,18,244,3,3
	.word	71079
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,18,247,3,16,4,11
	.byte	'FRE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	887
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR_Bits',0,18,129,4,3
	.word	71170
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,18,132,4,16,4,11
	.byte	'CDC',0,4
	.word	887
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	887
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	887
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	887
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	887
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	887
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSW_Bits',0,18,147,4,3
	.word	71373
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,18,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	887
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN_Bits',0,18,156,4,3
	.word	71616
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,18,159,4,16,4,11
	.byte	'PC',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	887
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	887
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	887
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON_Bits',0,18,171,4,3
	.word	71744
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,18,174,4,16,4,11
	.byte	'EN',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,18,177,4,3
	.word	71985
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,18,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,18,183,4,3
	.word	72068
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,186,4,16,4,11
	.byte	'EN',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,189,4,3
	.word	72159
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,195,4,3
	.word	72250
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,18,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,18,202,4,3
	.word	72349
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,18,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,18,209,4,3
	.word	72456
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,18,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT_Bits',0,18,220,4,3
	.word	72563
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,18,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON_Bits',0,18,231,4,3
	.word	72717
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,18,234,4,16,4,11
	.byte	'ASI',0,4
	.word	887
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,18,238,4,3
	.word	72878
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,18,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	887
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	887
	.byte	15,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON_Bits',0,18,249,4,3
	.word	72976
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,18,252,4,16,4,11
	.byte	'Timer',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,18,255,4,3
	.word	73148
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,18,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR_Bits',0,18,133,5,3
	.word	73228
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,18,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	887
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	887
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	887
	.byte	3,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT_Bits',0,18,153,5,3
	.word	73301
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,18,156,5,16,4,11
	.byte	'T0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,18,167,5,3
	.word	73619
	.byte	12,18,175,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64906
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_A',0,18,180,5,3
	.word	73814
	.byte	12,18,183,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64967
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BIV',0,18,188,5,3
	.word	73873
	.byte	12,18,191,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65046
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BTV',0,18,196,5,3
	.word	73934
	.byte	12,18,199,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65132
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT',0,18,204,5,3
	.word	73995
	.byte	12,18,207,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65221
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL',0,18,212,5,3
	.word	74057
	.byte	12,18,215,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65367
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT',0,18,220,5,3
	.word	74120
	.byte	29
	.byte	'Ifx_CPU_CORE_ID',0,18,228,5,3
	.word	28741
	.byte	12,18,231,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65523
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L',0,18,236,5,3
	.word	74209
	.byte	12,18,239,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65616
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U',0,18,244,5,3
	.word	74272
	.byte	12,18,247,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65709
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID',0,18,252,5,3
	.word	74335
	.byte	12,18,255,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65816
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE',0,18,132,6,3
	.word	74399
	.byte	12,18,135,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65903
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT',0,18,140,6,3
	.word	74461
	.byte	12,18,143,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66057
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID',0,18,148,6,3
	.word	74524
	.byte	12,18,151,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66151
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_D',0,18,156,6,3
	.word	74588
	.byte	12,18,159,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66214
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DATR',0,18,164,6,3
	.word	74647
	.byte	12,18,167,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66432
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR',0,18,172,6,3
	.word	74709
	.byte	12,18,175,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66647
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR',0,18,180,6,3
	.word	74772
	.byte	12,18,183,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66741
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0',0,18,188,6,3
	.word	74836
	.byte	12,18,191,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66857
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2',0,18,196,6,3
	.word	74899
	.byte	12,18,199,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66958
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCX',0,18,204,6,3
	.word	74962
	.byte	12,18,207,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67051
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD',0,18,212,6,3
	.word	75023
	.byte	12,18,215,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67131
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR',0,18,220,6,3
	.word	75086
	.byte	12,18,223,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67200
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR',0,18,228,6,3
	.word	75149
	.byte	12,18,231,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67429
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DMS',0,18,236,6,3
	.word	75212
	.byte	12,18,239,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67522
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L',0,18,244,6,3
	.word	75273
	.byte	12,18,247,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67617
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U',0,18,252,6,3
	.word	75336
	.byte	12,18,255,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67712
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE',0,18,132,7,3
	.word	75399
	.byte	12,18,135,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67802
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE',0,18,140,7,3
	.word	75461
	.byte	12,18,143,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67892
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR',0,18,148,7,3
	.word	75523
	.byte	12,18,151,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68216
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT',0,18,156,7,3
	.word	75585
	.byte	12,18,159,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68370
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FCX',0,18,164,7,3
	.word	75648
	.byte	12,18,167,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68476
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,18,172,7,3
	.word	75709
	.byte	12,18,175,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68825
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,18,180,7,3
	.word	75779
	.byte	12,18,183,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68985
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,18,188,7,3
	.word	75849
	.byte	12,18,191,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69066
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,18,196,7,3
	.word	75918
	.byte	12,18,199,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69153
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,18,204,7,3
	.word	75989
	.byte	12,18,207,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69240
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,18,212,7,3
	.word	76060
	.byte	12,18,215,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69327
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT',0,18,220,7,3
	.word	76131
	.byte	12,18,223,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69418
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICR',0,18,228,7,3
	.word	76193
	.byte	12,18,231,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69561
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ISP',0,18,236,7,3
	.word	76254
	.byte	12,18,239,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69627
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_LCX',0,18,244,7,3
	.word	76315
	.byte	12,18,247,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69733
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT',0,18,252,7,3
	.word	76376
	.byte	12,18,255,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69826
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT',0,18,132,8,3
	.word	76439
	.byte	12,18,135,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69919
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT',0,18,140,8,3
	.word	76502
	.byte	12,18,143,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70012
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PC',0,18,148,8,3
	.word	76565
	.byte	12,18,151,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70097
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0',0,18,156,8,3
	.word	76625
	.byte	12,18,159,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70213
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1',0,18,164,8,3
	.word	76688
	.byte	12,18,167,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70324
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2',0,18,172,8,3
	.word	76751
	.byte	12,18,175,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70425
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI',0,18,180,8,3
	.word	76814
	.byte	12,18,183,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70555
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR',0,18,188,8,3
	.word	76876
	.byte	12,18,191,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70624
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR',0,18,196,8,3
	.word	76939
	.byte	12,18,199,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70853
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0',0,18,204,8,3
	.word	77002
	.byte	12,18,207,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70966
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1',0,18,212,8,3
	.word	77064
	.byte	12,18,215,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71079
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2',0,18,220,8,3
	.word	77126
	.byte	12,18,223,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71170
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR',0,18,228,8,3
	.word	77188
	.byte	12,18,231,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71373
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSW',0,18,236,8,3
	.word	77250
	.byte	12,18,239,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71616
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN',0,18,244,8,3
	.word	77311
	.byte	12,18,247,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71744
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON',0,18,252,8,3
	.word	77374
	.byte	12,18,255,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71985
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA',0,18,132,9,3
	.word	77438
	.byte	12,18,135,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72068
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB',0,18,140,9,3
	.word	77508
	.byte	12,18,143,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72159
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,18,148,9,3
	.word	77578
	.byte	12,18,151,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72250
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,18,156,9,3
	.word	77652
	.byte	12,18,159,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72349
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,18,164,9,3
	.word	77726
	.byte	12,18,167,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72456
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,18,172,9,3
	.word	77796
	.byte	12,18,175,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72563
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT',0,18,180,9,3
	.word	77866
	.byte	12,18,183,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72717
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON',0,18,188,9,3
	.word	77929
	.byte	12,18,191,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72878
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI',0,18,196,9,3
	.word	77993
	.byte	12,18,199,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72976
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON',0,18,204,9,3
	.word	78059
	.byte	12,18,207,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73148
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER',0,18,212,9,3
	.word	78124
	.byte	12,18,215,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73228
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR',0,18,220,9,3
	.word	78191
	.byte	12,18,223,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73301
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT',0,18,228,9,3
	.word	78255
	.byte	12,18,231,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73619
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC',0,18,236,9,3
	.word	78319
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,18,247,9,25,8,13
	.byte	'L',0
	.word	74209
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	74272
	.byte	4,2,35,4,0,14
	.word	78385
	.byte	29
	.byte	'Ifx_CPU_CPR',0,18,251,9,3
	.word	78427
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,18,254,9,25,8,13
	.byte	'L',0
	.word	75273
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	75336
	.byte	4,2,35,4,0,14
	.word	78453
	.byte	29
	.byte	'Ifx_CPU_DPR',0,18,130,10,3
	.word	78495
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,18,133,10,25,16,13
	.byte	'LA',0
	.word	77726
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	77796
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	77578
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	77652
	.byte	4,2,35,12,0,14
	.word	78521
	.byte	29
	.byte	'Ifx_CPU_SPROT_RGN',0,18,139,10,3
	.word	78603
	.byte	18,12
	.word	78124
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,18,142,10,25,16,13
	.byte	'CON',0
	.word	78059
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	78635
	.byte	12,2,35,4,0,14
	.word	78644
	.byte	29
	.byte	'Ifx_CPU_TPS',0,18,146,10,3
	.word	78692
	.byte	10
	.byte	'_Ifx_CPU_TR',0,18,149,10,25,8,13
	.byte	'EVT',0
	.word	78255
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	78191
	.byte	4,2,35,4,0,14
	.word	78718
	.byte	29
	.byte	'Ifx_CPU_TR',0,18,153,10,3
	.word	78763
	.byte	18,176,32
	.word	488
	.byte	19,175,32,0,18,208,223,1
	.word	488
	.byte	19,207,223,1,0,18,248,1
	.word	488
	.byte	19,247,1,0,18,244,29
	.word	488
	.byte	19,243,29,0,18,188,3
	.word	488
	.byte	19,187,3,0,18,232,3
	.word	488
	.byte	19,231,3,0,18,252,23
	.word	488
	.byte	19,251,23,0,18,228,63
	.word	488
	.byte	19,227,63,0,18,128,1
	.word	78453
	.byte	19,15,0,14
	.word	78878
	.byte	18,64
	.word	78385
	.byte	19,7,0,14
	.word	78893
	.byte	18,192,31
	.word	488
	.byte	19,191,31,0,18,16
	.word	74399
	.byte	19,3,0,18,16
	.word	75399
	.byte	19,3,0,18,16
	.word	75461
	.byte	19,3,0,18,208,7
	.word	488
	.byte	19,207,7,0,14
	.word	78644
	.byte	18,240,23
	.word	488
	.byte	19,239,23,0,18,64
	.word	78718
	.byte	19,7,0,14
	.word	78972
	.byte	18,192,23
	.word	488
	.byte	19,191,23,0,18,232,1
	.word	488
	.byte	19,231,1,0,18,180,1
	.word	488
	.byte	19,179,1,0,18,172,1
	.word	488
	.byte	19,171,1,0,18,64
	.word	74588
	.byte	19,15,0,18,64
	.word	488
	.byte	19,63,0,18,64
	.word	73814
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,18,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	78788
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	77311
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	78799
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	77993
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	78812
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	77002
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	77064
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	77126
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	78823
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	74899
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	5307
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	77374
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	75523
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3488
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	74647
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	75023
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	75086
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	75149
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4678
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	74836
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	78834
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	77188
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	76688
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	76751
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	76625
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	76876
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	76939
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	78845
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	74120
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	78856
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	75709
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	75849
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	75779
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3488
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	75918
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	75989
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	76060
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	78867
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	78888
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	17626
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	78902
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	78907
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	78918
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	78927
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	78936
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	78945
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	78956
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	78961
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	78981
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	78986
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	74057
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	73995
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	76131
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	76376
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	76439
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	76502
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	78997
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	74709
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3488
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	75585
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	74461
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	77866
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	11182
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	78319
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5647
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	75212
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	74962
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	74772
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	79008
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	76814
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	77250
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	76565
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	5307
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	77929
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	74335
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	28741
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	73873
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	73934
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	76254
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	76193
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	5307
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	75648
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	76315
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	33436
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	74524
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	79019
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	79030
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	79039
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	79048
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	79039
	.byte	64,4,35,192,255,3,0,14
	.word	79057
	.byte	29
	.byte	'Ifx_CPU',0,18,130,11,3
	.word	80848
	.byte	29
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	2014
	.byte	29
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	2096
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,23,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_STM_ACCEN0_Bits',0,23,79,3
	.word	80917
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,23,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN1_Bits',0,23,85,3
	.word	81474
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,23,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CAP_Bits',0,23,91,3
	.word	81551
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,23,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CAPSV_Bits',0,23,97,3
	.word	81623
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,23,100,16,4,11
	.byte	'DISR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_STM_CLC_Bits',0,23,107,3
	.word	81699
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,23,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	488
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	488
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_STM_CMCON_Bits',0,23,120,3
	.word	81840
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,23,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CMP_Bits',0,23,126,3
	.word	82058
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,23,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	465
	.byte	25,0,2,35,0,0,29
	.byte	'Ifx_STM_ICR_Bits',0,23,139,1,3
	.word	82125
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,23,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_STM_ID_Bits',0,23,147,1,3
	.word	82328
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,23,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_STM_ISCR_Bits',0,23,157,1,3
	.word	82435
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,23,160,1,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_STM_KRST0_Bits',0,23,165,1,3
	.word	82586
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,23,168,1,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_STM_KRST1_Bits',0,23,172,1,3
	.word	82697
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,23,175,1,16,4,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_STM_KRSTCLR_Bits',0,23,179,1,3
	.word	82789
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,23,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_STM_OCS_Bits',0,23,189,1,3
	.word	82885
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,23,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM0_Bits',0,23,195,1,3
	.word	83031
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,23,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM0SV_Bits',0,23,201,1,3
	.word	83103
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,23,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM1_Bits',0,23,207,1,3
	.word	83179
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,23,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM2_Bits',0,23,213,1,3
	.word	83251
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,23,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM3_Bits',0,23,219,1,3
	.word	83323
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,23,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM4_Bits',0,23,225,1,3
	.word	83396
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,23,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM5_Bits',0,23,231,1,3
	.word	83469
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,23,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM6_Bits',0,23,237,1,3
	.word	83542
	.byte	12,23,245,1,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80917
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN0',0,23,250,1,3
	.word	83615
	.byte	12,23,253,1,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81474
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN1',0,23,130,2,3
	.word	83679
	.byte	12,23,133,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81551
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CAP',0,23,138,2,3
	.word	83743
	.byte	12,23,141,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81623
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CAPSV',0,23,146,2,3
	.word	83804
	.byte	12,23,149,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81699
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CLC',0,23,154,2,3
	.word	83867
	.byte	12,23,157,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81840
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CMCON',0,23,162,2,3
	.word	83928
	.byte	12,23,165,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82058
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CMP',0,23,170,2,3
	.word	83991
	.byte	12,23,173,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82125
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ICR',0,23,178,2,3
	.word	84052
	.byte	12,23,181,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82328
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ID',0,23,186,2,3
	.word	84113
	.byte	12,23,189,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82435
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ISCR',0,23,194,2,3
	.word	84173
	.byte	12,23,197,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82586
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRST0',0,23,202,2,3
	.word	84235
	.byte	12,23,205,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82697
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRST1',0,23,210,2,3
	.word	84298
	.byte	12,23,213,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82789
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRSTCLR',0,23,218,2,3
	.word	84361
	.byte	12,23,221,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82885
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_OCS',0,23,226,2,3
	.word	84426
	.byte	12,23,229,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83031
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM0',0,23,234,2,3
	.word	84487
	.byte	12,23,237,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83103
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM0SV',0,23,242,2,3
	.word	84549
	.byte	12,23,245,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83179
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM1',0,23,250,2,3
	.word	84613
	.byte	12,23,253,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83251
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM2',0,23,130,3,3
	.word	84675
	.byte	12,23,133,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83323
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM3',0,23,138,3,3
	.word	84737
	.byte	12,23,141,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83396
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM4',0,23,146,3,3
	.word	84799
	.byte	12,23,149,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83469
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM5',0,23,154,3,3
	.word	84861
	.byte	12,23,157,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83542
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM6',0,23,162,3,3
	.word	84923
	.byte	15,7,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,29
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	84985
	.byte	21,7,160,1,9,6,13
	.byte	'counter',0
	.word	2251
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	488
	.byte	1,2,35,4,0,29
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	85074
	.byte	21,7,172,1,9,32,13
	.byte	'instruction',0
	.word	85074
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	85074
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	85074
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	85074
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	85074
	.byte	6,2,35,24,0,29
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	85140
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,24,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,24,79,3
	.word	85258
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,24,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,24,85,3
	.word	85819
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,24,88,16,4,11
	.byte	'SEL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,24,95,3
	.word	85900
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,24,98,16,4,11
	.byte	'VLD0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,24,111,3
	.word	86053
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,24,114,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,24,121,3
	.word	86301
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,24,124,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0_Bits',0,24,128,1,3
	.word	86447
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,24,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM1_Bits',0,24,136,1,3
	.word	86545
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,24,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM2_Bits',0,24,144,1,3
	.word	86661
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,24,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRD_Bits',0,24,153,1,3
	.word	86777
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,24,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRP_Bits',0,24,162,1,3
	.word	86917
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,24,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCW_Bits',0,24,171,1,3
	.word	87057
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,24,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1039
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FCON_Bits',0,24,193,1,3
	.word	87196
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,24,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FPRO_Bits',0,24,218,1,3
	.word	87558
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,24,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FSR_Bits',0,24,254,1,3
	.word	87999
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,24,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_ID_Bits',0,24,134,2,3
	.word	88605
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,24,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1039
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARD_Bits',0,24,147,2,3
	.word	88716
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,24,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARP_Bits',0,24,159,2,3
	.word	88930
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,24,162,2,16,4,11
	.byte	'L',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	488
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1039
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCOND_Bits',0,24,179,2,3
	.word	89117
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,24,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,24,188,2,3
	.word	89441
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,24,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,24,199,2,3
	.word	89584
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,24,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	488
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,24,219,2,3
	.word	89773
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,24,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,24,254,2,3
	.word	90136
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,24,129,3,16,4,11
	.byte	'S0L',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONP_Bits',0,24,160,3,3
	.word	90731
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,24,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,24,194,3,3
	.word	91255
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,24,197,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,24,201,3,3
	.word	91837
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,24,204,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,24,208,3,3
	.word	91939
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,24,211,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,24,215,3,3
	.word	92041
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,24,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	465
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD_Bits',0,24,222,3,3
	.word	92143
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,24,225,3,16,4,11
	.byte	'STRT',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_RRCT_Bits',0,24,236,3,3
	.word	92237
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,24,239,3,16,4,11
	.byte	'DATA',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0_Bits',0,24,242,3,3
	.word	92447
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,24,245,3,16,4,11
	.byte	'DATA',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1_Bits',0,24,248,3,3
	.word	92520
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,24,251,3,16,4,11
	.byte	'SEL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,24,130,4,3
	.word	92593
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,24,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,24,137,4,3
	.word	92748
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,24,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,24,147,4,3
	.word	92853
	.byte	12,24,155,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85258
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN0',0,24,160,4,3
	.word	93001
	.byte	12,24,163,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85819
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1',0,24,168,4,3
	.word	93067
	.byte	12,24,171,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85900
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG',0,24,176,4,3
	.word	93133
	.byte	12,24,179,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86053
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT',0,24,184,4,3
	.word	93201
	.byte	12,24,187,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86301
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_TOP',0,24,192,4,3
	.word	93270
	.byte	12,24,195,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86447
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0',0,24,200,4,3
	.word	93338
	.byte	12,24,203,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86545
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM1',0,24,208,4,3
	.word	93403
	.byte	12,24,211,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86661
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM2',0,24,216,4,3
	.word	93468
	.byte	12,24,219,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86777
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRD',0,24,224,4,3
	.word	93533
	.byte	12,24,227,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86917
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRP',0,24,232,4,3
	.word	93598
	.byte	12,24,235,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87057
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCW',0,24,240,4,3
	.word	93663
	.byte	12,24,243,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87196
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FCON',0,24,248,4,3
	.word	93727
	.byte	12,24,251,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87558
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FPRO',0,24,128,5,3
	.word	93791
	.byte	12,24,131,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87999
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FSR',0,24,136,5,3
	.word	93855
	.byte	12,24,139,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88605
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ID',0,24,144,5,3
	.word	93918
	.byte	12,24,147,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88716
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARD',0,24,152,5,3
	.word	93980
	.byte	12,24,155,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88930
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARP',0,24,160,5,3
	.word	94044
	.byte	12,24,163,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89117
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCOND',0,24,168,5,3
	.word	94108
	.byte	12,24,171,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89441
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG',0,24,176,5,3
	.word	94175
	.byte	12,24,179,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89584
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSM',0,24,184,5,3
	.word	94244
	.byte	12,24,187,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89773
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,24,192,5,3
	.word	94313
	.byte	12,24,195,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90136
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONOTP',0,24,200,5,3
	.word	94386
	.byte	12,24,203,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90731
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONP',0,24,208,5,3
	.word	94455
	.byte	12,24,211,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91255
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONWOP',0,24,216,5,3
	.word	94522
	.byte	12,24,219,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91837
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0',0,24,224,5,3
	.word	94591
	.byte	12,24,227,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91939
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1',0,24,232,5,3
	.word	94659
	.byte	12,24,235,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92041
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2',0,24,240,5,3
	.word	94727
	.byte	12,24,243,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92143
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD',0,24,248,5,3
	.word	94795
	.byte	12,24,251,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92237
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRCT',0,24,128,6,3
	.word	94859
	.byte	12,24,131,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92447
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0',0,24,136,6,3
	.word	94923
	.byte	12,24,139,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92520
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1',0,24,144,6,3
	.word	94987
	.byte	12,24,147,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92593
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG',0,24,152,6,3
	.word	95051
	.byte	12,24,155,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92748
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT',0,24,160,6,3
	.word	95119
	.byte	12,24,163,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92853
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_TOP',0,24,168,6,3
	.word	95188
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,24,179,6,25,12,13
	.byte	'CFG',0
	.word	93133
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	93201
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	93270
	.byte	4,2,35,8,0,14
	.word	95256
	.byte	29
	.byte	'Ifx_FLASH_CBAB',0,24,184,6,3
	.word	95319
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,24,187,6,25,12,13
	.byte	'CFG0',0
	.word	94591
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	94659
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	94727
	.byte	4,2,35,8,0,14
	.word	95348
	.byte	29
	.byte	'Ifx_FLASH_RDB',0,24,192,6,3
	.word	95412
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,24,195,6,25,12,13
	.byte	'CFG',0
	.word	95051
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	95119
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	95188
	.byte	4,2,35,8,0,14
	.word	95440
	.byte	29
	.byte	'Ifx_FLASH_UBAB',0,24,200,6,3
	.word	95503
	.byte	29
	.byte	'Ifx_P_ACCEN0_Bits',0,10,79,3
	.word	9060
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,10,85,3
	.word	8973
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,10,107,3
	.word	5316
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,10,115,3
	.word	3369
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,10,137,1,3
	.word	4364
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,10,150,1,3
	.word	3497
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,10,163,1,3
	.word	4144
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,10,176,1,3
	.word	3712
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,10,189,1,3
	.word	3927
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,10,197,1,3
	.word	8332
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,10,205,1,3
	.word	8456
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,10,215,1,3
	.word	8540
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,10,229,1,3
	.word	8720
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,10,240,1,3
	.word	6971
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,10,250,1,3
	.word	7495
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,10,133,2,3
	.word	7145
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,10,144,2,3
	.word	7319
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,10,166,2,3
	.word	7984
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,10,203,2,3
	.word	2798
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,10,213,2,3
	.word	6308
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,10,224,2,3
	.word	6796
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,10,235,2,3
	.word	6455
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,10,246,2,3
	.word	6624
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,10,140,3,3
	.word	7651
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,10,162,3,3
	.word	2482
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,10,180,3,3
	.word	6022
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,10,202,3,3
	.word	5656
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,10,223,3,3
	.word	4687
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,10,244,3,3
	.word	4991
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,10,129,4,3
	.word	9587
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,10,137,4,3
	.word	9020
	.byte	29
	.byte	'Ifx_P_ESR',0,10,145,4,3
	.word	5607
	.byte	29
	.byte	'Ifx_P_ID',0,10,153,4,3
	.word	3448
	.byte	29
	.byte	'Ifx_P_IN',0,10,161,4,3
	.word	4638
	.byte	29
	.byte	'Ifx_P_IOCR0',0,10,169,4,3
	.word	3672
	.byte	29
	.byte	'Ifx_P_IOCR12',0,10,177,4,3
	.word	4324
	.byte	29
	.byte	'Ifx_P_IOCR4',0,10,185,4,3
	.word	3887
	.byte	29
	.byte	'Ifx_P_IOCR8',0,10,193,4,3
	.word	4104
	.byte	29
	.byte	'Ifx_P_LPCR0',0,10,201,4,3
	.word	8416
	.byte	29
	.byte	'Ifx_P_LPCR1',0,10,210,4,3
	.word	8665
	.byte	29
	.byte	'Ifx_P_LPCR2',0,10,218,4,3
	.word	8924
	.byte	29
	.byte	'Ifx_P_OMCR',0,10,226,4,3
	.word	8292
	.byte	29
	.byte	'Ifx_P_OMCR0',0,10,234,4,3
	.word	7105
	.byte	29
	.byte	'Ifx_P_OMCR12',0,10,242,4,3
	.word	7611
	.byte	29
	.byte	'Ifx_P_OMCR4',0,10,250,4,3
	.word	7279
	.byte	29
	.byte	'Ifx_P_OMCR8',0,10,130,5,3
	.word	7455
	.byte	29
	.byte	'Ifx_P_OMR',0,10,138,5,3
	.word	3329
	.byte	29
	.byte	'Ifx_P_OMSR',0,10,146,5,3
	.word	7944
	.byte	29
	.byte	'Ifx_P_OMSR0',0,10,154,5,3
	.word	6415
	.byte	29
	.byte	'Ifx_P_OMSR12',0,10,162,5,3
	.word	6931
	.byte	29
	.byte	'Ifx_P_OMSR4',0,10,170,5,3
	.word	6584
	.byte	29
	.byte	'Ifx_P_OMSR8',0,10,178,5,3
	.word	6756
	.byte	29
	.byte	'Ifx_P_OUT',0,10,186,5,3
	.word	2758
	.byte	29
	.byte	'Ifx_P_PCSR',0,10,194,5,3
	.word	6268
	.byte	29
	.byte	'Ifx_P_PDISC',0,10,202,5,3
	.word	5982
	.byte	29
	.byte	'Ifx_P_PDR0',0,10,210,5,3
	.word	4951
	.byte	29
	.byte	'Ifx_P_PDR1',0,10,218,5,3
	.word	5267
	.byte	14
	.word	9627
	.byte	29
	.byte	'Ifx_P',0,10,139,6,3
	.word	96850
	.byte	29
	.byte	'IfxPort_InputMode',0,9,89,3
	.word	10240
	.byte	15,9,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,29
	.byte	'IfxPort_OutputIdx',0,9,130,1,3
	.word	96896
	.byte	15,9,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,29
	.byte	'IfxPort_OutputMode',0,9,138,1,3
	.word	97140
	.byte	15,9,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,9,158,1,3
	.word	97238
	.byte	29
	.byte	'IfxPort_State',0,9,178,1,3
	.word	10445
	.byte	21,9,190,1,9,8,13
	.byte	'port',0
	.word	10235
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	488
	.byte	1,2,35,4,0,29
	.byte	'IfxPort_Pin',0,9,194,1,3
	.word	97703
	.byte	14
	.word	62894
	.byte	3
	.word	97763
	.byte	21,25,74,15,20,13
	.byte	'module',0
	.word	97768
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	488
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	97703
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	29137
	.byte	1,2,35,16,0,20
	.word	97773
	.byte	29
	.byte	'IfxScu_Req_In',0,25,80,3
	.word	97843
	.byte	29
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,26,148,1,16
	.word	204
	.byte	21,26,212,5,9,8,13
	.byte	'value',0
	.word	2251
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2251
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_CcuconRegConfig',0,26,216,5,3
	.word	97910
	.byte	21,26,221,5,9,8,13
	.byte	'pDivider',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	488
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	488
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	261
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_InitialStepConfig',0,26,227,5,3
	.word	97981
	.byte	21,26,231,5,9,12,13
	.byte	'k2Step',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	261
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	97870
	.byte	4,2,35,8,0,29
	.byte	'IfxScuCcu_PllStepsConfig',0,26,236,5,3
	.word	98098
	.byte	3
	.word	201
	.byte	21,26,244,5,9,48,13
	.byte	'ccucon0',0
	.word	97910
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	97910
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	97910
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	97910
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	97910
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	97910
	.byte	8,2,35,40,0,29
	.byte	'IfxScuCcu_ClockDistributionConfig',0,26,252,5,3
	.word	98200
	.byte	21,26,128,6,9,8,13
	.byte	'value',0
	.word	2251
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2251
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,26,132,6,3
	.word	98352
	.byte	3
	.word	98098
	.byte	21,26,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	98428
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	97981
	.byte	8,2,35,8,0,29
	.byte	'IfxScuCcu_SysPllConfig',0,26,142,6,3
	.word	98433
	.byte	29
	.byte	'IfxDma_ChannelBusPriority',0,11,99,3
	.word	25022
	.byte	29
	.byte	'IfxDma_ChannelIncrementCircular',0,11,122,3
	.word	25490
	.byte	29
	.byte	'IfxDma_ChannelIncrementDirection',0,11,131,1,3
	.word	25396
	.byte	29
	.byte	'IfxDma_ChannelIncrementStep',0,11,146,1,3
	.word	25128
	.byte	29
	.byte	'IfxDma_ChannelInterruptControl',0,11,155,1,3
	.word	26657
	.byte	29
	.byte	'IfxDma_ChannelMove',0,11,170,1,3
	.word	24080
	.byte	29
	.byte	'IfxDma_ChannelMoveSize',0,11,183,1,3
	.word	24472
	.byte	29
	.byte	'IfxDma_ChannelOperationMode',0,11,192,1,3
	.word	24387
	.byte	29
	.byte	'IfxDma_ChannelPattern',0,11,207,1,3
	.word	24666
	.byte	29
	.byte	'IfxDma_ChannelRequestMode',0,11,225,1,3
	.word	24272
	.byte	29
	.byte	'IfxDma_ChannelRequestSource',0,11,234,1,3
	.word	24933
	.byte	29
	.byte	'IfxDma_ChannelShadow',0,11,254,1,3
	.word	26104
	.byte	15,11,128,2,9,1,16
	.byte	'IfxDma_HardwareResourcePartition_0',0,0,16
	.byte	'IfxDma_HardwareResourcePartition_1',0,1,16
	.byte	'IfxDma_HardwareResourcePartition_2',0,2,16
	.byte	'IfxDma_HardwareResourcePartition_3',0,3,0,29
	.byte	'IfxDma_HardwareResourcePartition',0,11,134,2,3
	.word	98973
	.byte	15,11,138,2,9,1,16
	.byte	'IfxDma_MoveEngine_0',0,0,16
	.byte	'IfxDma_MoveEngine_1',0,1,0,29
	.byte	'IfxDma_MoveEngine',0,11,142,2,3
	.word	99170
	.byte	15,11,147,2,9,1,16
	.byte	'IfxDma_SleepMode_enable',0,0,16
	.byte	'IfxDma_SleepMode_disable',0,1,0,29
	.byte	'IfxDma_SleepMode',0,11,151,2,3
	.word	99248
	.byte	29
	.byte	'IfxDma_Dma',0,14,210,3,3
	.word	23892
	.byte	29
	.byte	'IfxDma_Dma_Channel',0,14,223,3,3
	.word	23287
	.byte	29
	.byte	'IfxDma_Dma_ChannelConfig',0,14,135,4,3
	.word	26834
	.byte	29
	.byte	'IfxDma_Dma_Config',0,14,142,4,3
	.word	23917
	.byte	29
	.byte	'_iob_flag_t',0,27,82,25
	.word	1039
	.byte	7
	.byte	'char',0,1,6,29
	.byte	'int8',0,28,54,29
	.word	99463
	.byte	29
	.byte	'int16',0,28,55,29
	.word	28781
	.byte	29
	.byte	'int32',0,28,56,29
	.word	481
	.byte	29
	.byte	'int64',0,28,57,29
	.word	29031
	.byte	29
	.byte	'exti_pin_enum',0,17,61,2
	.word	28216
	.byte	29
	.byte	'exti_trigger_enum',0,17,70,2
	.word	28545
	.byte	18,192,2
	.word	20433
	.byte	19,9,0,14
	.word	99574
.L144:
	.byte	21,19,42,9,204,2,13
	.byte	'linked_list',0
	.word	99584
	.byte	192,2,2,35,0,13
	.byte	'channel',0
	.word	23287
	.byte	12,3,35,192,2,0,29
	.byte	'DMA_LINK',0,19,46,2
	.word	99589
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,38,0,73,19,0,0,21,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,22,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,23,5,0,73,19,0,0,24,46,1,49,19,0,0,25,5,0,49,19,0,0
	.byte	26,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,27,29,1,49,19,0,0,28,11,0,49,19,0,0,29,22,0
	.byte	3,8,58,15,59,15,57,15,73,19,0,0,30,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L47:
	.word	.L180-.L179
.L179:
	.half	3
	.word	.L182-.L181
.L181:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Dma\\Std\\IfxDma.h',0
	.byte	0,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxDma_cfg.h',0
	.byte	0,0,0
	.byte	'IfxDma_Dma.h',0,2,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_exti.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,3,0,0,0
.L182:
.L180:
	.sdecl	'.debug_info',debug,cluster('dma_init')
	.sect	'.debug_info'
.L48:
	.word	943
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L51,.L50
	.byte	2
	.word	.L44
	.byte	3
	.byte	'dma_init',0,1,70,7
	.word	.L65
	.byte	1,1,1
	.word	.L39,.L66,.L38
	.byte	4
	.byte	'dma_ch',0,1,70,34
	.word	.L67,.L68
	.byte	4
	.byte	'source_addr',0,1,70,49
	.word	.L69,.L70
	.byte	4
	.byte	'destination_addr',0,1,70,69
	.word	.L69,.L71
	.byte	4
	.byte	'exti_pin',0,1,70,101
	.word	.L72,.L73
	.byte	4
	.byte	'trigger',0,1,70,129,1
	.word	.L74,.L75
	.byte	4
	.byte	'dma_count',0,1,70,145,1
	.word	.L76,.L77
	.byte	5
	.word	.L39,.L66
	.byte	6
	.byte	'dmaChn',0,1,72,24
	.word	.L78,.L79
	.byte	5
	.word	.L80,.L66
	.byte	6
	.byte	'dmaConfig',0,1,76,30
	.word	.L81,.L82
	.byte	5
	.word	.L83,.L66
	.byte	6
	.byte	'dma',0,1,79,30
	.word	.L84,.L85
	.byte	5
	.word	.L86,.L66
	.byte	6
	.byte	'cfg',0,1,82,30
	.word	.L87,.L88
	.byte	5
	.word	.L89,.L66
	.byte	6
	.byte	'list_num',0,1,85,12
	.word	.L65,.L90
	.byte	6
	.byte	'i',0,1,85,22
	.word	.L65,.L91
	.byte	6
	.byte	'single_channel_dma_count',0,1,86,12
	.word	.L76,.L92
	.byte	7
	.word	.L93,.L94,.L12
	.byte	8
	.word	.L95,.L94,.L12
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L96,.L97
	.byte	0,0,7
	.word	.L93,.L98,.L15
	.byte	8
	.word	.L95,.L98,.L15
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L96,.L99
	.byte	0,0,7
	.word	.L93,.L100,.L18
	.byte	8
	.word	.L95,.L100,.L18
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L96,.L101
	.byte	0,0,7
	.word	.L93,.L102,.L24
	.byte	8
	.word	.L95,.L102,.L24
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L96,.L103
	.byte	0,0,7
	.word	.L93,.L104,.L28
	.byte	8
	.word	.L95,.L104,.L28
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L96,.L105
	.byte	0,0,7
	.word	.L93,.L106,.L32
	.byte	8
	.word	.L95,.L106,.L32
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L96,.L107
	.byte	0,0,7
	.word	.L108,.L109,.L110
	.byte	9
	.word	.L111,.L112
	.byte	10
	.word	.L113,.L114
	.byte	7
	.word	.L117,.L118,.L35
	.byte	9
	.word	.L119,.L120
	.byte	9
	.word	.L121,.L122
	.byte	11
	.word	.L123,.L118,.L35
	.byte	0,0,0,7
	.word	.L108,.L115,.L116
	.byte	9
	.word	.L111,.L112
	.byte	0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dma_init')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49
	.byte	16,2,6,0,0,10,11,1,49,16,85,6,0,0,11,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('dma_init')
	.sect	'.debug_line'
.L50:
	.word	.L184-.L183
.L183:
	.half	3
	.word	.L186-.L185
.L185:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'IfxDma_Dma.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Dma\\Std\\IfxDma.h',0
	.byte	0,0,0,0
.L186:
	.byte	5,7,7,0,5,2
	.word	.L39
	.byte	3,197,0,1,5,25,9
	.half	.L152-.L39
	.byte	3,4,1,5,34,9
	.half	.L80-.L152
	.byte	3,3,1,5,46,9
	.half	.L187-.L80
	.byte	1,5,28,9
	.half	.L83-.L187
	.byte	3,3,1,5,34,9
	.half	.L188-.L83
	.byte	1,5,35,9
	.half	.L86-.L188
	.byte	3,3,1,5,41,9
	.half	.L189-.L86
	.byte	1,5,5,9
	.half	.L89-.L189
	.byte	3,5,1,5,14,9
	.half	.L190-.L89
	.byte	3,3,1,5,42,9
	.half	.L153-.L190
	.byte	3,1,1,5,8,9
	.half	.L155-.L153
	.byte	3,1,1,5,5,9
	.half	.L191-.L155
	.byte	1,5,19,7,9
	.half	.L192-.L191
	.byte	3,2,1,5,50,9
	.half	.L4-.L192
	.byte	3,2,1,5,38,9
	.half	.L193-.L4
	.byte	1,5,45,9
	.half	.L194-.L193
	.byte	3,1,1,5,16,9
	.half	.L195-.L194
	.byte	1,5,67,7,9
	.half	.L196-.L195
	.byte	1,5,56,9
	.half	.L197-.L196
	.byte	1,5,17,7,9
	.half	.L198-.L197
	.byte	3,2,1,5,21,9
	.half	.L5-.L198
	.byte	3,2,1,5,13,9
	.half	.L156-.L5
	.byte	3,1,1,5,17,7,9
	.half	.L199-.L156
	.byte	3,2,1,5,19,9
	.half	.L3-.L199
	.byte	3,118,1,5,5,9
	.half	.L2-.L3
	.byte	3,16,1,5,43,7,9
	.half	.L200-.L2
	.byte	3,2,1,5,41,9
	.half	.L201-.L200
	.byte	1,5,43,9
	.half	.L202-.L201
	.byte	3,1,1,5,41,9
	.half	.L203-.L202
	.byte	1,5,43,9
	.half	.L204-.L203
	.byte	3,1,1,5,41,9
	.half	.L205-.L204
	.byte	1,5,68,9
	.half	.L206-.L205
	.byte	3,126,1,5,43,9
	.half	.L9-.L206
	.byte	3,6,1,5,41,9
	.half	.L207-.L9
	.byte	1,5,43,9
	.half	.L208-.L207
	.byte	3,1,1,5,41,9
	.half	.L209-.L208
	.byte	1,5,43,9
	.half	.L210-.L209
	.byte	3,1,1,4,2,5,19,9
	.half	.L94-.L210
	.byte	3,143,5,1,5,28,9
	.half	.L157-.L94
	.byte	3,1,1,5,5,9
	.half	.L158-.L157
	.byte	1,4,1,5,43,9
	.half	.L12-.L158
	.byte	3,240,122,1,5,41,9
	.half	.L13-.L12
	.byte	1,5,43,9
	.half	.L10-.L13
	.byte	3,3,1,5,41,9
	.half	.L211-.L10
	.byte	1,5,43,9
	.half	.L212-.L211
	.byte	3,1,1,5,41,9
	.half	.L213-.L212
	.byte	1,5,43,9
	.half	.L214-.L213
	.byte	3,1,1,5,41,9
	.half	.L215-.L214
	.byte	1,5,43,9
	.half	.L216-.L215
	.byte	3,2,1,4,2,5,19,9
	.half	.L98-.L216
	.byte	3,136,5,1,5,28,9
	.half	.L161-.L98
	.byte	3,1,1,5,5,9
	.half	.L162-.L161
	.byte	1,4,1,5,43,9
	.half	.L15-.L162
	.byte	3,247,122,1,5,41,9
	.half	.L16-.L15
	.byte	1,5,43,9
	.half	.L217-.L16
	.byte	3,1,1,5,41,9
	.half	.L218-.L217
	.byte	1,5,43,9
	.half	.L219-.L218
	.byte	3,1,1,5,41,9
	.half	.L220-.L219
	.byte	1,5,43,9
	.half	.L221-.L220
	.byte	3,2,1,5,41,9
	.half	.L222-.L221
	.byte	1,9
	.half	.L223-.L222
	.byte	3,2,1,5,43,9
	.half	.L224-.L223
	.byte	3,1,1,5,41,9
	.half	.L225-.L224
	.byte	1,5,43,9
	.half	.L226-.L225
	.byte	3,1,1,5,41,9
	.half	.L227-.L226
	.byte	1,5,43,9
	.half	.L228-.L227
	.byte	3,1,1,5,41,9
	.half	.L229-.L228
	.byte	1,5,43,9
	.half	.L230-.L229
	.byte	3,1,1,5,41,9
	.half	.L231-.L230
	.byte	1,5,43,9
	.half	.L232-.L231
	.byte	3,4,1,4,2,5,19,9
	.half	.L100-.L232
	.byte	3,250,4,1,5,28,9
	.half	.L167-.L100
	.byte	3,1,1,5,5,9
	.half	.L168-.L167
	.byte	1,4,1,5,43,9
	.half	.L18-.L168
	.byte	3,133,123,1,5,41,9
	.half	.L19-.L18
	.byte	1,9
	.half	.L233-.L19
	.byte	3,2,1,5,29,9
	.half	.L234-.L233
	.byte	3,2,1,5,38,9
	.half	.L235-.L234
	.byte	1,5,5,9
	.half	.L236-.L235
	.byte	3,2,1,5,11,7,9
	.half	.L237-.L236
	.byte	3,2,1,5,27,9
	.half	.L149-.L237
	.byte	3,1,1,5,43,9
	.half	.L22-.L149
	.byte	3,2,1,4,2,5,19,9
	.half	.L102-.L22
	.byte	3,239,4,1,5,28,9
	.half	.L172-.L102
	.byte	3,1,1,5,5,9
	.half	.L238-.L172
	.byte	1,4,1,5,43,9
	.half	.L24-.L238
	.byte	3,144,123,1,5,41,9
	.half	.L25-.L24
	.byte	1,5,31,9
	.half	.L239-.L25
	.byte	3,1,1,5,13,9
	.half	.L240-.L239
	.byte	1,5,43,7,9
	.half	.L241-.L240
	.byte	3,2,1,4,2,5,19,9
	.half	.L104-.L241
	.byte	3,236,4,1,5,28,9
	.half	.L174-.L104
	.byte	3,1,1,5,5,9
	.half	.L175-.L174
	.byte	1,4,1,5,43,9
	.half	.L28-.L175
	.byte	3,147,123,1,5,41,9
	.half	.L29-.L28
	.byte	1,5,122,9
	.half	.L242-.L29
	.byte	1,5,43,9
	.half	.L26-.L242
	.byte	3,4,1,4,2,5,19,9
	.half	.L106-.L26
	.byte	3,232,4,1,5,28,9
	.half	.L176-.L106
	.byte	3,1,1,5,5,9
	.half	.L243-.L176
	.byte	1,4,1,5,43,9
	.half	.L32-.L243
	.byte	3,151,123,1,5,41,9
	.half	.L33-.L32
	.byte	1,9
	.half	.L30-.L33
	.byte	3,2,1,5,78,9
	.half	.L244-.L30
	.byte	3,2,1,5,53,9
	.half	.L245-.L244
	.byte	1,5,78,9
	.half	.L246-.L245
	.byte	1,5,84,9
	.half	.L247-.L246
	.byte	1,5,14,9
	.half	.L248-.L247
	.byte	3,1,1,5,27,9
	.half	.L21-.L248
	.byte	3,114,1,5,31,7,9
	.half	.L20-.L21
	.byte	3,18,1,5,44,9
	.half	.L249-.L20
	.byte	1,4,3,5,40,9
	.half	.L109-.L249
	.byte	3,187,4,1,5,54,9
	.half	.L250-.L109
	.byte	1,4,4,5,5,9
	.half	.L118-.L250
	.byte	3,152,6,1,5,37,7,9
	.half	.L34-.L118
	.byte	3,2,1,5,5,9
	.half	.L251-.L34
	.byte	1,4,3,9
	.half	.L35-.L251
	.byte	3,230,121,1,4,1,5,56,9
	.half	.L36-.L35
	.byte	3,197,123,1,5,62,9
	.half	.L252-.L36
	.byte	1,5,5,9
	.half	.L116-.L252
	.byte	3,2,1,5,1,9
	.half	.L37-.L116
	.byte	3,1,1,7,9
	.half	.L52-.L37
	.byte	0,1,1
.L184:
	.sdecl	'.debug_ranges',debug,cluster('dma_init')
	.sect	'.debug_ranges'
.L51:
	.word	-1,.L39,0,.L52-.L39,0,0
.L114:
	.word	-1,.L39,.L109-.L39,.L110-.L39,.L115-.L39,.L116-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('dma_disable')
	.sect	'.debug_info'
.L53:
	.word	315
	.half	3
	.word	.L54
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L56,.L55
	.byte	2
	.word	.L44
	.byte	3
	.byte	'dma_disable',0,1,182,1,6,1,1,1
	.word	.L41,.L124,.L40
	.byte	4
	.byte	'dma_ch',0,1,182,1,36
	.word	.L67,.L125
	.byte	5
	.word	.L41,.L124
	.byte	6
	.word	.L126,.L127,.L128
	.byte	7
	.word	.L129,.L130
	.byte	7
	.word	.L131,.L132
	.byte	8
	.word	.L133,.L127,.L128
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dma_disable')
	.sect	'.debug_abbrev'
.L54:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('dma_disable')
	.sect	'.debug_line'
.L55:
	.word	.L254-.L253
.L253:
	.half	3
	.word	.L256-.L255
.L255:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Dma\\Std\\IfxDma.h',0
	.byte	0,0,0,0
.L256:
	.byte	5,39,7,0,5,2
	.word	.L41
	.byte	3,183,1,1,4,2,5,13,9
	.half	.L127-.L41
	.byte	3,218,8,1,5,26,9
	.half	.L257-.L127
	.byte	1,5,31,9
	.half	.L258-.L257
	.byte	1,4,1,5,1,9
	.half	.L128-.L258
	.byte	3,167,119,1,7,9
	.half	.L57-.L128
	.byte	0,1,1
.L254:
	.sdecl	'.debug_ranges',debug,cluster('dma_disable')
	.sect	'.debug_ranges'
.L56:
	.word	-1,.L41,0,.L57-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('dma_enable')
	.sect	'.debug_info'
.L58:
	.word	314
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L61,.L60
	.byte	2
	.word	.L44
	.byte	3
	.byte	'dma_enable',0,1,195,1,6,1,1,1
	.word	.L43,.L134,.L42
	.byte	4
	.byte	'dma_ch',0,1,195,1,35
	.word	.L67,.L135
	.byte	5
	.word	.L43,.L134
	.byte	6
	.word	.L136,.L137,.L138
	.byte	7
	.word	.L139,.L140
	.byte	7
	.word	.L141,.L142
	.byte	8
	.word	.L143,.L137,.L138
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dma_enable')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('dma_enable')
	.sect	'.debug_line'
.L60:
	.word	.L260-.L259
.L259:
	.half	3
	.word	.L262-.L261
.L261:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Dma\\Std\\IfxDma.h',0
	.byte	0,0,0,0
.L262:
	.byte	5,38,7,0,5,2
	.word	.L43
	.byte	3,196,1,1,4,2,5,13,9
	.half	.L137-.L43
	.byte	3,249,8,1,5,26,9
	.half	.L263-.L137
	.byte	1,5,31,9
	.half	.L264-.L263
	.byte	1,4,1,5,1,9
	.half	.L138-.L264
	.byte	3,136,119,1,7,9
	.half	.L62-.L138
	.byte	0,1,1
.L260:
	.sdecl	'.debug_ranges',debug,cluster('dma_enable')
	.sect	'.debug_ranges'
.L61:
	.word	-1,.L43,0,.L62-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('dma_link_list')
	.sect	'.debug_info'
.L63:
	.word	228
	.half	3
	.word	.L64
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L44
	.byte	3
	.byte	'dma_link_list',0,19,50,25
	.word	.L144
	.byte	1,5,3
	.word	dma_link_list
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dma_link_list')
	.sect	'.debug_abbrev'
.L64:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('dma_disable')
	.sect	'.debug_loc'
.L132:
	.word	0,0
.L130:
	.word	0,0
.L125:
	.word	-1,.L41,0,.L124-.L41
	.half	1
	.byte	84
	.word	0,0
.L40:
	.word	-1,.L41,0,.L124-.L41
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dma_enable')
	.sect	'.debug_loc'
.L142:
	.word	0,0
.L140:
	.word	0,0
.L135:
	.word	-1,.L43,0,.L134-.L43
	.half	1
	.byte	84
	.word	0,0
.L42:
	.word	-1,.L43,0,.L134-.L43
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dma_init')
	.sect	'.debug_loc'
.L88:
	.word	-1,.L39,0,.L66-.L39
	.half	2
	.byte	145,68
	.word	0,0
.L112:
	.word	0,0
.L122:
	.word	0,0
.L71:
	.word	-1,.L39,0,.L80-.L39
	.half	1
	.byte	101
	.word	.L151-.L39,.L66-.L39
	.half	1
	.byte	109
	.word	.L165-.L39,.L166-.L39
	.half	1
	.byte	95
	.word	.L169-.L39,.L170-.L39
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L85:
	.word	-1,.L39,0,.L66-.L39
	.half	2
	.byte	145,64
	.word	0,0
.L120:
	.word	0,0
.L79:
	.word	-1,.L39,0,.L66-.L39
	.half	3
	.byte	145,176,127
	.word	0,0
.L82:
	.word	-1,.L39,0,.L66-.L39
	.half	3
	.byte	145,188,127
	.word	0,0
.L68:
	.word	-1,.L39,0,.L146-.L39
	.half	1
	.byte	84
	.word	.L148-.L39,.L149-.L39
	.half	1
	.byte	88
	.word	0,0
.L77:
	.word	-1,.L39,0,.L80-.L39
	.half	1
	.byte	87
	.word	.L152-.L39,.L66-.L39
	.half	1
	.byte	89
	.word	0,0
.L38:
	.word	-1,.L39,0,.L145-.L39
	.half	2
	.byte	138,0
	.word	.L145-.L39,.L66-.L39
	.half	3
	.byte	138,208,0
	.word	.L66-.L39,.L66-.L39
	.half	2
	.byte	138,0
	.word	0,0
.L73:
	.word	-1,.L39,0,.L147-.L39
	.half	1
	.byte	85
	.word	.L146-.L39,.L80-.L39
	.half	1
	.byte	84
	.word	0,0
.L91:
	.word	-1,.L39,.L149-.L39,.L171-.L39
	.half	1
	.byte	88
	.word	.L21-.L39,.L20-.L39
	.half	1
	.byte	88
	.word	0,0
.L90:
	.word	-1,.L39,.L153-.L39,.L154-.L39
	.half	1
	.byte	92
	.word	.L156-.L39,.L66-.L39
	.half	1
	.byte	92
	.word	.L178-.L39,.L66-.L39
	.half	1
	.byte	82
	.word	0,0
.L105:
	.word	-1,.L39,.L174-.L39,.L175-.L39
	.half	1
	.byte	95
	.word	0,0
.L101:
	.word	-1,.L39,.L167-.L39,.L168-.L39
	.half	1
	.byte	95
	.word	0,0
.L103:
	.word	-1,.L39,.L172-.L39,.L173-.L39
	.half	1
	.byte	95
	.word	0,0
.L107:
	.word	-1,.L39,.L176-.L39,.L177-.L39
	.half	1
	.byte	95
	.word	0,0
.L97:
	.word	-1,.L39,.L157-.L39,.L158-.L39
	.half	1
	.byte	95
	.word	0,0
.L99:
	.word	-1,.L39,.L161-.L39,.L162-.L39
	.half	1
	.byte	95
	.word	0,0
.L92:
	.word	-1,.L39,.L155-.L39,.L66-.L39
	.half	1
	.byte	90
	.word	0,0
.L70:
	.word	-1,.L39,0,.L80-.L39
	.half	1
	.byte	100
	.word	.L150-.L39,.L66-.L39
	.half	1
	.byte	108
	.word	.L159-.L39,.L160-.L39
	.half	1
	.byte	95
	.word	.L163-.L39,.L164-.L39
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L75:
	.word	-1,.L39,0,.L80-.L39
	.half	1
	.byte	86
	.word	.L147-.L39,.L80-.L39
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L265:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('dma_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L265,.L39,.L66-.L39
	.byte	4
	.word	(.L145-.L39)/2
	.byte	19,208,0,22,26,4,19,138,208,0,4
	.word	(.L66-.L145)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('dma_disable')
	.sect	'.debug_frame'
	.word	24
	.word	.L265,.L41,.L124-.L41
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('dma_enable')
	.sect	'.debug_frame'
	.word	24
	.word	.L265,.L43,.L134-.L43
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	; Module end
