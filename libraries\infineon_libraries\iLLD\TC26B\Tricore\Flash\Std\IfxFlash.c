/**
 * \file IfxFlash.c
 * \brief FLASH  basic functionality
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2019 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 *
 */

/******************************************************************************/
/*----------------------------------Includes----------------------------------*/
/******************************************************************************/

#include "IfxFlash.h"

/******************************************************************************/
/*-------------------------Function Implementations---------------------------*/
/******************************************************************************/

void IfxFlash_clearCorrectableErrorTracking(IfxFlash_PortId portId)
{
    MODULE_FLASH0.CBAB[portId].CFG.B.CLR = 1;
}


void IfxFlash_clearUncorrectableErrorTracking(IfxFlash_PortId portId)
{
    MODULE_FLASH0.UBAB[portId].CFG.B.CLR = 1;
}


void IfxFlash_disableCorrectableErrorTracking(IfxFlash_PortId portId, boolean disable)
{
    MODULE_FLASH0.CBAB[portId].CFG.B.DIS = disable;
}


void IfxFlash_disableUncorrectableErrorTracking(IfxFlash_PortId portId, boolean disable)
{
    MODULE_FLASH0.UBAB[portId].CFG.B.DIS = disable;
}


void IfxFlash_disableWriteProtection(uint32 flash, IfxFlash_UcbType ucb, uint32 *password)
{
    IFX_UNUSED_PARAMETER(flash);
    volatile uint32 *addr1 = (volatile uint32 *)(IFXFLASH_CMD_BASE_ADDRESS | 0x553c);
    uint32           i;

    *addr1 = ucb;

    for (i = 0; i < 8; i++)
    {
        *addr1 = password[i];
    }

    __dsync();
}


uint32 IfxFlash_getTrackedCorrectableErrors(IfxFlash_PortId portId, IfxFlash_ErrorTracking_Address *trackedFlashAdresses)
{
    uint32 numErrors    = 0;
    uint32 fillingLevel = MODULE_FLASH0.CBAB[portId].STAT.U;

    int    i;

    for (i = 0;
         i < IFXFLASH_ERROR_TRACKING_MAX_CORRECTABLE_ERRORS &&
         (fillingLevel & (1 << i)) != 0;
         ++i)
    {
        Ifx_FLASH_CBAB_TOP top;
        top.U = MODULE_FLASH0.CBAB[portId].TOP.U;

        if (top.B.VLD)
        {
            trackedFlashAdresses[numErrors].address   = 0xa0000000 | (top.B.ADDR << 5);
            trackedFlashAdresses[numErrors].errorType = (IfxFlash_ErrorTracking)top.B.ERR;
            ++numErrors;
        }

        // clear entry
        MODULE_FLASH0.CBAB[portId].TOP.U = (((uint32)1) << 31);
    }

    return numErrors;
}


uint32 IfxFlash_getTrackedUncorrectableErrors(IfxFlash_PortId portId, IfxFlash_ErrorTracking_Address *trackedFlashAdresses)
{
    uint32 numErrors    = 0;
    uint32 fillingLevel = MODULE_FLASH0.UBAB[portId].STAT.U;

    int    i;

    for (i = 0;
         i < IFXFLASH_ERROR_TRACKING_MAX_UNCORRECTABLE_ERRORS &&
         (fillingLevel & (1 << i)) != 0;
         ++i)
    {
        Ifx_FLASH_UBAB_TOP top;
        top.U = MODULE_FLASH0.UBAB[portId].TOP.U;

        if (top.B.VLD)
        {
            trackedFlashAdresses[numErrors].address   = 0xa0000000 | (top.B.ADDR << 5);
            trackedFlashAdresses[numErrors].errorType = (IfxFlash_ErrorTracking)top.B.ERR;
            ++numErrors;
        }

        // clear entry
        MODULE_FLASH0.UBAB[portId].TOP.U = (((uint32)1) << 31);
    }

    return numErrors;
}


void IfxFlash_selectCorrectableErrorTracking(IfxFlash_PortId portId, IfxFlash_ErrorTracking errorTracking)
{
    IFX_ASSERT(IFX_VERBOSE_LEVEL_ERROR,
        errorTracking == IfxFlash_ErrorTracking_none ||
        errorTracking == IfxFlash_ErrorTracking_correctedSingleBitError ||
        errorTracking == IfxFlash_ErrorTracking_correctedDoubleBitError ||
        errorTracking == IfxFlash_ErrorTracking_correctedSingleOrDoubleBitError);

    MODULE_FLASH0.CBAB[portId].CFG.B.SEL = errorTracking;
}


void IfxFlash_selectUncorrectableErrorTracking(IfxFlash_PortId portId, IfxFlash_ErrorTracking errorTracking)
{
    IFX_ASSERT(IFX_VERBOSE_LEVEL_ERROR,
        errorTracking == IfxFlash_ErrorTracking_none ||
        errorTracking == IfxFlash_ErrorTracking_uncorrectableMultiBitError);

    MODULE_FLASH0.UBAB[portId].CFG.B.SEL = errorTracking;
}
