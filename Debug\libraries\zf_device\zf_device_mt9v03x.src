	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc20868a --dep-file=zf_device_mt9v03x.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_mt9v03x.src ../libraries/zf_device/zf_device_mt9v03x.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_mt9v03x.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_mt9v03x.mt9v03x_uart_handler',code,cluster('mt9v03x_uart_handler')
	.sect	'.text.zf_device_mt9v03x.mt9v03x_uart_handler'
	.align	2
	
; Function mt9v03x_uart_handler
.L38:
mt9v03x_uart_handler:	.type	func
	sub.a	a10,#8
.L195:
	mov	d15,#0
.L378:
	st.b	[a10],d15
.L379:
	mov	d4,#1
.L380:
	lea	a4,[a10]0
	call	uart_query_byte
.L381:
	ld.bu	d0,[a10]
.L382:
	mov	d15,#165
.L383:
	jne	d15,d0,.L2
.L384:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
	call	fifo_clear
.L2:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
.L385:
	ld.bu	d4,[a10]
	call	fifo_write_element
.L386:
	ret
.L141:
	
__mt9v03x_uart_handler_function_end:
	.size	mt9v03x_uart_handler,__mt9v03x_uart_handler_function_end-mt9v03x_uart_handler
.L79:
	; End of function
	
	.sdecl	'.text.zf_device_mt9v03x.mt9v03x_vsync_handler',code,cluster('mt9v03x_vsync_handler')
	.sect	'.text.zf_device_mt9v03x.mt9v03x_vsync_handler'
	.align	2
	
; Function mt9v03x_vsync_handler
.L40:
mt9v03x_vsync_handler:	.type	func
	mov	d4,#3
	call	IfxScuEru_clearEventFlag
.L391:
	movh.a	a15,#@his(mt9v03x_dma_int_num)
	lea	a15,[a15]@los(mt9v03x_dma_int_num)
.L392:
	mov	d15,#0
.L393:
	st.b	[a15],d15
.L394:
	movh.a	a15,#@his(mt9v03x_dma_init_flag)
	lea	a15,[a15]@los(mt9v03x_dma_init_flag)
	ld.bu	d15,[a15]
.L395:
	jeq	d15,#0,.L3
.L396:
	movh.a	a15,#@his(mt9v03x_dma_init_flag)
	lea	a15,[a15]@los(mt9v03x_dma_init_flag)
.L397:
	mov	d15,#0
.L398:
	st.b	[a15],d15
.L399:
	movh.a	a15,#61441
.L400:
	mov	d0,#5
.L145:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L401:
	ld.bu	d15,[a15]7680
.L402:
	or	d15,#1
	st.b	[a2]7680,d15
.L146:
	mov	d4,#0
	call	IfxPort_getAddress
	lea	a4,[a2]36
.L403:
	movh.a	a15,#@his(mt9v03x_link_list_num)
	lea	a15,[a15]@los(mt9v03x_link_list_num)
.L404:
	mov	d4,#5
.L405:
	movh.a	a5,#@his(mt9v03x_image)
	lea	a5,[a5]@los(mt9v03x_image)
.L406:
	mov	d5,#7
.L407:
	mov	d6,#0
.L408:
	mov	d7,#22560
	call	dma_init
.L409:
	st.b	[a15],d2
.L410:
	mov	d4,#5
	call	dma_enable
.L411:
	j	.L4
.L3:
	movh.a	a15,#@his(mt9v03x_link_list_num)
	lea	a15,[a15]@los(mt9v03x_link_list_num)
	ld.bu	d15,[a15]
.L412:
	jne	d15,#1,.L5
.L413:
	movh.a	a15,#@his(mt9v03x_image)
	lea	a15,[a15]@los(mt9v03x_image)
	mov.d	d15,a15
	insert	d15,d15,#0,#0,#28
	movh	d0,#53248
	jne	d15,d0,.L6
.L153:
	mfcr	d15,#65052
.L196:
	and	d15,#7
.L197:
	j	.L7
.L7:
	movh.a	a15,#@his(mt9v03x_image)
	lea	a15,[a15]@los(mt9v03x_image)
	mov.d	d0,a15
	insert	d0,d0,#0,#20,#12
	insert	d0,d0,#7,#28,#3
	movh	d1,#4096
	mul	d15,d1
	sub	d0,d15
	j	.L8
.L6:
	movh.a	a15,#@his(mt9v03x_image)
	lea	a15,[a15]@los(mt9v03x_image)
	mov.d	d0,a15
.L8:
	movh.a	a15,#61441
	mov	d15,#5
.L158:
	mul	d15,d15,#32
	addsc.a	a15,a15,d15,#0
.L414:
	st.w	[a15]8204,d0
.L5:
	mov	d4,#5
	call	dma_enable
.L4:
	movh.a	a15,#@his(mt9v03x_lost_flag)
	lea	a15,[a15]@los(mt9v03x_lost_flag)
.L415:
	mov	d15,#1
.L416:
	st.b	[a15],d15
.L417:
	ret
.L143:
	
__mt9v03x_vsync_handler_function_end:
	.size	mt9v03x_vsync_handler,__mt9v03x_vsync_handler_function_end-mt9v03x_vsync_handler
.L84:
	; End of function
	
	.sdecl	'.text.zf_device_mt9v03x.mt9v03x_dma_handler',code,cluster('mt9v03x_dma_handler')
	.sect	'.text.zf_device_mt9v03x.mt9v03x_dma_handler'
	.align	2
	
; Function mt9v03x_dma_handler
.L42:
mt9v03x_dma_handler:	.type	func
	movh.a	a15,#61441
	mov	d0,#5
.L168:
	mul	d15,d0,#32
	addsc.a	a2,a15,d15,#0
	mul	d15,d0,#32
	addsc.a	a15,a15,d15,#0
.L422:
	ld.bu	d15,[a15]8223
.L423:
	or	d15,#4
	st.b	[a2]8223,d15
.L169:
	movh.a	a15,#61441
.L424:
	mov	d15,#5
.L176:
	mul	d15,d15,#4
	addsc.a	a15,a15,d15,#0
.L425:
	ld.bu	d15,[a15]7680
	extr.u	d15,d15,#2,#1
.L426:
	ne	d15,d15,#0
.L427:
	j	.L9
.L9:
	jeq	d15,#0,.L10
.L428:
	movh.a	a15,#@his(mt9v03x_finish_flag)
	lea	a15,[a15]@los(mt9v03x_finish_flag)
.L429:
	mov	d15,#0
.L430:
	st.b	[a15],d15
.L431:
	mov	d4,#5
	call	dma_disable
.L432:
	movh.a	a15,#61441
.L433:
	mov	d0,#5
.L183:
	mul	d15,d0,#4
	addsc.a	a2,a15,d15,#0
	mul	d15,d0,#4
	addsc.a	a15,a15,d15,#0
.L434:
	ld.bu	d15,[a15]7682
.L435:
	or	d15,#4
	st.b	[a2]7682,d15
.L184:
	movh.a	a15,#@his(mt9v03x_dma_init_flag)
	lea	a15,[a15]@los(mt9v03x_dma_init_flag)
.L436:
	mov	d15,#1
.L437:
	st.b	[a15],d15
.L438:
	j	.L11
.L10:
	movh.a	a15,#@his(mt9v03x_dma_int_num)
	lea	a15,[a15]@los(mt9v03x_dma_int_num)
	movh.a	a2,#@his(mt9v03x_dma_int_num)
	lea	a2,[a2]@los(mt9v03x_dma_int_num)
	ld.bu	d15,[a2]
.L439:
	add	d15,#1
	st.b	[a15],d15
.L440:
	movh.a	a15,#@his(mt9v03x_dma_int_num)
	lea	a15,[a15]@los(mt9v03x_dma_int_num)
	ld.bu	d15,[a15]
.L441:
	movh.a	a15,#@his(mt9v03x_link_list_num)
	lea	a15,[a15]@los(mt9v03x_link_list_num)
	ld.bu	d0,[a15]
.L442:
	jlt.u	d15,d0,.L12
.L443:
	movh.a	a15,#@his(mt9v03x_dma_int_num)
	lea	a15,[a15]@los(mt9v03x_dma_int_num)
.L444:
	mov	d15,#0
.L445:
	st.b	[a15],d15
.L446:
	movh.a	a15,#@his(mt9v03x_lost_flag)
	lea	a15,[a15]@los(mt9v03x_lost_flag)
.L447:
	mov	d15,#0
.L448:
	st.b	[a15],d15
.L449:
	movh.a	a15,#@his(mt9v03x_finish_flag)
	lea	a15,[a15]@los(mt9v03x_finish_flag)
.L450:
	mov	d15,#1
.L451:
	st.b	[a15],d15
.L452:
	mov	d4,#5
	call	dma_disable
.L12:
.L11:
	ret
.L166:
	
__mt9v03x_dma_handler_function_end:
	.size	mt9v03x_dma_handler,__mt9v03x_dma_handler_function_end-mt9v03x_dma_handler
.L89:
	; End of function
	
	.sdecl	'.text.zf_device_mt9v03x.mt9v03x_get_version',code,cluster('mt9v03x_get_version')
	.sect	'.text.zf_device_mt9v03x.mt9v03x_get_version'
	.align	2
	
	.global	mt9v03x_get_version
; Function mt9v03x_get_version
.L44:
mt9v03x_get_version:	.type	func
	sub.a	a10,#8
.L198:
	mov	d9,#0
.L199:
	mov	d8,#0
.L201:
	mov	d15,#0
.L250:
	st.w	[a10]4,d15
.L251:
	mov	d15,#165
.L252:
	st.b	[a10],d15
.L253:
	mov	d15,#241
.L254:
	st.b	[a10]1,d15
.L255:
	mov	d15,#242
.L203:
	sha	d0,d15,#-8
.L256:
	st.b	[a10]2,d0
.L257:
	st.b	[a10]3,d15
.L258:
	mov	d4,#1
.L259:
	lea	a4,[a10]0
.L260:
	mov	d5,#4
	call	uart_write_buffer
.L13:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
	call	fifo_used
.L261:
	jlt.u	d2,#3,.L14
.L262:
	mov	d15,#3
.L263:
	st.w	[a10]4,d15
.L264:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
.L265:
	lea	a5,[a10]0
.L266:
	lea	a6,[a10]4
.L267:
	mov	d4,#0
	call	fifo_read_buffer
.L268:
	ld.bu	d15,[a10]1
.L269:
	sha	d8,d15,#8
.L202:
	ld.bu	d15,[a10]2
.L204:
	or	d8,d15
.L270:
	j	.L15
.L14:
	mov	d4,#1
	call	system_delay_ms
.L271:
	mov	d0,d9
	add	d9,#1
.L200:
	extr.u	d9,d9,#0,#16
.L205:
	mov	d15,#128
.L272:
	jlt.u	d0,d15,.L13
.L15:
	mov	d2,d8
.L206:
	j	.L16
.L16:
	ret
.L109:
	
__mt9v03x_get_version_function_end:
	.size	mt9v03x_get_version,__mt9v03x_get_version_function_end-mt9v03x_get_version
.L59:
	; End of function
	
	.sdecl	'.text.zf_device_mt9v03x.mt9v03x_set_exposure_time',code,cluster('mt9v03x_set_exposure_time')
	.sect	'.text.zf_device_mt9v03x.mt9v03x_set_exposure_time'
	.align	2
	
	.global	mt9v03x_set_exposure_time
; Function mt9v03x_set_exposure_time
.L46:
mt9v03x_set_exposure_time:	.type	func
	sub.a	a10,#8
.L207:
	mov	d9,d4
.L209:
	mov	d11,#0
.L210:
	movh.a	a15,#@his(mt9v03x_type)
	lea	a15,[a15]@los(mt9v03x_type)
	ld.bu	d15,[a15]
.L277:
	jne	d15,#0,.L17
.L278:
	mov	d4,#3
.L208:
	movh.a	a4,#@his(mt9v03x_vsync_handler)
	lea	a4,[a4]@los(mt9v03x_vsync_handler)
.L279:
	movh.a	a5,#@his(mt9v03x_dma_handler)
	lea	a5,[a5]@los(mt9v03x_dma_handler)
.L280:
	movh.a	a6,#@his(mt9v03x_uart_handler)
	lea	a6,[a6]@los(mt9v03x_uart_handler)
	call	set_camera_type
.L121:
	mov	d10,#0
.L212:
	mov	d15,#0
.L281:
	st.w	[a10]4,d15
.L282:
	mov	d15,#165
.L283:
	st.b	[a10],d15
.L284:
	mov	d15,#240
.L285:
	st.b	[a10]1,d15
.L286:
	mov	d8,d9
.L214:
	sha	d15,d9,#-8
.L287:
	st.b	[a10]2,d15
.L288:
	st.b	[a10]3,d9
.L289:
	mov	d4,#1
.L290:
	lea	a4,[a10]0
.L291:
	mov	d5,#4
	call	uart_write_buffer
.L18:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
	call	fifo_used
.L292:
	jlt.u	d2,#3,.L19
.L293:
	mov	d15,#3
.L294:
	st.w	[a10]4,d15
.L295:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
.L296:
	lea	a5,[a10]0
.L297:
	lea	a6,[a10]4
.L298:
	mov	d4,#0
	call	fifo_read_buffer
.L299:
	ld.bu	d15,[a10]1
.L300:
	sha	d8,d15,#8
.L215:
	ld.bu	d15,[a10]2
.L216:
	or	d8,d15
.L301:
	j	.L20
.L19:
	mov	d4,#1
	call	system_delay_ms
.L302:
	mov	d0,d10
	add	d10,#1
.L213:
	extr.u	d10,d10,#0,#16
.L217:
	mov	d15,#128
.L303:
	jlt.u	d0,d15,.L18
.L20:
	jne	d8,d9,.L21
.L304:
	mov	d15,#128
.L305:
	jlt.u	d10,d15,.L22
.L21:
	mov	d11,#1
.L22:
	mov	d4,#3
.L306:
	movh.a	a4,#@his(mt9v03x_vsync_handler)
	lea	a4,[a4]@los(mt9v03x_vsync_handler)
.L307:
	movh.a	a5,#@his(mt9v03x_dma_handler)
	lea	a5,[a5]@los(mt9v03x_dma_handler)
.L308:
	mov.a	a6,#0
	call	set_camera_type
.L122:
	j	.L23
.L17:
	mov	d4,d9
	call	mt9v03x_set_exposure_time_sccb
.L211:
	mov	d11,d2
.L23:
	mov	d2,d11
.L218:
	j	.L24
.L24:
	ret
.L118:
	
__mt9v03x_set_exposure_time_function_end:
	.size	mt9v03x_set_exposure_time,__mt9v03x_set_exposure_time_function_end-mt9v03x_set_exposure_time
.L64:
	; End of function
	
	.sdecl	'.text.zf_device_mt9v03x.mt9v03x_set_reg',code,cluster('mt9v03x_set_reg')
	.sect	'.text.zf_device_mt9v03x.mt9v03x_set_reg'
	.align	2
	
	.global	mt9v03x_set_reg
; Function mt9v03x_set_reg
.L48:
mt9v03x_set_reg:	.type	func
	sub.a	a10,#8
.L219:
	mov	e8,d5,d4
.L313:
	mov	d11,#0
.L221:
	movh.a	a15,#@his(mt9v03x_type)
	lea	a15,[a15]@los(mt9v03x_type)
	ld.bu	d0,[a15]
.L314:
	jne	d0,#0,.L25
.L315:
	mov	d4,#3
.L220:
	movh.a	a4,#@his(mt9v03x_vsync_handler)
	lea	a4,[a4]@los(mt9v03x_vsync_handler)
.L316:
	movh.a	a5,#@his(mt9v03x_dma_handler)
	lea	a5,[a5]@los(mt9v03x_dma_handler)
.L317:
	movh.a	a6,#@his(mt9v03x_uart_handler)
	lea	a6,[a6]@los(mt9v03x_uart_handler)
	call	set_camera_type
.L131:
	mov	d10,#0
.L223:
	mov	d15,#0
.L318:
	st.w	[a10]4,d15
.L319:
	mov	d15,#165
.L320:
	st.b	[a10],d15
.L321:
	mov	d15,#254
.L322:
	st.b	[a10]1,d15
.L225:
	sha	d15,d8,#-8
.L226:
	st.b	[a10]2,d15
.L227:
	st.b	[a10]3,d8
.L228:
	mov	d4,#1
.L323:
	lea	a4,[a10]0
.L324:
	mov	d5,#4
	call	uart_write_buffer
.L325:
	mov	d4,#10
	call	system_delay_ms
.L326:
	mov	d15,#165
.L327:
	st.b	[a10],d15
.L328:
	mov	d15,#255
.L329:
	st.b	[a10]1,d15
.L229:
	mov	d8,d9
.L231:
	sha	d15,d9,#-8
.L230:
	st.b	[a10]2,d15
.L233:
	st.b	[a10]3,d9
.L234:
	mov	d4,#1
.L330:
	lea	a4,[a10]0
.L331:
	mov	d5,#4
	call	uart_write_buffer
.L26:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
	call	fifo_used
.L332:
	jlt.u	d2,#3,.L27
.L333:
	mov	d15,#3
.L334:
	st.w	[a10]4,d15
.L335:
	movh.a	a4,#@his(camera_receiver_fifo)
	lea	a4,[a4]@los(camera_receiver_fifo)
.L336:
	lea	a5,[a10]0
.L337:
	lea	a6,[a10]4
.L338:
	mov	d4,#0
	call	fifo_read_buffer
.L339:
	ld.bu	d15,[a10]1
.L340:
	sha	d8,d15,#8
.L232:
	ld.bu	d15,[a10]2
.L235:
	or	d8,d15
.L341:
	j	.L28
.L27:
	mov	d4,#1
	call	system_delay_ms
.L342:
	mov	d0,d10
	add	d10,#1
.L224:
	extr.u	d10,d10,#0,#16
.L236:
	mov	d15,#128
.L343:
	jlt.u	d0,d15,.L26
.L28:
	jne	d8,d9,.L29
.L237:
	mov	d15,#128
.L344:
	jlt.u	d10,d15,.L30
.L29:
	mov	d11,#1
.L30:
	mov	d4,#3
.L345:
	movh.a	a4,#@his(mt9v03x_vsync_handler)
	lea	a4,[a4]@los(mt9v03x_vsync_handler)
.L346:
	movh.a	a5,#@his(mt9v03x_dma_handler)
	lea	a5,[a5]@los(mt9v03x_dma_handler)
.L347:
	mov.a	a6,#0
	call	set_camera_type
.L132:
	j	.L31
.L25:
	mov	e4,d9,d8
.L238:
	call	mt9v03x_set_reg_sccb
.L222:
	mov	d11,d2
.L31:
	mov	d2,d11
.L239:
	j	.L32
.L32:
	ret
.L127:
	
__mt9v03x_set_reg_function_end:
	.size	mt9v03x_set_reg,__mt9v03x_set_reg_function_end-mt9v03x_set_reg
.L69:
	; End of function
	
	.sdecl	'.text.zf_device_mt9v03x.mt9v03x_init',code,cluster('mt9v03x_init')
	.sect	'.text.zf_device_mt9v03x.mt9v03x_init'
	.align	2
	
	.global	mt9v03x_init
; Function mt9v03x_init
.L50:
mt9v03x_init:	.type	func
	sub.a	a10,#24
.L240:
	mov	d15,#0
.L33:
	mov	d4,#200
	call	system_delay_ms
.L352:
	movh.a	a15,#@his(mt9v03x_type)
	lea	a15,[a15]@los(mt9v03x_type)
.L353:
	mov	d0,#1
.L354:
	st.b	[a15],d0
.L355:
	mov	d4,#3
.L356:
	movh.a	a4,#@his(mt9v03x_vsync_handler)
	lea	a4,[a4]@los(mt9v03x_vsync_handler)
.L357:
	movh.a	a5,#@his(mt9v03x_dma_handler)
	lea	a5,[a5]@los(mt9v03x_dma_handler)
.L358:
	mov.a	a6,#0
	call	set_camera_type
.L359:
	lea	a4,[a10]0
.L360:
	mov	d4,#0
.L361:
	mov	d5,#800
.L362:
	mov	d6,#67
.L363:
	mov	d7,#66
	call	soft_iic_init
.L364:
	lea	a4,[a10]0
.L365:
	movh.a	a5,#@his(mt9v03x_set_confing_buffer)
	lea	a5,[a5]@los(mt9v03x_set_confing_buffer)
	call	mt9v03x_set_config_sccb
.L366:
	jeq	d2,#0,.L34
.L367:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov	d5,#465
	call	debug_log_handler
.L368:
	mov	d15,#1
.L369:
	j	.L35
.L34:
	mov	d4,#0
	call	IfxPort_getAddress
	lea	a4,[a2]36
.L370:
	movh.a	a15,#@his(mt9v03x_link_list_num)
	lea	a15,[a15]@los(mt9v03x_link_list_num)
.L371:
	movh.a	a5,#@his(mt9v03x_image)
	lea	a5,[a5]@los(mt9v03x_image)
.L372:
	mov	d4,#22560
	call	camera_init
.L373:
	st.b	[a15],d2
.L35:
	mov	d2,d15
.L241:
	j	.L36
.L36:
	ret
.L137:
	
__mt9v03x_init_function_end:
	.size	mt9v03x_init,__mt9v03x_init_function_end-mt9v03x_init
.L74:
	; End of function
	
	.sdecl	'.data.zf_device_mt9v03x.mt9v03x_finish_flag',data,cluster('mt9v03x_finish_flag')
	.sect	'.data.zf_device_mt9v03x.mt9v03x_finish_flag'
	.global	mt9v03x_finish_flag
mt9v03x_finish_flag:	.type	object
	.size	mt9v03x_finish_flag,1
	.space	1
	.sdecl	'.bss.zf_device_mt9v03x.mt9v03x_image',data,cluster('mt9v03x_image')
	.sect	'.bss.zf_device_mt9v03x.mt9v03x_image'
	.global	mt9v03x_image
	.align	4
mt9v03x_image:	.type	object
	.size	mt9v03x_image,22560
	.space	22560
	.sdecl	'.bss.zf_device_mt9v03x.mt9v03x_type',data,cluster('mt9v03x_type')
	.sect	'.bss.zf_device_mt9v03x.mt9v03x_type'
mt9v03x_type:	.type	object
	.size	mt9v03x_type,1
	.space	1
	.sdecl	'.data.zf_device_mt9v03x.timeout',data,cluster('timeout')
	.sect	'.data.zf_device_mt9v03x.timeout'
	.global	timeout
	.align	2
timeout:	.type	object
	.size	timeout,2
	.half	128
	.sdecl	'.data.zf_device_mt9v03x.mt9v03x_lost_flag',data,cluster('mt9v03x_lost_flag')
	.sect	'.data.zf_device_mt9v03x.mt9v03x_lost_flag'
	.global	mt9v03x_lost_flag
mt9v03x_lost_flag:	.type	object
	.size	mt9v03x_lost_flag,1
	.byte	1
	.sdecl	'.bss.zf_device_mt9v03x.mt9v03x_dma_int_num',data,cluster('mt9v03x_dma_int_num')
	.sect	'.bss.zf_device_mt9v03x.mt9v03x_dma_int_num'
	.global	mt9v03x_dma_int_num
mt9v03x_dma_int_num:	.type	object
	.size	mt9v03x_dma_int_num,1
	.space	1
	.sdecl	'.bss.zf_device_mt9v03x.mt9v03x_dma_init_flag',data,cluster('mt9v03x_dma_init_flag')
	.sect	'.bss.zf_device_mt9v03x.mt9v03x_dma_init_flag'
	.global	mt9v03x_dma_init_flag
mt9v03x_dma_init_flag:	.type	object
	.size	mt9v03x_dma_init_flag,1
	.space	1
	.sdecl	'.bss.zf_device_mt9v03x.mt9v03x_link_list_num',data,cluster('mt9v03x_link_list_num')
	.sect	'.bss.zf_device_mt9v03x.mt9v03x_link_list_num'
	.global	mt9v03x_link_list_num
mt9v03x_link_list_num:	.type	object
	.size	mt9v03x_link_list_num,1
	.space	1
	.sdecl	'.data.zf_device_mt9v03x.mt9v03x_set_confing_buffer',data,cluster('mt9v03x_set_confing_buffer')
	.sect	'.data.zf_device_mt9v03x.mt9v03x_set_confing_buffer'
	.align	2
mt9v03x_set_confing_buffer:	.type	object
	.size	mt9v03x_set_confing_buffer,40
	.space	4
	.half	1
	.space	2
	.half	2,30,3,50,4,188,5,120
	.half	6
	.space	2
	.half	7
	.space	2
	.half	8,32,9
	.space	2
	.sdecl	'.rodata.zf_device_mt9v03x..1.str',data,rom
	.sect	'.rodata.zf_device_mt9v03x..1.str'
.1.str:	.type	object
	.size	.1.str,31
	.byte	77,84,57,86,48,51,88,32
	.byte	83,67,67,66,32,115,101,116
	.byte	32,99,111,110,102,105,103,32
	.byte	101,114,114,111
	.byte	114,46
	.space	1
	.sdecl	'.rodata.zf_device_mt9v03x..2.str',data,rom
	.sect	'.rodata.zf_device_mt9v03x..2.str'
.2.str:	.type	object
	.size	.2.str,43
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,109,116,57,118,48,51,120
	.byte	46,99
	.space	1
	.calls	'__INDIRECT__','mt9v03x_uart_handler'
	.calls	'__INDIRECT__','mt9v03x_vsync_handler'
	.calls	'__INDIRECT__','mt9v03x_dma_handler'
	.calls	'mt9v03x_uart_handler','uart_query_byte'
	.calls	'mt9v03x_uart_handler','fifo_clear'
	.calls	'mt9v03x_uart_handler','fifo_write_element'
	.calls	'mt9v03x_vsync_handler','IfxScuEru_clearEventFlag'
	.calls	'mt9v03x_vsync_handler','IfxPort_getAddress'
	.calls	'mt9v03x_vsync_handler','dma_init'
	.calls	'mt9v03x_vsync_handler','dma_enable'
	.calls	'mt9v03x_dma_handler','dma_disable'
	.calls	'mt9v03x_get_version','uart_write_buffer'
	.calls	'mt9v03x_get_version','fifo_used'
	.calls	'mt9v03x_get_version','fifo_read_buffer'
	.calls	'mt9v03x_get_version','system_delay_ms'
	.calls	'mt9v03x_set_exposure_time','set_camera_type'
	.calls	'mt9v03x_set_exposure_time','uart_write_buffer'
	.calls	'mt9v03x_set_exposure_time','fifo_used'
	.calls	'mt9v03x_set_exposure_time','fifo_read_buffer'
	.calls	'mt9v03x_set_exposure_time','system_delay_ms'
	.calls	'mt9v03x_set_exposure_time','mt9v03x_set_exposure_time_sccb'
	.calls	'mt9v03x_set_reg','set_camera_type'
	.calls	'mt9v03x_set_reg','uart_write_buffer'
	.calls	'mt9v03x_set_reg','system_delay_ms'
	.calls	'mt9v03x_set_reg','fifo_used'
	.calls	'mt9v03x_set_reg','fifo_read_buffer'
	.calls	'mt9v03x_set_reg','mt9v03x_set_reg_sccb'
	.calls	'mt9v03x_init','system_delay_ms'
	.calls	'mt9v03x_init','set_camera_type'
	.calls	'mt9v03x_init','soft_iic_init'
	.calls	'mt9v03x_init','mt9v03x_set_config_sccb'
	.calls	'mt9v03x_init','debug_log_handler'
	.calls	'mt9v03x_init','IfxPort_getAddress'
	.calls	'mt9v03x_init','camera_init'
	.calls	'mt9v03x_uart_handler','',8
	.calls	'mt9v03x_vsync_handler','',0
	.calls	'mt9v03x_dma_handler','',0
	.calls	'mt9v03x_get_version','',8
	.calls	'mt9v03x_set_exposure_time','',8
	.calls	'mt9v03x_set_reg','',8
	.extern	debug_log_handler
	.extern	fifo_clear
	.extern	fifo_used
	.extern	fifo_write_element
	.extern	fifo_read_buffer
	.extern	IfxPort_getAddress
	.extern	soft_iic_init
	.extern	system_delay_ms
	.extern	IfxScuEru_clearEventFlag
	.extern	dma_init
	.extern	dma_disable
	.extern	dma_enable
	.extern	uart_write_buffer
	.extern	uart_query_byte
	.extern	set_camera_type
	.extern	camera_receiver_fifo
	.extern	camera_init
	.extern	mt9v03x_set_config_sccb
	.extern	mt9v03x_set_exposure_time_sccb
	.extern	mt9v03x_set_reg_sccb
	.extern	__INDIRECT__
	.calls	'mt9v03x_init','',24
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L52:
	.word	123058
	.half	3
	.word	.L53
	.byte	4
.L51:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L54
	.byte	2,1,1,3
	.word	205
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	208
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	253
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	265
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	492
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	492
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	508
	.byte	4,2,35,0,0
.L117:
	.byte	7
	.byte	'unsigned char',0,1,8
.L108:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	683
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	604
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	887
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1118
	.byte	4,2,35,8,0,14
	.word	1158
	.byte	3
	.word	1221
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1226
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	661
	.byte	6,0,4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,3,204,3,17,1,1,5
	.byte	'password',0,3,204,3,59
	.word	661
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1226
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	661
	.byte	6,0,4
	.byte	'IfxScuWdt_setSafetyEndinitInline',0,3,3,163,4,17,1,1,5
	.byte	'password',0,3,163,4,57
	.word	661
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	661
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1226
	.byte	6,0,8
	.byte	'IfxScuWdt_getSafetyWatchdogPasswordInline',0,3,3,253,3,19
	.word	661
	.byte	1,1,6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1636
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1952
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2523
	.byte	4,2,35,0,0,15,4
	.word	644
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2651
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2866
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3081
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3298
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3518
	.byte	4,2,35,0,0,15,24
	.word	644
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	644
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3841
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	644
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4145
	.byte	4,2,35,0,0,15,8
	.word	644
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4470
	.byte	4,2,35,0,0,15,12
	.word	644
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4810
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5176
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5609
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5778
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5950
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	661
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6125
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6299
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6649
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6805
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7138
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7486
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7610
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7694
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7874
	.byte	4,2,35,0,0,15,76
	.word	644
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8127
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8214
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1912
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2483
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2602
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2642
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2826
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3041
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3258
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3478
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2642
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3792
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3832
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4105
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4421
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4461
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4761
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4801
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5136
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5422
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4461
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5569
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5738
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5910
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6085
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6259
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6433
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6609
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6765
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7098
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7446
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4461
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7570
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7819
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8078
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8118
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8174
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8741
	.byte	4,3,35,252,1,0,14
	.word	8781
	.byte	3
	.word	9384
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9389
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	644
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9394
	.byte	6,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,5,202,4,17,1,1,5
	.byte	'port',0,5,202,4,49
	.word	9389
	.byte	5
	.byte	'pinIndex',0,5,202,4,61
	.word	644
	.byte	5
	.byte	'mode',0,5,202,4,90
	.word	9599
	.byte	5
	.byte	'index',0,5,202,4,114
	.word	9669
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9389
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	644
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9982
	.byte	6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,8,45,16,4,11
	.byte	'SRPN',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	644
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	644
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	644
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,8,70,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10163
	.byte	4,2,35,0,0,14
	.word	10453
	.byte	3
	.word	10492
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,7,250,1,17,1,1,5
	.byte	'src',0,7,250,1,60
	.word	10497
	.byte	6,0,17,10,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0
.L152:
	.byte	8
	.byte	'IfxCpu_getCoreId',0,3,9,133,6,22
	.word	10545
	.byte	1,1
.L154:
	.byte	6,0,17,10,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,9,141,6,31
	.word	10627
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,9,139,5,20
	.word	644
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,9,147,5,20
	.word	644
	.byte	1,1,19,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,9,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,9,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,9,168,7,17,1,1,5
	.byte	'enabled',0,9,168,7,50
	.word	644
	.byte	6,0
.L115:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,9,161,6,19
	.word	10949
	.byte	1,1,5
	.byte	'address',0,9,161,6,55
	.word	661
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,9,190,6,20
	.word	644
	.byte	1,1,5
	.byte	'address',0,9,190,6,70
	.word	661
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,9,172,8,17,1,1,5
	.byte	'address',0,9,172,8,56
	.word	10949
	.byte	5
	.byte	'count',0,9,172,8,72
	.word	10949
	.byte	19,6,0,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,11,226,8,20
	.word	265
	.byte	1,1,6,0,10
	.byte	'_Ifx_DMA_CLC_Bits',0,13,131,4,16,4,11
	.byte	'DISR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,13,160,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11220
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ID_Bits',0,13,155,4,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,13,184,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11377
	.byte	4,2,35,0,0,15,20
	.word	644
	.byte	16,19,0,10
	.byte	'_Ifx_DMA_MEMCON_Bits',0,13,163,4,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	2,30,2,35,0,11
	.byte	'INTERR',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'RMWERR',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'DATAERR',0,4
	.word	492
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	492
	.byte	1,24,2,35,0,11
	.byte	'PMIC',0,4
	.word	492
	.byte	1,23,2,35,0,11
	.byte	'ERRDIS',0,4
	.word	492
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	492
	.byte	22,0,2,35,0,0,12,13,192,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11508
	.byte	4,2,35,0,0,15,28
	.word	644
	.byte	16,27,0,10
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,128,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11785
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,136,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12355
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,13,88,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,144,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12445
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,13,125,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,152,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13015
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,13,131,1,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,160,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13105
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,13,168,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,168,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13676
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,13,174,1,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,176,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13767
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,13,211,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,184,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14338
	.byte	4,2,35,0,0,15,192,1
	.word	644
	.byte	16,191,1,0,10
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,13,233,1,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'ESER',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EDER',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	6,0,2,35,2,11
	.byte	'ERER',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'ELER',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,13,200,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14440
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,13,246,1,16,4,11
	.byte	'LEC',0,1
	.word	644
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	9,0,2,35,0,11
	.byte	'SER',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'DER',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	2,4,2,35,2,11
	.byte	'SPBER',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'SRIER',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'RAMER',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'SLLER',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'DLLER',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,13,208,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14664
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,13,217,1,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'CSER',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'CDER',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	2,4,2,35,2,11
	.byte	'CSPBER',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'CSRIER',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'CRAMER',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CSLLER',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'CDLLER',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	5,0,2,35,3,0,12,13,192,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14956
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,13,161,3,16,4,11
	.byte	'RS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	3,4,2,35,0,11
	.byte	'WS',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	661
	.byte	11,0,2,35,0,11
	.byte	'CH',0,1
	.word	644
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	9,0,2,35,2,0,12,13,216,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15239
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,13,193,2,16,4,11
	.byte	'RD00',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD01',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD02',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD03',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,248,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15419
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,13,202,2,16,4,11
	.byte	'RD10',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD11',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD12',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD13',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,128,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15554
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,13,211,2,16,4,11
	.byte	'RD20',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD21',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD22',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD23',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,136,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15689
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,13,220,2,16,4,11
	.byte	'RD30',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD31',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD32',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD33',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,144,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15824
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,13,229,2,16,4,11
	.byte	'RD40',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD41',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD42',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD43',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,152,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15959
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,13,238,2,16,4,11
	.byte	'RD50',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD51',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD52',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD53',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,160,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16094
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,13,247,2,16,4,11
	.byte	'RD60',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD61',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD62',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD63',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,168,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16229
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,13,128,3,16,4,11
	.byte	'RD70',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'RD71',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'RD72',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'RD73',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,176,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16364
	.byte	4,2,35,0,0,15,32
	.word	644
	.byte	16,31,0,10
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,13,137,3,16,4,11
	.byte	'RDCRC',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,184,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16508
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,13,149,3,16,4,11
	.byte	'SDCRC',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,200,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16599
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,13,143,3,16,4,11
	.byte	'SADR',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,192,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16690
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,13,187,2,16,4,11
	.byte	'DADR',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,240,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16779
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,13,135,2,16,4,11
	.byte	'SMF',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,13,216,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16868
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,13,155,2,16,4,11
	.byte	'TREL',0,2
	.word	661
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	644
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	644
	.byte	2,0,2,35,3,0,12,13,224,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17184
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,13,155,3,16,4,11
	.byte	'SHADR',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,208,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17463
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,13,171,2,16,4,11
	.byte	'TCOUNT',0,2
	.word	661
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,232,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17554
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME',0,13,144,8,25,112,13
	.byte	'SR',0
	.word	15379
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4801
	.byte	12,2,35,4,13
	.byte	'R0',0
	.word	15514
	.byte	4,2,35,16,13
	.byte	'R1',0
	.word	15649
	.byte	4,2,35,20,13
	.byte	'R2',0
	.word	15784
	.byte	4,2,35,24,13
	.byte	'R3',0
	.word	15919
	.byte	4,2,35,28,13
	.byte	'R4',0
	.word	16054
	.byte	4,2,35,32,13
	.byte	'R5',0
	.word	16189
	.byte	4,2,35,36,13
	.byte	'R6',0
	.word	16324
	.byte	4,2,35,40,13
	.byte	'R7',0
	.word	16459
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	16499
	.byte	32,2,35,48,13
	.byte	'RDCRC',0
	.word	16559
	.byte	4,2,35,80,13
	.byte	'SDCRC',0
	.word	16650
	.byte	4,2,35,84,13
	.byte	'SADR',0
	.word	16739
	.byte	4,2,35,88,13
	.byte	'DADR',0
	.word	16828
	.byte	4,2,35,92,13
	.byte	'ADICR',0
	.word	17144
	.byte	4,2,35,96,13
	.byte	'CHCR',0
	.word	17423
	.byte	4,2,35,100,13
	.byte	'SHADR',0
	.word	17514
	.byte	4,2,35,104,13
	.byte	'CHSR',0
	.word	17787
	.byte	4,2,35,108,0,14
	.word	17827
	.byte	10
	.byte	'_Ifx_DMA_BLK',0,13,178,8,25,128,1,13
	.byte	'EER',0
	.word	14624
	.byte	4,2,35,0,13
	.byte	'ERRSR',0
	.word	14916
	.byte	4,2,35,4,13
	.byte	'CLRE',0
	.word	15199
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2642
	.byte	4,2,35,12,13
	.byte	'ME',0
	.word	18115
	.byte	112,2,35,16,0,14
	.word	18120
	.byte	15,128,31
	.word	644
	.byte	16,255,30,0,14
	.word	18120
	.byte	15,96
	.word	644
	.byte	16,95,0,10
	.byte	'_Ifx_DMA_OTSS_Bits',0,13,185,4,16,4,11
	.byte	'TGS',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'BS',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,13,208,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18245
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,13,141,4,16,4,11
	.byte	'SIT',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,13,168,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18384
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_PRR0_Bits',0,13,194,4,16,4,11
	.byte	'PAT00',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'PAT01',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'PAT02',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'PAT03',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,216,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18490
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_PRR1_Bits',0,13,203,4,16,4,11
	.byte	'PAT10',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'PAT11',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'PAT12',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'PAT13',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,13,224,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18624
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_TIME_Bits',0,13,226,4,16,4,11
	.byte	'COUNT',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,248,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18758
	.byte	4,2,35,0,0,15,236,1
	.word	644
	.byte	16,235,1,0,10
	.byte	'_Ifx_DMA_MODE_Bits',0,13,178,4,16,4,11
	.byte	'MODE',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,13,200,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18852
	.byte	4,2,35,0,0,15,16
	.word	18916
	.byte	16,3,0,15,240,9
	.word	644
	.byte	16,239,9,0,10
	.byte	'_Ifx_DMA_HRR_Bits',0,13,148,4,16,4,11
	.byte	'HRP',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,13,176,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18976
	.byte	4,2,35,0,0,15,192,1
	.word	19038
	.byte	16,47,0,15,192,2
	.word	644
	.byte	16,191,2,0,10
	.byte	'_Ifx_DMA_SUSENR_Bits',0,13,219,4,16,4,11
	.byte	'SUSEN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,13,240,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19099
	.byte	4,2,35,0,0,15,192,1
	.word	19166
	.byte	16,47,0,10
	.byte	'_Ifx_DMA_SUSACR_Bits',0,13,212,4,16,4,11
	.byte	'SUSAC',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,13,232,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19216
	.byte	4,2,35,0,0,15,192,1
	.word	19283
	.byte	16,47,0,10
	.byte	'_Ifx_DMA_TSR_Bits',0,13,232,4,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'HTRE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'TRL',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'CH',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'HLTREQ',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'HLTACK',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	6,0,2,35,1,11
	.byte	'ECH',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'DCH',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'CTL',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'HLTCLR',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	644
	.byte	7,0,2,35,3,0,12,13,128,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19333
	.byte	4,2,35,0,0,15,192,1
	.word	19608
	.byte	16,47,0,10
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,13,235,3,16,4,11
	.byte	'RDCRC',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,128,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19658
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,13,247,3,16,4,11
	.byte	'SDCRC',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,144,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19746
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,13,241,3,16,4,11
	.byte	'SADR',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,136,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19834
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,13,229,3,16,4,11
	.byte	'DADR',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,248,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19919
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,13,172,3,16,4,11
	.byte	'SMF',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,13,224,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20004
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,13,192,3,16,4,11
	.byte	'TREL',0,2
	.word	661
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	644
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	644
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	644
	.byte	2,0,2,35,3,0,12,13,232,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20316
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,13,253,3,16,4,11
	.byte	'SHADR',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,13,152,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20593
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,13,208,3,16,4,11
	.byte	'TCOUNT',0,2
	.word	661
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'SWB',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CWRP',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'CICH',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'SIT',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'SCH',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,13,240,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH',0,13,188,8,25,32,13
	.byte	'RDCRCR',0
	.word	19706
	.byte	4,2,35,0,13
	.byte	'SDCRCR',0
	.word	19794
	.byte	4,2,35,4,13
	.byte	'SADR',0
	.word	19879
	.byte	4,2,35,8,13
	.byte	'DADR',0
	.word	19964
	.byte	4,2,35,12,13
	.byte	'ADICR',0
	.word	20276
	.byte	4,2,35,16,13
	.byte	'CHCFGR',0
	.word	20553
	.byte	4,2,35,20,13
	.byte	'SHADR',0
	.word	20640
	.byte	4,2,35,24,13
	.byte	'CHCSR',0
	.word	20987
	.byte	4,2,35,28,0,15,128,12
	.word	21027
	.byte	16,47,0,14
	.word	21167
	.byte	15,128,52
	.word	644
	.byte	16,255,51,0,10
	.byte	'_Ifx_DMA',0,13,211,8,25,128,128,1,13
	.byte	'CLC',0
	.word	11337
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2642
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11459
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	11499
	.byte	20,2,35,12,13
	.byte	'MEMCON',0
	.word	11736
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	11776
	.byte	28,2,35,36,13
	.byte	'ACCEN00',0
	.word	12315
	.byte	4,2,35,64,13
	.byte	'ACCEN01',0
	.word	12405
	.byte	4,2,35,68,13
	.byte	'ACCEN10',0
	.word	12975
	.byte	4,2,35,72,13
	.byte	'ACCEN11',0
	.word	13065
	.byte	4,2,35,76,13
	.byte	'ACCEN20',0
	.word	13636
	.byte	4,2,35,80,13
	.byte	'ACCEN21',0
	.word	13727
	.byte	4,2,35,84,13
	.byte	'ACCEN30',0
	.word	14298
	.byte	4,2,35,88,13
	.byte	'ACCEN31',0
	.word	14389
	.byte	4,2,35,92,13
	.byte	'reserved_60',0
	.word	14429
	.byte	192,1,2,35,96,13
	.byte	'BLK0',0
	.word	18215
	.byte	128,1,3,35,160,2,13
	.byte	'reserved_1A0',0
	.word	18220
	.byte	128,31,3,35,160,3,13
	.byte	'BLK1',0
	.word	18231
	.byte	128,1,3,35,160,34,13
	.byte	'reserved_11A0',0
	.word	18236
	.byte	96,3,35,160,35,13
	.byte	'OTSS',0
	.word	18344
	.byte	4,3,35,128,36,13
	.byte	'ERRINTR',0
	.word	18450
	.byte	4,3,35,132,36,13
	.byte	'PRR0',0
	.word	18584
	.byte	4,3,35,136,36,13
	.byte	'PRR1',0
	.word	18718
	.byte	4,3,35,140,36,13
	.byte	'TIME',0
	.word	18801
	.byte	4,3,35,144,36,13
	.byte	'reserved_1214',0
	.word	18841
	.byte	236,1,3,35,148,36,13
	.byte	'MODE',0
	.word	18956
	.byte	16,3,35,128,38,13
	.byte	'reserved_1310',0
	.word	18965
	.byte	240,9,3,35,144,38,13
	.byte	'HRR',0
	.word	19078
	.byte	192,1,3,35,128,48,13
	.byte	'reserved_18C0',0
	.word	19088
	.byte	192,2,3,35,192,49,13
	.byte	'SUSENR',0
	.word	19206
	.byte	192,1,3,35,128,52,13
	.byte	'reserved_1AC0',0
	.word	19088
	.byte	192,2,3,35,192,53,13
	.byte	'SUSACR',0
	.word	19323
	.byte	192,1,3,35,128,56,13
	.byte	'reserved_1CC0',0
	.word	19088
	.byte	192,2,3,35,192,57,13
	.byte	'TSR',0
	.word	19648
	.byte	192,1,3,35,128,60,13
	.byte	'reserved_1EC0',0
	.word	19088
	.byte	192,2,3,35,192,61,13
	.byte	'CH',0
	.word	21177
	.byte	128,12,3,35,128,64,13
	.byte	'reserved_2600',0
	.word	21182
	.byte	128,52,3,35,128,76,0,14
	.word	21193
	.byte	3
	.word	21899
	.byte	17,14,105,9,1,18
	.byte	'IfxDma_ChannelId_none',0,127,18
	.byte	'IfxDma_ChannelId_0',0,0,18
	.byte	'IfxDma_ChannelId_1',0,1,18
	.byte	'IfxDma_ChannelId_2',0,2,18
	.byte	'IfxDma_ChannelId_3',0,3,18
	.byte	'IfxDma_ChannelId_4',0,4,18
	.byte	'IfxDma_ChannelId_5',0,5,18
	.byte	'IfxDma_ChannelId_6',0,6,18
	.byte	'IfxDma_ChannelId_7',0,7,18
	.byte	'IfxDma_ChannelId_8',0,8,18
	.byte	'IfxDma_ChannelId_9',0,9,18
	.byte	'IfxDma_ChannelId_10',0,10,18
	.byte	'IfxDma_ChannelId_11',0,11,18
	.byte	'IfxDma_ChannelId_12',0,12,18
	.byte	'IfxDma_ChannelId_13',0,13,18
	.byte	'IfxDma_ChannelId_14',0,14,18
	.byte	'IfxDma_ChannelId_15',0,15,18
	.byte	'IfxDma_ChannelId_16',0,16,18
	.byte	'IfxDma_ChannelId_17',0,17,18
	.byte	'IfxDma_ChannelId_18',0,18,18
	.byte	'IfxDma_ChannelId_19',0,19,18
	.byte	'IfxDma_ChannelId_20',0,20,18
	.byte	'IfxDma_ChannelId_21',0,21,18
	.byte	'IfxDma_ChannelId_22',0,22,18
	.byte	'IfxDma_ChannelId_23',0,23,18
	.byte	'IfxDma_ChannelId_24',0,24,18
	.byte	'IfxDma_ChannelId_25',0,25,18
	.byte	'IfxDma_ChannelId_26',0,26,18
	.byte	'IfxDma_ChannelId_27',0,27,18
	.byte	'IfxDma_ChannelId_28',0,28,18
	.byte	'IfxDma_ChannelId_29',0,29,18
	.byte	'IfxDma_ChannelId_30',0,30,18
	.byte	'IfxDma_ChannelId_31',0,31,18
	.byte	'IfxDma_ChannelId_32',0,32,18
	.byte	'IfxDma_ChannelId_33',0,33,18
	.byte	'IfxDma_ChannelId_34',0,34,18
	.byte	'IfxDma_ChannelId_35',0,35,18
	.byte	'IfxDma_ChannelId_36',0,36,18
	.byte	'IfxDma_ChannelId_37',0,37,18
	.byte	'IfxDma_ChannelId_38',0,38,18
	.byte	'IfxDma_ChannelId_39',0,39,18
	.byte	'IfxDma_ChannelId_40',0,40,18
	.byte	'IfxDma_ChannelId_41',0,41,18
	.byte	'IfxDma_ChannelId_42',0,42,18
	.byte	'IfxDma_ChannelId_43',0,43,18
	.byte	'IfxDma_ChannelId_44',0,44,18
	.byte	'IfxDma_ChannelId_45',0,45,18
	.byte	'IfxDma_ChannelId_46',0,46,18
	.byte	'IfxDma_ChannelId_47',0,47,0
.L144:
	.byte	4
	.byte	'IfxDma_resetChannel',0,3,12,160,12,17,1,1
.L147:
	.byte	5
	.byte	'dma',0,12,160,12,46
	.word	21904
.L149:
	.byte	5
	.byte	'channelId',0,12,160,12,68
	.word	21909
.L151:
	.byte	6,0
.L182:
	.byte	4
	.byte	'IfxDma_clearChannelTransactionRequestLost',0,3,12,247,9,17,1,1
.L185:
	.byte	5
	.byte	'dma',0,12,247,9,68
	.word	21904
.L187:
	.byte	5
	.byte	'channelId',0,12,247,9,90
	.word	21909
.L189:
	.byte	6,0
.L175:
	.byte	8
	.byte	'IfxDma_getChannelTransactionRequestLost',0,3,12,198,11,20
	.word	644
	.byte	1,1
.L177:
	.byte	5
	.byte	'dma',0,12,198,11,69
	.word	21904
.L179:
	.byte	5
	.byte	'channelId',0,12,198,11,91
	.word	21909
.L181:
	.byte	6,0
.L157:
	.byte	4
	.byte	'IfxDma_setChannelDestinationAddress',0,3,12,178,12,17,1,1
.L159:
	.byte	5
	.byte	'dma',0,12,178,12,62
	.word	21904
.L161:
	.byte	5
	.byte	'channelId',0,12,178,12,84
	.word	21909
.L163:
	.byte	5
	.byte	'address',0,12,178,12,101
	.word	383
.L165:
	.byte	6,0
.L167:
	.byte	4
	.byte	'IfxDma_clearChannelInterrupt',0,3,12,241,9,17,1,1
.L170:
	.byte	5
	.byte	'dma',0,12,241,9,55
	.word	21904
.L172:
	.byte	5
	.byte	'channelId',0,12,241,9,77
	.word	21909
.L174:
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,16,118,16,4,11
	.byte	'DISR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,16,207,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23383
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,16,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	661
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	644
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,151,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23542
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,16,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,16,143,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23837
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,16,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	661
	.byte	11,0,2,35,2,0,12,16,247,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23962
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,16,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	661
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,231,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24187
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,16,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	661
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	644
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,183,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24428
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,16,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	661
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	644
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	644
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,135,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24649
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,16,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	661
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,12,16,223,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24914
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,16,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	661
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	661
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,16,199,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25111
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,16,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	661
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,16,191,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,16,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,12,16,191,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25422
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,16,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,12,16,183,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25622
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,16,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,16,199,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25736
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,16,135,5,25,12,13
	.byte	'CON',0
	.word	25582
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	25696
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	25811
	.byte	4,2,35,8,0,14
	.word	25851
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,16,148,1,16,4,11
	.byte	'TH',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,231,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25924
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,16,241,1,16,4,11
	.byte	'THS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,255,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26410
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,16,180,1,16,4,11
	.byte	'THC',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,239,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26923
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,16,212,1,16,4,11
	.byte	'THE',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,247,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27438
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,16,143,3,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,239,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27903
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,16,245,2,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,215,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27990
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,16,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	469
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,215,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28077
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,16,251,2,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,223,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28200
	.byte	4,2,35,0,0,15,148,1
	.word	644
	.byte	16,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,16,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	2,0,2,35,3,0,12,16,207,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28299
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,16,202,2,16,4,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,16,175,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,16,195,2,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,16,167,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,16,187,2,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,16,159,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28678
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,16,175,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28804
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,16,167,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28896
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,16,153,5,25,128,2,13
	.byte	'CLC',0
	.word	23502
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	23797
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	23922
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	24147
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	24388
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	24609
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	24874
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	25071
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	25228
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	25382
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	25919
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	26370
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	26883
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	27398
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	27863
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	27950
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	28037
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	28160
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	28248
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	28288
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	28422
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	28531
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	28638
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	28764
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	28856
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	29428
	.byte	4,3,35,252,1,0,14
	.word	29468
	.byte	3
	.word	29910
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,15,228,13,17,1,1,5
	.byte	'asclin',0,15,228,13,49
	.word	29915
	.byte	5
	.byte	'enable',0,15,228,13,65
	.word	644
	.byte	6,0,17,15,123,9,1,18
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,18
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,18
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,18
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,15,169,17,17,1,1,5
	.byte	'asclin',0,15,169,17,51
	.word	29915
	.byte	5
	.byte	'ctsi',0,15,169,17,84
	.word	29982
	.byte	6,0,17,15,181,2,9,1,18
	.byte	'IfxAsclin_RxInputSelect_0',0,0,18
	.byte	'IfxAsclin_RxInputSelect_1',0,1,18
	.byte	'IfxAsclin_RxInputSelect_2',0,2,18
	.byte	'IfxAsclin_RxInputSelect_3',0,3,18
	.byte	'IfxAsclin_RxInputSelect_4',0,4,18
	.byte	'IfxAsclin_RxInputSelect_5',0,5,18
	.byte	'IfxAsclin_RxInputSelect_6',0,6,18
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,15,191,18,17,1,1,5
	.byte	'asclin',0,15,191,18,50
	.word	29915
	.byte	5
	.byte	'alti',0,15,191,18,82
	.word	30166
	.byte	6,0
.L193:
	.byte	7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,17,60,9,12,13
	.byte	'count',0
	.word	30458
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	30471
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	30471
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	30458
	.byte	2,2,35,10,0,14
	.word	644
	.byte	14
	.word	644
	.byte	10
	.byte	'_Fifo',0,17,73,16,28,13
	.byte	'buffer',0
	.word	383
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	30483
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	30458
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	30458
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	30458
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	30458
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	30564
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	30569
	.byte	1,2,35,25,0,3
	.word	30574
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,17,206,1,22
	.word	30458
	.byte	1,1,5
	.byte	'fifo',0,17,206,1,51
	.word	30733
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,19,100,16,4,11
	.byte	'DISR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,19,149,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30785
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,19,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,12,19,181,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30941
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,19,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,229,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31063
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,19,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,245,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31148
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,19,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,253,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31233
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,19,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,133,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31318
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,19,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,141,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31404
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,19,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,149,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31490
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,19,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,157,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31576
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,19,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,133,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31662
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,19,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,165,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31749
	.byte	4,2,35,0,0,15,8
	.word	31791
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,19,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	644
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	644
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	644
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	3,0,2,35,3,0,12,19,157,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31840
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,19,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	469
	.byte	25,0,2,35,0,0,12,19,173,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,19,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,19,189,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32288
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,19,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,237,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32452
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,19,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,141,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32539
	.byte	4,2,35,0,0,15,144,1
	.word	644
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,19,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	2,0,2,35,3,0,12,19,221,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32639
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,19,175,1,16,4,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,19,213,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32799
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,19,168,1,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,12,19,205,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32905
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,19,160,1,16,4,11
	.byte	'RST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,19,197,2,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33009
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,19,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,19,253,1,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33132
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,19,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,12,19,245,1,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33221
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,19,173,3,25,128,2,13
	.byte	'CLC',0
	.word	30901
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2642
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	31023
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2642
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	31108
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	31193
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	31278
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	31364
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	31450
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	31536
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	31622
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	31709
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	31831
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	32031
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	32248
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	32412
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4801
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	32499
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	32588
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	32628
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	32759
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	32865
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	32969
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	33092
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	33181
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	33750
	.byte	4,3,35,252,1,0,14
	.word	33790
	.byte	3
	.word	34210
	.byte	8
	.byte	'IfxStm_get',0,3,18,162,4,19
	.word	351
	.byte	1,1,5
	.byte	'stm',0,18,162,4,39
	.word	34215
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,18,179,4,20
	.word	265
	.byte	1,1,5
	.byte	'stm',0,18,179,4,49
	.word	34215
	.byte	19,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,18,190,4,19
	.word	10949
	.byte	1,1,5
	.byte	'stm',0,18,190,4,44
	.word	34215
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,20,108,20
	.word	644
	.byte	1,1,19,6,0,0,4
	.byte	'restoreInterrupts',0,3,20,142,1,17,1,1,5
	.byte	'enabled',0,20,142,1,43
	.word	644
	.byte	19,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,20,164,2,25
	.word	34431
	.byte	1,1,5
	.byte	'timeout',0,20,164,2,50
	.word	34431
	.byte	19,6,0,0,8
	.byte	'isDeadLine',0,3,20,211,2,20
	.word	644
	.byte	1,1,5
	.byte	'deadLine',0,20,211,2,44
	.word	34431
	.byte	19,6,0,0,8
	.byte	'now',0,3,20,221,1,25
	.word	34431
	.byte	1,1,19,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,20,240,1,25
	.word	34431
	.byte	1,1,19,6,0,0,14
	.word	485
	.byte	21
	.byte	'__mfcr',0
	.word	34602
	.byte	1,1,1,1,22
	.word	485
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	34629
	.byte	23
	.byte	'debug_log_handler',0,21,113,9,1,1,1,1,5
	.byte	'pass',0,21,113,47
	.word	644
	.byte	5
	.byte	'str',0,21,113,59
	.word	34637
	.byte	5
	.byte	'file',0,21,113,70
	.word	34637
	.byte	5
	.byte	'line',0,21,113,80
	.word	485
	.byte	0,17,22,42,9,1,18
	.byte	'FIFO_SUCCESS',0,0,18
	.byte	'FIFO_RESET_UNDO',0,1,18
	.byte	'FIFO_CLEAR_UNDO',0,2,18
	.byte	'FIFO_BUFFER_NULL',0,3,18
	.byte	'FIFO_WRITE_UNDO',0,4,18
	.byte	'FIFO_SPACE_NO_ENOUGH',0,5,18
	.byte	'FIFO_READ_UNDO',0,6,18
	.byte	'FIFO_DATA_NO_ENOUGH',0,7,0,17,22,78,9,1,18
	.byte	'FIFO_DATA_8BIT',0,0,18
	.byte	'FIFO_DATA_16BIT',0,1,18
	.byte	'FIFO_DATA_32BIT',0,2,0,20,22,85,9,24,13
	.byte	'execution',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'type',0
	.word	34876
	.byte	1,2,35,1,13
	.byte	'buffer',0
	.word	383
	.byte	4,2,35,4,13
	.byte	'head',0
	.word	10949
	.byte	4,2,35,8,13
	.byte	'end',0
	.word	10949
	.byte	4,2,35,12,13
	.byte	'size',0
	.word	10949
	.byte	4,2,35,16,13
	.byte	'max',0
	.word	10949
	.byte	4,2,35,20,0,3
	.word	34935
	.byte	24
	.byte	'fifo_clear',0,22,96,17
	.word	34720
	.byte	1,1,1,1,5
	.byte	'fifo',0,22,96,55
	.word	35044
	.byte	0,24
	.byte	'fifo_used',0,22,97,17
	.word	10949
	.byte	1,1,1,1,5
	.byte	'fifo',0,22,97,55
	.word	35044
	.byte	0,24
	.byte	'fifo_write_element',0,22,99,17
	.word	34720
	.byte	1,1,1,1,5
	.byte	'fifo',0,22,99,55
	.word	35044
	.byte	5
	.byte	'dat',0,22,99,68
	.word	10949
	.byte	0,3
	.word	10949
	.byte	17,22,72,9,1,18
	.byte	'FIFO_READ_AND_CLEAN',0,0,18
	.byte	'FIFO_READ_ONLY',0,1,0,24
	.byte	'fifo_read_buffer',0,22,102,17
	.word	34720
	.byte	1,1,1,1,5
	.byte	'fifo',0,22,102,55
	.word	35044
	.byte	5
	.byte	'dat',0,22,102,67
	.word	383
	.byte	5
	.byte	'length',0,22,102,80
	.word	35179
	.byte	5
	.byte	'flag',0,22,102,108
	.word	35184
	.byte	0,25
	.word	213
	.byte	26
	.word	239
	.byte	6,0,25
	.word	274
	.byte	26
	.word	306
	.byte	6,0,25
	.word	319
	.byte	6,0,25
	.word	388
	.byte	26
	.word	407
	.byte	6,0,25
	.word	423
	.byte	26
	.word	438
	.byte	26
	.word	452
	.byte	6,0,25
	.word	1231
	.byte	26
	.word	1271
	.byte	26
	.word	1289
	.byte	6,0,25
	.word	1309
	.byte	26
	.word	1352
	.byte	6,0,25
	.word	1372
	.byte	26
	.word	1410
	.byte	26
	.word	1428
	.byte	6,0,25
	.word	1448
	.byte	26
	.word	1489
	.byte	6,0,25
	.word	1509
	.byte	26
	.word	1560
	.byte	6,0,25
	.word	1580
	.byte	6,0,25
	.word	9519
	.byte	26
	.word	9551
	.byte	26
	.word	9565
	.byte	26
	.word	9583
	.byte	6,0,25
	.word	9886
	.byte	26
	.word	9919
	.byte	26
	.word	9933
	.byte	26
	.word	9951
	.byte	26
	.word	9965
	.byte	6,0,25
	.word	10085
	.byte	26
	.word	10113
	.byte	26
	.word	10127
	.byte	26
	.word	10145
	.byte	6,0,17,23,79,9,1,18
	.byte	'IfxPort_Index_none',0,127,18
	.byte	'IfxPort_Index_00',0,0,18
	.byte	'IfxPort_Index_02',0,2,18
	.byte	'IfxPort_Index_10',0,10,18
	.byte	'IfxPort_Index_11',0,11,18
	.byte	'IfxPort_Index_13',0,13,18
	.byte	'IfxPort_Index_14',0,14,18
	.byte	'IfxPort_Index_15',0,15,18
	.byte	'IfxPort_Index_20',0,20,18
	.byte	'IfxPort_Index_21',0,21,18
	.byte	'IfxPort_Index_22',0,22,18
	.byte	'IfxPort_Index_23',0,23,18
	.byte	'IfxPort_Index_32',0,32,18
	.byte	'IfxPort_Index_33',0,33,0,24
	.byte	'IfxPort_getAddress',0,5,187,3,19
	.word	9389
	.byte	1,1,1,1,5
	.byte	'port',0,5,187,3,52
	.word	35520
	.byte	0
.L139:
	.byte	20,24,42,9,24,13
	.byte	'scl_pin',0
	.word	10949
	.byte	4,2,35,0,13
	.byte	'sda_pin',0
	.word	10949
	.byte	4,2,35,4,13
	.byte	'addr',0
	.word	644
	.byte	1,2,35,8,13
	.byte	'delay',0
	.word	10949
	.byte	4,2,35,10,13
	.byte	'iic_scl',0
	.word	383
	.byte	4,2,35,16,13
	.byte	'iic_sda',0
	.word	383
	.byte	4,2,35,20,0,3
	.word	35841
	.byte	17,25,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,23
	.byte	'soft_iic_init',0,24,83,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,24,83,68
	.word	35944
	.byte	5
	.byte	'addr',0,24,83,88
	.word	644
	.byte	5
	.byte	'delay',0,24,83,101
	.word	10949
	.byte	5
	.byte	'scl_pin',0,24,83,122
	.word	35949
	.byte	5
	.byte	'sda_pin',0,24,83,145,1
	.word	35949
	.byte	0,23
	.byte	'system_delay_ms',0,26,46,9,1,1,1,1,5
	.byte	'time',0,26,46,45
	.word	10949
	.byte	0,25
	.word	10502
	.byte	26
	.word	10530
	.byte	6,0,25
	.word	10596
	.byte	6,0,25
	.word	10706
	.byte	6,0,25
	.word	10740
	.byte	6,0,25
	.word	10782
	.byte	19,27
	.word	10740
	.byte	28
	.word	10780
	.byte	0,6,0,0,25
	.word	10823
	.byte	6,0,25
	.word	10857
	.byte	6,0,25
	.word	10897
	.byte	26
	.word	10930
	.byte	6,0,25
	.word	10970
	.byte	26
	.word	11011
	.byte	6,0,25
	.word	11030
	.byte	26
	.word	11085
	.byte	6,0,25
	.word	11104
	.byte	26
	.word	11144
	.byte	26
	.word	11161
	.byte	19,6,0,0,25
	.word	11180
	.byte	6,0,25
	.word	22985
	.byte	26
	.word	23013
	.byte	26
	.word	23026
	.byte	6,0,25
	.word	23047
	.byte	26
	.word	23097
	.byte	26
	.word	23110
	.byte	6,0,25
	.word	23131
	.byte	26
	.word	23183
	.byte	26
	.word	23196
	.byte	6,0,25
	.word	23217
	.byte	26
	.word	23261
	.byte	26
	.word	23274
	.byte	26
	.word	23293
	.byte	6,0,25
	.word	23312
	.byte	26
	.word	23349
	.byte	26
	.word	23362
	.byte	6,0,17,27,92,9,1,18
	.byte	'IfxScuEru_InputChannel_0',0,0,18
	.byte	'IfxScuEru_InputChannel_1',0,1,18
	.byte	'IfxScuEru_InputChannel_2',0,2,18
	.byte	'IfxScuEru_InputChannel_3',0,3,18
	.byte	'IfxScuEru_InputChannel_4',0,4,18
	.byte	'IfxScuEru_InputChannel_5',0,5,18
	.byte	'IfxScuEru_InputChannel_6',0,6,18
	.byte	'IfxScuEru_InputChannel_7',0,7,0,23
	.byte	'IfxScuEru_clearEventFlag',0,27,189,1,17,1,1,1,1,5
	.byte	'inputChannel',0,27,189,1,65
	.word	38250
	.byte	0,3
	.word	644
	.byte	17,29,42,9,1,18
	.byte	'ERU_CH0_REQ0_P15_4',0,1,18
	.byte	'ERU_CH1_REQ10_P14_3',0,4,18
	.byte	'ERU_CH2_REQ7_P00_4',0,6,18
	.byte	'ERU_CH2_REQ14_P02_1',0,7,18
	.byte	'ERU_CH2_REQ2_P10_2',0,8,18
	.byte	'ERU_CH3_REQ6_P02_0',0,9,18
	.byte	'ERU_CH3_REQ3_P10_3',0,10,18
	.byte	'ERU_CH3_REQ15_P14_1',0,11,18
	.byte	'ERU_CH4_REQ13_P15_5',0,12,18
	.byte	'ERU_CH4_REQ8_P33_7',0,13,18
	.byte	'ERU_CH5_REQ1_P15_8',0,15,18
	.byte	'ERU_CH6_REQ12_P11_10',0,18,18
	.byte	'ERU_CH6_REQ9_P20_0',0,19,18
	.byte	'ERU_CH7_REQ16_P15_1',0,21,18
	.byte	'ERU_CH7_REQ11_P20_9',0,22,0,17,29,65,9,1,18
	.byte	'EXTI_TRIGGER_RISING',0,0,18
	.byte	'EXTI_TRIGGER_FALLING',0,1,18
	.byte	'EXTI_TRIGGER_BOTH',0,2,0,24
	.byte	'dma_init',0,28,48,7
	.word	644
	.byte	1,1,1,1,5
	.byte	'dma_ch',0,28,48,39
	.word	21909
	.byte	5
	.byte	'source_addr',0,28,48,54
	.word	38529
	.byte	5
	.byte	'destination_addr',0,28,48,74
	.word	38529
	.byte	5
	.byte	'eru_pin',0,28,48,106
	.word	38534
	.byte	5
	.byte	'trigger',0,28,48,133,1
	.word	38863
	.byte	5
	.byte	'dma_count',0,28,48,149,1
	.word	10949
	.byte	0,23
	.byte	'dma_disable',0,28,49,7,1,1,1,1,5
	.byte	'dma_ch',0,28,49,39
	.word	21909
	.byte	0,23
	.byte	'dma_enable',0,28,50,7,1,1,1,1,5
	.byte	'dma_ch',0,28,50,39
	.word	21909
	.byte	0,25
	.word	29920
	.byte	26
	.word	29948
	.byte	26
	.word	29964
	.byte	6,0,25
	.word	30104
	.byte	26
	.word	30134
	.byte	26
	.word	30150
	.byte	6,0,25
	.word	30397
	.byte	26
	.word	30426
	.byte	26
	.word	30442
	.byte	6,0,25
	.word	30738
	.byte	26
	.word	30769
	.byte	6,0,25
	.word	34220
	.byte	26
	.word	34243
	.byte	6,0,25
	.word	34258
	.byte	26
	.word	34290
	.byte	19,19,27
	.word	11180
	.byte	28
	.word	11218
	.byte	0,0,6,0,0,25
	.word	34308
	.byte	26
	.word	34336
	.byte	6,0,25
	.word	34351
	.byte	19,27
	.word	10782
	.byte	29
	.word	10819
	.byte	27
	.word	10740
	.byte	28
	.word	10780
	.byte	0,28
	.word	10820
	.byte	0,0,6,0,0,25
	.word	34384
	.byte	26
	.word	34410
	.byte	19,27
	.word	10897
	.byte	26
	.word	10930
	.byte	28
	.word	10947
	.byte	0,6,0,0,25
	.word	34448
	.byte	26
	.word	34472
	.byte	19,27
	.word	34538
	.byte	29
	.word	34554
	.byte	27
	.word	34351
	.byte	29
	.word	34380
	.byte	27
	.word	10782
	.byte	29
	.word	10819
	.byte	27
	.word	10740
	.byte	28
	.word	10780
	.byte	0,28
	.word	10820
	.byte	0,0,28
	.word	34381
	.byte	0,0,28
	.word	34555
	.byte	27
	.word	34384
	.byte	26
	.word	34410
	.byte	29
	.word	34427
	.byte	27
	.word	10897
	.byte	26
	.word	10930
	.byte	28
	.word	10947
	.byte	0,28
	.word	34428
	.byte	0,0,28
	.word	34556
	.byte	27
	.word	34220
	.byte	26
	.word	34243
	.byte	28
	.word	34256
	.byte	0,28
	.word	34557
	.byte	0,0,6,0,0,25
	.word	34493
	.byte	26
	.word	34516
	.byte	19,27
	.word	34538
	.byte	29
	.word	34554
	.byte	27
	.word	34351
	.byte	29
	.word	34380
	.byte	27
	.word	10782
	.byte	29
	.word	10819
	.byte	27
	.word	10740
	.byte	28
	.word	10780
	.byte	0,28
	.word	10820
	.byte	0,0,28
	.word	34381
	.byte	0,0,28
	.word	34555
	.byte	27
	.word	34384
	.byte	26
	.word	34410
	.byte	29
	.word	34427
	.byte	27
	.word	10897
	.byte	26
	.word	10930
	.byte	28
	.word	10947
	.byte	0,28
	.word	34428
	.byte	0,0,28
	.word	34556
	.byte	27
	.word	34220
	.byte	26
	.word	34243
	.byte	28
	.word	34256
	.byte	0,28
	.word	34557
	.byte	0,0,6,0,0,25
	.word	34538
	.byte	19,27
	.word	34351
	.byte	29
	.word	34380
	.byte	27
	.word	10782
	.byte	29
	.word	10819
	.byte	27
	.word	10740
	.byte	28
	.word	10780
	.byte	0,28
	.word	10820
	.byte	0,0,28
	.word	34381
	.byte	0,0,6,27
	.word	34384
	.byte	26
	.word	34410
	.byte	29
	.word	34427
	.byte	27
	.word	10897
	.byte	26
	.word	10930
	.byte	28
	.word	10947
	.byte	0,28
	.word	34428
	.byte	0,0,6,27
	.word	34220
	.byte	26
	.word	34243
	.byte	28
	.word	34256
	.byte	0,6,0,0,25
	.word	34560
	.byte	19,27
	.word	34220
	.byte	26
	.word	34243
	.byte	28
	.word	34256
	.byte	0,6,0,0,17,30,103,9,1,18
	.byte	'UART_0',0,0,18
	.byte	'UART_1',0,1,18
	.byte	'UART_2',0,2,18
	.byte	'UART_3',0,3,0,30
	.word	644
	.byte	3
	.word	39777
	.byte	23
	.byte	'uart_write_buffer',0,30,119,9,1,1,1,1,5
	.byte	'uartn',0,30,119,62
	.word	39735
	.byte	5
	.byte	'buff',0,30,119,82
	.word	39782
	.byte	5
	.byte	'len',0,30,119,95
	.word	10949
	.byte	0,24
	.byte	'uart_query_byte',0,30,123,9
	.word	644
	.byte	1,1,1,1,5
	.byte	'uartn',0,30,123,62
	.word	39735
	.byte	5
	.byte	'dat',0,30,123,76
	.word	38529
	.byte	0,17,31,44,9,1,18
	.byte	'NO_CAMERE',0,0,18
	.byte	'CAMERA_BIN_IIC',0,1,18
	.byte	'CAMERA_BIN_UART',0,2,18
	.byte	'CAMERA_GRAYSCALE',0,3,18
	.byte	'CAMERA_COLOR',0,4,0,31
	.byte	'callback_function',0,31,73,16
	.word	208
	.byte	23
	.byte	'set_camera_type',0,31,90,8,1,1,1,1,5
	.byte	'type_set',0,31,90,51
	.word	39908
	.byte	5
	.byte	'vsync_callback',0,31,90,79
	.word	39995
	.byte	5
	.byte	'dma_callback',0,31,90,113
	.word	39995
	.byte	5
	.byte	'uart_callback',0,31,90,145,1
	.word	39995
	.byte	0,3
	.word	205
	.byte	24
	.byte	'camera_init',0,32,55,7
	.word	644
	.byte	1,1,1,1,5
	.byte	'source_addr',0,32,55,49
	.word	38529
	.byte	5
	.byte	'destination_addr',0,32,55,69
	.word	38529
	.byte	5
	.byte	'image_size',0,32,55,94
	.word	10949
	.byte	0,15,4
	.word	30458
	.byte	16,1,0,3
	.word	40224
	.byte	24
	.byte	'mt9v03x_set_config_sccb',0,33,43,17
	.word	644
	.byte	1,1,1,1,5
	.byte	'soft_iic_obj',0,33,43,56
	.word	383
	.byte	5
	.byte	'buff',0,33,43,80
	.word	40233
	.byte	0,24
	.byte	'mt9v03x_set_exposure_time_sccb',0,33,44,17
	.word	644
	.byte	1,1,1,1,5
	.byte	'light',0,33,44,69
	.word	661
	.byte	0,24
	.byte	'mt9v03x_set_reg_sccb',0,33,45,17
	.word	644
	.byte	1,1,1,1,5
	.byte	'addr',0,33,45,64
	.word	644
	.byte	5
	.byte	'data',0,33,45,89
	.word	661
	.byte	0
.L111:
	.byte	15,4
	.word	644
	.byte	16,3,0,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,34,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	29,0,2,35,0,0
.L155:
	.byte	12,34,223,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40436
	.byte	4,2,35,0,0,32
	.byte	'__INDIRECT__',0,35,1,1,1,1,1,31
	.byte	'__wchar_t',0,35,1,1
	.word	30458
	.byte	31
	.byte	'__size_t',0,35,1,1
	.word	469
	.byte	31
	.byte	'__ptrdiff_t',0,35,1,1
	.word	485
	.byte	33,1,3
	.word	40620
	.byte	31
	.byte	'__codeptr',0,35,1,1
	.word	40622
	.byte	31
	.byte	'__intptr_t',0,35,1,1
	.word	485
	.byte	31
	.byte	'__uintptr_t',0,35,1,1
	.word	469
	.byte	31
	.byte	'_iob_flag_t',0,36,82,25
	.word	661
	.byte	31
	.byte	'boolean',0,37,101,29
	.word	644
	.byte	31
	.byte	'uint8',0,37,105,29
	.word	644
	.byte	31
	.byte	'uint16',0,37,109,29
	.word	661
	.byte	31
	.byte	'uint32',0,37,113,29
	.word	10949
	.byte	31
	.byte	'uint64',0,37,118,29
	.word	351
	.byte	31
	.byte	'sint16',0,37,126,29
	.word	30458
	.byte	31
	.byte	'sint32',0,37,131,1,29
	.word	30471
	.byte	31
	.byte	'sint64',0,37,138,1,29
	.word	34431
	.byte	31
	.byte	'float32',0,37,167,1,29
	.word	265
	.byte	31
	.byte	'pvoid',0,38,57,28
	.word	383
	.byte	31
	.byte	'Ifx_TickTime',0,38,79,28
	.word	34431
	.byte	31
	.byte	'Ifx_SizeT',0,38,92,16
	.word	30458
	.byte	31
	.byte	'Ifx_Priority',0,38,103,16
	.word	661
	.byte	17,38,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,31
	.byte	'Ifx_RxSel',0,38,140,1,3
	.word	40917
	.byte	17,38,164,1,9,1,18
	.byte	'Ifx_DataBufferMode_normal',0,0,18
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,31
	.byte	'Ifx_DataBufferMode',0,38,169,1,2
	.word	41055
	.byte	7
	.byte	'char',0,1,6,31
	.byte	'int8',0,39,54,29
	.word	41155
	.byte	31
	.byte	'int16',0,39,55,29
	.word	30458
	.byte	31
	.byte	'int32',0,39,56,29
	.word	485
	.byte	31
	.byte	'int64',0,39,57,29
	.word	34431
	.byte	14
	.word	644
	.byte	31
	.byte	'vuint8',0,39,59,29
	.word	41218
	.byte	31
	.byte	'fifo_state_enum',0,22,53,2
	.word	34720
	.byte	31
	.byte	'fifo_operation_enum',0,22,76,2
	.word	35184
	.byte	31
	.byte	'fifo_data_type_enum',0,22,83,2
	.word	34876
	.byte	31
	.byte	'fifo_struct',0,22,94,2
	.word	34935
	.byte	31
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8214
	.byte	31
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	8127
	.byte	31
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4470
	.byte	31
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2523
	.byte	31
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3518
	.byte	31
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2651
	.byte	31
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3298
	.byte	31
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2866
	.byte	31
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	3081
	.byte	31
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7486
	.byte	31
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7610
	.byte	31
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7694
	.byte	31
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7874
	.byte	31
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	6125
	.byte	31
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6649
	.byte	31
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6299
	.byte	31
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6473
	.byte	31
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	7138
	.byte	31
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1952
	.byte	31
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5462
	.byte	31
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5950
	.byte	31
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5609
	.byte	31
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5778
	.byte	31
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6805
	.byte	31
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1636
	.byte	31
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5176
	.byte	31
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4810
	.byte	31
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3841
	.byte	31
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	4145
	.byte	31
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8741
	.byte	31
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8174
	.byte	31
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4761
	.byte	31
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2602
	.byte	31
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3792
	.byte	31
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2826
	.byte	31
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3478
	.byte	31
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	3041
	.byte	31
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3258
	.byte	31
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7570
	.byte	31
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7819
	.byte	31
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	8078
	.byte	31
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7446
	.byte	31
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6259
	.byte	31
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6765
	.byte	31
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6433
	.byte	31
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6609
	.byte	31
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2483
	.byte	31
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	7098
	.byte	31
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5569
	.byte	31
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	6085
	.byte	31
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5738
	.byte	31
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5910
	.byte	31
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1912
	.byte	31
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5422
	.byte	31
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	5136
	.byte	31
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	4105
	.byte	31
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4421
	.byte	14
	.word	8781
	.byte	31
	.byte	'Ifx_P',0,6,139,6,3
	.word	42656
	.byte	31
	.byte	'IfxPort_Index',0,23,95,3
	.word	35520
	.byte	17,40,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,31
	.byte	'IfxScu_CCUCON0_CLKSEL',0,40,240,10,3
	.word	42698
	.byte	17,40,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,31
	.byte	'IfxScu_WDTCON1_IR',0,40,255,10,3
	.word	42795
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	42917
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	43474
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	43551
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	644
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	43687
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	644
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	43967
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	44205
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	644
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	644
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	44333
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	644
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	644
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	44576
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	44811
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	44939
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	45039
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	644
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	45139
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	469
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	45347
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	661
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	45512
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	45695
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	644
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	45849
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	46213
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	661
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	644
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	46424
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	46676
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	46794
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	46905
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	47068
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	47231
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	47389
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	644
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	644
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	644
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	10,0,2,35,2,0,31
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	47554
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	644
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	644
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	661
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	47883
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	48104
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	48267
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	48539
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	48692
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	48848
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	49010
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	49153
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	49318
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	49463
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	644
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	49644
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	49818
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	49978
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	50122
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	50396
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	50535
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	644
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	661
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	644
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	644
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	50698
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	661
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	644
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	661
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	50916
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	51079
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	51415
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	644
	.byte	2,0,2,35,3,0,31
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	51522
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	51974
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	52073
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	661
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	52223
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	644
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	52372
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	52533
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	661
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	52663
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	52795
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	661
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	52910
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	661
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	53021
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	644
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	644
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	644
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	53179
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	53591
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	661
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	6,0,2,35,3,0,31
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	53692
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	53959
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	54095
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	644
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	54206
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	54339
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	54542
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	644
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	644
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	644
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	54898
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	661
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	55076
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	644
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	644
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	55176
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	644
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	644
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	644
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	55546
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	55732
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	55930
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	644
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	56163
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	644
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	644
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	644
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	56315
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	644
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	644
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	644
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	644
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	56882
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	644
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	644
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	57176
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	644
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	644
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	644
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	57454
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	57950
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	661
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	58263
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	644
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	644
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	644
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	644
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	58472
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	644
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	3,0,2,35,3,0,31
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	58683
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	59115
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	644
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	644
	.byte	7,0,2,35,3,0,31
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	59211
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	59471
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	644
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	59596
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	59793
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	59946
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	60099
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	60252
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	508
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	683
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	927
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	492
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	60507
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	60633
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	60885
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42917
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	61104
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43474
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	61168
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43551
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	61232
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43687
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	61297
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43967
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	61362
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44205
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	61427
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44333
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	61492
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44576
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	61557
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44811
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	61622
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44939
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	61687
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45039
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	61752
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45139
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	61817
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45347
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	61881
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45512
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	61945
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45695
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	62009
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45849
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	62074
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46213
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	62136
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46424
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	62198
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46676
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	62260
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46794
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	62324
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46905
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	62389
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47068
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	62455
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47231
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	62521
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47389
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	62589
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47554
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	62656
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47883
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	62724
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48104
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	62792
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48267
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	62858
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48539
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	62925
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48692
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	62994
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48848
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	63063
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49010
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	63132
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49153
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	63201
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49318
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	63270
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49463
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	63339
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49644
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	63407
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49818
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	63475
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49978
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	63543
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50122
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	63611
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50396
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	63676
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50535
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	63741
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50698
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	63807
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50916
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	63871
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51079
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	63932
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51415
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	63993
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51522
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	64053
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51974
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	64115
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52073
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	64175
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52223
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	64237
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52372
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	64305
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52533
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	64373
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52663
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	64441
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52795
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	64505
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52910
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	64570
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53021
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	64633
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53179
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	64694
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53591
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	64758
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53692
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	64819
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53959
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	64883
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54095
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	64950
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54206
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	65013
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54339
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	65074
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54542
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	65136
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54898
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	65201
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55076
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	65266
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55176
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	65331
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55546
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	65400
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55732
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	65469
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55930
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	65538
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56163
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	65603
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56315
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	65666
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56882
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	65731
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57176
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	65796
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57454
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	65861
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57950
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	65927
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58472
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	65996
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58263
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	66060
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58683
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	66125
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59115
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	66190
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59211
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	66255
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59471
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	66319
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59596
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	66385
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59793
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	66449
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59946
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	66514
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60099
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	66579
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60252
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	66644
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	604
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	887
	.byte	31
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1118
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60507
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	66795
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60633
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	66862
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60885
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	66929
	.byte	14
	.word	1158
	.byte	31
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	66994
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	66795
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	66862
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	66929
	.byte	4,2,35,8,0,14
	.word	67023
	.byte	31
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	67084
	.byte	15,8
	.word	62260
	.byte	16,1,0,15,8
	.word	65603
	.byte	16,1,0,14
	.word	67023
	.byte	15,24
	.word	1158
	.byte	16,1,0,14
	.word	67134
	.byte	15,16
	.word	644
	.byte	16,15,0,15,40
	.word	644
	.byte	16,39,0,15,16
	.word	62074
	.byte	16,3,0,15,16
	.word	64053
	.byte	16,3,0,15,180,3
	.word	644
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4461
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	63993
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2642
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	64694
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	65538
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	65136
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	65201
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	65266
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	65469
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	65331
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	65400
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	61297
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	61362
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	63871
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	63807
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	61427
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	61492
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	61557
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	61622
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	66125
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2642
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	65996
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	61232
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	66319
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	66060
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2642
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	62858
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	67111
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	62324
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	66385
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	61687
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	61752
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	11499
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	65013
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	64175
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	64758
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	64633
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	64115
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	63611
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	62589
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	62389
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	62455
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	66255
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2642
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	65666
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	65861
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	65927
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	67120
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2642
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	62009
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	61881
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	65731
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	65796
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	67129
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	62198
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	67143
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4801
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	66644
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	66579
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	66449
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	66514
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2642
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	64441
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	64505
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	61817
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	64570
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4461
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	66190
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	67148
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	64237
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	64305
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	64373
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	11776
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	64950
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4461
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	63676
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	62521
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	63741
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	62792
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	62656
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2642
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	63339
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	63407
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	63475
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	63543
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	62925
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	62994
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	63063
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	63132
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	63201
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	63270
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	62724
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2642
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	64883
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	64819
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	67157
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	67166
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	62136
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	63932
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	65074
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	67175
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2642
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	61945
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	67184
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	61168
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	61104
	.byte	4,3,35,252,7,0,14
	.word	67195
	.byte	31
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	69185
	.byte	31
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9394
	.byte	31
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	9669
	.byte	31
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	9599
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,31
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	69288
	.byte	31
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9982
	.byte	20,5,190,1,9,8,13
	.byte	'port',0
	.word	9389
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	644
	.byte	1,2,35,4,0,31
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	69753
	.byte	31
	.byte	'gpio_pin_enum',0,25,89,2
	.word	35949
	.byte	31
	.byte	'soft_iic_info_struct',0,24,50,2
	.word	35841
	.byte	31
	.byte	'IfxDma_ChannelId',0,14,156,1,3
	.word	21909
	.byte	31
	.byte	'Ifx_DMA_ACCEN00_Bits',0,13,79,3
	.word	11785
	.byte	31
	.byte	'Ifx_DMA_ACCEN01_Bits',0,13,85,3
	.word	12355
	.byte	31
	.byte	'Ifx_DMA_ACCEN10_Bits',0,13,122,3
	.word	12445
	.byte	31
	.byte	'Ifx_DMA_ACCEN11_Bits',0,13,128,1,3
	.word	13015
	.byte	31
	.byte	'Ifx_DMA_ACCEN20_Bits',0,13,165,1,3
	.word	13105
	.byte	31
	.byte	'Ifx_DMA_ACCEN21_Bits',0,13,171,1,3
	.word	13676
	.byte	31
	.byte	'Ifx_DMA_ACCEN30_Bits',0,13,208,1,3
	.word	13767
	.byte	31
	.byte	'Ifx_DMA_ACCEN31_Bits',0,13,214,1,3
	.word	14338
	.byte	31
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,13,230,1,3
	.word	14956
	.byte	31
	.byte	'Ifx_DMA_BLK_EER_Bits',0,13,243,1,3
	.word	14440
	.byte	31
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,13,132,2,3
	.word	14664
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,13,152,2,3
	.word	16868
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,13,168,2,3
	.word	17184
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,13,184,2,3
	.word	17554
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,13,190,2,3
	.word	16779
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,13,199,2,3
	.word	15419
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,13,208,2,3
	.word	15554
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,13,217,2,3
	.word	15689
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,13,226,2,3
	.word	15824
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,13,235,2,3
	.word	15959
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,13,244,2,3
	.word	16094
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,13,253,2,3
	.word	16229
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,13,134,3,3
	.word	16364
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,13,140,3,3
	.word	16508
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,13,146,3,3
	.word	16690
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,13,152,3,3
	.word	16599
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,13,158,3,3
	.word	17463
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,13,169,3,3
	.word	15239
	.byte	31
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,13,189,3,3
	.word	20004
	.byte	31
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,13,205,3,3
	.word	20316
	.byte	31
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,13,226,3,3
	.word	20680
	.byte	31
	.byte	'Ifx_DMA_CH_DADR_Bits',0,13,232,3,3
	.word	19919
	.byte	31
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,13,238,3,3
	.word	19658
	.byte	31
	.byte	'Ifx_DMA_CH_SADR_Bits',0,13,244,3,3
	.word	19834
	.byte	31
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,13,250,3,3
	.word	19746
	.byte	31
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,13,128,4,3
	.word	20593
	.byte	31
	.byte	'Ifx_DMA_CLC_Bits',0,13,138,4,3
	.word	11220
	.byte	31
	.byte	'Ifx_DMA_ERRINTR_Bits',0,13,145,4,3
	.word	18384
	.byte	31
	.byte	'Ifx_DMA_HRR_Bits',0,13,152,4,3
	.word	18976
	.byte	31
	.byte	'Ifx_DMA_ID_Bits',0,13,160,4,3
	.word	11377
	.byte	31
	.byte	'Ifx_DMA_MEMCON_Bits',0,13,175,4,3
	.word	11508
	.byte	31
	.byte	'Ifx_DMA_MODE_Bits',0,13,182,4,3
	.word	18852
	.byte	31
	.byte	'Ifx_DMA_OTSS_Bits',0,13,191,4,3
	.word	18245
	.byte	31
	.byte	'Ifx_DMA_PRR0_Bits',0,13,200,4,3
	.word	18490
	.byte	31
	.byte	'Ifx_DMA_PRR1_Bits',0,13,209,4,3
	.word	18624
	.byte	31
	.byte	'Ifx_DMA_SUSACR_Bits',0,13,216,4,3
	.word	19216
	.byte	31
	.byte	'Ifx_DMA_SUSENR_Bits',0,13,223,4,3
	.word	19099
	.byte	31
	.byte	'Ifx_DMA_TIME_Bits',0,13,229,4,3
	.word	18758
	.byte	31
	.byte	'Ifx_DMA_TSR_Bits',0,13,248,4,3
	.word	19333
	.byte	31
	.byte	'Ifx_DMA_ACCEN00',0,13,133,5,3
	.word	12315
	.byte	31
	.byte	'Ifx_DMA_ACCEN01',0,13,141,5,3
	.word	12405
	.byte	31
	.byte	'Ifx_DMA_ACCEN10',0,13,149,5,3
	.word	12975
	.byte	31
	.byte	'Ifx_DMA_ACCEN11',0,13,157,5,3
	.word	13065
	.byte	31
	.byte	'Ifx_DMA_ACCEN20',0,13,165,5,3
	.word	13636
	.byte	31
	.byte	'Ifx_DMA_ACCEN21',0,13,173,5,3
	.word	13727
	.byte	31
	.byte	'Ifx_DMA_ACCEN30',0,13,181,5,3
	.word	14298
	.byte	31
	.byte	'Ifx_DMA_ACCEN31',0,13,189,5,3
	.word	14389
	.byte	31
	.byte	'Ifx_DMA_BLK_CLRE',0,13,197,5,3
	.word	15199
	.byte	31
	.byte	'Ifx_DMA_BLK_EER',0,13,205,5,3
	.word	14624
	.byte	31
	.byte	'Ifx_DMA_BLK_ERRSR',0,13,213,5,3
	.word	14916
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,13,221,5,3
	.word	17144
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,13,229,5,3
	.word	17423
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,13,237,5,3
	.word	17787
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_DADR',0,13,245,5,3
	.word	16828
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R0',0,13,253,5,3
	.word	15514
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R1',0,13,133,6,3
	.word	15649
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R2',0,13,141,6,3
	.word	15784
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R3',0,13,149,6,3
	.word	15919
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R4',0,13,157,6,3
	.word	16054
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R5',0,13,165,6,3
	.word	16189
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R6',0,13,173,6,3
	.word	16324
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_R7',0,13,181,6,3
	.word	16459
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,13,189,6,3
	.word	16559
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SADR',0,13,197,6,3
	.word	16739
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,13,205,6,3
	.word	16650
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,13,213,6,3
	.word	17514
	.byte	31
	.byte	'Ifx_DMA_BLK_ME_SR',0,13,221,6,3
	.word	15379
	.byte	31
	.byte	'Ifx_DMA_CH_ADICR',0,13,229,6,3
	.word	20276
	.byte	31
	.byte	'Ifx_DMA_CH_CHCFGR',0,13,237,6,3
	.word	20553
	.byte	31
	.byte	'Ifx_DMA_CH_CHCSR',0,13,245,6,3
	.word	20987
	.byte	31
	.byte	'Ifx_DMA_CH_DADR',0,13,253,6,3
	.word	19964
	.byte	31
	.byte	'Ifx_DMA_CH_RDCRCR',0,13,133,7,3
	.word	19706
	.byte	31
	.byte	'Ifx_DMA_CH_SADR',0,13,141,7,3
	.word	19879
	.byte	31
	.byte	'Ifx_DMA_CH_SDCRCR',0,13,149,7,3
	.word	19794
	.byte	31
	.byte	'Ifx_DMA_CH_SHADR',0,13,157,7,3
	.word	20640
	.byte	31
	.byte	'Ifx_DMA_CLC',0,13,165,7,3
	.word	11337
	.byte	31
	.byte	'Ifx_DMA_ERRINTR',0,13,173,7,3
	.word	18450
	.byte	31
	.byte	'Ifx_DMA_HRR',0,13,181,7,3
	.word	19038
	.byte	31
	.byte	'Ifx_DMA_ID',0,13,189,7,3
	.word	11459
	.byte	31
	.byte	'Ifx_DMA_MEMCON',0,13,197,7,3
	.word	11736
	.byte	31
	.byte	'Ifx_DMA_MODE',0,13,205,7,3
	.word	18916
	.byte	31
	.byte	'Ifx_DMA_OTSS',0,13,213,7,3
	.word	18344
	.byte	31
	.byte	'Ifx_DMA_PRR0',0,13,221,7,3
	.word	18584
	.byte	31
	.byte	'Ifx_DMA_PRR1',0,13,229,7,3
	.word	18718
	.byte	31
	.byte	'Ifx_DMA_SUSACR',0,13,237,7,3
	.word	19283
	.byte	31
	.byte	'Ifx_DMA_SUSENR',0,13,245,7,3
	.word	19166
	.byte	31
	.byte	'Ifx_DMA_TIME',0,13,253,7,3
	.word	18801
	.byte	31
	.byte	'Ifx_DMA_TSR',0,13,133,8,3
	.word	19608
	.byte	14
	.word	17827
	.byte	31
	.byte	'Ifx_DMA_BLK_ME',0,13,165,8,3
	.word	72644
	.byte	14
	.word	18120
	.byte	31
	.byte	'Ifx_DMA_BLK',0,13,185,8,3
	.word	72673
	.byte	14
	.word	21027
	.byte	31
	.byte	'Ifx_DMA_CH',0,13,198,8,3
	.word	72699
	.byte	14
	.word	21193
	.byte	31
	.byte	'Ifx_DMA',0,13,250,8,3
	.word	72724
	.byte	17,41,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,31
	.byte	'IfxSrc_Tos',0,41,74,3
	.word	72746
	.byte	31
	.byte	'Ifx_SRC_SRCR_Bits',0,8,62,3
	.word	10163
	.byte	31
	.byte	'Ifx_SRC_SRCR',0,8,75,3
	.word	10453
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,8,86,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	72871
	.byte	31
	.byte	'Ifx_SRC_AGBT',0,8,89,3
	.word	72903
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,8,92,25,12,13
	.byte	'TX',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10453
	.byte	4,2,35,8,0,14
	.word	72929
	.byte	31
	.byte	'Ifx_SRC_ASCLIN',0,8,97,3
	.word	72988
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,8,100,25,4,13
	.byte	'SBSRC',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	73016
	.byte	31
	.byte	'Ifx_SRC_BCUSPB',0,8,103,3
	.word	73053
	.byte	15,64
	.word	10453
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,8,106,25,64,13
	.byte	'INT',0
	.word	73081
	.byte	64,2,35,0,0,14
	.word	73090
	.byte	31
	.byte	'Ifx_SRC_CAN',0,8,109,3
	.word	73122
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,8,112,25,16,13
	.byte	'SR0',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10453
	.byte	4,2,35,12,0,14
	.word	73147
	.byte	31
	.byte	'Ifx_SRC_CCU6',0,8,118,3
	.word	73219
	.byte	15,8
	.word	10453
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,8,121,25,8,13
	.byte	'SR',0
	.word	73245
	.byte	8,2,35,0,0,14
	.word	73254
	.byte	31
	.byte	'Ifx_SRC_CERBERUS',0,8,124,3
	.word	73290
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,8,127,25,16,13
	.byte	'MI',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10453
	.byte	4,2,35,12,0,14
	.word	73320
	.byte	31
	.byte	'Ifx_SRC_CIF',0,8,133,1,3
	.word	73393
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,8,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	73419
	.byte	31
	.byte	'Ifx_SRC_CPU',0,8,139,1,3
	.word	73454
	.byte	15,192,1
	.word	10453
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,8,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4801
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	73480
	.byte	192,1,2,35,16,0,14
	.word	73490
	.byte	31
	.byte	'Ifx_SRC_DMA',0,8,147,1,3
	.word	73557
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,8,150,1,25,8,13
	.byte	'SRM',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10453
	.byte	4,2,35,4,0,14
	.word	73583
	.byte	31
	.byte	'Ifx_SRC_DSADC',0,8,154,1,3
	.word	73631
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,8,157,1,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	73659
	.byte	31
	.byte	'Ifx_SRC_EMEM',0,8,160,1,3
	.word	73692
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,8,163,1,25,80,13
	.byte	'INT',0
	.word	73245
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	73245
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	73245
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	73245
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10453
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10453
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	67157
	.byte	40,2,35,40,0,14
	.word	73719
	.byte	31
	.byte	'Ifx_SRC_ERAY',0,8,172,1,3
	.word	73846
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,8,175,1,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	73873
	.byte	31
	.byte	'Ifx_SRC_ETH',0,8,178,1,3
	.word	73905
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,8,181,1,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	73931
	.byte	31
	.byte	'Ifx_SRC_FCE',0,8,184,1,3
	.word	73963
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,8,187,1,25,12,13
	.byte	'DONE',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10453
	.byte	4,2,35,8,0,14
	.word	73989
	.byte	31
	.byte	'Ifx_SRC_FFT',0,8,192,1,3
	.word	74049
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,8,195,1,25,32,13
	.byte	'SR0',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10453
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	67148
	.byte	16,2,35,16,0,14
	.word	74075
	.byte	31
	.byte	'Ifx_SRC_GPSR',0,8,202,1,3
	.word	74169
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,8,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10453
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10453
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10453
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3832
	.byte	24,2,35,24,0,14
	.word	74196
	.byte	31
	.byte	'Ifx_SRC_GPT12',0,8,214,1,3
	.word	74313
	.byte	15,12
	.word	10453
	.byte	16,2,0,15,32
	.word	10453
	.byte	16,7,0,15,32
	.word	74350
	.byte	16,0,0,15,88
	.word	644
	.byte	16,87,0,15,108
	.word	10453
	.byte	16,26,0,15,96
	.word	74350
	.byte	16,2,0,15,160,3
	.word	644
	.byte	16,159,3,0,15,64
	.word	74350
	.byte	16,1,0,15,192,3
	.word	644
	.byte	16,191,3,0,15,16
	.word	10453
	.byte	16,3,0,15,64
	.word	74426
	.byte	16,3,0,15,52
	.word	644
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,8,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	74341
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2642
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10453
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10453
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	73245
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4461
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	74359
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	74368
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	74377
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	18236
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10453
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4801
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	74386
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	74395
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	74386
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	74395
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	74406
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	74415
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	74435
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	19088
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	74341
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	74444
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	74341
	.byte	12,3,35,192,18,0,14
	.word	74453
	.byte	31
	.byte	'Ifx_SRC_GTM',0,8,243,1,3
	.word	74913
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,8,246,1,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	74939
	.byte	31
	.byte	'Ifx_SRC_HSCT',0,8,249,1,3
	.word	74972
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,8,252,1,25,16,13
	.byte	'COK',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10453
	.byte	4,2,35,12,0,14
	.word	74999
	.byte	31
	.byte	'Ifx_SRC_HSSL',0,8,130,2,3
	.word	75072
	.byte	15,56
	.word	644
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,8,133,2,25,80,13
	.byte	'BREQ',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10453
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10453
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10453
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	75099
	.byte	56,2,35,24,0,14
	.word	75108
	.byte	31
	.byte	'Ifx_SRC_I2C',0,8,142,2,3
	.word	75231
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,8,145,2,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	75257
	.byte	31
	.byte	'Ifx_SRC_LMU',0,8,148,2,3
	.word	75289
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,8,151,2,25,20,13
	.byte	'SR0',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10453
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10453
	.byte	4,2,35,16,0,14
	.word	75315
	.byte	31
	.byte	'Ifx_SRC_MSC',0,8,158,2,3
	.word	75400
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,8,161,2,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	75426
	.byte	31
	.byte	'Ifx_SRC_PMU',0,8,164,2,3
	.word	75458
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,8,167,2,25,32,13
	.byte	'SR',0
	.word	74350
	.byte	32,2,35,0,0,14
	.word	75484
	.byte	31
	.byte	'Ifx_SRC_PSI5',0,8,170,2,3
	.word	75517
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,8,173,2,25,32,13
	.byte	'SR',0
	.word	74350
	.byte	32,2,35,0,0,14
	.word	75544
	.byte	31
	.byte	'Ifx_SRC_PSI5S',0,8,176,2,3
	.word	75578
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,8,179,2,25,24,13
	.byte	'TX',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10453
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10453
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10453
	.byte	4,2,35,20,0,14
	.word	75606
	.byte	31
	.byte	'Ifx_SRC_QSPI',0,8,187,2,3
	.word	75699
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,8,190,2,25,4,13
	.byte	'SR',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	75726
	.byte	31
	.byte	'Ifx_SRC_SCR',0,8,193,2,3
	.word	75758
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,8,196,2,25,20,13
	.byte	'DTS',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	74426
	.byte	16,2,35,4,0,14
	.word	75784
	.byte	31
	.byte	'Ifx_SRC_SCU',0,8,200,2,3
	.word	75830
	.byte	15,24
	.word	10453
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,8,203,2,25,24,13
	.byte	'SR',0
	.word	75856
	.byte	24,2,35,0,0,14
	.word	75865
	.byte	31
	.byte	'Ifx_SRC_SENT',0,8,206,2,3
	.word	75898
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,8,209,2,25,12,13
	.byte	'SR',0
	.word	74341
	.byte	12,2,35,0,0,14
	.word	75925
	.byte	31
	.byte	'Ifx_SRC_SMU',0,8,212,2,3
	.word	75957
	.byte	10
	.byte	'_Ifx_SRC_STM',0,8,215,2,25,8,13
	.byte	'SR0',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10453
	.byte	4,2,35,4,0,14
	.word	75983
	.byte	31
	.byte	'Ifx_SRC_STM',0,8,219,2,3
	.word	76029
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,8,222,2,25,16,13
	.byte	'SR0',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10453
	.byte	4,2,35,12,0,14
	.word	76055
	.byte	31
	.byte	'Ifx_SRC_VADCCG',0,8,228,2,3
	.word	76130
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,8,231,2,25,16,13
	.byte	'SR0',0
	.word	10453
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10453
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10453
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10453
	.byte	4,2,35,12,0,14
	.word	76159
	.byte	31
	.byte	'Ifx_SRC_VADCG',0,8,237,2,3
	.word	76233
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,8,240,2,25,4,13
	.byte	'SRC',0
	.word	10453
	.byte	4,2,35,0,0,14
	.word	76261
	.byte	31
	.byte	'Ifx_SRC_XBAR',0,8,243,2,3
	.word	76295
	.byte	15,4
	.word	72871
	.byte	16,0,0,14
	.word	76322
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,8,128,3,25,4,13
	.byte	'AGBT',0
	.word	76331
	.byte	4,2,35,0,0,14
	.word	76336
	.byte	31
	.byte	'Ifx_SRC_GAGBT',0,8,131,3,3
	.word	76372
	.byte	15,48
	.word	72929
	.byte	16,3,0,14
	.word	76400
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,8,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	76409
	.byte	48,2,35,0,0,14
	.word	76414
	.byte	31
	.byte	'Ifx_SRC_GASCLIN',0,8,137,3,3
	.word	76454
	.byte	14
	.word	73016
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,8,140,3,25,4,13
	.byte	'SPB',0
	.word	76484
	.byte	4,2,35,0,0,14
	.word	76489
	.byte	31
	.byte	'Ifx_SRC_GBCU',0,8,143,3,3
	.word	76523
	.byte	15,64
	.word	73090
	.byte	16,0,0,14
	.word	76550
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,8,146,3,25,64,13
	.byte	'CAN',0
	.word	76559
	.byte	64,2,35,0,0,14
	.word	76564
	.byte	31
	.byte	'Ifx_SRC_GCAN',0,8,149,3,3
	.word	76598
	.byte	15,32
	.word	73147
	.byte	16,1,0,14
	.word	76625
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,8,152,3,25,32,13
	.byte	'CCU6',0
	.word	76634
	.byte	32,2,35,0,0,14
	.word	76639
	.byte	31
	.byte	'Ifx_SRC_GCCU6',0,8,155,3,3
	.word	76675
	.byte	14
	.word	73254
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,8,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	76703
	.byte	8,2,35,0,0,14
	.word	76708
	.byte	31
	.byte	'Ifx_SRC_GCERBERUS',0,8,161,3,3
	.word	76752
	.byte	15,16
	.word	73320
	.byte	16,0,0,14
	.word	76784
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,8,164,3,25,16,13
	.byte	'CIF',0
	.word	76793
	.byte	16,2,35,0,0,14
	.word	76798
	.byte	31
	.byte	'Ifx_SRC_GCIF',0,8,167,3,3
	.word	76832
	.byte	15,8
	.word	73419
	.byte	16,1,0,14
	.word	76859
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,8,170,3,25,8,13
	.byte	'CPU',0
	.word	76868
	.byte	8,2,35,0,0,14
	.word	76873
	.byte	31
	.byte	'Ifx_SRC_GCPU',0,8,173,3,3
	.word	76907
	.byte	15,208,1
	.word	73490
	.byte	16,0,0,14
	.word	76934
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,8,176,3,25,208,1,13
	.byte	'DMA',0
	.word	76944
	.byte	208,1,2,35,0,0,14
	.word	76949
	.byte	31
	.byte	'Ifx_SRC_GDMA',0,8,179,3,3
	.word	76985
	.byte	14
	.word	73583
	.byte	14
	.word	73583
	.byte	14
	.word	73583
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,8,182,3,25,32,13
	.byte	'DSADC0',0
	.word	77012
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4461
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	77017
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	77022
	.byte	8,2,35,24,0,14
	.word	77027
	.byte	31
	.byte	'Ifx_SRC_GDSADC',0,8,188,3,3
	.word	77118
	.byte	15,4
	.word	73659
	.byte	16,0,0,14
	.word	77147
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,8,191,3,25,4,13
	.byte	'EMEM',0
	.word	77156
	.byte	4,2,35,0,0,14
	.word	77161
	.byte	31
	.byte	'Ifx_SRC_GEMEM',0,8,194,3,3
	.word	77197
	.byte	15,80
	.word	73719
	.byte	16,0,0,14
	.word	77225
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,8,197,3,25,80,13
	.byte	'ERAY',0
	.word	77234
	.byte	80,2,35,0,0,14
	.word	77239
	.byte	31
	.byte	'Ifx_SRC_GERAY',0,8,200,3,3
	.word	77275
	.byte	15,4
	.word	73873
	.byte	16,0,0,14
	.word	77303
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,8,203,3,25,4,13
	.byte	'ETH',0
	.word	77312
	.byte	4,2,35,0,0,14
	.word	77317
	.byte	31
	.byte	'Ifx_SRC_GETH',0,8,206,3,3
	.word	77351
	.byte	15,4
	.word	73931
	.byte	16,0,0,14
	.word	77378
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,8,209,3,25,4,13
	.byte	'FCE',0
	.word	77387
	.byte	4,2,35,0,0,14
	.word	77392
	.byte	31
	.byte	'Ifx_SRC_GFCE',0,8,212,3,3
	.word	77426
	.byte	15,12
	.word	73989
	.byte	16,0,0,14
	.word	77453
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,8,215,3,25,12,13
	.byte	'FFT',0
	.word	77462
	.byte	12,2,35,0,0,14
	.word	77467
	.byte	31
	.byte	'Ifx_SRC_GFFT',0,8,218,3,3
	.word	77501
	.byte	15,64
	.word	74075
	.byte	16,1,0,14
	.word	77528
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,8,221,3,25,64,13
	.byte	'GPSR',0
	.word	77537
	.byte	64,2,35,0,0,14
	.word	77542
	.byte	31
	.byte	'Ifx_SRC_GGPSR',0,8,224,3,3
	.word	77578
	.byte	15,48
	.word	74196
	.byte	16,0,0,14
	.word	77606
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,8,227,3,25,48,13
	.byte	'GPT12',0
	.word	77615
	.byte	48,2,35,0,0,14
	.word	77620
	.byte	31
	.byte	'Ifx_SRC_GGPT12',0,8,230,3,3
	.word	77658
	.byte	15,204,18
	.word	74453
	.byte	16,0,0,14
	.word	77687
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,8,233,3,25,204,18,13
	.byte	'GTM',0
	.word	77697
	.byte	204,18,2,35,0,0,14
	.word	77702
	.byte	31
	.byte	'Ifx_SRC_GGTM',0,8,236,3,3
	.word	77738
	.byte	15,4
	.word	74939
	.byte	16,0,0,14
	.word	77765
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,8,239,3,25,4,13
	.byte	'HSCT',0
	.word	77774
	.byte	4,2,35,0,0,14
	.word	77779
	.byte	31
	.byte	'Ifx_SRC_GHSCT',0,8,242,3,3
	.word	77815
	.byte	15,64
	.word	74999
	.byte	16,3,0,14
	.word	77843
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,8,245,3,25,68,13
	.byte	'HSSL',0
	.word	77852
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10453
	.byte	4,2,35,64,0,14
	.word	77857
	.byte	31
	.byte	'Ifx_SRC_GHSSL',0,8,249,3,3
	.word	77906
	.byte	15,80
	.word	75108
	.byte	16,0,0,14
	.word	77934
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,8,252,3,25,80,13
	.byte	'I2C',0
	.word	77943
	.byte	80,2,35,0,0,14
	.word	77948
	.byte	31
	.byte	'Ifx_SRC_GI2C',0,8,255,3,3
	.word	77982
	.byte	15,4
	.word	75257
	.byte	16,0,0,14
	.word	78009
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,8,130,4,25,4,13
	.byte	'LMU',0
	.word	78018
	.byte	4,2,35,0,0,14
	.word	78023
	.byte	31
	.byte	'Ifx_SRC_GLMU',0,8,133,4,3
	.word	78057
	.byte	15,40
	.word	75315
	.byte	16,1,0,14
	.word	78084
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,8,136,4,25,40,13
	.byte	'MSC',0
	.word	78093
	.byte	40,2,35,0,0,14
	.word	78098
	.byte	31
	.byte	'Ifx_SRC_GMSC',0,8,139,4,3
	.word	78132
	.byte	15,8
	.word	75426
	.byte	16,1,0,14
	.word	78159
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,8,142,4,25,8,13
	.byte	'PMU',0
	.word	78168
	.byte	8,2,35,0,0,14
	.word	78173
	.byte	31
	.byte	'Ifx_SRC_GPMU',0,8,145,4,3
	.word	78207
	.byte	15,32
	.word	75484
	.byte	16,0,0,14
	.word	78234
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,8,148,4,25,32,13
	.byte	'PSI5',0
	.word	78243
	.byte	32,2,35,0,0,14
	.word	78248
	.byte	31
	.byte	'Ifx_SRC_GPSI5',0,8,151,4,3
	.word	78284
	.byte	15,32
	.word	75544
	.byte	16,0,0,14
	.word	78312
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,8,154,4,25,32,13
	.byte	'PSI5S',0
	.word	78321
	.byte	32,2,35,0,0,14
	.word	78326
	.byte	31
	.byte	'Ifx_SRC_GPSI5S',0,8,157,4,3
	.word	78364
	.byte	15,96
	.word	75606
	.byte	16,3,0,14
	.word	78393
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,8,160,4,25,96,13
	.byte	'QSPI',0
	.word	78402
	.byte	96,2,35,0,0,14
	.word	78407
	.byte	31
	.byte	'Ifx_SRC_GQSPI',0,8,163,4,3
	.word	78443
	.byte	15,4
	.word	75726
	.byte	16,0,0,14
	.word	78471
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,8,166,4,25,4,13
	.byte	'SCR',0
	.word	78480
	.byte	4,2,35,0,0,14
	.word	78485
	.byte	31
	.byte	'Ifx_SRC_GSCR',0,8,169,4,3
	.word	78519
	.byte	14
	.word	75784
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,8,172,4,25,20,13
	.byte	'SCU',0
	.word	78546
	.byte	20,2,35,0,0,14
	.word	78551
	.byte	31
	.byte	'Ifx_SRC_GSCU',0,8,175,4,3
	.word	78585
	.byte	15,24
	.word	75865
	.byte	16,0,0,14
	.word	78612
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,8,178,4,25,24,13
	.byte	'SENT',0
	.word	78621
	.byte	24,2,35,0,0,14
	.word	78626
	.byte	31
	.byte	'Ifx_SRC_GSENT',0,8,181,4,3
	.word	78662
	.byte	15,12
	.word	75925
	.byte	16,0,0,14
	.word	78690
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,8,184,4,25,12,13
	.byte	'SMU',0
	.word	78699
	.byte	12,2,35,0,0,14
	.word	78704
	.byte	31
	.byte	'Ifx_SRC_GSMU',0,8,187,4,3
	.word	78738
	.byte	15,16
	.word	75983
	.byte	16,1,0,14
	.word	78765
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,8,190,4,25,16,13
	.byte	'STM',0
	.word	78774
	.byte	16,2,35,0,0,14
	.word	78779
	.byte	31
	.byte	'Ifx_SRC_GSTM',0,8,193,4,3
	.word	78813
	.byte	15,64
	.word	76159
	.byte	16,3,0,14
	.word	78840
	.byte	15,224,1
	.word	644
	.byte	16,223,1,0,15,32
	.word	76055
	.byte	16,1,0,14
	.word	78865
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,8,196,4,25,192,2,13
	.byte	'G',0
	.word	78849
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	78854
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	78874
	.byte	32,3,35,160,2,0,14
	.word	78879
	.byte	31
	.byte	'Ifx_SRC_GVADC',0,8,201,4,3
	.word	78948
	.byte	14
	.word	76261
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,8,204,4,25,4,13
	.byte	'XBAR',0
	.word	78976
	.byte	4,2,35,0,0,14
	.word	78981
	.byte	31
	.byte	'Ifx_SRC_GXBAR',0,8,207,4,3
	.word	79017
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,34,45,16,4,11
	.byte	'ADDR',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_A_Bits',0,34,48,3
	.word	79045
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,34,51,16,4,11
	.byte	'VSS',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	492
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BIV_Bits',0,34,55,3
	.word	79106
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,34,58,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	492
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_BTV_Bits',0,34,62,3
	.word	79185
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,34,65,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT_Bits',0,34,69,3
	.word	79271
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,34,72,16,4,11
	.byte	'CM',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	492
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	492
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	492
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	492
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL_Bits',0,34,80,3
	.word	79360
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,34,83,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT_Bits',0,34,89,3
	.word	79506
	.byte	31
	.byte	'Ifx_CPU_CORE_ID_Bits',0,34,96,3
	.word	40436
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,34,99,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L_Bits',0,34,103,3
	.word	79662
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,34,106,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U_Bits',0,34,110,3
	.word	79755
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,34,113,16,4,11
	.byte	'MODREV',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	492
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID_Bits',0,34,118,3
	.word	79848
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,34,121,16,4,11
	.byte	'XE',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE_Bits',0,34,125,3
	.word	79955
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,34,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT_Bits',0,34,136,1,3
	.word	80042
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,34,139,1,16,4,11
	.byte	'CID',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID_Bits',0,34,143,1,3
	.word	80196
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,34,146,1,16,4,11
	.byte	'DATA',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_D_Bits',0,34,149,1,3
	.word	80290
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,34,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	492
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	492
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	492
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	492
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DATR_Bits',0,34,163,1,3
	.word	80353
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,34,166,1,16,4,11
	.byte	'DE',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	492
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	492
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	492
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	492
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	492
	.byte	19,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR_Bits',0,34,177,1,3
	.word	80571
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,34,180,1,16,4,11
	.byte	'DTA',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	492
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR_Bits',0,34,184,1,3
	.word	80786
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,34,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0_Bits',0,34,192,1,3
	.word	80880
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,34,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2_Bits',0,34,199,1,3
	.word	80996
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,34,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	492
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_CPU_DCX_Bits',0,34,206,1,3
	.word	81097
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,34,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD_Bits',0,34,212,1,3
	.word	81190
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,34,215,1,16,4,11
	.byte	'TA',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR_Bits',0,34,218,1,3
	.word	81270
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,34,221,1,16,4,11
	.byte	'IED',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	492
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	492
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	492
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR_Bits',0,34,233,1,3
	.word	81339
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,34,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	492
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_DMS_Bits',0,34,240,1,3
	.word	81568
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,34,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L_Bits',0,34,247,1,3
	.word	81661
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,34,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	492
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U_Bits',0,34,254,1,3
	.word	81756
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,34,129,2,16,4,11
	.byte	'RE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE_Bits',0,34,133,2,3
	.word	81851
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,34,136,2,16,4,11
	.byte	'WE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE_Bits',0,34,140,2,3
	.word	81941
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,34,143,2,16,4,11
	.byte	'SRE',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	492
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	492
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	492
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	492
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	492
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	492
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	492
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	492
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	492
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR_Bits',0,34,161,2,3
	.word	82031
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,34,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT_Bits',0,34,172,2,3
	.word	82355
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,34,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	492
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FCX_Bits',0,34,180,2,3
	.word	82509
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,34,183,2,16,4,11
	.byte	'TST',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	492
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	492
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	492
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	492
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	492
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	492
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	492
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	492
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,34,202,2,3
	.word	82615
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,34,205,2,16,4,11
	.byte	'OPC',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	492
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	492
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	492
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,34,212,2,3
	.word	82964
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,34,215,2,16,4,11
	.byte	'PC',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,34,218,2,3
	.word	83124
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,34,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,34,224,2,3
	.word	83205
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,34,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,34,230,2,3
	.word	83292
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,34,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,34,236,2,3
	.word	83379
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,34,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT_Bits',0,34,243,2,3
	.word	83466
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,34,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	492
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	492
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	492
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	492
	.byte	6,0,2,35,0,0,31
	.byte	'Ifx_CPU_ICR_Bits',0,34,253,2,3
	.word	83557
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,34,128,3,16,4,11
	.byte	'ISP',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_ISP_Bits',0,34,131,3,3
	.word	83700
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,34,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	492
	.byte	12,0,2,35,0,0,31
	.byte	'Ifx_CPU_LCX_Bits',0,34,139,3,3
	.word	83766
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,34,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT_Bits',0,34,146,3,3
	.word	83872
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,34,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT_Bits',0,34,153,3,3
	.word	83965
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,34,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	492
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT_Bits',0,34,160,3,3
	.word	84058
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,34,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	492
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_CPU_PC_Bits',0,34,167,3,3
	.word	84151
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,34,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0_Bits',0,34,175,3,3
	.word	84236
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,34,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	492
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1_Bits',0,34,183,3,3
	.word	84352
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,34,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2_Bits',0,34,190,3,3
	.word	84463
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,34,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	492
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	492
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	492
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	492
	.byte	10,0,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI_Bits',0,34,200,3,3
	.word	84564
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,34,203,3,16,4,11
	.byte	'TA',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR_Bits',0,34,206,3,3
	.word	84694
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,34,209,3,16,4,11
	.byte	'IED',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	492
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	492
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	492
	.byte	18,0,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR_Bits',0,34,221,3,3
	.word	84763
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,34,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	492
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0_Bits',0,34,229,3,3
	.word	84992
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,34,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	492
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	492
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1_Bits',0,34,237,3,3
	.word	85105
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,34,240,3,16,4,11
	.byte	'PSI',0,4
	.word	492
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	492
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2_Bits',0,34,244,3,3
	.word	85218
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,34,247,3,16,4,11
	.byte	'FRE',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	492
	.byte	17,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR_Bits',0,34,129,4,3
	.word	85309
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,34,132,4,16,4,11
	.byte	'CDC',0,4
	.word	492
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	492
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	492
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	492
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	492
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	492
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	492
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_PSW_Bits',0,34,147,4,3
	.word	85512
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,34,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	492
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	492
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	492
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	492
	.byte	1,0,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN_Bits',0,34,156,4,3
	.word	85755
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,34,159,4,16,4,11
	.byte	'PC',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	492
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	492
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	492
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	492
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	492
	.byte	7,0,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON_Bits',0,34,171,4,3
	.word	85883
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,34,174,4,16,4,11
	.byte	'EN',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,34,177,4,3
	.word	86124
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,34,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,34,183,4,3
	.word	86207
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,34,186,4,16,4,11
	.byte	'EN',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,34,189,4,3
	.word	86298
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,34,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,34,195,4,3
	.word	86389
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,34,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,34,202,4,3
	.word	86488
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,34,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,34,209,4,3
	.word	86595
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,34,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT_Bits',0,34,220,4,3
	.word	86702
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,34,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON_Bits',0,34,231,4,3
	.word	86856
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,34,234,4,16,4,11
	.byte	'ASI',0,4
	.word	492
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	492
	.byte	27,0,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,34,238,4,3
	.word	87017
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,34,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	492
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	492
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	492
	.byte	15,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON_Bits',0,34,249,4,3
	.word	87115
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,34,252,4,16,4,11
	.byte	'Timer',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,34,255,4,3
	.word	87287
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,34,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	492
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR_Bits',0,34,133,5,3
	.word	87367
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,34,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	492
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	492
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	492
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	492
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	492
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	492
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	492
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	492
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	492
	.byte	3,0,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT_Bits',0,34,153,5,3
	.word	87440
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,34,156,5,16,4,11
	.byte	'T0',0,4
	.word	492
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	492
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	492
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	492
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	492
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	492
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	492
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	492
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	492
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,34,167,5,3
	.word	87758
	.byte	12,34,175,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79045
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_A',0,34,180,5,3
	.word	87953
	.byte	12,34,183,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79106
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BIV',0,34,188,5,3
	.word	88012
	.byte	12,34,191,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79185
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_BTV',0,34,196,5,3
	.word	88073
	.byte	12,34,199,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79271
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCNT',0,34,204,5,3
	.word	88134
	.byte	12,34,207,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79360
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CCTRL',0,34,212,5,3
	.word	88196
	.byte	12,34,215,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79506
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_COMPAT',0,34,220,5,3
	.word	88259
	.byte	31
	.byte	'Ifx_CPU_CORE_ID',0,34,228,5,3
	.word	40505
	.byte	12,34,231,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79662
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_L',0,34,236,5,3
	.word	88348
	.byte	12,34,239,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79755
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPR_U',0,34,244,5,3
	.word	88411
	.byte	12,34,247,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79848
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPU_ID',0,34,252,5,3
	.word	88474
	.byte	12,34,255,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79955
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CPXE',0,34,132,6,3
	.word	88538
	.byte	12,34,135,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80042
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CREVT',0,34,140,6,3
	.word	88600
	.byte	12,34,143,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80196
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_CUS_ID',0,34,148,6,3
	.word	88663
	.byte	12,34,151,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80290
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_D',0,34,156,6,3
	.word	88727
	.byte	12,34,159,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80353
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DATR',0,34,164,6,3
	.word	88786
	.byte	12,34,167,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80571
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGSR',0,34,172,6,3
	.word	88848
	.byte	12,34,175,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80786
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DBGTCR',0,34,180,6,3
	.word	88911
	.byte	12,34,183,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80880
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON0',0,34,188,6,3
	.word	88975
	.byte	12,34,191,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80996
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCON2',0,34,196,6,3
	.word	89038
	.byte	12,34,199,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81097
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DCX',0,34,204,6,3
	.word	89101
	.byte	12,34,207,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81190
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DEADD',0,34,212,6,3
	.word	89162
	.byte	12,34,215,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81270
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIEAR',0,34,220,6,3
	.word	89225
	.byte	12,34,223,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81339
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DIETR',0,34,228,6,3
	.word	89288
	.byte	12,34,231,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81568
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DMS',0,34,236,6,3
	.word	89351
	.byte	12,34,239,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81661
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_L',0,34,244,6,3
	.word	89412
	.byte	12,34,247,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81756
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPR_U',0,34,252,6,3
	.word	89475
	.byte	12,34,255,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81851
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPRE',0,34,132,7,3
	.word	89538
	.byte	12,34,135,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81941
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DPWE',0,34,140,7,3
	.word	89600
	.byte	12,34,143,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82031
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_DSTR',0,34,148,7,3
	.word	89662
	.byte	12,34,151,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82355
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_EXEVT',0,34,156,7,3
	.word	89724
	.byte	12,34,159,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82509
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FCX',0,34,164,7,3
	.word	89787
	.byte	12,34,167,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82615
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,34,172,7,3
	.word	89848
	.byte	12,34,175,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82964
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,34,180,7,3
	.word	89918
	.byte	12,34,183,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83124
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,34,188,7,3
	.word	89988
	.byte	12,34,191,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83205
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,34,196,7,3
	.word	90057
	.byte	12,34,199,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83292
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,34,204,7,3
	.word	90128
	.byte	12,34,207,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83379
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,34,212,7,3
	.word	90199
	.byte	12,34,215,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83466
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ICNT',0,34,220,7,3
	.word	90270
	.byte	12,34,223,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83557
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ICR',0,34,228,7,3
	.word	90332
	.byte	12,34,231,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83700
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_ISP',0,34,236,7,3
	.word	90393
	.byte	12,34,239,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83766
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_LCX',0,34,244,7,3
	.word	90454
	.byte	12,34,247,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83872
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M1CNT',0,34,252,7,3
	.word	90515
	.byte	12,34,255,7,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83965
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M2CNT',0,34,132,8,3
	.word	90578
	.byte	12,34,135,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84058
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_M3CNT',0,34,140,8,3
	.word	90641
	.byte	12,34,143,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84151
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PC',0,34,148,8,3
	.word	90704
	.byte	12,34,151,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84236
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON0',0,34,156,8,3
	.word	90764
	.byte	12,34,159,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84352
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON1',0,34,164,8,3
	.word	90827
	.byte	12,34,167,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84463
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCON2',0,34,172,8,3
	.word	90890
	.byte	12,34,175,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84564
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PCXI',0,34,180,8,3
	.word	90953
	.byte	12,34,183,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84694
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIEAR',0,34,188,8,3
	.word	91015
	.byte	12,34,191,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84763
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PIETR',0,34,196,8,3
	.word	91078
	.byte	12,34,199,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84992
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA0',0,34,204,8,3
	.word	91141
	.byte	12,34,207,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85105
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA1',0,34,212,8,3
	.word	91203
	.byte	12,34,215,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85218
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PMA2',0,34,220,8,3
	.word	91265
	.byte	12,34,223,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85309
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSTR',0,34,228,8,3
	.word	91327
	.byte	12,34,231,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85512
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_PSW',0,34,236,8,3
	.word	91389
	.byte	12,34,239,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85755
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SEGEN',0,34,244,8,3
	.word	91450
	.byte	12,34,247,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85883
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SMACON',0,34,252,8,3
	.word	91513
	.byte	12,34,255,8,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86124
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENA',0,34,132,9,3
	.word	91577
	.byte	12,34,135,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86207
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_ACCENB',0,34,140,9,3
	.word	91647
	.byte	12,34,143,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86298
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,34,148,9,3
	.word	91717
	.byte	12,34,151,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86389
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,34,156,9,3
	.word	91791
	.byte	12,34,159,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86488
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,34,164,9,3
	.word	91865
	.byte	12,34,167,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86595
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,34,172,9,3
	.word	91935
	.byte	12,34,175,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86702
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SWEVT',0,34,180,9,3
	.word	92005
	.byte	12,34,183,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86856
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_SYSCON',0,34,188,9,3
	.word	92068
	.byte	12,34,191,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87017
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TASK_ASI',0,34,196,9,3
	.word	92132
	.byte	12,34,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87115
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_CON',0,34,204,9,3
	.word	92198
	.byte	12,34,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87287
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TPS_TIMER',0,34,212,9,3
	.word	92263
	.byte	12,34,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87367
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_ADR',0,34,220,9,3
	.word	92330
	.byte	12,34,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87440
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TR_EVT',0,34,228,9,3
	.word	92394
	.byte	12,34,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87758
	.byte	4,2,35,0,0,31
	.byte	'Ifx_CPU_TRIG_ACC',0,34,236,9,3
	.word	92458
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,34,247,9,25,8,13
	.byte	'L',0
	.word	88348
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	88411
	.byte	4,2,35,4,0,14
	.word	92524
	.byte	31
	.byte	'Ifx_CPU_CPR',0,34,251,9,3
	.word	92566
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,34,254,9,25,8,13
	.byte	'L',0
	.word	89412
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	89475
	.byte	4,2,35,4,0,14
	.word	92592
	.byte	31
	.byte	'Ifx_CPU_DPR',0,34,130,10,3
	.word	92634
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,34,133,10,25,16,13
	.byte	'LA',0
	.word	91865
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	91935
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	91717
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	91791
	.byte	4,2,35,12,0,14
	.word	92660
	.byte	31
	.byte	'Ifx_CPU_SPROT_RGN',0,34,139,10,3
	.word	92742
	.byte	15,12
	.word	92263
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,34,142,10,25,16,13
	.byte	'CON',0
	.word	92198
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	92774
	.byte	12,2,35,4,0,14
	.word	92783
	.byte	31
	.byte	'Ifx_CPU_TPS',0,34,146,10,3
	.word	92831
	.byte	10
	.byte	'_Ifx_CPU_TR',0,34,149,10,25,8,13
	.byte	'EVT',0
	.word	92394
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	92330
	.byte	4,2,35,4,0,14
	.word	92857
	.byte	31
	.byte	'Ifx_CPU_TR',0,34,153,10,3
	.word	92902
	.byte	15,176,32
	.word	644
	.byte	16,175,32,0,15,208,223,1
	.word	644
	.byte	16,207,223,1,0,15,248,1
	.word	644
	.byte	16,247,1,0,15,244,29
	.word	644
	.byte	16,243,29,0,15,188,3
	.word	644
	.byte	16,187,3,0,15,232,3
	.word	644
	.byte	16,231,3,0,15,252,23
	.word	644
	.byte	16,251,23,0,15,228,63
	.word	644
	.byte	16,227,63,0,15,128,1
	.word	92592
	.byte	16,15,0,14
	.word	93017
	.byte	15,64
	.word	92524
	.byte	16,7,0,14
	.word	93032
	.byte	15,192,31
	.word	644
	.byte	16,191,31,0,15,16
	.word	88538
	.byte	16,3,0,15,16
	.word	89538
	.byte	16,3,0,15,16
	.word	89600
	.byte	16,3,0,15,208,7
	.word	644
	.byte	16,207,7,0,14
	.word	92783
	.byte	15,240,23
	.word	644
	.byte	16,239,23,0,15,64
	.word	92857
	.byte	16,7,0,14
	.word	93111
	.byte	15,192,23
	.word	644
	.byte	16,191,23,0,15,232,1
	.word	644
	.byte	16,231,1,0,15,180,1
	.word	644
	.byte	16,179,1,0,15,172,1
	.word	644
	.byte	16,171,1,0,15,64
	.word	88727
	.byte	16,15,0,15,64
	.word	644
	.byte	16,63,0,15,64
	.word	87953
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,34,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	92927
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	91450
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	92938
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	92132
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	92951
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	91141
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	91203
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	91265
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	92962
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	89038
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4461
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	91513
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	89662
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2642
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	88786
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	89162
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	89225
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	89288
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3832
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	88975
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	92973
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	91327
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	90827
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	90890
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	90764
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	91015
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	91078
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	92984
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	88259
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	92995
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	89848
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	89988
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	89918
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2642
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	90057
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	90128
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	90199
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	93006
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	93027
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	18220
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	93041
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	93046
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	93057
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	93066
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	93075
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	93084
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	93095
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	93100
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	93120
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	93125
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	88196
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	88134
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	90270
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	90515
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	90578
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	90641
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	93136
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	88848
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2642
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	89724
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	88600
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	92005
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	11776
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	92458
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4801
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	89351
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	89101
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	88911
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	93147
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	90953
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	91389
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	90704
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4461
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	92068
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	88474
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	40505
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	88012
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	88073
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	90393
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	90332
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4461
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	89787
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	90454
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	67148
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	88663
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	93158
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	93169
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	93178
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	93187
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	93178
	.byte	64,4,35,192,255,3,0,14
	.word	93196
	.byte	31
	.byte	'Ifx_CPU',0,34,130,11,3
	.word	94987
	.byte	31
	.byte	'IfxCpu_Id',0,10,132,1,3
	.word	10545
	.byte	31
	.byte	'IfxCpu_ResourceCpu',0,10,161,1,3
	.word	10627
	.byte	31
	.byte	'Ifx_STM_ACCEN0_Bits',0,19,79,3
	.word	33221
	.byte	31
	.byte	'Ifx_STM_ACCEN1_Bits',0,19,85,3
	.word	33132
	.byte	31
	.byte	'Ifx_STM_CAP_Bits',0,19,91,3
	.word	31662
	.byte	31
	.byte	'Ifx_STM_CAPSV_Bits',0,19,97,3
	.word	32539
	.byte	31
	.byte	'Ifx_STM_CLC_Bits',0,19,107,3
	.word	30785
	.byte	31
	.byte	'Ifx_STM_CMCON_Bits',0,19,120,3
	.word	31840
	.byte	31
	.byte	'Ifx_STM_CMP_Bits',0,19,126,3
	.word	31749
	.byte	31
	.byte	'Ifx_STM_ICR_Bits',0,19,139,1,3
	.word	32071
	.byte	31
	.byte	'Ifx_STM_ID_Bits',0,19,147,1,3
	.word	30941
	.byte	31
	.byte	'Ifx_STM_ISCR_Bits',0,19,157,1,3
	.word	32288
	.byte	31
	.byte	'Ifx_STM_KRST0_Bits',0,19,165,1,3
	.word	33009
	.byte	31
	.byte	'Ifx_STM_KRST1_Bits',0,19,172,1,3
	.word	32905
	.byte	31
	.byte	'Ifx_STM_KRSTCLR_Bits',0,19,179,1,3
	.word	32799
	.byte	31
	.byte	'Ifx_STM_OCS_Bits',0,19,189,1,3
	.word	32639
	.byte	31
	.byte	'Ifx_STM_TIM0_Bits',0,19,195,1,3
	.word	31063
	.byte	31
	.byte	'Ifx_STM_TIM0SV_Bits',0,19,201,1,3
	.word	32452
	.byte	31
	.byte	'Ifx_STM_TIM1_Bits',0,19,207,1,3
	.word	31148
	.byte	31
	.byte	'Ifx_STM_TIM2_Bits',0,19,213,1,3
	.word	31233
	.byte	31
	.byte	'Ifx_STM_TIM3_Bits',0,19,219,1,3
	.word	31318
	.byte	31
	.byte	'Ifx_STM_TIM4_Bits',0,19,225,1,3
	.word	31404
	.byte	31
	.byte	'Ifx_STM_TIM5_Bits',0,19,231,1,3
	.word	31490
	.byte	31
	.byte	'Ifx_STM_TIM6_Bits',0,19,237,1,3
	.word	31576
	.byte	31
	.byte	'Ifx_STM_ACCEN0',0,19,250,1,3
	.word	33750
	.byte	31
	.byte	'Ifx_STM_ACCEN1',0,19,130,2,3
	.word	33181
	.byte	31
	.byte	'Ifx_STM_CAP',0,19,138,2,3
	.word	31709
	.byte	31
	.byte	'Ifx_STM_CAPSV',0,19,146,2,3
	.word	32588
	.byte	31
	.byte	'Ifx_STM_CLC',0,19,154,2,3
	.word	30901
	.byte	31
	.byte	'Ifx_STM_CMCON',0,19,162,2,3
	.word	32031
	.byte	31
	.byte	'Ifx_STM_CMP',0,19,170,2,3
	.word	31791
	.byte	31
	.byte	'Ifx_STM_ICR',0,19,178,2,3
	.word	32248
	.byte	31
	.byte	'Ifx_STM_ID',0,19,186,2,3
	.word	31023
	.byte	31
	.byte	'Ifx_STM_ISCR',0,19,194,2,3
	.word	32412
	.byte	31
	.byte	'Ifx_STM_KRST0',0,19,202,2,3
	.word	33092
	.byte	31
	.byte	'Ifx_STM_KRST1',0,19,210,2,3
	.word	32969
	.byte	31
	.byte	'Ifx_STM_KRSTCLR',0,19,218,2,3
	.word	32865
	.byte	31
	.byte	'Ifx_STM_OCS',0,19,226,2,3
	.word	32759
	.byte	31
	.byte	'Ifx_STM_TIM0',0,19,234,2,3
	.word	31108
	.byte	31
	.byte	'Ifx_STM_TIM0SV',0,19,242,2,3
	.word	32499
	.byte	31
	.byte	'Ifx_STM_TIM1',0,19,250,2,3
	.word	31193
	.byte	31
	.byte	'Ifx_STM_TIM2',0,19,130,3,3
	.word	31278
	.byte	31
	.byte	'Ifx_STM_TIM3',0,19,138,3,3
	.word	31364
	.byte	31
	.byte	'Ifx_STM_TIM4',0,19,146,3,3
	.word	31450
	.byte	31
	.byte	'Ifx_STM_TIM5',0,19,154,3,3
	.word	31536
	.byte	31
	.byte	'Ifx_STM_TIM6',0,19,162,3,3
	.word	31622
	.byte	14
	.word	33790
	.byte	31
	.byte	'Ifx_STM',0,19,201,3,3
	.word	96139
	.byte	17,9,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,31
	.byte	'IfxCpu_CounterMode',0,9,148,1,3
	.word	96161
	.byte	20,9,160,1,9,6,13
	.byte	'counter',0
	.word	10949
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	644
	.byte	1,2,35,4,0,31
	.byte	'IfxCpu_Counter',0,9,164,1,3
	.word	96250
	.byte	20,9,172,1,9,32,13
	.byte	'instruction',0
	.word	96250
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	96250
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	96250
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	96250
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	96250
	.byte	6,2,35,24,0,31
	.byte	'IfxCpu_Perf',0,9,179,1,3
	.word	96316
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,42,45,16,4,11
	.byte	'EN0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,42,79,3
	.word	96434
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,42,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,42,85,3
	.word	96995
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,42,88,16,4,11
	.byte	'SEL',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,42,95,3
	.word	97076
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,42,98,16,4,11
	.byte	'VLD0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,42,111,3
	.word	97229
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,42,114,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	644
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,42,121,3
	.word	97477
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,42,124,16,4,11
	.byte	'STATUS',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0_Bits',0,42,128,1,3
	.word	97623
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,42,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM1_Bits',0,42,136,1,3
	.word	97721
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,42,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_COMM2_Bits',0,42,144,1,3
	.word	97837
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,42,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	469
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRD_Bits',0,42,153,1,3
	.word	97953
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,42,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	469
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCRP_Bits',0,42,162,1,3
	.word	98093
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,42,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	469
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	661
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_ECCW_Bits',0,42,171,1,3
	.word	98233
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,42,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	644
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	644
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	661
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	644
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	644
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FCON_Bits',0,42,193,1,3
	.word	98372
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,42,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	644
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	644
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	644
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FPRO_Bits',0,42,218,1,3
	.word	98734
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,42,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	661
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	644
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	644
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	644
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_FSR_Bits',0,42,254,1,3
	.word	99175
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,42,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	644
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	644
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_ID_Bits',0,42,134,2,3
	.word	99781
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,42,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	661
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARD_Bits',0,42,147,2,3
	.word	99892
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,42,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	661
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_MARP_Bits',0,42,159,2,3
	.word	100106
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,42,162,2,16,4,11
	.byte	'L',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	644
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	644
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	661
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	644
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCOND_Bits',0,42,179,2,3
	.word	100293
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,42,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	644
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,42,188,2,3
	.word	100617
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,42,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	661
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,42,199,2,3
	.word	100760
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,42,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	661
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	644
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	644
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	644
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	661
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,42,219,2,3
	.word	100949
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,42,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	644
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,42,254,2,3
	.word	101312
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,42,129,3,16,4,11
	.byte	'S0L',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONP_Bits',0,42,160,3,3
	.word	101907
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,42,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	644
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	644
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	644
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	644
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	644
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	644
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	644
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	644
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	644
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	644
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	644
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	644
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	644
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	644
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	644
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	644
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	644
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	644
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	644
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	644
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	644
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,42,194,3,3
	.word	102431
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,42,197,3,16,4,11
	.byte	'TAG',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,42,201,3,3
	.word	103013
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,42,204,3,16,4,11
	.byte	'TAG',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,42,208,3,3
	.word	103115
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,42,211,3,16,4,11
	.byte	'TAG',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,42,215,3,3
	.word	103217
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,42,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	469
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD_Bits',0,42,222,3,3
	.word	103319
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,42,225,3,16,4,11
	.byte	'STRT',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	644
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	644
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	644
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	661
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_FLASH_RRCT_Bits',0,42,236,3,3
	.word	103413
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,42,239,3,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0_Bits',0,42,242,3,3
	.word	103623
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,42,245,3,16,4,11
	.byte	'DATA',0,4
	.word	469
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1_Bits',0,42,248,3,3
	.word	103696
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,42,251,3,16,4,11
	.byte	'SEL',0,1
	.word	644
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	644
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	644
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	22,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,42,130,4,3
	.word	103769
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,42,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,42,137,4,3
	.word	103924
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,42,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	644
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	469
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	644
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	644
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	644
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,42,147,4,3
	.word	104029
	.byte	12,42,155,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96434
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN0',0,42,160,4,3
	.word	104177
	.byte	12,42,163,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96995
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ACCEN1',0,42,168,4,3
	.word	104243
	.byte	12,42,171,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97076
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_CFG',0,42,176,4,3
	.word	104309
	.byte	12,42,179,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97229
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_STAT',0,42,184,4,3
	.word	104377
	.byte	12,42,187,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97477
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_CBAB_TOP',0,42,192,4,3
	.word	104446
	.byte	12,42,195,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97623
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM0',0,42,200,4,3
	.word	104514
	.byte	12,42,203,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97721
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM1',0,42,208,4,3
	.word	104579
	.byte	12,42,211,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97837
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_COMM2',0,42,216,4,3
	.word	104644
	.byte	12,42,219,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97953
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRD',0,42,224,4,3
	.word	104709
	.byte	12,42,227,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98093
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCRP',0,42,232,4,3
	.word	104774
	.byte	12,42,235,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98233
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ECCW',0,42,240,4,3
	.word	104839
	.byte	12,42,243,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98372
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FCON',0,42,248,4,3
	.word	104903
	.byte	12,42,251,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98734
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FPRO',0,42,128,5,3
	.word	104967
	.byte	12,42,131,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99175
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_FSR',0,42,136,5,3
	.word	105031
	.byte	12,42,139,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99781
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_ID',0,42,144,5,3
	.word	105094
	.byte	12,42,147,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99892
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARD',0,42,152,5,3
	.word	105156
	.byte	12,42,155,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100106
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_MARP',0,42,160,5,3
	.word	105220
	.byte	12,42,163,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100293
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCOND',0,42,168,5,3
	.word	105284
	.byte	12,42,171,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100617
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONDBG',0,42,176,5,3
	.word	105351
	.byte	12,42,179,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100760
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSM',0,42,184,5,3
	.word	105420
	.byte	12,42,187,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100949
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,42,192,5,3
	.word	105489
	.byte	12,42,195,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101312
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONOTP',0,42,200,5,3
	.word	105562
	.byte	12,42,203,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101907
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONP',0,42,208,5,3
	.word	105631
	.byte	12,42,211,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	102431
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_PROCONWOP',0,42,216,5,3
	.word	105698
	.byte	12,42,219,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103013
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG0',0,42,224,5,3
	.word	105767
	.byte	12,42,227,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103115
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG1',0,42,232,5,3
	.word	105835
	.byte	12,42,235,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103217
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RDB_CFG2',0,42,240,5,3
	.word	105903
	.byte	12,42,243,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103319
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRAD',0,42,248,5,3
	.word	105971
	.byte	12,42,251,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103413
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRCT',0,42,128,6,3
	.word	106035
	.byte	12,42,131,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103623
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD0',0,42,136,6,3
	.word	106099
	.byte	12,42,139,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103696
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_RRD1',0,42,144,6,3
	.word	106163
	.byte	12,42,147,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103769
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_CFG',0,42,152,6,3
	.word	106227
	.byte	12,42,155,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103924
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_STAT',0,42,160,6,3
	.word	106295
	.byte	12,42,163,6,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	104029
	.byte	4,2,35,0,0,31
	.byte	'Ifx_FLASH_UBAB_TOP',0,42,168,6,3
	.word	106364
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,42,179,6,25,12,13
	.byte	'CFG',0
	.word	104309
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	104377
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	104446
	.byte	4,2,35,8,0,14
	.word	106432
	.byte	31
	.byte	'Ifx_FLASH_CBAB',0,42,184,6,3
	.word	106495
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,42,187,6,25,12,13
	.byte	'CFG0',0
	.word	105767
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	105835
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	105903
	.byte	4,2,35,8,0,14
	.word	106524
	.byte	31
	.byte	'Ifx_FLASH_RDB',0,42,192,6,3
	.word	106588
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,42,195,6,25,12,13
	.byte	'CFG',0
	.word	106227
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	106295
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	106364
	.byte	4,2,35,8,0,14
	.word	106616
	.byte	31
	.byte	'Ifx_FLASH_UBAB',0,42,200,6,3
	.word	106679
	.byte	14
	.word	67195
	.byte	3
	.word	106708
	.byte	20,43,74,15,20,13
	.byte	'module',0
	.word	106713
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	644
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	69753
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	40917
	.byte	1,2,35,16,0,30
	.word	106718
	.byte	31
	.byte	'IfxScu_Req_In',0,43,80,3
	.word	106788
	.byte	31
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,11,148,1,16
	.word	208
	.byte	20,11,212,5,9,8,13
	.byte	'value',0
	.word	10949
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10949
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_CcuconRegConfig',0,11,216,5,3
	.word	106855
	.byte	20,11,221,5,9,8,13
	.byte	'pDivider',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	644
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	644
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	265
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_InitialStepConfig',0,11,227,5,3
	.word	106926
	.byte	20,11,231,5,9,12,13
	.byte	'k2Step',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	265
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	106815
	.byte	4,2,35,8,0,31
	.byte	'IfxScuCcu_PllStepsConfig',0,11,236,5,3
	.word	107043
	.byte	3
	.word	205
	.byte	20,11,244,5,9,48,13
	.byte	'ccucon0',0
	.word	106855
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	106855
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	106855
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	106855
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	106855
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	106855
	.byte	8,2,35,40,0,31
	.byte	'IfxScuCcu_ClockDistributionConfig',0,11,252,5,3
	.word	107145
	.byte	20,11,128,6,9,8,13
	.byte	'value',0
	.word	10949
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10949
	.byte	4,2,35,4,0,31
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,11,132,6,3
	.word	107297
	.byte	3
	.word	107043
	.byte	20,11,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	107373
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	106926
	.byte	8,2,35,8,0,31
	.byte	'IfxScuCcu_SysPllConfig',0,11,142,6,3
	.word	107378
	.byte	17,12,104,9,1,18
	.byte	'IfxDma_ChannelIncrementCircular_none',0,0,18
	.byte	'IfxDma_ChannelIncrementCircular_2',0,1,18
	.byte	'IfxDma_ChannelIncrementCircular_4',0,2,18
	.byte	'IfxDma_ChannelIncrementCircular_8',0,3,18
	.byte	'IfxDma_ChannelIncrementCircular_16',0,4,18
	.byte	'IfxDma_ChannelIncrementCircular_32',0,5,18
	.byte	'IfxDma_ChannelIncrementCircular_64',0,6,18
	.byte	'IfxDma_ChannelIncrementCircular_128',0,7,18
	.byte	'IfxDma_ChannelIncrementCircular_256',0,8,18
	.byte	'IfxDma_ChannelIncrementCircular_512',0,9,18
	.byte	'IfxDma_ChannelIncrementCircular_1024',0,10,18
	.byte	'IfxDma_ChannelIncrementCircular_2048',0,11,18
	.byte	'IfxDma_ChannelIncrementCircular_4096',0,12,18
	.byte	'IfxDma_ChannelIncrementCircular_8192',0,13,18
	.byte	'IfxDma_ChannelIncrementCircular_16384',0,14,18
	.byte	'IfxDma_ChannelIncrementCircular_32768',0,15,0,31
	.byte	'IfxDma_ChannelIncrementCircular',0,12,122,3
	.word	107495
	.byte	17,12,127,9,1,18
	.byte	'IfxDma_ChannelIncrementDirection_negative',0,0,18
	.byte	'IfxDma_ChannelIncrementDirection_positive',0,1,0,31
	.byte	'IfxDma_ChannelIncrementDirection',0,12,131,1,3
	.word	108149
	.byte	17,12,136,1,9,1,18
	.byte	'IfxDma_ChannelIncrementStep_1',0,0,18
	.byte	'IfxDma_ChannelIncrementStep_2',0,1,18
	.byte	'IfxDma_ChannelIncrementStep_4',0,2,18
	.byte	'IfxDma_ChannelIncrementStep_8',0,3,18
	.byte	'IfxDma_ChannelIncrementStep_16',0,4,18
	.byte	'IfxDma_ChannelIncrementStep_32',0,5,18
	.byte	'IfxDma_ChannelIncrementStep_64',0,6,18
	.byte	'IfxDma_ChannelIncrementStep_128',0,7,0,31
	.byte	'IfxDma_ChannelIncrementStep',0,12,146,1,3
	.word	108285
	.byte	17,12,160,1,9,1,18
	.byte	'IfxDma_ChannelMove_1',0,0,18
	.byte	'IfxDma_ChannelMove_2',0,1,18
	.byte	'IfxDma_ChannelMove_4',0,2,18
	.byte	'IfxDma_ChannelMove_8',0,3,18
	.byte	'IfxDma_ChannelMove_16',0,4,18
	.byte	'IfxDma_ChannelMove_3',0,5,18
	.byte	'IfxDma_ChannelMove_5',0,6,18
	.byte	'IfxDma_ChannelMove_9',0,7,0,31
	.byte	'IfxDma_ChannelMove',0,12,170,1,3
	.word	108590
	.byte	17,12,175,1,9,1,18
	.byte	'IfxDma_ChannelMoveSize_8bit',0,0,18
	.byte	'IfxDma_ChannelMoveSize_16bit',0,1,18
	.byte	'IfxDma_ChannelMoveSize_32bit',0,2,18
	.byte	'IfxDma_ChannelMoveSize_64bit',0,3,18
	.byte	'IfxDma_ChannelMoveSize_128bit',0,4,18
	.byte	'IfxDma_ChannelMoveSize_256bit',0,5,0,31
	.byte	'IfxDma_ChannelMoveSize',0,12,183,1,3
	.word	108810
	.byte	17,12,239,1,9,1,18
	.byte	'IfxDma_ChannelShadow_none',0,0,18
	.byte	'IfxDma_ChannelShadow_src',0,1,18
	.byte	'IfxDma_ChannelShadow_dst',0,2,18
	.byte	'IfxDma_ChannelShadow_srcDirectWrite',0,5,18
	.byte	'IfxDma_ChannelShadow_dstDirectWrite',0,6,18
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingSwSwitch',0,8,18
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingHwSwSwitch',0,9,18
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingSwSwitch',0,10,18
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingHwSwSwitch',0,11,18
	.byte	'IfxDma_ChannelShadow_linkedList',0,12,18
	.byte	'IfxDma_ChannelShadow_accumulatedLinkedList',0,13,18
	.byte	'IfxDma_ChannelShadow_safeLinkedList',0,14,18
	.byte	'IfxDma_ChannelShadow_conditionalLinkedList',0,15,0,31
	.byte	'IfxDma_ChannelShadow',0,12,254,1,3
	.word	109036
	.byte	17,12,128,2,9,1,18
	.byte	'IfxDma_HardwareResourcePartition_0',0,0,18
	.byte	'IfxDma_HardwareResourcePartition_1',0,1,18
	.byte	'IfxDma_HardwareResourcePartition_2',0,2,18
	.byte	'IfxDma_HardwareResourcePartition_3',0,3,0,31
	.byte	'IfxDma_HardwareResourcePartition',0,12,134,2,3
	.word	109619
	.byte	17,12,138,2,9,1,18
	.byte	'IfxDma_MoveEngine_0',0,0,18
	.byte	'IfxDma_MoveEngine_1',0,1,0,31
	.byte	'IfxDma_MoveEngine',0,12,142,2,3
	.word	109816
	.byte	17,12,147,2,9,1,18
	.byte	'IfxDma_SleepMode_enable',0,0,18
	.byte	'IfxDma_SleepMode_disable',0,1,0,31
	.byte	'IfxDma_SleepMode',0,12,151,2,3
	.word	109894
	.byte	31
	.byte	'IfxScuEru_InputChannel',0,27,102,3
	.word	38250
	.byte	31
	.byte	'exti_pin_enum',0,29,61,2
	.word	38534
	.byte	31
	.byte	'exti_trigger_enum',0,29,70,2
	.word	38863
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,16,79,3
	.word	28896
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,16,85,3
	.word	28804
	.byte	31
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,16,97,3
	.word	24428
	.byte	31
	.byte	'Ifx_ASCLIN_BRD_Bits',0,16,106,3
	.word	25268
	.byte	31
	.byte	'Ifx_ASCLIN_BRG_Bits',0,16,115,3
	.word	25111
	.byte	31
	.byte	'Ifx_ASCLIN_CLC_Bits',0,16,125,3
	.word	23383
	.byte	31
	.byte	'Ifx_ASCLIN_CSR_Bits',0,16,133,1,3
	.word	28077
	.byte	31
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,16,145,1,3
	.word	24914
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,16,177,1,3
	.word	25924
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,16,209,1,3
	.word	26923
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,16,238,1,3
	.word	27438
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,16,142,2,3
	.word	26410
	.byte	31
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,16,158,2,3
	.word	24649
	.byte	31
	.byte	'Ifx_ASCLIN_ID_Bits',0,16,166,2,3
	.word	23837
	.byte	31
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,16,184,2,3
	.word	23542
	.byte	31
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,16,192,2,3
	.word	28678
	.byte	31
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,16,199,2,3
	.word	28571
	.byte	31
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,16,206,2,3
	.word	28462
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,16,213,2,3
	.word	25622
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,16,225,2,3
	.word	25422
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,16,232,2,3
	.word	25736
	.byte	31
	.byte	'Ifx_ASCLIN_OCS_Bits',0,16,242,2,3
	.word	28299
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,16,248,2,3
	.word	27990
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,16,254,2,3
	.word	28200
	.byte	31
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,16,140,3,3
	.word	24187
	.byte	31
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,16,146,3,3
	.word	27903
	.byte	31
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,16,159,3,3
	.word	23962
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN0',0,16,172,3,3
	.word	29428
	.byte	31
	.byte	'Ifx_ASCLIN_ACCEN1',0,16,180,3,3
	.word	28856
	.byte	31
	.byte	'Ifx_ASCLIN_BITCON',0,16,188,3,3
	.word	24609
	.byte	31
	.byte	'Ifx_ASCLIN_BRD',0,16,196,3,3
	.word	25382
	.byte	31
	.byte	'Ifx_ASCLIN_BRG',0,16,204,3,3
	.word	25228
	.byte	31
	.byte	'Ifx_ASCLIN_CLC',0,16,212,3,3
	.word	23502
	.byte	31
	.byte	'Ifx_ASCLIN_CSR',0,16,220,3,3
	.word	28160
	.byte	31
	.byte	'Ifx_ASCLIN_DATCON',0,16,228,3,3
	.word	25071
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGS',0,16,236,3,3
	.word	26370
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,16,244,3,3
	.word	27398
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,16,252,3,3
	.word	27863
	.byte	31
	.byte	'Ifx_ASCLIN_FLAGSSET',0,16,132,4,3
	.word	26883
	.byte	31
	.byte	'Ifx_ASCLIN_FRAMECON',0,16,140,4,3
	.word	24874
	.byte	31
	.byte	'Ifx_ASCLIN_ID',0,16,148,4,3
	.word	23922
	.byte	31
	.byte	'Ifx_ASCLIN_IOCR',0,16,156,4,3
	.word	23797
	.byte	31
	.byte	'Ifx_ASCLIN_KRST0',0,16,164,4,3
	.word	28764
	.byte	31
	.byte	'Ifx_ASCLIN_KRST1',0,16,172,4,3
	.word	28638
	.byte	31
	.byte	'Ifx_ASCLIN_KRSTCLR',0,16,180,4,3
	.word	28531
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,16,188,4,3
	.word	25696
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_CON',0,16,196,4,3
	.word	25582
	.byte	31
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,16,204,4,3
	.word	25811
	.byte	31
	.byte	'Ifx_ASCLIN_OCS',0,16,212,4,3
	.word	28422
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATA',0,16,220,4,3
	.word	28037
	.byte	31
	.byte	'Ifx_ASCLIN_RXDATAD',0,16,228,4,3
	.word	28248
	.byte	31
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,16,236,4,3
	.word	24388
	.byte	31
	.byte	'Ifx_ASCLIN_TXDATA',0,16,244,4,3
	.word	27950
	.byte	31
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,16,252,4,3
	.word	24147
	.byte	14
	.word	25851
	.byte	31
	.byte	'Ifx_ASCLIN_LIN',0,16,140,5,3
	.word	111658
	.byte	14
	.word	29468
	.byte	31
	.byte	'Ifx_ASCLIN',0,16,181,5,3
	.word	111687
	.byte	20,44,59,15,16,13
	.byte	'module',0
	.word	29915
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	69753
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	40917
	.byte	1,2,35,12,0,30
	.word	111712
	.byte	31
	.byte	'IfxAsclin_Cts_In',0,44,64,3
	.word	111763
	.byte	20,44,67,15,16,13
	.byte	'module',0
	.word	29915
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	69753
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	40917
	.byte	1,2,35,12,0,30
	.word	111793
	.byte	31
	.byte	'IfxAsclin_Rx_In',0,44,72,3
	.word	111844
	.byte	20,44,75,15,16,13
	.byte	'module',0
	.word	29915
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	69753
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9669
	.byte	1,2,35,12,0,30
	.word	111873
	.byte	31
	.byte	'IfxAsclin_Rts_Out',0,44,80,3
	.word	111924
	.byte	20,44,83,15,16,13
	.byte	'module',0
	.word	29915
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	69753
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9669
	.byte	1,2,35,12,0,30
	.word	111955
	.byte	31
	.byte	'IfxAsclin_Sclk_Out',0,44,88,3
	.word	112006
	.byte	20,44,91,15,16,13
	.byte	'module',0
	.word	29915
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	69753
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9669
	.byte	1,2,35,12,0,30
	.word	112038
	.byte	31
	.byte	'IfxAsclin_Slso_Out',0,44,96,3
	.word	112089
	.byte	20,44,99,15,16,13
	.byte	'module',0
	.word	29915
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	69753
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9669
	.byte	1,2,35,12,0,30
	.word	112121
	.byte	31
	.byte	'IfxAsclin_Tx_Out',0,44,104,3
	.word	112172
	.byte	17,15,82,9,1,18
	.byte	'IfxAsclin_Checksum_classic',0,0,18
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,31
	.byte	'IfxAsclin_Checksum',0,15,86,3
	.word	112202
	.byte	17,15,91,9,1,18
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,18
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,31
	.byte	'IfxAsclin_ChecksumInjection',0,15,95,3
	.word	112294
	.byte	17,15,101,9,1,18
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,18
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,31
	.byte	'IfxAsclin_ClockPolarity',0,15,105,3
	.word	112415
	.byte	17,15,110,9,1,18
	.byte	'IfxAsclin_ClockSource_noClock',0,0,18
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,18
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,18
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,18
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,18
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,31
	.byte	'IfxAsclin_ClockSource',0,15,118,3
	.word	112522
	.byte	31
	.byte	'IfxAsclin_CtsInputSelect',0,15,129,1,3
	.word	29982
	.byte	17,15,134,1,9,1,18
	.byte	'IfxAsclin_DataLength_1',0,0,18
	.byte	'IfxAsclin_DataLength_2',0,1,18
	.byte	'IfxAsclin_DataLength_3',0,2,18
	.byte	'IfxAsclin_DataLength_4',0,3,18
	.byte	'IfxAsclin_DataLength_5',0,4,18
	.byte	'IfxAsclin_DataLength_6',0,5,18
	.byte	'IfxAsclin_DataLength_7',0,6,18
	.byte	'IfxAsclin_DataLength_8',0,7,18
	.byte	'IfxAsclin_DataLength_9',0,8,18
	.byte	'IfxAsclin_DataLength_10',0,9,18
	.byte	'IfxAsclin_DataLength_11',0,10,18
	.byte	'IfxAsclin_DataLength_12',0,11,18
	.byte	'IfxAsclin_DataLength_13',0,12,18
	.byte	'IfxAsclin_DataLength_14',0,13,18
	.byte	'IfxAsclin_DataLength_15',0,14,18
	.byte	'IfxAsclin_DataLength_16',0,15,0,31
	.byte	'IfxAsclin_DataLength',0,15,152,1,3
	.word	112811
	.byte	17,15,157,1,9,1,18
	.byte	'IfxAsclin_FrameMode_initialise',0,0,18
	.byte	'IfxAsclin_FrameMode_asc',0,1,18
	.byte	'IfxAsclin_FrameMode_spi',0,2,18
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,31
	.byte	'IfxAsclin_FrameMode',0,15,163,1,3
	.word	113255
	.byte	17,15,168,1,9,1,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,18
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,31
	.byte	'IfxAsclin_HeaderResponseSelect',0,15,172,1,3
	.word	113402
	.byte	17,15,179,1,9,1,18
	.byte	'IfxAsclin_IdleDelay_0',0,0,18
	.byte	'IfxAsclin_IdleDelay_1',0,1,18
	.byte	'IfxAsclin_IdleDelay_2',0,2,18
	.byte	'IfxAsclin_IdleDelay_3',0,3,18
	.byte	'IfxAsclin_IdleDelay_4',0,4,18
	.byte	'IfxAsclin_IdleDelay_5',0,5,18
	.byte	'IfxAsclin_IdleDelay_6',0,6,18
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,31
	.byte	'IfxAsclin_IdleDelay',0,15,189,1,3
	.word	113544
	.byte	17,15,195,1,9,1,18
	.byte	'IfxAsclin_LeadDelay_0',0,0,18
	.byte	'IfxAsclin_LeadDelay_1',0,1,18
	.byte	'IfxAsclin_LeadDelay_2',0,2,18
	.byte	'IfxAsclin_LeadDelay_3',0,3,18
	.byte	'IfxAsclin_LeadDelay_4',0,4,18
	.byte	'IfxAsclin_LeadDelay_5',0,5,18
	.byte	'IfxAsclin_LeadDelay_6',0,6,18
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,31
	.byte	'IfxAsclin_LeadDelay',0,15,205,1,3
	.word	113772
	.byte	17,15,210,1,9,1,18
	.byte	'IfxAsclin_LinMode_slave',0,0,18
	.byte	'IfxAsclin_LinMode_master',0,1,0,31
	.byte	'IfxAsclin_LinMode',0,15,214,1,3
	.word	114000
	.byte	17,15,219,1,9,1,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,18
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,31
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,15,223,1,3
	.word	114087
	.byte	17,15,228,1,9,1,18
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,18
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,18
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,18
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,18
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,18
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,18
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,18
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,18
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,18
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,18
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,18
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,18
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,31
	.byte	'IfxAsclin_OversamplingFactor',0,15,243,1,3
	.word	114235
	.byte	17,15,248,1,9,1,18
	.byte	'IfxAsclin_ParityType_even',0,0,18
	.byte	'IfxAsclin_ParityType_odd',0,1,0,31
	.byte	'IfxAsclin_ParityType',0,15,252,1,3
	.word	114716
	.byte	17,15,129,2,9,1,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,18
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,31
	.byte	'IfxAsclin_ReceiveBufferMode',0,15,133,2,3
	.word	114808
	.byte	17,15,138,2,9,1,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,18
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,31
	.byte	'IfxAsclin_RtsCtsPolarity',0,15,142,2,3
	.word	114928
	.byte	17,15,147,2,9,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,18
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,31
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,15,165,2,3
	.word	115044
	.byte	17,15,170,2,9,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,18
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,18
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,18
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,31
	.byte	'IfxAsclin_RxFifoOutletWidth',0,15,176,2,3
	.word	115658
	.byte	31
	.byte	'IfxAsclin_RxInputSelect',0,15,191,2,3
	.word	30166
	.byte	17,15,196,2,9,1,18
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,18
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,18
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,18
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,18
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,18
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,18
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,18
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,18
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,18
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,18
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,18
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,18
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,18
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,18
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,31
	.byte	'IfxAsclin_SamplePointPosition',0,15,213,2,3
	.word	115863
	.byte	17,15,218,2,9,1,18
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,18
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,31
	.byte	'IfxAsclin_SamplesPerBit',0,15,222,2,3
	.word	116425
	.byte	17,15,228,2,9,1,18
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,18
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,31
	.byte	'IfxAsclin_ShiftDirection',0,15,232,2,3
	.word	116527
	.byte	17,15,238,2,9,1,18
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,18
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,31
	.byte	'IfxAsclin_SlavePolarity',0,15,242,2,3
	.word	116640
	.byte	17,15,247,2,9,1,18
	.byte	'IfxAsclin_SleepMode_enable',0,0,18
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,31
	.byte	'IfxAsclin_SleepMode',0,15,251,2,3
	.word	116749
	.byte	17,15,136,3,9,1,18
	.byte	'IfxAsclin_StopBit_0',0,0,18
	.byte	'IfxAsclin_StopBit_1',0,1,18
	.byte	'IfxAsclin_StopBit_2',0,2,18
	.byte	'IfxAsclin_StopBit_3',0,3,18
	.byte	'IfxAsclin_StopBit_4',0,4,18
	.byte	'IfxAsclin_StopBit_5',0,5,18
	.byte	'IfxAsclin_StopBit_6',0,6,18
	.byte	'IfxAsclin_StopBit_7',0,7,0,31
	.byte	'IfxAsclin_StopBit',0,15,146,3,3
	.word	116844
	.byte	17,15,150,3,9,1,18
	.byte	'IfxAsclin_SuspendMode_none',0,0,18
	.byte	'IfxAsclin_SuspendMode_hard',0,1,18
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,31
	.byte	'IfxAsclin_SuspendMode',0,15,155,3,3
	.word	117054
	.byte	17,15,160,3,9,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,18
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,18
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,18
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,31
	.byte	'IfxAsclin_TxFifoInletWidth',0,15,166,3,3
	.word	117179
	.byte	17,15,171,3,9,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,18
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,31
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,15,189,3,3
	.word	117346
	.byte	31
	.byte	'Ifx_Fifo_Shared',0,17,66,3
	.word	30483
	.byte	31
	.byte	'Ifx_Fifo',0,17,83,3
	.word	30574
	.byte	17,18,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,31
	.byte	'IfxStm_Comparator',0,18,155,1,3
	.word	118000
	.byte	17,18,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,31
	.byte	'IfxStm_ComparatorInterrupt',0,18,163,1,3
	.word	118078
	.byte	17,18,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,31
	.byte	'IfxStm_ComparatorOffset',0,18,201,1,3
	.word	118187
	.byte	17,18,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,31
	.byte	'IfxStm_ComparatorSize',0,18,239,1,3
	.word	119145
	.byte	17,18,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,31
	.byte	'IfxStm_SleepMode',0,18,248,1,3
	.word	120165
	.byte	17,18,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,31
	.byte	'IfxStm_SuspendMode',0,18,129,2,3
	.word	120251
	.byte	31
	.byte	'IfxStdIf_InterfaceDriver',0,45,118,15
	.word	383
	.byte	3
	.word	30458
	.byte	34
	.word	644
	.byte	1,1,22
	.word	383
	.byte	22
	.word	383
	.byte	22
	.word	120397
	.byte	22
	.word	34431
	.byte	0,3
	.word	120402
	.byte	31
	.byte	'IfxStdIf_DPipe_Write',0,46,92,19
	.word	120430
	.byte	31
	.byte	'IfxStdIf_DPipe_Read',0,46,107,19
	.word	120430
	.byte	34
	.word	30471
	.byte	1,1,22
	.word	383
	.byte	0,3
	.word	120492
	.byte	31
	.byte	'IfxStdIf_DPipe_GetReadCount',0,46,115,18
	.word	120505
	.byte	14
	.word	644
	.byte	3
	.word	120546
	.byte	34
	.word	120551
	.byte	1,1,22
	.word	383
	.byte	0,3
	.word	120556
	.byte	31
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,46,123,36
	.word	120569
	.byte	31
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,46,147,1,18
	.word	120505
	.byte	3
	.word	120556
	.byte	31
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,46,155,1,37
	.word	120648
	.byte	34
	.word	644
	.byte	1,1,22
	.word	383
	.byte	22
	.word	30458
	.byte	22
	.word	34431
	.byte	0,3
	.word	120691
	.byte	31
	.byte	'IfxStdIf_DPipe_CanReadCount',0,46,166,1,19
	.word	120714
	.byte	31
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,46,177,1,19
	.word	120714
	.byte	34
	.word	644
	.byte	1,1,22
	.word	383
	.byte	22
	.word	34431
	.byte	0,3
	.word	120794
	.byte	31
	.byte	'IfxStdIf_DPipe_FlushTx',0,46,186,1,19
	.word	120812
	.byte	35,1,1,22
	.word	383
	.byte	0,3
	.word	120849
	.byte	31
	.byte	'IfxStdIf_DPipe_ClearTx',0,46,200,1,16
	.word	120858
	.byte	31
	.byte	'IfxStdIf_DPipe_ClearRx',0,46,193,1,16
	.word	120858
	.byte	31
	.byte	'IfxStdIf_DPipe_OnReceive',0,46,208,1,16
	.word	120858
	.byte	31
	.byte	'IfxStdIf_DPipe_OnTransmit',0,46,215,1,16
	.word	120858
	.byte	31
	.byte	'IfxStdIf_DPipe_OnError',0,46,222,1,16
	.word	120858
	.byte	34
	.word	10949
	.byte	1,1,22
	.word	383
	.byte	0,3
	.word	121028
	.byte	31
	.byte	'IfxStdIf_DPipe_GetSendCount',0,46,131,1,18
	.word	121041
	.byte	34
	.word	34431
	.byte	1,1,22
	.word	383
	.byte	0,3
	.word	121083
	.byte	31
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,46,139,1,24
	.word	121096
	.byte	31
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,46,229,1,16
	.word	120858
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,46,233,1,8,76,13
	.byte	'driver',0
	.word	120364
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	644
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	120435
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	120464
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	120510
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	120574
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	120610
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	120653
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	120719
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	120756
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	120817
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	120863
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	120895
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	120927
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	120961
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	120996
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	121046
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	121101
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	121140
	.byte	4,2,35,72,0,31
	.byte	'IfxStdIf_DPipe',0,46,71,32
	.word	121179
	.byte	3
	.word	377
	.byte	3
	.word	120402
	.byte	3
	.word	120402
	.byte	3
	.word	120492
	.byte	3
	.word	120556
	.byte	3
	.word	120492
	.byte	3
	.word	120556
	.byte	3
	.word	120691
	.byte	3
	.word	120691
	.byte	3
	.word	120794
	.byte	3
	.word	120849
	.byte	3
	.word	120849
	.byte	3
	.word	120849
	.byte	3
	.word	120849
	.byte	3
	.word	120849
	.byte	3
	.word	121028
	.byte	3
	.word	121083
	.byte	3
	.word	120849
	.byte	14
	.word	644
	.byte	3
	.word	121692
	.byte	31
	.byte	'IfxStdIf_DPipe_WriteEvent',0,46,73,32
	.word	121697
	.byte	31
	.byte	'IfxStdIf_DPipe_ReadEvent',0,46,74,32
	.word	121697
	.byte	20,47,252,1,9,1,11
	.byte	'parityError',0,1
	.word	644
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	644
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	644
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	644
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	644
	.byte	1,3,2,35,0,0,31
	.byte	'IfxAsclin_Asc_ErrorFlags',0,47,131,2,3
	.word	121769
	.byte	20,47,137,2,9,8,13
	.byte	'baudrate',0
	.word	265
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	661
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	114235
	.byte	1,2,35,6,0,31
	.byte	'IfxAsclin_Asc_BaudRate',0,47,142,2,3
	.word	121934
	.byte	20,47,146,2,9,2,13
	.byte	'medianFilter',0
	.word	116425
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	115863
	.byte	1,2,35,1,0,31
	.byte	'IfxAsclin_Asc_BitTimingControl',0,47,150,2,3
	.word	122032
	.byte	20,47,154,2,9,6,13
	.byte	'inWidth',0
	.word	117179
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	115658
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	117346
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	115044
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	114808
	.byte	1,2,35,4,0,31
	.byte	'IfxAsclin_Asc_FifoControl',0,47,161,2,3
	.word	122130
	.byte	20,47,165,2,9,8,13
	.byte	'idleDelay',0
	.word	113544
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	116844
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	113255
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	116527
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	114716
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	112811
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	644
	.byte	1,2,35,6,0,31
	.byte	'IfxAsclin_Asc_FrameControl',0,47,174,2,3
	.word	122285
	.byte	20,47,178,2,9,8,13
	.byte	'txPriority',0
	.word	661
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	661
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	661
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	72746
	.byte	1,2,35,6,0,31
	.byte	'IfxAsclin_Asc_InterruptConfig',0,47,184,2,3
	.word	122460
	.byte	30
	.word	111712
	.byte	3
	.word	122589
	.byte	30
	.word	111793
	.byte	3
	.word	122599
	.byte	30
	.word	111873
	.byte	3
	.word	122609
	.byte	30
	.word	112121
	.byte	3
	.word	122619
	.byte	20,47,188,2,9,32,13
	.byte	'cts',0
	.word	122594
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9394
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	122604
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9394
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	122614
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9599
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	122624
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9599
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	69288
	.byte	1,2,35,29,0,31
	.byte	'IfxAsclin_Asc_Pins',0,47,199,2,3
	.word	122629
	.byte	12,47,205,2,9,1,13
	.byte	'ALL',0
	.word	644
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	121769
	.byte	1,2,35,0,0,31
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,47,209,2,3
	.word	122799
	.byte	31
	.byte	'uart_index_enum',0,30,109,2
	.word	39735
	.byte	31
	.byte	'camera_type_enum',0,31,51,2
	.word	39908
	.byte	36
	.byte	'camera_receiver_fifo',0,32,47,20
	.word	34935
	.byte	1,1
.L192:
	.byte	17,48,128,1,9,1,18
	.byte	'MT9V03X_UART',0,0,18
	.byte	'MT9V03X_SCCB',0,1,0,31
	.byte	'm9v03x_type_enum',0,48,132,1,2
	.word	122953
.L190:
	.byte	14
	.word	644
	.byte	15,188,1
	.word	644
	.byte	16,187,1,0
.L191:
	.byte	15,160,176,1
	.word	123021
	.byte	16,119,0,15,4
	.word	30458
	.byte	16,1,0
.L194:
	.byte	15,40
	.word	123043
	.byte	16,9,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,3,8,73
	.byte	19,54,15,39,12,63,12,60,12,0,0,22,5,0,73,19,0,0,23,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,24,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,25,46,1,49,19,0,0,26,5,0,49,19,0,0
	.byte	27,29,1,49,19,0,0,28,11,0,49,19,0,0,29,11,1,49,19,0,0,30,38,0,73,19,0,0,31,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,32,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,33,21,0,54,15,0,0,34,21,1,73,19,54,15,39
	.byte	12,0,0,35,21,1,54,15,39,12,0,0,36,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L54:
	.word	.L243-.L242
.L242:
	.half	3
	.word	.L245-.L244
.L244:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IFXPORT.h',0,2,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'IfxDma.h',0,3,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxDma_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'zf_common_fifo.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxPort_cfg.h',0
	.byte	0,0,0
	.byte	'zf_driver_soft_iic.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,5,0,0
	.byte	'zf_driver_delay.h',0,5,0,0
	.byte	'IfxScuEru.h',0,6,0,0
	.byte	'zf_driver_dma.h',0,5,0,0
	.byte	'zf_driver_exti.h',0,5,0,0
	.byte	'zf_driver_uart.h',0,5,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_type.h',0,0,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_camera.h',0,0,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_config.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0
	.byte	'stdio.h',0,7,0,0
	.byte	'Platform_Types.h',0,8,0,0
	.byte	'ifx_types.h',0,8,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,9,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_mt9v03x.h',0,0,0,0,0
.L245:
.L243:
	.sdecl	'.debug_info',debug,cluster('mt9v03x_get_version')
	.sect	'.debug_info'
.L55:
	.word	393
	.half	3
	.word	.L56
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L58,.L57
	.byte	2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_get_version',0,1,179,2,8
	.word	.L108
	.byte	1,1,1
	.word	.L44,.L109,.L43
	.byte	4
	.word	.L44,.L109
	.byte	5
	.byte	'temp',0,1,181,2,12
	.word	.L108,.L110
	.byte	5
	.byte	'uart_buffer',0,1,182,2,12
	.word	.L111,.L112
	.byte	5
	.byte	'timeout_count',0,1,183,2,12
	.word	.L108,.L113
	.byte	5
	.byte	'return_value',0,1,184,2,12
	.word	.L108,.L114
	.byte	5
	.byte	'uart_buffer_index',0,1,185,2,12
	.word	.L115,.L116
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_get_version')
	.sect	'.debug_abbrev'
.L56:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mt9v03x_get_version')
	.sect	'.debug_line'
.L57:
	.word	.L247-.L246
.L246:
	.half	3
	.word	.L249-.L248
.L248:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0,0
.L249:
	.byte	5,8,7,0,5,2
	.word	.L44
	.byte	3,178,2,1,5,26,9
	.half	.L198-.L44
	.byte	3,4,1,5,25,9
	.half	.L199-.L198
	.byte	3,1,1,5,32,9
	.half	.L201-.L199
	.byte	3,1,1,5,30,9
	.half	.L250-.L201
	.byte	1,5,22,9
	.half	.L251-.L250
	.byte	3,2,1,5,20,9
	.half	.L252-.L251
	.byte	1,5,22,9
	.half	.L253-.L252
	.byte	3,1,1,5,20,9
	.half	.L254-.L253
	.byte	1,5,10,9
	.half	.L255-.L254
	.byte	3,1,1,5,27,9
	.half	.L203-.L255
	.byte	3,1,1,5,20,9
	.half	.L256-.L203
	.byte	1,9
	.half	.L257-.L256
	.byte	3,1,1,5,23,9
	.half	.L258-.L257
	.byte	3,1,1,5,41,9
	.half	.L259-.L258
	.byte	1,5,54,9
	.half	.L260-.L259
	.byte	1,5,28,9
	.half	.L13-.L260
	.byte	3,4,1,5,9,9
	.half	.L261-.L13
	.byte	1,5,33,7,9
	.half	.L262-.L261
	.byte	3,2,1,5,31,9
	.half	.L263-.L262
	.byte	1,9
	.half	.L264-.L263
	.byte	3,1,1,5,53,9
	.half	.L265-.L264
	.byte	1,5,67,9
	.half	.L266-.L265
	.byte	1,5,86,9
	.half	.L267-.L266
	.byte	1,5,39,9
	.half	.L268-.L267
	.byte	3,1,1,5,43,9
	.half	.L269-.L268
	.byte	1,5,61,9
	.half	.L202-.L269
	.byte	1,5,48,9
	.half	.L204-.L202
	.byte	1,5,13,9
	.half	.L270-.L204
	.byte	3,1,1,5,25,9
	.half	.L14-.L270
	.byte	3,2,1,5,49,9
	.half	.L271-.L14
	.byte	3,1,1,5,12,9
	.half	.L205-.L271
	.byte	1,5,52,9
	.half	.L272-.L205
	.byte	1,5,5,7,9
	.half	.L15-.L272
	.byte	3,1,1,5,1,9
	.half	.L16-.L15
	.byte	3,1,1,7,9
	.half	.L59-.L16
	.byte	0,1,1
.L247:
	.sdecl	'.debug_ranges',debug,cluster('mt9v03x_get_version')
	.sect	'.debug_ranges'
.L58:
	.word	-1,.L44,0,.L59-.L44,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_set_exposure_time')
	.sect	'.debug_info'
.L60:
	.word	428
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L63,.L62
	.byte	2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_set_exposure_time',0,1,217,2,7
	.word	.L117
	.byte	1,1,1
	.word	.L46,.L118,.L45
	.byte	4
	.byte	'light',0,1,217,2,41
	.word	.L108,.L119
	.byte	5
	.word	.L46,.L118
	.byte	6
	.byte	'return_state',0,1,219,2,11
	.word	.L117,.L120
	.byte	5
	.word	.L121,.L122
	.byte	6
	.byte	'uart_buffer',0,1,223,2,16
	.word	.L111,.L123
	.byte	6
	.byte	'temp',0,1,224,2,16
	.word	.L108,.L124
	.byte	6
	.byte	'timeout_count',0,1,225,2,16
	.word	.L108,.L125
	.byte	6
	.byte	'uart_buffer_index',0,1,226,2,16
	.word	.L115,.L126
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_set_exposure_time')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mt9v03x_set_exposure_time')
	.sect	'.debug_line'
.L62:
	.word	.L274-.L273
.L273:
	.half	3
	.word	.L276-.L275
.L275:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0,0
.L276:
	.byte	5,7,7,0,5,2
	.word	.L46
	.byte	3,216,2,1,5,24,9
	.half	.L209-.L46
	.byte	3,2,1,9
	.half	.L210-.L209
	.byte	3,1,1,5,5,9
	.half	.L277-.L210
	.byte	1,5,25,7,9
	.half	.L278-.L277
	.byte	3,2,1,5,43,9
	.half	.L208-.L278
	.byte	1,5,66,9
	.half	.L279-.L208
	.byte	1,5,87,9
	.half	.L280-.L279
	.byte	1,5,30,9
	.half	.L121-.L280
	.byte	3,3,1,5,36,9
	.half	.L212-.L121
	.byte	3,1,1,5,34,9
	.half	.L281-.L212
	.byte	1,5,26,9
	.half	.L282-.L281
	.byte	3,2,1,5,24,9
	.half	.L283-.L282
	.byte	1,5,26,9
	.half	.L284-.L283
	.byte	3,1,1,5,24,9
	.half	.L285-.L284
	.byte	1,5,14,9
	.half	.L286-.L285
	.byte	3,1,1,5,31,9
	.half	.L214-.L286
	.byte	3,1,1,5,24,9
	.half	.L287-.L214
	.byte	1,9
	.half	.L288-.L287
	.byte	3,1,1,5,27,9
	.half	.L289-.L288
	.byte	3,1,1,5,45,9
	.half	.L290-.L289
	.byte	1,5,58,9
	.half	.L291-.L290
	.byte	1,5,32,9
	.half	.L18-.L291
	.byte	3,4,1,5,13,9
	.half	.L292-.L18
	.byte	1,5,37,7,9
	.half	.L293-.L292
	.byte	3,2,1,5,35,9
	.half	.L294-.L293
	.byte	1,9
	.half	.L295-.L294
	.byte	3,1,1,5,57,9
	.half	.L296-.L295
	.byte	1,5,71,9
	.half	.L297-.L296
	.byte	1,5,90,9
	.half	.L298-.L297
	.byte	1,5,35,9
	.half	.L299-.L298
	.byte	3,1,1,5,39,9
	.half	.L300-.L299
	.byte	1,5,57,9
	.half	.L215-.L300
	.byte	1,5,44,9
	.half	.L216-.L215
	.byte	1,5,17,9
	.half	.L301-.L216
	.byte	3,1,1,5,29,9
	.half	.L19-.L301
	.byte	3,2,1,5,53,9
	.half	.L302-.L19
	.byte	3,1,1,5,16,9
	.half	.L217-.L302
	.byte	1,5,56,9
	.half	.L303-.L217
	.byte	1,5,12,7,9
	.half	.L20-.L303
	.byte	3,1,1,5,32,7,9
	.half	.L304-.L20
	.byte	1,5,53,9
	.half	.L305-.L304
	.byte	1,5,26,7,9
	.half	.L21-.L305
	.byte	3,2,1,5,25,9
	.half	.L22-.L21
	.byte	3,2,1,5,43,9
	.half	.L306-.L22
	.byte	1,5,66,9
	.half	.L307-.L306
	.byte	1,5,87,9
	.half	.L308-.L307
	.byte	1,5,92,9
	.half	.L122-.L308
	.byte	1,5,55,9
	.half	.L17-.L122
	.byte	3,4,1,5,22,9
	.half	.L211-.L17
	.byte	1,5,5,9
	.half	.L23-.L211
	.byte	3,2,1,5,1,9
	.half	.L24-.L23
	.byte	3,1,1,7,9
	.half	.L64-.L24
	.byte	0,1,1
.L274:
	.sdecl	'.debug_ranges',debug,cluster('mt9v03x_set_exposure_time')
	.sect	'.debug_ranges'
.L63:
	.word	-1,.L46,0,.L64-.L46,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_set_reg')
	.sect	'.debug_info'
.L65:
	.word	435
	.half	3
	.word	.L66
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L68,.L67
	.byte	2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_set_reg',0,1,139,3,7
	.word	.L117
	.byte	1,1,1
	.word	.L48,.L127,.L47
	.byte	4
	.byte	'addr',0,1,139,3,30
	.word	.L117,.L128
	.byte	4
	.byte	'data',0,1,139,3,43
	.word	.L108,.L129
	.byte	5
	.word	.L48,.L127
	.byte	6
	.byte	'return_state',0,1,141,3,11
	.word	.L117,.L130
	.byte	5
	.word	.L131,.L132
	.byte	6
	.byte	'uart_buffer',0,1,145,3,16
	.word	.L111,.L133
	.byte	6
	.byte	'temp',0,1,146,3,16
	.word	.L108,.L134
	.byte	6
	.byte	'timeout_count',0,1,147,3,16
	.word	.L108,.L135
	.byte	6
	.byte	'uart_buffer_index',0,1,148,3,16
	.word	.L115,.L136
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_set_reg')
	.sect	'.debug_abbrev'
.L66:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mt9v03x_set_reg')
	.sect	'.debug_line'
.L67:
	.word	.L310-.L309
.L309:
	.half	3
	.word	.L312-.L311
.L311:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0,0
.L312:
	.byte	5,7,7,0,5,2
	.word	.L48
	.byte	3,138,3,1,5,24,9
	.half	.L313-.L48
	.byte	3,2,1,9
	.half	.L221-.L313
	.byte	3,1,1,5,5,9
	.half	.L314-.L221
	.byte	1,5,25,7,9
	.half	.L315-.L314
	.byte	3,2,1,5,43,9
	.half	.L220-.L315
	.byte	1,5,66,9
	.half	.L316-.L220
	.byte	1,5,87,9
	.half	.L317-.L316
	.byte	1,5,30,9
	.half	.L131-.L317
	.byte	3,3,1,5,36,9
	.half	.L223-.L131
	.byte	3,1,1,5,34,9
	.half	.L318-.L223
	.byte	1,5,26,9
	.half	.L319-.L318
	.byte	3,2,1,5,24,9
	.half	.L320-.L319
	.byte	1,5,26,9
	.half	.L321-.L320
	.byte	3,1,1,5,24,9
	.half	.L322-.L321
	.byte	1,5,31,9
	.half	.L225-.L322
	.byte	3,2,1,5,24,9
	.half	.L226-.L225
	.byte	1,9
	.half	.L227-.L226
	.byte	3,1,1,5,27,9
	.half	.L228-.L227
	.byte	3,1,1,5,45,9
	.half	.L323-.L228
	.byte	1,5,58,9
	.half	.L324-.L323
	.byte	1,5,25,9
	.half	.L325-.L324
	.byte	3,2,1,5,26,9
	.half	.L326-.L325
	.byte	3,1,1,5,24,9
	.half	.L327-.L326
	.byte	1,5,26,9
	.half	.L328-.L327
	.byte	3,1,1,5,24,9
	.half	.L329-.L328
	.byte	1,5,14,9
	.half	.L229-.L329
	.byte	3,1,1,5,31,9
	.half	.L231-.L229
	.byte	3,1,1,5,24,9
	.half	.L230-.L231
	.byte	1,9
	.half	.L233-.L230
	.byte	3,1,1,5,27,9
	.half	.L234-.L233
	.byte	3,1,1,5,45,9
	.half	.L330-.L234
	.byte	1,5,58,9
	.half	.L331-.L330
	.byte	1,5,32,9
	.half	.L26-.L331
	.byte	3,4,1,5,13,9
	.half	.L332-.L26
	.byte	1,5,37,7,9
	.half	.L333-.L332
	.byte	3,2,1,5,35,9
	.half	.L334-.L333
	.byte	1,9
	.half	.L335-.L334
	.byte	3,1,1,5,57,9
	.half	.L336-.L335
	.byte	1,5,71,9
	.half	.L337-.L336
	.byte	1,5,90,9
	.half	.L338-.L337
	.byte	1,5,35,9
	.half	.L339-.L338
	.byte	3,1,1,5,39,9
	.half	.L340-.L339
	.byte	1,5,57,9
	.half	.L232-.L340
	.byte	1,5,44,9
	.half	.L235-.L232
	.byte	1,5,17,9
	.half	.L341-.L235
	.byte	3,1,1,5,29,9
	.half	.L27-.L341
	.byte	3,2,1,5,53,9
	.half	.L342-.L27
	.byte	3,1,1,5,16,9
	.half	.L236-.L342
	.byte	1,5,56,9
	.half	.L343-.L236
	.byte	1,5,12,7,9
	.half	.L28-.L343
	.byte	3,1,1,5,31,7,9
	.half	.L237-.L28
	.byte	1,5,52,9
	.half	.L344-.L237
	.byte	1,5,26,7,9
	.half	.L29-.L344
	.byte	3,2,1,5,25,9
	.half	.L30-.L29
	.byte	3,2,1,5,43,9
	.half	.L345-.L30
	.byte	1,5,66,9
	.half	.L346-.L345
	.byte	1,5,87,9
	.half	.L347-.L346
	.byte	1,5,92,9
	.half	.L132-.L347
	.byte	1,5,51,9
	.half	.L25-.L132
	.byte	3,4,1,5,22,9
	.half	.L222-.L25
	.byte	1,5,5,9
	.half	.L31-.L222
	.byte	3,2,1,5,1,9
	.half	.L32-.L31
	.byte	3,1,1,7,9
	.half	.L69-.L32
	.byte	0,1,1
.L310:
	.sdecl	'.debug_ranges',debug,cluster('mt9v03x_set_reg')
	.sect	'.debug_ranges'
.L68:
	.word	-1,.L48,0,.L69-.L48,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_init')
	.sect	'.debug_info'
.L70:
	.word	317
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L73,.L72
	.byte	2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_init',0,1,196,3,7
	.word	.L117
	.byte	1,1,1
	.word	.L50,.L137,.L49
	.byte	4
	.word	.L50,.L137
	.byte	5
	.byte	'return_state',0,1,198,3,11
	.word	.L117,.L138
	.byte	5
	.byte	'mt9v03x_iic_struct',0,1,199,3,26
	.word	.L139,.L140
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_init')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mt9v03x_init')
	.sect	'.debug_line'
.L72:
	.word	.L349-.L348
.L348:
	.half	3
	.word	.L351-.L350
.L350:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0,0
.L351:
	.byte	5,7,7,0,5,2
	.word	.L50
	.byte	3,195,3,1,5,24,9
	.half	.L240-.L50
	.byte	3,2,1,5,25,9
	.half	.L33-.L240
	.byte	3,4,1,5,9,9
	.half	.L352-.L33
	.byte	3,2,1,5,24,9
	.half	.L353-.L352
	.byte	1,5,22,9
	.half	.L354-.L353
	.byte	1,5,25,9
	.half	.L355-.L354
	.byte	3,1,1,5,43,9
	.half	.L356-.L355
	.byte	1,5,66,9
	.half	.L357-.L356
	.byte	1,5,87,9
	.half	.L358-.L357
	.byte	1,5,24,9
	.half	.L359-.L358
	.byte	3,1,1,5,44,9
	.half	.L360-.L359
	.byte	1,5,47,9
	.half	.L361-.L360
	.byte	1,5,70,9
	.half	.L362-.L361
	.byte	1,5,91,9
	.half	.L363-.L362
	.byte	1,5,37,9
	.half	.L364-.L363
	.byte	3,1,1,5,57,9
	.half	.L365-.L364
	.byte	1,5,9,9
	.half	.L366-.L365
	.byte	1,5,13,7,9
	.half	.L367-.L366
	.byte	3,2,1,5,26,9
	.half	.L368-.L367
	.byte	3,1,1,5,13,9
	.half	.L369-.L368
	.byte	3,1,1,5,45,9
	.half	.L34-.L369
	.byte	3,50,1,5,9,9
	.half	.L370-.L34
	.byte	1,5,63,9
	.half	.L371-.L370
	.byte	1,5,81,9
	.half	.L372-.L371
	.byte	1,5,31,9
	.half	.L373-.L372
	.byte	1,5,5,9
	.half	.L35-.L373
	.byte	3,2,1,5,1,9
	.half	.L36-.L35
	.byte	3,1,1,7,9
	.half	.L74-.L36
	.byte	0,1,1
.L349:
	.sdecl	'.debug_ranges',debug,cluster('mt9v03x_init')
	.sect	'.debug_ranges'
.L73:
	.word	-1,.L50,0,.L74-.L50,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_uart_handler')
	.sect	'.debug_info'
.L75:
	.word	280
	.half	3
	.word	.L76
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L78,.L77
	.byte	2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_uart_handler',0,1,224,1,13,1,1
	.word	.L38,.L141,.L37
	.byte	4
	.word	.L38,.L141
	.byte	5
	.byte	'data',0,1,226,1,11
	.word	.L117,.L142
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_uart_handler')
	.sect	'.debug_abbrev'
.L76:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mt9v03x_uart_handler')
	.sect	'.debug_line'
.L77:
	.word	.L375-.L374
.L374:
	.half	3
	.word	.L377-.L376
.L376:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0,0
.L377:
	.byte	5,13,7,0,5,2
	.word	.L38
	.byte	3,223,1,1,5,18,9
	.half	.L195-.L38
	.byte	3,2,1,5,16,9
	.half	.L378-.L195
	.byte	1,5,21,9
	.half	.L379-.L378
	.byte	3,1,1,5,40,9
	.half	.L380-.L379
	.byte	1,5,16,9
	.half	.L381-.L380
	.byte	3,1,1,5,8,9
	.half	.L382-.L381
	.byte	1,5,5,9
	.half	.L383-.L382
	.byte	1,5,21,7,9
	.half	.L384-.L383
	.byte	3,2,1,5,25,9
	.half	.L2-.L384
	.byte	3,2,1,5,47,9
	.half	.L385-.L2
	.byte	1,5,1,9
	.half	.L386-.L385
	.byte	3,1,1,7,9
	.half	.L79-.L386
	.byte	0,1,1
.L375:
	.sdecl	'.debug_ranges',debug,cluster('mt9v03x_uart_handler')
	.sect	'.debug_ranges'
.L78:
	.word	-1,.L38,0,.L79-.L38,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_vsync_handler')
	.sect	'.debug_info'
.L80:
	.word	407
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L83,.L82
	.byte	2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_vsync_handler',0,1,241,1,13,1,1
	.word	.L40,.L143,.L39
	.byte	4
	.word	.L40,.L143
	.byte	5
	.word	.L144,.L145,.L146
	.byte	6
	.word	.L147,.L148
	.byte	6
	.word	.L149,.L150
	.byte	7
	.word	.L151,.L145,.L146
	.byte	0,5
	.word	.L152,.L153,.L7
	.byte	8
	.word	.L154,.L153,.L7
	.byte	9
	.byte	'reg',0,2,135,6,21
	.word	.L155,.L156
	.byte	0,0,5
	.word	.L157,.L158,.L5
	.byte	6
	.word	.L159,.L160
	.byte	6
	.word	.L161,.L162
	.byte	6
	.word	.L163,.L164
	.byte	7
	.word	.L165,.L158,.L5
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_vsync_handler')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,29,1,49,16,17,1,18,1,0,0,6,5,0,49,16,2,6,0,0,7,11,0,49
	.byte	16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mt9v03x_vsync_handler')
	.sect	'.debug_line'
.L82:
	.word	.L388-.L387
.L387:
	.half	3
	.word	.L390-.L389
.L389:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'IfxDma.h',0,1,0,0,0
.L390:
	.byte	5,5,7,0,5,2
	.word	.L40
	.byte	3,242,1,1,9
	.half	.L391-.L40
	.byte	3,1,1,5,27,9
	.half	.L392-.L391
	.byte	1,5,25,9
	.half	.L393-.L392
	.byte	1,5,8,9
	.half	.L394-.L393
	.byte	3,1,1,5,5,9
	.half	.L395-.L394
	.byte	1,5,9,7,9
	.half	.L396-.L395
	.byte	3,2,1,5,33,9
	.half	.L397-.L396
	.byte	1,5,31,9
	.half	.L398-.L397
	.byte	1,5,30,9
	.half	.L399-.L398
	.byte	3,1,1,5,42,9
	.half	.L400-.L399
	.byte	1,4,3,5,13,9
	.half	.L145-.L400
	.byte	3,170,10,1,5,26,9
	.half	.L401-.L145
	.byte	1,5,31,9
	.half	.L402-.L401
	.byte	1,4,1,5,42,9
	.half	.L146-.L402
	.byte	3,216,117,1,5,9,9
	.half	.L403-.L146
	.byte	3,127,1,5,42,9
	.half	.L404-.L403
	.byte	1,9
	.half	.L405-.L404
	.byte	3,2,1,9
	.half	.L406-.L405
	.byte	3,1,1,9
	.half	.L407-.L406
	.byte	3,1,1,9
	.half	.L408-.L407
	.byte	3,1,1,5,31,9
	.half	.L409-.L408
	.byte	3,123,1,5,20,9
	.half	.L410-.L409
	.byte	3,6,1,5,34,9
	.half	.L411-.L410
	.byte	3,120,1,5,17,9
	.half	.L3-.L411
	.byte	3,12,1,5,9,9
	.half	.L412-.L3
	.byte	1,5,13,7,9
	.half	.L413-.L412
	.byte	3,2,1,4,2,5,19,9
	.half	.L153-.L413
	.byte	3,131,4,1,5,28,9
	.half	.L196-.L153
	.byte	3,1,1,5,5,9
	.half	.L197-.L196
	.byte	1,4,1,5,13,9
	.half	.L7-.L197
	.byte	3,252,123,1,4,3,5,12,9
	.half	.L158-.L7
	.byte	3,175,10,1,5,31,9
	.half	.L414-.L158
	.byte	1,4,1,5,20,9
	.half	.L5-.L414
	.byte	3,211,117,1,5,5,9
	.half	.L4-.L5
	.byte	3,2,1,5,25,9
	.half	.L415-.L4
	.byte	1,5,23,9
	.half	.L416-.L415
	.byte	1,5,1,9
	.half	.L417-.L416
	.byte	3,1,1,7,9
	.half	.L84-.L417
	.byte	0,1,1
.L388:
	.sdecl	'.debug_ranges',debug,cluster('mt9v03x_vsync_handler')
	.sect	'.debug_ranges'
.L83:
	.word	-1,.L40,0,.L84-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_dma_handler')
	.sect	'.debug_info'
.L85:
	.word	396
	.half	3
	.word	.L86
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L88,.L87
	.byte	2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_dma_handler',0,1,146,2,13,1,1
	.word	.L42,.L166,.L41
	.byte	4
	.word	.L42,.L166
	.byte	5
	.word	.L167,.L168,.L169
	.byte	6
	.word	.L170,.L171
	.byte	6
	.word	.L172,.L173
	.byte	7
	.word	.L174,.L168,.L169
	.byte	0,5
	.word	.L175,.L176,.L9
	.byte	6
	.word	.L177,.L178
	.byte	6
	.word	.L179,.L180
	.byte	7
	.word	.L181,.L176,.L9
	.byte	0,5
	.word	.L182,.L183,.L184
	.byte	6
	.word	.L185,.L186
	.byte	6
	.word	.L187,.L188
	.byte	7
	.word	.L189,.L183,.L184
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_dma_handler')
	.sect	'.debug_abbrev'
.L86:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,29,1,49,16,17,1,18,1,0,0,6,5,0,49,16,2,6,0,0,7,11,0,49
	.byte	16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('mt9v03x_dma_handler')
	.sect	'.debug_line'
.L87:
	.word	.L419-.L418
.L418:
	.half	3
	.word	.L421-.L420
.L420:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0,0,0,0
	.byte	'IfxDma.h',0,1,0,0,0
.L421:
	.byte	5,5,7,0,5,2
	.word	.L42
	.byte	3,147,2,1,4,2,5,12,9
	.half	.L168-.L42
	.byte	3,223,7,1,5,31,9
	.half	.L422-.L168
	.byte	1,5,37,9
	.half	.L423-.L422
	.byte	1,4,1,5,49,9
	.half	.L169-.L423
	.byte	3,163,120,1,5,61,9
	.half	.L424-.L169
	.byte	1,4,2,5,20,9
	.half	.L176-.L424
	.byte	3,178,9,1,5,33,9
	.half	.L425-.L176
	.byte	1,5,38,9
	.half	.L426-.L425
	.byte	1,5,5,9
	.half	.L427-.L426
	.byte	1,4,1,9
	.half	.L9-.L427
	.byte	3,206,118,1,5,9,7,9
	.half	.L428-.L9
	.byte	3,2,1,5,31,9
	.half	.L429-.L428
	.byte	1,5,29,9
	.half	.L430-.L429
	.byte	1,5,21,9
	.half	.L431-.L430
	.byte	3,1,1,5,52,9
	.half	.L432-.L431
	.byte	3,1,1,5,64,9
	.half	.L433-.L432
	.byte	1,4,2,5,13,9
	.half	.L183-.L433
	.byte	3,223,7,1,5,26,9
	.half	.L434-.L183
	.byte	1,5,31,9
	.half	.L435-.L434
	.byte	1,4,1,5,9,9
	.half	.L184-.L435
	.byte	3,162,120,1,5,33,9
	.half	.L436-.L184
	.byte	1,5,31,9
	.half	.L437-.L436
	.byte	1,5,32,9
	.half	.L438-.L437
	.byte	3,125,1,5,9,9
	.half	.L10-.L438
	.byte	3,7,1,5,28,9
	.half	.L439-.L10
	.byte	1,5,12,9
	.half	.L440-.L439
	.byte	3,1,1,5,35,9
	.half	.L441-.L440
	.byte	1,5,9,9
	.half	.L442-.L441
	.byte	1,5,13,7,9
	.half	.L443-.L442
	.byte	3,4,1,5,35,9
	.half	.L444-.L443
	.byte	1,5,33,9
	.half	.L445-.L444
	.byte	1,5,13,9
	.half	.L446-.L445
	.byte	3,1,1,5,35,9
	.half	.L447-.L446
	.byte	1,5,33,9
	.half	.L448-.L447
	.byte	1,5,13,9
	.half	.L449-.L448
	.byte	3,1,1,5,35,9
	.half	.L450-.L449
	.byte	1,5,33,9
	.half	.L451-.L450
	.byte	1,5,25,9
	.half	.L452-.L451
	.byte	3,1,1,5,1,9
	.half	.L11-.L452
	.byte	3,3,1,7,9
	.half	.L89-.L11
	.byte	0,1,1
.L419:
	.sdecl	'.debug_ranges',debug,cluster('mt9v03x_dma_handler')
	.sect	'.debug_ranges'
.L88:
	.word	-1,.L42,0,.L89-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_finish_flag')
	.sect	'.debug_info'
.L90:
	.word	238
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_finish_flag',0,35,64,9
	.word	.L190
	.byte	1,5,3
	.word	mt9v03x_finish_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_finish_flag')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_image')
	.sect	'.debug_info'
.L92:
	.word	232
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_image',0,35,65,21
	.word	.L191
	.byte	1,5,3
	.word	mt9v03x_image
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_image')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_type')
	.sect	'.debug_info'
.L94:
	.word	230
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_type',0,35,67,26
	.word	.L192
	.byte	5,3
	.word	mt9v03x_type
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_type')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('timeout')
	.sect	'.debug_info'
.L96:
	.word	226
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'timeout',0,35,71,9
	.word	.L193
	.byte	1,5,3
	.word	timeout
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('timeout')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_lost_flag')
	.sect	'.debug_info'
.L98:
	.word	236
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_lost_flag',0,35,73,9
	.word	.L117
	.byte	1,5,3
	.word	mt9v03x_lost_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_lost_flag')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_dma_int_num')
	.sect	'.debug_info'
.L100:
	.word	238
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_dma_int_num',0,35,74,9
	.word	.L117
	.byte	1,5,3
	.word	mt9v03x_dma_int_num
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_dma_int_num')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_dma_init_flag')
	.sect	'.debug_info'
.L102:
	.word	240
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_dma_init_flag',0,35,75,9
	.word	.L117
	.byte	1,5,3
	.word	mt9v03x_dma_init_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_dma_init_flag')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_link_list_num')
	.sect	'.debug_info'
.L104:
	.word	240
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_link_list_num',0,35,76,9
	.word	.L117
	.byte	1,5,3
	.word	mt9v03x_link_list_num
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_link_list_num')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mt9v03x_set_confing_buffer')
	.sect	'.debug_info'
.L106:
	.word	244
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_mt9v03x.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L51
	.byte	3
	.byte	'mt9v03x_set_confing_buffer',0,35,79,14
	.word	.L194
	.byte	5,3
	.word	mt9v03x_set_confing_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mt9v03x_set_confing_buffer')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('mt9v03x_dma_handler')
	.sect	'.debug_loc'
.L173:
	.word	0,0
.L188:
	.word	0,0
.L180:
	.word	0,0
.L171:
	.word	0,0
.L186:
	.word	0,0
.L178:
	.word	0,0
.L41:
	.word	-1,.L42,0,.L166-.L42
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mt9v03x_get_version')
	.sect	'.debug_loc'
.L43:
	.word	-1,.L44,0,.L198-.L44
	.half	2
	.byte	138,0
	.word	.L198-.L44,.L109-.L44
	.half	2
	.byte	138,8
	.word	.L109-.L44,.L109-.L44
	.half	2
	.byte	138,0
	.word	0,0
.L114:
	.word	-1,.L44,.L201-.L44,.L202-.L44
	.half	1
	.byte	88
	.word	.L204-.L44,.L109-.L44
	.half	1
	.byte	88
	.word	.L206-.L44,.L109-.L44
	.half	1
	.byte	82
	.word	0,0
.L110:
	.word	-1,.L44,.L203-.L44,.L13-.L44
	.half	1
	.byte	95
	.word	0,0
.L113:
	.word	-1,.L44,.L199-.L44,.L200-.L44
	.half	1
	.byte	89
	.word	.L205-.L44,.L109-.L44
	.half	1
	.byte	89
	.word	0,0
.L112:
	.word	-1,.L44,0,.L109-.L44
	.half	2
	.byte	145,120
	.word	0,0
.L116:
	.word	-1,.L44,0,.L109-.L44
	.half	2
	.byte	145,124
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mt9v03x_init')
	.sect	'.debug_loc'
.L140:
	.word	-1,.L50,0,.L137-.L50
	.half	2
	.byte	145,104
	.word	0,0
.L49:
	.word	-1,.L50,0,.L240-.L50
	.half	2
	.byte	138,0
	.word	.L240-.L50,.L137-.L50
	.half	2
	.byte	138,24
	.word	.L137-.L50,.L137-.L50
	.half	2
	.byte	138,0
	.word	0,0
.L138:
	.word	-1,.L50,.L33-.L50,.L137-.L50
	.half	1
	.byte	95
	.word	.L241-.L50,.L137-.L50
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mt9v03x_set_exposure_time')
	.sect	'.debug_loc'
.L119:
	.word	-1,.L46,0,.L208-.L46
	.half	1
	.byte	84
	.word	.L209-.L46,.L118-.L46
	.half	1
	.byte	89
	.word	.L17-.L46,.L211-.L46
	.half	1
	.byte	84
	.word	0,0
.L45:
	.word	-1,.L46,0,.L207-.L46
	.half	2
	.byte	138,0
	.word	.L207-.L46,.L118-.L46
	.half	2
	.byte	138,8
	.word	.L118-.L46,.L118-.L46
	.half	2
	.byte	138,0
	.word	0,0
.L120:
	.word	-1,.L46,.L210-.L46,.L211-.L46
	.half	1
	.byte	91
	.word	.L211-.L46,.L23-.L46
	.half	1
	.byte	82
	.word	.L23-.L46,.L118-.L46
	.half	1
	.byte	91
	.word	.L218-.L46,.L118-.L46
	.half	1
	.byte	82
	.word	0,0
.L124:
	.word	-1,.L46,.L214-.L46,.L215-.L46
	.half	1
	.byte	88
	.word	.L216-.L46,.L17-.L46
	.half	1
	.byte	88
	.word	0,0
.L125:
	.word	-1,.L46,.L212-.L46,.L213-.L46
	.half	1
	.byte	90
	.word	.L217-.L46,.L17-.L46
	.half	1
	.byte	90
	.word	0,0
.L123:
	.word	-1,.L46,0,.L118-.L46
	.half	2
	.byte	145,120
	.word	0,0
.L126:
	.word	-1,.L46,0,.L118-.L46
	.half	2
	.byte	145,124
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mt9v03x_set_reg')
	.sect	'.debug_loc'
.L128:
	.word	-1,.L48,0,.L220-.L48
	.half	1
	.byte	84
	.word	.L225-.L48,.L226-.L48
	.half	1
	.byte	88
	.word	.L227-.L48,.L228-.L48
	.half	1
	.byte	88
	.word	.L25-.L48,.L238-.L48
	.half	1
	.byte	88
	.word	.L25-.L48,.L238-.L48
	.half	1
	.byte	84
	.word	0,0
.L129:
	.word	-1,.L48,0,.L131-.L48
	.half	1
	.byte	85
	.word	.L229-.L48,.L230-.L48
	.half	1
	.byte	89
	.word	.L233-.L48,.L234-.L48
	.half	1
	.byte	89
	.word	.L28-.L48,.L237-.L48
	.half	1
	.byte	89
	.word	.L25-.L48,.L238-.L48
	.half	1
	.byte	89
	.word	.L25-.L48,.L238-.L48
	.half	1
	.byte	85
	.word	0,0
.L47:
	.word	-1,.L48,0,.L219-.L48
	.half	2
	.byte	138,0
	.word	.L219-.L48,.L127-.L48
	.half	2
	.byte	138,8
	.word	.L127-.L48,.L127-.L48
	.half	2
	.byte	138,0
	.word	0,0
.L130:
	.word	-1,.L48,.L221-.L48,.L222-.L48
	.half	1
	.byte	91
	.word	.L222-.L48,.L31-.L48
	.half	1
	.byte	82
	.word	.L31-.L48,.L127-.L48
	.half	1
	.byte	91
	.word	.L239-.L48,.L127-.L48
	.half	1
	.byte	82
	.word	0,0
.L134:
	.word	-1,.L48,.L231-.L48,.L232-.L48
	.half	1
	.byte	88
	.word	.L235-.L48,.L25-.L48
	.half	1
	.byte	88
	.word	0,0
.L135:
	.word	-1,.L48,.L223-.L48,.L224-.L48
	.half	1
	.byte	90
	.word	.L236-.L48,.L25-.L48
	.half	1
	.byte	90
	.word	0,0
.L133:
	.word	-1,.L48,0,.L127-.L48
	.half	2
	.byte	145,120
	.word	0,0
.L136:
	.word	-1,.L48,0,.L127-.L48
	.half	2
	.byte	145,124
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mt9v03x_uart_handler')
	.sect	'.debug_loc'
.L142:
	.word	-1,.L38,0,.L141-.L38
	.half	2
	.byte	145,120
	.word	0,0
.L37:
	.word	-1,.L38,0,.L195-.L38
	.half	2
	.byte	138,0
	.word	.L195-.L38,.L141-.L38
	.half	2
	.byte	138,8
	.word	.L141-.L38,.L141-.L38
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mt9v03x_vsync_handler')
	.sect	'.debug_loc'
.L164:
	.word	0,0
.L150:
	.word	0,0
.L162:
	.word	0,0
.L148:
	.word	0,0
.L160:
	.word	0,0
.L39:
	.word	-1,.L40,0,.L143-.L40
	.half	2
	.byte	138,0
	.word	0,0
.L156:
	.word	-1,.L40,.L196-.L40,.L197-.L40
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L453:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('mt9v03x_uart_handler')
	.sect	'.debug_frame'
	.word	36
	.word	.L453,.L38,.L141-.L38
	.byte	4
	.word	(.L195-.L38)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L141-.L195)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('mt9v03x_vsync_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L453,.L40,.L143-.L40
	.sdecl	'.debug_frame',debug,cluster('mt9v03x_dma_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L453,.L42,.L166-.L42
	.sdecl	'.debug_frame',debug,cluster('mt9v03x_get_version')
	.sect	'.debug_frame'
	.word	36
	.word	.L453,.L44,.L109-.L44
	.byte	4
	.word	(.L198-.L44)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L109-.L198)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('mt9v03x_set_exposure_time')
	.sect	'.debug_frame'
	.word	36
	.word	.L453,.L46,.L118-.L46
	.byte	4
	.word	(.L207-.L46)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L118-.L207)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('mt9v03x_set_reg')
	.sect	'.debug_frame'
	.word	36
	.word	.L453,.L48,.L127-.L48
	.byte	4
	.word	(.L219-.L48)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L127-.L219)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('mt9v03x_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L453,.L50,.L137-.L50
	.byte	4
	.word	(.L240-.L50)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L137-.L240)/2
	.byte	19,0,8,26,0,0
	; Module end
