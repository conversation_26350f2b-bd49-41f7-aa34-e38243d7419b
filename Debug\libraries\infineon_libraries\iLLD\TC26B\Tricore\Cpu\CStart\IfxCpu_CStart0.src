	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc35268a --dep-file=IfxCpu_CStart0.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c'

	
$TC16X
	
		 .extern _SMALL_DATA_, _LITERAL_DATA_, _A8_DATA_, _A9_DATA_
		 .extern __USTACK0
		 .extern core0_main
	.sdecl	'.text.IfxCpu_CStart0._Core0_start',code,cluster('_Core0_start')
	.sect	'.text.IfxCpu_CStart0._Core0_start'
	.align	2
	
	.global	_Core0_start
; Function _Core0_start
.L39:
_Core0_start:	.type	func
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L66:
	ld.w	d15,[a15]
	extr.u	d15,d15,#2,#14
.L157:
	xor	d8,d15,#63
.L158:
	j	.L2
.L2:
	 movh.a	 sp,#@his(__USTACK0)
 lea	 sp,[sp]@los(__USTACK0)
.L197:
	dsync
.L198:
	mov	d15,#2432
.L199:
	mtcr	#65028,d15
	isync
.L200:
	mfcr	d15,#65024
.L159:
	insert	d15,d15,#0,#0,#20
.L201:
	mtcr	#65024,d15
	isync
.L202:
	mov	d1,#1
.L72:
	jeq	d1,#0,.L3
.L78:
	mov	d15,#0
.L160:
	or	d15,#1
.L203:
	mtcr	#37380,d15
	isync
.L3:
	mfcr	d15,#65052
.L161:
	and	d2,d15,#7
.L204:
	j	.L4
.L4:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L205:
	mul	d15,d2,#12
.L162:
	addsc.a	a15,a15,d15,#0
.L88:
	ld.w	d0,[a15]
	extr.u	d0,d0,#2,#14
.L163:
	xor	d3,d0,#63
.L164:
	j	.L5
.L5:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L206:
	mul	d15,d2,#12
	addsc.a	a15,a15,d15,#0
.L91:
	ld.w	d15,[a15]
.L207:
	jz.t	d15:1,.L6
.L208:
	sha	d15,d3,#2
	or	d15,#1
.L209:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L210:
	sha	d0,d0,#16
.L211:
	or	d0,d15
.L212:
	st.w	[a15],d0
.L6:
	sha	d15,d3,#2
	or	d15,#2
.L213:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L214:
	sha	d0,d0,#16
.L215:
	or	d15,d0
.L216:
	st.w	[a15],d15
.L217:
	j	.L7
.L8:
.L7:
	ld.w	d15,[a15]
	and	d15,#1
.L218:
	jeq	d15,#1,.L8
.L92:
	mov	d15,#0
.L166:
	jeq	d1,#0,.L9
.L219:
	mov	d0,#0
.L220:
	j	.L10
.L9:
	mov	d0,#1
.L10:
	insert	d15,d15,d0,#1,#1
.L221:
	mtcr	#37388,d15
	isync
.L222:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L223:
	mul	d15,d2,#12
.L167:
	addsc.a	a15,a15,d15,#0
.L103:
	ld.w	d15,[a15]
.L224:
	jz.t	d15:1,.L11
.L225:
	sha	d15,d3,#2
	or	d15,#1
.L226:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L227:
	sha	d0,d0,#16
.L228:
	or	d15,d0
.L229:
	st.w	[a15],d15
.L11:
	sha	d3,#2
.L165:
	or	d15,d3,#3
.L230:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L231:
	sha	d0,d0,#16
.L232:
	or	d15,d0
.L233:
	st.w	[a15],d15
.L234:
	j	.L12
.L13:
.L12:
	ld.w	d15,[a15]
.L235:
	jz.t	d15:0,.L13
.L99:
	isync
.L73:
	mov	d1,#1
.L110:
	mfcr	d15,#65052
.L168:
	and	d2,d15,#7
.L236:
	j	.L14
.L14:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L237:
	mul	d15,d2,#12
.L169:
	addsc.a	a15,a15,d15,#0
.L118:
	ld.w	d0,[a15]
	extr.u	d0,d0,#2,#14
.L170:
	xor	d3,d0,#63
.L171:
	j	.L15
.L15:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L238:
	mul	d15,d2,#12
	addsc.a	a15,a15,d15,#0
.L120:
	ld.w	d15,[a15]
.L239:
	jz.t	d15:1,.L16
.L240:
	sha	d15,d3,#2
	or	d15,#1
.L241:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L242:
	sha	d0,d0,#16
.L243:
	or	d0,d15
.L244:
	st.w	[a15],d0
.L16:
	sha	d15,d3,#2
	or	d15,#2
.L245:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L246:
	sha	d0,d0,#16
.L247:
	or	d15,d0
.L248:
	st.w	[a15],d15
.L249:
	j	.L17
.L18:
.L17:
	ld.w	d15,[a15]
	and	d15,#1
.L250:
	jeq	d15,#1,.L18
.L121:
	mov	d15,#0
.L173:
	jeq	d1,#0,.L19
.L251:
	mov	d0,#0
.L252:
	j	.L20
.L19:
	mov	d0,#1
.L20:
	insert	d15,d15,d0,#1,#1
.L253:
	mtcr	#36928,d15
	isync
.L254:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L255:
	mul	d15,d2,#12
.L174:
	addsc.a	a15,a15,d15,#0
.L126:
	ld.w	d15,[a15]
.L256:
	jz.t	d15:1,.L21
.L257:
	sha	d15,d3,#2
	or	d15,#1
.L258:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L259:
	sha	d0,d0,#16
.L260:
	or	d15,d0
.L261:
	st.w	[a15],d15
.L21:
	sha	d3,#2
.L172:
	or	d15,d3,#3
.L262:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L263:
	sha	d0,d0,#16
.L264:
	or	d15,d0
.L265:
	st.w	[a15],d15
.L266:
	j	.L22
.L23:
.L22:
	ld.w	d15,[a15]
.L267:
	jz.t	d15:0,.L23
.L123:
	isync
.L111:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L127:
	ld.w	d15,[a15]
.L268:
	jz.t	d15:1,.L24
.L269:
	sha	d15,d8,#2
	or	d15,#1
.L270:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L271:
	sha	d0,d0,#16
.L272:
	or	d15,d0
.L273:
	st.w	[a15],d15
.L24:
	sha	d15,d8,#2
	or	d15,#2
.L274:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L275:
	sha	d0,d0,#16
.L276:
	or	d15,d0
.L277:
	st.w	[a15],d15
.L278:
	j	.L25
.L26:
.L25:
	ld.w	d15,[a15]
	and	d15,#1
.L279:
	jeq	d15,#1,.L26
.L128:
	movh.a	a15,#@his(__TRAPTAB_CPU0)
	lea	a15,[a15]@los(__TRAPTAB_CPU0)
	mov.d	d15,a15
.L280:
	mtcr	#65060,d15
	isync
.L281:
	movh.a	a15,#@his(__INTTAB_CPU0)
	lea	a15,[a15]@los(__INTTAB_CPU0)
	mov.d	d15,a15
.L282:
	mtcr	#65056,d15
	isync
.L283:
	movh.a	a15,#@his(__ISTACK0)
	lea	a15,[a15]@los(__ISTACK0)
	mov.d	d15,a15
.L284:
	mtcr	#65064,d15
	isync
.L285:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L129:
	ld.w	d15,[a15]
.L286:
	jz.t	d15:1,.L27
.L287:
	sha	d15,d8,#2
	or	d15,#1
.L288:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L289:
	sha	d0,d0,#16
.L290:
	or	d15,d0
.L291:
	st.w	[a15],d15
.L27:
	sha	d15,d8,#2
	or	d15,#3
.L292:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L293:
	sha	d0,d0,#16
.L294:
	or	d15,d0
.L295:
	st.w	[a15],d15
.L296:
	j	.L28
.L29:
.L28:
	ld.w	d15,[a15]
.L297:
	jz.t	d15:0,.L29
.L130:
	 movh.a	 a0,#@his(_SMALL_DATA_)
 lea	 a0,[a0]@los(_SMALL_DATA_)
.L298:
	 movh.a	 a1,#@his(_LITERAL_DATA_)
 lea	 a1,[a1]@los(_LITERAL_DATA_)
.L299:
	 movh.a	 a8,#@his(_A8_DATA_)
 lea	 a8,[a8]@los(_A8_DATA_)
.L300:
	 movh.a	 a9,#@his(_A9_DATA_)
 lea	 a9,[a9]@los(_A9_DATA_)
.L301:
	movh.a	a15,#@his(__CSA0)
.L175:
	lea	a15,[a15]@los(__CSA0)
.L302:
	movh.a	a2,#@his(__CSA0_END)
	lea	a2,[a2]@los(__CSA0_END)
.L132:
	mov.a	a4,#0
.L177:
	mov.d	d0,a2
.L303:
	mov.d	d15,a15
.L304:
	sub	d0,d15
.L305:
	mov	d15,#64
.L306:
	div.u	e4,d0,d15
.L179:
	mov	d0,#0
.L180:
	j	.L30
.L31:
	mov.d	d15,a15
.L307:
	insert	d15,d15,#0,#0,#28
.L308:
	sh	d1,d15,#-12
.L309:
	mov.d	d15,a15
.L310:
	mov.u	d2,#65535
	sh	d2,#6
.L311:
	and	d15,d2
.L312:
	sh	d15,#-6
.L181:
	or	d1,d15
.L313:
	jne	d0,#0,.L32
.L314:
	mtcr	#65080,d1
	isync
.L315:
	j	.L33
.L32:
	st.w	[a4],d1
.L33:
	add	d15,d4,#-3
.L316:
	jne	d15,d0,.L34
.L317:
	mtcr	#65084,d1
	isync
.L34:
	mov.aa	a4,a15
.L318:
	lea	a15,[a15]64
.L319:
	add	d0,#1
.L30:
	jlt.u	d0,d4,.L31
.L320:
	mov	d15,#0
.L321:
	st.w	[a4],d15
.L133:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L178:
	mov	d15,d2
.L183:
	mov	d4,d8
.L185:
	call	IfxScuWdt_disableCpuWatchdog
.L182:
	mov	d4,d15
.L186:
	call	IfxScuWdt_disableSafetyWatchdog
.L187:
	call	Ifx_C_Init
.L145:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L149:
	movh.a	a15,#61443
.L176:
	ld.w	d15,[a15]@los(0xf00360f0)
.L184:
	jz.t	d15:1,.L35
.L322:
	sha	d15,d2,#2
	or	d15,#1
.L323:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L324:
	sha	d0,d0,#16
.L325:
	or	d15,d0
.L326:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L35:
	sha	d2,#2
.L188:
	or	d15,d2,#2
.L327:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L328:
	sha	d0,d0,#16
.L329:
	or	d15,d0
.L330:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L331:
	j	.L36
.L37:
.L36:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
	and	d15,#1
.L332:
	jeq	d15,#1,.L37
.L150:
	mov	d15,#0
.L333:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf0036058),d15
.L334:
	movh.a	a4,#@his(IfxScuCcu_defaultClockConfig)
	lea	a4,[a4]@los(IfxScuCcu_defaultClockConfig)
	call	IfxScuCcu_init
.L335:
	movh.a	a4,#63619
.L336:
	movh.a	a15,#@his(_Core1_start)
	lea	a15,[a15]@los(_Core1_start)
	mov.d	d4,a15
.L337:
	call	IfxCpu_startCore
.L338:
	movh.a	a15,#@his(core0_main)
	lea	a15,[a15]@los(core0_main)
		ji a15
.L339:
	ret
.L60:
	
___Core0_start_function_end:
	.size	_Core0_start,___Core0_start_function_end-_Core0_start
.L50:
	; End of function
	
	.sdecl	'.text.start',code,cluster('_START'),protect
	.sect	'.text.start'
	.align	2
	
	.global	_START
; Function _START
.L41:
_START:	.type	func
	movh.a	a15,#@his(_Core0_start)
	lea	a15,[a15]@los(_Core0_start)
		ji a15
.L344:
	ret
.L154:
	
___START_function_end:
	.size	_START,___START_function_end-_START
.L55:
	; End of function
	
	.sdecl	'.rodata.bmhd_0',data,rom,cluster('BootModeHeader_0'),protect
	.sect	'.rodata.bmhd_0'
	.global	BootModeHeader_0
	.align	2
BootModeHeader_0:	.type	object
	.size	BootModeHeader_0,32
	.space	4
	.word	-1286012816
	.space	16
	.word	2032056420,-2032056421
	.sdecl	'.rodata.bmhd_1',data,rom,cluster('BootModeHeader_1'),protect
	.sect	'.rodata.bmhd_1'
	.global	BootModeHeader_1
	.align	2
BootModeHeader_1:	.type	object
	.size	BootModeHeader_1,32
	.space	4
	.word	-1286012816
	.space	16
	.word	2032056420,-2032056421
	.calls	'__INDIRECT__','_Core1_start'
	.calls	'__INDIRECT__','core0_main'
	.calls	'__INDIRECT__','_Core0_start'
	.calls	'_Core0_start','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'_Core0_start','IfxScuWdt_disableCpuWatchdog'
	.calls	'_Core0_start','IfxScuWdt_disableSafetyWatchdog'
	.calls	'_Core0_start','Ifx_C_Init'
	.calls	'_Core0_start','IfxScuCcu_init'
	.calls	'_Core0_start','IfxCpu_startCore'
	.calls	'_Core0_start','',0
	.extern	Ifx_C_Init
	.extern	IfxScuWdt_disableCpuWatchdog
	.extern	IfxScuWdt_disableSafetyWatchdog
	.extern	IfxScuWdt_getSafetyWatchdogPassword
	.extern	IfxCpu_startCore
	.extern	IfxScuCcu_init
	.extern	IfxScuCcu_defaultClockConfig
	.extern	_Core1_start
	.extern	__ISTACK0
	.extern	__INTTAB_CPU0
	.extern	__TRAPTAB_CPU0
	.extern	__CSA0
	.extern	__CSA0_END
	.extern	core0_main
	.extern	__INDIRECT__
	.calls	'_START','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L43:
	.word	78571
	.half	3
	.word	.L44
	.byte	4
.L42:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L45
	.byte	2,1,1,3
	.word	241
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	244
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	289
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	301
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	413
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	387
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	419
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	419
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	387
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	528
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	544
	.byte	4,2,35,0,0,7
	.byte	'unsigned char',0,1,8
.L63:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	719
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	963
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	640
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	923
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1154
	.byte	4,2,35,8,0,14
	.word	1194
	.byte	3
	.word	1257
.L90:
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1
.L93:
	.byte	5
	.byte	'watchdog',0,3,181,3,65
	.word	1262
.L95:
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	697
.L97:
	.byte	6,0
.L148:
	.byte	4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,3,204,3,17,1,1
.L151:
	.byte	5
	.byte	'password',0,3,204,3,59
	.word	697
.L153:
	.byte	6,0
.L102:
	.byte	4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1
.L104:
	.byte	5
	.byte	'watchdog',0,3,140,4,63
	.word	1262
.L106:
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	697
.L108:
	.byte	6,0
.L65:
	.byte	8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	697
	.byte	1,1
.L67:
	.byte	5
	.byte	'watchdog',0,3,227,3,74
	.word	1262
.L69:
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0
.L84:
	.byte	8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1555
	.byte	1,1
.L85:
	.byte	6,0
.L61:
	.byte	7
	.byte	'unsigned long int',0,4,7
.L141:
	.byte	3
	.word	1668
.L131:
	.byte	4
	.byte	'IfxCpu_initCSA',0,3,5,219,6,17,1,1
.L134:
	.byte	5
	.byte	'csaBegin',0,5,219,6,40
	.word	1689
.L136:
	.byte	5
	.byte	'csaEnd',0,5,219,6,58
	.word	1689
.L138:
	.byte	6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	680
	.byte	1,1,6,0
.L109:
	.byte	4
	.byte	'IfxCpu_setDataCache',0,3,5,177,7,17,1,1
.L112:
	.byte	5
	.byte	'enable',0,5,177,7,45
	.word	680
.L114:
	.byte	17
.L122:
	.byte	6,6,6,6,6,0,0
.L71:
	.byte	4
	.byte	'IfxCpu_setProgramCache',0,3,5,204,7,17,1,1
.L74:
	.byte	5
	.byte	'enable',0,5,204,7,48
	.word	680
.L76:
	.byte	17
.L77:
	.byte	6
.L81:
	.byte	17
.L98:
	.byte	6,0,6,6,6,6,0,0,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1668
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	697
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	680
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	697
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1668
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1668
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2431
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3002
	.byte	4,2,35,0,0,18,4
	.word	680
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3130
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3345
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3560
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3777
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3997
	.byte	4,2,35,0,0,18,24
	.word	680
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	680
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4320
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	680
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4624
	.byte	4,2,35,0,0,18,8
	.word	680
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4949
	.byte	4,2,35,0,0,18,12
	.word	680
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5289
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	505
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5655
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5941
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6088
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	505
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6257
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6429
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	697
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6604
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6778
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6952
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7128
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7284
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7617
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7965
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8089
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8173
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8353
	.byte	4,2,35,0,0,18,76
	.word	680
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8606
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8693
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2391
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2962
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3081
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3121
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3305
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3520
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3737
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3957
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3121
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4271
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4311
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4584
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4900
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4940
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5240
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5280
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5615
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5901
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4940
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6048
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6217
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6389
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6564
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6738
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6912
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7088
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7244
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7577
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7925
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4940
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8049
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8298
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8557
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8597
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8653
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9220
	.byte	4,3,35,252,1,0,14
	.word	9260
	.byte	3
	.word	9863
	.byte	15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9868
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	680
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	9873
	.byte	6,0,20
	.byte	'__mtcr',0,1,1,1,1,21
	.word	521
	.byte	21
	.word	521
	.byte	0,14
	.word	521
	.byte	22
	.byte	'__mfcr',0
	.word	10077
	.byte	1,1,1,1,21
	.word	521
	.byte	0,23
	.byte	'__isync',0,1,1,1,1,23
	.byte	'__dsync',0,1,1,1,1,24
	.byte	'Ifx_C_Init',0,9,140,1,6,1,1,1,1,25
	.word	249
	.byte	26
	.word	275
	.byte	6,0,25
	.word	310
	.byte	26
	.word	342
	.byte	6,0,25
	.word	355
	.byte	6,0,25
	.word	424
	.byte	26
	.word	443
	.byte	6,0,25
	.word	459
	.byte	26
	.word	474
	.byte	26
	.word	488
	.byte	6,0,25
	.word	1267
	.byte	26
	.word	1307
	.byte	26
	.word	1325
	.byte	6,0,25
	.word	1345
	.byte	26
	.word	1388
	.byte	6,0,25
	.word	1408
	.byte	26
	.word	1446
	.byte	26
	.word	1464
	.byte	6,0,25
	.word	1484
	.byte	26
	.word	1535
	.byte	6,0,27
	.byte	'IfxScuWdt_disableCpuWatchdog',0,3,218,2,17,1,1,1,1,5
	.byte	'password',0,3,218,2,53
	.word	697
	.byte	0,27
	.byte	'IfxScuWdt_disableSafetyWatchdog',0,3,228,2,17,1,1,1,1,5
	.byte	'password',0,3,228,2,56
	.word	697
	.byte	0,28
	.byte	'IfxScuWdt_getSafetyWatchdogPassword',0,3,143,3,19
	.word	697
	.byte	1,1,1,1,25
	.word	1634
	.byte	6,0,25
	.word	1694
	.byte	26
	.word	1717
	.byte	26
	.word	1735
	.byte	6,0,18,176,32
	.word	680
	.byte	19,175,32,0,10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,10,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	528
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	528
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,239,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10469
	.byte	4,2,35,0,0,18,208,223,1
	.word	680
	.byte	19,207,223,1,0,10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,10,234,4,16,4,11
	.byte	'ASI',0,4
	.word	528
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	27,0,2,35,0,0,12,10,191,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10622
	.byte	4,2,35,0,0,18,248,1
	.word	680
	.byte	19,247,1,0,10
	.byte	'_Ifx_CPU_PMA0_Bits',0,10,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	528
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,199,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10740
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PMA1_Bits',0,10,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	528
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,207,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10866
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PMA2_Bits',0,10,240,3,16,4,11
	.byte	'PSI',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,215,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10992
	.byte	4,2,35,0,0,18,244,29
	.word	680
	.byte	19,243,29,0,10
	.byte	'_Ifx_CPU_DCON2_Bits',0,10,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,191,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11107
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_SMACON_Bits',0,10,159,4,16,4,11
	.byte	'PC',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	528
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	528
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	528
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	528
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	528
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	528
	.byte	7,0,2,35,0,0,12,10,247,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11220
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DSTR_Bits',0,10,143,2,16,4,11
	.byte	'SRE',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	528
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	528
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	528
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	528
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	528
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	528
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	528
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	528
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	528
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	528
	.byte	7,0,2,35,0,0,12,10,143,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11472
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DATR_Bits',0,10,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	528
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	528
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	528
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	528
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,159,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11809
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DEADD_Bits',0,10,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,207,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12040
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,10,215,1,16,4,11
	.byte	'TA',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,215,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12132
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DIETR_Bits',0,10,221,1,16,4,11
	.byte	'IED',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	528
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	528
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	528
	.byte	18,0,2,35,0,0,12,10,223,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12213
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DCON0_Bits',0,10,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	30,0,2,35,0,0
.L124:
	.byte	12,10,183,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12454
	.byte	4,2,35,0,0,18,188,3
	.word	680
	.byte	19,187,3,0,10
	.byte	'_Ifx_CPU_PSTR_Bits',0,10,247,3,16,4,11
	.byte	'FRE',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	528
	.byte	17,0,2,35,0,0,12,10,223,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12593
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PCON1_Bits',0,10,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	30,0,2,35,0,0
.L79:
	.byte	12,10,159,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12809
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PCON2_Bits',0,10,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,167,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12932
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PCON0_Bits',0,10,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	30,0,2,35,0,0
.L100:
	.byte	12,10,151,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13045
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,10,203,3,16,4,11
	.byte	'TA',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,183,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13173
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PIETR_Bits',0,10,209,3,16,4,11
	.byte	'IED',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	528
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	528
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	528
	.byte	18,0,2,35,0,0,12,10,191,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13254
	.byte	4,2,35,0,0,18,232,3
	.word	680
	.byte	19,231,3,0,10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,10,83,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	27,0,2,35,0,0,12,10,215,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13506
	.byte	4,2,35,0,0,18,252,23
	.word	680
	.byte	19,251,23,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,10,183,2,16,4,11
	.byte	'TST',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	528
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	528
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	528
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	528
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	528
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	528
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	528
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	528
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	528
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	528
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	528
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	528
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	528
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,167,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13656
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,10,215,2,16,4,11
	.byte	'PC',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,183,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14010
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,10,205,2,16,4,11
	.byte	'OPC',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	528
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	528
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	528
	.byte	12,0,2,35,0,0,12,10,175,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14097
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,10,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,191,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14262
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,10,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,199,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14353
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,10,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,207,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14444
	.byte	4,2,35,0,0,18,228,63
	.word	680
	.byte	19,227,63,0,10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,10,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,12,10,239,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,10,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,12,10,247,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14653
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DPR',0,10,254,9,25,8,13
	.byte	'L',0
	.word	14613
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	14720
	.byte	4,2,35,4,0,18,128,1
	.word	14760
	.byte	19,15,0,14
	.word	14802
	.byte	18,128,31
	.word	680
	.byte	19,255,30,0,10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,10,99,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,12,10,231,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14828
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,10,106,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,12,10,239,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14934
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CPR',0,10,247,9,25,8,13
	.byte	'L',0
	.word	14894
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	15000
	.byte	4,2,35,4,0,18,64
	.word	15040
	.byte	19,7,0,14
	.word	15082
	.byte	18,192,31
	.word	680
	.byte	19,191,31,0,10
	.byte	'_Ifx_CPU_CPXE_Bits',0,10,121,16,4,11
	.byte	'XE',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,12,10,255,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15107
	.byte	4,2,35,0,0,18,16
	.word	15168
	.byte	19,3,0,10
	.byte	'_Ifx_CPU_DPRE_Bits',0,10,129,2,16,4,11
	.byte	'RE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,255,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15217
	.byte	4,2,35,0,0,18,16
	.word	15280
	.byte	19,3,0,10
	.byte	'_Ifx_CPU_DPWE_Bits',0,10,136,2,16,4,11
	.byte	'WE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,135,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15329
	.byte	4,2,35,0,0,18,16
	.word	15392
	.byte	19,3,0,18,208,7
	.word	680
	.byte	19,207,7,0,10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,10,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	528
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	528
	.byte	15,0,2,35,0,0,12,10,199,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15452
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,10,252,4,16,4,11
	.byte	'Timer',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,207,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15634
	.byte	4,2,35,0,0,18,12
	.word	15682
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,10,142,10,25,16,13
	.byte	'CON',0
	.word	15594
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	15722
	.byte	12,2,35,4,0,14
	.word	15731
	.byte	18,240,23
	.word	680
	.byte	19,239,23,0,10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,10,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	528
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	528
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	528
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	528
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	528
	.byte	3,0,2,35,0,0,12,10,223,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15795
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,10,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,215,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16124
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_TR',0,10,149,10,25,8,13
	.byte	'EVT',0
	.word	16084
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	16168
	.byte	4,2,35,4,0,18,64
	.word	16208
	.byte	19,7,0,14
	.word	16253
	.byte	18,192,23
	.word	680
	.byte	19,191,23,0,10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,10,72,16,4,11
	.byte	'CM',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	528
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	528
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	528
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	528
	.byte	21,0,2,35,0,0,12,10,207,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16278
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CCNT_Bits',0,10,65,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,199,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16437
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_ICNT_Bits',0,10,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,215,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16540
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,10,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,247,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16644
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,10,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,255,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16749
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,10,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,135,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16854
	.byte	4,2,35,0,0,18,232,1
	.word	680
	.byte	19,231,1,0,10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,10,166,1,16,4,11
	.byte	'DE',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	528
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	528
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	528
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	528
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	528
	.byte	19,0,2,35,0,0,12,10,167,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16970
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,10,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,12,10,151,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17197
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CREVT_Bits',0,10,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,12,10,135,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17363
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,10,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,12,10,175,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17529
	.byte	4,2,35,0,0,18,28
	.word	680
	.byte	19,27,0,10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,10,156,5,16,4,11
	.byte	'T0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	528
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	528
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,12,10,231,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17704
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DMS_Bits',0,10,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	528
	.byte	31,0,2,35,0,0,12,10,231,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17908
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DCX_Bits',0,10,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	528
	.byte	26,0,2,35,0,0,12,10,199,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18015
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,10,180,1,16,4,11
	.byte	'DTA',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	528
	.byte	31,0,2,35,0,0,12,10,175,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18122
	.byte	4,2,35,0,0,18,180,1
	.word	680
	.byte	19,179,1,0,10
	.byte	'_Ifx_CPU_PCXI_Bits',0,10,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	528
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	528
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	528
	.byte	10,0,2,35,0,0,12,10,175,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18238
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PSW_Bits',0,10,132,4,16,4,11
	.byte	'CDC',0,4
	.word	528
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	528
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	528
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	528
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	528
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	528
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	528
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	528
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	528
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	528
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	528
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	528
	.byte	1,0,2,35,0,0,12,10,231,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18381
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PC_Bits',0,10,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	528
	.byte	31,0,2,35,0,0,12,10,143,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18638
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,10,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	27,0,2,35,0,0,12,10,183,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,10,113,16,4,11
	.byte	'MODREV',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	528
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,10,247,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18910
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,10,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	29,0,2,35,0,0
.L86:
	.byte	12,10,223,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19029
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_BIV_Bits',0,10,51,16,4,11
	.byte	'VSS',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	528
	.byte	31,0,2,35,0,0,12,10,183,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19138
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_BTV_Bits',0,10,58,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	528
	.byte	31,0,2,35,0,0,12,10,191,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19232
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_ISP_Bits',0,10,128,3,16,4,11
	.byte	'ISP',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,231,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19333
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_ICR_Bits',0,10,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	528
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	528
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	528
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	528
	.byte	6,0,2,35,0,0,12,10,223,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19413
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_FCX_Bits',0,10,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	528
	.byte	12,0,2,35,0,0,12,10,159,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19570
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_LCX_Bits',0,10,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	528
	.byte	12,0,2,35,0,0,12,10,239,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19690
	.byte	4,2,35,0,0,18,16
	.word	680
	.byte	19,15,0,10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,10,139,1,16,4,11
	.byte	'CID',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	29,0,2,35,0,0,12,10,143,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19819
	.byte	4,2,35,0,0,18,172,1
	.word	680
	.byte	19,171,1,0,10
	.byte	'_Ifx_CPU_D_Bits',0,10,146,1,16,4,11
	.byte	'DATA',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,151,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19935
	.byte	4,2,35,0,0,18,64
	.word	19974
	.byte	19,15,0,18,64
	.word	680
	.byte	19,63,0,10
	.byte	'_Ifx_CPU_A_Bits',0,10,45,16,4,11
	.byte	'ADDR',0,4
	.word	528
	.byte	32,0,2,35,0,0,12,10,175,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20032
	.byte	4,2,35,0,0,18,64
	.word	20070
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,10,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	10458
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	10569
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	10609
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	10689
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	10729
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	10826
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	10952
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	11056
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	11096
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	11180
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4940
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	11432
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	11769
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3121
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	12000
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	12092
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	12173
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	12414
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4311
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	12542
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	12582
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	12769
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	12892
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	13005
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	13133
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	13214
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	13455
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	13495
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	13605
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	13645
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	13970
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	14057
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	14222
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3121
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	14313
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	14404
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	14495
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	14535
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	14812
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	14817
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	15091
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	15096
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	15208
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	15320
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	15432
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	15441
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	15779
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	15784
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	16262
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	16267
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	16397
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	16500
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	16604
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	16709
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	16814
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	16919
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	16959
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	17157
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3121
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	17323
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	17489
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	17655
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	17695
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	17868
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5280
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	17975
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	18082
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	18187
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	18227
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	18341
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	18598
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	18698
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4940
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	18870
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	18989
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	19098
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	19192
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	19293
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	19373
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	19530
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4940
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	19650
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	19770
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	19810
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	19884
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	19924
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	20014
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	20023
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	20110
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	20023
	.byte	64,4,35,192,255,3,0,14
	.word	20119
	.byte	3
	.word	21910
	.byte	29
	.byte	'IfxCpu_startCore',0,5,142,2,20
	.word	680
	.byte	1,1,1,1,5
	.byte	'cpu',0,5,142,2,46
	.word	21915
	.byte	5
	.byte	'programCounter',0,5,142,2,58
	.word	1668
	.byte	0,25
	.word	1753
	.byte	6,0,25
	.word	1795
	.byte	26
	.word	1823
	.byte	17,17,30
	.word	1408
	.byte	26
	.word	1446
	.byte	26
	.word	1464
	.byte	31
	.word	1482
	.byte	0,0,30
	.word	1634
	.byte	31
	.word	1666
	.byte	0,6,30
	.word	1484
	.byte	26
	.word	1535
	.byte	31
	.word	1553
	.byte	0,6,30
	.word	1267
	.byte	26
	.word	1307
	.byte	26
	.word	1325
	.byte	31
	.word	1343
	.byte	0,6,6,0,0,25
	.word	1847
	.byte	26
	.word	1878
	.byte	17,6,17,17,30
	.word	1408
	.byte	26
	.word	1446
	.byte	26
	.word	1464
	.byte	31
	.word	1482
	.byte	0,0,30
	.word	1634
	.byte	31
	.word	1666
	.byte	0,30
	.word	1484
	.byte	26
	.word	1535
	.byte	31
	.word	1553
	.byte	0,30
	.word	1267
	.byte	26
	.word	1307
	.byte	26
	.word	1325
	.byte	31
	.word	1343
	.byte	0,0,6,6,6,6,0,0,25
	.word	1905
	.byte	26
	.word	1946
	.byte	6,0,25
	.word	1965
	.byte	26
	.word	2020
	.byte	6,0,25
	.word	2039
	.byte	26
	.word	2079
	.byte	26
	.word	2096
	.byte	17,6,0,0,25
	.word	9976
	.byte	26
	.word	10004
	.byte	26
	.word	10018
	.byte	26
	.word	10036
	.byte	6,0,32
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,11,148,1,16
	.word	244
	.byte	33,11,231,5,9,12,13
	.byte	'k2Step',0
	.word	680
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	301
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	22239
	.byte	4,2,35,8,0,3
	.word	22279
	.byte	33,11,221,5,9,8,13
	.byte	'pDivider',0
	.word	680
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	680
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	680
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	301
	.byte	4,2,35,4,0,33,11,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	680
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	22342
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	22347
	.byte	8,2,35,8,0,33,11,212,5,9,8,13
	.byte	'value',0
	.word	1668
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1668
	.byte	4,2,35,4,0,33,11,244,5,9,48,13
	.byte	'ccucon0',0
	.word	22512
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	22512
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	22512
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	22512
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	22512
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	22512
	.byte	8,2,35,40,0,33,11,128,6,9,8,13
	.byte	'value',0
	.word	1668
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1668
	.byte	4,2,35,4,0,33,11,150,6,9,76,13
	.byte	'sysPll',0
	.word	22427
	.byte	16,2,35,0,13
	.byte	'clockDistribution',0
	.word	22548
	.byte	48,2,35,16,13
	.byte	'flashFconWaitStateConfig',0
	.word	22657
	.byte	8,2,35,64,13
	.byte	'xtalFrequency',0
	.word	1668
	.byte	4,2,35,72,0,34
	.word	22693
	.byte	3
	.word	22800
	.byte	29
	.byte	'IfxScuCcu_init',0,11,246,7,20
	.word	680
	.byte	1,1,1,1,5
	.byte	'cfg',0,11,246,7,59
	.word	22805
	.byte	0,3
	.word	241
	.byte	24
	.byte	'_Core1_start',0,12,81,6,1,1,1,1,24
	.byte	'core0_main',0,13,89,17,1,1,1,1,35
	.byte	'__INDIRECT__',0,13,1,1,1,1,1,7
	.byte	'short int',0,2,5,32
	.byte	'__wchar_t',0,13,1,1
	.word	22917
	.byte	32
	.byte	'__size_t',0,13,1,1
	.word	505
	.byte	32
	.byte	'__ptrdiff_t',0,13,1,1
	.word	521
	.byte	36,1,3
	.word	22985
	.byte	32
	.byte	'__codeptr',0,13,1,1
	.word	22987
	.byte	15,14,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,32
	.byte	'IfxScu_CCUCON0_CLKSEL',0,14,240,10,3
	.word	23010
	.byte	15,14,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,32
	.byte	'IfxScu_WDTCON1_IR',0,14,255,10,3
	.word	23107
	.byte	32
	.byte	'boolean',0,15,101,29
	.word	680
	.byte	32
	.byte	'uint8',0,15,105,29
	.word	680
	.byte	32
	.byte	'uint16',0,15,109,29
	.word	697
	.byte	32
	.byte	'uint32',0,15,113,29
	.word	1668
	.byte	32
	.byte	'uint64',0,15,118,29
	.word	387
	.byte	32
	.byte	'sint16',0,15,126,29
	.word	22917
	.byte	7
	.byte	'long int',0,4,5,32
	.byte	'sint32',0,15,131,1,29
	.word	23319
	.byte	7
	.byte	'long long int',0,8,5,32
	.byte	'sint64',0,15,138,1,29
	.word	23347
	.byte	32
	.byte	'float32',0,15,167,1,29
	.word	301
	.byte	32
	.byte	'pvoid',0,16,57,28
	.word	419
	.byte	32
	.byte	'Ifx_TickTime',0,16,79,28
	.word	23347
	.byte	15,16,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,32
	.byte	'Ifx_RxSel',0,16,140,1,3
	.word	23432
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	23570
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	24127
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	505
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	24204
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	680
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	680
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	680
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	24340
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	680
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	680
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	24620
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	24858
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	680
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	680
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	24986
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	680
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	680
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	25229
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	25464
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	25592
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	25692
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	680
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	25792
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	505
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	26000
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	697
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	26165
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	26348
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	505
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	680
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	26502
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	26866
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	697
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	680
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	680
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	680
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	27077
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	505
	.byte	23,0,2,35,0,0,32
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	27329
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	27447
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	27558
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	27721
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	27884
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	28042
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	680
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	680
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	680
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	680
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	680
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	10,0,2,35,2,0,32
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	28207
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	680
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	680
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	697
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	28536
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	28757
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	28920
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	29192
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	29345
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	29501
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	29663
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	29806
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	29971
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	30116
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	680
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	30297
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	30471
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	505
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	30631
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	505
	.byte	21,0,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	30775
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	31049
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	31188
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	680
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	697
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	680
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	680
	.byte	8,0,2,35,3,0,32
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	31351
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	697
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	31569
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	8,0,2,35,3,0,32
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	31732
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	32068
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	680
	.byte	2,0,2,35,3,0,32
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	32175
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	32627
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	32726
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	697
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	32876
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	505
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	680
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	33025
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	505
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	33186
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	697
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	33316
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	33448
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	697
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	33563
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	697
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	697
	.byte	14,0,2,35,2,0,32
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	33674
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	680
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	680
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	680
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	33832
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	34244
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	697
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	6,0,2,35,3,0,32
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	34345
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	505
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	34612
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	34748
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	34859
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	34992
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	35195
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	680
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	680
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	680
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	9,0,2,35,2,0,32
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	35551
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	35729
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	680
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	35829
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	680
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	680
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	9,0,2,35,2,0,32
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	36199
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	36385
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	36583
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	505
	.byte	21,0,2,35,0,0,32
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	36816
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	680
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	680
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	680
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	36968
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	680
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	37535
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	37829
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	680
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	680
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,32
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	38107
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	697
	.byte	14,0,2,35,2,0,32
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	38603
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	697
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	38916
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	680
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	680
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	680
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	39125
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	3,0,2,35,3,0,32
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	39336
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	39768
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	680
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	680
	.byte	7,0,2,35,3,0,32
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	39864
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	40124
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	680
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	505
	.byte	23,0,2,35,0,0,32
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	40249
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	40446
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	40599
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	40752
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	40905
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	544
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	719
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	963
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	528
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	528
	.byte	16,0,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	41160
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	41286
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	41538
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23570
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	41757
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24127
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	41821
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24204
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	41885
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24340
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	41950
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24620
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	42015
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24858
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	42080
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24986
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	42145
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25229
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	42210
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25464
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	42275
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25592
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	42340
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25692
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	42405
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25792
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	42470
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26000
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	42534
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26165
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	42598
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26348
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	42662
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26502
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	42727
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26866
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	42789
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27077
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	42851
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27329
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	42913
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27447
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	42977
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27558
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	43042
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27721
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	43108
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27884
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	43174
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28042
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	43242
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28207
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	43309
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28536
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	43377
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28757
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	43445
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28920
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	43511
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29192
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	43578
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29345
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	43647
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29501
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	43716
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29663
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	43785
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29806
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	43854
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29971
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	43923
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30116
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	43992
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30297
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	44060
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30471
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	44128
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30631
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	44196
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30775
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	44264
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31049
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	44329
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31188
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	44394
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31351
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	44460
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31569
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	44524
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31732
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	44585
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32068
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	44646
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32175
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	44706
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32627
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	44768
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32726
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	44828
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32876
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	44890
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33025
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	44958
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33186
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	45026
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33316
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	45094
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33448
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	45158
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33563
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	45223
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33674
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	45286
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33832
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	45347
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34244
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	45411
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34345
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	45472
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34612
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	45536
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34748
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	45603
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34859
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	45666
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34992
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	45727
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35195
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	45789
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35551
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	45854
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35729
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	45919
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35829
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	45984
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36199
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	46053
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36385
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	46122
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36583
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	46191
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36816
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	46256
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36968
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	46319
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37535
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	46384
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37829
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	46449
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38107
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	46514
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38603
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	46580
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39125
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	46649
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38916
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	46713
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39336
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	46778
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39768
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	46843
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39864
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	46908
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40124
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	46972
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40249
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	47038
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40446
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	47102
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40599
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	47167
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40752
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	47232
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40905
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	47297
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	640
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	923
	.byte	32
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1154
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41160
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	47448
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41286
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	47515
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41538
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	47582
	.byte	14
	.word	1194
	.byte	32
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	47647
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	47448
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	47515
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	47582
	.byte	4,2,35,8,0,14
	.word	47676
	.byte	32
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	47737
	.byte	18,8
	.word	42913
	.byte	19,1,0,18,20
	.word	680
	.byte	19,19,0,18,8
	.word	46256
	.byte	19,1,0,14
	.word	47676
	.byte	18,24
	.word	1194
	.byte	19,1,0,14
	.word	47796
	.byte	18,40
	.word	680
	.byte	19,39,0,18,16
	.word	42727
	.byte	19,3,0,18,16
	.word	44706
	.byte	19,3,0,18,180,3
	.word	680
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4940
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	44646
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3121
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	45347
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	46191
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	45789
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	45854
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	45919
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	46122
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	45984
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	46053
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	41950
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	42015
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	44524
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	44460
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	42080
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	42145
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	42210
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	42275
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	46778
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3121
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	46649
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	41885
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	46972
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	46713
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3121
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	43511
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	47764
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	42977
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	47038
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	42340
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	42405
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	47773
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	45666
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	44828
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	45411
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	45286
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	44768
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	44264
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	43242
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	43042
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	43108
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	46908
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3121
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	46319
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	46514
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	46580
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	47782
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3121
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	42662
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	42534
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	46384
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	46449
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	47791
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	42851
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	47805
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5280
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	47297
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	47232
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	47102
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	47167
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3121
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	45094
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	45158
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	42470
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	45223
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4940
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	46843
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	19810
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	44890
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	44958
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	45026
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	17695
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	45603
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4940
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	44329
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	43174
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	44394
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	43445
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	43309
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3121
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	43992
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	44060
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	44128
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	44196
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	43578
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	43647
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	43716
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	43785
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	43854
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	43923
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	43377
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3121
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	45536
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	45472
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	47810
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	47819
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	42789
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	44585
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	45727
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	47828
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3121
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	42598
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	47837
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	41821
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	41757
	.byte	4,3,35,252,7,0,14
	.word	47848
	.byte	32
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	49838
	.byte	32
	.byte	'Ifx_CPU_A_Bits',0,10,48,3
	.word	20032
	.byte	32
	.byte	'Ifx_CPU_BIV_Bits',0,10,55,3
	.word	19138
	.byte	32
	.byte	'Ifx_CPU_BTV_Bits',0,10,62,3
	.word	19232
	.byte	32
	.byte	'Ifx_CPU_CCNT_Bits',0,10,69,3
	.word	16437
	.byte	32
	.byte	'Ifx_CPU_CCTRL_Bits',0,10,80,3
	.word	16278
	.byte	32
	.byte	'Ifx_CPU_COMPAT_Bits',0,10,89,3
	.word	13506
	.byte	32
	.byte	'Ifx_CPU_CORE_ID_Bits',0,10,96,3
	.word	19029
	.byte	32
	.byte	'Ifx_CPU_CPR_L_Bits',0,10,103,3
	.word	14828
	.byte	32
	.byte	'Ifx_CPU_CPR_U_Bits',0,10,110,3
	.word	14934
	.byte	32
	.byte	'Ifx_CPU_CPU_ID_Bits',0,10,118,3
	.word	18910
	.byte	32
	.byte	'Ifx_CPU_CPXE_Bits',0,10,125,3
	.word	15107
	.byte	32
	.byte	'Ifx_CPU_CREVT_Bits',0,10,136,1,3
	.word	17363
	.byte	32
	.byte	'Ifx_CPU_CUS_ID_Bits',0,10,143,1,3
	.word	19819
	.byte	32
	.byte	'Ifx_CPU_D_Bits',0,10,149,1,3
	.word	19935
	.byte	32
	.byte	'Ifx_CPU_DATR_Bits',0,10,163,1,3
	.word	11809
	.byte	32
	.byte	'Ifx_CPU_DBGSR_Bits',0,10,177,1,3
	.word	16970
	.byte	32
	.byte	'Ifx_CPU_DBGTCR_Bits',0,10,184,1,3
	.word	18122
	.byte	32
	.byte	'Ifx_CPU_DCON0_Bits',0,10,192,1,3
	.word	12454
	.byte	32
	.byte	'Ifx_CPU_DCON2_Bits',0,10,199,1,3
	.word	11107
	.byte	32
	.byte	'Ifx_CPU_DCX_Bits',0,10,206,1,3
	.word	18015
	.byte	32
	.byte	'Ifx_CPU_DEADD_Bits',0,10,212,1,3
	.word	12040
	.byte	32
	.byte	'Ifx_CPU_DIEAR_Bits',0,10,218,1,3
	.word	12132
	.byte	32
	.byte	'Ifx_CPU_DIETR_Bits',0,10,233,1,3
	.word	12213
	.byte	32
	.byte	'Ifx_CPU_DMS_Bits',0,10,240,1,3
	.word	17908
	.byte	32
	.byte	'Ifx_CPU_DPR_L_Bits',0,10,247,1,3
	.word	14546
	.byte	32
	.byte	'Ifx_CPU_DPR_U_Bits',0,10,254,1,3
	.word	14653
	.byte	32
	.byte	'Ifx_CPU_DPRE_Bits',0,10,133,2,3
	.word	15217
	.byte	32
	.byte	'Ifx_CPU_DPWE_Bits',0,10,140,2,3
	.word	15329
	.byte	32
	.byte	'Ifx_CPU_DSTR_Bits',0,10,161,2,3
	.word	11472
	.byte	32
	.byte	'Ifx_CPU_EXEVT_Bits',0,10,172,2,3
	.word	17197
	.byte	32
	.byte	'Ifx_CPU_FCX_Bits',0,10,180,2,3
	.word	19570
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,10,202,2,3
	.word	13656
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,10,212,2,3
	.word	14097
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,10,218,2,3
	.word	14010
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,10,224,2,3
	.word	14262
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,10,230,2,3
	.word	14353
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,10,236,2,3
	.word	14444
	.byte	32
	.byte	'Ifx_CPU_ICNT_Bits',0,10,243,2,3
	.word	16540
	.byte	32
	.byte	'Ifx_CPU_ICR_Bits',0,10,253,2,3
	.word	19413
	.byte	32
	.byte	'Ifx_CPU_ISP_Bits',0,10,131,3,3
	.word	19333
	.byte	32
	.byte	'Ifx_CPU_LCX_Bits',0,10,139,3,3
	.word	19690
	.byte	32
	.byte	'Ifx_CPU_M1CNT_Bits',0,10,146,3,3
	.word	16644
	.byte	32
	.byte	'Ifx_CPU_M2CNT_Bits',0,10,153,3,3
	.word	16749
	.byte	32
	.byte	'Ifx_CPU_M3CNT_Bits',0,10,160,3,3
	.word	16854
	.byte	32
	.byte	'Ifx_CPU_PC_Bits',0,10,167,3,3
	.word	18638
	.byte	32
	.byte	'Ifx_CPU_PCON0_Bits',0,10,175,3,3
	.word	13045
	.byte	32
	.byte	'Ifx_CPU_PCON1_Bits',0,10,183,3,3
	.word	12809
	.byte	32
	.byte	'Ifx_CPU_PCON2_Bits',0,10,190,3,3
	.word	12932
	.byte	32
	.byte	'Ifx_CPU_PCXI_Bits',0,10,200,3,3
	.word	18238
	.byte	32
	.byte	'Ifx_CPU_PIEAR_Bits',0,10,206,3,3
	.word	13173
	.byte	32
	.byte	'Ifx_CPU_PIETR_Bits',0,10,221,3,3
	.word	13254
	.byte	32
	.byte	'Ifx_CPU_PMA0_Bits',0,10,229,3,3
	.word	10740
	.byte	32
	.byte	'Ifx_CPU_PMA1_Bits',0,10,237,3,3
	.word	10866
	.byte	32
	.byte	'Ifx_CPU_PMA2_Bits',0,10,244,3,3
	.word	10992
	.byte	32
	.byte	'Ifx_CPU_PSTR_Bits',0,10,129,4,3
	.word	12593
	.byte	32
	.byte	'Ifx_CPU_PSW_Bits',0,10,147,4,3
	.word	18381
	.byte	32
	.byte	'Ifx_CPU_SEGEN_Bits',0,10,156,4,3
	.word	10469
	.byte	32
	.byte	'Ifx_CPU_SMACON_Bits',0,10,171,4,3
	.word	11220
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,10,174,4,16,4,11
	.byte	'EN',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,10,177,4,3
	.word	51483
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,10,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,10,183,4,3
	.word	51566
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,10,186,4,16,4,11
	.byte	'EN',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,10,189,4,3
	.word	51657
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,10,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,10,195,4,3
	.word	51748
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,10,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	27,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,10,202,4,3
	.word	51847
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,10,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	27,0,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,10,209,4,3
	.word	51954
	.byte	32
	.byte	'Ifx_CPU_SWEVT_Bits',0,10,220,4,3
	.word	17529
	.byte	32
	.byte	'Ifx_CPU_SYSCON_Bits',0,10,231,4,3
	.word	18738
	.byte	32
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,10,238,4,3
	.word	10622
	.byte	32
	.byte	'Ifx_CPU_TPS_CON_Bits',0,10,249,4,3
	.word	15452
	.byte	32
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,10,255,4,3
	.word	15634
	.byte	32
	.byte	'Ifx_CPU_TR_ADR_Bits',0,10,133,5,3
	.word	16124
	.byte	32
	.byte	'Ifx_CPU_TR_EVT_Bits',0,10,153,5,3
	.word	15795
	.byte	32
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,10,167,5,3
	.word	17704
	.byte	32
	.byte	'Ifx_CPU_A',0,10,180,5,3
	.word	20070
	.byte	32
	.byte	'Ifx_CPU_BIV',0,10,188,5,3
	.word	19192
	.byte	32
	.byte	'Ifx_CPU_BTV',0,10,196,5,3
	.word	19293
	.byte	32
	.byte	'Ifx_CPU_CCNT',0,10,204,5,3
	.word	16500
	.byte	32
	.byte	'Ifx_CPU_CCTRL',0,10,212,5,3
	.word	16397
	.byte	32
	.byte	'Ifx_CPU_COMPAT',0,10,220,5,3
	.word	13605
	.byte	32
	.byte	'Ifx_CPU_CORE_ID',0,10,228,5,3
	.word	19098
	.byte	32
	.byte	'Ifx_CPU_CPR_L',0,10,236,5,3
	.word	14894
	.byte	32
	.byte	'Ifx_CPU_CPR_U',0,10,244,5,3
	.word	15000
	.byte	32
	.byte	'Ifx_CPU_CPU_ID',0,10,252,5,3
	.word	18989
	.byte	32
	.byte	'Ifx_CPU_CPXE',0,10,132,6,3
	.word	15168
	.byte	32
	.byte	'Ifx_CPU_CREVT',0,10,140,6,3
	.word	17489
	.byte	32
	.byte	'Ifx_CPU_CUS_ID',0,10,148,6,3
	.word	19884
	.byte	32
	.byte	'Ifx_CPU_D',0,10,156,6,3
	.word	19974
	.byte	32
	.byte	'Ifx_CPU_DATR',0,10,164,6,3
	.word	12000
	.byte	32
	.byte	'Ifx_CPU_DBGSR',0,10,172,6,3
	.word	17157
	.byte	32
	.byte	'Ifx_CPU_DBGTCR',0,10,180,6,3
	.word	18187
	.byte	32
	.byte	'Ifx_CPU_DCON0',0,10,188,6,3
	.word	12542
	.byte	32
	.byte	'Ifx_CPU_DCON2',0,10,196,6,3
	.word	11180
	.byte	32
	.byte	'Ifx_CPU_DCX',0,10,204,6,3
	.word	18082
	.byte	32
	.byte	'Ifx_CPU_DEADD',0,10,212,6,3
	.word	12092
	.byte	32
	.byte	'Ifx_CPU_DIEAR',0,10,220,6,3
	.word	12173
	.byte	32
	.byte	'Ifx_CPU_DIETR',0,10,228,6,3
	.word	12414
	.byte	32
	.byte	'Ifx_CPU_DMS',0,10,236,6,3
	.word	17975
	.byte	32
	.byte	'Ifx_CPU_DPR_L',0,10,244,6,3
	.word	14613
	.byte	32
	.byte	'Ifx_CPU_DPR_U',0,10,252,6,3
	.word	14720
	.byte	32
	.byte	'Ifx_CPU_DPRE',0,10,132,7,3
	.word	15280
	.byte	32
	.byte	'Ifx_CPU_DPWE',0,10,140,7,3
	.word	15392
	.byte	32
	.byte	'Ifx_CPU_DSTR',0,10,148,7,3
	.word	11769
	.byte	32
	.byte	'Ifx_CPU_EXEVT',0,10,156,7,3
	.word	17323
	.byte	32
	.byte	'Ifx_CPU_FCX',0,10,164,7,3
	.word	19650
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,10,172,7,3
	.word	13970
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,10,180,7,3
	.word	14222
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,10,188,7,3
	.word	14057
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,10,196,7,3
	.word	14313
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,10,204,7,3
	.word	14404
	.byte	32
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,10,212,7,3
	.word	14495
	.byte	32
	.byte	'Ifx_CPU_ICNT',0,10,220,7,3
	.word	16604
	.byte	32
	.byte	'Ifx_CPU_ICR',0,10,228,7,3
	.word	19530
	.byte	32
	.byte	'Ifx_CPU_ISP',0,10,236,7,3
	.word	19373
	.byte	32
	.byte	'Ifx_CPU_LCX',0,10,244,7,3
	.word	19770
	.byte	32
	.byte	'Ifx_CPU_M1CNT',0,10,252,7,3
	.word	16709
	.byte	32
	.byte	'Ifx_CPU_M2CNT',0,10,132,8,3
	.word	16814
	.byte	32
	.byte	'Ifx_CPU_M3CNT',0,10,140,8,3
	.word	16919
	.byte	32
	.byte	'Ifx_CPU_PC',0,10,148,8,3
	.word	18698
	.byte	32
	.byte	'Ifx_CPU_PCON0',0,10,156,8,3
	.word	13133
	.byte	32
	.byte	'Ifx_CPU_PCON1',0,10,164,8,3
	.word	12892
	.byte	32
	.byte	'Ifx_CPU_PCON2',0,10,172,8,3
	.word	13005
	.byte	32
	.byte	'Ifx_CPU_PCXI',0,10,180,8,3
	.word	18341
	.byte	32
	.byte	'Ifx_CPU_PIEAR',0,10,188,8,3
	.word	13214
	.byte	32
	.byte	'Ifx_CPU_PIETR',0,10,196,8,3
	.word	13455
	.byte	32
	.byte	'Ifx_CPU_PMA0',0,10,204,8,3
	.word	10826
	.byte	32
	.byte	'Ifx_CPU_PMA1',0,10,212,8,3
	.word	10952
	.byte	32
	.byte	'Ifx_CPU_PMA2',0,10,220,8,3
	.word	11056
	.byte	32
	.byte	'Ifx_CPU_PSTR',0,10,228,8,3
	.word	12769
	.byte	32
	.byte	'Ifx_CPU_PSW',0,10,236,8,3
	.word	18598
	.byte	32
	.byte	'Ifx_CPU_SEGEN',0,10,244,8,3
	.word	10569
	.byte	32
	.byte	'Ifx_CPU_SMACON',0,10,252,8,3
	.word	11432
	.byte	12,10,255,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51483
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENA',0,10,132,9,3
	.word	53644
	.byte	12,10,135,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51566
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_ACCENB',0,10,140,9,3
	.word	53714
	.byte	12,10,143,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51657
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,10,148,9,3
	.word	53784
	.byte	12,10,151,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51748
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,10,156,9,3
	.word	53858
	.byte	12,10,159,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51847
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,10,164,9,3
	.word	53932
	.byte	12,10,167,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51954
	.byte	4,2,35,0,0,32
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,10,172,9,3
	.word	54002
	.byte	32
	.byte	'Ifx_CPU_SWEVT',0,10,180,9,3
	.word	17655
	.byte	32
	.byte	'Ifx_CPU_SYSCON',0,10,188,9,3
	.word	18870
	.byte	32
	.byte	'Ifx_CPU_TASK_ASI',0,10,196,9,3
	.word	10689
	.byte	32
	.byte	'Ifx_CPU_TPS_CON',0,10,204,9,3
	.word	15594
	.byte	32
	.byte	'Ifx_CPU_TPS_TIMER',0,10,212,9,3
	.word	15682
	.byte	32
	.byte	'Ifx_CPU_TR_ADR',0,10,220,9,3
	.word	16168
	.byte	32
	.byte	'Ifx_CPU_TR_EVT',0,10,228,9,3
	.word	16084
	.byte	32
	.byte	'Ifx_CPU_TRIG_ACC',0,10,236,9,3
	.word	17868
	.byte	14
	.word	15040
	.byte	32
	.byte	'Ifx_CPU_CPR',0,10,251,9,3
	.word	54271
	.byte	14
	.word	14760
	.byte	32
	.byte	'Ifx_CPU_DPR',0,10,130,10,3
	.word	54297
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,10,133,10,25,16,13
	.byte	'LA',0
	.word	53932
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	54002
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	53784
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	53858
	.byte	4,2,35,12,0,14
	.word	54323
	.byte	32
	.byte	'Ifx_CPU_SPROT_RGN',0,10,139,10,3
	.word	54405
	.byte	14
	.word	15731
	.byte	32
	.byte	'Ifx_CPU_TPS',0,10,146,10,3
	.word	54437
	.byte	14
	.word	16208
	.byte	32
	.byte	'Ifx_CPU_TR',0,10,153,10,3
	.word	54463
	.byte	14
	.word	20119
	.byte	32
	.byte	'Ifx_CPU',0,10,130,11,3
	.word	54488
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,32
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	54510
	.byte	32
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1555
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,17,45,16,4,11
	.byte	'SRPN',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	680
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	680
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	680
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_SRC_SRCR_Bits',0,17,62,3
	.word	54608
	.byte	12,17,70,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54608
	.byte	4,2,35,0,0,32
	.byte	'Ifx_SRC_SRCR',0,17,75,3
	.word	54924
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,17,86,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	54984
	.byte	32
	.byte	'Ifx_SRC_AGBT',0,17,89,3
	.word	55016
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,17,92,25,12,13
	.byte	'TX',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54924
	.byte	4,2,35,8,0,14
	.word	55042
	.byte	32
	.byte	'Ifx_SRC_ASCLIN',0,17,97,3
	.word	55101
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,17,100,25,4,13
	.byte	'SBSRC',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	55129
	.byte	32
	.byte	'Ifx_SRC_BCUSPB',0,17,103,3
	.word	55166
	.byte	18,64
	.word	54924
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,17,106,25,64,13
	.byte	'INT',0
	.word	55194
	.byte	64,2,35,0,0,14
	.word	55203
	.byte	32
	.byte	'Ifx_SRC_CAN',0,17,109,3
	.word	55235
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,17,112,25,16,13
	.byte	'SR0',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54924
	.byte	4,2,35,12,0,14
	.word	55260
	.byte	32
	.byte	'Ifx_SRC_CCU6',0,17,118,3
	.word	55332
	.byte	18,8
	.word	54924
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,17,121,25,8,13
	.byte	'SR',0
	.word	55358
	.byte	8,2,35,0,0,14
	.word	55367
	.byte	32
	.byte	'Ifx_SRC_CERBERUS',0,17,124,3
	.word	55403
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,17,127,25,16,13
	.byte	'MI',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	54924
	.byte	4,2,35,12,0,14
	.word	55433
	.byte	32
	.byte	'Ifx_SRC_CIF',0,17,133,1,3
	.word	55506
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,17,136,1,25,4,13
	.byte	'SBSRC',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	55532
	.byte	32
	.byte	'Ifx_SRC_CPU',0,17,139,1,3
	.word	55567
	.byte	18,192,1
	.word	54924
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,17,142,1,25,208,1,13
	.byte	'ERR',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5280
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	55593
	.byte	192,1,2,35,16,0,14
	.word	55603
	.byte	32
	.byte	'Ifx_SRC_DMA',0,17,147,1,3
	.word	55670
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,17,150,1,25,8,13
	.byte	'SRM',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	54924
	.byte	4,2,35,4,0,14
	.word	55696
	.byte	32
	.byte	'Ifx_SRC_DSADC',0,17,154,1,3
	.word	55744
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,17,157,1,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	55772
	.byte	32
	.byte	'Ifx_SRC_EMEM',0,17,160,1,3
	.word	55805
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,17,163,1,25,80,13
	.byte	'INT',0
	.word	55358
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	55358
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	55358
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	55358
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	54924
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	54924
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	47810
	.byte	40,2,35,40,0,14
	.word	55832
	.byte	32
	.byte	'Ifx_SRC_ERAY',0,17,172,1,3
	.word	55959
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,17,175,1,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	55986
	.byte	32
	.byte	'Ifx_SRC_ETH',0,17,178,1,3
	.word	56018
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,17,181,1,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	56044
	.byte	32
	.byte	'Ifx_SRC_FCE',0,17,184,1,3
	.word	56076
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,17,187,1,25,12,13
	.byte	'DONE',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	54924
	.byte	4,2,35,8,0,14
	.word	56102
	.byte	32
	.byte	'Ifx_SRC_FFT',0,17,192,1,3
	.word	56162
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,17,195,1,25,32,13
	.byte	'SR0',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54924
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	19810
	.byte	16,2,35,16,0,14
	.word	56188
	.byte	32
	.byte	'Ifx_SRC_GPSR',0,17,202,1,3
	.word	56282
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,17,205,1,25,48,13
	.byte	'CIRQ',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	54924
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	54924
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	54924
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4311
	.byte	24,2,35,24,0,14
	.word	56309
	.byte	32
	.byte	'Ifx_SRC_GPT12',0,17,214,1,3
	.word	56426
	.byte	18,12
	.word	54924
	.byte	19,2,0,18,32
	.word	54924
	.byte	19,7,0,18,32
	.word	56463
	.byte	19,0,0,18,88
	.word	680
	.byte	19,87,0,18,108
	.word	54924
	.byte	19,26,0,18,96
	.word	680
	.byte	19,95,0,18,96
	.word	56463
	.byte	19,2,0,18,160,3
	.word	680
	.byte	19,159,3,0,18,64
	.word	56463
	.byte	19,1,0,18,192,3
	.word	680
	.byte	19,191,3,0,18,16
	.word	54924
	.byte	19,3,0,18,64
	.word	56548
	.byte	19,3,0,18,192,2
	.word	680
	.byte	19,191,2,0,18,52
	.word	680
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,17,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	56454
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3121
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	54924
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	54924
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	55358
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4940
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	56472
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	56481
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	56490
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	56499
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	54924
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5280
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	56508
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	56517
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	56508
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	56517
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	56528
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	56537
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	56557
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	56566
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	56454
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	56577
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	56454
	.byte	12,3,35,192,18,0,14
	.word	56586
	.byte	32
	.byte	'Ifx_SRC_GTM',0,17,243,1,3
	.word	57046
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,17,246,1,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	57072
	.byte	32
	.byte	'Ifx_SRC_HSCT',0,17,249,1,3
	.word	57105
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,17,252,1,25,16,13
	.byte	'COK',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	54924
	.byte	4,2,35,12,0,14
	.word	57132
	.byte	32
	.byte	'Ifx_SRC_HSSL',0,17,130,2,3
	.word	57205
	.byte	18,56
	.word	680
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,17,133,2,25,80,13
	.byte	'BREQ',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	54924
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	54924
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	54924
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	57232
	.byte	56,2,35,24,0,14
	.word	57241
	.byte	32
	.byte	'Ifx_SRC_I2C',0,17,142,2,3
	.word	57364
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,17,145,2,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	57390
	.byte	32
	.byte	'Ifx_SRC_LMU',0,17,148,2,3
	.word	57422
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,17,151,2,25,20,13
	.byte	'SR0',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54924
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	54924
	.byte	4,2,35,16,0,14
	.word	57448
	.byte	32
	.byte	'Ifx_SRC_MSC',0,17,158,2,3
	.word	57533
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,17,161,2,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	57559
	.byte	32
	.byte	'Ifx_SRC_PMU',0,17,164,2,3
	.word	57591
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,17,167,2,25,32,13
	.byte	'SR',0
	.word	56463
	.byte	32,2,35,0,0,14
	.word	57617
	.byte	32
	.byte	'Ifx_SRC_PSI5',0,17,170,2,3
	.word	57650
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,17,173,2,25,32,13
	.byte	'SR',0
	.word	56463
	.byte	32,2,35,0,0,14
	.word	57677
	.byte	32
	.byte	'Ifx_SRC_PSI5S',0,17,176,2,3
	.word	57711
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,17,179,2,25,24,13
	.byte	'TX',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	54924
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	54924
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	54924
	.byte	4,2,35,20,0,14
	.word	57739
	.byte	32
	.byte	'Ifx_SRC_QSPI',0,17,187,2,3
	.word	57832
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,17,190,2,25,4,13
	.byte	'SR',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	57859
	.byte	32
	.byte	'Ifx_SRC_SCR',0,17,193,2,3
	.word	57891
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,17,196,2,25,20,13
	.byte	'DTS',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	56548
	.byte	16,2,35,4,0,14
	.word	57917
	.byte	32
	.byte	'Ifx_SRC_SCU',0,17,200,2,3
	.word	57963
	.byte	18,24
	.word	54924
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,17,203,2,25,24,13
	.byte	'SR',0
	.word	57989
	.byte	24,2,35,0,0,14
	.word	57998
	.byte	32
	.byte	'Ifx_SRC_SENT',0,17,206,2,3
	.word	58031
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,17,209,2,25,12,13
	.byte	'SR',0
	.word	56454
	.byte	12,2,35,0,0,14
	.word	58058
	.byte	32
	.byte	'Ifx_SRC_SMU',0,17,212,2,3
	.word	58090
	.byte	10
	.byte	'_Ifx_SRC_STM',0,17,215,2,25,8,13
	.byte	'SR0',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54924
	.byte	4,2,35,4,0,14
	.word	58116
	.byte	32
	.byte	'Ifx_SRC_STM',0,17,219,2,3
	.word	58162
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,17,222,2,25,16,13
	.byte	'SR0',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54924
	.byte	4,2,35,12,0,14
	.word	58188
	.byte	32
	.byte	'Ifx_SRC_VADCCG',0,17,228,2,3
	.word	58263
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,17,231,2,25,16,13
	.byte	'SR0',0
	.word	54924
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54924
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54924
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54924
	.byte	4,2,35,12,0,14
	.word	58292
	.byte	32
	.byte	'Ifx_SRC_VADCG',0,17,237,2,3
	.word	58366
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,17,240,2,25,4,13
	.byte	'SRC',0
	.word	54924
	.byte	4,2,35,0,0,14
	.word	58394
	.byte	32
	.byte	'Ifx_SRC_XBAR',0,17,243,2,3
	.word	58428
	.byte	18,4
	.word	54984
	.byte	19,0,0,14
	.word	58455
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,17,128,3,25,4,13
	.byte	'AGBT',0
	.word	58464
	.byte	4,2,35,0,0,14
	.word	58469
	.byte	32
	.byte	'Ifx_SRC_GAGBT',0,17,131,3,3
	.word	58505
	.byte	18,48
	.word	55042
	.byte	19,3,0,14
	.word	58533
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,17,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	58542
	.byte	48,2,35,0,0,14
	.word	58547
	.byte	32
	.byte	'Ifx_SRC_GASCLIN',0,17,137,3,3
	.word	58587
	.byte	14
	.word	55129
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,17,140,3,25,4,13
	.byte	'SPB',0
	.word	58617
	.byte	4,2,35,0,0,14
	.word	58622
	.byte	32
	.byte	'Ifx_SRC_GBCU',0,17,143,3,3
	.word	58656
	.byte	18,64
	.word	55203
	.byte	19,0,0,14
	.word	58683
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,17,146,3,25,64,13
	.byte	'CAN',0
	.word	58692
	.byte	64,2,35,0,0,14
	.word	58697
	.byte	32
	.byte	'Ifx_SRC_GCAN',0,17,149,3,3
	.word	58731
	.byte	18,32
	.word	55260
	.byte	19,1,0,14
	.word	58758
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,17,152,3,25,32,13
	.byte	'CCU6',0
	.word	58767
	.byte	32,2,35,0,0,14
	.word	58772
	.byte	32
	.byte	'Ifx_SRC_GCCU6',0,17,155,3,3
	.word	58808
	.byte	14
	.word	55367
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,17,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	58836
	.byte	8,2,35,0,0,14
	.word	58841
	.byte	32
	.byte	'Ifx_SRC_GCERBERUS',0,17,161,3,3
	.word	58885
	.byte	18,16
	.word	55433
	.byte	19,0,0,14
	.word	58917
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,17,164,3,25,16,13
	.byte	'CIF',0
	.word	58926
	.byte	16,2,35,0,0,14
	.word	58931
	.byte	32
	.byte	'Ifx_SRC_GCIF',0,17,167,3,3
	.word	58965
	.byte	18,8
	.word	55532
	.byte	19,1,0,14
	.word	58992
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,17,170,3,25,8,13
	.byte	'CPU',0
	.word	59001
	.byte	8,2,35,0,0,14
	.word	59006
	.byte	32
	.byte	'Ifx_SRC_GCPU',0,17,173,3,3
	.word	59040
	.byte	18,208,1
	.word	55603
	.byte	19,0,0,14
	.word	59067
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,17,176,3,25,208,1,13
	.byte	'DMA',0
	.word	59077
	.byte	208,1,2,35,0,0,14
	.word	59082
	.byte	32
	.byte	'Ifx_SRC_GDMA',0,17,179,3,3
	.word	59118
	.byte	14
	.word	55696
	.byte	14
	.word	55696
	.byte	14
	.word	55696
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,17,182,3,25,32,13
	.byte	'DSADC0',0
	.word	59145
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4940
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	59150
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	59155
	.byte	8,2,35,24,0,14
	.word	59160
	.byte	32
	.byte	'Ifx_SRC_GDSADC',0,17,188,3,3
	.word	59251
	.byte	18,4
	.word	55772
	.byte	19,0,0,14
	.word	59280
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,17,191,3,25,4,13
	.byte	'EMEM',0
	.word	59289
	.byte	4,2,35,0,0,14
	.word	59294
	.byte	32
	.byte	'Ifx_SRC_GEMEM',0,17,194,3,3
	.word	59330
	.byte	18,80
	.word	55832
	.byte	19,0,0,14
	.word	59358
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,17,197,3,25,80,13
	.byte	'ERAY',0
	.word	59367
	.byte	80,2,35,0,0,14
	.word	59372
	.byte	32
	.byte	'Ifx_SRC_GERAY',0,17,200,3,3
	.word	59408
	.byte	18,4
	.word	55986
	.byte	19,0,0,14
	.word	59436
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,17,203,3,25,4,13
	.byte	'ETH',0
	.word	59445
	.byte	4,2,35,0,0,14
	.word	59450
	.byte	32
	.byte	'Ifx_SRC_GETH',0,17,206,3,3
	.word	59484
	.byte	18,4
	.word	56044
	.byte	19,0,0,14
	.word	59511
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,17,209,3,25,4,13
	.byte	'FCE',0
	.word	59520
	.byte	4,2,35,0,0,14
	.word	59525
	.byte	32
	.byte	'Ifx_SRC_GFCE',0,17,212,3,3
	.word	59559
	.byte	18,12
	.word	56102
	.byte	19,0,0,14
	.word	59586
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,17,215,3,25,12,13
	.byte	'FFT',0
	.word	59595
	.byte	12,2,35,0,0,14
	.word	59600
	.byte	32
	.byte	'Ifx_SRC_GFFT',0,17,218,3,3
	.word	59634
	.byte	18,64
	.word	56188
	.byte	19,1,0,14
	.word	59661
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,17,221,3,25,64,13
	.byte	'GPSR',0
	.word	59670
	.byte	64,2,35,0,0,14
	.word	59675
	.byte	32
	.byte	'Ifx_SRC_GGPSR',0,17,224,3,3
	.word	59711
	.byte	18,48
	.word	56309
	.byte	19,0,0,14
	.word	59739
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,17,227,3,25,48,13
	.byte	'GPT12',0
	.word	59748
	.byte	48,2,35,0,0,14
	.word	59753
	.byte	32
	.byte	'Ifx_SRC_GGPT12',0,17,230,3,3
	.word	59791
	.byte	18,204,18
	.word	56586
	.byte	19,0,0,14
	.word	59820
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,17,233,3,25,204,18,13
	.byte	'GTM',0
	.word	59830
	.byte	204,18,2,35,0,0,14
	.word	59835
	.byte	32
	.byte	'Ifx_SRC_GGTM',0,17,236,3,3
	.word	59871
	.byte	18,4
	.word	57072
	.byte	19,0,0,14
	.word	59898
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,17,239,3,25,4,13
	.byte	'HSCT',0
	.word	59907
	.byte	4,2,35,0,0,14
	.word	59912
	.byte	32
	.byte	'Ifx_SRC_GHSCT',0,17,242,3,3
	.word	59948
	.byte	18,64
	.word	57132
	.byte	19,3,0,14
	.word	59976
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,17,245,3,25,68,13
	.byte	'HSSL',0
	.word	59985
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	54924
	.byte	4,2,35,64,0,14
	.word	59990
	.byte	32
	.byte	'Ifx_SRC_GHSSL',0,17,249,3,3
	.word	60039
	.byte	18,80
	.word	57241
	.byte	19,0,0,14
	.word	60067
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,17,252,3,25,80,13
	.byte	'I2C',0
	.word	60076
	.byte	80,2,35,0,0,14
	.word	60081
	.byte	32
	.byte	'Ifx_SRC_GI2C',0,17,255,3,3
	.word	60115
	.byte	18,4
	.word	57390
	.byte	19,0,0,14
	.word	60142
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,17,130,4,25,4,13
	.byte	'LMU',0
	.word	60151
	.byte	4,2,35,0,0,14
	.word	60156
	.byte	32
	.byte	'Ifx_SRC_GLMU',0,17,133,4,3
	.word	60190
	.byte	18,40
	.word	57448
	.byte	19,1,0,14
	.word	60217
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,17,136,4,25,40,13
	.byte	'MSC',0
	.word	60226
	.byte	40,2,35,0,0,14
	.word	60231
	.byte	32
	.byte	'Ifx_SRC_GMSC',0,17,139,4,3
	.word	60265
	.byte	18,8
	.word	57559
	.byte	19,1,0,14
	.word	60292
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,17,142,4,25,8,13
	.byte	'PMU',0
	.word	60301
	.byte	8,2,35,0,0,14
	.word	60306
	.byte	32
	.byte	'Ifx_SRC_GPMU',0,17,145,4,3
	.word	60340
	.byte	18,32
	.word	57617
	.byte	19,0,0,14
	.word	60367
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,17,148,4,25,32,13
	.byte	'PSI5',0
	.word	60376
	.byte	32,2,35,0,0,14
	.word	60381
	.byte	32
	.byte	'Ifx_SRC_GPSI5',0,17,151,4,3
	.word	60417
	.byte	18,32
	.word	57677
	.byte	19,0,0,14
	.word	60445
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,17,154,4,25,32,13
	.byte	'PSI5S',0
	.word	60454
	.byte	32,2,35,0,0,14
	.word	60459
	.byte	32
	.byte	'Ifx_SRC_GPSI5S',0,17,157,4,3
	.word	60497
	.byte	18,96
	.word	57739
	.byte	19,3,0,14
	.word	60526
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,17,160,4,25,96,13
	.byte	'QSPI',0
	.word	60535
	.byte	96,2,35,0,0,14
	.word	60540
	.byte	32
	.byte	'Ifx_SRC_GQSPI',0,17,163,4,3
	.word	60576
	.byte	18,4
	.word	57859
	.byte	19,0,0,14
	.word	60604
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,17,166,4,25,4,13
	.byte	'SCR',0
	.word	60613
	.byte	4,2,35,0,0,14
	.word	60618
	.byte	32
	.byte	'Ifx_SRC_GSCR',0,17,169,4,3
	.word	60652
	.byte	14
	.word	57917
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,17,172,4,25,20,13
	.byte	'SCU',0
	.word	60679
	.byte	20,2,35,0,0,14
	.word	60684
	.byte	32
	.byte	'Ifx_SRC_GSCU',0,17,175,4,3
	.word	60718
	.byte	18,24
	.word	57998
	.byte	19,0,0,14
	.word	60745
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,17,178,4,25,24,13
	.byte	'SENT',0
	.word	60754
	.byte	24,2,35,0,0,14
	.word	60759
	.byte	32
	.byte	'Ifx_SRC_GSENT',0,17,181,4,3
	.word	60795
	.byte	18,12
	.word	58058
	.byte	19,0,0,14
	.word	60823
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,17,184,4,25,12,13
	.byte	'SMU',0
	.word	60832
	.byte	12,2,35,0,0,14
	.word	60837
	.byte	32
	.byte	'Ifx_SRC_GSMU',0,17,187,4,3
	.word	60871
	.byte	18,16
	.word	58116
	.byte	19,1,0,14
	.word	60898
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,17,190,4,25,16,13
	.byte	'STM',0
	.word	60907
	.byte	16,2,35,0,0,14
	.word	60912
	.byte	32
	.byte	'Ifx_SRC_GSTM',0,17,193,4,3
	.word	60946
	.byte	18,64
	.word	58292
	.byte	19,3,0,14
	.word	60973
	.byte	18,224,1
	.word	680
	.byte	19,223,1,0,18,32
	.word	58188
	.byte	19,1,0,14
	.word	60998
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,17,196,4,25,192,2,13
	.byte	'G',0
	.word	60982
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	60987
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	61007
	.byte	32,3,35,160,2,0,14
	.word	61012
	.byte	32
	.byte	'Ifx_SRC_GVADC',0,17,201,4,3
	.word	61081
	.byte	14
	.word	58394
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,17,204,4,25,4,13
	.byte	'XBAR',0
	.word	61109
	.byte	4,2,35,0,0,14
	.word	61114
	.byte	32
	.byte	'Ifx_SRC_GXBAR',0,17,207,4,3
	.word	61150
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,18,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_STM_ACCEN0_Bits',0,18,79,3
	.word	61178
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,18,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_ACCEN1_Bits',0,18,85,3
	.word	61735
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,18,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_CAP_Bits',0,18,91,3
	.word	61812
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,18,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_CAPSV_Bits',0,18,97,3
	.word	61884
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,18,100,16,4,11
	.byte	'DISR',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_STM_CLC_Bits',0,18,107,3
	.word	61960
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,18,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	680
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	680
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	680
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	680
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	3,0,2,35,3,0,32
	.byte	'Ifx_STM_CMCON_Bits',0,18,120,3
	.word	62101
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,18,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_CMP_Bits',0,18,126,3
	.word	62319
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,18,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	505
	.byte	25,0,2,35,0,0,32
	.byte	'Ifx_STM_ICR_Bits',0,18,139,1,3
	.word	62386
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,18,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_STM_ID_Bits',0,18,147,1,3
	.word	62589
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,18,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_STM_ISCR_Bits',0,18,157,1,3
	.word	62696
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,18,160,1,16,4,11
	.byte	'RST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,32
	.byte	'Ifx_STM_KRST0_Bits',0,18,165,1,3
	.word	62847
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,18,168,1,16,4,11
	.byte	'RST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_STM_KRST1_Bits',0,18,172,1,3
	.word	62958
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,18,175,1,16,4,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_STM_KRSTCLR_Bits',0,18,179,1,3
	.word	63050
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,18,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	2,0,2,35,3,0,32
	.byte	'Ifx_STM_OCS_Bits',0,18,189,1,3
	.word	63146
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,18,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM0_Bits',0,18,195,1,3
	.word	63292
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,18,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM0SV_Bits',0,18,201,1,3
	.word	63364
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,18,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM1_Bits',0,18,207,1,3
	.word	63440
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,18,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM2_Bits',0,18,213,1,3
	.word	63512
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,18,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM3_Bits',0,18,219,1,3
	.word	63584
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,18,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM4_Bits',0,18,225,1,3
	.word	63657
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,18,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM5_Bits',0,18,231,1,3
	.word	63730
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,18,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_STM_TIM6_Bits',0,18,237,1,3
	.word	63803
	.byte	12,18,245,1,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61178
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_ACCEN0',0,18,250,1,3
	.word	63876
	.byte	12,18,253,1,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61735
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_ACCEN1',0,18,130,2,3
	.word	63940
	.byte	12,18,133,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61812
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_CAP',0,18,138,2,3
	.word	64004
	.byte	12,18,141,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61884
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_CAPSV',0,18,146,2,3
	.word	64065
	.byte	12,18,149,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61960
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_CLC',0,18,154,2,3
	.word	64128
	.byte	12,18,157,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62101
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_CMCON',0,18,162,2,3
	.word	64189
	.byte	12,18,165,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62319
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_CMP',0,18,170,2,3
	.word	64252
	.byte	12,18,173,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62386
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_ICR',0,18,178,2,3
	.word	64313
	.byte	12,18,181,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62589
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_ID',0,18,186,2,3
	.word	64374
	.byte	12,18,189,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62696
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_ISCR',0,18,194,2,3
	.word	64434
	.byte	12,18,197,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62847
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_KRST0',0,18,202,2,3
	.word	64496
	.byte	12,18,205,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62958
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_KRST1',0,18,210,2,3
	.word	64559
	.byte	12,18,213,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63050
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_KRSTCLR',0,18,218,2,3
	.word	64622
	.byte	12,18,221,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63146
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_OCS',0,18,226,2,3
	.word	64687
	.byte	12,18,229,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63292
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM0',0,18,234,2,3
	.word	64748
	.byte	12,18,237,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63364
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM0SV',0,18,242,2,3
	.word	64810
	.byte	12,18,245,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63440
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM1',0,18,250,2,3
	.word	64874
	.byte	12,18,253,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63512
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM2',0,18,130,3,3
	.word	64936
	.byte	12,18,133,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63584
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM3',0,18,138,3,3
	.word	64998
	.byte	12,18,141,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63657
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM4',0,18,146,3,3
	.word	65060
	.byte	12,18,149,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63730
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM5',0,18,154,3,3
	.word	65122
	.byte	12,18,157,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63803
	.byte	4,2,35,0,0,32
	.byte	'Ifx_STM_TIM6',0,18,162,3,3
	.word	65184
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,32
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	65246
	.byte	33,5,160,1,9,6,13
	.byte	'counter',0
	.word	1668
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	680
	.byte	1,2,35,4,0,32
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	65335
	.byte	33,5,172,1,9,32,13
	.byte	'instruction',0
	.word	65335
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	65335
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	65335
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	65335
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	65335
	.byte	6,2,35,24,0,32
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	65401
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,19,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,19,79,3
	.word	65519
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,19,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,19,85,3
	.word	66080
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,19,88,16,4,11
	.byte	'SEL',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	505
	.byte	22,0,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,19,95,3
	.word	66161
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,19,98,16,4,11
	.byte	'VLD0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	505
	.byte	22,0,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,19,111,3
	.word	66314
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,19,114,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	680
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,19,121,3
	.word	66562
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,19,124,16,4,11
	.byte	'STATUS',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM0_Bits',0,19,128,1,3
	.word	66708
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,19,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_COMM1_Bits',0,19,136,1,3
	.word	66806
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,19,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_COMM2_Bits',0,19,144,1,3
	.word	66922
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,19,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	505
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ECCRD_Bits',0,19,153,1,3
	.word	67038
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,19,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	505
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ECCRP_Bits',0,19,162,1,3
	.word	67178
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,19,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	505
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_ECCW_Bits',0,19,171,1,3
	.word	67318
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,19,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	680
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	697
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	680
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_FCON_Bits',0,19,193,1,3
	.word	67457
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,19,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	8,0,2,35,3,0,32
	.byte	'Ifx_FLASH_FPRO_Bits',0,19,218,1,3
	.word	67819
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,19,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_FSR_Bits',0,19,254,1,3
	.word	68260
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,19,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_ID_Bits',0,19,134,2,3
	.word	68866
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,19,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	697
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_MARD_Bits',0,19,147,2,3
	.word	68977
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,19,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	697
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_MARP_Bits',0,19,159,2,3
	.word	69191
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,19,162,2,16,4,11
	.byte	'L',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	680
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	697
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCOND_Bits',0,19,179,2,3
	.word	69378
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,19,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,19,188,2,3
	.word	69702
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,19,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	697
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,19,199,2,3
	.word	69845
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,19,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	680
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	680
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	697
	.byte	14,0,2,35,2,0,32
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,19,219,2,3
	.word	70034
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,19,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	680
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,19,254,2,3
	.word	70397
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,19,129,3,16,4,11
	.byte	'S0L',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCONP_Bits',0,19,160,3,3
	.word	70992
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,19,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,19,194,3,3
	.word	71516
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,19,197,3,16,4,11
	.byte	'TAG',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,19,201,3,3
	.word	72098
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,19,204,3,16,4,11
	.byte	'TAG',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,19,208,3,3
	.word	72200
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,19,211,3,16,4,11
	.byte	'TAG',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,19,215,3,3
	.word	72302
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,19,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	505
	.byte	29,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RRAD_Bits',0,19,222,3,3
	.word	72404
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,19,225,3,16,4,11
	.byte	'STRT',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	680
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	697
	.byte	16,0,2,35,2,0,32
	.byte	'Ifx_FLASH_RRCT_Bits',0,19,236,3,3
	.word	72498
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,19,239,3,16,4,11
	.byte	'DATA',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD0_Bits',0,19,242,3,3
	.word	72708
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,19,245,3,16,4,11
	.byte	'DATA',0,4
	.word	505
	.byte	32,0,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD1_Bits',0,19,248,3,3
	.word	72781
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,19,251,3,16,4,11
	.byte	'SEL',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	505
	.byte	22,0,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,19,130,4,3
	.word	72854
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,19,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,19,137,4,3
	.word	73009
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,19,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	680
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,0,2,35,3,0,32
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,19,147,4,3
	.word	73114
	.byte	12,19,155,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65519
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ACCEN0',0,19,160,4,3
	.word	73262
	.byte	12,19,163,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66080
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ACCEN1',0,19,168,4,3
	.word	73328
	.byte	12,19,171,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66161
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_CFG',0,19,176,4,3
	.word	73394
	.byte	12,19,179,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66314
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_STAT',0,19,184,4,3
	.word	73462
	.byte	12,19,187,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66562
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_CBAB_TOP',0,19,192,4,3
	.word	73531
	.byte	12,19,195,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66708
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM0',0,19,200,4,3
	.word	73599
	.byte	12,19,203,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66806
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM1',0,19,208,4,3
	.word	73664
	.byte	12,19,211,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66922
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_COMM2',0,19,216,4,3
	.word	73729
	.byte	12,19,219,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67038
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ECCRD',0,19,224,4,3
	.word	73794
	.byte	12,19,227,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67178
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ECCRP',0,19,232,4,3
	.word	73859
	.byte	12,19,235,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67318
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ECCW',0,19,240,4,3
	.word	73924
	.byte	12,19,243,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67457
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_FCON',0,19,248,4,3
	.word	73988
	.byte	12,19,251,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67819
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_FPRO',0,19,128,5,3
	.word	74052
	.byte	12,19,131,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68260
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_FSR',0,19,136,5,3
	.word	74116
	.byte	12,19,139,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68866
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_ID',0,19,144,5,3
	.word	74179
	.byte	12,19,147,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68977
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_MARD',0,19,152,5,3
	.word	74241
	.byte	12,19,155,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69191
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_MARP',0,19,160,5,3
	.word	74305
	.byte	12,19,163,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69378
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCOND',0,19,168,5,3
	.word	74369
	.byte	12,19,171,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69702
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONDBG',0,19,176,5,3
	.word	74436
	.byte	12,19,179,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69845
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONHSM',0,19,184,5,3
	.word	74505
	.byte	12,19,187,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70034
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,19,192,5,3
	.word	74574
	.byte	12,19,195,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70397
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONOTP',0,19,200,5,3
	.word	74647
	.byte	12,19,203,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70992
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONP',0,19,208,5,3
	.word	74716
	.byte	12,19,211,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71516
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_PROCONWOP',0,19,216,5,3
	.word	74783
	.byte	12,19,219,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72098
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG0',0,19,224,5,3
	.word	74852
	.byte	12,19,227,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72200
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG1',0,19,232,5,3
	.word	74920
	.byte	12,19,235,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72302
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RDB_CFG2',0,19,240,5,3
	.word	74988
	.byte	12,19,243,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72404
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRAD',0,19,248,5,3
	.word	75056
	.byte	12,19,251,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72498
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRCT',0,19,128,6,3
	.word	75120
	.byte	12,19,131,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72708
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD0',0,19,136,6,3
	.word	75184
	.byte	12,19,139,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72781
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_RRD1',0,19,144,6,3
	.word	75248
	.byte	12,19,147,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72854
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_CFG',0,19,152,6,3
	.word	75312
	.byte	12,19,155,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73009
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_STAT',0,19,160,6,3
	.word	75380
	.byte	12,19,163,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73114
	.byte	4,2,35,0,0,32
	.byte	'Ifx_FLASH_UBAB_TOP',0,19,168,6,3
	.word	75449
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,19,179,6,25,12,13
	.byte	'CFG',0
	.word	73394
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	73462
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73531
	.byte	4,2,35,8,0,14
	.word	75517
	.byte	32
	.byte	'Ifx_FLASH_CBAB',0,19,184,6,3
	.word	75580
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,19,187,6,25,12,13
	.byte	'CFG0',0
	.word	74852
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	74920
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	74988
	.byte	4,2,35,8,0,14
	.word	75609
	.byte	32
	.byte	'Ifx_FLASH_RDB',0,19,192,6,3
	.word	75673
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,19,195,6,25,12,13
	.byte	'CFG',0
	.word	75312
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	75380
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	75449
	.byte	4,2,35,8,0,14
	.word	75701
	.byte	32
	.byte	'Ifx_FLASH_UBAB',0,19,200,6,3
	.word	75764
	.byte	32
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8693
	.byte	32
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8606
	.byte	32
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4949
	.byte	32
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	3002
	.byte	32
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3997
	.byte	32
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	3130
	.byte	32
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3777
	.byte	32
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3345
	.byte	32
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3560
	.byte	32
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7965
	.byte	32
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	8089
	.byte	32
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	8173
	.byte	32
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8353
	.byte	32
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6604
	.byte	32
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	7128
	.byte	32
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6778
	.byte	32
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6952
	.byte	32
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7617
	.byte	32
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2431
	.byte	32
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5941
	.byte	32
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6429
	.byte	32
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	6088
	.byte	32
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6257
	.byte	32
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7284
	.byte	32
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	2115
	.byte	32
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5655
	.byte	32
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5289
	.byte	32
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4320
	.byte	32
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4624
	.byte	32
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9220
	.byte	32
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8653
	.byte	32
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5240
	.byte	32
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	3081
	.byte	32
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4271
	.byte	32
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3305
	.byte	32
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3957
	.byte	32
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3520
	.byte	32
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3737
	.byte	32
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	8049
	.byte	32
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8298
	.byte	32
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8557
	.byte	32
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7925
	.byte	32
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6738
	.byte	32
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7244
	.byte	32
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6912
	.byte	32
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	7088
	.byte	32
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2962
	.byte	32
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7577
	.byte	32
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	6048
	.byte	32
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6564
	.byte	32
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6217
	.byte	32
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6389
	.byte	32
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2391
	.byte	32
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5901
	.byte	32
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5615
	.byte	32
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4584
	.byte	32
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4900
	.byte	14
	.word	9260
	.byte	32
	.byte	'Ifx_P',0,8,139,6,3
	.word	77111
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,32
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	77131
	.byte	15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,32
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	77282
	.byte	15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,32
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	77526
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,32
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	77624
	.byte	32
	.byte	'IfxPort_State',0,7,178,1,3
	.word	9873
	.byte	33,7,190,1,9,8,13
	.byte	'port',0
	.word	9868
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	680
	.byte	1,2,35,4,0,32
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	78089
	.byte	32
	.byte	'IfxScuCcu_CcuconRegConfig',0,11,216,5,3
	.word	22512
	.byte	32
	.byte	'IfxScuCcu_InitialStepConfig',0,11,227,5,3
	.word	22347
	.byte	32
	.byte	'IfxScuCcu_PllStepsConfig',0,11,236,5,3
	.word	22279
	.byte	32
	.byte	'IfxScuCcu_ClockDistributionConfig',0,11,252,5,3
	.word	22548
	.byte	32
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,11,132,6,3
	.word	22657
	.byte	32
	.byte	'IfxScuCcu_SysPllConfig',0,11,142,6,3
	.word	22427
	.byte	32
	.byte	'IfxScuCcu_Config',0,11,156,6,3
	.word	22693
	.byte	34
	.word	22693
	.byte	37
	.byte	'IfxScuCcu_defaultClockConfig',0,11,178,8,39
	.word	78396
	.byte	1,1,38
	.word	505
	.byte	39,0,37
	.byte	'__ISTACK0',0,13,87,1
	.word	78441
	.byte	1,1,37
	.byte	'__INTTAB_CPU0',0,13,87,1
	.word	78441
	.byte	1,1,37
	.byte	'__TRAPTAB_CPU0',0,13,87,1
	.word	78441
	.byte	1,1,37
	.byte	'__CSA0',0,13,87,1
	.word	78441
	.byte	1,1,37
	.byte	'__CSA0_END',0,13,87,1
	.word	78441
	.byte	1,1,18,32
	.word	1668
	.byte	19,7,0
.L155:
	.byte	34
	.word	78555
.L156:
	.byte	34
	.word	78555
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L44:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,3,8,54,15,39,12,63,12,60,12,0,0,21,5,0,73
	.byte	19,0,0,22,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,23,46,0,3,8,54,15,39,12,63,12,60,12,0,0,24,46,0,3
	.byte	8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,46,1,49,19,0,0,26,5,0,49,19,0,0,27,46,1,3,8,58,15,59
	.byte	15,57,15,54,15,39,12,63,12,60,12,0,0,28,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,29
	.byte	46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,30,29,1,49,19,0,0,31,11,0,49,19,0,0,32,22
	.byte	0,3,8,58,15,59,15,57,15,73,19,0,0,33,19,1,58,15,59,15,57,15,11,15,0,0,34,38,0,73,19,0,0,35,46,0,3,8,58
	.byte	15,59,15,57,15,54,15,63,12,60,12,0,0,36,21,0,54,15,0,0,37,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12
	.byte	0,0,38,1,1,73,19,0,0,39,33,0,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L45:
	.word	.L190-.L189
.L189:
	.half	3
	.word	.L192-.L191
.L191:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\Tricore\\Compilers\\Compilers.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\CStart\\IfxCpu_CStart.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0,0
.L192:
.L190:
	.sdecl	'.debug_info',debug,cluster('_Core0_start')
	.sect	'.debug_info'
.L46:
	.word	1411
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L49,.L48
	.byte	2
	.word	.L42
	.byte	3
	.byte	'_Core0_start',0,1,107,6,1,1,1
	.word	.L39,.L60,.L38
	.byte	4
	.word	.L39,.L60
	.byte	5
	.byte	'pcxi',0,1,109,12
	.word	.L61,.L62
	.byte	5
	.byte	'cpuWdtPassword',0,1,110,12
	.word	.L63,.L64
	.byte	6
	.word	.L65,.L66,.L2
	.byte	7
	.word	.L67,.L68
	.byte	8
	.word	.L69,.L66,.L2
	.byte	5
	.byte	'password',0,2,229,3,12
	.word	.L63,.L70
	.byte	0,0,6
	.word	.L71,.L72,.L73
	.byte	7
	.word	.L74,.L75
	.byte	8
	.word	.L76,.L72,.L73
	.byte	8
	.word	.L77,.L78,.L3
	.byte	5
	.byte	'pcon1',0,3,208,7,23
	.word	.L79,.L80
	.byte	0,8
	.word	.L81,.L3,.L73
	.byte	5
	.byte	'coreIndex',0,3,214,7,12
	.word	.L61,.L82
	.byte	5
	.byte	'wdtPassword',0,3,215,7,12
	.word	.L63,.L83
	.byte	6
	.word	.L84,.L3,.L4
	.byte	8
	.word	.L85,.L3,.L4
	.byte	5
	.byte	'reg',0,3,143,6,21
	.word	.L86,.L87
	.byte	0,0,6
	.word	.L65,.L88,.L5
	.byte	7
	.word	.L67,.L68
	.byte	8
	.word	.L69,.L88,.L5
	.byte	5
	.byte	'password',0,2,229,3,12
	.word	.L63,.L89
	.byte	0,0,6
	.word	.L90,.L91,.L92
	.byte	7
	.word	.L93,.L94
	.byte	7
	.word	.L95,.L96
	.byte	9
	.word	.L97,.L91,.L92
	.byte	0,8
	.word	.L98,.L92,.L99
	.byte	5
	.byte	'pcon0',0,3,219,7,23
	.word	.L100,.L101
	.byte	6
	.word	.L102,.L103,.L99
	.byte	7
	.word	.L104,.L105
	.byte	7
	.word	.L106,.L107
	.byte	9
	.word	.L108,.L103,.L99
	.byte	0,0,0,0,0,6
	.word	.L109,.L110,.L111
	.byte	7
	.word	.L112,.L113
	.byte	8
	.word	.L114,.L110,.L111
	.byte	5
	.byte	'coreIndex',0,3,179,7,12
	.word	.L61,.L115
	.byte	5
	.byte	'wdtPassword',0,3,180,7,12
	.word	.L63,.L116
	.byte	6
	.word	.L84,.L110,.L14
	.byte	8
	.word	.L85,.L110,.L14
	.byte	5
	.byte	'reg',0,3,143,6,21
	.word	.L86,.L117
	.byte	0,0,6
	.word	.L65,.L118,.L15
	.byte	7
	.word	.L67,.L68
	.byte	8
	.word	.L69,.L118,.L15
	.byte	5
	.byte	'password',0,2,229,3,12
	.word	.L63,.L119
	.byte	0,0,6
	.word	.L90,.L120,.L121
	.byte	7
	.word	.L93,.L94
	.byte	7
	.word	.L95,.L96
	.byte	9
	.word	.L97,.L120,.L121
	.byte	0,8
	.word	.L122,.L121,.L123
	.byte	5
	.byte	'dcon0',0,3,184,7,23
	.word	.L124,.L125
	.byte	6
	.word	.L102,.L126,.L123
	.byte	7
	.word	.L104,.L105
	.byte	7
	.word	.L106,.L107
	.byte	9
	.word	.L108,.L126,.L123
	.byte	0,0,0,0,6
	.word	.L90,.L127,.L128
	.byte	7
	.word	.L93,.L94
	.byte	7
	.word	.L95,.L96
	.byte	9
	.word	.L97,.L127,.L128
	.byte	0,6
	.word	.L102,.L129,.L130
	.byte	7
	.word	.L104,.L105
	.byte	7
	.word	.L106,.L107
	.byte	9
	.word	.L108,.L129,.L130
	.byte	0,6
	.word	.L131,.L132,.L133
	.byte	7
	.word	.L134,.L135
	.byte	7
	.word	.L136,.L137
	.byte	8
	.word	.L138,.L132,.L133
	.byte	5
	.byte	'k',0,3,221,6,13
	.word	.L61,.L139
	.byte	5
	.byte	'nxt_cxi_val',0,3,222,6,13
	.word	.L61,.L140
	.byte	5
	.byte	'prvCsa',0,3,223,6,13
	.word	.L141,.L142
	.byte	5
	.byte	'nxtCsa',0,3,224,6,13
	.word	.L141,.L143
	.byte	5
	.byte	'numOfCsa',0,3,225,6,13
	.word	.L61,.L144
	.byte	0,0,4
	.word	.L133,.L145
	.byte	5
	.byte	'safetyWdtPassword',0,1,160,1,16
	.word	.L63,.L146
	.byte	0,4
	.word	.L145,.L60
	.byte	5
	.byte	'password',0,1,169,1,12
	.word	.L63,.L147
	.byte	6
	.word	.L148,.L149,.L150
	.byte	7
	.word	.L151,.L152
	.byte	9
	.word	.L153,.L149,.L150
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('_Core0_start')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,29,1
	.byte	49,16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('_Core0_start')
	.sect	'.debug_line'
.L48:
	.word	.L194-.L193
.L193:
	.half	3
	.word	.L196-.L195
.L195:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L196:
	.byte	5,86,7,0,5,2
	.word	.L39
	.byte	3,237,0,1,4,2,5,33,9
	.half	.L66-.L39
	.byte	3,252,2,1,5,14,9
	.half	.L157-.L66
	.byte	3,1,1,5,5,9
	.half	.L158-.L157
	.byte	3,2,1,4,1,9
	.half	.L2-.L158
	.byte	3,136,125,1,5,12,9
	.half	.L197-.L2
	.byte	3,1,1,5,21,9
	.half	.L198-.L197
	.byte	3,3,1,5,11,9
	.half	.L199-.L198
	.byte	1,5,19,9
	.half	.L200-.L199
	.byte	3,3,1,5,10,9
	.half	.L159-.L200
	.byte	3,1,1,5,11,9
	.half	.L201-.L159
	.byte	3,1,1,5,28,9
	.half	.L202-.L201
	.byte	3,3,1,4,3,5,5,9
	.half	.L72-.L202
	.byte	3,205,6,1,5,23,7,9
	.half	.L78-.L72
	.byte	3,3,1,9
	.half	.L160-.L78
	.byte	3,1,1,5,15,9
	.half	.L203-.L160
	.byte	3,1,1,5,19,9
	.half	.L3-.L203
	.byte	3,189,126,1,5,37,9
	.half	.L161-.L3
	.byte	3,1,1,5,5,9
	.half	.L204-.L161
	.byte	1,5,76,9
	.half	.L4-.L204
	.byte	3,198,1,1,5,83,9
	.half	.L205-.L4
	.byte	1,4,2,5,33,9
	.half	.L88-.L205
	.byte	3,147,124,1,5,14,9
	.half	.L163-.L88
	.byte	3,1,1,5,5,9
	.half	.L164-.L163
	.byte	3,2,1,4,3,5,52,9
	.half	.L5-.L164
	.byte	3,237,3,1,5,59,9
	.half	.L206-.L5
	.byte	1,4,2,5,25,9
	.half	.L91-.L206
	.byte	3,221,123,1,5,5,9
	.half	.L207-.L91
	.byte	1,5,38,7,9
	.half	.L208-.L207
	.byte	3,5,1,5,45,9
	.half	.L209-.L208
	.byte	3,1,1,5,50,9
	.half	.L210-.L209
	.byte	1,5,69,9
	.half	.L211-.L210
	.byte	3,127,1,5,26,9
	.half	.L212-.L211
	.byte	3,126,1,5,34,9
	.half	.L6-.L212
	.byte	3,9,1,5,41,9
	.half	.L213-.L6
	.byte	3,1,1,5,46,9
	.half	.L214-.L213
	.byte	1,5,65,9
	.half	.L215-.L214
	.byte	3,127,1,5,22,9
	.half	.L216-.L215
	.byte	3,126,1,5,41,9
	.half	.L217-.L216
	.byte	3,6,1,5,28,9
	.half	.L7-.L217
	.byte	1,5,41,9
	.half	.L218-.L7
	.byte	1,4,3,5,23,7,9
	.half	.L92-.L218
	.byte	3,149,4,1,5,25,9
	.half	.L166-.L92
	.byte	3,1,1,5,32,7,9
	.half	.L219-.L166
	.byte	1,5,36,9
	.half	.L220-.L219
	.byte	1,5,32,9
	.half	.L9-.L220
	.byte	1,5,23,9
	.half	.L10-.L9
	.byte	1,5,15,9
	.half	.L221-.L10
	.byte	3,1,1,5,50,9
	.half	.L222-.L221
	.byte	3,1,1,5,57,9
	.half	.L223-.L222
	.byte	1,4,2,5,25,9
	.half	.L103-.L223
	.byte	3,175,124,1,5,5,9
	.half	.L224-.L103
	.byte	1,5,38,7,9
	.half	.L225-.L224
	.byte	3,5,1,5,45,9
	.half	.L226-.L225
	.byte	3,1,1,5,50,9
	.half	.L227-.L226
	.byte	1,5,69,9
	.half	.L228-.L227
	.byte	3,127,1,5,26,9
	.half	.L229-.L228
	.byte	3,126,1,5,34,9
	.half	.L11-.L229
	.byte	3,9,1,5,41,9
	.half	.L230-.L11
	.byte	3,1,1,5,46,9
	.half	.L231-.L230
	.byte	1,5,65,9
	.half	.L232-.L231
	.byte	3,127,1,5,22,9
	.half	.L233-.L232
	.byte	3,126,1,5,41,9
	.half	.L234-.L233
	.byte	3,6,1,5,28,9
	.half	.L12-.L234
	.byte	1,5,41,9
	.half	.L235-.L12
	.byte	1,4,3,5,12,7,9
	.half	.L99-.L235
	.byte	3,196,3,1,4,1,5,25,9
	.half	.L73-.L99
	.byte	3,162,121,1,4,3,5,19,9
	.half	.L110-.L73
	.byte	3,140,5,1,5,37,9
	.half	.L168-.L110
	.byte	3,1,1,5,5,9
	.half	.L236-.L168
	.byte	1,5,76,9
	.half	.L14-.L236
	.byte	3,163,1,1,5,83,9
	.half	.L237-.L14
	.byte	1,4,2,5,33,9
	.half	.L118-.L237
	.byte	3,182,124,1,5,14,9
	.half	.L170-.L118
	.byte	3,1,1,5,5,9
	.half	.L171-.L170
	.byte	3,2,1,4,3,5,52,9
	.half	.L15-.L171
	.byte	3,202,3,1,5,59,9
	.half	.L238-.L15
	.byte	1,4,2,5,25,9
	.half	.L120-.L238
	.byte	3,128,124,1,5,5,9
	.half	.L239-.L120
	.byte	1,5,38,7,9
	.half	.L240-.L239
	.byte	3,5,1,5,45,9
	.half	.L241-.L240
	.byte	3,1,1,5,50,9
	.half	.L242-.L241
	.byte	1,5,69,9
	.half	.L243-.L242
	.byte	3,127,1,5,26,9
	.half	.L244-.L243
	.byte	3,126,1,5,34,9
	.half	.L16-.L244
	.byte	3,9,1,5,41,9
	.half	.L245-.L16
	.byte	3,1,1,5,46,9
	.half	.L246-.L245
	.byte	1,5,65,9
	.half	.L247-.L246
	.byte	3,127,1,5,22,9
	.half	.L248-.L247
	.byte	3,126,1,5,41,9
	.half	.L249-.L248
	.byte	3,6,1,5,28,9
	.half	.L17-.L249
	.byte	1,5,41,9
	.half	.L250-.L17
	.byte	1,4,3,5,23,7,9
	.half	.L121-.L250
	.byte	3,242,3,1,5,25,9
	.half	.L173-.L121
	.byte	3,1,1,5,32,7,9
	.half	.L251-.L173
	.byte	1,5,36,9
	.half	.L252-.L251
	.byte	1,5,32,9
	.half	.L19-.L252
	.byte	1,5,23,9
	.half	.L20-.L19
	.byte	1,5,15,9
	.half	.L253-.L20
	.byte	3,1,1,5,50,9
	.half	.L254-.L253
	.byte	3,1,1,5,57,9
	.half	.L255-.L254
	.byte	1,4,2,5,25,9
	.half	.L126-.L255
	.byte	3,210,124,1,5,5,9
	.half	.L256-.L126
	.byte	1,5,38,7,9
	.half	.L257-.L256
	.byte	3,5,1,5,45,9
	.half	.L258-.L257
	.byte	3,1,1,5,50,9
	.half	.L259-.L258
	.byte	1,5,69,9
	.half	.L260-.L259
	.byte	3,127,1,5,26,9
	.half	.L261-.L260
	.byte	3,126,1,5,34,9
	.half	.L21-.L261
	.byte	3,9,1,5,41,9
	.half	.L262-.L21
	.byte	3,1,1,5,46,9
	.half	.L263-.L262
	.byte	1,5,65,9
	.half	.L264-.L263
	.byte	3,127,1,5,22,9
	.half	.L265-.L264
	.byte	3,126,1,5,41,9
	.half	.L266-.L265
	.byte	3,6,1,5,28,9
	.half	.L22-.L266
	.byte	1,5,41,9
	.half	.L267-.L22
	.byte	1,4,3,5,12,7,9
	.half	.L123-.L267
	.byte	3,161,3,1,4,1,5,55,9
	.half	.L111-.L123
	.byte	3,200,121,1,4,2,5,25,9
	.half	.L127-.L111
	.byte	3,176,2,1,5,5,9
	.half	.L268-.L127
	.byte	1,5,38,7,9
	.half	.L269-.L268
	.byte	3,5,1,5,45,9
	.half	.L270-.L269
	.byte	3,1,1,5,50,9
	.half	.L271-.L270
	.byte	1,5,69,9
	.half	.L272-.L271
	.byte	3,127,1,5,26,9
	.half	.L273-.L272
	.byte	3,126,1,5,34,9
	.half	.L24-.L273
	.byte	3,9,1,5,41,9
	.half	.L274-.L24
	.byte	3,1,1,5,46,9
	.half	.L275-.L274
	.byte	1,5,65,9
	.half	.L276-.L275
	.byte	3,127,1,5,22,9
	.half	.L277-.L276
	.byte	3,126,1,5,41,9
	.half	.L278-.L277
	.byte	3,6,1,5,28,9
	.half	.L25-.L278
	.byte	1,5,41,9
	.half	.L279-.L25
	.byte	1,4,1,5,29,7,9
	.half	.L128-.L279
	.byte	3,195,125,1,5,11,9
	.half	.L280-.L128
	.byte	1,5,29,9
	.half	.L281-.L280
	.byte	3,3,1,5,11,9
	.half	.L282-.L281
	.byte	1,5,29,9
	.half	.L283-.L282
	.byte	3,3,1,5,11,9
	.half	.L284-.L283
	.byte	1,5,53,9
	.half	.L285-.L284
	.byte	3,2,1,4,2,5,25,9
	.half	.L129-.L285
	.byte	3,252,2,1,5,5,9
	.half	.L286-.L129
	.byte	1,5,38,7,9
	.half	.L287-.L286
	.byte	3,5,1,5,45,9
	.half	.L288-.L287
	.byte	3,1,1,5,50,9
	.half	.L289-.L288
	.byte	1,5,69,9
	.half	.L290-.L289
	.byte	3,127,1,5,26,9
	.half	.L291-.L290
	.byte	3,126,1,5,34,9
	.half	.L27-.L291
	.byte	3,9,1,5,41,9
	.half	.L292-.L27
	.byte	3,1,1,5,46,9
	.half	.L293-.L292
	.byte	1,5,65,9
	.half	.L294-.L293
	.byte	3,127,1,5,22,9
	.half	.L295-.L294
	.byte	3,126,1,5,41,9
	.half	.L296-.L295
	.byte	3,6,1,5,28,9
	.half	.L28-.L296
	.byte	1,5,41,9
	.half	.L297-.L28
	.byte	1,4,1,5,5,7,9
	.half	.L130-.L297
	.byte	3,247,124,1,9
	.half	.L298-.L130
	.byte	3,1,1,9
	.half	.L299-.L298
	.byte	3,3,1,9
	.half	.L300-.L299
	.byte	3,1,1,5,30,9
	.half	.L301-.L300
	.byte	3,3,1,5,50,9
	.half	.L302-.L301
	.byte	1,4,3,5,25,9
	.half	.L132-.L302
	.byte	3,194,5,1,5,37,9
	.half	.L177-.L132
	.byte	3,2,1,5,54,9
	.half	.L303-.L177
	.byte	1,5,44,9
	.half	.L304-.L303
	.byte	1,5,66,9
	.half	.L305-.L304
	.byte	1,5,64,9
	.half	.L306-.L305
	.byte	1,5,12,9
	.half	.L179-.L306
	.byte	3,2,1,5,29,9
	.half	.L180-.L179
	.byte	1,5,32,9
	.half	.L31-.L180
	.byte	3,2,1,5,39,9
	.half	.L307-.L31
	.byte	1,5,55,9
	.half	.L308-.L307
	.byte	1,5,72,9
	.half	.L309-.L308
	.byte	1,5,90,9
	.half	.L310-.L309
	.byte	1,5,79,9
	.half	.L311-.L310
	.byte	1,5,97,9
	.half	.L312-.L311
	.byte	1,5,61,9
	.half	.L181-.L312
	.byte	1,5,9,9
	.half	.L313-.L181
	.byte	3,2,1,5,19,7,9
	.half	.L314-.L313
	.byte	3,2,1,5,41,9
	.half	.L315-.L314
	.byte	1,5,21,9
	.half	.L32-.L315
	.byte	3,4,1,5,28,9
	.half	.L33-.L32
	.byte	3,3,1,5,9,9
	.half	.L316-.L33
	.byte	1,5,19,7,9
	.half	.L317-.L316
	.byte	3,2,1,5,17,9
	.half	.L34-.L317
	.byte	3,3,1,5,16,9
	.half	.L318-.L34
	.byte	3,1,1,5,32,9
	.half	.L319-.L318
	.byte	3,109,1,5,29,9
	.half	.L30-.L319
	.byte	1,5,15,7,9
	.half	.L320-.L30
	.byte	3,22,1,5,13,9
	.half	.L321-.L320
	.byte	1,4,1,5,71,9
	.half	.L133-.L321
	.byte	3,167,122,1,5,34,9
	.half	.L178-.L133
	.byte	1,5,38,9
	.half	.L183-.L178
	.byte	3,1,1,5,41,9
	.half	.L182-.L183
	.byte	3,1,1,5,19,9
	.half	.L187-.L182
	.byte	3,2,1,5,51,9
	.half	.L145-.L187
	.byte	3,6,1,4,2,5,24,9
	.half	.L149-.L145
	.byte	3,164,2,1,5,5,9
	.half	.L184-.L149
	.byte	1,5,37,7,9
	.half	.L322-.L184
	.byte	3,5,1,5,43,9
	.half	.L323-.L322
	.byte	3,1,1,5,48,9
	.half	.L324-.L323
	.byte	1,5,66,9
	.half	.L325-.L324
	.byte	3,127,1,5,25,9
	.half	.L326-.L325
	.byte	3,126,1,5,33,9
	.half	.L35-.L326
	.byte	3,9,1,5,39,9
	.half	.L327-.L35
	.byte	3,1,1,5,44,9
	.half	.L328-.L327
	.byte	1,5,62,9
	.half	.L329-.L328
	.byte	3,127,1,5,21,9
	.half	.L330-.L329
	.byte	3,126,1,5,40,9
	.half	.L331-.L330
	.byte	3,6,1,5,27,9
	.half	.L36-.L331
	.byte	1,5,40,9
	.half	.L332-.L36
	.byte	1,4,1,5,27,7,9
	.half	.L150-.L332
	.byte	3,206,125,1,5,25,9
	.half	.L333-.L150
	.byte	1,5,5,9
	.half	.L334-.L333
	.byte	3,3,1,5,29,9
	.half	.L335-.L334
	.byte	3,4,1,5,51,9
	.half	.L336-.L335
	.byte	1,5,42,9
	.half	.L337-.L336
	.byte	1,5,5,9
	.half	.L338-.L337
	.byte	3,10,1,5,1,9
	.half	.L339-.L338
	.byte	3,1,1,7,9
	.half	.L50-.L339
	.byte	0,1,1
.L194:
	.sdecl	'.debug_ranges',debug,cluster('_Core0_start')
	.sect	'.debug_ranges'
.L49:
	.word	-1,.L39,0,.L50-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('_START')
	.sect	'.debug_info'
.L51:
	.word	284
	.half	3
	.word	.L52
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L54,.L53
	.byte	2
	.word	.L42
	.byte	3
	.byte	'_START',0,1,210,1,6,1,1,1
	.word	.L41,.L154,.L40
	.byte	4
	.word	.L41,.L154
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('_START')
	.sect	'.debug_abbrev'
.L52:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('_START')
	.sect	'.debug_line'
.L53:
	.word	.L341-.L340
.L340:
	.half	3
	.word	.L343-.L342
.L342:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0,0,0,0,0
.L343:
	.byte	5,5,7,0,5,2
	.word	.L41
	.byte	3,211,1,1,5,1,9
	.half	.L344-.L41
	.byte	3,1,1,7,9
	.half	.L55-.L344
	.byte	0,1,1
.L341:
	.sdecl	'.debug_ranges',debug,cluster('_START')
	.sect	'.debug_ranges'
.L54:
	.word	-1,.L41,0,.L55-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('BootModeHeader_0')
	.sect	'.debug_info'
.L56:
	.word	272
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L42
	.byte	3
	.byte	'BootModeHeader_0',0,13,248,1,14
	.word	.L155
	.byte	1,5,3
	.word	BootModeHeader_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('BootModeHeader_0')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('BootModeHeader_1')
	.sect	'.debug_info'
.L58:
	.word	272
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart0.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L42
	.byte	3
	.byte	'BootModeHeader_1',0,13,160,2,14
	.word	.L156
	.byte	1,5,3
	.word	BootModeHeader_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('BootModeHeader_1')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('_Core0_start')
	.sect	'.debug_loc'
.L38:
	.word	-1,.L39,0,.L60-.L39
	.half	2
	.byte	138,0
	.word	0,0
.L115:
	.word	0,0
.L82:
	.word	0,0
.L64:
	.word	0,0
.L135:
	.word	0,0
.L137:
	.word	0,0
.L125:
	.word	-1,.L39,.L173-.L39,.L174-.L39
	.half	1
	.byte	95
	.word	0,0
.L113:
	.word	0,0
.L75:
	.word	0,0
.L139:
	.word	-1,.L39,.L180-.L39,.L178-.L39
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L144:
	.word	-1,.L39,.L179-.L39,.L178-.L39
	.half	1
	.byte	84
	.word	0,0
.L143:
	.word	-1,.L39,.L175-.L39,.L176-.L39
	.half	1
	.byte	111
	.word	0,0
.L140:
	.word	-1,.L39,.L181-.L39,.L30-.L39
	.half	1
	.byte	81
	.word	0,0
.L147:
	.word	-1,.L39,.L149-.L39,.L188-.L39
	.half	1
	.byte	82
	.word	0,0
.L96:
	.word	0,0
.L152:
	.word	0,0
.L89:
	.word	-1,.L39,.L163-.L39,.L164-.L39
	.half	5
	.byte	144,32,157,32,0
	.word	.L164-.L39,.L165-.L39
	.half	1
	.byte	83
	.word	0,0
.L119:
	.word	-1,.L39,.L170-.L39,.L171-.L39
	.half	5
	.byte	144,32,157,32,0
	.word	.L171-.L39,.L172-.L39
	.half	1
	.byte	83
	.word	0,0
.L70:
	.word	-1,.L39,.L157-.L39,.L158-.L39
	.half	1
	.byte	95
	.word	.L158-.L39,.L60-.L39
	.half	1
	.byte	88
	.word	.L185-.L39,.L182-.L39
	.half	1
	.byte	84
	.word	0,0
.L107:
	.word	0,0
.L101:
	.word	-1,.L39,.L166-.L39,.L167-.L39
	.half	1
	.byte	95
	.word	0,0
.L80:
	.word	-1,.L39,.L160-.L39,.L3-.L39
	.half	1
	.byte	95
	.word	0,0
.L62:
	.word	-1,.L39,.L159-.L39,.L160-.L39
	.half	1
	.byte	95
	.word	0,0
.L142:
	.word	-1,.L39,.L177-.L39,.L178-.L39
	.half	1
	.byte	100
	.word	0,0
.L87:
	.word	-1,.L39,.L161-.L39,.L162-.L39
	.half	1
	.byte	95
	.word	0,0
.L117:
	.word	-1,.L39,.L168-.L39,.L169-.L39
	.half	1
	.byte	95
	.word	0,0
.L146:
	.word	-1,.L39,.L178-.L39,.L182-.L39
	.half	1
	.byte	82
	.word	.L183-.L39,.L184-.L39
	.half	1
	.byte	95
	.word	.L186-.L39,.L187-.L39
	.half	1
	.byte	84
	.word	0,0
.L94:
	.word	0,0
.L68:
	.word	0,0
.L105:
	.word	0,0
.L116:
	.word	0,0
.L83:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('_START')
	.sect	'.debug_loc'
.L40:
	.word	-1,.L41,0,.L154-.L41
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L345:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('_Core0_start')
	.sect	'.debug_frame'
	.word	12
	.word	.L345,.L39,.L60-.L39
	.sdecl	'.debug_frame',debug,cluster('_START')
	.sect	'.debug_frame'
	.word	24
	.word	.L345,.L41,.L154-.L41
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	; Module end
