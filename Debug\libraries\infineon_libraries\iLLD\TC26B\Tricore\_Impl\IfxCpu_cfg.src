	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc38296a --dep-file=IfxCpu_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxCpu_cfg.IfxCpu_cfg_indexMap',data,rom,cluster('IfxCpu_cfg_indexMap')
	.sect	'.rodata.IfxCpu_cfg.IfxCpu_cfg_indexMap'
	.global	IfxCpu_cfg_indexMap
	.align	4
IfxCpu_cfg_indexMap:	.type	object
	.size	IfxCpu_cfg_indexMap,16
	.word	-125763584
	.space	4
	.word	-125632512,1
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	14974
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	232
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	235
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	280
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	292
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	372
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	346
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	378
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	378
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	346
	.byte	6,0,10
	.word	240
	.byte	11
	.word	266
	.byte	6,0,10
	.word	301
	.byte	11
	.word	333
	.byte	6,0,10
	.word	383
	.byte	11
	.word	402
	.byte	6,0,10
	.word	418
	.byte	11
	.word	433
	.byte	11
	.word	447
	.byte	6,0,7
	.byte	'short int',0,2,5,12
	.byte	'__wchar_t',0,3,1,1
	.word	517
	.byte	7
	.byte	'unsigned int',0,4,7,12
	.byte	'__size_t',0,3,1,1
	.word	548
	.byte	7
	.byte	'int',0,4,5,12
	.byte	'__ptrdiff_t',0,3,1,1
	.word	581
	.byte	13,1,3
	.word	608
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	610
	.byte	7
	.byte	'unsigned char',0,1,8,12
	.byte	'uint8',0,4,105,29
	.word	633
	.byte	7
	.byte	'unsigned short int',0,2,7,12
	.byte	'uint16',0,4,109,29
	.word	664
	.byte	7
	.byte	'unsigned long int',0,4,7,12
	.byte	'uint32',0,4,113,29
	.word	701
	.byte	12
	.byte	'uint64',0,4,118,29
	.word	346
	.byte	12
	.byte	'sint16',0,4,126,29
	.word	517
	.byte	7
	.byte	'long int',0,4,5,12
	.byte	'sint32',0,4,131,1,29
	.word	767
	.byte	7
	.byte	'long long int',0,8,5,12
	.byte	'sint64',0,4,138,1,29
	.word	795
	.byte	12
	.byte	'float32',0,4,167,1,29
	.word	292
	.byte	12
	.byte	'pvoid',0,5,57,28
	.word	378
	.byte	12
	.byte	'Ifx_TickTime',0,5,79,28
	.word	795
	.byte	14
	.word	372
	.byte	3
	.word	880
	.byte	15,5,143,1,9,8,16
	.byte	'module',0
	.word	885
	.byte	4,2,35,0,16
	.byte	'index',0
	.word	767
	.byte	4,2,35,4,0,12
	.byte	'IfxModule_IndexMap',0,5,147,1,3
	.word	890
	.byte	7
	.byte	'unsigned int',0,4,7,17
	.byte	'_Ifx_CPU_A_Bits',0,6,45,16,4,18
	.byte	'ADDR',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_A_Bits',0,6,48,3
	.word	972
	.byte	17
	.byte	'_Ifx_CPU_BIV_Bits',0,6,51,16,4,18
	.byte	'VSS',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'BIV',0,4
	.word	956
	.byte	31,0,2,35,0,0,12
	.byte	'Ifx_CPU_BIV_Bits',0,6,55,3
	.word	1033
	.byte	17
	.byte	'_Ifx_CPU_BTV_Bits',0,6,58,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'BTV',0,4
	.word	956
	.byte	31,0,2,35,0,0,12
	.byte	'Ifx_CPU_BTV_Bits',0,6,62,3
	.word	1112
	.byte	17
	.byte	'_Ifx_CPU_CCNT_Bits',0,6,65,16,4,18
	.byte	'CountValue',0,4
	.word	956
	.byte	31,1,2,35,0,18
	.byte	'SOvf',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_CCNT_Bits',0,6,69,3
	.word	1198
	.byte	17
	.byte	'_Ifx_CPU_CCTRL_Bits',0,6,72,16,4,18
	.byte	'CM',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'CE',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'M1',0,4
	.word	956
	.byte	3,27,2,35,0,18
	.byte	'M2',0,4
	.word	956
	.byte	3,24,2,35,0,18
	.byte	'M3',0,4
	.word	956
	.byte	3,21,2,35,0,18
	.byte	'reserved_11',0,4
	.word	956
	.byte	21,0,2,35,0,0,12
	.byte	'Ifx_CPU_CCTRL_Bits',0,6,80,3
	.word	1287
	.byte	17
	.byte	'_Ifx_CPU_COMPAT_Bits',0,6,83,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'RM',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'SP',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'reserved_5',0,4
	.word	956
	.byte	27,0,2,35,0,0,12
	.byte	'Ifx_CPU_COMPAT_Bits',0,6,89,3
	.word	1433
	.byte	17
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,6,92,16,4,18
	.byte	'CORE_ID',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'reserved_3',0,4
	.word	956
	.byte	29,0,2,35,0,0,12
	.byte	'Ifx_CPU_CORE_ID_Bits',0,6,96,3
	.word	1560
	.byte	17
	.byte	'_Ifx_CPU_CPR_L_Bits',0,6,99,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'LOWBND',0,4
	.word	956
	.byte	29,0,2,35,0,0,12
	.byte	'Ifx_CPU_CPR_L_Bits',0,6,103,3
	.word	1658
	.byte	17
	.byte	'_Ifx_CPU_CPR_U_Bits',0,6,106,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'UPPBND',0,4
	.word	956
	.byte	29,0,2,35,0,0,12
	.byte	'Ifx_CPU_CPR_U_Bits',0,6,110,3
	.word	1751
	.byte	17
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,6,113,16,4,18
	.byte	'MODREV',0,4
	.word	956
	.byte	8,24,2,35,0,18
	.byte	'MOD_32B',0,4
	.word	956
	.byte	8,16,2,35,0,18
	.byte	'MOD',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_CPU_ID_Bits',0,6,118,3
	.word	1844
	.byte	17
	.byte	'_Ifx_CPU_CPXE_Bits',0,6,121,16,4,18
	.byte	'XE',0,4
	.word	956
	.byte	8,24,2,35,0,18
	.byte	'reserved_8',0,4
	.word	956
	.byte	24,0,2,35,0,0,12
	.byte	'Ifx_CPU_CPXE_Bits',0,6,125,3
	.word	1951
	.byte	17
	.byte	'_Ifx_CPU_CREVT_Bits',0,6,128,1,16,4,18
	.byte	'EVTA',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'BBM',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'BOD',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'SUSP',0,4
	.word	956
	.byte	1,26,2,35,0,18
	.byte	'CNT',0,4
	.word	956
	.byte	2,24,2,35,0,18
	.byte	'reserved_8',0,4
	.word	956
	.byte	24,0,2,35,0,0,12
	.byte	'Ifx_CPU_CREVT_Bits',0,6,136,1,3
	.word	2038
	.byte	17
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,6,139,1,16,4,18
	.byte	'CID',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'reserved_3',0,4
	.word	956
	.byte	29,0,2,35,0,0,12
	.byte	'Ifx_CPU_CUS_ID_Bits',0,6,143,1,3
	.word	2192
	.byte	17
	.byte	'_Ifx_CPU_D_Bits',0,6,146,1,16,4,18
	.byte	'DATA',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_D_Bits',0,6,149,1,3
	.word	2286
	.byte	17
	.byte	'_Ifx_CPU_DATR_Bits',0,6,152,1,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'SBE',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'reserved_4',0,4
	.word	956
	.byte	5,23,2,35,0,18
	.byte	'CWE',0,4
	.word	956
	.byte	1,22,2,35,0,18
	.byte	'CFE',0,4
	.word	956
	.byte	1,21,2,35,0,18
	.byte	'reserved_11',0,4
	.word	956
	.byte	3,18,2,35,0,18
	.byte	'SOE',0,4
	.word	956
	.byte	1,17,2,35,0,18
	.byte	'SME',0,4
	.word	956
	.byte	1,16,2,35,0,18
	.byte	'reserved_16',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_DATR_Bits',0,6,163,1,3
	.word	2349
	.byte	17
	.byte	'_Ifx_CPU_DBGSR_Bits',0,6,166,1,16,4,18
	.byte	'DE',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'HALT',0,4
	.word	956
	.byte	2,29,2,35,0,18
	.byte	'SIH',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'SUSP',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'reserved_5',0,4
	.word	956
	.byte	1,26,2,35,0,18
	.byte	'PREVSUSP',0,4
	.word	956
	.byte	1,25,2,35,0,18
	.byte	'PEVT',0,4
	.word	956
	.byte	1,24,2,35,0,18
	.byte	'EVTSRC',0,4
	.word	956
	.byte	5,19,2,35,0,18
	.byte	'reserved_13',0,4
	.word	956
	.byte	19,0,2,35,0,0,12
	.byte	'Ifx_CPU_DBGSR_Bits',0,6,177,1,3
	.word	2567
	.byte	17
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,6,180,1,16,4,18
	.byte	'DTA',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'reserved_1',0,4
	.word	956
	.byte	31,0,2,35,0,0,12
	.byte	'Ifx_CPU_DBGTCR_Bits',0,6,184,1,3
	.word	2782
	.byte	17
	.byte	'_Ifx_CPU_DCON0_Bits',0,6,187,1,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'DCBYP',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'reserved_2',0,4
	.word	956
	.byte	30,0,2,35,0,0,12
	.byte	'Ifx_CPU_DCON0_Bits',0,6,192,1,3
	.word	2876
	.byte	17
	.byte	'_Ifx_CPU_DCON2_Bits',0,6,195,1,16,4,18
	.byte	'DCACHE_SZE',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'DSCRATCH_SZE',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_DCON2_Bits',0,6,199,1,3
	.word	2992
	.byte	17
	.byte	'_Ifx_CPU_DCX_Bits',0,6,202,1,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	6,26,2,35,0,18
	.byte	'DCXValue',0,4
	.word	956
	.byte	26,0,2,35,0,0,12
	.byte	'Ifx_CPU_DCX_Bits',0,6,206,1,3
	.word	3093
	.byte	17
	.byte	'_Ifx_CPU_DEADD_Bits',0,6,209,1,16,4,18
	.byte	'ERROR_ADDRESS',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_DEADD_Bits',0,6,212,1,3
	.word	3186
	.byte	17
	.byte	'_Ifx_CPU_DIEAR_Bits',0,6,215,1,16,4,18
	.byte	'TA',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_DIEAR_Bits',0,6,218,1,3
	.word	3266
	.byte	17
	.byte	'_Ifx_CPU_DIETR_Bits',0,6,221,1,16,4,18
	.byte	'IED',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'IE_T',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'IE_C',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'IE_S',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'IE_BI',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'E_INFO',0,4
	.word	956
	.byte	6,21,2,35,0,18
	.byte	'IE_DUAL',0,4
	.word	956
	.byte	1,20,2,35,0,18
	.byte	'IE_SP',0,4
	.word	956
	.byte	1,19,2,35,0,18
	.byte	'IE_BS',0,4
	.word	956
	.byte	1,18,2,35,0,18
	.byte	'reserved_14',0,4
	.word	956
	.byte	18,0,2,35,0,0,12
	.byte	'Ifx_CPU_DIETR_Bits',0,6,233,1,3
	.word	3335
	.byte	17
	.byte	'_Ifx_CPU_DMS_Bits',0,6,236,1,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'DMSValue',0,4
	.word	956
	.byte	31,0,2,35,0,0,12
	.byte	'Ifx_CPU_DMS_Bits',0,6,240,1,3
	.word	3564
	.byte	17
	.byte	'_Ifx_CPU_DPR_L_Bits',0,6,243,1,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'LOWBND',0,4
	.word	956
	.byte	29,0,2,35,0,0,12
	.byte	'Ifx_CPU_DPR_L_Bits',0,6,247,1,3
	.word	3657
	.byte	17
	.byte	'_Ifx_CPU_DPR_U_Bits',0,6,250,1,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'UPPBND',0,4
	.word	956
	.byte	29,0,2,35,0,0,12
	.byte	'Ifx_CPU_DPR_U_Bits',0,6,254,1,3
	.word	3752
	.byte	17
	.byte	'_Ifx_CPU_DPRE_Bits',0,6,129,2,16,4,18
	.byte	'RE',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'reserved_16',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_DPRE_Bits',0,6,133,2,3
	.word	3847
	.byte	17
	.byte	'_Ifx_CPU_DPWE_Bits',0,6,136,2,16,4,18
	.byte	'WE',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'reserved_16',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_DPWE_Bits',0,6,140,2,3
	.word	3937
	.byte	17
	.byte	'_Ifx_CPU_DSTR_Bits',0,6,143,2,16,4,18
	.byte	'SRE',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'GAE',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'LBE',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'reserved_3',0,4
	.word	956
	.byte	3,26,2,35,0,18
	.byte	'CRE',0,4
	.word	956
	.byte	1,25,2,35,0,18
	.byte	'reserved_7',0,4
	.word	956
	.byte	7,18,2,35,0,18
	.byte	'DTME',0,4
	.word	956
	.byte	1,17,2,35,0,18
	.byte	'LOE',0,4
	.word	956
	.byte	1,16,2,35,0,18
	.byte	'SDE',0,4
	.word	956
	.byte	1,15,2,35,0,18
	.byte	'SCE',0,4
	.word	956
	.byte	1,14,2,35,0,18
	.byte	'CAC',0,4
	.word	956
	.byte	1,13,2,35,0,18
	.byte	'MPE',0,4
	.word	956
	.byte	1,12,2,35,0,18
	.byte	'CLE',0,4
	.word	956
	.byte	1,11,2,35,0,18
	.byte	'reserved_21',0,4
	.word	956
	.byte	3,8,2,35,0,18
	.byte	'ALN',0,4
	.word	956
	.byte	1,7,2,35,0,18
	.byte	'reserved_25',0,4
	.word	956
	.byte	7,0,2,35,0,0,12
	.byte	'Ifx_CPU_DSTR_Bits',0,6,161,2,3
	.word	4027
	.byte	17
	.byte	'_Ifx_CPU_EXEVT_Bits',0,6,164,2,16,4,18
	.byte	'EVTA',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'BBM',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'BOD',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'SUSP',0,4
	.word	956
	.byte	1,26,2,35,0,18
	.byte	'CNT',0,4
	.word	956
	.byte	2,24,2,35,0,18
	.byte	'reserved_8',0,4
	.word	956
	.byte	24,0,2,35,0,0,12
	.byte	'Ifx_CPU_EXEVT_Bits',0,6,172,2,3
	.word	4351
	.byte	17
	.byte	'_Ifx_CPU_FCX_Bits',0,6,175,2,16,4,18
	.byte	'FCXO',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'FCXS',0,4
	.word	956
	.byte	4,12,2,35,0,18
	.byte	'reserved_20',0,4
	.word	956
	.byte	12,0,2,35,0,0,12
	.byte	'Ifx_CPU_FCX_Bits',0,6,180,2,3
	.word	4505
	.byte	17
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,6,183,2,16,4,18
	.byte	'TST',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'TCL',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'reserved_2',0,4
	.word	956
	.byte	6,24,2,35,0,18
	.byte	'RM',0,4
	.word	956
	.byte	2,22,2,35,0,18
	.byte	'reserved_10',0,4
	.word	956
	.byte	8,14,2,35,0,18
	.byte	'FXE',0,4
	.word	956
	.byte	1,13,2,35,0,18
	.byte	'FUE',0,4
	.word	956
	.byte	1,12,2,35,0,18
	.byte	'FZE',0,4
	.word	956
	.byte	1,11,2,35,0,18
	.byte	'FVE',0,4
	.word	956
	.byte	1,10,2,35,0,18
	.byte	'FIE',0,4
	.word	956
	.byte	1,9,2,35,0,18
	.byte	'reserved_23',0,4
	.word	956
	.byte	3,6,2,35,0,18
	.byte	'FX',0,4
	.word	956
	.byte	1,5,2,35,0,18
	.byte	'FU',0,4
	.word	956
	.byte	1,4,2,35,0,18
	.byte	'FZ',0,4
	.word	956
	.byte	1,3,2,35,0,18
	.byte	'FV',0,4
	.word	956
	.byte	1,2,2,35,0,18
	.byte	'FI',0,4
	.word	956
	.byte	1,1,2,35,0,18
	.byte	'reserved_31',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,6,202,2,3
	.word	4611
	.byte	17
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,205,2,16,4,18
	.byte	'OPC',0,4
	.word	956
	.byte	8,24,2,35,0,18
	.byte	'FMT',0,4
	.word	956
	.byte	1,23,2,35,0,18
	.byte	'reserved_9',0,4
	.word	956
	.byte	7,16,2,35,0,18
	.byte	'DREG',0,4
	.word	956
	.byte	4,12,2,35,0,18
	.byte	'reserved_20',0,4
	.word	956
	.byte	12,0,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,212,2,3
	.word	4960
	.byte	17
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,6,215,2,16,4,18
	.byte	'PC',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,6,218,2,3
	.word	5120
	.byte	17
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,221,2,16,4,18
	.byte	'SRC1',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,224,2,3
	.word	5201
	.byte	17
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,227,2,16,4,18
	.byte	'SRC2',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,230,2,3
	.word	5288
	.byte	17
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,233,2,16,4,18
	.byte	'SRC3',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,236,2,3
	.word	5375
	.byte	17
	.byte	'_Ifx_CPU_ICNT_Bits',0,6,239,2,16,4,18
	.byte	'CountValue',0,4
	.word	956
	.byte	31,1,2,35,0,18
	.byte	'SOvf',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_ICNT_Bits',0,6,243,2,3
	.word	5462
	.byte	17
	.byte	'_Ifx_CPU_ICR_Bits',0,6,246,2,16,4,18
	.byte	'CCPN',0,4
	.word	956
	.byte	10,22,2,35,0,18
	.byte	'reserved_10',0,4
	.word	956
	.byte	5,17,2,35,0,18
	.byte	'IE',0,4
	.word	956
	.byte	1,16,2,35,0,18
	.byte	'PIPN',0,4
	.word	956
	.byte	10,6,2,35,0,18
	.byte	'reserved_26',0,4
	.word	956
	.byte	6,0,2,35,0,0,12
	.byte	'Ifx_CPU_ICR_Bits',0,6,253,2,3
	.word	5553
	.byte	17
	.byte	'_Ifx_CPU_ISP_Bits',0,6,128,3,16,4,18
	.byte	'ISP',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_ISP_Bits',0,6,131,3,3
	.word	5696
	.byte	17
	.byte	'_Ifx_CPU_LCX_Bits',0,6,134,3,16,4,18
	.byte	'LCXO',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'LCXS',0,4
	.word	956
	.byte	4,12,2,35,0,18
	.byte	'reserved_20',0,4
	.word	956
	.byte	12,0,2,35,0,0,12
	.byte	'Ifx_CPU_LCX_Bits',0,6,139,3,3
	.word	5762
	.byte	17
	.byte	'_Ifx_CPU_M1CNT_Bits',0,6,142,3,16,4,18
	.byte	'CountValue',0,4
	.word	956
	.byte	31,1,2,35,0,18
	.byte	'SOvf',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_M1CNT_Bits',0,6,146,3,3
	.word	5868
	.byte	17
	.byte	'_Ifx_CPU_M2CNT_Bits',0,6,149,3,16,4,18
	.byte	'CountValue',0,4
	.word	956
	.byte	31,1,2,35,0,18
	.byte	'SOvf',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_M2CNT_Bits',0,6,153,3,3
	.word	5961
	.byte	17
	.byte	'_Ifx_CPU_M3CNT_Bits',0,6,156,3,16,4,18
	.byte	'CountValue',0,4
	.word	956
	.byte	31,1,2,35,0,18
	.byte	'SOvf',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_M3CNT_Bits',0,6,160,3,3
	.word	6054
	.byte	17
	.byte	'_Ifx_CPU_PC_Bits',0,6,163,3,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'PC',0,4
	.word	956
	.byte	31,0,2,35,0,0,12
	.byte	'Ifx_CPU_PC_Bits',0,6,167,3,3
	.word	6147
	.byte	17
	.byte	'_Ifx_CPU_PCON0_Bits',0,6,170,3,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'PCBYP',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'reserved_2',0,4
	.word	956
	.byte	30,0,2,35,0,0,12
	.byte	'Ifx_CPU_PCON0_Bits',0,6,175,3,3
	.word	6232
	.byte	17
	.byte	'_Ifx_CPU_PCON1_Bits',0,6,178,3,16,4,18
	.byte	'PCINV',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'PBINV',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'reserved_2',0,4
	.word	956
	.byte	30,0,2,35,0,0,12
	.byte	'Ifx_CPU_PCON1_Bits',0,6,183,3,3
	.word	6348
	.byte	17
	.byte	'_Ifx_CPU_PCON2_Bits',0,6,186,3,16,4,18
	.byte	'PCACHE_SZE',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'PSCRATCH_SZE',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_PCON2_Bits',0,6,190,3,3
	.word	6459
	.byte	17
	.byte	'_Ifx_CPU_PCXI_Bits',0,6,193,3,16,4,18
	.byte	'PCXO',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'PCXS',0,4
	.word	956
	.byte	4,12,2,35,0,18
	.byte	'UL',0,4
	.word	956
	.byte	1,11,2,35,0,18
	.byte	'PIE',0,4
	.word	956
	.byte	1,10,2,35,0,18
	.byte	'PCPN',0,4
	.word	956
	.byte	10,0,2,35,0,0,12
	.byte	'Ifx_CPU_PCXI_Bits',0,6,200,3,3
	.word	6560
	.byte	17
	.byte	'_Ifx_CPU_PIEAR_Bits',0,6,203,3,16,4,18
	.byte	'TA',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_PIEAR_Bits',0,6,206,3,3
	.word	6690
	.byte	17
	.byte	'_Ifx_CPU_PIETR_Bits',0,6,209,3,16,4,18
	.byte	'IED',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'IE_T',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'IE_C',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'IE_S',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'IE_BI',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'E_INFO',0,4
	.word	956
	.byte	6,21,2,35,0,18
	.byte	'IE_DUAL',0,4
	.word	956
	.byte	1,20,2,35,0,18
	.byte	'IE_SP',0,4
	.word	956
	.byte	1,19,2,35,0,18
	.byte	'IE_BS',0,4
	.word	956
	.byte	1,18,2,35,0,18
	.byte	'reserved_14',0,4
	.word	956
	.byte	18,0,2,35,0,0,12
	.byte	'Ifx_CPU_PIETR_Bits',0,6,221,3,3
	.word	6759
	.byte	17
	.byte	'_Ifx_CPU_PMA0_Bits',0,6,224,3,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	13,19,2,35,0,18
	.byte	'DAC',0,4
	.word	956
	.byte	3,16,2,35,0,18
	.byte	'reserved_16',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_PMA0_Bits',0,6,229,3,3
	.word	6988
	.byte	17
	.byte	'_Ifx_CPU_PMA1_Bits',0,6,232,3,16,4,18
	.byte	'reserved_0',0,4
	.word	956
	.byte	14,18,2,35,0,18
	.byte	'CAC',0,4
	.word	956
	.byte	2,16,2,35,0,18
	.byte	'reserved_16',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_PMA1_Bits',0,6,237,3,3
	.word	7101
	.byte	17
	.byte	'_Ifx_CPU_PMA2_Bits',0,6,240,3,16,4,18
	.byte	'PSI',0,4
	.word	956
	.byte	16,16,2,35,0,18
	.byte	'reserved_16',0,4
	.word	956
	.byte	16,0,2,35,0,0,12
	.byte	'Ifx_CPU_PMA2_Bits',0,6,244,3,3
	.word	7214
	.byte	17
	.byte	'_Ifx_CPU_PSTR_Bits',0,6,247,3,16,4,18
	.byte	'FRE',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'reserved_1',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'FBE',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'reserved_3',0,4
	.word	956
	.byte	9,20,2,35,0,18
	.byte	'FPE',0,4
	.word	956
	.byte	1,19,2,35,0,18
	.byte	'reserved_13',0,4
	.word	956
	.byte	1,18,2,35,0,18
	.byte	'FME',0,4
	.word	956
	.byte	1,17,2,35,0,18
	.byte	'reserved_15',0,4
	.word	956
	.byte	17,0,2,35,0,0,12
	.byte	'Ifx_CPU_PSTR_Bits',0,6,129,4,3
	.word	7305
	.byte	17
	.byte	'_Ifx_CPU_PSW_Bits',0,6,132,4,16,4,18
	.byte	'CDC',0,4
	.word	956
	.byte	7,25,2,35,0,18
	.byte	'CDE',0,4
	.word	956
	.byte	1,24,2,35,0,18
	.byte	'GW',0,4
	.word	956
	.byte	1,23,2,35,0,18
	.byte	'IS',0,4
	.word	956
	.byte	1,22,2,35,0,18
	.byte	'IO',0,4
	.word	956
	.byte	2,20,2,35,0,18
	.byte	'PRS',0,4
	.word	956
	.byte	2,18,2,35,0,18
	.byte	'S',0,4
	.word	956
	.byte	1,17,2,35,0,18
	.byte	'reserved_15',0,4
	.word	956
	.byte	12,5,2,35,0,18
	.byte	'SAV',0,4
	.word	956
	.byte	1,4,2,35,0,18
	.byte	'AV',0,4
	.word	956
	.byte	1,3,2,35,0,18
	.byte	'SV',0,4
	.word	956
	.byte	1,2,2,35,0,18
	.byte	'V',0,4
	.word	956
	.byte	1,1,2,35,0,18
	.byte	'C',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_PSW_Bits',0,6,147,4,3
	.word	7508
	.byte	17
	.byte	'_Ifx_CPU_SEGEN_Bits',0,6,150,4,16,4,18
	.byte	'ADFLIP',0,4
	.word	956
	.byte	8,24,2,35,0,18
	.byte	'ADTYPE',0,4
	.word	956
	.byte	2,22,2,35,0,18
	.byte	'reserved_10',0,4
	.word	956
	.byte	21,1,2,35,0,18
	.byte	'AE',0,4
	.word	956
	.byte	1,0,2,35,0,0,12
	.byte	'Ifx_CPU_SEGEN_Bits',0,6,156,4,3
	.word	7751
	.byte	17
	.byte	'_Ifx_CPU_SMACON_Bits',0,6,159,4,16,4,18
	.byte	'PC',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'reserved_1',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'PT',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'reserved_3',0,4
	.word	956
	.byte	5,24,2,35,0,18
	.byte	'DC',0,4
	.word	956
	.byte	1,23,2,35,0,18
	.byte	'reserved_9',0,4
	.word	956
	.byte	1,22,2,35,0,18
	.byte	'DT',0,4
	.word	956
	.byte	1,21,2,35,0,18
	.byte	'reserved_11',0,4
	.word	956
	.byte	13,8,2,35,0,18
	.byte	'IODT',0,4
	.word	956
	.byte	1,7,2,35,0,18
	.byte	'reserved_25',0,4
	.word	956
	.byte	7,0,2,35,0,0,12
	.byte	'Ifx_CPU_SMACON_Bits',0,6,171,4,3
	.word	7879
	.byte	17
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,6,174,4,16,4,18
	.byte	'EN',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,6,177,4,3
	.word	8120
	.byte	17
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,6,180,4,16,4,18
	.byte	'reserved_0',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,6,183,4,3
	.word	8203
	.byte	17
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,186,4,16,4,18
	.byte	'EN',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,189,4,3
	.word	8294
	.byte	17
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,192,4,16,4,18
	.byte	'reserved_0',0,4
	.word	548
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,195,4,3
	.word	8385
	.byte	17
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,6,198,4,16,4,18
	.byte	'reserved_0',0,1
	.word	633
	.byte	5,3,2,35,0,18
	.byte	'ADDR',0,4
	.word	548
	.byte	27,0,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,6,202,4,3
	.word	8484
	.byte	17
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,6,205,4,16,4,18
	.byte	'reserved_0',0,1
	.word	633
	.byte	5,3,2,35,0,18
	.byte	'ADDR',0,4
	.word	548
	.byte	27,0,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,6,209,4,3
	.word	8591
	.byte	17
	.byte	'_Ifx_CPU_SWEVT_Bits',0,6,212,4,16,4,18
	.byte	'EVTA',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'BBM',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'BOD',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'SUSP',0,4
	.word	956
	.byte	1,26,2,35,0,18
	.byte	'CNT',0,4
	.word	956
	.byte	2,24,2,35,0,18
	.byte	'reserved_8',0,4
	.word	956
	.byte	24,0,2,35,0,0,12
	.byte	'Ifx_CPU_SWEVT_Bits',0,6,220,4,3
	.word	8698
	.byte	17
	.byte	'_Ifx_CPU_SYSCON_Bits',0,6,223,4,16,4,18
	.byte	'FCDSF',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'PROTEN',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'TPROTEN',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'IS',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'IT',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'reserved_5',0,4
	.word	956
	.byte	27,0,2,35,0,0,12
	.byte	'Ifx_CPU_SYSCON_Bits',0,6,231,4,3
	.word	8852
	.byte	17
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,6,234,4,16,4,18
	.byte	'ASI',0,4
	.word	956
	.byte	5,27,2,35,0,18
	.byte	'reserved_5',0,4
	.word	956
	.byte	27,0,2,35,0,0,12
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,6,238,4,3
	.word	9013
	.byte	17
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,6,241,4,16,4,18
	.byte	'TEXP0',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'TEXP1',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'TEXP2',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'reserved_3',0,4
	.word	956
	.byte	13,16,2,35,0,18
	.byte	'TTRAP',0,4
	.word	956
	.byte	1,15,2,35,0,18
	.byte	'reserved_17',0,4
	.word	956
	.byte	15,0,2,35,0,0,12
	.byte	'Ifx_CPU_TPS_CON_Bits',0,6,249,4,3
	.word	9111
	.byte	17
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,6,252,4,16,4,18
	.byte	'Timer',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,6,255,4,3
	.word	9283
	.byte	17
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,6,130,5,16,4,18
	.byte	'ADDR',0,4
	.word	956
	.byte	32,0,2,35,0,0,12
	.byte	'Ifx_CPU_TR_ADR_Bits',0,6,133,5,3
	.word	9363
	.byte	17
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,6,136,5,16,4,18
	.byte	'EVTA',0,4
	.word	956
	.byte	3,29,2,35,0,18
	.byte	'BBM',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'BOD',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'SUSP',0,4
	.word	956
	.byte	1,26,2,35,0,18
	.byte	'CNT',0,4
	.word	956
	.byte	2,24,2,35,0,18
	.byte	'reserved_8',0,4
	.word	956
	.byte	4,20,2,35,0,18
	.byte	'TYP',0,4
	.word	956
	.byte	1,19,2,35,0,18
	.byte	'RNG',0,4
	.word	956
	.byte	1,18,2,35,0,18
	.byte	'reserved_14',0,4
	.word	956
	.byte	1,17,2,35,0,18
	.byte	'ASI_EN',0,4
	.word	956
	.byte	1,16,2,35,0,18
	.byte	'ASI',0,4
	.word	956
	.byte	5,11,2,35,0,18
	.byte	'reserved_21',0,4
	.word	956
	.byte	6,5,2,35,0,18
	.byte	'AST',0,4
	.word	956
	.byte	1,4,2,35,0,18
	.byte	'ALD',0,4
	.word	956
	.byte	1,3,2,35,0,18
	.byte	'reserved_29',0,4
	.word	956
	.byte	3,0,2,35,0,0,12
	.byte	'Ifx_CPU_TR_EVT_Bits',0,6,153,5,3
	.word	9436
	.byte	17
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,6,156,5,16,4,18
	.byte	'T0',0,4
	.word	956
	.byte	1,31,2,35,0,18
	.byte	'T1',0,4
	.word	956
	.byte	1,30,2,35,0,18
	.byte	'T2',0,4
	.word	956
	.byte	1,29,2,35,0,18
	.byte	'T3',0,4
	.word	956
	.byte	1,28,2,35,0,18
	.byte	'T4',0,4
	.word	956
	.byte	1,27,2,35,0,18
	.byte	'T5',0,4
	.word	956
	.byte	1,26,2,35,0,18
	.byte	'T6',0,4
	.word	956
	.byte	1,25,2,35,0,18
	.byte	'T7',0,4
	.word	956
	.byte	1,24,2,35,0,18
	.byte	'reserved_8',0,4
	.word	956
	.byte	24,0,2,35,0,0,12
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,6,167,5,3
	.word	9754
	.byte	19,6,175,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	972
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_A',0,6,180,5,3
	.word	9949
	.byte	19,6,183,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1033
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_BIV',0,6,188,5,3
	.word	10008
	.byte	19,6,191,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1112
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_BTV',0,6,196,5,3
	.word	10069
	.byte	19,6,199,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1198
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CCNT',0,6,204,5,3
	.word	10130
	.byte	19,6,207,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1287
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CCTRL',0,6,212,5,3
	.word	10192
	.byte	19,6,215,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1433
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_COMPAT',0,6,220,5,3
	.word	10255
	.byte	19,6,223,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1560
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CORE_ID',0,6,228,5,3
	.word	10319
	.byte	19,6,231,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1658
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CPR_L',0,6,236,5,3
	.word	10384
	.byte	19,6,239,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1751
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CPR_U',0,6,244,5,3
	.word	10447
	.byte	19,6,247,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1844
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CPU_ID',0,6,252,5,3
	.word	10510
	.byte	19,6,255,5,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	1951
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CPXE',0,6,132,6,3
	.word	10574
	.byte	19,6,135,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2038
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CREVT',0,6,140,6,3
	.word	10636
	.byte	19,6,143,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2192
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_CUS_ID',0,6,148,6,3
	.word	10699
	.byte	19,6,151,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2286
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_D',0,6,156,6,3
	.word	10763
	.byte	19,6,159,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2349
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DATR',0,6,164,6,3
	.word	10822
	.byte	19,6,167,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2567
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DBGSR',0,6,172,6,3
	.word	10884
	.byte	19,6,175,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2782
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DBGTCR',0,6,180,6,3
	.word	10947
	.byte	19,6,183,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2876
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DCON0',0,6,188,6,3
	.word	11011
	.byte	19,6,191,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	2992
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DCON2',0,6,196,6,3
	.word	11074
	.byte	19,6,199,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3093
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DCX',0,6,204,6,3
	.word	11137
	.byte	19,6,207,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3186
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DEADD',0,6,212,6,3
	.word	11198
	.byte	19,6,215,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3266
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DIEAR',0,6,220,6,3
	.word	11261
	.byte	19,6,223,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3335
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DIETR',0,6,228,6,3
	.word	11324
	.byte	19,6,231,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3564
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DMS',0,6,236,6,3
	.word	11387
	.byte	19,6,239,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3657
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DPR_L',0,6,244,6,3
	.word	11448
	.byte	19,6,247,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3752
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DPR_U',0,6,252,6,3
	.word	11511
	.byte	19,6,255,6,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3847
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DPRE',0,6,132,7,3
	.word	11574
	.byte	19,6,135,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	3937
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DPWE',0,6,140,7,3
	.word	11636
	.byte	19,6,143,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4027
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_DSTR',0,6,148,7,3
	.word	11698
	.byte	19,6,151,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4351
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_EXEVT',0,6,156,7,3
	.word	11760
	.byte	19,6,159,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4505
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_FCX',0,6,164,7,3
	.word	11823
	.byte	19,6,167,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4611
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,6,172,7,3
	.word	11884
	.byte	19,6,175,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	4960
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,6,180,7,3
	.word	11954
	.byte	19,6,183,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5120
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,6,188,7,3
	.word	12024
	.byte	19,6,191,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5201
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,6,196,7,3
	.word	12093
	.byte	19,6,199,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5288
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,6,204,7,3
	.word	12164
	.byte	19,6,207,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5375
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,6,212,7,3
	.word	12235
	.byte	19,6,215,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5462
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_ICNT',0,6,220,7,3
	.word	12306
	.byte	19,6,223,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5553
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_ICR',0,6,228,7,3
	.word	12368
	.byte	19,6,231,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5696
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_ISP',0,6,236,7,3
	.word	12429
	.byte	19,6,239,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5762
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_LCX',0,6,244,7,3
	.word	12490
	.byte	19,6,247,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5868
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_M1CNT',0,6,252,7,3
	.word	12551
	.byte	19,6,255,7,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	5961
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_M2CNT',0,6,132,8,3
	.word	12614
	.byte	19,6,135,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6054
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_M3CNT',0,6,140,8,3
	.word	12677
	.byte	19,6,143,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6147
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PC',0,6,148,8,3
	.word	12740
	.byte	19,6,151,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6232
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PCON0',0,6,156,8,3
	.word	12800
	.byte	19,6,159,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6348
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PCON1',0,6,164,8,3
	.word	12863
	.byte	19,6,167,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6459
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PCON2',0,6,172,8,3
	.word	12926
	.byte	19,6,175,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6560
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PCXI',0,6,180,8,3
	.word	12989
	.byte	19,6,183,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6690
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PIEAR',0,6,188,8,3
	.word	13051
	.byte	19,6,191,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6759
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PIETR',0,6,196,8,3
	.word	13114
	.byte	19,6,199,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	6988
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PMA0',0,6,204,8,3
	.word	13177
	.byte	19,6,207,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7101
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PMA1',0,6,212,8,3
	.word	13239
	.byte	19,6,215,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7214
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PMA2',0,6,220,8,3
	.word	13301
	.byte	19,6,223,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7305
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PSTR',0,6,228,8,3
	.word	13363
	.byte	19,6,231,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7508
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_PSW',0,6,236,8,3
	.word	13425
	.byte	19,6,239,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7751
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SEGEN',0,6,244,8,3
	.word	13486
	.byte	19,6,247,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	7879
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SMACON',0,6,252,8,3
	.word	13549
	.byte	19,6,255,8,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8120
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_ACCENA',0,6,132,9,3
	.word	13613
	.byte	19,6,135,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8203
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_ACCENB',0,6,140,9,3
	.word	13683
	.byte	19,6,143,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8294
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,6,148,9,3
	.word	13753
	.byte	19,6,151,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8385
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,6,156,9,3
	.word	13827
	.byte	19,6,159,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8484
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,6,164,9,3
	.word	13901
	.byte	19,6,167,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8591
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,6,172,9,3
	.word	13971
	.byte	19,6,175,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8698
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SWEVT',0,6,180,9,3
	.word	14041
	.byte	19,6,183,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	8852
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_SYSCON',0,6,188,9,3
	.word	14104
	.byte	19,6,191,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	9013
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_TASK_ASI',0,6,196,9,3
	.word	14168
	.byte	19,6,199,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	9111
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_TPS_CON',0,6,204,9,3
	.word	14234
	.byte	19,6,207,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	9283
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_TPS_TIMER',0,6,212,9,3
	.word	14299
	.byte	19,6,215,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	9363
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_TR_ADR',0,6,220,9,3
	.word	14366
	.byte	19,6,223,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	9436
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_TR_EVT',0,6,228,9,3
	.word	14430
	.byte	19,6,231,9,9,4,16
	.byte	'U',0
	.word	548
	.byte	4,2,35,0,16
	.byte	'I',0
	.word	581
	.byte	4,2,35,0,16
	.byte	'B',0
	.word	9754
	.byte	4,2,35,0,0,12
	.byte	'Ifx_CPU_TRIG_ACC',0,6,236,9,3
	.word	14494
	.byte	17
	.byte	'_Ifx_CPU_CPR',0,6,247,9,25,8,16
	.byte	'L',0
	.word	10384
	.byte	4,2,35,0,16
	.byte	'U',0
	.word	10447
	.byte	4,2,35,4,0,14
	.word	14560
	.byte	12
	.byte	'Ifx_CPU_CPR',0,6,251,9,3
	.word	14602
	.byte	17
	.byte	'_Ifx_CPU_DPR',0,6,254,9,25,8,16
	.byte	'L',0
	.word	11448
	.byte	4,2,35,0,16
	.byte	'U',0
	.word	11511
	.byte	4,2,35,4,0,14
	.word	14628
	.byte	12
	.byte	'Ifx_CPU_DPR',0,6,130,10,3
	.word	14670
	.byte	17
	.byte	'_Ifx_CPU_SPROT_RGN',0,6,133,10,25,16,16
	.byte	'LA',0
	.word	13901
	.byte	4,2,35,0,16
	.byte	'UA',0
	.word	13971
	.byte	4,2,35,4,16
	.byte	'ACCENA',0
	.word	13753
	.byte	4,2,35,8,16
	.byte	'ACCENB',0
	.word	13827
	.byte	4,2,35,12,0,14
	.word	14696
	.byte	12
	.byte	'Ifx_CPU_SPROT_RGN',0,6,139,10,3
	.word	14778
	.byte	20,12
	.word	14299
	.byte	21,2,0,17
	.byte	'_Ifx_CPU_TPS',0,6,142,10,25,16,16
	.byte	'CON',0
	.word	14234
	.byte	4,2,35,0,16
	.byte	'TIMER',0
	.word	14810
	.byte	12,2,35,4,0,14
	.word	14819
	.byte	12
	.byte	'Ifx_CPU_TPS',0,6,146,10,3
	.word	14867
	.byte	17
	.byte	'_Ifx_CPU_TR',0,6,149,10,25,8,16
	.byte	'EVT',0
	.word	14430
	.byte	4,2,35,0,16
	.byte	'ADR',0
	.word	14366
	.byte	4,2,35,4,0,14
	.word	14893
	.byte	12
	.byte	'Ifx_CPU_TR',0,6,153,10,3
	.word	14938
	.byte	20,16
	.word	890
	.byte	21,1,0
.L8:
	.byte	22
	.word	14963
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,46,1,49,19,0,0,11,5
	.byte	0,49,19,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,53,0,73,19,0,0,15,19,1,58,15
	.byte	59,15,57,15,11,15,0,0,16,13,0,3,8,73,19,11,15,56,9,0,0,17,19,1,3,8,58,15,59,15,57,15,11,15,0,0,18,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,19,23,1,58,15,59,15,57,15,11,15,0,0,20,1,1,11,15,73,19,0,0,21,33
	.byte	0,47,15,0,0,22,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxCpu_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	265
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxCpu_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxCpu_cfg_indexMap',0,3,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxCpu_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxCpu_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
