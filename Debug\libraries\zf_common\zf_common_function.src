	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc43668a --dep-file=zf_common_function.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_common/zf_common_function.src ../libraries/zf_common/zf_common_function.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_common/zf_common_function.c'

	
$TC16X
	
	.sdecl	'.text.zf_common_function.func_get_greatest_common_divisor',code,cluster('func_get_greatest_common_divisor')
	.sect	'.text.zf_common_function.func_get_greatest_common_divisor'
	.align	2
	
	.global	func_get_greatest_common_divisor
; Function func_get_greatest_common_divisor
.L210:
func_get_greatest_common_divisor:	.type	func
	j	.L2
.L3:
	jge.u	d5,d4,.L4
.L657:
	sub	d4,d5
.L4:
	jge.u	d4,d5,.L5
.L658:
	sub	d5,d4
.L5:
.L2:
	jne	d4,d5,.L3
.L659:
	mov	d2,d5
.L461:
	j	.L6
.L6:
	ret
.L319:
	
__func_get_greatest_common_divisor_function_end:
	.size	func_get_greatest_common_divisor,__func_get_greatest_common_divisor_function_end-func_get_greatest_common_divisor
.L247:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_soft_delay',code,cluster('func_soft_delay')
	.sect	'.text.zf_common_function.func_soft_delay'
	.align	2
	
	.global	func_soft_delay
; Function func_soft_delay
.L212:
func_soft_delay:	.type	func
	j	.L7
.L8:
.L7:
	mov	d15,d4
	add	d4,#-1
.L664:
	jne	d15,#0,.L8
.L665:
	ret
.L322:
	
__func_soft_delay_function_end:
	.size	func_soft_delay,__func_soft_delay_function_end-func_soft_delay
.L252:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_str_to_int',code,cluster('func_str_to_int')
	.sect	'.text.zf_common_function.func_str_to_int'
	.align	2
	
	.global	func_str_to_int
; Function func_str_to_int
.L214:
func_str_to_int:	.type	func
	mov.aa	a15,a4
.L463:
	mov.a	a2,#0
	ne.a	d4,a15,a2
	movh.a	a4,#@his(.1.str)
.L462:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#84
	call	debug_assert_handler
.L329:
	mov	d0,#0
.L464:
	mov	d2,#0
.L9:
	mov.a	a2,#0
.L670:
	jne.a	a2,a15,.L10
.L671:
	j	.L11
.L10:
	ld.b	d1,[a15]0
.L672:
	mov	d15,#45
.L673:
	jne	d15,d1,.L12
.L674:
	mov	d0,#1
.L675:
	add.a	a15,#1
.L676:
	j	.L13
.L12:
	ld.b	d1,[a15]0
.L677:
	mov	d15,#43
.L678:
	jne	d15,d1,.L14
.L679:
	add.a	a15,#1
.L14:
.L13:
	j	.L15
.L16:
	mov	d15,#10
.L680:
	ld.b	d1,[a15]0
.L681:
	add	d1,d1,#-48
.L682:
	madd	d2,d1,d2,d15
.L683:
	add.a	a15,#1
.L15:
	ld.b	d1,[a15]0
.L684:
	mov	d15,#48
.L685:
	jlt	d1,d15,.L17
.L686:
	ld.b	d1,[a15]0
.L687:
	mov	d15,#57
.L688:
	jge	d15,d1,.L16
.L17:
	jeq	d0,#0,.L18
.L689:
	rsub	d2,#0
.L18:
.L11:
	j	.L19
.L19:
	ret
.L326:
	
__func_str_to_int_function_end:
	.size	func_str_to_int,__func_str_to_int_function_end-func_str_to_int
.L257:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_int_to_str',code,cluster('func_int_to_str')
	.sect	'.text.zf_common_function.func_int_to_str'
	.align	2
	
	.global	func_int_to_str
; Function func_int_to_str
.L216:
func_int_to_str:	.type	func
	sub.a	a10,#16
.L465:
	mov.aa	a12,a4
.L468:
	mov	d8,d4
.L469:
	mov.a	a15,#0
	ne.a	d4,a12,a15
.L466:
	movh.a	a4,#@his(.1.str)
.L467:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#128
	call	debug_assert_handler
.L336:
	mov	d15,#0
.L20:
	mov.a	a15,#0
.L694:
	jne.a	a15,a12,.L21
.L695:
	j	.L22
.L21:
	jge	d8,#0,.L23
.L696:
	mov	d0,#45
.L697:
	st.b	[a12],d0
.L698:
	add.a	a12,#1
.L699:
	rsub	d8,#0
.L700:
	j	.L24
.L23:
	jne	d8,#0,.L25
.L701:
	mov	d15,#48
.L470:
	st.b	[a12],d15
.L702:
	j	.L26
.L25:
.L24:
	j	.L27
.L28:
	mov	d0,#10
.L703:
	div	e0,d8,d0
.L472:
	jlt	d1,#0,.L29
.L704:
	j	.L30
.L29:
	rsub	d1,#0
.L30:
	addsc.a	a15,a10,d15,#0
.L705:
	st.b	[a15],d1
.L706:
	add	d15,#1
.L471:
	extr.u	d15,d15,#0,#8
.L473:
	mov	d0,#10
.L707:
	div	e8,d8,d0
.L27:
	jne	d8,#0,.L28
.L708:
	j	.L31
.L32:
	addsc.a	a15,a10,d15,#0
	ld.bu	d0,[a15]-1
.L709:
	add	d0,d0,#48
.L710:
	st.b	[a12],d0
.L711:
	add.a	a12,#1
.L712:
	add	d15,#-1
.L31:
	jne	d15,#0,.L32
.L26:
.L22:
	ret
.L333:
	
__func_int_to_str_function_end:
	.size	func_int_to_str,__func_int_to_str_function_end-func_int_to_str
.L262:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_str_to_uint',code,cluster('func_str_to_uint')
	.sect	'.text.zf_common_function.func_str_to_uint'
	.align	2
	
	.global	func_str_to_uint
; Function func_str_to_uint
.L218:
func_str_to_uint:	.type	func
	mov.aa	a15,a4
.L475:
	mov.a	a2,#0
	ne.a	d4,a15,a2
	movh.a	a4,#@his(.1.str)
.L474:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#174
	call	debug_assert_handler
.L343:
	mov	d2,#0
.L33:
	mov.a	a2,#0
.L717:
	jne.a	a2,a15,.L34
.L718:
	j	.L35
.L34:
	j	.L36
.L37:
	mov	d15,#10
.L719:
	ld.b	d0,[a15]0
.L720:
	add	d0,d0,#-48
.L721:
	madd	d2,d0,d2,d15
.L722:
	add.a	a15,#1
.L36:
	ld.b	d0,[a15]0
.L723:
	mov	d15,#48
.L724:
	jlt	d0,d15,.L38
.L725:
	ld.b	d0,[a15]0
.L726:
	mov	d15,#57
.L727:
	jge	d15,d0,.L37
.L38:
.L35:
	j	.L39
.L39:
	ret
.L341:
	
__func_str_to_uint_function_end:
	.size	func_str_to_uint,__func_str_to_uint_function_end-func_str_to_uint
.L267:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_uint_to_str',code,cluster('func_uint_to_str')
	.sect	'.text.zf_common_function.func_uint_to_str'
	.align	2
	
	.global	func_uint_to_str
; Function func_uint_to_str
.L220:
func_uint_to_str:	.type	func
	sub.a	a10,#16
.L476:
	mov.aa	a12,a4
.L479:
	mov	d8,d4
.L480:
	mov.a	a15,#0
	ne.a	d4,a12,a15
.L477:
	movh.a	a4,#@his(.1.str)
.L478:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#204
	call	debug_assert_handler
.L348:
	mov	d15,#0
.L40:
	mov.a	a15,#0
.L732:
	jne.a	a15,a12,.L41
.L733:
	j	.L42
.L41:
	jne	d8,#0,.L43
.L734:
	mov	d15,#48
.L481:
	st.b	[a12],d15
.L735:
	j	.L44
.L43:
	j	.L45
.L46:
	addsc.a	a15,a10,d15,#0
.L736:
	mov	d0,#10
.L737:
	div.u	e0,d8,d0
.L738:
	st.b	[a15],d1
.L739:
	add	d15,#1
.L482:
	extr.u	d15,d15,#0,#8
.L483:
	mov	d0,#10
.L740:
	div.u	e8,d8,d0
.L45:
	jne	d8,#0,.L46
.L741:
	j	.L47
.L48:
	addsc.a	a15,a10,d15,#0
	ld.b	d0,[a15]-1
.L742:
	add	d0,d0,#48
.L743:
	st.b	[a12],d0
.L744:
	add.a	a12,#1
.L745:
	add	d15,#-1
.L47:
	jne	d15,#0,.L48
.L44:
.L42:
	ret
.L345:
	
__func_uint_to_str_function_end:
	.size	func_uint_to_str,__func_uint_to_str_function_end-func_uint_to_str
.L272:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_str_to_float',code,cluster('func_str_to_float')
	.sect	'.text.zf_common_function.func_str_to_float'
	.align	2
	
	.global	func_str_to_float
; Function func_str_to_float
.L222:
func_str_to_float:	.type	func
	mov.aa	a15,a4
.L485:
	mov.a	a2,#0
	ne.a	d4,a15,a2
	movh.a	a4,#@his(.1.str)
.L484:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#243
	call	debug_assert_handler
.L355:
	mov	d8,#0
.L486:
	mov	d9,#0
.L487:
	mov	d10,#0
.L488:
	movh	d11,#16256
.L49:
	mov.a	a2,#0
.L750:
	jne.a	a2,a15,.L50
.L751:
	j	.L51
.L50:
	ld.b	d0,[a15]0
.L752:
	mov	d15,#45
.L753:
	jne	d15,d0,.L52
.L754:
	mov	d8,#1
.L755:
	add.a	a15,#1
.L756:
	j	.L53
.L52:
	ld.b	d0,[a15]0
.L757:
	mov	d15,#43
.L758:
	jne	d15,d0,.L54
.L759:
	add.a	a15,#1
.L54:
.L53:
	j	.L55
.L56:
	movh	d15,#16672
.L760:
	ld.b	d0,[a15]0
.L761:
	add	d0,d0,#-48
	itof	d0,d0
.L762:
	madd.f	d9,d0,d9,d15
.L763:
	add.a	a15,#1
.L55:
	ld.b	d0,[a15]0
.L764:
	mov	d15,#48
.L765:
	jlt	d0,d15,.L57
.L766:
	ld.b	d0,[a15]0
.L767:
	mov	d15,#57
.L768:
	jge	d15,d0,.L56
.L57:
	ld.b	d0,[a15]0
.L769:
	mov	d15,#46
.L770:
	jne	d15,d0,.L58
.L771:
	add.a	a15,#1
.L772:
	j	.L59
.L60:
	movh	d15,#16672
.L773:
	ld.b	d0,[a15]0
.L774:
	add	d0,d0,#-48
	itof	d0,d0
.L775:
	madd.f	d10,d0,d10,d15
.L776:
	movh	d15,#16672
.L777:
	mul.f	d11,d11,d15
.L778:
	add.a	a15,#1
.L59:
	ld.b	d0,[a15]0
.L779:
	mov	d15,#48
.L780:
	jlt	d0,d15,.L61
.L781:
	ld.b	d0,[a15]0
.L782:
	mov	d15,#57
.L783:
	jlt	d15,d0,.L62
.L784:
	mov	d4,d11
.L489:
	call	__f_ftod
.L490:
	mov	e6,d3,d2
.L785:
	mov	d4,#0
	mov.u	d5,#33920
	addih	d5,d5,#16686
.L786:
	call	__d_fgt
	jne	d2,#0,.L60
.L62:
.L61:
	div.f	d10,d10,d11
.L58:
	add.f	d9,d9,d10
.L787:
	jeq	d8,#0,.L63
.L788:
	insn.t	d9,d9:31,d9:31
.L63:
.L51:
	mov	d2,d9
.L491:
	j	.L64
.L64:
	ret
.L353:
	
__func_str_to_float_function_end:
	.size	func_str_to_float,__func_str_to_float_function_end-func_str_to_float
.L277:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_float_to_str',code,cluster('func_float_to_str')
	.sect	'.text.zf_common_function.func_float_to_str'
	.align	2
	
	.global	func_float_to_str
; Function func_float_to_str
.L224:
func_float_to_str:	.type	func
	sub.a	a10,#56
.L492:
	mov.aa	a12,a4
.L496:
	mov	e10,d5,d4
.L793:
	mov.a	a15,#0
	ne.a	d4,a12,a15
.L493:
	movh.a	a4,#@his(.1.str)
.L495:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#304
.L494:
	call	debug_assert_handler
.L364:
	mov	d12,d11
.L65:
	mov.a	a15,#0
.L794:
	jne.a	a15,a12,.L66
.L795:
	j	.L67
.L66:
	ftoiz	d8,d10
.L497:
	mov	d0,#0
.L498:
	cmp.f	d0,d10,d0
.L499:
	extr.u	d15,d0,#0,#1
.L796:
	jeq	d15,#0,.L68
.L797:
	mov	d15,#45
.L798:
	st.b	[a12],d15
.L799:
	add.a	a12,#1
.L800:
	j	.L69
.L68:
	mov	d4,d10
.L500:
	call	__f_ftod
.L501:
	extr.u	d15,d3,#20,#11
	eq	d15,d15,#0
.L801:
	jeq	d15,#0,.L70
.L802:
	mov	d15,#48
.L803:
	st.b	[a12],d15
.L804:
	add.a	a12,#1
.L805:
	mov	d15,#46
.L806:
	st.b	[a12],d15
.L807:
	add.a	a12,#1
.L808:
	mov	d15,#48
.L809:
	st.b	[a12],d15
.L810:
	j	.L71
.L70:
.L69:
	itof	d15,d8
.L502:
	sub.f	d15,d10,d15
.L503:
	j	.L72
.L73:
	add	d12,#-1
.L811:
	movh	d0,#16672
.L812:
	mul.f	d15,d15,d0
.L72:
	jne	d12,#0,.L73
.L813:
	ftoiz	d2,d15
.L504:
	mov	d4,#0
.L74:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
.L814:
	mov	d15,#10
.L815:
	div	e0,d8,d15
.L816:
	st.w	[a15],d1
.L817:
	add	d15,d4,#1
	extr.u	d4,d15,#0,#8
.L818:
	mov	d15,#10
.L819:
	div	e8,d8,d15
.L820:
	jne	d8,#0,.L74
.L821:
	j	.L75
.L76:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d0,[a15]
	jlt	d0,#0,.L77
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]
	j	.L78
.L77:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]
	rsub	d15,#0
.L78:
	add	d15,d15,#48
.L822:
	st.b	[a12],d15
.L823:
	add.a	a12,#1
.L824:
	add	d4,#-1
.L75:
	jne	d4,#0,.L76
.L505:
	jeq	d11,#0,.L79
.L506:
	mov	d4,#0
.L825:
	mov	d15,#46
.L826:
	st.b	[a12],d15
.L827:
	add.a	a12,#1
.L828:
	jne	d2,#0,.L80
.L829:
	mov	d15,#48
.L830:
	st.b	[a12],d15
.L831:
	j	.L81
.L80:
	j	.L82
.L83:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
.L832:
	mov	d15,#10
.L833:
	div	e0,d2,d15
.L834:
	st.w	[a15]32,d1
.L835:
	add	d15,d4,#1
	extr.u	d4,d15,#0,#8
.L836:
	mov	d15,#10
.L837:
	div	e2,d2,d15
.L507:
	add	d11,#-1
.L82:
	jne	d11,#0,.L83
.L508:
	j	.L84
.L85:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]32
	jlt	d15,#0,.L86
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]32
	j	.L87
.L86:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]32
	rsub	d15,#0
.L87:
	add	d15,d15,#48
.L838:
	st.b	[a12],d15
.L839:
	add.a	a12,#1
.L840:
	add	d4,#-1
.L84:
	jne	d4,#0,.L85
.L81:
.L79:
.L71:
.L67:
	ret
.L360:
	
__func_float_to_str_function_end:
	.size	func_float_to_str,__func_float_to_str_function_end-func_float_to_str
.L282:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_str_to_double',code,cluster('func_str_to_double')
	.sect	'.text.zf_common_function.func_str_to_double'
	.align	2
	
	.global	func_str_to_double
; Function func_str_to_double
.L226:
func_str_to_double:	.type	func
	sub.a	a10,#8
.L509:
	mov.aa	a15,a4
.L511:
	mov.a	a2,#0
	ne.a	d4,a15,a2
	movh.a	a4,#@his(.1.str)
.L510:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#389
	call	debug_assert_handler
.L375:
	mov	d8,#0
.L512:
	mov	d0,#0
	mov	d1,#0
.L845:
	mov	e10,d1,d0
.L846:
	mov	d12,#0
	mov	d13,#0
.L513:
	mov	d0,#0
	mov	d1,#0
.L514:
	addih	d1,d1,#16368
	st.d	[a10]0,e0
.L88:
	mov.a	a2,#0
.L847:
	jne.a	a2,a15,.L89
.L848:
	j	.L90
.L89:
	ld.b	d0,[a15]0
.L515:
	mov	d15,#45
.L849:
	jne	d15,d0,.L91
.L850:
	mov	d8,#1
.L851:
	add.a	a15,#1
.L852:
	j	.L92
.L91:
	ld.b	d0,[a15]0
.L853:
	mov	d15,#43
.L854:
	jne	d15,d0,.L93
.L855:
	add.a	a15,#1
.L93:
.L92:
	j	.L94
.L95:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L516:
	mov	e4,d11,d10
.L517:
	call	__d_mul
	mov	e10,d3,d2
.L856:
	ld.b	d15,[a15]0
.L857:
	add	d4,d15,#-48
	call	__d_itod
	mov	e6,d3,d2
.L858:
	mov	e4,d11,d10
	call	__d_add
	mov	e10,d3,d2
.L859:
	add.a	a15,#1
.L94:
	ld.b	d0,[a15]0
.L860:
	mov	d15,#48
.L861:
	jlt	d0,d15,.L96
.L862:
	ld.b	d0,[a15]0
.L863:
	mov	d15,#57
.L864:
	jge	d15,d0,.L95
.L96:
	ld.b	d0,[a15]0
.L865:
	mov	d15,#46
.L866:
	jne	d15,d0,.L97
.L867:
	add.a	a15,#1
.L868:
	j	.L98
.L99:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L518:
	mov	e4,d13,d12
.L519:
	call	__d_mul
	mov	e12,d3,d2
.L869:
	ld.b	d15,[a15]0
.L870:
	add	d4,d15,#-48
	call	__d_itod
	mov	e6,d3,d2
.L871:
	mov	e4,d13,d12
	call	__d_add
	mov	e12,d3,d2
.L872:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L873:
	ld.d	e4,[a10]0
.L520:
	call	__d_mul
.L521:
	st.d	[a10]0,e2
.L522:
	add.a	a15,#1
.L98:
	ld.b	d0,[a15]0
.L874:
	mov	d15,#48
.L875:
	jlt	d0,d15,.L100
.L876:
	ld.b	d0,[a15]0
.L877:
	mov	d15,#57
.L878:
	jlt	d15,d0,.L101
.L879:
	mov	d4,#0
	mov.u	d5,#52581
	addih	d5,d5,#16845
.L880:
	ld.d	e6,[a10]0
.L523:
	call	__d_fgt
.L524:
	mov	d15,d2
	jne	d15,#0,.L99
.L101:
.L100:
	mov	e4,d13,d12
.L525:
	ld.d	e6,[a10]0
.L526:
	call	__d_div
.L527:
	mov	e12,d3,d2
.L97:
	mov	e4,d11,d10
.L528:
	mov	e6,d13,d12
.L529:
	call	__d_add
	mov	e10,d3,d2
.L881:
	jeq	d8,#0,.L102
.L882:
	insn.t	d11,d11:31,d11:31
.L102:
.L90:
	mov	e2,d11,d10
.L530:
	j	.L103
.L103:
	ret
.L373:
	
__func_str_to_double_function_end:
	.size	func_str_to_double,__func_str_to_double_function_end-func_str_to_double
.L287:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_double_to_str',code,cluster('func_double_to_str')
	.sect	'.text.zf_common_function.func_double_to_str'
	.align	2
	
	.global	func_double_to_str
; Function func_double_to_str
.L228:
func_double_to_str:	.type	func
	sub.a	a10,#88
.L531:
	mov.aa	a12,a4
.L534:
	mov	e10,d5,d4
	mov	d9,d6
.L535:
	mov.a	a15,#0
	ne.a	d4,a12,a15
.L532:
	movh.a	a4,#@his(.1.str)
.L533:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#451
	call	debug_assert_handler
.L384:
	mov	d8,d9
.L104:
	mov.a	a15,#0
.L887:
	jne.a	a15,a12,.L105
.L888:
	j	.L106
.L105:
	mov	e4,d11,d10
.L536:
	call	__d_dtoi
.L537:
	mov	d12,d2
.L538:
	mov	d4,#0
	mov	d5,#0
.L539:
	mov	e6,d11,d10
.L540:
	call	__d_fgt
	mov	d15,d2
.L889:
	jeq	d15,#0,.L107
.L890:
	mov	d15,#45
.L891:
	st.b	[a12],d15
.L892:
	add.a	a12,#1
.L893:
	j	.L108
.L107:
	extr.u	d15,d11,#20,#11
	eq	d15,d15,#0
.L894:
	jeq	d15,#0,.L109
.L895:
	mov	d15,#48
.L896:
	st.b	[a12],d15
.L897:
	add.a	a12,#1
.L898:
	mov	d15,#46
.L899:
	st.b	[a12],d15
.L900:
	add.a	a12,#1
.L901:
	mov	d15,#48
.L902:
	st.b	[a12],d15
.L903:
	j	.L110
.L109:
.L108:
	mov	d4,d12
.L541:
	call	__d_itod
.L542:
	mov	e6,d3,d2
.L543:
	mov	e4,d11,d10
.L544:
	call	__d_sub
	mov	e4,d3,d2
.L904:
	j	.L111
.L112:
	add	d8,#-1
.L905:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L906:
	call	__d_mul
	mov	e4,d3,d2
.L111:
	jne	d8,#0,.L112
.L907:
	call	__d_dtoi
.L908:
	mov	d4,#0
.L113:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
.L909:
	mov	d15,#10
.L910:
	div	e0,d12,d15
.L911:
	st.w	[a15],d1
.L912:
	add	d15,d4,#1
	extr.u	d4,d15,#0,#8
.L913:
	mov	d15,#10
.L914:
	div	e12,d12,d15
.L915:
	jne	d12,#0,.L113
.L916:
	j	.L114
.L115:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d0,[a15]
	jlt	d0,#0,.L116
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]
	j	.L117
.L116:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]
	rsub	d15,#0
.L117:
	add	d15,d15,#48
.L917:
	st.b	[a12],d15
.L918:
	add.a	a12,#1
.L919:
	add	d4,#-1
.L114:
	jne	d4,#0,.L115
.L920:
	jeq	d9,#0,.L118
.L921:
	mov	d4,#0
.L922:
	mov	d15,#46
.L923:
	st.b	[a12],d15
.L924:
	add.a	a12,#1
.L545:
	jne	d2,#0,.L119
.L546:
	mov	d15,#48
.L925:
	st.b	[a12],d15
.L926:
	j	.L120
.L119:
	j	.L121
.L122:
	mul	d15,d4,#4
	addsc.a	a15,a10,d15,#0
.L927:
	mov	d15,#10
.L547:
	div	e0,d2,d15
.L548:
	st.w	[a15]48,d1
.L928:
	add	d15,d4,#1
	extr.u	d4,d15,#0,#8
.L929:
	mov	d15,#10
.L549:
	div	e2,d2,d15
.L930:
	add	d9,#-1
.L121:
	jne	d9,#0,.L122
.L931:
	j	.L123
.L124:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]48
	jlt	d15,#0,.L125
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]48
	j	.L126
.L125:
	add	d15,d4,#-1
	mul	d15,d15,#4
	addsc.a	a15,a10,d15,#0
	ld.w	d15,[a15]48
	rsub	d15,#0
.L126:
	add	d15,d15,#48
.L932:
	st.b	[a12],d15
.L933:
	add.a	a12,#1
.L934:
	add	d4,#-1
.L123:
	jne	d4,#0,.L124
.L120:
.L118:
.L110:
.L106:
	ret
.L380:
	
__func_double_to_str_function_end:
	.size	func_double_to_str,__func_double_to_str_function_end-func_double_to_str
.L292:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_str_to_hex',code,cluster('func_str_to_hex')
	.sect	'.text.zf_common_function.func_str_to_hex'
	.align	2
	
	.global	func_str_to_hex
; Function func_str_to_hex
.L230:
func_str_to_hex:	.type	func
	mov.aa	a15,a4
.L551:
	mov.a	a2,#0
	ne.a	d4,a15,a2
	movh.a	a4,#@his(.1.str)
.L550:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#534
	call	debug_assert_handler
.L394:
	mov.aa	a4,a15
.L552:
	call	strlen
.L553:
	mov	d1,#0
.L555:
	mov	d3,#0
.L127:
	mov.a	a2,#0
.L939:
	jne.a	a2,a15,.L128
.L940:
	j	.L129
.L128:
	jeq	d3,#0,.L130
.L941:
	ld.b	d0,[a15]0
.L942:
	mov	d15,#97
.L943:
	jlt	d0,d15,.L131
.L944:
	ld.b	d0,[a15]0
.L945:
	mov	d15,#102
.L946:
	jlt	d15,d0,.L132
.L947:
	ld.b	d0,[a15]0
.L948:
	add	d15,d0,#-87
.L557:
	j	.L133
.L132:
.L131:
	ld.b	d0,[a15]0
.L949:
	mov	d15,#65
.L950:
	jlt	d0,d15,.L134
.L951:
	ld.b	d0,[a15]0
.L952:
	mov	d15,#70
.L953:
	jlt	d15,d0,.L135
.L954:
	ld.b	d0,[a15]0
.L955:
	add	d15,d0,#-55
.L558:
	j	.L136
.L135:
.L134:
	ld.b	d0,[a15]0
.L956:
	mov	d15,#48
.L957:
	jlt	d0,d15,.L137
.L958:
	ld.b	d0,[a15]0
.L959:
	mov	d15,#57
.L960:
	jlt	d15,d0,.L138
.L961:
	ld.b	d15,[a15]0
.L962:
	add	d15,d15,#-48
.L559:
	j	.L139
.L138:
.L137:
	j	.L140
.L139:
.L136:
.L133:
	sh	d1,#4
.L556:
	and	d15,#15
.L560:
	or	d1,d15
.L963:
	j	.L141
.L130:
	ld.b	d0,[a15]0
.L964:
	mov	d15,#48
.L965:
	jne	d15,d0,.L142
.L966:
	ld.b	d0,[a15]1
.L967:
	mov	d15,#120
.L968:
	jne	d15,d0,.L143
.L969:
	add.a	a15,#1
.L970:
	mov	d3,#1
.L143:
.L142:
.L141:
	add.a	a15,#1
.L971:
	mov	d15,d2
	add	d2,#-1
.L972:
	jne	d15,#0,.L127
.L140:
.L129:
	mov	d2,d1
.L554:
	j	.L144
.L144:
	ret
.L392:
	
__func_str_to_hex_function_end:
	.size	func_str_to_hex,__func_str_to_hex_function_end-func_str_to_hex
.L297:
	; End of function
	
	.sdecl	'.text.zf_common_function.func_hex_to_str',code,cluster('func_hex_to_str')
	.sect	'.text.zf_common_function.func_hex_to_str'
	.align	2
	
	.global	func_hex_to_str
; Function func_hex_to_str
.L232:
func_hex_to_str:	.type	func
	sub.a	a10,#32
.L561:
	mov.aa	a12,a4
.L564:
	mov	d8,d4
.L565:
	mov.a	a15,#0
	ne.a	d4,a12,a15
.L562:
	movh.a	a4,#@his(.1.str)
.L563:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#592
	call	debug_assert_handler
.L402:
	movh.a	a15,#@his(.2.ini)
	lea	a15,[a15]@los(.2.ini)
	lea	a15,[a15]0
.L977:
	lea	a2,[a10]0
	mov.a	a4,#15
.L145:
	ld.bu	d15,[a15+]
	st.b	[a2+],d15
	loop	a4,.L145
.L978:
	mov	d15,#0
.L566:
	mov	d0,#48
.L979:
	st.b	[a12],d0
.L980:
	add.a	a12,#1
.L981:
	mov	d0,#120
.L982:
	st.b	[a12],d0
.L983:
	add.a	a12,#1
.L146:
	mov.a	a15,#0
.L984:
	jne.a	a15,a12,.L147
.L985:
	j	.L148
.L147:
	jne	d8,#0,.L149
.L986:
	mov	d15,#48
.L567:
	st.b	[a12],d15
.L987:
	j	.L150
.L149:
	j	.L151
.L152:
	addsc.a	a15,a10,d15,#0
.L988:
	and	d0,d8,#15
.L989:
	st.b	[a15]16,d0
.L990:
	add	d15,#1
.L568:
	extr.u	d15,d15,#0,#8
.L569:
	sh	d8,#-4
.L151:
	jne	d8,#0,.L152
.L991:
	j	.L153
.L154:
	addsc.a	a15,a10,d15,#0
	ld.b	d0,[a15]15
.L992:
	addsc.a	a15,a10,d0,#0
	ld.b	d0,[a15]0
.L993:
	st.b	[a12],d0
.L994:
	add.a	a12,#1
.L995:
	add	d15,#-1
.L153:
	jne	d15,#0,.L154
.L150:
.L148:
	ret
.L399:
	
__func_hex_to_str_function_end:
	.size	func_hex_to_str,__func_hex_to_str_function_end-func_hex_to_str
.L302:
	; End of function
	
	.sdecl	'.text.zf_common_function.number_conversion_ascii',code,cluster('number_conversion_ascii')
	.sect	'.text.zf_common_function.number_conversion_ascii'
	.align	2
	
; Function number_conversion_ascii
.L234:
number_conversion_ascii:	.type	func
	mov	d2,d4
.L570:
	mov	d15,#0
.L572:
	jeq	d5,#0,.L155
.L1101:
	jge	d2,#0,.L156
.L1102:
	rsub	d2,#0
.L156:
	j	.L157
.L158:
	div	e0,d2,d6
.L1103:
	add	d0,d1,#48
.L1104:
	st.b	[a4],d0
.L1105:
	div	e2,d2,d6
.L1106:
	add	d15,#1
.L573:
	extr.u	d15,d15,#0,#8
.L574:
	jne	d2,#0,.L159
.L1107:
	j	.L160
.L159:
	add.a	a4,#1
.L157:
	j	.L158
.L160:
	j	.L161
.L155:
	j	.L162
.L163:
	div.u	e0,d2,d6
.L576:
	jlt.u	d1,#10,.L164
.L1108:
	add	d0,d1,#55
	extr.u	d0,d0,#0,#8
.L577:
	j	.L165
.L164:
	add	d0,d1,#48
.L165:
	st.b	[a4],d0
.L1109:
	div.u	e0,d2,d6
.L578:
	mov	d2,d0
.L1110:
	add	d15,#1
.L575:
	extr.u	d15,d15,#0,#8
.L579:
	jne	d2,#0,.L166
.L1111:
	j	.L167
.L166:
	add.a	a4,#1
.L162:
	j	.L163
.L167:
.L161:
	mov	d2,d15
.L571:
	j	.L168
.L168:
	ret
.L447:
	
__number_conversion_ascii_function_end:
	.size	number_conversion_ascii,__number_conversion_ascii_function_end-number_conversion_ascii
.L312:
	; End of function
	
	.sdecl	'.text.zf_common_function.printf_reverse_order',code,cluster('printf_reverse_order')
	.sect	'.text.zf_common_function.printf_reverse_order'
	.align	2
	
; Function printf_reverse_order
.L236:
printf_reverse_order:	.type	func
	mov	d0,#0
.L580:
	j	.L169
.L170:
	add	d15,d4,#-1
.L1116:
	sub	d15,d0
.L1117:
	addsc.a	a15,a4,d15,#0
	ld.b	d1,[a15]0
.L581:
	add	d15,d4,#-1
.L1118:
	sub	d15,d0
.L1119:
	addsc.a	a15,a4,d15,#0
.L1120:
	addsc.a	a2,a4,d0,#0
	ld.b	d15,[a2]0
.L1121:
	st.b	[a15],d15
.L1122:
	addsc.a	a15,a4,d0,#0
.L1123:
	st.b	[a15],d1
.L1124:
	add	d0,#1
.L169:
	mov	d15,#2
.L1125:
	div.u	e2,d4,d15
.L1126:
	jlt.u	d0,d2,.L170
.L1127:
	ret
.L456:
	
__printf_reverse_order_function_end:
	.size	printf_reverse_order,__printf_reverse_order_function_end-printf_reverse_order
.L317:
	; End of function
	
	.sdecl	'.text.zf_common_function.zf_sprintf',code,cluster('zf_sprintf')
	.sect	'.text.zf_common_function.zf_sprintf'
	.align	2
	
	.global	zf_sprintf
; Function zf_sprintf
.L238:
zf_sprintf:	.type	func
	sub.a	a10,#40
.L582:
	mov.aa	a12,a4
.L583:
	mov.aa	a13,a5
.L584:
	mov	d8,#0
.L585:
	lea	a14,[a10]40
.L586:
	j	.L171
.L172:
	ld.b	d0,[a13]0
.L587:
	mov	d15,#37
.L1000:
	jne	d15,d0,.L173
.L1001:
	add.a	a13,#1
.L1002:
	ld.b	d0,[a13]0
.L588:
	mov	d15,#37
	jeq	d15,d0,.L174
.L1003:
	mov	d15,#70
	jeq	d15,d0,.L175
.L1004:
	mov	d15,#88
	jeq	d15,d0,.L176
.L1005:
	mov	d15,#97
	jeq	d15,d0,.L177
.L1006:
	mov	d15,#99
	jeq	d15,d0,.L178
.L1007:
	mov	d15,#100
	jeq	d15,d0,.L179
.L1008:
	mov	d15,#102
	jeq	d15,d0,.L180
.L1009:
	mov	d15,#105
	jeq	d15,d0,.L181
.L1010:
	mov	d15,#111
	jeq	d15,d0,.L182
.L1011:
	mov	d15,#112
	jeq	d15,d0,.L183
.L1012:
	mov	d15,#115
	jeq	d15,d0,.L184
.L1013:
	mov	d15,#117
	jeq	d15,d0,.L185
.L1014:
	mov	d15,#120
	jeq	d15,d0,.L186
	j	.L187
.L177:
	j	.L188
.L178:
	add.a	a14,#4
	ld.w	d15,[a14]-4
.L1015:
	extr	d15,d15,#0,#8
.L589:
	st.b	[a12],d15
.L1016:
	add.a	a12,#1
.L1017:
	add	d8,#1
.L418:
	j	.L189
.L179:
.L181:
	add.a	a14,#4
	ld.w	d9,[a14]-4
.L590:
	lea	a4,[a10]0
.L1018:
	mov	d5,#1
.L1019:
	mov	d6,#10
	mov	d4,d9
.L591:
	call	number_conversion_ascii
.L592:
	mov	d15,d2
.L593:
	jge	d9,#0,.L190
.L1020:
	addsc.a	a15,a10,d15,#0
.L1021:
	mov	d0,#45
.L1022:
	st.b	[a15],d0
.L1023:
	add	d15,#1
.L594:
	extr.u	d15,d15,#0,#8
.L190:
	lea	a4,[a10]0
.L1024:
	mov	d4,d15
.L595:
	call	printf_reverse_order
.L596:
	lea	a5,[a10]0
.L1025:
	mov.aa	a4,a12
.L597:
	mov	d4,d15
.L599:
	call	memcpy
.L598:
	addsc.a	a12,a12,d15,#0
.L1026:
	add	d8,d15
.L420:
	j	.L191
.L180:
.L175:
	lea	a14,[a14]8
	ld.d	e10,[a14]-8
.L600:
	mov	e4,d11,d10
	call	__d_dtoi
	mov	d4,d2
.L1027:
	lea	a4,[a10]0
.L1028:
	mov	d5,#1
.L1029:
	mov	d15,#10
	mov	d6,d15
	call	number_conversion_ascii
.L601:
	mov	d15,d2
.L603:
	mov	d4,#0
	mov	d5,#0
.L1030:
	mov	e6,d11,d10
	call	__d_fgt
.L602:
	jeq	d2,#0,.L192
.L1031:
	addsc.a	a15,a10,d15,#0
.L1032:
	mov	d0,#45
.L1033:
	st.b	[a15],d0
.L1034:
	add	d15,#1
.L604:
	extr.u	d15,d15,#0,#8
.L192:
	lea	a4,[a10]0
.L1035:
	mov	d4,d15
.L606:
	call	printf_reverse_order
.L607:
	lea	a5,[a10]0
.L1036:
	mov.aa	a4,a12
.L608:
	mov	d4,d15
.L610:
	call	memcpy
.L609:
	addsc.a	a12,a12,d15,#0
.L1037:
	add	d8,d15
.L1038:
	mov	e4,d11,d10
	call	__d_dtoi
	mov	d4,d2
	call	__d_itod
	mov	e6,d3,d2
.L1039:
	mov	e4,d11,d10
	call	__d_sub
	mov	e4,d3,d2
.L1040:
	mov	d6,#0
	mov.u	d7,#33920
	addih	d7,d7,#16686
.L1041:
	call	__d_mul
	mov	e4,d3,d2
.L1042:
	extr.u	d15,d5,#20,#11
.L605:
	ne	d15,d15,#0
	jne	d15,#0,.L193
.L1043:
	mov	d15,#48
	st.b	[a10]5,d15
.L1044:
	st.b	[a10]4,d15
.L1045:
	st.b	[a10]3,d15
.L1046:
	st.b	[a10]2,d15
.L1047:
	st.b	[a10]1,d15
.L1048:
	st.b	[a10],d15
.L1049:
	mov	d2,#6
.L611:
	j	.L194
.L193:
	call	__d_dtoi
.L1050:
	lea	a4,[a10]0
.L1051:
	mov	d5,#1
.L1052:
	mov	d6,#10
	mov	d4,d2
	call	number_conversion_ascii
.L194:
	j	.L195
.L196:
	addsc.a	a15,a10,d2,#0
.L1053:
	mov	d15,#48
.L1054:
	st.b	[a15],d15
.L1055:
	add	d2,#1
.L195:
	jlt.u	d2,#6,.L196
.L1056:
	addsc.a	a15,a10,d2,#0
.L1057:
	mov	d15,#46
.L1058:
	st.b	[a15],d15
.L1059:
	add	d2,#1
.L612:
	extr.u	d15,d2,#0,#8
.L613:
	lea	a4,[a10]0
.L1060:
	mov	d4,d15
.L614:
	call	printf_reverse_order
.L615:
	lea	a5,[a10]0
.L1061:
	mov.aa	a4,a12
.L616:
	mov	d4,d15
.L618:
	call	memcpy
.L617:
	add	d8,d15
.L425:
	j	.L197
.L185:
	add.a	a14,#4
	ld.w	d4,[a14]-4
.L619:
	lea	a4,[a10]0
.L1062:
	mov	d5,#0
.L1063:
	mov	d6,#10
	call	number_conversion_ascii
.L620:
	mov	d15,d2
.L622:
	lea	a4,[a10]0
.L1064:
	mov	d4,d15
.L621:
	call	printf_reverse_order
.L623:
	lea	a5,[a10]0
.L1065:
	mov.aa	a4,a12
.L624:
	mov	d4,d15
.L626:
	call	memcpy
.L625:
	addsc.a	a12,a12,d15,#0
.L1066:
	add	d8,d15
.L429:
	j	.L198
.L182:
	add.a	a14,#4
	ld.w	d4,[a14]-4
.L627:
	lea	a4,[a10]0
.L1067:
	mov	d5,#0
.L1068:
	mov	d6,#8
	call	number_conversion_ascii
.L628:
	mov	d15,d2
.L630:
	lea	a4,[a10]0
.L1069:
	mov	d4,d15
.L629:
	call	printf_reverse_order
.L631:
	lea	a5,[a10]0
.L1070:
	mov.aa	a4,a12
.L632:
	mov	d4,d15
.L634:
	call	memcpy
.L633:
	addsc.a	a12,a12,d15,#0
.L1071:
	add	d8,d15
.L433:
	j	.L199
.L186:
.L176:
	add.a	a14,#4
	ld.w	d4,[a14]-4
.L635:
	lea	a4,[a10]0
.L1072:
	mov	d5,#0
.L1073:
	mov	d6,#16
	call	number_conversion_ascii
.L636:
	mov	d15,d2
.L638:
	lea	a4,[a10]0
.L1074:
	mov	d4,d15
.L637:
	call	printf_reverse_order
.L639:
	lea	a5,[a10]0
.L1075:
	mov.aa	a4,a12
.L640:
	mov	d4,d15
.L642:
	call	memcpy
.L641:
	addsc.a	a12,a12,d15,#0
.L1076:
	add	d8,d15
.L437:
	j	.L200
.L184:
	add.a	a14,#4
	ld.a	a15,[a14]-4
.L643:
	j	.L201
.L202:
	ld.b	d15,[a15]0
.L1077:
	st.b	[a12],d15
.L1078:
	add.a	a12,#1
.L1079:
	add	d8,#1
.L1080:
	add.a	a15,#1
.L201:
	ld.b	d15,[a15]0
.L1081:
	jne	d15,#0,.L202
.L441:
	j	.L203
.L183:
	add.a	a14,#4
	ld.w	d4,[a14]-4
.L644:
	lea	a4,[a10]0
.L1082:
	mov	d5,#0
.L1083:
	mov	d6,#16
	call	number_conversion_ascii
.L645:
	lea	a4,[a10]0
.L1084:
	mov	d4,#8
	call	printf_reverse_order
.L1085:
	lea	a5,[a10]0
.L1086:
	mov	d4,#8
	mov.aa	a4,a12
.L646:
	call	memcpy
.L647:
	lea	a12,[a12]8
.L1087:
	add	d8,d8,#8
.L443:
	j	.L204
.L174:
	mov	d15,#37
.L1088:
	st.b	[a12],d15
.L1089:
	add.a	a12,#1
.L1090:
	add	d8,#1
.L1091:
	j	.L205
.L187:
	j	.L206
.L206:
.L205:
.L204:
.L203:
.L200:
.L199:
.L198:
.L197:
.L191:
.L189:
.L188:
	j	.L207
.L173:
	ld.b	d15,[a13]0
.L1092:
	st.b	[a12],d15
.L1093:
	add.a	a12,#1
.L1094:
	add	d8,#1
.L207:
	add.a	a13,#1
.L171:
	ld.b	d15,[a13]0
.L1095:
	jne	d15,#0,.L172
.L1096:
	mov	d2,d8
.L648:
	j	.L208
.L208:
	ret
.L408:
	
__zf_sprintf_function_end:
	.size	zf_sprintf,__zf_sprintf_function_end-zf_sprintf
.L307:
	; End of function
	
	.sdecl	'.rodata.zf_common_function..1.str',data,rom
	.sect	'.rodata.zf_common_function..1.str'
.1.str:	.type	object
	.size	.1.str,44
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	99,111,109,109,111,110,47,122
	.byte	102,95,99,111,109,109,111,110
	.byte	95,102,117,110,99,116,105,111
	.byte	110,46,99
	.space	1
	.sdecl	'.rodata.zf_common_function..2.ini',data,rom
	.sect	'.rodata.zf_common_function..2.ini'
.2.ini:	.type	object
	.size	.2.ini,16
	.byte	48,49,50,51,52,53,54,55
	.byte	56,57,65,66,67,68,69,70
	.calls	'func_str_to_float','__f_ftod'
	.calls	'func_str_to_float','__d_fgt'
	.calls	'func_float_to_str','__f_ftod'
	.calls	'func_str_to_double','__d_mul'
	.calls	'func_str_to_double','__d_itod'
	.calls	'func_str_to_double','__d_add'
	.calls	'func_str_to_double','__d_fgt'
	.calls	'func_str_to_double','__d_div'
	.calls	'func_double_to_str','__d_dtoi'
	.calls	'func_double_to_str','__d_fgt'
	.calls	'func_double_to_str','__d_itod'
	.calls	'func_double_to_str','__d_sub'
	.calls	'func_double_to_str','__d_mul'
	.calls	'zf_sprintf','__d_dtoi'
	.calls	'zf_sprintf','__d_fgt'
	.calls	'zf_sprintf','__d_itod'
	.calls	'zf_sprintf','__d_sub'
	.calls	'zf_sprintf','__d_mul'
	.calls	'func_str_to_int','debug_assert_handler'
	.calls	'func_int_to_str','debug_assert_handler'
	.calls	'func_str_to_uint','debug_assert_handler'
	.calls	'func_uint_to_str','debug_assert_handler'
	.calls	'func_str_to_float','debug_assert_handler'
	.calls	'func_float_to_str','debug_assert_handler'
	.calls	'func_str_to_double','debug_assert_handler'
	.calls	'func_double_to_str','debug_assert_handler'
	.calls	'func_str_to_hex','debug_assert_handler'
	.calls	'func_str_to_hex','strlen'
	.calls	'func_hex_to_str','debug_assert_handler'
	.calls	'zf_sprintf','number_conversion_ascii'
	.calls	'zf_sprintf','printf_reverse_order'
	.calls	'zf_sprintf','memcpy'
	.calls	'func_get_greatest_common_divisor','',0
	.calls	'func_soft_delay','',0
	.calls	'func_str_to_int','',0
	.calls	'func_int_to_str','',16
	.calls	'func_str_to_uint','',0
	.calls	'func_uint_to_str','',16
	.calls	'func_str_to_float','',0
	.calls	'func_float_to_str','',56
	.calls	'func_str_to_double','',8
	.calls	'func_double_to_str','',88
	.calls	'func_str_to_hex','',0
	.calls	'func_hex_to_str','',32
	.calls	'number_conversion_ascii','',0
	.calls	'printf_reverse_order','',0
	.extern	strlen
	.extern	memcpy
	.extern	debug_assert_handler
	.extern	__f_ftod
	.extern	__d_fgt
	.extern	__d_mul
	.extern	__d_itod
	.extern	__d_add
	.extern	__d_div
	.extern	__d_dtoi
	.extern	__d_sub
	.calls	'zf_sprintf','',40
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L240:
	.word	1093
	.half	3
	.word	.L241
	.byte	4
.L239:
	.byte	1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L242
	.byte	2
	.byte	'void',0,3
	.word	206
	.byte	4
	.byte	'__dotdotdot__',0
	.word	212
	.byte	1,1,1,1,5
	.byte	'unsigned int',0,4,7,5
	.byte	'char',0,1,6,6
	.word	256
	.byte	3
	.word	264
	.byte	7
	.byte	'strlen',0,1,52,17
	.word	240
	.byte	1,1,1,1,8,1,52,39
	.word	269
	.byte	0,9
	.word	212
	.byte	6
	.word	206
	.byte	3
	.word	307
	.byte	9
	.word	312
	.byte	7
	.byte	'memcpy',0,1,53,17
	.word	212
	.byte	1,1,1,1,8,1,53,33
	.word	302
	.byte	8,1,53,56
	.word	317
	.byte	8,1,53,68
	.word	240
	.byte	0
.L330:
	.byte	5
	.byte	'unsigned char',0,1,8
.L327:
	.byte	3
	.word	256
.L325:
	.byte	5
	.byte	'int',0,4,5,10
	.byte	'debug_assert_handler',0,2,112,9,1,1,1,1,11
	.byte	'pass',0,2,112,47
	.word	366
	.byte	11
	.byte	'file',0,2,112,59
	.word	383
	.byte	11
	.byte	'line',0,2,112,69
	.word	388
	.byte	0
.L318:
	.byte	5
	.byte	'unsigned long int',0,4,7,5
	.byte	'long int',0,4,5
.L323:
	.byte	12
	.word	485
.L337:
	.byte	13,16
	.word	366
	.byte	14,15,0
.L416:
	.byte	5
	.byte	'char',0,1,6
.L349:
	.byte	13,16
	.word	511
	.byte	14,15,0
.L352:
	.byte	5
	.byte	'float',0,4,4
.L367:
	.byte	13,32
	.word	388
	.byte	14,7,0
.L369:
	.byte	13,24
	.word	388
	.byte	14,5,0
.L372:
	.byte	5
	.byte	'double',0,8,4
.L387:
	.byte	13,48
	.word	388
	.byte	14,11,0
.L389:
	.byte	13,36
	.word	388
	.byte	14,8,0,13,16
	.word	256
	.byte	14,15,0
.L403:
	.byte	6
	.word	583
.L405:
	.byte	13,12
	.word	511
	.byte	14,11,0
.L409:
	.byte	3
	.word	511
	.byte	6
	.word	511
.L411:
	.byte	3
	.word	611
	.byte	3
	.word	256
.L414:
	.byte	15
	.byte	'va_list',0,3,24,16
	.word	383
.L421:
	.byte	13,33
	.word	511
	.byte	14,32,0,5
	.byte	'short int',0,2,5,15
	.byte	'__wchar_t',0,4,1,1
	.word	651
	.byte	15
	.byte	'__size_t',0,4,1,1
	.word	240
	.byte	15
	.byte	'__ptrdiff_t',0,4,1,1
	.word	388
	.byte	16,1,3
	.word	719
	.byte	15
	.byte	'__codeptr',0,4,1,1
	.word	721
	.byte	15
	.byte	'__intptr_t',0,4,1,1
	.word	388
	.byte	15
	.byte	'__uintptr_t',0,4,1,1
	.word	240
	.byte	15
	.byte	'size_t',0,5,31,25
	.word	240
	.byte	5
	.byte	'unsigned short int',0,2,7,15
	.byte	'_iob_flag_t',0,5,82,25
	.word	798
	.byte	15
	.byte	'uint8',0,6,105,29
	.word	366
	.byte	15
	.byte	'uint16',0,6,109,29
	.word	798
	.byte	15
	.byte	'uint32',0,6,113,29
	.word	464
	.byte	5
	.byte	'unsigned long long int',0,8,7,15
	.byte	'uint64',0,6,118,29
	.word	884
	.byte	15
	.byte	'sint16',0,6,126,29
	.word	651
	.byte	15
	.byte	'sint32',0,6,131,1,29
	.word	485
	.byte	5
	.byte	'long long int',0,8,5,15
	.byte	'sint64',0,6,138,1,29
	.word	956
	.byte	15
	.byte	'float32',0,6,167,1,29
	.word	528
	.byte	15
	.byte	'pvoid',0,7,57,28
	.word	212
	.byte	15
	.byte	'Ifx_TickTime',0,7,79,28
	.word	956
	.byte	15
	.byte	'int8',0,8,54,29
	.word	511
	.byte	15
	.byte	'int16',0,8,55,29
	.word	651
	.byte	15
	.byte	'int32',0,8,56,29
	.word	388
	.byte	15
	.byte	'int64',0,8,57,29
	.word	956
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,46,0,3,8,73,19,54,15,39
	.byte	12,63,12,60,12,0,0,5,36,0,3,8,11,15,62,15,0,0,6,38,0,73,19,0,0,7,46,1,3,8,58,15,59,15,57,15,73,19,54,15
	.byte	39,12,63,12,60,12,0,0,8,5,0,58,15,59,15,57,15,73,19,0,0,9,55,0,73,19,0,0,10,46,1,3,8,58,15,59,15,57,15
	.byte	54,15,39,12,63,12,60,12,0,0,11,5,0,3,8,58,15,59,15,57,15,73,19,0,0,12,53,0,73,19,0,0,13,1,1,11,15,73,19
	.byte	0,0,14,33,0,47,15,0,0,15,22,0,3,8,58,15,59,15,57,15,73,19,0,0,16,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L242:
	.word	.L650-.L649
.L649:
	.half	3
	.word	.L652-.L651
.L651:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'string.h',0,1,0,0
	.byte	'..\\libraries\\zf_common\\zf_common_debug.h',0,0,0,0
	.byte	'stdarg.h',0,1,0,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0
	.byte	'stdio.h',0,1,0,0
	.byte	'Platform_Types.h',0,2,0,0
	.byte	'ifx_types.h',0,2,0,0
	.byte	'..\\libraries\\zf_common\\zf_common_typedef.h',0,0,0,0,0
.L652:
.L650:
	.sdecl	'.debug_info',debug,cluster('func_get_greatest_common_divisor')
	.sect	'.debug_info'
.L243:
	.word	312
	.half	3
	.word	.L244
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L246,.L245
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_get_greatest_common_divisor',0,1,47,8
	.word	.L318
	.byte	1,1,1
	.word	.L210,.L319,.L209
	.byte	4
	.byte	'num1',0,1,47,49
	.word	.L318,.L320
	.byte	4
	.byte	'num2',0,1,47,62
	.word	.L318,.L321
	.byte	5
	.word	.L210,.L319
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_get_greatest_common_divisor')
	.sect	'.debug_abbrev'
.L244:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_get_greatest_common_divisor')
	.sect	'.debug_line'
.L245:
	.word	.L654-.L653
.L653:
	.half	3
	.word	.L656-.L655
.L655:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L656:
	.byte	5,23,7,0,5,2
	.word	.L210
	.byte	3,48,1,5,9,9
	.half	.L3-.L210
	.byte	3,2,1,5,25,7,9
	.half	.L657-.L3
	.byte	3,2,1,5,9,9
	.half	.L4-.L657
	.byte	3,2,1,5,25,7,9
	.half	.L658-.L4
	.byte	3,2,1,5,23,9
	.half	.L2-.L658
	.byte	3,120,1,5,5,7,9
	.half	.L659-.L2
	.byte	3,11,1,5,1,9
	.half	.L6-.L659
	.byte	3,1,1,7,9
	.half	.L247-.L6
	.byte	0,1,1
.L654:
	.sdecl	'.debug_ranges',debug,cluster('func_get_greatest_common_divisor')
	.sect	'.debug_ranges'
.L246:
	.word	-1,.L210,0,.L247-.L210,0,0
	.sdecl	'.debug_info',debug,cluster('func_soft_delay')
	.sect	'.debug_info'
.L248:
	.word	271
	.half	3
	.word	.L249
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L251,.L250
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_soft_delay',0,1,70,6,1,1,1
	.word	.L212,.L322,.L211
	.byte	4
	.byte	't',0,1,70,37
	.word	.L323,.L324
	.byte	5
	.word	.L212,.L322
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_soft_delay')
	.sect	'.debug_abbrev'
.L249:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_soft_delay')
	.sect	'.debug_line'
.L250:
	.word	.L661-.L660
.L660:
	.half	3
	.word	.L663-.L662
.L662:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L663:
	.byte	5,15,7,0,5,2
	.word	.L212
	.byte	3,199,0,1,5,13,9
	.half	.L7-.L212
	.byte	1,5,15,9
	.half	.L664-.L7
	.byte	1,5,1,7,9
	.half	.L665-.L664
	.byte	3,1,1,7,9
	.half	.L252-.L665
	.byte	0,1,1
.L661:
	.sdecl	'.debug_ranges',debug,cluster('func_soft_delay')
	.sect	'.debug_ranges'
.L251:
	.word	-1,.L212,0,.L252-.L212,0,0
	.sdecl	'.debug_info',debug,cluster('func_str_to_int')
	.sect	'.debug_info'
.L253:
	.word	322
	.half	3
	.word	.L254
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L256,.L255
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_str_to_int',0,1,82,7
	.word	.L325
	.byte	1,1,1
	.word	.L214,.L326,.L213
	.byte	4
	.byte	'str',0,1,82,30
	.word	.L327,.L328
	.byte	5
	.word	.L214,.L326
	.byte	5
	.word	.L329,.L326
	.byte	6
	.byte	'sign',0,1,85,11
	.word	.L330,.L331
	.byte	6
	.byte	'temp',0,1,86,11
	.word	.L325,.L332
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_str_to_int')
	.sect	'.debug_abbrev'
.L254:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_str_to_int')
	.sect	'.debug_line'
.L255:
	.word	.L667-.L666
.L666:
	.half	3
	.word	.L669-.L668
.L668:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L669:
	.byte	5,7,7,0,5,2
	.word	.L214
	.byte	3,209,0,1,5,5,9
	.half	.L463-.L214
	.byte	3,2,1,5,16,9
	.half	.L329-.L463
	.byte	3,1,1,9
	.half	.L464-.L329
	.byte	3,1,1,5,12,9
	.half	.L9-.L464
	.byte	3,3,1,5,9,9
	.half	.L670-.L9
	.byte	1,5,13,7,9
	.half	.L671-.L670
	.byte	3,2,1,5,19,9
	.half	.L10-.L671
	.byte	3,3,1,5,12,9
	.half	.L672-.L10
	.byte	1,5,9,9
	.half	.L673-.L672
	.byte	1,5,18,7,9
	.half	.L674-.L673
	.byte	3,2,1,5,17,9
	.half	.L675-.L674
	.byte	3,1,1,5,21,9
	.half	.L676-.L675
	.byte	3,127,1,5,24,9
	.half	.L12-.L676
	.byte	3,3,1,5,17,9
	.half	.L677-.L12
	.byte	1,5,14,9
	.half	.L678-.L677
	.byte	1,5,17,7,9
	.half	.L679-.L678
	.byte	3,2,1,5,45,9
	.half	.L13-.L679
	.byte	3,3,1,5,27,9
	.half	.L16-.L13
	.byte	3,2,1,5,41,9
	.half	.L680-.L16
	.byte	1,5,47,9
	.half	.L681-.L680
	.byte	1,5,30,9
	.half	.L682-.L681
	.byte	1,5,17,9
	.half	.L683-.L682
	.byte	3,1,1,5,23,9
	.half	.L15-.L683
	.byte	3,125,1,5,16,9
	.half	.L684-.L15
	.byte	1,5,15,9
	.half	.L685-.L684
	.byte	1,5,40,7,9
	.half	.L686-.L685
	.byte	1,5,33,9
	.half	.L687-.L686
	.byte	1,5,37,9
	.half	.L688-.L687
	.byte	1,5,9,7,9
	.half	.L17-.L688
	.byte	3,6,1,5,20,7,9
	.half	.L689-.L17
	.byte	3,2,1,5,5,9
	.half	.L11-.L689
	.byte	3,3,1,5,1,9
	.half	.L19-.L11
	.byte	3,1,1,7,9
	.half	.L257-.L19
	.byte	0,1,1
.L667:
	.sdecl	'.debug_ranges',debug,cluster('func_str_to_int')
	.sect	'.debug_ranges'
.L256:
	.word	-1,.L214,0,.L257-.L214,0,0
	.sdecl	'.debug_info',debug,cluster('func_int_to_str')
	.sect	'.debug_info'
.L258:
	.word	368
	.half	3
	.word	.L259
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L261,.L260
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_int_to_str',0,1,126,6,1,1,1
	.word	.L216,.L333,.L215
	.byte	4
	.byte	'str',0,1,126,29
	.word	.L327,.L334
	.byte	4
	.byte	'number',0,1,126,40
	.word	.L325,.L335
	.byte	5
	.word	.L216,.L333
	.byte	5
	.word	.L336,.L333
	.byte	6
	.byte	'data_temp',0,1,129,1,11
	.word	.L337,.L338
	.byte	6
	.byte	'bit',0,1,130,1,11
	.word	.L330,.L339
	.byte	6
	.byte	'number_temp',0,1,131,1,11
	.word	.L325,.L340
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_int_to_str')
	.sect	'.debug_abbrev'
.L259:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_int_to_str')
	.sect	'.debug_line'
.L260:
	.word	.L691-.L690
.L690:
	.half	3
	.word	.L693-.L692
.L692:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L693:
	.byte	5,6,7,0,5,2
	.word	.L216
	.byte	3,253,0,1,5,5,9
	.half	.L469-.L216
	.byte	3,2,1,5,15,9
	.half	.L336-.L469
	.byte	3,2,1,5,12,9
	.half	.L20-.L336
	.byte	3,5,1,5,9,9
	.half	.L694-.L20
	.byte	1,5,13,7,9
	.half	.L695-.L694
	.byte	3,2,1,5,9,9
	.half	.L21-.L695
	.byte	3,3,1,5,23,7,9
	.half	.L696-.L21
	.byte	3,2,1,5,21,9
	.half	.L697-.L696
	.byte	1,5,18,9
	.half	.L698-.L697
	.byte	1,5,22,9
	.half	.L699-.L698
	.byte	3,1,1,5,26,9
	.half	.L700-.L699
	.byte	3,127,1,5,14,9
	.half	.L23-.L700
	.byte	3,3,1,5,20,7,9
	.half	.L701-.L23
	.byte	3,2,1,5,18,9
	.half	.L470-.L701
	.byte	1,5,13,9
	.half	.L702-.L470
	.byte	3,1,1,5,26,9
	.half	.L24-.L702
	.byte	3,3,1,5,36,9
	.half	.L28-.L24
	.byte	3,2,1,5,34,9
	.half	.L703-.L28
	.byte	1,5,33,9
	.half	.L472-.L703
	.byte	3,1,1,7,9
	.half	.L704-.L472
	.byte	1,5,22,9
	.half	.L30-.L704
	.byte	1,5,31,9
	.half	.L705-.L30
	.byte	1,5,27,9
	.half	.L706-.L705
	.byte	1,5,23,9
	.half	.L473-.L706
	.byte	3,1,1,5,20,9
	.half	.L707-.L473
	.byte	1,5,26,9
	.half	.L27-.L707
	.byte	3,124,1,5,23,7,9
	.half	.L708-.L27
	.byte	3,6,1,5,33,9
	.half	.L32-.L708
	.byte	3,2,1,5,43,9
	.half	.L709-.L32
	.byte	1,5,21,9
	.half	.L710-.L709
	.byte	1,5,18,9
	.half	.L711-.L710
	.byte	1,5,17,9
	.half	.L712-.L711
	.byte	3,1,1,5,23,9
	.half	.L31-.L712
	.byte	3,125,1,5,1,7,9
	.half	.L22-.L31
	.byte	3,6,1,7,9
	.half	.L262-.L22
	.byte	0,1,1
.L691:
	.sdecl	'.debug_ranges',debug,cluster('func_int_to_str')
	.sect	'.debug_ranges'
.L261:
	.word	-1,.L216,0,.L262-.L216,0,0
	.sdecl	'.debug_info',debug,cluster('func_str_to_uint')
	.sect	'.debug_info'
.L263:
	.word	309
	.half	3
	.word	.L264
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L266,.L265
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_str_to_uint',0,1,172,1,8
	.word	.L318
	.byte	1,1,1
	.word	.L218,.L341,.L217
	.byte	4
	.byte	'str',0,1,172,1,32
	.word	.L327,.L342
	.byte	5
	.word	.L218,.L341
	.byte	5
	.word	.L343,.L341
	.byte	6
	.byte	'temp',0,1,175,1,12
	.word	.L318,.L344
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_str_to_uint')
	.sect	'.debug_abbrev'
.L264:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_str_to_uint')
	.sect	'.debug_line'
.L265:
	.word	.L714-.L713
.L713:
	.half	3
	.word	.L716-.L715
.L715:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L716:
	.byte	5,8,7,0,5,2
	.word	.L218
	.byte	3,171,1,1,5,5,9
	.half	.L475-.L218
	.byte	3,2,1,5,17,9
	.half	.L343-.L475
	.byte	3,1,1,5,12,9
	.half	.L33-.L343
	.byte	3,4,1,5,9,9
	.half	.L717-.L33
	.byte	1,5,13,7,9
	.half	.L718-.L717
	.byte	3,2,1,5,45,9
	.half	.L34-.L718
	.byte	3,3,1,5,27,9
	.half	.L37-.L34
	.byte	3,2,1,5,41,9
	.half	.L719-.L37
	.byte	1,5,47,9
	.half	.L720-.L719
	.byte	1,5,30,9
	.half	.L721-.L720
	.byte	1,5,17,9
	.half	.L722-.L721
	.byte	3,1,1,5,23,9
	.half	.L36-.L722
	.byte	3,125,1,5,16,9
	.half	.L723-.L36
	.byte	1,5,15,9
	.half	.L724-.L723
	.byte	1,5,40,7,9
	.half	.L725-.L724
	.byte	1,5,33,9
	.half	.L726-.L725
	.byte	1,5,37,9
	.half	.L727-.L726
	.byte	1,5,5,7,9
	.half	.L35-.L727
	.byte	3,7,1,5,1,9
	.half	.L39-.L35
	.byte	3,1,1,7,9
	.half	.L267-.L39
	.byte	0,1,1
.L714:
	.sdecl	'.debug_ranges',debug,cluster('func_str_to_uint')
	.sect	'.debug_ranges'
.L266:
	.word	-1,.L218,0,.L267-.L218,0,0
	.sdecl	'.debug_info',debug,cluster('func_uint_to_str')
	.sect	'.debug_info'
.L268:
	.word	347
	.half	3
	.word	.L269
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L271,.L270
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_uint_to_str',0,1,202,1,6,1,1,1
	.word	.L220,.L345,.L219
	.byte	4
	.byte	'str',0,1,202,1,30
	.word	.L327,.L346
	.byte	4
	.byte	'number',0,1,202,1,42
	.word	.L318,.L347
	.byte	5
	.word	.L220,.L345
	.byte	5
	.word	.L348,.L345
	.byte	6
	.byte	'data_temp',0,1,205,1,10
	.word	.L349,.L350
	.byte	6
	.byte	'bit',0,1,206,1,11
	.word	.L330,.L351
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_uint_to_str')
	.sect	'.debug_abbrev'
.L269:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_uint_to_str')
	.sect	'.debug_line'
.L270:
	.word	.L729-.L728
.L728:
	.half	3
	.word	.L731-.L730
.L730:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L731:
	.byte	5,6,7,0,5,2
	.word	.L220
	.byte	3,201,1,1,5,5,9
	.half	.L480-.L220
	.byte	3,2,1,5,15,9
	.half	.L348-.L480
	.byte	3,2,1,5,12,9
	.half	.L40-.L348
	.byte	3,4,1,5,9,9
	.half	.L732-.L40
	.byte	1,5,13,7,9
	.half	.L733-.L732
	.byte	3,2,1,5,9,9
	.half	.L41-.L733
	.byte	3,3,1,5,20,7,9
	.half	.L734-.L41
	.byte	3,2,1,5,18,9
	.half	.L481-.L734
	.byte	1,5,13,9
	.half	.L735-.L481
	.byte	3,1,1,5,26,9
	.half	.L43-.L735
	.byte	3,3,1,5,22,9
	.half	.L46-.L43
	.byte	3,2,1,5,43,9
	.half	.L736-.L46
	.byte	1,5,41,9
	.half	.L737-.L736
	.byte	1,5,31,9
	.half	.L738-.L737
	.byte	1,5,27,9
	.half	.L739-.L738
	.byte	1,5,23,9
	.half	.L483-.L739
	.byte	3,1,1,5,20,9
	.half	.L740-.L483
	.byte	1,5,26,9
	.half	.L45-.L740
	.byte	3,125,1,5,23,7,9
	.half	.L741-.L45
	.byte	3,5,1,5,33,9
	.half	.L48-.L741
	.byte	3,2,1,5,43,9
	.half	.L742-.L48
	.byte	1,5,21,9
	.half	.L743-.L742
	.byte	1,5,18,9
	.half	.L744-.L743
	.byte	1,5,17,9
	.half	.L745-.L744
	.byte	3,1,1,5,23,9
	.half	.L47-.L745
	.byte	3,125,1,5,1,7,9
	.half	.L42-.L47
	.byte	3,6,1,7,9
	.half	.L272-.L42
	.byte	0,1,1
.L729:
	.sdecl	'.debug_ranges',debug,cluster('func_uint_to_str')
	.sect	'.debug_ranges'
.L271:
	.word	-1,.L220,0,.L272-.L220,0,0
	.sdecl	'.debug_info',debug,cluster('func_str_to_float')
	.sect	'.debug_info'
.L273:
	.word	375
	.half	3
	.word	.L274
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L276,.L275
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_str_to_float',0,1,241,1,7
	.word	.L352
	.byte	1,1,1
	.word	.L222,.L353,.L221
	.byte	4
	.byte	'str',0,1,241,1,32
	.word	.L327,.L354
	.byte	5
	.word	.L222,.L353
	.byte	5
	.word	.L355,.L353
	.byte	6
	.byte	'sign',0,1,244,1,11
	.word	.L330,.L356
	.byte	6
	.byte	'temp',0,1,245,1,11
	.word	.L352,.L357
	.byte	6
	.byte	'temp_point',0,1,246,1,11
	.word	.L352,.L358
	.byte	6
	.byte	'point_bit',0,1,247,1,11
	.word	.L352,.L359
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_str_to_float')
	.sect	'.debug_abbrev'
.L274:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_str_to_float')
	.sect	'.debug_line'
.L275:
	.word	.L747-.L746
.L746:
	.half	3
	.word	.L749-.L748
.L748:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L749:
	.byte	5,7,7,0,5,2
	.word	.L222
	.byte	3,240,1,1,5,5,9
	.half	.L485-.L222
	.byte	3,2,1,5,16,9
	.half	.L355-.L485
	.byte	3,1,1,9
	.half	.L486-.L355
	.byte	3,1,1,5,22,9
	.half	.L487-.L486
	.byte	3,1,1,5,23,9
	.half	.L488-.L487
	.byte	3,1,1,5,12,9
	.half	.L49-.L488
	.byte	3,4,1,5,9,9
	.half	.L750-.L49
	.byte	1,5,13,7,9
	.half	.L751-.L750
	.byte	3,2,1,5,19,9
	.half	.L50-.L751
	.byte	3,3,1,5,12,9
	.half	.L752-.L50
	.byte	1,5,9,9
	.half	.L753-.L752
	.byte	1,5,18,7,9
	.half	.L754-.L753
	.byte	3,2,1,5,17,9
	.half	.L755-.L754
	.byte	3,1,1,5,21,9
	.half	.L756-.L755
	.byte	3,127,1,5,24,9
	.half	.L52-.L756
	.byte	3,3,1,5,17,9
	.half	.L757-.L52
	.byte	1,5,14,9
	.half	.L758-.L757
	.byte	1,5,17,7,9
	.half	.L759-.L758
	.byte	3,2,1,5,45,9
	.half	.L53-.L759
	.byte	3,4,1,5,27,9
	.half	.L56-.L53
	.byte	3,2,1,5,41,9
	.half	.L760-.L56
	.byte	1,5,47,9
	.half	.L761-.L760
	.byte	1,5,30,9
	.half	.L762-.L761
	.byte	1,5,17,9
	.half	.L763-.L762
	.byte	3,1,1,5,23,9
	.half	.L55-.L763
	.byte	3,125,1,5,16,9
	.half	.L764-.L55
	.byte	1,5,15,9
	.half	.L765-.L764
	.byte	1,5,40,7,9
	.half	.L766-.L765
	.byte	1,5,33,9
	.half	.L767-.L766
	.byte	1,5,37,9
	.half	.L768-.L767
	.byte	1,5,19,7,9
	.half	.L57-.L768
	.byte	3,5,1,5,12,9
	.half	.L769-.L57
	.byte	1,5,9,9
	.half	.L770-.L769
	.byte	1,5,17,7,9
	.half	.L771-.L770
	.byte	3,2,1,5,74,9
	.half	.L772-.L771
	.byte	3,1,1,5,43,9
	.half	.L60-.L772
	.byte	3,2,1,5,57,9
	.half	.L773-.L60
	.byte	1,5,63,9
	.half	.L774-.L773
	.byte	1,5,46,9
	.half	.L775-.L774
	.byte	1,5,30,9
	.half	.L776-.L775
	.byte	3,1,1,5,27,9
	.half	.L777-.L776
	.byte	1,5,21,9
	.half	.L778-.L777
	.byte	3,1,1,5,27,9
	.half	.L59-.L778
	.byte	3,124,1,5,20,9
	.half	.L779-.L59
	.byte	1,5,19,9
	.half	.L780-.L779
	.byte	1,5,44,7,9
	.half	.L781-.L780
	.byte	1,5,37,9
	.half	.L782-.L781
	.byte	1,5,41,9
	.half	.L783-.L782
	.byte	1,5,53,7,9
	.half	.L784-.L783
	.byte	1,5,65,9
	.half	.L785-.L784
	.byte	1,5,63,9
	.half	.L786-.L785
	.byte	1,5,24,9
	.half	.L61-.L786
	.byte	3,6,1,5,14,9
	.half	.L58-.L61
	.byte	3,2,1,5,9,9
	.half	.L787-.L58
	.byte	3,2,1,5,20,7,9
	.half	.L788-.L787
	.byte	3,2,1,5,5,9
	.half	.L51-.L788
	.byte	3,3,1,5,1,9
	.half	.L64-.L51
	.byte	3,1,1,7,9
	.half	.L277-.L64
	.byte	0,1,1
.L747:
	.sdecl	'.debug_ranges',debug,cluster('func_str_to_float')
	.sect	'.debug_ranges'
.L276:
	.word	-1,.L222,0,.L277-.L222,0,0
	.sdecl	'.debug_info',debug,cluster('func_float_to_str')
	.sect	'.debug_info'
.L278:
	.word	446
	.half	3
	.word	.L279
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L281,.L280
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_float_to_str',0,1,174,2,6,1,1,1
	.word	.L224,.L360,.L223
	.byte	4
	.byte	'str',0,1,174,2,31
	.word	.L327,.L361
	.byte	4
	.byte	'number',0,1,174,2,42
	.word	.L352,.L362
	.byte	4
	.byte	'point_bit',0,1,174,2,56
	.word	.L330,.L363
	.byte	5
	.word	.L224,.L360
	.byte	5
	.word	.L364,.L360
	.byte	6
	.byte	'data_int',0,1,177,2,9
	.word	.L325,.L365
	.byte	6
	.byte	'data_float',0,1,178,2,9
	.word	.L325,.L366
	.byte	6
	.byte	'data_temp',0,1,179,2,9
	.word	.L367,.L368
	.byte	6
	.byte	'data_temp_point',0,1,180,2,9
	.word	.L369,.L370
	.byte	6
	.byte	'bit',0,1,181,2,11
	.word	.L330,.L371
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_float_to_str')
	.sect	'.debug_abbrev'
.L279:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_float_to_str')
	.sect	'.debug_line'
.L280:
	.word	.L790-.L789
.L789:
	.half	3
	.word	.L792-.L791
.L791:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L792:
	.byte	5,6,7,0,5,2
	.word	.L224
	.byte	3,173,2,1,5,5,9
	.half	.L793-.L224
	.byte	3,2,1,5,15,9
	.half	.L364-.L793
	.byte	3,5,1,5,12,9
	.half	.L65-.L364
	.byte	3,4,1,5,9,9
	.half	.L794-.L65
	.byte	1,5,13,7,9
	.half	.L795-.L794
	.byte	3,2,1,5,20,9
	.half	.L66-.L795
	.byte	3,4,1,5,12,9
	.half	.L497-.L66
	.byte	3,1,1,5,14,9
	.half	.L498-.L497
	.byte	1,5,9,9
	.half	.L796-.L498
	.byte	1,5,23,7,9
	.half	.L797-.L796
	.byte	3,2,1,5,21,9
	.half	.L798-.L797
	.byte	1,5,18,9
	.half	.L799-.L798
	.byte	1,5,26,9
	.half	.L800-.L799
	.byte	1,5,24,9
	.half	.L68-.L800
	.byte	3,2,1,5,21,9
	.half	.L501-.L68
	.byte	1,5,14,9
	.half	.L801-.L501
	.byte	1,5,23,7,9
	.half	.L802-.L801
	.byte	3,2,1,5,21,9
	.half	.L803-.L802
	.byte	1,5,18,9
	.half	.L804-.L803
	.byte	1,5,23,9
	.half	.L805-.L804
	.byte	3,1,1,5,21,9
	.half	.L806-.L805
	.byte	1,5,18,9
	.half	.L807-.L806
	.byte	1,5,20,9
	.half	.L808-.L807
	.byte	3,1,1,5,18,9
	.half	.L809-.L808
	.byte	1,5,13,9
	.half	.L810-.L809
	.byte	3,1,1,5,27,9
	.half	.L69-.L810
	.byte	3,4,1,5,25,9
	.half	.L502-.L69
	.byte	1,5,21,9
	.half	.L503-.L502
	.byte	3,1,1,5,19,9
	.half	.L73-.L503
	.byte	1,5,31,9
	.half	.L811-.L73
	.byte	3,2,1,5,29,9
	.half	.L812-.L811
	.byte	1,5,21,9
	.half	.L72-.L812
	.byte	3,126,1,5,22,7,9
	.half	.L813-.L72
	.byte	3,4,1,5,13,9
	.half	.L504-.L813
	.byte	3,3,1,5,22,9
	.half	.L74-.L504
	.byte	3,3,1,5,44,9
	.half	.L814-.L74
	.byte	1,5,42,9
	.half	.L815-.L814
	.byte	1,5,31,9
	.half	.L816-.L815
	.byte	1,5,27,9
	.half	.L817-.L816
	.byte	1,5,25,9
	.half	.L818-.L817
	.byte	3,1,1,5,22,9
	.half	.L819-.L818
	.byte	1,5,30,9
	.half	.L820-.L819
	.byte	3,1,1,5,23,7,9
	.half	.L821-.L820
	.byte	3,1,1,5,24,9
	.half	.L76-.L821
	.byte	3,2,1,5,53,9
	.half	.L78-.L76
	.byte	1,5,21,9
	.half	.L822-.L78
	.byte	1,5,18,9
	.half	.L823-.L822
	.byte	1,5,17,9
	.half	.L824-.L823
	.byte	3,1,1,5,23,9
	.half	.L75-.L824
	.byte	3,125,1,5,9,7,9
	.half	.L505-.L75
	.byte	3,7,1,5,17,7,9
	.half	.L506-.L505
	.byte	3,2,1,5,23,9
	.half	.L825-.L506
	.byte	3,1,1,5,21,9
	.half	.L826-.L825
	.byte	1,5,18,9
	.half	.L827-.L826
	.byte	1,5,13,9
	.half	.L828-.L827
	.byte	3,1,1,5,24,7,9
	.half	.L829-.L828
	.byte	3,2,1,5,22,9
	.half	.L830-.L829
	.byte	1,5,27,9
	.half	.L831-.L830
	.byte	1,5,37,9
	.half	.L80-.L831
	.byte	3,4,1,5,36,9
	.half	.L83-.L80
	.byte	3,2,1,5,60,9
	.half	.L832-.L83
	.byte	1,5,58,9
	.half	.L833-.L832
	.byte	1,5,45,9
	.half	.L834-.L833
	.byte	1,5,41,9
	.half	.L835-.L834
	.byte	1,5,35,9
	.half	.L836-.L835
	.byte	3,1,1,5,32,9
	.half	.L837-.L836
	.byte	1,5,31,9
	.half	.L507-.L837
	.byte	3,1,1,5,37,9
	.half	.L82-.L507
	.byte	3,124,1,5,31,7,9
	.half	.L508-.L82
	.byte	3,6,1,5,32,9
	.half	.L85-.L508
	.byte	3,2,1,5,67,9
	.half	.L87-.L85
	.byte	1,5,29,9
	.half	.L838-.L87
	.byte	1,5,26,9
	.half	.L839-.L838
	.byte	1,5,25,9
	.half	.L840-.L839
	.byte	3,1,1,5,31,9
	.half	.L84-.L840
	.byte	3,125,1,5,1,7,9
	.half	.L67-.L84
	.byte	3,8,1,7,9
	.half	.L282-.L67
	.byte	0,1,1
.L790:
	.sdecl	'.debug_ranges',debug,cluster('func_float_to_str')
	.sect	'.debug_ranges'
.L281:
	.word	-1,.L224,0,.L282-.L224,0,0
	.sdecl	'.debug_info',debug,cluster('func_str_to_double')
	.sect	'.debug_info'
.L283:
	.word	376
	.half	3
	.word	.L284
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L286,.L285
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_str_to_double',0,1,131,3,8
	.word	.L372
	.byte	1,1,1
	.word	.L226,.L373,.L225
	.byte	4
	.byte	'str',0,1,131,3,34
	.word	.L327,.L374
	.byte	5
	.word	.L226,.L373
	.byte	5
	.word	.L375,.L373
	.byte	6
	.byte	'sign',0,1,134,3,11
	.word	.L330,.L376
	.byte	6
	.byte	'temp',0,1,135,3,12
	.word	.L372,.L377
	.byte	6
	.byte	'temp_point',0,1,136,3,12
	.word	.L372,.L378
	.byte	6
	.byte	'point_bit',0,1,137,3,12
	.word	.L372,.L379
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_str_to_double')
	.sect	'.debug_abbrev'
.L284:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_str_to_double')
	.sect	'.debug_line'
.L285:
	.word	.L842-.L841
.L841:
	.half	3
	.word	.L844-.L843
.L843:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L844:
	.byte	5,8,7,0,5,2
	.word	.L226
	.byte	3,130,3,1,5,5,9
	.half	.L511-.L226
	.byte	3,2,1,5,16,9
	.half	.L375-.L511
	.byte	3,1,1,5,19,9
	.half	.L512-.L375
	.byte	3,1,1,5,17,9
	.half	.L845-.L512
	.byte	1,5,25,9
	.half	.L846-.L845
	.byte	3,1,1,5,24,9
	.half	.L513-.L846
	.byte	3,1,1,5,12,9
	.half	.L88-.L513
	.byte	3,4,1,5,9,9
	.half	.L847-.L88
	.byte	1,5,13,7,9
	.half	.L848-.L847
	.byte	3,2,1,5,19,9
	.half	.L89-.L848
	.byte	3,3,1,5,12,9
	.half	.L515-.L89
	.byte	1,5,9,9
	.half	.L849-.L515
	.byte	1,5,18,7,9
	.half	.L850-.L849
	.byte	3,2,1,5,17,9
	.half	.L851-.L850
	.byte	3,1,1,5,21,9
	.half	.L852-.L851
	.byte	3,127,1,5,24,9
	.half	.L91-.L852
	.byte	3,3,1,5,17,9
	.half	.L853-.L91
	.byte	1,5,14,9
	.half	.L854-.L853
	.byte	1,5,17,7,9
	.half	.L855-.L854
	.byte	3,2,1,5,45,9
	.half	.L92-.L855
	.byte	3,4,1,5,27,9
	.half	.L95-.L92
	.byte	3,2,1,5,25,9
	.half	.L516-.L95
	.byte	1,5,41,9
	.half	.L856-.L516
	.byte	1,5,47,9
	.half	.L857-.L856
	.byte	1,5,30,9
	.half	.L858-.L857
	.byte	1,5,17,9
	.half	.L859-.L858
	.byte	3,1,1,5,23,9
	.half	.L94-.L859
	.byte	3,125,1,5,16,9
	.half	.L860-.L94
	.byte	1,5,15,9
	.half	.L861-.L860
	.byte	1,5,40,7,9
	.half	.L862-.L861
	.byte	1,5,33,9
	.half	.L863-.L862
	.byte	1,5,37,9
	.half	.L864-.L863
	.byte	1,5,19,7,9
	.half	.L96-.L864
	.byte	3,5,1,5,12,9
	.half	.L865-.L96
	.byte	1,5,9,9
	.half	.L866-.L865
	.byte	1,5,17,7,9
	.half	.L867-.L866
	.byte	3,2,1,5,77,9
	.half	.L868-.L867
	.byte	3,1,1,5,43,9
	.half	.L99-.L868
	.byte	3,2,1,5,41,9
	.half	.L518-.L99
	.byte	1,5,57,9
	.half	.L869-.L518
	.byte	1,5,63,9
	.half	.L870-.L869
	.byte	1,5,46,9
	.half	.L871-.L870
	.byte	1,5,30,9
	.half	.L872-.L871
	.byte	3,1,1,5,27,9
	.half	.L873-.L872
	.byte	1,5,21,9
	.half	.L522-.L873
	.byte	3,1,1,5,27,9
	.half	.L98-.L522
	.byte	3,124,1,5,20,9
	.half	.L874-.L98
	.byte	1,5,19,9
	.half	.L875-.L874
	.byte	1,5,44,7,9
	.half	.L876-.L875
	.byte	1,5,37,9
	.half	.L877-.L876
	.byte	1,5,41,9
	.half	.L878-.L877
	.byte	1,5,65,7,9
	.half	.L879-.L878
	.byte	1,5,63,9
	.half	.L880-.L879
	.byte	1,5,24,9
	.half	.L100-.L880
	.byte	3,6,1,5,14,9
	.half	.L97-.L100
	.byte	3,2,1,5,9,9
	.half	.L881-.L97
	.byte	3,2,1,5,20,7,9
	.half	.L882-.L881
	.byte	3,2,1,5,5,9
	.half	.L90-.L882
	.byte	3,3,1,5,1,9
	.half	.L103-.L90
	.byte	3,2,1,7,9
	.half	.L287-.L103
	.byte	0,1,1
.L842:
	.sdecl	'.debug_ranges',debug,cluster('func_str_to_double')
	.sect	'.debug_ranges'
.L286:
	.word	-1,.L226,0,.L287-.L226,0,0
	.sdecl	'.debug_info',debug,cluster('func_double_to_str')
	.sect	'.debug_info'
.L288:
	.word	447
	.half	3
	.word	.L289
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L291,.L290
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_double_to_str',0,1,193,3,6,1,1,1
	.word	.L228,.L380,.L227
	.byte	4
	.byte	'str',0,1,193,3,32
	.word	.L327,.L381
	.byte	4
	.byte	'number',0,1,193,3,44
	.word	.L372,.L382
	.byte	4
	.byte	'point_bit',0,1,193,3,58
	.word	.L330,.L383
	.byte	5
	.word	.L228,.L380
	.byte	5
	.word	.L384,.L380
	.byte	6
	.byte	'data_int',0,1,196,3,9
	.word	.L325,.L385
	.byte	6
	.byte	'data_float',0,1,197,3,9
	.word	.L325,.L386
	.byte	6
	.byte	'data_temp',0,1,198,3,9
	.word	.L387,.L388
	.byte	6
	.byte	'data_temp_point',0,1,199,3,9
	.word	.L389,.L390
	.byte	6
	.byte	'bit',0,1,200,3,11
	.word	.L330,.L391
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_double_to_str')
	.sect	'.debug_abbrev'
.L289:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_double_to_str')
	.sect	'.debug_line'
.L290:
	.word	.L884-.L883
.L883:
	.half	3
	.word	.L886-.L885
.L885:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L886:
	.byte	5,6,7,0,5,2
	.word	.L228
	.byte	3,192,3,1,5,5,9
	.half	.L535-.L228
	.byte	3,2,1,5,15,9
	.half	.L384-.L535
	.byte	3,5,1,5,12,9
	.half	.L104-.L384
	.byte	3,4,1,5,9,9
	.half	.L887-.L104
	.byte	1,5,13,7,9
	.half	.L888-.L887
	.byte	3,2,1,5,20,9
	.half	.L105-.L888
	.byte	3,4,1,5,12,9
	.half	.L538-.L105
	.byte	3,1,1,5,14,9
	.half	.L539-.L538
	.byte	1,5,9,9
	.half	.L889-.L539
	.byte	1,5,23,7,9
	.half	.L890-.L889
	.byte	3,2,1,5,21,9
	.half	.L891-.L890
	.byte	1,5,18,9
	.half	.L892-.L891
	.byte	1,5,26,9
	.half	.L893-.L892
	.byte	1,5,21,9
	.half	.L107-.L893
	.byte	3,2,1,5,14,9
	.half	.L894-.L107
	.byte	1,5,23,7,9
	.half	.L895-.L894
	.byte	3,2,1,5,21,9
	.half	.L896-.L895
	.byte	1,5,18,9
	.half	.L897-.L896
	.byte	1,5,23,9
	.half	.L898-.L897
	.byte	3,1,1,5,21,9
	.half	.L899-.L898
	.byte	1,5,18,9
	.half	.L900-.L899
	.byte	1,5,20,9
	.half	.L901-.L900
	.byte	3,1,1,5,18,9
	.half	.L902-.L901
	.byte	1,5,13,9
	.half	.L903-.L902
	.byte	3,1,1,5,27,9
	.half	.L108-.L903
	.byte	3,4,1,5,25,9
	.half	.L543-.L108
	.byte	1,5,21,9
	.half	.L904-.L543
	.byte	3,1,1,5,19,9
	.half	.L112-.L904
	.byte	1,5,31,9
	.half	.L905-.L112
	.byte	3,2,1,5,29,9
	.half	.L906-.L905
	.byte	1,5,21,9
	.half	.L111-.L906
	.byte	3,126,1,5,22,7,9
	.half	.L907-.L111
	.byte	3,4,1,5,13,9
	.half	.L908-.L907
	.byte	3,3,1,5,22,9
	.half	.L113-.L908
	.byte	3,3,1,5,44,9
	.half	.L909-.L113
	.byte	1,5,42,9
	.half	.L910-.L909
	.byte	1,5,31,9
	.half	.L911-.L910
	.byte	1,5,27,9
	.half	.L912-.L911
	.byte	1,5,25,9
	.half	.L913-.L912
	.byte	3,1,1,5,22,9
	.half	.L914-.L913
	.byte	1,5,30,9
	.half	.L915-.L914
	.byte	3,1,1,5,23,7,9
	.half	.L916-.L915
	.byte	3,1,1,5,24,9
	.half	.L115-.L916
	.byte	3,2,1,5,53,9
	.half	.L117-.L115
	.byte	1,5,21,9
	.half	.L917-.L117
	.byte	1,5,18,9
	.half	.L918-.L917
	.byte	1,5,17,9
	.half	.L919-.L918
	.byte	3,1,1,5,23,9
	.half	.L114-.L919
	.byte	3,125,1,5,9,7,9
	.half	.L920-.L114
	.byte	3,7,1,5,17,7,9
	.half	.L921-.L920
	.byte	3,2,1,5,23,9
	.half	.L922-.L921
	.byte	3,1,1,5,21,9
	.half	.L923-.L922
	.byte	1,5,18,9
	.half	.L924-.L923
	.byte	1,5,13,9
	.half	.L545-.L924
	.byte	3,1,1,5,24,7,9
	.half	.L546-.L545
	.byte	3,1,1,5,22,9
	.half	.L925-.L546
	.byte	1,5,27,9
	.half	.L926-.L925
	.byte	1,5,37,9
	.half	.L119-.L926
	.byte	3,3,1,5,36,9
	.half	.L122-.L119
	.byte	3,2,1,5,60,9
	.half	.L927-.L122
	.byte	1,5,58,9
	.half	.L547-.L927
	.byte	1,5,45,9
	.half	.L548-.L547
	.byte	1,5,41,9
	.half	.L928-.L548
	.byte	1,5,35,9
	.half	.L929-.L928
	.byte	3,1,1,5,32,9
	.half	.L549-.L929
	.byte	1,5,31,9
	.half	.L930-.L549
	.byte	3,1,1,5,37,9
	.half	.L121-.L930
	.byte	3,124,1,5,31,7,9
	.half	.L931-.L121
	.byte	3,6,1,5,32,9
	.half	.L124-.L931
	.byte	3,2,1,5,67,9
	.half	.L126-.L124
	.byte	1,5,29,9
	.half	.L932-.L126
	.byte	1,5,26,9
	.half	.L933-.L932
	.byte	1,5,25,9
	.half	.L934-.L933
	.byte	3,1,1,5,31,9
	.half	.L123-.L934
	.byte	3,125,1,5,1,7,9
	.half	.L106-.L123
	.byte	3,8,1,7,9
	.half	.L292-.L106
	.byte	0,1,1
.L884:
	.sdecl	'.debug_ranges',debug,cluster('func_double_to_str')
	.sect	'.debug_ranges'
.L291:
	.word	-1,.L228,0,.L292-.L228,0,0
	.sdecl	'.debug_info',debug,cluster('func_str_to_hex')
	.sect	'.debug_info'
.L293:
	.word	372
	.half	3
	.word	.L294
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L296,.L295
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_str_to_hex',0,1,148,4,8
	.word	.L318
	.byte	1,1,1
	.word	.L230,.L392,.L229
	.byte	4
	.byte	'str',0,1,148,4,31
	.word	.L327,.L393
	.byte	5
	.word	.L230,.L392
	.byte	5
	.word	.L394,.L392
	.byte	6
	.byte	'str_len',0,1,151,4,12
	.word	.L318,.L395
	.byte	6
	.byte	'result_data',0,1,152,4,12
	.word	.L318,.L396
	.byte	6
	.byte	'temp',0,1,153,4,11
	.word	.L330,.L397
	.byte	6
	.byte	'flag',0,1,154,4,11
	.word	.L330,.L398
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_str_to_hex')
	.sect	'.debug_abbrev'
.L294:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_str_to_hex')
	.sect	'.debug_line'
.L295:
	.word	.L936-.L935
.L935:
	.half	3
	.word	.L938-.L937
.L937:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L938:
	.byte	5,8,7,0,5,2
	.word	.L230
	.byte	3,147,4,1,5,5,9
	.half	.L551-.L230
	.byte	3,2,1,5,29,9
	.half	.L394-.L551
	.byte	3,1,1,5,24,9
	.half	.L553-.L394
	.byte	3,1,1,5,16,9
	.half	.L555-.L553
	.byte	3,2,1,5,12,9
	.half	.L127-.L555
	.byte	3,4,1,5,9,9
	.half	.L939-.L127
	.byte	1,5,13,7,9
	.half	.L940-.L939
	.byte	3,2,1,5,9,9
	.half	.L128-.L940
	.byte	3,3,1,5,24,7,9
	.half	.L941-.L128
	.byte	3,2,1,5,17,9
	.half	.L942-.L941
	.byte	1,5,16,9
	.half	.L943-.L942
	.byte	1,5,41,7,9
	.half	.L944-.L943
	.byte	1,5,34,9
	.half	.L945-.L944
	.byte	1,5,38,9
	.half	.L946-.L945
	.byte	1,5,25,7,9
	.half	.L947-.L946
	.byte	3,2,1,5,30,9
	.half	.L948-.L947
	.byte	1,5,35,9
	.half	.L557-.L948
	.byte	1,5,29,9
	.half	.L131-.L557
	.byte	3,2,1,5,22,9
	.half	.L949-.L131
	.byte	1,5,21,9
	.half	.L950-.L949
	.byte	1,5,46,7,9
	.half	.L951-.L950
	.byte	1,5,39,9
	.half	.L952-.L951
	.byte	1,5,43,9
	.half	.L953-.L952
	.byte	1,5,25,7,9
	.half	.L954-.L953
	.byte	3,2,1,5,30,9
	.half	.L955-.L954
	.byte	1,5,35,9
	.half	.L558-.L955
	.byte	1,5,29,9
	.half	.L134-.L558
	.byte	3,2,1,5,22,9
	.half	.L956-.L134
	.byte	1,5,21,9
	.half	.L957-.L956
	.byte	1,5,46,7,9
	.half	.L958-.L957
	.byte	1,5,39,9
	.half	.L959-.L958
	.byte	1,5,43,9
	.half	.L960-.L959
	.byte	1,5,25,7,9
	.half	.L961-.L960
	.byte	3,2,1,5,30,9
	.half	.L962-.L961
	.byte	1,5,35,9
	.half	.L559-.L962
	.byte	1,5,17,9
	.half	.L137-.L559
	.byte	3,4,1,5,41,9
	.half	.L133-.L137
	.byte	3,2,1,5,55,9
	.half	.L556-.L133
	.byte	1,5,47,9
	.half	.L560-.L556
	.byte	1,5,63,9
	.half	.L963-.L560
	.byte	1,5,17,9
	.half	.L130-.L963
	.byte	3,5,1,5,25,9
	.half	.L964-.L130
	.byte	1,5,16,9
	.half	.L965-.L964
	.byte	1,5,34,7,9
	.half	.L966-.L965
	.byte	1,5,48,9
	.half	.L967-.L966
	.byte	1,5,45,9
	.half	.L968-.L967
	.byte	1,5,21,7,9
	.half	.L969-.L968
	.byte	3,2,1,5,22,9
	.half	.L970-.L969
	.byte	3,1,1,5,13,9
	.half	.L141-.L970
	.byte	3,3,1,5,20,9
	.half	.L971-.L141
	.byte	3,1,1,5,23,9
	.half	.L972-.L971
	.byte	1,5,5,7,9
	.half	.L129-.L972
	.byte	3,2,1,5,1,9
	.half	.L144-.L129
	.byte	3,1,1,7,9
	.half	.L297-.L144
	.byte	0,1,1
.L936:
	.sdecl	'.debug_ranges',debug,cluster('func_str_to_hex')
	.sect	'.debug_ranges'
.L296:
	.word	-1,.L230,0,.L297-.L230,0,0
	.sdecl	'.debug_info',debug,cluster('func_hex_to_str')
	.sect	'.debug_info'
.L298:
	.word	369
	.half	3
	.word	.L299
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L301,.L300
	.byte	2
	.word	.L239
	.byte	3
	.byte	'func_hex_to_str',0,1,206,4,6,1,1,1
	.word	.L232,.L399,.L231
	.byte	4
	.byte	'str',0,1,206,4,29
	.word	.L327,.L400
	.byte	4
	.byte	'number',0,1,206,4,41
	.word	.L318,.L401
	.byte	5
	.word	.L232,.L399
	.byte	5
	.word	.L402,.L399
	.byte	6
	.byte	'hex_index',0,1,209,4,16
	.word	.L403,.L404
	.byte	6
	.byte	'data_temp',0,1,214,4,10
	.word	.L405,.L406
	.byte	6
	.byte	'bit',0,1,215,4,11
	.word	.L330,.L407
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('func_hex_to_str')
	.sect	'.debug_abbrev'
.L299:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('func_hex_to_str')
	.sect	'.debug_line'
.L300:
	.word	.L974-.L973
.L973:
	.half	3
	.word	.L976-.L975
.L975:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L976:
	.byte	5,6,7,0,5,2
	.word	.L232
	.byte	3,205,4,1,5,5,9
	.half	.L565-.L232
	.byte	3,2,1,5,32,9
	.half	.L402-.L565
	.byte	3,1,1,5,30,9
	.half	.L977-.L402
	.byte	1,5,15,9
	.half	.L978-.L977
	.byte	3,6,1,5,14,9
	.half	.L566-.L978
	.byte	3,2,1,5,12,9
	.half	.L979-.L566
	.byte	1,5,9,9
	.half	.L980-.L979
	.byte	1,5,14,9
	.half	.L981-.L980
	.byte	3,1,1,5,12,9
	.half	.L982-.L981
	.byte	1,5,9,9
	.half	.L983-.L982
	.byte	1,5,12,9
	.half	.L146-.L983
	.byte	3,3,1,5,9,9
	.half	.L984-.L146
	.byte	1,5,13,7,9
	.half	.L985-.L984
	.byte	3,2,1,5,9,9
	.half	.L147-.L985
	.byte	3,3,1,5,20,7,9
	.half	.L986-.L147
	.byte	3,2,1,5,18,9
	.half	.L567-.L986
	.byte	1,5,13,9
	.half	.L987-.L567
	.byte	3,1,1,5,26,9
	.half	.L149-.L987
	.byte	3,3,1,5,22,9
	.half	.L152-.L149
	.byte	3,2,1,5,41,9
	.half	.L988-.L152
	.byte	1,5,31,9
	.half	.L989-.L988
	.byte	1,5,27,9
	.half	.L990-.L989
	.byte	1,5,20,9
	.half	.L569-.L990
	.byte	3,1,1,5,26,9
	.half	.L151-.L569
	.byte	3,125,1,5,23,7,9
	.half	.L991-.L151
	.byte	3,5,1,5,42,9
	.half	.L154-.L991
	.byte	3,2,1,5,32,9
	.half	.L992-.L154
	.byte	1,5,21,9
	.half	.L993-.L992
	.byte	1,5,18,9
	.half	.L994-.L993
	.byte	1,5,17,9
	.half	.L995-.L994
	.byte	3,1,1,5,23,9
	.half	.L153-.L995
	.byte	3,125,1,5,1,7,9
	.half	.L148-.L153
	.byte	3,6,1,7,9
	.half	.L302-.L148
	.byte	0,1,1
.L974:
	.sdecl	'.debug_ranges',debug,cluster('func_hex_to_str')
	.sect	'.debug_ranges'
.L301:
	.word	-1,.L232,0,.L302-.L232,0,0
	.sdecl	'.debug_info',debug,cluster('zf_sprintf')
	.sect	'.debug_info'
.L303:
	.word	803
	.half	3
	.word	.L304
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L306,.L305
	.byte	2
	.word	.L239
	.byte	3
	.byte	'zf_sprintf',0,1,213,5,8
	.word	.L318
	.byte	1,1,1
	.word	.L238,.L408,.L237
	.byte	4
	.byte	'buff',0,1,213,5,26
	.word	.L409,.L410
	.byte	4
	.byte	'format',0,1,213,5,44
	.word	.L411,.L412
	.byte	5,1,213,5,52,6
	.word	.L238,.L408
	.byte	7
	.byte	'buff_len',0,1,215,5,12
	.word	.L318,.L413
	.byte	7
	.byte	'arg',0,1,216,5,13
	.word	.L414,.L415
	.byte	6
	.word	.L172,.L171
	.byte	7
	.byte	'ret',0,1,221,5,14
	.word	.L416,.L417
	.byte	6
	.word	.L178,.L418
	.byte	7
	.byte	'ch',0,1,233,5,30
	.word	.L416,.L419
	.byte	0,6
	.word	.L181,.L420
	.byte	7
	.byte	'vstr',0,1,243,5,30
	.word	.L421,.L422
	.byte	7
	.byte	'ival',0,1,244,5,31
	.word	.L325,.L423
	.byte	7
	.byte	'vlen',0,1,245,5,31
	.word	.L330,.L424
	.byte	0,6
	.word	.L175,.L425
	.byte	7
	.byte	'vstr',0,1,134,6,26
	.word	.L421,.L426
	.byte	7
	.byte	'ival',0,1,135,6,28
	.word	.L372,.L427
	.byte	7
	.byte	'vlen',0,1,136,6,27
	.word	.L330,.L428
	.byte	0,6
	.word	.L185,.L429
	.byte	7
	.byte	'vstr',0,1,176,6,30
	.word	.L421,.L430
	.byte	7
	.byte	'ival',0,1,177,6,32
	.word	.L318,.L431
	.byte	7
	.byte	'vlen',0,1,178,6,31
	.word	.L330,.L432
	.byte	0,6
	.word	.L182,.L433
	.byte	7
	.byte	'vstr',0,1,189,6,30
	.word	.L421,.L434
	.byte	7
	.byte	'ival',0,1,190,6,32
	.word	.L318,.L435
	.byte	7
	.byte	'vlen',0,1,191,6,31
	.word	.L330,.L436
	.byte	0,6
	.word	.L176,.L437
	.byte	7
	.byte	'vstr',0,1,204,6,30
	.word	.L421,.L438
	.byte	7
	.byte	'ival',0,1,205,6,32
	.word	.L318,.L439
	.byte	7
	.byte	'vlen',0,1,206,6,31
	.word	.L330,.L440
	.byte	0,6
	.word	.L184,.L441
	.byte	7
	.byte	'pc',0,1,217,6,31
	.word	.L409,.L442
	.byte	0,6
	.word	.L183,.L443
	.byte	7
	.byte	'vstr',0,1,230,6,30
	.word	.L421,.L444
	.byte	7
	.byte	'ival',0,1,231,6,32
	.word	.L318,.L445
	.byte	7
	.byte	'vlen',0,1,232,6,31
	.word	.L330,.L446
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('zf_sprintf')
	.sect	'.debug_abbrev'
.L304:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,24,0,58,15,59,15,57,15
	.byte	0,0,6,11,1,17,1,18,1,0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('zf_sprintf')
	.sect	'.debug_line'
.L305:
	.word	.L997-.L996
.L996:
	.half	3
	.word	.L999-.L998
.L998:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L999:
	.byte	5,8,7,0,5,2
	.word	.L238
	.byte	3,212,5,1,5,21,9
	.half	.L584-.L238
	.byte	3,2,1,5,5,9
	.half	.L585-.L584
	.byte	3,2,1,5,19,9
	.half	.L586-.L585
	.byte	3,2,1,5,20,9
	.half	.L172-.L586
	.byte	3,2,1,5,13,9
	.half	.L587-.L172
	.byte	3,1,1,5,9,9
	.half	.L1000-.L587
	.byte	1,5,22,7,9
	.half	.L1001-.L1000
	.byte	3,2,1,5,21,9
	.half	.L1002-.L1001
	.byte	1,5,22,9
	.half	.L588-.L1002
	.byte	3,145,1,1,9
	.half	.L1003-.L588
	.byte	3,147,127,1,9
	.half	.L1004-.L1003
	.byte	3,198,0,1,9
	.half	.L1005-.L1004
	.byte	3,152,127,1,9
	.half	.L1006-.L1005
	.byte	3,5,1,9
	.half	.L1007-.L1006
	.byte	3,9,1,9
	.half	.L1008-.L1007
	.byte	3,19,1,9
	.half	.L1009-.L1008
	.byte	3,110,1,9
	.half	.L1010-.L1009
	.byte	3,202,0,1,9
	.half	.L1011-.L1010
	.byte	3,41,1,9
	.half	.L1012-.L1011
	.byte	3,115,1,9
	.half	.L1013-.L1012
	.byte	3,87,1,9
	.half	.L1014-.L1013
	.byte	3,27,1,5,21,9
	.half	.L177-.L1014
	.byte	3,156,127,1,5,41,9
	.half	.L178-.L177
	.byte	3,4,1,5,35,9
	.half	.L1015-.L178
	.byte	1,5,31,9
	.half	.L589-.L1015
	.byte	3,1,1,5,30,9
	.half	.L1016-.L589
	.byte	3,1,1,5,34,9
	.half	.L1017-.L1016
	.byte	3,1,1,5,21,9
	.half	.L418-.L1017
	.byte	3,2,1,5,45,9
	.half	.L181-.L418
	.byte	3,6,1,5,76,9
	.half	.L590-.L181
	.byte	3,1,1,5,82,9
	.half	.L1018-.L590
	.byte	1,5,85,9
	.half	.L1019-.L1018
	.byte	1,5,36,9
	.half	.L592-.L1019
	.byte	1,5,25,9
	.half	.L593-.L592
	.byte	3,2,1,5,33,7,9
	.half	.L1020-.L593
	.byte	3,2,1,5,42,9
	.half	.L1021-.L1020
	.byte	1,5,40,9
	.half	.L1022-.L1021
	.byte	1,5,34,9
	.half	.L1023-.L1022
	.byte	3,1,1,5,46,9
	.half	.L190-.L1023
	.byte	3,2,1,5,52,9
	.half	.L1024-.L190
	.byte	1,5,38,9
	.half	.L596-.L1024
	.byte	3,1,1,5,44,9
	.half	.L1025-.L596
	.byte	1,5,30,9
	.half	.L598-.L1025
	.byte	3,1,1,5,34,9
	.half	.L1026-.L598
	.byte	3,1,1,5,21,9
	.half	.L420-.L1026
	.byte	3,2,1,5,43,9
	.half	.L175-.L420
	.byte	3,6,1,5,66,9
	.half	.L600-.L175
	.byte	3,1,1,5,79,9
	.half	.L1027-.L600
	.byte	1,5,85,9
	.half	.L1028-.L1027
	.byte	1,5,88,9
	.half	.L1029-.L1028
	.byte	1,5,24,9
	.half	.L603-.L1029
	.byte	3,2,1,5,26,9
	.half	.L1030-.L603
	.byte	1,5,21,9
	.half	.L602-.L1030
	.byte	1,5,29,7,9
	.half	.L1031-.L602
	.byte	3,2,1,5,38,9
	.half	.L1032-.L1031
	.byte	1,5,36,9
	.half	.L1033-.L1032
	.byte	1,5,30,9
	.half	.L1034-.L1033
	.byte	3,1,1,5,42,9
	.half	.L192-.L1034
	.byte	3,2,1,5,48,9
	.half	.L1035-.L192
	.byte	1,5,34,9
	.half	.L607-.L1035
	.byte	3,1,1,5,40,9
	.half	.L1036-.L607
	.byte	1,5,26,9
	.half	.L609-.L1036
	.byte	3,1,1,5,30,9
	.half	.L1037-.L609
	.byte	3,1,1,5,44,9
	.half	.L1038-.L1037
	.byte	3,2,1,5,42,9
	.half	.L1039-.L1038
	.byte	1,5,57,9
	.half	.L1040-.L1039
	.byte	1,5,56,9
	.half	.L1041-.L1040
	.byte	1,5,21,9
	.half	.L1042-.L1041
	.byte	3,1,1,5,83,9
	.half	.L1043-.L1042
	.byte	3,6,1,5,73,9
	.half	.L1044-.L1043
	.byte	1,5,63,9
	.half	.L1045-.L1044
	.byte	1,5,53,9
	.half	.L1046-.L1045
	.byte	1,5,43,9
	.half	.L1047-.L1046
	.byte	1,5,33,9
	.half	.L1048-.L1047
	.byte	1,5,30,9
	.half	.L1049-.L1048
	.byte	3,1,1,5,89,9
	.half	.L611-.L1049
	.byte	3,123,1,5,64,9
	.half	.L193-.L611
	.byte	1,5,77,9
	.half	.L1050-.L193
	.byte	1,5,83,9
	.half	.L1051-.L1050
	.byte	1,5,86,9
	.half	.L1052-.L1051
	.byte	1,5,35,9
	.half	.L194-.L1052
	.byte	3,8,1,5,29,9
	.half	.L196-.L194
	.byte	3,2,1,5,38,9
	.half	.L1053-.L196
	.byte	1,5,36,9
	.half	.L1054-.L1053
	.byte	1,5,30,9
	.half	.L1055-.L1054
	.byte	3,1,1,5,35,9
	.half	.L195-.L1055
	.byte	3,125,1,5,25,7,9
	.half	.L1056-.L195
	.byte	3,6,1,5,34,9
	.half	.L1057-.L1056
	.byte	1,5,32,9
	.half	.L1058-.L1057
	.byte	1,5,26,9
	.half	.L1059-.L1058
	.byte	3,1,1,5,42,9
	.half	.L613-.L1059
	.byte	3,2,1,5,48,9
	.half	.L1060-.L613
	.byte	1,5,34,9
	.half	.L615-.L1060
	.byte	3,1,1,5,40,9
	.half	.L1061-.L615
	.byte	1,5,30,9
	.half	.L617-.L1061
	.byte	3,1,1,5,17,9
	.half	.L425-.L617
	.byte	3,2,1,5,47,9
	.half	.L185-.L425
	.byte	3,5,1,5,68,9
	.half	.L619-.L185
	.byte	3,1,1,5,74,9
	.half	.L1062-.L619
	.byte	1,5,77,9
	.half	.L1063-.L1062
	.byte	1,5,36,9
	.half	.L620-.L1063
	.byte	1,5,46,9
	.half	.L622-.L620
	.byte	3,2,1,5,52,9
	.half	.L1064-.L622
	.byte	1,5,38,9
	.half	.L623-.L1064
	.byte	3,1,1,5,44,9
	.half	.L1065-.L623
	.byte	1,5,30,9
	.half	.L625-.L1065
	.byte	3,1,1,5,34,9
	.half	.L1066-.L625
	.byte	3,1,1,5,21,9
	.half	.L429-.L1066
	.byte	3,2,1,5,47,9
	.half	.L182-.L429
	.byte	3,5,1,5,68,9
	.half	.L627-.L182
	.byte	3,1,1,5,74,9
	.half	.L1067-.L627
	.byte	1,5,77,9
	.half	.L1068-.L1067
	.byte	1,5,36,9
	.half	.L628-.L1068
	.byte	1,5,46,9
	.half	.L630-.L628
	.byte	3,2,1,5,52,9
	.half	.L1069-.L630
	.byte	1,5,38,9
	.half	.L631-.L1069
	.byte	3,1,1,5,44,9
	.half	.L1070-.L631
	.byte	1,5,30,9
	.half	.L633-.L1070
	.byte	3,1,1,5,34,9
	.half	.L1071-.L633
	.byte	3,1,1,5,21,9
	.half	.L433-.L1071
	.byte	3,3,1,5,47,9
	.half	.L176-.L433
	.byte	3,6,1,5,68,9
	.half	.L635-.L176
	.byte	3,1,1,5,74,9
	.half	.L1072-.L635
	.byte	1,5,77,9
	.half	.L1073-.L1072
	.byte	1,5,36,9
	.half	.L636-.L1073
	.byte	1,5,46,9
	.half	.L638-.L636
	.byte	3,2,1,5,52,9
	.half	.L1074-.L638
	.byte	1,5,38,9
	.half	.L639-.L1074
	.byte	3,1,1,5,44,9
	.half	.L1075-.L639
	.byte	1,5,30,9
	.half	.L641-.L1075
	.byte	3,1,1,5,34,9
	.half	.L1076-.L641
	.byte	3,1,1,5,21,9
	.half	.L437-.L1076
	.byte	3,2,1,5,36,9
	.half	.L184-.L437
	.byte	3,4,1,5,35,9
	.half	.L643-.L184
	.byte	3,1,1,5,37,9
	.half	.L202-.L643
	.byte	3,2,1,5,35,9
	.half	.L1077-.L202
	.byte	1,5,34,9
	.half	.L1078-.L1077
	.byte	3,1,1,5,38,9
	.half	.L1079-.L1078
	.byte	3,1,1,5,32,9
	.half	.L1080-.L1079
	.byte	3,1,1,9
	.half	.L201-.L1080
	.byte	3,123,1,5,35,9
	.half	.L1081-.L201
	.byte	1,5,21,7,9
	.half	.L441-.L1081
	.byte	3,8,1,5,47,9
	.half	.L183-.L441
	.byte	3,5,1,5,62,9
	.half	.L644-.L183
	.byte	3,2,1,5,68,9
	.half	.L1082-.L644
	.byte	1,5,71,9
	.half	.L1083-.L1082
	.byte	1,5,46,9
	.half	.L645-.L1083
	.byte	3,1,1,5,52,9
	.half	.L1084-.L645
	.byte	1,5,38,9
	.half	.L1085-.L1084
	.byte	3,1,1,5,44,9
	.half	.L1086-.L1085
	.byte	1,5,30,9
	.half	.L647-.L1086
	.byte	3,1,1,5,34,9
	.half	.L1087-.L647
	.byte	3,1,1,5,21,9
	.half	.L443-.L1087
	.byte	3,2,1,5,33,9
	.half	.L174-.L443
	.byte	3,4,1,5,31,9
	.half	.L1088-.L174
	.byte	1,5,30,9
	.half	.L1089-.L1088
	.byte	3,1,1,5,34,9
	.half	.L1090-.L1089
	.byte	3,1,1,5,21,9
	.half	.L1091-.L1090
	.byte	3,2,1,9
	.half	.L187-.L1091
	.byte	3,3,1,5,9,9
	.half	.L188-.L187
	.byte	3,2,1,5,28,9
	.half	.L173-.L188
	.byte	3,3,1,5,19,9
	.half	.L1092-.L173
	.byte	1,5,18,9
	.half	.L1093-.L1092
	.byte	3,1,1,5,22,9
	.half	.L1094-.L1093
	.byte	3,1,1,5,16,9
	.half	.L207-.L1094
	.byte	3,2,1,5,12,9
	.half	.L171-.L207
	.byte	3,216,126,1,5,19,9
	.half	.L1095-.L171
	.byte	1,5,5,7,9
	.half	.L1096-.L1095
	.byte	3,172,1,1,5,1,9
	.half	.L208-.L1096
	.byte	3,1,1,7,9
	.half	.L307-.L208
	.byte	0,1,1
.L997:
	.sdecl	'.debug_ranges',debug,cluster('zf_sprintf')
	.sect	'.debug_ranges'
.L306:
	.word	-1,.L238,0,.L307-.L238,0,0
	.sdecl	'.debug_info',debug,cluster('number_conversion_ascii')
	.sect	'.debug_info'
.L308:
	.word	431
	.half	3
	.word	.L309
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L311,.L310
	.byte	2
	.word	.L239
	.byte	3
	.byte	'number_conversion_ascii',0,1,255,4,14
	.word	.L330
	.byte	1,1
	.word	.L234,.L447,.L233
	.byte	4
	.byte	'dat',0,1,255,4,46
	.word	.L318,.L448
	.byte	4
	.byte	'p',0,1,255,4,57
	.word	.L409,.L449
	.byte	4
	.byte	'neg_type',0,1,255,4,66
	.word	.L330,.L450
	.byte	4
	.byte	'radix',0,1,255,4,82
	.word	.L330,.L451
	.byte	5
	.word	.L234,.L447
	.byte	6
	.byte	'neg_dat',0,1,129,5,13
	.word	.L325,.L452
	.byte	6
	.byte	'pos_dat',0,1,130,5,13
	.word	.L318,.L453
	.byte	6
	.byte	'temp_data',0,1,131,5,13
	.word	.L330,.L454
	.byte	6
	.byte	'valid_num',0,1,132,5,13
	.word	.L330,.L455
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('number_conversion_ascii')
	.sect	'.debug_abbrev'
.L309:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('number_conversion_ascii')
	.sect	'.debug_line'
.L310:
	.word	.L1098-.L1097
.L1097:
	.half	3
	.word	.L1100-.L1099
.L1099:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L1100:
	.byte	5,14,7,0,5,2
	.word	.L234
	.byte	3,254,4,1,5,23,9
	.half	.L570-.L234
	.byte	3,5,1,5,5,9
	.half	.L572-.L570
	.byte	3,2,1,5,9,7,9
	.half	.L1101-.L572
	.byte	3,3,1,5,23,7,9
	.half	.L1102-.L1101
	.byte	3,2,1,5,16,9
	.half	.L156-.L1102
	.byte	3,2,1,5,25,9
	.half	.L158-.L156
	.byte	3,2,1,5,32,9
	.half	.L1103-.L158
	.byte	1,5,16,9
	.half	.L1104-.L1103
	.byte	1,5,30,9
	.half	.L1105-.L1104
	.byte	3,1,1,5,23,9
	.half	.L1106-.L1105
	.byte	3,1,1,5,17,9
	.half	.L574-.L1106
	.byte	3,2,1,7,9
	.half	.L1107-.L574
	.byte	3,2,1,5,15,9
	.half	.L159-.L1107
	.byte	3,2,1,5,16,9
	.half	.L157-.L159
	.byte	3,118,1,5,5,9
	.half	.L160-.L157
	.byte	3,12,1,5,16,9
	.half	.L155-.L160
	.byte	3,4,1,5,32,9
	.half	.L163-.L155
	.byte	3,2,1,5,13,9
	.half	.L576-.L163
	.byte	3,1,1,5,27,7,9
	.half	.L1108-.L576
	.byte	3,2,1,5,36,9
	.half	.L577-.L1108
	.byte	1,5,27,9
	.half	.L164-.L577
	.byte	3,4,1,5,16,9
	.half	.L165-.L164
	.byte	3,3,1,5,30,9
	.half	.L1109-.L165
	.byte	3,2,1,5,21,9
	.half	.L578-.L1109
	.byte	1,5,23,9
	.half	.L1110-.L578
	.byte	3,1,1,5,17,9
	.half	.L579-.L1110
	.byte	3,2,1,7,9
	.half	.L1111-.L579
	.byte	3,2,1,5,15,9
	.half	.L166-.L1111
	.byte	3,2,1,5,16,9
	.half	.L162-.L166
	.byte	3,107,1,5,5,9
	.half	.L161-.L162
	.byte	3,24,1,5,1,9
	.half	.L168-.L161
	.byte	3,1,1,7,9
	.half	.L312-.L168
	.byte	0,1,1
.L1098:
	.sdecl	'.debug_ranges',debug,cluster('number_conversion_ascii')
	.sect	'.debug_ranges'
.L311:
	.word	-1,.L234,0,.L312-.L234,0,0
	.sdecl	'.debug_info',debug,cluster('printf_reverse_order')
	.sect	'.debug_info'
.L313:
	.word	338
	.half	3
	.word	.L314
	.byte	4,1
	.byte	'../libraries/zf_common/zf_common_function.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L316,.L315
	.byte	2
	.word	.L239
	.byte	3
	.byte	'printf_reverse_order',0,1,192,5,13,1,1
	.word	.L236,.L456,.L235
	.byte	4
	.byte	'd_buff',0,1,192,5,41
	.word	.L409,.L457
	.byte	4
	.byte	'len',0,1,192,5,56
	.word	.L318,.L458
	.byte	5
	.word	.L236,.L456
	.byte	6
	.byte	'i',0,1,194,5,12
	.word	.L318,.L459
	.byte	6
	.byte	'temp_data',0,1,195,5,11
	.word	.L416,.L460
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('printf_reverse_order')
	.sect	'.debug_abbrev'
.L314:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('printf_reverse_order')
	.sect	'.debug_line'
.L315:
	.word	.L1113-.L1112
.L1112:
	.half	3
	.word	.L1115-.L1114
.L1114:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_common/zf_common_function.c',0,0,0,0,0
.L1115:
	.byte	5,11,7,0,5,2
	.word	.L236
	.byte	3,195,5,1,5,27,9
	.half	.L580-.L236
	.byte	1,5,32,9
	.half	.L170-.L580
	.byte	3,2,1,5,36,9
	.half	.L1116-.L170
	.byte	1,5,27,9
	.half	.L1117-.L1116
	.byte	1,5,20,9
	.half	.L581-.L1117
	.byte	3,1,1,5,24,9
	.half	.L1118-.L581
	.byte	1,5,15,9
	.half	.L1119-.L1118
	.byte	1,5,37,9
	.half	.L1120-.L1119
	.byte	1,5,29,9
	.half	.L1121-.L1120
	.byte	1,5,15,9
	.half	.L1122-.L1121
	.byte	3,1,1,5,19,9
	.half	.L1123-.L1122
	.byte	1,5,31,9
	.half	.L1124-.L1123
	.byte	3,124,1,5,22,9
	.half	.L169-.L1124
	.byte	1,5,20,9
	.half	.L1125-.L169
	.byte	1,5,27,9
	.half	.L1126-.L1125
	.byte	1,5,1,7,9
	.half	.L1127-.L1126
	.byte	3,6,1,7,9
	.half	.L317-.L1127
	.byte	0,1,1
.L1113:
	.sdecl	'.debug_ranges',debug,cluster('printf_reverse_order')
	.sect	'.debug_ranges'
.L316:
	.word	-1,.L236,0,.L317-.L236,0,0
	.sdecl	'.debug_loc',debug,cluster('func_double_to_str')
	.sect	'.debug_loc'
.L391:
	.word	-1,.L228,.L104-.L228,.L113-.L228
	.half	1
	.byte	88
	.word	.L113-.L228,.L106-.L228
	.half	1
	.byte	84
	.word	0,0
.L386:
	.word	-1,.L228,.L545-.L228,.L546-.L228
	.half	1
	.byte	82
	.word	.L547-.L228,.L548-.L228
	.half	1
	.byte	82
	.word	.L549-.L228,.L121-.L228
	.half	1
	.byte	82
	.word	0,0
.L385:
	.word	-1,.L228,.L537-.L228,.L538-.L228
	.half	1
	.byte	82
	.word	.L538-.L228,.L106-.L228
	.half	1
	.byte	92
	.word	.L541-.L228,.L542-.L228
	.half	1
	.byte	84
	.word	0,0
.L388:
	.word	-1,.L228,0,.L380-.L228
	.half	3
	.byte	145,168,127
	.word	0,0
.L390:
	.word	-1,.L228,0,.L380-.L228
	.half	2
	.byte	145,88
	.word	0,0
.L227:
	.word	-1,.L228,0,.L531-.L228
	.half	2
	.byte	138,0
	.word	.L531-.L228,.L380-.L228
	.half	3
	.byte	138,216,0
	.word	.L380-.L228,.L380-.L228
	.half	2
	.byte	138,0
	.word	0,0
.L382:
	.word	-1,.L228,0,.L532-.L228
	.half	2
	.byte	144,34
	.word	.L105-.L228,.L536-.L228
	.half	2
	.byte	144,37
	.word	.L539-.L228,.L540-.L228
	.half	2
	.byte	144,37
	.word	.L543-.L228,.L544-.L228
	.half	2
	.byte	144,37
	.word	0,0
.L383:
	.word	-1,.L228,0,.L384-.L228
	.half	1
	.byte	86
	.word	.L535-.L228,.L380-.L228
	.half	1
	.byte	89
	.word	0,0
.L381:
	.word	-1,.L228,0,.L533-.L228
	.half	1
	.byte	100
	.word	.L534-.L228,.L380-.L228
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_float_to_str')
	.sect	'.debug_loc'
.L371:
	.word	-1,.L224,.L65-.L224,.L74-.L224
	.half	1
	.byte	92
	.word	.L74-.L224,.L67-.L224
	.half	1
	.byte	84
	.word	0,0
.L366:
	.word	-1,.L224,.L504-.L224,.L67-.L224
	.half	1
	.byte	82
	.word	0,0
.L365:
	.word	-1,.L224,.L497-.L224,.L67-.L224
	.half	1
	.byte	88
	.word	0,0
.L368:
	.word	-1,.L224,0,.L360-.L224
	.half	2
	.byte	145,72
	.word	0,0
.L370:
	.word	-1,.L224,0,.L360-.L224
	.half	2
	.byte	145,104
	.word	0,0
.L223:
	.word	-1,.L224,0,.L492-.L224
	.half	2
	.byte	138,0
	.word	.L492-.L224,.L360-.L224
	.half	2
	.byte	138,56
	.word	.L360-.L224,.L360-.L224
	.half	2
	.byte	138,0
	.word	0,0
.L362:
	.word	-1,.L224,0,.L493-.L224
	.half	1
	.byte	84
	.word	.L66-.L224,.L497-.L224
	.half	1
	.byte	90
	.word	.L498-.L224,.L499-.L224
	.half	1
	.byte	90
	.word	.L68-.L224,.L500-.L224
	.half	1
	.byte	90
	.word	.L500-.L224,.L501-.L224
	.half	1
	.byte	84
	.word	.L502-.L224,.L503-.L224
	.half	1
	.byte	90
	.word	.L503-.L224,.L74-.L224
	.half	1
	.byte	95
	.word	0,0
.L363:
	.word	-1,.L224,0,.L494-.L224
	.half	1
	.byte	85
	.word	.L364-.L224,.L65-.L224
	.half	1
	.byte	91
	.word	.L505-.L224,.L506-.L224
	.half	1
	.byte	91
	.word	.L507-.L224,.L508-.L224
	.half	1
	.byte	91
	.word	0,0
.L361:
	.word	-1,.L224,0,.L495-.L224
	.half	1
	.byte	100
	.word	.L496-.L224,.L360-.L224
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_get_greatest_common_divisor')
	.sect	'.debug_loc'
.L209:
	.word	-1,.L210,0,.L319-.L210
	.half	2
	.byte	138,0
	.word	0,0
.L320:
	.word	-1,.L210,0,.L319-.L210
	.half	1
	.byte	84
	.word	0,0
.L321:
	.word	-1,.L210,0,.L319-.L210
	.half	1
	.byte	85
	.word	.L461-.L210,.L319-.L210
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_hex_to_str')
	.sect	'.debug_loc'
.L407:
	.word	-1,.L232,.L566-.L232,.L567-.L232
	.half	1
	.byte	95
	.word	.L149-.L232,.L568-.L232
	.half	1
	.byte	95
	.word	.L569-.L232,.L148-.L232
	.half	1
	.byte	95
	.word	0,0
.L406:
	.word	-1,.L232,0,.L399-.L232
	.half	2
	.byte	145,112
	.word	0,0
.L231:
	.word	-1,.L232,0,.L561-.L232
	.half	2
	.byte	138,0
	.word	.L561-.L232,.L399-.L232
	.half	2
	.byte	138,32
	.word	.L399-.L232,.L399-.L232
	.half	2
	.byte	138,0
	.word	0,0
.L404:
	.word	-1,.L232,0,.L399-.L232
	.half	2
	.byte	145,96
	.word	0,0
.L401:
	.word	-1,.L232,0,.L562-.L232
	.half	1
	.byte	84
	.word	.L565-.L232,.L399-.L232
	.half	1
	.byte	88
	.word	0,0
.L400:
	.word	-1,.L232,0,.L563-.L232
	.half	1
	.byte	100
	.word	.L564-.L232,.L399-.L232
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_int_to_str')
	.sect	'.debug_loc'
.L339:
	.word	-1,.L216,.L20-.L216,.L470-.L216
	.half	1
	.byte	95
	.word	.L24-.L216,.L471-.L216
	.half	1
	.byte	95
	.word	.L473-.L216,.L22-.L216
	.half	1
	.byte	95
	.word	0,0
.L338:
	.word	-1,.L216,0,.L333-.L216
	.half	2
	.byte	145,112
	.word	0,0
.L215:
	.word	-1,.L216,0,.L465-.L216
	.half	2
	.byte	138,0
	.word	.L465-.L216,.L333-.L216
	.half	2
	.byte	138,16
	.word	.L333-.L216,.L333-.L216
	.half	2
	.byte	138,0
	.word	0,0
.L335:
	.word	-1,.L216,0,.L466-.L216
	.half	1
	.byte	84
	.word	.L469-.L216,.L333-.L216
	.half	1
	.byte	88
	.word	0,0
.L340:
	.word	-1,.L216,.L472-.L216,.L27-.L216
	.half	1
	.byte	81
	.word	0,0
.L334:
	.word	-1,.L216,0,.L467-.L216
	.half	1
	.byte	100
	.word	.L468-.L216,.L333-.L216
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_soft_delay')
	.sect	'.debug_loc'
.L211:
	.word	-1,.L212,0,.L322-.L212
	.half	2
	.byte	138,0
	.word	0,0
.L324:
	.word	-1,.L212,0,.L322-.L212
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_str_to_double')
	.sect	'.debug_loc'
.L225:
	.word	-1,.L226,0,.L509-.L226
	.half	2
	.byte	138,0
	.word	.L509-.L226,.L373-.L226
	.half	2
	.byte	138,8
	.word	.L373-.L226,.L373-.L226
	.half	2
	.byte	138,0
	.word	0,0
.L379:
	.word	-1,.L226,.L514-.L226,.L515-.L226
	.half	2
	.byte	144,32
	.word	.L88-.L226,.L373-.L226
	.half	2
	.byte	145,120
	.word	.L520-.L226,.L521-.L226
	.half	2
	.byte	144,34
	.word	.L521-.L226,.L522-.L226
	.half	2
	.byte	144,33
	.word	.L523-.L226,.L524-.L226
	.half	2
	.byte	144,35
	.word	.L526-.L226,.L527-.L226
	.half	2
	.byte	144,35
	.word	0,0
.L376:
	.word	-1,.L226,.L512-.L226,.L373-.L226
	.half	1
	.byte	88
	.word	0,0
.L374:
	.word	-1,.L226,0,.L510-.L226
	.half	1
	.byte	100
	.word	.L511-.L226,.L373-.L226
	.half	1
	.byte	111
	.word	0,0
.L377:
	.word	-1,.L226,.L516-.L226,.L517-.L226
	.half	2
	.byte	144,37
	.word	.L97-.L226,.L528-.L226
	.half	2
	.byte	144,37
	.word	.L90-.L226,.L530-.L226
	.half	2
	.byte	144,37
	.word	0,0
.L378:
	.word	-1,.L226,.L513-.L226,.L99-.L226
	.half	2
	.byte	144,38
	.word	.L518-.L226,.L519-.L226
	.half	2
	.byte	144,38
	.word	.L100-.L226,.L525-.L226
	.half	2
	.byte	144,38
	.word	.L528-.L226,.L529-.L226
	.half	2
	.byte	144,38
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_str_to_float')
	.sect	'.debug_loc'
.L221:
	.word	-1,.L222,0,.L353-.L222
	.half	2
	.byte	138,0
	.word	0,0
.L359:
	.word	-1,.L222,.L49-.L222,.L353-.L222
	.half	1
	.byte	91
	.word	.L489-.L222,.L490-.L222
	.half	1
	.byte	84
	.word	0,0
.L356:
	.word	-1,.L222,.L486-.L222,.L353-.L222
	.half	1
	.byte	88
	.word	0,0
.L354:
	.word	-1,.L222,0,.L484-.L222
	.half	1
	.byte	100
	.word	.L485-.L222,.L353-.L222
	.half	1
	.byte	111
	.word	0,0
.L357:
	.word	-1,.L222,.L487-.L222,.L353-.L222
	.half	1
	.byte	89
	.word	.L491-.L222,.L353-.L222
	.half	1
	.byte	82
	.word	0,0
.L358:
	.word	-1,.L222,.L488-.L222,.L353-.L222
	.half	1
	.byte	90
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_str_to_hex')
	.sect	'.debug_loc'
.L398:
	.word	-1,.L230,.L127-.L230,.L392-.L230
	.half	1
	.byte	83
	.word	0,0
.L229:
	.word	-1,.L230,0,.L392-.L230
	.half	2
	.byte	138,0
	.word	0,0
.L396:
	.word	-1,.L230,.L555-.L230,.L556-.L230
	.half	1
	.byte	81
	.word	.L560-.L230,.L392-.L230
	.half	1
	.byte	81
	.word	.L554-.L230,.L392-.L230
	.half	1
	.byte	82
	.word	0,0
.L393:
	.word	-1,.L230,0,.L550-.L230
	.half	1
	.byte	100
	.word	.L551-.L230,.L392-.L230
	.half	1
	.byte	111
	.word	.L552-.L230,.L553-.L230
	.half	1
	.byte	100
	.word	0,0
.L395:
	.word	-1,.L230,.L553-.L230,.L554-.L230
	.half	1
	.byte	82
	.word	0,0
.L397:
	.word	-1,.L230,.L557-.L230,.L131-.L230
	.half	1
	.byte	95
	.word	.L558-.L230,.L134-.L230
	.half	1
	.byte	95
	.word	.L559-.L230,.L137-.L230
	.half	1
	.byte	95
	.word	.L133-.L230,.L560-.L230
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_str_to_int')
	.sect	'.debug_loc'
.L213:
	.word	-1,.L214,0,.L326-.L214
	.half	2
	.byte	138,0
	.word	0,0
.L331:
	.word	-1,.L214,.L464-.L214,.L326-.L214
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L328:
	.word	-1,.L214,0,.L462-.L214
	.half	1
	.byte	100
	.word	.L463-.L214,.L326-.L214
	.half	1
	.byte	111
	.word	0,0
.L332:
	.word	-1,.L214,.L9-.L214,.L326-.L214
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_str_to_uint')
	.sect	'.debug_loc'
.L217:
	.word	-1,.L218,0,.L341-.L218
	.half	2
	.byte	138,0
	.word	0,0
.L342:
	.word	-1,.L218,0,.L474-.L218
	.half	1
	.byte	100
	.word	.L475-.L218,.L341-.L218
	.half	1
	.byte	111
	.word	0,0
.L344:
	.word	-1,.L218,.L33-.L218,.L341-.L218
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('func_uint_to_str')
	.sect	'.debug_loc'
.L351:
	.word	-1,.L220,.L40-.L220,.L481-.L220
	.half	1
	.byte	95
	.word	.L43-.L220,.L482-.L220
	.half	1
	.byte	95
	.word	.L483-.L220,.L42-.L220
	.half	1
	.byte	95
	.word	0,0
.L350:
	.word	-1,.L220,0,.L345-.L220
	.half	2
	.byte	145,112
	.word	0,0
.L219:
	.word	-1,.L220,0,.L476-.L220
	.half	2
	.byte	138,0
	.word	.L476-.L220,.L345-.L220
	.half	2
	.byte	138,16
	.word	.L345-.L220,.L345-.L220
	.half	2
	.byte	138,0
	.word	0,0
.L347:
	.word	-1,.L220,0,.L477-.L220
	.half	1
	.byte	84
	.word	.L480-.L220,.L345-.L220
	.half	1
	.byte	88
	.word	0,0
.L346:
	.word	-1,.L220,0,.L478-.L220
	.half	1
	.byte	100
	.word	.L479-.L220,.L345-.L220
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('number_conversion_ascii')
	.sect	'.debug_loc'
.L448:
	.word	-1,.L234,0,.L156-.L234
	.half	1
	.byte	84
	.word	.L570-.L234,.L571-.L234
	.half	1
	.byte	82
	.word	.L155-.L234,.L163-.L234
	.half	1
	.byte	84
	.word	0,0
.L452:
	.word	0,0
.L450:
	.word	-1,.L234,0,.L447-.L234
	.half	1
	.byte	85
	.word	0,0
.L233:
	.word	-1,.L234,0,.L447-.L234
	.half	2
	.byte	138,0
	.word	0,0
.L449:
	.word	-1,.L234,0,.L447-.L234
	.half	1
	.byte	100
	.word	0,0
.L453:
	.word	0,0
.L451:
	.word	-1,.L234,0,.L447-.L234
	.half	1
	.byte	86
	.word	0,0
.L454:
	.word	-1,.L234,.L576-.L234,.L577-.L234
	.half	1
	.byte	81
	.word	.L577-.L234,.L164-.L234
	.half	5
	.byte	144,32,157,32,0
	.word	.L164-.L234,.L165-.L234
	.half	1
	.byte	81
	.word	.L165-.L234,.L578-.L234
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L455:
	.word	-1,.L234,.L572-.L234,.L573-.L234
	.half	1
	.byte	95
	.word	.L574-.L234,.L575-.L234
	.half	1
	.byte	95
	.word	.L579-.L234,.L447-.L234
	.half	1
	.byte	95
	.word	.L571-.L234,.L447-.L234
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('printf_reverse_order')
	.sect	'.debug_loc'
.L457:
	.word	-1,.L236,0,.L456-.L236
	.half	1
	.byte	100
	.word	0,0
.L459:
	.word	-1,.L236,.L580-.L236,.L456-.L236
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L458:
	.word	-1,.L236,0,.L456-.L236
	.half	1
	.byte	84
	.word	0,0
.L235:
	.word	-1,.L236,0,.L456-.L236
	.half	2
	.byte	138,0
	.word	0,0
.L460:
	.word	-1,.L236,.L581-.L236,.L169-.L236
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('zf_sprintf')
	.sect	'.debug_loc'
.L415:
	.word	-1,.L238,.L586-.L238,.L408-.L238
	.half	1
	.byte	110
	.word	0,0
.L410:
	.word	-1,.L238,0,.L172-.L238
	.half	1
	.byte	100
	.word	.L583-.L238,.L408-.L238
	.half	1
	.byte	108
	.word	.L597-.L238,.L598-.L238
	.half	1
	.byte	100
	.word	.L608-.L238,.L609-.L238
	.half	1
	.byte	100
	.word	.L616-.L238,.L617-.L238
	.half	1
	.byte	100
	.word	.L624-.L238,.L625-.L238
	.half	1
	.byte	100
	.word	.L632-.L238,.L633-.L238
	.half	1
	.byte	100
	.word	.L640-.L238,.L641-.L238
	.half	1
	.byte	100
	.word	.L646-.L238,.L647-.L238
	.half	1
	.byte	100
	.word	0,0
.L413:
	.word	-1,.L238,.L585-.L238,.L408-.L238
	.half	1
	.byte	88
	.word	.L648-.L238,.L408-.L238
	.half	1
	.byte	82
	.word	0,0
.L419:
	.word	-1,.L238,.L589-.L238,.L181-.L238
	.half	1
	.byte	95
	.word	0,0
.L412:
	.word	-1,.L238,0,.L172-.L238
	.half	1
	.byte	101
	.word	.L584-.L238,.L408-.L238
	.half	1
	.byte	109
	.word	0,0
.L423:
	.word	-1,.L238,.L590-.L238,.L175-.L238
	.half	1
	.byte	89
	.word	.L591-.L238,.L592-.L238
	.half	1
	.byte	84
	.word	0,0
.L427:
	.word	-1,.L238,.L600-.L238,.L185-.L238
	.half	2
	.byte	144,37
	.word	0,0
.L431:
	.word	-1,.L238,.L619-.L238,.L620-.L238
	.half	1
	.byte	84
	.word	0,0
.L435:
	.word	-1,.L238,.L627-.L238,.L628-.L238
	.half	1
	.byte	84
	.word	0,0
.L439:
	.word	-1,.L238,.L635-.L238,.L636-.L238
	.half	1
	.byte	84
	.word	0,0
.L445:
	.word	-1,.L238,.L644-.L238,.L645-.L238
	.half	1
	.byte	84
	.word	0,0
.L442:
	.word	-1,.L238,.L643-.L238,.L183-.L238
	.half	1
	.byte	111
	.word	0,0
.L417:
	.word	-1,.L238,.L587-.L238,.L588-.L238
	.half	5
	.byte	144,32,157,32,0
	.word	.L173-.L238,.L207-.L238
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L424:
	.word	-1,.L238,.L592-.L238,.L190-.L238
	.half	1
	.byte	82
	.word	.L593-.L238,.L594-.L238
	.half	1
	.byte	95
	.word	.L190-.L238,.L175-.L238
	.half	1
	.byte	95
	.word	.L595-.L238,.L596-.L238
	.half	1
	.byte	84
	.word	.L599-.L238,.L598-.L238
	.half	1
	.byte	84
	.word	0,0
.L428:
	.word	-1,.L238,.L601-.L238,.L602-.L238
	.half	1
	.byte	82
	.word	.L603-.L238,.L604-.L238
	.half	1
	.byte	95
	.word	.L192-.L238,.L605-.L238
	.half	1
	.byte	95
	.word	.L606-.L238,.L607-.L238
	.half	1
	.byte	84
	.word	.L610-.L238,.L609-.L238
	.half	1
	.byte	84
	.word	.L611-.L238,.L193-.L238
	.half	1
	.byte	82
	.word	.L194-.L238,.L612-.L238
	.half	1
	.byte	82
	.word	.L613-.L238,.L185-.L238
	.half	1
	.byte	95
	.word	.L614-.L238,.L615-.L238
	.half	1
	.byte	84
	.word	.L618-.L238,.L617-.L238
	.half	1
	.byte	84
	.word	0,0
.L432:
	.word	-1,.L238,.L620-.L238,.L621-.L238
	.half	1
	.byte	82
	.word	.L622-.L238,.L182-.L238
	.half	1
	.byte	95
	.word	.L621-.L238,.L623-.L238
	.half	1
	.byte	84
	.word	.L626-.L238,.L625-.L238
	.half	1
	.byte	84
	.word	0,0
.L436:
	.word	-1,.L238,.L628-.L238,.L629-.L238
	.half	1
	.byte	82
	.word	.L630-.L238,.L176-.L238
	.half	1
	.byte	95
	.word	.L629-.L238,.L631-.L238
	.half	1
	.byte	84
	.word	.L634-.L238,.L633-.L238
	.half	1
	.byte	84
	.word	0,0
.L440:
	.word	-1,.L238,.L636-.L238,.L637-.L238
	.half	1
	.byte	82
	.word	.L638-.L238,.L184-.L238
	.half	1
	.byte	95
	.word	.L637-.L238,.L639-.L238
	.half	1
	.byte	84
	.word	.L642-.L238,.L641-.L238
	.half	1
	.byte	84
	.word	0,0
.L446:
	.word	0,0
.L422:
	.word	-1,.L238,0,.L408-.L238
	.half	2
	.byte	145,88
	.word	0,0
.L426:
	.word	-1,.L238,0,.L408-.L238
	.half	2
	.byte	145,88
	.word	0,0
.L430:
	.word	-1,.L238,0,.L408-.L238
	.half	2
	.byte	145,88
	.word	0,0
.L434:
	.word	-1,.L238,0,.L408-.L238
	.half	2
	.byte	145,88
	.word	0,0
.L438:
	.word	-1,.L238,0,.L408-.L238
	.half	2
	.byte	145,88
	.word	0,0
.L444:
	.word	-1,.L238,0,.L408-.L238
	.half	2
	.byte	145,88
	.word	0,0
.L237:
	.word	-1,.L238,0,.L582-.L238
	.half	2
	.byte	138,0
	.word	.L582-.L238,.L408-.L238
	.half	2
	.byte	138,40
	.word	.L408-.L238,.L408-.L238
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1128:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('func_get_greatest_common_divisor')
	.sect	'.debug_frame'
	.word	24
	.word	.L1128,.L210,.L319-.L210
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('func_soft_delay')
	.sect	'.debug_frame'
	.word	24
	.word	.L1128,.L212,.L322-.L212
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('func_str_to_int')
	.sect	'.debug_frame'
	.word	12
	.word	.L1128,.L214,.L326-.L214
	.sdecl	'.debug_frame',debug,cluster('func_int_to_str')
	.sect	'.debug_frame'
	.word	36
	.word	.L1128,.L216,.L333-.L216
	.byte	4
	.word	(.L465-.L216)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L333-.L465)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('func_str_to_uint')
	.sect	'.debug_frame'
	.word	12
	.word	.L1128,.L218,.L341-.L218
	.sdecl	'.debug_frame',debug,cluster('func_uint_to_str')
	.sect	'.debug_frame'
	.word	36
	.word	.L1128,.L220,.L345-.L220
	.byte	4
	.word	(.L476-.L220)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L345-.L476)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('func_str_to_float')
	.sect	'.debug_frame'
	.word	12
	.word	.L1128,.L222,.L353-.L222
	.sdecl	'.debug_frame',debug,cluster('func_float_to_str')
	.sect	'.debug_frame'
	.word	36
	.word	.L1128,.L224,.L360-.L224
	.byte	4
	.word	(.L492-.L224)/2
	.byte	19,56,22,26,3,19,138,56,4
	.word	(.L360-.L492)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('func_str_to_double')
	.sect	'.debug_frame'
	.word	36
	.word	.L1128,.L226,.L373-.L226
	.byte	4
	.word	(.L509-.L226)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L373-.L509)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('func_double_to_str')
	.sect	'.debug_frame'
	.word	36
	.word	.L1128,.L228,.L380-.L228
	.byte	4
	.word	(.L531-.L228)/2
	.byte	19,216,0,22,26,4,19,138,216,0,4
	.word	(.L380-.L531)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('func_str_to_hex')
	.sect	'.debug_frame'
	.word	12
	.word	.L1128,.L230,.L392-.L230
	.sdecl	'.debug_frame',debug,cluster('func_hex_to_str')
	.sect	'.debug_frame'
	.word	36
	.word	.L1128,.L232,.L399-.L232
	.byte	4
	.word	(.L561-.L232)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L399-.L561)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('number_conversion_ascii')
	.sect	'.debug_frame'
	.word	24
	.word	.L1128,.L234,.L447-.L234
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('printf_reverse_order')
	.sect	'.debug_frame'
	.word	20
	.word	.L1128,.L236,.L456-.L236
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('zf_sprintf')
	.sect	'.debug_frame'
	.word	36
	.word	.L1128,.L238,.L408-.L238
	.byte	4
	.word	(.L582-.L238)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L408-.L582)/2
	.byte	19,0,8,26,0,0
	; Module end
