	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc27732a --dep-file=IfxGtm_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.c'

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	146007
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	232
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	235
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	280
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	292
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	372
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	346
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	378
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	378
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	346
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	526
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	487
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	842
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1413
	.byte	4,2,35,0,0,14,4
	.word	487
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	487
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	487
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	487
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	487
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	487
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1541
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	487
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	487
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	487
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	487
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	487
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1756
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	487
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	487
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	487
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	487
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	487
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1971
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	487
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	487
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	487
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	487
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	487
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2188
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2408
	.byte	4,2,35,0,0,14,24
	.word	487
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	487
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	487
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	487
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	487
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	487
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	487
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	487
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2731
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	487
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	487
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	487
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	487
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	487
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	487
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	487
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3035
	.byte	4,2,35,0,0,14,8
	.word	487
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3360
	.byte	4,2,35,0,0,14,12
	.word	487
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3700
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	464
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4066
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	464
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4352
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	464
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4499
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	464
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4668
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	504
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4840
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	504
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	504
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5015
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	464
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5189
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	464
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	487
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5363
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	464
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	487
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5539
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5695
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	504
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	487
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6028
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6376
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	487
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	464
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6500
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6584
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	487
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6764
	.byte	4,2,35,0,0,14,76
	.word	487
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	464
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7017
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	487
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7104
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	802
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1373
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1492
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1532
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1716
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1931
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2148
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2368
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1532
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2682
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2722
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2995
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3311
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3351
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3651
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3691
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4026
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4312
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3351
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4459
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4628
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4800
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4975
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5149
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5323
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5499
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5655
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5988
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6336
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3351
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6460
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6709
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6968
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7008
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7064
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7631
	.byte	4,3,35,252,1,0,16
	.word	7671
	.byte	3
	.word	8274
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8279
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	487
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8284
	.byte	6,0,19
	.word	240
	.byte	20
	.word	266
	.byte	6,0,19
	.word	301
	.byte	20
	.word	333
	.byte	6,0,19
	.word	383
	.byte	20
	.word	402
	.byte	6,0,19
	.word	418
	.byte	20
	.word	433
	.byte	20
	.word	447
	.byte	6,0,19
	.word	8387
	.byte	20
	.word	8415
	.byte	20
	.word	8429
	.byte	20
	.word	8447
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8540
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	464
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	480
	.byte	22,1,3
	.word	8608
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8610
	.byte	21
	.byte	'boolean',0,6,101,29
	.word	487
	.byte	21
	.byte	'uint8',0,6,105,29
	.word	487
	.byte	21
	.byte	'uint16',0,6,109,29
	.word	504
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,6,113,29
	.word	8678
	.byte	21
	.byte	'uint64',0,6,118,29
	.word	346
	.byte	21
	.byte	'sint16',0,6,126,29
	.word	8540
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,6,131,1,29
	.word	8744
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,6,138,1,29
	.word	8772
	.byte	21
	.byte	'float32',0,6,167,1,29
	.word	292
	.byte	21
	.byte	'pvoid',0,7,57,28
	.word	378
	.byte	21
	.byte	'Ifx_TickTime',0,7,79,28
	.word	8772
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,8,49,16,4,11
	.byte	'EN0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN22',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'EN23',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'EN24',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'EN25',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'EN26',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'EN27',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'EN28',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'EN29',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'EN30',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'EN31',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_ACCEN0_Bits',0,8,83,3
	.word	8873
	.byte	10
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,8,86,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_GTM_ACCEN1_Bits',0,8,89,3
	.word	9430
	.byte	10
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,8,92,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,8,100,3
	.word	9507
	.byte	10
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,8,103,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,8,111,3
	.word	9677
	.byte	10
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,8,114,16,4,11
	.byte	'TO_ADDR',0,4
	.word	8857
	.byte	20,12,2,35,0,11
	.byte	'TO_W1R0',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	11,0,2,35,0,0,21
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,8,119,3
	.word	9847
	.byte	10
	.byte	'_Ifx_GTM_AFD_CH_BUF_ACC_Bits',0,8,122,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_AFD_CH_BUF_ACC_Bits',0,8,126,3
	.word	9975
	.byte	10
	.byte	'_Ifx_GTM_ARU_ARU_ACCESS_Bits',0,8,129,1,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'RREQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'WREQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	8857
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_ARU_ACCESS_Bits',0,8,136,1,3
	.word	10085
	.byte	10
	.byte	'_Ifx_GTM_ARU_DATA_H_Bits',0,8,139,1,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DATA_H_Bits',0,8,143,1,3
	.word	10251
	.byte	10
	.byte	'_Ifx_GTM_ARU_DATA_L_Bits',0,8,146,1,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DATA_L_Bits',0,8,150,1,3
	.word	10355
	.byte	10
	.byte	'_Ifx_GTM_ARU_DBG_ACCESS0_Bits',0,8,153,1,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_ACCESS0_Bits',0,8,157,1,3
	.word	10459
	.byte	10
	.byte	'_Ifx_GTM_ARU_DBG_ACCESS1_Bits',0,8,160,1,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_ACCESS1_Bits',0,8,164,1,3
	.word	10572
	.byte	10
	.byte	'_Ifx_GTM_ARU_DBG_DATA0_H_Bits',0,8,167,1,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA0_H_Bits',0,8,171,1,3
	.word	10685
	.byte	10
	.byte	'_Ifx_GTM_ARU_DBG_DATA0_L_Bits',0,8,174,1,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA0_L_Bits',0,8,178,1,3
	.word	10799
	.byte	10
	.byte	'_Ifx_GTM_ARU_DBG_DATA1_H_Bits',0,8,181,1,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA1_H_Bits',0,8,185,1,3
	.word	10913
	.byte	10
	.byte	'_Ifx_GTM_ARU_DBG_DATA1_L_Bits',0,8,188,1,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	29,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA1_L_Bits',0,8,192,1,3
	.word	11027
	.byte	10
	.byte	'_Ifx_GTM_ARU_IRQ_EN_Bits',0,8,195,1,16,4,11
	.byte	'NEW_DATA0_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'NEW_DATA1_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ACC_ACK_IRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_EN_Bits',0,8,201,1,3
	.word	11141
	.byte	10
	.byte	'_Ifx_GTM_ARU_IRQ_FORCINT_Bits',0,8,204,1,16,4,11
	.byte	'TRG_NEW_DATA0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_NEW_DATA',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_ACC_ACK',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_FORCINT_Bits',0,8,210,1,3
	.word	11310
	.byte	10
	.byte	'_Ifx_GTM_ARU_IRQ_MODE_Bits',0,8,213,1,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_MODE_Bits',0,8,217,1,3
	.word	11479
	.byte	10
	.byte	'_Ifx_GTM_ARU_IRQ_NOTIFY_Bits',0,8,220,1,16,4,11
	.byte	'NEW_DATA0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'NEW_DATA1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ACC_ACK',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_NOTIFY_Bits',0,8,226,1,3
	.word	11590
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_ACT_TB_Bits',0,8,229,1,16,4,11
	.byte	'ACT_TB',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	8857
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_ACT_TB_Bits',0,8,235,1,3
	.word	11746
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_ENDIS_CTRL_Bits',0,8,238,1,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_CTRL_Bits',0,8,249,1,3
	.word	11900
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_ENDIS_STAT_Bits',0,8,252,1,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_STAT_Bits',0,8,135,2,3
	.word	12190
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_FUPD_CTRL_Bits',0,8,138,2,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_FUPD_CTRL_Bits',0,8,156,2,3
	.word	12480
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_GLB_CTRL_Bits',0,8,159,2,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_GLB_CTRL_Bits',0,8,179,2,3
	.word	12913
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_INT_TRIG_Bits',0,8,182,2,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_INT_TRIG_Bits',0,8,193,2,3
	.word	13363
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_OUTEN_CTRL_Bits',0,8,196,2,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_CTRL_Bits',0,8,207,2,3
	.word	13633
	.byte	10
	.byte	'_Ifx_GTM_ATOM_AGC_OUTEN_STAT_Bits',0,8,210,2,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_STAT_Bits',0,8,221,2,3
	.word	13923
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_CM0_Bits',0,8,224,2,16,4,11
	.byte	'CM0',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CM0_Bits',0,8,228,2,3
	.word	14213
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_CM1_Bits',0,8,231,2,16,4,11
	.byte	'CM1',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CM1_Bits',0,8,235,2,3
	.word	14318
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_CN0_Bits',0,8,238,2,16,4,11
	.byte	'CN0',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CN0_Bits',0,8,242,2,3
	.word	14423
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_CTRL_Bits',0,8,245,2,16,4,11
	.byte	'MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'TB12_SEL',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ACB',0,4
	.word	8857
	.byte	5,23,2,35,0,11
	.byte	'CMP_CTRL',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'SL',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC',0,4
	.word	8857
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'WR_REQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8857
	.byte	3,12,2,35,0,11
	.byte	'RST_CCU0',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'SLA',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'ABM',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CTRL_Bits',0,8,137,3,3
	.word	14528
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_EN_Bits',0,8,140,3,16,4,11
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_EN_Bits',0,8,145,3,3
	.word	14936
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_FORCINT_Bits',0,8,148,3,16,4,11
	.byte	'TRG_CCU0TC',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_CCU1TC',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_FORCINT_Bits',0,8,153,3,3
	.word	15081
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_MODE_Bits',0,8,156,3,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_MODE_Bits',0,8,160,3,3
	.word	15230
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_IRQ_NOTIFY_Bits',0,8,163,3,16,4,11
	.byte	'CCU0TC',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_NOTIFY_Bits',0,8,168,3,3
	.word	15349
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_RDADDR_Bits',0,8,171,3,16,4,11
	.byte	'RDADDR0',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	7,16,2,35,0,11
	.byte	'RDADDR1',0,4
	.word	8857
	.byte	9,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8857
	.byte	7,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_RDADDR_Bits',0,8,177,3,3
	.word	15488
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_SOMC_Bits',0,8,180,3,16,4,11
	.byte	'MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'TB12_SEL',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ACB10',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'ACB42',0,4
	.word	8857
	.byte	3,23,2,35,0,11
	.byte	'CMP_CTRL',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'SL',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'WR_REQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8857
	.byte	7,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'SLA',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'ABM',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMC_Bits',0,8,198,3,3
	.word	15644
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_SOMI_Bits',0,8,201,3,16,4,11
	.byte	'MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ACB0',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	6,21,2,35,0,11
	.byte	'SL',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMI_Bits',0,8,210,3,3
	.word	16017
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_SOMP_Bits',0,8,213,3,16,4,11
	.byte	'MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ADL',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8857
	.byte	5,21,2,35,0,11
	.byte	'SL',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC_SR',0,4
	.word	8857
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8857
	.byte	5,12,2,35,0,11
	.byte	'RST_CCU0',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMP_Bits',0,8,229,3,3
	.word	16217
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_SOMS_Bits',0,8,232,3,16,4,11
	.byte	'MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ACB0',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	6,21,2,35,0,11
	.byte	'SL',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC',0,4
	.word	8857
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8857
	.byte	11,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMS_Bits',0,8,244,3,3
	.word	16561
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_SR0_Bits',0,8,247,3,16,4,11
	.byte	'SR0',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SR0_Bits',0,8,251,3,3
	.word	16818
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_SR1_Bits',0,8,254,3,16,4,11
	.byte	'SR1',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SR1_Bits',0,8,130,4,3
	.word	16923
	.byte	10
	.byte	'_Ifx_GTM_ATOM_CH_STAT_Bits',0,8,133,4,16,4,11
	.byte	'OL',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	15,16,2,35,0,11
	.byte	'ACBI',0,4
	.word	8857
	.byte	5,11,2,35,0,11
	.byte	'DV',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'WRF',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'ACBO',0,4
	.word	8857
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_STAT_Bits',0,8,143,4,3
	.word	17028
	.byte	10
	.byte	'_Ifx_GTM_BRC_EIRQ_EN_Bits',0,8,146,4,16,4,11
	.byte	'DEST_ERR_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'DID_EN0',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'DID_EN1',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'DID_EN2',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'DID_EN3',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'DID_EN4',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'DID_EN5',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'DID_EN6',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'DID_EN7',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'DID_EN8',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'DID_EN9',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'DID_EN10',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'DID_EN11',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_EIRQ_EN_Bits',0,8,162,4,3
	.word	17240
	.byte	10
	.byte	'_Ifx_GTM_BRC_IRQ_EN_Bits',0,8,165,4,16,4,11
	.byte	'DEST_ERR_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'DID_EN0',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'DID_EN1',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'DID_EN2',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'DID_EN3',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'DID_EN4',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'DID_EN5',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'DID_EN6',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'DID_EN7',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'DID_EN8',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'DID_EN9',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'DID_EN10',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'DID_EN11',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_EN_Bits',0,8,181,4,3
	.word	17583
	.byte	10
	.byte	'_Ifx_GTM_BRC_IRQ_FORCINT_Bits',0,8,184,4,16,4,11
	.byte	'TRG_DEST_ERR',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_DID0',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_DID1',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG_DID2',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRG_DID3',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TRG_DID4',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TRG_DID5',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TRG_DID6',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TRG_DID7',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TRG_DID8',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TRG_DID9',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TRG_DID10',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TRG_DID11',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_FORCINT_Bits',0,8,200,4,3
	.word	17924
	.byte	10
	.byte	'_Ifx_GTM_BRC_IRQ_MODE_Bits',0,8,203,4,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_MODE_Bits',0,8,207,4,3
	.word	18288
	.byte	10
	.byte	'_Ifx_GTM_BRC_IRQ_NOTIFY_Bits',0,8,210,4,16,4,11
	.byte	'DEST_ERR',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'DID0',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'DID1',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'DID2',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'DID3',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'DID4',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'DID5',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'DID6',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'DID7',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'DID8',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'DID9',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'DID10',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'DID11',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_NOTIFY_Bits',0,8,226,4,3
	.word	18399
	.byte	10
	.byte	'_Ifx_GTM_BRC_RST_Bits',0,8,229,4,16,4,11
	.byte	'RST',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_RST_Bits',0,8,233,4,3
	.word	18709
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC0_ADDR_Bits',0,8,236,4,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC0_ADDR_Bits',0,8,242,4,3
	.word	18805
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC0_DEST_Bits',0,8,245,4,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC0_DEST_Bits',0,8,143,5,3
	.word	18957
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC10_ADDR_Bits',0,8,146,5,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC10_ADDR_Bits',0,8,152,5,3
	.word	19526
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC10_DEST_Bits',0,8,155,5,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC10_DEST_Bits',0,8,181,5,3
	.word	19680
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC11_ADDR_Bits',0,8,184,5,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC11_ADDR_Bits',0,8,190,5,3
	.word	20251
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC11_DEST_Bits',0,8,193,5,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC11_DEST_Bits',0,8,219,5,3
	.word	20405
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC1_ADDR_Bits',0,8,222,5,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC1_ADDR_Bits',0,8,228,5,3
	.word	20976
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC1_DEST_Bits',0,8,231,5,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC1_DEST_Bits',0,8,129,6,3
	.word	21128
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC2_ADDR_Bits',0,8,132,6,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC2_ADDR_Bits',0,8,138,6,3
	.word	21697
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC2_DEST_Bits',0,8,141,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC2_DEST_Bits',0,8,167,6,3
	.word	21849
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC3_ADDR_Bits',0,8,170,6,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC3_ADDR_Bits',0,8,176,6,3
	.word	22418
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC3_DEST_Bits',0,8,179,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC3_DEST_Bits',0,8,205,6,3
	.word	22570
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC4_ADDR_Bits',0,8,208,6,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC4_ADDR_Bits',0,8,214,6,3
	.word	23139
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC4_DEST_Bits',0,8,217,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC4_DEST_Bits',0,8,243,6,3
	.word	23291
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC5_ADDR_Bits',0,8,246,6,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC5_ADDR_Bits',0,8,252,6,3
	.word	23860
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC5_DEST_Bits',0,8,255,6,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC5_DEST_Bits',0,8,153,7,3
	.word	24012
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC6_ADDR_Bits',0,8,156,7,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC6_ADDR_Bits',0,8,162,7,3
	.word	24581
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC6_DEST_Bits',0,8,165,7,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC6_DEST_Bits',0,8,191,7,3
	.word	24733
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC7_ADDR_Bits',0,8,194,7,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC7_ADDR_Bits',0,8,200,7,3
	.word	25302
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC7_DEST_Bits',0,8,203,7,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC7_DEST_Bits',0,8,229,7,3
	.word	25454
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC8_ADDR_Bits',0,8,232,7,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC8_ADDR_Bits',0,8,238,7,3
	.word	26023
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC8_DEST_Bits',0,8,241,7,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC8_DEST_Bits',0,8,139,8,3
	.word	26175
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC9_ADDR_Bits',0,8,142,8,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'BRC_MODE',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	19,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC9_ADDR_Bits',0,8,148,8,3
	.word	26744
	.byte	10
	.byte	'_Ifx_GTM_BRC_SRC9_DEST_Bits',0,8,151,8,16,4,11
	.byte	'EN_DEST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'EN_DEST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'EN_DEST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EN_DEST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'EN_DEST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'EN_DEST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'EN_DEST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'EN_DEST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'EN_DEST8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'EN_DEST9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'EN_DEST10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'EN_DEST11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'EN_DEST12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'EN_DEST13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'EN_DEST14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EN_DEST15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'EN_DEST16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'EN_DEST17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'EN_DEST18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'EN_DEST19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'EN_DEST20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'EN_DEST21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'EN_TRASHBIN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC9_DEST_Bits',0,8,177,8,3
	.word	26896
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,8,180,8,16,4,11
	.byte	'BRG_MODE',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'MSK_WR_RSP',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	6,24,2,35,0,11
	.byte	'MODE_UP_PGR',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'BUFF_OVL',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'SYNC_INPUT_REG',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'BRG_RST',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8857
	.byte	7,8,2,35,0,11
	.byte	'BUFF_DPT',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,8,193,8,3
	.word	27465
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,8,196,8,16,4,11
	.byte	'NEW_TRAN_PTR',0,4
	.word	8857
	.byte	5,27,2,35,0,11
	.byte	'FIRST_RSP_PTR',0,4
	.word	8857
	.byte	5,22,2,35,0,11
	.byte	'TRAN_IN_PGR',0,4
	.word	8857
	.byte	5,17,2,35,0,11
	.byte	'ABT_TRAN_PGR',0,4
	.word	8857
	.byte	5,12,2,35,0,11
	.byte	'FBC',0,4
	.word	8857
	.byte	6,6,2,35,0,11
	.byte	'RSP_TRAN_RDY',0,4
	.word	8857
	.byte	6,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,8,204,8,3
	.word	27773
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,8,207,8,16,4,11
	.byte	'TRAN_IN_PGR2',0,4
	.word	8857
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,8,211,8,3
	.word	27975
	.byte	10
	.byte	'_Ifx_GTM_CLC_Bits',0,8,214,8,16,4,11
	.byte	'DISR',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'DISS',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'EDIS',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_CLC_Bits',0,8,221,8,3
	.word	28088
	.byte	10
	.byte	'_Ifx_GTM_CMP_EIRQ_EN_Bits',0,8,224,8,16,4,11
	.byte	'ABWC0_EN_EIRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ABWC1_EN_EIRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ABWC2_EN_EIRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ABWC3_EN_EIRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ABWC4_EN_EIRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ABWC5_EN_EIRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'ABWC6_EN_EIRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'ABWC7_EN_EIRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'ABWC8_EN_EIRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'ABWC9_EN_EIRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'ABWC10_EN_EIRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'ABWC11_EN_EIRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TBWC0_EN_EIRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TBWC1_EN_EIRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TBWC2_EN_EIRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TBWC3_EN_EIRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TBWC4_EN_EIRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TBWC5_EN_EIRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TBWC6_EN_EIRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TBWC7_EN_EIRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TBWC8_EN_EIRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TBWC9_EN_EIRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TBWC10_EN_EIRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TBWC11_EN_EIRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_EIRQ_EN_Bits',0,8,251,8,3
	.word	28231
	.byte	10
	.byte	'_Ifx_GTM_CMP_EN_Bits',0,8,254,8,16,4,11
	.byte	'ABWC0_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ABWC1_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ABWC2_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ABWC3_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ABWC4_EN',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ABWC5_EN',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'ABWC6_EN',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'ABWC7_EN',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'ABWC8_EN',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'ABWC9_EN',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'ABWC10_EN',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'ABWC11_EN',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TBWC0_EN',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TBWC1_EN',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TBWC2_EN',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TBWC3_EN',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TBWC4_EN',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TBWC5_EN',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TBWC6_EN',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TBWC7_EN',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TBWC8_EN',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TBWC9_EN',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TBWC10_EN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TBWC11_EN',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_EN_Bits',0,8,153,9,3
	.word	28925
	.byte	10
	.byte	'_Ifx_GTM_CMP_IRQ_EN_Bits',0,8,156,9,16,4,11
	.byte	'ABWC0_EN_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ABWC1_EN_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ABWC2_EN_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ABWC3_EN_IRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ABWC4_EN_IRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ABWC5_EN_IRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'ABWC6_EN_IRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'ABWC7_EN_IRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'ABWC8_EN_IRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'ABWC9_EN_IRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'ABWC10_EN_IRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'ABWC11_EN_IRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TBWC0_EN_IRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TBWC1_EN_IRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TBWC2_EN_IRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TBWC3_EN_IRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TBWC4_EN_IRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TBWC5_EN_IRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TBWC6_EN_IRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TBWC7_EN_IRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TBWC8_EN_IRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TBWC9_EN_IRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TBWC10_EN_IRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TBWC11_EN_IRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_EN_Bits',0,8,183,9,3
	.word	29489
	.byte	10
	.byte	'_Ifx_GTM_CMP_IRQ_FORCINT_Bits',0,8,186,9,16,4,11
	.byte	'TRG_ABWC0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_ABWC1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_ABWC2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG_ABWC3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRG_ABWC4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TRG_ABWC5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TRG_ABWC6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TRG_ABWC7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TRG_ABWC8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TRG_ABWC9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TRG_ABWC10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TRG_ABWC11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TRG_TBWC0',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TRG_TBWC1',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TRG_TBWC2',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TRG_TBWC3',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TRG_TBWC4',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TRG_TBWC5',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TRG_TBWC6',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TRG_TBWC7',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TRG_TBWC8',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TRG_TBWC9',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TRG_TBWC10',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TRG_TBWC11',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_FORCINT_Bits',0,8,213,9,3
	.word	30157
	.byte	10
	.byte	'_Ifx_GTM_CMP_IRQ_MODE_Bits',0,8,216,9,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_MODE_Bits',0,8,220,9,3
	.word	30763
	.byte	10
	.byte	'_Ifx_GTM_CMP_IRQ_NOTIFY_Bits',0,8,223,9,16,4,11
	.byte	'ABWC0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ABWC1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ABWC2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ABWC3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ABWC4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ABWC5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'ABWC6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'ABWC7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'ABWC8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'ABWC9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'ABWC10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'ABWC11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TBWC0',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TBWC1',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TBWC2',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TBWC3',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TBWC4',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TBWC5',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TBWC6',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TBWC7',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TBWC8',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TBWC9',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TBWC10',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TBWC11',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_NOTIFY_Bits',0,8,250,9,3
	.word	30874
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,8,253,9,16,4,11
	.byte	'CLK_CNT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,8,129,10,3
	.word	31382
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,8,132,10,16,4,11
	.byte	'CLK_CNT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'CLK6_SEL',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8857
	.byte	7,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,8,137,10,3
	.word	31499
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,8,140,10,16,4,11
	.byte	'CLK_CNT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'CLK7_SEL',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8857
	.byte	7,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,8,145,10,3
	.word	31634
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,8,148,10,16,4,11
	.byte	'EN_CLK0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'EN_CLK1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'EN_CLK2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'EN_CLK3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'EN_CLK4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'EN_CLK5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'EN_CLK6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'EN_CLK7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'EN_ECLK0',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'EN_ECLK1',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'EN_ECLK2',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'EN_FXCLK',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,8,163,10,3
	.word	31769
	.byte	10
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,8,166,10,16,4,11
	.byte	'ECLK_DEN',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,8,170,10,3
	.word	32089
	.byte	10
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,8,173,10,16,4,11
	.byte	'ECLK_NUM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,8,177,10,3
	.word	32201
	.byte	10
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,8,180,10,16,4,11
	.byte	'FXCLK_SEL',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,8,184,10,3
	.word	32313
	.byte	10
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,8,187,10,16,4,11
	.byte	'GCLK_DEN',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,8,191,10,3
	.word	32429
	.byte	10
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,8,194,10,16,4,11
	.byte	'GCLK_NUM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,8,198,10,3
	.word	32541
	.byte	10
	.byte	'_Ifx_GTM_CTRL_Bits',0,8,201,10,16,4,11
	.byte	'RF_PROT',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TO_MODE',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'TO_VAL',0,4
	.word	8857
	.byte	5,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_GTM_CTRL_Bits',0,8,208,10,3
	.word	32653
	.byte	10
	.byte	'_Ifx_GTM_DATAIN_Bits',0,8,211,10,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_GTM_DATAIN_Bits',0,8,214,10,3
	.word	32806
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ACB_Bits',0,8,217,10,16,4,11
	.byte	'ACB_0',0,4
	.word	8857
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	3,24,2,35,0,11
	.byte	'ACB_1',0,4
	.word	8857
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'ACB_2',0,4
	.word	8857
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'ACB_3',0,4
	.word	8857
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ACB_Bits',0,8,227,10,3
	.word	32879
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ACT_STA_Bits',0,8,230,10,16,4,11
	.byte	'ACT_Ni',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ACT_STA_Bits',0,8,234,10,3
	.word	33099
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_CAL1_Bits',0,8,237,10,16,4,11
	.byte	'ADD_IN_CAL_1',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL1_Bits',0,8,241,10,3
	.word	33209
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_CAL2_Bits',0,8,244,10,16,4,11
	.byte	'ADD_IN_CAL_2',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL2_Bits',0,8,248,10,3
	.word	33333
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_LD1_Bits',0,8,251,10,16,4,11
	.byte	'ADD_IN_LD_1',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD1_Bits',0,8,255,10,3
	.word	33457
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ADD_IN_LD2_Bits',0,8,130,11,16,4,11
	.byte	'ADD_IN_LD_2',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD2_Bits',0,8,134,11,3
	.word	33578
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ADT_S_Bits',0,8,137,11,16,4,11
	.byte	'PD_S',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'NS',0,4
	.word	8857
	.byte	6,10,2,35,0,11
	.byte	'reserved_22',0,4
	.word	8857
	.byte	10,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADT_S_Bits',0,8,142,11,3
	.word	33699
	.byte	10
	.byte	'_Ifx_GTM_DPLL_AOSV_2_Bits',0,8,145,11,16,4,11
	.byte	'AOSV_2A',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'AOSV_2B',0,4
	.word	8857
	.byte	8,16,2,35,0,11
	.byte	'AOSV_2C',0,4
	.word	8857
	.byte	8,8,2,35,0,11
	.byte	'AOSV_2D',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_AOSV_2_Bits',0,8,151,11,3
	.word	33817
	.byte	10
	.byte	'_Ifx_GTM_DPLL_APS_1C3_Bits',0,8,154,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'APS_1C3',0,4
	.word	8857
	.byte	6,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APS_1C3_Bits',0,8,159,11,3
	.word	33960
	.byte	10
	.byte	'_Ifx_GTM_DPLL_APS_Bits',0,8,162,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'WAPS',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'APS',0,4
	.word	8857
	.byte	6,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	5,19,2,35,0,11
	.byte	'WAPS_1C2',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'APS_1C2',0,4
	.word	8857
	.byte	6,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APS_Bits',0,8,171,11,3
	.word	34092
	.byte	10
	.byte	'_Ifx_GTM_DPLL_APS_SYNC_Bits',0,8,174,11,16,4,11
	.byte	'APS_1C2_EXT',0,4
	.word	8857
	.byte	6,26,2,35,0,11
	.byte	'APS_1C2_STATUS',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8857
	.byte	7,18,2,35,0,11
	.byte	'APS_1C2_OLD',0,4
	.word	8857
	.byte	6,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APS_SYNC_Bits',0,8,181,11,3
	.word	34290
	.byte	10
	.byte	'_Ifx_GTM_DPLL_APT_2C_Bits',0,8,184,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'APT_2C',0,4
	.word	8857
	.byte	10,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APT_2C_Bits',0,8,189,11,3
	.word	34478
	.byte	10
	.byte	'_Ifx_GTM_DPLL_APT_Bits',0,8,192,11,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'WAPT',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'APT',0,4
	.word	8857
	.byte	10,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'WAPT_2B',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'APT_2B',0,4
	.word	8857
	.byte	10,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APT_Bits',0,8,201,11,3
	.word	34608
	.byte	10
	.byte	'_Ifx_GTM_DPLL_APT_SYNC_Bits',0,8,204,11,16,4,11
	.byte	'APT_2B_EXT',0,4
	.word	8857
	.byte	6,26,2,35,0,11
	.byte	'APT_2B_STATUS',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8857
	.byte	7,18,2,35,0,11
	.byte	'APT_2B_OLD',0,4
	.word	8857
	.byte	10,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APT_SYNC_Bits',0,8,211,11,3
	.word	34805
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CDT_SX_Bits',0,8,214,11,16,4,11
	.byte	'CDT_SX',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_SX_Bits',0,8,218,11,3
	.word	34990
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CDT_SX_NOM_Bits',0,8,221,11,16,4,11
	.byte	'CDT_SX_NOM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_SX_NOM_Bits',0,8,225,11,3
	.word	35098
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CDT_TX_Bits',0,8,228,11,16,4,11
	.byte	'CDT_TX',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_TX_Bits',0,8,232,11,3
	.word	35218
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CDT_TX_NOM_Bits',0,8,235,11,16,4,11
	.byte	'CDT_TX_NOM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_TX_NOM_Bits',0,8,239,11,3
	.word	35326
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CNT_NUM1_Bits',0,8,242,11,16,4,11
	.byte	'CNT_NUM_1',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CNT_NUM1_Bits',0,8,246,11,3
	.word	35446
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CNT_NUM2_Bits',0,8,249,11,16,4,11
	.byte	'CNT_NUM_2',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CNT_NUM2_Bits',0,8,253,11,3
	.word	35561
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CRTL_1_SHADOW_STATE_Bits',0,8,128,12,16,4,11
	.byte	'DMO',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	2,29,2,35,0,11
	.byte	'COA',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'PIT',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'SGE1',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'DLM1',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'PCM1',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'SGE2',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'DLM2',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'PCM2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'SYN_NS',0,4
	.word	8857
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CRTL_1_SHADOW_STATE_Bits',0,8,141,12,3
	.word	35676
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_0_Bits',0,8,144,12,16,4,11
	.byte	'MLT',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'IFP',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'SNU',0,4
	.word	8857
	.byte	5,16,2,35,0,11
	.byte	'TNU',0,4
	.word	8857
	.byte	9,7,2,35,0,11
	.byte	'AMS',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'AMT',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'IDS',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'IDT',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'SEN',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'TEN',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'RMO',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_0_Bits',0,8,157,12,3
	.word	35950
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_0_SHADOW_STATE_Bits',0,8,160,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'IFP',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	14,7,2,35,0,11
	.byte	'AMS',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'IDS',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	3,1,2,35,0,11
	.byte	'RMO',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_STATE_Bits',0,8,170,12,3
	.word	36182
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_0_SHADOW_TRIGGER_Bits',0,8,173,12,16,4,11
	.byte	'MLT',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'IFP',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	15,6,2,35,0,11
	.byte	'AMT',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'IDS',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'IDT',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	2,1,2,35,0,11
	.byte	'RMO',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_TRIGGER_Bits',0,8,183,12,3
	.word	36426
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_1_Bits',0,8,186,12,16,4,11
	.byte	'DMO',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'DEN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'IDDS',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'COA',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'PIT',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'SGE1',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'DLM1',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'PCM1',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'SGE2',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'DLM2',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'PCM2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'SYN_NS',0,4
	.word	8857
	.byte	5,16,2,35,0,11
	.byte	'SYN_NT',0,4
	.word	8857
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'LCD',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'SWR',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'SYSF',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TS0_HRS',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'TS0_HRT',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'SMC',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'SSL',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'TSL',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_1_Bits',0,8,210,12,3
	.word	36659
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_1_SHADOW_TRIGGER_Bits',0,8,213,12,16,4,11
	.byte	'DMO',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	2,29,2,35,0,11
	.byte	'COA',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'PIT',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'SGE1',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'DLM1',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'PCM1',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_1_SHADOW_TRIGGER_Bits',0,8,223,12,3
	.word	37086
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_2_Bits',0,8,226,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'AEN0',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'AEN1',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'AEN2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'AEN3',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'AEN4',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'AEN5',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'AEN6',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'AEN7',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'WAD0',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'WAD1',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'WAD2',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'WAD3',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'WAD4',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'WAD5',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'WAD6',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'WAD7',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_2_Bits',0,8,246,12,3
	.word	37320
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_3_Bits',0,8,249,12,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'AEN8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'AEN9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'AEN10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'AEN11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'AEN12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'AEN13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'AEN14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'AEN15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'WAD8',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'WAD9',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'WAD10',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'WAD11',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'WAD12',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'WAD13',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'WAD14',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'WAD15',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_3_Bits',0,8,141,13,3
	.word	37688
	.byte	10
	.byte	'_Ifx_GTM_DPLL_CTRL_4_Bits',0,8,144,13,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'AEN16',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'AEN17',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'AEN18',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'AEN19',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'AEN20',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'AEN21',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'AEN22',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'AEN23',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'WAD16',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'WAD17',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'WAD18',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'WAD19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'WAD20',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'WAD21',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'WAD22',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'WAD23',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_4_Bits',0,8,164,13,3
	.word	38068
	.byte	10
	.byte	'_Ifx_GTM_DPLL_DLA_Bits',0,8,167,13,16,4,11
	.byte	'DLA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DLA_Bits',0,8,171,13,3
	.word	38452
	.byte	10
	.byte	'_Ifx_GTM_DPLL_DT_S_ACT_Bits',0,8,174,13,16,4,11
	.byte	'DT_S_ACT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DT_S_ACT_Bits',0,8,178,13,3
	.word	38551
	.byte	10
	.byte	'_Ifx_GTM_DPLL_DT_S_Bits',0,8,181,13,16,4,11
	.byte	'DT_S',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DT_S_Bits',0,8,185,13,3
	.word	38665
	.byte	10
	.byte	'_Ifx_GTM_DPLL_DT_T_ACT_Bits',0,8,188,13,16,4,11
	.byte	'DT_T_ACT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DT_T_ACT_Bits',0,8,192,13,3
	.word	38767
	.byte	10
	.byte	'_Ifx_GTM_DPLL_DTA_Bits',0,8,195,13,16,4,11
	.byte	'DTA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DTA_Bits',0,8,199,13,3
	.word	38881
	.byte	10
	.byte	'_Ifx_GTM_DPLL_EDT_S_Bits',0,8,203,13,16,4,11
	.byte	'EDT_S',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_EDT_S_Bits',0,8,207,13,3
	.word	38980
	.byte	10
	.byte	'_Ifx_GTM_DPLL_EDT_T_Bits',0,8,211,13,16,4,11
	.byte	'EDT_T',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_EDT_T_Bits',0,8,215,13,3
	.word	39085
	.byte	10
	.byte	'_Ifx_GTM_DPLL_EIRQ_EN_Bits',0,8,218,13,16,4,11
	.byte	'PDI_EIRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'PEI_EIRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TINI_EIRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TAXI_EIRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'SISI_EIRQ_EN',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TISI_EIRQ_EN',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MSI_EIRQ_EN',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MTI_EIRQ_EN',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'SASI_EIRQ_EN',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TASI_EIRQ_EN',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'PWI_EIRQ_EN',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'W2I_EIRQ_EN',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'W1I_EIRQ_EN',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'GL1I_EIRQ_EN',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'LL1I_EIRQ_EN',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EI_EIRQ_EN',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'GL2I_EIRQ_EN',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'LL2I_EIRQ_EN',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TE0I_EIRQ_EN',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TE1I_EIRQ_EN',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TE2I_EIRQ_EN',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TE3I_EIRQ_EN',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TE4I_EIRQ_EN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'CDTI_EIRQ_EN',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'CDSI_EIRQ_EN',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TORI',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'SORI',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'DCGI',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_EIRQ_EN_Bits',0,8,249,13,3
	.word	39190
	.byte	10
	.byte	'_Ifx_GTM_DPLL_FTV_S_Bits',0,8,252,13,16,4,11
	.byte	'STATE_FT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_FTV_S_Bits',0,8,128,14,3
	.word	39921
	.byte	10
	.byte	'_Ifx_GTM_DPLL_FTV_T_Bits',0,8,131,14,16,4,11
	.byte	'TRIGGER_FT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_FTV_T_Bits',0,8,135,14,3
	.word	40029
	.byte	10
	.byte	'_Ifx_GTM_DPLL_ID_PMTR_Bits',0,8,138,14,16,4,11
	.byte	'ID_PMTR_x',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ID_PMTR_Bits',0,8,142,14,3
	.word	40139
	.byte	10
	.byte	'_Ifx_GTM_DPLL_INC_CNT1_Bits',0,8,145,14,16,4,11
	.byte	'INC_CNT1',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_INC_CNT1_Bits',0,8,149,14,3
	.word	40251
	.byte	10
	.byte	'_Ifx_GTM_DPLL_INC_CNT2_Bits',0,8,153,14,16,4,11
	.byte	'INC_CNT2',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_INC_CNT2_Bits',0,8,157,14,3
	.word	40365
	.byte	10
	.byte	'_Ifx_GTM_DPLL_IRQ_EN_Bits',0,8,160,14,16,4,11
	.byte	'PDI_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'PEI_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TINI_IRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TAXI_IRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'SISI_IRQ_EN',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TISI_IRQ_EN',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MSI_IRQ_EN',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MTI_IRQ_EN',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'SASI_IRQ_EN',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TASI_IRQ_EN',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'PWI_IRQ_EN',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'W2I_IRQ_EN',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'W1I_IRQ_EN',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'GL1I_IRQ_EN',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'LL1I_IRQ_EN',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EI_IRQ_EN',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'GL2I_IRQ_EN',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'LL2I_IRQ_EN',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TE0I_IRQ_EN',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TE1I_IRQ_EN',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TE2I_IRQ_EN',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TE3I_IRQ_EN',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TE4I_IRQ_EN',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'CDTI_IRQ_EN',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'CDSI_IRQ_EN',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TORI',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'SORI',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'DCGI',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_EN_Bits',0,8,191,14,3
	.word	40479
	.byte	10
	.byte	'_Ifx_GTM_DPLL_IRQ_FORCINT_Bits',0,8,194,14,16,4,11
	.byte	'TRG_PDI',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_PEI',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_TINI',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG_TAXI',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRG_SISI',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TRG_TISI',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TRG_MSI',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TRG_MTI',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TRG_SASI',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TRG_TASI',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TRG_PWI',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TRG_W2I',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TRG_W1I',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TRG_GL1I',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TRG_LL1I',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TRG_EI',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TRG_GL2I',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TRG_LL2I',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TRG_TE0I',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TRG_TE1I',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TRG_TE2I',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TRG_TE3I',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TRG_TE4I',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TRG_CDTI',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'TRG_CDSI',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TRG_TORI',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'TRG_SORI',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'TRG_DCGI',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_FORCINT_Bits',0,8,225,14,3
	.word	41183
	.byte	10
	.byte	'_Ifx_GTM_DPLL_IRQ_MODE_Bits',0,8,228,14,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_MODE_Bits',0,8,232,14,3
	.word	41834
	.byte	10
	.byte	'_Ifx_GTM_DPLL_IRQ_NOTIFY_Bits',0,8,235,14,16,4,11
	.byte	'PDI',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'PEI',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TINI',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TAXI',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'SISI',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TISI',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MSI',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MTI',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'SASI',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TASI',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'PWI',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'W2I',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'W1I',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'GL1I',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'LL1I',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'EI',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'GL2I',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'LL2I',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TE0I',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TE1I',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TE2I',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TE3I',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TE4I',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'CDTI',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'CDSI',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TORI',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'SORI',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'DCGI',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_NOTIFY_Bits',0,8,138,15,3
	.word	41947
	.byte	10
	.byte	'_Ifx_GTM_DPLL_MEDT_S_Bits',0,8,142,15,16,4,11
	.byte	'MEDT_S',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MEDT_S_Bits',0,8,146,15,3
	.word	42484
	.byte	10
	.byte	'_Ifx_GTM_DPLL_MEDT_T_Bits',0,8,150,15,16,4,11
	.byte	'MEDT_T',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MEDT_T_Bits',0,8,154,15,3
	.word	42592
	.byte	10
	.byte	'_Ifx_GTM_DPLL_MLS1_Bits',0,8,157,15,16,4,11
	.byte	'MLS1',0,4
	.word	8857
	.byte	18,14,2,35,0,11
	.byte	'reserved_18',0,4
	.word	8857
	.byte	14,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MLS1_Bits',0,8,161,15,3
	.word	42700
	.byte	10
	.byte	'_Ifx_GTM_DPLL_MLS2_Bits',0,8,164,15,16,4,11
	.byte	'MLS2',0,4
	.word	8857
	.byte	18,14,2,35,0,11
	.byte	'reserved_18',0,4
	.word	8857
	.byte	14,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MLS2_Bits',0,8,168,15,3
	.word	42802
	.byte	10
	.byte	'_Ifx_GTM_DPLL_MPVAL1_Bits',0,8,172,15,16,4,11
	.byte	'MPVAL1',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'SIX1',0,4
	.word	8857
	.byte	8,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MPVAL1_Bits',0,8,177,15,3
	.word	42904
	.byte	10
	.byte	'_Ifx_GTM_DPLL_MPVAL2_Bits',0,8,181,15,16,4,11
	.byte	'MPVAL2',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'SIX2',0,4
	.word	8857
	.byte	8,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MPVAL2_Bits',0,8,186,15,3
	.word	43028
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NA_Bits',0,8,189,15,16,4,11
	.byte	'DB',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'DW',0,4
	.word	8857
	.byte	10,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NA_Bits',0,8,194,15,3
	.word	43152
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NMB_S_Bits',0,8,197,15,16,4,11
	.byte	'NMB_S',0,4
	.word	8857
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_S_Bits',0,8,201,15,3
	.word	43262
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NMB_S_TAR_Bits',0,8,204,15,16,4,11
	.byte	'NMB_S_TAR',0,4
	.word	8857
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR_Bits',0,8,208,15,3
	.word	43367
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NMB_S_TAR_OLD_Bits',0,8,211,15,16,4,11
	.byte	'NMB_S_TAR_OLD',0,4
	.word	8857
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR_OLD_Bits',0,8,215,15,3
	.word	43484
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NMB_T_Bits',0,8,218,15,16,4,11
	.byte	'NMB_T',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_T_Bits',0,8,222,15,3
	.word	43613
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NMB_T_TAR_Bits',0,8,225,15,16,4,11
	.byte	'NMB_T_TAR',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR_Bits',0,8,229,15,3
	.word	43718
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NMB_T_TAR_OLD_Bits',0,8,232,15,16,4,11
	.byte	'NMB_T_TAR_OLD',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR_OLD_Bits',0,8,236,15,3
	.word	43835
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NTI_CNT_Bits',0,8,239,15,16,4,11
	.byte	'NTI_CNT',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NTI_CNT_Bits',0,8,243,15,3
	.word	43964
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NUSC_Bits',0,8,246,15,16,4,11
	.byte	'NUSE',0,4
	.word	8857
	.byte	6,26,2,35,0,11
	.byte	'FSS',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'SYN_S',0,4
	.word	8857
	.byte	6,19,2,35,0,11
	.byte	'SYN_S_OLD',0,4
	.word	8857
	.byte	6,13,2,35,0,11
	.byte	'VSN',0,4
	.word	8857
	.byte	6,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8857
	.byte	4,3,2,35,0,11
	.byte	'WNUS',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'WSYN',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'WVSN',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NUSC_Bits',0,8,129,16,3
	.word	44075
	.byte	10
	.byte	'_Ifx_GTM_DPLL_NUTC_Bits',0,8,132,16,16,4,11
	.byte	'NUTE',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'FST',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	2,19,2,35,0,11
	.byte	'SYN_T',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'SYN_T_OLD',0,4
	.word	8857
	.byte	3,13,2,35,0,11
	.byte	'VTN',0,4
	.word	8857
	.byte	6,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8857
	.byte	4,3,2,35,0,11
	.byte	'WNUT',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'WSYN',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'WVTN',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NUTC_Bits',0,8,144,16,3
	.word	44293
	.byte	10
	.byte	'_Ifx_GTM_DPLL_OSW_Bits',0,8,147,16,16,4,11
	.byte	'SWON_S',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'SWON_T',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	6,24,2,35,0,11
	.byte	'OSS',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_OSW_Bits',0,8,154,16,3
	.word	44534
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PDT_T_Bits',0,8,157,16,16,4,11
	.byte	'DB',0,4
	.word	8857
	.byte	14,18,2,35,0,11
	.byte	'DW',0,4
	.word	8857
	.byte	10,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PDT_T_Bits',0,8,162,16,3
	.word	44691
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSA_Bits',0,8,165,16,16,4,11
	.byte	'PSA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSA_Bits',0,8,169,16,3
	.word	44807
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSAC_Bits',0,8,172,16,16,4,11
	.byte	'PSAC',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSAC_Bits',0,8,176,16,3
	.word	44906
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSSC_Bits',0,8,179,16,16,4,11
	.byte	'PSSC',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSSC_Bits',0,8,183,16,3
	.word	45008
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSSM_0_Bits',0,8,186,16,16,4,11
	.byte	'PSSM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSSM_0_Bits',0,8,190,16,3
	.word	45110
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSSM_1_Bits',0,8,193,16,16,4,11
	.byte	'PSSM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSSM_1_Bits',0,8,197,16,3
	.word	45216
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSTC_Bits',0,8,200,16,16,4,11
	.byte	'PSTC',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSTC_Bits',0,8,204,16,3
	.word	45322
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSTM_0_Bits',0,8,207,16,16,4,11
	.byte	'PSTM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSTM_0_Bits',0,8,211,16,3
	.word	45424
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PSTM_1_Bits',0,8,214,16,16,4,11
	.byte	'PSTM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSTM_1_Bits',0,8,218,16,3
	.word	45530
	.byte	10
	.byte	'_Ifx_GTM_DPLL_PVT_Bits',0,8,221,16,16,4,11
	.byte	'PVT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PVT_Bits',0,8,225,16,3
	.word	45636
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RAM_INI_Bits',0,8,228,16,16,4,11
	.byte	'INIT_1A',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'INIT_1B',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'INIT_2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'INIT_RAM',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RAM_INI_Bits',0,8,236,16,3
	.word	45735
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RCDT_SX_Bits',0,8,239,16,16,4,11
	.byte	'RCDT_SX',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_SX_Bits',0,8,243,16,3
	.word	45924
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RCDT_SX_NOM_Bits',0,8,247,16,16,4,11
	.byte	'RCDT_SX_NOM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_SX_NOM_Bits',0,8,251,16,3
	.word	46035
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RCDT_TX_Bits',0,8,254,16,16,4,11
	.byte	'RCDT_TX',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_TX_Bits',0,8,130,17,3
	.word	46158
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RCDT_TX_NOM_Bits',0,8,134,17,16,4,11
	.byte	'RCDT_TX_NOM',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_TX_NOM_Bits',0,8,138,17,3
	.word	46269
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RDT_S_ACT_Bits',0,8,141,17,16,4,11
	.byte	'RDT_S_ACT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RDT_S_ACT_Bits',0,8,145,17,3
	.word	46392
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RDT_S_Bits',0,8,148,17,16,4,11
	.byte	'RDT_S',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RDT_S_Bits',0,8,152,17,3
	.word	46509
	.byte	10
	.byte	'_Ifx_GTM_DPLL_RDT_T_ACT_Bits',0,8,155,17,16,4,11
	.byte	'RDT_T_ACT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RDT_T_ACT_Bits',0,8,159,17,3
	.word	46614
	.byte	10
	.byte	'_Ifx_GTM_DPLL_SLR_Bits',0,8,162,17,16,4,11
	.byte	'SLR',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_SLR_Bits',0,8,166,17,3
	.word	46731
	.byte	10
	.byte	'_Ifx_GTM_DPLL_STATUS_Bits',0,8,169,17,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CSO',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'CTO',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'CRO',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'RCS',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'RCT',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'PSE',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'SOR',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'MS',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TOR',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'MT',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'RAM2_ERR',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	2,17,2,35,0,11
	.byte	'LOW_RES',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'CSVS',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'CSVT',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'CAIP2',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'CAIP1',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'ISN',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'ITN',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'BWD2',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'BWD1',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'LOCK2',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'SYS',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'SYT',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'FSD',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'FTD',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'LOCK1',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'ERR',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_STATUS_Bits',0,8,202,17,3
	.word	46829
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TBU_TS0_S_Bits',0,8,205,17,16,4,11
	.byte	'TBU_TS0_S',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TBU_TS0_S_Bits',0,8,209,17,3
	.word	47410
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TBU_TS0_T_Bits',0,8,212,17,16,4,11
	.byte	'TBU_TS0_T',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TBU_TS0_T_Bits',0,8,216,17,3
	.word	47527
	.byte	10
	.byte	'_Ifx_GTM_DPLL_THMA_Bits',0,8,219,17,16,4,11
	.byte	'THMA',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_THMA_Bits',0,8,223,17,3
	.word	47644
	.byte	10
	.byte	'_Ifx_GTM_DPLL_THMI_Bits',0,8,226,17,16,4,11
	.byte	'THMI',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_THMI_Bits',0,8,230,17,3
	.word	47746
	.byte	10
	.byte	'_Ifx_GTM_DPLL_THVAL_Bits',0,8,233,17,16,4,11
	.byte	'THVAL',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_THVAL_Bits',0,8,237,17,3
	.word	47848
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TLR_Bits',0,8,240,17,16,4,11
	.byte	'TLR',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TLR_Bits',0,8,244,17,3
	.word	47953
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TOV_Bits',0,8,247,17,16,4,11
	.byte	'DB',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'DW',0,4
	.word	8857
	.byte	6,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TOV_Bits',0,8,252,17,3
	.word	48051
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TOV_S_Bits',0,8,255,17,16,4,11
	.byte	'DB',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'DW',0,4
	.word	8857
	.byte	6,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TOV_S_Bits',0,8,132,18,3
	.word	48163
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TS_S_0_Bits',0,8,135,18,16,4,11
	.byte	'STATE_TS',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_S_0_Bits',0,8,139,18,3
	.word	48279
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TS_S_1_Bits',0,8,142,18,16,4,11
	.byte	'STATE_TS',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_S_1_Bits',0,8,146,18,3
	.word	48389
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TS_T_0_Bits',0,8,149,18,16,4,11
	.byte	'TRIGGER_TS',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_T_0_Bits',0,8,153,18,3
	.word	48499
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TS_T_1_Bits',0,8,156,18,16,4,11
	.byte	'TRIGGER_TS',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_T_1_Bits',0,8,160,18,3
	.word	48611
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TSAC_Bits',0,8,163,18,16,4,11
	.byte	'TSAC',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TSAC_Bits',0,8,167,18,3
	.word	48723
	.byte	10
	.byte	'_Ifx_GTM_DPLL_TSF_S_Bits',0,8,170,18,16,4,11
	.byte	'TSF_S',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TSF_S_Bits',0,8,174,18,3
	.word	48825
	.byte	10
	.byte	'_Ifx_GTM_DXINCON_Bits',0,8,177,18,16,4,11
	.byte	'IN00',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'IN01',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'IN02',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	5,24,2,35,0,11
	.byte	'IN10',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'IN11',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'IN12',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	5,16,2,35,0,11
	.byte	'DSS00',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'DSS01',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'DSS02',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8857
	.byte	5,8,2,35,0,11
	.byte	'DSS10',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'DSS11',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'DSS12',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_DXINCON_Bits',0,8,195,18,3
	.word	48930
	.byte	10
	.byte	'_Ifx_GTM_DXOUTCON_Bits',0,8,198,18,16,4,11
	.byte	'OUT00',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'OUT01',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'OUT02',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	5,24,2,35,0,11
	.byte	'OUT10',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'OUT11',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'OUT12',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_GTM_DXOUTCON_Bits',0,8,208,18,3
	.word	49278
	.byte	10
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,8,211,18,16,4,11
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,8,218,18,3
	.word	49486
	.byte	10
	.byte	'_Ifx_GTM_F2A_ENABLE_Bits',0,8,221,18,16,4,11
	.byte	'STR0_EN',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'STR1_EN',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'STR2_EN',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'STR3_EN',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'STR4_EN',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'STR5_EN',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'STR6_EN',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'STR7_EN',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_F2A_ENABLE_Bits',0,8,232,18,3
	.word	49690
	.byte	10
	.byte	'_Ifx_GTM_F2A_RD_CH_ARU_RD_FIFO_Bits',0,8,235,18,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	9,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	8857
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_GTM_F2A_RD_CH_ARU_RD_FIFO_Bits',0,8,239,18,3
	.word	49930
	.byte	10
	.byte	'_Ifx_GTM_F2A_STR_CH_STR_CFG_Bits',0,8,242,18,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'TMODE',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'DIR',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8857
	.byte	13,0,2,35,0,0,21
	.byte	'Ifx_GTM_F2A_STR_CH_STR_CFG_Bits',0,8,248,18,3
	.word	50055
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_CTRL_Bits',0,8,251,18,16,4,11
	.byte	'RBM',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'RAP',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'FLUSH',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'WULOCK',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_CTRL_Bits',0,8,130,19,3
	.word	50213
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_EIRQ_EN_Bits',0,8,133,19,16,4,11
	.byte	'FIFO_EMPTY_EIRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'FIFO_FULL_EIRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'FIFO_LWM_EIRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'FIFO_UWM_EIRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'Reserved',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_EIRQ_EN_Bits',0,8,140,19,3
	.word	50369
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_END_ADDR_Bits',0,8,143,19,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_END_ADDR_Bits',0,8,147,19,3
	.word	50579
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_FILL_LEVEL_Bits',0,8,150,19,16,4,11
	.byte	'LEVEL',0,4
	.word	8857
	.byte	11,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_FILL_LEVEL_Bits',0,8,154,19,3
	.word	50695
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_EN_Bits',0,8,157,19,16,4,11
	.byte	'FIFO_EMPTY_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'FIFO_FULL_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'FIFO_LWM_IRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'FIFO_UWM_IRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_EN_Bits',0,8,164,19,3
	.word	50816
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_FORCINT_Bits',0,8,167,19,16,4,11
	.byte	'TRG_FIFO_EMPTY',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_FIFO_FULL',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_FIFO_LWM',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG_FIFO_UWM',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_FORCINT_Bits',0,8,174,19,3
	.word	51022
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_MODE_Bits',0,8,177,19,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'DMA_HYSTERESIS',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'DMA_HYST_DIR',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_MODE_Bits',0,8,183,19,3
	.word	51226
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_IRQ_NOTIFY_Bits',0,8,186,19,16,4,11
	.byte	'FIFO_EMPTY',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'FIFO_FULL',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'FIFO_LWM',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'FIFO_UWM',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_NOTIFY_Bits',0,8,193,19,3
	.word	51395
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_LOWER_WM_Bits',0,8,196,19,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_LOWER_WM_Bits',0,8,200,19,3
	.word	51581
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_RD_PTR_Bits',0,8,203,19,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_RD_PTR_Bits',0,8,207,19,3
	.word	51697
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_START_ADDR_Bits',0,8,210,19,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_START_ADDR_Bits',0,8,214,19,3
	.word	51809
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_STATUS_Bits',0,8,217,19,16,4,11
	.byte	'EMPTY',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'FULL',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'LOW_WM',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'UP_WM',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_STATUS_Bits',0,8,224,19,3
	.word	51929
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_UPPER_WM_Bits',0,8,227,19,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_UPPER_WM_Bits',0,8,231,19,3
	.word	52092
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH_WR_PTR_Bits',0,8,234,19,16,4,11
	.byte	'ADDR',0,4
	.word	8857
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_WR_PTR_Bits',0,8,238,19,3
	.word	52208
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,8,241,19,16,4,11
	.byte	'ARU_NEW_DATA0_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ARU_NEW_DATA1_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ARU_ACC_ACK_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'BRC_IRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'AEI_IRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'CMP_IRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'SPE0_IRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'SPE1_IRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	8,16,2,35,0,11
	.byte	'PSM0_CH0_IRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'PSM0_CH1_IRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'PSM0_CH2_IRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'PSM0_CH3_IRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'PSM0_CH4_IRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'PSM0_CH5_IRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'PSM0_CH6_IRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'PSM0_CH7_IRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,8,133,20,3
	.word	52320
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_1_Bits',0,8,136,20,16,4,11
	.byte	'DPLL_DCG_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'DPLL_EDI_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'DPLL_TIN_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'DPLL_TAX_IRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'DPLL_SIS_IRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'DPLL_TIS_IRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'DPLL_MSI_IRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'DPLL_MTI_IRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'DPLL_SAS_IRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'DPLL_TAS_IRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'DPLL_PWI_IRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'DPLL_W2I_IRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'DPLL_W1I_IRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'DPLL_GLI_IRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'DPLL_LLI_IRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'DPLL_EI_IRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'DPLL_GL2I_IRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'DPLL_LL2I_IRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'DPLL_TE0_IRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'DPLL_TE1_IRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'DPLL_TE2_IRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'DPLL_TE3_IRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'DPLL_TE4_IRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'DPLL_CDIT_IRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'DPLL_CDIS_IRQ',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'DPLL_TORI_IRQ',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'DPLL_SORI_IRQ',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_1_Bits',0,8,166,20,3
	.word	52804
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,8,169,20,16,4,11
	.byte	'TIM0_CH0_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TIM0_CH1_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TIM0_CH2_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TIM0_CH3_IRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TIM0_CH4_IRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TIM0_CH5_IRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TIM0_CH6_IRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TIM0_CH7_IRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TIM1_CH0_IRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TIM1_CH1_IRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TIM1_CH2_IRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TIM1_CH3_IRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TIM1_CH4_IRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TIM1_CH5_IRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TIM1_CH6_IRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TIM1_CH7_IRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TIM2_CH0_IRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TIM2_CH1_IRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TIM2_CH2_IRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TIM2_CH3_IRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TIM2_CH4_IRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TIM2_CH5_IRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TIM2_CH6_IRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TIM2_CH7_IRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,8,196,20,3
	.word	53545
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_4_Bits',0,8,199,20,16,4,11
	.byte	'MCS0_CH0_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'MCS0_CH1_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MCS0_CH2_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'MCS0_CH3_IRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'MCS0_CH4_IRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'MCS0_CH5_IRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MCS0_CH6_IRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MCS0_CH7_IRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'MCS1_CH0_IRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'MCS1_CH1_IRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'MCS1_CH2_IRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'MCS1_CH3_IRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'MCS1_CH4_IRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'MCS1_CH5_IRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'MCS1_CH6_IRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'MCS1_CH7_IRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'MCS2_CH0_IRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'MCS2_CH1_IRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'MCS2_CH2_IRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'MCS2_CH3_IRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'MCS2_CH4_IRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'MCS2_CH5_IRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'MCS2_CH6_IRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'MCS2_CH7_IRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_4_Bits',0,8,226,20,3
	.word	54209
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,8,229,20,16,4,11
	.byte	'TOM0_CH0_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TOM0_CH1_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TOM0_CH2_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TOM0_CH3_IRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TOM0_CH4_IRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TOM0_CH5_IRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TOM0_CH6_IRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TOM0_CH7_IRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TOM0_CH8_IRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TOM0_CH9_IRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TOM0_CH10_IRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TOM0_CH11_IRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TOM0_CH12_IRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TOM0_CH13_IRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TOM0_CH14_IRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TOM0_CH15_IRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TOM1_CH0_IRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TOM1_CH1_IRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TOM1_CH2_IRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TOM1_CH3_IRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TOM1_CH4_IRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TOM1_CH5_IRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TOM1_CH6_IRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TOM1_CH7_IRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'TOM1_CH8_IRQ',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TOM1_CH9_IRQ',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'TOM1_CH10_IRQ',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'TOM1_CH11_IRQ',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'TOM1_CH12_IRQ',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'TOM1_CH13_IRQ',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'TOM1_CH14_IRQ',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'TOM1_CH15_IRQ',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,8,135,21,3
	.word	54873
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_9_Bits',0,8,138,21,16,4,11
	.byte	'ATOM0_CH0_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ATOM0_CH1_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ATOM0_CH2_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ATOM0_CH3_IRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ATOM0_CH4_IRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ATOM0_CH5_IRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'ATOM0_CH6_IRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'ATOM0_CH7_IRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'ATOM1_CH0_IRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'ATOM1_CH1_IRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'ATOM1_CH2_IRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'ATOM1_CH3_IRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'ATOM1_CH4_IRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'ATOM1_CH5_IRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'ATOM1_CH6_IRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'ATOM1_CH7_IRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'ATOM2_CH0_IRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'ATOM2_CH1_IRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'ATOM2_CH2_IRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'ATOM2_CH3_IRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'ATOM2_CH4_IRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'ATOM2_CH5_IRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'ATOM2_CH6_IRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'ATOM2_CH7_IRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'ATOM3_CH0_IRQ',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'ATOM3_CH1_IRQ',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'ATOM3_CH2_IRQ',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'ATOM3_CH3_IRQ',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'ATOM3_CH4_IRQ',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'ATOM3_CH5_IRQ',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'ATOM3_CH6_IRQ',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'ATOM3_CH7_IRQ',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_9_Bits',0,8,172,21,3
	.word	55718
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_CEI0_Bits',0,8,175,21,16,4,11
	.byte	'FIFO0_CH0_EIRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'FIFO0_CH1_EIRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'FIFO0_CH2_EIRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'FIFO0_CH3_EIRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'FIFO0_CH4_EIRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'FIFO0_CH5_EIRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'FIFO0_CH6_EIRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'FIFO0_CH7_EIRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_CEI0_Bits',0,8,186,21,3
	.word	56583
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,8,189,21,16,4,11
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TIM1_CH0_EIRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TIM1_CH1_EIRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TIM1_CH2_EIRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TIM1_CH3_EIRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TIM1_CH4_EIRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TIM1_CH5_EIRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TIM1_CH6_EIRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TIM1_CH7_EIRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'TIM2_CH0_EIRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TIM2_CH1_EIRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'TIM2_CH2_EIRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'TIM2_CH3_EIRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'TIM2_CH4_EIRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TIM2_CH5_EIRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TIM2_CH6_EIRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'TIM2_CH7_EIRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,8,216,21,3
	.word	56884
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_CEI3_Bits',0,8,219,21,16,4,11
	.byte	'MCS0_CH0_EIRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'MCS0_CH1_EIRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MCS0_CH2_EIRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'MCS0_CH3_EIRQ',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'MCS0_CH4_EIRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'MCS0_CH5_EIRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MCS0_CH6_EIRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MCS0_CH7_EIRQ',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'MCS1_CH0_EIRQ',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'MCS1_CH1_EIRQ',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'MCS1_CH2_EIRQ',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'MCS1_CH3_EIRQ',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'MCS1_CH4_EIRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'MCS1_CH5_EIRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'MCS1_CH6_EIRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'MCS1_CH7_EIRQ',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'MCS2_CH0_EIRQ',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'MCS2_CH1_EIRQ',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'MCS2_CH2_EIRQ',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'MCS2_CH3_EIRQ',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'MCS2_CH4_EIRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'MCS2_CH5_EIRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'MCS2_CH6_EIRQ',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'MCS2_CH7_EIRQ',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_CEI3_Bits',0,8,246,21,3
	.word	57578
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,8,249,21,16,4,11
	.byte	'GTM_EIRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'BRC_EIRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'FIFO0_EIRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TIM0_EIRQ',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TIM1_EIRQ',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TIM2_EIRQ',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8857
	.byte	5,20,2,35,0,11
	.byte	'MCS0_EIRQ',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'MCS1_EIRQ',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'MCS2_EIRQ',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8857
	.byte	5,12,2,35,0,11
	.byte	'SPE0_EIRQ',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'SPE1_EIRQ',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'reserved_22',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'CMP_EIRQ',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'DPLL_EIRQ',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8857
	.byte	6,0,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,8,141,22,3
	.word	58272
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,8,144,22,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	4,8,2,35,0,11
	.byte	'SEL6',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'SEL7',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,8,154,22,3
	.word	58725
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_DSADC_INSEL_Bits',0,8,157,22,16,4,11
	.byte	'INSEL0',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'INSEL1',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'INSEL2',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'INSEL3',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'INSEL4',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'INSEL5',0,4
	.word	8857
	.byte	4,8,2,35,0,11
	.byte	'INSEL6',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'INSEL7',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_DSADC_INSEL_Bits',0,8,167,22,3
	.word	58936
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_DSADC_OUTSEL_Bits',0,8,170,22,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	5,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8857
	.byte	17,0,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_DSADC_OUTSEL_Bits',0,8,178,22,3
	.word	59165
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5_OUTSEL0_Bits',0,8,181,22,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	4,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_PSI5_OUTSEL0_Bits',0,8,190,22,3
	.word	59368
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5S_OUTSEL_Bits',0,8,193,22,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	4,8,2,35,0,11
	.byte	'SEL6',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_PSI5S_OUTSEL_Bits',0,8,203,22,3
	.word	59574
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,8,206,22,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,8,224,22,3
	.word	59803
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,8,227,22,16,4,11
	.byte	'CH0SEL',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'CH1SEL',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'CH2SEL',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'CH3SEL',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'CH4SEL',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'CH5SEL',0,4
	.word	8857
	.byte	4,8,2,35,0,11
	.byte	'CH6SEL',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'CH7SEL',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,8,237,22,3
	.word	60144
	.byte	10
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,8,240,22,16,4,11
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,8,247,22,3
	.word	60369
	.byte	10
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,8,250,22,16,4,11
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG_AEI_USP_BE',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,8,129,23,3
	.word	60567
	.byte	10
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,8,132,23,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,8,136,23,3
	.word	60763
	.byte	10
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,8,139,23,16,4,11
	.byte	'AEI_TO_XPT',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,8,146,23,3
	.word	60866
	.byte	10
	.byte	'_Ifx_GTM_KRST0_Bits',0,8,149,23,16,4,11
	.byte	'RST',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'RSTSTAT',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_KRST0_Bits',0,8,154,23,3
	.word	61044
	.byte	10
	.byte	'_Ifx_GTM_KRST1_Bits',0,8,157,23,16,4,11
	.byte	'RST',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_GTM_KRST1_Bits',0,8,161,23,3
	.word	61155
	.byte	10
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,8,164,23,16,4,11
	.byte	'CLR',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,8,168,23,3
	.word	61247
	.byte	10
	.byte	'_Ifx_GTM_MAP_CTRL_Bits',0,8,171,23,16,4,11
	.byte	'TSEL',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'SSL',0,4
	.word	8857
	.byte	3,28,2,35,0,11
	.byte	'LSEL',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	11,16,2,35,0,11
	.byte	'TSPP0_EN',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'TSPP0_DLD',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'reserved_18',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'TSPP0_I0V',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'TSPP0_I1V',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'TSPP0_I2V',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'TSPP1_EN',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TSPP1_DLD',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'TSPP1_I0V',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'TSPP1_I1V',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'TSPP1_I2V',0,4
	.word	8857
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_MAP_CTRL_Bits',0,8,191,23,3
	.word	61343
	.byte	10
	.byte	'_Ifx_GTM_MCFG_CTRL_Bits',0,8,194,23,16,4,11
	.byte	'MEM0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'MEM1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'MEM2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8857
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCFG_CTRL_Bits',0,8,200,23,3
	.word	61773
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH0_CTRG_Bits',0,8,203,23,16,4,11
	.byte	'TRG0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRG4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TRG5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TRG6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TRG7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TRG8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TRG9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TRG10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TRG11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TRG12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TRG13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TRG14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TRG15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH0_CTRG_Bits',0,8,222,23,3
	.word	61906
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH0_STRG_Bits',0,8,225,23,16,4,11
	.byte	'TRG0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRG4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TRG5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'TRG6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TRG7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'TRG8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'TRG9',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'TRG10',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'TRG11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'TRG12',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'TRG13',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'TRG14',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'TRG15',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH0_STRG_Bits',0,8,244,23,3
	.word	62260
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_ACB_Bits',0,8,247,23,16,4,11
	.byte	'ACB0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ACB1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ACB2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ACB3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ACB4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_ACB_Bits',0,8,255,23,3
	.word	62614
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_CTRL_Bits',0,8,130,24,16,4,11
	.byte	'EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ERR',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'CY',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'Z',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'V',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'N',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'CAT',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'CWT',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	6,16,2,35,0,11
	.byte	'SP_CNT',0,4
	.word	8857
	.byte	3,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8857
	.byte	13,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_CTRL_Bits',0,8,145,24,3
	.word	62781
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_EIRQ_EN_Bits',0,8,148,24,16,4,11
	.byte	'MCS_EIRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'STK_ERR_EIRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MEM_ERR_EIRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_EIRQ_EN_Bits',0,8,154,24,3
	.word	63061
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_EN_Bits',0,8,157,24,16,4,11
	.byte	'MCS_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'STK_ERR_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MEM_ERR_IRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_EN_Bits',0,8,163,24,3
	.word	63233
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_FORCINT_Bits',0,8,166,24,16,4,11
	.byte	'TRG_MCS_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_STK_ERR_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_MEM_ERR_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_FORCINT_Bits',0,8,172,24,3
	.word	63400
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_MODE_Bits',0,8,175,24,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_MODE_Bits',0,8,179,24,3
	.word	63580
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_IRQ_NOTIFY_Bits',0,8,182,24,16,4,11
	.byte	'MCS_IRQ',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'STK_ERR_IRQ',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MEM_ERR_IRQ',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_NOTIFY_Bits',0,8,188,24,3
	.word	63697
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_PC_Bits',0,8,191,24,16,4,11
	.byte	'PC',0,4
	.word	8857
	.byte	14,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	8857
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_PC_Bits',0,8,195,24,3
	.word	63863
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R0_Bits',0,8,198,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R0_Bits',0,8,202,24,3
	.word	63963
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R1_Bits',0,8,205,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R1_Bits',0,8,209,24,3
	.word	64065
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R2_Bits',0,8,212,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R2_Bits',0,8,216,24,3
	.word	64167
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R3_Bits',0,8,219,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R3_Bits',0,8,223,24,3
	.word	64269
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R4_Bits',0,8,226,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R4_Bits',0,8,230,24,3
	.word	64371
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R5_Bits',0,8,233,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R5_Bits',0,8,237,24,3
	.word	64473
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R6_Bits',0,8,240,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R6_Bits',0,8,244,24,3
	.word	64575
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH_R7_Bits',0,8,247,24,16,4,11
	.byte	'DATA',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R7_Bits',0,8,251,24,3
	.word	64677
	.byte	10
	.byte	'_Ifx_GTM_MCS_CTRL_Bits',0,8,254,24,16,4,11
	.byte	'SCHED',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'HLT_SP_OFL',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	14,16,2,35,0,11
	.byte	'RAM_RST',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8857
	.byte	15,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CTRL_Bits',0,8,133,25,3
	.word	64779
	.byte	10
	.byte	'_Ifx_GTM_MCS_ERR_Bits',0,8,136,25,16,4,11
	.byte	'ERR0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ERR1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ERR2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ERR3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ERR4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ERR5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'ERR6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'ERR7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_ERR_Bits',0,8,147,25,3
	.word	64943
	.byte	10
	.byte	'_Ifx_GTM_MCS_RST_Bits',0,8,150,25,16,4,11
	.byte	'RST0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'RST1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'RST2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'RST3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'RST4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'RST5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'RST6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'RST7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'CAT0',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'CAT1',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'CAT2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'CAT3',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'CAT4',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'CAT5',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'CAT6',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'CAT7',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'CWT0',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'CWT1',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'CWT2',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'CWT3',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'CWT4',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'CWT5',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'CWT6',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'CWT7',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_RST_Bits',0,8,177,25,3
	.word	65152
	.byte	10
	.byte	'_Ifx_GTM_MCSINTCLR_Bits',0,8,180,25,16,4,11
	.byte	'MCS000',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'MCS001',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MCS010',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'MCS011',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'MCS100',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'MCS101',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MCS110',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MCS111',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'MCS200',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'MCS201',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'MCS210',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'MCS211',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCSINTCLR_Bits',0,8,195,25,3
	.word	65618
	.byte	10
	.byte	'_Ifx_GTM_MCSINTSTAT_Bits',0,8,198,25,16,4,11
	.byte	'MCS000',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'MCS001',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MCS010',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'MCS011',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'MCS100',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'MCS101',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MCS110',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MCS111',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'MCS200',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'MCS201',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'MCS210',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'MCS211',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_GTM_MCSINTSTAT_Bits',0,8,213,25,3
	.word	65920
	.byte	10
	.byte	'_Ifx_GTM_MON_ACTIVITY_0_Bits',0,8,216,25,16,4,11
	.byte	'MCA_0_0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'MCA_0_1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'MCA_0_2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'MCA_0_3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'MCA_0_4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'MCA_0_5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'MCA_0_6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'MCA_0_7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'MCA_1_0',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'MCA_1_1',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'MCA_1_2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'MCA_1_3',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'MCA_1_4',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'MCA_1_5',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'MCA_1_6',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'MCA_1_7',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'MCA_2_0',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'MCA_2_1',0,4
	.word	8857
	.byte	1,14,2,35,0,11
	.byte	'MCA_2_2',0,4
	.word	8857
	.byte	1,13,2,35,0,11
	.byte	'MCA_2_3',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'MCA_2_4',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'MCA_2_5',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'MCA_2_6',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'MCA_2_7',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_MON_ACTIVITY_0_Bits',0,8,243,25,3
	.word	66224
	.byte	10
	.byte	'_Ifx_GTM_MON_STATUS_Bits',0,8,246,25,16,4,11
	.byte	'ACT_CMU0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ACT_CMU1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'ACT_CMU2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'ACT_CMU3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'ACT_CMU4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ACT_CMU5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'ACT_CMU6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'ACT_CMU7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'ACT_CMUFX0',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'ACT_CMUFX1',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'ACT_CMUFX2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'ACT_CMUFX3',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'ACT_CMUFX4',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'CMP_ERR',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	8857
	.byte	3,12,2,35,0,11
	.byte	'MCS0_ERR',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'MCS1_ERR',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'MCS2_ERR',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	9,0,2,35,0,0,21
	.byte	'Ifx_GTM_MON_STATUS_Bits',0,8,140,26,3
	.word	66776
	.byte	10
	.byte	'_Ifx_GTM_MSC0INLEXTCON_Bits',0,8,143,26,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_MSC0INLEXTCON_Bits',0,8,161,26,3
	.word	67259
	.byte	10
	.byte	'_Ifx_GTM_MSCIN_INHCON_Bits',0,8,164,26,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_MSCIN_INHCON_Bits',0,8,182,26,3
	.word	67592
	.byte	10
	.byte	'_Ifx_GTM_MSCIN_INLCON_Bits',0,8,185,26,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_MSCIN_INLCON_Bits',0,8,203,26,3
	.word	67923
	.byte	10
	.byte	'_Ifx_GTM_MSCSET_CON0_Bits',0,8,206,26,16,4,11
	.byte	'SEL0',0,4
	.word	8857
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	3,24,2,35,0,11
	.byte	'SEL1',0,4
	.word	8857
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'SEL2',0,4
	.word	8857
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'SEL3',0,4
	.word	8857
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON0_Bits',0,8,216,26,3
	.word	68254
	.byte	10
	.byte	'_Ifx_GTM_MSCSET_CON1_Bits',0,8,219,26,16,4,11
	.byte	'SEL4',0,4
	.word	8857
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	3,24,2,35,0,11
	.byte	'SEL5',0,4
	.word	8857
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'SEL6',0,4
	.word	8857
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'SEL7',0,4
	.word	8857
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON1_Bits',0,8,229,26,3
	.word	68476
	.byte	10
	.byte	'_Ifx_GTM_MSCSET_CON2_Bits',0,8,232,26,16,4,11
	.byte	'SEL8',0,4
	.word	8857
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	3,24,2,35,0,11
	.byte	'SEL9',0,4
	.word	8857
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'SEL10',0,4
	.word	8857
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'SEL11',0,4
	.word	8857
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON2_Bits',0,8,242,26,3
	.word	68698
	.byte	10
	.byte	'_Ifx_GTM_MSCSET_CON3_Bits',0,8,245,26,16,4,11
	.byte	'SEL12',0,4
	.word	8857
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	3,24,2,35,0,11
	.byte	'SEL13',0,4
	.word	8857
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'SEL14',0,4
	.word	8857
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'SEL15',0,4
	.word	8857
	.byte	5,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON3_Bits',0,8,255,26,3
	.word	68922
	.byte	10
	.byte	'_Ifx_GTM_OCS_Bits',0,8,130,27,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'SUS_P',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'SUSSTA',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_OCS_Bits',0,8,137,27,3
	.word	69148
	.byte	10
	.byte	'_Ifx_GTM_ODA_Bits',0,8,140,27,16,4,11
	.byte	'DDREN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'DREN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_ODA_Bits',0,8,145,27,3
	.word	69294
	.byte	10
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,8,148,27,16,4,11
	.byte	'CV',0,4
	.word	8857
	.byte	27,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'CM',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_OTBU0T_Bits',0,8,154,27,3
	.word	69400
	.byte	10
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,8,157,27,16,4,11
	.byte	'CV',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'EN',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_OTBU1T_Bits',0,8,163,27,3
	.word	69531
	.byte	10
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,8,166,27,16,4,11
	.byte	'CV',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'EN',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_OTBU2T_Bits',0,8,172,27,3
	.word	69662
	.byte	10
	.byte	'_Ifx_GTM_OTSC0_Bits',0,8,175,27,16,4,11
	.byte	'B0LMT',0,4
	.word	8857
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'B0LMI',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'B0HMT',0,4
	.word	8857
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'B0HMI',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'B1LMT',0,4
	.word	8857
	.byte	3,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'B1LMI',0,4
	.word	8857
	.byte	4,8,2,35,0,11
	.byte	'B1HMT',0,4
	.word	8857
	.byte	3,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'B1HMI',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_OTSC0_Bits',0,8,189,27,3
	.word	69793
	.byte	10
	.byte	'_Ifx_GTM_OTSC1_Bits',0,8,192,27,16,4,11
	.byte	'MCS',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'MI',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'MOE',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	8857
	.byte	22,0,2,35,0,0,21
	.byte	'Ifx_GTM_OTSC1_Bits',0,8,199,27,3
	.word	70075
	.byte	10
	.byte	'_Ifx_GTM_OTSS_Bits',0,8,202,27,16,4,11
	.byte	'OTGB0',0,4
	.word	8857
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	4,24,2,35,0,11
	.byte	'OTGB1',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'OTGB2',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8857
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_GTM_OTSS_Bits',0,8,210,27,3
	.word	70219
	.byte	10
	.byte	'_Ifx_GTM_REV_Bits',0,8,213,27,16,4,11
	.byte	'STEP',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'NO',0,4
	.word	8857
	.byte	4,20,2,35,0,11
	.byte	'MINOR',0,4
	.word	8857
	.byte	4,16,2,35,0,11
	.byte	'MAJOR',0,4
	.word	8857
	.byte	4,12,2,35,0,11
	.byte	'DEV_CODE0',0,4
	.word	8857
	.byte	4,8,2,35,0,11
	.byte	'DEV_CODE1',0,4
	.word	8857
	.byte	4,4,2,35,0,11
	.byte	'DEV_CODE2',0,4
	.word	8857
	.byte	4,0,2,35,0,0,21
	.byte	'Ifx_GTM_REV_Bits',0,8,222,27,3
	.word	70391
	.byte	10
	.byte	'_Ifx_GTM_RST_Bits',0,8,225,27,16,4,11
	.byte	'RST',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_GTM_RST_Bits',0,8,229,27,3
	.word	70569
	.byte	10
	.byte	'_Ifx_GTM_SPE_CMP_Bits',0,8,232,27,16,4,11
	.byte	'CMP',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_CMP_Bits',0,8,236,27,3
	.word	70657
	.byte	10
	.byte	'_Ifx_GTM_SPE_CNT_Bits',0,8,239,27,16,4,11
	.byte	'CNT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_CNT_Bits',0,8,243,27,3
	.word	70754
	.byte	10
	.byte	'_Ifx_GTM_SPE_CTRL_STAT_Bits',0,8,246,27,16,4,11
	.byte	'SPE_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'SIE0',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'SIE1',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'SIE2',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRIG_SEL',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'TIM_SEL',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'FSOM',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'SPE_PAT_PTR',0,4
	.word	8857
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'AIP',0,4
	.word	8857
	.byte	3,17,2,35,0,11
	.byte	'ADIR',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'PIP',0,4
	.word	8857
	.byte	3,13,2,35,0,11
	.byte	'PDIR',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'NIP',0,4
	.word	8857
	.byte	3,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'FSOL',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_CTRL_STAT_Bits',0,8,136,28,3
	.word	70851
	.byte	10
	.byte	'_Ifx_GTM_SPE_EIRQ_EN_Bits',0,8,139,28,16,4,11
	.byte	'SPE_NIPD_EIRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'SPE_DCHG_EIRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'SPE_PERR_EIRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'SPE_BIS_EIRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'SPE_RCMP_EIRQ_EN',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_EIRQ_EN_Bits',0,8,147,28,3
	.word	71205
	.byte	10
	.byte	'_Ifx_GTM_SPE_IRQ_EN_Bits',0,8,150,28,16,4,11
	.byte	'SPE_NIPD_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'SPE_DCHG_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'SPE_PERR_IRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'SPE_BIS_IRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'SPE_RCMP_IRQ_EN',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_EN_Bits',0,8,158,28,3
	.word	71433
	.byte	10
	.byte	'_Ifx_GTM_SPE_IRQ_FORCINT_Bits',0,8,161,28,16,4,11
	.byte	'TRG_SPE_NIPD',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_SPE_DCHG',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_SPE_PERR',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG_SPE_BIS',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRG_SPE_RCMP',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_FORCINT_Bits',0,8,169,28,3
	.word	71654
	.byte	10
	.byte	'_Ifx_GTM_SPE_IRQ_MODE_Bits',0,8,172,28,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_MODE_Bits',0,8,176,28,3
	.word	71870
	.byte	10
	.byte	'_Ifx_GTM_SPE_IRQ_NOTIFY_Bits',0,8,179,28,16,4,11
	.byte	'SPE_NIPD',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'SPE_DCHG',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'SPE_PERR',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'SPE_BIS',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'SPE_RCMP',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	8857
	.byte	27,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_NOTIFY_Bits',0,8,187,28,3
	.word	71981
	.byte	10
	.byte	'_Ifx_GTM_SPE_OUT_CTRL_Bits',0,8,190,28,16,4,11
	.byte	'SPE_OUT_CTRL',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_OUT_CTRL_Bits',0,8,194,28,3
	.word	72175
	.byte	10
	.byte	'_Ifx_GTM_SPE_OUT_PAT_Bits',0,8,197,28,16,4,11
	.byte	'SPE_OUT_PAT',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_OUT_PAT_Bits',0,8,201,28,3
	.word	72291
	.byte	10
	.byte	'_Ifx_GTM_SPE_PAT_Bits',0,8,204,28,16,4,11
	.byte	'IP0_VAL',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'IP0_PAT',0,4
	.word	8857
	.byte	3,28,2,35,0,11
	.byte	'IP1_VAL',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'IP1_PAT',0,4
	.word	8857
	.byte	3,24,2,35,0,11
	.byte	'IP2_VAL',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'IP2_PAT',0,4
	.word	8857
	.byte	3,20,2,35,0,11
	.byte	'IP3_VAL',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'IP3_PAT',0,4
	.word	8857
	.byte	3,16,2,35,0,11
	.byte	'IP4_VAL',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'IP4_PAT',0,4
	.word	8857
	.byte	3,12,2,35,0,11
	.byte	'IP5_VAL',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'IP5_PAT',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'IP6_VAL',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'IP6_PAT',0,4
	.word	8857
	.byte	3,4,2,35,0,11
	.byte	'IP7_VAL',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'IP7_PAT',0,4
	.word	8857
	.byte	3,0,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_PAT_Bits',0,8,222,28,3
	.word	72404
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,8,225,28,16,4,11
	.byte	'BASE',0,4
	.word	8857
	.byte	27,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,8,229,28,3
	.word	72767
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,8,232,28,16,4,11
	.byte	'LOW_RES',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	8857
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,8,237,28,3
	.word	72875
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,8,240,28,16,4,11
	.byte	'BASE',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,8,244,28,3
	.word	73007
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,8,247,28,16,4,11
	.byte	'CH_MODE',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	8857
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,8,252,28,3
	.word	73115
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,8,255,28,16,4,11
	.byte	'BASE',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,8,131,29,3
	.word	73247
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,8,134,29,16,4,11
	.byte	'CH_MODE',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	8857
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8857
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,8,139,29,3
	.word	73355
	.byte	10
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,8,142,29,16,4,11
	.byte	'ENDIS_CH0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CH1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CH2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8857
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,8,148,29,3
	.word	73487
	.byte	10
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,8,151,29,16,4,11
	.byte	'SRC_CH0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'SRC_CH1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'SRC_CH2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'SRC_CH3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'SRC_CH4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'SRC_CH5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'SRC_CH6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'SRC_CH7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,8,162,29,3
	.word	73633
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,8,165,29,16,4,11
	.byte	'CNT',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,8,169,29,3
	.word	73880
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,8,172,29,16,4,11
	.byte	'CNTS',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,8,176,29,3
	.word	73983
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,8,179,29,16,4,11
	.byte	'TIM_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TIM_MODE',0,4
	.word	8857
	.byte	3,28,2,35,0,11
	.byte	'OSM',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'ARU_EN',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'CICTRL',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'TBU0_SEL',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'GPR0_SEL',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'GPR1_SEL',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'CNTS_SEL',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'DSL',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'ISL',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'ECNT_RESET',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'FLT_EN',0,4
	.word	8857
	.byte	1,15,2,35,0,11
	.byte	'FLT_CNT_FRQ',0,4
	.word	8857
	.byte	2,13,2,35,0,11
	.byte	'EXT_CAP_EN',0,4
	.word	8857
	.byte	1,12,2,35,0,11
	.byte	'FLT_MODE_RE',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'FLT_CTR_RE',0,4
	.word	8857
	.byte	1,10,2,35,0,11
	.byte	'FLT_MODE_FE',0,4
	.word	8857
	.byte	1,9,2,35,0,11
	.byte	'FLT_CTR_FE',0,4
	.word	8857
	.byte	1,8,2,35,0,11
	.byte	'CLK_SEL',0,4
	.word	8857
	.byte	3,5,2,35,0,11
	.byte	'FR_ECNT_OFL',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'EGPR0_SEL',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'EGPR1_SEL',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'TOCTRL',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,8,205,29,3
	.word	74082
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,8,208,29,16,4,11
	.byte	'ECNT',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,8,212,29,3
	.word	74625
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,8,215,29,16,4,11
	.byte	'EXT_CAP_SRC',0,4
	.word	8857
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8857
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,8,219,29,3
	.word	74731
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,8,222,29,16,4,11
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'GPROFL_EIRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TODET_EIRQ_EN',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8857
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,8,231,29,3
	.word	74845
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,8,234,29,16,4,11
	.byte	'FLT_FE',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,8,238,29,3
	.word	75099
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,8,241,29,16,4,11
	.byte	'FLT_RE',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,8,245,29,3
	.word	75211
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,8,248,29,16,4,11
	.byte	'GPR0',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,8,252,29,3
	.word	75323
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,8,255,29,16,4,11
	.byte	'GPR1',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	8857
	.byte	8,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,8,131,30,3
	.word	75422
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,8,134,30,16,4,11
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'GPROFL_IRQ_EN',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TODET_IRQ_EN',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8857
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,8,143,30,3
	.word	75521
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,8,146,30,16,4,11
	.byte	'TRG_NEWVAL',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_ECNTOFL',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'TRG_CNTOFL',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'TRG_GPROFL',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TRG_TODET',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'TRG_GLITCHDET',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8857
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,8,155,30,3
	.word	75767
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,8,158,30,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,8,162,30,3
	.word	76005
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,8,165,30,16,4,11
	.byte	'NEWVAL',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'GPROFL',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'TODET',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	8857
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,8,174,30,3
	.word	76122
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,8,177,30,16,4,11
	.byte	'TO_CNT',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,8,181,30,3
	.word	76334
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,8,184,30,16,4,11
	.byte	'TOV',0,4
	.word	8857
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	20,4,2,35,0,11
	.byte	'TCS',0,4
	.word	8857
	.byte	3,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	8857
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,8,190,30,3
	.word	76441
	.byte	10
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,8,193,30,16,4,11
	.byte	'VAL_0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'MODE_0',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'VAL_1',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'MODE_1',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'VAL_2',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'MODE_2',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'VAL_3',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'MODE_3',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'VAL_4',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'MODE_4',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'VAL_5',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'MODE_5',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'VAL_6',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'MODE_6',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'VAL_7',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'MODE_7',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,8,211,30,3
	.word	76583
	.byte	10
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,8,214,30,16,4,11
	.byte	'RST_CH0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8857
	.byte	1,29,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8857
	.byte	1,28,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8857
	.byte	1,27,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8857
	.byte	1,26,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8857
	.byte	1,25,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8857
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8857
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_RST_Bits',0,8,225,30,3
	.word	76928
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,8,228,30,16,4,11
	.byte	'CM0',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,8,232,30,3
	.word	77161
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,8,235,30,16,4,11
	.byte	'CM1',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,8,239,30,3
	.word	77264
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,8,242,30,16,4,11
	.byte	'CN0',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,8,246,30,3
	.word	77367
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,8,249,30,16,4,11
	.byte	'reserved_0',0,4
	.word	8857
	.byte	11,21,2,35,0,11
	.byte	'SL',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC_SR',0,4
	.word	8857
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	8857
	.byte	5,12,2,35,0,11
	.byte	'RST_CCU0',0,4
	.word	8857
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	8857
	.byte	3,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	8857
	.byte	1,6,2,35,0,11
	.byte	'OSM',0,4
	.word	8857
	.byte	1,5,2,35,0,11
	.byte	'BITREV',0,4
	.word	8857
	.byte	1,4,2,35,0,11
	.byte	'SPEM',0,4
	.word	8857
	.byte	1,3,2,35,0,11
	.byte	'GCM',0,4
	.word	8857
	.byte	1,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,8,136,31,3
	.word	77470
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,8,139,31,16,4,11
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,8,144,31,3
	.word	77790
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,8,147,31,16,4,11
	.byte	'TRG_CCU0TC0',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'TRG_CCU1TC0',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,8,152,31,3
	.word	77933
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,8,155,31,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,8,159,31,3
	.word	78082
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,8,162,31,16,4,11
	.byte	'CCU0TC',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8857
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,8,167,31,3
	.word	78199
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,8,170,31,16,4,11
	.byte	'SR0',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,8,174,31,3
	.word	78336
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,8,177,31,16,4,11
	.byte	'SR1',0,4
	.word	8857
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,8,181,31,3
	.word	78439
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,8,184,31,16,4,11
	.byte	'OL',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,8,188,31,3
	.word	78542
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,8,191,31,16,4,11
	.byte	'ACT_TB',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	8857
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,8,197,31,3
	.word	78645
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,8,200,31,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,8,211,31,3
	.word	78799
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,8,214,31,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,8,225,31,3
	.word	79089
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,8,228,31,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,8,246,31,3
	.word	79379
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,8,249,31,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,8,141,32,3
	.word	79812
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,8,144,32,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,8,155,32,3
	.word	80262
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,8,158,32,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,8,169,32,3
	.word	80532
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,8,172,32,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,8,183,32,3
	.word	80822
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,8,186,32,16,4,11
	.byte	'ACT_TB',0,4
	.word	8857
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	8857
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	8857
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	8857
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,8,192,32,3
	.word	81112
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,8,195,32,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,8,206,32,3
	.word	81266
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,8,209,32,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,8,220,32,3
	.word	81556
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,8,223,32,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,8,241,32,3
	.word	81846
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,8,244,32,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8857
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	8857
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	8857
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	8857
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	8857
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	8857
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	8857
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	8857
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	8857
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	8857
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	8857
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	8857
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	8857
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	8857
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	8857
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	8857
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	8857
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,8,136,33,3
	.word	82279
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,8,139,33,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,8,150,33,3
	.word	82729
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,8,153,33,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,8,164,33,3
	.word	82999
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,8,167,33,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,8,178,33,3
	.word	83289
	.byte	10
	.byte	'_Ifx_GTM_TRIGOUT_Bits',0,8,181,33,16,4,11
	.byte	'INT0',0,4
	.word	8857
	.byte	2,30,2,35,0,11
	.byte	'INT1',0,4
	.word	8857
	.byte	2,28,2,35,0,11
	.byte	'TRIG0',0,4
	.word	8857
	.byte	2,26,2,35,0,11
	.byte	'TRIG1',0,4
	.word	8857
	.byte	2,24,2,35,0,11
	.byte	'TRIG2',0,4
	.word	8857
	.byte	2,22,2,35,0,11
	.byte	'TRIG3',0,4
	.word	8857
	.byte	2,20,2,35,0,11
	.byte	'TRIG4',0,4
	.word	8857
	.byte	2,18,2,35,0,11
	.byte	'TRIG5',0,4
	.word	8857
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_GTM_TRIGOUT_Bits',0,8,192,33,3
	.word	83579
	.byte	12,8,200,33,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8873
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ACCEN0',0,8,205,33,3
	.word	83795
	.byte	12,8,208,33,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9430
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ACCEN1',0,8,213,33,3
	.word	83859
	.byte	12,8,216,33,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9507
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,8,221,33,3
	.word	83923
	.byte	12,8,224,33,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9677
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,8,229,33,3
	.word	83993
	.byte	12,8,232,33,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9847
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,8,237,33,3
	.word	84063
	.byte	12,8,240,33,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9975
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_AFD_CH_BUF_ACC',0,8,245,33,3
	.word	84133
	.byte	12,8,248,33,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10085
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_ARU_ACCESS',0,8,253,33,3
	.word	84205
	.byte	12,8,128,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10251
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DATA_H',0,8,133,34,3
	.word	84277
	.byte	12,8,136,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10355
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DATA_L',0,8,141,34,3
	.word	84345
	.byte	12,8,144,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10459
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_ACCESS0',0,8,149,34,3
	.word	84413
	.byte	12,8,152,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10572
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_ACCESS1',0,8,157,34,3
	.word	84486
	.byte	12,8,160,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10685
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA0_H',0,8,165,34,3
	.word	84559
	.byte	12,8,168,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10799
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA0_L',0,8,173,34,3
	.word	84632
	.byte	12,8,176,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10913
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA1_H',0,8,181,34,3
	.word	84705
	.byte	12,8,184,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11027
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_DBG_DATA1_L',0,8,189,34,3
	.word	84778
	.byte	12,8,192,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11141
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_EN',0,8,197,34,3
	.word	84851
	.byte	12,8,200,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11310
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_FORCINT',0,8,205,34,3
	.word	84919
	.byte	12,8,208,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11479
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_MODE',0,8,213,34,3
	.word	84992
	.byte	12,8,216,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11590
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ARU_IRQ_NOTIFY',0,8,221,34,3
	.word	85062
	.byte	12,8,224,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11746
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_ACT_TB',0,8,229,34,3
	.word	85134
	.byte	12,8,232,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11900
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_CTRL',0,8,237,34,3
	.word	85207
	.byte	12,8,240,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12190
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_ENDIS_STAT',0,8,245,34,3
	.word	85284
	.byte	12,8,248,34,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12480
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_FUPD_CTRL',0,8,253,34,3
	.word	85361
	.byte	12,8,128,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12913
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_GLB_CTRL',0,8,133,35,3
	.word	85437
	.byte	12,8,136,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13363
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_INT_TRIG',0,8,141,35,3
	.word	85512
	.byte	12,8,144,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13633
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_CTRL',0,8,149,35,3
	.word	85587
	.byte	12,8,152,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_AGC_OUTEN_STAT',0,8,157,35,3
	.word	85664
	.byte	12,8,160,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14213
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CM0',0,8,165,35,3
	.word	85741
	.byte	12,8,168,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14318
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CM1',0,8,173,35,3
	.word	85810
	.byte	12,8,176,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14423
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CN0',0,8,181,35,3
	.word	85879
	.byte	12,8,184,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14528
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_CTRL',0,8,189,35,3
	.word	85948
	.byte	12,8,192,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14936
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_EN',0,8,197,35,3
	.word	86018
	.byte	12,8,200,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15081
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_FORCINT',0,8,205,35,3
	.word	86090
	.byte	12,8,208,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15230
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_MODE',0,8,213,35,3
	.word	86167
	.byte	12,8,216,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15349
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_IRQ_NOTIFY',0,8,221,35,3
	.word	86241
	.byte	12,8,224,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15488
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_RDADDR',0,8,229,35,3
	.word	86317
	.byte	12,8,232,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15644
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMC',0,8,237,35,3
	.word	86389
	.byte	12,8,240,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16017
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMI',0,8,245,35,3
	.word	86459
	.byte	12,8,248,35,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16217
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMP',0,8,253,35,3
	.word	86529
	.byte	12,8,128,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16561
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SOMS',0,8,133,36,3
	.word	86599
	.byte	12,8,136,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16818
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SR0',0,8,141,36,3
	.word	86669
	.byte	12,8,144,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_SR1',0,8,149,36,3
	.word	86738
	.byte	12,8,152,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17028
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ATOM_CH_STAT',0,8,157,36,3
	.word	86807
	.byte	12,8,160,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17240
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_EIRQ_EN',0,8,165,36,3
	.word	86877
	.byte	12,8,168,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_EN',0,8,173,36,3
	.word	86946
	.byte	12,8,176,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17924
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_FORCINT',0,8,181,36,3
	.word	87014
	.byte	12,8,184,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18288
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_MODE',0,8,189,36,3
	.word	87087
	.byte	12,8,192,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18399
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_IRQ_NOTIFY',0,8,197,36,3
	.word	87157
	.byte	12,8,200,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18709
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_RST',0,8,205,36,3
	.word	87229
	.byte	12,8,208,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18805
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC0_ADDR',0,8,213,36,3
	.word	87294
	.byte	12,8,216,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18957
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC0_DEST',0,8,221,36,3
	.word	87365
	.byte	12,8,224,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19526
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC10_ADDR',0,8,229,36,3
	.word	87436
	.byte	12,8,232,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19680
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC10_DEST',0,8,237,36,3
	.word	87508
	.byte	12,8,240,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20251
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC11_ADDR',0,8,245,36,3
	.word	87580
	.byte	12,8,248,36,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20405
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC11_DEST',0,8,253,36,3
	.word	87652
	.byte	12,8,128,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20976
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC1_ADDR',0,8,133,37,3
	.word	87724
	.byte	12,8,136,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21128
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC1_DEST',0,8,141,37,3
	.word	87795
	.byte	12,8,144,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21697
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC2_ADDR',0,8,149,37,3
	.word	87866
	.byte	12,8,152,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21849
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC2_DEST',0,8,157,37,3
	.word	87937
	.byte	12,8,160,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22418
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC3_ADDR',0,8,165,37,3
	.word	88008
	.byte	12,8,168,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22570
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC3_DEST',0,8,173,37,3
	.word	88079
	.byte	12,8,176,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23139
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC4_ADDR',0,8,181,37,3
	.word	88150
	.byte	12,8,184,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23291
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC4_DEST',0,8,189,37,3
	.word	88221
	.byte	12,8,192,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23860
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC5_ADDR',0,8,197,37,3
	.word	88292
	.byte	12,8,200,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24012
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC5_DEST',0,8,205,37,3
	.word	88363
	.byte	12,8,208,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24581
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC6_ADDR',0,8,213,37,3
	.word	88434
	.byte	12,8,216,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24733
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC6_DEST',0,8,221,37,3
	.word	88505
	.byte	12,8,224,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25302
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC7_ADDR',0,8,229,37,3
	.word	88576
	.byte	12,8,232,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25454
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC7_DEST',0,8,237,37,3
	.word	88647
	.byte	12,8,240,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26023
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC8_ADDR',0,8,245,37,3
	.word	88718
	.byte	12,8,248,37,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26175
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC8_DEST',0,8,253,37,3
	.word	88789
	.byte	12,8,128,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26744
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC9_ADDR',0,8,133,38,3
	.word	88860
	.byte	12,8,136,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26896
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRC_SRC9_DEST',0,8,141,38,3
	.word	88931
	.byte	12,8,144,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27465
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRIDGE_MODE',0,8,149,38,3
	.word	89002
	.byte	12,8,152,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27773
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,8,157,38,3
	.word	89071
	.byte	12,8,160,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27975
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,8,165,38,3
	.word	89140
	.byte	12,8,168,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28088
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CLC',0,8,173,38,3
	.word	89209
	.byte	12,8,176,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28231
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_EIRQ_EN',0,8,181,38,3
	.word	89270
	.byte	12,8,184,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28925
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_EN',0,8,189,38,3
	.word	89339
	.byte	12,8,192,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29489
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_EN',0,8,197,38,3
	.word	89403
	.byte	12,8,200,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30157
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_FORCINT',0,8,205,38,3
	.word	89471
	.byte	12,8,208,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30763
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_MODE',0,8,213,38,3
	.word	89544
	.byte	12,8,216,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30874
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMP_IRQ_NOTIFY',0,8,221,38,3
	.word	89614
	.byte	12,8,224,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31382
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,8,229,38,3
	.word	89686
	.byte	12,8,232,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31499
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,8,237,38,3
	.word	89759
	.byte	12,8,240,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31634
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,8,245,38,3
	.word	89831
	.byte	12,8,248,38,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31769
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_CLK_EN',0,8,253,38,3
	.word	89903
	.byte	12,8,128,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32089
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,8,133,39,3
	.word	89971
	.byte	12,8,136,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32201
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,8,141,39,3
	.word	90041
	.byte	12,8,144,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32313
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,8,149,39,3
	.word	90111
	.byte	12,8,152,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32429
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,8,157,39,3
	.word	90183
	.byte	12,8,160,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32541
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,8,165,39,3
	.word	90253
	.byte	12,8,168,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32653
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_CTRL',0,8,173,39,3
	.word	90323
	.byte	12,8,176,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32806
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DATAIN',0,8,181,39,3
	.word	90385
	.byte	12,8,184,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32879
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ACB',0,8,189,39,3
	.word	90449
	.byte	12,8,192,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33099
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ACT_STA',0,8,197,39,3
	.word	90515
	.byte	12,8,200,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33209
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL1',0,8,205,39,3
	.word	90585
	.byte	12,8,208,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33333
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_CAL2',0,8,213,39,3
	.word	90659
	.byte	12,8,216,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33457
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD1',0,8,221,39,3
	.word	90733
	.byte	12,8,224,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33578
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADD_IN_LD2',0,8,229,39,3
	.word	90806
	.byte	12,8,232,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33699
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ADT_S',0,8,237,39,3
	.word	90879
	.byte	12,8,240,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33817
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_AOSV_2',0,8,245,39,3
	.word	90947
	.byte	12,8,248,39,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34092
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APS',0,8,253,39,3
	.word	91016
	.byte	12,8,128,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33960
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APS_1C3',0,8,133,40,3
	.word	91082
	.byte	12,8,136,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34290
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APS_SYNC',0,8,141,40,3
	.word	91152
	.byte	12,8,144,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34608
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APT',0,8,149,40,3
	.word	91223
	.byte	12,8,152,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34478
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APT_2C',0,8,157,40,3
	.word	91289
	.byte	12,8,160,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34805
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_APT_SYNC',0,8,165,40,3
	.word	91358
	.byte	12,8,168,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34990
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_SX',0,8,173,40,3
	.word	91429
	.byte	12,8,176,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35098
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_SX_NOM',0,8,181,40,3
	.word	91498
	.byte	12,8,184,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35218
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_TX',0,8,189,40,3
	.word	91571
	.byte	12,8,192,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35326
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CDT_TX_NOM',0,8,197,40,3
	.word	91640
	.byte	12,8,200,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35446
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CNT_NUM1',0,8,205,40,3
	.word	91713
	.byte	12,8,208,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35561
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CNT_NUM2',0,8,213,40,3
	.word	91784
	.byte	12,8,216,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35676
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CRTL_1_SHADOW_STATE',0,8,221,40,3
	.word	91855
	.byte	12,8,224,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35950
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_0',0,8,229,40,3
	.word	91937
	.byte	12,8,232,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36182
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_STATE',0,8,237,40,3
	.word	92006
	.byte	12,8,240,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36426
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_0_SHADOW_TRIGGER',0,8,245,40,3
	.word	92088
	.byte	12,8,248,40,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36659
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_1',0,8,253,40,3
	.word	92172
	.byte	12,8,128,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37086
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_1_SHADOW_TRIGGER',0,8,133,41,3
	.word	92241
	.byte	12,8,136,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37320
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_2',0,8,141,41,3
	.word	92325
	.byte	12,8,144,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37688
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_3',0,8,149,41,3
	.word	92394
	.byte	12,8,152,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38068
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_CTRL_4',0,8,157,41,3
	.word	92463
	.byte	12,8,160,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38452
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DLA',0,8,165,41,3
	.word	92532
	.byte	12,8,168,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38665
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DT_S',0,8,173,41,3
	.word	92598
	.byte	12,8,176,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38551
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DT_S_ACT',0,8,181,41,3
	.word	92665
	.byte	12,8,184,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38767
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DT_T_ACT',0,8,189,41,3
	.word	92736
	.byte	12,8,192,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38881
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_DTA',0,8,197,41,3
	.word	92807
	.byte	12,8,201,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38980
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_EDT_S',0,8,206,41,3
	.word	92873
	.byte	12,8,210,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39085
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_EDT_T',0,8,215,41,3
	.word	92941
	.byte	12,8,218,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39190
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_EIRQ_EN',0,8,223,41,3
	.word	93009
	.byte	12,8,226,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39921
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_FTV_S',0,8,231,41,3
	.word	93079
	.byte	12,8,234,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40029
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_FTV_T',0,8,239,41,3
	.word	93147
	.byte	12,8,242,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40139
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_ID_PMTR',0,8,247,41,3
	.word	93215
	.byte	12,8,250,41,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40251
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_INC_CNT1',0,8,255,41,3
	.word	93285
	.byte	12,8,131,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40365
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_INC_CNT2',0,8,136,42,3
	.word	93356
	.byte	12,8,139,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40479
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_EN',0,8,144,42,3
	.word	93427
	.byte	12,8,147,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41183
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_FORCINT',0,8,152,42,3
	.word	93496
	.byte	12,8,155,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41834
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_MODE',0,8,160,42,3
	.word	93570
	.byte	12,8,163,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41947
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_IRQ_NOTIFY',0,8,168,42,3
	.word	93641
	.byte	12,8,172,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42484
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MEDT_S',0,8,177,42,3
	.word	93714
	.byte	12,8,181,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42592
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MEDT_T',0,8,186,42,3
	.word	93783
	.byte	12,8,189,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42700
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MLS1',0,8,194,42,3
	.word	93852
	.byte	12,8,197,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42802
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MLS2',0,8,202,42,3
	.word	93919
	.byte	12,8,206,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42904
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MPVAL1',0,8,211,42,3
	.word	93986
	.byte	12,8,215,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43028
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_MPVAL2',0,8,220,42,3
	.word	94055
	.byte	12,8,223,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43152
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NA',0,8,228,42,3
	.word	94124
	.byte	12,8,231,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43262
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_S',0,8,236,42,3
	.word	94189
	.byte	12,8,239,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43367
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR',0,8,244,42,3
	.word	94257
	.byte	12,8,247,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43484
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_S_TAR_OLD',0,8,252,42,3
	.word	94329
	.byte	12,8,255,42,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43613
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_T',0,8,132,43,3
	.word	94405
	.byte	12,8,135,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43718
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR',0,8,140,43,3
	.word	94473
	.byte	12,8,143,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43835
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NMB_T_TAR_OLD',0,8,148,43,3
	.word	94545
	.byte	12,8,151,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43964
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NTI_CNT',0,8,156,43,3
	.word	94621
	.byte	12,8,159,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44075
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NUSC',0,8,164,43,3
	.word	94691
	.byte	12,8,167,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44293
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_NUTC',0,8,172,43,3
	.word	94758
	.byte	12,8,175,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44534
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_OSW',0,8,180,43,3
	.word	94825
	.byte	12,8,183,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44691
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PDT_T',0,8,188,43,3
	.word	94891
	.byte	12,8,191,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44807
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSA',0,8,196,43,3
	.word	94959
	.byte	12,8,199,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44906
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSAC',0,8,204,43,3
	.word	95025
	.byte	12,8,207,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45008
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSSC',0,8,212,43,3
	.word	95092
	.byte	12,8,215,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45110
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSSM_0',0,8,220,43,3
	.word	95159
	.byte	12,8,223,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45216
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSSM_1',0,8,228,43,3
	.word	95228
	.byte	12,8,231,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45322
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSTC',0,8,236,43,3
	.word	95297
	.byte	12,8,239,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45424
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSTM_0',0,8,244,43,3
	.word	95364
	.byte	12,8,247,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45530
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PSTM_1',0,8,252,43,3
	.word	95433
	.byte	12,8,255,43,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45636
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_PVT',0,8,132,44,3
	.word	95502
	.byte	12,8,135,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45735
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RAM_INI',0,8,140,44,3
	.word	95568
	.byte	12,8,143,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45924
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_SX',0,8,148,44,3
	.word	95638
	.byte	12,8,152,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46035
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_SX_NOM',0,8,157,44,3
	.word	95708
	.byte	12,8,160,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46158
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_TX',0,8,165,44,3
	.word	95782
	.byte	12,8,169,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46269
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RCDT_TX_NOM',0,8,174,44,3
	.word	95852
	.byte	12,8,177,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46509
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RDT_S',0,8,182,44,3
	.word	95926
	.byte	12,8,185,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46392
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RDT_S_ACT',0,8,190,44,3
	.word	95994
	.byte	12,8,193,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46614
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_RDT_T_ACT',0,8,198,44,3
	.word	96066
	.byte	12,8,201,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46731
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_SLR',0,8,206,44,3
	.word	96138
	.byte	12,8,209,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46829
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_STATUS',0,8,214,44,3
	.word	96204
	.byte	12,8,217,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47410
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TBU_TS0_S',0,8,222,44,3
	.word	96273
	.byte	12,8,225,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47527
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TBU_TS0_T',0,8,230,44,3
	.word	96345
	.byte	12,8,233,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47644
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_THMA',0,8,238,44,3
	.word	96417
	.byte	12,8,241,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47746
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_THMI',0,8,246,44,3
	.word	96484
	.byte	12,8,249,44,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47848
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_THVAL',0,8,254,44,3
	.word	96551
	.byte	12,8,129,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47953
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TLR',0,8,134,45,3
	.word	96619
	.byte	12,8,137,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48051
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TOV',0,8,142,45,3
	.word	96685
	.byte	12,8,145,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48163
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TOV_S',0,8,150,45,3
	.word	96751
	.byte	12,8,153,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48279
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_S_0',0,8,158,45,3
	.word	96819
	.byte	12,8,161,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48389
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_S_1',0,8,166,45,3
	.word	96888
	.byte	12,8,169,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48499
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_T_0',0,8,174,45,3
	.word	96957
	.byte	12,8,177,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48611
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TS_T_1',0,8,182,45,3
	.word	97026
	.byte	12,8,185,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48723
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TSAC',0,8,190,45,3
	.word	97095
	.byte	12,8,193,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48825
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DPLL_TSF_S',0,8,198,45,3
	.word	97162
	.byte	12,8,201,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48930
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DXINCON',0,8,206,45,3
	.word	97230
	.byte	12,8,209,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49278
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_DXOUTCON',0,8,214,45,3
	.word	97295
	.byte	12,8,217,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49486
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_EIRQ_EN',0,8,222,45,3
	.word	97361
	.byte	12,8,225,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49690
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_F2A_ENABLE',0,8,230,45,3
	.word	97426
	.byte	12,8,233,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49930
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_F2A_RD_CH_ARU_RD_FIFO',0,8,238,45,3
	.word	97494
	.byte	12,8,241,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50055
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_F2A_STR_CH_STR_CFG',0,8,246,45,3
	.word	97573
	.byte	12,8,249,45,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50213
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_CTRL',0,8,254,45,3
	.word	97649
	.byte	12,8,129,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50369
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_EIRQ_EN',0,8,134,46,3
	.word	97719
	.byte	12,8,137,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50579
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_END_ADDR',0,8,142,46,3
	.word	97792
	.byte	12,8,145,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50695
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_FILL_LEVEL',0,8,150,46,3
	.word	97866
	.byte	12,8,153,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50816
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_EN',0,8,158,46,3
	.word	97942
	.byte	12,8,161,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51022
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_FORCINT',0,8,166,46,3
	.word	98014
	.byte	12,8,169,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51226
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_MODE',0,8,174,46,3
	.word	98091
	.byte	12,8,177,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51395
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_IRQ_NOTIFY',0,8,182,46,3
	.word	98165
	.byte	12,8,185,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51581
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_LOWER_WM',0,8,190,46,3
	.word	98241
	.byte	12,8,193,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51697
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_RD_PTR',0,8,198,46,3
	.word	98315
	.byte	12,8,201,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51809
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_START_ADDR',0,8,206,46,3
	.word	98387
	.byte	12,8,209,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51929
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_STATUS',0,8,214,46,3
	.word	98463
	.byte	12,8,217,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52092
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_UPPER_WM',0,8,222,46,3
	.word	98535
	.byte	12,8,225,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52208
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_FIFO_CH_WR_PTR',0,8,230,46,3
	.word	98609
	.byte	12,8,233,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52320
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_0',0,8,238,46,3
	.word	98681
	.byte	12,8,241,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52804
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_1',0,8,246,46,3
	.word	98749
	.byte	12,8,249,46,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53545
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_2',0,8,254,46,3
	.word	98817
	.byte	12,8,129,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54209
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_4',0,8,134,47,3
	.word	98885
	.byte	12,8,137,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54873
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_6',0,8,142,47,3
	.word	98953
	.byte	12,8,145,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55718
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_9',0,8,150,47,3
	.word	99021
	.byte	12,8,153,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_CEI0',0,8,158,47,3
	.word	99089
	.byte	12,8,161,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56884
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,8,166,47,3
	.word	99160
	.byte	12,8,169,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57578
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_CEI3',0,8,174,47,3
	.word	99231
	.byte	12,8,177,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58272
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,8,182,47,3
	.word	99302
	.byte	12,8,185,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58725
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,8,190,47,3
	.word	99372
	.byte	12,8,193,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58936
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_DSADC_INSEL',0,8,198,47,3
	.word	99449
	.byte	12,8,201,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59165
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_DSADC_OUTSEL',0,8,206,47,3
	.word	99527
	.byte	12,8,209,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59368
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_PSI5_OUTSEL0',0,8,214,47,3
	.word	99606
	.byte	12,8,217,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59574
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_PSI5S_OUTSEL',0,8,222,47,3
	.word	99685
	.byte	12,8,225,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59803
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,8,230,47,3
	.word	99764
	.byte	12,8,233,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60144
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,8,238,47,3
	.word	99839
	.byte	12,8,241,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60369
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_EN',0,8,246,47,3
	.word	99915
	.byte	12,8,249,47,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60567
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_FORCINT',0,8,254,47,3
	.word	99979
	.byte	12,8,129,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60763
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_MODE',0,8,134,48,3
	.word	100048
	.byte	12,8,137,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60866
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,8,142,48,3
	.word	100114
	.byte	12,8,145,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61044
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_KRST0',0,8,150,48,3
	.word	100182
	.byte	12,8,153,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61155
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_KRST1',0,8,158,48,3
	.word	100245
	.byte	12,8,161,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61247
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_KRSTCLR',0,8,166,48,3
	.word	100308
	.byte	12,8,169,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61343
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MAP_CTRL',0,8,174,48,3
	.word	100373
	.byte	12,8,177,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61773
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCFG_CTRL',0,8,182,48,3
	.word	100439
	.byte	12,8,185,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61906
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH0_CTRG',0,8,190,48,3
	.word	100506
	.byte	12,8,193,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62260
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH0_STRG',0,8,198,48,3
	.word	100576
	.byte	12,8,201,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62614
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_ACB',0,8,206,48,3
	.word	100646
	.byte	12,8,209,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62781
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_CTRL',0,8,214,48,3
	.word	100714
	.byte	12,8,217,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63061
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_EIRQ_EN',0,8,222,48,3
	.word	100783
	.byte	12,8,225,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63233
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_EN',0,8,230,48,3
	.word	100855
	.byte	12,8,233,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63400
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_FORCINT',0,8,238,48,3
	.word	100926
	.byte	12,8,241,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63580
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_MODE',0,8,246,48,3
	.word	101002
	.byte	12,8,249,48,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63697
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_IRQ_NOTIFY',0,8,254,48,3
	.word	101075
	.byte	12,8,129,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63863
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_PC',0,8,134,49,3
	.word	101150
	.byte	12,8,137,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63963
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R0',0,8,142,49,3
	.word	101217
	.byte	12,8,145,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64065
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R1',0,8,150,49,3
	.word	101284
	.byte	12,8,153,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64167
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R2',0,8,158,49,3
	.word	101351
	.byte	12,8,161,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64269
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R3',0,8,166,49,3
	.word	101418
	.byte	12,8,169,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64371
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R4',0,8,174,49,3
	.word	101485
	.byte	12,8,177,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64473
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R5',0,8,182,49,3
	.word	101552
	.byte	12,8,185,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64575
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R6',0,8,190,49,3
	.word	101619
	.byte	12,8,193,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64677
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CH_R7',0,8,198,49,3
	.word	101686
	.byte	12,8,201,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64779
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_CTRL',0,8,206,49,3
	.word	101753
	.byte	12,8,209,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64943
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_ERR',0,8,214,49,3
	.word	101819
	.byte	12,8,217,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65152
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCS_RST',0,8,222,49,3
	.word	101884
	.byte	12,8,225,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65618
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCSINTCLR',0,8,230,49,3
	.word	101949
	.byte	12,8,233,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65920
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MCSINTSTAT',0,8,238,49,3
	.word	102016
	.byte	12,8,241,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66224
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MON_ACTIVITY_0',0,8,246,49,3
	.word	102084
	.byte	12,8,249,49,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66776
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MON_STATUS',0,8,254,49,3
	.word	102156
	.byte	12,8,129,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67259
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MSC0INLEXTCON',0,8,134,50,3
	.word	102224
	.byte	12,8,137,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67592
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MSCIN_INHCON',0,8,142,50,3
	.word	102295
	.byte	12,8,145,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MSCIN_INLCON',0,8,150,50,3
	.word	102365
	.byte	12,8,153,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68254
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON0',0,8,158,50,3
	.word	102435
	.byte	12,8,161,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68476
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON1',0,8,166,50,3
	.word	102504
	.byte	12,8,169,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68698
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON2',0,8,174,50,3
	.word	102573
	.byte	12,8,177,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68922
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_MSCSET_CON3',0,8,182,50,3
	.word	102642
	.byte	12,8,185,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69148
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_OCS',0,8,190,50,3
	.word	102711
	.byte	12,8,193,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69294
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_ODA',0,8,198,50,3
	.word	102772
	.byte	12,8,201,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69400
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_OTBU0T',0,8,206,50,3
	.word	102833
	.byte	12,8,209,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69531
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_OTBU1T',0,8,214,50,3
	.word	102897
	.byte	12,8,217,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69662
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_OTBU2T',0,8,222,50,3
	.word	102961
	.byte	12,8,225,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69793
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_OTSC0',0,8,230,50,3
	.word	103025
	.byte	12,8,233,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70075
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_OTSC1',0,8,238,50,3
	.word	103088
	.byte	12,8,241,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70219
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_OTSS',0,8,246,50,3
	.word	103151
	.byte	12,8,249,50,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70391
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_REV',0,8,254,50,3
	.word	103213
	.byte	12,8,129,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70569
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_RST',0,8,134,51,3
	.word	103274
	.byte	12,8,137,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70657
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_CMP',0,8,142,51,3
	.word	103335
	.byte	12,8,145,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70754
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_CNT',0,8,150,51,3
	.word	103400
	.byte	12,8,153,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70851
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_CTRL_STAT',0,8,158,51,3
	.word	103465
	.byte	12,8,161,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71205
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_EIRQ_EN',0,8,166,51,3
	.word	103536
	.byte	12,8,169,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_EN',0,8,174,51,3
	.word	103605
	.byte	12,8,177,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71654
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_FORCINT',0,8,182,51,3
	.word	103673
	.byte	12,8,185,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71870
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_MODE',0,8,190,51,3
	.word	103746
	.byte	12,8,193,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71981
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_IRQ_NOTIFY',0,8,198,51,3
	.word	103816
	.byte	12,8,201,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72175
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_OUT_CTRL',0,8,206,51,3
	.word	103888
	.byte	12,8,209,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72291
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_OUT_PAT',0,8,214,51,3
	.word	103958
	.byte	12,8,217,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72404
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_SPE_PAT',0,8,222,51,3
	.word	104027
	.byte	12,8,225,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72767
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,8,230,51,3
	.word	104092
	.byte	12,8,233,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72875
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,8,238,51,3
	.word	104162
	.byte	12,8,241,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73007
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,8,246,51,3
	.word	104232
	.byte	12,8,249,51,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73115
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,8,254,51,3
	.word	104302
	.byte	12,8,129,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73247
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,8,134,52,3
	.word	104372
	.byte	12,8,137,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73355
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,8,142,52,3
	.word	104442
	.byte	12,8,145,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73487
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TBU_CHEN',0,8,150,52,3
	.word	104512
	.byte	12,8,153,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73633
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,8,158,52,3
	.word	104578
	.byte	12,8,161,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73880
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_CNT',0,8,166,52,3
	.word	104650
	.byte	12,8,169,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73983
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,8,174,52,3
	.word	104718
	.byte	12,8,177,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74082
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,8,182,52,3
	.word	104787
	.byte	12,8,185,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74625
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,8,190,52,3
	.word	104856
	.byte	12,8,193,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74731
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,8,198,52,3
	.word	104925
	.byte	12,8,201,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74845
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,8,206,52,3
	.word	104995
	.byte	12,8,209,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75099
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,8,214,52,3
	.word	105067
	.byte	12,8,217,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75211
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,8,222,52,3
	.word	105138
	.byte	12,8,225,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75323
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,8,230,52,3
	.word	105209
	.byte	12,8,233,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75422
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,8,238,52,3
	.word	105278
	.byte	12,8,241,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75521
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,8,246,52,3
	.word	105347
	.byte	12,8,249,52,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75767
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,8,254,52,3
	.word	105418
	.byte	12,8,129,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76005
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,8,134,53,3
	.word	105494
	.byte	12,8,137,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76122
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,8,142,53,3
	.word	105567
	.byte	12,8,145,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76334
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,8,150,53,3
	.word	105642
	.byte	12,8,153,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76441
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,8,158,53,3
	.word	105711
	.byte	12,8,161,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_IN_SRC',0,8,166,53,3
	.word	105780
	.byte	12,8,169,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76928
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TIM_RST',0,8,174,53,3
	.word	105848
	.byte	12,8,177,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77161
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CM0',0,8,182,53,3
	.word	105913
	.byte	12,8,185,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77264
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CM1',0,8,190,53,3
	.word	105981
	.byte	12,8,193,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77367
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CN0',0,8,198,53,3
	.word	106049
	.byte	12,8,201,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77470
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,8,206,53,3
	.word	106117
	.byte	12,8,209,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77790
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,8,214,53,3
	.word	106186
	.byte	12,8,217,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77933
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,8,222,53,3
	.word	106257
	.byte	12,8,225,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78082
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,8,230,53,3
	.word	106333
	.byte	12,8,233,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78199
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,8,238,53,3
	.word	106406
	.byte	12,8,241,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78336
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_SR0',0,8,246,53,3
	.word	106481
	.byte	12,8,249,53,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78439
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_SR1',0,8,254,53,3
	.word	106549
	.byte	12,8,129,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78542
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_CH_STAT',0,8,134,54,3
	.word	106617
	.byte	12,8,137,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78645
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,8,142,54,3
	.word	106686
	.byte	12,8,145,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78799
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,8,150,54,3
	.word	106759
	.byte	12,8,153,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79089
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,8,158,54,3
	.word	106836
	.byte	12,8,161,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79379
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,8,166,54,3
	.word	106913
	.byte	12,8,169,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79812
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,8,174,54,3
	.word	106989
	.byte	12,8,177,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80262
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,8,182,54,3
	.word	107064
	.byte	12,8,185,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80532
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,8,190,54,3
	.word	107139
	.byte	12,8,193,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80822
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,8,198,54,3
	.word	107216
	.byte	12,8,201,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81112
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,8,206,54,3
	.word	107293
	.byte	12,8,209,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81266
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,8,214,54,3
	.word	107366
	.byte	12,8,217,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81556
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,8,222,54,3
	.word	107443
	.byte	12,8,225,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81846
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,8,230,54,3
	.word	107520
	.byte	12,8,233,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82279
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,8,238,54,3
	.word	107596
	.byte	12,8,241,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82729
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,8,246,54,3
	.word	107671
	.byte	12,8,249,54,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82999
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,8,254,54,3
	.word	107746
	.byte	12,8,129,55,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83289
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,8,134,55,3
	.word	107823
	.byte	12,8,137,55,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83579
	.byte	4,2,35,0,0,21
	.byte	'Ifx_GTM_TRIGOUT',0,8,142,55,3
	.word	107900
	.byte	10
	.byte	'_Ifx_GTM_AFD_CH',0,8,153,55,25,16,13
	.byte	'BUF_ACC',0
	.word	84133
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3691
	.byte	12,2,35,4,0,16
	.word	107965
	.byte	21
	.byte	'Ifx_GTM_AFD_CH',0,8,157,55,3
	.word	108025
	.byte	14,32
	.word	487
	.byte	15,31,0,10
	.byte	'_Ifx_GTM_ATOM_AGC',0,8,160,55,25,64,13
	.byte	'GLB_CTRL',0
	.word	85437
	.byte	4,2,35,0,13
	.byte	'ENDIS_CTRL',0
	.word	85207
	.byte	4,2,35,4,13
	.byte	'ENDIS_STAT',0
	.word	85284
	.byte	4,2,35,8,13
	.byte	'ACT_TB',0
	.word	85134
	.byte	4,2,35,12,13
	.byte	'OUTEN_CTRL',0
	.word	85587
	.byte	4,2,35,16,13
	.byte	'OUTEN_STAT',0
	.word	85664
	.byte	4,2,35,20,13
	.byte	'FUPD_CTRL',0
	.word	85361
	.byte	4,2,35,24,13
	.byte	'INT_TRIG',0
	.word	85512
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	108054
	.byte	32,2,35,32,0,16
	.word	108063
	.byte	21
	.byte	'Ifx_GTM_ATOM_AGC',0,8,171,55,3
	.word	108260
	.byte	12,8,177,55,5,4,13
	.byte	'CTRL',0
	.word	85948
	.byte	4,2,35,0,13
	.byte	'SOMC',0
	.word	86389
	.byte	4,2,35,0,13
	.byte	'SOMI',0
	.word	86459
	.byte	4,2,35,0,13
	.byte	'SOMP',0
	.word	86529
	.byte	4,2,35,0,13
	.byte	'SOMS',0
	.word	86599
	.byte	4,2,35,0,0,14,16
	.word	487
	.byte	15,15,0,10
	.byte	'_Ifx_GTM_ATOM_CH',0,8,174,55,25,64,13
	.byte	'RDADDR',0
	.word	86317
	.byte	4,2,35,0,23
	.word	108291
	.byte	4,2,35,4,13
	.byte	'SR0',0
	.word	86669
	.byte	4,2,35,8,13
	.byte	'SR1',0
	.word	86738
	.byte	4,2,35,12,13
	.byte	'CM0',0
	.word	85741
	.byte	4,2,35,16,13
	.byte	'CM1',0
	.word	85810
	.byte	4,2,35,20,13
	.byte	'CN0',0
	.word	85879
	.byte	4,2,35,24,13
	.byte	'STAT',0
	.word	86807
	.byte	4,2,35,28,13
	.byte	'IRQ_NOTIFY',0
	.word	86241
	.byte	4,2,35,32,13
	.byte	'IRQ_EN',0
	.word	86018
	.byte	4,2,35,36,13
	.byte	'IRQ_FORCINT',0
	.word	86090
	.byte	4,2,35,40,13
	.byte	'IRQ_MODE',0
	.word	86167
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	108368
	.byte	16,2,35,48,0,16
	.word	108377
	.byte	21
	.byte	'Ifx_GTM_ATOM_CH',0,8,197,55,3
	.word	108601
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,8,200,55,25,4,13
	.byte	'CTRL',0
	.word	89686
	.byte	4,2,35,0,0,16
	.word	108631
	.byte	21
	.byte	'Ifx_GTM_CMU_CLK0_5',0,8,203,55,3
	.word	108672
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_6',0,8,206,55,25,4,13
	.byte	'CTRL',0
	.word	89759
	.byte	4,2,35,0,0,16
	.word	108705
	.byte	21
	.byte	'Ifx_GTM_CMU_CLK_6',0,8,209,55,3
	.word	108745
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_7',0,8,212,55,25,4,13
	.byte	'CTRL',0
	.word	89831
	.byte	4,2,35,0,0,16
	.word	108777
	.byte	21
	.byte	'Ifx_GTM_CMU_CLK_7',0,8,215,55,3
	.word	108817
	.byte	10
	.byte	'_Ifx_GTM_CMU_ECLK',0,8,218,55,25,8,13
	.byte	'NUM',0
	.word	90041
	.byte	4,2,35,0,13
	.byte	'DEN',0
	.word	89971
	.byte	4,2,35,4,0,16
	.word	108849
	.byte	21
	.byte	'Ifx_GTM_CMU_ECLK',0,8,222,55,3
	.word	108900
	.byte	10
	.byte	'_Ifx_GTM_CMU_FXCLK',0,8,225,55,25,4,13
	.byte	'CTRL',0
	.word	90111
	.byte	4,2,35,0,0,16
	.word	108931
	.byte	21
	.byte	'Ifx_GTM_CMU_FXCLK',0,8,228,55,3
	.word	108971
	.byte	10
	.byte	'_Ifx_GTM_F2A_RD_CH',0,8,231,55,25,4,13
	.byte	'ARU_RD_FIFO',0
	.word	97494
	.byte	4,2,35,0,0,16
	.word	109003
	.byte	21
	.byte	'Ifx_GTM_F2A_RD_CH',0,8,234,55,3
	.word	109050
	.byte	10
	.byte	'_Ifx_GTM_F2A_STR_CH',0,8,237,55,25,4,13
	.byte	'STR_CFG',0
	.word	97573
	.byte	4,2,35,0,0,16
	.word	109082
	.byte	21
	.byte	'Ifx_GTM_F2A_STR_CH',0,8,240,55,3
	.word	109126
	.byte	10
	.byte	'_Ifx_GTM_FIFO_CH',0,8,243,55,25,64,13
	.byte	'CTRL',0
	.word	97649
	.byte	4,2,35,0,13
	.byte	'END_ADDR',0
	.word	97792
	.byte	4,2,35,4,13
	.byte	'START_ADDR',0
	.word	98387
	.byte	4,2,35,8,13
	.byte	'UPPER_WM',0
	.word	98535
	.byte	4,2,35,12,13
	.byte	'LOWER_WM',0
	.word	98241
	.byte	4,2,35,16,13
	.byte	'STATUS',0
	.word	98463
	.byte	4,2,35,20,13
	.byte	'FILL_LEVEL',0
	.word	97866
	.byte	4,2,35,24,13
	.byte	'WR_PTR',0
	.word	98609
	.byte	4,2,35,28,13
	.byte	'RD_PTR',0
	.word	98315
	.byte	4,2,35,32,13
	.byte	'IRQ_NOTIFY',0
	.word	98165
	.byte	4,2,35,36,13
	.byte	'IRQ_EN',0
	.word	97942
	.byte	4,2,35,40,13
	.byte	'IRQ_FORCINT',0
	.word	98014
	.byte	4,2,35,44,13
	.byte	'IRQ_MODE',0
	.word	98091
	.byte	4,2,35,48,13
	.byte	'EIRQ_EN',0
	.word	97719
	.byte	4,2,35,52,13
	.byte	'reserved_38',0
	.word	3351
	.byte	8,2,35,56,0,16
	.word	109159
	.byte	21
	.byte	'Ifx_GTM_FIFO_CH',0,8,132,56,3
	.word	109452
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,8,135,56,25,4,13
	.byte	'OUTSEL',0
	.word	99372
	.byte	4,2,35,0,0,16
	.word	109482
	.byte	21
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,8,138,56,3
	.word	109527
	.byte	14,12
	.word	99449
	.byte	15,2,0,10
	.byte	'_Ifx_GTM_INOUTSEL_DSADC',0,8,141,56,25,24,13
	.byte	'INSEL',0
	.word	109562
	.byte	12,2,35,0,13
	.byte	'OUTSEL00',0
	.word	99527
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	1532
	.byte	4,2,35,16,13
	.byte	'OUTSEL10',0
	.word	99527
	.byte	4,2,35,20,0,16
	.word	109571
	.byte	21
	.byte	'Ifx_GTM_INOUTSEL_DSADC',0,8,147,56,3
	.word	109674
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5',0,8,150,56,25,4,13
	.byte	'OUTSEL0',0
	.word	99606
	.byte	4,2,35,0,0,16
	.word	109711
	.byte	21
	.byte	'Ifx_GTM_INOUTSEL_PSI5',0,8,153,56,3
	.word	109758
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_PSI5S',0,8,156,56,25,4,13
	.byte	'OUTSEL',0
	.word	99685
	.byte	4,2,35,0,0,16
	.word	109794
	.byte	21
	.byte	'Ifx_GTM_INOUTSEL_PSI5S',0,8,159,56,3
	.word	109841
	.byte	14,40
	.word	99764
	.byte	15,9,0,10
	.byte	'_Ifx_GTM_INOUTSEL_T',0,8,162,56,25,40,13
	.byte	'OUTSEL',0
	.word	109878
	.byte	40,2,35,0,0,16
	.word	109887
	.byte	21
	.byte	'Ifx_GTM_INOUTSEL_T',0,8,165,56,3
	.word	109930
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,8,168,56,25,4,13
	.byte	'INSEL',0
	.word	99839
	.byte	4,2,35,0,0,16
	.word	109963
	.byte	21
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,8,171,56,3
	.word	110007
	.byte	14,40
	.word	487
	.byte	15,39,0,10
	.byte	'_Ifx_GTM_MCS_CH',0,8,174,56,25,128,1,13
	.byte	'R0',0
	.word	101217
	.byte	4,2,35,0,13
	.byte	'R1',0
	.word	101284
	.byte	4,2,35,4,13
	.byte	'R2',0
	.word	101351
	.byte	4,2,35,8,13
	.byte	'R3',0
	.word	101418
	.byte	4,2,35,12,13
	.byte	'R4',0
	.word	101485
	.byte	4,2,35,16,13
	.byte	'R5',0
	.word	101552
	.byte	4,2,35,20,13
	.byte	'R6',0
	.word	101619
	.byte	4,2,35,24,13
	.byte	'R7',0
	.word	101686
	.byte	4,2,35,28,13
	.byte	'CTRL',0
	.word	100714
	.byte	4,2,35,32,13
	.byte	'ACB',0
	.word	100646
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2722
	.byte	24,2,35,40,13
	.byte	'PC',0
	.word	101150
	.byte	4,2,35,64,13
	.byte	'IRQ_NOTIFY',0
	.word	101075
	.byte	4,2,35,68,13
	.byte	'IRQ_EN',0
	.word	100855
	.byte	4,2,35,72,13
	.byte	'IRQ_FORCINT',0
	.word	100926
	.byte	4,2,35,76,13
	.byte	'IRQ_MODE',0
	.word	101002
	.byte	4,2,35,80,13
	.byte	'EIRQ_EN',0
	.word	100783
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	110042
	.byte	40,2,35,88,0,16
	.word	110051
	.byte	21
	.byte	'Ifx_GTM_MCS_CH',0,8,194,56,3
	.word	110344
	.byte	10
	.byte	'_Ifx_GTM_MCS_CH0',0,8,197,56,25,88,13
	.byte	'R0',0
	.word	101217
	.byte	4,2,35,0,13
	.byte	'R1',0
	.word	101284
	.byte	4,2,35,4,13
	.byte	'R2',0
	.word	101351
	.byte	4,2,35,8,13
	.byte	'R3',0
	.word	101418
	.byte	4,2,35,12,13
	.byte	'R4',0
	.word	101485
	.byte	4,2,35,16,13
	.byte	'R5',0
	.word	101552
	.byte	4,2,35,20,13
	.byte	'R6',0
	.word	101619
	.byte	4,2,35,24,13
	.byte	'R7',0
	.word	101686
	.byte	4,2,35,28,13
	.byte	'CTRL',0
	.word	100714
	.byte	4,2,35,32,13
	.byte	'ACB',0
	.word	100646
	.byte	4,2,35,36,13
	.byte	'CTRG',0
	.word	100506
	.byte	4,2,35,40,13
	.byte	'STRG',0
	.word	100576
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	108368
	.byte	16,2,35,48,13
	.byte	'PC',0
	.word	101150
	.byte	4,2,35,64,13
	.byte	'IRQ_NOTIFY',0
	.word	101075
	.byte	4,2,35,68,13
	.byte	'IRQ_EN',0
	.word	100855
	.byte	4,2,35,72,13
	.byte	'IRQ_FORCINT',0
	.word	100926
	.byte	4,2,35,76,13
	.byte	'IRQ_MODE',0
	.word	101002
	.byte	4,2,35,80,13
	.byte	'EIRQ_EN',0
	.word	100783
	.byte	4,2,35,84,0,16
	.word	110373
	.byte	21
	.byte	'Ifx_GTM_MCS_CH0',0,8,218,56,3
	.word	110673
	.byte	14,56
	.word	487
	.byte	15,55,0,10
	.byte	'_Ifx_GTM_TIM_CH',0,8,221,56,25,120,13
	.byte	'GPR0',0
	.word	105209
	.byte	4,2,35,0,13
	.byte	'GPR1',0
	.word	105278
	.byte	4,2,35,4,13
	.byte	'CNT',0
	.word	104650
	.byte	4,2,35,8,13
	.byte	'ECNT',0
	.word	104856
	.byte	4,2,35,12,13
	.byte	'CNTS',0
	.word	104718
	.byte	4,2,35,16,13
	.byte	'TDUC',0
	.word	105642
	.byte	4,2,35,20,13
	.byte	'TDUV',0
	.word	105711
	.byte	4,2,35,24,13
	.byte	'FLT_RE',0
	.word	105138
	.byte	4,2,35,28,13
	.byte	'FLT_FE',0
	.word	105067
	.byte	4,2,35,32,13
	.byte	'CTRL',0
	.word	104787
	.byte	4,2,35,36,13
	.byte	'ECTRL',0
	.word	104925
	.byte	4,2,35,40,13
	.byte	'IRQ_NOTIFY',0
	.word	105567
	.byte	4,2,35,44,13
	.byte	'IRQ_EN',0
	.word	105347
	.byte	4,2,35,48,13
	.byte	'IRQ_FORCINT',0
	.word	105418
	.byte	4,2,35,52,13
	.byte	'IRQ_MODE',0
	.word	105494
	.byte	4,2,35,56,13
	.byte	'EIRQ_EN',0
	.word	104995
	.byte	4,2,35,60,13
	.byte	'reserved_40',0
	.word	110703
	.byte	56,2,35,64,0,16
	.word	110712
	.byte	21
	.byte	'Ifx_GTM_TIM_CH',0,8,240,56,3
	.word	111006
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH',0,8,243,56,25,48,13
	.byte	'CTRL',0
	.word	106117
	.byte	4,2,35,0,13
	.byte	'SR0',0
	.word	106481
	.byte	4,2,35,4,13
	.byte	'SR1',0
	.word	106549
	.byte	4,2,35,8,13
	.byte	'CM0',0
	.word	105913
	.byte	4,2,35,12,13
	.byte	'CM1',0
	.word	105981
	.byte	4,2,35,16,13
	.byte	'CN0',0
	.word	106049
	.byte	4,2,35,20,13
	.byte	'STAT',0
	.word	106617
	.byte	4,2,35,24,13
	.byte	'IRQ_NOTIFY',0
	.word	106406
	.byte	4,2,35,28,13
	.byte	'IRQ_EN',0
	.word	106186
	.byte	4,2,35,32,13
	.byte	'IRQ_FORCINT',0
	.word	106257
	.byte	4,2,35,36,13
	.byte	'IRQ_MODE',0
	.word	106333
	.byte	4,2,35,40,13
	.byte	'reserved_2C',0
	.word	1532
	.byte	4,2,35,44,0,16
	.word	111035
	.byte	21
	.byte	'Ifx_GTM_TOM_CH',0,8,129,57,3
	.word	111247
	.byte	14,128,1
	.word	107965
	.byte	15,7,0,16
	.word	111276
	.byte	10
	.byte	'_Ifx_GTM_AFD',0,8,142,57,25,128,1,13
	.byte	'CH',0
	.word	111286
	.byte	128,1,2,35,0,0,16
	.word	111291
	.byte	21
	.byte	'Ifx_GTM_AFD',0,8,145,57,3
	.word	111325
	.byte	10
	.byte	'_Ifx_GTM_ARU',0,8,148,57,25,52,13
	.byte	'ARU_ACCESS',0
	.word	84205
	.byte	4,2,35,0,13
	.byte	'DATA_H',0
	.word	84277
	.byte	4,2,35,4,13
	.byte	'DATA_L',0
	.word	84345
	.byte	4,2,35,8,13
	.byte	'DBG_ACCESS0',0
	.word	84413
	.byte	4,2,35,12,13
	.byte	'DBG_DATA0_H',0
	.word	84559
	.byte	4,2,35,16,13
	.byte	'DBG_DATA0_L',0
	.word	84632
	.byte	4,2,35,20,13
	.byte	'DBG_ACCESS1',0
	.word	84486
	.byte	4,2,35,24,13
	.byte	'DBG_DATA1_H',0
	.word	84705
	.byte	4,2,35,28,13
	.byte	'DBG_DATA1_L',0
	.word	84778
	.byte	4,2,35,32,13
	.byte	'IRQ_NOTIFY',0
	.word	85062
	.byte	4,2,35,36,13
	.byte	'IRQ_EN',0
	.word	84851
	.byte	4,2,35,40,13
	.byte	'IRQ_FORCINT',0
	.word	84919
	.byte	4,2,35,44,13
	.byte	'IRQ_MODE',0
	.word	84992
	.byte	4,2,35,48,0,16
	.word	111351
	.byte	21
	.byte	'Ifx_GTM_ARU',0,8,163,57,3
	.word	111624
	.byte	16
	.word	108377
	.byte	16
	.word	108063
	.byte	16
	.word	108377
	.byte	14,64
	.word	487
	.byte	15,63,0,16
	.word	108377
	.byte	16
	.word	108377
	.byte	16
	.word	108377
	.byte	16
	.word	108377
	.byte	16
	.word	108377
	.byte	16
	.word	108377
	.byte	14,192,8
	.word	487
	.byte	15,191,8,0,10
	.byte	'_Ifx_GTM_ATOM',0,8,166,57,25,128,16,13
	.byte	'CH0',0
	.word	111650
	.byte	64,2,35,0,13
	.byte	'AGC',0
	.word	111655
	.byte	64,2,35,64,13
	.byte	'CH1',0
	.word	111660
	.byte	64,3,35,128,1,13
	.byte	'reserved_C0',0
	.word	111665
	.byte	64,3,35,192,1,13
	.byte	'CH2',0
	.word	111674
	.byte	64,3,35,128,2,13
	.byte	'reserved_140',0
	.word	111665
	.byte	64,3,35,192,2,13
	.byte	'CH3',0
	.word	111679
	.byte	64,3,35,128,3,13
	.byte	'reserved_1C0',0
	.word	111665
	.byte	64,3,35,192,3,13
	.byte	'CH4',0
	.word	111684
	.byte	64,3,35,128,4,13
	.byte	'reserved_240',0
	.word	111665
	.byte	64,3,35,192,4,13
	.byte	'CH5',0
	.word	111689
	.byte	64,3,35,128,5,13
	.byte	'reserved_2C0',0
	.word	111665
	.byte	64,3,35,192,5,13
	.byte	'CH6',0
	.word	111694
	.byte	64,3,35,128,6,13
	.byte	'reserved_340',0
	.word	111665
	.byte	64,3,35,192,6,13
	.byte	'CH7',0
	.word	111699
	.byte	64,3,35,128,7,13
	.byte	'reserved_3C0',0
	.word	111704
	.byte	192,8,3,35,192,7,0,16
	.word	111715
	.byte	21
	.byte	'Ifx_GTM_ATOM',0,8,184,57,3
	.word	112022
	.byte	10
	.byte	'_Ifx_GTM_BRC',0,8,187,57,25,120,13
	.byte	'SRC0_ADDR',0
	.word	87294
	.byte	4,2,35,0,13
	.byte	'SRC0_DEST',0
	.word	87365
	.byte	4,2,35,4,13
	.byte	'SRC1_ADDR',0
	.word	87724
	.byte	4,2,35,8,13
	.byte	'SRC1_DEST',0
	.word	87795
	.byte	4,2,35,12,13
	.byte	'SRC2_ADDR',0
	.word	87866
	.byte	4,2,35,16,13
	.byte	'SRC2_DEST',0
	.word	87937
	.byte	4,2,35,20,13
	.byte	'SRC3_ADDR',0
	.word	88008
	.byte	4,2,35,24,13
	.byte	'SRC3_DEST',0
	.word	88079
	.byte	4,2,35,28,13
	.byte	'SRC4_ADDR',0
	.word	88150
	.byte	4,2,35,32,13
	.byte	'SRC4_DEST',0
	.word	88221
	.byte	4,2,35,36,13
	.byte	'SRC5_ADDR',0
	.word	88292
	.byte	4,2,35,40,13
	.byte	'SRC5_DEST',0
	.word	88363
	.byte	4,2,35,44,13
	.byte	'SRC6_ADDR',0
	.word	88434
	.byte	4,2,35,48,13
	.byte	'SRC6_DEST',0
	.word	88505
	.byte	4,2,35,52,13
	.byte	'SRC7_ADDR',0
	.word	88576
	.byte	4,2,35,56,13
	.byte	'SRC7_DEST',0
	.word	88647
	.byte	4,2,35,60,13
	.byte	'SRC8_ADDR',0
	.word	88718
	.byte	4,2,35,64,13
	.byte	'SRC8_DEST',0
	.word	88789
	.byte	4,2,35,68,13
	.byte	'SRC9_ADDR',0
	.word	88860
	.byte	4,2,35,72,13
	.byte	'SRC9_DEST',0
	.word	88931
	.byte	4,2,35,76,13
	.byte	'SRC10_ADDR',0
	.word	87436
	.byte	4,2,35,80,13
	.byte	'SRC10_DEST',0
	.word	87508
	.byte	4,2,35,84,13
	.byte	'SRC11_ADDR',0
	.word	87580
	.byte	4,2,35,88,13
	.byte	'SRC11_DEST',0
	.word	87652
	.byte	4,2,35,92,13
	.byte	'IRQ_NOTIFY',0
	.word	87157
	.byte	4,2,35,96,13
	.byte	'IRQ_EN',0
	.word	86946
	.byte	4,2,35,100,13
	.byte	'IRQ_FORCINT',0
	.word	87014
	.byte	4,2,35,104,13
	.byte	'IRQ_MODE',0
	.word	87087
	.byte	4,2,35,108,13
	.byte	'RST',0
	.word	87229
	.byte	4,2,35,112,13
	.byte	'EIRQ_EN',0
	.word	86877
	.byte	4,2,35,116,0,16
	.word	112049
	.byte	21
	.byte	'Ifx_GTM_BRC',0,8,219,57,3
	.word	112634
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE',0,8,222,57,25,12,13
	.byte	'MODE',0
	.word	89002
	.byte	4,2,35,0,13
	.byte	'PTR1',0
	.word	89071
	.byte	4,2,35,4,13
	.byte	'PTR2',0
	.word	89140
	.byte	4,2,35,8,0,16
	.word	112660
	.byte	21
	.byte	'Ifx_GTM_BRIDGE',0,8,227,57,3
	.word	112725
	.byte	10
	.byte	'_Ifx_GTM_CMP',0,8,230,57,25,24,13
	.byte	'EN',0
	.word	89339
	.byte	4,2,35,0,13
	.byte	'IRQ_NOTIFY',0
	.word	89614
	.byte	4,2,35,4,13
	.byte	'IRQ_EN',0
	.word	89403
	.byte	4,2,35,8,13
	.byte	'IRQ_FORCINT',0
	.word	89471
	.byte	4,2,35,12,13
	.byte	'IRQ_MODE',0
	.word	89544
	.byte	4,2,35,16,13
	.byte	'EIRQ_EN',0
	.word	89270
	.byte	4,2,35,20,0,16
	.word	112754
	.byte	21
	.byte	'Ifx_GTM_CMP',0,8,238,57,3
	.word	112878
	.byte	14,24
	.word	108631
	.byte	15,5,0,16
	.word	112904
	.byte	16
	.word	108705
	.byte	16
	.word	108777
	.byte	14,24
	.word	108849
	.byte	15,2,0,16
	.word	112928
	.byte	16
	.word	108931
	.byte	10
	.byte	'_Ifx_GTM_CMU',0,8,241,57,25,72,13
	.byte	'CLK_EN',0
	.word	89903
	.byte	4,2,35,0,13
	.byte	'GCLK_NUM',0
	.word	90253
	.byte	4,2,35,4,13
	.byte	'GCLK_DEN',0
	.word	90183
	.byte	4,2,35,8,13
	.byte	'CLK0_5',0
	.word	112913
	.byte	24,2,35,12,13
	.byte	'CLK_6',0
	.word	112918
	.byte	4,2,35,36,13
	.byte	'CLK_7',0
	.word	112923
	.byte	4,2,35,40,13
	.byte	'ECLK',0
	.word	112937
	.byte	24,2,35,44,13
	.byte	'FXCLK',0
	.word	112942
	.byte	4,2,35,68,0,16
	.word	112947
	.byte	21
	.byte	'Ifx_GTM_CMU',0,8,251,57,3
	.word	113094
	.byte	14,92
	.word	487
	.byte	15,91,0,14,44
	.word	487
	.byte	15,43,0,14,96
	.word	93215
	.byte	15,23,0,14,128,1
	.word	487
	.byte	15,127,0,14,96
	.word	94959
	.byte	15,23,0,14,96
	.word	92532
	.byte	15,23,0,14,96
	.word	94124
	.byte	15,23,0,14,96
	.word	92807
	.byte	15,23,0,14,88
	.word	487
	.byte	15,87,0,14,96
	.word	94891
	.byte	15,23,0,14,96
	.word	487
	.byte	15,95,0,14,128,2
	.word	95926
	.byte	15,63,0,14,128,2
	.word	97162
	.byte	15,63,0,14,128,2
	.word	90879
	.byte	15,63,0,14,128,2
	.word	92598
	.byte	15,63,0,14,128,8
	.word	487
	.byte	15,255,7,0,14,96
	.word	97095
	.byte	15,23,0,14,96
	.word	95025
	.byte	15,23,0,14,24
	.word	90449
	.byte	15,5,0,10
	.byte	'_Ifx_GTM_DPLL',0,8,254,57,25,152,30,13
	.byte	'CTRL_0',0
	.word	91937
	.byte	4,2,35,0,13
	.byte	'CTRL_1',0
	.word	92172
	.byte	4,2,35,4,13
	.byte	'CTRL_2',0
	.word	92325
	.byte	4,2,35,8,13
	.byte	'CTRL_3',0
	.word	92394
	.byte	4,2,35,12,13
	.byte	'CTRL_4',0
	.word	92463
	.byte	4,2,35,16,13
	.byte	'reserved_14',0
	.word	1532
	.byte	4,2,35,20,13
	.byte	'ACT_STA',0
	.word	90515
	.byte	4,2,35,24,13
	.byte	'OSW',0
	.word	94825
	.byte	4,2,35,28,13
	.byte	'AOSV_2',0
	.word	90947
	.byte	4,2,35,32,13
	.byte	'APT',0
	.word	91223
	.byte	4,2,35,36,13
	.byte	'APS',0
	.word	91016
	.byte	4,2,35,40,13
	.byte	'APT_2C',0
	.word	91289
	.byte	4,2,35,44,13
	.byte	'APS_1C3',0
	.word	91082
	.byte	4,2,35,48,13
	.byte	'NUTC',0
	.word	94758
	.byte	4,2,35,52,13
	.byte	'NUSC',0
	.word	94691
	.byte	4,2,35,56,13
	.byte	'NTI_CNT',0
	.word	94621
	.byte	4,2,35,60,13
	.byte	'IRQ_NOTIFY',0
	.word	93641
	.byte	4,2,35,64,13
	.byte	'IRQ_EN',0
	.word	93427
	.byte	4,2,35,68,13
	.byte	'IRQ_FORCINT',0
	.word	93496
	.byte	4,2,35,72,13
	.byte	'IRQ_MODE',0
	.word	93570
	.byte	4,2,35,76,13
	.byte	'EIRQ_EN',0
	.word	93009
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	113120
	.byte	92,2,35,84,13
	.byte	'INC_CNT1',0
	.word	93285
	.byte	4,3,35,176,1,13
	.byte	'INC_CNT2',0
	.word	93356
	.byte	4,3,35,180,1,13
	.byte	'APT_SYNC',0
	.word	91358
	.byte	4,3,35,184,1,13
	.byte	'APS_SYNC',0
	.word	91152
	.byte	4,3,35,188,1,13
	.byte	'TBU_TS0_T',0
	.word	96345
	.byte	4,3,35,192,1,13
	.byte	'TBU_TS0_S',0
	.word	96273
	.byte	4,3,35,196,1,13
	.byte	'ADD_IN_LD1',0
	.word	90733
	.byte	4,3,35,200,1,13
	.byte	'ADD_IN_LD2',0
	.word	90806
	.byte	4,3,35,204,1,13
	.byte	'reserved_D0',0
	.word	113129
	.byte	44,3,35,208,1,13
	.byte	'STATUS',0
	.word	96204
	.byte	4,3,35,252,1,13
	.byte	'ID_PMTR',0
	.word	113138
	.byte	96,3,35,128,2,13
	.byte	'reserved_160',0
	.word	113147
	.byte	128,1,3,35,224,2,13
	.byte	'CTRL_0_SHADOW_TRIGGER',0
	.word	92088
	.byte	4,3,35,224,3,13
	.byte	'CTRL_0_SHADOW_STATE',0
	.word	92006
	.byte	4,3,35,228,3,13
	.byte	'CTRL_1_SHADOW_TRIGGER',0
	.word	92241
	.byte	4,3,35,232,3,13
	.byte	'CRTL_1_SHADOW_STATE',0
	.word	91855
	.byte	4,3,35,236,3,13
	.byte	'reserved_1F0',0
	.word	3691
	.byte	12,3,35,240,3,13
	.byte	'RAM_INI',0
	.word	95568
	.byte	4,3,35,252,3,13
	.byte	'PSA',0
	.word	113157
	.byte	96,3,35,128,4,13
	.byte	'reserved_260',0
	.word	108054
	.byte	32,3,35,224,4,13
	.byte	'DLA',0
	.word	113166
	.byte	96,3,35,128,5,13
	.byte	'reserved_2E0',0
	.word	108054
	.byte	32,3,35,224,5,13
	.byte	'NA',0
	.word	113175
	.byte	96,3,35,128,6,13
	.byte	'reserved_360',0
	.word	108054
	.byte	32,3,35,224,6,13
	.byte	'DTA',0
	.word	113184
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	108054
	.byte	32,3,35,224,7,13
	.byte	'TS_T_0',0
	.word	96957
	.byte	4,3,35,128,8,13
	.byte	'TS_T_1',0
	.word	97026
	.byte	4,3,35,132,8,13
	.byte	'FTV_T',0
	.word	93147
	.byte	4,3,35,136,8,13
	.byte	'reserved_40C',0
	.word	1532
	.byte	4,3,35,140,8,13
	.byte	'TS_S_0',0
	.word	96819
	.byte	4,3,35,144,8,13
	.byte	'TS_S_1',0
	.word	96888
	.byte	4,3,35,148,8,13
	.byte	'FTV_S',0
	.word	93079
	.byte	4,3,35,152,8,13
	.byte	'reserved_41C',0
	.word	1532
	.byte	4,3,35,156,8,13
	.byte	'THMI',0
	.word	96484
	.byte	4,3,35,160,8,13
	.byte	'THMA',0
	.word	96417
	.byte	4,3,35,164,8,13
	.byte	'THVAL',0
	.word	96551
	.byte	4,3,35,168,8,13
	.byte	'reserved_42C',0
	.word	1532
	.byte	4,3,35,172,8,13
	.byte	'TOV',0
	.word	96685
	.byte	4,3,35,176,8,13
	.byte	'TOV_S',0
	.word	96751
	.byte	4,3,35,180,8,13
	.byte	'ADD_IN_CAL1',0
	.word	90585
	.byte	4,3,35,184,8,13
	.byte	'ADD_IN_CAL2',0
	.word	90659
	.byte	4,3,35,188,8,13
	.byte	'MPVAL1',0
	.word	93986
	.byte	4,3,35,192,8,13
	.byte	'MPVAL2',0
	.word	94055
	.byte	4,3,35,196,8,13
	.byte	'NMB_T_TAR',0
	.word	94473
	.byte	4,3,35,200,8,13
	.byte	'NMB_T_TAR_OLD',0
	.word	94545
	.byte	4,3,35,204,8,13
	.byte	'NMB_S_TAR',0
	.word	94257
	.byte	4,3,35,208,8,13
	.byte	'NMB_S_TAR_OLD',0
	.word	94329
	.byte	4,3,35,212,8,13
	.byte	'reserved_458',0
	.word	3351
	.byte	8,3,35,216,8,13
	.byte	'RCDT_TX',0
	.word	95782
	.byte	4,3,35,224,8,13
	.byte	'RCDT_SX',0
	.word	95638
	.byte	4,3,35,228,8,13
	.byte	'RCDT_TX_NOM',0
	.word	95852
	.byte	4,3,35,232,8,13
	.byte	'RCDT_SX_NOM',0
	.word	95708
	.byte	4,3,35,236,8,13
	.byte	'RDT_T_ACT',0
	.word	96066
	.byte	4,3,35,240,8,13
	.byte	'RDT_S_ACT',0
	.word	95994
	.byte	4,3,35,244,8,13
	.byte	'DT_T_ACT',0
	.word	92736
	.byte	4,3,35,248,8,13
	.byte	'DT_S_ACT',0
	.word	92665
	.byte	4,3,35,252,8,13
	.byte	'EDT_T',0
	.word	92941
	.byte	4,3,35,128,9,13
	.byte	'MEDT_T',0
	.word	93783
	.byte	4,3,35,132,9,13
	.byte	'EDT_S',0
	.word	92873
	.byte	4,3,35,136,9,13
	.byte	'MEDT_S',0
	.word	93714
	.byte	4,3,35,140,9,13
	.byte	'CDT_TX',0
	.word	91571
	.byte	4,3,35,144,9,13
	.byte	'CDT_SX',0
	.word	91429
	.byte	4,3,35,148,9,13
	.byte	'CDT_TX_NOM',0
	.word	91640
	.byte	4,3,35,152,9,13
	.byte	'CDT_SX_NOM',0
	.word	91498
	.byte	4,3,35,156,9,13
	.byte	'TLR',0
	.word	96619
	.byte	4,3,35,160,9,13
	.byte	'SLR',0
	.word	96138
	.byte	4,3,35,164,9,13
	.byte	'reserved_4A8',0
	.word	113193
	.byte	88,3,35,168,9,13
	.byte	'PDT_T',0
	.word	113202
	.byte	96,3,35,128,10,13
	.byte	'reserved_560',0
	.word	113211
	.byte	96,3,35,224,10,13
	.byte	'MLS1',0
	.word	93852
	.byte	4,3,35,192,11,13
	.byte	'MLS2',0
	.word	93919
	.byte	4,3,35,196,11,13
	.byte	'CNT_NUM1',0
	.word	91713
	.byte	4,3,35,200,11,13
	.byte	'CNT_NUM2',0
	.word	91784
	.byte	4,3,35,204,11,13
	.byte	'PVT',0
	.word	95502
	.byte	4,3,35,208,11,13
	.byte	'reserved_5D4',0
	.word	3691
	.byte	12,3,35,212,11,13
	.byte	'PSTC',0
	.word	95297
	.byte	4,3,35,224,11,13
	.byte	'PSSC',0
	.word	95092
	.byte	4,3,35,228,11,13
	.byte	'PSTM_0',0
	.word	95364
	.byte	4,3,35,232,11,13
	.byte	'PSTM_1',0
	.word	95433
	.byte	4,3,35,236,11,13
	.byte	'PSSM_0',0
	.word	95159
	.byte	4,3,35,240,11,13
	.byte	'PSSM_1',0
	.word	95228
	.byte	4,3,35,244,11,13
	.byte	'NMB_T',0
	.word	94405
	.byte	4,3,35,248,11,13
	.byte	'NMB_S',0
	.word	94189
	.byte	4,3,35,252,11,13
	.byte	'RDT_S',0
	.word	113220
	.byte	128,2,3,35,128,12,13
	.byte	'TSF_S',0
	.word	113230
	.byte	128,2,3,35,128,14,13
	.byte	'ADT_S',0
	.word	113240
	.byte	128,2,3,35,128,16,13
	.byte	'DT_S',0
	.word	113250
	.byte	128,2,3,35,128,18,13
	.byte	'reserved_A00',0
	.word	113260
	.byte	128,8,3,35,128,20,13
	.byte	'TSAC',0
	.word	113271
	.byte	96,3,35,128,28,13
	.byte	'reserved_E60',0
	.word	108054
	.byte	32,3,35,224,28,13
	.byte	'PSAC',0
	.word	113280
	.byte	96,3,35,128,29,13
	.byte	'reserved_EE0',0
	.word	108054
	.byte	32,3,35,224,29,13
	.byte	'ACB',0
	.word	113289
	.byte	24,3,35,128,30,0,16
	.word	113298
	.byte	21
	.byte	'Ifx_GTM_DPLL',0,8,244,58,3
	.word	115475
	.byte	14,32
	.word	109003
	.byte	15,7,0,16
	.word	115502
	.byte	14,32
	.word	109082
	.byte	15,7,0,16
	.word	115516
	.byte	10
	.byte	'_Ifx_GTM_F2A',0,8,247,58,25,68,13
	.byte	'RD_CH',0
	.word	115511
	.byte	32,2,35,0,13
	.byte	'STR_CH',0
	.word	115525
	.byte	32,2,35,32,13
	.byte	'ENABLE',0
	.word	97426
	.byte	4,2,35,64,0,16
	.word	115530
	.byte	21
	.byte	'Ifx_GTM_F2A',0,8,252,58,3
	.word	115597
	.byte	14,128,4
	.word	109159
	.byte	15,7,0,16
	.word	115623
	.byte	10
	.byte	'_Ifx_GTM_FIFO',0,8,255,58,25,128,4,13
	.byte	'CH',0
	.word	115633
	.byte	128,4,2,35,0,0,16
	.word	115638
	.byte	21
	.byte	'Ifx_GTM_FIFO',0,8,130,59,3
	.word	115673
	.byte	10
	.byte	'_Ifx_GTM_ICM',0,8,133,59,25,68,13
	.byte	'IRQG_0',0
	.word	98681
	.byte	4,2,35,0,13
	.byte	'IRQG_1',0
	.word	98749
	.byte	4,2,35,4,13
	.byte	'IRQG_2',0
	.word	98817
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1532
	.byte	4,2,35,12,13
	.byte	'IRQG_4',0
	.word	98885
	.byte	4,2,35,16,13
	.byte	'reserved_14',0
	.word	1532
	.byte	4,2,35,20,13
	.byte	'IRQG_6',0
	.word	98953
	.byte	4,2,35,24,13
	.byte	'reserved_1C',0
	.word	3351
	.byte	8,2,35,28,13
	.byte	'IRQG_9',0
	.word	99021
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3351
	.byte	8,2,35,40,13
	.byte	'IRQG_MEI',0
	.word	99302
	.byte	4,2,35,48,13
	.byte	'IRQG_CEI0',0
	.word	99089
	.byte	4,2,35,52,13
	.byte	'IRQG_CEI1',0
	.word	99160
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	1532
	.byte	4,2,35,60,13
	.byte	'IRQG_CEI3',0
	.word	99231
	.byte	4,2,35,64,0,16
	.word	115700
	.byte	21
	.byte	'Ifx_GTM_ICM',0,8,150,59,3
	.word	115995
	.byte	14,12
	.word	109963
	.byte	15,2,0,16
	.word	116021
	.byte	14,20
	.word	487
	.byte	15,19,0,16
	.word	109887
	.byte	14,36
	.word	487
	.byte	15,35,0,16
	.word	109571
	.byte	16
	.word	109482
	.byte	16
	.word	109711
	.byte	16
	.word	109794
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL',0,8,153,59,25,156,1,13
	.byte	'TIM',0
	.word	116030
	.byte	12,2,35,0,13
	.byte	'reserved_C',0
	.word	116035
	.byte	20,2,35,12,13
	.byte	'T',0
	.word	116044
	.byte	40,2,35,32,13
	.byte	'reserved_48',0
	.word	116049
	.byte	36,2,35,72,13
	.byte	'DSADC',0
	.word	116058
	.byte	24,2,35,108,13
	.byte	'reserved_84',0
	.word	3691
	.byte	12,3,35,132,1,13
	.byte	'CAN',0
	.word	116063
	.byte	4,3,35,144,1,13
	.byte	'PSI5',0
	.word	116068
	.byte	4,3,35,148,1,13
	.byte	'PSI5S',0
	.word	116073
	.byte	4,3,35,152,1,0,16
	.word	116078
	.byte	21
	.byte	'Ifx_GTM_INOUTSEL',0,8,164,59,3
	.word	116251
	.byte	16
	.word	110373
	.byte	14,28
	.word	487
	.byte	15,27,0,16
	.word	110051
	.byte	16
	.word	110051
	.byte	16
	.word	110051
	.byte	16
	.word	110051
	.byte	16
	.word	110051
	.byte	16
	.word	110051
	.byte	16
	.word	110051
	.byte	14,128,24
	.word	487
	.byte	15,255,23,0,10
	.byte	'_Ifx_GTM_MCS',0,8,167,59,25,128,32,13
	.byte	'CH0',0
	.word	116282
	.byte	88,2,35,0,13
	.byte	'reserved_58',0
	.word	116287
	.byte	28,2,35,88,13
	.byte	'CTRL',0
	.word	101753
	.byte	4,2,35,116,13
	.byte	'RST',0
	.word	101884
	.byte	4,2,35,120,13
	.byte	'ERR',0
	.word	101819
	.byte	4,2,35,124,13
	.byte	'CH1',0
	.word	116296
	.byte	128,1,3,35,128,1,13
	.byte	'CH2',0
	.word	116301
	.byte	128,1,3,35,128,2,13
	.byte	'CH3',0
	.word	116306
	.byte	128,1,3,35,128,3,13
	.byte	'CH4',0
	.word	116311
	.byte	128,1,3,35,128,4,13
	.byte	'CH5',0
	.word	116316
	.byte	128,1,3,35,128,5,13
	.byte	'CH6',0
	.word	116321
	.byte	128,1,3,35,128,6,13
	.byte	'CH7',0
	.word	116326
	.byte	128,1,3,35,128,7,13
	.byte	'reserved_400',0
	.word	116331
	.byte	128,24,3,35,128,8,0,16
	.word	116342
	.byte	21
	.byte	'Ifx_GTM_MCS',0,8,182,59,3
	.word	116566
	.byte	10
	.byte	'_Ifx_GTM_MON',0,8,185,59,25,8,13
	.byte	'STATUS',0
	.word	102156
	.byte	4,2,35,0,13
	.byte	'ACTIVITY_0',0
	.word	102084
	.byte	4,2,35,4,0,16
	.word	116592
	.byte	21
	.byte	'Ifx_GTM_MON',0,8,189,59,3
	.word	116648
	.byte	10
	.byte	'_Ifx_GTM_MSCIN',0,8,192,59,25,8,13
	.byte	'INLCON',0
	.word	102365
	.byte	4,2,35,0,13
	.byte	'INHCON',0
	.word	102295
	.byte	4,2,35,4,0,16
	.word	116674
	.byte	21
	.byte	'Ifx_GTM_MSCIN',0,8,196,59,3
	.word	116728
	.byte	10
	.byte	'_Ifx_GTM_MSCSET',0,8,199,59,25,16,13
	.byte	'CON0',0
	.word	102435
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	102504
	.byte	4,2,35,4,13
	.byte	'CON2',0
	.word	102573
	.byte	4,2,35,8,13
	.byte	'CON3',0
	.word	102642
	.byte	4,2,35,12,0,16
	.word	116756
	.byte	21
	.byte	'Ifx_GTM_MSCSET',0,8,205,59,3
	.word	116835
	.byte	14,32
	.word	103958
	.byte	15,7,0,10
	.byte	'_Ifx_GTM_SPE',0,8,208,59,25,128,1,13
	.byte	'CTRL_STAT',0
	.word	103465
	.byte	4,2,35,0,13
	.byte	'PAT',0
	.word	104027
	.byte	4,2,35,4,13
	.byte	'OUT_PAT',0
	.word	116864
	.byte	32,2,35,8,13
	.byte	'OUT_CTRL',0
	.word	103888
	.byte	4,2,35,40,13
	.byte	'IRQ_NOTIFY',0
	.word	103816
	.byte	4,2,35,44,13
	.byte	'IRQ_EN',0
	.word	103605
	.byte	4,2,35,48,13
	.byte	'IRQ_FORCINT',0
	.word	103673
	.byte	4,2,35,52,13
	.byte	'IRQ_MODE',0
	.word	103746
	.byte	4,2,35,56,13
	.byte	'EIRQ_EN',0
	.word	103536
	.byte	4,2,35,60,13
	.byte	'CNT',0
	.word	103400
	.byte	4,2,35,64,13
	.byte	'CMP',0
	.word	103335
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	110703
	.byte	56,2,35,72,0,16
	.word	116873
	.byte	21
	.byte	'Ifx_GTM_SPE',0,8,222,59,3
	.word	117100
	.byte	10
	.byte	'_Ifx_GTM_TBU',0,8,225,59,25,28,13
	.byte	'CHEN',0
	.word	104512
	.byte	4,2,35,0,13
	.byte	'CH0_CTRL',0
	.word	104162
	.byte	4,2,35,4,13
	.byte	'CH0_BASE',0
	.word	104092
	.byte	4,2,35,8,13
	.byte	'CH1_CTRL',0
	.word	104302
	.byte	4,2,35,12,13
	.byte	'CH1_BASE',0
	.word	104232
	.byte	4,2,35,16,13
	.byte	'CH2_CTRL',0
	.word	104442
	.byte	4,2,35,20,13
	.byte	'CH2_BASE',0
	.word	104372
	.byte	4,2,35,24,0,16
	.word	117126
	.byte	21
	.byte	'Ifx_GTM_TBU',0,8,234,59,3
	.word	117268
	.byte	16
	.word	110712
	.byte	16
	.word	110712
	.byte	16
	.word	110712
	.byte	16
	.word	110712
	.byte	16
	.word	110712
	.byte	16
	.word	110712
	.byte	16
	.word	110712
	.byte	16
	.word	110712
	.byte	14,136,8
	.word	487
	.byte	15,135,8,0,10
	.byte	'_Ifx_GTM_TIM',0,8,237,59,25,128,16,13
	.byte	'CH0',0
	.word	117294
	.byte	120,2,35,0,13
	.byte	'IN_SRC',0
	.word	105780
	.byte	4,2,35,120,13
	.byte	'RST',0
	.word	105848
	.byte	4,2,35,124,13
	.byte	'CH1',0
	.word	117299
	.byte	120,3,35,128,1,13
	.byte	'reserved_F8',0
	.word	3351
	.byte	8,3,35,248,1,13
	.byte	'CH2',0
	.word	117304
	.byte	120,3,35,128,2,13
	.byte	'reserved_178',0
	.word	3351
	.byte	8,3,35,248,2,13
	.byte	'CH3',0
	.word	117309
	.byte	120,3,35,128,3,13
	.byte	'reserved_1F8',0
	.word	3351
	.byte	8,3,35,248,3,13
	.byte	'CH4',0
	.word	117314
	.byte	120,3,35,128,4,13
	.byte	'reserved_278',0
	.word	3351
	.byte	8,3,35,248,4,13
	.byte	'CH5',0
	.word	117319
	.byte	120,3,35,128,5,13
	.byte	'reserved_2F8',0
	.word	3351
	.byte	8,3,35,248,5,13
	.byte	'CH6',0
	.word	117324
	.byte	120,3,35,128,6,13
	.byte	'reserved_378',0
	.word	3351
	.byte	8,3,35,248,6,13
	.byte	'CH7',0
	.word	117329
	.byte	120,3,35,128,7,13
	.byte	'reserved_3F8',0
	.word	117334
	.byte	136,8,3,35,248,7,0,16
	.word	117345
	.byte	21
	.byte	'Ifx_GTM_TIM',0,8,128,60,3
	.word	117667
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	16
	.word	111035
	.byte	14,144,8
	.word	487
	.byte	15,143,8,0,10
	.byte	'_Ifx_GTM_TOM',0,8,131,60,25,128,16,13
	.byte	'CH0',0
	.word	117693
	.byte	48,2,35,0,13
	.byte	'TGC0_GLB_CTRL',0
	.word	106989
	.byte	4,2,35,48,13
	.byte	'TGC0_ACT_TB',0
	.word	106686
	.byte	4,2,35,52,13
	.byte	'TGC0_FUPD_CTRL',0
	.word	106913
	.byte	4,2,35,56,13
	.byte	'TGC0_INT_TRIG',0
	.word	107064
	.byte	4,2,35,60,13
	.byte	'CH1',0
	.word	117698
	.byte	48,2,35,64,13
	.byte	'TGC0_ENDIS_CTRL',0
	.word	106759
	.byte	4,2,35,112,13
	.byte	'TGC0_ENDIS_STAT',0
	.word	106836
	.byte	4,2,35,116,13
	.byte	'TGC0_OUTEN_CTRL',0
	.word	107139
	.byte	4,2,35,120,13
	.byte	'TGC0_OUTEN_STAT',0
	.word	107216
	.byte	4,2,35,124,13
	.byte	'CH2',0
	.word	117703
	.byte	48,3,35,128,1,13
	.byte	'reserved_B0',0
	.word	108368
	.byte	16,3,35,176,1,13
	.byte	'CH3',0
	.word	117708
	.byte	48,3,35,192,1,13
	.byte	'reserved_F0',0
	.word	108368
	.byte	16,3,35,240,1,13
	.byte	'CH4',0
	.word	117713
	.byte	48,3,35,128,2,13
	.byte	'reserved_130',0
	.word	108368
	.byte	16,3,35,176,2,13
	.byte	'CH5',0
	.word	117718
	.byte	48,3,35,192,2,13
	.byte	'reserved_170',0
	.word	108368
	.byte	16,3,35,240,2,13
	.byte	'CH6',0
	.word	117723
	.byte	48,3,35,128,3,13
	.byte	'reserved_1B0',0
	.word	108368
	.byte	16,3,35,176,3,13
	.byte	'CH7',0
	.word	117728
	.byte	48,3,35,192,3,13
	.byte	'reserved_1F0',0
	.word	108368
	.byte	16,3,35,240,3,13
	.byte	'CH8',0
	.word	117733
	.byte	48,3,35,128,4,13
	.byte	'TGC1_GLB_CTRL',0
	.word	107596
	.byte	4,3,35,176,4,13
	.byte	'TGC1_ACT_TB',0
	.word	107293
	.byte	4,3,35,180,4,13
	.byte	'TGC1_FUPD_CTRL',0
	.word	107520
	.byte	4,3,35,184,4,13
	.byte	'TGC1_INT_TRIG',0
	.word	107671
	.byte	4,3,35,188,4,13
	.byte	'CH9',0
	.word	117738
	.byte	48,3,35,192,4,13
	.byte	'TGC1_ENDIS_CTRL',0
	.word	107366
	.byte	4,3,35,240,4,13
	.byte	'TGC1_ENDIS_STAT',0
	.word	107443
	.byte	4,3,35,244,4,13
	.byte	'TGC1_OUTEN_CTRL',0
	.word	107746
	.byte	4,3,35,248,4,13
	.byte	'TGC1_OUTEN_STAT',0
	.word	107823
	.byte	4,3,35,252,4,13
	.byte	'CH10',0
	.word	117743
	.byte	48,3,35,128,5,13
	.byte	'reserved_2B0',0
	.word	108368
	.byte	16,3,35,176,5,13
	.byte	'CH11',0
	.word	117748
	.byte	48,3,35,192,5,13
	.byte	'reserved_2F0',0
	.word	108368
	.byte	16,3,35,240,5,13
	.byte	'CH12',0
	.word	117753
	.byte	48,3,35,128,6,13
	.byte	'reserved_330',0
	.word	108368
	.byte	16,3,35,176,6,13
	.byte	'CH13',0
	.word	117758
	.byte	48,3,35,192,6,13
	.byte	'reserved_370',0
	.word	108368
	.byte	16,3,35,240,6,13
	.byte	'CH14',0
	.word	117763
	.byte	48,3,35,128,7,13
	.byte	'reserved_3B0',0
	.word	108368
	.byte	16,3,35,176,7,13
	.byte	'CH15',0
	.word	117768
	.byte	48,3,35,192,7,13
	.byte	'reserved_3F0',0
	.word	117773
	.byte	144,8,3,35,240,7,0,16
	.word	117784
	.byte	21
	.byte	'Ifx_GTM_TOM',0,8,177,60,3
	.word	118698
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7104
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7017
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3360
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1413
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2408
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1541
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2188
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1756
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1971
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6376
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6500
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6584
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6764
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5015
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5539
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5189
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5363
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6028
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	842
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4352
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4840
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4499
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4668
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5695
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	526
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4066
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3700
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2731
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3035
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7631
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7064
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3651
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1492
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2682
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1716
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2368
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1931
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2148
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6460
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6709
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6968
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6336
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5149
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5655
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5323
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5499
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1373
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5988
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4459
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4975
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4628
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4800
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	802
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4312
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4026
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2995
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3311
	.byte	16
	.word	7671
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	120042
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	120062
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	120184
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	464
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	120741
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	464
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	120818
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	487
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	487
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	487
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	487
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	487
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	487
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	487
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	487
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	487
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	487
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	120954
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	487
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	487
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	487
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	487
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	487
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	487
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	487
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	487
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	121234
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	487
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	464
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	121472
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	487
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	487
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	487
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	487
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	487
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	121600
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	487
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	487
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	487
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	487
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	487
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	121843
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	487
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	464
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	122078
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	487
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	464
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	122206
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	487
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	464
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	122306
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	487
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	487
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	487
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	487
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	487
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	122406
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	464
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	122614
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	504
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	487
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	504
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	487
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	122779
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	504
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	487
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	122962
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	487
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	487
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	464
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	487
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	487
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	123116
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	464
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	123480
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	504
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	487
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	487
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	487
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	487
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	123691
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	504
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	464
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	123943
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	124061
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	464
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	124172
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	464
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	124335
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	124498
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	124656
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	487
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	487
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	487
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	487
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	487
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	487
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	487
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	487
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	487
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	504
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	124821
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	504
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	487
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	487
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	504
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	487
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	125150
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	125371
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	464
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	125534
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	125806
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	125959
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	126115
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	126277
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	126420
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	126585
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	504
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	487
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	487
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	126730
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	487
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	126911
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	127085
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	464
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	127245
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	464
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	127389
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	127663
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	127802
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	487
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	504
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	487
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	487
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	487
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	127965
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	504
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	487
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	504
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	487
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	128183
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	128346
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	128682
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	487
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	128789
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	129241
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	487
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	487
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	129340
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	504
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	129490
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	464
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	487
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	129639
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	464
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	129800
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	504
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	504
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	129930
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	130062
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	487
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	504
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	130177
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	504
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	504
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	130288
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	487
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	487
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	487
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	487
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	487
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	487
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	130446
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	130858
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	504
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	487
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	487
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	130959
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	464
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	131226
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	131362
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	487
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	487
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	464
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	131473
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	464
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	131606
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	504
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	487
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	487
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	487
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	487
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	131809
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	487
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	487
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	487
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	504
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	132165
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	504
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	132343
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	504
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	487
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	487
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	487
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	487
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	132443
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	487
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	487
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	487
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	487
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	504
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	132813
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	464
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	132999
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	464
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	133197
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	487
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	487
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	464
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	133430
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	487
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	487
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	487
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	487
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	487
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	487
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	487
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	133582
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	487
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	487
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	487
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	487
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	134149
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	487
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	487
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	487
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	487
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	487
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	487
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	487
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	134443
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	487
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	487
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	487
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	487
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	487
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	504
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	487
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	487
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	134721
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	504
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	135217
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	504
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	487
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	487
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	487
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	135530
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	487
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	487
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	487
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	487
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	487
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	487
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	487
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	487
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	135739
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	487
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	487
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	487
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	487
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	487
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	487
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	487
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	135950
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	464
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	136382
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	487
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	487
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	487
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	487
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	487
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	487
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	487
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	487
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	487
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	487
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	487
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	136478
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	464
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	136738
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	487
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	487
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	464
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	136863
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	464
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	137060
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	464
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	137213
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	464
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	137366
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	464
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	137519
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8857
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	137674
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	487
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	487
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	137804
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	487
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	138042
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	8857
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8857
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8857
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8857
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	138265
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	487
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	138391
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	487
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	487
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	487
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	487
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	487
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	487
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	487
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	487
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	487
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	487
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	504
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	138643
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120184
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	138862
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120741
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	138926
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120818
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	138990
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	120954
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	139055
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121234
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	139120
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121472
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	139185
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121600
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	139250
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	121843
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	139315
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122078
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	139380
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122206
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	139445
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122306
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	139510
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122406
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	139575
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122614
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	139639
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122779
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	139703
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	122962
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	139767
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123116
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	139832
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123480
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	139894
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123691
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	139956
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	123943
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	140018
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124061
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	140082
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124172
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	140147
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124335
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	140213
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124498
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	140279
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124656
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	140347
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	124821
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	140414
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125150
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	140482
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125371
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	140550
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125534
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	140616
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125806
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	140683
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	125959
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	140752
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126115
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	140821
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	140890
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126420
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	140959
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126585
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	141028
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126730
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	141097
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	126911
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	141165
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127085
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	141233
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127245
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	141301
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127389
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	141369
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127663
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	141434
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127802
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	141499
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	127965
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	141565
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128183
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	141629
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128346
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	141690
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128682
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	141751
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	128789
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	141811
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129241
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	141873
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129340
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	141933
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129490
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	141995
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	142063
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129800
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	142131
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	129930
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	142199
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130062
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	142263
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130177
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	142328
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130288
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	142391
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130446
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	142452
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130858
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	142516
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	130959
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	142577
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131226
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	142641
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131362
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	142708
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131473
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	142771
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131606
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	142832
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	131809
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	142894
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132165
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	142959
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132343
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	143024
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132443
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	143089
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132813
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	143158
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	132999
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	143227
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133197
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	143296
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133430
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	143361
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	133582
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	143424
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	134149
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	143489
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	134443
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	143554
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	134721
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	143619
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135217
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	143685
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135739
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	143754
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135530
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	143818
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	135950
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	143883
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	136382
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	143948
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	136478
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	144013
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	136738
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	144077
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	136863
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	144143
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137060
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	144207
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137213
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	144272
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137366
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	144337
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137519
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	144402
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137674
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	144468
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	137804
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	144537
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138042
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	144606
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138265
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	144673
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138391
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	144740
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	464
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	480
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	138643
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	144807
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	144468
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	144537
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	144606
	.byte	4,2,35,8,0,16
	.word	144872
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	144935
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	144673
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	144740
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	144807
	.byte	4,2,35,8,0,16
	.word	144964
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	145025
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	145052
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	145203
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	145447
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	145545
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8284
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,13,0,73,19,11,15,56,9,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxGtm_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxGtm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0,0
.L9:
.L7:
	; Module end
