	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc37376a --dep-file=IfxDma_Dma.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c'

	
$TC16X
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_configureTransactionSet',code,cluster('IfxDma_Dma_configureTransactionSet')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_configureTransactionSet'
	.align	2
	
; Function IfxDma_Dma_configureTransactionSet
.L22:
IfxDma_Dma_configureTransactionSet:	.type	func
	mov	d15,#0
.L186:
	ld.hu	d0,[a5]26
.L278:
	insert	d15,d15,d0,#0,#14
.L279:
	ld.bu	d0,[a5]28
.L280:
	insert	d15,d15,d0,#16,#3
.L281:
	ld.bu	d0,[a5]29
.L282:
	insert	d15,d15,d0,#19,#1
.L283:
	ld.bu	d0,[a5]30
.L284:
	insert	d15,d15,d0,#20,#1
.L285:
	ld.bu	d0,[a5]31
.L286:
	insert	d15,d15,d0,#21,#3
.L287:
	ld.bu	d0,[a5]33
.L288:
	insert	d15,d15,d0,#28,#1
.L289:
	ld.bu	d0,[a5]32
.L290:
	insert	d15,d15,d0,#24,#3
.L291:
	st.w	[a4]20,d15
.L180:
	mov	d1,#0
.L188:
	ld.bu	d0,[a5]36
.L292:
	insert	d1,d1,d0,#0,#3
.L293:
	ld.bu	d0,[a5]37
.L294:
	insert	d1,d1,d0,#3,#1
.L295:
	ld.bu	d0,[a5]38
.L296:
	insert	d1,d1,d0,#8,#4
.L297:
	ld.bu	d0,[a5]43
.L298:
	insert	d1,d1,d0,#20,#1
.L299:
	ld.bu	d0,[a5]39
.L300:
	insert	d1,d1,d0,#4,#3
.L301:
	ld.bu	d0,[a5]40
.L302:
	insert	d1,d1,d0,#7,#1
.L303:
	ld.bu	d0,[a5]41
.L304:
	insert	d1,d1,d0,#12,#4
.L305:
	ld.bu	d0,[a5]44
.L306:
	insert	d1,d1,d0,#21,#1
.L307:
	ld.bu	d0,[a5]42
.L308:
	insert	d1,d1,d0,#16,#4
.L309:
	ld.bu	d0,[a5]45
.L310:
	insert	d1,d1,d0,#22,#1
.L311:
	ld.bu	d0,[a5]46
.L312:
	insert	d1,d1,d0,#24,#1
.L313:
	ld.bu	d0,[a5]47
.L314:
	insert	d1,d1,d0,#25,#1
.L315:
	ld.bu	d15,[a5]48
.L187:
	jeq	d15,#0,.L2
.L316:
	mov	d0,#2
.L317:
	j	.L3
.L2:
	mov	d0,#0
.L3:
	ld.bu	d15,[a5]49
.L318:
	jeq	d15,#0,.L4
.L319:
	mov	d15,#1
.L320:
	j	.L5
.L4:
	mov	d15,#0
.L5:
	or	d0,d15
.L321:
	insert	d1,d1,d0,#26,#2
.L322:
	ld.bu	d15,[a5]50
.L323:
	insert	d1,d1,d15,#28,#4
.L324:
	ld.bu	d15,[a5]51
.L325:
	insert	d1,d1,d15,#23,#1
.L326:
	st.w	[a4]16,d1
.L183:
	ld.w	d15,[a5]6
.L327:
	st.w	[a4]8,d15
.L328:
	ld.w	d15,[a5]10
.L329:
	st.w	[a4]12,d15
.L330:
	ld.w	d15,[a5]22
.L331:
	st.w	[a4]4,d15
.L332:
	ld.w	d15,[a5]18
.L333:
	st.w	[a4],d15
.L334:
	ld.bu	d15,[a5]42
.L335:
	jeq	d15,#0,.L6
.L336:
	ld.bu	d15,[a5]42
.L337:
	jeq	d15,#1,.L7
.L338:
	ld.bu	d15,[a5]42
.L339:
	jeq	d15,#2,.L8
.L340:
	ld.w	d15,[a5]14
.L341:
	st.w	[a4]24,d15
.L8:
.L7:
.L6:
	ret
.L176:
	
__IfxDma_Dma_configureTransactionSet_function_end:
	.size	IfxDma_Dma_configureTransactionSet,__IfxDma_Dma_configureTransactionSet_function_end-IfxDma_Dma_configureTransactionSet
.L80:
	; End of function
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_createModuleHandle',code,cluster('IfxDma_Dma_createModuleHandle')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_createModuleHandle'
	.align	2
	
	.global	IfxDma_Dma_createModuleHandle
; Function IfxDma_Dma_createModuleHandle
.L24:
IfxDma_Dma_createModuleHandle:	.type	func
	st.a	[a4],a5
.L207:
	ret
.L81:
	
__IfxDma_Dma_createModuleHandle_function_end:
	.size	IfxDma_Dma_createModuleHandle,__IfxDma_Dma_createModuleHandle_function_end-IfxDma_Dma_createModuleHandle
.L45:
	; End of function
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_deInitChannel',code,cluster('IfxDma_Dma_deInitChannel')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_deInitChannel'
	.align	2
	
	.global	IfxDma_Dma_deInitChannel
; Function IfxDma_Dma_deInitChannel
.L26:
IfxDma_Dma_deInitChannel:	.type	func
	ld.a	a15,[a4]
.L91:
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L212:
	ld.bu	d15,[a15]7680
	extr.u	d15,d15,#1,#1
.L213:
	ne	d15,d15,#0
.L214:
	j	.L9
.L9:
	jeq	d15,#0,.L10
.L215:
	ld.a	a15,[a4]
.L98:
	mul	d15,d4,#4
	addsc.a	a2,a15,d15,#0
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L216:
	ld.bu	d15,[a15]7682
.L217:
	or	d15,#2
	st.b	[a2]7682,d15
.L10:
	ld.a	a15,[a4]
.L105:
	mul	d15,d4,#4
	addsc.a	a2,a15,d15,#0
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L218:
	ld.bu	d15,[a15]7680
.L219:
	or	d15,#1
	st.b	[a2]7680,d15
.L106:
	j	.L11
.L12:
.L11:
	ld.a	a15,[a4]
.L113:
	mul	d15,d4,#4
	addsc.a	a15,a15,d15,#0
.L220:
	ld.bu	d15,[a15]7680
	and	d15,#1
.L221:
	eq	d15,d15,#0
.L222:
	j	.L13
.L13:
	jeq	d15,#0,.L12
.L223:
	ret
.L86:
	
__IfxDma_Dma_deInitChannel_function_end:
	.size	IfxDma_Dma_deInitChannel,__IfxDma_Dma_deInitChannel_function_end-IfxDma_Dma_deInitChannel
.L50:
	; End of function
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_initChannel',code,cluster('IfxDma_Dma_initChannel')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_initChannel'
	.align	2
	
	.global	IfxDma_Dma_initChannel
; Function IfxDma_Dma_initChannel
.L28:
IfxDma_Dma_initChannel:	.type	func
	mov.aa	a15,a4
.L190:
	mov.aa	a12,a5
.L192:
	ld.a	a2,[a12]
.L239:
	ld.a	a13,[a2]
.L193:
	st.a	[a15],a13
.L240:
	ld.b	d15,[a12]4
.L241:
	st.b	[a15]4,d15
.L242:
	ld.b	d15,[a12]4
.L243:
	mul	d15,d15,#32
	addsc.a	a2,a13,d15,#0
	lea	a2,[a2]8192
.L244:
	st.a	[a15]8,a2
.L245:
	ld.a	a4,[a15]8
.L189:
	mov.aa	a5,a12
	call	IfxDma_Dma_configureTransactionSet
.L133:
	mov	d0,#0
.L194:
	ld.bu	d15,[a12]35
.L246:
	jeq	d15,#0,.L14
.L247:
	mov	d15,#1
.L248:
	insert	d0,d0,d15,#16,#1
.L249:
	j	.L15
.L14:
	mov	d15,#1
.L250:
	insert	d0,d0,d15,#17,#1
.L15:
	ld.b	d15,[a15]4
.L251:
	mul	d15,d15,#4
	addsc.a	a2,a13,d15,#0
.L252:
	st.w	[a2]7680,d0
.L134:
	ld.hu	d15,[a12]52
.L253:
	jlt.u	d15,#1,.L16
.L137:
	ld.a	a2,[a15]
.L254:
	ld.b	d15,[a15]4
.L141:
	jz.a	a2,.L17
.L17:
	mul	d15,d15,#4
	mov.a	a15,d15
.L191:
	movh.a	a3,#61444
	add.a	a3,a15
	lea	a15,[a3]-31488
.L196:
	j	.L18
.L18:
	ld.bu	d0,[a12]54
.L195:
	ld.hu	d15,[a12]52
.L148:
	ld.bu	d1,[a15]
.L255:
	extr.u	d15,d15,#0,#8
.L256:
	insert	d15,d1,d15,#0,#8
	st.b	[a15],d15
.L257:
	ld.bu	d15,[a15]1
.L258:
	insert	d15,d15,d0,#3,#2
	st.b	[a15]1,d15
.L158:
	ld.bu	d15,[a15]3
.L259:
	or	d15,#2
	st.b	[a15]3,d15
.L149:
	ld.bu	d15,[a15]1
.L260:
	or	d15,#4
	st.b	[a15]1,d15
.L16:
	ret
.L127:
	
__IfxDma_Dma_initChannel_function_end:
	.size	IfxDma_Dma_initChannel,__IfxDma_Dma_initChannel_function_end-IfxDma_Dma_initChannel
.L65:
	; End of function
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_initChannelConfig',code,cluster('IfxDma_Dma_initChannelConfig')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_initChannelConfig'
	.align	2
	
	.global	IfxDma_Dma_initChannelConfig
; Function IfxDma_Dma_initChannelConfig
.L30:
IfxDma_Dma_initChannelConfig:	.type	func
	sub.a	a10,#56
.L197:
	movh.a	a15,#@his(.1.ini)
	lea	a15,[a15]@los(.1.ini)
	lea	a15,[a15]0
.L265:
	lea	a2,[a10]0
	mov.a	a6,#13
.L19:
	ld.w	d15,[a15+]
	st.w	[a2+],d15
	loop	a6,.L19
.L266:
	lea	a15,[a10]0
.L267:
	lea	a2,[a4]0
	mov.a	a6,#13
.L20:
	ld.w	d15,[a15+]
	st.w	[a2+],d15
	loop	a6,.L20
.L268:
	st.a	[a4],a5
.L269:
	ret
.L166:
	
__IfxDma_Dma_initChannelConfig_function_end:
	.size	IfxDma_Dma_initChannelConfig,__IfxDma_Dma_initChannelConfig_function_end-IfxDma_Dma_initChannelConfig
.L70:
	; End of function
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_initLinkedListEntry',code,cluster('IfxDma_Dma_initLinkedListEntry')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_initLinkedListEntry'
	.align	2
	
	.global	IfxDma_Dma_initLinkedListEntry
; Function IfxDma_Dma_initLinkedListEntry
.L32:
IfxDma_Dma_initLinkedListEntry:	.type	func
	call	IfxDma_Dma_configureTransactionSet
.L198:
	ret
.L172:
	
__IfxDma_Dma_initLinkedListEntry_function_end:
	.size	IfxDma_Dma_initLinkedListEntry,__IfxDma_Dma_initLinkedListEntry_function_end-IfxDma_Dma_initLinkedListEntry
.L75:
	; End of function
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_initModule',code,cluster('IfxDma_Dma_initModule')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_initModule'
	.align	2
	
	.global	IfxDma_Dma_initModule
; Function IfxDma_Dma_initModule
.L34:
IfxDma_Dma_initModule:	.type	func
	ld.a	a15,[a5]
.L228:
	st.a	[a4],a15
.L229:
	ret
.L119:
	
__IfxDma_Dma_initModule_function_end:
	.size	IfxDma_Dma_initModule,__IfxDma_Dma_initModule_function_end-IfxDma_Dma_initModule
.L55:
	; End of function
	
	.sdecl	'.text.IfxDma_Dma.IfxDma_Dma_initModuleConfig',code,cluster('IfxDma_Dma_initModuleConfig')
	.sect	'.text.IfxDma_Dma.IfxDma_Dma_initModuleConfig'
	.align	2
	
	.global	IfxDma_Dma_initModuleConfig
; Function IfxDma_Dma_initModuleConfig
.L36:
IfxDma_Dma_initModuleConfig:	.type	func
	st.a	[a4],a5
.L234:
	ret
.L123:
	
__IfxDma_Dma_initModuleConfig_function_end:
	.size	IfxDma_Dma_initModuleConfig,__IfxDma_Dma_initModuleConfig_function_end-IfxDma_Dma_initModuleConfig
.L60:
	; End of function
	
	.sdecl	'.rodata.IfxDma_Dma..1.ini',data,rom
	.sect	'.rodata.IfxDma_Dma..1.ini'
	.align	4
.1.ini:	.type	object
	.size	.1.ini,56
	.space	34
	.byte	1
	.space	2
	.byte	1,15
	.space	1
	.byte	1,15
	.space	14
	.calls	'IfxDma_Dma_initChannel','IfxDma_Dma_configureTransactionSet'
	.calls	'IfxDma_Dma_initLinkedListEntry','IfxDma_Dma_configureTransactionSet'
	.calls	'IfxDma_Dma_configureTransactionSet','',0
	.calls	'IfxDma_Dma_createModuleHandle','',0
	.calls	'IfxDma_Dma_deInitChannel','',0
	.calls	'IfxDma_Dma_initChannel','',0
	.calls	'IfxDma_Dma_initChannelConfig','',56
	.calls	'IfxDma_Dma_initLinkedListEntry','',0
	.calls	'IfxDma_Dma_initModule','',0
	.calls	'IfxDma_Dma_initModuleConfig','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L38:
	.word	98530
	.half	3
	.word	.L39
	.byte	4
.L37:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L40
	.byte	2,1,1,3
	.word	234
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	237
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	282
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	294
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0
.L173:
	.byte	3
	.word	406
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	380
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	412
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	412
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	380
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	521
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	521
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	538
	.byte	4,2,35,0,0,14
	.word	828
.L138:
	.byte	3
	.word	867
.L157:
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1
.L159:
	.byte	5
	.byte	'src',0,3,250,1,60
	.word	872
.L161:
	.byte	6,0
.L162:
	.byte	4
	.byte	'IfxSrc_enable',0,3,3,140,2,17,1,1
.L163:
	.byte	5
	.byte	'src',0,3,140,2,54
	.word	872
.L165:
	.byte	6,0,15,5,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,7
	.byte	'unsigned short int',0,2,7
.L147:
	.byte	4
	.byte	'IfxSrc_init',0,3,3,146,2,17,1,1
.L150:
	.byte	5
	.byte	'src',0,3,146,2,52
	.word	872
.L152:
	.byte	5
	.byte	'typOfService',0,3,146,2,68
	.word	957
.L154:
	.byte	5
	.byte	'priority',0,3,146,2,95
	.word	1016
.L156:
	.byte	17,6,0,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,7,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	1115
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	1115
	.byte	16,0,2,35,0,0,12,7,247,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1131
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,7,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,7,255,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1267
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,7,137,9,16,4,11
	.byte	'AE',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,7,135,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,7,175,15,25,12,13
	.byte	'CON0',0
	.word	1227
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1471
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1702
	.byte	4,2,35,8,0,14
	.word	1742
	.byte	3
	.word	1805
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,6,181,3,17,1,1,5
	.byte	'watchdog',0,6,181,3,65
	.word	1810
	.byte	5
	.byte	'password',0,6,181,3,82
	.word	1016
	.byte	6,0,4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,6,204,3,17,1,1,5
	.byte	'password',0,6,204,3,59
	.word	1016
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,6,140,4,17,1,1,5
	.byte	'watchdog',0,6,140,4,63
	.word	1810
	.byte	5
	.byte	'password',0,6,140,4,80
	.word	1016
	.byte	6,0,4
	.byte	'IfxScuWdt_setSafetyEndinitInline',0,3,6,163,4,17,1,1,5
	.byte	'password',0,6,163,4,57
	.word	1016
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,6,227,3,19
	.word	1016
	.byte	1,1,5
	.byte	'watchdog',0,6,227,3,74
	.word	1810
	.byte	6,0,8
	.byte	'IfxScuWdt_getSafetyWatchdogPasswordInline',0,3,6,253,3,19
	.word	1016
	.byte	1,1,6,0,15,9,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	2220
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	521
	.byte	1,1,6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	2375
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	1016
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	521
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	1016
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	2375
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	2375
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,11,143,3,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,181,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2606
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,11,169,2,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,11,133,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2922
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,11,110,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,148,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3493
	.byte	4,2,35,0,0,18,4
	.word	521
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,11,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,11,164,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3621
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,11,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,11,180,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3836
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,11,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,11,188,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4051
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,11,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,11,172,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4268
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,11,118,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,156,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4488
	.byte	4,2,35,0,0,18,24
	.word	521
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,11,205,3,16,4,11
	.byte	'PD0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	521
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,11,205,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4811
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,11,226,3,16,4,11
	.byte	'PD8',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	521
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,11,213,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5115
	.byte	4,2,35,0,0,18,8
	.word	521
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,11,88,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,140,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5440
	.byte	4,2,35,0,0,18,12
	.word	521
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,11,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,197,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5780
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,11,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	498
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,11,189,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6146
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,11,206,2,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,12,11,149,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6432
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,11,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,12,11,165,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6579
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,11,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	498
	.byte	20,0,2,35,0,0,12,11,173,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6748
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,11,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1016
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,157,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6920
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,11,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1016
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1016
	.byte	12,0,2,35,2,0,12,11,229,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7095
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,11,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,11,245,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7269
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,11,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,12,11,253,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7443
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,11,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,11,237,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7619
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,11,249,2,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,141,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7775
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,11,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1016
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,11,221,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8108
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,11,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,12,11,196,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8456
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,11,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,11,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,12,11,204,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8580
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8664
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,11,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,11,213,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8844
	.byte	4,2,35,0,0,18,76
	.word	521
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,11,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,11,132,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9097
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,11,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,11,252,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9184
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,11,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2882
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	3453
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3572
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3612
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3796
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	4011
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	4228
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	4448
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3612
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4762
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4802
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	5075
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	5391
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	5431
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5731
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5771
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	6106
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	6392
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	5431
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6539
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6708
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6880
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	7055
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	7229
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	7403
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7579
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7735
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	8068
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	8416
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5431
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8540
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8789
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	9048
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	9088
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	9144
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9711
	.byte	4,3,35,252,1,0,14
	.word	9751
	.byte	3
	.word	10354
	.byte	15,10,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,10,208,4,17,1,1,5
	.byte	'port',0,10,208,4,44
	.word	10359
	.byte	5
	.byte	'pinIndex',0,10,208,4,56
	.word	521
	.byte	5
	.byte	'action',0,10,208,4,80
	.word	10364
	.byte	6,0,10
	.byte	'_Ifx_DMA_CLC_Bits',0,13,131,4,16,4,11
	.byte	'DISR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,12,13,160,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ID_Bits',0,13,155,4,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1016
	.byte	16,0,2,35,2,0,12,13,184,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10702
	.byte	4,2,35,0,0,18,20
	.word	521
	.byte	19,19,0,10
	.byte	'_Ifx_DMA_MEMCON_Bits',0,13,163,4,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	2,30,2,35,0,11
	.byte	'INTERR',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'RMWERR',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1115
	.byte	1,26,2,35,0,11
	.byte	'DATAERR',0,4
	.word	1115
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	1115
	.byte	1,24,2,35,0,11
	.byte	'PMIC',0,4
	.word	1115
	.byte	1,23,2,35,0,11
	.byte	'ERRDIS',0,4
	.word	1115
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1115
	.byte	22,0,2,35,0,0,12,13,192,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10833
	.byte	4,2,35,0,0,18,28
	.word	521
	.byte	19,27,0,10
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,13,128,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,136,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,13,88,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,13,144,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11770
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,13,125,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,152,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12340
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,13,131,1,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,13,160,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12430
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,13,168,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,168,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13001
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,13,174,1,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,13,176,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13092
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,13,211,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,184,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13663
	.byte	4,2,35,0,0,18,192,1
	.word	521
	.byte	19,191,1,0,10
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,13,233,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1016
	.byte	16,0,2,35,0,11
	.byte	'ESER',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EDER',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	6,0,2,35,2,11
	.byte	'ERER',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'ELER',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,13,200,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13765
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,13,246,1,16,4,11
	.byte	'LEC',0,1
	.word	521
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1016
	.byte	9,0,2,35,0,11
	.byte	'SER',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'DER',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	2,4,2,35,2,11
	.byte	'SPBER',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'SRIER',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'RAMER',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'SLLER',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'DLLER',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,13,208,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,13,217,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1016
	.byte	16,0,2,35,0,11
	.byte	'CSER',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'CDER',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	2,4,2,35,2,11
	.byte	'CSPBER',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'CSRIER',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'CRAMER',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'CSLLER',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'CDLLER',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	5,0,2,35,3,0,12,13,192,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14281
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,13,161,3,16,4,11
	.byte	'RS',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	3,4,2,35,0,11
	.byte	'WS',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1016
	.byte	11,0,2,35,0,11
	.byte	'CH',0,1
	.word	521
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1016
	.byte	9,0,2,35,2,0,12,13,216,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14564
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,13,193,2,16,4,11
	.byte	'RD00',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD01',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD02',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD03',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,248,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14744
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,13,202,2,16,4,11
	.byte	'RD10',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD11',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD12',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD13',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,128,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14879
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,13,211,2,16,4,11
	.byte	'RD20',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD21',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD22',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD23',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,136,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15014
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,13,220,2,16,4,11
	.byte	'RD30',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD31',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD32',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD33',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,144,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15149
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,13,229,2,16,4,11
	.byte	'RD40',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD41',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD42',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD43',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,152,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15284
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,13,238,2,16,4,11
	.byte	'RD50',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD51',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD52',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD53',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,160,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15419
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,13,247,2,16,4,11
	.byte	'RD60',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD61',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD62',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD63',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,168,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15554
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,13,128,3,16,4,11
	.byte	'RD70',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'RD71',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'RD72',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'RD73',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,176,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15689
	.byte	4,2,35,0,0,18,32
	.word	521
	.byte	19,31,0,10
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,13,137,3,16,4,11
	.byte	'RDCRC',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,184,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15833
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,13,149,3,16,4,11
	.byte	'SDCRC',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,200,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15924
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,13,143,3,16,4,11
	.byte	'SADR',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,192,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16015
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,13,187,2,16,4,11
	.byte	'DADR',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,240,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16104
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,13,135,2,16,4,11
	.byte	'SMF',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	521
	.byte	4,0,2,35,3,0,12,13,216,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,13,155,2,16,4,11
	.byte	'TREL',0,2
	.word	1016
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	521
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	521
	.byte	2,0,2,35,3,0,12,13,224,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16509
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,13,155,3,16,4,11
	.byte	'SHADR',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,208,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16788
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,13,171,2,16,4,11
	.byte	'TCOUNT',0,2
	.word	1016
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,232,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16879
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_BLK_ME',0,13,144,8,25,112,13
	.byte	'SR',0
	.word	14704
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5771
	.byte	12,2,35,4,13
	.byte	'R0',0
	.word	14839
	.byte	4,2,35,16,13
	.byte	'R1',0
	.word	14974
	.byte	4,2,35,20,13
	.byte	'R2',0
	.word	15109
	.byte	4,2,35,24,13
	.byte	'R3',0
	.word	15244
	.byte	4,2,35,28,13
	.byte	'R4',0
	.word	15379
	.byte	4,2,35,32,13
	.byte	'R5',0
	.word	15514
	.byte	4,2,35,36,13
	.byte	'R6',0
	.word	15649
	.byte	4,2,35,40,13
	.byte	'R7',0
	.word	15784
	.byte	4,2,35,44,13
	.byte	'reserved_30',0
	.word	15824
	.byte	32,2,35,48,13
	.byte	'RDCRC',0
	.word	15884
	.byte	4,2,35,80,13
	.byte	'SDCRC',0
	.word	15975
	.byte	4,2,35,84,13
	.byte	'SADR',0
	.word	16064
	.byte	4,2,35,88,13
	.byte	'DADR',0
	.word	16153
	.byte	4,2,35,92,13
	.byte	'ADICR',0
	.word	16469
	.byte	4,2,35,96,13
	.byte	'CHCR',0
	.word	16748
	.byte	4,2,35,100,13
	.byte	'SHADR',0
	.word	16839
	.byte	4,2,35,104,13
	.byte	'CHSR',0
	.word	17112
	.byte	4,2,35,108,0,14
	.word	17152
	.byte	10
	.byte	'_Ifx_DMA_BLK',0,13,178,8,25,128,1,13
	.byte	'EER',0
	.word	13949
	.byte	4,2,35,0,13
	.byte	'ERRSR',0
	.word	14241
	.byte	4,2,35,4,13
	.byte	'CLRE',0
	.word	14524
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3612
	.byte	4,2,35,12,13
	.byte	'ME',0
	.word	17440
	.byte	112,2,35,16,0,14
	.word	17445
	.byte	18,128,31
	.word	521
	.byte	19,255,30,0,14
	.word	17445
	.byte	18,96
	.word	521
	.byte	19,95,0,10
	.byte	'_Ifx_DMA_OTSS_Bits',0,13,185,4,16,4,11
	.byte	'TGS',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'BS',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,12,13,208,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17570
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,13,141,4,16,4,11
	.byte	'SIT',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,12,13,168,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17709
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_PRR0_Bits',0,13,194,4,16,4,11
	.byte	'PAT00',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'PAT01',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'PAT02',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'PAT03',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,216,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17815
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_PRR1_Bits',0,13,203,4,16,4,11
	.byte	'PAT10',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'PAT11',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'PAT12',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'PAT13',0,1
	.word	521
	.byte	8,0,2,35,3,0,12,13,224,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17949
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_TIME_Bits',0,13,226,4,16,4,11
	.byte	'COUNT',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,248,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18083
	.byte	4,2,35,0,0,18,236,1
	.word	521
	.byte	19,235,1,0,10
	.byte	'_Ifx_DMA_MODE_Bits',0,13,178,4,16,4,11
	.byte	'MODE',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,12,13,200,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18177
	.byte	4,2,35,0,0,18,16
	.word	18241
	.byte	19,3,0,18,240,9
	.word	521
	.byte	19,239,9,0,10
	.byte	'_Ifx_DMA_HRR_Bits',0,13,148,4,16,4,11
	.byte	'HRP',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,12,13,176,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18301
	.byte	4,2,35,0,0,18,192,1
	.word	18363
	.byte	19,47,0,18,192,2
	.word	521
	.byte	19,191,2,0,10
	.byte	'_Ifx_DMA_SUSENR_Bits',0,13,219,4,16,4,11
	.byte	'SUSEN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,12,13,240,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18424
	.byte	4,2,35,0,0,18,192,1
	.word	18491
	.byte	19,47,0,10
	.byte	'_Ifx_DMA_SUSACR_Bits',0,13,212,4,16,4,11
	.byte	'SUSAC',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,12,13,232,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18541
	.byte	4,2,35,0,0,18,192,1
	.word	18608
	.byte	19,47,0,10
	.byte	'_Ifx_DMA_TSR_Bits',0,13,232,4,16,4,11
	.byte	'RST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'HTRE',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'TRL',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'CH',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'HLTREQ',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'HLTACK',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	6,0,2,35,1,11
	.byte	'ECH',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'DCH',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'CTL',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'HLTCLR',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	521
	.byte	7,0,2,35,3,0
.L135:
	.byte	12,13,128,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18658
	.byte	4,2,35,0,0,18,192,1
	.word	18933
	.byte	19,47,0,10
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,13,235,3,16,4,11
	.byte	'RDCRC',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,128,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18983
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,13,247,3,16,4,11
	.byte	'SDCRC',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,144,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,13,241,3,16,4,11
	.byte	'SADR',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,136,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19159
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,13,229,3,16,4,11
	.byte	'DADR',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,248,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19244
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,13,172,3,16,4,11
	.byte	'SMF',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	521
	.byte	4,0,2,35,3,0
.L184:
	.byte	12,13,224,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19329
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,13,192,3,16,4,11
	.byte	'TREL',0,2
	.word	1016
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	521
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	521
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	521
	.byte	2,0,2,35,3,0
.L181:
	.byte	12,13,232,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19641
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,13,253,3,16,4,11
	.byte	'SHADR',0,4
	.word	498
	.byte	32,0,2,35,0,0,12,13,152,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19918
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,13,208,3,16,4,11
	.byte	'TCOUNT',0,2
	.word	1016
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'SWB',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'CWRP',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'CICH',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'SIT',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'SCH',0,1
	.word	521
	.byte	1,0,2,35,3,0,12,13,240,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20005
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_DMA_CH',0,13,188,8,25,32,13
	.byte	'RDCRCR',0
	.word	19031
	.byte	4,2,35,0,13
	.byte	'SDCRCR',0
	.word	19119
	.byte	4,2,35,4,13
	.byte	'SADR',0
	.word	19204
	.byte	4,2,35,8,13
	.byte	'DADR',0
	.word	19289
	.byte	4,2,35,12,13
	.byte	'ADICR',0
	.word	19601
	.byte	4,2,35,16,13
	.byte	'CHCFGR',0
	.word	19878
	.byte	4,2,35,20,13
	.byte	'SHADR',0
	.word	19965
	.byte	4,2,35,24,13
	.byte	'CHCSR',0
	.word	20312
	.byte	4,2,35,28,0,18,128,12
	.word	20352
	.byte	19,47,0,14
	.word	20492
	.byte	18,128,52
	.word	521
	.byte	19,255,51,0,10
	.byte	'_Ifx_DMA',0,13,211,8,25,128,128,1,13
	.byte	'CLC',0
	.word	10662
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3612
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	10784
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	10824
	.byte	20,2,35,12,13
	.byte	'MEMCON',0
	.word	11061
	.byte	4,2,35,32,13
	.byte	'reserved_24',0
	.word	11101
	.byte	28,2,35,36,13
	.byte	'ACCEN00',0
	.word	11640
	.byte	4,2,35,64,13
	.byte	'ACCEN01',0
	.word	11730
	.byte	4,2,35,68,13
	.byte	'ACCEN10',0
	.word	12300
	.byte	4,2,35,72,13
	.byte	'ACCEN11',0
	.word	12390
	.byte	4,2,35,76,13
	.byte	'ACCEN20',0
	.word	12961
	.byte	4,2,35,80,13
	.byte	'ACCEN21',0
	.word	13052
	.byte	4,2,35,84,13
	.byte	'ACCEN30',0
	.word	13623
	.byte	4,2,35,88,13
	.byte	'ACCEN31',0
	.word	13714
	.byte	4,2,35,92,13
	.byte	'reserved_60',0
	.word	13754
	.byte	192,1,2,35,96,13
	.byte	'BLK0',0
	.word	17540
	.byte	128,1,3,35,160,2,13
	.byte	'reserved_1A0',0
	.word	17545
	.byte	128,31,3,35,160,3,13
	.byte	'BLK1',0
	.word	17556
	.byte	128,1,3,35,160,34,13
	.byte	'reserved_11A0',0
	.word	17561
	.byte	96,3,35,160,35,13
	.byte	'OTSS',0
	.word	17669
	.byte	4,3,35,128,36,13
	.byte	'ERRINTR',0
	.word	17775
	.byte	4,3,35,132,36,13
	.byte	'PRR0',0
	.word	17909
	.byte	4,3,35,136,36,13
	.byte	'PRR1',0
	.word	18043
	.byte	4,3,35,140,36,13
	.byte	'TIME',0
	.word	18126
	.byte	4,3,35,144,36,13
	.byte	'reserved_1214',0
	.word	18166
	.byte	236,1,3,35,148,36,13
	.byte	'MODE',0
	.word	18281
	.byte	16,3,35,128,38,13
	.byte	'reserved_1310',0
	.word	18290
	.byte	240,9,3,35,144,38,13
	.byte	'HRR',0
	.word	18403
	.byte	192,1,3,35,128,48,13
	.byte	'reserved_18C0',0
	.word	18413
	.byte	192,2,3,35,192,49,13
	.byte	'SUSENR',0
	.word	18531
	.byte	192,1,3,35,128,52,13
	.byte	'reserved_1AC0',0
	.word	18413
	.byte	192,2,3,35,192,53,13
	.byte	'SUSACR',0
	.word	18648
	.byte	192,1,3,35,128,56,13
	.byte	'reserved_1CC0',0
	.word	18413
	.byte	192,2,3,35,192,57,13
	.byte	'TSR',0
	.word	18973
	.byte	192,1,3,35,128,60,13
	.byte	'reserved_1EC0',0
	.word	18413
	.byte	192,2,3,35,192,61,13
	.byte	'CH',0
	.word	20502
	.byte	128,12,3,35,128,64,13
	.byte	'reserved_2600',0
	.word	20507
	.byte	128,52,3,35,128,76,0,14
	.word	20518
.L84:
	.byte	3
	.word	21224
.L88:
	.byte	15,14,105,9,1,16
	.byte	'IfxDma_ChannelId_none',0,127,16
	.byte	'IfxDma_ChannelId_0',0,0,16
	.byte	'IfxDma_ChannelId_1',0,1,16
	.byte	'IfxDma_ChannelId_2',0,2,16
	.byte	'IfxDma_ChannelId_3',0,3,16
	.byte	'IfxDma_ChannelId_4',0,4,16
	.byte	'IfxDma_ChannelId_5',0,5,16
	.byte	'IfxDma_ChannelId_6',0,6,16
	.byte	'IfxDma_ChannelId_7',0,7,16
	.byte	'IfxDma_ChannelId_8',0,8,16
	.byte	'IfxDma_ChannelId_9',0,9,16
	.byte	'IfxDma_ChannelId_10',0,10,16
	.byte	'IfxDma_ChannelId_11',0,11,16
	.byte	'IfxDma_ChannelId_12',0,12,16
	.byte	'IfxDma_ChannelId_13',0,13,16
	.byte	'IfxDma_ChannelId_14',0,14,16
	.byte	'IfxDma_ChannelId_15',0,15,16
	.byte	'IfxDma_ChannelId_16',0,16,16
	.byte	'IfxDma_ChannelId_17',0,17,16
	.byte	'IfxDma_ChannelId_18',0,18,16
	.byte	'IfxDma_ChannelId_19',0,19,16
	.byte	'IfxDma_ChannelId_20',0,20,16
	.byte	'IfxDma_ChannelId_21',0,21,16
	.byte	'IfxDma_ChannelId_22',0,22,16
	.byte	'IfxDma_ChannelId_23',0,23,16
	.byte	'IfxDma_ChannelId_24',0,24,16
	.byte	'IfxDma_ChannelId_25',0,25,16
	.byte	'IfxDma_ChannelId_26',0,26,16
	.byte	'IfxDma_ChannelId_27',0,27,16
	.byte	'IfxDma_ChannelId_28',0,28,16
	.byte	'IfxDma_ChannelId_29',0,29,16
	.byte	'IfxDma_ChannelId_30',0,30,16
	.byte	'IfxDma_ChannelId_31',0,31,16
	.byte	'IfxDma_ChannelId_32',0,32,16
	.byte	'IfxDma_ChannelId_33',0,33,16
	.byte	'IfxDma_ChannelId_34',0,34,16
	.byte	'IfxDma_ChannelId_35',0,35,16
	.byte	'IfxDma_ChannelId_36',0,36,16
	.byte	'IfxDma_ChannelId_37',0,37,16
	.byte	'IfxDma_ChannelId_38',0,38,16
	.byte	'IfxDma_ChannelId_39',0,39,16
	.byte	'IfxDma_ChannelId_40',0,40,16
	.byte	'IfxDma_ChannelId_41',0,41,16
	.byte	'IfxDma_ChannelId_42',0,42,16
	.byte	'IfxDma_ChannelId_43',0,43,16
	.byte	'IfxDma_ChannelId_44',0,44,16
	.byte	'IfxDma_ChannelId_45',0,45,16
	.byte	'IfxDma_ChannelId_46',0,46,16
	.byte	'IfxDma_ChannelId_47',0,47,0
.L112:
	.byte	8
	.byte	'IfxDma_isChannelReset',0,3,12,136,12,20
	.word	521
	.byte	1,1
.L114:
	.byte	5
	.byte	'dma',0,12,136,12,51
	.word	21229
.L116:
	.byte	5
	.byte	'channelId',0,12,136,12,73
	.word	21234
.L118:
	.byte	6,0
.L104:
	.byte	4
	.byte	'IfxDma_resetChannel',0,3,12,160,12,17,1,1
.L107:
	.byte	5
	.byte	'dma',0,12,160,12,46
	.word	21229
.L109:
	.byte	5
	.byte	'channelId',0,12,160,12,68
	.word	21234
.L111:
	.byte	6,0
.L97:
	.byte	4
	.byte	'IfxDma_disableChannelTransaction',0,3,12,144,10,17,1,1
.L99:
	.byte	5
	.byte	'dma',0,12,144,10,59
	.word	21229
.L101:
	.byte	5
	.byte	'channelId',0,12,144,10,81
	.word	21234
.L103:
	.byte	6,0
.L90:
	.byte	8
	.byte	'IfxDma_isChannelTransactionEnabled',0,3,12,142,12,20
	.word	521
	.byte	1,1
.L92:
	.byte	5
	.byte	'dma',0,12,142,12,64
	.word	21229
.L94:
	.byte	5
	.byte	'channelId',0,12,142,12,86
	.word	21234
.L96:
	.byte	6,0,8
	.byte	'IfxDma_isChannelTransactionPending',0,3,12,148,12,20
	.word	521
	.byte	1,1,5
	.byte	'dma',0,12,148,12,64
	.word	21229
	.byte	5
	.byte	'channelId',0,12,148,12,86
	.word	21234
	.byte	6,0,4
	.byte	'IfxDma_startChannelTransaction',0,3,12,147,13,17,1,1,5
	.byte	'dma',0,12,147,13,57
	.word	21229
	.byte	5
	.byte	'channelId',0,12,147,13,79
	.word	21234
	.byte	6,0,4
	.byte	'IfxDma_setChannelDestinationAddress',0,3,12,178,12,17,1,1,5
	.byte	'dma',0,12,178,12,62
	.word	21229
	.byte	5
	.byte	'channelId',0,12,178,12,84
	.word	21234
	.byte	5
	.byte	'address',0,12,178,12,101
	.word	412
	.byte	6,0,20
	.word	406
	.byte	3
	.word	22845
	.byte	4
	.byte	'IfxDma_setChannelSourceAddress',0,3,12,237,12,17,1,1,5
	.byte	'dma',0,12,237,12,57
	.word	21229
	.byte	5
	.byte	'channelId',0,12,237,12,79
	.word	21234
	.byte	5
	.byte	'address',0,12,237,12,102
	.word	22850
	.byte	6,0,4
	.byte	'IfxDma_setChannelTransferCount',0,3,12,132,13,17,1,1,5
	.byte	'dma',0,12,132,13,57
	.word	21229
	.byte	5
	.byte	'channelId',0,12,132,13,79
	.word	21234
	.byte	5
	.byte	'transferCount',0,12,132,13,97
	.word	2375
	.byte	6,0,4
	.byte	'IfxDma_clearChannelInterrupt',0,3,12,241,9,17,1,1,5
	.byte	'dma',0,12,241,9,55
	.word	21229
	.byte	5
	.byte	'channelId',0,12,241,9,77
	.word	21234
	.byte	6,0,8
	.byte	'IfxDma_getAndClearChannelInterrupt',0,3,12,226,10,20
	.word	521
	.byte	1,1,5
	.byte	'dma',0,12,226,10,64
	.word	21229
	.byte	5
	.byte	'channelId',0,12,226,10,86
	.word	21234
	.byte	6,0,8
	.byte	'IfxDma_getChannelInterrupt',0,3,12,170,11,20
	.word	521
	.byte	1,1,5
	.byte	'dma',0,12,170,11,56
	.word	21229
	.byte	5
	.byte	'channelId',0,12,170,11,78
	.word	21234
	.byte	6,0
.L140:
	.byte	8
	.byte	'IfxDma_getSrcPointer',0,3,12,250,11,35
	.word	872
	.byte	1,1
.L142:
	.byte	5
	.byte	'dma',0,12,250,11,65
	.word	21229
.L144:
	.byte	5
	.byte	'channelId',0,12,250,11,87
	.word	21234
.L146:
	.byte	6,0,21
	.word	242
	.byte	22
	.word	268
	.byte	6,0,21
	.word	303
	.byte	22
	.word	335
	.byte	6,0,21
	.word	348
	.byte	6,0,21
	.word	417
	.byte	22
	.word	436
	.byte	6,0,21
	.word	452
	.byte	22
	.word	467
	.byte	22
	.word	481
	.byte	6,0,21
	.word	877
	.byte	22
	.word	905
	.byte	6,0,21
	.word	920
	.byte	22
	.word	942
	.byte	6,0,21
	.word	1038
	.byte	22
	.word	1058
	.byte	22
	.word	1071
	.byte	22
	.word	1093
	.byte	17,23
	.word	877
	.byte	22
	.word	905
	.byte	24
	.word	918
	.byte	0,6,0,0,21
	.word	1815
	.byte	22
	.word	1855
	.byte	22
	.word	1873
	.byte	6,0,21
	.word	1893
	.byte	22
	.word	1936
	.byte	6,0,21
	.word	1956
	.byte	22
	.word	1994
	.byte	22
	.word	2012
	.byte	6,0,21
	.word	2032
	.byte	22
	.word	2073
	.byte	6,0,21
	.word	2093
	.byte	22
	.word	2144
	.byte	6,0,21
	.word	2164
	.byte	6,0,21
	.word	2299
	.byte	6,0,21
	.word	2333
	.byte	6,0,21
	.word	2396
	.byte	22
	.word	2437
	.byte	6,0,21
	.word	2456
	.byte	22
	.word	2511
	.byte	6,0,21
	.word	2530
	.byte	22
	.word	2570
	.byte	22
	.word	2587
	.byte	17,6,0,0,21
	.word	10467
	.byte	22
	.word	10495
	.byte	22
	.word	10509
	.byte	22
	.word	10527
	.byte	6,0,21
	.word	22310
	.byte	22
	.word	22344
	.byte	22
	.word	22357
	.byte	6,0,21
	.word	22378
	.byte	22
	.word	22406
	.byte	22
	.word	22419
	.byte	6,0,21
	.word	22440
	.byte	22
	.word	22481
	.byte	22
	.word	22494
	.byte	6,0,21
	.word	22515
	.byte	22
	.word	22562
	.byte	22
	.word	22575
	.byte	6,0,21
	.word	22596
	.byte	22
	.word	22643
	.byte	22
	.word	22656
	.byte	6,0,21
	.word	22677
	.byte	22
	.word	22716
	.byte	22
	.word	22729
	.byte	6,0,21
	.word	22750
	.byte	22
	.word	22794
	.byte	22
	.word	22807
	.byte	22
	.word	22826
	.byte	6,0,21
	.word	22855
	.byte	22
	.word	22894
	.byte	22
	.word	22907
	.byte	22
	.word	22926
	.byte	6,0,21
	.word	22945
	.byte	22
	.word	22984
	.byte	22
	.word	22997
	.byte	22
	.word	23016
	.byte	6,0,21
	.word	23041
	.byte	22
	.word	23078
	.byte	22
	.word	23091
	.byte	6,0,21
	.word	23112
	.byte	22
	.word	23159
	.byte	22
	.word	23172
	.byte	6,0,21
	.word	23193
	.byte	22
	.word	23232
	.byte	22
	.word	23245
	.byte	6,0,21
	.word	23266
	.byte	22
	.word	23299
	.byte	22
	.word	23312
	.byte	6,0,25,15,207,3,9,4,13
	.byte	'dma',0
	.word	21229
	.byte	4,2,35,0,0
.L82:
	.byte	3
	.word	23849
	.byte	25,15,139,4,9,4,13
	.byte	'dma',0
	.word	21229
	.byte	4,2,35,0,0,20
	.word	23874
.L121:
	.byte	3
	.word	23894
.L124:
	.byte	3
	.word	23874
	.byte	14
	.word	20352
.L177:
	.byte	3
	.word	23909
	.byte	25,15,218,3,9,12,13
	.byte	'dma',0
	.word	21229
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	21234
	.byte	1,2,35,4,13
	.byte	'channel',0
	.word	23914
	.byte	4,2,35,8,0
.L128:
	.byte	3
	.word	23919
	.byte	15,12,160,1,9,1,16
	.byte	'IfxDma_ChannelMove_1',0,0,16
	.byte	'IfxDma_ChannelMove_2',0,1,16
	.byte	'IfxDma_ChannelMove_4',0,2,16
	.byte	'IfxDma_ChannelMove_8',0,3,16
	.byte	'IfxDma_ChannelMove_16',0,4,16
	.byte	'IfxDma_ChannelMove_3',0,5,16
	.byte	'IfxDma_ChannelMove_5',0,6,16
	.byte	'IfxDma_ChannelMove_9',0,7,0,15,12,221,1,9,1,16
	.byte	'IfxDma_ChannelRequestMode_oneTransferPerRequest',0,0,16
	.byte	'IfxDma_ChannelRequestMode_completeTransactionPerRequest',0,1,0,15,12,188,1,9,1,16
	.byte	'IfxDma_ChannelOperationMode_single',0,0,16
	.byte	'IfxDma_ChannelOperationMode_continuous',0,1,0,15,12,175,1,9,1,16
	.byte	'IfxDma_ChannelMoveSize_8bit',0,0,16
	.byte	'IfxDma_ChannelMoveSize_16bit',0,1,16
	.byte	'IfxDma_ChannelMoveSize_32bit',0,2,16
	.byte	'IfxDma_ChannelMoveSize_64bit',0,3,16
	.byte	'IfxDma_ChannelMoveSize_128bit',0,4,16
	.byte	'IfxDma_ChannelMoveSize_256bit',0,5,0,15,12,197,1,9,1,16
	.byte	'IfxDma_ChannelPattern_0_disable',0,0,16
	.byte	'IfxDma_ChannelPattern_0_mode1',0,1,16
	.byte	'IfxDma_ChannelPattern_0_mode2',0,2,16
	.byte	'IfxDma_ChannelPattern_0_mode3',0,3,16
	.byte	'IfxDma_ChannelPattern_1_disable',0,4,16
	.byte	'IfxDma_ChannelPattern_1_mode1',0,5,16
	.byte	'IfxDma_ChannelPattern_1_mode2',0,6,16
	.byte	'IfxDma_ChannelPattern_1_mode3',0,7,0,15,12,230,1,9,1,16
	.byte	'IfxDma_ChannelRequestSource_peripheral',0,0,16
	.byte	'IfxDma_ChannelRequestSource_daisyChain',0,1,0,15,12,94,9,1,16
	.byte	'IfxDma_ChannelBusPriority_low',0,0,16
	.byte	'IfxDma_ChannelBusPriority_medium',0,1,16
	.byte	'IfxDma_ChannelBusPriority_high',0,2,0,15,12,136,1,9,1,16
	.byte	'IfxDma_ChannelIncrementStep_1',0,0,16
	.byte	'IfxDma_ChannelIncrementStep_2',0,1,16
	.byte	'IfxDma_ChannelIncrementStep_4',0,2,16
	.byte	'IfxDma_ChannelIncrementStep_8',0,3,16
	.byte	'IfxDma_ChannelIncrementStep_16',0,4,16
	.byte	'IfxDma_ChannelIncrementStep_32',0,5,16
	.byte	'IfxDma_ChannelIncrementStep_64',0,6,16
	.byte	'IfxDma_ChannelIncrementStep_128',0,7,0,15,12,127,9,1,16
	.byte	'IfxDma_ChannelIncrementDirection_negative',0,0,16
	.byte	'IfxDma_ChannelIncrementDirection_positive',0,1,0,15,12,104,9,1,16
	.byte	'IfxDma_ChannelIncrementCircular_none',0,0,16
	.byte	'IfxDma_ChannelIncrementCircular_2',0,1,16
	.byte	'IfxDma_ChannelIncrementCircular_4',0,2,16
	.byte	'IfxDma_ChannelIncrementCircular_8',0,3,16
	.byte	'IfxDma_ChannelIncrementCircular_16',0,4,16
	.byte	'IfxDma_ChannelIncrementCircular_32',0,5,16
	.byte	'IfxDma_ChannelIncrementCircular_64',0,6,16
	.byte	'IfxDma_ChannelIncrementCircular_128',0,7,16
	.byte	'IfxDma_ChannelIncrementCircular_256',0,8,16
	.byte	'IfxDma_ChannelIncrementCircular_512',0,9,16
	.byte	'IfxDma_ChannelIncrementCircular_1024',0,10,16
	.byte	'IfxDma_ChannelIncrementCircular_2048',0,11,16
	.byte	'IfxDma_ChannelIncrementCircular_4096',0,12,16
	.byte	'IfxDma_ChannelIncrementCircular_8192',0,13,16
	.byte	'IfxDma_ChannelIncrementCircular_16384',0,14,16
	.byte	'IfxDma_ChannelIncrementCircular_32768',0,15,0,15,12,239,1,9,1,16
	.byte	'IfxDma_ChannelShadow_none',0,0,16
	.byte	'IfxDma_ChannelShadow_src',0,1,16
	.byte	'IfxDma_ChannelShadow_dst',0,2,16
	.byte	'IfxDma_ChannelShadow_srcDirectWrite',0,5,16
	.byte	'IfxDma_ChannelShadow_dstDirectWrite',0,6,16
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingSwSwitch',0,8,16
	.byte	'IfxDma_ChannelShadow_doubleSourceBufferingHwSwSwitch',0,9,16
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingSwSwitch',0,10,16
	.byte	'IfxDma_ChannelShadow_doubleDestinationBufferingHwSwSwitch',0,11,16
	.byte	'IfxDma_ChannelShadow_linkedList',0,12,16
	.byte	'IfxDma_ChannelShadow_accumulatedLinkedList',0,13,16
	.byte	'IfxDma_ChannelShadow_safeLinkedList',0,14,16
	.byte	'IfxDma_ChannelShadow_conditionalLinkedList',0,15,0,15,12,151,1,9,1,16
	.byte	'IfxDma_ChannelInterruptControl_thresholdLimitMatch',0,0,16
	.byte	'IfxDma_ChannelInterruptControl_transferCountDecremented',0,1,0,25,15,227,3,9,56,13
	.byte	'module',0
	.word	23869
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	21234
	.byte	1,2,35,4,13
	.byte	'sourceAddress',0
	.word	2375
	.byte	4,2,35,6,13
	.byte	'destinationAddress',0
	.word	2375
	.byte	4,2,35,10,13
	.byte	'shadowAddress',0
	.word	2375
	.byte	4,2,35,14,13
	.byte	'readDataCrc',0
	.word	2375
	.byte	4,2,35,18,13
	.byte	'sourceDestinationAddressCrc',0
	.word	2375
	.byte	4,2,35,22,13
	.byte	'transferCount',0
	.word	1016
	.byte	2,2,35,26,13
	.byte	'blockMode',0
	.word	23980
	.byte	1,2,35,28,13
	.byte	'requestMode',0
	.word	24172
	.byte	1,2,35,29,13
	.byte	'operationMode',0
	.word	24287
	.byte	1,2,35,30,13
	.byte	'moveSize',0
	.word	24372
	.byte	1,2,35,31,13
	.byte	'pattern',0
	.word	24566
	.byte	1,2,35,32,13
	.byte	'requestSource',0
	.word	24833
	.byte	1,2,35,33,13
	.byte	'busPriority',0
	.word	24922
	.byte	1,2,35,34,13
	.byte	'hardwareRequestEnabled',0
	.word	521
	.byte	1,2,35,35,13
	.byte	'sourceAddressIncrementStep',0
	.word	25028
	.byte	1,2,35,36,13
	.byte	'sourceAddressIncrementDirection',0
	.word	25296
	.byte	1,2,35,37,13
	.byte	'sourceAddressCircularRange',0
	.word	25390
	.byte	1,2,35,38,13
	.byte	'destinationAddressIncrementStep',0
	.word	25028
	.byte	1,2,35,39,13
	.byte	'destinationAddressIncrementDirection',0
	.word	25296
	.byte	1,2,35,40,13
	.byte	'destinationAddressCircularRange',0
	.word	25390
	.byte	1,2,35,41,13
	.byte	'shadowControl',0
	.word	26004
	.byte	1,2,35,42,13
	.byte	'sourceCircularBufferEnabled',0
	.word	521
	.byte	1,2,35,43,13
	.byte	'destinationCircularBufferEnabled',0
	.word	521
	.byte	1,2,35,44,13
	.byte	'timestampEnabled',0
	.word	521
	.byte	1,2,35,45,13
	.byte	'wrapSourceInterruptEnabled',0
	.word	521
	.byte	1,2,35,46,13
	.byte	'wrapDestinationInterruptEnabled',0
	.word	521
	.byte	1,2,35,47,13
	.byte	'channelInterruptEnabled',0
	.word	521
	.byte	1,2,35,48,13
	.byte	'channelInterruptControl',0
	.word	26557
	.byte	1,2,35,49,13
	.byte	'interruptRaiseThreshold',0
	.word	521
	.byte	1,2,35,50,13
	.byte	'transactionRequestLostInterruptEnabled',0
	.word	521
	.byte	1,2,35,51,13
	.byte	'channelInterruptPriority',0
	.word	1016
	.byte	2,2,35,52,13
	.byte	'channelInterruptTypeOfService',0
	.word	957
	.byte	1,2,35,54,0,20
	.word	26675
.L130:
	.byte	3
	.word	27712
.L167:
	.byte	3
	.word	26675
.L170:
	.byte	20
	.word	26675
	.byte	7
	.byte	'short int',0,2,5,26
	.byte	'__wchar_t',0,16,1,1
	.word	27732
	.byte	26
	.byte	'__size_t',0,16,1,1
	.word	498
	.byte	26
	.byte	'__ptrdiff_t',0,16,1,1
	.word	514
	.byte	27,1,3
	.word	27800
	.byte	26
	.byte	'__codeptr',0,16,1,1
	.word	27802
	.byte	26
	.byte	'boolean',0,17,101,29
	.word	521
	.byte	26
	.byte	'uint8',0,17,105,29
	.word	521
	.byte	26
	.byte	'uint16',0,17,109,29
	.word	1016
	.byte	26
	.byte	'uint32',0,17,113,29
	.word	2375
	.byte	26
	.byte	'uint64',0,17,118,29
	.word	380
	.byte	26
	.byte	'sint16',0,17,126,29
	.word	27732
	.byte	7
	.byte	'long int',0,4,5,26
	.byte	'sint32',0,17,131,1,29
	.word	27915
	.byte	7
	.byte	'long long int',0,8,5,26
	.byte	'sint64',0,17,138,1,29
	.word	27943
	.byte	26
	.byte	'float32',0,17,167,1,29
	.word	294
	.byte	26
	.byte	'pvoid',0,18,57,28
	.word	412
	.byte	26
	.byte	'Ifx_TickTime',0,18,79,28
	.word	27943
	.byte	26
	.byte	'Ifx_Priority',0,18,103,16
	.word	1016
	.byte	15,18,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,26
	.byte	'Ifx_RxSel',0,18,140,1,3
	.word	28049
	.byte	26
	.byte	'IfxDma_ChannelId',0,14,156,1,3
	.word	21234
	.byte	26
	.byte	'Ifx_DMA_ACCEN00_Bits',0,13,79,3
	.word	11110
	.byte	26
	.byte	'Ifx_DMA_ACCEN01_Bits',0,13,85,3
	.word	11680
	.byte	26
	.byte	'Ifx_DMA_ACCEN10_Bits',0,13,122,3
	.word	11770
	.byte	26
	.byte	'Ifx_DMA_ACCEN11_Bits',0,13,128,1,3
	.word	12340
	.byte	26
	.byte	'Ifx_DMA_ACCEN20_Bits',0,13,165,1,3
	.word	12430
	.byte	26
	.byte	'Ifx_DMA_ACCEN21_Bits',0,13,171,1,3
	.word	13001
	.byte	26
	.byte	'Ifx_DMA_ACCEN30_Bits',0,13,208,1,3
	.word	13092
	.byte	26
	.byte	'Ifx_DMA_ACCEN31_Bits',0,13,214,1,3
	.word	13663
	.byte	26
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,13,230,1,3
	.word	14281
	.byte	26
	.byte	'Ifx_DMA_BLK_EER_Bits',0,13,243,1,3
	.word	13765
	.byte	26
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,13,132,2,3
	.word	13989
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,13,152,2,3
	.word	16193
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,13,168,2,3
	.word	16509
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,13,184,2,3
	.word	16879
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,13,190,2,3
	.word	16104
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,13,199,2,3
	.word	14744
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,13,208,2,3
	.word	14879
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,13,217,2,3
	.word	15014
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,13,226,2,3
	.word	15149
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,13,235,2,3
	.word	15284
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,13,244,2,3
	.word	15419
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,13,253,2,3
	.word	15554
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,13,134,3,3
	.word	15689
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,13,140,3,3
	.word	15833
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,13,146,3,3
	.word	16015
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,13,152,3,3
	.word	15924
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,13,158,3,3
	.word	16788
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,13,169,3,3
	.word	14564
	.byte	26
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,13,189,3,3
	.word	19329
	.byte	26
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,13,205,3,3
	.word	19641
	.byte	26
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,13,226,3,3
	.word	20005
	.byte	26
	.byte	'Ifx_DMA_CH_DADR_Bits',0,13,232,3,3
	.word	19244
	.byte	26
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,13,238,3,3
	.word	18983
	.byte	26
	.byte	'Ifx_DMA_CH_SADR_Bits',0,13,244,3,3
	.word	19159
	.byte	26
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,13,250,3,3
	.word	19071
	.byte	26
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,13,128,4,3
	.word	19918
	.byte	26
	.byte	'Ifx_DMA_CLC_Bits',0,13,138,4,3
	.word	10545
	.byte	26
	.byte	'Ifx_DMA_ERRINTR_Bits',0,13,145,4,3
	.word	17709
	.byte	26
	.byte	'Ifx_DMA_HRR_Bits',0,13,152,4,3
	.word	18301
	.byte	26
	.byte	'Ifx_DMA_ID_Bits',0,13,160,4,3
	.word	10702
	.byte	26
	.byte	'Ifx_DMA_MEMCON_Bits',0,13,175,4,3
	.word	10833
	.byte	26
	.byte	'Ifx_DMA_MODE_Bits',0,13,182,4,3
	.word	18177
	.byte	26
	.byte	'Ifx_DMA_OTSS_Bits',0,13,191,4,3
	.word	17570
	.byte	26
	.byte	'Ifx_DMA_PRR0_Bits',0,13,200,4,3
	.word	17815
	.byte	26
	.byte	'Ifx_DMA_PRR1_Bits',0,13,209,4,3
	.word	17949
	.byte	26
	.byte	'Ifx_DMA_SUSACR_Bits',0,13,216,4,3
	.word	18541
	.byte	26
	.byte	'Ifx_DMA_SUSENR_Bits',0,13,223,4,3
	.word	18424
	.byte	26
	.byte	'Ifx_DMA_TIME_Bits',0,13,229,4,3
	.word	18083
	.byte	26
	.byte	'Ifx_DMA_TSR_Bits',0,13,248,4,3
	.word	18658
	.byte	26
	.byte	'Ifx_DMA_ACCEN00',0,13,133,5,3
	.word	11640
	.byte	26
	.byte	'Ifx_DMA_ACCEN01',0,13,141,5,3
	.word	11730
	.byte	26
	.byte	'Ifx_DMA_ACCEN10',0,13,149,5,3
	.word	12300
	.byte	26
	.byte	'Ifx_DMA_ACCEN11',0,13,157,5,3
	.word	12390
	.byte	26
	.byte	'Ifx_DMA_ACCEN20',0,13,165,5,3
	.word	12961
	.byte	26
	.byte	'Ifx_DMA_ACCEN21',0,13,173,5,3
	.word	13052
	.byte	26
	.byte	'Ifx_DMA_ACCEN30',0,13,181,5,3
	.word	13623
	.byte	26
	.byte	'Ifx_DMA_ACCEN31',0,13,189,5,3
	.word	13714
	.byte	26
	.byte	'Ifx_DMA_BLK_CLRE',0,13,197,5,3
	.word	14524
	.byte	26
	.byte	'Ifx_DMA_BLK_EER',0,13,205,5,3
	.word	13949
	.byte	26
	.byte	'Ifx_DMA_BLK_ERRSR',0,13,213,5,3
	.word	14241
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,13,221,5,3
	.word	16469
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,13,229,5,3
	.word	16748
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,13,237,5,3
	.word	17112
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_DADR',0,13,245,5,3
	.word	16153
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R0',0,13,253,5,3
	.word	14839
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R1',0,13,133,6,3
	.word	14974
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R2',0,13,141,6,3
	.word	15109
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R3',0,13,149,6,3
	.word	15244
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R4',0,13,157,6,3
	.word	15379
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R5',0,13,165,6,3
	.word	15514
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R6',0,13,173,6,3
	.word	15649
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_R7',0,13,181,6,3
	.word	15784
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,13,189,6,3
	.word	15884
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SADR',0,13,197,6,3
	.word	16064
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,13,205,6,3
	.word	15975
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,13,213,6,3
	.word	16839
	.byte	26
	.byte	'Ifx_DMA_BLK_ME_SR',0,13,221,6,3
	.word	14704
	.byte	26
	.byte	'Ifx_DMA_CH_ADICR',0,13,229,6,3
	.word	19601
	.byte	26
	.byte	'Ifx_DMA_CH_CHCFGR',0,13,237,6,3
	.word	19878
	.byte	26
	.byte	'Ifx_DMA_CH_CHCSR',0,13,245,6,3
	.word	20312
	.byte	26
	.byte	'Ifx_DMA_CH_DADR',0,13,253,6,3
	.word	19289
	.byte	26
	.byte	'Ifx_DMA_CH_RDCRCR',0,13,133,7,3
	.word	19031
	.byte	26
	.byte	'Ifx_DMA_CH_SADR',0,13,141,7,3
	.word	19204
	.byte	26
	.byte	'Ifx_DMA_CH_SDCRCR',0,13,149,7,3
	.word	19119
	.byte	26
	.byte	'Ifx_DMA_CH_SHADR',0,13,157,7,3
	.word	19965
	.byte	26
	.byte	'Ifx_DMA_CLC',0,13,165,7,3
	.word	10662
	.byte	26
	.byte	'Ifx_DMA_ERRINTR',0,13,173,7,3
	.word	17775
	.byte	26
	.byte	'Ifx_DMA_HRR',0,13,181,7,3
	.word	18363
	.byte	26
	.byte	'Ifx_DMA_ID',0,13,189,7,3
	.word	10784
	.byte	26
	.byte	'Ifx_DMA_MEMCON',0,13,197,7,3
	.word	11061
	.byte	26
	.byte	'Ifx_DMA_MODE',0,13,205,7,3
	.word	18241
	.byte	26
	.byte	'Ifx_DMA_OTSS',0,13,213,7,3
	.word	17669
	.byte	26
	.byte	'Ifx_DMA_PRR0',0,13,221,7,3
	.word	17909
	.byte	26
	.byte	'Ifx_DMA_PRR1',0,13,229,7,3
	.word	18043
	.byte	26
	.byte	'Ifx_DMA_SUSACR',0,13,237,7,3
	.word	18608
	.byte	26
	.byte	'Ifx_DMA_SUSENR',0,13,245,7,3
	.word	18491
	.byte	26
	.byte	'Ifx_DMA_TIME',0,13,253,7,3
	.word	18126
	.byte	26
	.byte	'Ifx_DMA_TSR',0,13,133,8,3
	.word	18933
	.byte	14
	.word	17152
	.byte	26
	.byte	'Ifx_DMA_BLK_ME',0,13,165,8,3
	.word	30967
	.byte	14
	.word	17445
	.byte	26
	.byte	'Ifx_DMA_BLK',0,13,185,8,3
	.word	30996
	.byte	14
	.word	20352
	.byte	26
	.byte	'Ifx_DMA_CH',0,13,198,8,3
	.word	31022
	.byte	14
	.word	20518
	.byte	26
	.byte	'Ifx_DMA',0,13,250,8,3
	.word	31047
	.byte	26
	.byte	'IfxSrc_Tos',0,5,74,3
	.word	957
	.byte	26
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	538
	.byte	26
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	828
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	31135
	.byte	26
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	31167
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,8,0,14
	.word	31193
	.byte	26
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	31252
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	31280
	.byte	26
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	31317
	.byte	18,64
	.word	828
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	31345
	.byte	64,2,35,0,0,14
	.word	31354
	.byte	26
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	31386
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	31411
	.byte	26
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	31483
	.byte	18,8
	.word	828
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	31509
	.byte	8,2,35,0,0,14
	.word	31518
	.byte	26
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	31554
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	31584
	.byte	26
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	31657
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	31683
	.byte	26
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	31718
	.byte	18,192,1
	.word	828
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5771
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	31744
	.byte	192,1,2,35,16,0,14
	.word	31754
	.byte	26
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	31821
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	828
	.byte	4,2,35,4,0,14
	.word	31847
	.byte	26
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	31895
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	31923
	.byte	26
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	31956
	.byte	18,40
	.word	521
	.byte	19,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	31509
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	31509
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	31509
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	31509
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	828
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	828
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	31983
	.byte	40,2,35,40,0,14
	.word	31992
	.byte	26
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	32119
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	32146
	.byte	26
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	32178
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	32204
	.byte	26
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	32236
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	828
	.byte	4,2,35,8,0,14
	.word	32262
	.byte	26
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	32322
	.byte	18,16
	.word	521
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	32348
	.byte	16,2,35,16,0,14
	.word	32357
	.byte	26
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	32451
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	828
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	828
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4802
	.byte	24,2,35,24,0,14
	.word	32478
	.byte	26
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	32595
	.byte	18,12
	.word	828
	.byte	19,2,0,18,32
	.word	828
	.byte	19,7,0,18,32
	.word	32632
	.byte	19,0,0,18,88
	.word	521
	.byte	19,87,0,18,108
	.word	828
	.byte	19,26,0,18,96
	.word	32632
	.byte	19,2,0,18,160,3
	.word	521
	.byte	19,159,3,0,18,64
	.word	32632
	.byte	19,1,0,18,192,3
	.word	521
	.byte	19,191,3,0,18,16
	.word	828
	.byte	19,3,0,18,64
	.word	32708
	.byte	19,3,0,18,52
	.word	521
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	32623
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3612
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	828
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	828
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	31509
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	5431
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	32641
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	32650
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	32659
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	17561
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	828
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5771
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	32668
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	32677
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	32668
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	32677
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	32688
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	32697
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	32717
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	18413
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	32623
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	32726
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	32623
	.byte	12,3,35,192,18,0,14
	.word	32735
	.byte	26
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	33195
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	33221
	.byte	26
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	33254
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	33281
	.byte	26
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	33354
	.byte	18,56
	.word	521
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	828
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	33381
	.byte	56,2,35,24,0,14
	.word	33390
	.byte	26
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	33513
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	33539
	.byte	26
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	33571
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	828
	.byte	4,2,35,16,0,14
	.word	33597
	.byte	26
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	33682
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	33708
	.byte	26
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	33740
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	32632
	.byte	32,2,35,0,0,14
	.word	33766
	.byte	26
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	33799
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	32632
	.byte	32,2,35,0,0,14
	.word	33826
	.byte	26
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	33860
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	828
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	828
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	828
	.byte	4,2,35,20,0,14
	.word	33888
	.byte	26
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	33981
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	34008
	.byte	26
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	34040
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	32708
	.byte	16,2,35,4,0,14
	.word	34066
	.byte	26
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	34112
	.byte	18,24
	.word	828
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	34138
	.byte	24,2,35,0,0,14
	.word	34147
	.byte	26
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	34180
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	32623
	.byte	12,2,35,0,0,14
	.word	34207
	.byte	26
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	34239
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,0,14
	.word	34265
	.byte	26
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	34311
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	34337
	.byte	26
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	34412
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	828
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	828
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	828
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	828
	.byte	4,2,35,12,0,14
	.word	34441
	.byte	26
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	34515
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	828
	.byte	4,2,35,0,0,14
	.word	34543
	.byte	26
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	34577
	.byte	18,4
	.word	31135
	.byte	19,0,0,14
	.word	34604
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	34613
	.byte	4,2,35,0,0,14
	.word	34618
	.byte	26
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	34654
	.byte	18,48
	.word	31193
	.byte	19,3,0,14
	.word	34682
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	34691
	.byte	48,2,35,0,0,14
	.word	34696
	.byte	26
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	34736
	.byte	14
	.word	31280
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	34766
	.byte	4,2,35,0,0,14
	.word	34771
	.byte	26
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	34805
	.byte	18,64
	.word	31354
	.byte	19,0,0,14
	.word	34832
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	34841
	.byte	64,2,35,0,0,14
	.word	34846
	.byte	26
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	34880
	.byte	18,32
	.word	31411
	.byte	19,1,0,14
	.word	34907
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	34916
	.byte	32,2,35,0,0,14
	.word	34921
	.byte	26
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	34957
	.byte	14
	.word	31518
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	34985
	.byte	8,2,35,0,0,14
	.word	34990
	.byte	26
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	35034
	.byte	18,16
	.word	31584
	.byte	19,0,0,14
	.word	35066
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	35075
	.byte	16,2,35,0,0,14
	.word	35080
	.byte	26
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	35114
	.byte	18,8
	.word	31683
	.byte	19,1,0,14
	.word	35141
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	35150
	.byte	8,2,35,0,0,14
	.word	35155
	.byte	26
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	35189
	.byte	18,208,1
	.word	31754
	.byte	19,0,0,14
	.word	35216
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	35226
	.byte	208,1,2,35,0,0,14
	.word	35231
	.byte	26
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	35267
	.byte	14
	.word	31847
	.byte	14
	.word	31847
	.byte	14
	.word	31847
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	35294
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	5431
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	35299
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	35304
	.byte	8,2,35,24,0,14
	.word	35309
	.byte	26
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	35400
	.byte	18,4
	.word	31923
	.byte	19,0,0,14
	.word	35429
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	35438
	.byte	4,2,35,0,0,14
	.word	35443
	.byte	26
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	35479
	.byte	18,80
	.word	31992
	.byte	19,0,0,14
	.word	35507
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	35516
	.byte	80,2,35,0,0,14
	.word	35521
	.byte	26
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	35557
	.byte	18,4
	.word	32146
	.byte	19,0,0,14
	.word	35585
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	35594
	.byte	4,2,35,0,0,14
	.word	35599
	.byte	26
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	35633
	.byte	18,4
	.word	32204
	.byte	19,0,0,14
	.word	35660
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	35669
	.byte	4,2,35,0,0,14
	.word	35674
	.byte	26
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	35708
	.byte	18,12
	.word	32262
	.byte	19,0,0,14
	.word	35735
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	35744
	.byte	12,2,35,0,0,14
	.word	35749
	.byte	26
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	35783
	.byte	18,64
	.word	32357
	.byte	19,1,0,14
	.word	35810
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	35819
	.byte	64,2,35,0,0,14
	.word	35824
	.byte	26
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	35860
	.byte	18,48
	.word	32478
	.byte	19,0,0,14
	.word	35888
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	35897
	.byte	48,2,35,0,0,14
	.word	35902
	.byte	26
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	35940
	.byte	18,204,18
	.word	32735
	.byte	19,0,0,14
	.word	35969
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	35979
	.byte	204,18,2,35,0,0,14
	.word	35984
	.byte	26
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	36020
	.byte	18,4
	.word	33221
	.byte	19,0,0,14
	.word	36047
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	36056
	.byte	4,2,35,0,0,14
	.word	36061
	.byte	26
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	36097
	.byte	18,64
	.word	33281
	.byte	19,3,0,14
	.word	36125
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	36134
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	828
	.byte	4,2,35,64,0,14
	.word	36139
	.byte	26
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	36188
	.byte	18,80
	.word	33390
	.byte	19,0,0,14
	.word	36216
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	36225
	.byte	80,2,35,0,0,14
	.word	36230
	.byte	26
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	36264
	.byte	18,4
	.word	33539
	.byte	19,0,0,14
	.word	36291
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	36300
	.byte	4,2,35,0,0,14
	.word	36305
	.byte	26
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	36339
	.byte	18,40
	.word	33597
	.byte	19,1,0,14
	.word	36366
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	36375
	.byte	40,2,35,0,0,14
	.word	36380
	.byte	26
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	36414
	.byte	18,8
	.word	33708
	.byte	19,1,0,14
	.word	36441
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	36450
	.byte	8,2,35,0,0,14
	.word	36455
	.byte	26
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	36489
	.byte	18,32
	.word	33766
	.byte	19,0,0,14
	.word	36516
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	36525
	.byte	32,2,35,0,0,14
	.word	36530
	.byte	26
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	36566
	.byte	18,32
	.word	33826
	.byte	19,0,0,14
	.word	36594
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	36603
	.byte	32,2,35,0,0,14
	.word	36608
	.byte	26
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	36646
	.byte	18,96
	.word	33888
	.byte	19,3,0,14
	.word	36675
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	36684
	.byte	96,2,35,0,0,14
	.word	36689
	.byte	26
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	36725
	.byte	18,4
	.word	34008
	.byte	19,0,0,14
	.word	36753
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	36762
	.byte	4,2,35,0,0,14
	.word	36767
	.byte	26
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	36801
	.byte	14
	.word	34066
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	36828
	.byte	20,2,35,0,0,14
	.word	36833
	.byte	26
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	36867
	.byte	18,24
	.word	34147
	.byte	19,0,0,14
	.word	36894
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	36903
	.byte	24,2,35,0,0,14
	.word	36908
	.byte	26
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	36944
	.byte	18,12
	.word	34207
	.byte	19,0,0,14
	.word	36972
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	36981
	.byte	12,2,35,0,0,14
	.word	36986
	.byte	26
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	37020
	.byte	18,16
	.word	34265
	.byte	19,1,0,14
	.word	37047
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	37056
	.byte	16,2,35,0,0,14
	.word	37061
	.byte	26
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	37095
	.byte	18,64
	.word	34441
	.byte	19,3,0,14
	.word	37122
	.byte	18,224,1
	.word	521
	.byte	19,223,1,0,18,32
	.word	34337
	.byte	19,1,0,14
	.word	37147
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	37131
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	37136
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	37156
	.byte	32,3,35,160,2,0,14
	.word	37161
	.byte	26
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	37230
	.byte	14
	.word	34543
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	37258
	.byte	4,2,35,0,0,14
	.word	37263
	.byte	26
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	37299
	.byte	15,19,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,26
	.byte	'IfxScu_CCUCON0_CLKSEL',0,19,240,10,3
	.word	37327
	.byte	15,19,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,26
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	37424
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,7,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_ACCEN0_Bits',0,7,79,3
	.word	37546
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,7,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN1_Bits',0,7,85,3
	.word	38103
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,7,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,7,94,3
	.word	38180
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,7,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	521
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON0_Bits',0,7,111,3
	.word	38316
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,7,114,16,4,11
	.byte	'CANDIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	521
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON1_Bits',0,7,126,3
	.word	38596
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,7,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON2_Bits',0,7,135,1,3
	.word	38834
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,7,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	521
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	521
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON3_Bits',0,7,150,1,3
	.word	38962
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,7,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	521
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	521
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON4_Bits',0,7,165,1,3
	.word	39205
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,7,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON5_Bits',0,7,174,1,3
	.word	39440
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,7,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON6_Bits',0,7,181,1,3
	.word	39568
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,7,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON7_Bits',0,7,188,1,3
	.word	39668
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,7,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	521
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CHIPID_Bits',0,7,202,1,3
	.word	39768
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,7,205,1,16,4,11
	.byte	'PWD',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	498
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_DTSCON_Bits',0,7,213,1,3
	.word	39976
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,7,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1016
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1016
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_DTSLIM_Bits',0,7,225,1,3
	.word	40141
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,7,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1016
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,7,235,1,3
	.word	40324
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,7,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	498
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	521
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EICR_Bits',0,7,129,2,3
	.word	40478
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,7,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_EIFR_Bits',0,7,143,2,3
	.word	40842
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,7,146,2,16,4,11
	.byte	'POL',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1016
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	521
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_EMSR_Bits',0,7,159,2,3
	.word	41053
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,7,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1016
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	23,0,2,35,0,0,26
	.byte	'Ifx_SCU_ESRCFG_Bits',0,7,167,2,3
	.word	41305
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,7,170,2,16,4,11
	.byte	'ARI',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_ESROCFG_Bits',0,7,175,2,3
	.word	41423
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,7,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVR13CON_Bits',0,7,185,2,3
	.word	41534
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,7,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVR33CON_Bits',0,7,195,2,3
	.word	41697
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,7,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,7,205,2,3
	.word	41860
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,7,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,7,215,2,3
	.word	42018
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,7,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	521
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	521
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	521
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1016
	.byte	10,0,2,35,2,0,26
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,7,232,2,3
	.word	42183
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,7,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1016
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	521
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	521
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1016
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,7,245,2,3
	.word	42512
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,7,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVROVMON_Bits',0,7,255,2,3
	.word	42733
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,7,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,7,142,3,3
	.word	42896
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,7,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,7,152,3,3
	.word	43168
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,7,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,7,162,3,3
	.word	43321
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,7,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,7,172,3,3
	.word	43477
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,7,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,7,181,3,3
	.word	43639
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,7,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,7,191,3,3
	.word	43782
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,7,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,7,200,3,3
	.word	43947
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,7,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1016
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,7,211,3,3
	.word	44092
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,7,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	521
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,7,222,3,3
	.word	44273
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,7,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,7,232,3,3
	.word	44447
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,7,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,7,241,3,3
	.word	44607
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,7,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,7,130,4,3
	.word	44751
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,7,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,7,139,4,3
	.word	45025
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,7,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,7,149,4,3
	.word	45164
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,7,152,4,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	521
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1016
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	521
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	521
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_SCU_EXTCON_Bits',0,7,163,4,3
	.word	45327
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,7,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1016
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	521
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1016
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_FDR_Bits',0,7,174,4,3
	.word	45545
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,7,177,4,16,4,11
	.byte	'FS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_SCU_FMR_Bits',0,7,197,4,3
	.word	45708
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,7,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_ID_Bits',0,7,205,4,3
	.word	46044
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,7,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	521
	.byte	2,0,2,35,3,0,26
	.byte	'Ifx_SCU_IGCR_Bits',0,7,232,4,3
	.word	46151
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,7,235,4,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_IN_Bits',0,7,240,4,3
	.word	46603
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,7,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_IOCR_Bits',0,7,250,4,3
	.word	46702
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,7,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1016
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,7,131,5,3
	.word	46852
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,7,134,5,16,4,11
	.byte	'SEED',0,4
	.word	498
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,7,141,5,3
	.word	47001
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,7,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,7,149,5,3
	.word	47162
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,7,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1016
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1016
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_LCLCON_Bits',0,7,158,5,3
	.word	47292
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,7,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_LCLTEST_Bits',0,7,166,5,3
	.word	47424
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,7,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1016
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_MANID_Bits',0,7,174,5,3
	.word	47539
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,7,177,5,16,4,11
	.byte	'PS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1016
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1016
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_SCU_OMR_Bits',0,7,185,5,3
	.word	47650
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,7,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	521
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	521
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	521
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_OSCCON_Bits',0,7,209,5,3
	.word	47808
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,7,212,5,16,4,11
	.byte	'P0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_OUT_Bits',0,7,217,5,3
	.word	48220
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,7,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1016
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	6,0,2,35,3,0,26
	.byte	'Ifx_SCU_OVCCON_Bits',0,7,233,5,3
	.word	48321
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,7,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,7,242,5,3
	.word	48588
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,7,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDISC_Bits',0,7,250,5,3
	.word	48724
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,7,253,5,16,4,11
	.byte	'PD0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	521
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDR_Bits',0,7,132,6,3
	.word	48835
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,7,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDRR_Bits',0,7,146,6,3
	.word	48968
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,7,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1016
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PLLCON0_Bits',0,7,166,6,3
	.word	49171
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,7,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	521
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	521
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	521
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1016
	.byte	9,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLCON1_Bits',0,7,177,6,3
	.word	49527
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,7,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1016
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLCON2_Bits',0,7,184,6,3
	.word	49705
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,7,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1016
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	521
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,7,204,6,3
	.word	49805
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,7,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	521
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	521
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	521
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1016
	.byte	9,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,7,215,6,3
	.word	50175
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,7,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,7,227,6,3
	.word	50361
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,7,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,7,241,6,3
	.word	50559
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,7,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	521
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_SCU_PMCSR_Bits',0,7,251,6,3
	.word	50792
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,7,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	521
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	521
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,7,153,7,3
	.word	50944
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,7,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	521
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	521
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	521
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	521
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,7,170,7,3
	.word	51511
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,7,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	521
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	521
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,7,187,7,3
	.word	51805
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,7,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	521
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	521
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1016
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	521
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,7,214,7,3
	.word	52083
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,7,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1016
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,7,230,7,3
	.word	52579
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,7,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1016
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_RSTCON2_Bits',0,7,243,7,3
	.word	52892
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,7,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	521
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	521
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	521
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	521
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_RSTCON_Bits',0,7,129,8,3
	.word	53101
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,7,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	521
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	3,0,2,35,3,0,26
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,7,155,8,3
	.word	53312
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,7,158,8,16,4,11
	.byte	'HBT',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_SCU_SAFECON_Bits',0,7,162,8,3
	.word	53744
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,7,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	521
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	521
	.byte	7,0,2,35,3,0,26
	.byte	'Ifx_SCU_STSTAT_Bits',0,7,178,8,3
	.word	53840
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,7,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,7,186,8,3
	.word	54100
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,7,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	521
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	23,0,2,35,0,0,26
	.byte	'Ifx_SCU_SYSCON_Bits',0,7,198,8,3
	.word	54225
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,7,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,7,208,8,3
	.word	54422
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,7,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,7,218,8,3
	.word	54575
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,7,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSET_Bits',0,7,228,8,3
	.word	54728
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,7,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,7,238,8,3
	.word	54881
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,7,247,8,3
	.word	1131
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,7,134,9,3
	.word	1267
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,7,150,9,3
	.word	1511
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,7,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	1115
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,7,159,9,3
	.word	55136
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,7,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,7,175,9,3
	.word	55262
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,7,178,9,16,4,11
	.byte	'AE',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,7,191,9,3
	.word	55514
	.byte	12,7,199,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37546
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN0',0,7,204,9,3
	.word	55733
	.byte	12,7,207,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38103
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN1',0,7,212,9,3
	.word	55797
	.byte	12,7,215,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38180
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ARSTDIS',0,7,220,9,3
	.word	55861
	.byte	12,7,223,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38316
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON0',0,7,228,9,3
	.word	55926
	.byte	12,7,231,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38596
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON1',0,7,236,9,3
	.word	55991
	.byte	12,7,239,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38834
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON2',0,7,244,9,3
	.word	56056
	.byte	12,7,247,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38962
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON3',0,7,252,9,3
	.word	56121
	.byte	12,7,255,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39205
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON4',0,7,132,10,3
	.word	56186
	.byte	12,7,135,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39440
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON5',0,7,140,10,3
	.word	56251
	.byte	12,7,143,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39568
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON6',0,7,148,10,3
	.word	56316
	.byte	12,7,151,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39668
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON7',0,7,156,10,3
	.word	56381
	.byte	12,7,159,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39768
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CHIPID',0,7,164,10,3
	.word	56446
	.byte	12,7,167,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39976
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSCON',0,7,172,10,3
	.word	56510
	.byte	12,7,175,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40141
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSLIM',0,7,180,10,3
	.word	56574
	.byte	12,7,183,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40324
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSSTAT',0,7,188,10,3
	.word	56638
	.byte	12,7,191,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40478
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EICR',0,7,196,10,3
	.word	56703
	.byte	12,7,199,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40842
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EIFR',0,7,204,10,3
	.word	56765
	.byte	12,7,207,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41053
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EMSR',0,7,212,10,3
	.word	56827
	.byte	12,7,215,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41305
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ESRCFG',0,7,220,10,3
	.word	56889
	.byte	12,7,223,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41423
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ESROCFG',0,7,228,10,3
	.word	56953
	.byte	12,7,231,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41534
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVR13CON',0,7,236,10,3
	.word	57018
	.byte	12,7,239,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41697
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVR33CON',0,7,244,10,3
	.word	57084
	.byte	12,7,247,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41860
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRADCSTAT',0,7,252,10,3
	.word	57150
	.byte	12,7,255,10,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42018
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRDVSTAT',0,7,132,11,3
	.word	57218
	.byte	12,7,135,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42183
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRMONCTRL',0,7,140,11,3
	.word	57285
	.byte	12,7,143,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42512
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVROSCCTRL',0,7,148,11,3
	.word	57353
	.byte	12,7,151,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42733
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVROVMON',0,7,156,11,3
	.word	57421
	.byte	12,7,159,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42896
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRRSTCON',0,7,164,11,3
	.word	57487
	.byte	12,7,167,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43168
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,7,172,11,3
	.word	57554
	.byte	12,7,175,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43321
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,7,180,11,3
	.word	57623
	.byte	12,7,183,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43477
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,7,188,11,3
	.word	57692
	.byte	12,7,191,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43639
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,7,196,11,3
	.word	57761
	.byte	12,7,199,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43782
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,7,204,11,3
	.word	57830
	.byte	12,7,207,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43947
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,7,212,11,3
	.word	57899
	.byte	12,7,215,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44092
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL1',0,7,220,11,3
	.word	57968
	.byte	12,7,223,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44273
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL2',0,7,228,11,3
	.word	58036
	.byte	12,7,231,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44447
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL3',0,7,236,11,3
	.word	58104
	.byte	12,7,239,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44607
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL4',0,7,244,11,3
	.word	58172
	.byte	12,7,247,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44751
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSTAT',0,7,252,11,3
	.word	58240
	.byte	12,7,255,11,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45025
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRTRIM',0,7,132,12,3
	.word	58305
	.byte	12,7,135,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45164
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRUVMON',0,7,140,12,3
	.word	58370
	.byte	12,7,143,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45327
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EXTCON',0,7,148,12,3
	.word	58436
	.byte	12,7,151,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45545
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_FDR',0,7,156,12,3
	.word	58500
	.byte	12,7,159,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45708
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_FMR',0,7,164,12,3
	.word	58561
	.byte	12,7,167,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46044
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ID',0,7,172,12,3
	.word	58622
	.byte	12,7,175,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46151
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IGCR',0,7,180,12,3
	.word	58682
	.byte	12,7,183,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46603
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IN',0,7,188,12,3
	.word	58744
	.byte	12,7,191,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46702
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IOCR',0,7,196,12,3
	.word	58804
	.byte	12,7,199,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46852
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL0',0,7,204,12,3
	.word	58866
	.byte	12,7,207,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47001
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL1',0,7,212,12,3
	.word	58934
	.byte	12,7,215,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47162
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL2',0,7,220,12,3
	.word	59002
	.byte	12,7,223,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47292
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LCLCON',0,7,228,12,3
	.word	59070
	.byte	12,7,231,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47424
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LCLTEST',0,7,236,12,3
	.word	59134
	.byte	12,7,239,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47539
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_MANID',0,7,244,12,3
	.word	59199
	.byte	12,7,247,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47650
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OMR',0,7,252,12,3
	.word	59262
	.byte	12,7,255,12,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47808
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OSCCON',0,7,132,13,3
	.word	59323
	.byte	12,7,135,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48220
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OUT',0,7,140,13,3
	.word	59387
	.byte	12,7,143,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48321
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OVCCON',0,7,148,13,3
	.word	59448
	.byte	12,7,151,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48588
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OVCENABLE',0,7,156,13,3
	.word	59512
	.byte	12,7,159,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48724
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDISC',0,7,164,13,3
	.word	59579
	.byte	12,7,167,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48835
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDR',0,7,172,13,3
	.word	59642
	.byte	12,7,175,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48968
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDRR',0,7,180,13,3
	.word	59703
	.byte	12,7,183,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49171
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON0',0,7,188,13,3
	.word	59765
	.byte	12,7,191,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49527
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON1',0,7,196,13,3
	.word	59830
	.byte	12,7,199,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49705
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON2',0,7,204,13,3
	.word	59895
	.byte	12,7,207,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49805
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYCON0',0,7,212,13,3
	.word	59960
	.byte	12,7,215,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50175
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYCON1',0,7,220,13,3
	.word	60029
	.byte	12,7,223,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50361
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYSTAT',0,7,228,13,3
	.word	60098
	.byte	12,7,231,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50559
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLSTAT',0,7,236,13,3
	.word	60167
	.byte	12,7,239,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50792
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMCSR',0,7,244,13,3
	.word	60232
	.byte	12,7,247,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50944
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR0',0,7,252,13,3
	.word	60295
	.byte	12,7,255,13,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51511
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR1',0,7,132,14,3
	.word	60360
	.byte	12,7,135,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51805
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR2',0,7,140,14,3
	.word	60425
	.byte	12,7,143,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52083
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWSTAT',0,7,148,14,3
	.word	60490
	.byte	12,7,151,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52579
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWSTATCLR',0,7,156,14,3
	.word	60556
	.byte	12,7,159,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53101
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTCON',0,7,164,14,3
	.word	60625
	.byte	12,7,167,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52892
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTCON2',0,7,172,14,3
	.word	60689
	.byte	12,7,175,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53312
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTSTAT',0,7,180,14,3
	.word	60754
	.byte	12,7,183,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53744
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SAFECON',0,7,188,14,3
	.word	60819
	.byte	12,7,191,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53840
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_STSTAT',0,7,196,14,3
	.word	60884
	.byte	12,7,199,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54100
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SWRSTCON',0,7,204,14,3
	.word	60948
	.byte	12,7,207,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54225
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SYSCON',0,7,212,14,3
	.word	61014
	.byte	12,7,215,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54422
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPCLR',0,7,220,14,3
	.word	61078
	.byte	12,7,223,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54575
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPDIS',0,7,228,14,3
	.word	61143
	.byte	12,7,231,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54728
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSET',0,7,236,14,3
	.word	61208
	.byte	12,7,239,14,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54881
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSTAT',0,7,244,14,3
	.word	61273
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON0',0,7,252,14,3
	.word	1227
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON1',0,7,132,15,3
	.word	1471
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_SR',0,7,140,15,3
	.word	1702
	.byte	12,7,143,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55136
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_CON0',0,7,148,15,3
	.word	61424
	.byte	12,7,151,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55262
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_CON1',0,7,156,15,3
	.word	61491
	.byte	12,7,159,15,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55514
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_WDTS_SR',0,7,164,15,3
	.word	61558
	.byte	14
	.word	1742
	.byte	26
	.byte	'Ifx_SCU_WDTCPU',0,7,180,15,3
	.word	61623
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,7,183,15,25,12,13
	.byte	'CON0',0
	.word	61424
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	61491
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	61558
	.byte	4,2,35,8,0,14
	.word	61652
	.byte	26
	.byte	'Ifx_SCU_WDTS',0,7,188,15,3
	.word	61713
	.byte	18,8
	.word	56889
	.byte	19,1,0,18,8
	.word	60232
	.byte	19,1,0,14
	.word	61652
	.byte	18,24
	.word	1742
	.byte	19,1,0,14
	.word	61763
	.byte	18,16
	.word	56703
	.byte	19,3,0,18,16
	.word	58682
	.byte	19,3,0,18,180,3
	.word	521
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,7,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	5431
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	58622
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3612
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	59323
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	60167
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	59765
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	59830
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	59895
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	60098
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	59960
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	60029
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	55926
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	55991
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	58500
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	58436
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	56056
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	56121
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	56186
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	56251
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	60754
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3612
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	60625
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	55861
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	60948
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	60689
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3612
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	57487
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	61740
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	56953
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	61014
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	56316
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	56381
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	10824
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	59642
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	58804
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	59387
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	59262
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	58744
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	58240
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	57218
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	57018
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	57084
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	60884
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3612
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	60295
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	60490
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	60556
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	61749
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3612
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	56638
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	56510
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	60360
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	60425
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	61758
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	56827
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	61772
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5771
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	61273
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	61208
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	61078
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	61143
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3612
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	59070
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	59134
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	56446
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	59199
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	5431
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	60819
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	32348
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	58866
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	58934
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	59002
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	11101
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	59579
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	5431
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	58305
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	57150
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	58370
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	57421
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	57285
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3612
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	57968
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	58036
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	58104
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	58172
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	57554
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	57623
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	57692
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	57761
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	57830
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	57899
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	57353
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3612
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	59512
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	59448
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	31983
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	61777
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	56765
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	58561
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	59703
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	61786
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3612
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	56574
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	61795
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	55797
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	55733
	.byte	4,3,35,252,7,0,14
	.word	61806
	.byte	26
	.byte	'Ifx_SCU',0,7,181,16,3
	.word	63796
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,20,45,16,4,11
	.byte	'ADDR',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_A_Bits',0,20,48,3
	.word	63818
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,20,51,16,4,11
	.byte	'VSS',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	1115
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_BIV_Bits',0,20,55,3
	.word	63879
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,20,58,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	1115
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_BTV_Bits',0,20,62,3
	.word	63958
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,20,65,16,4,11
	.byte	'CountValue',0,4
	.word	1115
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_CCNT_Bits',0,20,69,3
	.word	64044
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,20,72,16,4,11
	.byte	'CM',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	1115
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	1115
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	1115
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1115
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_CPU_CCTRL_Bits',0,20,80,3
	.word	64133
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,20,83,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1115
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_COMPAT_Bits',0,20,89,3
	.word	64279
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,20,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1115
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CORE_ID_Bits',0,20,96,3
	.word	64406
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,20,99,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	1115
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_L_Bits',0,20,103,3
	.word	64504
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,20,106,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	1115
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_U_Bits',0,20,110,3
	.word	64597
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,20,113,16,4,11
	.byte	'MODREV',0,4
	.word	1115
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	1115
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPU_ID_Bits',0,20,118,3
	.word	64690
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,20,121,16,4,11
	.byte	'XE',0,4
	.word	1115
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1115
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPXE_Bits',0,20,125,3
	.word	64797
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,20,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1115
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1115
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1115
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_CREVT_Bits',0,20,136,1,3
	.word	64884
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,20,139,1,16,4,11
	.byte	'CID',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1115
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CUS_ID_Bits',0,20,143,1,3
	.word	65038
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,20,146,1,16,4,11
	.byte	'DATA',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_D_Bits',0,20,149,1,3
	.word	65132
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,20,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1115
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	1115
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	1115
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1115
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	1115
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	1115
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DATR_Bits',0,20,163,1,3
	.word	65195
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,20,166,1,16,4,11
	.byte	'DE',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	1115
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1115
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	1115
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	1115
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	1115
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	1115
	.byte	19,0,2,35,0,0,26
	.byte	'Ifx_CPU_DBGSR_Bits',0,20,177,1,3
	.word	65413
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,20,180,1,16,4,11
	.byte	'DTA',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1115
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_DBGTCR_Bits',0,20,184,1,3
	.word	65628
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,20,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1115
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCON0_Bits',0,20,192,1,3
	.word	65722
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,20,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCON2_Bits',0,20,199,1,3
	.word	65838
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,20,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	1115
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCX_Bits',0,20,206,1,3
	.word	65939
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,20,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_DEADD_Bits',0,20,212,1,3
	.word	66032
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,20,215,1,16,4,11
	.byte	'TA',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_DIEAR_Bits',0,20,218,1,3
	.word	66112
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,20,221,1,16,4,11
	.byte	'IED',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	1115
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	1115
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	1115
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	1115
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1115
	.byte	18,0,2,35,0,0,26
	.byte	'Ifx_CPU_DIETR_Bits',0,20,233,1,3
	.word	66181
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,20,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	1115
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_DMS_Bits',0,20,240,1,3
	.word	66410
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,20,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	1115
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_L_Bits',0,20,247,1,3
	.word	66503
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,20,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	1115
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_U_Bits',0,20,254,1,3
	.word	66598
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,20,129,2,16,4,11
	.byte	'RE',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPRE_Bits',0,20,133,2,3
	.word	66693
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,20,136,2,16,4,11
	.byte	'WE',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPWE_Bits',0,20,140,2,3
	.word	66783
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,20,143,2,16,4,11
	.byte	'SRE',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1115
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	1115
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	1115
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	1115
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	1115
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	1115
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	1115
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	1115
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	1115
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	1115
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	1115
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	1115
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1115
	.byte	7,0,2,35,0,0,26
	.byte	'Ifx_CPU_DSTR_Bits',0,20,161,2,3
	.word	66873
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,20,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1115
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1115
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1115
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_EXEVT_Bits',0,20,172,2,3
	.word	67197
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,20,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	1115
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1115
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_FCX_Bits',0,20,180,2,3
	.word	67351
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,20,183,2,16,4,11
	.byte	'TST',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1115
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	1115
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1115
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	1115
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	1115
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	1115
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	1115
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	1115
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	1115
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	1115
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	1115
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	1115
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	1115
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	1115
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,20,202,2,3
	.word	67457
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,205,2,16,4,11
	.byte	'OPC',0,4
	.word	1115
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	1115
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	1115
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	1115
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1115
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,20,212,2,3
	.word	67806
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,20,215,2,16,4,11
	.byte	'PC',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,20,218,2,3
	.word	67966
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,20,224,2,3
	.word	68047
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,20,230,2,3
	.word	68134
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,20,236,2,3
	.word	68221
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,20,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	1115
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_ICNT_Bits',0,20,243,2,3
	.word	68308
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,20,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	1115
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1115
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	1115
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	1115
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	1115
	.byte	6,0,2,35,0,0,26
	.byte	'Ifx_CPU_ICR_Bits',0,20,253,2,3
	.word	68399
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,20,128,3,16,4,11
	.byte	'ISP',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_ISP_Bits',0,20,131,3,3
	.word	68542
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,20,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	1115
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1115
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_LCX_Bits',0,20,139,3,3
	.word	68608
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,20,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	1115
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M1CNT_Bits',0,20,146,3,3
	.word	68714
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,20,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	1115
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M2CNT_Bits',0,20,153,3,3
	.word	68807
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,20,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	1115
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M3CNT_Bits',0,20,160,3,3
	.word	68900
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,20,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	1115
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_PC_Bits',0,20,167,3,3
	.word	68993
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,20,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1115
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON0_Bits',0,20,175,3,3
	.word	69078
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,20,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1115
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON1_Bits',0,20,183,3,3
	.word	69194
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,20,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON2_Bits',0,20,190,3,3
	.word	69305
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,20,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	1115
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	1115
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	1115
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	1115
	.byte	10,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCXI_Bits',0,20,200,3,3
	.word	69406
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,20,203,3,16,4,11
	.byte	'TA',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_PIEAR_Bits',0,20,206,3,3
	.word	69536
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,20,209,3,16,4,11
	.byte	'IED',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	1115
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	1115
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	1115
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	1115
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1115
	.byte	18,0,2,35,0,0,26
	.byte	'Ifx_CPU_PIETR_Bits',0,20,221,3,3
	.word	69605
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,20,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	1115
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA0_Bits',0,20,229,3,3
	.word	69834
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,20,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1115
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	1115
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA1_Bits',0,20,237,3,3
	.word	69947
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,20,240,3,16,4,11
	.byte	'PSI',0,4
	.word	1115
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1115
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA2_Bits',0,20,244,3,3
	.word	70060
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,20,247,3,16,4,11
	.byte	'FRE',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1115
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	1115
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	1115
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	1115
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	1115
	.byte	17,0,2,35,0,0,26
	.byte	'Ifx_CPU_PSTR_Bits',0,20,129,4,3
	.word	70151
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,20,132,4,16,4,11
	.byte	'CDC',0,4
	.word	1115
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	1115
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	1115
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	1115
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	1115
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	1115
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	1115
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	1115
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	1115
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	1115
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	1115
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	1115
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_PSW_Bits',0,20,147,4,3
	.word	70354
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,20,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	1115
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	1115
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1115
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	1115
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_SEGEN_Bits',0,20,156,4,3
	.word	70597
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,20,159,4,16,4,11
	.byte	'PC',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1115
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	1115
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	1115
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	1115
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1115
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	1115
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1115
	.byte	7,0,2,35,0,0,26
	.byte	'Ifx_CPU_SMACON_Bits',0,20,171,4,3
	.word	70725
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,20,174,4,16,4,11
	.byte	'EN',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,20,177,4,3
	.word	70966
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,20,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,20,183,4,3
	.word	71049
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,186,4,16,4,11
	.byte	'EN',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,20,189,4,3
	.word	71140
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,20,195,4,3
	.word	71231
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,20,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,20,202,4,3
	.word	71330
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,20,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,20,209,4,3
	.word	71437
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,20,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1115
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1115
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1115
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_SWEVT_Bits',0,20,220,4,3
	.word	71544
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,20,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1115
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SYSCON_Bits',0,20,231,4,3
	.word	71698
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,20,234,4,16,4,11
	.byte	'ASI',0,4
	.word	1115
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1115
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,20,238,4,3
	.word	71859
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,20,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1115
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	1115
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	1115
	.byte	15,0,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_CON_Bits',0,20,249,4,3
	.word	71957
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,20,252,4,16,4,11
	.byte	'Timer',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,20,255,4,3
	.word	72129
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,20,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	1115
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_TR_ADR_Bits',0,20,133,5,3
	.word	72209
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,20,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	1115
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	1115
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	1115
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1115
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	1115
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	1115
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1115
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	1115
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	1115
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	1115
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	1115
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	1115
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	1115
	.byte	3,0,2,35,0,0,26
	.byte	'Ifx_CPU_TR_EVT_Bits',0,20,153,5,3
	.word	72282
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,20,156,5,16,4,11
	.byte	'T0',0,4
	.word	1115
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	1115
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	1115
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	1115
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	1115
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	1115
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	1115
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	1115
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1115
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,20,167,5,3
	.word	72600
	.byte	12,20,175,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63818
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_A',0,20,180,5,3
	.word	72795
	.byte	12,20,183,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63879
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_BIV',0,20,188,5,3
	.word	72854
	.byte	12,20,191,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63958
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_BTV',0,20,196,5,3
	.word	72915
	.byte	12,20,199,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64044
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CCNT',0,20,204,5,3
	.word	72976
	.byte	12,20,207,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64133
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CCTRL',0,20,212,5,3
	.word	73038
	.byte	12,20,215,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64279
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_COMPAT',0,20,220,5,3
	.word	73101
	.byte	12,20,223,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64406
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CORE_ID',0,20,228,5,3
	.word	73165
	.byte	12,20,231,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64504
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_L',0,20,236,5,3
	.word	73230
	.byte	12,20,239,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64597
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_U',0,20,244,5,3
	.word	73293
	.byte	12,20,247,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64690
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPU_ID',0,20,252,5,3
	.word	73356
	.byte	12,20,255,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64797
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPXE',0,20,132,6,3
	.word	73420
	.byte	12,20,135,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64884
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CREVT',0,20,140,6,3
	.word	73482
	.byte	12,20,143,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65038
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CUS_ID',0,20,148,6,3
	.word	73545
	.byte	12,20,151,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65132
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_D',0,20,156,6,3
	.word	73609
	.byte	12,20,159,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65195
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DATR',0,20,164,6,3
	.word	73668
	.byte	12,20,167,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65413
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DBGSR',0,20,172,6,3
	.word	73730
	.byte	12,20,175,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65628
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DBGTCR',0,20,180,6,3
	.word	73793
	.byte	12,20,183,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65722
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCON0',0,20,188,6,3
	.word	73857
	.byte	12,20,191,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65838
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCON2',0,20,196,6,3
	.word	73920
	.byte	12,20,199,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65939
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCX',0,20,204,6,3
	.word	73983
	.byte	12,20,207,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66032
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DEADD',0,20,212,6,3
	.word	74044
	.byte	12,20,215,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66112
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DIEAR',0,20,220,6,3
	.word	74107
	.byte	12,20,223,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66181
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DIETR',0,20,228,6,3
	.word	74170
	.byte	12,20,231,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66410
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DMS',0,20,236,6,3
	.word	74233
	.byte	12,20,239,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66503
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_L',0,20,244,6,3
	.word	74294
	.byte	12,20,247,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66598
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_U',0,20,252,6,3
	.word	74357
	.byte	12,20,255,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66693
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPRE',0,20,132,7,3
	.word	74420
	.byte	12,20,135,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66783
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPWE',0,20,140,7,3
	.word	74482
	.byte	12,20,143,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66873
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DSTR',0,20,148,7,3
	.word	74544
	.byte	12,20,151,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67197
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_EXEVT',0,20,156,7,3
	.word	74606
	.byte	12,20,159,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67351
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FCX',0,20,164,7,3
	.word	74669
	.byte	12,20,167,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67457
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,20,172,7,3
	.word	74730
	.byte	12,20,175,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67806
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,20,180,7,3
	.word	74800
	.byte	12,20,183,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67966
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,20,188,7,3
	.word	74870
	.byte	12,20,191,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68047
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,20,196,7,3
	.word	74939
	.byte	12,20,199,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68134
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,20,204,7,3
	.word	75010
	.byte	12,20,207,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68221
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,20,212,7,3
	.word	75081
	.byte	12,20,215,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68308
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ICNT',0,20,220,7,3
	.word	75152
	.byte	12,20,223,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68399
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ICR',0,20,228,7,3
	.word	75214
	.byte	12,20,231,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68542
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ISP',0,20,236,7,3
	.word	75275
	.byte	12,20,239,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68608
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_LCX',0,20,244,7,3
	.word	75336
	.byte	12,20,247,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68714
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M1CNT',0,20,252,7,3
	.word	75397
	.byte	12,20,255,7,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68807
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M2CNT',0,20,132,8,3
	.word	75460
	.byte	12,20,135,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68900
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M3CNT',0,20,140,8,3
	.word	75523
	.byte	12,20,143,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68993
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PC',0,20,148,8,3
	.word	75586
	.byte	12,20,151,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69078
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON0',0,20,156,8,3
	.word	75646
	.byte	12,20,159,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69194
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON1',0,20,164,8,3
	.word	75709
	.byte	12,20,167,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69305
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON2',0,20,172,8,3
	.word	75772
	.byte	12,20,175,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69406
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCXI',0,20,180,8,3
	.word	75835
	.byte	12,20,183,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69536
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PIEAR',0,20,188,8,3
	.word	75897
	.byte	12,20,191,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69605
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PIETR',0,20,196,8,3
	.word	75960
	.byte	12,20,199,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69834
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA0',0,20,204,8,3
	.word	76023
	.byte	12,20,207,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69947
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA1',0,20,212,8,3
	.word	76085
	.byte	12,20,215,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70060
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA2',0,20,220,8,3
	.word	76147
	.byte	12,20,223,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70151
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PSTR',0,20,228,8,3
	.word	76209
	.byte	12,20,231,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70354
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PSW',0,20,236,8,3
	.word	76271
	.byte	12,20,239,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70597
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SEGEN',0,20,244,8,3
	.word	76332
	.byte	12,20,247,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70725
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SMACON',0,20,252,8,3
	.word	76395
	.byte	12,20,255,8,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70966
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENA',0,20,132,9,3
	.word	76459
	.byte	12,20,135,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71049
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENB',0,20,140,9,3
	.word	76529
	.byte	12,20,143,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71140
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,20,148,9,3
	.word	76599
	.byte	12,20,151,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71231
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,20,156,9,3
	.word	76673
	.byte	12,20,159,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71330
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,20,164,9,3
	.word	76747
	.byte	12,20,167,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71437
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,20,172,9,3
	.word	76817
	.byte	12,20,175,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71544
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SWEVT',0,20,180,9,3
	.word	76887
	.byte	12,20,183,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71698
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SYSCON',0,20,188,9,3
	.word	76950
	.byte	12,20,191,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71859
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TASK_ASI',0,20,196,9,3
	.word	77014
	.byte	12,20,199,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71957
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_CON',0,20,204,9,3
	.word	77080
	.byte	12,20,207,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72129
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_TIMER',0,20,212,9,3
	.word	77145
	.byte	12,20,215,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72209
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TR_ADR',0,20,220,9,3
	.word	77212
	.byte	12,20,223,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72282
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TR_EVT',0,20,228,9,3
	.word	77276
	.byte	12,20,231,9,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72600
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TRIG_ACC',0,20,236,9,3
	.word	77340
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,20,247,9,25,8,13
	.byte	'L',0
	.word	73230
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	73293
	.byte	4,2,35,4,0,14
	.word	77406
	.byte	26
	.byte	'Ifx_CPU_CPR',0,20,251,9,3
	.word	77448
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,20,254,9,25,8,13
	.byte	'L',0
	.word	74294
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	74357
	.byte	4,2,35,4,0,14
	.word	77474
	.byte	26
	.byte	'Ifx_CPU_DPR',0,20,130,10,3
	.word	77516
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,20,133,10,25,16,13
	.byte	'LA',0
	.word	76747
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	76817
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	76599
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	76673
	.byte	4,2,35,12,0,14
	.word	77542
	.byte	26
	.byte	'Ifx_CPU_SPROT_RGN',0,20,139,10,3
	.word	77624
	.byte	18,12
	.word	77145
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,20,142,10,25,16,13
	.byte	'CON',0
	.word	77080
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	77656
	.byte	12,2,35,4,0,14
	.word	77665
	.byte	26
	.byte	'Ifx_CPU_TPS',0,20,146,10,3
	.word	77713
	.byte	10
	.byte	'_Ifx_CPU_TR',0,20,149,10,25,8,13
	.byte	'EVT',0
	.word	77276
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	77212
	.byte	4,2,35,4,0,14
	.word	77739
	.byte	26
	.byte	'Ifx_CPU_TR',0,20,153,10,3
	.word	77784
	.byte	18,176,32
	.word	521
	.byte	19,175,32,0,18,208,223,1
	.word	521
	.byte	19,207,223,1,0,18,248,1
	.word	521
	.byte	19,247,1,0,18,244,29
	.word	521
	.byte	19,243,29,0,18,188,3
	.word	521
	.byte	19,187,3,0,18,232,3
	.word	521
	.byte	19,231,3,0,18,252,23
	.word	521
	.byte	19,251,23,0,18,228,63
	.word	521
	.byte	19,227,63,0,18,128,1
	.word	77474
	.byte	19,15,0,14
	.word	77899
	.byte	18,64
	.word	77406
	.byte	19,7,0,14
	.word	77914
	.byte	18,192,31
	.word	521
	.byte	19,191,31,0,18,16
	.word	73420
	.byte	19,3,0,18,16
	.word	74420
	.byte	19,3,0,18,16
	.word	74482
	.byte	19,3,0,18,208,7
	.word	521
	.byte	19,207,7,0,14
	.word	77665
	.byte	18,240,23
	.word	521
	.byte	19,239,23,0,18,64
	.word	77739
	.byte	19,7,0,14
	.word	77993
	.byte	18,192,23
	.word	521
	.byte	19,191,23,0,18,232,1
	.word	521
	.byte	19,231,1,0,18,180,1
	.word	521
	.byte	19,179,1,0,18,172,1
	.word	521
	.byte	19,171,1,0,18,64
	.word	73609
	.byte	19,15,0,18,64
	.word	521
	.byte	19,63,0,18,64
	.word	72795
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,20,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	77809
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	76332
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	77820
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	77014
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	77833
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	76023
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	76085
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	76147
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	77844
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	73920
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	5431
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	76395
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	74544
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3612
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	73668
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	74044
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	74107
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	74170
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4802
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	73857
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	77855
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	76209
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	75709
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	75772
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	75646
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	75897
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	75960
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	77866
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	73101
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	77877
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	74730
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	74870
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	74800
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3612
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	74939
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	75010
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	75081
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	77888
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	77909
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	17545
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	77923
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	77928
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	77939
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	77948
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	77957
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	77966
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	77977
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	77982
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	78002
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	78007
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	73038
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	72976
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	75152
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	75397
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	75460
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	75523
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	78018
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	73730
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3612
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	74606
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	73482
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	76887
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	11101
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	77340
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5771
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	74233
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	73983
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	73793
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	78029
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	75835
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	76271
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	75586
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	5431
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	76950
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	73356
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	73165
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	72854
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	72915
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	75275
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	75214
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	5431
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	74669
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	75336
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	32348
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	73545
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	78040
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	78051
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	78060
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	78069
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	78060
	.byte	64,4,35,192,255,3,0,14
	.word	78078
	.byte	26
	.byte	'Ifx_CPU',0,20,130,11,3
	.word	79869
	.byte	15,9,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,26
	.byte	'IfxCpu_Id',0,9,132,1,3
	.word	79891
	.byte	26
	.byte	'IfxCpu_ResourceCpu',0,9,161,1,3
	.word	2220
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,21,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_STM_ACCEN0_Bits',0,21,79,3
	.word	79989
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,21,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN1_Bits',0,21,85,3
	.word	80546
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,21,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CAP_Bits',0,21,91,3
	.word	80623
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,21,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CAPSV_Bits',0,21,97,3
	.word	80695
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,21,100,16,4,11
	.byte	'DISR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_STM_CLC_Bits',0,21,107,3
	.word	80771
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,21,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	521
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	521
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	521
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	521
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	521
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	3,0,2,35,3,0,26
	.byte	'Ifx_STM_CMCON_Bits',0,21,120,3
	.word	80912
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,21,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CMP_Bits',0,21,126,3
	.word	81130
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,21,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	498
	.byte	25,0,2,35,0,0,26
	.byte	'Ifx_STM_ICR_Bits',0,21,139,1,3
	.word	81197
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,21,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_STM_ID_Bits',0,21,147,1,3
	.word	81400
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,21,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_STM_ISCR_Bits',0,21,157,1,3
	.word	81507
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,21,160,1,16,4,11
	.byte	'RST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_STM_KRST0_Bits',0,21,165,1,3
	.word	81658
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,21,168,1,16,4,11
	.byte	'RST',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_STM_KRST1_Bits',0,21,172,1,3
	.word	81769
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,21,175,1,16,4,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_STM_KRSTCLR_Bits',0,21,179,1,3
	.word	81861
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,21,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	521
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	521
	.byte	2,0,2,35,3,0,26
	.byte	'Ifx_STM_OCS_Bits',0,21,189,1,3
	.word	81957
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,21,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM0_Bits',0,21,195,1,3
	.word	82103
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,21,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM0SV_Bits',0,21,201,1,3
	.word	82175
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,21,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM1_Bits',0,21,207,1,3
	.word	82251
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,21,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM2_Bits',0,21,213,1,3
	.word	82323
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,21,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM3_Bits',0,21,219,1,3
	.word	82395
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,21,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM4_Bits',0,21,225,1,3
	.word	82468
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,21,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM5_Bits',0,21,231,1,3
	.word	82541
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,21,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM6_Bits',0,21,237,1,3
	.word	82614
	.byte	12,21,245,1,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79989
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN0',0,21,250,1,3
	.word	82687
	.byte	12,21,253,1,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80546
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN1',0,21,130,2,3
	.word	82751
	.byte	12,21,133,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80623
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CAP',0,21,138,2,3
	.word	82815
	.byte	12,21,141,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80695
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CAPSV',0,21,146,2,3
	.word	82876
	.byte	12,21,149,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80771
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CLC',0,21,154,2,3
	.word	82939
	.byte	12,21,157,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80912
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CMCON',0,21,162,2,3
	.word	83000
	.byte	12,21,165,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81130
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CMP',0,21,170,2,3
	.word	83063
	.byte	12,21,173,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81197
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ICR',0,21,178,2,3
	.word	83124
	.byte	12,21,181,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81400
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ID',0,21,186,2,3
	.word	83185
	.byte	12,21,189,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81507
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ISCR',0,21,194,2,3
	.word	83245
	.byte	12,21,197,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81658
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRST0',0,21,202,2,3
	.word	83307
	.byte	12,21,205,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81769
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRST1',0,21,210,2,3
	.word	83370
	.byte	12,21,213,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81861
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRSTCLR',0,21,218,2,3
	.word	83433
	.byte	12,21,221,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81957
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_OCS',0,21,226,2,3
	.word	83498
	.byte	12,21,229,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82103
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM0',0,21,234,2,3
	.word	83559
	.byte	12,21,237,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82175
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM0SV',0,21,242,2,3
	.word	83621
	.byte	12,21,245,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82251
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM1',0,21,250,2,3
	.word	83685
	.byte	12,21,253,2,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82323
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM2',0,21,130,3,3
	.word	83747
	.byte	12,21,133,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82395
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM3',0,21,138,3,3
	.word	83809
	.byte	12,21,141,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82468
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM4',0,21,146,3,3
	.word	83871
	.byte	12,21,149,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82541
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM5',0,21,154,3,3
	.word	83933
	.byte	12,21,157,3,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82614
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM6',0,21,162,3,3
	.word	83995
	.byte	15,8,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,26
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	84057
	.byte	25,8,160,1,9,6,13
	.byte	'counter',0
	.word	2375
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	521
	.byte	1,2,35,4,0,26
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	84146
	.byte	25,8,172,1,9,32,13
	.byte	'instruction',0
	.word	84146
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	84146
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	84146
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	84146
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	84146
	.byte	6,2,35,24,0,26
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	84212
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,22,45,16,4,11
	.byte	'EN0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,22,79,3
	.word	84330
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,22,82,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,22,85,3
	.word	84891
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,22,88,16,4,11
	.byte	'SEL',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,22,95,3
	.word	84972
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,22,98,16,4,11
	.byte	'VLD0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,22,111,3
	.word	85125
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,22,114,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	521
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,22,121,3
	.word	85373
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,22,124,16,4,11
	.byte	'STATUS',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM0_Bits',0,22,128,1,3
	.word	85519
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,22,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_COMM1_Bits',0,22,136,1,3
	.word	85617
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,22,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_COMM2_Bits',0,22,144,1,3
	.word	85733
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,22,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1016
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCRD_Bits',0,22,153,1,3
	.word	85849
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,22,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1016
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCRP_Bits',0,22,162,1,3
	.word	85989
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,22,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	498
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1016
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCW_Bits',0,22,171,1,3
	.word	86129
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,22,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	521
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	521
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1016
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	521
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	521
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FCON_Bits',0,22,193,1,3
	.word	86268
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,22,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	521
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	521
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	521
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FPRO_Bits',0,22,218,1,3
	.word	86630
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,22,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1016
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	521
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	521
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	521
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FSR_Bits',0,22,254,1,3
	.word	87071
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,22,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	521
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	521
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_ID_Bits',0,22,134,2,3
	.word	87677
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,22,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1016
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_MARD_Bits',0,22,147,2,3
	.word	87788
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,22,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1016
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_MARP_Bits',0,22,159,2,3
	.word	88002
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,22,162,2,16,4,11
	.byte	'L',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	521
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	521
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1016
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	521
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCOND_Bits',0,22,179,2,3
	.word	88189
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,22,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	521
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,22,188,2,3
	.word	88513
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,22,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1016
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,22,199,2,3
	.word	88656
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,22,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1016
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	521
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	521
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	521
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1016
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,22,219,2,3
	.word	88845
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,22,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	521
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,22,254,2,3
	.word	89208
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,22,129,3,16,4,11
	.byte	'S0L',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONP_Bits',0,22,160,3,3
	.word	89803
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,22,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	521
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	521
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	521
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	521
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	521
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	521
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	521
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	521
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	521
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	521
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	521
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	521
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	521
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	521
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	521
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	521
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	521
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	521
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	521
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	521
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	521
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,22,194,3,3
	.word	90327
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,22,197,3,16,4,11
	.byte	'TAG',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,22,201,3,3
	.word	90909
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,22,204,3,16,4,11
	.byte	'TAG',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,22,208,3,3
	.word	91011
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,22,211,3,16,4,11
	.byte	'TAG',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	498
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,22,215,3,3
	.word	91113
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,22,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	498
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRAD_Bits',0,22,222,3,3
	.word	91215
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,22,225,3,16,4,11
	.byte	'STRT',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	521
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	521
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	521
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	521
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	521
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	521
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1016
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_RRCT_Bits',0,22,236,3,3
	.word	91309
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,22,239,3,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD0_Bits',0,22,242,3,3
	.word	91519
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,22,245,3,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD1_Bits',0,22,248,3,3
	.word	91592
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,22,251,3,16,4,11
	.byte	'SEL',0,1
	.word	521
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	521
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	521
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,22,130,4,3
	.word	91665
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,22,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	521
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,22,137,4,3
	.word	91820
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,22,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	521
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	498
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	521
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	521
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	521
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,22,147,4,3
	.word	91925
	.byte	12,22,155,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84330
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN0',0,22,160,4,3
	.word	92073
	.byte	12,22,163,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84891
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN1',0,22,168,4,3
	.word	92139
	.byte	12,22,171,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84972
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_CFG',0,22,176,4,3
	.word	92205
	.byte	12,22,179,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85125
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_STAT',0,22,184,4,3
	.word	92273
	.byte	12,22,187,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85373
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_TOP',0,22,192,4,3
	.word	92342
	.byte	12,22,195,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85519
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM0',0,22,200,4,3
	.word	92410
	.byte	12,22,203,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85617
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM1',0,22,208,4,3
	.word	92475
	.byte	12,22,211,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85733
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM2',0,22,216,4,3
	.word	92540
	.byte	12,22,219,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85849
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCRD',0,22,224,4,3
	.word	92605
	.byte	12,22,227,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	85989
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCRP',0,22,232,4,3
	.word	92670
	.byte	12,22,235,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86129
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCW',0,22,240,4,3
	.word	92735
	.byte	12,22,243,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86268
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FCON',0,22,248,4,3
	.word	92799
	.byte	12,22,251,4,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	86630
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FPRO',0,22,128,5,3
	.word	92863
	.byte	12,22,131,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87071
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FSR',0,22,136,5,3
	.word	92927
	.byte	12,22,139,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87677
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ID',0,22,144,5,3
	.word	92990
	.byte	12,22,147,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	87788
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_MARD',0,22,152,5,3
	.word	93052
	.byte	12,22,155,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88002
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_MARP',0,22,160,5,3
	.word	93116
	.byte	12,22,163,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88189
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCOND',0,22,168,5,3
	.word	93180
	.byte	12,22,171,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88513
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONDBG',0,22,176,5,3
	.word	93247
	.byte	12,22,179,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88656
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONHSM',0,22,184,5,3
	.word	93316
	.byte	12,22,187,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	88845
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,22,192,5,3
	.word	93385
	.byte	12,22,195,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89208
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONOTP',0,22,200,5,3
	.word	93458
	.byte	12,22,203,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	89803
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONP',0,22,208,5,3
	.word	93527
	.byte	12,22,211,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90327
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONWOP',0,22,216,5,3
	.word	93594
	.byte	12,22,219,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	90909
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG0',0,22,224,5,3
	.word	93663
	.byte	12,22,227,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91011
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG1',0,22,232,5,3
	.word	93731
	.byte	12,22,235,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91113
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG2',0,22,240,5,3
	.word	93799
	.byte	12,22,243,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91215
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRAD',0,22,248,5,3
	.word	93867
	.byte	12,22,251,5,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91309
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRCT',0,22,128,6,3
	.word	93931
	.byte	12,22,131,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91519
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD0',0,22,136,6,3
	.word	93995
	.byte	12,22,139,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91592
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD1',0,22,144,6,3
	.word	94059
	.byte	12,22,147,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91665
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_CFG',0,22,152,6,3
	.word	94123
	.byte	12,22,155,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91820
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_STAT',0,22,160,6,3
	.word	94191
	.byte	12,22,163,6,9,4,13
	.byte	'U',0
	.word	498
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	514
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91925
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_TOP',0,22,168,6,3
	.word	94260
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,22,179,6,25,12,13
	.byte	'CFG',0
	.word	92205
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	92273
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	92342
	.byte	4,2,35,8,0,14
	.word	94328
	.byte	26
	.byte	'Ifx_FLASH_CBAB',0,22,184,6,3
	.word	94391
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,22,187,6,25,12,13
	.byte	'CFG0',0
	.word	93663
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	93731
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	93799
	.byte	4,2,35,8,0,14
	.word	94420
	.byte	26
	.byte	'Ifx_FLASH_RDB',0,22,192,6,3
	.word	94484
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,22,195,6,25,12,13
	.byte	'CFG',0
	.word	94123
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	94191
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	94260
	.byte	4,2,35,8,0,14
	.word	94512
	.byte	26
	.byte	'Ifx_FLASH_UBAB',0,22,200,6,3
	.word	94575
	.byte	26
	.byte	'Ifx_P_ACCEN0_Bits',0,11,79,3
	.word	9184
	.byte	26
	.byte	'Ifx_P_ACCEN1_Bits',0,11,85,3
	.word	9097
	.byte	26
	.byte	'Ifx_P_ESR_Bits',0,11,107,3
	.word	5440
	.byte	26
	.byte	'Ifx_P_ID_Bits',0,11,115,3
	.word	3493
	.byte	26
	.byte	'Ifx_P_IN_Bits',0,11,137,1,3
	.word	4488
	.byte	26
	.byte	'Ifx_P_IOCR0_Bits',0,11,150,1,3
	.word	3621
	.byte	26
	.byte	'Ifx_P_IOCR12_Bits',0,11,163,1,3
	.word	4268
	.byte	26
	.byte	'Ifx_P_IOCR4_Bits',0,11,176,1,3
	.word	3836
	.byte	26
	.byte	'Ifx_P_IOCR8_Bits',0,11,189,1,3
	.word	4051
	.byte	26
	.byte	'Ifx_P_LPCR0_Bits',0,11,197,1,3
	.word	8456
	.byte	26
	.byte	'Ifx_P_LPCR1_Bits',0,11,205,1,3
	.word	8580
	.byte	26
	.byte	'Ifx_P_LPCR1_P21_Bits',0,11,215,1,3
	.word	8664
	.byte	26
	.byte	'Ifx_P_LPCR2_Bits',0,11,229,1,3
	.word	8844
	.byte	26
	.byte	'Ifx_P_OMCR0_Bits',0,11,240,1,3
	.word	7095
	.byte	26
	.byte	'Ifx_P_OMCR12_Bits',0,11,250,1,3
	.word	7619
	.byte	26
	.byte	'Ifx_P_OMCR4_Bits',0,11,133,2,3
	.word	7269
	.byte	26
	.byte	'Ifx_P_OMCR8_Bits',0,11,144,2,3
	.word	7443
	.byte	26
	.byte	'Ifx_P_OMCR_Bits',0,11,166,2,3
	.word	8108
	.byte	26
	.byte	'Ifx_P_OMR_Bits',0,11,203,2,3
	.word	2922
	.byte	26
	.byte	'Ifx_P_OMSR0_Bits',0,11,213,2,3
	.word	6432
	.byte	26
	.byte	'Ifx_P_OMSR12_Bits',0,11,224,2,3
	.word	6920
	.byte	26
	.byte	'Ifx_P_OMSR4_Bits',0,11,235,2,3
	.word	6579
	.byte	26
	.byte	'Ifx_P_OMSR8_Bits',0,11,246,2,3
	.word	6748
	.byte	26
	.byte	'Ifx_P_OMSR_Bits',0,11,140,3,3
	.word	7775
	.byte	26
	.byte	'Ifx_P_OUT_Bits',0,11,162,3,3
	.word	2606
	.byte	26
	.byte	'Ifx_P_PCSR_Bits',0,11,180,3,3
	.word	6146
	.byte	26
	.byte	'Ifx_P_PDISC_Bits',0,11,202,3,3
	.word	5780
	.byte	26
	.byte	'Ifx_P_PDR0_Bits',0,11,223,3,3
	.word	4811
	.byte	26
	.byte	'Ifx_P_PDR1_Bits',0,11,244,3,3
	.word	5115
	.byte	26
	.byte	'Ifx_P_ACCEN0',0,11,129,4,3
	.word	9711
	.byte	26
	.byte	'Ifx_P_ACCEN1',0,11,137,4,3
	.word	9144
	.byte	26
	.byte	'Ifx_P_ESR',0,11,145,4,3
	.word	5731
	.byte	26
	.byte	'Ifx_P_ID',0,11,153,4,3
	.word	3572
	.byte	26
	.byte	'Ifx_P_IN',0,11,161,4,3
	.word	4762
	.byte	26
	.byte	'Ifx_P_IOCR0',0,11,169,4,3
	.word	3796
	.byte	26
	.byte	'Ifx_P_IOCR12',0,11,177,4,3
	.word	4448
	.byte	26
	.byte	'Ifx_P_IOCR4',0,11,185,4,3
	.word	4011
	.byte	26
	.byte	'Ifx_P_IOCR8',0,11,193,4,3
	.word	4228
	.byte	26
	.byte	'Ifx_P_LPCR0',0,11,201,4,3
	.word	8540
	.byte	26
	.byte	'Ifx_P_LPCR1',0,11,210,4,3
	.word	8789
	.byte	26
	.byte	'Ifx_P_LPCR2',0,11,218,4,3
	.word	9048
	.byte	26
	.byte	'Ifx_P_OMCR',0,11,226,4,3
	.word	8416
	.byte	26
	.byte	'Ifx_P_OMCR0',0,11,234,4,3
	.word	7229
	.byte	26
	.byte	'Ifx_P_OMCR12',0,11,242,4,3
	.word	7735
	.byte	26
	.byte	'Ifx_P_OMCR4',0,11,250,4,3
	.word	7403
	.byte	26
	.byte	'Ifx_P_OMCR8',0,11,130,5,3
	.word	7579
	.byte	26
	.byte	'Ifx_P_OMR',0,11,138,5,3
	.word	3453
	.byte	26
	.byte	'Ifx_P_OMSR',0,11,146,5,3
	.word	8068
	.byte	26
	.byte	'Ifx_P_OMSR0',0,11,154,5,3
	.word	6539
	.byte	26
	.byte	'Ifx_P_OMSR12',0,11,162,5,3
	.word	7055
	.byte	26
	.byte	'Ifx_P_OMSR4',0,11,170,5,3
	.word	6708
	.byte	26
	.byte	'Ifx_P_OMSR8',0,11,178,5,3
	.word	6880
	.byte	26
	.byte	'Ifx_P_OUT',0,11,186,5,3
	.word	2882
	.byte	26
	.byte	'Ifx_P_PCSR',0,11,194,5,3
	.word	6392
	.byte	26
	.byte	'Ifx_P_PDISC',0,11,202,5,3
	.word	6106
	.byte	26
	.byte	'Ifx_P_PDR0',0,11,210,5,3
	.word	5075
	.byte	26
	.byte	'Ifx_P_PDR1',0,11,218,5,3
	.word	5391
	.byte	14
	.word	9751
	.byte	26
	.byte	'Ifx_P',0,11,139,6,3
	.word	95922
	.byte	15,10,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,26
	.byte	'IfxPort_InputMode',0,10,89,3
	.word	95942
	.byte	15,10,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,26
	.byte	'IfxPort_OutputIdx',0,10,130,1,3
	.word	96093
	.byte	15,10,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,26
	.byte	'IfxPort_OutputMode',0,10,138,1,3
	.word	96337
	.byte	15,10,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,26
	.byte	'IfxPort_PadDriver',0,10,158,1,3
	.word	96435
	.byte	26
	.byte	'IfxPort_State',0,10,178,1,3
	.word	10364
	.byte	25,10,190,1,9,8,13
	.byte	'port',0
	.word	10359
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	521
	.byte	1,2,35,4,0,26
	.byte	'IfxPort_Pin',0,10,194,1,3
	.word	96900
	.byte	26
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,23,148,1,16
	.word	237
	.byte	25,23,212,5,9,8,13
	.byte	'value',0
	.word	2375
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2375
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_CcuconRegConfig',0,23,216,5,3
	.word	97000
	.byte	25,23,221,5,9,8,13
	.byte	'pDivider',0
	.word	521
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	521
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	521
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	294
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_InitialStepConfig',0,23,227,5,3
	.word	97071
	.byte	25,23,231,5,9,12,13
	.byte	'k2Step',0
	.word	521
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	294
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	96960
	.byte	4,2,35,8,0,26
	.byte	'IfxScuCcu_PllStepsConfig',0,23,236,5,3
	.word	97188
	.byte	3
	.word	234
	.byte	25,23,244,5,9,48,13
	.byte	'ccucon0',0
	.word	97000
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	97000
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	97000
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	97000
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	97000
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	97000
	.byte	8,2,35,40,0,26
	.byte	'IfxScuCcu_ClockDistributionConfig',0,23,252,5,3
	.word	97290
	.byte	25,23,128,6,9,8,13
	.byte	'value',0
	.word	2375
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2375
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,23,132,6,3
	.word	97442
	.byte	3
	.word	97188
	.byte	25,23,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	521
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	97518
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	97071
	.byte	8,2,35,8,0,26
	.byte	'IfxScuCcu_SysPllConfig',0,23,142,6,3
	.word	97523
	.byte	26
	.byte	'IfxDma_ChannelBusPriority',0,12,99,3
	.word	24922
	.byte	26
	.byte	'IfxDma_ChannelIncrementCircular',0,12,122,3
	.word	25390
	.byte	26
	.byte	'IfxDma_ChannelIncrementDirection',0,12,131,1,3
	.word	25296
	.byte	26
	.byte	'IfxDma_ChannelIncrementStep',0,12,146,1,3
	.word	25028
	.byte	26
	.byte	'IfxDma_ChannelInterruptControl',0,12,155,1,3
	.word	26557
	.byte	26
	.byte	'IfxDma_ChannelMove',0,12,170,1,3
	.word	23980
	.byte	26
	.byte	'IfxDma_ChannelMoveSize',0,12,183,1,3
	.word	24372
	.byte	26
	.byte	'IfxDma_ChannelOperationMode',0,12,192,1,3
	.word	24287
	.byte	26
	.byte	'IfxDma_ChannelPattern',0,12,207,1,3
	.word	24566
	.byte	26
	.byte	'IfxDma_ChannelRequestMode',0,12,225,1,3
	.word	24172
	.byte	26
	.byte	'IfxDma_ChannelRequestSource',0,12,234,1,3
	.word	24833
	.byte	26
	.byte	'IfxDma_ChannelShadow',0,12,254,1,3
	.word	26004
	.byte	15,12,128,2,9,1,16
	.byte	'IfxDma_HardwareResourcePartition_0',0,0,16
	.byte	'IfxDma_HardwareResourcePartition_1',0,1,16
	.byte	'IfxDma_HardwareResourcePartition_2',0,2,16
	.byte	'IfxDma_HardwareResourcePartition_3',0,3,0,26
	.byte	'IfxDma_HardwareResourcePartition',0,12,134,2,3
	.word	98063
	.byte	15,12,138,2,9,1,16
	.byte	'IfxDma_MoveEngine_0',0,0,16
	.byte	'IfxDma_MoveEngine_1',0,1,0,26
	.byte	'IfxDma_MoveEngine',0,12,142,2,3
	.word	98260
	.byte	15,12,147,2,9,1,16
	.byte	'IfxDma_SleepMode_enable',0,0,16
	.byte	'IfxDma_SleepMode_disable',0,1,0,26
	.byte	'IfxDma_SleepMode',0,12,151,2,3
	.word	98338
	.byte	26
	.byte	'IfxDma_Dma',0,15,210,3,3
	.word	23849
	.byte	26
	.byte	'IfxDma_Dma_Channel',0,15,223,3,3
	.word	23919
	.byte	26
	.byte	'IfxDma_Dma_ChannelConfig',0,15,135,4,3
	.word	26675
	.byte	26
	.byte	'IfxDma_Dma_Config',0,15,142,4,3
	.word	23874
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,38,0,73,19,0,0,21,46,1,49,19,0,0,22,5,0,49,19
	.byte	0,0,23,29,1,49,19,0,0,24,11,0,49,19,0,0,25,19,1,58,15,59,15,57,15,11,15,0,0,26,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,27,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L40:
	.word	.L200-.L199
.L199:
	.half	3
	.word	.L202-.L201
.L201:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Dma\\Std\\IfxDma.h',0
	.byte	0,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxDma_cfg.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Dma\\Dma\\IfxDma_Dma.h',0,0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L202:
.L200:
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_createModuleHandle')
	.sect	'.debug_info'
.L41:
	.word	337
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L44,.L43
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_createModuleHandle',0,1,124,6,1,1,1
	.word	.L24,.L81,.L23
	.byte	4
	.byte	'dmaHandle',0,1,124,48
	.word	.L82,.L83
	.byte	4
	.byte	'dma',0,1,124,68
	.word	.L84,.L85
	.byte	5
	.word	.L24,.L81
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_createModuleHandle')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_createModuleHandle')
	.sect	'.debug_line'
.L43:
	.word	.L204-.L203
.L203:
	.half	3
	.word	.L206-.L205
.L205:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0,0
.L206:
	.byte	5,20,7,0,5,2
	.word	.L24
	.byte	3,253,0,1,5,1,9
	.half	.L207-.L24
	.byte	3,1,1,7,9
	.half	.L45-.L207
	.byte	0,1,1
.L204:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_createModuleHandle')
	.sect	'.debug_ranges'
.L44:
	.word	-1,.L24,0,.L45-.L24,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_deInitChannel')
	.sect	'.debug_info'
.L46:
	.word	514
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L49,.L48
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_deInitChannel',0,1,130,1,6,1,1,1
	.word	.L26,.L86,.L25
	.byte	4
	.byte	'dma',0,1,130,1,43
	.word	.L82,.L87
	.byte	4
	.byte	'channel',0,1,130,1,65
	.word	.L88,.L89
	.byte	5
	.word	.L26,.L86
	.byte	6
	.word	.L90,.L91,.L9
	.byte	7
	.word	.L92,.L93
	.byte	7
	.word	.L94,.L95
	.byte	8
	.word	.L96,.L91,.L9
	.byte	0,6
	.word	.L97,.L98,.L10
	.byte	7
	.word	.L99,.L100
	.byte	7
	.word	.L101,.L102
	.byte	8
	.word	.L103,.L98,.L10
	.byte	0,6
	.word	.L104,.L105,.L106
	.byte	7
	.word	.L107,.L108
	.byte	7
	.word	.L109,.L110
	.byte	8
	.word	.L111,.L105,.L106
	.byte	0,6
	.word	.L112,.L113,.L13
	.byte	7
	.word	.L114,.L115
	.byte	7
	.word	.L116,.L117
	.byte	8
	.word	.L118,.L113,.L13
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_deInitChannel')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_deInitChannel')
	.sect	'.debug_line'
.L48:
	.word	.L209-.L208
.L208:
	.half	3
	.word	.L211-.L210
.L210:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Dma\\Std\\IfxDma.h',0
	.byte	0,0,0,0
.L211:
	.byte	5,47,7,0,5,2
	.word	.L26
	.byte	3,131,1,1,4,2,5,20,9
	.half	.L91-.L26
	.byte	3,140,11,1,5,33,9
	.half	.L212-.L91
	.byte	1,5,39,9
	.half	.L213-.L212
	.byte	1,5,5,9
	.half	.L214-.L213
	.byte	1,4,1,9
	.half	.L9-.L214
	.byte	3,244,116,1,5,45,7,9
	.half	.L215-.L9
	.byte	3,2,1,4,2,5,13,9
	.half	.L98-.L215
	.byte	3,140,9,1,5,26,9
	.half	.L216-.L98
	.byte	1,5,31,9
	.half	.L217-.L216
	.byte	1,4,1,5,28,9
	.half	.L10-.L217
	.byte	3,247,118,1,4,2,5,13,9
	.half	.L105-.L10
	.byte	3,153,11,1,5,26,9
	.half	.L218-.L105
	.byte	1,5,31,9
	.half	.L219-.L218
	.byte	1,4,1,5,55,9
	.half	.L106-.L219
	.byte	3,233,116,1,5,39,9
	.half	.L11-.L106
	.byte	1,4,2,5,20,9
	.half	.L113-.L11
	.byte	3,255,10,1,5,33,9
	.half	.L220-.L113
	.byte	1,5,38,9
	.half	.L221-.L220
	.byte	1,5,5,9
	.half	.L222-.L221
	.byte	1,4,1,5,13,9
	.half	.L13-.L222
	.byte	3,129,117,1,5,1,7,9
	.half	.L223-.L13
	.byte	3,2,1,7,9
	.half	.L50-.L223
	.byte	0,1,1
.L209:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_deInitChannel')
	.sect	'.debug_ranges'
.L49:
	.word	-1,.L26,0,.L50-.L26,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_initModule')
	.sect	'.debug_info'
.L51:
	.word	329
	.half	3
	.word	.L52
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L54,.L53
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_initModule',0,1,232,1,6,1,1,1
	.word	.L34,.L119,.L33
	.byte	4
	.byte	'dma',0,1,232,1,40
	.word	.L82,.L120
	.byte	4
	.byte	'config',0,1,232,1,70
	.word	.L121,.L122
	.byte	5
	.word	.L34,.L119
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_initModule')
	.sect	'.debug_abbrev'
.L52:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_initModule')
	.sect	'.debug_line'
.L53:
	.word	.L225-.L224
.L224:
	.half	3
	.word	.L227-.L226
.L226:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0,0
.L227:
	.byte	5,22,7,0,5,2
	.word	.L34
	.byte	3,233,1,1,5,14,9
	.half	.L228-.L34
	.byte	1,5,1,9
	.half	.L229-.L228
	.byte	3,1,1,7,9
	.half	.L55-.L229
	.byte	0,1,1
.L225:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_initModule')
	.sect	'.debug_ranges'
.L54:
	.word	-1,.L34,0,.L55-.L34,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_initModuleConfig')
	.sect	'.debug_info'
.L56:
	.word	335
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L59,.L58
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_initModuleConfig',0,1,238,1,6,1,1,1
	.word	.L36,.L123,.L35
	.byte	4
	.byte	'config',0,1,238,1,53
	.word	.L124,.L125
	.byte	4
	.byte	'dma',0,1,238,1,70
	.word	.L84,.L126
	.byte	5
	.word	.L36,.L123
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_initModuleConfig')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_initModuleConfig')
	.sect	'.debug_line'
.L58:
	.word	.L231-.L230
.L230:
	.half	3
	.word	.L233-.L232
.L232:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0,0
.L233:
	.byte	5,17,7,0,5,2
	.word	.L36
	.byte	3,239,1,1,5,1,9
	.half	.L234-.L36
	.byte	3,1,1,7,9
	.half	.L60-.L234
	.byte	0,1,1
.L231:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_initModuleConfig')
	.sect	'.debug_ranges'
.L59:
	.word	-1,.L36,0,.L60-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_initChannel')
	.sect	'.debug_info'
.L61:
	.word	578
	.half	3
	.word	.L62
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L64,.L63
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_initChannel',0,1,144,1,6,1,1,1
	.word	.L28,.L127,.L27
	.byte	4
	.byte	'channel',0,1,144,1,49
	.word	.L128,.L129
	.byte	4
	.byte	'config',0,1,144,1,90
	.word	.L130,.L131
	.byte	5
	.word	.L28,.L127
	.byte	6
	.byte	'dma',0,1,146,1,14
	.word	.L84,.L132
	.byte	5
	.word	.L133,.L134
	.byte	6
	.byte	'tsr',0,1,155,1,21
	.word	.L135,.L136
	.byte	0,5
	.word	.L137,.L16
	.byte	6
	.byte	'src',0,1,172,1,32
	.word	.L138,.L139
	.byte	7
	.word	.L140,.L141,.L18
	.byte	8
	.word	.L142,.L143
	.byte	8
	.word	.L144,.L145
	.byte	9
	.word	.L146,.L141,.L18
	.byte	0,7
	.word	.L147,.L148,.L149
	.byte	8
	.word	.L150,.L151
	.byte	8
	.word	.L152,.L153
	.byte	8
	.word	.L154,.L155
	.byte	10
	.word	.L156,.L148,.L149
	.byte	7
	.word	.L157,.L158,.L149
	.byte	8
	.word	.L159,.L160
	.byte	9
	.word	.L161,.L158,.L149
	.byte	0,0,0,7
	.word	.L162,.L149,.L16
	.byte	8
	.word	.L163,.L164
	.byte	9
	.word	.L165,.L149,.L16
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_initChannel')
	.sect	'.debug_abbrev'
.L62:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_initChannel')
	.sect	'.debug_line'
.L63:
	.word	.L236-.L235
.L235:
	.half	3
	.word	.L238-.L237
.L237:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Dma\\Std\\IfxDma.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0,0
.L238:
	.byte	5,6,7,0,5,2
	.word	.L28
	.byte	3,143,1,1,5,26,9
	.half	.L192-.L28
	.byte	3,2,1,5,34,9
	.half	.L239-.L192
	.byte	1,5,24,9
	.half	.L193-.L239
	.byte	3,2,1,5,32,9
	.half	.L240-.L193
	.byte	3,1,1,5,24,9
	.half	.L241-.L240
	.byte	1,5,41,9
	.half	.L242-.L241
	.byte	3,1,1,5,34,9
	.half	.L243-.L242
	.byte	1,5,24,9
	.half	.L244-.L243
	.byte	1,5,47,9
	.half	.L245-.L244
	.byte	3,2,1,5,58,9
	.half	.L189-.L245
	.byte	1,5,15,9
	.half	.L133-.L189
	.byte	3,4,1,5,19,9
	.half	.L194-.L133
	.byte	3,2,1,5,9,9
	.half	.L246-.L194
	.byte	1,5,25,7,9
	.half	.L247-.L246
	.byte	3,2,1,5,23,9
	.half	.L248-.L247
	.byte	1,5,26,9
	.half	.L249-.L248
	.byte	1,5,25,9
	.half	.L14-.L249
	.byte	3,4,1,5,23,9
	.half	.L250-.L14
	.byte	1,5,25,9
	.half	.L15-.L250
	.byte	3,3,1,5,17,9
	.half	.L251-.L15
	.byte	1,5,40,9
	.half	.L252-.L251
	.byte	1,5,15,9
	.half	.L134-.L252
	.byte	3,3,1,5,5,9
	.half	.L253-.L134
	.byte	1,5,66,7,9
	.half	.L137-.L253
	.byte	3,2,1,5,80,9
	.half	.L254-.L137
	.byte	1,4,2,5,5,9
	.half	.L141-.L254
	.byte	3,208,10,1,5,37,7,9
	.half	.L17-.L141
	.byte	3,2,1,5,5,9
	.half	.L196-.L17
	.byte	1,4,1,5,32,9
	.half	.L18-.L196
	.byte	3,175,117,1,5,71,9
	.half	.L195-.L18
	.byte	1,4,3,5,11,9
	.half	.L148-.L195
	.byte	3,231,0,1,5,19,9
	.half	.L255-.L148
	.byte	1,5,17,9
	.half	.L256-.L255
	.byte	1,5,11,9
	.half	.L257-.L256
	.byte	3,1,1,5,17,9
	.half	.L258-.L257
	.byte	1,5,11,9
	.half	.L158-.L258
	.byte	3,103,1,5,17,9
	.half	.L259-.L158
	.byte	1,5,11,9
	.half	.L149-.L259
	.byte	3,18,1,5,16,9
	.half	.L260-.L149
	.byte	1,4,1,5,1,9
	.half	.L16-.L260
	.byte	3,162,127,1,7,9
	.half	.L65-.L16
	.byte	0,1,1
.L236:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_initChannel')
	.sect	'.debug_ranges'
.L64:
	.word	-1,.L28,0,.L65-.L28,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_initChannelConfig')
	.sect	'.debug_info'
.L66:
	.word	364
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L69,.L68
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_initChannelConfig',0,1,179,1,6,1,1,1
	.word	.L30,.L166,.L29
	.byte	4
	.byte	'config',0,1,179,1,61
	.word	.L167,.L168
	.byte	4
	.byte	'dma',0,1,179,1,81
	.word	.L82,.L169
	.byte	5
	.word	.L30,.L166
	.byte	6
	.byte	'defaultConfig',0,1,181,1,36
	.word	.L170,.L171
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_initChannelConfig')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_initChannelConfig')
	.sect	'.debug_line'
.L68:
	.word	.L262-.L261
.L261:
	.half	3
	.word	.L264-.L263
.L263:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0,0
.L264:
	.byte	5,6,7,0,5,2
	.word	.L30
	.byte	3,178,1,1,5,52,9
	.half	.L197-.L30
	.byte	3,2,1,5,50,9
	.half	.L265-.L197
	.byte	1,5,15,9
	.half	.L266-.L265
	.byte	3,38,1,5,13,9
	.half	.L267-.L266
	.byte	1,5,20,9
	.half	.L268-.L267
	.byte	3,3,1,5,1,9
	.half	.L269-.L268
	.byte	3,1,1,7,9
	.half	.L70-.L269
	.byte	0,1,1
.L262:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_initChannelConfig')
	.sect	'.debug_ranges'
.L69:
	.word	-1,.L30,0,.L70-.L30,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_initLinkedListEntry')
	.sect	'.debug_info'
.L71:
	.word	347
	.half	3
	.word	.L72
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L74,.L73
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_initLinkedListEntry',0,1,226,1,6,1,1,1
	.word	.L32,.L172,.L31
	.byte	4
	.byte	'ptrToAddress',0,1,226,1,43
	.word	.L173,.L174
	.byte	4
	.byte	'config',0,1,226,1,89
	.word	.L130,.L175
	.byte	5
	.word	.L32,.L172
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_initLinkedListEntry')
	.sect	'.debug_abbrev'
.L72:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_initLinkedListEntry')
	.sect	'.debug_line'
.L73:
	.word	.L271-.L270
.L270:
	.half	3
	.word	.L273-.L272
.L272:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0,0
.L273:
	.byte	5,68,7,0,5,2
	.word	.L32
	.byte	3,227,1,1,5,1,9
	.half	.L198-.L32
	.byte	3,1,1,7,9
	.half	.L75-.L198
	.byte	0,1,1
.L271:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_initLinkedListEntry')
	.sect	'.debug_ranges'
.L74:
	.word	-1,.L32,0,.L75-.L32,0,0
	.sdecl	'.debug_info',debug,cluster('IfxDma_Dma_configureTransactionSet')
	.sect	'.debug_info'
.L76:
	.word	400
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L79,.L78
	.byte	2
	.word	.L37
	.byte	3
	.byte	'IfxDma_Dma_configureTransactionSet',0,1,66,17,1,1
	.word	.L22,.L176,.L21
	.byte	4
	.byte	'channel',0,1,66,64
	.word	.L177,.L178
	.byte	4
	.byte	'config',0,1,66,105
	.word	.L130,.L179
	.byte	5
	.word	.L22,.L176
	.byte	5
	.word	.L22,.L180
	.byte	6
	.byte	'chcfgr',0,1,74,27
	.word	.L181,.L182
	.byte	0,5
	.word	.L180,.L183
	.byte	6
	.byte	'adicr',0,1,88,26
	.word	.L184,.L185
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxDma_Dma_configureTransactionSet')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxDma_Dma_configureTransactionSet')
	.sect	'.debug_line'
.L78:
	.word	.L275-.L274
.L274:
	.half	3
	.word	.L277-.L276
.L276:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma/IfxDma_Dma.c',0,0,0,0,0
.L277:
	.byte	5,27,7,0,5,2
	.word	.L22
	.byte	3,202,0,1,5,35,9
	.half	.L186-.L22
	.byte	3,1,1,5,27,9
	.half	.L278-.L186
	.byte	1,5,35,9
	.half	.L279-.L278
	.byte	3,1,1,5,27,9
	.half	.L280-.L279
	.byte	1,5,35,9
	.half	.L281-.L280
	.byte	3,1,1,5,27,9
	.half	.L282-.L281
	.byte	1,5,35,9
	.half	.L283-.L282
	.byte	3,1,1,5,27,9
	.half	.L284-.L283
	.byte	1,5,35,9
	.half	.L285-.L284
	.byte	3,1,1,5,27,9
	.half	.L286-.L285
	.byte	1,5,35,9
	.half	.L287-.L286
	.byte	3,1,1,5,27,9
	.half	.L288-.L287
	.byte	1,5,35,9
	.half	.L289-.L288
	.byte	3,1,1,5,27,9
	.half	.L290-.L289
	.byte	1,9
	.half	.L291-.L290
	.byte	3,2,1,5,23,9
	.half	.L180-.L291
	.byte	3,5,1,5,31,9
	.half	.L188-.L180
	.byte	3,1,1,5,23,9
	.half	.L292-.L188
	.byte	1,5,31,9
	.half	.L293-.L292
	.byte	3,1,1,5,23,9
	.half	.L294-.L293
	.byte	1,5,31,9
	.half	.L295-.L294
	.byte	3,1,1,5,23,9
	.half	.L296-.L295
	.byte	1,5,31,9
	.half	.L297-.L296
	.byte	3,1,1,5,23,9
	.half	.L298-.L297
	.byte	1,5,31,9
	.half	.L299-.L298
	.byte	3,1,1,5,23,9
	.half	.L300-.L299
	.byte	1,5,31,9
	.half	.L301-.L300
	.byte	3,1,1,5,23,9
	.half	.L302-.L301
	.byte	1,5,31,9
	.half	.L303-.L302
	.byte	3,1,1,5,23,9
	.half	.L304-.L303
	.byte	1,5,31,9
	.half	.L305-.L304
	.byte	3,1,1,5,23,9
	.half	.L306-.L305
	.byte	1,5,31,9
	.half	.L307-.L306
	.byte	3,1,1,5,23,9
	.half	.L308-.L307
	.byte	1,5,31,9
	.half	.L309-.L308
	.byte	3,1,1,5,23,9
	.half	.L310-.L309
	.byte	1,5,31,9
	.half	.L311-.L310
	.byte	3,1,1,5,23,9
	.half	.L312-.L311
	.byte	1,5,31,9
	.half	.L313-.L312
	.byte	3,1,1,5,23,9
	.half	.L314-.L313
	.byte	1,5,32,9
	.half	.L315-.L314
	.byte	3,1,1,5,26,9
	.half	.L187-.L315
	.byte	1,5,58,7,9
	.half	.L316-.L187
	.byte	1,5,62,9
	.half	.L317-.L316
	.byte	1,5,58,9
	.half	.L2-.L317
	.byte	1,5,76,9
	.half	.L3-.L2
	.byte	1,5,70,9
	.half	.L318-.L3
	.byte	1,5,102,7,9
	.half	.L319-.L318
	.byte	1,5,106,9
	.half	.L320-.L319
	.byte	1,5,102,9
	.half	.L4-.L320
	.byte	1,5,67,9
	.half	.L5-.L4
	.byte	1,5,23,9
	.half	.L321-.L5
	.byte	1,5,31,9
	.half	.L322-.L321
	.byte	3,1,1,5,23,9
	.half	.L323-.L322
	.byte	1,5,31,9
	.half	.L324-.L323
	.byte	3,1,1,5,23,9
	.half	.L325-.L324
	.byte	1,5,26,9
	.half	.L326-.L325
	.byte	3,3,1,5,31,9
	.half	.L183-.L326
	.byte	3,2,1,5,23,9
	.half	.L327-.L183
	.byte	1,5,31,9
	.half	.L328-.L327
	.byte	3,1,1,5,23,9
	.half	.L329-.L328
	.byte	1,5,31,9
	.half	.L330-.L329
	.byte	3,1,1,5,23,9
	.half	.L331-.L330
	.byte	1,5,31,9
	.half	.L332-.L331
	.byte	3,1,1,5,23,9
	.half	.L333-.L332
	.byte	1,5,16,9
	.half	.L334-.L333
	.byte	3,3,1,5,9,9
	.half	.L335-.L334
	.byte	1,5,16,7,9
	.half	.L336-.L335
	.byte	3,1,1,5,32,9
	.half	.L337-.L336
	.byte	1,5,16,7,9
	.half	.L338-.L337
	.byte	3,1,1,5,32,9
	.half	.L339-.L338
	.byte	1,5,34,7,9
	.half	.L340-.L339
	.byte	3,2,1,5,26,9
	.half	.L341-.L340
	.byte	1,5,1,9
	.half	.L6-.L341
	.byte	3,2,1,7,9
	.half	.L80-.L6
	.byte	0,1,1
.L275:
	.sdecl	'.debug_ranges',debug,cluster('IfxDma_Dma_configureTransactionSet')
	.sect	'.debug_ranges'
.L79:
	.word	-1,.L22,0,.L80-.L22,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_configureTransactionSet')
	.sect	'.debug_loc'
.L21:
	.word	-1,.L22,0,.L176-.L22
	.half	2
	.byte	138,0
	.word	0,0
.L185:
	.word	-1,.L22,.L188-.L22,.L176-.L22
	.half	1
	.byte	81
	.word	0,0
.L178:
	.word	-1,.L22,0,.L176-.L22
	.half	1
	.byte	100
	.word	0,0
.L182:
	.word	-1,.L22,.L186-.L22,.L187-.L22
	.half	1
	.byte	95
	.word	0,0
.L179:
	.word	-1,.L22,0,.L176-.L22
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_createModuleHandle')
	.sect	'.debug_loc'
.L23:
	.word	-1,.L24,0,.L81-.L24
	.half	2
	.byte	138,0
	.word	0,0
.L85:
	.word	-1,.L24,0,.L81-.L24
	.half	1
	.byte	101
	.word	0,0
.L83:
	.word	-1,.L24,0,.L81-.L24
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_deInitChannel')
	.sect	'.debug_loc'
.L25:
	.word	-1,.L26,0,.L86-.L26
	.half	2
	.byte	138,0
	.word	0,0
.L89:
	.word	-1,.L26,0,.L86-.L26
	.half	1
	.byte	84
	.word	0,0
.L102:
	.word	0,0
.L117:
	.word	0,0
.L95:
	.word	0,0
.L110:
	.word	0,0
.L87:
	.word	-1,.L26,0,.L86-.L26
	.half	1
	.byte	100
	.word	0,0
.L100:
	.word	0,0
.L115:
	.word	0,0
.L93:
	.word	0,0
.L108:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_initChannel')
	.sect	'.debug_loc'
.L27:
	.word	-1,.L28,0,.L127-.L28
	.half	2
	.byte	138,0
	.word	0,0
.L129:
	.word	-1,.L28,0,.L189-.L28
	.half	1
	.byte	100
	.word	.L190-.L28,.L191-.L28
	.half	1
	.byte	111
	.word	0,0
.L145:
	.word	0,0
.L131:
	.word	-1,.L28,0,.L133-.L28
	.half	1
	.byte	101
	.word	.L192-.L28,.L127-.L28
	.half	1
	.byte	108
	.word	0,0
.L132:
	.word	-1,.L28,.L193-.L28,.L127-.L28
	.half	1
	.byte	109
	.word	0,0
.L143:
	.word	0,0
.L155:
	.word	0,0
.L139:
	.word	-1,.L28,.L196-.L28,.L16-.L28
	.half	1
	.byte	111
	.word	0,0
.L160:
	.word	0,0
.L164:
	.word	0,0
.L151:
	.word	0,0
.L136:
	.word	-1,.L28,.L194-.L28,.L195-.L28
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L153:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_initChannelConfig')
	.sect	'.debug_loc'
.L29:
	.word	-1,.L30,0,.L197-.L30
	.half	2
	.byte	138,0
	.word	.L197-.L30,.L166-.L30
	.half	2
	.byte	138,56
	.word	.L166-.L30,.L166-.L30
	.half	2
	.byte	138,0
	.word	0,0
.L168:
	.word	-1,.L30,0,.L166-.L30
	.half	1
	.byte	100
	.word	0,0
.L171:
	.word	0,0
.L169:
	.word	-1,.L30,0,.L166-.L30
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_initLinkedListEntry')
	.sect	'.debug_loc'
.L31:
	.word	-1,.L32,0,.L172-.L32
	.half	2
	.byte	138,0
	.word	0,0
.L175:
	.word	-1,.L32,0,.L198-.L32
	.half	1
	.byte	101
	.word	0,0
.L174:
	.word	-1,.L32,0,.L198-.L32
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_initModule')
	.sect	'.debug_loc'
.L33:
	.word	-1,.L34,0,.L119-.L34
	.half	2
	.byte	138,0
	.word	0,0
.L122:
	.word	-1,.L34,0,.L119-.L34
	.half	1
	.byte	101
	.word	0,0
.L120:
	.word	-1,.L34,0,.L119-.L34
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxDma_Dma_initModuleConfig')
	.sect	'.debug_loc'
.L35:
	.word	-1,.L36,0,.L123-.L36
	.half	2
	.byte	138,0
	.word	0,0
.L125:
	.word	-1,.L36,0,.L123-.L36
	.half	1
	.byte	100
	.word	0,0
.L126:
	.word	-1,.L36,0,.L123-.L36
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L342:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_configureTransactionSet')
	.sect	'.debug_frame'
	.word	20
	.word	.L342,.L22,.L176-.L22
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_createModuleHandle')
	.sect	'.debug_frame'
	.word	20
	.word	.L342,.L24,.L81-.L24
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_deInitChannel')
	.sect	'.debug_frame'
	.word	20
	.word	.L342,.L26,.L86-.L26
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_initChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L342,.L28,.L127-.L28
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_initChannelConfig')
	.sect	'.debug_frame'
	.word	40
	.word	.L342,.L30,.L166-.L30
	.byte	8,19,8,23,4
	.word	(.L197-.L30)/2
	.byte	19,56,22,26,3,19,138,56,4
	.word	(.L166-.L197)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_initLinkedListEntry')
	.sect	'.debug_frame'
	.word	12
	.word	.L342,.L32,.L172-.L32
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_initModule')
	.sect	'.debug_frame'
	.word	20
	.word	.L342,.L34,.L119-.L34
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxDma_Dma_initModuleConfig')
	.sect	'.debug_frame'
	.word	20
	.word	.L342,.L36,.L123-.L36
	.byte	8,18,8,19,8,22,8,23
	; Module end
