/*
 * Device.h
 *
 *  Created on: 2025��3��30��
 *      Author: lenovo
 */

#ifndef CODE_DEVICE_H_
#define CODE_DEVICE_H_

#include "zf_common_headfile.h"
#include "zf_device_tft180.h"

//궨

//***********************************************������*************************************************************
#define BUZZER_PIN              (P33_10)
//***********************************************������*************************************************************

//***********************************************���***************************************************************
#define SERVO_MOTOR_PWM         (ATOM1_CH1_P33_9)              //�����϶����Ӧ����
#define SERVO_MOTOR_FREQ        (50)                           //���Ƶ�� 50~300
#define SERVO_MOTOR_MID         (84)                            //��ֵ           772
#define SERVO_MOTOR_LMAX        (120)                            //����     944  �����
#define SERVO_MOTOR_RMAX        (60)                            //�Ҽ���    555  �Ҵ���
#define SERVO_MOTOR_DUTY(x)    ((float)PWM_DUTY_MAX/(1000.0/(float)SERVO_MOTOR_FREQ) * (0.5+(float)(x)/90.0))  //���ռ�ձȼ��㹫ʽ

//***********************************************���***************************************************************

//***********************************************���***************************************************************
#define MAX_DUTY            (50)                                               // ��� MAX_DUTY% ռ�ձ�
#define DIR1                (P21_4)
#define PWM1                (ATOM0_CH3_P21_5)
//#define DIR1                (P02_4)
//#define PWM1                (ATOM0_CH5_P02_5)
//#define DIR2                (P02_6)
//#define PWM2                (ATOM0_CH7_P02_7)
#define DIR2                (P02_4)
#define PWM2                (ATOM0_CH5_P02_5)
//***********************************************���***************************************************************

//***********************************************����***************************************************************
#define LED1                    (P20_9)
#define LED2                    (P20_8)
#define LED3                    (P21_5)
#define LED4                    (P21_4)
#define KEY1                    (P20_6)
#define KEY2                    (P20_7)
#define KEY3                    (P11_2)
#define KEY4                    (P11_3)
#define SWITCH1                 (P33_11)
#define SWITCH2                 (P33_12)
//***********************************************����***************************************************************
//����
extern uint8 key1_flag;
extern uint8 key2_flag;
extern uint8 key3_flag;
extern uint8 key4_flag;
extern uint8 key1_state_last;
extern uint8 key2_state_last;
extern uint8 key3_state_last;
extern uint8 key4_state_last;
extern uint8 key1_state;
extern uint8 key2_state;
extern uint8 key3_state;
extern uint8 key4_state;
extern int32 Overall_Motor,a; //���ȫ��pwm����
extern uint32 Overall_Steer; //���ȫ��pwm����

//����

void ALL_Init(void);
void mykey_Init(void);
void key_scan(void);
void Steer_Init(void);   //���
void Steer_Set(uint32 duty);
void Motor_Init(void);   //���
void Speed_Set(int32 duty);


#endif /* CODE_DEVICE_H_ */
