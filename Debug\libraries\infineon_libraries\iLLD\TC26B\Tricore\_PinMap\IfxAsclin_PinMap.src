	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc32680a --dep-file=IfxAsclin_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_CTSA_P14_9_IN',data,rom,cluster('IfxAsclin0_CTSA_P14_9_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_CTSA_P14_9_IN'
	.global	IfxAsclin0_CTSA_P14_9_IN
	.align	4
IfxAsclin0_CTSA_P14_9_IN:	.type	object
	.size	IfxAsclin0_CTSA_P14_9_IN,16
	.word	-268433920,-268192768
	.byte	9
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_CTSA_P20_7_IN',data,rom,cluster('IfxAsclin1_CTSA_P20_7_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_CTSA_P20_7_IN'
	.global	IfxAsclin1_CTSA_P20_7_IN
	.align	4
IfxAsclin1_CTSA_P20_7_IN:	.type	object
	.size	IfxAsclin1_CTSA_P20_7_IN,16
	.word	-268433664,-268189696
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_CTSB_P32_4_IN',data,rom,cluster('IfxAsclin1_CTSB_P32_4_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_CTSB_P32_4_IN'
	.global	IfxAsclin1_CTSB_P32_4_IN
	.align	4
IfxAsclin1_CTSB_P32_4_IN:	.type	object
	.size	IfxAsclin1_CTSB_P32_4_IN,16
	.word	-268433664,-268185088
	.byte	4
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_CTSA_P10_7_IN',data,rom,cluster('IfxAsclin2_CTSA_P10_7_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_CTSA_P10_7_IN'
	.global	IfxAsclin2_CTSA_P10_7_IN
	.align	4
IfxAsclin2_CTSA_P10_7_IN:	.type	object
	.size	IfxAsclin2_CTSA_P10_7_IN,16
	.word	-268433408,-268193792
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_CTSB_P33_5_IN',data,rom,cluster('IfxAsclin2_CTSB_P33_5_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_CTSB_P33_5_IN'
	.global	IfxAsclin2_CTSB_P33_5_IN
	.align	4
IfxAsclin2_CTSB_P33_5_IN:	.type	object
	.size	IfxAsclin2_CTSB_P33_5_IN,16
	.word	-268433408,-268184832
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_CTSA_P00_12_IN',data,rom,cluster('IfxAsclin3_CTSA_P00_12_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_CTSA_P00_12_IN'
	.global	IfxAsclin3_CTSA_P00_12_IN
	.align	4
IfxAsclin3_CTSA_P00_12_IN:	.type	object
	.size	IfxAsclin3_CTSA_P00_12_IN,16
	.word	-268433152,-268197888
	.byte	12
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_RTS_P14_7_OUT',data,rom,cluster('IfxAsclin0_RTS_P14_7_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_RTS_P14_7_OUT'
	.global	IfxAsclin0_RTS_P14_7_OUT
	.align	4
IfxAsclin0_RTS_P14_7_OUT:	.type	object
	.size	IfxAsclin0_RTS_P14_7_OUT,16
	.word	-268433920,-268192768
	.byte	7
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RTS_P20_6_OUT',data,rom,cluster('IfxAsclin1_RTS_P20_6_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RTS_P20_6_OUT'
	.global	IfxAsclin1_RTS_P20_6_OUT
	.align	4
IfxAsclin1_RTS_P20_6_OUT:	.type	object
	.size	IfxAsclin1_RTS_P20_6_OUT,16
	.word	-268433664,-268189696
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RTS_P23_1_OUT',data,rom,cluster('IfxAsclin1_RTS_P23_1_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RTS_P23_1_OUT'
	.global	IfxAsclin1_RTS_P23_1_OUT
	.align	4
IfxAsclin1_RTS_P23_1_OUT:	.type	object
	.size	IfxAsclin1_RTS_P23_1_OUT,16
	.word	-268433664,-268188928
	.byte	1
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RTS_P10_8_OUT',data,rom,cluster('IfxAsclin2_RTS_P10_8_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RTS_P10_8_OUT'
	.global	IfxAsclin2_RTS_P10_8_OUT
	.align	4
IfxAsclin2_RTS_P10_8_OUT:	.type	object
	.size	IfxAsclin2_RTS_P10_8_OUT,16
	.word	-268433408,-268193792
	.byte	8
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RTS_P33_4_OUT',data,rom,cluster('IfxAsclin2_RTS_P33_4_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RTS_P33_4_OUT'
	.global	IfxAsclin2_RTS_P33_4_OUT
	.align	4
IfxAsclin2_RTS_P33_4_OUT:	.type	object
	.size	IfxAsclin2_RTS_P33_4_OUT,16
	.word	-268433408,-268184832
	.byte	4
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RTS_P00_9_OUT',data,rom,cluster('IfxAsclin3_RTS_P00_9_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RTS_P00_9_OUT'
	.global	IfxAsclin3_RTS_P00_9_OUT
	.align	4
IfxAsclin3_RTS_P00_9_OUT:	.type	object
	.size	IfxAsclin3_RTS_P00_9_OUT,16
	.word	-268433152,-268197888
	.byte	9
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_RXA_P14_1_IN',data,rom,cluster('IfxAsclin0_RXA_P14_1_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_RXA_P14_1_IN'
	.global	IfxAsclin0_RXA_P14_1_IN
	.align	4
IfxAsclin0_RXA_P14_1_IN:	.type	object
	.size	IfxAsclin0_RXA_P14_1_IN,16
	.word	-268433920,-268192768
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_RXB_P15_3_IN',data,rom,cluster('IfxAsclin0_RXB_P15_3_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_RXB_P15_3_IN'
	.global	IfxAsclin0_RXB_P15_3_IN
	.align	4
IfxAsclin0_RXB_P15_3_IN:	.type	object
	.size	IfxAsclin0_RXB_P15_3_IN,16
	.word	-268433920,-268192512
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXA_P15_1_IN',data,rom,cluster('IfxAsclin1_RXA_P15_1_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXA_P15_1_IN'
	.global	IfxAsclin1_RXA_P15_1_IN
	.align	4
IfxAsclin1_RXA_P15_1_IN:	.type	object
	.size	IfxAsclin1_RXA_P15_1_IN,16
	.word	-268433664,-268192512
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXB_P15_5_IN',data,rom,cluster('IfxAsclin1_RXB_P15_5_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXB_P15_5_IN'
	.global	IfxAsclin1_RXB_P15_5_IN
	.align	4
IfxAsclin1_RXB_P15_5_IN:	.type	object
	.size	IfxAsclin1_RXB_P15_5_IN,16
	.word	-268433664,-268192512
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXC_P20_9_IN',data,rom,cluster('IfxAsclin1_RXC_P20_9_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXC_P20_9_IN'
	.global	IfxAsclin1_RXC_P20_9_IN
	.align	4
IfxAsclin1_RXC_P20_9_IN:	.type	object
	.size	IfxAsclin1_RXC_P20_9_IN,16
	.word	-268433664,-268189696
	.byte	9
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXD_P14_8_IN',data,rom,cluster('IfxAsclin1_RXD_P14_8_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXD_P14_8_IN'
	.global	IfxAsclin1_RXD_P14_8_IN
	.align	4
IfxAsclin1_RXD_P14_8_IN:	.type	object
	.size	IfxAsclin1_RXD_P14_8_IN,16
	.word	-268433664,-268192768
	.byte	8
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXE_P11_10_IN',data,rom,cluster('IfxAsclin1_RXE_P11_10_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXE_P11_10_IN'
	.global	IfxAsclin1_RXE_P11_10_IN
	.align	4
IfxAsclin1_RXE_P11_10_IN:	.type	object
	.size	IfxAsclin1_RXE_P11_10_IN,16
	.word	-268433664,-268193536
	.byte	10
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXF_P33_13_IN',data,rom,cluster('IfxAsclin1_RXF_P33_13_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXF_P33_13_IN'
	.global	IfxAsclin1_RXF_P33_13_IN
	.align	4
IfxAsclin1_RXF_P33_13_IN:	.type	object
	.size	IfxAsclin1_RXF_P33_13_IN,16
	.word	-268433664,-268184832
	.byte	13
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXG_P02_3_IN',data,rom,cluster('IfxAsclin1_RXG_P02_3_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_RXG_P02_3_IN'
	.global	IfxAsclin1_RXG_P02_3_IN
	.align	4
IfxAsclin1_RXG_P02_3_IN:	.type	object
	.size	IfxAsclin1_RXG_P02_3_IN,16
	.word	-268433664,-268197376
	.byte	3
	.space	3
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXA_P14_3_IN',data,rom,cluster('IfxAsclin2_RXA_P14_3_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXA_P14_3_IN'
	.global	IfxAsclin2_RXA_P14_3_IN
	.align	4
IfxAsclin2_RXA_P14_3_IN:	.type	object
	.size	IfxAsclin2_RXA_P14_3_IN,16
	.word	-268433408,-268192768
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXB_P02_1_IN',data,rom,cluster('IfxAsclin2_RXB_P02_1_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXB_P02_1_IN'
	.global	IfxAsclin2_RXB_P02_1_IN
	.align	4
IfxAsclin2_RXB_P02_1_IN:	.type	object
	.size	IfxAsclin2_RXB_P02_1_IN,16
	.word	-268433408,-268197376
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXD_P10_6_IN',data,rom,cluster('IfxAsclin2_RXD_P10_6_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXD_P10_6_IN'
	.global	IfxAsclin2_RXD_P10_6_IN
	.align	4
IfxAsclin2_RXD_P10_6_IN:	.type	object
	.size	IfxAsclin2_RXD_P10_6_IN,16
	.word	-268433408,-268193792
	.byte	6
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXE_P33_8_IN',data,rom,cluster('IfxAsclin2_RXE_P33_8_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXE_P33_8_IN'
	.global	IfxAsclin2_RXE_P33_8_IN
	.align	4
IfxAsclin2_RXE_P33_8_IN:	.type	object
	.size	IfxAsclin2_RXE_P33_8_IN,16
	.word	-268433408,-268184832
	.byte	8
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXG_P02_0_IN',data,rom,cluster('IfxAsclin2_RXG_P02_0_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_RXG_P02_0_IN'
	.global	IfxAsclin2_RXG_P02_0_IN
	.align	4
IfxAsclin2_RXG_P02_0_IN:	.type	object
	.size	IfxAsclin2_RXG_P02_0_IN,16
	.word	-268433408,-268197376
	.space	4
	.byte	6
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXA_P15_7_IN',data,rom,cluster('IfxAsclin3_RXA_P15_7_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXA_P15_7_IN'
	.global	IfxAsclin3_RXA_P15_7_IN
	.align	4
IfxAsclin3_RXA_P15_7_IN:	.type	object
	.size	IfxAsclin3_RXA_P15_7_IN,16
	.word	-268433152,-268192512
	.byte	7
	.space	7
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXC_P20_3_IN',data,rom,cluster('IfxAsclin3_RXC_P20_3_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXC_P20_3_IN'
	.global	IfxAsclin3_RXC_P20_3_IN
	.align	4
IfxAsclin3_RXC_P20_3_IN:	.type	object
	.size	IfxAsclin3_RXC_P20_3_IN,16
	.word	-268433152,-268189696
	.byte	3
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXD_P32_2_IN',data,rom,cluster('IfxAsclin3_RXD_P32_2_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXD_P32_2_IN'
	.global	IfxAsclin3_RXD_P32_2_IN
	.align	4
IfxAsclin3_RXD_P32_2_IN:	.type	object
	.size	IfxAsclin3_RXD_P32_2_IN,16
	.word	-268433152,-268185088
	.byte	2
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXE_P00_1_IN',data,rom,cluster('IfxAsclin3_RXE_P00_1_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXE_P00_1_IN'
	.global	IfxAsclin3_RXE_P00_1_IN
	.align	4
IfxAsclin3_RXE_P00_1_IN:	.type	object
	.size	IfxAsclin3_RXE_P00_1_IN,16
	.word	-268433152,-268197888
	.byte	1
	.space	3
	.byte	4
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXF_P21_6_IN',data,rom,cluster('IfxAsclin3_RXF_P21_6_IN')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_RXF_P21_6_IN'
	.global	IfxAsclin3_RXF_P21_6_IN
	.align	4
IfxAsclin3_RXF_P21_6_IN:	.type	object
	.size	IfxAsclin3_RXF_P21_6_IN,16
	.word	-268433152,-268189440
	.byte	6
	.space	3
	.byte	5
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_SCLK_P14_0_OUT',data,rom,cluster('IfxAsclin0_SCLK_P14_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_SCLK_P14_0_OUT'
	.global	IfxAsclin0_SCLK_P14_0_OUT
	.align	4
IfxAsclin0_SCLK_P14_0_OUT:	.type	object
	.size	IfxAsclin0_SCLK_P14_0_OUT,16
	.word	-268433920,-268192768
	.space	4
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_SCLK_P15_2_OUT',data,rom,cluster('IfxAsclin0_SCLK_P15_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_SCLK_P15_2_OUT'
	.global	IfxAsclin0_SCLK_P15_2_OUT
	.align	4
IfxAsclin0_SCLK_P15_2_OUT:	.type	object
	.size	IfxAsclin0_SCLK_P15_2_OUT,16
	.word	-268433920,-268192512
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P15_0_OUT',data,rom,cluster('IfxAsclin1_SCLK_P15_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P15_0_OUT'
	.global	IfxAsclin1_SCLK_P15_0_OUT
	.align	4
IfxAsclin1_SCLK_P15_0_OUT:	.type	object
	.size	IfxAsclin1_SCLK_P15_0_OUT,16
	.word	-268433664,-268192512
	.space	4
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P20_10_OUT',data,rom,cluster('IfxAsclin1_SCLK_P20_10_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P20_10_OUT'
	.global	IfxAsclin1_SCLK_P20_10_OUT
	.align	4
IfxAsclin1_SCLK_P20_10_OUT:	.type	object
	.size	IfxAsclin1_SCLK_P20_10_OUT,16
	.word	-268433664,-268189696
	.byte	10
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P33_11_OUT',data,rom,cluster('IfxAsclin1_SCLK_P33_11_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P33_11_OUT'
	.global	IfxAsclin1_SCLK_P33_11_OUT
	.align	4
IfxAsclin1_SCLK_P33_11_OUT:	.type	object
	.size	IfxAsclin1_SCLK_P33_11_OUT,16
	.word	-268433664,-268184832
	.byte	11
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P33_12_OUT',data,rom,cluster('IfxAsclin1_SCLK_P33_12_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SCLK_P33_12_OUT'
	.global	IfxAsclin1_SCLK_P33_12_OUT
	.align	4
IfxAsclin1_SCLK_P33_12_OUT:	.type	object
	.size	IfxAsclin1_SCLK_P33_12_OUT,16
	.word	-268433664,-268184832
	.byte	12
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P02_4_OUT',data,rom,cluster('IfxAsclin2_SCLK_P02_4_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P02_4_OUT'
	.global	IfxAsclin2_SCLK_P02_4_OUT
	.align	4
IfxAsclin2_SCLK_P02_4_OUT:	.type	object
	.size	IfxAsclin2_SCLK_P02_4_OUT,16
	.word	-268433408,-268197376
	.byte	4
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P10_6_OUT',data,rom,cluster('IfxAsclin2_SCLK_P10_6_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P10_6_OUT'
	.global	IfxAsclin2_SCLK_P10_6_OUT
	.align	4
IfxAsclin2_SCLK_P10_6_OUT:	.type	object
	.size	IfxAsclin2_SCLK_P10_6_OUT,16
	.word	-268433408,-268193792
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P14_2_OUT',data,rom,cluster('IfxAsclin2_SCLK_P14_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P14_2_OUT'
	.global	IfxAsclin2_SCLK_P14_2_OUT
	.align	4
IfxAsclin2_SCLK_P14_2_OUT:	.type	object
	.size	IfxAsclin2_SCLK_P14_2_OUT,16
	.word	-268433408,-268192768
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P33_7_OUT',data,rom,cluster('IfxAsclin2_SCLK_P33_7_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P33_7_OUT'
	.global	IfxAsclin2_SCLK_P33_7_OUT
	.align	4
IfxAsclin2_SCLK_P33_7_OUT:	.type	object
	.size	IfxAsclin2_SCLK_P33_7_OUT,16
	.word	-268433408,-268184832
	.byte	7
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P33_9_OUT',data,rom,cluster('IfxAsclin2_SCLK_P33_9_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SCLK_P33_9_OUT'
	.global	IfxAsclin2_SCLK_P33_9_OUT
	.align	4
IfxAsclin2_SCLK_P33_9_OUT:	.type	object
	.size	IfxAsclin2_SCLK_P33_9_OUT,16
	.word	-268433408,-268184832
	.byte	9
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P00_0_OUT',data,rom,cluster('IfxAsclin3_SCLK_P00_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P00_0_OUT'
	.global	IfxAsclin3_SCLK_P00_0_OUT
	.align	4
IfxAsclin3_SCLK_P00_0_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P00_0_OUT,16
	.word	-268433152,-268197888
	.space	4
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P00_2_OUT',data,rom,cluster('IfxAsclin3_SCLK_P00_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P00_2_OUT'
	.global	IfxAsclin3_SCLK_P00_2_OUT
	.align	4
IfxAsclin3_SCLK_P00_2_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P00_2_OUT,16
	.word	-268433152,-268197888
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P15_6_OUT',data,rom,cluster('IfxAsclin3_SCLK_P15_6_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P15_6_OUT'
	.global	IfxAsclin3_SCLK_P15_6_OUT
	.align	4
IfxAsclin3_SCLK_P15_6_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P15_6_OUT,16
	.word	-268433152,-268192512
	.byte	6
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P15_8_OUT',data,rom,cluster('IfxAsclin3_SCLK_P15_8_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P15_8_OUT'
	.global	IfxAsclin3_SCLK_P15_8_OUT
	.align	4
IfxAsclin3_SCLK_P15_8_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P15_8_OUT,16
	.word	-268433152,-268192512
	.byte	8
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P20_0_OUT',data,rom,cluster('IfxAsclin3_SCLK_P20_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P20_0_OUT'
	.global	IfxAsclin3_SCLK_P20_0_OUT
	.align	4
IfxAsclin3_SCLK_P20_0_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P20_0_OUT,16
	.word	-268433152,-268189696
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P21_5_OUT',data,rom,cluster('IfxAsclin3_SCLK_P21_5_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P21_5_OUT'
	.global	IfxAsclin3_SCLK_P21_5_OUT
	.align	4
IfxAsclin3_SCLK_P21_5_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P21_5_OUT,16
	.word	-268433152,-268189440
	.byte	5
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P21_7_OUT',data,rom,cluster('IfxAsclin3_SCLK_P21_7_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P21_7_OUT'
	.global	IfxAsclin3_SCLK_P21_7_OUT
	.align	4
IfxAsclin3_SCLK_P21_7_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P21_7_OUT,16
	.word	-268433152,-268189440
	.byte	7
	.space	3
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P32_3_OUT',data,rom,cluster('IfxAsclin3_SCLK_P32_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P32_3_OUT'
	.global	IfxAsclin3_SCLK_P32_3_OUT
	.align	4
IfxAsclin3_SCLK_P32_3_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P32_3_OUT,16
	.word	-268433152,-268185088
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P33_2_OUT',data,rom,cluster('IfxAsclin3_SCLK_P33_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SCLK_P33_2_OUT'
	.global	IfxAsclin3_SCLK_P33_2_OUT
	.align	4
IfxAsclin3_SCLK_P33_2_OUT:	.type	object
	.size	IfxAsclin3_SCLK_P33_2_OUT,16
	.word	-268433152,-268184832
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SLSO_P14_3_OUT',data,rom,cluster('IfxAsclin1_SLSO_P14_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SLSO_P14_3_OUT'
	.global	IfxAsclin1_SLSO_P14_3_OUT
	.align	4
IfxAsclin1_SLSO_P14_3_OUT:	.type	object
	.size	IfxAsclin1_SLSO_P14_3_OUT,16
	.word	-268433664,-268192768
	.byte	3
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SLSO_P20_8_OUT',data,rom,cluster('IfxAsclin1_SLSO_P20_8_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SLSO_P20_8_OUT'
	.global	IfxAsclin1_SLSO_P20_8_OUT
	.align	4
IfxAsclin1_SLSO_P20_8_OUT:	.type	object
	.size	IfxAsclin1_SLSO_P20_8_OUT,16
	.word	-268433664,-268189696
	.byte	8
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SLSO_P33_10_OUT',data,rom,cluster('IfxAsclin1_SLSO_P33_10_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_SLSO_P33_10_OUT'
	.global	IfxAsclin1_SLSO_P33_10_OUT
	.align	4
IfxAsclin1_SLSO_P33_10_OUT:	.type	object
	.size	IfxAsclin1_SLSO_P33_10_OUT,16
	.word	-268433664,-268184832
	.byte	10
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SLSO_P02_3_OUT',data,rom,cluster('IfxAsclin2_SLSO_P02_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SLSO_P02_3_OUT'
	.global	IfxAsclin2_SLSO_P02_3_OUT
	.align	4
IfxAsclin2_SLSO_P02_3_OUT:	.type	object
	.size	IfxAsclin2_SLSO_P02_3_OUT,16
	.word	-268433408,-268197376
	.byte	3
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SLSO_P10_5_OUT',data,rom,cluster('IfxAsclin2_SLSO_P10_5_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SLSO_P10_5_OUT'
	.global	IfxAsclin2_SLSO_P10_5_OUT
	.align	4
IfxAsclin2_SLSO_P10_5_OUT:	.type	object
	.size	IfxAsclin2_SLSO_P10_5_OUT,16
	.word	-268433408,-268193792
	.byte	5
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SLSO_P33_6_OUT',data,rom,cluster('IfxAsclin2_SLSO_P33_6_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_SLSO_P33_6_OUT'
	.global	IfxAsclin2_SLSO_P33_6_OUT
	.align	4
IfxAsclin2_SLSO_P33_6_OUT:	.type	object
	.size	IfxAsclin2_SLSO_P33_6_OUT,16
	.word	-268433408,-268184832
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P00_3_OUT',data,rom,cluster('IfxAsclin3_SLSO_P00_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P00_3_OUT'
	.global	IfxAsclin3_SLSO_P00_3_OUT
	.align	4
IfxAsclin3_SLSO_P00_3_OUT:	.type	object
	.size	IfxAsclin3_SLSO_P00_3_OUT,16
	.word	-268433152,-268197888
	.byte	3
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P14_3_OUT',data,rom,cluster('IfxAsclin3_SLSO_P14_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P14_3_OUT'
	.global	IfxAsclin3_SLSO_P14_3_OUT
	.align	4
IfxAsclin3_SLSO_P14_3_OUT:	.type	object
	.size	IfxAsclin3_SLSO_P14_3_OUT,16
	.word	-268433152,-268192768
	.byte	3
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P21_2_OUT',data,rom,cluster('IfxAsclin3_SLSO_P21_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P21_2_OUT'
	.global	IfxAsclin3_SLSO_P21_2_OUT
	.align	4
IfxAsclin3_SLSO_P21_2_OUT:	.type	object
	.size	IfxAsclin3_SLSO_P21_2_OUT,16
	.word	-268433152,-268189440
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P21_6_OUT',data,rom,cluster('IfxAsclin3_SLSO_P21_6_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P21_6_OUT'
	.global	IfxAsclin3_SLSO_P21_6_OUT
	.align	4
IfxAsclin3_SLSO_P21_6_OUT:	.type	object
	.size	IfxAsclin3_SLSO_P21_6_OUT,16
	.word	-268433152,-268189440
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P33_1_OUT',data,rom,cluster('IfxAsclin3_SLSO_P33_1_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_SLSO_P33_1_OUT'
	.global	IfxAsclin3_SLSO_P33_1_OUT
	.align	4
IfxAsclin3_SLSO_P33_1_OUT:	.type	object
	.size	IfxAsclin3_SLSO_P33_1_OUT,16
	.word	-268433152,-268184832
	.byte	1
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P14_0_OUT',data,rom,cluster('IfxAsclin0_TX_P14_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P14_0_OUT'
	.global	IfxAsclin0_TX_P14_0_OUT
	.align	4
IfxAsclin0_TX_P14_0_OUT:	.type	object
	.size	IfxAsclin0_TX_P14_0_OUT,16
	.word	-268433920,-268192768
	.space	4
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P14_1_OUT',data,rom,cluster('IfxAsclin0_TX_P14_1_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P14_1_OUT'
	.global	IfxAsclin0_TX_P14_1_OUT
	.align	4
IfxAsclin0_TX_P14_1_OUT:	.type	object
	.size	IfxAsclin0_TX_P14_1_OUT,16
	.word	-268433920,-268192768
	.byte	1
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P15_2_OUT',data,rom,cluster('IfxAsclin0_TX_P15_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P15_2_OUT'
	.global	IfxAsclin0_TX_P15_2_OUT
	.align	4
IfxAsclin0_TX_P15_2_OUT:	.type	object
	.size	IfxAsclin0_TX_P15_2_OUT,16
	.word	-268433920,-268192512
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P15_3_OUT',data,rom,cluster('IfxAsclin0_TX_P15_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin0_TX_P15_3_OUT'
	.global	IfxAsclin0_TX_P15_3_OUT
	.align	4
IfxAsclin0_TX_P15_3_OUT:	.type	object
	.size	IfxAsclin0_TX_P15_3_OUT,16
	.word	-268433920,-268192512
	.byte	3
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P02_2_OUT',data,rom,cluster('IfxAsclin1_TX_P02_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P02_2_OUT'
	.global	IfxAsclin1_TX_P02_2_OUT
	.align	4
IfxAsclin1_TX_P02_2_OUT:	.type	object
	.size	IfxAsclin1_TX_P02_2_OUT,16
	.word	-268433664,-268197376
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P11_12_OUT',data,rom,cluster('IfxAsclin1_TX_P11_12_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P11_12_OUT'
	.global	IfxAsclin1_TX_P11_12_OUT
	.align	4
IfxAsclin1_TX_P11_12_OUT:	.type	object
	.size	IfxAsclin1_TX_P11_12_OUT,16
	.word	-268433664,-268193536
	.byte	12
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P14_10_OUT',data,rom,cluster('IfxAsclin1_TX_P14_10_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P14_10_OUT'
	.global	IfxAsclin1_TX_P14_10_OUT
	.align	4
IfxAsclin1_TX_P14_10_OUT:	.type	object
	.size	IfxAsclin1_TX_P14_10_OUT,16
	.word	-268433664,-268192768
	.byte	10
	.space	3
	.byte	160
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_0_OUT',data,rom,cluster('IfxAsclin1_TX_P15_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_0_OUT'
	.global	IfxAsclin1_TX_P15_0_OUT
	.align	4
IfxAsclin1_TX_P15_0_OUT:	.type	object
	.size	IfxAsclin1_TX_P15_0_OUT,16
	.word	-268433664,-268192512
	.space	4
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_1_OUT',data,rom,cluster('IfxAsclin1_TX_P15_1_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_1_OUT'
	.global	IfxAsclin1_TX_P15_1_OUT
	.align	4
IfxAsclin1_TX_P15_1_OUT:	.type	object
	.size	IfxAsclin1_TX_P15_1_OUT,16
	.word	-268433664,-268192512
	.byte	1
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_4_OUT',data,rom,cluster('IfxAsclin1_TX_P15_4_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_4_OUT'
	.global	IfxAsclin1_TX_P15_4_OUT
	.align	4
IfxAsclin1_TX_P15_4_OUT:	.type	object
	.size	IfxAsclin1_TX_P15_4_OUT,16
	.word	-268433664,-268192512
	.byte	4
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_5_OUT',data,rom,cluster('IfxAsclin1_TX_P15_5_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P15_5_OUT'
	.global	IfxAsclin1_TX_P15_5_OUT
	.align	4
IfxAsclin1_TX_P15_5_OUT:	.type	object
	.size	IfxAsclin1_TX_P15_5_OUT,16
	.word	-268433664,-268192512
	.byte	5
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P20_10_OUT',data,rom,cluster('IfxAsclin1_TX_P20_10_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P20_10_OUT'
	.global	IfxAsclin1_TX_P20_10_OUT
	.align	4
IfxAsclin1_TX_P20_10_OUT:	.type	object
	.size	IfxAsclin1_TX_P20_10_OUT,16
	.word	-268433664,-268189696
	.byte	10
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P33_12_OUT',data,rom,cluster('IfxAsclin1_TX_P33_12_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P33_12_OUT'
	.global	IfxAsclin1_TX_P33_12_OUT
	.align	4
IfxAsclin1_TX_P33_12_OUT:	.type	object
	.size	IfxAsclin1_TX_P33_12_OUT,16
	.word	-268433664,-268184832
	.byte	12
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P33_13_OUT',data,rom,cluster('IfxAsclin1_TX_P33_13_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin1_TX_P33_13_OUT'
	.global	IfxAsclin1_TX_P33_13_OUT
	.align	4
IfxAsclin1_TX_P33_13_OUT:	.type	object
	.size	IfxAsclin1_TX_P33_13_OUT,16
	.word	-268433664,-268184832
	.byte	13
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P02_0_OUT',data,rom,cluster('IfxAsclin2_TX_P02_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P02_0_OUT'
	.global	IfxAsclin2_TX_P02_0_OUT
	.align	4
IfxAsclin2_TX_P02_0_OUT:	.type	object
	.size	IfxAsclin2_TX_P02_0_OUT,16
	.word	-268433408,-268197376
	.space	4
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P10_5_OUT',data,rom,cluster('IfxAsclin2_TX_P10_5_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P10_5_OUT'
	.global	IfxAsclin2_TX_P10_5_OUT
	.align	4
IfxAsclin2_TX_P10_5_OUT:	.type	object
	.size	IfxAsclin2_TX_P10_5_OUT,16
	.word	-268433408,-268193792
	.byte	5
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P14_2_OUT',data,rom,cluster('IfxAsclin2_TX_P14_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P14_2_OUT'
	.global	IfxAsclin2_TX_P14_2_OUT
	.align	4
IfxAsclin2_TX_P14_2_OUT:	.type	object
	.size	IfxAsclin2_TX_P14_2_OUT,16
	.word	-268433408,-268192768
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P14_3_OUT',data,rom,cluster('IfxAsclin2_TX_P14_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P14_3_OUT'
	.global	IfxAsclin2_TX_P14_3_OUT
	.align	4
IfxAsclin2_TX_P14_3_OUT:	.type	object
	.size	IfxAsclin2_TX_P14_3_OUT,16
	.word	-268433408,-268192768
	.byte	3
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P33_8_OUT',data,rom,cluster('IfxAsclin2_TX_P33_8_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P33_8_OUT'
	.global	IfxAsclin2_TX_P33_8_OUT
	.align	4
IfxAsclin2_TX_P33_8_OUT:	.type	object
	.size	IfxAsclin2_TX_P33_8_OUT,16
	.word	-268433408,-268184832
	.byte	8
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P33_9_OUT',data,rom,cluster('IfxAsclin2_TX_P33_9_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin2_TX_P33_9_OUT'
	.global	IfxAsclin2_TX_P33_9_OUT
	.align	4
IfxAsclin2_TX_P33_9_OUT:	.type	object
	.size	IfxAsclin2_TX_P33_9_OUT,16
	.word	-268433408,-268184832
	.byte	9
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P00_0_OUT',data,rom,cluster('IfxAsclin3_TX_P00_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P00_0_OUT'
	.global	IfxAsclin3_TX_P00_0_OUT
	.align	4
IfxAsclin3_TX_P00_0_OUT:	.type	object
	.size	IfxAsclin3_TX_P00_0_OUT,16
	.word	-268433152,-268197888
	.space	4
	.byte	152
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P00_1_OUT',data,rom,cluster('IfxAsclin3_TX_P00_1_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P00_1_OUT'
	.global	IfxAsclin3_TX_P00_1_OUT
	.align	4
IfxAsclin3_TX_P00_1_OUT:	.type	object
	.size	IfxAsclin3_TX_P00_1_OUT,16
	.word	-268433152,-268197888
	.byte	1
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P15_6_OUT',data,rom,cluster('IfxAsclin3_TX_P15_6_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P15_6_OUT'
	.global	IfxAsclin3_TX_P15_6_OUT
	.align	4
IfxAsclin3_TX_P15_6_OUT:	.type	object
	.size	IfxAsclin3_TX_P15_6_OUT,16
	.word	-268433152,-268192512
	.byte	6
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P15_7_OUT',data,rom,cluster('IfxAsclin3_TX_P15_7_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P15_7_OUT'
	.global	IfxAsclin3_TX_P15_7_OUT
	.align	4
IfxAsclin3_TX_P15_7_OUT:	.type	object
	.size	IfxAsclin3_TX_P15_7_OUT,16
	.word	-268433152,-268192512
	.byte	7
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P20_0_OUT',data,rom,cluster('IfxAsclin3_TX_P20_0_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P20_0_OUT'
	.global	IfxAsclin3_TX_P20_0_OUT
	.align	4
IfxAsclin3_TX_P20_0_OUT:	.type	object
	.size	IfxAsclin3_TX_P20_0_OUT,16
	.word	-268433152,-268189696
	.space	4
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P20_3_OUT',data,rom,cluster('IfxAsclin3_TX_P20_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P20_3_OUT'
	.global	IfxAsclin3_TX_P20_3_OUT
	.align	4
IfxAsclin3_TX_P20_3_OUT:	.type	object
	.size	IfxAsclin3_TX_P20_3_OUT,16
	.word	-268433152,-268189696
	.byte	3
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P21_7_OUT',data,rom,cluster('IfxAsclin3_TX_P21_7_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P21_7_OUT'
	.global	IfxAsclin3_TX_P21_7_OUT
	.align	4
IfxAsclin3_TX_P21_7_OUT:	.type	object
	.size	IfxAsclin3_TX_P21_7_OUT,16
	.word	-268433152,-268189440
	.byte	7
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P32_2_OUT',data,rom,cluster('IfxAsclin3_TX_P32_2_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P32_2_OUT'
	.global	IfxAsclin3_TX_P32_2_OUT
	.align	4
IfxAsclin3_TX_P32_2_OUT:	.type	object
	.size	IfxAsclin3_TX_P32_2_OUT,16
	.word	-268433152,-268185088
	.byte	2
	.space	3
	.byte	144
	.space	3
	.sdecl	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P32_3_OUT',data,rom,cluster('IfxAsclin3_TX_P32_3_OUT')
	.sect	'.rodata.IfxAsclin_PinMap.IfxAsclin3_TX_P32_3_OUT'
	.global	IfxAsclin3_TX_P32_3_OUT
	.align	4
IfxAsclin3_TX_P32_3_OUT:	.type	object
	.size	IfxAsclin3_TX_P32_3_OUT,16
	.word	-268433152,-268185088
	.byte	3
	.space	3
	.byte	144
	.space	3
	.sdecl	'.data.IfxAsclin_PinMap.IfxAsclin_Cts_In_pinTable',data,cluster('IfxAsclin_Cts_In_pinTable')
	.sect	'.data.IfxAsclin_PinMap.IfxAsclin_Cts_In_pinTable'
	.global	IfxAsclin_Cts_In_pinTable
	.align	4
IfxAsclin_Cts_In_pinTable:	.type	object
	.size	IfxAsclin_Cts_In_pinTable,32
	.word	IfxAsclin0_CTSA_P14_9_IN
	.space	4
	.word	IfxAsclin1_CTSA_P20_7_IN,IfxAsclin1_CTSB_P32_4_IN,IfxAsclin2_CTSA_P10_7_IN,IfxAsclin2_CTSB_P33_5_IN
	.word	IfxAsclin3_CTSA_P00_12_IN
	.space	4
	.sdecl	'.data.IfxAsclin_PinMap.IfxAsclin_Rts_Out_pinTable',data,cluster('IfxAsclin_Rts_Out_pinTable')
	.sect	'.data.IfxAsclin_PinMap.IfxAsclin_Rts_Out_pinTable'
	.global	IfxAsclin_Rts_Out_pinTable
	.align	4
IfxAsclin_Rts_Out_pinTable:	.type	object
	.size	IfxAsclin_Rts_Out_pinTable,32
	.word	IfxAsclin0_RTS_P14_7_OUT
	.space	4
	.word	IfxAsclin1_RTS_P20_6_OUT,IfxAsclin1_RTS_P23_1_OUT,IfxAsclin2_RTS_P10_8_OUT,IfxAsclin2_RTS_P33_4_OUT
	.word	IfxAsclin3_RTS_P00_9_OUT
	.space	4
	.sdecl	'.data.IfxAsclin_PinMap.IfxAsclin_Rx_In_pinTable',data,cluster('IfxAsclin_Rx_In_pinTable')
	.sect	'.data.IfxAsclin_PinMap.IfxAsclin_Rx_In_pinTable'
	.global	IfxAsclin_Rx_In_pinTable
	.align	4
IfxAsclin_Rx_In_pinTable:	.type	object
	.size	IfxAsclin_Rx_In_pinTable,112
	.word	IfxAsclin0_RXA_P14_1_IN,IfxAsclin0_RXB_P15_3_IN
	.space	20
	.word	IfxAsclin1_RXA_P15_1_IN,IfxAsclin1_RXB_P15_5_IN,IfxAsclin1_RXC_P20_9_IN,IfxAsclin1_RXD_P14_8_IN,IfxAsclin1_RXE_P11_10_IN,IfxAsclin1_RXF_P33_13_IN,IfxAsclin1_RXG_P02_3_IN,IfxAsclin2_RXA_P14_3_IN
	.word	IfxAsclin2_RXB_P02_1_IN
	.space	4
	.word	IfxAsclin2_RXD_P10_6_IN,IfxAsclin2_RXE_P33_8_IN
	.space	4
	.word	IfxAsclin2_RXG_P02_0_IN,IfxAsclin3_RXA_P15_7_IN
	.space	4
	.word	IfxAsclin3_RXC_P20_3_IN,IfxAsclin3_RXD_P32_2_IN,IfxAsclin3_RXE_P00_1_IN,IfxAsclin3_RXF_P21_6_IN
	.space	4
	.sdecl	'.data.IfxAsclin_PinMap.IfxAsclin_Sclk_Out_pinTable',data,cluster('IfxAsclin_Sclk_Out_pinTable')
	.sect	'.data.IfxAsclin_PinMap.IfxAsclin_Sclk_Out_pinTable'
	.global	IfxAsclin_Sclk_Out_pinTable
	.align	4
IfxAsclin_Sclk_Out_pinTable:	.type	object
	.size	IfxAsclin_Sclk_Out_pinTable,144
	.word	IfxAsclin0_SCLK_P14_0_OUT,IfxAsclin0_SCLK_P15_2_OUT
	.space	28
	.word	IfxAsclin1_SCLK_P15_0_OUT,IfxAsclin1_SCLK_P20_10_OUT,IfxAsclin1_SCLK_P33_11_OUT,IfxAsclin1_SCLK_P33_12_OUT
	.space	20
	.word	IfxAsclin2_SCLK_P02_4_OUT,IfxAsclin2_SCLK_P10_6_OUT,IfxAsclin2_SCLK_P14_2_OUT,IfxAsclin2_SCLK_P33_7_OUT
	.word	IfxAsclin2_SCLK_P33_9_OUT
	.space	16
	.word	IfxAsclin3_SCLK_P00_0_OUT,IfxAsclin3_SCLK_P00_2_OUT,IfxAsclin3_SCLK_P15_6_OUT,IfxAsclin3_SCLK_P15_8_OUT,IfxAsclin3_SCLK_P20_0_OUT,IfxAsclin3_SCLK_P21_5_OUT,IfxAsclin3_SCLK_P21_7_OUT,IfxAsclin3_SCLK_P32_3_OUT
	.word	IfxAsclin3_SCLK_P33_2_OUT
	.sdecl	'.data.IfxAsclin_PinMap.IfxAsclin_Slso_Out_pinTable',data,cluster('IfxAsclin_Slso_Out_pinTable')
	.sect	'.data.IfxAsclin_PinMap.IfxAsclin_Slso_Out_pinTable'
	.global	IfxAsclin_Slso_Out_pinTable
	.align	4
IfxAsclin_Slso_Out_pinTable:	.type	object
	.size	IfxAsclin_Slso_Out_pinTable,80
	.space	20
	.word	IfxAsclin1_SLSO_P14_3_OUT,IfxAsclin1_SLSO_P20_8_OUT,IfxAsclin1_SLSO_P33_10_OUT
	.space	8
	.word	IfxAsclin2_SLSO_P02_3_OUT,IfxAsclin2_SLSO_P10_5_OUT,IfxAsclin2_SLSO_P33_6_OUT
	.space	8
	.word	IfxAsclin3_SLSO_P00_3_OUT,IfxAsclin3_SLSO_P14_3_OUT,IfxAsclin3_SLSO_P21_2_OUT,IfxAsclin3_SLSO_P21_6_OUT
	.word	IfxAsclin3_SLSO_P33_1_OUT
	.sdecl	'.data.IfxAsclin_PinMap.IfxAsclin_Tx_Out_pinTable',data,cluster('IfxAsclin_Tx_Out_pinTable')
	.sect	'.data.IfxAsclin_PinMap.IfxAsclin_Tx_Out_pinTable'
	.global	IfxAsclin_Tx_Out_pinTable
	.align	4
IfxAsclin_Tx_Out_pinTable:	.type	object
	.size	IfxAsclin_Tx_Out_pinTable,160
	.word	IfxAsclin0_TX_P14_0_OUT,IfxAsclin0_TX_P14_1_OUT,IfxAsclin0_TX_P15_2_OUT,IfxAsclin0_TX_P15_3_OUT
	.space	24
	.word	IfxAsclin1_TX_P02_2_OUT,IfxAsclin1_TX_P11_12_OUT,IfxAsclin1_TX_P14_10_OUT,IfxAsclin1_TX_P15_0_OUT,IfxAsclin1_TX_P15_1_OUT,IfxAsclin1_TX_P15_4_OUT,IfxAsclin1_TX_P15_5_OUT,IfxAsclin1_TX_P20_10_OUT
	.word	IfxAsclin1_TX_P33_12_OUT,IfxAsclin1_TX_P33_13_OUT,IfxAsclin2_TX_P02_0_OUT,IfxAsclin2_TX_P10_5_OUT,IfxAsclin2_TX_P14_2_OUT,IfxAsclin2_TX_P14_3_OUT,IfxAsclin2_TX_P33_8_OUT,IfxAsclin2_TX_P33_9_OUT
	.space	16
	.word	IfxAsclin3_TX_P00_0_OUT,IfxAsclin3_TX_P00_1_OUT,IfxAsclin3_TX_P15_6_OUT,IfxAsclin3_TX_P15_7_OUT,IfxAsclin3_TX_P20_0_OUT,IfxAsclin3_TX_P20_3_OUT,IfxAsclin3_TX_P21_7_OUT,IfxAsclin3_TX_P32_2_OUT
	.word	IfxAsclin3_TX_P32_3_OUT
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	45667
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	240
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	243
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	288
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	300
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	380
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	354
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	386
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	386
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	354
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	534
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	495
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	850
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1421
	.byte	4,2,35,0,0,14,4
	.word	495
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	495
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	495
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	495
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	495
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	495
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1549
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	495
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	495
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	495
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	495
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	495
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1764
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	495
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	495
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	495
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	495
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	495
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1979
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	495
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	495
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	495
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	495
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	495
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2196
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2416
	.byte	4,2,35,0,0,14,24
	.word	495
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	495
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	495
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	495
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	495
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	495
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	495
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2739
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	495
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	495
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	495
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	495
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	495
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	495
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3043
	.byte	4,2,35,0,0,14,8
	.word	495
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3368
	.byte	4,2,35,0,0,14,12
	.word	495
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3708
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	472
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4074
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4360
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4507
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	472
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4676
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	512
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	512
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	512
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5197
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5371
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	495
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5703
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	512
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	495
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6036
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6384
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	495
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6508
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6592
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	495
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6772
	.byte	4,2,35,0,0,14,76
	.word	495
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7025
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	495
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7112
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	810
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1381
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1500
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1540
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1724
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1939
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2156
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2376
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1540
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2690
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2730
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3003
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3319
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3359
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3659
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3699
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4034
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4320
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3359
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4467
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4636
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4808
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4983
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5157
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5331
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5507
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5663
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5996
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6344
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3359
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6468
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6717
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6976
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7016
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7072
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7639
	.byte	4,3,35,252,1,0,16
	.word	7679
	.byte	3
	.word	8282
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8287
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	495
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8292
	.byte	6,0,19
	.word	248
	.byte	20
	.word	274
	.byte	6,0,19
	.word	309
	.byte	20
	.word	341
	.byte	6,0,19
	.word	391
	.byte	20
	.word	410
	.byte	6,0,19
	.word	426
	.byte	20
	.word	441
	.byte	20
	.word	455
	.byte	6,0,19
	.word	8395
	.byte	20
	.word	8423
	.byte	20
	.word	8437
	.byte	20
	.word	8455
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8548
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	472
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	488
	.byte	22,1,3
	.word	8616
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8618
	.byte	10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,6,79,3
	.word	8641
	.byte	10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,6,85,3
	.word	9204
	.byte	10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,6,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	512
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	495
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	495
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	495
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,6,97,3
	.word	9287
	.byte	10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,6,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	512
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_BRD_Bits',0,6,106,3
	.word	9499
	.byte	10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,6,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	512
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	512
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_BRG_Bits',0,6,115,3
	.word	9641
	.byte	10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,6,118,16,4,11
	.byte	'DISR',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_CLC_Bits',0,6,125,3
	.word	9786
	.byte	10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,6,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	495
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	472
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_CSR_Bits',0,6,133,1,3
	.word	9933
	.byte	10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,6,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	512
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,6,145,1,3
	.word	10045
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,6,148,1,16,4,11
	.byte	'TH',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	512
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,6,177,1,3
	.word	10234
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,6,180,1,16,4,11
	.byte	'THC',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	512
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,6,209,1,3
	.word	10711
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,6,212,1,16,4,11
	.byte	'THE',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	512
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,6,238,1,3
	.word	11222
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,6,241,1,16,4,11
	.byte	'THS',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	512
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,6,142,2,3
	.word	11684
	.byte	10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,6,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	512
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	495
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	495
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	495
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	512
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,6,158,2,3
	.word	12191
	.byte	10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,6,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ASCLIN_ID_Bits',0,6,166,2,3
	.word	12450
	.byte	10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,6,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	512
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	495
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	495
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	512
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,6,184,2,3
	.word	12563
	.byte	10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,6,187,2,16,4,11
	.byte	'RST',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,6,192,2,3
	.word	12848
	.byte	10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,6,195,2,16,4,11
	.byte	'RST',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	472
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,6,199,2,3
	.word	12965
	.byte	10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,6,202,2,16,4,11
	.byte	'CLR',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	472
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,6,206,2,3
	.word	13063
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,6,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	495
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	472
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,6,213,2,3
	.word	13165
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,6,225,2,3
	.word	13275
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,6,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,6,232,2,3
	.word	13468
	.byte	10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,6,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	495
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	495
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_OCS_Bits',0,6,242,2,3
	.word	13579
	.byte	10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,6,245,2,16,4,11
	.byte	'DATA',0,4
	.word	472
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,6,248,2,3
	.word	13731
	.byte	10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,6,251,2,16,4,11
	.byte	'DATA',0,4
	.word	472
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,6,254,2,3
	.word	13810
	.byte	10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,6,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	495
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	495
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	495
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	512
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,6,140,3,3
	.word	13891
	.byte	10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,6,143,3,16,4,11
	.byte	'DATA',0,4
	.word	472
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,6,146,3,3
	.word	14127
	.byte	10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,6,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	495
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	495
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	495
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	512
	.byte	11,0,2,35,2,0,21
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,6,159,3,3
	.word	14206
	.byte	12,6,167,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8641
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_ACCEN0',0,6,172,3,3
	.word	14426
	.byte	12,6,175,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9204
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_ACCEN1',0,6,180,3,3
	.word	14493
	.byte	12,6,183,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9287
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_BITCON',0,6,188,3,3
	.word	14560
	.byte	12,6,191,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9499
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_BRD',0,6,196,3,3
	.word	14627
	.byte	12,6,199,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9641
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_BRG',0,6,204,3,3
	.word	14691
	.byte	12,6,207,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9786
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_CLC',0,6,212,3,3
	.word	14755
	.byte	12,6,215,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9933
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_CSR',0,6,220,3,3
	.word	14819
	.byte	12,6,223,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10045
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_DATCON',0,6,228,3,3
	.word	14883
	.byte	12,6,231,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10234
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_FLAGS',0,6,236,3,3
	.word	14950
	.byte	12,6,239,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10711
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,6,244,3,3
	.word	15016
	.byte	12,6,247,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11222
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,6,252,3,3
	.word	15087
	.byte	12,6,255,3,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11684
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_FLAGSSET',0,6,132,4,3
	.word	15159
	.byte	12,6,135,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12191
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_FRAMECON',0,6,140,4,3
	.word	15228
	.byte	12,6,143,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12450
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_ID',0,6,148,4,3
	.word	15297
	.byte	12,6,151,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12563
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_IOCR',0,6,156,4,3
	.word	15360
	.byte	12,6,159,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12848
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_KRST0',0,6,164,4,3
	.word	15425
	.byte	12,6,167,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12965
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_KRST1',0,6,172,4,3
	.word	15491
	.byte	12,6,175,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13063
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_KRSTCLR',0,6,180,4,3
	.word	15557
	.byte	12,6,183,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13165
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,6,188,4,3
	.word	15625
	.byte	12,6,191,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13275
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_LIN_CON',0,6,196,4,3
	.word	15696
	.byte	12,6,199,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13468
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,6,204,4,3
	.word	15764
	.byte	12,6,207,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13579
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_OCS',0,6,212,4,3
	.word	15835
	.byte	12,6,215,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13731
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_RXDATA',0,6,220,4,3
	.word	15899
	.byte	12,6,223,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13810
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_RXDATAD',0,6,228,4,3
	.word	15966
	.byte	12,6,231,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13891
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,6,236,4,3
	.word	16034
	.byte	12,6,239,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14127
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_TXDATA',0,6,244,4,3
	.word	16104
	.byte	12,6,247,4,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14206
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,6,252,4,3
	.word	16171
	.byte	10
	.byte	'_Ifx_ASCLIN_LIN',0,6,135,5,25,12,13
	.byte	'CON',0
	.word	15696
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	15625
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	15764
	.byte	4,2,35,8,0,16
	.word	16241
	.byte	21
	.byte	'Ifx_ASCLIN_LIN',0,6,140,5,3
	.word	16309
	.byte	16
	.word	16241
	.byte	14,148,1
	.word	495
	.byte	15,147,1,0,10
	.byte	'_Ifx_ASCLIN',0,6,153,5,25,128,2,13
	.byte	'CLC',0
	.word	14755
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	15360
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	15297
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	16171
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	16034
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	14560
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	15228
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	14883
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	14691
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	14627
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	16338
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	14950
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	15159
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	15016
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15087
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	16104
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15899
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	14819
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15966
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	16343
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	15835
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	15557
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	15491
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	15425
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	14493
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	14426
	.byte	4,3,35,252,1,0,16
	.word	16354
	.byte	21
	.byte	'Ifx_ASCLIN',0,6,181,5,3
	.word	16796
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	495
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	495
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	512
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	16866
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	354
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8548
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	16932
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	16960
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	300
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	386
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	16960
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	17045
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7112
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7025
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3368
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1421
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2416
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1549
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2196
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1764
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1979
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6384
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6508
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6592
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6772
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5023
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5547
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5197
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5371
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6036
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	850
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4360
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4848
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4507
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4676
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5703
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	534
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4074
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3708
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2739
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3043
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7639
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7072
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3659
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1500
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2690
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1724
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2376
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1939
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2156
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6468
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6717
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6976
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6344
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5157
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5663
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5331
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5507
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1381
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5996
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4467
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4983
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4636
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4808
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	810
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4320
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4034
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3003
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3319
	.byte	16
	.word	7679
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	18501
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	18521
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	18643
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	19200
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	472
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	19277
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	495
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	495
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	495
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	495
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	495
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	495
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	495
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	495
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	19413
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	495
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	495
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	495
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	495
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	495
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	495
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	19693
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	19931
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	495
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	495
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	495
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	495
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	495
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	20059
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	495
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	495
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	495
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	495
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	495
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	20302
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	20537
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	495
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	472
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	20665
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	495
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	472
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	20765
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	495
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	495
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	495
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	495
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	495
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	20865
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	472
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	21073
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	512
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	495
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	512
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	495
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	21238
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	512
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	495
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	21421
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	495
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	495
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	472
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	495
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	495
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	21575
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	21939
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	512
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	495
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	495
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	495
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	22150
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	512
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	472
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	22402
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	22520
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	22631
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	472
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	22794
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	22957
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	23115
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	495
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	495
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	495
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	495
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	495
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	495
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	495
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	495
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	495
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	512
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	23280
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	512
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	495
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	495
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	512
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	495
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	23609
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	23830
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	23993
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	24265
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	24418
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	24574
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	24736
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	24879
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	25044
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	512
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	495
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	25189
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	495
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	25370
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	25544
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	472
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	25704
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	472
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	25848
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	26122
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	26261
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	495
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	512
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	495
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	495
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	495
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	26424
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	512
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	495
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	512
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	495
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	26642
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	26805
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	27141
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	495
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	27248
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	27700
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	495
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	27799
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	512
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	27949
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	472
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	28098
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	472
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	28259
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	512
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	512
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	28389
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	28521
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	495
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	512
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	28636
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	512
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	512
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	28747
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	495
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	495
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	495
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	495
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	28905
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	29317
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	512
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	495
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	495
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	29418
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	472
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	29685
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	29821
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	495
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	495
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	29932
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	30065
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	512
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	495
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	495
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	495
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	30268
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	495
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	495
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	495
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	512
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	30624
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	512
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	30802
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	512
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	495
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	495
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	495
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	30902
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	495
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	495
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	495
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	512
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	31272
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	472
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	31458
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	472
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	31656
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	495
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	495
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	472
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	31889
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	495
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	495
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	495
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	495
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	495
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	495
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	495
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	32041
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	495
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	495
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	495
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	495
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	32608
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	495
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	495
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	495
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	495
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	495
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	495
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	495
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	32902
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	495
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	495
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	495
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	495
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	495
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	512
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	495
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	495
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	33180
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	512
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	33676
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	512
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	495
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	495
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	495
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	33989
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	495
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	495
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	495
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	495
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	495
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	495
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	495
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	495
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	34198
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	495
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	495
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	495
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	495
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	495
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	495
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	495
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	34409
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	472
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	34841
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	495
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	495
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	495
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	495
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	495
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	495
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	495
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	495
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	495
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	495
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	495
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	34937
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	472
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	35197
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	495
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	495
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	472
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	35322
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	35519
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	35672
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	35825
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	472
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	35978
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	36133
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	36133
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	36133
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	36133
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	36149
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	495
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	495
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	36279
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	495
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	36517
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	36133
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	36133
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	36133
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	36133
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	36740
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	495
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	36866
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	495
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	495
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	495
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	495
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	495
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	495
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	495
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	495
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	495
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	495
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	512
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	37118
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18643
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	37337
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19200
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	37401
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	37465
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19413
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	37530
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19693
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	37595
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19931
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	37660
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20059
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	37725
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20302
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	37790
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20537
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	37855
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20665
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	37920
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20765
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	37985
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20865
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	38050
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21073
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	38114
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21238
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	38178
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21421
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	38242
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21575
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	38307
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21939
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	38369
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22150
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	38431
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22402
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	38493
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22520
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	38557
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22631
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	38622
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22794
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	38688
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22957
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	38754
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23115
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	38822
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23280
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	38889
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23609
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	38957
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23830
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	39025
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23993
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	39091
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24265
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	39158
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24418
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	39227
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24574
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	39296
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24736
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	39365
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24879
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	39434
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25044
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	39503
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25189
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	39572
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25370
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	39640
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25544
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	39708
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25704
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	39776
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25848
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	39844
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26122
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	39909
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26261
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	39974
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26424
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	40040
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26642
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	40104
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26805
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	40165
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27141
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	40226
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27248
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	40286
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27700
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	40348
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27799
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	40408
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27949
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	40470
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28098
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	40538
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28259
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	40606
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28389
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	40674
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28521
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	40738
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28636
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	40803
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28747
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	40866
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28905
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	40927
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29317
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	40991
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29418
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	41052
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29685
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	41116
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29821
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	41183
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29932
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	41246
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30065
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	41307
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30268
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	41369
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30624
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	41434
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30802
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	41499
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30902
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	41564
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31272
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	41633
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31458
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	41702
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31656
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	41771
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31889
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	41836
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32041
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	41899
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32608
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	41964
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32902
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	42029
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33180
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	42094
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33676
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	42160
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	42229
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33989
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	42293
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34409
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	42358
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34841
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	42423
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34937
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	42488
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35197
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	42552
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35322
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	42618
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35519
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	42682
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35672
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	42747
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35825
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	42812
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35978
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	42877
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36149
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	42943
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36279
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	43012
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36517
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	43081
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36740
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	43148
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36866
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	43215
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	488
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37118
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	43282
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	42943
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43012
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43081
	.byte	4,2,35,8,0,16
	.word	43347
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	43410
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	43148
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43215
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43282
	.byte	4,2,35,8,0,16
	.word	43439
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	43500
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	43527
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	43678
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	43922
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	44020
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8292
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8287
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	495
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	44485
	.byte	16
	.word	16354
	.byte	3
	.word	44545
	.byte	23,11,59,15,16,13
	.byte	'module',0
	.word	44550
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	44485
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	17045
	.byte	1,2,35,12,0,24
	.word	44555
	.byte	21
	.byte	'IfxAsclin_Cts_In',0,11,64,3
	.word	44606
	.byte	23,11,67,15,16,13
	.byte	'module',0
	.word	44550
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	44485
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	17045
	.byte	1,2,35,12,0,24
	.word	44636
	.byte	21
	.byte	'IfxAsclin_Rx_In',0,11,72,3
	.word	44687
	.byte	23,11,75,15,16,13
	.byte	'module',0
	.word	44550
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	44485
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	43678
	.byte	1,2,35,12,0,24
	.word	44716
	.byte	21
	.byte	'IfxAsclin_Rts_Out',0,11,80,3
	.word	44767
	.byte	23,11,83,15,16,13
	.byte	'module',0
	.word	44550
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	44485
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	43678
	.byte	1,2,35,12,0,24
	.word	44798
	.byte	21
	.byte	'IfxAsclin_Sclk_Out',0,11,88,3
	.word	44849
	.byte	23,11,91,15,16,13
	.byte	'module',0
	.word	44550
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	44485
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	43678
	.byte	1,2,35,12,0,24
	.word	44881
	.byte	21
	.byte	'IfxAsclin_Slso_Out',0,11,96,3
	.word	44932
	.byte	23,11,99,15,16,13
	.byte	'module',0
	.word	44550
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	44485
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	43678
	.byte	1,2,35,12,0,24
	.word	44964
	.byte	21
	.byte	'IfxAsclin_Tx_Out',0,11,104,3
	.word	45015
.L200:
	.byte	24
	.word	44555
.L201:
	.byte	24
	.word	44555
.L202:
	.byte	24
	.word	44555
.L203:
	.byte	24
	.word	44555
.L204:
	.byte	24
	.word	44555
.L205:
	.byte	24
	.word	44555
.L206:
	.byte	24
	.word	44716
.L207:
	.byte	24
	.word	44716
.L208:
	.byte	24
	.word	44716
.L209:
	.byte	24
	.word	44716
.L210:
	.byte	24
	.word	44716
.L211:
	.byte	24
	.word	44716
.L212:
	.byte	24
	.word	44636
.L213:
	.byte	24
	.word	44636
.L214:
	.byte	24
	.word	44636
.L215:
	.byte	24
	.word	44636
.L216:
	.byte	24
	.word	44636
.L217:
	.byte	24
	.word	44636
.L218:
	.byte	24
	.word	44636
.L219:
	.byte	24
	.word	44636
.L220:
	.byte	24
	.word	44636
.L221:
	.byte	24
	.word	44636
.L222:
	.byte	24
	.word	44636
.L223:
	.byte	24
	.word	44636
.L224:
	.byte	24
	.word	44636
.L225:
	.byte	24
	.word	44636
.L226:
	.byte	24
	.word	44636
.L227:
	.byte	24
	.word	44636
.L228:
	.byte	24
	.word	44636
.L229:
	.byte	24
	.word	44636
.L230:
	.byte	24
	.word	44636
.L231:
	.byte	24
	.word	44798
.L232:
	.byte	24
	.word	44798
.L233:
	.byte	24
	.word	44798
.L234:
	.byte	24
	.word	44798
.L235:
	.byte	24
	.word	44798
.L236:
	.byte	24
	.word	44798
.L237:
	.byte	24
	.word	44798
.L238:
	.byte	24
	.word	44798
.L239:
	.byte	24
	.word	44798
.L240:
	.byte	24
	.word	44798
.L241:
	.byte	24
	.word	44798
.L242:
	.byte	24
	.word	44798
.L243:
	.byte	24
	.word	44798
.L244:
	.byte	24
	.word	44798
.L245:
	.byte	24
	.word	44798
.L246:
	.byte	24
	.word	44798
.L247:
	.byte	24
	.word	44798
.L248:
	.byte	24
	.word	44798
.L249:
	.byte	24
	.word	44798
.L250:
	.byte	24
	.word	44798
.L251:
	.byte	24
	.word	44881
.L252:
	.byte	24
	.word	44881
.L253:
	.byte	24
	.word	44881
.L254:
	.byte	24
	.word	44881
.L255:
	.byte	24
	.word	44881
.L256:
	.byte	24
	.word	44881
.L257:
	.byte	24
	.word	44881
.L258:
	.byte	24
	.word	44881
.L259:
	.byte	24
	.word	44881
.L260:
	.byte	24
	.word	44881
.L261:
	.byte	24
	.word	44881
.L262:
	.byte	24
	.word	44964
.L263:
	.byte	24
	.word	44964
.L264:
	.byte	24
	.word	44964
.L265:
	.byte	24
	.word	44964
.L266:
	.byte	24
	.word	44964
.L267:
	.byte	24
	.word	44964
.L268:
	.byte	24
	.word	44964
.L269:
	.byte	24
	.word	44964
.L270:
	.byte	24
	.word	44964
.L271:
	.byte	24
	.word	44964
.L272:
	.byte	24
	.word	44964
.L273:
	.byte	24
	.word	44964
.L274:
	.byte	24
	.word	44964
.L275:
	.byte	24
	.word	44964
.L276:
	.byte	24
	.word	44964
.L277:
	.byte	24
	.word	44964
.L278:
	.byte	24
	.word	44964
.L279:
	.byte	24
	.word	44964
.L280:
	.byte	24
	.word	44964
.L281:
	.byte	24
	.word	44964
.L282:
	.byte	24
	.word	44964
.L283:
	.byte	24
	.word	44964
.L284:
	.byte	24
	.word	44964
.L285:
	.byte	24
	.word	44964
.L286:
	.byte	24
	.word	44964
.L287:
	.byte	24
	.word	44964
.L288:
	.byte	24
	.word	44964
.L289:
	.byte	24
	.word	44964
.L290:
	.byte	24
	.word	44964
	.byte	24
	.word	44555
	.byte	3
	.word	45500
	.byte	14,8
	.word	45505
	.byte	15,1,0
.L291:
	.byte	14,32
	.word	45510
	.byte	15,3,0,24
	.word	44716
	.byte	3
	.word	45528
	.byte	14,8
	.word	45533
	.byte	15,1,0
.L292:
	.byte	14,32
	.word	45538
	.byte	15,3,0,24
	.word	44636
	.byte	3
	.word	45556
	.byte	14,28
	.word	45561
	.byte	15,6,0
.L293:
	.byte	14,112
	.word	45566
	.byte	15,3,0,24
	.word	44798
	.byte	3
	.word	45584
	.byte	14,36
	.word	45589
	.byte	15,8,0
.L294:
	.byte	14,144,1
	.word	45594
	.byte	15,3,0,24
	.word	44881
	.byte	3
	.word	45613
	.byte	14,20
	.word	45618
	.byte	15,4,0
.L295:
	.byte	14,80
	.word	45623
	.byte	15,3,0,24
	.word	44964
	.byte	3
	.word	45641
	.byte	14,40
	.word	45646
	.byte	15,9,0
.L296:
	.byte	14,160,1
	.word	45651
	.byte	15,3,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L298-.L297
.L297:
	.half	3
	.word	.L300-.L299
.L299:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0,0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0,0,0,0,0
.L300:
.L298:
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_CTSA_P14_9_IN')
	.sect	'.debug_info'
.L6:
	.word	278
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_CTSA_P14_9_IN',0,5,48,18
	.word	.L200
	.byte	1,5,3
	.word	IfxAsclin0_CTSA_P14_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_CTSA_P14_9_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_CTSA_P20_7_IN')
	.sect	'.debug_info'
.L8:
	.word	278
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_CTSA_P20_7_IN',0,5,49,18
	.word	.L201
	.byte	1,5,3
	.word	IfxAsclin1_CTSA_P20_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_CTSA_P20_7_IN')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_CTSB_P32_4_IN')
	.sect	'.debug_info'
.L10:
	.word	278
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_CTSB_P32_4_IN',0,5,50,18
	.word	.L202
	.byte	1,5,3
	.word	IfxAsclin1_CTSB_P32_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_CTSB_P32_4_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_CTSA_P10_7_IN')
	.sect	'.debug_info'
.L12:
	.word	278
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_CTSA_P10_7_IN',0,5,51,18
	.word	.L203
	.byte	1,5,3
	.word	IfxAsclin2_CTSA_P10_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_CTSA_P10_7_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_CTSB_P33_5_IN')
	.sect	'.debug_info'
.L14:
	.word	278
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_CTSB_P33_5_IN',0,5,52,18
	.word	.L204
	.byte	1,5,3
	.word	IfxAsclin2_CTSB_P33_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_CTSB_P33_5_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_CTSA_P00_12_IN')
	.sect	'.debug_info'
.L16:
	.word	279
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_CTSA_P00_12_IN',0,5,53,18
	.word	.L205
	.byte	1,5,3
	.word	IfxAsclin3_CTSA_P00_12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_CTSA_P00_12_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_RTS_P14_7_OUT')
	.sect	'.debug_info'
.L18:
	.word	278
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_RTS_P14_7_OUT',0,5,54,19
	.word	.L206
	.byte	1,5,3
	.word	IfxAsclin0_RTS_P14_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_RTS_P14_7_OUT')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RTS_P20_6_OUT')
	.sect	'.debug_info'
.L20:
	.word	278
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RTS_P20_6_OUT',0,5,55,19
	.word	.L207
	.byte	1,5,3
	.word	IfxAsclin1_RTS_P20_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RTS_P20_6_OUT')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RTS_P23_1_OUT')
	.sect	'.debug_info'
.L22:
	.word	278
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RTS_P23_1_OUT',0,5,56,19
	.word	.L208
	.byte	1,5,3
	.word	IfxAsclin1_RTS_P23_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RTS_P23_1_OUT')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_RTS_P10_8_OUT')
	.sect	'.debug_info'
.L24:
	.word	278
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_RTS_P10_8_OUT',0,5,57,19
	.word	.L209
	.byte	1,5,3
	.word	IfxAsclin2_RTS_P10_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_RTS_P10_8_OUT')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_RTS_P33_4_OUT')
	.sect	'.debug_info'
.L26:
	.word	278
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_RTS_P33_4_OUT',0,5,58,19
	.word	.L210
	.byte	1,5,3
	.word	IfxAsclin2_RTS_P33_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_RTS_P33_4_OUT')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_RTS_P00_9_OUT')
	.sect	'.debug_info'
.L28:
	.word	278
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_RTS_P00_9_OUT',0,5,59,19
	.word	.L211
	.byte	1,5,3
	.word	IfxAsclin3_RTS_P00_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_RTS_P00_9_OUT')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_RXA_P14_1_IN')
	.sect	'.debug_info'
.L30:
	.word	277
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_RXA_P14_1_IN',0,5,60,17
	.word	.L212
	.byte	1,5,3
	.word	IfxAsclin0_RXA_P14_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_RXA_P14_1_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_RXB_P15_3_IN')
	.sect	'.debug_info'
.L32:
	.word	277
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_RXB_P15_3_IN',0,5,61,17
	.word	.L213
	.byte	1,5,3
	.word	IfxAsclin0_RXB_P15_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_RXB_P15_3_IN')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RXA_P15_1_IN')
	.sect	'.debug_info'
.L34:
	.word	277
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RXA_P15_1_IN',0,5,62,17
	.word	.L214
	.byte	1,5,3
	.word	IfxAsclin1_RXA_P15_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RXA_P15_1_IN')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RXB_P15_5_IN')
	.sect	'.debug_info'
.L36:
	.word	277
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RXB_P15_5_IN',0,5,63,17
	.word	.L215
	.byte	1,5,3
	.word	IfxAsclin1_RXB_P15_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RXB_P15_5_IN')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RXC_P20_9_IN')
	.sect	'.debug_info'
.L38:
	.word	277
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RXC_P20_9_IN',0,5,64,17
	.word	.L216
	.byte	1,5,3
	.word	IfxAsclin1_RXC_P20_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RXC_P20_9_IN')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RXD_P14_8_IN')
	.sect	'.debug_info'
.L40:
	.word	277
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RXD_P14_8_IN',0,5,65,17
	.word	.L217
	.byte	1,5,3
	.word	IfxAsclin1_RXD_P14_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RXD_P14_8_IN')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RXE_P11_10_IN')
	.sect	'.debug_info'
.L42:
	.word	278
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RXE_P11_10_IN',0,5,66,17
	.word	.L218
	.byte	1,5,3
	.word	IfxAsclin1_RXE_P11_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RXE_P11_10_IN')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RXF_P33_13_IN')
	.sect	'.debug_info'
.L44:
	.word	278
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RXF_P33_13_IN',0,5,67,17
	.word	.L219
	.byte	1,5,3
	.word	IfxAsclin1_RXF_P33_13_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RXF_P33_13_IN')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_RXG_P02_3_IN')
	.sect	'.debug_info'
.L46:
	.word	277
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_RXG_P02_3_IN',0,5,68,17
	.word	.L220
	.byte	1,5,3
	.word	IfxAsclin1_RXG_P02_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_RXG_P02_3_IN')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_RXA_P14_3_IN')
	.sect	'.debug_info'
.L48:
	.word	277
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_RXA_P14_3_IN',0,5,69,17
	.word	.L221
	.byte	1,5,3
	.word	IfxAsclin2_RXA_P14_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_RXA_P14_3_IN')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_RXB_P02_1_IN')
	.sect	'.debug_info'
.L50:
	.word	277
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_RXB_P02_1_IN',0,5,70,17
	.word	.L222
	.byte	1,5,3
	.word	IfxAsclin2_RXB_P02_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_RXB_P02_1_IN')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_RXD_P10_6_IN')
	.sect	'.debug_info'
.L52:
	.word	277
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_RXD_P10_6_IN',0,5,71,17
	.word	.L223
	.byte	1,5,3
	.word	IfxAsclin2_RXD_P10_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_RXD_P10_6_IN')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_RXE_P33_8_IN')
	.sect	'.debug_info'
.L54:
	.word	277
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_RXE_P33_8_IN',0,5,72,17
	.word	.L224
	.byte	1,5,3
	.word	IfxAsclin2_RXE_P33_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_RXE_P33_8_IN')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_RXG_P02_0_IN')
	.sect	'.debug_info'
.L56:
	.word	277
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_RXG_P02_0_IN',0,5,73,17
	.word	.L225
	.byte	1,5,3
	.word	IfxAsclin2_RXG_P02_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_RXG_P02_0_IN')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_RXA_P15_7_IN')
	.sect	'.debug_info'
.L58:
	.word	277
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_RXA_P15_7_IN',0,5,74,17
	.word	.L226
	.byte	1,5,3
	.word	IfxAsclin3_RXA_P15_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_RXA_P15_7_IN')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_RXC_P20_3_IN')
	.sect	'.debug_info'
.L60:
	.word	277
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_RXC_P20_3_IN',0,5,75,17
	.word	.L227
	.byte	1,5,3
	.word	IfxAsclin3_RXC_P20_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_RXC_P20_3_IN')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_RXD_P32_2_IN')
	.sect	'.debug_info'
.L62:
	.word	277
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_RXD_P32_2_IN',0,5,76,17
	.word	.L228
	.byte	1,5,3
	.word	IfxAsclin3_RXD_P32_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_RXD_P32_2_IN')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_RXE_P00_1_IN')
	.sect	'.debug_info'
.L64:
	.word	277
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_RXE_P00_1_IN',0,5,77,17
	.word	.L229
	.byte	1,5,3
	.word	IfxAsclin3_RXE_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_RXE_P00_1_IN')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_RXF_P21_6_IN')
	.sect	'.debug_info'
.L66:
	.word	277
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_RXF_P21_6_IN',0,5,78,17
	.word	.L230
	.byte	1,5,3
	.word	IfxAsclin3_RXF_P21_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_RXF_P21_6_IN')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_SCLK_P14_0_OUT')
	.sect	'.debug_info'
.L68:
	.word	279
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_SCLK_P14_0_OUT',0,5,79,20
	.word	.L231
	.byte	1,5,3
	.word	IfxAsclin0_SCLK_P14_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_SCLK_P14_0_OUT')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_SCLK_P15_2_OUT')
	.sect	'.debug_info'
.L70:
	.word	279
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_SCLK_P15_2_OUT',0,5,80,20
	.word	.L232
	.byte	1,5,3
	.word	IfxAsclin0_SCLK_P15_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_SCLK_P15_2_OUT')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_SCLK_P15_0_OUT')
	.sect	'.debug_info'
.L72:
	.word	279
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_SCLK_P15_0_OUT',0,5,81,20
	.word	.L233
	.byte	1,5,3
	.word	IfxAsclin1_SCLK_P15_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_SCLK_P15_0_OUT')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_SCLK_P20_10_OUT')
	.sect	'.debug_info'
.L74:
	.word	280
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_SCLK_P20_10_OUT',0,5,82,20
	.word	.L234
	.byte	1,5,3
	.word	IfxAsclin1_SCLK_P20_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_SCLK_P20_10_OUT')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_SCLK_P33_11_OUT')
	.sect	'.debug_info'
.L76:
	.word	280
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_SCLK_P33_11_OUT',0,5,83,20
	.word	.L235
	.byte	1,5,3
	.word	IfxAsclin1_SCLK_P33_11_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_SCLK_P33_11_OUT')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_SCLK_P33_12_OUT')
	.sect	'.debug_info'
.L78:
	.word	280
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_SCLK_P33_12_OUT',0,5,84,20
	.word	.L236
	.byte	1,5,3
	.word	IfxAsclin1_SCLK_P33_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_SCLK_P33_12_OUT')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SCLK_P02_4_OUT')
	.sect	'.debug_info'
.L80:
	.word	279
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SCLK_P02_4_OUT',0,5,85,20
	.word	.L237
	.byte	1,5,3
	.word	IfxAsclin2_SCLK_P02_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SCLK_P02_4_OUT')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SCLK_P10_6_OUT')
	.sect	'.debug_info'
.L82:
	.word	279
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SCLK_P10_6_OUT',0,5,86,20
	.word	.L238
	.byte	1,5,3
	.word	IfxAsclin2_SCLK_P10_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SCLK_P10_6_OUT')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SCLK_P14_2_OUT')
	.sect	'.debug_info'
.L84:
	.word	279
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SCLK_P14_2_OUT',0,5,87,20
	.word	.L239
	.byte	1,5,3
	.word	IfxAsclin2_SCLK_P14_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SCLK_P14_2_OUT')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SCLK_P33_7_OUT')
	.sect	'.debug_info'
.L86:
	.word	279
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SCLK_P33_7_OUT',0,5,88,20
	.word	.L240
	.byte	1,5,3
	.word	IfxAsclin2_SCLK_P33_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SCLK_P33_7_OUT')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SCLK_P33_9_OUT')
	.sect	'.debug_info'
.L88:
	.word	279
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SCLK_P33_9_OUT',0,5,89,20
	.word	.L241
	.byte	1,5,3
	.word	IfxAsclin2_SCLK_P33_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SCLK_P33_9_OUT')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P00_0_OUT')
	.sect	'.debug_info'
.L90:
	.word	279
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P00_0_OUT',0,5,90,20
	.word	.L242
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P00_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P00_0_OUT')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P00_2_OUT')
	.sect	'.debug_info'
.L92:
	.word	279
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P00_2_OUT',0,5,91,20
	.word	.L243
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P00_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P00_2_OUT')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P15_6_OUT')
	.sect	'.debug_info'
.L94:
	.word	279
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P15_6_OUT',0,5,92,20
	.word	.L244
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P15_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P15_6_OUT')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P15_8_OUT')
	.sect	'.debug_info'
.L96:
	.word	279
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P15_8_OUT',0,5,93,20
	.word	.L245
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P15_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P15_8_OUT')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P20_0_OUT')
	.sect	'.debug_info'
.L98:
	.word	279
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P20_0_OUT',0,5,94,20
	.word	.L246
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P20_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P20_0_OUT')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P21_5_OUT')
	.sect	'.debug_info'
.L100:
	.word	279
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P21_5_OUT',0,5,95,20
	.word	.L247
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P21_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P21_5_OUT')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P21_7_OUT')
	.sect	'.debug_info'
.L102:
	.word	279
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P21_7_OUT',0,5,96,20
	.word	.L248
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P21_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P21_7_OUT')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P32_3_OUT')
	.sect	'.debug_info'
.L104:
	.word	279
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P32_3_OUT',0,5,97,20
	.word	.L249
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P32_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P32_3_OUT')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SCLK_P33_2_OUT')
	.sect	'.debug_info'
.L106:
	.word	279
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SCLK_P33_2_OUT',0,5,98,20
	.word	.L250
	.byte	1,5,3
	.word	IfxAsclin3_SCLK_P33_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SCLK_P33_2_OUT')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_SLSO_P14_3_OUT')
	.sect	'.debug_info'
.L108:
	.word	279
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_SLSO_P14_3_OUT',0,5,99,20
	.word	.L251
	.byte	1,5,3
	.word	IfxAsclin1_SLSO_P14_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_SLSO_P14_3_OUT')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_SLSO_P20_8_OUT')
	.sect	'.debug_info'
.L110:
	.word	279
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_SLSO_P20_8_OUT',0,5,100,20
	.word	.L252
	.byte	1,5,3
	.word	IfxAsclin1_SLSO_P20_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_SLSO_P20_8_OUT')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_SLSO_P33_10_OUT')
	.sect	'.debug_info'
.L112:
	.word	280
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_SLSO_P33_10_OUT',0,5,101,20
	.word	.L253
	.byte	1,5,3
	.word	IfxAsclin1_SLSO_P33_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_SLSO_P33_10_OUT')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SLSO_P02_3_OUT')
	.sect	'.debug_info'
.L114:
	.word	279
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SLSO_P02_3_OUT',0,5,102,20
	.word	.L254
	.byte	1,5,3
	.word	IfxAsclin2_SLSO_P02_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SLSO_P02_3_OUT')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SLSO_P10_5_OUT')
	.sect	'.debug_info'
.L116:
	.word	279
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SLSO_P10_5_OUT',0,5,103,20
	.word	.L255
	.byte	1,5,3
	.word	IfxAsclin2_SLSO_P10_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SLSO_P10_5_OUT')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_SLSO_P33_6_OUT')
	.sect	'.debug_info'
.L118:
	.word	279
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_SLSO_P33_6_OUT',0,5,104,20
	.word	.L256
	.byte	1,5,3
	.word	IfxAsclin2_SLSO_P33_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_SLSO_P33_6_OUT')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SLSO_P00_3_OUT')
	.sect	'.debug_info'
.L120:
	.word	279
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SLSO_P00_3_OUT',0,5,105,20
	.word	.L257
	.byte	1,5,3
	.word	IfxAsclin3_SLSO_P00_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SLSO_P00_3_OUT')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SLSO_P14_3_OUT')
	.sect	'.debug_info'
.L122:
	.word	279
	.half	3
	.word	.L123
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SLSO_P14_3_OUT',0,5,106,20
	.word	.L258
	.byte	1,5,3
	.word	IfxAsclin3_SLSO_P14_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SLSO_P14_3_OUT')
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SLSO_P21_2_OUT')
	.sect	'.debug_info'
.L124:
	.word	279
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SLSO_P21_2_OUT',0,5,107,20
	.word	.L259
	.byte	1,5,3
	.word	IfxAsclin3_SLSO_P21_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SLSO_P21_2_OUT')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SLSO_P21_6_OUT')
	.sect	'.debug_info'
.L126:
	.word	279
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SLSO_P21_6_OUT',0,5,108,20
	.word	.L260
	.byte	1,5,3
	.word	IfxAsclin3_SLSO_P21_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SLSO_P21_6_OUT')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_SLSO_P33_1_OUT')
	.sect	'.debug_info'
.L128:
	.word	279
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_SLSO_P33_1_OUT',0,5,109,20
	.word	.L261
	.byte	1,5,3
	.word	IfxAsclin3_SLSO_P33_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_SLSO_P33_1_OUT')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_TX_P14_0_OUT')
	.sect	'.debug_info'
.L130:
	.word	277
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_TX_P14_0_OUT',0,5,110,18
	.word	.L262
	.byte	1,5,3
	.word	IfxAsclin0_TX_P14_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_TX_P14_0_OUT')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_TX_P14_1_OUT')
	.sect	'.debug_info'
.L132:
	.word	277
	.half	3
	.word	.L133
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_TX_P14_1_OUT',0,5,111,18
	.word	.L263
	.byte	1,5,3
	.word	IfxAsclin0_TX_P14_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_TX_P14_1_OUT')
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_TX_P15_2_OUT')
	.sect	'.debug_info'
.L134:
	.word	277
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_TX_P15_2_OUT',0,5,112,18
	.word	.L264
	.byte	1,5,3
	.word	IfxAsclin0_TX_P15_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_TX_P15_2_OUT')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin0_TX_P15_3_OUT')
	.sect	'.debug_info'
.L136:
	.word	277
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin0_TX_P15_3_OUT',0,5,113,18
	.word	.L265
	.byte	1,5,3
	.word	IfxAsclin0_TX_P15_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin0_TX_P15_3_OUT')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P02_2_OUT')
	.sect	'.debug_info'
.L138:
	.word	277
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P02_2_OUT',0,5,114,18
	.word	.L266
	.byte	1,5,3
	.word	IfxAsclin1_TX_P02_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P02_2_OUT')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P11_12_OUT')
	.sect	'.debug_info'
.L140:
	.word	278
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P11_12_OUT',0,5,115,18
	.word	.L267
	.byte	1,5,3
	.word	IfxAsclin1_TX_P11_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P11_12_OUT')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P14_10_OUT')
	.sect	'.debug_info'
.L142:
	.word	278
	.half	3
	.word	.L143
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P14_10_OUT',0,5,116,18
	.word	.L268
	.byte	1,5,3
	.word	IfxAsclin1_TX_P14_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P14_10_OUT')
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P15_0_OUT')
	.sect	'.debug_info'
.L144:
	.word	277
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P15_0_OUT',0,5,117,18
	.word	.L269
	.byte	1,5,3
	.word	IfxAsclin1_TX_P15_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P15_0_OUT')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P15_1_OUT')
	.sect	'.debug_info'
.L146:
	.word	277
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P15_1_OUT',0,5,118,18
	.word	.L270
	.byte	1,5,3
	.word	IfxAsclin1_TX_P15_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P15_1_OUT')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P15_4_OUT')
	.sect	'.debug_info'
.L148:
	.word	277
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P15_4_OUT',0,5,119,18
	.word	.L271
	.byte	1,5,3
	.word	IfxAsclin1_TX_P15_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P15_4_OUT')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P15_5_OUT')
	.sect	'.debug_info'
.L150:
	.word	277
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P15_5_OUT',0,5,120,18
	.word	.L272
	.byte	1,5,3
	.word	IfxAsclin1_TX_P15_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P15_5_OUT')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P20_10_OUT')
	.sect	'.debug_info'
.L152:
	.word	278
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P20_10_OUT',0,5,121,18
	.word	.L273
	.byte	1,5,3
	.word	IfxAsclin1_TX_P20_10_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P20_10_OUT')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P33_12_OUT')
	.sect	'.debug_info'
.L154:
	.word	278
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P33_12_OUT',0,5,122,18
	.word	.L274
	.byte	1,5,3
	.word	IfxAsclin1_TX_P33_12_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P33_12_OUT')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin1_TX_P33_13_OUT')
	.sect	'.debug_info'
.L156:
	.word	278
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin1_TX_P33_13_OUT',0,5,123,18
	.word	.L275
	.byte	1,5,3
	.word	IfxAsclin1_TX_P33_13_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin1_TX_P33_13_OUT')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_TX_P02_0_OUT')
	.sect	'.debug_info'
.L158:
	.word	277
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_TX_P02_0_OUT',0,5,124,18
	.word	.L276
	.byte	1,5,3
	.word	IfxAsclin2_TX_P02_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_TX_P02_0_OUT')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_TX_P10_5_OUT')
	.sect	'.debug_info'
.L160:
	.word	277
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_TX_P10_5_OUT',0,5,125,18
	.word	.L277
	.byte	1,5,3
	.word	IfxAsclin2_TX_P10_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_TX_P10_5_OUT')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_TX_P14_2_OUT')
	.sect	'.debug_info'
.L162:
	.word	277
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_TX_P14_2_OUT',0,5,126,18
	.word	.L278
	.byte	1,5,3
	.word	IfxAsclin2_TX_P14_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_TX_P14_2_OUT')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_TX_P14_3_OUT')
	.sect	'.debug_info'
.L164:
	.word	277
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_TX_P14_3_OUT',0,5,127,18
	.word	.L279
	.byte	1,5,3
	.word	IfxAsclin2_TX_P14_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_TX_P14_3_OUT')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_TX_P33_8_OUT')
	.sect	'.debug_info'
.L166:
	.word	278
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_TX_P33_8_OUT',0,5,128,1,18
	.word	.L280
	.byte	1,5,3
	.word	IfxAsclin2_TX_P33_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_TX_P33_8_OUT')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin2_TX_P33_9_OUT')
	.sect	'.debug_info'
.L168:
	.word	278
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin2_TX_P33_9_OUT',0,5,129,1,18
	.word	.L281
	.byte	1,5,3
	.word	IfxAsclin2_TX_P33_9_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin2_TX_P33_9_OUT')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P00_0_OUT')
	.sect	'.debug_info'
.L170:
	.word	278
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P00_0_OUT',0,5,130,1,18
	.word	.L282
	.byte	1,5,3
	.word	IfxAsclin3_TX_P00_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P00_0_OUT')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P00_1_OUT')
	.sect	'.debug_info'
.L172:
	.word	278
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P00_1_OUT',0,5,131,1,18
	.word	.L283
	.byte	1,5,3
	.word	IfxAsclin3_TX_P00_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P00_1_OUT')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P15_6_OUT')
	.sect	'.debug_info'
.L174:
	.word	278
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P15_6_OUT',0,5,132,1,18
	.word	.L284
	.byte	1,5,3
	.word	IfxAsclin3_TX_P15_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P15_6_OUT')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P15_7_OUT')
	.sect	'.debug_info'
.L176:
	.word	278
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P15_7_OUT',0,5,133,1,18
	.word	.L285
	.byte	1,5,3
	.word	IfxAsclin3_TX_P15_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P15_7_OUT')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P20_0_OUT')
	.sect	'.debug_info'
.L178:
	.word	278
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P20_0_OUT',0,5,134,1,18
	.word	.L286
	.byte	1,5,3
	.word	IfxAsclin3_TX_P20_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P20_0_OUT')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P20_3_OUT')
	.sect	'.debug_info'
.L180:
	.word	278
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P20_3_OUT',0,5,135,1,18
	.word	.L287
	.byte	1,5,3
	.word	IfxAsclin3_TX_P20_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P20_3_OUT')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P21_7_OUT')
	.sect	'.debug_info'
.L182:
	.word	278
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P21_7_OUT',0,5,136,1,18
	.word	.L288
	.byte	1,5,3
	.word	IfxAsclin3_TX_P21_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P21_7_OUT')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P32_2_OUT')
	.sect	'.debug_info'
.L184:
	.word	278
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P32_2_OUT',0,5,137,1,18
	.word	.L289
	.byte	1,5,3
	.word	IfxAsclin3_TX_P32_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P32_2_OUT')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin3_TX_P32_3_OUT')
	.sect	'.debug_info'
.L186:
	.word	278
	.half	3
	.word	.L187
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin3_TX_P32_3_OUT',0,5,138,1,18
	.word	.L290
	.byte	1,5,3
	.word	IfxAsclin3_TX_P32_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin3_TX_P32_3_OUT')
	.sect	'.debug_abbrev'
.L187:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_Cts_In_pinTable')
	.sect	'.debug_info'
.L188:
	.word	280
	.half	3
	.word	.L189
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin_Cts_In_pinTable',0,5,141,1,25
	.word	.L291
	.byte	1,5,3
	.word	IfxAsclin_Cts_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_Cts_In_pinTable')
	.sect	'.debug_abbrev'
.L189:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_Rts_Out_pinTable')
	.sect	'.debug_info'
.L190:
	.word	281
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin_Rts_Out_pinTable',0,5,160,1,26
	.word	.L292
	.byte	1,5,3
	.word	IfxAsclin_Rts_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_Rts_Out_pinTable')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_Rx_In_pinTable')
	.sect	'.debug_info'
.L192:
	.word	279
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin_Rx_In_pinTable',0,5,179,1,24
	.word	.L293
	.byte	1,5,3
	.word	IfxAsclin_Rx_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_Rx_In_pinTable')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_Sclk_Out_pinTable')
	.sect	'.debug_info'
.L194:
	.word	282
	.half	3
	.word	.L195
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin_Sclk_Out_pinTable',0,5,218,1,27
	.word	.L294
	.byte	1,5,3
	.word	IfxAsclin_Sclk_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_Sclk_Out_pinTable')
	.sect	'.debug_abbrev'
.L195:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_Slso_Out_pinTable')
	.sect	'.debug_info'
.L196:
	.word	282
	.half	3
	.word	.L197
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin_Slso_Out_pinTable',0,5,137,2,27
	.word	.L295
	.byte	1,5,3
	.word	IfxAsclin_Slso_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_Slso_Out_pinTable')
	.sect	'.debug_abbrev'
.L197:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxAsclin_Tx_Out_pinTable')
	.sect	'.debug_info'
.L198:
	.word	280
	.half	3
	.word	.L199
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxAsclin_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxAsclin_Tx_Out_pinTable',0,5,168,2,25
	.word	.L296
	.byte	1,5,3
	.word	IfxAsclin_Tx_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxAsclin_Tx_Out_pinTable')
	.sect	'.debug_abbrev'
.L199:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
