	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc27160a --dep-file=Ifx_WndF32_HannTable.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_HannTable.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_HannTable.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_HannTable.c'

	
$TC16X
	
	.sdecl	'.rodata.Ifx_WndF32_HannTable.Ifx_g_WndF32_hannTable',data,rom,cluster('Ifx_g_WndF32_hannTable')
	.sect	'.rodata.Ifx_WndF32_HannTable.Ifx_g_WndF32_hannTable'
	.global	Ifx_g_WndF32_hannTable
	.align	2
Ifx_g_WndF32_hannTable:	.type	object
	.size	Ifx_g_WndF32_hannTable,2048
	.space	4
	.word	924727512,941504631,951189198,958281455,964113420,967965424,972176815,975057107
	.word	977810190,980886816,982877003,984738680,986761937,988946696,990574310,991828067
	.word	993162442,994577386,996072845,997648762,998774715,999643042,1000551505,1001500069
	.word	1002488698,1003517356,1004586003,1005694599,1006738031,1007332215,1007946309,1008580289
	.word	1009234131,1009907811,1010601304,1011314583,1012047621,1012800392,1013572865,1014365013
	.word	1015099186,1015514889,1015940383,1016375652,1016820680,1017275449,1017739944,1018214145
	.word	1018698036,1019191598,1019694813,1020207661,1020730123,1021262179,1021803810,1022354995
	.word	1022915713,1023448059,1023737920,1024032514,1024331832,1024635861,1024944591,1025258010
	.word	1025576106,1025898866,1026226280,1026558334,1026895016,1027236312,1027582212,1027932700
	.word	1028287765,1028647392,1029011569,1029380281,1029753514,1030131254,1030513488,1030900200
	.word	1031291377,1031687003,1031942924,1032145163,1032349605,1032556242,1032765064,1032976066
	.word	1033189238,1033404573,1033622062,1033841697,1034063471,1034287374,1034513399,1034741536
	.word	1034971778,1035204114,1035438538,1035675039,1035913609,1036154239,1036396920,1036641643
	.word	1036888397,1037137175,1037387967,1037640763,1037895554,1038152330,1038411081,1038671798
	.word	1038934471,1039199089,1039465644,1039734125,1040004521,1040232107,1040369206,1040507247
	.word	1040646225,1040786136,1040926973,1041068731,1041211406,1041354991,1041499482,1041644872
	.word	1041791157,1041938331,1042086388,1042235322,1042385129,1042535802,1042687336,1042839725
	.word	1042992963,1043147045,1043301964,1043457715,1043614292,1043771690,1043929901,1044088920
	.word	1044248742,1044409360,1044570768,1044732959,1044895929,1045059670,1045224177,1045389444
	.word	1045555463,1045722229,1045889736,1046057977,1046226946,1046396636,1046567041,1046738155
	.word	1046909972,1047082484,1047255685,1047429569,1047604129,1047779359,1047955251,1048131800
	.word	1048308999,1048486840,1048620659,1048710213,1048800078,1048890251,1048980728,1049071507
	.word	1049162584,1049253954,1049345616,1049437565,1049529798,1049622312,1049715102,1049808166
	.word	1049901500,1049995101,1050088964,1050183087,1050277465,1050372096,1050466975,1050562100
	.word	1050657466,1050753070,1050848908,1050944977,1051041273,1051137793,1051234532,1051331488
	.word	1051428656,1051526032,1051623614,1051721398,1051819379,1051917554,1052015920,1052114473
	.word	1052213208,1052312123,1052411213,1052510475,1052609905,1052709499,1052809254,1052909165
	.word	1053009229,1053109443,1053209802,1053310303,1053410941,1053511714,1053612617,1053713646
	.word	1053814798,1053916069,1054017455,1054118952,1054220556,1054322264,1054424071,1054525975
	.word	1054627970,1054730054,1054832221,1054934470,1055036794,1055139192,1055241658,1055344190
	.word	1055446782,1055549432,1055652135,1055754888,1055857686,1055960526,1056063404,1056166316
	.word	1056269258,1056372226,1056475217,1056578226,1056681250,1056784284,1056887325,1056977489
	.word	1057029010,1057080529,1057132044,1057183552,1057235053,1057286542,1057338020,1057389484
	.word	1057440932,1057492361,1057543771,1057595159,1057646523,1057697862,1057749173,1057800454
	.word	1057851704,1057902920,1057954101,1058005244,1058056348,1058107411,1058158431,1058209406
	.word	1058260334,1058311213,1058362042,1058412817,1058463538,1058514202,1058564808,1058615354
	.word	1058665837,1058716256,1058766609,1058816894,1058867110,1058917253,1058967323,1059017317
	.word	1059067234,1059117071,1059166828,1059216501,1059266089,1059315590,1059365003,1059414325
	.word	1059463555,1059512691,1059561730,1059610671,1059659513,1059708253,1059756889,1059805420
	.word	1059853844,1059902159,1059950363,1059998455,1060046432,1060094293,1060142036,1060189658
	.word	1060237160,1060284537,1060331790,1060378915,1060425912,1060472778,1060519512,1060566112
	.word	1060612576,1060658902,1060705089,1060751135,1060797037,1060842796,1060888408,1060933872
	.word	1060979186,1061024349,1061069359,1061114214,1061158912,1061203452,1061247832,1061292051
	.word	1061336106,1061379997,1061423721,1061467276,1061510662,1061553877,1061596918,1061639784
	.word	1061682475,1061724987,1061767319,1061809471,1061851439,1061893224,1061934822,1061976233
	.word	1062017455,1062058486,1062099325,1062139971,1062180421,1062220674,1062260729,1062300584
	.word	1062340239,1062379690,1062418937,1062457978,1062496812,1062535437,1062573852,1062612056
	.word	1062650047,1062687823,1062725383,1062762726,1062799850,1062836754,1062873436,1062909896
	.word	1062946131,1062982141,1063017923,1063053478,1063088802,1063123896,1063158757,1063193385
	.word	1063227777,1063261934,1063295852,1063329532,1063362972,1063396171,1063429127,1063461839
	.word	1063494306,1063526526,1063558499,1063590224,1063621698,1063652921,1063683892,1063714610
	.word	1063745073,1063775280,1063805230,1063834922,1063864355,1063893527,1063922439,1063951087
	.word	1063979473,1064007593,1064035448,1064063037,1064090357,1064117409,1064144190,1064170701
	.word	1064196940,1064222907,1064248599,1064274017,1064299159,1064324024,1064348612,1064372921
	.word	1064396951,1064420700,1064444168,1064467354,1064490257,1064512876,1064535210,1064557259
	.word	1064579021,1064600496,1064621683,1064642581,1064663189,1064683508,1064703534,1064723269
	.word	1064742712,1064761860,1064780715,1064799275,1064817539,1064835507,1064853178,1064870552
	.word	1064887627,1064904404,1064920881,1064937057,1064952933,1064968508,1064983781,1064998751
	.word	1065013418,1065027782,1065041841,1065055596,1065069046,1065082189,1065095027,1065107558
	.word	1065119781,1065131697,1065143305,1065154605,1065165595,1065176276,1065186648,1065196709
	.word	1065206459,1065215899,1065225028,1065233844,1065242349,1065250542,1065258422,1065265989
	.word	1065273243,1065280184,1065286811,1065293124,1065299123,1065304807,1065310177,1065315232
	.word	1065319972,1065324396,1065328506,1065332300,1065335778,1065338940,1065341787,1065344318
	.word	1065346532,1065348430,1065350012,1065351278
	.word	1065352227,1065352860,1065353176
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1166
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_HannTable.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	247
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	250
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	295
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	307
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	387
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	361
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	393
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	393
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	361
	.byte	6,0,10,4,61,9,8,11
	.byte	'real',0
	.word	307
	.byte	4,2,35,0,11
	.byte	'imag',0
	.word	307
	.byte	4,2,35,4,0,12
	.word	479
	.byte	3
	.word	513
	.byte	8
	.byte	'IFX_Cf32_dot',0,3,3,85,20
	.word	307
	.byte	1,1,5
	.byte	'b',0,3,85,49
	.word	518
	.byte	6,0,8
	.byte	'IFX_Cf32_mag',0,3,3,91,20
	.word	307
	.byte	1,1,5
	.byte	'c',0,3,91,49
	.word	518
	.byte	13,6,0,0,3
	.word	479
	.byte	4
	.byte	'IFX_Cf32_set',0,3,3,125,17,1,1,5
	.byte	'a',0,3,125,40
	.word	597
	.byte	5
	.byte	're',0,3,125,51
	.word	307
	.byte	5
	.byte	'im',0,3,125,63
	.word	307
	.byte	6,0,14
	.word	255
	.byte	15
	.word	281
	.byte	6,0,14
	.word	316
	.byte	15
	.word	348
	.byte	6,0,14
	.word	398
	.byte	15
	.word	417
	.byte	6,0,14
	.word	433
	.byte	15
	.word	448
	.byte	15
	.word	462
	.byte	6,0,14
	.word	523
	.byte	15
	.word	547
	.byte	6,0,14
	.word	559
	.byte	15
	.word	583
	.byte	13,16
	.word	523
	.byte	15
	.word	547
	.byte	17
	.word	557
	.byte	0,6,0,0,14
	.word	602
	.byte	15
	.word	622
	.byte	15
	.word	632
	.byte	15
	.word	643
	.byte	6,0,7
	.byte	'short int',0,2,5,18
	.byte	'__wchar_t',0,5,1,1
	.word	773
	.byte	7
	.byte	'unsigned int',0,4,7,18
	.byte	'__size_t',0,5,1,1
	.word	804
	.byte	7
	.byte	'int',0,4,5,18
	.byte	'__ptrdiff_t',0,5,1,1
	.word	837
	.byte	19,1,3
	.word	864
	.byte	18
	.byte	'__codeptr',0,5,1,1
	.word	866
	.byte	7
	.byte	'unsigned char',0,1,8,18
	.byte	'uint8',0,6,105,29
	.word	889
	.byte	7
	.byte	'unsigned short int',0,2,7,18
	.byte	'uint16',0,6,109,29
	.word	920
	.byte	7
	.byte	'unsigned long int',0,4,7,18
	.byte	'uint32',0,6,113,29
	.word	957
	.byte	18
	.byte	'uint64',0,6,118,29
	.word	361
	.byte	18
	.byte	'sint16',0,6,126,29
	.word	773
	.byte	7
	.byte	'long int',0,4,5,18
	.byte	'sint32',0,6,131,1,29
	.word	1023
	.byte	7
	.byte	'long long int',0,8,5,18
	.byte	'sint64',0,6,138,1,29
	.word	1051
	.byte	18
	.byte	'float32',0,6,167,1,29
	.word	307
	.byte	18
	.byte	'pvoid',0,4,57,28
	.word	393
	.byte	18
	.byte	'cfloat32',0,4,65,3
	.word	479
	.byte	18
	.byte	'Ifx_TickTime',0,4,79,28
	.word	1051
	.byte	20,128,16
	.word	307
	.byte	21,255,3,0
.L8:
	.byte	12
	.word	1153
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,58,15,59,15,57
	.byte	15,11,15,0,0,11,13,0,3,8,73,19,11,15,56,9,0,0,12,38,0,73,19,0,0,13,11,1,0,0,14,46,1,49,19,0,0,15,5,0,49
	.byte	19,0,0,16,29,1,49,19,0,0,17,11,0,49,19,0,0,18,22,0,3,8,58,15,59,15,57,15,73,19,0,0,19,21,0,54,15,0,0,20
	.byte	1,1,11,15,73,19,0,0,21,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_HannTable.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('Ifx_g_WndF32_hannTable')
	.sect	'.debug_info'
.L6:
	.word	283
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_WndF32_HannTable.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'Ifx_g_WndF32_hannTable',0,5,52,19
	.word	.L8
	.byte	1,5,3
	.word	Ifx_g_WndF32_hannTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_g_WndF32_hannTable')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
