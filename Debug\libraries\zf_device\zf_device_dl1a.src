	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc38412a --dep-file=zf_device_dl1a.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_dl1a.src ../libraries/zf_device/zf_device_dl1a.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_dl1a.c'

	
$TC16X
	.sdecl	'.rodata.zf_device_dl1a..4.cnt',data,rom
	.sect	'.rodata.zf_device_dl1a..4.cnt'
	.align	4
.4.cnt:	.type	object
	.size	.4.cnt,8
	.word	171798692,1082130391
	
	.sdecl	'.text.zf_device_dl1a.dl1a_get_spad_info',code,cluster('dl1a_get_spad_info')
	.sect	'.text.zf_device_dl1a.dl1a_get_spad_info'
	.align	2
	
; Function dl1a_get_spad_info
.L75:
dl1a_get_spad_info:	.type	func
	sub.a	a10,#8
.L274:
	mov.aa	a15,a4
.L277:
	mov.aa	a12,a5
.L278:
	mov	d15,#0
.L577:
	st.b	[a10],d15
.L578:
	mov	d8,#0
.L279:
	mov	d15,#0
.L579:
	st.h	[a10]2,d15
.L2:
	movh.a	a4,#@his(dl1a_iic_struct)
.L275:
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L276:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L580:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L581:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#6
	call	soft_iic_write_8bit_register
.L582:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#131
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L583:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#131
	ld.bu	d15,[a10]
	or	d5,d15,#4
	call	soft_iic_write_8bit_register
.L584:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#7
	call	soft_iic_write_8bit_register
.L585:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#129
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L586:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L587:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#148
	mov	d5,#107
	call	soft_iic_write_8bit_register
.L588:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#131
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L589:
	mov	d0,#0
.L590:
	st.b	[a10],d0
.L591:
	j	.L3
.L4:
.L5:
	mov	d4,#1
	call	system_delay_ms
.L592:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#131
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L593:
	ld.hu	d1,[a10]2
	ld.hu	d0,[a10]2
.L594:
	add	d15,d0,#1
	st.h	[a10]2,d15
.L595:
	mov	d15,#255
.L596:
	jge.u	d15,d1,.L6
.L597:
	mov	d8,#1
.L598:
	j	.L7
.L6:
.L3:
	ld.bu	d15,[a10]
.L599:
	jeq	d15,#0,.L5
.L600:
	ld.bu	d0,[a10]
.L601:
	mov	d15,#255
.L602:
	jeq	d15,d0,.L4
.L7:
	jeq	d8,#0,.L8
.L603:
	j	.L9
.L8:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#131
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L604:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#146
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L605:
	ld.bu	d15,[a10]
.L606:
	and	d15,#127
.L607:
	st.b	[a15],d15
.L608:
	ld.bu	d15,[a10]
.L609:
	sha	d15,#-7
.L610:
	and	d15,#1
.L611:
	st.b	[a12],d15
.L612:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#129
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L613:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#6
	call	soft_iic_write_8bit_register
.L614:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#131
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L615:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#131
	ld.bu	d5,[a10]
	call	soft_iic_write_8bit_register
.L616:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L617:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L618:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L619:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L9:
	mov	d2,d8
.L280:
	j	.L10
.L10:
	ret
.L205:
	
__dl1a_get_spad_info_function_end:
	.size	dl1a_get_spad_info,__dl1a_get_spad_info_function_end-dl1a_get_spad_info
.L127:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_timeout_mclks_to_microseconds',code,cluster('dl1a_timeout_mclks_to_microseconds')
	.sect	'.text.zf_device_dl1a.dl1a_timeout_mclks_to_microseconds'
	.align	2
	
; Function dl1a_timeout_mclks_to_microseconds
.L77:
dl1a_timeout_mclks_to_microseconds:	.type	func
	mov	d15,#2304
	mul	d5,d15
.L281:
	mov	d15,#1655
	mov	d0,#500
	madd	d15,d0,d5,d15
	mov	d0,#1000
	div.u	e0,d15,d0
.L282:
	mov	d15,#2
.L624:
	div.u	e2,d0,d15
.L625:
	madd	d15,d2,d4,d0
.L626:
	mov	d0,#1000
.L283:
	div.u	e2,d15,d0
.L627:
	j	.L11
.L11:
	ret
.L213:
	
__dl1a_timeout_mclks_to_microseconds_function_end:
	.size	dl1a_timeout_mclks_to_microseconds,__dl1a_timeout_mclks_to_microseconds_function_end-dl1a_timeout_mclks_to_microseconds
.L132:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_timeout_microseconds_to_mclks',code,cluster('dl1a_timeout_microseconds_to_mclks')
	.sect	'.text.zf_device_dl1a.dl1a_timeout_microseconds_to_mclks'
	.align	2
	
; Function dl1a_timeout_microseconds_to_mclks
.L79:
dl1a_timeout_microseconds_to_mclks:	.type	func
	mov	d15,#2304
	mul	d5,d15
.L284:
	mov	d15,#1655
	mov	d0,#500
	madd	d15,d0,d5,d15
	mov	d0,#1000
	div.u	e0,d15,d0
.L285:
	mov	d15,#1000
.L632:
	mov	d2,#2
.L633:
	div.u	e2,d0,d2
.L634:
	madd	d15,d2,d4,d15
.L635:
	div.u	e2,d15,d0
.L636:
	j	.L12
.L12:
	ret
.L218:
	
__dl1a_timeout_microseconds_to_mclks_function_end:
	.size	dl1a_timeout_microseconds_to_mclks,__dl1a_timeout_microseconds_to_mclks_function_end-dl1a_timeout_microseconds_to_mclks
.L137:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_decode_timeout',code,cluster('dl1a_decode_timeout')
	.sect	'.text.zf_device_dl1a.dl1a_decode_timeout'
	.align	2
	
; Function dl1a_decode_timeout
.L81:
dl1a_decode_timeout:	.type	func
	and	d15,d4,#255
.L641:
	mov.u	d0,#65280
.L642:
	and	d4,d0
.L286:
	sha	d4,#-8
.L643:
	sha	d15,d15,d4
.L644:
	extr.u	d15,d15,#0,#16
.L645:
	add	d15,#1
	extr.u	d2,d15,#0,#16
.L646:
	j	.L13
.L13:
	ret
.L222:
	
__dl1a_decode_timeout_function_end:
	.size	dl1a_decode_timeout,__dl1a_decode_timeout_function_end-dl1a_decode_timeout
.L142:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_encode_timeout',code,cluster('dl1a_encode_timeout')
	.sect	'.text.zf_device_dl1a.dl1a_encode_timeout'
	.align	2
	
; Function dl1a_encode_timeout
.L83:
dl1a_encode_timeout:	.type	func
	mov	d15,#0
.L288:
	mov	d2,#0
.L290:
	jlt.u	d4,#1,.L14
.L291:
	add	d4,#-1
.L287:
	j	.L15
.L16:
	sh	d4,#-1
.L651:
	add	d15,#1
.L289:
	extr.u	d15,d15,#0,#16
.L15:
	insert	d0,d4,#0,#0,#8
.L652:
	jge.u	d0,#1,.L16
.L653:
	sha	d0,d15,#8
.L654:
	extr.u	d15,d4,#0,#16
.L292:
	and	d15,#255
.L655:
	or	d0,d15
	extr.u	d2,d0,#0,#16
.L14:
	j	.L17
.L17:
	ret
.L224:
	
__dl1a_encode_timeout_function_end:
	.size	dl1a_encode_timeout,__dl1a_encode_timeout_function_end-dl1a_encode_timeout
.L147:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_get_sequence_step_enables',code,cluster('dl1a_get_sequence_step_enables')
	.sect	'.text.zf_device_dl1a.dl1a_get_sequence_step_enables'
	.align	2
	
; Function dl1a_get_sequence_step_enables
.L85:
dl1a_get_sequence_step_enables:	.type	func
	sub.a	a10,#8
.L293:
	mov.aa	a15,a4
.L295:
	mov	d15,#0
.L660:
	st.b	[a10],d15
.L661:
	movh.a	a4,#@his(dl1a_iic_struct)
.L294:
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#1
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L662:
	ld.bu	d15,[a10]
.L663:
	sha	d15,#-4
.L664:
	and	d15,#1
.L665:
	st.b	[a15],d15
.L666:
	ld.bu	d15,[a10]
.L667:
	sha	d15,#-3
.L668:
	and	d15,#1
.L669:
	st.b	[a15]2,d15
.L670:
	ld.bu	d15,[a10]
.L671:
	sha	d15,#-2
.L672:
	and	d15,#1
.L673:
	st.b	[a15]1,d15
.L674:
	ld.bu	d15,[a10]
.L675:
	sha	d15,#-6
.L676:
	and	d15,#1
.L677:
	st.b	[a15]3,d15
.L678:
	ld.bu	d15,[a10]
.L679:
	sha	d15,#-7
.L680:
	and	d15,#1
.L681:
	st.b	[a15]4,d15
.L682:
	ret
.L229:
	
__dl1a_get_sequence_step_enables_function_end:
	.size	dl1a_get_sequence_step_enables,__dl1a_get_sequence_step_enables_function_end-dl1a_get_sequence_step_enables
.L152:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_get_vcsel_pulse_period',code,cluster('dl1a_get_vcsel_pulse_period')
	.sect	'.text.zf_device_dl1a.dl1a_get_vcsel_pulse_period'
	.align	2
	
; Function dl1a_get_vcsel_pulse_period
.L87:
dl1a_get_vcsel_pulse_period:	.type	func
	sub.a	a10,#8
.L296:
	mov	d15,#0
.L687:
	st.b	[a10],d15
.L688:
	jne	d4,#0,.L18
.L689:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#80
.L297:
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L690:
	ld.bu	d15,[a10]
	add	d15,#1
	sha	d15,#1
.L691:
	st.b	[a10],d15
.L692:
	j	.L19
.L18:
	jne	d4,#1,.L20
.L693:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#112
.L298:
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L694:
	ld.bu	d15,[a10]
	add	d15,#1
	sha	d15,#1
.L695:
	st.b	[a10],d15
.L696:
	j	.L21
.L20:
	mov	d15,#255
.L697:
	st.b	[a10],d15
.L21:
.L19:
	ld.bu	d2,[a10]
.L698:
	j	.L22
.L22:
	ret
.L233:
	
__dl1a_get_vcsel_pulse_period_function_end:
	.size	dl1a_get_vcsel_pulse_period,__dl1a_get_vcsel_pulse_period_function_end-dl1a_get_vcsel_pulse_period
.L157:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_get_sequence_step_timeouts',code,cluster('dl1a_get_sequence_step_timeouts')
	.sect	'.text.zf_device_dl1a.dl1a_get_sequence_step_timeouts'
	.align	2
	
; Function dl1a_get_sequence_step_timeouts
.L89:
dl1a_get_sequence_step_timeouts:	.type	func
	sub.a	a10,#8
.L299:
	mov.aa	a12,a4
.L301:
	mov.aa	a15,a5
.L302:
	mov	d4,#0
	call	dl1a_get_vcsel_pulse_period
.L300:
	st.h	[a15],d2
.L703:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#70
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L704:
	ld.bu	d15,[a10]
.L705:
	add	d15,#1
.L706:
	st.h	[a15]4,d15
.L707:
	ld.hu	d4,[a15]4
.L708:
	ld.hu	d15,[a15]0
.L709:
	extr.u	d5,d15,#0,#8
	call	dl1a_timeout_mclks_to_microseconds
.L710:
	st.w	[a15]10,d2
.L711:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#81
	lea	a5,[a10]0
	mov	d5,#2
	call	soft_iic_read_8bit_registers
.L712:
	ld.bu	d15,[a10]
.L713:
	sha	d4,d15,#8
.L714:
	ld.bu	d15,[a10]1
.L303:
	or	d4,d15
.L715:
	call	dl1a_decode_timeout
.L304:
	st.h	[a15]6,d2
.L716:
	ld.hu	d4,[a15]6
.L717:
	ld.hu	d15,[a15]0
.L718:
	extr.u	d5,d15,#0,#8
	call	dl1a_timeout_mclks_to_microseconds
.L719:
	st.w	[a15]14,d2
.L720:
	mov	d4,#1
	call	dl1a_get_vcsel_pulse_period
.L721:
	st.h	[a15]2,d2
.L722:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#113
	lea	a5,[a10]0
	mov	d5,#2
	call	soft_iic_read_8bit_registers
.L723:
	ld.bu	d15,[a10]
.L724:
	sha	d4,d15,#8
.L725:
	ld.bu	d15,[a10]1
.L305:
	or	d4,d15
.L726:
	call	dl1a_decode_timeout
.L306:
	st.h	[a15]8,d2
.L727:
	ld.bu	d15,[a12]3
.L728:
	jeq	d15,#0,.L23
.L729:
	ld.hu	d15,[a15]8
.L730:
	ld.hu	d0,[a15]6
.L731:
	sub	d15,d0
	st.h	[a15]8,d15
.L23:
	ld.hu	d4,[a15]8
.L732:
	ld.hu	d15,[a15]2
.L733:
	extr.u	d5,d15,#0,#8
	call	dl1a_timeout_mclks_to_microseconds
.L734:
	st.w	[a15]18,d2
.L735:
	ret
.L237:
	
__dl1a_get_sequence_step_timeouts_function_end:
	.size	dl1a_get_sequence_step_timeouts,__dl1a_get_sequence_step_timeouts_function_end-dl1a_get_sequence_step_timeouts
.L162:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_perform_single_ref_calibration',code,cluster('dl1a_perform_single_ref_calibration')
	.sect	'.text.zf_device_dl1a.dl1a_perform_single_ref_calibration'
	.align	2
	
; Function dl1a_perform_single_ref_calibration
.L91:
dl1a_perform_single_ref_calibration:	.type	func
	sub.a	a10,#8
.L307:
	mov	d8,#0
.L309:
	mov	d0,#0
.L740:
	st.b	[a10],d0
.L741:
	mov	d15,#0
.L742:
	st.h	[a10]2,d15
.L24:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d0,#0
	or	d5,d4,#1
	mov	d4,d0
.L308:
	call	soft_iic_write_8bit_register
.L743:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#70
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L744:
	j	.L25
.L26:
	mov	d4,#1
	call	system_delay_ms
.L745:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#70
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L746:
	ld.hu	d1,[a10]2
	ld.hu	d0,[a10]2
.L747:
	add	d15,d0,#1
	st.h	[a10]2,d15
.L748:
	mov	d15,#255
.L749:
	jge.u	d15,d1,.L27
.L750:
	mov	d8,#1
.L751:
	j	.L28
.L27:
.L25:
	ld.bu	d15,[a10]
.L752:
	and	d15,#7
.L753:
	jeq	d15,#0,.L26
.L28:
	jeq	d8,#0,.L29
.L754:
	j	.L30
.L29:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#11
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L755:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L30:
	mov	d2,d8
.L310:
	j	.L31
.L31:
	ret
.L245:
	
__dl1a_perform_single_ref_calibration_function_end:
	.size	dl1a_perform_single_ref_calibration,__dl1a_perform_single_ref_calibration_function_end-dl1a_perform_single_ref_calibration
.L167:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_set_measurement_timing_budget',code,cluster('dl1a_set_measurement_timing_budget')
	.sect	'.text.zf_device_dl1a.dl1a_set_measurement_timing_budget'
	.align	2
	
; Function dl1a_set_measurement_timing_budget
.L93:
dl1a_set_measurement_timing_budget:	.type	func
	sub.a	a10,#40
.L311:
	mov	d8,d4
.L313:
	mov	d9,#0
.L32:
	mov	d0,#20000
.L760:
	jge.u	d8,d0,.L33
.L761:
	mov	d9,#1
.L762:
	j	.L34
.L33:
	mov	d10,#2280
.L314:
	lea	a4,[a10]4
	call	dl1a_get_sequence_step_enables
.L312:
	lea	a4,[a10]4
.L763:
	lea	a5,[a10]12
	call	dl1a_get_sequence_step_timeouts
.L764:
	ld.bu	d15,[a10]4
.L765:
	jeq	d15,#0,.L35
.L766:
	ld.w	d0,[a10]22
.L767:
	addi	d0,d0,#590
.L768:
	add	d10,d0
.L35:
	ld.bu	d15,[a10]6
.L769:
	jeq	d15,#0,.L36
.L770:
	ld.w	d0,[a10]22
.L771:
	addi	d15,d0,#690
.L772:
	mov	d0,#2
.L773:
	madd	d10,d10,d15,d0
.L774:
	j	.L37
.L36:
	ld.bu	d15,[a10]5
.L775:
	jeq	d15,#0,.L38
.L776:
	ld.w	d15,[a10]22
.L777:
	addi	d15,d15,#660
.L778:
	add	d10,d15
.L38:
.L37:
	ld.bu	d15,[a10]7
.L779:
	jeq	d15,#0,.L39
.L780:
	ld.w	d15,[a10]26
.L781:
	addi	d15,d15,#660
.L782:
	add	d10,d15
.L39:
	ld.bu	d15,[a10]8
.L783:
	jeq	d15,#0,.L40
.L784:
	addi	d15,d10,#550
.L315:
	jge.u	d8,d15,.L41
.L785:
	mov	d9,#1
.L786:
	j	.L42
.L41:
	sub	d4,d8,d15
.L317:
	ld.hu	d15,[a10]14
.L316:
	extr.u	d5,d15,#0,#8
	call	dl1a_timeout_microseconds_to_mclks
.L318:
	extr.u	d4,d2,#0,#16
.L319:
	ld.bu	d15,[a10]7
.L787:
	jeq	d15,#0,.L43
.L788:
	ld.hu	d15,[a10]18
.L789:
	add	d4,d15
.L320:
	extr.u	d4,d4,#0,#16
.L43:
	call	dl1a_encode_timeout
.L321:
	mov	d15,#113
.L790:
	st.b	[a10],d15
.L791:
	sha	d15,d2,#-8
.L792:
	and	d15,#255
.L793:
	st.b	[a10]1,d15
.L794:
	and	d15,d2,#255
.L795:
	st.b	[a10]2,d15
.L796:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	lea	a5,[a10]0
	mov	d4,#3
	call	soft_iic_write_8bit_array
.L40:
.L42:
.L34:
	mov	d2,d9
.L322:
	j	.L44
.L44:
	ret
.L251:
	
__dl1a_set_measurement_timing_budget_function_end:
	.size	dl1a_set_measurement_timing_budget,__dl1a_set_measurement_timing_budget_function_end-dl1a_set_measurement_timing_budget
.L172:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_get_measurement_timing_budget',code,cluster('dl1a_get_measurement_timing_budget')
	.sect	'.text.zf_device_dl1a.dl1a_get_measurement_timing_budget'
	.align	2
	
; Function dl1a_get_measurement_timing_budget
.L95:
dl1a_get_measurement_timing_budget:	.type	func
	sub.a	a10,#32
.L323:
	mov	d8,#2870
.L324:
	lea	a4,[a10]0
	call	dl1a_get_sequence_step_enables
.L801:
	lea	a4,[a10]0
.L802:
	lea	a5,[a10]8
	call	dl1a_get_sequence_step_timeouts
.L803:
	ld.bu	d15,[a10]
.L804:
	jeq	d15,#0,.L45
.L805:
	ld.w	d15,[a10]18
.L806:
	addi	d15,d15,#590
.L807:
	add	d8,d15
.L45:
	ld.bu	d15,[a10]2
.L808:
	jeq	d15,#0,.L46
.L809:
	ld.w	d15,[a10]18
.L810:
	addi	d15,d15,#690
.L811:
	mov	d0,#2
.L812:
	madd	d8,d8,d15,d0
.L813:
	j	.L47
.L46:
	ld.bu	d15,[a10]1
.L814:
	jeq	d15,#0,.L48
.L815:
	ld.w	d15,[a10]18
.L816:
	addi	d15,d15,#660
.L817:
	add	d8,d15
.L48:
.L47:
	ld.bu	d15,[a10]3
.L818:
	jeq	d15,#0,.L49
.L819:
	ld.w	d15,[a10]22
.L820:
	addi	d15,d15,#660
.L821:
	add	d8,d15
.L49:
	ld.bu	d15,[a10]4
.L822:
	jeq	d15,#0,.L50
.L823:
	ld.w	d15,[a10]26
.L824:
	addi	d15,d15,#550
.L825:
	add	d8,d15
.L50:
	mov	d2,d8
.L325:
	j	.L51
.L51:
	ret
.L263:
	
__dl1a_get_measurement_timing_budget_function_end:
	.size	dl1a_get_measurement_timing_budget,__dl1a_get_measurement_timing_budget_function_end-dl1a_get_measurement_timing_budget
.L177:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_set_signal_rate_limit',code,cluster('dl1a_set_signal_rate_limit')
	.sect	'.text.zf_device_dl1a.dl1a_set_signal_rate_limit'
	.align	2
	
; Function dl1a_set_signal_rate_limit
.L97:
dl1a_set_signal_rate_limit:	.type	func
	sub.a	a10,#8
.L326:
	mov	d15,d4
.L328:
	j	.L52
.L327:
	mov	d4,d15
.L329:
	call	__f_ftod
.L330:
	movh.a	a15,#@his(.4.cnt)
	lea	a15,[a15]@los(.4.cnt)
	ld.d	e4,[a15]0
	mov	e6,d3,d2
	call	__d_fge
	jeq	d2,#0,.L53
.L52:
	mov	d4,#1
.L53:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#486
	call	debug_assert_handler
.L270:
	movh	d0,#17152
.L830:
	mul.f	d4,d15,d0
	call	__f_ftous
.L831:
	mov	d15,#68
.L331:
	st.b	[a10],d15
.L332:
	sha	d15,d2,#-8
.L333:
	and	d15,#255
.L832:
	st.b	[a10]1,d15
.L334:
	and	d15,d2,#255
.L335:
	st.b	[a10]2,d15
.L833:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	lea	a5,[a10]0
	mov	d4,#3
	call	soft_iic_write_8bit_array
.L834:
	ret
.L267:
	
__dl1a_set_signal_rate_limit_function_end:
	.size	dl1a_set_signal_rate_limit,__dl1a_set_signal_rate_limit_function_end-dl1a_set_signal_rate_limit
.L182:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_get_distance',code,cluster('dl1a_get_distance')
	.sect	'.text.zf_device_dl1a.dl1a_get_distance'
	.align	2
	
	.global	dl1a_get_distance
; Function dl1a_get_distance
.L99:
dl1a_get_distance:	.type	func
	sub.a	a10,#8
.L336:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#19
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L360:
	ld.bu	d15,[a10]
.L361:
	and	d15,#7
.L362:
	jeq	d15,#0,.L54
.L363:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#30
	lea	a5,[a10]0
	mov	d5,#2
	call	soft_iic_read_8bit_registers
.L364:
	movh.a	a15,#@his(dl1a_distance_mm)
	lea	a15,[a15]@los(dl1a_distance_mm)
.L365:
	ld.bu	d15,[a10]
.L366:
	sha	d15,d15,#8
.L367:
	st.h	[a15],d15
.L368:
	movh.a	a15,#@his(dl1a_distance_mm)
	lea	a15,[a15]@los(dl1a_distance_mm)
	ld.bu	d15,[a10]
	sha	d0,d15,#8
.L369:
	ld.bu	d15,[a10]1
.L370:
	or	d0,d15
	st.h	[a15],d0
.L371:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#11
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L372:
	movh.a	a15,#@his(dl1a_finsh_flag)
	lea	a15,[a15]@los(dl1a_finsh_flag)
.L373:
	mov	d15,#1
.L374:
	st.b	[a15],d15
.L54:
	ld.bu	d15,[a10]
.L375:
	jz.t	d15:4,.L55
.L376:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#30
	lea	a5,[a10]0
	mov	d5,#2
	call	soft_iic_read_8bit_registers
.L377:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#11
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L55:
	ret
.L189:
	
__dl1a_get_distance_function_end:
	.size	dl1a_get_distance,__dl1a_get_distance_function_end-dl1a_get_distance
.L112:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_int_handler',code,cluster('dl1a_int_handler')
	.sect	'.text.zf_device_dl1a.dl1a_int_handler'
	.align	2
	
	.global	dl1a_int_handler
; Function dl1a_int_handler
.L101:
dl1a_int_handler:	.type	func
	call	dl1a_get_distance
.L382:
	ret
.L192:
	
__dl1a_int_handler_function_end:
	.size	dl1a_int_handler,__dl1a_int_handler_function_end-dl1a_int_handler
.L117:
	; End of function
	
	.sdecl	'.text.zf_device_dl1a.dl1a_init',code,cluster('dl1a_init')
	.sect	'.text.zf_device_dl1a.dl1a_init'
	.align	2
	
	.global	dl1a_init
; Function dl1a_init
.L103:
dl1a_init:	.type	func
	sub.a	a10,#16
.L337:
	mov	d15,#0
.L387:
	st.b	[a10],d15
.L388:
	mov	d8,#0
.L338:
	lea	a4,[a10]1
.L389:
	mov	d4,#0
.L390:
	mov	d5,#6
	call	memset
.L391:
	lea	a4,[a10]7
.L392:
	mov	d4,#0
.L393:
	mov	d5,#7
	call	memset
.L394:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
.L395:
	mov	d4,#41
.L396:
	mov	d5,#100
.L397:
	mov	d6,#1060
.L398:
	mov	d7,#1061
	call	soft_iic_init
.L399:
	mov	d4,#650
.L400:
	mov	d5,#1
.L401:
	mov	d6,#1
.L402:
	mov	d7,#3
	call	gpio_init
.L56:
	mov	d4,#100
	call	system_delay_ms
.L403:
	mov	d4,#650
	call	get_port
	add.a	a2,#4
	movh	d15,#1024
	st.w	[a2],d15
.L404:
	mov	d4,#50
	call	system_delay_ms
.L405:
	mov	d4,#650
	call	get_port
	add.a	a2,#4
	mov	d15,#1024
	st.w	[a2],d15
.L406:
	mov	d4,#100
	call	system_delay_ms
.L407:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#137
	call	soft_iic_read_8bit_register
.L339:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#137
	or	d5,d2,#1
	call	soft_iic_write_8bit_register
.L340:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#136
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L408:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L409:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L410:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L411:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#145
	lea	a5,[a10]0
	mov	d5,#1
	call	soft_iic_read_8bit_registers
.L412:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L413:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L414:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L415:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#96
	call	soft_iic_read_8bit_register
.L341:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#96
	or	d5,d2,#18
	call	soft_iic_write_8bit_register
.L342:
	movh	d4,#16000
	call	dl1a_set_signal_rate_limit
.L416:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#1
	mov	d5,#255
	call	soft_iic_write_8bit_register
.L417:
	lea	a4,[a10]7
.L418:
	lea	a5,[a10]8
	call	dl1a_get_spad_info
.L419:
	jeq	d2,#0,.L57
.L420:
	mov	d8,#1
.L421:
	mov	d4,#0
	movh.a	a4,#@his(.2.str)
	lea	a4,[a4]@los(.2.str)
	movh.a	a5,#@his(.1.str)
	lea	a5,[a5]@los(.1.str)
	mov	d5,#603
	call	debug_log_handler
.L422:
	j	.L58
.L57:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#176
	lea	a5,[a10]1
	mov	d5,#6
	call	soft_iic_read_8bit_registers
.L423:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L424:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#79
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L425:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#78
	mov	d5,#44
	call	soft_iic_write_8bit_register
.L426:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L427:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#182
	mov	d5,#180
	call	soft_iic_write_8bit_register
.L428:
	ld.bu	d15,[a10]8
.L429:
	jeq	d15,#0,.L59
.L430:
	mov	d2,#12
.L431:
	j	.L60
.L59:
	mov	d2,#0
.L60:
	st.b	[a10]9,d2
.L432:
	mov	d3,#0
.L343:
	j	.L61
.L62:
	extr.u	d0,d2,#0,#8
.L433:
	jlt.u	d3,d0,.L63
.L434:
	ld.bu	d0,[a10]10
.L435:
	ld.bu	d15,[a10]7
.L436:
	jne	d15,d0,.L64
.L63:
	mov	d15,#8
.L437:
	div	e0,d3,d15
.L438:
	addsc.a	a15,a10,d0,#0
.L439:
	mov	d15,#8
.L440:
	div	e0,d3,d15
.L441:
	addsc.a	a2,a10,d0,#0
	ld.bu	d0,[a2]1
.L442:
	mov	d1,#1
.L443:
	mov	d15,#8
.L444:
	div	e4,d3,d15
.L445:
	sha	d1,d1,d5
.L446:
	mov	d15,#-1
	xor	d1,d15
.L447:
	and	d0,d1
	st.b	[a15]1,d0
.L448:
	j	.L65
.L64:
	mov	d15,#8
.L449:
	div	e0,d3,d15
.L450:
	addsc.a	a15,a10,d0,#0
	ld.bu	d0,[a15]1
.L451:
	mov	d15,#8
.L452:
	div	e4,d3,d15
.L453:
	rsub	d5,#0
	sha	d0,d0,d5
.L454:
	jz.t	d0:0,.L66
.L455:
	ld.bu	d15,[a10]10
.L456:
	add	d15,#1
	st.b	[a10]10,d15
.L66:
.L65:
	add	d3,#1
.L61:
	mov	d15,#48
.L457:
	jlt.u	d3,d15,.L62
.L458:
	mov	d15,#176
.L459:
	st.b	[a10]7,d15
.L460:
	mov	d15,#1
.L344:
	j	.L67
.L68:
	addsc.a	a15,a10,d15,#0
	ld.bu	d0,[a15]
.L461:
	st.b	[a10]8,d0
.L462:
	add	d15,#1
.L67:
	jlt.u	d15,#7,.L68
.L463:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	lea	a5,[a10]7
	mov	d4,#7
	call	soft_iic_write_8bit_array
.L464:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L465:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L466:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L467:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#9
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L468:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#16
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L469:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#17
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L470:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#36
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L471:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#37
	mov	d5,#255
	call	soft_iic_write_8bit_register
.L472:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#117
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L473:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L474:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#78
	mov	d5,#44
	call	soft_iic_write_8bit_register
.L475:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#72
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L476:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#48
	mov	d5,#32
	call	soft_iic_write_8bit_register
.L477:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L478:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#48
	mov	d5,#9
	call	soft_iic_write_8bit_register
.L479:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#84
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L480:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#49
	mov	d5,#4
	call	soft_iic_write_8bit_register
.L481:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#50
	mov	d5,#3
	call	soft_iic_write_8bit_register
.L482:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#64
	mov	d5,#131
	call	soft_iic_write_8bit_register
.L483:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#70
	mov	d5,#37
	call	soft_iic_write_8bit_register
.L484:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#96
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L485:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#39
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L486:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#80
	mov	d5,#6
	call	soft_iic_write_8bit_register
.L487:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#81
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L488:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#82
	mov	d5,#150
	call	soft_iic_write_8bit_register
.L489:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#86
	mov	d5,#8
	call	soft_iic_write_8bit_register
.L490:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#87
	mov	d5,#48
	call	soft_iic_write_8bit_register
.L491:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#97
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L492:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#98
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L493:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#100
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L494:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#101
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L495:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#102
	mov	d5,#160
	call	soft_iic_write_8bit_register
.L496:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L497:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#34
	mov	d5,#50
	call	soft_iic_write_8bit_register
.L498:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#71
	mov	d5,#20
	call	soft_iic_write_8bit_register
.L499:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#73
	mov	d5,#255
	call	soft_iic_write_8bit_register
.L500:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#74
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L501:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L502:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#122
	mov	d5,#10
	call	soft_iic_write_8bit_register
.L503:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#123
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L504:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#120
	mov	d5,#33
	call	soft_iic_write_8bit_register
.L505:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L506:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#35
	mov	d5,#52
	call	soft_iic_write_8bit_register
.L507:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#66
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L508:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#68
	mov	d5,#255
	call	soft_iic_write_8bit_register
.L509:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#69
	mov	d5,#38
	call	soft_iic_write_8bit_register
.L510:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#70
	mov	d5,#5
	call	soft_iic_write_8bit_register
.L511:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#64
	mov	d5,#64
	call	soft_iic_write_8bit_register
.L512:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#14
	mov	d5,#6
	call	soft_iic_write_8bit_register
.L513:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#32
	mov	d5,#26
	call	soft_iic_write_8bit_register
.L514:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#67
	mov	d5,#64
	call	soft_iic_write_8bit_register
.L515:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L516:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#52
	mov	d5,#3
	call	soft_iic_write_8bit_register
.L517:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#53
	mov	d5,#68
	call	soft_iic_write_8bit_register
.L518:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L519:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#49
	mov	d5,#4
	call	soft_iic_write_8bit_register
.L520:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#75
	mov	d5,#9
	call	soft_iic_write_8bit_register
.L521:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#76
	mov	d5,#5
	call	soft_iic_write_8bit_register
.L522:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#77
	mov	d5,#4
	call	soft_iic_write_8bit_register
.L523:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L524:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#68
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L525:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#69
	mov	d5,#32
	call	soft_iic_write_8bit_register
.L526:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#71
	mov	d5,#8
	call	soft_iic_write_8bit_register
.L527:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#72
	mov	d5,#40
	call	soft_iic_write_8bit_register
.L528:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#103
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L529:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#112
	mov	d5,#4
	call	soft_iic_write_8bit_register
.L530:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#113
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L531:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#114
	mov	d5,#254
	call	soft_iic_write_8bit_register
.L532:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#118
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L533:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#119
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L534:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L535:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#13
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L536:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L537:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L538:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#1
	mov	d5,#248
	call	soft_iic_write_8bit_register
.L539:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L540:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#142
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L541:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L542:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L543:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L544:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#10
	mov	d5,#4
	call	soft_iic_write_8bit_register
.L545:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#132
	call	soft_iic_read_8bit_register
.L346:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#132
	insert	d5,d2,#0,#4,#1
	call	soft_iic_write_8bit_register
.L347:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#11
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L546:
	call	dl1a_get_measurement_timing_budget
.L348:
	mov	d15,d2
.L345:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#1
	mov	d5,#232
	call	soft_iic_write_8bit_register
.L547:
	mov	d4,d15
.L349:
	call	dl1a_set_measurement_timing_budget
.L350:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#1
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L548:
	mov	d4,#64
	call	dl1a_perform_single_ref_calibration
.L549:
	jeq	d2,#0,.L69
.L550:
	mov	d8,#1
.L551:
	mov	d4,#0
	movh.a	a4,#@his(.3.str)
	lea	a4,[a4]@los(.3.str)
	movh.a	a5,#@his(.1.str)
	lea	a5,[a5]@los(.1.str)
	mov	d5,#740
	call	debug_log_handler
.L552:
	j	.L70
.L69:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#1
	mov	d5,#2
	call	soft_iic_write_8bit_register
.L553:
	mov	d4,#0
	call	dl1a_perform_single_ref_calibration
.L554:
	jeq	d2,#0,.L71
.L555:
	mov	d8,#1
.L556:
	mov	d4,#0
	movh.a	a4,#@his(.3.str)
	lea	a4,[a4]@los(.3.str)
	movh.a	a5,#@his(.1.str)
	lea	a5,[a5]@los(.1.str)
	mov	d5,#747
	call	debug_log_handler
.L557:
	j	.L72
.L71:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#1
	mov	d5,#232
	call	soft_iic_write_8bit_register
.L558:
	mov	d4,#100
	call	system_delay_ms
.L559:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L560:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L561:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L562:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#145
	ld.bu	d5,[a10]
	call	soft_iic_write_8bit_register
.L563:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#1
	call	soft_iic_write_8bit_register
.L564:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#255
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L565:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#128
	mov	d5,#0
	call	soft_iic_write_8bit_register
.L566:
	movh.a	a4,#@his(dl1a_iic_struct)
	lea	a4,[a4]@los(dl1a_iic_struct)
	mov	d4,#0
	mov	d5,#2
	call	soft_iic_write_8bit_register
.L72:
.L70:
.L58:
	mov	d4,#4
.L567:
	mov	d5,#1
	call	exti_init
.L568:
	call	dl1a_int_handler
.L569:
	movh.a	a15,#@his(dl1a_finsh_flag)
	lea	a15,[a15]@los(dl1a_finsh_flag)
.L570:
	mov	d15,#0
.L571:
	st.b	[a15],d15
.L572:
	mov	d2,d8
.L351:
	j	.L73
.L73:
	ret
.L194:
	
__dl1a_init_function_end:
	.size	dl1a_init,__dl1a_init_function_end-dl1a_init
.L122:
	; End of function
	
	.sdecl	'.data.zf_device_dl1a.dl1a_finsh_flag',data,cluster('dl1a_finsh_flag')
	.sect	'.data.zf_device_dl1a.dl1a_finsh_flag'
	.global	dl1a_finsh_flag
dl1a_finsh_flag:	.type	object
	.size	dl1a_finsh_flag,1
	.space	1
	.sdecl	'.data.zf_device_dl1a.dl1a_distance_mm',data,cluster('dl1a_distance_mm')
	.sect	'.data.zf_device_dl1a.dl1a_distance_mm'
	.global	dl1a_distance_mm
	.align	2
dl1a_distance_mm:	.type	object
	.size	dl1a_distance_mm,2
	.half	8192
	.sdecl	'.bss.zf_device_dl1a.dl1a_iic_struct',data,cluster('dl1a_iic_struct')
	.sect	'.bss.zf_device_dl1a.dl1a_iic_struct'
	.align	4
dl1a_iic_struct:	.type	object
	.size	dl1a_iic_struct,24
	.space	24
	.sdecl	'.rodata.zf_device_dl1a..1.str',data,rom
	.sect	'.rodata.zf_device_dl1a..1.str'
.1.str:	.type	object
	.size	.1.str,40
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,100,108,49
	.byte	97,46,99
	.space	1
	.sdecl	'.rodata.zf_device_dl1a..2.str',data,rom
	.sect	'.rodata.zf_device_dl1a..2.str'
.2.str:	.type	object
	.size	.2.str,23
	.byte	68,76,49,65,32,115,101,108
	.byte	102,32,99,104,101,99,107,32
	.byte	101,114,114,111
	.byte	114,46
	.space	1
	.sdecl	'.rodata.zf_device_dl1a..3.str',data,rom
	.sect	'.rodata.zf_device_dl1a..3.str'
.3.str:	.type	object
	.size	.3.str,49
	.byte	68,76,49,65,32,112,101,114
	.byte	102,111,114,109,32,115,105,110
	.byte	103,108,101,32,114,101,102,101
	.byte	114,101,110,99,101,32,99,97
	.byte	108,105,98,114,97,116,105,111
	.byte	110,32,101,114,114,111,114,46
	.space	1
	.calls	'dl1a_set_signal_rate_limit','__f_ftod'
	.calls	'dl1a_set_signal_rate_limit','__d_fge'
	.calls	'dl1a_set_signal_rate_limit','__f_ftous'
	.calls	'dl1a_get_spad_info','soft_iic_write_8bit_register'
	.calls	'dl1a_get_spad_info','soft_iic_read_8bit_registers'
	.calls	'dl1a_get_spad_info','system_delay_ms'
	.calls	'dl1a_get_sequence_step_enables','soft_iic_read_8bit_registers'
	.calls	'dl1a_get_vcsel_pulse_period','soft_iic_read_8bit_registers'
	.calls	'dl1a_get_sequence_step_timeouts','dl1a_get_vcsel_pulse_period'
	.calls	'dl1a_get_sequence_step_timeouts','soft_iic_read_8bit_registers'
	.calls	'dl1a_get_sequence_step_timeouts','dl1a_timeout_mclks_to_microseconds'
	.calls	'dl1a_get_sequence_step_timeouts','dl1a_decode_timeout'
	.calls	'dl1a_perform_single_ref_calibration','soft_iic_write_8bit_register'
	.calls	'dl1a_perform_single_ref_calibration','soft_iic_read_8bit_registers'
	.calls	'dl1a_perform_single_ref_calibration','system_delay_ms'
	.calls	'dl1a_set_measurement_timing_budget','dl1a_get_sequence_step_enables'
	.calls	'dl1a_set_measurement_timing_budget','dl1a_get_sequence_step_timeouts'
	.calls	'dl1a_set_measurement_timing_budget','dl1a_timeout_microseconds_to_mclks'
	.calls	'dl1a_set_measurement_timing_budget','dl1a_encode_timeout'
	.calls	'dl1a_set_measurement_timing_budget','soft_iic_write_8bit_array'
	.calls	'dl1a_get_measurement_timing_budget','dl1a_get_sequence_step_enables'
	.calls	'dl1a_get_measurement_timing_budget','dl1a_get_sequence_step_timeouts'
	.calls	'dl1a_set_signal_rate_limit','debug_assert_handler'
	.calls	'dl1a_set_signal_rate_limit','soft_iic_write_8bit_array'
	.calls	'dl1a_get_distance','soft_iic_read_8bit_registers'
	.calls	'dl1a_get_distance','soft_iic_write_8bit_register'
	.calls	'dl1a_int_handler','dl1a_get_distance'
	.calls	'dl1a_init','memset'
	.calls	'dl1a_init','soft_iic_init'
	.calls	'dl1a_init','gpio_init'
	.calls	'dl1a_init','system_delay_ms'
	.calls	'dl1a_init','get_port'
	.calls	'dl1a_init','soft_iic_read_8bit_register'
	.calls	'dl1a_init','soft_iic_write_8bit_register'
	.calls	'dl1a_init','soft_iic_read_8bit_registers'
	.calls	'dl1a_init','dl1a_set_signal_rate_limit'
	.calls	'dl1a_init','dl1a_get_spad_info'
	.calls	'dl1a_init','debug_log_handler'
	.calls	'dl1a_init','soft_iic_write_8bit_array'
	.calls	'dl1a_init','dl1a_get_measurement_timing_budget'
	.calls	'dl1a_init','dl1a_set_measurement_timing_budget'
	.calls	'dl1a_init','dl1a_perform_single_ref_calibration'
	.calls	'dl1a_init','exti_init'
	.calls	'dl1a_init','dl1a_int_handler'
	.calls	'dl1a_get_spad_info','',8
	.calls	'dl1a_timeout_mclks_to_microseconds','',0
	.calls	'dl1a_timeout_microseconds_to_mclks','',0
	.calls	'dl1a_decode_timeout','',0
	.calls	'dl1a_encode_timeout','',0
	.calls	'dl1a_get_sequence_step_enables','',8
	.calls	'dl1a_get_vcsel_pulse_period','',8
	.calls	'dl1a_get_sequence_step_timeouts','',8
	.calls	'dl1a_perform_single_ref_calibration','',8
	.calls	'dl1a_set_measurement_timing_budget','',40
	.calls	'dl1a_get_measurement_timing_budget','',32
	.calls	'dl1a_set_signal_rate_limit','',8
	.calls	'dl1a_get_distance','',8
	.calls	'dl1a_int_handler','',0
	.extern	memset
	.extern	debug_assert_handler
	.extern	debug_log_handler
	.extern	system_delay_ms
	.extern	exti_init
	.extern	get_port
	.extern	gpio_init
	.extern	soft_iic_write_8bit_array
	.extern	soft_iic_write_8bit_register
	.extern	soft_iic_read_8bit_register
	.extern	soft_iic_read_8bit_registers
	.extern	soft_iic_init
	.extern	__f_ftod
	.extern	__d_fge
	.extern	__f_ftous
	.calls	'dl1a_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L105:
	.word	81856
	.half	3
	.word	.L106
	.byte	4
.L104:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L107
	.byte	2,1,1,3
	.word	202
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	205
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L268:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	250
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	262
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	374
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	348
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	380
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	380
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	348
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	505
	.byte	4,2,35,0,0
.L193:
	.byte	7
	.byte	'unsigned char',0,1,8
.L214:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	924
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	601
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	884
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1115
	.byte	4,2,35,8,0,14
	.word	1155
	.byte	3
	.word	1218
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1223
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	658
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1223
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	658
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	658
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1223
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1453
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1769
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2340
	.byte	4,2,35,0,0,15,4
	.word	641
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2468
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2683
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2898
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	641
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3335
	.byte	4,2,35,0,0,15,24
	.word	641
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3658
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	641
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3962
	.byte	4,2,35,0,0,15,8
	.word	641
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4287
	.byte	4,2,35,0,0,15,12
	.word	641
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4627
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4993
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5279
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5426
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	466
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5595
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5767
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	658
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5942
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6116
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6290
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6466
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6622
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6955
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7303
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7427
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7691
	.byte	4,2,35,0,0,15,76
	.word	641
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7944
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8031
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1729
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2300
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2419
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2459
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2643
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2858
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3075
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3295
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2459
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3609
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3649
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3922
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4238
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4278
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4578
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4618
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4953
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5239
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4278
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5386
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5555
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5727
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5902
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6076
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6250
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6426
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6582
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6915
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7263
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4278
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7387
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7636
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7895
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7935
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7991
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8558
	.byte	4,3,35,252,1,0,14
	.word	8598
	.byte	3
	.word	9201
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,5,196,4,17,1,1,5
	.byte	'port',0,5,196,4,48
	.word	9206
	.byte	5
	.byte	'pinIndex',0,5,196,4,60
	.word	641
	.byte	5
	.byte	'mode',0,5,196,4,88
	.word	9211
	.byte	6,0,17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9206
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	641
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9416
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9597
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	641
	.byte	1,1,6,0
.L195:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	9752
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	658
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	641
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	658
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	9752
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	9752
	.byte	19,6,0,0,20
	.byte	'memset',0,9,56,17
	.word	380
	.byte	1,1,1,1,21,9,56,33
	.word	380
	.byte	21,9,56,36
	.word	482
	.byte	21,9,56,41
	.word	466
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	10027
	.byte	22
	.byte	'debug_assert_handler',0,10,112,9,1,1,1,1,5
	.byte	'pass',0,10,112,47
	.word	641
	.byte	5
	.byte	'file',0,10,112,59
	.word	10035
	.byte	5
	.byte	'line',0,10,112,69
	.word	482
	.byte	0,22
	.byte	'debug_log_handler',0,10,113,9,1,1,1,1,5
	.byte	'pass',0,10,113,47
	.word	641
	.byte	5
	.byte	'str',0,10,113,59
	.word	10035
	.byte	5
	.byte	'file',0,10,113,70
	.word	10035
	.byte	5
	.byte	'line',0,10,113,80
	.word	482
	.byte	0,22
	.byte	'system_delay_ms',0,11,46,9,1,1,1,1,5
	.byte	'time',0,11,46,45
	.word	9752
	.byte	0,23
	.word	210
	.byte	24
	.word	236
	.byte	6,0,23
	.word	271
	.byte	24
	.word	303
	.byte	6,0,23
	.word	316
	.byte	6,0,23
	.word	385
	.byte	24
	.word	404
	.byte	6,0,23
	.word	420
	.byte	24
	.word	435
	.byte	24
	.word	449
	.byte	6,0,23
	.word	1228
	.byte	24
	.word	1268
	.byte	24
	.word	1286
	.byte	6,0,23
	.word	1306
	.byte	24
	.word	1344
	.byte	24
	.word	1362
	.byte	6,0,23
	.word	1382
	.byte	24
	.word	1433
	.byte	6,0,23
	.word	9336
	.byte	24
	.word	9368
	.byte	24
	.word	9382
	.byte	24
	.word	9400
	.byte	6,0,23
	.word	9519
	.byte	24
	.word	9547
	.byte	24
	.word	9561
	.byte	24
	.word	9579
	.byte	6,0,23
	.word	9676
	.byte	6,0,23
	.word	9710
	.byte	6,0,23
	.word	9773
	.byte	24
	.word	9814
	.byte	6,0,23
	.word	9833
	.byte	24
	.word	9888
	.byte	6,0,23
	.word	9907
	.byte	24
	.word	9947
	.byte	24
	.word	9964
	.byte	19,6,0,0,17,12,42,9,1,18
	.byte	'ERU_CH0_REQ0_P15_4',0,1,18
	.byte	'ERU_CH1_REQ10_P14_3',0,4,18
	.byte	'ERU_CH2_REQ7_P00_4',0,6,18
	.byte	'ERU_CH2_REQ14_P02_1',0,7,18
	.byte	'ERU_CH2_REQ2_P10_2',0,8,18
	.byte	'ERU_CH3_REQ6_P02_0',0,9,18
	.byte	'ERU_CH3_REQ3_P10_3',0,10,18
	.byte	'ERU_CH3_REQ15_P14_1',0,11,18
	.byte	'ERU_CH4_REQ13_P15_5',0,12,18
	.byte	'ERU_CH4_REQ8_P33_7',0,13,18
	.byte	'ERU_CH5_REQ1_P15_8',0,15,18
	.byte	'ERU_CH6_REQ12_P11_10',0,18,18
	.byte	'ERU_CH6_REQ9_P20_0',0,19,18
	.byte	'ERU_CH7_REQ16_P15_1',0,21,18
	.byte	'ERU_CH7_REQ11_P20_9',0,22,0,17,12,65,9,1,18
	.byte	'EXTI_TRIGGER_RISING',0,0,18
	.byte	'EXTI_TRIGGER_FALLING',0,1,18
	.byte	'EXTI_TRIGGER_BOTH',0,2,0,22
	.byte	'exti_init',0,12,83,6,1,1,1,1,5
	.byte	'eru_pin',0,12,83,44
	.word	10432
	.byte	5
	.byte	'trigger',0,12,83,71
	.word	10761
	.byte	0,17,13,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,20
	.byte	'get_port',0,13,114,13
	.word	9206
	.byte	1,1,1,1,5
	.byte	'pin',0,13,114,56
	.word	10883
	.byte	0,17,13,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,13,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,22
	.byte	'gpio_init',0,13,143,1,7,1,1,1,1,5
	.byte	'pin',0,13,143,1,40
	.word	10883
	.byte	5
	.byte	'dir',0,13,143,1,59
	.word	12857
	.byte	5
	.byte	'dat',0,13,143,1,70
	.word	641
	.byte	5
	.byte	'pinconf',0,13,143,1,90
	.word	12875
	.byte	0
.L273:
	.byte	25,14,42,9,24,13
	.byte	'scl_pin',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'sda_pin',0
	.word	9752
	.byte	4,2,35,4,13
	.byte	'addr',0
	.word	641
	.byte	1,2,35,8,13
	.byte	'delay',0
	.word	9752
	.byte	4,2,35,10,13
	.byte	'iic_scl',0
	.word	380
	.byte	4,2,35,16,13
	.byte	'iic_sda',0
	.word	380
	.byte	4,2,35,20,0,3
	.word	13038
	.byte	26
	.word	641
	.byte	3
	.word	13146
	.byte	22
	.byte	'soft_iic_write_8bit_array',0,14,54,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,14,54,68
	.word	13141
	.byte	5
	.byte	'data',0,14,54,95
	.word	13151
	.byte	5
	.byte	'len',0,14,54,108
	.word	9752
	.byte	0,26
	.word	641
	.byte	26
	.word	641
	.byte	22
	.byte	'soft_iic_write_8bit_register',0,14,59,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,14,59,68
	.word	13141
	.byte	5
	.byte	'register_name',0,14,59,94
	.word	13237
	.byte	5
	.byte	'data',0,14,59,121
	.word	13242
	.byte	0,26
	.word	641
	.byte	20
	.byte	'soft_iic_read_8bit_register',0,14,71,13
	.word	641
	.byte	1,1,1,1,5
	.byte	'soft_iic_obj',0,14,71,68
	.word	13141
	.byte	5
	.byte	'register_name',0,14,71,94
	.word	13341
	.byte	0,26
	.word	641
.L206:
	.byte	3
	.word	641
	.byte	22
	.byte	'soft_iic_read_8bit_registers',0,14,72,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,14,72,68
	.word	13141
	.byte	5
	.byte	'register_name',0,14,72,94
	.word	13430
	.byte	5
	.byte	'data',0,14,72,116
	.word	13435
	.byte	5
	.byte	'len',0,14,72,129,1
	.word	9752
	.byte	0,22
	.byte	'soft_iic_init',0,14,83,13,1,1,1,1,5
	.byte	'soft_iic_obj',0,14,83,68
	.word	13141
	.byte	5
	.byte	'addr',0,14,83,88
	.word	641
	.byte	5
	.byte	'delay',0,14,83,101
	.word	9752
	.byte	5
	.byte	'scl_pin',0,14,83,122
	.word	10883
	.byte	5
	.byte	'sda_pin',0,14,83,145,1
	.word	10883
	.byte	0
.L190:
	.byte	15,3
	.word	641
	.byte	16,2,0
.L200:
	.byte	15,6
	.word	641
	.byte	16,5,0
.L202:
	.byte	15,7
	.word	641
	.byte	16,6,0
.L211:
	.byte	14
	.word	658
.L256:
	.byte	25,15,178,1,9,6,13
	.byte	'tcc',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'msrc',0
	.word	641
	.byte	1,2,35,1,13
	.byte	'dss',0
	.word	641
	.byte	1,2,35,2,13
	.byte	'pre_range',0
	.word	641
	.byte	1,2,35,3,13
	.byte	'final_range',0
	.word	641
	.byte	1,2,35,4,0
.L230:
	.byte	3
	.word	13683
.L234:
	.byte	17,15,172,1,9,1,18
	.byte	'DL1A_VCSEL_PERIOD_PER_RANGE',0,0,18
	.byte	'DL1A_VCSEL_PERIOD_FINAL_RANGE',0,1,0,26
	.word	13683
.L238:
	.byte	3
	.word	13844
.L258:
	.byte	25,15,187,1,9,24,13
	.byte	'pre_range_vcsel_period_pclks',0
	.word	658
	.byte	2,2,35,0,13
	.byte	'final_range_vcsel_period_pclks',0
	.word	658
	.byte	2,2,35,2,13
	.byte	'msrc_dss_tcc_mclks',0
	.word	658
	.byte	2,2,35,4,13
	.byte	'pre_range_mclks',0
	.word	658
	.byte	2,2,35,6,13
	.byte	'final_range_mclks',0
	.word	658
	.byte	2,2,35,8,13
	.byte	'msrc_dss_tcc_us',0
	.word	9752
	.byte	4,2,35,10,13
	.byte	'pre_range_us',0
	.word	9752
	.byte	4,2,35,14,13
	.byte	'final_range_us',0
	.word	9752
	.byte	4,2,35,18,0
.L240:
	.byte	3
	.word	13854
.L242:
	.byte	15,2
	.word	641
	.byte	16,1,0
.L249:
	.byte	14
	.word	658
	.byte	7
	.byte	'short int',0,2,5,27
	.byte	'__wchar_t',0,16,1,1
	.word	14109
	.byte	27
	.byte	'__size_t',0,16,1,1
	.word	466
	.byte	27
	.byte	'__ptrdiff_t',0,16,1,1
	.word	482
	.byte	28,1,3
	.word	14177
	.byte	27
	.byte	'__codeptr',0,16,1,1
	.word	14179
	.byte	27
	.byte	'__intptr_t',0,16,1,1
	.word	482
	.byte	27
	.byte	'__uintptr_t',0,16,1,1
	.word	466
	.byte	27
	.byte	'size_t',0,17,31,25
	.word	466
	.byte	27
	.byte	'_iob_flag_t',0,17,82,25
	.word	658
	.byte	27
	.byte	'boolean',0,18,101,29
	.word	641
	.byte	27
	.byte	'uint8',0,18,105,29
	.word	641
	.byte	27
	.byte	'uint16',0,18,109,29
	.word	658
	.byte	27
	.byte	'uint32',0,18,113,29
	.word	9752
	.byte	27
	.byte	'uint64',0,18,118,29
	.word	348
	.byte	27
	.byte	'sint16',0,18,126,29
	.word	14109
	.byte	7
	.byte	'long int',0,4,5,27
	.byte	'sint32',0,18,131,1,29
	.word	14366
	.byte	7
	.byte	'long long int',0,8,5,27
	.byte	'sint64',0,18,138,1,29
	.word	14394
	.byte	27
	.byte	'float32',0,18,167,1,29
	.word	262
	.byte	27
	.byte	'pvoid',0,19,57,28
	.word	380
	.byte	27
	.byte	'Ifx_TickTime',0,19,79,28
	.word	14394
	.byte	17,19,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,27
	.byte	'Ifx_RxSel',0,19,140,1,3
	.word	14479
	.byte	7
	.byte	'char',0,1,6,27
	.byte	'int8',0,20,54,29
	.word	14617
	.byte	27
	.byte	'int16',0,20,55,29
	.word	14109
	.byte	27
	.byte	'int32',0,20,56,29
	.word	482
	.byte	27
	.byte	'int64',0,20,57,29
	.word	14394
	.byte	17,21,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,27
	.byte	'IfxScu_CCUCON0_CLKSEL',0,21,240,10,3
	.word	14680
	.byte	17,21,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,27
	.byte	'IfxScu_WDTCON1_IR',0,21,255,10,3
	.word	14777
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	14899
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	15456
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	15533
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	15669
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	641
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	15949
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	16187
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	16315
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	641
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	16558
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	16793
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	16921
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	17021
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	641
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	17121
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	466
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	17329
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	17494
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	17677
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	466
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	641
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	17831
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	18195
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	641
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	18406
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	18658
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	18776
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	18887
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	19050
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	19213
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	19371
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	641
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	10,0,2,35,2,0,27
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	19536
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	641
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	641
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	658
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	19865
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	20086
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	20249
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	20521
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	20674
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	20830
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	20992
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	21135
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	21300
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	21445
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	21626
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	21800
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	21960
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	22104
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	22378
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	22517
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	641
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	658
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	641
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	641
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	22680
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	658
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	641
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	658
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	22898
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	23061
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	23397
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	641
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	23504
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	23956
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	24055
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	24205
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	466
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	641
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	24354
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	24515
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	658
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	24645
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	24777
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	658
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	24892
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	25003
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	641
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	25161
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	25573
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	658
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	6,0,2,35,3,0,27
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	25674
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	466
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	25941
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	26077
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	641
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	26188
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	26321
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	26524
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	26880
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	658
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	27058
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	641
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	27158
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	641
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	641
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	641
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	27528
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	27714
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	27912
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	466
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	28145
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	641
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	641
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	28297
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	641
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	641
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	641
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	28864
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	641
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	641
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	29158
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	641
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	641
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	641
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	29436
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	29932
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	658
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	30245
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	641
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	641
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	30454
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	641
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	30665
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	31097
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	641
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	641
	.byte	7,0,2,35,3,0,27
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	31193
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	31453
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	466
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	31578
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	31775
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	31928
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	32081
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	32234
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	505
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	680
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	924
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	489
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	32489
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	32615
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	32867
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14899
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	33086
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15456
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	33150
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15533
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	33214
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15669
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	33279
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15949
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	33344
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16187
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	33409
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16315
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	33474
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16558
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	33539
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16793
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	33604
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16921
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	33669
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17021
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	33734
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17121
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	33799
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17329
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	33863
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17494
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	33927
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17677
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	33991
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17831
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	34056
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18195
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	34118
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18406
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	34180
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18658
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	34242
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18776
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	34306
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18887
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	34371
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19050
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	34437
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19213
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	34503
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19371
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	34571
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19536
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	34638
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19865
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	34706
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20086
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	34774
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20249
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	34840
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20521
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	34907
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20674
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	34976
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20830
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	35045
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20992
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	35114
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21135
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	35183
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21300
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	35252
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21445
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	35321
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21626
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	35389
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21800
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	35457
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21960
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	35525
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22104
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	35593
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22378
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	35658
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22517
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	35723
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22680
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	35789
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22898
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	35853
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23061
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	35914
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23397
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	35975
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23504
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	36035
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23956
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	36097
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24055
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	36157
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24205
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	36219
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24354
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	36287
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24515
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	36355
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24645
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	36423
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24777
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	36487
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24892
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	36552
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25003
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	36615
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25161
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	36676
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25573
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	36740
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25674
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	36801
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25941
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	36865
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26077
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	36932
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26188
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	36995
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26321
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	37056
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26524
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	37118
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26880
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	37183
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27058
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	37248
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27158
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	37313
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27528
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	37382
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27714
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	37451
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27912
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	37520
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28145
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	37585
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28297
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	37648
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28864
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	37713
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29158
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	37778
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29436
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	37843
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29932
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	37909
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30454
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	37978
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30245
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	38042
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30665
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	38107
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31097
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	38172
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31193
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	38237
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31453
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	38301
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31578
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	38367
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31775
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	38431
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31928
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	38496
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32081
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	38561
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32234
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	38626
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	601
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	884
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1115
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32489
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	38777
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32615
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	38844
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32867
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	38911
	.byte	14
	.word	1155
	.byte	27
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	38976
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	38777
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	38844
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	38911
	.byte	4,2,35,8,0,14
	.word	39005
	.byte	27
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	39066
	.byte	15,8
	.word	34242
	.byte	16,1,0,15,20
	.word	641
	.byte	16,19,0,15,8
	.word	37585
	.byte	16,1,0,14
	.word	39005
	.byte	15,24
	.word	1155
	.byte	16,1,0,14
	.word	39125
	.byte	15,16
	.word	641
	.byte	16,15,0,15,28
	.word	641
	.byte	16,27,0,15,40
	.word	641
	.byte	16,39,0,15,16
	.word	34056
	.byte	16,3,0,15,16
	.word	36035
	.byte	16,3,0,15,180,3
	.word	641
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4278
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	35975
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2459
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	36676
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	37520
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	37118
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	37183
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	37248
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	37451
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	37313
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	37382
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	33279
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	33344
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	35853
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	35789
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	33409
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	33474
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	33539
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	33604
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	38107
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2459
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	37978
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	33214
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	38301
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	38042
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2459
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	34840
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	39093
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	34306
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	38367
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	33669
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	33734
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	39102
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	36995
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	36157
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	36740
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	36615
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	36097
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	35593
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	34571
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	34371
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	34437
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	38237
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2459
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	37648
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	37843
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	37909
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	39111
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2459
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	33991
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	33863
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	37713
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	37778
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	39120
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	34180
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	39134
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4618
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	38626
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	38561
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	38431
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	38496
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2459
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	36423
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	36487
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	33799
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	36552
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4278
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	38172
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	39139
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	36219
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	36287
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	36355
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	39148
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	36932
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4278
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	35658
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	34503
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	35723
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	34774
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	34638
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2459
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	35321
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	35389
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	35457
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	35525
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	34907
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	34976
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	35045
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	35114
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	35183
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	35252
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	34706
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2459
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	36865
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	36801
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	39157
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	39166
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	34118
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	35914
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	37056
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	39175
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2459
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	33927
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	39184
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	33150
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	33086
	.byte	4,3,35,252,7,0,14
	.word	39195
	.byte	27
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	41185
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,22,45,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_A_Bits',0,22,48,3
	.word	41207
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,22,51,16,4,11
	.byte	'VSS',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	489
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BIV_Bits',0,22,55,3
	.word	41268
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,22,58,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	489
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BTV_Bits',0,22,62,3
	.word	41347
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,22,65,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT_Bits',0,22,69,3
	.word	41433
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,22,72,16,4,11
	.byte	'CM',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	489
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	489
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	489
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL_Bits',0,22,80,3
	.word	41522
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,22,83,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT_Bits',0,22,89,3
	.word	41668
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,22,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID_Bits',0,22,96,3
	.word	41795
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,22,99,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L_Bits',0,22,103,3
	.word	41893
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,22,106,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U_Bits',0,22,110,3
	.word	41986
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,22,113,16,4,11
	.byte	'MODREV',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	489
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID_Bits',0,22,118,3
	.word	42079
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,22,121,16,4,11
	.byte	'XE',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE_Bits',0,22,125,3
	.word	42186
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,22,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT_Bits',0,22,136,1,3
	.word	42273
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,22,139,1,16,4,11
	.byte	'CID',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID_Bits',0,22,143,1,3
	.word	42427
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,22,146,1,16,4,11
	.byte	'DATA',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_D_Bits',0,22,149,1,3
	.word	42521
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,22,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	489
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DATR_Bits',0,22,163,1,3
	.word	42584
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,22,166,1,16,4,11
	.byte	'DE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	489
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	489
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	19,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR_Bits',0,22,177,1,3
	.word	42802
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,22,180,1,16,4,11
	.byte	'DTA',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR_Bits',0,22,184,1,3
	.word	43017
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,22,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0_Bits',0,22,192,1,3
	.word	43111
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,22,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2_Bits',0,22,199,1,3
	.word	43227
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,22,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	489
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCX_Bits',0,22,206,1,3
	.word	43328
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,22,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD_Bits',0,22,212,1,3
	.word	43421
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,22,215,1,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR_Bits',0,22,218,1,3
	.word	43501
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,22,221,1,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR_Bits',0,22,233,1,3
	.word	43570
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,22,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	489
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DMS_Bits',0,22,240,1,3
	.word	43799
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,22,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L_Bits',0,22,247,1,3
	.word	43892
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,22,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	489
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U_Bits',0,22,254,1,3
	.word	43987
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,22,129,2,16,4,11
	.byte	'RE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE_Bits',0,22,133,2,3
	.word	44082
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,22,136,2,16,4,11
	.byte	'WE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE_Bits',0,22,140,2,3
	.word	44172
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,22,143,2,16,4,11
	.byte	'SRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	489
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	489
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR_Bits',0,22,161,2,3
	.word	44262
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,22,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT_Bits',0,22,172,2,3
	.word	44586
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,22,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FCX_Bits',0,22,180,2,3
	.word	44740
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,22,183,2,16,4,11
	.byte	'TST',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	489
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	489
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	489
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	489
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	489
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,22,202,2,3
	.word	44846
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,22,205,2,16,4,11
	.byte	'OPC',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,22,212,2,3
	.word	45195
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,22,215,2,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,22,218,2,3
	.word	45355
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,22,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,22,224,2,3
	.word	45436
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,22,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,22,230,2,3
	.word	45523
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,22,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,22,236,2,3
	.word	45610
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,22,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT_Bits',0,22,243,2,3
	.word	45697
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,22,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	489
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	489
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	489
	.byte	6,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICR_Bits',0,22,253,2,3
	.word	45788
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,22,128,3,16,4,11
	.byte	'ISP',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_ISP_Bits',0,22,131,3,3
	.word	45931
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,22,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	489
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_LCX_Bits',0,22,139,3,3
	.word	45997
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,22,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT_Bits',0,22,146,3,3
	.word	46103
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,22,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT_Bits',0,22,153,3,3
	.word	46196
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,22,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	489
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT_Bits',0,22,160,3,3
	.word	46289
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,22,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	489
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_PC_Bits',0,22,167,3,3
	.word	46382
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,22,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0_Bits',0,22,175,3,3
	.word	46467
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,22,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	489
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1_Bits',0,22,183,3,3
	.word	46583
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,22,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2_Bits',0,22,190,3,3
	.word	46694
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,22,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	489
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	489
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	489
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	489
	.byte	10,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI_Bits',0,22,200,3,3
	.word	46795
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,22,203,3,16,4,11
	.byte	'TA',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR_Bits',0,22,206,3,3
	.word	46925
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,22,209,3,16,4,11
	.byte	'IED',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	489
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	489
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR_Bits',0,22,221,3,3
	.word	46994
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,22,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	489
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0_Bits',0,22,229,3,3
	.word	47223
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,22,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	489
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	489
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1_Bits',0,22,237,3,3
	.word	47336
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,22,240,3,16,4,11
	.byte	'PSI',0,4
	.word	489
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	489
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2_Bits',0,22,244,3,3
	.word	47449
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,22,247,3,16,4,11
	.byte	'FRE',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	17,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR_Bits',0,22,129,4,3
	.word	47540
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,22,132,4,16,4,11
	.byte	'CDC',0,4
	.word	489
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	489
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	489
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	489
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	489
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	489
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSW_Bits',0,22,147,4,3
	.word	47743
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,22,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	489
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	489
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	489
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	489
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN_Bits',0,22,156,4,3
	.word	47986
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,22,159,4,16,4,11
	.byte	'PC',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	489
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	489
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	489
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	489
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	489
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	489
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON_Bits',0,22,171,4,3
	.word	48114
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,22,174,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,22,177,4,3
	.word	48355
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,22,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,22,183,4,3
	.word	48438
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,22,186,4,16,4,11
	.byte	'EN',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,22,189,4,3
	.word	48529
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,22,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,22,195,4,3
	.word	48620
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,22,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,22,202,4,3
	.word	48719
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,22,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,22,209,4,3
	.word	48826
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,22,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT_Bits',0,22,220,4,3
	.word	48933
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,22,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON_Bits',0,22,231,4,3
	.word	49087
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,22,234,4,16,4,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	489
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,22,238,4,3
	.word	49248
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,22,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	489
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	489
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	489
	.byte	15,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON_Bits',0,22,249,4,3
	.word	49346
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,22,252,4,16,4,11
	.byte	'Timer',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,22,255,4,3
	.word	49518
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,22,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	489
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR_Bits',0,22,133,5,3
	.word	49598
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,22,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	489
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	489
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	489
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	489
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	489
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	489
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	489
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	489
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	489
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	489
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	489
	.byte	3,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT_Bits',0,22,153,5,3
	.word	49671
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,22,156,5,16,4,11
	.byte	'T0',0,4
	.word	489
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	489
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	489
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	489
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	489
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	489
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	489
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	489
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	489
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,22,167,5,3
	.word	49989
	.byte	12,22,175,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41207
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_A',0,22,180,5,3
	.word	50184
	.byte	12,22,183,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41268
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BIV',0,22,188,5,3
	.word	50243
	.byte	12,22,191,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41347
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BTV',0,22,196,5,3
	.word	50304
	.byte	12,22,199,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41433
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT',0,22,204,5,3
	.word	50365
	.byte	12,22,207,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41522
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL',0,22,212,5,3
	.word	50427
	.byte	12,22,215,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41668
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT',0,22,220,5,3
	.word	50490
	.byte	12,22,223,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41795
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID',0,22,228,5,3
	.word	50554
	.byte	12,22,231,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41893
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L',0,22,236,5,3
	.word	50619
	.byte	12,22,239,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41986
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U',0,22,244,5,3
	.word	50682
	.byte	12,22,247,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42079
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID',0,22,252,5,3
	.word	50745
	.byte	12,22,255,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42186
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE',0,22,132,6,3
	.word	50809
	.byte	12,22,135,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42273
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT',0,22,140,6,3
	.word	50871
	.byte	12,22,143,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42427
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID',0,22,148,6,3
	.word	50934
	.byte	12,22,151,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42521
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_D',0,22,156,6,3
	.word	50998
	.byte	12,22,159,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42584
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DATR',0,22,164,6,3
	.word	51057
	.byte	12,22,167,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42802
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR',0,22,172,6,3
	.word	51119
	.byte	12,22,175,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43017
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR',0,22,180,6,3
	.word	51182
	.byte	12,22,183,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43111
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0',0,22,188,6,3
	.word	51246
	.byte	12,22,191,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43227
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2',0,22,196,6,3
	.word	51309
	.byte	12,22,199,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43328
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCX',0,22,204,6,3
	.word	51372
	.byte	12,22,207,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43421
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD',0,22,212,6,3
	.word	51433
	.byte	12,22,215,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43501
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR',0,22,220,6,3
	.word	51496
	.byte	12,22,223,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43570
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR',0,22,228,6,3
	.word	51559
	.byte	12,22,231,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43799
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DMS',0,22,236,6,3
	.word	51622
	.byte	12,22,239,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43892
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L',0,22,244,6,3
	.word	51683
	.byte	12,22,247,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43987
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U',0,22,252,6,3
	.word	51746
	.byte	12,22,255,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44082
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE',0,22,132,7,3
	.word	51809
	.byte	12,22,135,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44172
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE',0,22,140,7,3
	.word	51871
	.byte	12,22,143,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44262
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR',0,22,148,7,3
	.word	51933
	.byte	12,22,151,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44586
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT',0,22,156,7,3
	.word	51995
	.byte	12,22,159,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44740
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FCX',0,22,164,7,3
	.word	52058
	.byte	12,22,167,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44846
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,22,172,7,3
	.word	52119
	.byte	12,22,175,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45195
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,22,180,7,3
	.word	52189
	.byte	12,22,183,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45355
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,22,188,7,3
	.word	52259
	.byte	12,22,191,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45436
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,22,196,7,3
	.word	52328
	.byte	12,22,199,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45523
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,22,204,7,3
	.word	52399
	.byte	12,22,207,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45610
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,22,212,7,3
	.word	52470
	.byte	12,22,215,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45697
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT',0,22,220,7,3
	.word	52541
	.byte	12,22,223,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45788
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICR',0,22,228,7,3
	.word	52603
	.byte	12,22,231,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45931
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ISP',0,22,236,7,3
	.word	52664
	.byte	12,22,239,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45997
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_LCX',0,22,244,7,3
	.word	52725
	.byte	12,22,247,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46103
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT',0,22,252,7,3
	.word	52786
	.byte	12,22,255,7,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46196
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT',0,22,132,8,3
	.word	52849
	.byte	12,22,135,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46289
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT',0,22,140,8,3
	.word	52912
	.byte	12,22,143,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46382
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PC',0,22,148,8,3
	.word	52975
	.byte	12,22,151,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46467
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0',0,22,156,8,3
	.word	53035
	.byte	12,22,159,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46583
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1',0,22,164,8,3
	.word	53098
	.byte	12,22,167,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46694
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2',0,22,172,8,3
	.word	53161
	.byte	12,22,175,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46795
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI',0,22,180,8,3
	.word	53224
	.byte	12,22,183,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46925
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR',0,22,188,8,3
	.word	53286
	.byte	12,22,191,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46994
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR',0,22,196,8,3
	.word	53349
	.byte	12,22,199,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47223
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0',0,22,204,8,3
	.word	53412
	.byte	12,22,207,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47336
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1',0,22,212,8,3
	.word	53474
	.byte	12,22,215,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47449
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2',0,22,220,8,3
	.word	53536
	.byte	12,22,223,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47540
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR',0,22,228,8,3
	.word	53598
	.byte	12,22,231,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47743
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSW',0,22,236,8,3
	.word	53660
	.byte	12,22,239,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47986
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN',0,22,244,8,3
	.word	53721
	.byte	12,22,247,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48114
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON',0,22,252,8,3
	.word	53784
	.byte	12,22,255,8,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48355
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA',0,22,132,9,3
	.word	53848
	.byte	12,22,135,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48438
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB',0,22,140,9,3
	.word	53918
	.byte	12,22,143,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48529
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,22,148,9,3
	.word	53988
	.byte	12,22,151,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48620
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,22,156,9,3
	.word	54062
	.byte	12,22,159,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48719
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,22,164,9,3
	.word	54136
	.byte	12,22,167,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48826
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,22,172,9,3
	.word	54206
	.byte	12,22,175,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48933
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT',0,22,180,9,3
	.word	54276
	.byte	12,22,183,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49087
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON',0,22,188,9,3
	.word	54339
	.byte	12,22,191,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49248
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI',0,22,196,9,3
	.word	54403
	.byte	12,22,199,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49346
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON',0,22,204,9,3
	.word	54469
	.byte	12,22,207,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49518
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER',0,22,212,9,3
	.word	54534
	.byte	12,22,215,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49598
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR',0,22,220,9,3
	.word	54601
	.byte	12,22,223,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49671
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT',0,22,228,9,3
	.word	54665
	.byte	12,22,231,9,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49989
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC',0,22,236,9,3
	.word	54729
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,22,247,9,25,8,13
	.byte	'L',0
	.word	50619
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	50682
	.byte	4,2,35,4,0,14
	.word	54795
	.byte	27
	.byte	'Ifx_CPU_CPR',0,22,251,9,3
	.word	54837
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,22,254,9,25,8,13
	.byte	'L',0
	.word	51683
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	51746
	.byte	4,2,35,4,0,14
	.word	54863
	.byte	27
	.byte	'Ifx_CPU_DPR',0,22,130,10,3
	.word	54905
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,22,133,10,25,16,13
	.byte	'LA',0
	.word	54136
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	54206
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	53988
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	54062
	.byte	4,2,35,12,0,14
	.word	54931
	.byte	27
	.byte	'Ifx_CPU_SPROT_RGN',0,22,139,10,3
	.word	55013
	.byte	15,12
	.word	54534
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,22,142,10,25,16,13
	.byte	'CON',0
	.word	54469
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	55045
	.byte	12,2,35,4,0,14
	.word	55054
	.byte	27
	.byte	'Ifx_CPU_TPS',0,22,146,10,3
	.word	55102
	.byte	10
	.byte	'_Ifx_CPU_TR',0,22,149,10,25,8,13
	.byte	'EVT',0
	.word	54665
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	54601
	.byte	4,2,35,4,0,14
	.word	55128
	.byte	27
	.byte	'Ifx_CPU_TR',0,22,153,10,3
	.word	55173
	.byte	15,176,32
	.word	641
	.byte	16,175,32,0,15,208,223,1
	.word	641
	.byte	16,207,223,1,0,15,248,1
	.word	641
	.byte	16,247,1,0,15,244,29
	.word	641
	.byte	16,243,29,0,15,188,3
	.word	641
	.byte	16,187,3,0,15,232,3
	.word	641
	.byte	16,231,3,0,15,252,23
	.word	641
	.byte	16,251,23,0,15,228,63
	.word	641
	.byte	16,227,63,0,15,128,1
	.word	54863
	.byte	16,15,0,14
	.word	55288
	.byte	15,128,31
	.word	641
	.byte	16,255,30,0,15,64
	.word	54795
	.byte	16,7,0,14
	.word	55314
	.byte	15,192,31
	.word	641
	.byte	16,191,31,0,15,16
	.word	50809
	.byte	16,3,0,15,16
	.word	51809
	.byte	16,3,0,15,16
	.word	51871
	.byte	16,3,0,15,208,7
	.word	641
	.byte	16,207,7,0,14
	.word	55054
	.byte	15,240,23
	.word	641
	.byte	16,239,23,0,15,64
	.word	55128
	.byte	16,7,0,14
	.word	55393
	.byte	15,192,23
	.word	641
	.byte	16,191,23,0,15,232,1
	.word	641
	.byte	16,231,1,0,15,180,1
	.word	641
	.byte	16,179,1,0,15,172,1
	.word	641
	.byte	16,171,1,0,15,64
	.word	50998
	.byte	16,15,0,15,64
	.word	641
	.byte	16,63,0,15,64
	.word	50184
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,22,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	55198
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	53721
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	55209
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	54403
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	55222
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	53412
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	53474
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	53536
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	55233
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	51309
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4278
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	53784
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	51933
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2459
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	51057
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	51433
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	51496
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	51559
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3649
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	51246
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	55244
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	53598
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	53098
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	53161
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	53035
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	53286
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	53349
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	55255
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	50490
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	55266
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	52119
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	52259
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	52189
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2459
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	52328
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	52399
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	52470
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	55277
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	55298
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	55303
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	55323
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	55328
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	55339
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	55348
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	55357
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	55366
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	55377
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	55382
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	55402
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	55407
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	50427
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	50365
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	52541
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	52786
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	52849
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	52912
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	55418
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	51119
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2459
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	51995
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	50871
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	54276
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	39148
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	54729
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4618
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	51622
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	51372
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	51182
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	55429
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	53224
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	53660
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	52975
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4278
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	54339
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	50745
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	50554
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	50243
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	50304
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	52664
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	52603
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4278
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	52058
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	52725
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	39139
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	50934
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	55440
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	55451
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	55460
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	55469
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	55460
	.byte	64,4,35,192,255,3,0,14
	.word	55478
	.byte	27
	.byte	'Ifx_CPU',0,22,130,11,3
	.word	57269
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,27
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	57291
	.byte	27
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9597
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,23,45,16,4,11
	.byte	'SRPN',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	641
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	641
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SRC_SRCR_Bits',0,23,62,3
	.word	57389
	.byte	12,23,70,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57389
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SRC_SRCR',0,23,75,3
	.word	57705
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,23,86,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	57765
	.byte	27
	.byte	'Ifx_SRC_AGBT',0,23,89,3
	.word	57797
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,23,92,25,12,13
	.byte	'TX',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	57705
	.byte	4,2,35,8,0,14
	.word	57823
	.byte	27
	.byte	'Ifx_SRC_ASCLIN',0,23,97,3
	.word	57882
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,23,100,25,4,13
	.byte	'SBSRC',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	57910
	.byte	27
	.byte	'Ifx_SRC_BCUSPB',0,23,103,3
	.word	57947
	.byte	15,64
	.word	57705
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,23,106,25,64,13
	.byte	'INT',0
	.word	57975
	.byte	64,2,35,0,0,14
	.word	57984
	.byte	27
	.byte	'Ifx_SRC_CAN',0,23,109,3
	.word	58016
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,23,112,25,16,13
	.byte	'SR0',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	57705
	.byte	4,2,35,12,0,14
	.word	58041
	.byte	27
	.byte	'Ifx_SRC_CCU6',0,23,118,3
	.word	58113
	.byte	15,8
	.word	57705
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,23,121,25,8,13
	.byte	'SR',0
	.word	58139
	.byte	8,2,35,0,0,14
	.word	58148
	.byte	27
	.byte	'Ifx_SRC_CERBERUS',0,23,124,3
	.word	58184
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,23,127,25,16,13
	.byte	'MI',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	57705
	.byte	4,2,35,12,0,14
	.word	58214
	.byte	27
	.byte	'Ifx_SRC_CIF',0,23,133,1,3
	.word	58287
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,23,136,1,25,4,13
	.byte	'SBSRC',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	58313
	.byte	27
	.byte	'Ifx_SRC_CPU',0,23,139,1,3
	.word	58348
	.byte	15,192,1
	.word	57705
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,23,142,1,25,208,1,13
	.byte	'ERR',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4618
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	58374
	.byte	192,1,2,35,16,0,14
	.word	58384
	.byte	27
	.byte	'Ifx_SRC_DMA',0,23,147,1,3
	.word	58451
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,23,150,1,25,8,13
	.byte	'SRM',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	57705
	.byte	4,2,35,4,0,14
	.word	58477
	.byte	27
	.byte	'Ifx_SRC_DSADC',0,23,154,1,3
	.word	58525
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,23,157,1,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	58553
	.byte	27
	.byte	'Ifx_SRC_EMEM',0,23,160,1,3
	.word	58586
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,23,163,1,25,80,13
	.byte	'INT',0
	.word	58139
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	58139
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	58139
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	58139
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	57705
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	57705
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	39157
	.byte	40,2,35,40,0,14
	.word	58613
	.byte	27
	.byte	'Ifx_SRC_ERAY',0,23,172,1,3
	.word	58740
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,23,175,1,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	58767
	.byte	27
	.byte	'Ifx_SRC_ETH',0,23,178,1,3
	.word	58799
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,23,181,1,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	58825
	.byte	27
	.byte	'Ifx_SRC_FCE',0,23,184,1,3
	.word	58857
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,23,187,1,25,12,13
	.byte	'DONE',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	57705
	.byte	4,2,35,8,0,14
	.word	58883
	.byte	27
	.byte	'Ifx_SRC_FFT',0,23,192,1,3
	.word	58943
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,23,195,1,25,32,13
	.byte	'SR0',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	57705
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	39139
	.byte	16,2,35,16,0,14
	.word	58969
	.byte	27
	.byte	'Ifx_SRC_GPSR',0,23,202,1,3
	.word	59063
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,23,205,1,25,48,13
	.byte	'CIRQ',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	57705
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	57705
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	57705
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3649
	.byte	24,2,35,24,0,14
	.word	59090
	.byte	27
	.byte	'Ifx_SRC_GPT12',0,23,214,1,3
	.word	59207
	.byte	15,12
	.word	57705
	.byte	16,2,0,15,32
	.word	57705
	.byte	16,7,0,15,32
	.word	59244
	.byte	16,0,0,15,88
	.word	641
	.byte	16,87,0,15,108
	.word	57705
	.byte	16,26,0,15,96
	.word	641
	.byte	16,95,0,15,96
	.word	59244
	.byte	16,2,0,15,160,3
	.word	641
	.byte	16,159,3,0,15,64
	.word	59244
	.byte	16,1,0,15,192,3
	.word	641
	.byte	16,191,3,0,15,16
	.word	57705
	.byte	16,3,0,15,64
	.word	59329
	.byte	16,3,0,15,192,2
	.word	641
	.byte	16,191,2,0,15,52
	.word	641
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,23,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	59235
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2459
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	57705
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	57705
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	58139
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4278
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	59253
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	59262
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	59271
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	59280
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	57705
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4618
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	59289
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	59298
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	59289
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	59298
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	59309
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	59318
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	59338
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	59347
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	59235
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	59358
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	59235
	.byte	12,3,35,192,18,0,14
	.word	59367
	.byte	27
	.byte	'Ifx_SRC_GTM',0,23,243,1,3
	.word	59827
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,23,246,1,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	59853
	.byte	27
	.byte	'Ifx_SRC_HSCT',0,23,249,1,3
	.word	59886
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,23,252,1,25,16,13
	.byte	'COK',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	57705
	.byte	4,2,35,12,0,14
	.word	59913
	.byte	27
	.byte	'Ifx_SRC_HSSL',0,23,130,2,3
	.word	59986
	.byte	15,56
	.word	641
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,23,133,2,25,80,13
	.byte	'BREQ',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	57705
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	57705
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	57705
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	60013
	.byte	56,2,35,24,0,14
	.word	60022
	.byte	27
	.byte	'Ifx_SRC_I2C',0,23,142,2,3
	.word	60145
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,23,145,2,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	60171
	.byte	27
	.byte	'Ifx_SRC_LMU',0,23,148,2,3
	.word	60203
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,23,151,2,25,20,13
	.byte	'SR0',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	57705
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	57705
	.byte	4,2,35,16,0,14
	.word	60229
	.byte	27
	.byte	'Ifx_SRC_MSC',0,23,158,2,3
	.word	60314
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,23,161,2,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	60340
	.byte	27
	.byte	'Ifx_SRC_PMU',0,23,164,2,3
	.word	60372
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,23,167,2,25,32,13
	.byte	'SR',0
	.word	59244
	.byte	32,2,35,0,0,14
	.word	60398
	.byte	27
	.byte	'Ifx_SRC_PSI5',0,23,170,2,3
	.word	60431
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,23,173,2,25,32,13
	.byte	'SR',0
	.word	59244
	.byte	32,2,35,0,0,14
	.word	60458
	.byte	27
	.byte	'Ifx_SRC_PSI5S',0,23,176,2,3
	.word	60492
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,23,179,2,25,24,13
	.byte	'TX',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	57705
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	57705
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	57705
	.byte	4,2,35,20,0,14
	.word	60520
	.byte	27
	.byte	'Ifx_SRC_QSPI',0,23,187,2,3
	.word	60613
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,23,190,2,25,4,13
	.byte	'SR',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	60640
	.byte	27
	.byte	'Ifx_SRC_SCR',0,23,193,2,3
	.word	60672
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,23,196,2,25,20,13
	.byte	'DTS',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	59329
	.byte	16,2,35,4,0,14
	.word	60698
	.byte	27
	.byte	'Ifx_SRC_SCU',0,23,200,2,3
	.word	60744
	.byte	15,24
	.word	57705
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,23,203,2,25,24,13
	.byte	'SR',0
	.word	60770
	.byte	24,2,35,0,0,14
	.word	60779
	.byte	27
	.byte	'Ifx_SRC_SENT',0,23,206,2,3
	.word	60812
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,23,209,2,25,12,13
	.byte	'SR',0
	.word	59235
	.byte	12,2,35,0,0,14
	.word	60839
	.byte	27
	.byte	'Ifx_SRC_SMU',0,23,212,2,3
	.word	60871
	.byte	10
	.byte	'_Ifx_SRC_STM',0,23,215,2,25,8,13
	.byte	'SR0',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	57705
	.byte	4,2,35,4,0,14
	.word	60897
	.byte	27
	.byte	'Ifx_SRC_STM',0,23,219,2,3
	.word	60943
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,23,222,2,25,16,13
	.byte	'SR0',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	57705
	.byte	4,2,35,12,0,14
	.word	60969
	.byte	27
	.byte	'Ifx_SRC_VADCCG',0,23,228,2,3
	.word	61044
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,23,231,2,25,16,13
	.byte	'SR0',0
	.word	57705
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	57705
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	57705
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	57705
	.byte	4,2,35,12,0,14
	.word	61073
	.byte	27
	.byte	'Ifx_SRC_VADCG',0,23,237,2,3
	.word	61147
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,23,240,2,25,4,13
	.byte	'SRC',0
	.word	57705
	.byte	4,2,35,0,0,14
	.word	61175
	.byte	27
	.byte	'Ifx_SRC_XBAR',0,23,243,2,3
	.word	61209
	.byte	15,4
	.word	57765
	.byte	16,0,0,14
	.word	61236
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,23,128,3,25,4,13
	.byte	'AGBT',0
	.word	61245
	.byte	4,2,35,0,0,14
	.word	61250
	.byte	27
	.byte	'Ifx_SRC_GAGBT',0,23,131,3,3
	.word	61286
	.byte	15,48
	.word	57823
	.byte	16,3,0,14
	.word	61314
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,23,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	61323
	.byte	48,2,35,0,0,14
	.word	61328
	.byte	27
	.byte	'Ifx_SRC_GASCLIN',0,23,137,3,3
	.word	61368
	.byte	14
	.word	57910
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,23,140,3,25,4,13
	.byte	'SPB',0
	.word	61398
	.byte	4,2,35,0,0,14
	.word	61403
	.byte	27
	.byte	'Ifx_SRC_GBCU',0,23,143,3,3
	.word	61437
	.byte	15,64
	.word	57984
	.byte	16,0,0,14
	.word	61464
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,23,146,3,25,64,13
	.byte	'CAN',0
	.word	61473
	.byte	64,2,35,0,0,14
	.word	61478
	.byte	27
	.byte	'Ifx_SRC_GCAN',0,23,149,3,3
	.word	61512
	.byte	15,32
	.word	58041
	.byte	16,1,0,14
	.word	61539
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,23,152,3,25,32,13
	.byte	'CCU6',0
	.word	61548
	.byte	32,2,35,0,0,14
	.word	61553
	.byte	27
	.byte	'Ifx_SRC_GCCU6',0,23,155,3,3
	.word	61589
	.byte	14
	.word	58148
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,23,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	61617
	.byte	8,2,35,0,0,14
	.word	61622
	.byte	27
	.byte	'Ifx_SRC_GCERBERUS',0,23,161,3,3
	.word	61666
	.byte	15,16
	.word	58214
	.byte	16,0,0,14
	.word	61698
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,23,164,3,25,16,13
	.byte	'CIF',0
	.word	61707
	.byte	16,2,35,0,0,14
	.word	61712
	.byte	27
	.byte	'Ifx_SRC_GCIF',0,23,167,3,3
	.word	61746
	.byte	15,8
	.word	58313
	.byte	16,1,0,14
	.word	61773
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,23,170,3,25,8,13
	.byte	'CPU',0
	.word	61782
	.byte	8,2,35,0,0,14
	.word	61787
	.byte	27
	.byte	'Ifx_SRC_GCPU',0,23,173,3,3
	.word	61821
	.byte	15,208,1
	.word	58384
	.byte	16,0,0,14
	.word	61848
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,23,176,3,25,208,1,13
	.byte	'DMA',0
	.word	61858
	.byte	208,1,2,35,0,0,14
	.word	61863
	.byte	27
	.byte	'Ifx_SRC_GDMA',0,23,179,3,3
	.word	61899
	.byte	14
	.word	58477
	.byte	14
	.word	58477
	.byte	14
	.word	58477
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,23,182,3,25,32,13
	.byte	'DSADC0',0
	.word	61926
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4278
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	61931
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	61936
	.byte	8,2,35,24,0,14
	.word	61941
	.byte	27
	.byte	'Ifx_SRC_GDSADC',0,23,188,3,3
	.word	62032
	.byte	15,4
	.word	58553
	.byte	16,0,0,14
	.word	62061
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,23,191,3,25,4,13
	.byte	'EMEM',0
	.word	62070
	.byte	4,2,35,0,0,14
	.word	62075
	.byte	27
	.byte	'Ifx_SRC_GEMEM',0,23,194,3,3
	.word	62111
	.byte	15,80
	.word	58613
	.byte	16,0,0,14
	.word	62139
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,23,197,3,25,80,13
	.byte	'ERAY',0
	.word	62148
	.byte	80,2,35,0,0,14
	.word	62153
	.byte	27
	.byte	'Ifx_SRC_GERAY',0,23,200,3,3
	.word	62189
	.byte	15,4
	.word	58767
	.byte	16,0,0,14
	.word	62217
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,23,203,3,25,4,13
	.byte	'ETH',0
	.word	62226
	.byte	4,2,35,0,0,14
	.word	62231
	.byte	27
	.byte	'Ifx_SRC_GETH',0,23,206,3,3
	.word	62265
	.byte	15,4
	.word	58825
	.byte	16,0,0,14
	.word	62292
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,23,209,3,25,4,13
	.byte	'FCE',0
	.word	62301
	.byte	4,2,35,0,0,14
	.word	62306
	.byte	27
	.byte	'Ifx_SRC_GFCE',0,23,212,3,3
	.word	62340
	.byte	15,12
	.word	58883
	.byte	16,0,0,14
	.word	62367
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,23,215,3,25,12,13
	.byte	'FFT',0
	.word	62376
	.byte	12,2,35,0,0,14
	.word	62381
	.byte	27
	.byte	'Ifx_SRC_GFFT',0,23,218,3,3
	.word	62415
	.byte	15,64
	.word	58969
	.byte	16,1,0,14
	.word	62442
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,23,221,3,25,64,13
	.byte	'GPSR',0
	.word	62451
	.byte	64,2,35,0,0,14
	.word	62456
	.byte	27
	.byte	'Ifx_SRC_GGPSR',0,23,224,3,3
	.word	62492
	.byte	15,48
	.word	59090
	.byte	16,0,0,14
	.word	62520
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,23,227,3,25,48,13
	.byte	'GPT12',0
	.word	62529
	.byte	48,2,35,0,0,14
	.word	62534
	.byte	27
	.byte	'Ifx_SRC_GGPT12',0,23,230,3,3
	.word	62572
	.byte	15,204,18
	.word	59367
	.byte	16,0,0,14
	.word	62601
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,23,233,3,25,204,18,13
	.byte	'GTM',0
	.word	62611
	.byte	204,18,2,35,0,0,14
	.word	62616
	.byte	27
	.byte	'Ifx_SRC_GGTM',0,23,236,3,3
	.word	62652
	.byte	15,4
	.word	59853
	.byte	16,0,0,14
	.word	62679
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,23,239,3,25,4,13
	.byte	'HSCT',0
	.word	62688
	.byte	4,2,35,0,0,14
	.word	62693
	.byte	27
	.byte	'Ifx_SRC_GHSCT',0,23,242,3,3
	.word	62729
	.byte	15,64
	.word	59913
	.byte	16,3,0,14
	.word	62757
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,23,245,3,25,68,13
	.byte	'HSSL',0
	.word	62766
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	57705
	.byte	4,2,35,64,0,14
	.word	62771
	.byte	27
	.byte	'Ifx_SRC_GHSSL',0,23,249,3,3
	.word	62820
	.byte	15,80
	.word	60022
	.byte	16,0,0,14
	.word	62848
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,23,252,3,25,80,13
	.byte	'I2C',0
	.word	62857
	.byte	80,2,35,0,0,14
	.word	62862
	.byte	27
	.byte	'Ifx_SRC_GI2C',0,23,255,3,3
	.word	62896
	.byte	15,4
	.word	60171
	.byte	16,0,0,14
	.word	62923
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,23,130,4,25,4,13
	.byte	'LMU',0
	.word	62932
	.byte	4,2,35,0,0,14
	.word	62937
	.byte	27
	.byte	'Ifx_SRC_GLMU',0,23,133,4,3
	.word	62971
	.byte	15,40
	.word	60229
	.byte	16,1,0,14
	.word	62998
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,23,136,4,25,40,13
	.byte	'MSC',0
	.word	63007
	.byte	40,2,35,0,0,14
	.word	63012
	.byte	27
	.byte	'Ifx_SRC_GMSC',0,23,139,4,3
	.word	63046
	.byte	15,8
	.word	60340
	.byte	16,1,0,14
	.word	63073
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,23,142,4,25,8,13
	.byte	'PMU',0
	.word	63082
	.byte	8,2,35,0,0,14
	.word	63087
	.byte	27
	.byte	'Ifx_SRC_GPMU',0,23,145,4,3
	.word	63121
	.byte	15,32
	.word	60398
	.byte	16,0,0,14
	.word	63148
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,23,148,4,25,32,13
	.byte	'PSI5',0
	.word	63157
	.byte	32,2,35,0,0,14
	.word	63162
	.byte	27
	.byte	'Ifx_SRC_GPSI5',0,23,151,4,3
	.word	63198
	.byte	15,32
	.word	60458
	.byte	16,0,0,14
	.word	63226
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,23,154,4,25,32,13
	.byte	'PSI5S',0
	.word	63235
	.byte	32,2,35,0,0,14
	.word	63240
	.byte	27
	.byte	'Ifx_SRC_GPSI5S',0,23,157,4,3
	.word	63278
	.byte	15,96
	.word	60520
	.byte	16,3,0,14
	.word	63307
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,23,160,4,25,96,13
	.byte	'QSPI',0
	.word	63316
	.byte	96,2,35,0,0,14
	.word	63321
	.byte	27
	.byte	'Ifx_SRC_GQSPI',0,23,163,4,3
	.word	63357
	.byte	15,4
	.word	60640
	.byte	16,0,0,14
	.word	63385
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,23,166,4,25,4,13
	.byte	'SCR',0
	.word	63394
	.byte	4,2,35,0,0,14
	.word	63399
	.byte	27
	.byte	'Ifx_SRC_GSCR',0,23,169,4,3
	.word	63433
	.byte	14
	.word	60698
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,23,172,4,25,20,13
	.byte	'SCU',0
	.word	63460
	.byte	20,2,35,0,0,14
	.word	63465
	.byte	27
	.byte	'Ifx_SRC_GSCU',0,23,175,4,3
	.word	63499
	.byte	15,24
	.word	60779
	.byte	16,0,0,14
	.word	63526
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,23,178,4,25,24,13
	.byte	'SENT',0
	.word	63535
	.byte	24,2,35,0,0,14
	.word	63540
	.byte	27
	.byte	'Ifx_SRC_GSENT',0,23,181,4,3
	.word	63576
	.byte	15,12
	.word	60839
	.byte	16,0,0,14
	.word	63604
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,23,184,4,25,12,13
	.byte	'SMU',0
	.word	63613
	.byte	12,2,35,0,0,14
	.word	63618
	.byte	27
	.byte	'Ifx_SRC_GSMU',0,23,187,4,3
	.word	63652
	.byte	15,16
	.word	60897
	.byte	16,1,0,14
	.word	63679
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,23,190,4,25,16,13
	.byte	'STM',0
	.word	63688
	.byte	16,2,35,0,0,14
	.word	63693
	.byte	27
	.byte	'Ifx_SRC_GSTM',0,23,193,4,3
	.word	63727
	.byte	15,64
	.word	61073
	.byte	16,3,0,14
	.word	63754
	.byte	15,224,1
	.word	641
	.byte	16,223,1,0,15,32
	.word	60969
	.byte	16,1,0,14
	.word	63779
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,23,196,4,25,192,2,13
	.byte	'G',0
	.word	63763
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	63768
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	63788
	.byte	32,3,35,160,2,0,14
	.word	63793
	.byte	27
	.byte	'Ifx_SRC_GVADC',0,23,201,4,3
	.word	63862
	.byte	14
	.word	61175
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,23,204,4,25,4,13
	.byte	'XBAR',0
	.word	63890
	.byte	4,2,35,0,0,14
	.word	63895
	.byte	27
	.byte	'Ifx_SRC_GXBAR',0,23,207,4,3
	.word	63931
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,24,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_STM_ACCEN0_Bits',0,24,79,3
	.word	63959
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,24,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1_Bits',0,24,85,3
	.word	64516
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,24,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAP_Bits',0,24,91,3
	.word	64593
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,24,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV_Bits',0,24,97,3
	.word	64665
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,24,100,16,4,11
	.byte	'DISR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_CLC_Bits',0,24,107,3
	.word	64741
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,24,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	641
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	641
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	641
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	641
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_STM_CMCON_Bits',0,24,120,3
	.word	64882
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,24,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CMP_Bits',0,24,126,3
	.word	65100
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,24,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	466
	.byte	25,0,2,35,0,0,27
	.byte	'Ifx_STM_ICR_Bits',0,24,139,1,3
	.word	65167
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,24,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_STM_ID_Bits',0,24,147,1,3
	.word	65370
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,24,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_ISCR_Bits',0,24,157,1,3
	.word	65477
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,24,160,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	466
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST0_Bits',0,24,165,1,3
	.word	65628
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,24,168,1,16,4,11
	.byte	'RST',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST1_Bits',0,24,172,1,3
	.word	65739
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,24,175,1,16,4,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR_Bits',0,24,179,1,3
	.word	65831
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,24,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	641
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	641
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_STM_OCS_Bits',0,24,189,1,3
	.word	65927
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,24,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0_Bits',0,24,195,1,3
	.word	66073
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,24,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV_Bits',0,24,201,1,3
	.word	66145
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,24,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM1_Bits',0,24,207,1,3
	.word	66221
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,24,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM2_Bits',0,24,213,1,3
	.word	66293
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,24,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM3_Bits',0,24,219,1,3
	.word	66365
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,24,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM4_Bits',0,24,225,1,3
	.word	66438
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,24,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM5_Bits',0,24,231,1,3
	.word	66511
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,24,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM6_Bits',0,24,237,1,3
	.word	66584
	.byte	12,24,245,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63959
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN0',0,24,250,1,3
	.word	66657
	.byte	12,24,253,1,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64516
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1',0,24,130,2,3
	.word	66721
	.byte	12,24,133,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64593
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAP',0,24,138,2,3
	.word	66785
	.byte	12,24,141,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64665
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV',0,24,146,2,3
	.word	66846
	.byte	12,24,149,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64741
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CLC',0,24,154,2,3
	.word	66909
	.byte	12,24,157,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64882
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMCON',0,24,162,2,3
	.word	66970
	.byte	12,24,165,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65100
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMP',0,24,170,2,3
	.word	67033
	.byte	12,24,173,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65167
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ICR',0,24,178,2,3
	.word	67094
	.byte	12,24,181,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65370
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ID',0,24,186,2,3
	.word	67155
	.byte	12,24,189,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65477
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ISCR',0,24,194,2,3
	.word	67215
	.byte	12,24,197,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65628
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST0',0,24,202,2,3
	.word	67277
	.byte	12,24,205,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65739
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST1',0,24,210,2,3
	.word	67340
	.byte	12,24,213,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65831
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR',0,24,218,2,3
	.word	67403
	.byte	12,24,221,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65927
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_OCS',0,24,226,2,3
	.word	67468
	.byte	12,24,229,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66073
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0',0,24,234,2,3
	.word	67529
	.byte	12,24,237,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66145
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV',0,24,242,2,3
	.word	67591
	.byte	12,24,245,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66221
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM1',0,24,250,2,3
	.word	67655
	.byte	12,24,253,2,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66293
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM2',0,24,130,3,3
	.word	67717
	.byte	12,24,133,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66365
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM3',0,24,138,3,3
	.word	67779
	.byte	12,24,141,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66438
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM4',0,24,146,3,3
	.word	67841
	.byte	12,24,149,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66511
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM5',0,24,154,3,3
	.word	67903
	.byte	12,24,157,3,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66584
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM6',0,24,162,3,3
	.word	67965
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,25,45,16,4,11
	.byte	'EN0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,25,79,3
	.word	68027
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,25,82,16,4,11
	.byte	'reserved_0',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,25,85,3
	.word	68588
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,25,88,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,25,95,3
	.word	68669
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,25,98,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,25,111,3
	.word	68822
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,25,114,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,25,121,3
	.word	69070
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,25,124,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	466
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0_Bits',0,25,128,1,3
	.word	69216
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,25,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM1_Bits',0,25,136,1,3
	.word	69314
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,25,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM2_Bits',0,25,144,1,3
	.word	69430
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,25,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRD_Bits',0,25,153,1,3
	.word	69546
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,25,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRP_Bits',0,25,162,1,3
	.word	69686
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,25,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	466
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	658
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCW_Bits',0,25,171,1,3
	.word	69826
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,25,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	641
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	641
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	658
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	641
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	641
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FCON_Bits',0,25,193,1,3
	.word	69965
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,25,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	641
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	641
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	641
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FPRO_Bits',0,25,218,1,3
	.word	70327
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,25,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	658
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	641
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	641
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FSR_Bits',0,25,254,1,3
	.word	70768
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,25,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	641
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	641
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_ID_Bits',0,25,134,2,3
	.word	71374
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,25,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	658
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARD_Bits',0,25,147,2,3
	.word	71485
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,25,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	658
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARP_Bits',0,25,159,2,3
	.word	71699
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,25,162,2,16,4,11
	.byte	'L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	641
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	658
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	641
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCOND_Bits',0,25,179,2,3
	.word	71886
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,25,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	641
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	466
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,25,188,2,3
	.word	72210
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,25,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	658
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,25,199,2,3
	.word	72353
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,25,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	658
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	641
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	641
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	641
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	658
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,25,219,2,3
	.word	72542
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,25,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	641
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,25,254,2,3
	.word	72905
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,25,129,3,16,4,11
	.byte	'S0L',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONP_Bits',0,25,160,3,3
	.word	73500
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,25,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	641
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	641
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	641
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	641
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	641
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	641
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	641
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	641
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	641
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	641
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	641
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	641
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	641
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	641
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	641
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	641
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	641
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	641
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	641
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	641
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	641
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,25,194,3,3
	.word	74024
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,25,197,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,25,201,3,3
	.word	74606
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,25,204,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,25,208,3,3
	.word	74708
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,25,211,3,16,4,11
	.byte	'TAG',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	466
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,25,215,3,3
	.word	74810
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,25,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	466
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD_Bits',0,25,222,3,3
	.word	74912
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,25,225,3,16,4,11
	.byte	'STRT',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	641
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	641
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	641
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	641
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	641
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	658
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_RRCT_Bits',0,25,236,3,3
	.word	75006
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,25,239,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0_Bits',0,25,242,3,3
	.word	75216
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,25,245,3,16,4,11
	.byte	'DATA',0,4
	.word	466
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1_Bits',0,25,248,3,3
	.word	75289
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,25,251,3,16,4,11
	.byte	'SEL',0,1
	.word	641
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	641
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	641
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	466
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,25,130,4,3
	.word	75362
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,25,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	641
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	466
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,25,137,4,3
	.word	75517
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,25,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	641
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	466
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	641
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	641
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	641
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,25,147,4,3
	.word	75622
	.byte	12,25,155,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68027
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN0',0,25,160,4,3
	.word	75770
	.byte	12,25,163,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68588
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1',0,25,168,4,3
	.word	75836
	.byte	12,25,171,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68669
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG',0,25,176,4,3
	.word	75902
	.byte	12,25,179,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68822
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT',0,25,184,4,3
	.word	75970
	.byte	12,25,187,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69070
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_TOP',0,25,192,4,3
	.word	76039
	.byte	12,25,195,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69216
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0',0,25,200,4,3
	.word	76107
	.byte	12,25,203,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69314
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM1',0,25,208,4,3
	.word	76172
	.byte	12,25,211,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69430
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM2',0,25,216,4,3
	.word	76237
	.byte	12,25,219,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69546
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRD',0,25,224,4,3
	.word	76302
	.byte	12,25,227,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69686
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRP',0,25,232,4,3
	.word	76367
	.byte	12,25,235,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69826
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCW',0,25,240,4,3
	.word	76432
	.byte	12,25,243,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69965
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FCON',0,25,248,4,3
	.word	76496
	.byte	12,25,251,4,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70327
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FPRO',0,25,128,5,3
	.word	76560
	.byte	12,25,131,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70768
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FSR',0,25,136,5,3
	.word	76624
	.byte	12,25,139,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71374
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ID',0,25,144,5,3
	.word	76687
	.byte	12,25,147,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71485
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARD',0,25,152,5,3
	.word	76749
	.byte	12,25,155,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71699
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARP',0,25,160,5,3
	.word	76813
	.byte	12,25,163,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71886
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCOND',0,25,168,5,3
	.word	76877
	.byte	12,25,171,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72210
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG',0,25,176,5,3
	.word	76944
	.byte	12,25,179,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72353
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSM',0,25,184,5,3
	.word	77013
	.byte	12,25,187,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72542
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,25,192,5,3
	.word	77082
	.byte	12,25,195,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72905
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONOTP',0,25,200,5,3
	.word	77155
	.byte	12,25,203,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73500
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONP',0,25,208,5,3
	.word	77224
	.byte	12,25,211,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74024
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONWOP',0,25,216,5,3
	.word	77291
	.byte	12,25,219,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74606
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0',0,25,224,5,3
	.word	77360
	.byte	12,25,227,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74708
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1',0,25,232,5,3
	.word	77428
	.byte	12,25,235,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74810
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2',0,25,240,5,3
	.word	77496
	.byte	12,25,243,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74912
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD',0,25,248,5,3
	.word	77564
	.byte	12,25,251,5,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75006
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRCT',0,25,128,6,3
	.word	77628
	.byte	12,25,131,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75216
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0',0,25,136,6,3
	.word	77692
	.byte	12,25,139,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75289
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1',0,25,144,6,3
	.word	77756
	.byte	12,25,147,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75362
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG',0,25,152,6,3
	.word	77820
	.byte	12,25,155,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75517
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT',0,25,160,6,3
	.word	77888
	.byte	12,25,163,6,9,4,13
	.byte	'U',0
	.word	466
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	482
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75622
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_TOP',0,25,168,6,3
	.word	77957
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,25,179,6,25,12,13
	.byte	'CFG',0
	.word	75902
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	75970
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	76039
	.byte	4,2,35,8,0,14
	.word	78025
	.byte	27
	.byte	'Ifx_FLASH_CBAB',0,25,184,6,3
	.word	78088
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,25,187,6,25,12,13
	.byte	'CFG0',0
	.word	77360
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	77428
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	77496
	.byte	4,2,35,8,0,14
	.word	78117
	.byte	27
	.byte	'Ifx_FLASH_RDB',0,25,192,6,3
	.word	78181
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,25,195,6,25,12,13
	.byte	'CFG',0
	.word	77820
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	77888
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	77957
	.byte	4,2,35,8,0,14
	.word	78209
	.byte	27
	.byte	'Ifx_FLASH_UBAB',0,25,200,6,3
	.word	78272
	.byte	27
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8031
	.byte	27
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7944
	.byte	27
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4287
	.byte	27
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2340
	.byte	27
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3335
	.byte	27
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2468
	.byte	27
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3115
	.byte	27
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2683
	.byte	27
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2898
	.byte	27
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7303
	.byte	27
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7427
	.byte	27
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7511
	.byte	27
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7691
	.byte	27
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5942
	.byte	27
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6466
	.byte	27
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6116
	.byte	27
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6290
	.byte	27
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6955
	.byte	27
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1769
	.byte	27
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5279
	.byte	27
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5767
	.byte	27
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5426
	.byte	27
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5595
	.byte	27
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6622
	.byte	27
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1453
	.byte	27
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4993
	.byte	27
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4627
	.byte	27
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3658
	.byte	27
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3962
	.byte	27
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8558
	.byte	27
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7991
	.byte	27
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4578
	.byte	27
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2419
	.byte	27
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3609
	.byte	27
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2643
	.byte	27
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3295
	.byte	27
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2858
	.byte	27
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3075
	.byte	27
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7387
	.byte	27
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7636
	.byte	27
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7895
	.byte	27
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7263
	.byte	27
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6076
	.byte	27
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6582
	.byte	27
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6250
	.byte	27
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6426
	.byte	27
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2300
	.byte	27
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6915
	.byte	27
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5386
	.byte	27
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5902
	.byte	27
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5555
	.byte	27
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5727
	.byte	27
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1729
	.byte	27
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5239
	.byte	27
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4953
	.byte	27
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3922
	.byte	27
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4238
	.byte	14
	.word	8598
	.byte	27
	.byte	'Ifx_P',0,6,139,6,3
	.word	79619
	.byte	27
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	9211
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,27
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	79665
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,27
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	79909
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,27
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	80007
	.byte	27
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9416
	.byte	25,5,190,1,9,8,13
	.byte	'port',0
	.word	9206
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	641
	.byte	1,2,35,4,0,27
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	80472
	.byte	14
	.word	39195
	.byte	3
	.word	80532
	.byte	25,26,74,15,20,13
	.byte	'module',0
	.word	80537
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	641
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	80472
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	14479
	.byte	1,2,35,16,0,26
	.word	80542
	.byte	27
	.byte	'IfxScu_Req_In',0,26,80,3
	.word	80612
	.byte	27
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,27,148,1,16
	.word	205
	.byte	25,27,212,5,9,8,13
	.byte	'value',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9752
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_CcuconRegConfig',0,27,216,5,3
	.word	80679
	.byte	25,27,221,5,9,8,13
	.byte	'pDivider',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	641
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	641
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_InitialStepConfig',0,27,227,5,3
	.word	80750
	.byte	25,27,231,5,9,12,13
	.byte	'k2Step',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	262
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	80639
	.byte	4,2,35,8,0,27
	.byte	'IfxScuCcu_PllStepsConfig',0,27,236,5,3
	.word	80867
	.byte	3
	.word	202
	.byte	25,27,244,5,9,48,13
	.byte	'ccucon0',0
	.word	80679
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	80679
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	80679
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	80679
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	80679
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	80679
	.byte	8,2,35,40,0,27
	.byte	'IfxScuCcu_ClockDistributionConfig',0,27,252,5,3
	.word	80969
	.byte	25,27,128,6,9,8,13
	.byte	'value',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9752
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,27,132,6,3
	.word	81121
	.byte	3
	.word	80867
	.byte	25,27,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	641
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	81197
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	80750
	.byte	8,2,35,8,0,27
	.byte	'IfxScuCcu_SysPllConfig',0,27,142,6,3
	.word	81202
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,27
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	81319
	.byte	25,7,160,1,9,6,13
	.byte	'counter',0
	.word	9752
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	641
	.byte	1,2,35,4,0,27
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	81408
	.byte	25,7,172,1,9,32,13
	.byte	'instruction',0
	.word	81408
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	81408
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	81408
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	81408
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	81408
	.byte	6,2,35,24,0,27
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	81474
	.byte	27
	.byte	'exti_pin_enum',0,12,61,2
	.word	10432
	.byte	27
	.byte	'exti_trigger_enum',0,12,70,2
	.word	10761
	.byte	27
	.byte	'gpio_pin_enum',0,13,89,2
	.word	10883
	.byte	27
	.byte	'gpio_dir_enum',0,13,95,2
	.word	12857
	.byte	27
	.byte	'gpio_mode_enum',0,13,111,2
	.word	12875
	.byte	27
	.byte	'soft_iic_info_struct',0,14,50,2
	.word	13038
	.byte	27
	.byte	'dl1a_vcsel_period_type_enum',0,15,176,1,2
	.word	13775
	.byte	27
	.byte	'dl1a_sequence_enables_step_struct',0,15,185,1,2
	.word	13683
	.byte	27
	.byte	'dl1a_sequence_timeout_step_struct',0,15,198,1,2
	.word	13854
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L106:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63
	.byte	12,60,12,0,0,21,5,0,58,15,59,15,57,15,73,19,0,0,22,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,19,1,58,15,59,15,57,15,11,15,0,0,26,38,0,73,19,0,0,27,22,0,3
	.byte	8,58,15,59,15,57,15,73,19,0,0,28,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L107:
	.word	.L353-.L352
.L352:
	.half	3
	.word	.L355-.L354
.L354:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'string.h',0,2,0,0
	.byte	'zf_common_debug.h',0,3,0,0
	.byte	'zf_driver_delay.h',0,4,0,0
	.byte	'zf_driver_exti.h',0,4,0,0
	.byte	'zf_driver_gpio.h',0,4,0,0
	.byte	'zf_driver_soft_iic.h',0,4,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_dl1a.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0
	.byte	'stdio.h',0,2,0,0
	.byte	'Platform_Types.h',0,5,0,0
	.byte	'ifx_types.h',0,5,0,0
	.byte	'zf_common_typedef.h',0,3,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxScu_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L355:
.L353:
	.sdecl	'.debug_info',debug,cluster('dl1a_get_distance')
	.sect	'.debug_info'
.L108:
	.word	285
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L111,.L110
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_get_distance',0,1,248,3,6,1,1,1
	.word	.L99,.L189,.L98
	.byte	4
	.word	.L99,.L189
	.byte	5
	.byte	'reg_databuffer',0,1,250,3,11
	.word	.L190,.L191
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_get_distance')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_get_distance')
	.sect	'.debug_line'
.L110:
	.word	.L357-.L356
.L356:
	.half	3
	.word	.L359-.L358
.L358:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L359:
	.byte	5,6,7,0,5,2
	.word	.L99
	.byte	3,247,3,1,5,5,9
	.half	.L336-.L99
	.byte	3,4,1,5,23,9
	.half	.L360-.L336
	.byte	3,1,1,5,27,9
	.half	.L361-.L360
	.byte	1,5,5,9
	.half	.L362-.L361
	.byte	1,5,9,7,9
	.half	.L363-.L362
	.byte	3,3,1,9
	.half	.L364-.L363
	.byte	3,1,1,5,53,9
	.half	.L365-.L364
	.byte	1,5,57,9
	.half	.L366-.L365
	.byte	1,5,26,9
	.half	.L367-.L366
	.byte	1,5,9,9
	.half	.L368-.L367
	.byte	3,1,1,5,43,9
	.half	.L369-.L368
	.byte	1,5,26,9
	.half	.L370-.L369
	.byte	1,5,9,9
	.half	.L371-.L370
	.byte	3,2,1,9
	.half	.L372-.L371
	.byte	3,1,1,5,27,9
	.half	.L373-.L372
	.byte	1,5,25,9
	.half	.L374-.L373
	.byte	1,5,22,9
	.half	.L54-.L374
	.byte	3,2,1,5,5,9
	.half	.L375-.L54
	.byte	1,5,9,7,9
	.half	.L376-.L375
	.byte	3,2,1,9
	.half	.L377-.L376
	.byte	3,1,1,5,1,9
	.half	.L55-.L377
	.byte	3,2,1,7,9
	.half	.L112-.L55
	.byte	0,1,1
.L357:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_get_distance')
	.sect	'.debug_ranges'
.L111:
	.word	-1,.L99,0,.L112-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_int_handler')
	.sect	'.debug_info'
.L113:
	.word	255
	.half	3
	.word	.L114
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L116,.L115
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_int_handler',0,1,149,4,6,1,1,1
	.word	.L101,.L192,.L100
	.byte	4
	.word	.L101,.L192
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_int_handler')
	.sect	'.debug_abbrev'
.L114:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_int_handler')
	.sect	'.debug_line'
.L115:
	.word	.L379-.L378
.L378:
	.half	3
	.word	.L381-.L380
.L380:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L381:
	.byte	5,22,7,0,5,2
	.word	.L101
	.byte	3,151,4,1,5,1,9
	.half	.L382-.L101
	.byte	3,2,1,7,9
	.half	.L117-.L382
	.byte	0,1,1
.L379:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_int_handler')
	.sect	'.debug_ranges'
.L116:
	.word	-1,.L101,0,.L117-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_init')
	.sect	'.debug_info'
.L118:
	.word	443
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L121,.L120
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_init',0,1,163,4,7
	.word	.L193
	.byte	1,1,1
	.word	.L103,.L194,.L102
	.byte	4
	.word	.L103,.L194
	.byte	5
	.byte	'measurement_timing_budget_us',0,1,165,4,12
	.word	.L195,.L196
	.byte	5
	.byte	'stop_variable',0,1,166,4,11
	.word	.L193,.L197
	.byte	5
	.byte	'return_state',0,1,167,4,11
	.word	.L193,.L198
	.byte	5
	.byte	'reg_data_buffer',0,1,168,4,11
	.word	.L193,.L199
	.byte	5
	.byte	'ref_spad_map',0,1,169,4,11
	.word	.L200,.L201
	.byte	5
	.byte	'data_buffer',0,1,170,4,11
	.word	.L202,.L203
	.byte	5
	.byte	'i',0,1,171,4,11
	.word	.L193,.L204
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_init')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_init')
	.sect	'.debug_line'
.L120:
	.word	.L384-.L383
.L383:
	.half	3
	.word	.L386-.L385
.L385:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L386:
	.byte	5,7,7,0,5,2
	.word	.L103
	.byte	3,162,4,1,5,27,9
	.half	.L337-.L103
	.byte	3,3,1,5,25,9
	.half	.L387-.L337
	.byte	1,5,24,9
	.half	.L388-.L387
	.byte	3,1,1,5,12,9
	.half	.L338-.L388
	.byte	3,6,1,5,26,9
	.half	.L389-.L338
	.byte	1,5,29,9
	.half	.L390-.L389
	.byte	1,5,12,9
	.half	.L391-.L390
	.byte	3,1,1,5,25,9
	.half	.L392-.L391
	.byte	1,5,28,9
	.half	.L393-.L392
	.byte	1,5,20,9
	.half	.L394-.L393
	.byte	3,3,1,5,37,9
	.half	.L395-.L394
	.byte	1,5,52,9
	.half	.L396-.L395
	.byte	1,5,73,9
	.half	.L397-.L396
	.byte	1,5,87,9
	.half	.L398-.L397
	.byte	1,5,15,9
	.half	.L399-.L398
	.byte	3,4,1,5,28,9
	.half	.L400-.L399
	.byte	1,5,33,9
	.half	.L401-.L400
	.byte	1,5,44,9
	.half	.L402-.L401
	.byte	1,5,25,9
	.half	.L56-.L402
	.byte	3,4,1,5,9,9
	.half	.L403-.L56
	.byte	3,1,1,5,25,9
	.half	.L404-.L403
	.byte	3,1,1,5,9,9
	.half	.L405-.L404
	.byte	3,1,1,5,25,9
	.half	.L406-.L405
	.byte	3,1,1,5,27,9
	.half	.L407-.L406
	.byte	3,3,1,5,9,9
	.half	.L339-.L407
	.byte	3,1,1,9
	.half	.L340-.L339
	.byte	3,2,1,9
	.half	.L408-.L340
	.byte	3,2,1,9
	.half	.L409-.L408
	.byte	3,1,1,9
	.half	.L410-.L409
	.byte	3,1,1,9
	.half	.L411-.L410
	.byte	3,2,1,9
	.half	.L412-.L411
	.byte	3,2,1,9
	.half	.L413-.L412
	.byte	3,1,1,9
	.half	.L414-.L413
	.byte	3,1,1,5,27,9
	.half	.L415-.L414
	.byte	3,3,1,5,9,9
	.half	.L341-.L415
	.byte	3,1,1,5,36,9
	.half	.L342-.L341
	.byte	3,2,1,5,9,9
	.half	.L416-.L342
	.byte	3,1,1,5,32,9
	.half	.L417-.L416
	.byte	3,4,1,5,60,9
	.half	.L418-.L417
	.byte	1,5,9,9
	.half	.L419-.L418
	.byte	1,5,26,7,9
	.half	.L420-.L419
	.byte	3,2,1,5,13,9
	.half	.L421-.L420
	.byte	3,1,1,9
	.half	.L422-.L421
	.byte	3,1,1,5,9,9
	.half	.L57-.L422
	.byte	3,4,1,9
	.half	.L423-.L57
	.byte	3,2,1,9
	.half	.L424-.L423
	.byte	3,1,1,9
	.half	.L425-.L424
	.byte	3,1,1,9
	.half	.L426-.L425
	.byte	3,1,1,9
	.half	.L427-.L426
	.byte	3,1,1,5,37,9
	.half	.L428-.L427
	.byte	3,2,1,5,26,9
	.half	.L429-.L428
	.byte	1,5,41,7,9
	.half	.L430-.L429
	.byte	1,5,46,9
	.half	.L431-.L430
	.byte	1,5,41,9
	.half	.L59-.L431
	.byte	1,5,24,9
	.half	.L60-.L59
	.byte	1,5,15,9
	.half	.L432-.L60
	.byte	3,1,1,5,26,9
	.half	.L343-.L432
	.byte	1,5,31,9
	.half	.L62-.L343
	.byte	3,2,1,5,16,9
	.half	.L433-.L62
	.byte	1,5,49,7,9
	.half	.L434-.L433
	.byte	1,5,67,9
	.half	.L435-.L434
	.byte	1,5,53,9
	.half	.L436-.L435
	.byte	1,5,34,7,9
	.half	.L63-.L436
	.byte	3,5,1,5,32,9
	.half	.L437-.L63
	.byte	1,5,29,9
	.half	.L438-.L437
	.byte	1,5,34,9
	.half	.L439-.L438
	.byte	1,5,32,9
	.half	.L440-.L439
	.byte	1,5,29,9
	.half	.L441-.L440
	.byte	1,5,42,9
	.half	.L442-.L441
	.byte	1,5,52,9
	.half	.L443-.L442
	.byte	1,5,50,9
	.half	.L444-.L443
	.byte	1,5,44,9
	.half	.L445-.L444
	.byte	1,5,40,9
	.half	.L446-.L445
	.byte	1,5,37,9
	.half	.L447-.L446
	.byte	1,5,55,9
	.half	.L448-.L447
	.byte	1,5,39,9
	.half	.L64-.L448
	.byte	3,2,1,5,37,9
	.half	.L449-.L64
	.byte	1,5,34,9
	.half	.L450-.L449
	.byte	1,5,50,9
	.half	.L451-.L450
	.byte	1,5,48,9
	.half	.L452-.L451
	.byte	1,5,42,9
	.half	.L453-.L452
	.byte	1,5,18,9
	.half	.L454-.L453
	.byte	1,5,28,7,9
	.half	.L455-.L454
	.byte	3,2,1,5,32,9
	.half	.L456-.L455
	.byte	1,5,30,9
	.half	.L65-.L456
	.byte	3,117,1,5,20,9
	.half	.L61-.L65
	.byte	1,5,26,9
	.half	.L457-.L61
	.byte	1,7,9
	.half	.L458-.L457
	.byte	3,15,1,5,24,9
	.half	.L459-.L458
	.byte	1,5,15,9
	.half	.L460-.L459
	.byte	3,1,1,5,25,9
	.half	.L344-.L460
	.byte	1,5,42,9
	.half	.L68-.L344
	.byte	3,2,1,5,28,9
	.half	.L461-.L68
	.byte	1,5,29,9
	.half	.L462-.L461
	.byte	3,126,1,5,25,9
	.half	.L67-.L462
	.byte	1,5,9,7,9
	.half	.L463-.L67
	.byte	3,4,1,9
	.half	.L464-.L463
	.byte	3,3,1,9
	.half	.L465-.L464
	.byte	3,1,1,9
	.half	.L466-.L465
	.byte	3,1,1,9
	.half	.L467-.L466
	.byte	3,1,1,9
	.half	.L468-.L467
	.byte	3,1,1,9
	.half	.L469-.L468
	.byte	3,1,1,9
	.half	.L470-.L469
	.byte	3,1,1,9
	.half	.L471-.L470
	.byte	3,1,1,9
	.half	.L472-.L471
	.byte	3,1,1,9
	.half	.L473-.L472
	.byte	3,1,1,9
	.half	.L474-.L473
	.byte	3,1,1,9
	.half	.L475-.L474
	.byte	3,1,1,9
	.half	.L476-.L475
	.byte	3,1,1,9
	.half	.L477-.L476
	.byte	3,1,1,9
	.half	.L478-.L477
	.byte	3,1,1,9
	.half	.L479-.L478
	.byte	3,1,1,9
	.half	.L480-.L479
	.byte	3,1,1,9
	.half	.L481-.L480
	.byte	3,1,1,9
	.half	.L482-.L481
	.byte	3,1,1,9
	.half	.L483-.L482
	.byte	3,1,1,9
	.half	.L484-.L483
	.byte	3,1,1,9
	.half	.L485-.L484
	.byte	3,1,1,9
	.half	.L486-.L485
	.byte	3,1,1,9
	.half	.L487-.L486
	.byte	3,1,1,9
	.half	.L488-.L487
	.byte	3,1,1,9
	.half	.L489-.L488
	.byte	3,1,1,9
	.half	.L490-.L489
	.byte	3,1,1,9
	.half	.L491-.L490
	.byte	3,1,1,9
	.half	.L492-.L491
	.byte	3,1,1,9
	.half	.L493-.L492
	.byte	3,1,1,9
	.half	.L494-.L493
	.byte	3,1,1,9
	.half	.L495-.L494
	.byte	3,1,1,9
	.half	.L496-.L495
	.byte	3,1,1,9
	.half	.L497-.L496
	.byte	3,1,1,9
	.half	.L498-.L497
	.byte	3,1,1,9
	.half	.L499-.L498
	.byte	3,1,1,9
	.half	.L500-.L499
	.byte	3,1,1,9
	.half	.L501-.L500
	.byte	3,1,1,9
	.half	.L502-.L501
	.byte	3,1,1,9
	.half	.L503-.L502
	.byte	3,1,1,9
	.half	.L504-.L503
	.byte	3,1,1,9
	.half	.L505-.L504
	.byte	3,1,1,9
	.half	.L506-.L505
	.byte	3,1,1,9
	.half	.L507-.L506
	.byte	3,1,1,9
	.half	.L508-.L507
	.byte	3,1,1,9
	.half	.L509-.L508
	.byte	3,1,1,9
	.half	.L510-.L509
	.byte	3,1,1,9
	.half	.L511-.L510
	.byte	3,1,1,9
	.half	.L512-.L511
	.byte	3,1,1,9
	.half	.L513-.L512
	.byte	3,1,1,9
	.half	.L514-.L513
	.byte	3,1,1,9
	.half	.L515-.L514
	.byte	3,1,1,9
	.half	.L516-.L515
	.byte	3,1,1,9
	.half	.L517-.L516
	.byte	3,1,1,9
	.half	.L518-.L517
	.byte	3,1,1,9
	.half	.L519-.L518
	.byte	3,1,1,9
	.half	.L520-.L519
	.byte	3,1,1,9
	.half	.L521-.L520
	.byte	3,1,1,9
	.half	.L522-.L521
	.byte	3,1,1,9
	.half	.L523-.L522
	.byte	3,1,1,9
	.half	.L524-.L523
	.byte	3,1,1,9
	.half	.L525-.L524
	.byte	3,1,1,9
	.half	.L526-.L525
	.byte	3,1,1,9
	.half	.L527-.L526
	.byte	3,1,1,9
	.half	.L528-.L527
	.byte	3,1,1,9
	.half	.L529-.L528
	.byte	3,1,1,9
	.half	.L530-.L529
	.byte	3,1,1,9
	.half	.L531-.L530
	.byte	3,1,1,9
	.half	.L532-.L531
	.byte	3,1,1,9
	.half	.L533-.L532
	.byte	3,1,1,9
	.half	.L534-.L533
	.byte	3,1,1,9
	.half	.L535-.L534
	.byte	3,1,1,9
	.half	.L536-.L535
	.byte	3,1,1,9
	.half	.L537-.L536
	.byte	3,1,1,9
	.half	.L538-.L537
	.byte	3,1,1,9
	.half	.L539-.L538
	.byte	3,1,1,9
	.half	.L540-.L539
	.byte	3,1,1,9
	.half	.L541-.L540
	.byte	3,1,1,9
	.half	.L542-.L541
	.byte	3,1,1,9
	.half	.L543-.L542
	.byte	3,1,1,9
	.half	.L544-.L543
	.byte	3,3,1,5,27,9
	.half	.L545-.L544
	.byte	3,1,1,5,9,9
	.half	.L346-.L545
	.byte	3,1,1,9
	.half	.L347-.L346
	.byte	3,1,1,5,75,9
	.half	.L546-.L347
	.byte	3,2,1,5,39,9
	.half	.L348-.L546
	.byte	1,5,9,9
	.half	.L345-.L348
	.byte	3,5,1,5,44,9
	.half	.L547-.L345
	.byte	3,1,1,5,9,9
	.half	.L350-.L547
	.byte	3,3,1,5,48,9
	.half	.L548-.L350
	.byte	3,1,1,5,9,9
	.half	.L549-.L548
	.byte	1,5,26,7,9
	.half	.L550-.L549
	.byte	3,2,1,5,13,9
	.half	.L551-.L550
	.byte	3,1,1,9
	.half	.L552-.L551
	.byte	3,1,1,5,9,9
	.half	.L69-.L552
	.byte	3,2,1,5,48,9
	.half	.L553-.L69
	.byte	3,1,1,5,9,9
	.half	.L554-.L553
	.byte	1,5,26,7,9
	.half	.L555-.L554
	.byte	3,2,1,5,13,9
	.half	.L556-.L555
	.byte	3,1,1,9
	.half	.L557-.L556
	.byte	3,1,1,5,9,9
	.half	.L71-.L557
	.byte	3,2,1,5,25,9
	.half	.L558-.L71
	.byte	3,2,1,5,9,9
	.half	.L559-.L558
	.byte	3,2,1,9
	.half	.L560-.L559
	.byte	3,1,1,9
	.half	.L561-.L560
	.byte	3,1,1,9
	.half	.L562-.L561
	.byte	3,1,1,9
	.half	.L563-.L562
	.byte	3,1,1,9
	.half	.L564-.L563
	.byte	3,1,1,9
	.half	.L565-.L564
	.byte	3,1,1,9
	.half	.L566-.L565
	.byte	3,2,1,5,15,9
	.half	.L58-.L566
	.byte	3,4,1,5,29,9
	.half	.L567-.L58
	.byte	1,5,21,9
	.half	.L568-.L567
	.byte	3,1,1,5,5,9
	.half	.L569-.L568
	.byte	3,1,1,5,23,9
	.half	.L570-.L569
	.byte	1,5,21,9
	.half	.L571-.L570
	.byte	1,5,5,9
	.half	.L572-.L571
	.byte	3,2,1,5,1,9
	.half	.L73-.L572
	.byte	3,1,1,7,9
	.half	.L122-.L73
	.byte	0,1,1
.L384:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_init')
	.sect	'.debug_ranges'
.L121:
	.word	-1,.L103,0,.L122-.L103,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_get_spad_info')
	.sect	'.debug_info'
.L123:
	.word	371
	.half	3
	.word	.L124
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L126,.L125
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_get_spad_info',0,1,90,14
	.word	.L193
	.byte	1,1
	.word	.L75,.L205,.L74
	.byte	4
	.byte	'index',0,1,90,41
	.word	.L206,.L207
	.byte	4
	.byte	'type_is_aperture',0,1,90,55
	.word	.L206,.L208
	.byte	5
	.word	.L75,.L205
	.byte	6
	.byte	'tmp',0,1,92,11
	.word	.L193,.L209
	.byte	6
	.byte	'return_state',0,1,93,11
	.word	.L193,.L210
	.byte	6
	.byte	'loop_count',0,1,94,21
	.word	.L211,.L212
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_get_spad_info')
	.sect	'.debug_abbrev'
.L124:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_get_spad_info')
	.sect	'.debug_line'
.L125:
	.word	.L574-.L573
.L573:
	.half	3
	.word	.L576-.L575
.L575:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L576:
	.byte	5,14,7,0,5,2
	.word	.L75
	.byte	3,217,0,1,5,17,9
	.half	.L278-.L75
	.byte	3,2,1,5,15,9
	.half	.L577-.L278
	.byte	1,5,24,9
	.half	.L578-.L577
	.byte	3,1,1,5,34,9
	.half	.L279-.L578
	.byte	3,1,1,5,32,9
	.half	.L579-.L279
	.byte	1,5,9,9
	.half	.L2-.L579
	.byte	3,4,1,9
	.half	.L276-.L2
	.byte	3,1,1,9
	.half	.L580-.L276
	.byte	3,1,1,9
	.half	.L581-.L580
	.byte	3,2,1,9
	.half	.L582-.L581
	.byte	3,1,1,9
	.half	.L583-.L582
	.byte	3,1,1,9
	.half	.L584-.L583
	.byte	3,1,1,9
	.half	.L585-.L584
	.byte	3,1,1,9
	.half	.L586-.L585
	.byte	3,2,1,9
	.half	.L587-.L586
	.byte	3,2,1,9
	.half	.L588-.L587
	.byte	3,1,1,5,15,9
	.half	.L589-.L588
	.byte	3,2,1,5,13,9
	.half	.L590-.L589
	.byte	1,5,41,9
	.half	.L591-.L590
	.byte	3,1,1,5,29,9
	.half	.L5-.L591
	.byte	3,2,1,5,13,9
	.half	.L592-.L5
	.byte	3,1,1,5,37,9
	.half	.L593-.L592
	.byte	3,1,1,5,48,9
	.half	.L594-.L593
	.byte	1,5,16,9
	.half	.L595-.L594
	.byte	1,5,13,9
	.half	.L596-.L595
	.byte	1,5,30,7,9
	.half	.L597-.L596
	.byte	3,2,1,5,17,9
	.half	.L598-.L597
	.byte	3,1,1,5,23,9
	.half	.L3-.L598
	.byte	3,121,1,5,15,9
	.half	.L599-.L3
	.byte	1,5,38,7,9
	.half	.L600-.L599
	.byte	1,5,30,9
	.half	.L601-.L600
	.byte	1,5,35,9
	.half	.L602-.L601
	.byte	1,5,9,7,9
	.half	.L7-.L602
	.byte	3,11,1,5,13,7,9
	.half	.L603-.L7
	.byte	3,2,1,5,9,9
	.half	.L8-.L603
	.byte	3,2,1,9
	.half	.L604-.L8
	.byte	3,1,1,5,18,9
	.half	.L605-.L604
	.byte	3,2,1,5,22,9
	.half	.L606-.L605
	.byte	1,5,16,9
	.half	.L607-.L606
	.byte	1,5,30,9
	.half	.L608-.L607
	.byte	3,1,1,5,34,9
	.half	.L609-.L608
	.byte	1,5,40,9
	.half	.L610-.L609
	.byte	1,5,27,9
	.half	.L611-.L610
	.byte	1,5,9,9
	.half	.L612-.L611
	.byte	3,2,1,9
	.half	.L613-.L612
	.byte	3,1,1,9
	.half	.L614-.L613
	.byte	3,1,1,9
	.half	.L615-.L614
	.byte	3,1,1,9
	.half	.L616-.L615
	.byte	3,1,1,9
	.half	.L617-.L616
	.byte	3,1,1,9
	.half	.L618-.L617
	.byte	3,2,1,9
	.half	.L619-.L618
	.byte	3,1,1,5,5,9
	.half	.L9-.L619
	.byte	3,3,1,5,1,9
	.half	.L10-.L9
	.byte	3,1,1,7,9
	.half	.L127-.L10
	.byte	0,1,1
.L574:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_get_spad_info')
	.sect	'.debug_ranges'
.L126:
	.word	-1,.L75,0,.L127-.L75,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_timeout_mclks_to_microseconds')
	.sect	'.debug_info'
.L128:
	.word	372
	.half	3
	.word	.L129
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L131,.L130
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_timeout_mclks_to_microseconds',0,1,157,1,15
	.word	.L195
	.byte	1,1
	.word	.L77,.L213,.L76
	.byte	4
	.byte	'timeout_period_mclks',0,1,157,1,58
	.word	.L214,.L215
	.byte	4
	.byte	'vcsel_period_pclks',0,1,157,1,86
	.word	.L193,.L216
	.byte	5
	.word	.L77,.L213
	.byte	6
	.byte	'macro_period_ns',0,1,159,1,12
	.word	.L195,.L217
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_timeout_mclks_to_microseconds')
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_timeout_mclks_to_microseconds')
	.sect	'.debug_line'
.L130:
	.word	.L621-.L620
.L620:
	.half	3
	.word	.L623-.L622
.L622:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L623:
	.byte	5,30,7,0,5,2
	.word	.L77
	.byte	3,158,1,1,5,75,9
	.half	.L282-.L77
	.byte	3,2,1,5,73,9
	.half	.L624-.L282
	.byte	1,5,54,9
	.half	.L625-.L624
	.byte	1,5,81,9
	.half	.L626-.L625
	.byte	1,5,79,9
	.half	.L283-.L626
	.byte	1,5,5,9
	.half	.L627-.L283
	.byte	1,5,1,9
	.half	.L11-.L627
	.byte	3,1,1,7,9
	.half	.L132-.L11
	.byte	0,1,1
.L621:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_timeout_mclks_to_microseconds')
	.sect	'.debug_ranges'
.L131:
	.word	-1,.L77,0,.L132-.L77,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_timeout_microseconds_to_mclks')
	.sect	'.debug_info'
.L133:
	.word	369
	.half	3
	.word	.L134
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L136,.L135
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_timeout_microseconds_to_mclks',0,1,172,1,15
	.word	.L195
	.byte	1,1
	.word	.L79,.L218,.L78
	.byte	4
	.byte	'timeout_period_us',0,1,172,1,58
	.word	.L195,.L219
	.byte	4
	.byte	'vcsel_period_pclks',0,1,172,1,83
	.word	.L193,.L220
	.byte	5
	.word	.L79,.L218
	.byte	6
	.byte	'macro_period_ns',0,1,174,1,12
	.word	.L195,.L221
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_timeout_microseconds_to_mclks')
	.sect	'.debug_abbrev'
.L134:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_timeout_microseconds_to_mclks')
	.sect	'.debug_line'
.L135:
	.word	.L629-.L628
.L628:
	.half	3
	.word	.L631-.L630
.L630:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L631:
	.byte	5,30,7,0,5,2
	.word	.L79
	.byte	3,173,1,1,5,35,9
	.half	.L285-.L79
	.byte	3,2,1,5,62,9
	.half	.L632-.L285
	.byte	1,5,60,9
	.half	.L633-.L632
	.byte	1,5,41,9
	.half	.L634-.L633
	.byte	1,5,66,9
	.half	.L635-.L634
	.byte	1,5,5,9
	.half	.L636-.L635
	.byte	1,5,1,9
	.half	.L12-.L636
	.byte	3,1,1,7,9
	.half	.L137-.L12
	.byte	0,1,1
.L629:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_timeout_microseconds_to_mclks')
	.sect	'.debug_ranges'
.L136:
	.word	-1,.L79,0,.L137-.L79,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_decode_timeout')
	.sect	'.debug_info'
.L138:
	.word	282
	.half	3
	.word	.L139
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L141,.L140
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_decode_timeout',0,1,186,1,15
	.word	.L214
	.byte	1,1
	.word	.L81,.L222,.L80
	.byte	4
	.byte	'reg_val',0,1,186,1,43
	.word	.L214,.L223
	.byte	5
	.word	.L81,.L222
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_decode_timeout')
	.sect	'.debug_abbrev'
.L139:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_decode_timeout')
	.sect	'.debug_line'
.L140:
	.word	.L638-.L637
.L637:
	.half	3
	.word	.L640-.L639
.L639:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L640:
	.byte	5,31,7,0,5,2
	.word	.L81
	.byte	3,188,1,1,5,33,9
	.half	.L641-.L81
	.byte	3,1,1,5,31,9
	.half	.L642-.L641
	.byte	1,5,41,9
	.half	.L286-.L642
	.byte	1,9
	.half	.L643-.L286
	.byte	3,127,1,5,13,9
	.half	.L644-.L643
	.byte	1,5,48,9
	.half	.L645-.L644
	.byte	3,1,1,5,5,9
	.half	.L646-.L645
	.byte	3,127,1,5,1,9
	.half	.L13-.L646
	.byte	3,2,1,7,9
	.half	.L142-.L13
	.byte	0,1,1
.L638:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_decode_timeout')
	.sect	'.debug_ranges'
.L141:
	.word	-1,.L81,0,.L142-.L81,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_encode_timeout')
	.sect	'.debug_info'
.L143:
	.word	356
	.half	3
	.word	.L144
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L146,.L145
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_encode_timeout',0,1,200,1,15
	.word	.L214
	.byte	1,1
	.word	.L83,.L224,.L82
	.byte	4
	.byte	'timeout_mclks',0,1,200,1,43
	.word	.L214,.L225
	.byte	5
	.word	.L83,.L224
	.byte	6
	.byte	'ls_byte',0,1,202,1,12
	.word	.L195,.L226
	.byte	6
	.byte	'ms_byte',0,1,203,1,12
	.word	.L214,.L227
	.byte	6
	.byte	'return_data',0,1,204,1,12
	.word	.L214,.L228
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_encode_timeout')
	.sect	'.debug_abbrev'
.L144:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_encode_timeout')
	.sect	'.debug_line'
.L145:
	.word	.L648-.L647
.L647:
	.half	3
	.word	.L650-.L649
.L649:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L650:
	.byte	5,20,7,0,5,2
	.word	.L83
	.byte	3,202,1,1,5,24,9
	.half	.L288-.L83
	.byte	3,1,1,5,5,9
	.half	.L290-.L288
	.byte	3,2,1,5,33,7,9
	.half	.L291-.L290
	.byte	3,3,1,5,41,9
	.half	.L287-.L291
	.byte	3,1,1,5,21,9
	.half	.L16-.L287
	.byte	3,2,1,5,20,9
	.half	.L651-.L16
	.byte	3,1,1,5,28,9
	.half	.L15-.L651
	.byte	3,125,1,5,41,9
	.half	.L652-.L15
	.byte	1,5,32,7,9
	.half	.L653-.L652
	.byte	3,5,1,5,41,9
	.half	.L654-.L653
	.byte	1,5,57,9
	.half	.L292-.L654
	.byte	1,5,38,9
	.half	.L655-.L292
	.byte	1,5,5,9
	.half	.L14-.L655
	.byte	3,2,1,5,1,9
	.half	.L17-.L14
	.byte	3,1,1,7,9
	.half	.L147-.L17
	.byte	0,1,1
.L648:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_encode_timeout')
	.sect	'.debug_ranges'
.L146:
	.word	-1,.L83,0,.L147-.L83,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_get_sequence_step_enables')
	.sect	'.debug_info'
.L148:
	.word	319
	.half	3
	.word	.L149
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L151,.L150
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_get_sequence_step_enables',0,1,227,1,13,1,1
	.word	.L85,.L229,.L84
	.byte	4
	.byte	'enables',0,1,227,1,79
	.word	.L230,.L231
	.byte	5
	.word	.L85,.L229
	.byte	6
	.byte	'sequence_config',0,1,229,1,11
	.word	.L193,.L232
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_get_sequence_step_enables')
	.sect	'.debug_abbrev'
.L149:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_get_sequence_step_enables')
	.sect	'.debug_line'
.L150:
	.word	.L657-.L656
.L656:
	.half	3
	.word	.L659-.L658
.L658:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L659:
	.byte	5,13,7,0,5,2
	.word	.L85
	.byte	3,226,1,1,5,29,9
	.half	.L295-.L85
	.byte	3,2,1,5,27,9
	.half	.L660-.L295
	.byte	1,5,5,9
	.half	.L661-.L660
	.byte	3,1,1,5,30,9
	.half	.L662-.L661
	.byte	3,2,1,5,46,9
	.half	.L663-.L662
	.byte	1,5,52,9
	.half	.L664-.L663
	.byte	1,5,27,9
	.half	.L665-.L664
	.byte	1,5,30,9
	.half	.L666-.L665
	.byte	3,1,1,5,46,9
	.half	.L667-.L666
	.byte	1,5,52,9
	.half	.L668-.L667
	.byte	1,5,27,9
	.half	.L669-.L668
	.byte	1,5,30,9
	.half	.L670-.L669
	.byte	3,1,1,5,46,9
	.half	.L671-.L670
	.byte	1,5,52,9
	.half	.L672-.L671
	.byte	1,5,27,9
	.half	.L673-.L672
	.byte	1,5,30,9
	.half	.L674-.L673
	.byte	3,1,1,5,46,9
	.half	.L675-.L674
	.byte	1,5,52,9
	.half	.L676-.L675
	.byte	1,5,27,9
	.half	.L677-.L676
	.byte	1,5,30,9
	.half	.L678-.L677
	.byte	3,1,1,5,46,9
	.half	.L679-.L678
	.byte	1,5,52,9
	.half	.L680-.L679
	.byte	1,5,27,9
	.half	.L681-.L680
	.byte	1,5,1,9
	.half	.L682-.L681
	.byte	3,1,1,7,9
	.half	.L152-.L682
	.byte	0,1,1
.L657:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_get_sequence_step_enables')
	.sect	'.debug_ranges'
.L151:
	.word	-1,.L85,0,.L152-.L85,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_get_vcsel_pulse_period')
	.sect	'.debug_info'
.L153:
	.word	313
	.half	3
	.word	.L154
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L156,.L155
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_get_vcsel_pulse_period',0,1,246,1,14
	.word	.L193
	.byte	1,1
	.word	.L87,.L233,.L86
	.byte	4
	.byte	'type',0,1,246,1,71
	.word	.L234,.L235
	.byte	5
	.word	.L87,.L233
	.byte	6
	.byte	'data_buffer',0,1,248,1,11
	.word	.L193,.L236
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_get_vcsel_pulse_period')
	.sect	'.debug_abbrev'
.L154:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_get_vcsel_pulse_period')
	.sect	'.debug_line'
.L155:
	.word	.L684-.L683
.L683:
	.half	3
	.word	.L686-.L685
.L685:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L686:
	.byte	5,14,7,0,5,2
	.word	.L87
	.byte	3,245,1,1,5,25,9
	.half	.L296-.L87
	.byte	3,2,1,5,23,9
	.half	.L687-.L296
	.byte	1,5,5,9
	.half	.L688-.L687
	.byte	3,1,1,5,9,7,9
	.half	.L689-.L688
	.byte	3,2,1,5,23,9
	.half	.L690-.L689
	.byte	3,1,1,5,21,9
	.half	.L691-.L690
	.byte	1,5,81,9
	.half	.L692-.L691
	.byte	3,127,1,5,10,9
	.half	.L18-.L692
	.byte	3,3,1,5,9,7,9
	.half	.L693-.L18
	.byte	3,2,1,5,23,9
	.half	.L694-.L693
	.byte	3,1,1,5,21,9
	.half	.L695-.L694
	.byte	1,5,83,9
	.half	.L696-.L695
	.byte	3,127,1,5,23,9
	.half	.L20-.L696
	.byte	3,5,1,5,21,9
	.half	.L697-.L20
	.byte	1,5,12,9
	.half	.L19-.L697
	.byte	3,2,1,5,5,9
	.half	.L698-.L19
	.byte	1,5,1,9
	.half	.L22-.L698
	.byte	3,1,1,7,9
	.half	.L157-.L22
	.byte	0,1,1
.L684:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_get_vcsel_pulse_period')
	.sect	'.debug_ranges'
.L156:
	.word	-1,.L87,0,.L157-.L87,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_get_sequence_step_timeouts')
	.sect	'.debug_info'
.L158:
	.word	364
	.half	3
	.word	.L159
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L161,.L160
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_get_sequence_step_timeouts',0,1,146,2,13,1,1
	.word	.L89,.L237,.L88
	.byte	4
	.byte	'enables',0,1,146,2,87
	.word	.L238,.L239
	.byte	4
	.byte	'timeouts',0,1,146,2,131,1
	.word	.L240,.L241
	.byte	5
	.word	.L89,.L237
	.byte	6
	.byte	'reg_buffer',0,1,148,2,11
	.word	.L242,.L243
	.byte	6
	.byte	'reg16_buffer',0,1,149,2,12
	.word	.L214,.L244
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_get_sequence_step_timeouts')
	.sect	'.debug_abbrev'
.L159:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_get_sequence_step_timeouts')
	.sect	'.debug_line'
.L160:
	.word	.L700-.L699
.L699:
	.half	3
	.word	.L702-.L701
.L701:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L702:
	.byte	5,13,7,0,5,2
	.word	.L89
	.byte	3,145,2,1,5,74,9
	.half	.L302-.L89
	.byte	3,5,1,5,44,9
	.half	.L300-.L302
	.byte	1,5,5,9
	.half	.L703-.L300
	.byte	3,2,1,5,46,9
	.half	.L704-.L703
	.byte	3,1,1,5,50,9
	.half	.L705-.L704
	.byte	1,5,34,9
	.half	.L706-.L705
	.byte	1,5,76,9
	.half	.L707-.L706
	.byte	3,1,1,5,113,9
	.half	.L708-.L707
	.byte	1,5,98,9
	.half	.L709-.L708
	.byte	1,5,31,9
	.half	.L710-.L709
	.byte	1,5,5,9
	.half	.L711-.L710
	.byte	3,2,1,5,40,9
	.half	.L712-.L711
	.byte	3,1,1,5,44,9
	.half	.L713-.L712
	.byte	1,5,62,9
	.half	.L714-.L713
	.byte	1,5,50,9
	.half	.L303-.L714
	.byte	1,5,53,9
	.half	.L715-.L303
	.byte	3,1,1,5,31,9
	.half	.L304-.L715
	.byte	1,5,73,9
	.half	.L716-.L304
	.byte	3,1,1,5,107,9
	.half	.L717-.L716
	.byte	1,5,92,9
	.half	.L718-.L717
	.byte	1,5,28,9
	.half	.L719-.L718
	.byte	1,5,76,9
	.half	.L720-.L719
	.byte	3,2,1,5,46,9
	.half	.L721-.L720
	.byte	1,5,5,9
	.half	.L722-.L721
	.byte	3,2,1,5,40,9
	.half	.L723-.L722
	.byte	3,1,1,5,44,9
	.half	.L724-.L723
	.byte	1,5,62,9
	.half	.L725-.L724
	.byte	1,5,50,9
	.half	.L305-.L725
	.byte	1,5,55,9
	.half	.L726-.L305
	.byte	3,1,1,5,33,9
	.half	.L306-.L726
	.byte	1,5,15,9
	.half	.L727-.L306
	.byte	3,2,1,5,5,9
	.half	.L728-.L727
	.byte	1,5,17,7,9
	.half	.L729-.L728
	.byte	3,2,1,5,48,9
	.half	.L730-.L729
	.byte	1,5,37,9
	.half	.L731-.L730
	.byte	1,5,75,9
	.half	.L23-.L731
	.byte	3,3,1,5,111,9
	.half	.L732-.L23
	.byte	1,5,96,9
	.half	.L733-.L732
	.byte	1,5,30,9
	.half	.L734-.L733
	.byte	1,5,1,9
	.half	.L735-.L734
	.byte	3,1,1,7,9
	.half	.L162-.L735
	.byte	0,1,1
.L700:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_get_sequence_step_timeouts')
	.sect	'.debug_ranges'
.L161:
	.word	-1,.L89,0,.L162-.L89,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_perform_single_ref_calibration')
	.sect	'.debug_info'
.L163:
	.word	380
	.half	3
	.word	.L164
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L166,.L165
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_perform_single_ref_calibration',0,1,183,2,14
	.word	.L193
	.byte	1,1
	.word	.L91,.L245,.L90
	.byte	4
	.byte	'vhv_init_byte',0,1,183,2,57
	.word	.L193,.L246
	.byte	5
	.word	.L91,.L245
	.byte	6
	.byte	'return_state',0,1,185,2,11
	.word	.L193,.L247
	.byte	6
	.byte	'data_buffer',0,1,186,2,11
	.word	.L193,.L248
	.byte	6
	.byte	'loop_count',0,1,187,2,21
	.word	.L249,.L250
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_perform_single_ref_calibration')
	.sect	'.debug_abbrev'
.L164:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_perform_single_ref_calibration')
	.sect	'.debug_line'
.L165:
	.word	.L737-.L736
.L736:
	.half	3
	.word	.L739-.L738
.L738:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L739:
	.byte	5,14,7,0,5,2
	.word	.L91
	.byte	3,182,2,1,5,24,9
	.half	.L307-.L91
	.byte	3,2,1,5,25,9
	.half	.L309-.L307
	.byte	3,1,1,5,23,9
	.half	.L740-.L309
	.byte	1,5,34,9
	.half	.L741-.L740
	.byte	3,1,1,5,32,9
	.half	.L742-.L741
	.byte	1,5,9,9
	.half	.L24-.L742
	.byte	3,3,1,9
	.half	.L743-.L24
	.byte	3,1,1,5,40,9
	.half	.L744-.L743
	.byte	3,1,1,5,29,9
	.half	.L26-.L744
	.byte	3,2,1,5,13,9
	.half	.L745-.L26
	.byte	3,1,1,5,37,9
	.half	.L746-.L745
	.byte	3,1,1,5,48,9
	.half	.L747-.L746
	.byte	1,5,16,9
	.half	.L748-.L747
	.byte	1,5,13,9
	.half	.L749-.L748
	.byte	1,5,30,7,9
	.half	.L750-.L749
	.byte	3,2,1,5,17,9
	.half	.L751-.L750
	.byte	3,1,1,5,21,9
	.half	.L25-.L751
	.byte	3,121,1,5,33,9
	.half	.L752-.L25
	.byte	1,5,40,9
	.half	.L753-.L752
	.byte	1,5,9,7,9
	.half	.L28-.L753
	.byte	3,10,1,5,13,7,9
	.half	.L754-.L28
	.byte	3,2,1,5,9,9
	.half	.L29-.L754
	.byte	3,2,1,9
	.half	.L755-.L29
	.byte	3,1,1,5,5,9
	.half	.L30-.L755
	.byte	3,3,1,5,1,9
	.half	.L31-.L30
	.byte	3,1,1,7,9
	.half	.L167-.L31
	.byte	0,1,1
.L737:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_perform_single_ref_calibration')
	.sect	'.debug_ranges'
.L166:
	.word	-1,.L91,0,.L167-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_set_measurement_timing_budget')
	.sect	'.debug_info'
.L168:
	.word	535
	.half	3
	.word	.L169
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L171,.L170
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_set_measurement_timing_budget',0,1,224,2,14
	.word	.L193
	.byte	1,1
	.word	.L93,.L251,.L92
	.byte	4
	.byte	'budget_us',0,1,224,2,57
	.word	.L195,.L252
	.byte	5
	.word	.L93,.L251
	.byte	6
	.byte	'return_state',0,1,226,2,11
	.word	.L193,.L253
	.byte	6
	.byte	'data_buffer',0,1,227,2,11
	.word	.L190,.L254
	.byte	6
	.byte	'data',0,1,228,2,12
	.word	.L214,.L255
	.byte	6
	.byte	'enables',0,1,230,2,39
	.word	.L256,.L257
	.byte	6
	.byte	'timeouts',0,1,231,2,39
	.word	.L258,.L259
	.byte	5
	.word	.L33,.L34
	.byte	6
	.byte	'used_budget_us',0,1,241,2,16
	.word	.L195,.L260
	.byte	5
	.word	.L41,.L34
	.byte	6
	.byte	'final_range_timeout_us',0,1,152,3,20
	.word	.L195,.L261
	.byte	6
	.byte	'final_range_timeout_mclks',0,1,153,3,20
	.word	.L214,.L262
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_set_measurement_timing_budget')
	.sect	'.debug_abbrev'
.L169:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_set_measurement_timing_budget')
	.sect	'.debug_line'
.L170:
	.word	.L757-.L756
.L756:
	.half	3
	.word	.L759-.L758
.L758:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L759:
	.byte	5,14,7,0,5,2
	.word	.L93
	.byte	3,223,2,1,5,24,9
	.half	.L313-.L93
	.byte	3,2,1,5,12,9
	.half	.L32-.L313
	.byte	3,9,1,5,9,9
	.half	.L760-.L32
	.byte	1,5,26,7,9
	.half	.L761-.L760
	.byte	3,2,1,5,13,9
	.half	.L762-.L761
	.byte	3,1,1,5,31,9
	.half	.L33-.L762
	.byte	3,3,1,5,41,9
	.half	.L314-.L33
	.byte	3,1,1,5,42,9
	.half	.L312-.L314
	.byte	3,1,1,5,52,9
	.half	.L763-.L312
	.byte	1,5,20,9
	.half	.L764-.L763
	.byte	3,2,1,5,9,9
	.half	.L765-.L764
	.byte	1,5,40,7,9
	.half	.L766-.L765
	.byte	3,2,1,5,57,9
	.half	.L767-.L766
	.byte	1,5,28,9
	.half	.L768-.L767
	.byte	1,5,20,9
	.half	.L35-.L768
	.byte	3,3,1,5,9,9
	.half	.L769-.L35
	.byte	1,5,44,7,9
	.half	.L770-.L769
	.byte	3,2,1,5,61,9
	.half	.L771-.L770
	.byte	1,5,31,9
	.half	.L772-.L771
	.byte	1,5,28,9
	.half	.L773-.L772
	.byte	1,5,81,9
	.half	.L774-.L773
	.byte	1,5,25,9
	.half	.L36-.L774
	.byte	3,2,1,5,14,9
	.half	.L775-.L36
	.byte	1,5,40,7,9
	.half	.L776-.L775
	.byte	3,2,1,5,57,9
	.half	.L777-.L776
	.byte	1,5,28,9
	.half	.L778-.L777
	.byte	1,5,20,9
	.half	.L37-.L778
	.byte	3,3,1,5,9,9
	.half	.L779-.L37
	.byte	1,5,40,7,9
	.half	.L780-.L779
	.byte	3,2,1,5,54,9
	.half	.L781-.L780
	.byte	1,5,28,9
	.half	.L782-.L781
	.byte	1,5,20,9
	.half	.L39-.L782
	.byte	3,3,1,5,9,9
	.half	.L783-.L39
	.byte	1,5,28,7,9
	.half	.L784-.L783
	.byte	3,5,1,5,13,9
	.half	.L315-.L784
	.byte	3,1,1,5,30,7,9
	.half	.L785-.L315
	.byte	3,3,1,5,17,9
	.half	.L786-.L785
	.byte	3,1,1,5,55,9
	.half	.L41-.L786
	.byte	3,6,1,5,37,9
	.half	.L317-.L41
	.byte	3,3,1,5,22,9
	.half	.L316-.L317
	.byte	1,5,13,9
	.half	.L318-.L316
	.byte	3,127,1,5,24,9
	.half	.L319-.L318
	.byte	3,3,1,5,13,9
	.half	.L787-.L319
	.byte	1,5,54,7,9
	.half	.L788-.L787
	.byte	3,2,1,5,43,9
	.half	.L789-.L788
	.byte	1,5,40,9
	.half	.L43-.L789
	.byte	3,3,1,5,30,9
	.half	.L321-.L43
	.byte	3,1,1,5,28,9
	.half	.L790-.L321
	.byte	1,5,37,9
	.half	.L791-.L790
	.byte	3,1,1,5,43,9
	.half	.L792-.L791
	.byte	1,5,28,9
	.half	.L793-.L792
	.byte	1,5,36,9
	.half	.L794-.L793
	.byte	3,1,1,5,28,9
	.half	.L795-.L794
	.byte	1,5,13,9
	.half	.L796-.L795
	.byte	3,1,1,5,5,9
	.half	.L34-.L796
	.byte	3,3,1,5,1,9
	.half	.L44-.L34
	.byte	3,1,1,7,9
	.half	.L172-.L44
	.byte	0,1,1
.L757:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_set_measurement_timing_budget')
	.sect	'.debug_ranges'
.L171:
	.word	-1,.L93,0,.L172-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_get_measurement_timing_budget')
	.sect	'.debug_info'
.L173:
	.word	343
	.half	3
	.word	.L174
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L176,.L175
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_get_measurement_timing_budget',0,1,179,3,15
	.word	.L195
	.byte	1,1
	.word	.L95,.L263,.L94
	.byte	4
	.word	.L95,.L263
	.byte	5
	.byte	'enables',0,1,181,3,39
	.word	.L256,.L264
	.byte	5
	.byte	'timeouts',0,1,182,3,39
	.word	.L258,.L265
	.byte	5
	.byte	'budget_us',0,1,185,3,12
	.word	.L195,.L266
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_get_measurement_timing_budget')
	.sect	'.debug_abbrev'
.L174:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_get_measurement_timing_budget')
	.sect	'.debug_line'
.L175:
	.word	.L798-.L797
.L797:
	.half	3
	.word	.L800-.L799
.L799:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L800:
	.byte	5,15,7,0,5,2
	.word	.L95
	.byte	3,178,3,1,5,22,9
	.half	.L323-.L95
	.byte	3,6,1,5,37,9
	.half	.L324-.L323
	.byte	3,2,1,5,38,9
	.half	.L801-.L324
	.byte	3,1,1,5,48,9
	.half	.L802-.L801
	.byte	1,5,16,9
	.half	.L803-.L802
	.byte	3,2,1,5,5,9
	.half	.L804-.L803
	.byte	1,5,31,7,9
	.half	.L805-.L804
	.byte	3,2,1,5,48,9
	.half	.L806-.L805
	.byte	1,5,19,9
	.half	.L807-.L806
	.byte	1,5,16,9
	.half	.L45-.L807
	.byte	3,3,1,5,5,9
	.half	.L808-.L45
	.byte	1,5,35,7,9
	.half	.L809-.L808
	.byte	3,2,1,5,52,9
	.half	.L810-.L809
	.byte	1,5,22,9
	.half	.L811-.L810
	.byte	1,5,19,9
	.half	.L812-.L811
	.byte	1,5,72,9
	.half	.L813-.L812
	.byte	1,5,21,9
	.half	.L46-.L813
	.byte	3,2,1,5,10,9
	.half	.L814-.L46
	.byte	1,5,31,7,9
	.half	.L815-.L814
	.byte	3,2,1,5,48,9
	.half	.L816-.L815
	.byte	1,5,19,9
	.half	.L817-.L816
	.byte	1,5,16,9
	.half	.L47-.L817
	.byte	3,3,1,5,5,9
	.half	.L818-.L47
	.byte	1,5,31,7,9
	.half	.L819-.L818
	.byte	3,2,1,5,45,9
	.half	.L820-.L819
	.byte	1,5,19,9
	.half	.L821-.L820
	.byte	1,5,16,9
	.half	.L49-.L821
	.byte	3,3,1,5,5,9
	.half	.L822-.L49
	.byte	1,5,31,7,9
	.half	.L823-.L822
	.byte	3,2,1,5,47,9
	.half	.L824-.L823
	.byte	1,5,19,9
	.half	.L825-.L824
	.byte	1,5,5,9
	.half	.L50-.L825
	.byte	3,3,1,5,1,9
	.half	.L51-.L50
	.byte	3,1,1,7,9
	.half	.L177-.L51
	.byte	0,1,1
.L798:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_get_measurement_timing_budget')
	.sect	'.debug_ranges'
.L176:
	.word	-1,.L95,0,.L177-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_set_signal_rate_limit')
	.sect	'.debug_info'
.L178:
	.word	354
	.half	3
	.word	.L179
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L181,.L180
	.byte	2
	.word	.L104
	.byte	3
	.byte	'dl1a_set_signal_rate_limit',0,1,228,3,13,1,1
	.word	.L97,.L267,.L96
	.byte	4
	.byte	'limit_mcps',0,1,228,3,47
	.word	.L268,.L269
	.byte	5
	.word	.L97,.L267
	.byte	5
	.word	.L270,.L267
	.byte	6
	.byte	'data_buffer',0,1,231,3,11
	.word	.L190,.L271
	.byte	6
	.byte	'limit_mcps_16bit',0,1,232,3,12
	.word	.L214,.L272
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_set_signal_rate_limit')
	.sect	'.debug_abbrev'
.L179:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('dl1a_set_signal_rate_limit')
	.sect	'.debug_line'
.L180:
	.word	.L827-.L826
.L826:
	.half	3
	.word	.L829-.L828
.L828:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0,0,0,0,0
.L829:
	.byte	5,13,7,0,5,2
	.word	.L97
	.byte	3,227,3,1,5,5,9
	.half	.L328-.L97
	.byte	3,2,1,5,48,9
	.half	.L270-.L328
	.byte	3,2,1,5,43,9
	.half	.L830-.L270
	.byte	1,5,22,9
	.half	.L831-.L830
	.byte	3,2,1,5,20,9
	.half	.L331-.L831
	.byte	1,5,41,9
	.half	.L332-.L331
	.byte	3,1,1,5,47,9
	.half	.L333-.L332
	.byte	1,5,20,9
	.half	.L832-.L333
	.byte	1,5,40,9
	.half	.L334-.L832
	.byte	3,1,1,5,20,9
	.half	.L335-.L334
	.byte	1,5,5,9
	.half	.L833-.L335
	.byte	3,2,1,5,1,9
	.half	.L834-.L833
	.byte	3,1,1,7,9
	.half	.L182-.L834
	.byte	0,1,1
.L827:
	.sdecl	'.debug_ranges',debug,cluster('dl1a_set_signal_rate_limit')
	.sect	'.debug_ranges'
.L181:
	.word	-1,.L97,0,.L182-.L97,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_finsh_flag')
	.sect	'.debug_info'
.L183:
	.word	231
	.half	3
	.word	.L184
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L104
	.byte	3
	.byte	'dl1a_finsh_flag',0,16,53,7
	.word	.L193
	.byte	1,5,3
	.word	dl1a_finsh_flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_finsh_flag')
	.sect	'.debug_abbrev'
.L184:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_distance_mm')
	.sect	'.debug_info'
.L185:
	.word	232
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L104
	.byte	3
	.byte	'dl1a_distance_mm',0,16,54,8
	.word	.L214
	.byte	1,5,3
	.word	dl1a_distance_mm
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_distance_mm')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('dl1a_iic_struct')
	.sect	'.debug_info'
.L187:
	.word	230
	.half	3
	.word	.L188
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_dl1a.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L104
	.byte	3
	.byte	'dl1a_iic_struct',0,16,57,29
	.word	.L273
	.byte	5,3
	.word	dl1a_iic_struct
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('dl1a_iic_struct')
	.sect	'.debug_abbrev'
.L188:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_decode_timeout')
	.sect	'.debug_loc'
.L80:
	.word	-1,.L81,0,.L222-.L81
	.half	2
	.byte	138,0
	.word	0,0
.L223:
	.word	-1,.L81,0,.L286-.L81
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_encode_timeout')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L83,0,.L224-.L83
	.half	2
	.byte	138,0
	.word	0,0
.L226:
	.word	-1,.L83,.L291-.L83,.L14-.L83
	.half	1
	.byte	84
	.word	0,0
.L227:
	.word	-1,.L83,.L288-.L83,.L289-.L83
	.half	1
	.byte	95
	.word	.L15-.L83,.L292-.L83
	.half	1
	.byte	95
	.word	0,0
.L228:
	.word	-1,.L83,.L290-.L83,.L224-.L83
	.half	1
	.byte	82
	.word	0,0
.L225:
	.word	-1,.L83,0,.L287-.L83
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_get_distance')
	.sect	'.debug_loc'
.L98:
	.word	-1,.L99,0,.L336-.L99
	.half	2
	.byte	138,0
	.word	.L336-.L99,.L189-.L99
	.half	2
	.byte	138,8
	.word	.L189-.L99,.L189-.L99
	.half	2
	.byte	138,0
	.word	0,0
.L191:
	.word	-1,.L99,0,.L189-.L99
	.half	2
	.byte	145,120
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_get_measurement_timing_budget')
	.sect	'.debug_loc'
.L266:
	.word	-1,.L95,.L324-.L95,.L263-.L95
	.half	1
	.byte	88
	.word	.L325-.L95,.L263-.L95
	.half	1
	.byte	82
	.word	0,0
.L94:
	.word	-1,.L95,0,.L323-.L95
	.half	2
	.byte	138,0
	.word	.L323-.L95,.L263-.L95
	.half	2
	.byte	138,32
	.word	.L263-.L95,.L263-.L95
	.half	2
	.byte	138,0
	.word	0,0
.L264:
	.word	-1,.L95,0,.L263-.L95
	.half	2
	.byte	145,96
	.word	0,0
.L265:
	.word	-1,.L95,0,.L263-.L95
	.half	2
	.byte	145,104
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_get_sequence_step_enables')
	.sect	'.debug_loc'
.L84:
	.word	-1,.L85,0,.L293-.L85
	.half	2
	.byte	138,0
	.word	.L293-.L85,.L229-.L85
	.half	2
	.byte	138,8
	.word	.L229-.L85,.L229-.L85
	.half	2
	.byte	138,0
	.word	0,0
.L231:
	.word	-1,.L85,0,.L294-.L85
	.half	1
	.byte	100
	.word	.L295-.L85,.L229-.L85
	.half	1
	.byte	111
	.word	0,0
.L232:
	.word	-1,.L85,0,.L229-.L85
	.half	2
	.byte	145,120
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_get_sequence_step_timeouts')
	.sect	'.debug_loc'
.L88:
	.word	-1,.L89,0,.L299-.L89
	.half	2
	.byte	138,0
	.word	.L299-.L89,.L237-.L89
	.half	2
	.byte	138,8
	.word	.L237-.L89,.L237-.L89
	.half	2
	.byte	138,0
	.word	0,0
.L239:
	.word	-1,.L89,0,.L300-.L89
	.half	1
	.byte	100
	.word	.L301-.L89,.L237-.L89
	.half	1
	.byte	108
	.word	0,0
.L244:
	.word	-1,.L89,.L303-.L89,.L304-.L89
	.half	1
	.byte	84
	.word	.L305-.L89,.L306-.L89
	.half	1
	.byte	84
	.word	0,0
.L243:
	.word	-1,.L89,0,.L237-.L89
	.half	2
	.byte	145,120
	.word	0,0
.L241:
	.word	-1,.L89,0,.L300-.L89
	.half	1
	.byte	101
	.word	.L302-.L89,.L237-.L89
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_get_spad_info')
	.sect	'.debug_loc'
.L74:
	.word	-1,.L75,0,.L274-.L75
	.half	2
	.byte	138,0
	.word	.L274-.L75,.L205-.L75
	.half	2
	.byte	138,8
	.word	.L205-.L75,.L205-.L75
	.half	2
	.byte	138,0
	.word	0,0
.L207:
	.word	-1,.L75,0,.L275-.L75
	.half	1
	.byte	100
	.word	.L277-.L75,.L205-.L75
	.half	1
	.byte	111
	.word	0,0
.L212:
	.word	-1,.L75,0,.L205-.L75
	.half	2
	.byte	145,122
	.word	0,0
.L210:
	.word	-1,.L75,.L279-.L75,.L205-.L75
	.half	1
	.byte	88
	.word	.L280-.L75,.L205-.L75
	.half	1
	.byte	82
	.word	0,0
.L209:
	.word	-1,.L75,0,.L205-.L75
	.half	2
	.byte	145,120
	.word	0,0
.L208:
	.word	-1,.L75,0,.L276-.L75
	.half	1
	.byte	101
	.word	.L278-.L75,.L205-.L75
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_get_vcsel_pulse_period')
	.sect	'.debug_loc'
.L236:
	.word	-1,.L87,0,.L233-.L87
	.half	2
	.byte	145,120
	.word	0,0
.L86:
	.word	-1,.L87,0,.L296-.L87
	.half	2
	.byte	138,0
	.word	.L296-.L87,.L233-.L87
	.half	2
	.byte	138,8
	.word	.L233-.L87,.L233-.L87
	.half	2
	.byte	138,0
	.word	0,0
.L235:
	.word	-1,.L87,0,.L297-.L87
	.half	1
	.byte	84
	.word	.L18-.L87,.L298-.L87
	.half	1
	.byte	84
	.word	.L20-.L87,.L19-.L87
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_init')
	.sect	'.debug_loc'
.L203:
	.word	-1,.L103,0,.L194-.L103
	.half	2
	.byte	145,119
	.word	0,0
.L102:
	.word	-1,.L103,0,.L337-.L103
	.half	2
	.byte	138,0
	.word	.L337-.L103,.L194-.L103
	.half	2
	.byte	138,16
	.word	.L194-.L103,.L194-.L103
	.half	2
	.byte	138,0
	.word	0,0
.L204:
	.word	-1,.L103,.L343-.L103,.L344-.L103
	.half	1
	.byte	83
	.word	.L344-.L103,.L345-.L103
	.half	1
	.byte	95
	.word	0,0
.L196:
	.word	-1,.L103,.L348-.L103,.L345-.L103
	.half	1
	.byte	82
	.word	.L345-.L103,.L58-.L103
	.half	1
	.byte	95
	.word	.L349-.L103,.L350-.L103
	.half	1
	.byte	84
	.word	0,0
.L201:
	.word	-1,.L103,0,.L194-.L103
	.half	2
	.byte	145,113
	.word	0,0
.L199:
	.word	-1,.L103,.L339-.L103,.L340-.L103
	.half	1
	.byte	82
	.word	.L341-.L103,.L342-.L103
	.half	1
	.byte	82
	.word	.L346-.L103,.L347-.L103
	.half	1
	.byte	82
	.word	0,0
.L198:
	.word	-1,.L103,.L338-.L103,.L194-.L103
	.half	1
	.byte	88
	.word	.L351-.L103,.L194-.L103
	.half	1
	.byte	82
	.word	0,0
.L197:
	.word	-1,.L103,0,.L194-.L103
	.half	2
	.byte	145,112
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_int_handler')
	.sect	'.debug_loc'
.L100:
	.word	-1,.L101,0,.L192-.L101
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_perform_single_ref_calibration')
	.sect	'.debug_loc'
.L248:
	.word	-1,.L91,0,.L245-.L91
	.half	2
	.byte	145,120
	.word	0,0
.L90:
	.word	-1,.L91,0,.L307-.L91
	.half	2
	.byte	138,0
	.word	.L307-.L91,.L245-.L91
	.half	2
	.byte	138,8
	.word	.L245-.L91,.L245-.L91
	.half	2
	.byte	138,0
	.word	0,0
.L250:
	.word	-1,.L91,0,.L245-.L91
	.half	2
	.byte	145,122
	.word	0,0
.L247:
	.word	-1,.L91,.L309-.L91,.L245-.L91
	.half	1
	.byte	88
	.word	.L310-.L91,.L245-.L91
	.half	1
	.byte	82
	.word	0,0
.L246:
	.word	-1,.L91,0,.L308-.L91
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_set_measurement_timing_budget')
	.sect	'.debug_loc'
.L252:
	.word	-1,.L93,0,.L312-.L93
	.half	1
	.byte	84
	.word	.L313-.L93,.L251-.L93
	.half	1
	.byte	88
	.word	0,0
.L255:
	.word	-1,.L93,.L321-.L93,.L34-.L93
	.half	1
	.byte	82
	.word	0,0
.L254:
	.word	-1,.L93,0,.L251-.L93
	.half	2
	.byte	145,88
	.word	0,0
.L92:
	.word	-1,.L93,0,.L311-.L93
	.half	2
	.byte	138,0
	.word	.L311-.L93,.L251-.L93
	.half	2
	.byte	138,40
	.word	.L251-.L93,.L251-.L93
	.half	2
	.byte	138,0
	.word	0,0
.L257:
	.word	-1,.L93,0,.L251-.L93
	.half	2
	.byte	145,92
	.word	0,0
.L262:
	.word	-1,.L93,.L319-.L93,.L320-.L93
	.half	1
	.byte	84
	.word	.L43-.L93,.L321-.L93
	.half	1
	.byte	84
	.word	0,0
.L261:
	.word	-1,.L93,.L317-.L93,.L318-.L93
	.half	1
	.byte	84
	.word	0,0
.L253:
	.word	-1,.L93,.L32-.L93,.L251-.L93
	.half	1
	.byte	89
	.word	.L322-.L93,.L251-.L93
	.half	1
	.byte	82
	.word	0,0
.L259:
	.word	-1,.L93,0,.L251-.L93
	.half	2
	.byte	145,100
	.word	0,0
.L260:
	.word	-1,.L93,.L314-.L93,.L315-.L93
	.half	1
	.byte	90
	.word	.L315-.L93,.L316-.L93
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_set_signal_rate_limit')
	.sect	'.debug_loc'
.L271:
	.word	-1,.L97,0,.L327-.L97
	.half	2
	.byte	145,120
	.word	.L52-.L97,.L267-.L97
	.half	2
	.byte	145,120
	.word	0,0
.L96:
	.word	-1,.L97,0,.L326-.L97
	.half	2
	.byte	138,0
	.word	.L326-.L97,.L267-.L97
	.half	2
	.byte	138,8
	.word	.L267-.L97,.L267-.L97
	.half	2
	.byte	138,0
	.word	0,0
.L269:
	.word	-1,.L97,0,.L327-.L97
	.half	1
	.byte	84
	.word	.L328-.L97,.L329-.L97
	.half	1
	.byte	95
	.word	.L329-.L97,.L330-.L97
	.half	1
	.byte	84
	.word	.L52-.L97,.L331-.L97
	.half	1
	.byte	95
	.word	.L52-.L97,.L53-.L97
	.half	1
	.byte	84
	.word	0,0
.L272:
	.word	-1,.L97,.L332-.L97,.L333-.L97
	.half	1
	.byte	82
	.word	.L334-.L97,.L335-.L97
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_timeout_mclks_to_microseconds')
	.sect	'.debug_loc'
.L76:
	.word	-1,.L77,0,.L213-.L77
	.half	2
	.byte	138,0
	.word	0,0
.L217:
	.word	-1,.L77,.L282-.L77,.L283-.L77
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L215:
	.word	-1,.L77,0,.L213-.L77
	.half	1
	.byte	84
	.word	0,0
.L216:
	.word	-1,.L77,0,.L281-.L77
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('dl1a_timeout_microseconds_to_mclks')
	.sect	'.debug_loc'
.L78:
	.word	-1,.L79,0,.L218-.L79
	.half	2
	.byte	138,0
	.word	0,0
.L221:
	.word	-1,.L79,.L285-.L79,.L218-.L79
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L219:
	.word	-1,.L79,0,.L218-.L79
	.half	1
	.byte	84
	.word	0,0
.L220:
	.word	-1,.L79,0,.L284-.L79
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L835:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('dl1a_get_spad_info')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L75,.L205-.L75
	.byte	4
	.word	(.L274-.L75)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L205-.L274)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_timeout_mclks_to_microseconds')
	.sect	'.debug_frame'
	.word	24
	.word	.L835,.L77,.L213-.L77
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('dl1a_timeout_microseconds_to_mclks')
	.sect	'.debug_frame'
	.word	24
	.word	.L835,.L79,.L218-.L79
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('dl1a_decode_timeout')
	.sect	'.debug_frame'
	.word	24
	.word	.L835,.L81,.L222-.L81
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('dl1a_encode_timeout')
	.sect	'.debug_frame'
	.word	24
	.word	.L835,.L83,.L224-.L83
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('dl1a_get_sequence_step_enables')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L85,.L229-.L85
	.byte	4
	.word	(.L293-.L85)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L229-.L293)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_get_vcsel_pulse_period')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L87,.L233-.L87
	.byte	4
	.word	(.L296-.L87)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L233-.L296)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_get_sequence_step_timeouts')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L89,.L237-.L89
	.byte	4
	.word	(.L299-.L89)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L237-.L299)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_perform_single_ref_calibration')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L91,.L245-.L91
	.byte	4
	.word	(.L307-.L91)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L245-.L307)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_set_measurement_timing_budget')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L93,.L251-.L93
	.byte	4
	.word	(.L311-.L93)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L251-.L311)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_get_measurement_timing_budget')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L95,.L263-.L95
	.byte	4
	.word	(.L323-.L95)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L263-.L323)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_set_signal_rate_limit')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L97,.L267-.L97
	.byte	4
	.word	(.L326-.L97)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L267-.L326)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_get_distance')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L99,.L189-.L99
	.byte	4
	.word	(.L336-.L99)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L189-.L336)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('dl1a_int_handler')
	.sect	'.debug_frame'
	.word	12
	.word	.L835,.L101,.L192-.L101
	.sdecl	'.debug_frame',debug,cluster('dl1a_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L835,.L103,.L194-.L103
	.byte	4
	.word	(.L337-.L103)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L194-.L337)/2
	.byte	19,0,8,26,0,0
	; Module end
