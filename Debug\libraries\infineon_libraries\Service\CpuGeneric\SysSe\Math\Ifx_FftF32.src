	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc42296a --dep-file=Ifx_FftF32.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c'

	
$TC16X
	
	.sdecl	'.text.Ifx_FftF32.Ifx_FftF32_generateTwiddleFactor',code,cluster('Ifx_FftF32_generateTwiddleFactor')
	.sect	'.text.Ifx_FftF32.Ifx_FftF32_generateTwiddleFactor'
	.align	2
	
	.global	Ifx_FftF32_generateTwiddleFactor
; Function Ifx_FftF32_generateTwiddleFactor
.L27:
Ifx_FftF32_generateTwiddleFactor:	.type	func
	mov.aa	a12,a4
.L159:
	mov	d8,d4
.L160:
	mov	d9,#0
.L161:
	j	.L2
.L3:
	itof	d15,d9
.L221:
	mov	d0,#4059
	addih	d0,d0,#16585
.L222:
	mul.f	d15,d15,d0
.L223:
	itof	d0,d8
.L224:
	div.f	d4,d15,d0
	call	__f_ftod
	mov	e10,d3,d2
.L225:
	mul	d15,d9,#8
	addsc.a	a15,a12,d15,#0
.L162:
	mov	e4,d11,d10
.L163:
	call	__d_dtof
	mov	d4,d2
	call	cosf
.L226:
	st.w	[a15],d2
.L227:
	mul	d15,d9,#8
	addsc.a	a15,a12,d15,#0
.L164:
	mov	e4,d11,d10
.L165:
	call	__d_dtof
	mov	d4,d2
	call	sinf
.L228:
	insn.t	d15,d2:31,d2:31
.L229:
	st.w	[a15]4,d15
.L230:
	add	d9,#1
.L2:
	mov	d15,#2
.L231:
	div	e0,d8,d15
.L232:
	jlt	d9,d0,.L3
.L233:
	mov.aa	a2,a12
.L166:
	j	.L4
.L4:
	ret
.L66:
	
__Ifx_FftF32_generateTwiddleFactor_function_end:
	.size	Ifx_FftF32_generateTwiddleFactor,__Ifx_FftF32_generateTwiddleFactor_function_end-Ifx_FftF32_generateTwiddleFactor
.L44:
	; End of function
	
	.sdecl	'.text.Ifx_FftF32.Ifx_FftF32_reverseBits',code,cluster('Ifx_FftF32_reverseBits')
	.sect	'.text.Ifx_FftF32.Ifx_FftF32_reverseBits'
	.align	2
	
	.global	Ifx_FftF32_reverseBits
; Function Ifx_FftF32_reverseBits
.L29:
Ifx_FftF32_reverseBits:	.type	func
	sh	d15,d4,#-1
.L272:
	mov	d0,#21845
	addih	d0,d0,#21845
.L273:
	and	d15,d0
.L274:
	mov	d0,#21845
	addih	d0,d0,#21845
.L275:
	and	d4,d0
.L167:
	sh	d4,#1
.L168:
	or	d15,d4
.L276:
	sh	d0,d15,#-2
.L277:
	mov	d1,#13107
	addih	d1,d1,#13107
.L278:
	and	d0,d1
.L279:
	mov	d1,#13107
	addih	d1,d1,#13107
.L280:
	and	d15,d1
.L169:
	sh	d15,#2
.L170:
	or	d0,d15
.L281:
	sh	d15,d0,#-4
.L282:
	mov	d1,#3855
	addih	d1,d1,#3855
.L283:
	and	d15,d1
.L284:
	mov	d1,#3855
	addih	d1,d1,#3855
.L285:
	and	d0,d1
.L171:
	sh	d0,#4
.L172:
	or	d15,d0
.L286:
	sh	d0,d15,#-8
.L287:
	mov	d1,#255
	addih	d1,d1,#255
.L288:
	and	d0,d1
.L289:
	mov	d1,#255
	addih	d1,d1,#255
.L290:
	and	d15,d1
.L173:
	sh	d15,d15,#8
.L174:
	or	d0,d15
.L291:
	sh	d15,d0,#-16
.L292:
	sh	d0,d0,#16
.L175:
	or	d15,d0
.L293:
	rsub	d0,d5,#32
.L294:
	rsub	d0,#0
	sh	d15,d15,d0
.L295:
	extr.u	d2,d15,#0,#16
.L296:
	j	.L5
.L5:
	ret
.L103:
	
__Ifx_FftF32_reverseBits_function_end:
	.size	Ifx_FftF32_reverseBits,__Ifx_FftF32_reverseBits_function_end-Ifx_FftF32_reverseBits
.L59:
	; End of function
	
	.sdecl	'.text.Ifx_FftF32.Ifx_FftF32_radix2DecimationInTime',code,cluster('Ifx_FftF32_radix2DecimationInTime')
	.sect	'.text.Ifx_FftF32.Ifx_FftF32_radix2DecimationInTime'
	.align	2
	
	.global	Ifx_FftF32_radix2DecimationInTime
; Function Ifx_FftF32_radix2DecimationInTime
.L31:
Ifx_FftF32_radix2DecimationInTime:	.type	func
	sub.a	a10,#16
.L176:
	mov	d10,d4
.L177:
	mov	d2,#1
.L301:
	add	d15,d10,#-1
.L302:
	sha	d2,d2,d15
.L178:
	mov	d3,#2
.L179:
	mov	d9,#0
.L180:
	j	.L6
.L7:
	sh	d4,d3,#-1
.L181:
	mov	d5,#0
.L182:
	mov	d6,#0
.L183:
	j	.L8
.L9:
	add	d7,d5,d4
.L184:
	mov	d8,#0
.L185:
	j	.L10
.L11:
	mov	d15,#16384
.L303:
	mul	d15,d8
.L304:
	div.u	e0,d15,d3
.L305:
	mul	d15,d0,#8
.L306:
	movh.a	a15,#@his(Ifx_g_FftF32_twiddleTable)
	lea	a15,[a15]@los(Ifx_g_FftF32_twiddleTable)
.L307:
	addsc.a	a15,a15,d15,#0
	ld.d	e0,[a15]0
.L308:
	j	.L12
.L12:
	st.d	[a10]0,e0
.L309:
	add	d15,d7,d8
.L310:
	mul	d15,d15,#8
	addsc.a	a15,a4,d15,#0
.L311:
	lea	a2,[a10]0
.L129:
	ld.w	d15,[a15]
.L312:
	ld.w	d0,[a2]
.L313:
	mul.f	d0,d15,d0
.L314:
	ld.w	d1,[a15]4
.L315:
	ld.w	d15,[a10]4
.L316:
	msub.f	d0,d0,d1,d15
.L317:
	ld.w	d11,[a15]4
.L318:
	ld.w	d12,[a2]
.L319:
	ld.w	d13,[a15]
.L320:
	ld.w	d15,[a10]4
.L321:
	mul.f	d15,d13,d15
.L322:
	madd.f	d15,d15,d11,d12
.L323:
	mov	d1,d15
.L186:
	j	.L13
.L13:
	st.d	[a10]8,e0
.L130:
	add	d15,d5,d8
.L324:
	mul	d15,d15,#8
	addsc.a	a15,a4,d15,#0
	ld.da	a2/a3,[a15]0
.L325:
	st.da	[a10]0,a2/a3
.L326:
	lea	a15,[a10]0
.L327:
	lea	a2,[a10]8
.L138:
	ld.w	d15,[a15]
.L328:
	ld.w	d0,[a2]
.L187:
	add.f	d0,d15,d0
.L329:
	ld.w	d11,[a10]4
.L330:
	ld.w	d15,[a10]12
.L331:
	add.f	d15,d11,d15
.L332:
	mov	d1,d15
.L188:
	j	.L14
.L14:
	add	d15,d5,d8
.L333:
	mul	d15,d15,#8
	addsc.a	a15,a4,d15,#0
.L145:
	st.d	[a15]0,e0
.L146:
	lea	a15,[a10]0
.L334:
	lea	a2,[a10]8
.L149:
	ld.w	d15,[a15]
.L335:
	ld.w	d0,[a2]
.L189:
	sub.f	d0,d15,d0
.L336:
	ld.w	d11,[a10]4
.L337:
	ld.w	d15,[a10]12
.L338:
	sub.f	d15,d11,d15
.L339:
	mov	d1,d15
.L190:
	j	.L15
.L15:
	add	d15,d7,d8
.L340:
	mul	d15,d15,#8
	addsc.a	a15,a4,d15,#0
.L156:
	st.d	[a15]0,e0
.L157:
	add	d8,#1
.L10:
	jlt.u	d8,d4,.L11
.L341:
	add	d5,d3
.L342:
	add	d6,#1
.L8:
	jlt.u	d6,d2,.L9
.L343:
	sh	d2,#-1
.L344:
	sh	d3,#1
.L345:
	add	d9,#1
.L6:
	jlt.u	d9,d10,.L7
.L346:
	ret
.L108:
	
__Ifx_FftF32_radix2DecimationInTime_function_end:
	.size	Ifx_FftF32_radix2DecimationInTime,__Ifx_FftF32_radix2DecimationInTime_function_end-Ifx_FftF32_radix2DecimationInTime
.L64:
	; End of function
	
	.sdecl	'.text.Ifx_FftF32.Ifx_FftF32_radix2',code,cluster('Ifx_FftF32_radix2')
	.sect	'.text.Ifx_FftF32.Ifx_FftF32_radix2'
	.align	2
	
	.global	Ifx_FftF32_radix2
; Function Ifx_FftF32_radix2
.L33:
Ifx_FftF32_radix2:	.type	func
	mov.aa	a12,a4
.L193:
	clz	d15,d4
.L238:
	rsub	d1,d15,#31
.L194:
	mov	d0,#0
.L195:
	j	.L16
.L17:
	rsub	d2,d1,#14
.L197:
	mul	d15,d0,#2
.L239:
	movh.a	a15,#@his(Ifx_g_FftF32_bitReverseTable)
	lea	a15,[a15]@los(Ifx_g_FftF32_bitReverseTable)
.L240:
	addsc.a	a15,a15,d15,#0
	ld.hu	d15,[a15]0
.L198:
	rsub	d2,#0
	sha	d15,d15,d2
.L199:
	j	.L18
.L18:
	mul	d15,d15,#8
	addsc.a	a15,a12,d15,#0
.L241:
	mul	d15,d0,#8
	addsc.a	a2,a5,d15,#0
	ld.da	a2/a3,[a2]0
.L242:
	st.da	[a15]0,a2/a3
.L243:
	add	d0,#1
.L196:
	extr.u	d0,d0,#0,#16
.L16:
	jlt.u	d0,d4,.L17
.L244:
	mov.aa	a4,a12
	mov	d4,d1
.L192:
	call	Ifx_FftF32_radix2DecimationInTime
.L191:
	mov.aa	a2,a12
.L200:
	j	.L19
.L19:
	ret
.L74:
	
__Ifx_FftF32_radix2_function_end:
	.size	Ifx_FftF32_radix2,__Ifx_FftF32_radix2_function_end-Ifx_FftF32_radix2
.L49:
	; End of function
	
	.sdecl	'.text.Ifx_FftF32.Ifx_FftF32_radix2I',code,cluster('Ifx_FftF32_radix2I')
	.sect	'.text.Ifx_FftF32.Ifx_FftF32_radix2I'
	.align	2
	
	.global	Ifx_FftF32_radix2I
; Function Ifx_FftF32_radix2I
.L35:
Ifx_FftF32_radix2I:	.type	func
	mov.aa	a12,a4
.L203:
	mov	d8,d4
.L204:
	clz	d15,d8
.L249:
	rsub	d4,d15,#31
.L202:
	mov	d0,#0
.L205:
	j	.L20
.L21:
	rsub	d2,d4,#14
.L207:
	mul	d15,d0,#2
.L250:
	movh.a	a15,#@his(Ifx_g_FftF32_bitReverseTable)
	lea	a15,[a15]@los(Ifx_g_FftF32_bitReverseTable)
.L251:
	addsc.a	a15,a15,d15,#0
	ld.hu	d1,[a15]0
.L208:
	rsub	d2,#0
	sha	d1,d1,d2
.L209:
	j	.L22
.L22:
	mul	d15,d1,#8
	addsc.a	a15,a12,d15,#0
.L252:
	mul	d15,d0,#8
	addsc.a	a2,a5,d15,#0
.L253:
	ld.w	d15,[a2]
.L254:
	st.w	[a15],d15
.L255:
	mul	d15,d1,#8
	addsc.a	a15,a12,d15,#0
.L256:
	mul	d15,d0,#8
	addsc.a	a2,a5,d15,#0
.L257:
	ld.w	d15,[a2]4
.L258:
	insn.t	d15,d15:31,d15:31
.L259:
	st.w	[a15]4,d15
.L260:
	add	d0,#1
.L206:
	extr.u	d0,d0,#0,#16
.L20:
	jlt.u	d0,d8,.L21
.L261:
	mov.aa	a4,a12
	call	Ifx_FftF32_radix2DecimationInTime
.L201:
	mov	d0,#0
.L210:
	j	.L23
.L24:
	mul	d15,d0,#8
	addsc.a	a15,a12,d15,#0
.L262:
	mul	d15,d0,#8
	addsc.a	a2,a12,d15,#0
.L263:
	ld.w	d15,[a2]4
.L264:
	insn.t	d15,d15:31,d15:31
.L265:
	st.w	[a15]4,d15
.L266:
	add	d0,#1
.L211:
	extr.u	d0,d0,#0,#16
.L23:
	jlt.u	d0,d8,.L24
.L267:
	mov.aa	a2,a12
.L212:
	j	.L25
.L25:
	ret
.L92:
	
__Ifx_FftF32_radix2I_function_end:
	.size	Ifx_FftF32_radix2I,__Ifx_FftF32_radix2I_function_end-Ifx_FftF32_radix2I
.L54:
	; End of function
	
	.calls	'Ifx_FftF32_generateTwiddleFactor','__f_ftod'
	.calls	'Ifx_FftF32_generateTwiddleFactor','__d_dtof'
	.calls	'Ifx_FftF32_generateTwiddleFactor','cosf'
	.calls	'Ifx_FftF32_generateTwiddleFactor','sinf'
	.calls	'Ifx_FftF32_radix2','Ifx_FftF32_radix2DecimationInTime'
	.calls	'Ifx_FftF32_radix2I','Ifx_FftF32_radix2DecimationInTime'
	.calls	'Ifx_FftF32_generateTwiddleFactor','',0
	.calls	'Ifx_FftF32_reverseBits','',0
	.calls	'Ifx_FftF32_radix2DecimationInTime','',16
	.calls	'Ifx_FftF32_radix2','',0
	.extern	sinf
	.extern	cosf
	.extern	Ifx_g_FftF32_bitReverseTable
	.extern	Ifx_g_FftF32_twiddleTable
	.extern	__f_ftod
	.extern	__d_dtof
	.calls	'Ifx_FftF32_radix2I','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L37:
	.word	1685
	.half	3
	.word	.L38
	.byte	4
.L36:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L39
	.byte	2,1,1,3
	.word	237
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	240
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	285
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	297
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0
.L119:
	.byte	10,4,61,9,8,11
	.byte	'real',0
	.word	297
	.byte	4,2,35,0,11
	.byte	'imag',0
	.word	297
	.byte	4,2,35,4,0,12
	.word	469
.L76:
	.byte	3
	.word	503
.L128:
	.byte	8
	.byte	'IFX_Cf32_mul',0,3,3,67,21
	.word	469
	.byte	1,1
.L131:
	.byte	5
	.byte	'a',0,3,67,50
	.word	508
.L133:
	.byte	5
	.byte	'b',0,3,67,69
	.word	508
.L135:
	.byte	6,0,8
	.byte	'IFX_Cf32_dot',0,3,3,85,20
	.word	297
	.byte	1,1,5
	.byte	'b',0,3,85,49
	.word	508
	.byte	6,0,8
	.byte	'IFX_Cf32_mag',0,3,3,91,20
	.word	297
	.byte	1,1,5
	.byte	'c',0,3,91,49
	.word	508
	.byte	13,6,0,0
.L137:
	.byte	8
	.byte	'IFX_Cf32_add',0,3,3,107,21
	.word	469
	.byte	1,1
.L139:
	.byte	5
	.byte	'a',0,3,107,50
	.word	508
.L141:
	.byte	5
	.byte	'b',0,3,107,69
	.word	508
.L143:
	.byte	6,0
.L148:
	.byte	8
	.byte	'IFX_Cf32_sub',0,3,3,116,21
	.word	469
	.byte	1,1
.L150:
	.byte	5
	.byte	'a',0,3,116,50
	.word	508
.L152:
	.byte	5
	.byte	'b',0,3,116,69
	.word	508
.L154:
	.byte	6,0
.L65:
	.byte	3
	.word	469
	.byte	4
	.byte	'IFX_Cf32_set',0,3,3,125,17,1,1,5
	.byte	'a',0,3,125,40
	.word	725
	.byte	5
	.byte	're',0,3,125,51
	.word	297
	.byte	5
	.byte	'im',0,3,125,63
	.word	297
	.byte	6,0
.L78:
	.byte	7
	.byte	'unsigned short int',0,2,7
.L80:
	.byte	7
	.byte	'unsigned int',0,4,7
.L84:
	.byte	8
	.byte	'Ifx_FftF32_lookUpReversedBits',0,3,5,93,19
	.word	784
	.byte	1,1
.L85:
	.byte	5
	.byte	'n',0,5,93,56
	.word	784
.L87:
	.byte	5
	.byte	'bits',0,5,93,68
	.word	806
.L89:
	.byte	6,0,7
	.byte	'long int',0,4,5
.L122:
	.byte	8
	.byte	'Ifx_FftF32_lookUpTwiddleFactor',0,3,5,102,21
	.word	469
	.byte	1,1
.L123:
	.byte	5
	.byte	'N',0,5,102,59
	.word	888
.L125:
	.byte	5
	.byte	'k',0,5,102,69
	.word	888
.L127:
	.byte	6,0
.L70:
	.byte	7
	.byte	'int',0,4,5,14
	.byte	'__clz',0
	.word	964
	.byte	1,1,1,1,15
	.word	964
	.byte	0,16
	.word	245
	.byte	17
	.word	271
	.byte	6,0,16
	.word	306
	.byte	17
	.word	338
	.byte	6,0,16
	.word	388
	.byte	17
	.word	407
	.byte	6,0,16
	.word	423
	.byte	17
	.word	438
	.byte	17
	.word	452
	.byte	6,0,18
	.byte	'sinf',0,6,89,25
	.word	297
	.byte	1,1,1,1,19,6,89,43
	.word	297
	.byte	0,18
	.byte	'cosf',0,6,92,25
	.word	297
	.byte	1,1,1,1,19,6,92,43
	.word	297
	.byte	0,16
	.word	513
	.byte	17
	.word	537
	.byte	17
	.word	547
	.byte	6,0,16
	.word	559
	.byte	17
	.word	583
	.byte	6,0,16
	.word	595
	.byte	17
	.word	619
	.byte	13,20
	.word	559
	.byte	17
	.word	583
	.byte	21
	.word	593
	.byte	0,6,0,0,16
	.word	633
	.byte	17
	.word	657
	.byte	17
	.word	667
	.byte	6,0,16
	.word	679
	.byte	17
	.word	703
	.byte	17
	.word	713
	.byte	6,0,16
	.word	730
	.byte	17
	.word	750
	.byte	17
	.word	760
	.byte	17
	.word	771
	.byte	6,0
.L68:
	.byte	7
	.byte	'short int',0,2,5
.L72:
	.byte	7
	.byte	'double',0,8,4,16
	.word	822
	.byte	17
	.word	863
	.byte	17
	.word	873
	.byte	6,0,16
	.word	900
	.byte	17
	.word	942
	.byte	17
	.word	952
	.byte	6,0
.L106:
	.byte	7
	.byte	'unsigned long int',0,4,7,22
	.byte	'__wchar_t',0,7,1,1
	.word	1212
	.byte	22
	.byte	'__size_t',0,7,1,1
	.word	806
	.byte	22
	.byte	'__ptrdiff_t',0,7,1,1
	.word	964
	.byte	23,1,3
	.word	1345
	.byte	22
	.byte	'__codeptr',0,7,1,1
	.word	1347
	.byte	7
	.byte	'unsigned char',0,1,8,22
	.byte	'uint8',0,8,105,29
	.word	1370
	.byte	22
	.byte	'uint16',0,8,109,29
	.word	784
	.byte	22
	.byte	'uint32',0,8,113,29
	.word	1269
	.byte	22
	.byte	'uint64',0,8,118,29
	.word	351
	.byte	22
	.byte	'sint16',0,8,126,29
	.word	1212
	.byte	22
	.byte	'sint32',0,8,131,1,29
	.word	888
	.byte	7
	.byte	'long long int',0,8,5,22
	.byte	'sint64',0,8,138,1,29
	.word	1477
	.byte	22
	.byte	'float32',0,8,167,1,29
	.word	297
	.byte	22
	.byte	'pvoid',0,4,57,28
	.word	383
	.byte	22
	.byte	'cfloat32',0,4,65,3
	.word	469
	.byte	22
	.byte	'Ifx_TickTime',0,4,79,28
	.word	1477
	.byte	24,128,128,2
	.word	784
	.byte	25,255,127,0,12
	.word	1579
	.byte	26
	.byte	'Ifx_g_FftF32_bitReverseTable',0,5,66,31
	.word	1591
	.byte	1,1,24,128,128,4
	.word	469
	.byte	25,255,63,0,12
	.word	1635
	.byte	26
	.byte	'Ifx_g_FftF32_twiddleTable',0,5,69,31
	.word	1647
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L38:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,58,15,59,15,57
	.byte	15,11,15,0,0,11,13,0,3,8,73,19,11,15,56,9,0,0,12,38,0,73,19,0,0,13,11,1,0,0,14,46,1,3,8,73,19,54,15,39
	.byte	12,63,12,60,12,0,0,15,5,0,73,19,0,0,16,46,1,49,19,0,0,17,5,0,49,19,0,0,18,46,1,3,8,58,15,59,15,57,15,73
	.byte	19,54,15,39,12,63,12,60,12,0,0,19,5,0,58,15,59,15,57,15,73,19,0,0,20,29,1,49,19,0,0,21,11,0,49,19,0,0
	.byte	22,22,0,3,8,58,15,59,15,57,15,73,19,0,0,23,21,0,54,15,0,0,24,1,1,11,15,73,19,0,0,25,33,0,47,15,0,0,26
	.byte	52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L39:
	.word	.L214-.L213
.L213:
	.half	3
	.word	.L216-.L215
.L215:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_FftF32.h',0,0,0,0
	.byte	'math.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0,0
.L216:
.L214:
	.sdecl	'.debug_info',debug,cluster('Ifx_FftF32_generateTwiddleFactor')
	.sect	'.debug_info'
.L40:
	.word	372
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L43,.L42
	.byte	2
	.word	.L36
	.byte	3
	.byte	'Ifx_FftF32_generateTwiddleFactor',0,1,51,11
	.word	.L65
	.byte	1,1,1
	.word	.L27,.L66,.L26
	.byte	4
	.byte	'TF',0,1,51,54
	.word	.L65,.L67
	.byte	4
	.byte	'nX',0,1,51,64
	.word	.L68,.L69
	.byte	5
	.word	.L27,.L66
	.byte	6
	.byte	'i',0,1,53,12
	.word	.L70,.L71
	.byte	6
	.byte	'Theta',0,1,54,12
	.word	.L72,.L73
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_FftF32_generateTwiddleFactor')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_FftF32_generateTwiddleFactor')
	.sect	'.debug_line'
.L42:
	.word	.L218-.L217
.L217:
	.half	3
	.word	.L220-.L219
.L219:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0,0,0,0,0
.L220:
	.byte	5,11,7,0,5,2
	.word	.L27
	.byte	3,50,1,5,12,9
	.half	.L160-.L27
	.byte	3,6,1,5,27,9
	.half	.L161-.L160
	.byte	1,5,35,9
	.half	.L3-.L161
	.byte	3,2,1,5,24,9
	.half	.L221-.L3
	.byte	1,5,35,9
	.half	.L222-.L221
	.byte	1,5,39,9
	.half	.L223-.L222
	.byte	1,5,37,9
	.half	.L224-.L223
	.byte	1,5,11,9
	.half	.L225-.L224
	.byte	3,2,1,5,37,9
	.half	.L162-.L225
	.byte	1,5,20,9
	.half	.L226-.L162
	.byte	1,5,11,9
	.half	.L227-.L226
	.byte	3,1,1,5,38,9
	.half	.L164-.L227
	.byte	1,5,32,9
	.half	.L228-.L164
	.byte	1,5,20,9
	.half	.L229-.L228
	.byte	1,5,30,9
	.half	.L230-.L229
	.byte	3,123,1,5,26,9
	.half	.L2-.L230
	.byte	1,5,24,9
	.half	.L231-.L2
	.byte	1,5,27,9
	.half	.L232-.L231
	.byte	1,5,5,7,9
	.half	.L233-.L232
	.byte	3,8,1,5,1,9
	.half	.L4-.L233
	.byte	3,1,1,7,9
	.half	.L44-.L4
	.byte	0,1,1
.L218:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_FftF32_generateTwiddleFactor')
	.sect	'.debug_ranges'
.L43:
	.word	-1,.L27,0,.L44-.L27,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_FftF32_radix2')
	.sect	'.debug_info'
.L45:
	.word	472
	.half	3
	.word	.L46
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L48,.L47
	.byte	2
	.word	.L36
	.byte	3
	.byte	'Ifx_FftF32_radix2',0,1,143,1,11
	.word	.L65
	.byte	1,1,1
	.word	.L33,.L74,.L32
	.byte	4
	.byte	'R',0,1,143,1,39
	.word	.L65,.L75
	.byte	4
	.byte	'X',0,1,143,1,58
	.word	.L76,.L77
	.byte	4
	.byte	'nX',0,1,143,1,76
	.word	.L78,.L79
	.byte	5
	.word	.L33,.L74
	.byte	6
	.byte	'logN',0,1,145,1,20
	.word	.L80,.L81
	.byte	6
	.byte	'n',0,1,146,1,20
	.word	.L78,.L82
	.byte	6
	.byte	'k',0,1,146,1,23
	.word	.L78,.L83
	.byte	7
	.word	.L84,.L17,.L18
	.byte	8
	.word	.L85,.L86
	.byte	8
	.word	.L87,.L88
	.byte	9
	.word	.L89,.L17,.L18
	.byte	6
	.byte	'shift',0,2,95,14
	.word	.L80,.L90
	.byte	6
	.byte	'index',0,2,96,14
	.word	.L78,.L91
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_FftF32_radix2')
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_FftF32_radix2')
	.sect	'.debug_line'
.L47:
	.word	.L235-.L234
.L234:
	.half	3
	.word	.L237-.L236
.L236:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_FftF32.h',0,0,0,0,0
.L237:
	.byte	5,11,7,0,5,2
	.word	.L33
	.byte	3,142,1,1,5,37,9
	.half	.L193-.L33
	.byte	3,2,1,5,30,9
	.half	.L238-.L193
	.byte	1,5,12,9
	.half	.L194-.L238
	.byte	3,4,1,5,23,9
	.half	.L195-.L194
	.byte	1,4,2,5,48,9
	.half	.L17-.L195
	.byte	3,74,1,5,50,9
	.half	.L197-.L17
	.byte	3,1,1,5,22,9
	.half	.L239-.L197
	.byte	1,5,50,9
	.half	.L240-.L239
	.byte	1,5,18,9
	.half	.L198-.L240
	.byte	3,1,1,5,5,9
	.half	.L199-.L198
	.byte	1,4,1,5,10,9
	.half	.L18-.L199
	.byte	3,56,1,5,17,9
	.half	.L241-.L18
	.byte	1,5,14,9
	.half	.L242-.L241
	.byte	1,5,26,9
	.half	.L243-.L242
	.byte	3,124,1,5,23,9
	.half	.L16-.L243
	.byte	1,5,42,7,9
	.half	.L244-.L16
	.byte	3,7,1,5,5,9
	.half	.L191-.L244
	.byte	3,2,1,5,1,9
	.half	.L19-.L191
	.byte	3,1,1,7,9
	.half	.L49-.L19
	.byte	0,1,1
.L235:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_FftF32_radix2')
	.sect	'.debug_ranges'
.L48:
	.word	-1,.L33,0,.L49-.L33,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_FftF32_radix2I')
	.sect	'.debug_info'
.L50:
	.word	473
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L53,.L52
	.byte	2
	.word	.L36
	.byte	3
	.byte	'Ifx_FftF32_radix2I',0,1,162,1,11
	.word	.L65
	.byte	1,1,1
	.word	.L35,.L92,.L34
	.byte	4
	.byte	'R',0,1,162,1,40
	.word	.L65,.L93
	.byte	4
	.byte	'X',0,1,162,1,59
	.word	.L76,.L94
	.byte	4
	.byte	'nX',0,1,162,1,77
	.word	.L78,.L95
	.byte	5
	.word	.L35,.L92
	.byte	6
	.byte	'logN',0,1,164,1,20
	.word	.L80,.L96
	.byte	6
	.byte	'n',0,1,165,1,20
	.word	.L78,.L97
	.byte	6
	.byte	'k',0,1,165,1,23
	.word	.L78,.L98
	.byte	7
	.word	.L84,.L21,.L22
	.byte	8
	.word	.L85,.L99
	.byte	8
	.word	.L87,.L100
	.byte	9
	.word	.L89,.L21,.L22
	.byte	6
	.byte	'shift',0,2,95,14
	.word	.L80,.L101
	.byte	6
	.byte	'index',0,2,96,14
	.word	.L78,.L102
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_FftF32_radix2I')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_FftF32_radix2I')
	.sect	'.debug_line'
.L52:
	.word	.L246-.L245
.L245:
	.half	3
	.word	.L248-.L247
.L247:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_FftF32.h',0,0,0,0,0
.L248:
	.byte	5,11,7,0,5,2
	.word	.L35
	.byte	3,161,1,1,5,37,9
	.half	.L204-.L35
	.byte	3,2,1,5,30,9
	.half	.L249-.L204
	.byte	1,5,12,9
	.half	.L202-.L249
	.byte	3,4,1,5,23,9
	.half	.L205-.L202
	.byte	1,4,2,5,48,9
	.half	.L21-.L205
	.byte	3,183,127,1,5,50,9
	.half	.L207-.L21
	.byte	3,1,1,5,22,9
	.half	.L250-.L207
	.byte	1,5,50,9
	.half	.L251-.L250
	.byte	1,5,18,9
	.half	.L208-.L251
	.byte	3,1,1,5,5,9
	.half	.L209-.L208
	.byte	1,4,1,5,10,9
	.half	.L22-.L209
	.byte	3,203,0,1,5,22,9
	.half	.L252-.L22
	.byte	1,5,25,9
	.half	.L253-.L252
	.byte	1,5,19,9
	.half	.L254-.L253
	.byte	1,5,10,9
	.half	.L255-.L254
	.byte	3,1,1,5,23,9
	.half	.L256-.L255
	.byte	1,5,26,9
	.half	.L257-.L256
	.byte	1,5,21,9
	.half	.L258-.L257
	.byte	1,5,19,9
	.half	.L259-.L258
	.byte	1,5,26,9
	.half	.L260-.L259
	.byte	3,123,1,5,23,9
	.half	.L20-.L260
	.byte	1,5,42,7,9
	.half	.L261-.L20
	.byte	3,8,1,5,12,9
	.half	.L201-.L261
	.byte	3,3,1,5,23,9
	.half	.L210-.L201
	.byte	1,5,10,9
	.half	.L24-.L210
	.byte	3,2,1,5,23,9
	.half	.L262-.L24
	.byte	1,5,26,9
	.half	.L263-.L262
	.byte	1,5,21,9
	.half	.L264-.L263
	.byte	1,5,19,9
	.half	.L265-.L264
	.byte	1,5,26,9
	.half	.L266-.L265
	.byte	3,126,1,5,23,9
	.half	.L23-.L266
	.byte	1,5,5,7,9
	.half	.L267-.L23
	.byte	3,5,1,5,1,9
	.half	.L25-.L267
	.byte	3,1,1,7,9
	.half	.L54-.L25
	.byte	0,1,1
.L246:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_FftF32_radix2I')
	.sect	'.debug_ranges'
.L53:
	.word	-1,.L35,0,.L54-.L35,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_FftF32_reverseBits')
	.sect	'.debug_info'
.L55:
	.word	345
	.half	3
	.word	.L56
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L58,.L57
	.byte	2
	.word	.L36
	.byte	3
	.byte	'Ifx_FftF32_reverseBits',0,1,70,16
	.word	.L78
	.byte	1,1,1
	.word	.L29,.L103,.L28
	.byte	4
	.byte	'n',0,1,70,54
	.word	.L78,.L104
	.byte	4
	.byte	'bits',0,1,70,66
	.word	.L80,.L105
	.byte	5
	.word	.L29,.L103
	.byte	6
	.byte	'v',0,1,75,19
	.word	.L106,.L107
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_FftF32_reverseBits')
	.sect	'.debug_abbrev'
.L56:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_FftF32_reverseBits')
	.sect	'.debug_line'
.L57:
	.word	.L269-.L268
.L268:
	.half	3
	.word	.L271-.L270
.L270:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0,0,0,0,0
.L271:
	.byte	5,13,7,0,5,2
	.word	.L29
	.byte	3,205,0,1,5,21,9
	.half	.L272-.L29
	.byte	1,5,19,9
	.half	.L273-.L272
	.byte	1,5,43,9
	.half	.L274-.L273
	.byte	1,5,41,9
	.half	.L275-.L274
	.byte	1,5,57,9
	.half	.L167-.L275
	.byte	1,5,35,9
	.half	.L168-.L167
	.byte	1,5,13,9
	.half	.L276-.L168
	.byte	3,2,1,5,21,9
	.half	.L277-.L276
	.byte	1,5,19,9
	.half	.L278-.L277
	.byte	1,5,43,9
	.half	.L279-.L278
	.byte	1,5,41,9
	.half	.L280-.L279
	.byte	1,5,57,9
	.half	.L169-.L280
	.byte	1,5,35,9
	.half	.L170-.L169
	.byte	1,5,13,9
	.half	.L281-.L170
	.byte	3,2,1,5,21,9
	.half	.L282-.L281
	.byte	1,5,19,9
	.half	.L283-.L282
	.byte	1,5,43,9
	.half	.L284-.L283
	.byte	1,5,41,9
	.half	.L285-.L284
	.byte	1,5,57,9
	.half	.L171-.L285
	.byte	1,5,35,9
	.half	.L172-.L171
	.byte	1,5,13,9
	.half	.L286-.L172
	.byte	3,2,1,5,21,9
	.half	.L287-.L286
	.byte	1,5,19,9
	.half	.L288-.L287
	.byte	1,5,43,9
	.half	.L289-.L288
	.byte	1,5,41,9
	.half	.L290-.L289
	.byte	1,5,57,9
	.half	.L173-.L290
	.byte	1,5,35,9
	.half	.L174-.L173
	.byte	1,5,12,9
	.half	.L291-.L174
	.byte	3,2,1,5,24,9
	.half	.L292-.L291
	.byte	1,5,19,9
	.half	.L175-.L292
	.byte	1,5,18,9
	.half	.L293-.L175
	.byte	3,2,1,5,11,9
	.half	.L294-.L293
	.byte	1,5,12,9
	.half	.L295-.L294
	.byte	3,1,1,5,5,9
	.half	.L296-.L295
	.byte	1,5,1,9
	.half	.L5-.L296
	.byte	3,1,1,7,9
	.half	.L59-.L5
	.byte	0,1,1
.L269:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_FftF32_reverseBits')
	.sect	'.debug_ranges'
.L58:
	.word	-1,.L29,0,.L59-.L29,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_FftF32_radix2DecimationInTime')
	.sect	'.debug_info'
.L60:
	.word	772
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L63,.L62
	.byte	2
	.word	.L36
	.byte	3
	.byte	'Ifx_FftF32_radix2DecimationInTime',0,1,94,6,1,1,1
	.word	.L31,.L108,.L30
	.byte	4
	.byte	'R',0,1,94,50
	.word	.L65,.L109
	.byte	4
	.byte	'p',0,1,94,67
	.word	.L106,.L110
	.byte	5
	.word	.L31,.L108
	.byte	6
	.byte	'Bp',0,1,104,19
	.word	.L106,.L111
	.byte	6
	.byte	'Np',0,1,104,23
	.word	.L106,.L112
	.byte	6
	.byte	'Npx',0,1,104,27
	.word	.L106,.L113
	.byte	6
	.byte	'P',0,1,104,32
	.word	.L106,.L114
	.byte	6
	.byte	'b',0,1,104,35
	.word	.L106,.L115
	.byte	6
	.byte	'k',0,1,104,38
	.word	.L106,.L116
	.byte	6
	.byte	'BaseT',0,1,104,41
	.word	.L106,.L117
	.byte	6
	.byte	'BaseB',0,1,104,48
	.word	.L106,.L118
	.byte	6
	.byte	'top',0,1,105,19
	.word	.L119,.L120
	.byte	6
	.byte	'bot',0,1,105,24
	.word	.L119,.L121
	.byte	7
	.word	.L122,.L11,.L12
	.byte	8
	.word	.L123,.L124
	.byte	8
	.word	.L125,.L126
	.byte	9
	.word	.L127,.L11,.L12
	.byte	0,7
	.word	.L128,.L129,.L130
	.byte	8
	.word	.L131,.L132
	.byte	8
	.word	.L133,.L134
	.byte	10
	.word	.L135,.L129,.L130
	.byte	6
	.byte	'R',0,2,69,14
	.word	.L119,.L136
	.byte	0,0,7
	.word	.L137,.L138,.L14
	.byte	8
	.word	.L139,.L140
	.byte	8
	.word	.L141,.L142
	.byte	11
	.word	.L143,.L144
	.byte	6
	.byte	'R',0,2,109,14
	.word	.L119,.L147
	.byte	0,0,7
	.word	.L137,.L145,.L146
	.byte	8
	.word	.L139,.L140
	.byte	8
	.word	.L141,.L142
	.byte	0,7
	.word	.L148,.L149,.L15
	.byte	8
	.word	.L150,.L151
	.byte	8
	.word	.L152,.L153
	.byte	11
	.word	.L154,.L155
	.byte	6
	.byte	'R',0,2,118,14
	.word	.L119,.L158
	.byte	0,0,7
	.word	.L148,.L156,.L157
	.byte	8
	.word	.L150,.L151
	.byte	8
	.word	.L152,.L153
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_FftF32_radix2DecimationInTime')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,10,11,1,49,16,17,1,18,1,0,0,11,11,1,49,16,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_FftF32_radix2DecimationInTime')
	.sect	'.debug_line'
.L62:
	.word	.L298-.L297
.L297:
	.half	3
	.word	.L300-.L299
.L299:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_FftF32.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_Cf32.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_FftF32.h',0,0,0,0,0
.L300:
	.byte	5,6,7,0,5,2
	.word	.L31
	.byte	3,221,0,1,5,10,9
	.half	.L177-.L31
	.byte	3,13,1,5,18,9
	.half	.L301-.L177
	.byte	1,5,12,9
	.half	.L302-.L301
	.byte	1,5,8,9
	.half	.L178-.L302
	.byte	3,1,1,5,12,9
	.half	.L179-.L178
	.byte	3,3,1,5,22,9
	.half	.L180-.L179
	.byte	1,5,20,9
	.half	.L7-.L180
	.byte	3,3,1,5,15,9
	.half	.L181-.L7
	.byte	3,1,1,5,16,9
	.half	.L182-.L181
	.byte	3,2,1,5,27,9
	.half	.L183-.L182
	.byte	1,9
	.half	.L9-.L183
	.byte	3,3,1,5,20,9
	.half	.L184-.L9
	.byte	3,2,1,5,32,9
	.half	.L185-.L184
	.byte	1,4,3,5,42,9
	.half	.L11-.L185
	.byte	3,110,1,5,40,9
	.half	.L303-.L11
	.byte	1,5,64,9
	.half	.L304-.L303
	.byte	1,5,37,9
	.half	.L305-.L304
	.byte	1,5,12,9
	.half	.L306-.L305
	.byte	1,5,37,9
	.half	.L307-.L306
	.byte	1,5,5,9
	.half	.L308-.L307
	.byte	1,4,1,5,30,9
	.half	.L12-.L308
	.byte	3,22,1,5,54,9
	.half	.L309-.L12
	.byte	3,1,1,5,47,9
	.half	.L310-.L309
	.byte	1,5,61,9
	.half	.L311-.L310
	.byte	1,4,2,5,16,9
	.half	.L129-.L311
	.byte	3,71,1,5,26,9
	.half	.L312-.L129
	.byte	1,5,23,9
	.half	.L313-.L312
	.byte	1,5,38,9
	.half	.L314-.L313
	.byte	1,5,48,9
	.half	.L315-.L314
	.byte	1,5,34,9
	.half	.L316-.L315
	.byte	1,5,16,9
	.half	.L317-.L316
	.byte	3,1,1,5,26,9
	.half	.L318-.L317
	.byte	1,5,38,9
	.half	.L319-.L318
	.byte	1,5,48,9
	.half	.L320-.L319
	.byte	1,5,45,9
	.half	.L321-.L320
	.byte	1,5,34,9
	.half	.L322-.L321
	.byte	1,5,12,9
	.half	.L323-.L322
	.byte	1,5,5,9
	.half	.L186-.L323
	.byte	3,1,1,4,1,5,30,9
	.half	.L13-.L186
	.byte	3,55,1,5,40,9
	.half	.L130-.L13
	.byte	3,1,1,5,33,9
	.half	.L324-.L130
	.byte	1,5,30,9
	.half	.L325-.L324
	.byte	1,5,46,9
	.half	.L326-.L325
	.byte	3,1,1,5,52,9
	.half	.L327-.L326
	.byte	1,4,2,5,16,9
	.half	.L138-.L327
	.byte	3,109,1,5,26,9
	.half	.L328-.L138
	.byte	1,5,23,9
	.half	.L187-.L328
	.byte	1,5,16,9
	.half	.L329-.L187
	.byte	3,1,1,5,26,9
	.half	.L330-.L329
	.byte	1,5,23,9
	.half	.L331-.L330
	.byte	1,5,12,9
	.half	.L332-.L331
	.byte	1,5,5,9
	.half	.L188-.L332
	.byte	3,1,1,4,1,5,25,9
	.half	.L14-.L188
	.byte	3,17,1,5,18,9
	.half	.L333-.L14
	.byte	1,5,30,9
	.half	.L145-.L333
	.byte	1,5,46,9
	.half	.L146-.L145
	.byte	3,1,1,5,52,9
	.half	.L334-.L146
	.byte	1,4,2,5,16,9
	.half	.L149-.L334
	.byte	3,117,1,5,26,9
	.half	.L335-.L149
	.byte	1,5,23,9
	.half	.L189-.L335
	.byte	1,5,16,9
	.half	.L336-.L189
	.byte	3,1,1,5,26,9
	.half	.L337-.L336
	.byte	1,5,23,9
	.half	.L338-.L337
	.byte	1,5,12,9
	.half	.L339-.L338
	.byte	1,5,5,9
	.half	.L190-.L339
	.byte	3,1,1,4,1,5,25,9
	.half	.L15-.L190
	.byte	3,9,1,5,18,9
	.half	.L340-.L15
	.byte	1,5,30,9
	.half	.L156-.L340
	.byte	1,5,35,9
	.half	.L157-.L156
	.byte	3,120,1,5,32,9
	.half	.L10-.L157
	.byte	1,5,27,7,9
	.half	.L341-.L10
	.byte	3,11,1,5,30,9
	.half	.L342-.L341
	.byte	3,112,1,5,27,9
	.half	.L8-.L342
	.byte	1,5,17,7,9
	.half	.L343-.L8
	.byte	3,20,1,9
	.half	.L344-.L343
	.byte	3,1,1,5,25,9
	.half	.L345-.L344
	.byte	3,101,1,5,22,9
	.half	.L6-.L345
	.byte	1,5,1,7,9
	.half	.L346-.L6
	.byte	3,29,1,7,9
	.half	.L64-.L346
	.byte	0,1,1
.L298:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_FftF32_radix2DecimationInTime')
	.sect	'.debug_ranges'
.L63:
	.word	-1,.L31,0,.L64-.L31,0,0
.L144:
	.word	-1,.L31,.L138-.L31,.L14-.L31,.L145-.L31,.L146-.L31,0,0
.L155:
	.word	-1,.L31,.L149-.L31,.L15-.L31,.L156-.L31,.L157-.L31,0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_FftF32_generateTwiddleFactor')
	.sect	'.debug_loc'
.L26:
	.word	-1,.L27,0,.L66-.L27
	.half	2
	.byte	138,0
	.word	0,0
.L67:
	.word	-1,.L27,0,.L3-.L27
	.half	1
	.byte	100
	.word	.L159-.L27,.L66-.L27
	.half	1
	.byte	108
	.word	.L166-.L27,.L66-.L27
	.half	1
	.byte	98
	.word	0,0
.L73:
	.word	-1,.L27,.L162-.L27,.L163-.L27
	.half	2
	.byte	144,37
	.word	.L164-.L27,.L165-.L27
	.half	2
	.byte	144,37
	.word	0,0
.L71:
	.word	-1,.L27,.L161-.L27,.L66-.L27
	.half	1
	.byte	89
	.word	0,0
.L69:
	.word	-1,.L27,0,.L3-.L27
	.half	1
	.byte	84
	.word	.L160-.L27,.L66-.L27
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_FftF32_radix2')
	.sect	'.debug_loc'
.L32:
	.word	-1,.L33,0,.L74-.L33
	.half	2
	.byte	138,0
	.word	0,0
.L75:
	.word	-1,.L33,0,.L191-.L33
	.half	1
	.byte	100
	.word	.L193-.L33,.L74-.L33
	.half	1
	.byte	108
	.word	.L200-.L33,.L74-.L33
	.half	1
	.byte	98
	.word	0,0
.L77:
	.word	-1,.L33,0,.L191-.L33
	.half	1
	.byte	101
	.word	0,0
.L88:
	.word	0,0
.L91:
	.word	-1,.L33,.L198-.L33,.L199-.L33
	.half	1
	.byte	95
	.word	0,0
.L83:
	.word	0,0
.L81:
	.word	-1,.L33,.L194-.L33,.L191-.L33
	.half	1
	.byte	81
	.word	.L192-.L33,.L191-.L33
	.half	1
	.byte	84
	.word	0,0
.L86:
	.word	0,0
.L82:
	.word	-1,.L33,.L195-.L33,.L196-.L33
	.half	5
	.byte	144,32,157,32,0
	.word	.L16-.L33,.L191-.L33
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L79:
	.word	-1,.L33,0,.L192-.L33
	.half	1
	.byte	84
	.word	0,0
.L90:
	.word	-1,.L33,.L197-.L33,.L16-.L33
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_FftF32_radix2DecimationInTime')
	.sect	'.debug_loc'
.L118:
	.word	-1,.L31,.L184-.L31,.L8-.L31
	.half	1
	.byte	87
	.word	0,0
.L117:
	.word	-1,.L31,.L182-.L31,.L6-.L31
	.half	1
	.byte	85
	.word	0,0
.L111:
	.word	-1,.L31,.L178-.L31,.L108-.L31
	.half	1
	.byte	82
	.word	0,0
.L30:
	.word	-1,.L31,0,.L176-.L31
	.half	2
	.byte	138,0
	.word	.L176-.L31,.L108-.L31
	.half	2
	.byte	138,16
	.word	.L108-.L31,.L108-.L31
	.half	2
	.byte	138,0
	.word	0,0
.L124:
	.word	0,0
.L112:
	.word	-1,.L31,.L179-.L31,.L108-.L31
	.half	1
	.byte	83
	.word	0,0
.L113:
	.word	-1,.L31,.L181-.L31,.L6-.L31
	.half	1
	.byte	84
	.word	0,0
.L114:
	.word	-1,.L31,.L180-.L31,.L108-.L31
	.half	1
	.byte	89
	.word	0,0
.L136:
	.word	-1,.L31,.L186-.L31,.L187-.L31
	.half	2
	.byte	144,32
	.word	0,0
.L109:
	.word	-1,.L31,0,.L108-.L31
	.half	1
	.byte	100
	.word	0,0
.L147:
	.word	-1,.L31,.L188-.L31,.L189-.L31
	.half	2
	.byte	144,32
	.word	0,0
.L158:
	.word	-1,.L31,.L190-.L31,.L10-.L31
	.half	2
	.byte	144,32
	.word	0,0
.L132:
	.word	0,0
.L140:
	.word	0,0
.L151:
	.word	0,0
.L134:
	.word	0,0
.L115:
	.word	-1,.L31,.L183-.L31,.L6-.L31
	.half	1
	.byte	86
	.word	0,0
.L142:
	.word	0,0
.L153:
	.word	0,0
.L121:
	.word	-1,.L31,0,.L108-.L31
	.half	2
	.byte	145,120
	.word	0,0
.L126:
	.word	0,0
.L116:
	.word	-1,.L31,.L185-.L31,.L8-.L31
	.half	1
	.byte	88
	.word	0,0
.L110:
	.word	-1,.L31,0,.L7-.L31
	.half	1
	.byte	84
	.word	.L177-.L31,.L108-.L31
	.half	1
	.byte	90
	.word	0,0
.L120:
	.word	-1,.L31,0,.L108-.L31
	.half	2
	.byte	145,112
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_FftF32_radix2I')
	.sect	'.debug_loc'
.L34:
	.word	-1,.L35,0,.L92-.L35
	.half	2
	.byte	138,0
	.word	0,0
.L93:
	.word	-1,.L35,0,.L201-.L35
	.half	1
	.byte	100
	.word	.L203-.L35,.L92-.L35
	.half	1
	.byte	108
	.word	.L212-.L35,.L92-.L35
	.half	1
	.byte	98
	.word	0,0
.L94:
	.word	-1,.L35,0,.L201-.L35
	.half	1
	.byte	101
	.word	0,0
.L100:
	.word	0,0
.L102:
	.word	-1,.L35,.L208-.L35,.L209-.L35
	.half	1
	.byte	81
	.word	0,0
.L98:
	.word	0,0
.L96:
	.word	-1,.L35,.L202-.L35,.L201-.L35
	.half	1
	.byte	84
	.word	0,0
.L99:
	.word	0,0
.L97:
	.word	-1,.L35,.L205-.L35,.L206-.L35
	.half	5
	.byte	144,32,157,32,0
	.word	.L20-.L35,.L201-.L35
	.half	5
	.byte	144,32,157,32,0
	.word	.L210-.L35,.L211-.L35
	.half	5
	.byte	144,32,157,32,0
	.word	.L23-.L35,.L92-.L35
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L95:
	.word	-1,.L35,0,.L202-.L35
	.half	1
	.byte	84
	.word	.L204-.L35,.L92-.L35
	.half	1
	.byte	88
	.word	0,0
.L101:
	.word	-1,.L35,.L207-.L35,.L20-.L35
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_FftF32_reverseBits')
	.sect	'.debug_loc'
.L28:
	.word	-1,.L29,0,.L103-.L29
	.half	2
	.byte	138,0
	.word	0,0
.L105:
	.word	-1,.L29,0,.L103-.L29
	.half	1
	.byte	85
	.word	0,0
.L104:
	.word	-1,.L29,0,.L167-.L29
	.half	1
	.byte	84
	.word	0,0
.L107:
	.word	-1,.L29,.L168-.L29,.L169-.L29
	.half	1
	.byte	95
	.word	.L170-.L29,.L171-.L29
	.half	5
	.byte	144,32,157,32,0
	.word	.L172-.L29,.L173-.L29
	.half	1
	.byte	95
	.word	.L174-.L29,.L175-.L29
	.half	5
	.byte	144,32,157,32,0
	.word	.L175-.L29,.L103-.L29
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L347:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Ifx_FftF32_generateTwiddleFactor')
	.sect	'.debug_frame'
	.word	12
	.word	.L347,.L27,.L66-.L27
	.sdecl	'.debug_frame',debug,cluster('Ifx_FftF32_reverseBits')
	.sect	'.debug_frame'
	.word	24
	.word	.L347,.L29,.L103-.L29
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Ifx_FftF32_radix2DecimationInTime')
	.sect	'.debug_frame'
	.word	40
	.word	.L347,.L31,.L108-.L31
	.byte	8,21,8,22,8,23,4
	.word	(.L176-.L31)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L108-.L176)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('Ifx_FftF32_radix2')
	.sect	'.debug_frame'
	.word	12
	.word	.L347,.L33,.L74-.L33
	.sdecl	'.debug_frame',debug,cluster('Ifx_FftF32_radix2I')
	.sect	'.debug_frame'
	.word	12
	.word	.L347,.L35,.L92-.L35
	; Module end
