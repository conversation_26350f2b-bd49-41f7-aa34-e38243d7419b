gsm_4g_error.o :	../code/user1/gsm_4g_error.c
../code/user1/gsm_4g_error.c :
gsm_4g_error.o :	..\code\user1\gsm_4g_error.h
..\code\user1\gsm_4g_error.h :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdint.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\typeinfo.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdbool.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_driver\zf_driver_timer.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_typedef.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\math.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdio.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdarg.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\string.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stdlib.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\ifx_types.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\Compilers.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Configurations\Ifx_Cfg.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Platform\Tricore\Compilers\CompilerTasking.h" :
gsm_4g_error.o :	"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h"
"F:\ADS\AURIX-Studio-1.10.2\tools\Compilers\Tasking_1.1r8\ctc\include\stddef.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Platform_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Platform_Types.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Ifx_TypesTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\Ifx_TypesTasking.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Cpu\\Std\PLATFORM_TYPES.H" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_clock.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\zf_common\zf_common_clock.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuWdt.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuWdt.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxScu_cfg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\_Impl\IfxScu_cfg.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_bf.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_bf.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxFlash_bf.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_reg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_reg.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_regdef.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\IfxScu_regdef.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\Ifx_TypesReg.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\Infra\\Sfr\\TC26B\\_Reg\Ifx_TypesReg.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuWdt.asm.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\IfxScuWdt.asm.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_Intrinsics.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_Intrinsics.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_IntrinsicsTasking.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\IfxCpu_IntrinsicsTasking.h" :
gsm_4g_error.o :	"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h"
"C:\\Users\\<USER>\\Desktop\\ORRN_Code_01\\ORRN_Code\\Mark_1\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\Cpu\Std\Ifx_Types.h" :
