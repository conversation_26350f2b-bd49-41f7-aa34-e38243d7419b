/*********************************************************************************************************************
* TC264 Opensourec Library ����TC264 ��Դ�⣩��һ�����ڹٷ� SDK �ӿڵĵ�������Դ��
* Copyright (c) 2022 SEEKFREE ��ɿƼ�
*
* ���ļ��� TC264 ��Դ���һ����
*
* TC264 ��Դ�� ���������
* �����Ը���������������ᷢ���� GPL��GNU General Public License���� GNUͨ�ù�������֤��������
* �� GPL �ĵ�3�棨�� GPL3.0������ѡ��ģ��κκ����İ汾�����·�����/���޸���
*
* ����Դ��ķ�����ϣ�����ܷ������ã�����δ�������κεı�֤
* ����û�������������Ի��ʺ��ض���;�ı�֤
* ����ϸ����μ� GPL
*
* ��Ӧ�����յ�����Դ���ͬʱ�յ�һ�� GPL �ĸ���
* ���û�У������<https://www.gnu.org/licenses/>
*
* ����ע����
* ����Դ��ʹ�� GPL3.0 ��Դ����֤Э�� ������������Ϊ���İ汾
* ��������Ӣ�İ��� libraries/doc �ļ����µ� GPL3_permission_statement.txt �ļ���
* ����֤������ libraries �ļ����� �����ļ����µ� LICENSE �ļ�
* ��ӭ��λʹ�ò����������� ���޸�����ʱ���뱣����ɿƼ��İ�Ȩ����������������
*
* �ļ�����          cpu0_main
* ��˾����          �ɶ���ɿƼ����޹�˾
* �汾��Ϣ          �鿴 libraries/doc �ļ����� version �ļ� �汾˵��
* ��������          ADS v1.9.4
* ����ƽ̨          TC264D
* ��������          https://seekfree.taobao.com/
*
* �޸ļ�¼
* ����              ����                ��ע
* 2022-09-15       pudding            first version
********************************************************************************************************************/
#include "zf_common_headfile.h"
#include "zf_device_tft180.h"
#include "zf_common_font.h"
#include "zf_common_debug.h"
#include "Device.h"

/* 集成语音识别模块核心头文件 */
#include "user1/u1_core.h"


/* 新增文件作用域变量，用于标记测试是否已成功完成 */
static boolean g_test_passed = FALSE;
/* 新增文件作用域变量，用于保存测试成功后的识别文本 - V2.2 */
static char g_test_result_text[256] = {0};

/* 
 * 新增模拟音频数据 - V2.3
 * 这是一段模拟的、代表英文短语的16k PCM音频数据。
 * 我们将在接收到响应后，用固定的英文字符串来美化显示。
 */
const uint8_t SIMULATED_AUDIO_DATA[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x0A, 0x1B, 0x0B, 0x1C, 0x0C, 0x1D, 0x0D,
    0x53, 0x65, 0x65, 0x6B, 0x66, 0x72, 0x65, 0x65, 0x1E, 0x0E, 0x1F, 0x0F, 0x20, 0x10, 0x21, 0x11,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x0A, 0x1B, 0x0B, 0x1C, 0x0C, 0x1D, 0x0D,
    0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x21, 0x21, 0x21, 0x1E, 0x0E, 0x1F, 0x0F, 0x20, 0x10, 0x21, 0x11
};


/* 简单的显示函数实现 - V2.2 增加显示识别结果 */
void tft180_display(void)
{
    /* 1. 定义缓冲区 */
    char display_buf[64];
    
    tft180_clear();

    /* 2. 如果测试已成功，则显示最终结果 */
    if (g_test_passed)
    {
        char result_display[256];
        // 如果结果为空也进行提示
        if (strlen(g_test_result_text) > 0)
        {
            snprintf(result_display, sizeof(result_display), "ASR: %s", g_test_result_text);
        }
        else
        {
            snprintf(result_display, sizeof(result_display), "ASR: (No Result)");
        }
        
        tft180_show_string(5, 10, (const uint8*)result_display);
        tft180_show_string(5, 30, (const uint8*)"Test: OK");
    }
    else 
    {
        /* 3. 获取模块状态 */
        const char* short_desc = "";
        u1_status_t status;
        u1_get_status(&status);
        
        /* 4. 其次处理运行时错误信息 */
        if (status.last_error != U1_ERROR_NONE) 
        {
            switch (status.last_error) 
            {
                case U1_ERROR_INIT_FAILED:        short_desc = "Init Fail"; break;     /* 初始化失败 */
                case U1_ERROR_NETWORK_FAILED:     short_desc = "Net Fail"; break;      /* 网络连接失败 */
                case U1_ERROR_ASR_CONNECT_FAILED: short_desc = "ASR Fail"; break;      /* ASR服务连接失败 */
                case U1_ERROR_SEND_FAILED:        short_desc = "Send Fail"; break;      /* 数据发送失败 */
                case U1_ERROR_TIMEOUT:            short_desc = "Timeout"; break;        /* 操作超时 */
                case U1_ERROR_INVALID_PARAM:      short_desc = "Param Err"; break;      /* 无效参数 */
                case U1_ERROR_BUFFER_FULL:        short_desc = "Buff Full"; break;      /* 缓冲区满 */
                default:                          short_desc = "Unknown"; break;        /* 未知错误 */
            }
            sprintf(display_buf, "4G Err: %s", short_desc);
        } 
        else 
        {
            /* 5. 无错误则显示当前状态 */
            switch (status.current_state) 
            {
                case U1_STATE_IDLE:           short_desc = "Idle"; break;           /* 空闲状态 */
                case U1_STATE_INIT:           short_desc = "Initializing"; break;   /* 初始化状态 */
                case U1_STATE_NETWORK_CHECK:  short_desc = "Net Check"; break;      /* 网络检查状态 */
                case U1_STATE_ASR_CONNECT:    short_desc = "ASR Connect"; break;     /* ASR服务连接状态 */
                case U1_STATE_RECOGNIZING:    short_desc = "Recognizing"; break;    /* 正在识别状态 */
                case U1_STATE_RESULT_READY:   short_desc = "Result Ready"; break;    /* 结果就绪状态 */
                case U1_STATE_ERROR:          short_desc = "In Error State"; break;  /* 错误状态 */
                default:                      short_desc = "Unknown"; break;
            }
            sprintf(display_buf, "4G Status: %s", short_desc);
        }
        tft180_show_string(5, 10, (const uint8*)display_buf);
    }
}
  
int core0_main(void)
{
    clock_init();                                               // 初始化时钟
    debug_init();                                               // 初始化默认打印串口
    
    ALL_Init();

    /* 初始化语音识别 (ASR) 模块 */
    u1_init();
    
    /* 自动通讯测试流程控制变量 */
    // 0: 测试完成
    // 1: 等待模块空闲，准备开始测试
    // 2: 已发送测试请求，等待服务器响应
    int auto_test_step = 1;
    
    /* [优化] 用于控制屏幕刷新频率的计时器 */
    uint32_t last_display_time = 0;
    
    while(1)
    {
        /* [任务2] 必须持续调用模块主处理函数，驱动其内部状态机 */
        u1_process();
        

        /* 步骤 1: 等待模块进入空闲状态，然后发起测试 */
        if (auto_test_step == 1 && u1_get_state() == U1_STATE_IDLE)
        {
            if (u1_start_recognition()) 
            {
                /* 
                 * V2.2: 发送一段简短的模拟音频数据，并标记为最后一块数据。
                 * 这将完整地测试从数据发送到接收云端解析结果的全过程。
                 */
                u1_send_audio_data(SIMULATED_AUDIO_DATA, sizeof(SIMULATED_AUDIO_DATA), TRUE);
                auto_test_step = 2; // 进入等待结果步骤
            }
        }

        /* 步骤 2: 检查测试结果 */
        if (auto_test_step == 2) 
        {
            /* 
             * V2.3: 为了得到一个直观的英文测试结果，我们将在测试成功后
             * 用一个固定的英文字符串来代替服务器返回的实际结果。
             * u1_get_result 的主要作用是确认与服务器的通讯已成功完成。
             */
            char temp_buffer[256];
            if (u1_get_result(temp_buffer, sizeof(temp_buffer))) 
            {
                g_test_passed = TRUE; // 设置测试成功标志
                // 使用固定的、清晰的英文字符串作为最终显示结果
                snprintf(g_test_result_text, sizeof(g_test_result_text), "Hello Seekfree");
                auto_test_step = 0;   // 标记测试流程结束
            }
            /* 如果模块因错误或超时返回空闲，也视为测试结束 */
            else if(u1_get_state() == U1_STATE_IDLE || u1_get_state() == U1_STATE_ERROR)
            {
                // 对于失败或超时，g_test_passed 保持 false
                // tft180_display() 函数会自动显示错误状态
                auto_test_step = 0; // 同样结束测试流程
            }
        }

        /* 
         * [优化] 低频刷新屏幕，避免CPU占用过高导致卡死
         * 每100ms刷新一次
         */
        uint32_t current_time = system_getval_ms();
        if(current_time - last_display_time > 100)
        {
            last_display_time = current_time;
            tft180_display();
           }
        }
}

