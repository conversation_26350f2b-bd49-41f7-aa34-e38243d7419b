/*
 * 4G语音识别测试程序 - 语音命令库集成版
 * 功能：插电即可测试user1（4G方案）的完整语音识别功能链 + 15条语音命令检测
 * 作者：BMW_智能车组_Baiyuyu_Xiaohuihui_Wzz
 * 日期：2025年07月16日
 *
 * 集成的15条语音命令：
 * 1. 打开双灯        2. 打开左转灯      3. 打开右转灯
 * 4. 打开远光灯      5. 打开近光灯      6. 打开刹灯
 * 7. 向前直行10米    8. 后退直行10米    9. 蛇形前进10米
 * 10. 蛇形后退10米  11. 逆时针转一圈   12. 顺时针转一圈
 * 13. 停进停车区1   14. 停进停车区2    15. 停进停车区3
 */
#include "zf_common_headfile.h"
#include "zf_common_debug.h"
#include "Device.h"
#include "user1/u1_core.h"
#include <string.h>
#include <stdio.h>

/* 简化的状态显示函数 */
void print_status(uint32 start_time)
{
    u1_state_t state = u1_get_state();
    u1_error_t error = u1_get_last_error();
    uint32 elapsed = (system_getval_ms() - start_time) / 1000;

    printf("=== 4G语音识别测试 [%d秒] ===\r\n", elapsed);

    switch(state)
    {
        case U1_STATE_IDLE:
            printf("状态: 空闲 - 准备就绪\r\n");
            break;
        case U1_STATE_INIT:
            printf("状态: 初始化中...\r\n");
            break;
        case U1_STATE_NETWORK_CHECK:
            printf("状态: 4G网络连接中...\r\n");
            break;
        case U1_STATE_ASR_CONNECT:
            printf("状态: 连接讯飞服务器...\r\n");
            break;
        case U1_STATE_RECOGNIZING:
            printf("状态: 语音识别中...\r\n");
            break;
        case U1_STATE_RESULT_READY:
            printf("状态: 结果处理中...\r\n");
            break;
        case U1_STATE_ERROR:
            printf("状态: 错误 - 错误码 %d\r\n", error);
            break;
        default:
            printf("状态: 未知状态\r\n");
            break;
    }

    // 结果显示将在main函数中处理

#if U1_FEATURE_VOICE_COMMANDS
        /* 显示语音命令检测状态 */
        if(u1_is_valid_command())
        {
            u1_voice_command_t cmd_id = u1_get_last_command();
            const char* cmd_desc = u1_get_command_description(cmd_id);
            printf("语音命令: %s (ID:%d)\r\n", cmd_desc, cmd_id);
        }
        else
        {
            printf("语音命令: 未检测到预设命令\r\n");
        }
#endif
    }

    printf("=====================================\r\n");
}

int core0_main(void)
{
    /* 基础初始化 */
    clock_init();
    debug_init();
    ALL_Init();

    /* 测试状态变量 */
    uint32 test_start_time = 0;
    uint32 last_status_time = 0;
    int test_completed = 0;
    char result_text[256];
    memset(result_text, 0, sizeof(result_text));

    printf("\r\n");
    printf("========================================\r\n");
    printf("    4G语音识别功能链测试程序\r\n");
    printf("    User1 vs User2 功能对比验证\r\n");
    printf("========================================\r\n");
    printf("测试目标: 验证User1完全实现User2功能\r\n");
    printf("通信方式: 4G网络 (替代WIFI)\r\n");
    printf("测试模式: 插电即测，无需按键\r\n");
    printf("语音命令: 集成15条智能车控制指令\r\n");
    printf("========================================\r\n");
    printf("支持的语音命令包括:\r\n");
    printf("• 灯光控制: 打开双灯、左转灯、右转灯等\r\n");
    printf("• 运动控制: 向前直行10米、后退直行10米等\r\n");
    printf("• 停车控制: 停进停车区1/2/3\r\n");
    printf("========================================\r\n\r\n");

    /* 初始化4G语音识别模块 */
    printf("步骤1: 初始化4G语音识别模块...\r\n");
    if(!u1_init())
    {
        printf("❌ 初始化失败！程序停止\r\n");
        while(1) system_delay_ms(1000);
    }
    printf("✅ 模块初始化成功\r\n\r\n");

    g_test_start_time = system_getval_ms();
    g_last_status_time = g_test_start_time;

    printf("步骤2: 开始自动测试流程...\r\n");
    printf("等待4G网络连接和语音识别...\r\n\r\n");

    /* 主循环 */
    while(1)
    {
        /*主要功能函数*/
        u1_process();

        uint32 current_time = system_getval_ms();

        /* 每3秒显示一次状态 */
        if(current_time - g_last_status_time > 3000)
        {
            g_last_status_time = current_time;
            print_status();
        }

        /* 检查测试结果 */
        if(!g_test_completed)
        {
            u1_state_t state = u1_get_state();

            /* 自动启动语音识别（当网络就绪时） */
            if(state == U1_STATE_IDLE)
            {
                printf("🚀 网络就绪，启动语音识别测试...\r\n");
                if(u1_start_recognition())
                {
                    printf("✅ 语音识别已启动\r\n");
                }
                else
                {
                    printf("❌ 启动语音识别失败\r\n");
                }
            }

            /* 获取识别结果 */
            if(state == U1_STATE_RESULT_READY)
            {
                if(u1_get_result(result_text, sizeof(result_text)))
                {
                    printf("\r\n🎉 测试成功完成！\r\n");
                    printf("📝 识别结果: %s\r\n", result_text);

#if U1_FEATURE_VOICE_COMMANDS
                    /* 检查是否识别到语音命令 */
                    if(u1_is_valid_command())
                    {
                        u1_voice_command_t cmd_id = u1_get_last_command();
                        const char* cmd_desc = u1_get_command_description(cmd_id);
                        printf("🎯 检测到语音命令: %s (ID:%d)\r\n", cmd_desc, cmd_id);
                        printf("✅ 语音命令库功能正常！\r\n");
                    }
                    else
                    {
                        printf("ℹ️  未检测到预设语音命令\r\n");
                        printf("💡 支持的命令包括: 打开双灯、向前直行10米等\r\n");
                    }
#endif

                    printf("⏱️  总耗时: %d秒\r\n", (current_time - g_test_start_time) / 1000);
                    printf("\r\n✅ User1功能链验证通过！\r\n");
                    printf("✅ 4G方案完全实现WIFI方案功能\r\n");
                    printf("✅ 语音命令库集成完成！\r\n");
                    g_test_completed = 1;
                }
            }

            /* 处理错误状态 */
            if(state == U1_STATE_ERROR)
            {
                u1_error_t error = u1_get_last_error();
                printf("\r\n❌ 测试遇到错误: %d\r\n", error);
                printf("🔄 10秒后自动重试...\r\n");
                system_delay_ms(10000);
                u1_reset();
                g_test_start_time = system_getval_ms();
            }
        }
        else
        {
            /* 测试完成，每30秒显示一次成功信息 */
            static uint32 last_success_msg = 0;
            if(current_time - last_success_msg > 30000)
            {
                last_success_msg = current_time;
                printf("✅ 测试已完成，User1功能验证成功！\r\n");
                printf("📝 最终结果: %s\r\n", result_text);
            }
        }

        /* 简单延时 */
        system_delay_ms(100);
    }
}