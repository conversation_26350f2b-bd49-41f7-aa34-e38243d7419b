	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc15900a --dep-file=Bsp.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c'

	
$TC16X
	.sdecl	'.rodata.Bsp..1.cnt',data,rom
	.sect	'.rodata.Bsp..1.cnt'
	.align	4
.1.cnt:	.type	object
	.size	.1.cnt,8
	.word	-1,2147483647
	
	.sdecl	'.text.Bsp.initTime',code,cluster('initTime')
	.sect	'.text.Bsp.initTime'
	.align	2
	
	.global	initTime
; Function initTime
.L26:
initTime:	.type	func
	movh.a	a15,#61440
.L56:
	jz.a	a15,.L2
.L2:
	call	IfxScuCcu_getSourceFrequency
.L145:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
	and	d15,#15
	itof	d15,d15
.L146:
	div.f	d15,d2,d15
.L147:
	j	.L3
.L3:
	j	.L4
.L4:
	ftoiz	d2,d15
.L57:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L148:
	mov.u	d15,#57600
	addih	d15,d15,#1525
.L149:
	div	e0,d2,d15
	mov	e0,d0
.L150:
	st.d	[a15]0,e0
.L151:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L152:
	mov.u	d15,#38528
	addih	d15,d15,#152
.L153:
	div	e0,d2,d15
	mov	e0,d0
.L154:
	st.d	[a15]8,e0
.L155:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L156:
	mov	d15,#15625
	sh	d15,#6
.L157:
	div	e0,d2,d15
	mov	e0,d0
.L158:
	st.d	[a15]16,e0
.L159:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L160:
	mov	d15,#3125
	sh	d15,#5
.L161:
	div	e0,d2,d15
	mov	e0,d0
.L162:
	st.d	[a15]24,e0
.L163:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L164:
	mov	d15,#10000
.L165:
	div	e0,d2,d15
	mov	e0,d0
.L166:
	st.d	[a15]32,e0
.L167:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L168:
	mov	d15,#1000
.L169:
	div	e0,d2,d15
	mov	e0,d0
.L170:
	st.d	[a15]40,e0
.L171:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L172:
	mov	d15,#100
.L173:
	div	e0,d2,d15
	mov	e0,d0
.L174:
	st.d	[a15]48,e0
.L175:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L176:
	mov	d15,#10
.L177:
	div	e0,d2,d15
	mov	e0,d0
.L178:
	st.d	[a15]56,e0
.L179:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L180:
	mul	d15,d2,#1
	mov	e0,d15
.L181:
	st.d	[a15]64,e0
.L182:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L183:
	mul	d15,d2,#10
	mov	e0,d15
.L184:
	st.d	[a15]72,e0
.L185:
	movh.a	a15,#@his(TimeConst)
	lea	a15,[a15]@los(TimeConst)
.L186:
	mul	d15,d2,#100
	mov	e0,d15
.L187:
	st.d	[a15]80,e0
.L188:
	ret
.L52:
	
__initTime_function_end:
	.size	initTime,__initTime_function_end-initTime
.L39:
	; End of function
	
	.sdecl	'.text.Bsp.waitPoll',code,cluster('waitPoll')
	.sect	'.text.Bsp.waitPoll'
	.align	2
	
	.global	waitPoll
; Function waitPoll
.L28:
waitPoll:	.type	func
	ret
.L66:
	
__waitPoll_function_end:
	.size	waitPoll,__waitPoll_function_end-waitPoll
.L44:
	; End of function
	
	.sdecl	'.text.Bsp.waitTime',code,cluster('waitTime')
	.sect	'.text.Bsp.waitTime'
	.align	2
	
	.global	waitTime
; Function waitTime
.L30:
waitTime:	.type	func
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e0,[a15]0
.L197:
	ne	d15,d4,d0
	or.ne	d15,d5,d1
.L198:
	jne	d15,#0,.L5
.L199:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e2,[a15]0
.L122:
	j	.L6
.L5:
	mfcr	d15,#65068
.L123:
	extr.u	d15,d15,#15,#1
.L124:
	ne	d15,d15,#0
.L125:
	j	.L7
.L7:
	disable
.L200:
	nop
.L201:
	j	.L8
.L8:
	j	.L9
.L9:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L126:
	ld.w	d1,0xf000002c
.L202:
	mov	d0,#0
.L203:
	or	d0,d2
.L127:
	or	d1,d3
.L204:
	j	.L10
.L10:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e2,[a15]0
.L205:
	and	d0,d2
.L128:
	and	d1,d3
.L102:
	jeq	d15,#0,.L11
.L206:
	enable
.L11:
	j	.L12
.L12:
	addx	d2,d0,d4
	addc	d3,d1,d5
.L6:
	j	.L13
.L13:
	j	.L14
.L15:
.L14:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e0,[a15]0
.L207:
	ne	d15,d2,d0
	or.ne	d15,d3,d1
.L208:
	jne	d15,#0,.L16
.L209:
	mov	d15,#0
.L129:
	j	.L17
.L16:
	mfcr	d15,#65068
.L130:
	extr.u	d15,d15,#15,#1
.L131:
	ne	d15,d15,#0
.L132:
	j	.L18
.L18:
	disable
.L210:
	nop
.L211:
	j	.L19
.L19:
	j	.L20
.L20:
	ld.w	d0,0xf0000010
	mov	d5,#0
	mov	d4,d0
.L134:
	ld.w	d1,0xf000002c
.L212:
	mov	d0,#0
.L213:
	or	d0,d4
.L135:
	or	d1,d5
.L214:
	j	.L21
.L21:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e4,[a15]0
.L215:
	and	d0,d4
.L136:
	and	d1,d5
.L120:
	jeq	d15,#0,.L22
.L216:
	enable
.L22:
	j	.L23
.L23:
	ge.u	d15,d0,d2
.L133:
	and.eq	d15,d1,d3
	or.lt	d15,d3,d1
.L17:
	j	.L24
.L24:
	jeq	d15,#0,.L15
.L71:
	ret
.L67:
	
__waitTime_function_end:
	.size	waitTime,__waitTime_function_end-waitTime
.L49:
	; End of function
	
	.sdecl	'.bss.Bsp.TimeConst',data,cluster('TimeConst')
	.sect	'.bss.Bsp.TimeConst'
	.global	TimeConst
	.align	4
TimeConst:	.type	object
	.size	TimeConst,88
	.space	88
	.calls	'initTime','IfxScuCcu_getSourceFrequency'
	.calls	'initTime','',0
	.calls	'waitPoll','',0
	.extern	IfxScuCcu_getSourceFrequency
	.calls	'waitTime','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L32:
	.word	81973
	.half	3
	.word	.L33
	.byte	4
.L31:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L34
	.byte	2,1,1,3
	.word	229
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	232
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L62:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	277
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	289
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0
.L99:
	.byte	7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	401
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	375
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	407
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	407
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	375
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	516
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	516
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	532
	.byte	4,2,35,0,0
.L84:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	668
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	707
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	668
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	951
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	628
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	911
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1142
	.byte	4,2,35,8,0,14
	.word	1182
	.byte	3
	.word	1245
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1250
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	685
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1250
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	685
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	685
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1250
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1480
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1796
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2367
	.byte	4,2,35,0,0,15,4
	.word	668
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	668
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	668
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	668
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	668
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	668
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	668
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	668
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	668
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	668
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	668
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2710
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	668
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	668
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	668
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	668
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	668
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	668
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	668
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	668
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	668
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	668
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3142
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3362
	.byte	4,2,35,0,0,15,24
	.word	668
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	668
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	668
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	668
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	668
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	668
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3685
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	668
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	668
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	668
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	668
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	668
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3989
	.byte	4,2,35,0,0,15,8
	.word	668
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4314
	.byte	4,2,35,0,0,15,12
	.word	668
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4654
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	493
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5306
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5453
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	493
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5622
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	685
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5794
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	685
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	685
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5969
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6143
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	668
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6317
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6493
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6649
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	685
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6982
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7330
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	668
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7454
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7538
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	668
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7718
	.byte	4,2,35,0,0,15,76
	.word	668
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7971
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8058
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1756
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2327
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2446
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2486
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2670
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2885
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3102
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3322
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2486
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3636
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3676
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3949
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4265
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4305
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4605
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4645
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4980
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5266
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4305
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5413
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5582
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5754
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5929
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6103
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6277
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6453
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6609
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6942
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7290
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4305
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7414
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7663
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7922
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7962
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8018
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8585
	.byte	4,3,35,252,1,0,14
	.word	8625
	.byte	3
	.word	9228
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9233
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	668
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9238
	.byte	6,0
.L64:
	.byte	8
	.byte	'IfxScuCcu_getStmFrequency',0,3,7,226,8,20
	.word	289
	.byte	1,1
.L65:
	.byte	6,0,17,9,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	9459
	.byte	1,1,6,0
.L91:
	.byte	8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	668
	.byte	1,1
.L92:
	.byte	6,0
.L88:
	.byte	8
	.byte	'IfxCpu_disableInterrupts',0,3,8,147,5,20
	.word	668
	.byte	1,1
.L89:
	.byte	19,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,8,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,8,225,5,17,1,1,6,0
.L106:
	.byte	4
	.byte	'IfxCpu_restoreInterrupts',0,3,8,168,7,17,1,1
.L107:
	.byte	5
	.byte	'enabled',0,8,168,7,50
	.word	668
.L109:
	.byte	6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	9781
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	685
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	668
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	685
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	9781
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	9781
	.byte	19,6,0,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	668
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	668
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	668
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	668
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10012
	.byte	4,2,35,0,0,14
	.word	10302
	.byte	3
	.word	10341
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10346
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,13,100,16,4,11
	.byte	'DISR',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,12,13,149,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10394
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,13,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	685
	.byte	16,0,2,35,2,0,12,13,181,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10550
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,13,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,229,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10672
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,13,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,245,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10757
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,13,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,253,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10842
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,13,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,133,3,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,13,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,141,3,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11013
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,13,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,149,3,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11099
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,13,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,157,3,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11185
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,13,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,133,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11271
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,13,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,165,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11358
	.byte	4,2,35,0,0,15,8
	.word	11400
	.byte	16,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,13,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	668
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	668
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	668
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	668
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	668
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	668
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	668
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	668
	.byte	3,0,2,35,3,0,12,13,157,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11449
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,13,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	493
	.byte	25,0,2,35,0,0,12,13,173,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,13,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,12,13,189,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11897
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,13,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,237,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12061
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,13,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,141,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12148
	.byte	4,2,35,0,0,15,144,1
	.word	668
	.byte	16,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,13,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	668
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	668
	.byte	2,0,2,35,3,0,12,13,221,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12248
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,13,175,1,16,4,11
	.byte	'CLR',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	493
	.byte	31,0,2,35,0,0,12,13,213,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12408
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,13,168,1,16,4,11
	.byte	'RST',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	493
	.byte	31,0,2,35,0,0,12,13,205,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12514
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,13,160,1,16,4,11
	.byte	'RST',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,12,13,197,2,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12618
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,12,13,253,1,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12741
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	668
	.byte	1,0,2,35,3,0,12,13,245,1,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12830
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,13,173,3,25,128,2,13
	.byte	'CLC',0
	.word	10510
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2486
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	10632
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2486
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	10717
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	10802
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	10887
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	10973
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	11059
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	11145
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	11231
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	11318
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	11440
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	11640
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	11857
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	12021
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	4645
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	12108
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	12197
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	12237
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	12368
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	12474
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	12578
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	12701
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	12790
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13359
	.byte	4,3,35,252,1,0,14
	.word	13399
	.byte	3
	.word	13819
.L95:
	.byte	8
	.byte	'IfxStm_get',0,3,12,162,4,19
	.word	375
	.byte	1,1
.L96:
	.byte	5
	.byte	'stm',0,12,162,4,39
	.word	13824
.L98:
	.byte	6,0
.L55:
	.byte	8
	.byte	'IfxStm_getFrequency',0,3,12,179,4,20
	.word	289
	.byte	1,1
.L58:
	.byte	5
	.byte	'stm',0,12,179,4,49
	.word	13824
.L60:
	.byte	19
.L61:
	.byte	6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,12,190,4,19
	.word	9781
	.byte	1,1,5
	.byte	'stm',0,12,190,4,44
	.word	13824
	.byte	6,0
.L86:
	.byte	8
	.byte	'disableInterrupts',0,3,14,108,20
	.word	668
	.byte	1,1
.L87:
	.byte	19,6,0,0
.L101:
	.byte	4
	.byte	'restoreInterrupts',0,3,14,142,1,17,1,1
.L103:
	.byte	5
	.byte	'enabled',0,14,142,1,43
	.word	668
.L105:
	.byte	19,6,0,0
.L68:
	.byte	7
	.byte	'long long int',0,8,5
.L76:
	.byte	8
	.byte	'getDeadLine',0,3,14,164,2,25
	.word	14040
	.byte	1,1
.L77:
	.byte	5
	.byte	'timeout',0,14,164,2,50
	.word	14040
.L79:
	.byte	19,6,0,0
.L110:
	.byte	8
	.byte	'isDeadLine',0,3,14,211,2,20
	.word	668
	.byte	1,1
.L111:
	.byte	5
	.byte	'deadLine',0,14,211,2,44
	.word	14040
.L113:
	.byte	19,6,0,0
.L81:
	.byte	8
	.byte	'now',0,3,14,221,1,25
	.word	14040
	.byte	1,1
.L82:
	.byte	19,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,14,240,1,25
	.word	14040
	.byte	1,1,19,6,0,0
.L70:
	.byte	4
	.byte	'wait',0,3,14,163,3,17,1,1
.L72:
	.byte	5
	.byte	'timeout',0,14,163,3,35
	.word	14040
.L74:
	.byte	19,6,6,0,0,14
	.word	509
	.byte	20
	.byte	'__mfcr',0
	.word	14246
	.byte	1,1,1,1,21
	.word	509
	.byte	0,22
	.byte	'__nop',0,1,1,1,1,22
	.byte	'__disable',0,1,1,1,1,22
	.byte	'__enable',0,1,1,1,1,23
	.word	237
	.byte	24
	.word	263
	.byte	6,0,23
	.word	298
	.byte	24
	.word	330
	.byte	6,0,23
	.word	343
	.byte	6,0,23
	.word	412
	.byte	24
	.word	431
	.byte	6,0,23
	.word	447
	.byte	24
	.word	462
	.byte	24
	.word	476
	.byte	6,0,23
	.word	1255
	.byte	24
	.word	1295
	.byte	24
	.word	1313
	.byte	6,0,23
	.word	1333
	.byte	24
	.word	1371
	.byte	24
	.word	1389
	.byte	6,0,23
	.word	1409
	.byte	24
	.word	1460
	.byte	6,0,23
	.word	9341
	.byte	24
	.word	9369
	.byte	24
	.word	9383
	.byte	24
	.word	9401
	.byte	6,0,23
	.word	9419
	.byte	6,0,25
	.byte	'IfxScuCcu_getSourceFrequency',0,7,173,7,20
	.word	289
	.byte	1,1,1,1,23
	.word	9538
	.byte	6,0,23
	.word	9572
	.byte	6,0,23
	.word	9614
	.byte	19,26
	.word	9572
	.byte	27
	.word	9612
	.byte	0,6,0,0,23
	.word	9655
	.byte	6,0,23
	.word	9689
	.byte	6,0,23
	.word	9729
	.byte	24
	.word	9762
	.byte	6,0,23
	.word	9802
	.byte	24
	.word	9843
	.byte	6,0,23
	.word	9862
	.byte	24
	.word	9917
	.byte	6,0,23
	.word	9936
	.byte	24
	.word	9976
	.byte	24
	.word	9993
	.byte	19,6,0,0,23
	.word	10351
	.byte	24
	.word	10379
	.byte	6,0,23
	.word	13829
	.byte	24
	.word	13852
	.byte	6,0,23
	.word	13867
	.byte	24
	.word	13899
	.byte	19,19,26
	.word	9419
	.byte	27
	.word	9457
	.byte	0,0,6,0,0,23
	.word	13917
	.byte	24
	.word	13945
	.byte	6,0,23
	.word	13960
	.byte	19,26
	.word	9614
	.byte	28
	.word	9651
	.byte	26
	.word	9572
	.byte	27
	.word	9612
	.byte	0,27
	.word	9652
	.byte	0,0,6,0,0,23
	.word	13993
	.byte	24
	.word	14019
	.byte	19,26
	.word	9729
	.byte	24
	.word	9762
	.byte	27
	.word	9779
	.byte	0,6,0,0,23
	.word	14057
	.byte	24
	.word	14081
	.byte	19,26
	.word	14147
	.byte	28
	.word	14163
	.byte	26
	.word	13960
	.byte	28
	.word	13989
	.byte	26
	.word	9614
	.byte	28
	.word	9651
	.byte	26
	.word	9572
	.byte	27
	.word	9612
	.byte	0,27
	.word	9652
	.byte	0,0,27
	.word	13990
	.byte	0,0,27
	.word	14164
	.byte	26
	.word	13993
	.byte	24
	.word	14019
	.byte	28
	.word	14036
	.byte	26
	.word	9729
	.byte	24
	.word	9762
	.byte	27
	.word	9779
	.byte	0,27
	.word	14037
	.byte	0,0,27
	.word	14165
	.byte	26
	.word	13829
	.byte	24
	.word	13852
	.byte	27
	.word	13865
	.byte	0,27
	.word	14166
	.byte	0,0,6,0,0
.L53:
	.byte	7
	.byte	'long int',0,4,5,23
	.word	14102
	.byte	24
	.word	14125
	.byte	19,26
	.word	14147
	.byte	28
	.word	14163
	.byte	26
	.word	13960
	.byte	28
	.word	13989
	.byte	26
	.word	9614
	.byte	28
	.word	9651
	.byte	26
	.word	9572
	.byte	27
	.word	9612
	.byte	0,27
	.word	9652
	.byte	0,0,27
	.word	13990
	.byte	0,0,27
	.word	14164
	.byte	26
	.word	13993
	.byte	24
	.word	14019
	.byte	28
	.word	14036
	.byte	26
	.word	9729
	.byte	24
	.word	9762
	.byte	27
	.word	9779
	.byte	0,27
	.word	14037
	.byte	0,0,27
	.word	14165
	.byte	26
	.word	13829
	.byte	24
	.word	13852
	.byte	27
	.word	13865
	.byte	0,27
	.word	14166
	.byte	0,0,6,0,0,23
	.word	14147
	.byte	19,26
	.word	13960
	.byte	28
	.word	13989
	.byte	26
	.word	9614
	.byte	28
	.word	9651
	.byte	26
	.word	9572
	.byte	27
	.word	9612
	.byte	0,27
	.word	9652
	.byte	0,0,27
	.word	13990
	.byte	0,0,6,26
	.word	13993
	.byte	24
	.word	14019
	.byte	28
	.word	14036
	.byte	26
	.word	9729
	.byte	24
	.word	9762
	.byte	27
	.word	9779
	.byte	0,27
	.word	14037
	.byte	0,0,6,26
	.word	13829
	.byte	24
	.word	13852
	.byte	27
	.word	13865
	.byte	0,6,0,0,23
	.word	14169
	.byte	19,26
	.word	13829
	.byte	24
	.word	13852
	.byte	27
	.word	13865
	.byte	0,6,0,0,23
	.word	14211
	.byte	24
	.word	14224
	.byte	19,26
	.word	14057
	.byte	24
	.word	14081
	.byte	28
	.word	14098
	.byte	26
	.word	14147
	.byte	28
	.word	14163
	.byte	26
	.word	13960
	.byte	28
	.word	13989
	.byte	26
	.word	9614
	.byte	28
	.word	9651
	.byte	26
	.word	9572
	.byte	27
	.word	9612
	.byte	0,27
	.word	9652
	.byte	0,0,27
	.word	13990
	.byte	0,0,27
	.word	14164
	.byte	26
	.word	13993
	.byte	24
	.word	14019
	.byte	28
	.word	14036
	.byte	26
	.word	9729
	.byte	24
	.word	9762
	.byte	27
	.word	9779
	.byte	0,27
	.word	14037
	.byte	0,0,27
	.word	14165
	.byte	26
	.word	13829
	.byte	24
	.word	13852
	.byte	27
	.word	13865
	.byte	0,27
	.word	14166
	.byte	0,0,27
	.word	14099
	.byte	0,0,6,26
	.word	14102
	.byte	24
	.word	14125
	.byte	28
	.word	14143
	.byte	26
	.word	14147
	.byte	28
	.word	14163
	.byte	26
	.word	13960
	.byte	28
	.word	13989
	.byte	26
	.word	9614
	.byte	28
	.word	9651
	.byte	26
	.word	9572
	.byte	27
	.word	9612
	.byte	0,27
	.word	9652
	.byte	0,0,27
	.word	13990
	.byte	0,0,27
	.word	14164
	.byte	26
	.word	13993
	.byte	24
	.word	14019
	.byte	28
	.word	14036
	.byte	26
	.word	9729
	.byte	24
	.word	9762
	.byte	27
	.word	9779
	.byte	0,27
	.word	14037
	.byte	0,0,27
	.word	14165
	.byte	26
	.word	13829
	.byte	24
	.word	13852
	.byte	27
	.word	13865
	.byte	0,27
	.word	14166
	.byte	0,0,27
	.word	14144
	.byte	0,0,6,0,0,10
	.byte	'_Ifx_CPU_ICR_Bits',0,15,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	516
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	516
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	516
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	516
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	516
	.byte	6,0,2,35,0,0
.L93:
	.byte	12,15,223,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15461
	.byte	4,2,35,0,0,7
	.byte	'short int',0,2,5,29
	.byte	'__wchar_t',0,16,1,1
	.word	15618
	.byte	29
	.byte	'__size_t',0,16,1,1
	.word	493
	.byte	29
	.byte	'__ptrdiff_t',0,16,1,1
	.word	509
	.byte	30,1,3
	.word	15686
	.byte	29
	.byte	'__codeptr',0,16,1,1
	.word	15688
	.byte	29
	.byte	'boolean',0,17,101,29
	.word	668
	.byte	29
	.byte	'uint8',0,17,105,29
	.word	668
	.byte	29
	.byte	'uint16',0,17,109,29
	.word	685
	.byte	29
	.byte	'uint32',0,17,113,29
	.word	9781
	.byte	29
	.byte	'uint64',0,17,118,29
	.word	375
	.byte	29
	.byte	'sint16',0,17,126,29
	.word	15618
	.byte	29
	.byte	'sint32',0,17,131,1,29
	.word	14863
	.byte	29
	.byte	'sint64',0,17,138,1,29
	.word	14040
	.byte	29
	.byte	'float32',0,17,167,1,29
	.word	289
	.byte	29
	.byte	'pvoid',0,18,57,28
	.word	407
	.byte	29
	.byte	'Ifx_TickTime',0,18,79,28
	.word	14040
	.byte	29
	.byte	'Ifx_Priority',0,18,103,16
	.word	685
	.byte	17,18,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,29
	.byte	'Ifx_RxSel',0,18,140,1,3
	.word	15906
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,15,45,16,4,11
	.byte	'ADDR',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_A_Bits',0,15,48,3
	.word	16044
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,15,51,16,4,11
	.byte	'VSS',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	516
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BIV_Bits',0,15,55,3
	.word	16105
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,15,58,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	516
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BTV_Bits',0,15,62,3
	.word	16184
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,15,65,16,4,11
	.byte	'CountValue',0,4
	.word	516
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT_Bits',0,15,69,3
	.word	16270
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,15,72,16,4,11
	.byte	'CM',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	516
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	516
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	516
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	516
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL_Bits',0,15,80,3
	.word	16359
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,15,83,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	516
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT_Bits',0,15,89,3
	.word	16505
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,15,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	516
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID_Bits',0,15,96,3
	.word	16632
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,15,99,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	516
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L_Bits',0,15,103,3
	.word	16730
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,15,106,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	516
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U_Bits',0,15,110,3
	.word	16823
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,15,113,16,4,11
	.byte	'MODREV',0,4
	.word	516
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	516
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID_Bits',0,15,118,3
	.word	16916
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,15,121,16,4,11
	.byte	'XE',0,4
	.word	516
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	516
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE_Bits',0,15,125,3
	.word	17023
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,15,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	516
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	516
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	516
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT_Bits',0,15,136,1,3
	.word	17110
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,15,139,1,16,4,11
	.byte	'CID',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	516
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID_Bits',0,15,143,1,3
	.word	17264
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,15,146,1,16,4,11
	.byte	'DATA',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_D_Bits',0,15,149,1,3
	.word	17358
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,15,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	516
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	516
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	516
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	516
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	516
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	516
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DATR_Bits',0,15,163,1,3
	.word	17421
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,15,166,1,16,4,11
	.byte	'DE',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	516
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	516
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	516
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	516
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	516
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	516
	.byte	19,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR_Bits',0,15,177,1,3
	.word	17639
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,15,180,1,16,4,11
	.byte	'DTA',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	516
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR_Bits',0,15,184,1,3
	.word	17854
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,15,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	516
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0_Bits',0,15,192,1,3
	.word	17948
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,15,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2_Bits',0,15,199,1,3
	.word	18064
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,15,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	516
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCX_Bits',0,15,206,1,3
	.word	18165
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,15,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD_Bits',0,15,212,1,3
	.word	18258
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,15,215,1,16,4,11
	.byte	'TA',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR_Bits',0,15,218,1,3
	.word	18338
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,15,221,1,16,4,11
	.byte	'IED',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	516
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	516
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	516
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	516
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	516
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR_Bits',0,15,233,1,3
	.word	18407
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,15,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	516
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DMS_Bits',0,15,240,1,3
	.word	18636
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,15,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	516
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L_Bits',0,15,247,1,3
	.word	18729
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,15,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	516
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U_Bits',0,15,254,1,3
	.word	18824
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,15,129,2,16,4,11
	.byte	'RE',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE_Bits',0,15,133,2,3
	.word	18919
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,15,136,2,16,4,11
	.byte	'WE',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE_Bits',0,15,140,2,3
	.word	19009
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,15,143,2,16,4,11
	.byte	'SRE',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	516
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	516
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	516
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	516
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	516
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	516
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	516
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	516
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	516
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	516
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	516
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	516
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	516
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR_Bits',0,15,161,2,3
	.word	19099
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,15,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	516
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	516
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	516
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT_Bits',0,15,172,2,3
	.word	19423
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,15,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	516
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	516
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FCX_Bits',0,15,180,2,3
	.word	19577
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,15,183,2,16,4,11
	.byte	'TST',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	516
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	516
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	516
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	516
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	516
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	516
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	516
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	516
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	516
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	516
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	516
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	516
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	516
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	516
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,15,202,2,3
	.word	19683
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,15,205,2,16,4,11
	.byte	'OPC',0,4
	.word	516
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	516
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	516
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	516
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	516
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,15,212,2,3
	.word	20032
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,15,215,2,16,4,11
	.byte	'PC',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,15,218,2,3
	.word	20192
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,15,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,15,224,2,3
	.word	20273
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,15,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,15,230,2,3
	.word	20360
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,15,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,15,236,2,3
	.word	20447
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,15,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	516
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT_Bits',0,15,243,2,3
	.word	20534
	.byte	29
	.byte	'Ifx_CPU_ICR_Bits',0,15,253,2,3
	.word	15461
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,15,128,3,16,4,11
	.byte	'ISP',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_ISP_Bits',0,15,131,3,3
	.word	20651
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,15,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	516
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	516
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_LCX_Bits',0,15,139,3,3
	.word	20717
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,15,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	516
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT_Bits',0,15,146,3,3
	.word	20823
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,15,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	516
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT_Bits',0,15,153,3,3
	.word	20916
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,15,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	516
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT_Bits',0,15,160,3,3
	.word	21009
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,15,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	516
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_PC_Bits',0,15,167,3,3
	.word	21102
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,15,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	516
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0_Bits',0,15,175,3,3
	.word	21187
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,15,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	516
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1_Bits',0,15,183,3,3
	.word	21303
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,15,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2_Bits',0,15,190,3,3
	.word	21414
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,15,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	516
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	516
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	516
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	516
	.byte	10,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI_Bits',0,15,200,3,3
	.word	21515
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,15,203,3,16,4,11
	.byte	'TA',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR_Bits',0,15,206,3,3
	.word	21645
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,15,209,3,16,4,11
	.byte	'IED',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	516
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	516
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	516
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	516
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	516
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR_Bits',0,15,221,3,3
	.word	21714
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,15,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	516
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0_Bits',0,15,229,3,3
	.word	21943
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,15,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	516
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	516
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1_Bits',0,15,237,3,3
	.word	22056
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,15,240,3,16,4,11
	.byte	'PSI',0,4
	.word	516
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2_Bits',0,15,244,3,3
	.word	22169
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,15,247,3,16,4,11
	.byte	'FRE',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	516
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	516
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	516
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	516
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	516
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR_Bits',0,15,129,4,3
	.word	22260
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,15,132,4,16,4,11
	.byte	'CDC',0,4
	.word	516
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	516
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	516
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	516
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	516
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	516
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	516
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	516
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	516
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	516
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	516
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	516
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSW_Bits',0,15,147,4,3
	.word	22463
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,15,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	516
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	516
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	516
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	516
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN_Bits',0,15,156,4,3
	.word	22706
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,15,159,4,16,4,11
	.byte	'PC',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	516
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	516
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	516
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	516
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	516
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	516
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	516
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON_Bits',0,15,171,4,3
	.word	22834
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,15,174,4,16,4,11
	.byte	'EN',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,15,177,4,3
	.word	23075
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,15,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,15,183,4,3
	.word	23158
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,15,186,4,16,4,11
	.byte	'EN',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,15,189,4,3
	.word	23249
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,15,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,15,195,4,3
	.word	23340
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,15,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	493
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,15,202,4,3
	.word	23439
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,15,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	493
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,15,209,4,3
	.word	23546
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,15,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	516
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	516
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	516
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT_Bits',0,15,220,4,3
	.word	23653
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,15,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	516
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON_Bits',0,15,231,4,3
	.word	23807
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,15,234,4,16,4,11
	.byte	'ASI',0,4
	.word	516
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	516
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,15,238,4,3
	.word	23968
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,15,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	516
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	516
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	516
	.byte	15,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON_Bits',0,15,249,4,3
	.word	24066
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,15,252,4,16,4,11
	.byte	'Timer',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,15,255,4,3
	.word	24238
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,15,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	516
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR_Bits',0,15,133,5,3
	.word	24318
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,15,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	516
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	516
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	516
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	516
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	516
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	516
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	516
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	516
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	516
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	516
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	516
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	516
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	516
	.byte	3,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT_Bits',0,15,153,5,3
	.word	24391
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,15,156,5,16,4,11
	.byte	'T0',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	516
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	516
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	516
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	516
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	516
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	516
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	516
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,15,167,5,3
	.word	24709
	.byte	12,15,175,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16044
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_A',0,15,180,5,3
	.word	24904
	.byte	12,15,183,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16105
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BIV',0,15,188,5,3
	.word	24963
	.byte	12,15,191,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16184
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BTV',0,15,196,5,3
	.word	25024
	.byte	12,15,199,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16270
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT',0,15,204,5,3
	.word	25085
	.byte	12,15,207,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16359
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL',0,15,212,5,3
	.word	25147
	.byte	12,15,215,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16505
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT',0,15,220,5,3
	.word	25210
	.byte	12,15,223,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16632
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID',0,15,228,5,3
	.word	25274
	.byte	12,15,231,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16730
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L',0,15,236,5,3
	.word	25339
	.byte	12,15,239,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16823
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U',0,15,244,5,3
	.word	25402
	.byte	12,15,247,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16916
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID',0,15,252,5,3
	.word	25465
	.byte	12,15,255,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17023
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE',0,15,132,6,3
	.word	25529
	.byte	12,15,135,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17110
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT',0,15,140,6,3
	.word	25591
	.byte	12,15,143,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17264
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID',0,15,148,6,3
	.word	25654
	.byte	12,15,151,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17358
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_D',0,15,156,6,3
	.word	25718
	.byte	12,15,159,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17421
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DATR',0,15,164,6,3
	.word	25777
	.byte	12,15,167,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17639
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR',0,15,172,6,3
	.word	25839
	.byte	12,15,175,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17854
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR',0,15,180,6,3
	.word	25902
	.byte	12,15,183,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17948
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0',0,15,188,6,3
	.word	25966
	.byte	12,15,191,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18064
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2',0,15,196,6,3
	.word	26029
	.byte	12,15,199,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18165
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCX',0,15,204,6,3
	.word	26092
	.byte	12,15,207,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18258
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD',0,15,212,6,3
	.word	26153
	.byte	12,15,215,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18338
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR',0,15,220,6,3
	.word	26216
	.byte	12,15,223,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18407
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR',0,15,228,6,3
	.word	26279
	.byte	12,15,231,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18636
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DMS',0,15,236,6,3
	.word	26342
	.byte	12,15,239,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18729
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L',0,15,244,6,3
	.word	26403
	.byte	12,15,247,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18824
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U',0,15,252,6,3
	.word	26466
	.byte	12,15,255,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18919
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE',0,15,132,7,3
	.word	26529
	.byte	12,15,135,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19009
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE',0,15,140,7,3
	.word	26591
	.byte	12,15,143,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19099
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR',0,15,148,7,3
	.word	26653
	.byte	12,15,151,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19423
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT',0,15,156,7,3
	.word	26715
	.byte	12,15,159,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19577
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FCX',0,15,164,7,3
	.word	26778
	.byte	12,15,167,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19683
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,15,172,7,3
	.word	26839
	.byte	12,15,175,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20032
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,15,180,7,3
	.word	26909
	.byte	12,15,183,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20192
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,15,188,7,3
	.word	26979
	.byte	12,15,191,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20273
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,15,196,7,3
	.word	27048
	.byte	12,15,199,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20360
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,15,204,7,3
	.word	27119
	.byte	12,15,207,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20447
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,15,212,7,3
	.word	27190
	.byte	12,15,215,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20534
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT',0,15,220,7,3
	.word	27261
	.byte	29
	.byte	'Ifx_CPU_ICR',0,15,228,7,3
	.word	15578
	.byte	12,15,231,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20651
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ISP',0,15,236,7,3
	.word	27344
	.byte	12,15,239,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20717
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_LCX',0,15,244,7,3
	.word	27405
	.byte	12,15,247,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20823
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT',0,15,252,7,3
	.word	27466
	.byte	12,15,255,7,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20916
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT',0,15,132,8,3
	.word	27529
	.byte	12,15,135,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21009
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT',0,15,140,8,3
	.word	27592
	.byte	12,15,143,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21102
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PC',0,15,148,8,3
	.word	27655
	.byte	12,15,151,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21187
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0',0,15,156,8,3
	.word	27715
	.byte	12,15,159,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21303
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1',0,15,164,8,3
	.word	27778
	.byte	12,15,167,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21414
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2',0,15,172,8,3
	.word	27841
	.byte	12,15,175,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21515
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI',0,15,180,8,3
	.word	27904
	.byte	12,15,183,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21645
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR',0,15,188,8,3
	.word	27966
	.byte	12,15,191,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21714
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR',0,15,196,8,3
	.word	28029
	.byte	12,15,199,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21943
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0',0,15,204,8,3
	.word	28092
	.byte	12,15,207,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22056
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1',0,15,212,8,3
	.word	28154
	.byte	12,15,215,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22169
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2',0,15,220,8,3
	.word	28216
	.byte	12,15,223,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22260
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR',0,15,228,8,3
	.word	28278
	.byte	12,15,231,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22463
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSW',0,15,236,8,3
	.word	28340
	.byte	12,15,239,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22706
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN',0,15,244,8,3
	.word	28401
	.byte	12,15,247,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22834
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON',0,15,252,8,3
	.word	28464
	.byte	12,15,255,8,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23075
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA',0,15,132,9,3
	.word	28528
	.byte	12,15,135,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23158
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB',0,15,140,9,3
	.word	28598
	.byte	12,15,143,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23249
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,15,148,9,3
	.word	28668
	.byte	12,15,151,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23340
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,15,156,9,3
	.word	28742
	.byte	12,15,159,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23439
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,15,164,9,3
	.word	28816
	.byte	12,15,167,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23546
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,15,172,9,3
	.word	28886
	.byte	12,15,175,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23653
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT',0,15,180,9,3
	.word	28956
	.byte	12,15,183,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23807
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON',0,15,188,9,3
	.word	29019
	.byte	12,15,191,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23968
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI',0,15,196,9,3
	.word	29083
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24066
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON',0,15,204,9,3
	.word	29149
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24238
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER',0,15,212,9,3
	.word	29214
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24318
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR',0,15,220,9,3
	.word	29281
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24391
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT',0,15,228,9,3
	.word	29345
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24709
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC',0,15,236,9,3
	.word	29409
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,15,247,9,25,8,13
	.byte	'L',0
	.word	25339
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	25402
	.byte	4,2,35,4,0,14
	.word	29475
	.byte	29
	.byte	'Ifx_CPU_CPR',0,15,251,9,3
	.word	29517
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,15,254,9,25,8,13
	.byte	'L',0
	.word	26403
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	26466
	.byte	4,2,35,4,0,14
	.word	29543
	.byte	29
	.byte	'Ifx_CPU_DPR',0,15,130,10,3
	.word	29585
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,15,133,10,25,16,13
	.byte	'LA',0
	.word	28816
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	28886
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	28668
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	28742
	.byte	4,2,35,12,0,14
	.word	29611
	.byte	29
	.byte	'Ifx_CPU_SPROT_RGN',0,15,139,10,3
	.word	29693
	.byte	15,12
	.word	29214
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,15,142,10,25,16,13
	.byte	'CON',0
	.word	29149
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	29725
	.byte	12,2,35,4,0,14
	.word	29734
	.byte	29
	.byte	'Ifx_CPU_TPS',0,15,146,10,3
	.word	29782
	.byte	10
	.byte	'_Ifx_CPU_TR',0,15,149,10,25,8,13
	.byte	'EVT',0
	.word	29345
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	29281
	.byte	4,2,35,4,0,14
	.word	29808
	.byte	29
	.byte	'Ifx_CPU_TR',0,15,153,10,3
	.word	29853
	.byte	15,176,32
	.word	668
	.byte	16,175,32,0,15,208,223,1
	.word	668
	.byte	16,207,223,1,0,15,248,1
	.word	668
	.byte	16,247,1,0,15,244,29
	.word	668
	.byte	16,243,29,0,15,188,3
	.word	668
	.byte	16,187,3,0,15,232,3
	.word	668
	.byte	16,231,3,0,15,252,23
	.word	668
	.byte	16,251,23,0,15,228,63
	.word	668
	.byte	16,227,63,0,15,128,1
	.word	29543
	.byte	16,15,0,14
	.word	29968
	.byte	15,128,31
	.word	668
	.byte	16,255,30,0,15,64
	.word	29475
	.byte	16,7,0,14
	.word	29994
	.byte	15,192,31
	.word	668
	.byte	16,191,31,0,15,16
	.word	25529
	.byte	16,3,0,15,16
	.word	26529
	.byte	16,3,0,15,16
	.word	26591
	.byte	16,3,0,15,208,7
	.word	668
	.byte	16,207,7,0,14
	.word	29734
	.byte	15,240,23
	.word	668
	.byte	16,239,23,0,15,64
	.word	29808
	.byte	16,7,0,14
	.word	30073
	.byte	15,192,23
	.word	668
	.byte	16,191,23,0,15,232,1
	.word	668
	.byte	16,231,1,0,15,28
	.word	668
	.byte	16,27,0,15,180,1
	.word	668
	.byte	16,179,1,0,15,16
	.word	668
	.byte	16,15,0,15,172,1
	.word	668
	.byte	16,171,1,0,15,64
	.word	25718
	.byte	16,15,0,15,64
	.word	668
	.byte	16,63,0,15,64
	.word	24904
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,15,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	29878
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	28401
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	29889
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	29083
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	29902
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	28092
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	28154
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	28216
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	29913
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	26029
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4305
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	28464
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	26653
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2486
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	25777
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	26153
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	26216
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	26279
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3676
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	25966
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	29924
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	28278
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	27778
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	27841
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	27715
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	27966
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	28029
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	29935
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	25210
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	29946
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	26839
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	26979
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	26909
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2486
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	27048
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	27119
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	27190
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	29957
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	29978
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	29983
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	30003
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	30008
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	30019
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	30028
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	30037
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	30046
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	30057
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	30062
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	30082
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	30087
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	25147
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	25085
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	27261
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	27466
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	27529
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	27592
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	30098
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	25839
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2486
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	26715
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	25591
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	28956
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	30109
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	29409
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4645
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	26342
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	26092
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	25902
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	30118
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	27904
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	28340
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	27655
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4305
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	29019
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	25465
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	25274
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	24963
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	25024
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	27344
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	15578
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4305
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	26778
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	27405
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	30129
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	25654
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	30138
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	30149
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	30158
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	30167
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	30158
	.byte	64,4,35,192,255,3,0,14
	.word	30176
	.byte	29
	.byte	'Ifx_CPU',0,15,130,11,3
	.word	31967
	.byte	17,9,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,29
	.byte	'IfxCpu_Id',0,9,132,1,3
	.word	31989
	.byte	29
	.byte	'IfxCpu_ResourceCpu',0,9,161,1,3
	.word	9459
	.byte	29
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10012
	.byte	29
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10302
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	32134
	.byte	29
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	32166
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10302
	.byte	4,2,35,8,0,14
	.word	32192
	.byte	29
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	32251
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	32279
	.byte	29
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	32316
	.byte	15,64
	.word	10302
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	32344
	.byte	64,2,35,0,0,14
	.word	32353
	.byte	29
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	32385
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10302
	.byte	4,2,35,12,0,14
	.word	32410
	.byte	29
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	32482
	.byte	15,8
	.word	10302
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	32508
	.byte	8,2,35,0,0,14
	.word	32517
	.byte	29
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	32553
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10302
	.byte	4,2,35,12,0,14
	.word	32583
	.byte	29
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	32656
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	32682
	.byte	29
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	32717
	.byte	15,192,1
	.word	10302
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4645
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	32743
	.byte	192,1,2,35,16,0,14
	.word	32753
	.byte	29
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	32820
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10302
	.byte	4,2,35,4,0,14
	.word	32846
	.byte	29
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	32894
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	32922
	.byte	29
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	32955
	.byte	15,40
	.word	668
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	32508
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	32508
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	32508
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	32508
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10302
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10302
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	32982
	.byte	40,2,35,40,0,14
	.word	32991
	.byte	29
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	33118
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	33145
	.byte	29
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	33177
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	33203
	.byte	29
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	33235
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10302
	.byte	4,2,35,8,0,14
	.word	33261
	.byte	29
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	33321
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10302
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	30129
	.byte	16,2,35,16,0,14
	.word	33347
	.byte	29
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	33441
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10302
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10302
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10302
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3676
	.byte	24,2,35,24,0,14
	.word	33468
	.byte	29
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	33585
	.byte	15,12
	.word	10302
	.byte	16,2,0,15,32
	.word	10302
	.byte	16,7,0,15,32
	.word	33622
	.byte	16,0,0,15,88
	.word	668
	.byte	16,87,0,15,108
	.word	10302
	.byte	16,26,0,15,96
	.word	668
	.byte	16,95,0,15,96
	.word	33622
	.byte	16,2,0,15,160,3
	.word	668
	.byte	16,159,3,0,15,64
	.word	33622
	.byte	16,1,0,15,192,3
	.word	668
	.byte	16,191,3,0,15,16
	.word	10302
	.byte	16,3,0,15,64
	.word	33707
	.byte	16,3,0,15,192,2
	.word	668
	.byte	16,191,2,0,15,52
	.word	668
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	33613
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2486
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10302
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10302
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	32508
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4305
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	33631
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	33640
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	33649
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	33658
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10302
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4645
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	33667
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	33676
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	33667
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	33676
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	33687
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	33696
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	33716
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	33725
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	33613
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	33736
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	33613
	.byte	12,3,35,192,18,0,14
	.word	33745
	.byte	29
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	34205
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	34231
	.byte	29
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	34264
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10302
	.byte	4,2,35,12,0,14
	.word	34291
	.byte	29
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	34364
	.byte	15,56
	.word	668
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10302
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10302
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10302
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	34391
	.byte	56,2,35,24,0,14
	.word	34400
	.byte	29
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	34523
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	34549
	.byte	29
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	34581
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10302
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10302
	.byte	4,2,35,16,0,14
	.word	34607
	.byte	29
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	34692
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	34718
	.byte	29
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	34750
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	33622
	.byte	32,2,35,0,0,14
	.word	34776
	.byte	29
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	34809
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	33622
	.byte	32,2,35,0,0,14
	.word	34836
	.byte	29
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	34870
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10302
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10302
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10302
	.byte	4,2,35,20,0,14
	.word	34898
	.byte	29
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	34991
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	35018
	.byte	29
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	35050
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	33707
	.byte	16,2,35,4,0,14
	.word	35076
	.byte	29
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	35122
	.byte	15,24
	.word	10302
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	35148
	.byte	24,2,35,0,0,14
	.word	35157
	.byte	29
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	35190
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	33613
	.byte	12,2,35,0,0,14
	.word	35217
	.byte	29
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	35249
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10302
	.byte	4,2,35,4,0,14
	.word	35275
	.byte	29
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	35321
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10302
	.byte	4,2,35,12,0,14
	.word	35347
	.byte	29
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	35422
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10302
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10302
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10302
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10302
	.byte	4,2,35,12,0,14
	.word	35451
	.byte	29
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	35525
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10302
	.byte	4,2,35,0,0,14
	.word	35553
	.byte	29
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	35587
	.byte	15,4
	.word	32134
	.byte	16,0,0,14
	.word	35614
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	35623
	.byte	4,2,35,0,0,14
	.word	35628
	.byte	29
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	35664
	.byte	15,48
	.word	32192
	.byte	16,3,0,14
	.word	35692
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	35701
	.byte	48,2,35,0,0,14
	.word	35706
	.byte	29
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	35746
	.byte	14
	.word	32279
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	35776
	.byte	4,2,35,0,0,14
	.word	35781
	.byte	29
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	35815
	.byte	15,64
	.word	32353
	.byte	16,0,0,14
	.word	35842
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	35851
	.byte	64,2,35,0,0,14
	.word	35856
	.byte	29
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	35890
	.byte	15,32
	.word	32410
	.byte	16,1,0,14
	.word	35917
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	35926
	.byte	32,2,35,0,0,14
	.word	35931
	.byte	29
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	35967
	.byte	14
	.word	32517
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	35995
	.byte	8,2,35,0,0,14
	.word	36000
	.byte	29
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	36044
	.byte	15,16
	.word	32583
	.byte	16,0,0,14
	.word	36076
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	36085
	.byte	16,2,35,0,0,14
	.word	36090
	.byte	29
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	36124
	.byte	15,8
	.word	32682
	.byte	16,1,0,14
	.word	36151
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	36160
	.byte	8,2,35,0,0,14
	.word	36165
	.byte	29
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	36199
	.byte	15,208,1
	.word	32753
	.byte	16,0,0,14
	.word	36226
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	36236
	.byte	208,1,2,35,0,0,14
	.word	36241
	.byte	29
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	36277
	.byte	14
	.word	32846
	.byte	14
	.word	32846
	.byte	14
	.word	32846
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	36304
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4305
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	36309
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	36314
	.byte	8,2,35,24,0,14
	.word	36319
	.byte	29
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	36410
	.byte	15,4
	.word	32922
	.byte	16,0,0,14
	.word	36439
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	36448
	.byte	4,2,35,0,0,14
	.word	36453
	.byte	29
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	36489
	.byte	15,80
	.word	32991
	.byte	16,0,0,14
	.word	36517
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	36526
	.byte	80,2,35,0,0,14
	.word	36531
	.byte	29
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	36567
	.byte	15,4
	.word	33145
	.byte	16,0,0,14
	.word	36595
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	36604
	.byte	4,2,35,0,0,14
	.word	36609
	.byte	29
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	36643
	.byte	15,4
	.word	33203
	.byte	16,0,0,14
	.word	36670
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	36679
	.byte	4,2,35,0,0,14
	.word	36684
	.byte	29
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	36718
	.byte	15,12
	.word	33261
	.byte	16,0,0,14
	.word	36745
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	36754
	.byte	12,2,35,0,0,14
	.word	36759
	.byte	29
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	36793
	.byte	15,64
	.word	33347
	.byte	16,1,0,14
	.word	36820
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	36829
	.byte	64,2,35,0,0,14
	.word	36834
	.byte	29
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	36870
	.byte	15,48
	.word	33468
	.byte	16,0,0,14
	.word	36898
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	36907
	.byte	48,2,35,0,0,14
	.word	36912
	.byte	29
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	36950
	.byte	15,204,18
	.word	33745
	.byte	16,0,0,14
	.word	36979
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	36989
	.byte	204,18,2,35,0,0,14
	.word	36994
	.byte	29
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	37030
	.byte	15,4
	.word	34231
	.byte	16,0,0,14
	.word	37057
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	37066
	.byte	4,2,35,0,0,14
	.word	37071
	.byte	29
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	37107
	.byte	15,64
	.word	34291
	.byte	16,3,0,14
	.word	37135
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	37144
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10302
	.byte	4,2,35,64,0,14
	.word	37149
	.byte	29
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	37198
	.byte	15,80
	.word	34400
	.byte	16,0,0,14
	.word	37226
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	37235
	.byte	80,2,35,0,0,14
	.word	37240
	.byte	29
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	37274
	.byte	15,4
	.word	34549
	.byte	16,0,0,14
	.word	37301
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	37310
	.byte	4,2,35,0,0,14
	.word	37315
	.byte	29
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	37349
	.byte	15,40
	.word	34607
	.byte	16,1,0,14
	.word	37376
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	37385
	.byte	40,2,35,0,0,14
	.word	37390
	.byte	29
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	37424
	.byte	15,8
	.word	34718
	.byte	16,1,0,14
	.word	37451
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	37460
	.byte	8,2,35,0,0,14
	.word	37465
	.byte	29
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	37499
	.byte	15,32
	.word	34776
	.byte	16,0,0,14
	.word	37526
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	37535
	.byte	32,2,35,0,0,14
	.word	37540
	.byte	29
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	37576
	.byte	15,32
	.word	34836
	.byte	16,0,0,14
	.word	37604
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	37613
	.byte	32,2,35,0,0,14
	.word	37618
	.byte	29
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	37656
	.byte	15,96
	.word	34898
	.byte	16,3,0,14
	.word	37685
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	37694
	.byte	96,2,35,0,0,14
	.word	37699
	.byte	29
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	37735
	.byte	15,4
	.word	35018
	.byte	16,0,0,14
	.word	37763
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	37772
	.byte	4,2,35,0,0,14
	.word	37777
	.byte	29
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	37811
	.byte	14
	.word	35076
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	37838
	.byte	20,2,35,0,0,14
	.word	37843
	.byte	29
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	37877
	.byte	15,24
	.word	35157
	.byte	16,0,0,14
	.word	37904
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	37913
	.byte	24,2,35,0,0,14
	.word	37918
	.byte	29
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	37954
	.byte	15,12
	.word	35217
	.byte	16,0,0,14
	.word	37982
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	37991
	.byte	12,2,35,0,0,14
	.word	37996
	.byte	29
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	38030
	.byte	15,16
	.word	35275
	.byte	16,1,0,14
	.word	38057
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	38066
	.byte	16,2,35,0,0,14
	.word	38071
	.byte	29
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	38105
	.byte	15,64
	.word	35451
	.byte	16,3,0,14
	.word	38132
	.byte	15,224,1
	.word	668
	.byte	16,223,1,0,15,32
	.word	35347
	.byte	16,1,0,14
	.word	38157
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	38141
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	38146
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	38166
	.byte	32,3,35,160,2,0,14
	.word	38171
	.byte	29
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	38240
	.byte	14
	.word	35553
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	38268
	.byte	4,2,35,0,0,14
	.word	38273
	.byte	29
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	38309
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	38337
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	38894
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	493
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	38971
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	668
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	668
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	668
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	668
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	668
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	668
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	668
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	668
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	668
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	39107
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	668
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	668
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	668
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	668
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	668
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	668
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	668
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	39387
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	39625
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	668
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	668
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	668
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	668
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	39753
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	668
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	668
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	668
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	668
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	39996
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	40231
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	493
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	40359
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	493
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	40459
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	668
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	668
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	668
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	668
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	40559
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	493
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	40767
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	685
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	668
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	685
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	668
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	40932
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	685
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	668
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	41115
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	668
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	668
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	493
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	668
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	668
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	41269
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	41633
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	685
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	668
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	668
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	668
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	668
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	41844
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	685
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	493
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	42096
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	42214
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	42325
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	42488
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	42651
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	42809
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	668
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	668
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	668
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	668
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	668
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	668
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	668
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	668
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	668
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	685
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	42974
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	685
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	668
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	668
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	685
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	668
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	43303
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	43524
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	43687
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	43959
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	44112
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	44268
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	44430
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	44573
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	44738
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	685
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	668
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	668
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	44883
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	668
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	45064
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	45238
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	493
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	45398
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	493
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	45542
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	45816
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	45955
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	668
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	685
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	668
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	668
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	668
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	46118
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	685
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	668
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	685
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	668
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	46336
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	46499
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	46835
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	668
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	46942
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	47394
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	668
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	668
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	47493
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	685
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	47643
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	493
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	668
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	47792
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	493
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	47953
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	685
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	685
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	48083
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	48215
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	668
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	685
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	48330
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	685
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	685
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	48441
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	668
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	668
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	668
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	668
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	668
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	668
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	48599
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	49011
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	685
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	668
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	668
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	49112
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	493
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	49379
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	49515
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	668
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	49626
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	49759
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	685
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	668
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	668
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	668
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	668
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	49962
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	668
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	668
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	668
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	685
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	50318
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	685
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	50496
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	685
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	668
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	668
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	668
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	668
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	50596
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	668
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	668
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	668
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	668
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	685
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	50966
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	493
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	51152
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	51350
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	668
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	668
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	493
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	51583
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	668
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	668
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	668
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	668
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	668
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	51735
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	668
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	668
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	668
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	668
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	52302
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	668
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	668
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	668
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	52596
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	668
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	668
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	685
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	668
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	52874
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	685
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	53370
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	685
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	53683
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	668
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	668
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	668
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	668
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	668
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	668
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	668
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	53892
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	668
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	668
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	668
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	54103
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	493
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	54535
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	668
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	668
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	668
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	54631
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	493
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	54891
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	668
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	668
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	493
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	55016
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	55213
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	55366
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	55519
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	55672
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	532
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	707
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	951
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	516
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	516
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	516
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	516
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	55927
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	668
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	56053
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	668
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	56305
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38337
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	56524
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38894
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	56588
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38971
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	56652
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39107
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	56717
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39387
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	56782
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39625
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	56847
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39753
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	56912
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39996
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	56977
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40231
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	57042
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40359
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	57107
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40459
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	57172
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40559
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	57237
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40767
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	57301
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40932
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	57365
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41115
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	57429
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41269
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	57494
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41633
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	57556
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41844
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	57618
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42096
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	57680
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42214
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	57744
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42325
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	57809
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42488
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	57875
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42651
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	57941
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42809
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	58009
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42974
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	58076
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43303
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	58144
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43524
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	58212
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43687
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	58278
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43959
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	58345
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44112
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	58414
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44268
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	58483
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44430
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	58552
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44573
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	58621
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44738
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	58690
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44883
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	58759
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45064
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	58827
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45238
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	58895
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45398
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	58963
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45542
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	59031
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45816
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	59096
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45955
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	59161
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46118
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	59227
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46336
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	59291
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46499
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	59352
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46835
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	59413
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46942
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	59473
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47394
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	59535
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47493
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	59595
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47643
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	59657
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47792
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	59725
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47953
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	59793
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48083
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	59861
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48215
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	59925
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48330
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	59990
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48441
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	60053
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48599
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	60114
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49011
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	60178
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49112
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	60239
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49379
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	60303
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49515
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	60370
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49626
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	60433
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49759
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	60494
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49962
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	60556
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50318
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	60621
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50496
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	60686
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50596
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	60751
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50966
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	60820
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51152
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	60889
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51350
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	60958
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51583
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	61023
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51735
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	61086
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52302
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	61151
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52596
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	61216
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52874
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	61281
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53370
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	61347
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53892
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	61416
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53683
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	61480
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54103
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	61545
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54535
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	61610
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54631
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	61675
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54891
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	61739
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55016
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	61805
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55213
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	61869
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55366
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	61934
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55519
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	61999
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55672
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	62064
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	628
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	911
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1142
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55927
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	62215
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56053
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	62282
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56305
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	62349
	.byte	14
	.word	1182
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	62414
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	62215
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	62282
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	62349
	.byte	4,2,35,8,0,14
	.word	62443
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	62504
	.byte	15,8
	.word	57680
	.byte	16,1,0,15,20
	.word	668
	.byte	16,19,0,15,8
	.word	61023
	.byte	16,1,0,14
	.word	62443
	.byte	15,24
	.word	1182
	.byte	16,1,0,14
	.word	62563
	.byte	15,16
	.word	57494
	.byte	16,3,0,15,16
	.word	59473
	.byte	16,3,0,15,180,3
	.word	668
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4305
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	59413
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2486
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	60114
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	60958
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	60556
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	60621
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	60686
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	60889
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	60751
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	60820
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	56717
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	56782
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	59291
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	59227
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	56847
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	56912
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	56977
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	57042
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	61545
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2486
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	61416
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	56652
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	61739
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	61480
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2486
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	58278
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	62531
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	57744
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	61805
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	57107
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	57172
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	62540
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	60433
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	59595
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	60178
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	60053
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	59535
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	59031
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	58009
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	57809
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	57875
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	61675
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2486
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	61086
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	61281
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	61347
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	62549
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2486
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	57429
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	57301
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	61151
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	61216
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	62558
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	57618
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	62572
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4645
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	62064
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	61999
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	61869
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	61934
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2486
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	59861
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	59925
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	57237
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	59990
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4305
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	61610
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	30129
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	59657
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	59725
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	59793
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	30109
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	60370
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4305
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	59096
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	57941
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	59161
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	58212
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	58076
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2486
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	58759
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	58827
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	58895
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	58963
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	58345
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	58414
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	58483
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	58552
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	58621
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	58690
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	58144
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2486
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	60303
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	60239
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	32982
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	62577
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	57556
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	59352
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	60494
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	62586
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2486
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	57365
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	62595
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	56588
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	56524
	.byte	4,3,35,252,7,0,14
	.word	62606
	.byte	29
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	64596
	.byte	29
	.byte	'Ifx_STM_ACCEN0_Bits',0,13,79,3
	.word	12830
	.byte	29
	.byte	'Ifx_STM_ACCEN1_Bits',0,13,85,3
	.word	12741
	.byte	29
	.byte	'Ifx_STM_CAP_Bits',0,13,91,3
	.word	11271
	.byte	29
	.byte	'Ifx_STM_CAPSV_Bits',0,13,97,3
	.word	12148
	.byte	29
	.byte	'Ifx_STM_CLC_Bits',0,13,107,3
	.word	10394
	.byte	29
	.byte	'Ifx_STM_CMCON_Bits',0,13,120,3
	.word	11449
	.byte	29
	.byte	'Ifx_STM_CMP_Bits',0,13,126,3
	.word	11358
	.byte	29
	.byte	'Ifx_STM_ICR_Bits',0,13,139,1,3
	.word	11680
	.byte	29
	.byte	'Ifx_STM_ID_Bits',0,13,147,1,3
	.word	10550
	.byte	29
	.byte	'Ifx_STM_ISCR_Bits',0,13,157,1,3
	.word	11897
	.byte	29
	.byte	'Ifx_STM_KRST0_Bits',0,13,165,1,3
	.word	12618
	.byte	29
	.byte	'Ifx_STM_KRST1_Bits',0,13,172,1,3
	.word	12514
	.byte	29
	.byte	'Ifx_STM_KRSTCLR_Bits',0,13,179,1,3
	.word	12408
	.byte	29
	.byte	'Ifx_STM_OCS_Bits',0,13,189,1,3
	.word	12248
	.byte	29
	.byte	'Ifx_STM_TIM0_Bits',0,13,195,1,3
	.word	10672
	.byte	29
	.byte	'Ifx_STM_TIM0SV_Bits',0,13,201,1,3
	.word	12061
	.byte	29
	.byte	'Ifx_STM_TIM1_Bits',0,13,207,1,3
	.word	10757
	.byte	29
	.byte	'Ifx_STM_TIM2_Bits',0,13,213,1,3
	.word	10842
	.byte	29
	.byte	'Ifx_STM_TIM3_Bits',0,13,219,1,3
	.word	10927
	.byte	29
	.byte	'Ifx_STM_TIM4_Bits',0,13,225,1,3
	.word	11013
	.byte	29
	.byte	'Ifx_STM_TIM5_Bits',0,13,231,1,3
	.word	11099
	.byte	29
	.byte	'Ifx_STM_TIM6_Bits',0,13,237,1,3
	.word	11185
	.byte	29
	.byte	'Ifx_STM_ACCEN0',0,13,250,1,3
	.word	13359
	.byte	29
	.byte	'Ifx_STM_ACCEN1',0,13,130,2,3
	.word	12790
	.byte	29
	.byte	'Ifx_STM_CAP',0,13,138,2,3
	.word	11318
	.byte	29
	.byte	'Ifx_STM_CAPSV',0,13,146,2,3
	.word	12197
	.byte	29
	.byte	'Ifx_STM_CLC',0,13,154,2,3
	.word	10510
	.byte	29
	.byte	'Ifx_STM_CMCON',0,13,162,2,3
	.word	11640
	.byte	29
	.byte	'Ifx_STM_CMP',0,13,170,2,3
	.word	11400
	.byte	29
	.byte	'Ifx_STM_ICR',0,13,178,2,3
	.word	11857
	.byte	29
	.byte	'Ifx_STM_ID',0,13,186,2,3
	.word	10632
	.byte	29
	.byte	'Ifx_STM_ISCR',0,13,194,2,3
	.word	12021
	.byte	29
	.byte	'Ifx_STM_KRST0',0,13,202,2,3
	.word	12701
	.byte	29
	.byte	'Ifx_STM_KRST1',0,13,210,2,3
	.word	12578
	.byte	29
	.byte	'Ifx_STM_KRSTCLR',0,13,218,2,3
	.word	12474
	.byte	29
	.byte	'Ifx_STM_OCS',0,13,226,2,3
	.word	12368
	.byte	29
	.byte	'Ifx_STM_TIM0',0,13,234,2,3
	.word	10717
	.byte	29
	.byte	'Ifx_STM_TIM0SV',0,13,242,2,3
	.word	12108
	.byte	29
	.byte	'Ifx_STM_TIM1',0,13,250,2,3
	.word	10802
	.byte	29
	.byte	'Ifx_STM_TIM2',0,13,130,3,3
	.word	10887
	.byte	29
	.byte	'Ifx_STM_TIM3',0,13,138,3,3
	.word	10973
	.byte	29
	.byte	'Ifx_STM_TIM4',0,13,146,3,3
	.word	11059
	.byte	29
	.byte	'Ifx_STM_TIM5',0,13,154,3,3
	.word	11145
	.byte	29
	.byte	'Ifx_STM_TIM6',0,13,162,3,3
	.word	11231
	.byte	14
	.word	13399
	.byte	29
	.byte	'Ifx_STM',0,13,201,3,3
	.word	65701
	.byte	17,19,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,29
	.byte	'IfxScu_CCUCON0_CLKSEL',0,19,240,10,3
	.word	65723
	.byte	17,19,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	65820
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,20,45,16,4,11
	.byte	'EN0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,20,79,3
	.word	65942
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,20,82,16,4,11
	.byte	'reserved_0',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,20,85,3
	.word	66503
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,20,88,16,4,11
	.byte	'SEL',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	668
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	493
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,20,95,3
	.word	66584
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,20,98,16,4,11
	.byte	'VLD0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	493
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,20,111,3
	.word	66737
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,20,114,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	493
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	668
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,20,121,3
	.word	66985
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,20,124,16,4,11
	.byte	'STATUS',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	493
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0_Bits',0,20,128,1,3
	.word	67131
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,20,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM1_Bits',0,20,136,1,3
	.word	67229
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,20,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM2_Bits',0,20,144,1,3
	.word	67345
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,20,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	493
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	685
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRD_Bits',0,20,153,1,3
	.word	67461
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,20,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	493
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	685
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRP_Bits',0,20,162,1,3
	.word	67601
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,20,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	493
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	685
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCW_Bits',0,20,171,1,3
	.word	67741
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,20,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	668
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	668
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	685
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	668
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	668
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	668
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	668
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FCON_Bits',0,20,193,1,3
	.word	67880
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,20,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	668
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	668
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	668
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FPRO_Bits',0,20,218,1,3
	.word	68242
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,20,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	685
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	668
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	668
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	668
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FSR_Bits',0,20,254,1,3
	.word	68683
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,20,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	668
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	668
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_ID_Bits',0,20,134,2,3
	.word	69289
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,20,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	685
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARD_Bits',0,20,147,2,3
	.word	69400
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,20,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	685
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARP_Bits',0,20,159,2,3
	.word	69614
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,20,162,2,16,4,11
	.byte	'L',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	668
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	668
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	668
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	685
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	668
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCOND_Bits',0,20,179,2,3
	.word	69801
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,20,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	668
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	493
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,20,188,2,3
	.word	70125
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,20,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	685
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,20,199,2,3
	.word	70268
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	685
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	668
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	668
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	668
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	685
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,219,2,3
	.word	70457
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,20,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	668
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	668
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,20,254,2,3
	.word	70820
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,20,129,3,16,4,11
	.byte	'S0L',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	668
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONP_Bits',0,20,160,3,3
	.word	71415
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,20,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	668
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	668
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	668
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	668
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	668
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	668
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	668
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	668
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	668
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	668
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	668
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	668
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	668
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	668
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	668
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	668
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	668
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	668
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	668
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	668
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	668
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,20,194,3,3
	.word	71939
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,20,197,3,16,4,11
	.byte	'TAG',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	493
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,20,201,3,3
	.word	72521
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,20,204,3,16,4,11
	.byte	'TAG',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	493
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,20,208,3,3
	.word	72623
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,20,211,3,16,4,11
	.byte	'TAG',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	493
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,20,215,3,3
	.word	72725
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,20,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	493
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD_Bits',0,20,222,3,3
	.word	72827
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,20,225,3,16,4,11
	.byte	'STRT',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	668
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	668
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	668
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	668
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	668
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	668
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	685
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_RRCT_Bits',0,20,236,3,3
	.word	72921
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,20,239,3,16,4,11
	.byte	'DATA',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0_Bits',0,20,242,3,3
	.word	73131
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,20,245,3,16,4,11
	.byte	'DATA',0,4
	.word	493
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1_Bits',0,20,248,3,3
	.word	73204
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,20,251,3,16,4,11
	.byte	'SEL',0,1
	.word	668
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	668
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	668
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	668
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	493
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,20,130,4,3
	.word	73277
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,20,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	668
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	493
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,20,137,4,3
	.word	73432
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,20,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	668
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	493
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	668
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	668
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	668
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,20,147,4,3
	.word	73537
	.byte	12,20,155,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65942
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN0',0,20,160,4,3
	.word	73685
	.byte	12,20,163,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66503
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1',0,20,168,4,3
	.word	73751
	.byte	12,20,171,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66584
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG',0,20,176,4,3
	.word	73817
	.byte	12,20,179,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66737
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT',0,20,184,4,3
	.word	73885
	.byte	12,20,187,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66985
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_TOP',0,20,192,4,3
	.word	73954
	.byte	12,20,195,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67131
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0',0,20,200,4,3
	.word	74022
	.byte	12,20,203,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67229
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM1',0,20,208,4,3
	.word	74087
	.byte	12,20,211,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67345
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM2',0,20,216,4,3
	.word	74152
	.byte	12,20,219,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67461
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRD',0,20,224,4,3
	.word	74217
	.byte	12,20,227,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67601
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRP',0,20,232,4,3
	.word	74282
	.byte	12,20,235,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67741
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCW',0,20,240,4,3
	.word	74347
	.byte	12,20,243,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67880
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FCON',0,20,248,4,3
	.word	74411
	.byte	12,20,251,4,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68242
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FPRO',0,20,128,5,3
	.word	74475
	.byte	12,20,131,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68683
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FSR',0,20,136,5,3
	.word	74539
	.byte	12,20,139,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69289
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ID',0,20,144,5,3
	.word	74602
	.byte	12,20,147,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69400
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARD',0,20,152,5,3
	.word	74664
	.byte	12,20,155,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69614
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARP',0,20,160,5,3
	.word	74728
	.byte	12,20,163,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69801
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCOND',0,20,168,5,3
	.word	74792
	.byte	12,20,171,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70125
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG',0,20,176,5,3
	.word	74859
	.byte	12,20,179,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70268
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSM',0,20,184,5,3
	.word	74928
	.byte	12,20,187,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70457
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,20,192,5,3
	.word	74997
	.byte	12,20,195,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70820
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONOTP',0,20,200,5,3
	.word	75070
	.byte	12,20,203,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71415
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONP',0,20,208,5,3
	.word	75139
	.byte	12,20,211,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71939
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONWOP',0,20,216,5,3
	.word	75206
	.byte	12,20,219,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72521
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0',0,20,224,5,3
	.word	75275
	.byte	12,20,227,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72623
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1',0,20,232,5,3
	.word	75343
	.byte	12,20,235,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72725
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2',0,20,240,5,3
	.word	75411
	.byte	12,20,243,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72827
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD',0,20,248,5,3
	.word	75479
	.byte	12,20,251,5,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72921
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRCT',0,20,128,6,3
	.word	75543
	.byte	12,20,131,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73131
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0',0,20,136,6,3
	.word	75607
	.byte	12,20,139,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73204
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1',0,20,144,6,3
	.word	75671
	.byte	12,20,147,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73277
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG',0,20,152,6,3
	.word	75735
	.byte	12,20,155,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73432
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT',0,20,160,6,3
	.word	75803
	.byte	12,20,163,6,9,4,13
	.byte	'U',0
	.word	493
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	509
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73537
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_TOP',0,20,168,6,3
	.word	75872
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,20,179,6,25,12,13
	.byte	'CFG',0
	.word	73817
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	73885
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73954
	.byte	4,2,35,8,0,14
	.word	75940
	.byte	29
	.byte	'Ifx_FLASH_CBAB',0,20,184,6,3
	.word	76003
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,20,187,6,25,12,13
	.byte	'CFG0',0
	.word	75275
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	75343
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	75411
	.byte	4,2,35,8,0,14
	.word	76032
	.byte	29
	.byte	'Ifx_FLASH_RDB',0,20,192,6,3
	.word	76096
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,20,195,6,25,12,13
	.byte	'CFG',0
	.word	75735
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	75803
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	75872
	.byte	4,2,35,8,0,14
	.word	76124
	.byte	29
	.byte	'Ifx_FLASH_UBAB',0,20,200,6,3
	.word	76187
	.byte	29
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8058
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7971
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4314
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2367
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3362
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2495
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3142
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2710
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2925
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7330
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7454
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7538
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7718
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5969
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6493
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6143
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6317
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6982
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1796
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5306
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5794
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5453
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5622
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6649
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1480
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5020
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4654
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3685
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3989
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8585
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8018
	.byte	29
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4605
	.byte	29
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2446
	.byte	29
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3636
	.byte	29
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2670
	.byte	29
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3322
	.byte	29
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2885
	.byte	29
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3102
	.byte	29
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7414
	.byte	29
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7663
	.byte	29
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7922
	.byte	29
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7290
	.byte	29
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6103
	.byte	29
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6609
	.byte	29
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6277
	.byte	29
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6453
	.byte	29
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2327
	.byte	29
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6942
	.byte	29
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5413
	.byte	29
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5929
	.byte	29
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5582
	.byte	29
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5754
	.byte	29
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1756
	.byte	29
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5266
	.byte	29
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4980
	.byte	29
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3949
	.byte	29
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4265
	.byte	14
	.word	8625
	.byte	29
	.byte	'Ifx_P',0,6,139,6,3
	.word	77534
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,29
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	77554
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,29
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	77705
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,29
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	77949
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	78047
	.byte	29
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9238
	.byte	31,5,190,1,9,8,13
	.byte	'port',0
	.word	9233
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	668
	.byte	1,2,35,4,0,29
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	78512
	.byte	29
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,7,148,1,16
	.word	232
	.byte	31,7,212,5,9,8,13
	.byte	'value',0
	.word	9781
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9781
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_CcuconRegConfig',0,7,216,5,3
	.word	78612
	.byte	31,7,221,5,9,8,13
	.byte	'pDivider',0
	.word	668
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	668
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	668
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	289
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_InitialStepConfig',0,7,227,5,3
	.word	78683
	.byte	31,7,231,5,9,12,13
	.byte	'k2Step',0
	.word	668
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	289
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	78572
	.byte	4,2,35,8,0,29
	.byte	'IfxScuCcu_PllStepsConfig',0,7,236,5,3
	.word	78800
	.byte	3
	.word	229
	.byte	31,7,244,5,9,48,13
	.byte	'ccucon0',0
	.word	78612
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	78612
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	78612
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	78612
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	78612
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	78612
	.byte	8,2,35,40,0,29
	.byte	'IfxScuCcu_ClockDistributionConfig',0,7,252,5,3
	.word	78902
	.byte	31,7,128,6,9,8,13
	.byte	'value',0
	.word	9781
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9781
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,7,132,6,3
	.word	79054
	.byte	3
	.word	78800
	.byte	31,7,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	668
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	79130
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	78683
	.byte	8,2,35,8,0,29
	.byte	'IfxScuCcu_SysPllConfig',0,7,142,6,3
	.word	79135
	.byte	17,8,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,29
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	79252
	.byte	31,8,160,1,9,6,13
	.byte	'counter',0
	.word	9781
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	668
	.byte	1,2,35,4,0,29
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	79341
	.byte	31,8,172,1,9,32,13
	.byte	'instruction',0
	.word	79341
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	79341
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	79341
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	79341
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	79341
	.byte	6,2,35,24,0,29
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	79407
	.byte	17,21,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,29
	.byte	'IfxSrc_Tos',0,21,74,3
	.word	79525
	.byte	17,12,151,1,9,1,18
	.byte	'IfxStm_Comparator_0',0,0,18
	.byte	'IfxStm_Comparator_1',0,1,0,29
	.byte	'IfxStm_Comparator',0,12,155,1,3
	.word	79603
	.byte	17,12,159,1,9,1,18
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,18
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,29
	.byte	'IfxStm_ComparatorInterrupt',0,12,163,1,3
	.word	79681
	.byte	17,12,167,1,9,1,18
	.byte	'IfxStm_ComparatorOffset_0',0,0,18
	.byte	'IfxStm_ComparatorOffset_1',0,1,18
	.byte	'IfxStm_ComparatorOffset_2',0,2,18
	.byte	'IfxStm_ComparatorOffset_3',0,3,18
	.byte	'IfxStm_ComparatorOffset_4',0,4,18
	.byte	'IfxStm_ComparatorOffset_5',0,5,18
	.byte	'IfxStm_ComparatorOffset_6',0,6,18
	.byte	'IfxStm_ComparatorOffset_7',0,7,18
	.byte	'IfxStm_ComparatorOffset_8',0,8,18
	.byte	'IfxStm_ComparatorOffset_9',0,9,18
	.byte	'IfxStm_ComparatorOffset_10',0,10,18
	.byte	'IfxStm_ComparatorOffset_11',0,11,18
	.byte	'IfxStm_ComparatorOffset_12',0,12,18
	.byte	'IfxStm_ComparatorOffset_13',0,13,18
	.byte	'IfxStm_ComparatorOffset_14',0,14,18
	.byte	'IfxStm_ComparatorOffset_15',0,15,18
	.byte	'IfxStm_ComparatorOffset_16',0,16,18
	.byte	'IfxStm_ComparatorOffset_17',0,17,18
	.byte	'IfxStm_ComparatorOffset_18',0,18,18
	.byte	'IfxStm_ComparatorOffset_19',0,19,18
	.byte	'IfxStm_ComparatorOffset_20',0,20,18
	.byte	'IfxStm_ComparatorOffset_21',0,21,18
	.byte	'IfxStm_ComparatorOffset_22',0,22,18
	.byte	'IfxStm_ComparatorOffset_23',0,23,18
	.byte	'IfxStm_ComparatorOffset_24',0,24,18
	.byte	'IfxStm_ComparatorOffset_25',0,25,18
	.byte	'IfxStm_ComparatorOffset_26',0,26,18
	.byte	'IfxStm_ComparatorOffset_27',0,27,18
	.byte	'IfxStm_ComparatorOffset_28',0,28,18
	.byte	'IfxStm_ComparatorOffset_29',0,29,18
	.byte	'IfxStm_ComparatorOffset_30',0,30,18
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,29
	.byte	'IfxStm_ComparatorOffset',0,12,201,1,3
	.word	79790
	.byte	17,12,205,1,9,1,18
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,18
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,18
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,18
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,18
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,18
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,18
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,18
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,18
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,18
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,18
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,18
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,18
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,18
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,18
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,18
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,18
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,18
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,18
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,18
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,18
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,18
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,18
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,18
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,18
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,18
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,18
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,18
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,18
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,18
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,18
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,18
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,29
	.byte	'IfxStm_ComparatorSize',0,12,239,1,3
	.word	80748
	.byte	17,12,244,1,9,1,18
	.byte	'IfxStm_SleepMode_enable',0,0,18
	.byte	'IfxStm_SleepMode_disable',0,1,0,29
	.byte	'IfxStm_SleepMode',0,12,248,1,3
	.word	81768
	.byte	17,12,252,1,9,1,18
	.byte	'IfxStm_SuspendMode_none',0,0,18
	.byte	'IfxStm_SuspendMode_hard',0,1,18
	.byte	'IfxStm_SuspendMode_soft',0,2,0,29
	.byte	'IfxStm_SuspendMode',0,12,129,2,3
	.word	81854
.L121:
	.byte	15,88
	.word	14040
	.byte	16,10,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,0,3,8,54,15,39,12,63,12,60,12,0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,46,0,3,8,58
	.byte	15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,26,29,1,49,19,0,0,27,11,0,49,19,0,0,28,11,1,49,19,0,0
	.byte	29,22,0,3,8,58,15,59,15,57,15,73,19,0,0,30,21,0,54,15,0,0,31,19,1,58,15,59,15,57,15,11,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L34:
	.word	.L138-.L137
.L137:
	.half	3
	.word	.L140-.L139
.L139:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0,0
.L140:
.L138:
	.sdecl	'.debug_info',debug,cluster('initTime')
	.sect	'.debug_info'
.L35:
	.word	389
	.half	3
	.word	.L36
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L38,.L37
	.byte	2
	.word	.L31
	.byte	3
	.byte	'initTime',0,1,57,6,1,1,1
	.word	.L26,.L52,.L25
	.byte	4
	.word	.L26,.L52
	.byte	5
	.byte	'Fsys',0,1,59,12
	.word	.L53,.L54
	.byte	6
	.word	.L55,.L56,.L57
	.byte	7
	.word	.L58,.L59
	.byte	8
	.word	.L60,.L56,.L57
	.byte	8
	.word	.L61,.L2,.L4
	.byte	5
	.byte	'result',0,2,182,4,13
	.word	.L62,.L63
	.byte	6
	.word	.L64,.L2,.L3
	.byte	9
	.word	.L65,.L2,.L3
	.byte	0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('initTime')
	.sect	'.debug_abbrev'
.L36:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,29,1
	.byte	49,16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('initTime')
	.sect	'.debug_line'
.L37:
	.word	.L142-.L141
.L141:
	.half	3
	.word	.L144-.L143
.L143:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L144:
	.byte	5,39,7,0,5,2
	.word	.L26
	.byte	3,58,1,4,2,5,5,9
	.half	.L56-.L26
	.byte	3,250,3,1,4,3,5,40,7,9
	.half	.L2-.L56
	.byte	3,175,4,1,5,58,9
	.half	.L145-.L2
	.byte	1,5,43,9
	.half	.L146-.L145
	.byte	1,5,5,9
	.half	.L147-.L146
	.byte	1,4,2,9
	.half	.L3-.L147
	.byte	3,214,123,1,4,1,5,38,9
	.half	.L4-.L3
	.byte	3,129,124,1,5,5,9
	.half	.L57-.L4
	.byte	3,2,1,5,55,9
	.half	.L148-.L57
	.byte	1,5,41,9
	.half	.L149-.L148
	.byte	1,5,34,9
	.half	.L150-.L149
	.byte	1,5,5,9
	.half	.L151-.L150
	.byte	3,1,1,5,55,9
	.half	.L152-.L151
	.byte	1,5,41,9
	.half	.L153-.L152
	.byte	1,5,34,9
	.half	.L154-.L153
	.byte	1,5,5,9
	.half	.L155-.L154
	.byte	3,1,1,5,52,9
	.half	.L156-.L155
	.byte	1,5,41,9
	.half	.L157-.L156
	.byte	1,5,34,9
	.half	.L158-.L157
	.byte	1,5,5,9
	.half	.L159-.L158
	.byte	3,1,1,5,52,9
	.half	.L160-.L159
	.byte	1,5,41,9
	.half	.L161-.L160
	.byte	1,5,34,9
	.half	.L162-.L161
	.byte	1,5,5,9
	.half	.L163-.L162
	.byte	3,1,1,5,52,9
	.half	.L164-.L163
	.byte	1,5,41,9
	.half	.L165-.L164
	.byte	1,5,34,9
	.half	.L166-.L165
	.byte	1,5,5,9
	.half	.L167-.L166
	.byte	3,1,1,5,49,9
	.half	.L168-.L167
	.byte	1,5,41,9
	.half	.L169-.L168
	.byte	1,5,34,9
	.half	.L170-.L169
	.byte	1,5,5,9
	.half	.L171-.L170
	.byte	3,1,1,5,49,9
	.half	.L172-.L171
	.byte	1,5,41,9
	.half	.L173-.L172
	.byte	1,5,34,9
	.half	.L174-.L173
	.byte	1,5,5,9
	.half	.L175-.L174
	.byte	3,1,1,5,49,9
	.half	.L176-.L175
	.byte	1,5,41,9
	.half	.L177-.L176
	.byte	1,5,34,9
	.half	.L178-.L177
	.byte	1,5,5,9
	.half	.L179-.L178
	.byte	3,1,1,5,41,9
	.half	.L180-.L179
	.byte	1,5,34,9
	.half	.L181-.L180
	.byte	1,5,5,9
	.half	.L182-.L181
	.byte	3,1,1,5,41,9
	.half	.L183-.L182
	.byte	1,5,34,9
	.half	.L184-.L183
	.byte	1,5,5,9
	.half	.L185-.L184
	.byte	3,1,1,5,41,9
	.half	.L186-.L185
	.byte	1,5,34,9
	.half	.L187-.L186
	.byte	1,5,1,9
	.half	.L188-.L187
	.byte	3,1,1,7,9
	.half	.L39-.L188
	.byte	0,1,1
.L142:
	.sdecl	'.debug_ranges',debug,cluster('initTime')
	.sect	'.debug_ranges'
.L38:
	.word	-1,.L26,0,.L39-.L26,0,0
	.sdecl	'.debug_info',debug,cluster('waitPoll')
	.sect	'.debug_info'
.L40:
	.word	273
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L43,.L42
	.byte	2
	.word	.L31
	.byte	3
	.byte	'waitPoll',0,1,81,6,1,1,1
	.word	.L28,.L66,.L27
	.byte	4
	.word	.L28,.L66
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('waitPoll')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('waitPoll')
	.sect	'.debug_line'
.L42:
	.word	.L190-.L189
.L189:
	.half	3
	.word	.L192-.L191
.L191:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0,0,0,0,0
.L192:
	.byte	5,2,7,0,5,2
	.word	.L28
	.byte	3,209,0,1,7,9
	.half	.L44-.L28
	.byte	0,1,1
.L190:
	.sdecl	'.debug_ranges',debug,cluster('waitPoll')
	.sect	'.debug_ranges'
.L43:
	.word	-1,.L28,0,.L44-.L28,0,0
	.sdecl	'.debug_info',debug,cluster('waitTime')
	.sect	'.debug_info'
.L45:
	.word	1125
	.half	3
	.word	.L46
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L48,.L47
	.byte	2
	.word	.L31
	.byte	3
	.byte	'waitTime',0,1,93,6,1,1,1
	.word	.L30,.L67,.L29
	.byte	4
	.byte	'timeout',0,1,93,28
	.word	.L68,.L69
	.byte	5
	.word	.L30,.L67
	.byte	6
	.word	.L70,.L30,.L71
	.byte	7
	.word	.L72,.L73
	.byte	8
	.word	.L74,.L30,.L71
	.byte	9
	.byte	'deadLine',0,2,165,3,18
	.word	.L68,.L75
	.byte	6
	.word	.L76,.L30,.L13
	.byte	7
	.word	.L77,.L78
	.byte	8
	.word	.L79,.L30,.L13
	.byte	9
	.byte	'deadLine',0,2,166,2,18
	.word	.L68,.L80
	.byte	6
	.word	.L81,.L5,.L12
	.byte	8
	.word	.L82,.L5,.L12
	.byte	9
	.byte	'stmNow',0,2,223,1,18
	.word	.L68,.L83
	.byte	9
	.byte	'interruptState',0,2,224,1,18
	.word	.L84,.L85
	.byte	6
	.word	.L86,.L5,.L9
	.byte	8
	.word	.L87,.L5,.L9
	.byte	6
	.word	.L88,.L5,.L8
	.byte	8
	.word	.L89,.L5,.L8
	.byte	9
	.byte	'enabled',0,3,149,5,13
	.word	.L84,.L90
	.byte	6
	.word	.L91,.L5,.L7
	.byte	8
	.word	.L92,.L5,.L7
	.byte	9
	.byte	'reg',0,3,141,5,17
	.word	.L93,.L94
	.byte	0,0,0,0,0,0,6
	.word	.L95,.L9,.L10
	.byte	7
	.word	.L96,.L97
	.byte	8
	.word	.L98,.L9,.L10
	.byte	9
	.byte	'result',0,4,164,4,12
	.word	.L99,.L100
	.byte	0,0,6
	.word	.L101,.L102,.L11
	.byte	7
	.word	.L103,.L104
	.byte	8
	.word	.L105,.L102,.L11
	.byte	6
	.word	.L106,.L102,.L11
	.byte	7
	.word	.L107,.L108
	.byte	10
	.word	.L109,.L102,.L11
	.byte	0,0,0,0,0,0,0,6
	.word	.L110,.L14,.L71
	.byte	7
	.word	.L111,.L112
	.byte	8
	.word	.L113,.L14,.L71
	.byte	9
	.byte	'result',0,2,213,2,13
	.word	.L84,.L114
	.byte	6
	.word	.L81,.L16,.L23
	.byte	8
	.word	.L82,.L16,.L23
	.byte	9
	.byte	'stmNow',0,2,223,1,18
	.word	.L68,.L115
	.byte	9
	.byte	'interruptState',0,2,224,1,18
	.word	.L84,.L116
	.byte	6
	.word	.L86,.L16,.L20
	.byte	8
	.word	.L87,.L16,.L20
	.byte	6
	.word	.L88,.L16,.L19
	.byte	8
	.word	.L89,.L16,.L19
	.byte	9
	.byte	'enabled',0,3,149,5,13
	.word	.L84,.L117
	.byte	6
	.word	.L91,.L16,.L18
	.byte	8
	.word	.L92,.L16,.L18
	.byte	9
	.byte	'reg',0,3,141,5,17
	.word	.L93,.L118
	.byte	0,0,0,0,0,0,6
	.word	.L95,.L20,.L21
	.byte	7
	.word	.L96,.L97
	.byte	8
	.word	.L98,.L20,.L21
	.byte	9
	.byte	'result',0,4,164,4,12
	.word	.L99,.L119
	.byte	0,0,6
	.word	.L101,.L120,.L22
	.byte	7
	.word	.L103,.L104
	.byte	8
	.word	.L105,.L120,.L22
	.byte	6
	.word	.L106,.L120,.L22
	.byte	7
	.word	.L107,.L108
	.byte	10
	.word	.L109,.L120,.L22
	.byte	0,0,0,0,0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('waitTime')
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('waitTime')
	.sect	'.debug_line'
.L47:
	.word	.L194-.L193
.L193:
	.half	3
	.word	.L196-.L195
.L195:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0,0
.L196:
	.byte	4,2,5,20,7,0,5,2
	.word	.L30
	.byte	3,167,2,1,5,17,9
	.half	.L197-.L30
	.byte	1,5,5,9
	.half	.L198-.L197
	.byte	1,5,20,7,9
	.half	.L199-.L198
	.byte	3,2,1,5,33,9
	.half	.L122-.L199
	.byte	1,4,3,5,19,9
	.half	.L5-.L122
	.byte	3,228,2,1,5,17,9
	.half	.L123-.L5
	.byte	3,1,1,5,21,9
	.half	.L124-.L123
	.byte	1,5,5,9
	.half	.L125-.L124
	.byte	1,5,14,9
	.half	.L7-.L125
	.byte	3,8,1,5,10,9
	.half	.L200-.L7
	.byte	3,1,1,5,5,9
	.half	.L201-.L200
	.byte	3,1,1,4,2,9
	.half	.L8-.L201
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L9-.L8
	.byte	3,184,3,1,5,32,9
	.half	.L126-.L9
	.byte	3,1,1,5,36,9
	.half	.L202-.L126
	.byte	1,5,12,9
	.half	.L203-.L202
	.byte	1,5,5,9
	.half	.L204-.L203
	.byte	3,2,1,4,2,5,68,9
	.half	.L10-.L204
	.byte	3,186,125,1,5,66,9
	.half	.L205-.L10
	.byte	1,4,3,5,5,9
	.half	.L102-.L205
	.byte	3,199,5,1,5,17,7,9
	.half	.L206-.L102
	.byte	3,2,1,4,2,5,5,9
	.half	.L11-.L206
	.byte	3,186,122,1,5,26,9
	.half	.L12-.L11
	.byte	3,200,0,1,5,5,9
	.half	.L6-.L12
	.byte	3,3,1,5,41,9
	.half	.L13-.L6
	.byte	3,246,0,1,5,21,9
	.half	.L14-.L13
	.byte	3,176,127,1,5,18,9
	.half	.L207-.L14
	.byte	1,5,5,9
	.half	.L208-.L207
	.byte	1,5,16,7,9
	.half	.L209-.L208
	.byte	3,2,1,5,23,9
	.half	.L129-.L209
	.byte	1,4,3,5,19,9
	.half	.L16-.L129
	.byte	3,181,2,1,5,17,9
	.half	.L130-.L16
	.byte	3,1,1,5,21,9
	.half	.L131-.L130
	.byte	1,5,5,9
	.half	.L132-.L131
	.byte	1,5,14,9
	.half	.L18-.L132
	.byte	3,8,1,5,10,9
	.half	.L210-.L18
	.byte	3,1,1,5,5,9
	.half	.L211-.L210
	.byte	3,1,1,4,2,9
	.half	.L19-.L211
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L20-.L19
	.byte	3,184,3,1,5,32,9
	.half	.L134-.L20
	.byte	3,1,1,5,36,9
	.half	.L212-.L134
	.byte	1,5,12,9
	.half	.L213-.L212
	.byte	1,5,5,9
	.half	.L214-.L213
	.byte	3,2,1,4,2,5,68,9
	.half	.L21-.L214
	.byte	3,186,125,1,5,66,9
	.half	.L215-.L21
	.byte	1,4,3,5,5,9
	.half	.L120-.L215
	.byte	3,199,5,1,5,17,7,9
	.half	.L216-.L120
	.byte	3,2,1,4,2,5,5,9
	.half	.L22-.L216
	.byte	3,186,122,1,5,24,9
	.half	.L23-.L22
	.byte	3,247,0,1,5,5,9
	.half	.L17-.L23
	.byte	3,3,1,5,41,9
	.half	.L24-.L17
	.byte	3,199,0,1,4,1,5,1,7,9
	.half	.L71-.L24
	.byte	3,185,125,1,7,9
	.half	.L49-.L71
	.byte	0,1,1
.L194:
	.sdecl	'.debug_ranges',debug,cluster('waitTime')
	.sect	'.debug_ranges'
.L48:
	.word	-1,.L30,0,.L49-.L30,0,0
	.sdecl	'.debug_info',debug,cluster('TimeConst')
	.sect	'.debug_info'
.L50:
	.word	252
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp/Bsp.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L31
	.byte	3
	.byte	'TimeConst',0,16,48,14
	.word	.L121
	.byte	1,5,3
	.word	TimeConst
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('TimeConst')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('initTime')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L26,.L57-.L26,.L52-.L26
	.half	1
	.byte	82
	.word	0,0
.L25:
	.word	-1,.L26,0,.L52-.L26
	.half	2
	.byte	138,0
	.word	0,0
.L63:
	.word	0,0
.L59:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('waitPoll')
	.sect	'.debug_loc'
.L27:
	.word	-1,.L28,0,.L66-.L28
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('waitTime')
	.sect	'.debug_loc'
.L80:
	.word	0,0
.L112:
	.word	0,0
.L75:
	.word	-1,.L30,.L122-.L30,.L5-.L30
	.half	2
	.byte	144,33
	.word	.L6-.L30,.L67-.L30
	.half	2
	.byte	144,33
	.word	0,0
.L104:
	.word	0,0
.L90:
	.word	0,0
.L117:
	.word	0,0
.L108:
	.word	0,0
.L116:
	.word	-1,.L30,.L132-.L30,.L133-.L30
	.half	1
	.byte	95
	.word	0,0
.L85:
	.word	-1,.L30,.L125-.L30,.L6-.L30
	.half	1
	.byte	95
	.word	0,0
.L118:
	.word	-1,.L30,.L130-.L30,.L131-.L30
	.half	1
	.byte	95
	.word	0,0
.L94:
	.word	-1,.L30,.L123-.L30,.L124-.L30
	.half	1
	.byte	95
	.word	0,0
.L114:
	.word	-1,.L30,.L129-.L30,.L16-.L30
	.half	1
	.byte	95
	.word	.L133-.L30,.L67-.L30
	.half	1
	.byte	95
	.word	0,0
.L119:
	.word	-1,.L30,.L134-.L30,.L135-.L30
	.half	2
	.byte	144,34
	.word	.L135-.L30,.L136-.L30
	.half	2
	.byte	144,32
	.word	0,0
.L100:
	.word	-1,.L30,.L126-.L30,.L127-.L30
	.half	2
	.byte	144,33
	.word	.L127-.L30,.L128-.L30
	.half	2
	.byte	144,32
	.word	0,0
.L97:
	.word	0,0
.L83:
	.word	-1,.L30,.L128-.L30,.L6-.L30
	.half	2
	.byte	144,32
	.word	0,0
.L115:
	.word	-1,.L30,.L136-.L30,.L17-.L30
	.half	2
	.byte	144,32
	.word	0,0
.L69:
	.word	-1,.L30,0,.L14-.L30
	.half	2
	.byte	144,34
	.word	0,0
.L78:
	.word	0,0
.L73:
	.word	0,0
.L29:
	.word	-1,.L30,0,.L67-.L30
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L217:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('initTime')
	.sect	'.debug_frame'
	.word	12
	.word	.L217,.L26,.L52-.L26
	.sdecl	'.debug_frame',debug,cluster('waitPoll')
	.sect	'.debug_frame'
	.word	24
	.word	.L217,.L28,.L66-.L28
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('waitTime')
	.sect	'.debug_frame'
	.word	24
	.word	.L217,.L30,.L67-.L30
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	; Module end
