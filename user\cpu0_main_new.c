/*
 * 4G语音识别测试程序 - 简化版
 * 功能：测试user1模块的基本功能
 * 作者：BMW_智能车组
 * 日期：2025年07月17日
 */
#include "zf_common_headfile.h"
#include "zf_common_debug.h"
#include "Device.h"
#include "user1/u1_core.h"

int core0_main(void)
{
    clock_init();                   // 获取时钟频率<务必保留>
    debug_init();                   // 初始化默认调试串口
    ALL_Init();                     // 初始化所有设备
    
    printf("\r\n========================================\r\n");
    printf("    4G语音识别测试程序\r\n");
    printf("========================================\r\n");
    
    // 初始化user1模块
    printf("初始化4G语音识别模块...\r\n");
    if(u1_init() != U1_ERROR_NONE)
    {
        printf("初始化失败\r\n");
        while(1) system_delay_ms(1000);
    }
    printf("初始化成功\r\n");
    
    uint32 start_time = system_getval_ms();
    uint32 last_print = start_time;
    
    printf("开始测试...\r\n\r\n");
    
    while(1)
    {
        u1_process();  // 处理user1模块
        
        uint32 now = system_getval_ms();
        
        // 每3秒打印一次状态
        if(now - last_print > 3000)
        {
            last_print = now;
            u1_state_t state = u1_get_state();
            
            printf("状态: ");
            switch(state)
            {
                case U1_STATE_IDLE:
                    printf("空闲\r\n");
                    break;
                case U1_STATE_INIT:
                    printf("初始化\r\n");
                    break;
                case U1_STATE_NETWORK_CHECK:
                    printf("网络检查\r\n");
                    break;
                case U1_STATE_ASR_CONNECT:
                    printf("连接服务器\r\n");
                    break;
                case U1_STATE_RECOGNIZING:
                    printf("语音识别中\r\n");
                    break;
                case U1_STATE_RESULT_READY:
                    printf("结果就绪\r\n");
                    break;
                case U1_STATE_ERROR:
                    printf("错误\r\n");
                    break;
                default:
                    printf("未知\r\n");
                    break;
            }
            
            // 如果有识别结果，显示出来
            if(state == U1_STATE_RESULT_READY)
            {
                const char* result = u1_get_result();
                if(result)
                {
                    printf("识别结果: %s\r\n", result);
                }
            }
            
            printf("运行时间: %d秒\r\n\r\n", (now - start_time) / 1000);
        }
        
        system_delay_ms(100);
    }
    
    return 0;
}
